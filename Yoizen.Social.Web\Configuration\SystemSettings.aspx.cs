﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Configuration;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Social.DomainModel;
using System.Net.Mail;
using System.Net;
using System.Text;
using System.IO;
using Yoizen.Common;
using RestSharp;
using Yoizen.Social.DomainModel.Settings;
using System.Security.Authentication;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Yoizen.Social.Core;
using Yoizen.Social.Core.Services;
using Yoizen.Social.DomainModel.Responses;
using Yoizen.Social.DomainModel.DTO.YSmart;

namespace Yoizen.Social.Web.Configuration
{
	public partial class SystemSettings : LoginRequiredBasePage
	{
		#region Propiedades

		protected override string RedirectUrl { get { return "~/Configuration/SystemSettings.aspx"; } }

		protected override string PageDescription { get { return "Parámetros del Sistema"; } }

		protected override string PageDescriptionLocalizationKey => "configuration-systemsettings-title";

		#endregion

		#region Private Methods

		private List<object> BuildFtps()
		{
			var ftps = DomainModel.SystemSettings.Instance.FTPs;
			if (ftps == null || ftps.Count == 0)
				return new List<object>();

			var ftpsAsList = new List<object>();
			foreach (var ftp in DomainModel.SystemSettings.Instance.FTPs)
			{
				ftpsAsList.Add(new
				{
					ID = ftp.Key,
					ftp.Value.Name
				});
			}

			return ftpsAsList;
		}

		private bool ValidateAccessTokenYFlow(YFlowSettings yFlowSettings, bool? isContingency = false)
		{
			string url = isContingency.HasValue && isContingency.Value ? textboxYFlowContingencyUrlWeb.Text : textboxYFlowUrlWeb.Text;

			if (string.IsNullOrEmpty(url))
				return false;

			if (!string.IsNullOrEmpty(Licensing.LicenseManager.Instance.License.Configuration.YFlowCompany))
			{
				url += (url.EndsWith("/") ? string.Empty : "/") + Licensing.LicenseManager.Instance.License.Configuration.YFlowCompany;
			}

			var client = new RestClient(url);
			var request = new RestRequest("validate_access_token", Method.POST);
			request.AddHeader("content-type", "application/x-www-form-urlencoded");
			request.AddParameter("application/x-www-form-urlencoded", $"accessToken={yFlowSettings.AccessToken}", ParameterType.RequestBody);

			IRestResponse response = client.Execute(request);

			Common.Tracer.TraceVerb("Validación de token de yFlow: {0}", response.Content);

			return response.IsSuccessful;
		}
	
		private static object BuildTags(DomainModel.SystemSettings settings)
		{
			if (settings == null)
				return null;

			return new
			{
				TagOnAutoCloseCase = GetTag(settings.Cases.TagOnAutoCloseCase),
				TagOnAutoCloseHsmCases = GetTag(settings.Cases.TagOnAutoCloseHsmCases),
				TagInvocationFailed = GetTag(settings.YFlow.TagInvocationFailed),
				TagTimeoutFailed = GetTag(settings.YFlow.TagTimeoutFailed)
			};
		}
		private static object GetTag(int tagId)
		{
			if (tagId > 0)
			{
				var tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(tagId);
				if (tag != null)
				{
					return new
					{
						tag.ID,
						tag.FullName
					};
				}
			}
			return null;
		}
		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			DomainModel.SystemSettings settings = DomainModel.SystemSettings.Instance;
			Licensing.LicenseType license = Licensing.LicenseManager.Instance.License;

			if (!IsPostBack)
			{
				dropdownlistDefaultLanguage.SelectedValue = settings.DefaultLanguage;
				textboxWebAgentURL.Text = settings.WebAgentURL;
				hiddenWebAgentVersion.Value = settings.WebAgentVersion;
				labelWebAgentVersion.Text = settings.WebAgentVersion;
				checkboxWebAgentAllowOutdatedLogins.Checked = settings.WebAgentAllowOutdatedLogins;
				panelWebAgentConfigurationAllowUrlLogin.Visible = license.Configuration.AllowWebAgent;
				panelWebAgentConfigurationStateManagement.Visible = license.Configuration.AllowWebAgent;
				messageWebAgentConfigurationAllowUrlLoginCloud.Visible = license.Configuration.InTheCloud;
				panelPbxIntegration.Visible = license.Configuration.AllowPbxIntegration;
				placeholderEncryptMessages.Visible = license.Configuration.EncryptMessages;
				checkboxEncryptMessages.Checked = settings.EncryptMessages;

				textboxDeleteNotifications.Text = settings.DeleteNotifications.ToString();

				placeholderTheme.Visible = LoggedUserIsSuper;
				foreach (var directory in Directory.GetDirectories(HttpContext.Current.Server.MapPath("~/app_themes")))
				{
					var name = Path.GetFileName(directory);
					dropdownlistTheme.Items.Add(name);
				}

				if (settings.Theme != null)
				{
					dropdownlistTheme.SelectedValue = settings.Theme;
				}
				else
				{
					var pagesSection = (System.Web.Configuration.PagesSection) System.Web.Configuration.WebConfigurationManager.GetSection("system.web/pages");
					var theme = pagesSection.Theme;
					dropdownlistTheme.SelectedValue = theme;
				}

				dropdownlistPbxIntegrationAgentGroupForNewAgents.DataSource = DomainModel.Cache.Instance.GetList<DomainModel.AgentGroup>();
				dropdownlistPbxIntegrationAgentGroupForNewAgents.DataBind();
				if (settings.PbxIntegrationAgentGroupForNewAgents != null)
				{
					try
					{
						dropdownlistPbxIntegrationAgentGroupForNewAgents.SelectedValue = settings.PbxIntegrationAgentGroupForNewAgents.Value.ToString();
					}
					catch { }
				}

				checkboxWebAgentConfigurationAllowUrlLogin.Checked = settings.WebAgentUrlLoginSettings.UseUrlLogin;
				if (settings.WebAgentUrlLoginSettings.UseUrlLogin)
				{
					textboxWebAgentConfigurationUserNameLoginParameter.Text = settings.WebAgentUrlLoginSettings.UserName;
					checkboxWebAgentConfigurationPasswordRequired.Checked = settings.WebAgentUrlLoginSettings.PasswordRequired;
					if (settings.WebAgentUrlLoginSettings.PasswordRequired)
					{
						textboxWebAgentConfigurationPasswordParameter.Text = settings.WebAgentUrlLoginSettings.PasswordParam;
						textboxWebAgentConfigurationKeyToDecryptPassword.Text = settings.WebAgentUrlLoginSettings.KeyToDecryptPasswordParam;
					}

					textboxWebAgentConfigurationHashParameter.Text = settings.WebAgentUrlLoginSettings.HashParam;
					textboxWebAgentConfigurationKeyToHash.Text = settings.WebAgentUrlLoginSettings.KeyToHash;
					checkboxWebAgentConfigurationRemoveLoginForm.Checked = settings.WebAgentUrlLoginSettings.RemoveLoginForm;

					dropdownlistLogoutAction.SelectedValue = ((short) ((DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum) settings.WebAgentUrlLoginSettings.LogoutAction)).ToString();

					if (settings.WebAgentUrlLoginSettings.LogoutAction == DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum.LogoutMessage)
					{
						textboxWebAgentConfigurationLogoutMessage.Text = settings.WebAgentUrlLoginSettings.EndSessionMessage;
					}
					else if (settings.WebAgentUrlLoginSettings.LogoutAction == DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum.UrlToRedirect)
					{
						textboxWebAgentConfigurationRedirectUrl.Text = settings.WebAgentUrlLoginSettings.UrlToRedirect;
					}

					checkboxWebAgentConfigurationRemoveLogoutButton.Checked = settings.WebAgentUrlLoginSettings.RemoveLogoutButton;
				}

				/* Comienzo de configuración de manejo de estado del agente web mediante mensajería interna */
				checkboxWebAgentConfigurationAllowChangeState.Checked = settings.WebAgentStateManagementSettings.AllowChangeState;

				if (settings.WebAgentStateManagementSettings.AllowChangeState)
				{
					checkboxWebAgentConfigurationAllowAgentsToChangeState.Checked = settings.WebAgentStateManagementSettings.AllowAgentsToChangeState;
					checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging.Checked = settings.WebAgentStateManagementSettings.SendStateOfChangeByPrivateMessaging;
					checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.Checked = settings.WebAgentStateManagementSettings.ShowInformativeMessageAfterChangeStateReceived;
					if (settings.WebAgentStateManagementSettings.ShowInformativeMessageAfterChangeStateReceived)
						textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.Text = settings.WebAgentStateManagementSettings.MessageAfterChangeStateReceived;

					textboxWebAgentConfigurationChangeStateTargetOrigin.Text = settings.WebAgentStateManagementSettings.TargetOriginForChangeStateMessaging;
				}

				checkboxWebAgentConfigurationAllowLogoutInvoke.Checked = settings.WebAgentStateManagementSettings.AllowLogoutInvoke;

				if (settings.WebAgentStateManagementSettings.AllowLogoutInvoke)
				{
					checkboxWebAgentConfigurationIgnoreLogoutAfterError.Checked = settings.WebAgentStateManagementSettings.IgnoreLogoutAfterError;
					checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging.Checked = settings.WebAgentStateManagementSettings.SendLogoutStateOfChangeByPrivateMessaging;

					checkboxWebAgentConfigurationShowMessageAfterLogoutReceived.Checked = settings.WebAgentStateManagementSettings.ShowMessageAfterLogoutReceived;
					if (settings.WebAgentStateManagementSettings.ShowMessageAfterLogoutReceived)
						textboxWebAgentConfigurationShowMessageAfterLogoutReceived.Text = settings.WebAgentStateManagementSettings.MessageAfterLogoutReceived;

					textboxWebAgentConfigurationLogoutInvokeTargetOrigin.Text = settings.WebAgentStateManagementSettings.TargetOriginForLogoutMessaging;
				}

				/* Fin de configuración de manejo de estado del agente web mediante mensajería interna */

				placeholderAllowToConfigureACDBalancing.Visible = this.LoggedUserIsSuper && license.Configuration.AllowToConfigureACDBalancing;
				checkboxUseACDBalancing.Checked = settings.UseACDBalancing;
				dropdownlistACDBalancingWithQueueLevels.SelectedValue = settings.ACDBalancingWithQueueLevels ? "0" : "1";
				checkboxACDBalancingWithQueueLevelsWorking.Checked = checkboxUseACDBalancing.Checked &&
					settings.ACDBalancingWithQueueLevels &&
					settings.ACDBalancingWithQueueLevelsWorking;

				checkboxUseSentDate.Checked = settings.UseSentDate;
				checkboxMarkWhiteListMessagesAsVIM.Checked = settings.MarkWhiteListMessagesAsVIM;
				dropdownlistPrioritizeVIMOverQueueLevel.SelectedValue = settings.PrioritizeVIMOverQueueLevel ? "0" : "1";
				textboxSessionTimeOut.Text = settings.SessionTimeOut.ToString();
				textboxAgentHistoricMaxMessages.Text = settings.AgentHistoricMaxMessages.ToString();

				textboxFilterEmailSubject.Text = settings.FilterEmailSettings.Subject;
				textboxFilterEmailEmails.Text = settings.FilterEmailSettings.Emails;
				checkboxFilterEmailSendToAdministrators.Checked = settings.FilterEmailSettings.SendToAdministrators;
				checkboxFilterEmailSendToSupervisors.Checked = settings.FilterEmailSettings.SendToSupervisors;
				textboxFilterEmailTemplate.Text = settings.FilterEmailSettings.Template;

				checkboxAllowAgentToBlockUsers.Checked = settings.AllowAgentToBlockUsers;
				placeholderAllowAgentsToReturnMessagesToQueue.Visible = license.Configuration.AllowAgentsToReturnMessagesToQueue;
				checkboxAllowAgentsToReturnMessagesToQueue.Checked = settings.AllowAgentsToReturnMessagesToQueue;
				checkboxAgentMustEnterReturnToQueueReason.Checked = settings.AgentMustEnterReturnToQueueReason;
				textboxMaximumNumberOfTimesMessageCanBeReturned.Text = settings.MaximumNumberOfTimesMessageCanBeReturned.ToString();
				placeholderAllowAgentsToReturnMessagesToSpecifiedQueue.Visible = license.Configuration.AllowAgentsToReturnMessagesToSpecifiedQueue;
				checkboxAllowAgentsToSelectQueueOnReturnToQueue.Checked = settings.AllowAgentsToSelectQueueOnReturnToQueue;
				placeholderAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.Visible = (license.Configuration.AllowAgentsToReturnMessagesToSpecifiedQueue || license.Configuration.AllowAgentsToReturnMessagesToQueue) &&
					license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Mail);
				checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.Checked = settings.AllowAgentsToReturnMessagesWithRelatedMessagesToQueue;

				var language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				if (this.LoggedUser.Settings != null && this.LoggedUser.Settings.ContainsKey(DomainModel.User.Locale))
					language = this.LoggedUser.Settings[DomainModel.User.Locale];

				checkboxGenerateDailyReport.Checked = DomainModel.SystemSettings.Instance.GenerateDailyReport;
				placeholderDailyReportsDeleteLocalCopy.Visible = this.LoggedUserIsSuper && license.Configuration.SaveReportsInStorage;
				checkboxDailyReportsDeleteLocalCopy.Checked = settings.DailyReportsDeleteLocalFiles;

				foreach (DomainModel.Reports.ReportTypes enumValue in DomainModel.Reports.ReportType.GetDailyReports())
				{
					if (enumValue == DomainModel.Reports.ReportTypes.Chats && !license.Configuration.AllowedSocialServiceTypes.Any(sst => sst == SocialServiceTypes.Chat))
						continue;

					if (enumValue == DomainModel.Reports.ReportTypes.ChatsMessages && !license.Configuration.AllowedSocialServiceTypes.Any(sst => sst == SocialServiceTypes.Chat))
						continue;

					if (enumValue == DomainModel.Reports.ReportTypes.DetailedWhatsappHSM && !license.Configuration.AllowedSocialServiceTypes.Any(sst => sst == SocialServiceTypes.WhatsApp))
						continue;

					if (enumValue == DomainModel.Reports.ReportTypes.WhatsappHSMWithoutCase && !license.Configuration.AllowWhatsappOutboundWithoutCaseCreation)
						continue;

					var item = Helpers.ControlsHelper.CreateListItemLocalized($"ReportTypes.{enumValue}", language, ((short) enumValue).ToString(), enumValue.ToString());
					checkboxlistDailyReportsToGenerate.Items.Add(item);
				}

				var ftps = this.BuildFtps().OrderBy(item => item.AsDictionary()["Name"]);
				listboxFtps.DataSource = ftps;
				listboxFtps.DataBind();

				textboxScheduleReportsToMantain.Text = DomainModel.SystemSettings.Instance.ScheduledReportsToMantain.ToString();

				if (license.Configuration.InTheCloud)
				{
					if (this.LoggedUserIsSuper)
					{
						placeholderDailyReportsToMantain.Visible = true;
						textboxDailyReportsToMantain.MaxLength = 3;
					}
					else
					{
						placeholderDailyReportsToMantain.Visible = false;
					}
				}

				if (checkboxGenerateDailyReport.Checked)
				{	
					textboxDailyReportsToMantain.Text = DomainModel.SystemSettings.Instance.DailyReportsToMantain.ToString();
					textboxDailyReportsEmailSubject.Text = DomainModel.SystemSettings.Instance.EmailDailyReports.Subject;
					textboxDailyReportsEmails.Text = DomainModel.SystemSettings.Instance.EmailDailyReports.Emails;
					textboxDailyReportsEmailTemplate.Text = DomainModel.SystemSettings.Instance.EmailDailyReports.Template;
					dropdownlistDailyReportsExportFormat.SelectedValue = ((short) DomainModel.SystemSettings.Instance.DailyReportsFormat).ToString();

					foreach (ListItem item in checkboxlistDailyReportsToGenerate.Items)
					{
						var value = (DomainModel.Reports.ReportTypes) short.Parse(item.Value);
						if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate != null && DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(value))
						{
							item.Selected = true;
						}
					}

					checkboxEnableFtpDailyReport.Checked = DomainModel.SystemSettings.Instance.EnableFtpDailyReports;

					if (DomainModel.SystemSettings.Instance.EnableFtpDailyReports)
					{
						for (int i = 0; i < ftps.Count(); i++)
						{
							var ftpName = ftps.ElementAt(i).AsDictionary()["ID"];
							if (DomainModel.SystemSettings.Instance.FtpIdDailyReports.Equals(ftpName, StringComparison.InvariantCultureIgnoreCase))
							{
								listboxFtps.SelectedIndex = i;
								break;
							}
						}

						textboxFtpDirectoryDailyReports.Text = DomainModel.SystemSettings.Instance.FtpDirectoryDailyReports;
						checkboxDailyReportsZipExcel.Checked = settings.DailyReportsZipExcel;
						checkboxDailyReportsZipCSV.Checked = settings.DailyReportsZipCSV;
					}
				}

				literalMaxRecordsToExport.Text = settings.MaxRecordsToExport.ToString();
				placeholderMinutesToAbortExporting.Visible = this.LoggedUserIsSuper;
				textboxMinutesToAbortExporting.Text = settings.MinutesToAbortExporting.ToString();
				textboxExportEmailSubject.Text = settings.EmailExport.Subject;
				textboxExportEmailTemplate.Text = settings.EmailExport.Template;
				textboxExportEmailAbortedSubject.Text = settings.EmailExportAborted.Subject;
				textboxExportEmailAbortedTemplate.Text = settings.EmailExportAborted.Template;

				IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();

				if (settings.DeliveryFailedNotification.NotifyByEmail)
					listboxAlertMessagesDeliveryFailedVia.Items.FindByValue("1").Selected = true;
				if (settings.DeliveryFailedNotification.NotifyByNotifications)
					listboxAlertMessagesDeliveryFailedVia.Items.FindByValue("2").Selected = true;

				var enabledServices = services.Where(s => s.Enabled && s.ID != 0 && s.Type != ServiceTypes.Chat && s.Type != ServiceTypes.IntegrationChat).OrderBy(s => s.Name);
				foreach (var enabledService in enabledServices)
				{
					var listItem = new ListItem(enabledService.Name, enabledService.ID.ToString());
					listItem.Attributes.Add("type", ((short) enabledService.Type).ToString());
					listboxAlertMessagesDeliveryFailedFromServices.Items.Add(listItem);

					if (settings.DeliveryFailedNotification.Services != null && settings.DeliveryFailedNotification.Services.Contains(enabledService.ID))
						listItem.Selected = true;
				}

				textboxAlertMessagesDeliveryFailedViaMailEmailSubject.Text = settings.DeliveryFailedNotification.EmailNotificationSettings.Subject;
				textboxAlertMessagesDeliveryFailedViaMailEmailTemplate.Text = settings.DeliveryFailedNotification.EmailNotificationSettings.Template;

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Twitter || s == ServiceTypes.TwitterRt))
				{
					liSocialServices.Visible = true;
					divSocialServices.Visible = true;
					divTwitterService.Visible = true;

					checkboxTwitterAllowMentionInMultipleServices.Checked = settings.Twitter.AllowMentionInMultipleServices;
				}
				else
				{
					placeholderActionsTwitterSubject.Visible = false;
					divTwitterService.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Facebook || s == ServiceTypes.FacebookRt || s == ServiceTypes.FacebookMessenger || s == ServiceTypes.Instagram))
				{
					placeholderActionsFacebookSubject.Visible = license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Facebook || s == ServiceTypes.FacebookRt);
					placeholderActionsFacebookMessengerSubject.Visible = license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.FacebookMessenger);
					placeholderActionsInstagramSubject.Visible = license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Instagram);
				}
				else
				{
					placeholderActionsFacebookSubject.Visible = false;
					placeholderActionsFacebookMessengerSubject.Visible = false;
					placeholderActionsInstagramSubject.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.IntegrationChat))
				{
					divRestChatService.Visible = this.LoggedUserIsSuper;
					textboxRestChatMaxConcurrentSessions.Text = settings.RestChat.MaxConcurrentSessions.ToString();
					textboxRestChatMaxConcurrentCallsPerSession.Text = settings.RestChat.MaxConcurrentCallsPerSession.ToString();
				}
				else
				{
					divRestChatService.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Chat))
				{
					if (
						(license.Configuration.InTheCloud && this.LoggedUserIsSuper) ||
						(!license.Configuration.InTheCloud && license.Configuration.UseSingleChatServiceOnPremise)
					)
					{
						liSocialServices.Visible = true;
						divSocialServices.Visible = true;
						divChatService.Visible = true;
					}
					else
					{
						divChatService.Visible = false;
					}

					dropdownlistChatCloudSameServer.SelectedValue = settings.Chat.CloudSameServer ? "1" : "0";
					if (!settings.Chat.CloudSameServer)
						textboxChatCloudOtherServer.Text = settings.Chat.CloudOtherServer;
					else
						textboxChatCloudOtherServer.Text = string.Empty;

					textboxChatCloudPort.Text = settings.Chat.CloudPort.ToString();
					textboxChatCloudPortForYSocial.Text = settings.Chat.CloudPortYSocial.ToString();
					dropdownlisCloudPortOverHttps.SelectedValue = settings.Chat.CloudPortOverHttps ? "1" : "0";
					dropdownlisCloudOtherServerPortYSocialOverHttps.SelectedValue = settings.Chat.CloudOtherServerPortYSocialOverHttps ? "1" : "0";
					placeholderChatOnPremise.Visible = !license.Configuration.InTheCloud && license.Configuration.UseSingleChatServiceOnPremise;
					panelChatOnPremiseAlerts.Visible = !license.Configuration.InTheCloud && license.Configuration.UseSingleChatServiceOnPremise;
					placeholderActionsChatSubject.Visible = true;

					if (!license.Configuration.InTheCloud && license.Configuration.UseSingleChatServiceOnPremise)
					{
						textboxChatStartedDestinationEmails.Text = settings.Chat.ServiceStartedEmail.Emails;
						textboxChatStartedEmailSubject.Text = settings.Chat.ServiceStartedEmail.Subject;
						textboxChatStartedEmailTemplate.Text = settings.Chat.ServiceStartedEmail.Template;
						textboxChatStoppedDestinationEmails.Text = settings.Chat.ServiceStoppedEmail.Emails;
						textboxChatStoppedEmailSubject.Text = settings.Chat.ServiceStoppedEmail.Subject;
						textboxChatStoppedEmailTemplate.Text = settings.Chat.ServiceStoppedEmail.Template;
						textboxChatCrashedDestinationEmails.Text = settings.Chat.ServiceCrashedEmail.Emails;
						textboxChatCrashedEmailSubject.Text = settings.Chat.ServiceCrashedEmail.Subject;
						textboxChatCrashedEmailTemplate.Text = settings.Chat.ServiceCrashedEmail.Template;
					}
				}
				else
				{
					divChatService.Visible = false;
					placeholderActionsChatSubject.Visible = false;
				}

				textboxFavoriteMails.Text = settings.FavoriteMails;

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Mail))
				{
				}
				else
				{
					placeholderActionsMailSubject.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.WhatsApp))
				{
					liSocialServices.Visible = true;
					divSocialServices.Visible = true;
					divWhatsappService.Visible = this.LoggedUserIsSuper;
					textboxWhatsappUrlRtNotifications.Text = settings.Whatsapp.UrlRtNotifications.Replace("api/whatsapp", string.Empty);
					placeholderPushNotificationsServiceBusConcurrentStatuses.Visible = true;
					placeholderPushNotificationsServiceBusConcurrentMassive.Visible = true;

					var countries = (List<Country>) Application["Countries"];
					if (countries == null)
					{
						countries = DAL.CountryDAO.GetAll();

						Application.Lock();
						Application.Add("Countries", countries);
						Application.UnLock();
					}

					dropdownlistWhatsappDefaultInternationCode.DataSource = countries.Select(c => new
					{
						c.InternationalCode,
						c.FullDescription
					});
					dropdownlistWhatsappDefaultInternationCode.DataBind();

					dropdownlistWhatsappDefaultInternationCode.SelectedValue = settings.Whatsapp.DefaultInternationCode.ToString();

					placeholderWhatsappServiceCatalogApp.Visible = this.LoggedUserIsSuper;
					dropdownlistWhatsappServiceCatalogApp.SelectedValue = settings.Whatsapp.CatalogApp;

					placeholderWhatsappEmbeddedSignUpApp.Visible = this.LoggedUserIsSuper;
					dropdownlistWhatsappEmbeddedSignUpApp.SelectedValue = settings.Whatsapp.EmbeddedSignUpApp;

					placeholderWhatsAppServiceVoiceCalls.Visible = license.Configuration.AllowWhatsappVoiceCalls;
					placeholderWhatsAppServiceVoiceCallsSuperYoizen.Visible = license.Configuration.AllowWhatsappVoiceCalls && this.LoggedUserIsSuper;
					panelWhatsAppServiceVoiceCallsRecording.Visible = license.Configuration.AllowWhatsappVoiceCalls && this.LoggedUserIsSuper && license.Configuration.AllowWhatsappVoiceCallsRecording;
					dropdownlistWhatsAppVoiceCallsOnCallBehaviour.SelectedValue = ((short) settings.Whatsapp.VoiceCallsOnCallBehaviour).ToString();
					try
					{
						if (!string.IsNullOrEmpty(settings.Whatsapp.VoiceCallsIceServers))
							textboxWhatsAppServiceVoiceCallsIceServers.Text = Newtonsoft.Json.Linq.JToken.Parse(settings.Whatsapp.VoiceCallsIceServers).ToString(Formatting.Indented);
					}
					catch
					{
						textboxWhatsAppServiceVoiceCallsIceServers.Text = settings.Whatsapp.VoiceCallsIceServers;
					}

					textboxWhatsAppServiceVoiceCallsRecordingHostname.Text = settings.Whatsapp.VoiceCallsRecordingHostname;
					textboxWhatsAppServiceVoiceCallsRecordingPort.Text = settings.Whatsapp.VoiceCallsRecordingPort.ToString();
					textboxWhatsAppServiceVoiceCallsRecordingJWTSecret.Text = settings.Whatsapp.VoiceCallsRecordingManagementJwtSecret;
					textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname.Text = settings.Whatsapp.VoiceCallsRecordingDownloadHostname;
				}
				else
				{
					divWhatsappService.Visible = false;
					placeholderActionsWhatsappSubject.Visible = false;
					placeholderWhatsAppServiceVoiceCalls.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.SMS))
				{
				}
				else
				{
					placeholderActionsSMSSubject.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Telegram))
				{
					liSocialServices.Visible = true;
					divSocialServices.Visible = true;
					divTelegramService.Visible = this.LoggedUserIsSuper;
					textboxTelegramUrlRtNotifications.Text = settings.Telegram.UrlRtNotifications.Replace("api/telegram", string.Empty);
				}
				else
				{
					divTelegramService.Visible = false;
					placeholderActionsTelegramSubject.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.SMS))
				{
				}
				else
				{
					placeholderActionsSMSSubject.Visible = false;
				}

				if (license.Configuration.AllowedServiceTypes.Any(s => s == ServiceTypes.Skype))
				{
				}
				else
				{
					placeholderActionsSkypeSubject.Visible = false;
				}

				checkboxAllowForwardAction.Checked = settings.AllowForwardAction;
				checkboxForwardOutsideDomainAvailable.Checked = settings.ForwardOutsideDomainAvailable;
				if (!checkboxForwardOutsideDomainAvailable.Checked)
					textboxAvailableDomains.Text = settings.AvailableDomainsToForward;

				textboxAttachmentsRoute.Text = settings.AttachmentsRoute;
				textboxAttachmentsMinimumFreeSpace.Text = settings.AttachmentsMinimumFreeSpace.ToString();
				hiddenFieldOutOfDiskSpaceForAttachmentsConnection.Value = settings.OutOfDiskSpaceForAttachments.EmailConnection;
				textboxOutOfDiskSpaceForAttachmentsEmailSubject.Text = settings.OutOfDiskSpaceForAttachments.Subject;
				textboxOutOfDiskSpaceForAttachmentsEmails.Text = settings.OutOfDiskSpaceForAttachments.Emails;
				textboxOutOfDiskSpaceForAttachmentsEmailTemplate.Text = settings.OutOfDiskSpaceForAttachments.Template;

				checkboxCheckLastQueueOfOpenCase.Checked = settings.Cases.CheckLastQueueOfOpenCase;
				checkboxIgnoreLastQueueForSLMovedMessage.Checked = settings.Cases.IgnoreLastQueueForSLMovedMessage;
				textboxMaxElapsedMinutesToCloseCases.Text = settings.Cases.MaxElapsedMinutesToCloseCases.ToString();
				textboxAutoReplyInCloseCaseText.Text = settings.Cases.AutoReplyInCloseCaseText;
				checkboxReplyInCloseCase.Checked = settings.Cases.AllowToAutoReplyCloseCases;
				checkboxCheckLastQueueByTime.Checked = settings.CheckLastQueueByTime;
				textboxLastQueueByTime.Text = settings.CheckLastQueueByTimeValue.ToString();
				textboxCasesCloseConcurrent.Text = settings.Cases.CasesCloseConcurrent.ToString();
				placeholderCasesCloseConcurrent.Visible = this.LoggedUserIsSuper;

				textboxMaxElapsedMinutesToCloseHsmCases.Text = settings.Cases.MaxElapsedMinutesToCloseHsmCases.ToString();

				//hiddenTagCloseHsmCase.Value = settings.Cases.TagOnAutoCloseHsmCases.ToString();

				//hiddenTagCloseCase.Value = settings.Cases.TagOnAutoCloseCase.ToString();

				checkboxAgentMustEnterDiscardReason.Checked = settings.AgentMustEnterDiscardReason;

				var list = new List<ListItem>();
				foreach (DomainModel.AgentTagRequiredOptions enumValue in DomainModel.AgentTagRequiredOption.GetAgentTagRequiredOptions())
				{
					var item = Helpers.ControlsHelper.CreateListItemLocalized($"AgentTagRequiredOptions.{enumValue}", language, ((short) enumValue).ToString(), enumValue.ToString());
					list.Add(item);
				}
				dropdownlistTagCasesOnStart.DataSource = list;
				dropdownlistTagCasesOnStart.DataTextField = "Text";
				dropdownlistTagCasesOnStart.DataValueField = "Value";
				dropdownlistTagCasesOnStart.DataBind();
				if (dropdownlistTagCasesOnStart.Items.FindByValue(((short) settings.Cases.TagCasesOnStart).ToString()) != null)
				{
					dropdownlistTagCasesOnStart.SelectedValue = ((short) settings.Cases.TagCasesOnStart).ToString();
				}
				else
				{
					dropdownlistTagCasesOnStart.SelectedIndex = 0;
				}

				dropdownlistTagCasesOnDiscard.DataSource = list;
				dropdownlistTagCasesOnDiscard.DataTextField = "Text";
				dropdownlistTagCasesOnDiscard.DataValueField = "Value";
				dropdownlistTagCasesOnDiscard.DataBind();
				if (dropdownlistTagCasesOnDiscard.Items.FindByValue(((short) settings.Cases.TagCasesOnDiscard).ToString()) != null)
				{
					dropdownlistTagCasesOnDiscard.SelectedValue = ((short) settings.Cases.TagCasesOnDiscard).ToString();
				}
				else
				{
					dropdownlistTagCasesOnDiscard.SelectedIndex = 0;
				}

				dropdownlistTagCasesOnClose.DataSource = list;
				dropdownlistTagCasesOnClose.DataTextField = "Text";
				dropdownlistTagCasesOnClose.DataValueField = "Value";
				dropdownlistTagCasesOnClose.DataBind();
				if (dropdownlistTagCasesOnClose.Items.FindByValue(((short) settings.Cases.TagCasesOnClose).ToString()) != null)
				{
					dropdownlistTagCasesOnClose.SelectedValue = ((short) settings.Cases.TagCasesOnClose).ToString();
				}
				else
				{
					dropdownlistTagCasesOnStart.SelectedIndex = 0;
				}


				checkboxTagOutgoing.Checked = settings.Cases.TagOutgoing;

				checkboxImportantTagOutgoing.Checked = settings.Cases.ImportantTagOutgoing;
				checkboxImportantTag.Checked = settings.Cases.ImportantTag;
				checkboxAlwaysUpdateCase.Checked = settings.Cases.AlwaysUpdateCase;
				//checkboxAllowAgentsToMarkCasesAsPending.Checked = settings.Cases.AllowAgentsToMarkCasesAsPending;
				checkboxAllowToAddMessagesToCasesWithMessagesInQueue.Checked = settings.Cases.AllowToAddMessagesToCasesWithMessagesInQueue;
				checkboxAllowNotificationWhenClosePendingCase.Checked = settings.Cases.AllowNotificationWhenClosePendingCase;

				textboxMaxAssignableMessagesPerUser.Text = settings.MaxAssignableMessagesPerUser.ToString();
				checkboxAutoMarkAsReadMessages.Checked = settings.AutoMarkAsReadMessages;

				checkboxOutgoingMessagesEnabled.Checked = settings.OutgoingMessagesEnabled;

				hiddenLicenseExpiredConnection.Value = settings.Service.EmailLicenseExpired.EmailConnection;
				textboxLicenseExpiredEmails.Text = settings.Service.EmailLicenseExpired.Emails;
				textboxLicenseExpiredEmailSubject.Text = settings.Service.EmailLicenseExpired.Subject;
				textboxLicenseExpiredEmailTemplate.Text = settings.Service.EmailLicenseExpired.Template;
				hiddenFieldDatabaseProblemsConnection.Value = settings.EmailDatabaseProblems.EmailConnection;
				textboxDatabaseProblemsEmails.Text = settings.EmailDatabaseProblems.Emails;
				textboxDatabaseProblemsEmailSubject.Text = settings.EmailDatabaseProblems.Subject;
				textboxDatabaseProblemsEmailTemplate.Text = settings.EmailDatabaseProblems.Template;
				hiddenFieldOutOfMemoryConnection.Value = settings.EmailOutOfMemory.EmailConnection;
				textboxOutOfMemoryEmails.Text = settings.EmailOutOfMemory.Emails;
				textboxOutOfMemoryEmailSubject.Text = settings.EmailOutOfMemory.Subject;
				textboxOutOfMemoryEmailTemplate.Text = settings.EmailOutOfMemory.Template;

				hiddenFieldAgentCreatedLoginInformationConnection.Value = settings.AgentCreatedLoginInformation.EmailConnection;
				textboxAgentCreatedLoginInformationEmailSubject.Text = settings.AgentCreatedLoginInformation.Subject;
				textboxAgentCreatedLoginInformationEmailTemplate.Text = settings.AgentCreatedLoginInformation.Template;
				hiddenFieldAgentPasswordChangedConnection.Value = settings.AgentPasswordChanged.EmailConnection;
				textboxAgentPasswordChangedEmailSubject.Text = settings.AgentPasswordChanged.Subject;
				textboxAgentPasswordChangedEmailTemplate.Text = settings.AgentPasswordChanged.Template;

				string defaultEmail = "";

				if (DomainModel.SystemSettings.Instance.EmailConnections != null &&
					DomainModel.SystemSettings.Instance.EmailConnections.Count > 0)
				{
					foreach (var email in DomainModel.SystemSettings.Instance.EmailConnections)
					{
						if (email.Value.UseAsDefault)
						{
							defaultEmail = email.Key.ToString();
							break;
						}
					}
				}

				hiddenFieldDefaultEmailConnection.Value = defaultEmail;

				textboxFacebookSubject.Text = settings.ForwardSettings.FacebookSubject;
				textboxFacebookMessengerSubject.Text = settings.ForwardSettings.FacebookMessengerSubject;
				textboxTwitterSubject.Text = settings.ForwardSettings.TwitterSubject;
				textboxWhatsappSubject.Text = settings.ForwardSettings.WhatsappSubject;
				textboxTelegramSubject.Text = settings.ForwardSettings.TelegramSubject;
				textboxSMSSubject.Text = settings.ForwardSettings.SMSSubject;
				textboxSkypeSubject.Text = settings.ForwardSettings.SkypeSubject;
				textboxInstagramSubject.Text = settings.ForwardSettings.InstagramSubject;
				textboxChatSubject.Text = settings.ForwardSettings.ChatSubject;
				textboxMailMaskSubject.Text = settings.ForwardSettings.MailMaskSubject;
				textboxMailMaskBody.Text = Server.HtmlDecode(settings.ForwardSettings.MailMaskBody);

				checkboxEnableCapi.Checked = settings.EnableCapi;

				IEnumerable<Tag> tags = DomainModel.Cache.Instance.GetList<Tag>();
				tags = tags.Where(tag => !tag.IsSystem && tag.TreeLevel > 1 && !tag.HasChildTags && tag.Enabled).OrderBy(t => t.FullName);
				listboxTags.DataSource = tags;
				listboxTags.DataBind();

				var tagGroups = DomainModel.Cache.Instance.GetList<DomainModel.TagGroup>();
				var tagGroupsNames = tagGroups.OrderBy(q => q.Name).Select(s => new { ID = s.ID, Name = s.Name });

				listboxGroupTags.DataSource = tagGroupsNames;
				listboxGroupTags.DataBind();

				listboxMetaEvents.DataSource = Enum.GetValues(typeof(MetaEventTypes))
					.Cast<MetaEventTypes>()
					.Select(m => new
					{
						Name = m.ToString(),
						Value = (int) m
					}).ToList();

				listboxMetaEvents.DataTextField = "Name";
				listboxMetaEvents.DataValueField = "Value";
				listboxMetaEvents.DataBind();


				if (this.LoggedUserIsSuper)
				{
					divKeycloakConfig.Visible = true;
				}

				dropdownlistAuthenticationType.SelectedValue = ((short) settings.AuthenticationType).ToString();
				checkboxLdapUseLdap.Checked = settings.LDAP.UseLDAP;
				placeholderLdapPassword.Visible = false;
				if (settings.LDAP.UseLDAP)
				{
					textboxLdapServer.Text = settings.LDAP.Server;
					textboxLdapPort.Text = settings.LDAP.Port.ToString();
					textboxLdapSearchDN.Text = settings.LDAP.RootDN;
					textboxLdapUser.Text = settings.LDAP.User;
					checkboxLdapLocalUsers.Checked = settings.LDAP.AllowCreateLocalUsers;
					checkboxLdapLocalAgents.Checked = settings.LDAP.AllowCreateLocalAgents;

					if (settings.LDAP.UseConfigurationParams)
					{
						checkboxLDAPUseConfigurationParams.Checked = settings.LDAP.UseConfigurationParams;
						textboxLDAPConfigurationFirstName.Text = settings.LDAP.ConfigurationParamFirstName;
						textboxLDAPConfigurationLastName.Text = settings.LDAP.ConfigurationParamLastName;
						textboxLDAPConfigurationUserName.Text = settings.LDAP.ConfigurationParamUserName;
						textboxLDAPConfigurationEmail.Text = settings.LDAP.ConfigurationParamEmail;
						textboxLDAPConfigurationLDAP.Text = settings.LDAP.ConfigurationParamLDAP;
					}

					else
					{
						checkboxLDAPUseConfigurationParams.Checked = false;
						textboxLDAPConfigurationFirstName.Text = string.Empty;
						textboxLDAPConfigurationLastName.Text = string.Empty;
						textboxLDAPConfigurationUserName.Text = string.Empty;
						textboxLDAPConfigurationEmail.Text = string.Empty;
						textboxLDAPConfigurationLDAP.Text = string.Empty;
					}

					if (settings.LDAP.PasswordLength > 0)
					{
						labelLdapPassword.Text = new string('•', settings.LDAP.PasswordLength);
						placeholderLdapPassword.Visible = true;
						literalLdapPasswordChangePasswordTitle.Visible = true;
						literalLdapPasswordNewPasswordTitle.Visible = false;
					}
					else
					{
						checkboxLdapPassword.Checked = true;
						checkboxLdapPassword.Enabled = false;
						labelLdapPassword.Text = string.Empty;
						literalLdapPasswordChangePasswordTitle.Visible = false;
						literalLdapPasswordNewPasswordTitle.Visible = true;
					}
				}
				else
				{
					checkboxLdapPassword.Visible = false;
					checkboxLdapPassword.Checked = true;
					labelLdapPassword.Text = string.Empty;
					literalLdapPasswordChangePasswordTitle.Visible = false;
					literalLdapPasswordNewPasswordTitle.Visible = true;
				}

				textboxLdapUserSearchFilter.Text = settings.LDAP.UserSearchFilter;
				checkboxLdapUseSecureAuthentication.Checked = settings.LDAP.Secure;

				checkboxGoogleAuthEnabled.Checked = settings.GoogleAuth.Enabled;
				textboxGoogleAuthHostedDomain.Text = settings.GoogleAuth.HostedDomain;
				checkboxGoogleAuthUseCustom.Checked = settings.GoogleAuth.UseCustom;
				textboxGoogleAuthClientID.Text = settings.GoogleAuth.ClientId;
				textboxGoogleAuthClientSecret.Text = settings.GoogleAuth.ClientSecret;
				checkboxGoogleAuthLocalUsers.Checked = settings.GoogleAuth.AllowCreateLocalUsers;
				checkboxGoogleAuthLocalAgents.Checked = settings.GoogleAuth.AllowCreateLocalAgents;

				checkboxKeycloakAuthEnabled.Checked = settings.SamlAuth.Enabled;
				textboxKeycloakEndpoint.Text = settings.SamlAuth.ClientEndpoint;
				textboxKeycloakRealmName.Text = settings.SamlAuth.RealmName;
				textboxKeycloakClientName.Text = settings.SamlAuth.ClientName;
				textboxSamlButtonText.Text = settings.SamlAuth.SamlButtonText;
				checkboxSamlLocalUsers.Checked = settings.SamlAuth.AllowCreateLocalUsers;
				checkboxSamlLocalAgents.Checked = settings.SamlAuth.AllowCreateLocalAgents;


				if (license.Configuration.AllowToExtendProfile)
				{
					divAllowToExtendProfile.Visible = true;
					hiddenExtendedProfileFields.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.ExtendedProfilesFields);
				}
				else
				{
					divAllowToExtendProfile.Visible = false;
				}

				if (license.Configuration.AllowToExtendCase)
				{
					divAllowToExtendCase.Visible = true;
					hiddenExtendedCaseFields.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.ExtendedCasesFields);
				}
				else
				{
					divAllowToExtendCase.Visible = false;
				}

				if (license.Configuration.AllowToExtendBusinessData)
				{
					divAllowToExtendBusinessData.Visible = true;
					hiddenExtendedProfileBusinessCodeFields.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.ExtendedProfilesBusinessCodeFields);

					if (license.Configuration.AllowCustomBusinessDataRegex)
					{
						divBusinessDataRegex.Visible = true;
						textboxBusinessDataRegex.Text = settings.BusinessDataRegex;
						textboxBusinessDataFormatMessage.Text = settings.BusinessDataFormatMessage;
						textboxBusinessDataWrongInputMessage.Text = settings.BusinessDataWrongInputMessage;
					}
					else
					{
						divBusinessDataRegex.Visible = false;
					}
				}
				else
				{
					divAllowToExtendBusinessData.Visible = false;
				}


				if (license.Configuration.AllowCustomShortener)
				{
					divBitLy.Visible = true;
					checkboxBitLyCustomShortener.Checked = settings.BitLy.UseCustom;
					textboxBitLyAccessToken.Text = settings.BitLy.AccessToken;
				}
				else
				{
					divBitLy.Visible = false;
				}

				if (license.Configuration.SurveysEnabled)
				{
					placeholderSurveysEnabled.Visible = true;
					messageSurveysDisabled.Visible = false;
					checkboxEnableSurveys.Checked = settings.EnableSurveys;
					liMoreServices.Visible = true;
					divMoreServices.Visible = true;
				}
				else
				{
					checkboxEnableSurveys.Checked = false;
					placeholderSurveysEnabled.Visible = false;
					messageSurveysDisabled.Visible = true;
				}

				if (license.Configuration.AllowCognitiveServices)
				{
					/*Parte que estaba para AllowCognitiveServices*/
					placeholderCognitiveServicesEnabled.Visible = true;
					checkboxUseCognitiveServices.Checked = settings.CognitiveServices.Enabled;
					textboxCognitiveServicesToken.Text = settings.CognitiveServices.Token;
					textboxCognitiveServicesTokenSecret.Text = settings.CognitiveServices.TokenSecret;
					liMoreServices.Visible = true;
					divMoreServices.Visible = true;

				}
				else
				{
					placeholderCognitiveServicesEnabled.Visible = false;
					checkboxUseCognitiveServices.Checked = false;
					textboxCognitiveServicesToken.Text = string.Empty;
					textboxCognitiveServicesTokenSecret.Text = string.Empty;
				}


				var videocallSectionVisible = license.Configuration.AllowAgentsToStartVideoCall && this.LoggedUserIsSuper;
				placeholderVideoCallsEnabled.Visible = videocallSectionVisible;
				divCubiq.Visible = videocallSectionVisible;
				textboxCubiqUrl.Text = settings.VideoCubiqUrl;
				textboxCubiqApiKey.Text = settings.VideoCubiqApiKey;
				textboxCubiqSecret.Text = settings.VideoCubiqSecret;
				textboxCubiqRecordingUrl.Text = settings.VideoCubiqRecordingUrl;
				

				placeholderPushNotificationsServiceBusConcurrentMessages.Visible = !license.Configuration.UseExternalServiceForIncomingMessages;

				if (license.Configuration.AllowYFlow)
				{
					placeholderAllowYFlow.Visible = true;

					liMoreServices.Visible = true;
					divMoreServices.Visible = true;
					placeholderYFlowCasesRelated.Visible = true;
					placeholderYFlowMaxConcurrentCallsPerSessionPendingMessages.Visible = this.LoggedUserIsSuper && !license.Configuration.UseExternalServiceForIncomingMessages;
					panelYFlowPendingReplyCases.Visible = this.LoggedUserIsSuper && !license.Configuration.UseExternalServiceForIncomingMessages;

					checkboxEnableYFlow.Checked = settings.YFlow.Enabled;
					textboxYFlowUrl.Text = settings.YFlow.Url;
					textboxYFlowUrlApi.Text = settings.YFlow.UrlApi;
					textboxYFlowUrlWeb.Text = settings.YFlow.UrlWeb;
					textboxYFlowTimeout.Text = settings.YFlow.Timeout.ToString();

					bool accessTokenIsValid = ValidateAccessTokenYFlow(settings.YFlow);
					spanAccessTokenOk.Attributes.CssStyle["display"] = accessTokenIsValid ? "" : "none";
					spanAccessTokenError.Attributes.CssStyle["display"] = accessTokenIsValid ? "none" : "";

					hiddenYFlowAuthenticationFailedConnection.Value = settings.YFlow.AuthenticationFailed.EmailConnection;
					textboxYFlowAuthenticationFailedSubject.Text = settings.YFlow.AuthenticationFailed.Subject;
					textboxYFlowAuthenticationFailedTemplate.Text = settings.YFlow.AuthenticationFailed.Template;
					textboxYFlowAuthenticationFailedEmails.Text = settings.YFlow.AuthenticationFailed.Emails;
					hiddenYFlowInvokeFailed.Value = settings.YFlow.InvokeFailed.EmailConnection;
					textboxYFlowInvokeFailedSubject.Text = settings.YFlow.InvokeFailed.Subject;
					textboxYFlowInvokeFailedTemplate.Text = settings.YFlow.InvokeFailed.Template;
					textboxYFlowInvokeFailedEmails.Text = settings.YFlow.InvokeFailed.Emails;
					checkboxYFlowDerivationEnabled.Checked = settings.YFlow.DerivationEnabled;
					textboxYFlowMaxMinutesForPendingMessages.Text = settings.YFlow.MaxMinutesForPendingMessages.ToString();
					dropdownlistYFlowActionAfterMaxMinutesForPendingMessages.SelectedValue = ((short) settings.YFlow.ActionAfterMaxMinutesForPendingMessages).ToString();
					textboxYFlowMaxConcurrentSessionsPendingMessages.Text = settings.YFlow.MaxConcurrentSessionsPendingMessages.ToString();
					textboxYFlowMaxConcurrentCallsPerSessionPendingMessages.Text = settings.YFlow.MaxConcurrentCallsPerSessionPendingMessages.ToString();
					hiddenYFlowPendingMessagesCallbackEndpoint.Value = string.Empty;
					if (settings.YFlow.PendingMessagesCallbackEndpoint != null)
						hiddenYFlowPendingMessagesCallbackEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.YFlow.PendingMessagesCallbackEndpoint);
					textboxMaxElapsedMinutesToCloseYFlowCases.Text = settings.Cases.MaxElapsedMinutesToCloseYFlowCases.ToString();
					checkboxInvokeYFlowWhenClosedCases.Checked = settings.Cases.InvokeYFlowWhenClosedCases;

					placeholderYFlowPendingMessagesConfig.Visible = this.LoggedUserIsSuper;
					dropdownlistYFlowPendingMessagesUseCustomCallback.SelectedValue = settings.YFlow.PendingMessagesUseCustomCallback ? "1" : "0";
					textboxYFlowPendingMessagesCustomCallbackUrl.Text = settings.YFlow.PendingMessagesCustomCallbackUrl;

					textboxYFlowPendingReplyCasesConcurrentCalls.Text = settings.YFlow.MaxConcurrentCallsPendingReplyCases.ToString();

					//Yflow Contingency
					if (Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled)
					{
						placeholderAllowYFlowContingency.Visible = true;
						accessTokenIsValid = ValidateAccessTokenYFlow(settings.YFlowContingency, true);

						textboxYFlowContingencyUrl.Text = settings.YFlowContingency.Url;
						textboxYFlowContingencyUrlApi.Text = settings.YFlowContingency.UrlApi;
						textboxYFlowContingencyUrlWeb.Text = settings.YFlowContingency.UrlWeb;
						textboxYFlowContingencyTimeout.Text = settings.YFlowContingency.Timeout.ToString();

						checkboxEnableYFlowContingency.Checked = settings.YFlowContingency.Enabled;
						spanAccessTokenContingencyOk.Attributes.CssStyle["display"] = accessTokenIsValid ? "" : "none";
						spanAccessTokenContingencyError.Attributes.CssStyle["display"] = accessTokenIsValid ? "none" : "";
					}
					else
					{
						placeholderAllowYFlowContingency.Visible = false;
					}
				}
				else
				{
					placeholderAllowYFlow.Visible = false;
					placeholderYFlowCasesRelated.Visible = false;
				}
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowChatSummary)
				{
					placeholderAllowYSmart.Visible = true;
					liMoreServices.Visible = true;
					divMoreServices.Visible = true;
					checkboxEnableYSmart.Checked = settings.YSmart.Enabled;
					textboxYSmartTimeout.Text = settings.YSmart.Timeout.ToString();
					if (!string.IsNullOrEmpty(settings.YSmart.ProjectId.ToString()))
					{
						hiddenProjectId.Value = settings.YSmart.ProjectId.ToString();
					}
					if (!string.IsNullOrEmpty(textboxTagTimeout1.Text))
					{
						settings.YSmart.TagTimeoutFailed = int.Parse(textboxTagTimeout1.Text);
					}
					else
					{
						settings.YSmart.TagTimeoutFailed = -1;
					}
				}
				else
				{
					placeholderAllowYSmart.Visible = false;
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowYUsage)
				{
					placeholderAllowYUsage.Visible = true;
					liMoreServices.Visible = true;
					divMoreServices.Visible = true;

					checkboxEnableYUsage.Checked = settings.YUsage.Enabled;
					textboxYUsageUrl.Text = settings.YUsage.Url;
					textboxYUsageUrlApi.Text = settings.YUsage.UrlApi;

                    bool tokenIsValid = ValidateYUsageToken(settings.YUsage);
					spanYUsageTokenOk.Attributes.CssStyle["display"] = tokenIsValid ? "" : "none";
					spanYUsageTokenError.Attributes.CssStyle["display"] = tokenIsValid ? "none" : "";
				}
				else
				{
					placeholderAllowYUsage.Visible = false;
				}

				textboxMaintenanceCases.Text = settings.Maintenance.DaysForCases.ToString();
				textboxMaintenanceHist.Text = settings.Maintenance.DaysForHistorical.ToString();
				textboxMaintenanceHistByInterval.Text = settings.Maintenance.DaysForHistoricalByInterval.ToString();

				checkboxUseAnnoyingUser.Checked = settings.AnnoyingEmailSettings.UseAnnoyingUser;
				if (settings.AnnoyingEmailSettings.DiscardMessagesFromAnnoyingUser)
					dropdownlistDiscardMessagesFromAnnoyingUser.SelectedValue = settings.AnnoyingEmailSettings.DiscardMessagesAndCloseCaseFromAnnoyingUser ? "2" : "1";
				else
					dropdownlistDiscardMessagesFromAnnoyingUser.SelectedValue = "0";
				checkboxAddAnnoyingUserToBlackList.Checked = settings.AnnoyingEmailSettings.AddAnnoyingUserToBlackList;
				checkboxNotifySupervisorFromScreen.Checked = settings.AnnoyingEmailSettings.NotifySupervisorFromScreen;
				textboxMaxMessagesAnnoyingUser.Text = settings.AnnoyingEmailSettings.MaxMessagesAnnoyingUser.ToString();
				checkboxMarkAnnoyingUserMessageAsVIM.Checked = settings.AnnoyingEmailSettings.MarkMessageAsVIM;
				hiddenFieldAnnoyingUserConnection.Value = settings.AnnoyingEmailSettings.EmailConnection;
				textboxAnnoyingUserEmails.Text = settings.AnnoyingEmailSettings.Emails;
				textboxAnnoyingUserEmailSubject.Text = settings.AnnoyingEmailSettings.Subject;
				textboxAnnoyingUserEmailTemplate.Text = settings.AnnoyingEmailSettings.Template;

				divPushNotifications.Visible = !license.Configuration.InTheCloud || this.LoggedUserIsSuper;
				dropdownlistPushNotificationsServiceBusProtocol.SelectedValue = settings.PushNotificationsServiceBusUseWebSockets ? "1" : "0";
				textboxPushNotificationsServiceBusConcurrentMessages.Text = settings.PushNotificationsServiceBusConcurrentMessages.ToString();
				textboxPushNotificationsServiceBusConcurrentStatuses.Text = settings.PushNotificationsServiceBusConcurrentStatuses.ToString();
				textboxPushNotificationsServiceBusConcurrentMassive.Text = settings.PushNotificationsServiceBusConcurrentMassive.ToString();
				
				if (license.Configuration.InTheCloud)
				{
					divCloudIPRestrictions.Visible = true;
					if (settings.RestrictedIPsForWeb == null || settings.RestrictedIPsForWeb.Length == 0)
						textboxCloudIPRestrictionsWeb.Text = string.Empty;
					else
						textboxCloudIPRestrictionsWeb.Text = string.Join("\n", settings.RestrictedIPsForWeb);
					if (settings.RestrictedIPsForWebAgent == null || settings.RestrictedIPsForWebAgent.Length == 0)
						textboxCloudIPRestrictionsWebAgent.Text = string.Empty;
					else
						textboxCloudIPRestrictionsWebAgent.Text = string.Join("\n", settings.RestrictedIPsForWebAgent);

					panelCloudHeaders.Visible = this.LoggedUserIsSuper;
					hiddenCloudHeadersToAdd.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.CloudHeadersToAdd);
					hiddenCloudHeadersToRemove.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.CloudHeadersToRemove);
				}
				else
				{
					divCloudIPRestrictions.Visible = false;
					panelCloudHeaders.Visible = false;
				}

				if (license.Configuration.InTheCloud)
				{
					var item = dropdownlistPasswordStrength.Items.FindByValue("0");
					dropdownlistPasswordStrength.Items.Remove(item);
					item = dropdownlistPasswordStrength.Items.FindByValue("1");
					dropdownlistPasswordStrength.Items.Remove(item);
					item = dropdownlistPasswordStrength.Items.FindByValue("2");
					dropdownlistPasswordStrength.Items.Remove(item);
				}

				dropdownlistPasswordStrength.SelectedValue = settings.MinimumPasswordStrength.ToString();
				textboxUserPasswordExpireDay.Text = settings.UserPasswordExpireDay.ToString();
				checkboxUserPasswordRepeat.Checked = settings.UserPasswordRepeat;
				checkboxMandatory2FAUser.Checked = settings.UserMandatory2FA;
				textboxUserPasswordRepeat.Text = settings.UserPasswordRepeatAmount.ToString();
				textboxUserPasswordWrongBlock.Text = settings.UserPasswordWrongBlock.ToString();
				textboxUserPasswordWrongsCaptha.Text = settings.UserPasswordWrongsCaptha.ToString();
				textboxDaysInactiveUserBlock.Text = settings.DaysInactiveUserBlock.ToString();
				checkboxUserRefreshPassword.Checked = settings.UserPasswordRefresh;
				checkboxUserPasswordFirstChange.Checked = settings.UserPasswordFirstChange;
				textboxUserRegex.Text = settings.UserPasswordValidationRegex;
				textboxMessageUserRegex.Text = settings.UserPasswordMessageRegex;


				if (license.Configuration.InTheCloud)
				{
					var item = dropdownlistAgentPasswordStrength.Items.FindByValue("0");
					dropdownlistAgentPasswordStrength.Items.Remove(item);
					item = dropdownlistAgentPasswordStrength.Items.FindByValue("1");
					dropdownlistAgentPasswordStrength.Items.Remove(item);
					item = dropdownlistAgentPasswordStrength.Items.FindByValue("2");
					dropdownlistAgentPasswordStrength.Items.Remove(item);
				}

				dropdownlistAgentPasswordStrength.SelectedValue = settings.MinimumAgentPasswordStrength.ToString();
				textboxAgentPasswordExpireDay.Text = settings.AgentPasswordExpireDay.ToString();
				checkboxAgentPasswordRepeat.Checked = settings.AgentPasswordRepeat;
				textboxAgentPasswordRepeat.Text = settings.AgentPasswordRepeatAmount.ToString();
				checkboxMandatory2FAAgent.Checked = settings.AgentMandatory2FA;
				textboxAgentPasswordWrongBlock.Text = settings.AgentPasswordWrongsBlock.ToString();
				textboxAgentPasswordWrongsCaptha.Text = settings.AgentPasswordWrongsCaptha.ToString();
				textboxDaysInactiveAgentBlock.Text = settings.DaysInactiveAgentBlock.ToString();
				checkboxAgentCanChangePassword.Checked = settings.AgentCanChangePassword;
				checkboxAgentRefreshPassword.Checked = settings.AgentPasswordRefresh;
				checkboxAgentPasswordFirstChange.Checked = settings.AgentPasswordFirstChange;
				textboxAgentRegex.Text = settings.AgentPasswordValidationRegex;
				textboxMessageAgentRegex.Text = settings.AgentPasswordMessageRegex;

				if (license.Configuration.InTheCloud)
				{
					panelAlertsOtherProblems.Visible = this.LoggedUserIsSuper;
					panelAttachments.Visible = this.LoggedUserIsSuper;
					panelMaintenance.Visible = this.LoggedUserIsSuper;

					if (string.IsNullOrEmpty(settings.AttachmentsRoute))
					{
						try
						{
							var currentPath = Server.MapPath("~");
							var currentDirectory = new DirectoryInfo(currentPath);
							var currentParentDirectory = currentDirectory.Parent;
							var attachmentsPath = Path.Combine(currentParentDirectory.FullName, "Attachments");
							var attachmentsDirectory = new DirectoryInfo(attachmentsPath);
							if (!attachmentsDirectory.Exists)
								attachmentsDirectory.Create();
							settings.AttachmentsRoute = textboxAttachmentsRoute.Text = attachmentsDirectory.FullName;
						}
						catch { }
					}

					settings.Service.EmailLicenseExpired.Emails = textboxLicenseExpiredEmails.Text = "<EMAIL>";
					settings.EmailDatabaseProblems.Emails = textboxDatabaseProblemsEmails.Text = "<EMAIL>";
					settings.EmailOutOfMemory.Emails = textboxOutOfMemoryEmails.Text = "<EMAIL>";
					settings.AttachmentsMinimumFreeSpace = 10;
					settings.OutOfDiskSpaceForAttachments.Emails = textboxOutOfDiskSpaceForAttachmentsEmails.Text = "<EMAIL>";

					textboxAttachmentsRoute.Text = settings.AttachmentsRoute;
				}

				if (this.LoggedUserIsSuper)
				{
					panelTimeZoneConfiguration.Visible = true;
					literalTimeZoneServer.Text = DomainModel.SystemSettings.Instance.LocalTimeZone.DisplayName;
					literalTimeZoneServerDST.Visible = DomainModel.SystemSettings.Instance.LocalTimeZone.IsDaylightSavingTime(DateTimeOffset.Now);
					hiddenDefaultTimeZone.Value = settings.DefaultTimeZone.Id;
					if (license.Configuration.AllowToConfigureTimeZones)
					{
						panelTimeZoneConsolidation.Visible = true;
						if (settings.TimeZonesToConsolide != null)
						{
							hiddenTimeZonesToConsolide.Value = string.Join(",", settings.TimeZonesToConsolide.Select(tz => tz.Id));
						}
						else
						{
							hiddenTimeZonesToConsolide.Value = string.Empty;
						}
					}
					else
					{
						panelTimeZoneConsolidation.Visible = false;
					}
				}
				else
				{
					panelTimeZoneConfiguration.Visible = false;
				}

				textboxMinutesPredictedAht.Text = settings.MinutesPredictedAht.ToString();
				textboxSecondsEwt.Text = settings.SecondsEwt.ToString();
				textboxAsaBase.Text = settings.ASADefaultValue.ToString();
				checkboxASAPersonalized.Checked = settings.AllowToSetASAValueByDefault;
			}
			else
			{
				requiredfieldvalidatorBitLyAcessToken.Enabled = checkboxBitLyCustomShortener.Checked;

				if (!checkboxBitLyCustomShortener.Checked)
				{
					textboxBitLyAccessToken.Text = settings.BitLy.AccessToken;
				}

				if (license.Configuration.AllowYFlow)
				{
					bool accessTokenIsValid = ValidateAccessTokenYFlow(settings.YFlow);
					spanAccessTokenOk.Attributes.CssStyle["display"] = accessTokenIsValid ? "" : "none";
					spanAccessTokenError.Attributes.CssStyle["display"] = accessTokenIsValid ? "none" : "";
				}

				if (license.Configuration.AllowYUsage)
				{
					bool tokenIsValid = ValidateYUsageToken(settings.YUsage);
					spanYUsageTokenOk.Attributes.CssStyle["display"] = tokenIsValid ? "" : "none";
					spanYUsageTokenError.Attributes.CssStyle["display"] = tokenIsValid ? "none" : "";
				}

			}

			var emails = DomainModel.SystemSettings.Instance.GetEmailsConnections().OrderBy(item => item.AsDictionary()["Name"]);
			this.RegisterJsonVariable("emails", emails);
		}

		protected bool ValidateUser()
		{
			if (!this.LoggedUser.HasPermission(Permissions.SystemSettings))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private async Task SaveAsync()
		{
			if (Page.IsValid)
			{
				try
				{
					var changedtheme = false;

					var license = Licensing.LicenseManager.Instance.License.Configuration;

					var settings = DomainModel.SystemSettings.Instance;
					var currentData = settings.Retrieve();

					settings.DefaultLanguage = dropdownlistDefaultLanguage.SelectedValue;
					if (LoggedUserIsSuper)
					{
						if (settings.Theme == null ||
							!settings.Theme.Equals(dropdownlistTheme.SelectedValue))
						{
							settings.Theme = dropdownlistTheme.SelectedValue;
							changedtheme = true;
						}
					}

					settings.MinimumPasswordStrength = byte.Parse(dropdownlistPasswordStrength.SelectedValue);
					settings.UserPasswordExpireDay = int.Parse(textboxUserPasswordExpireDay.Text);
					settings.UserPasswordRepeat = checkboxUserPasswordRepeat.Checked;
					settings.UserMandatory2FA = checkboxMandatory2FAUser.Checked;
					settings.UserPasswordRepeatAmount = int.Parse(textboxUserPasswordRepeat.Text);
					settings.UserPasswordWrongBlock = int.Parse(textboxUserPasswordWrongBlock.Text);
					settings.UserPasswordWrongsCaptha = int.Parse(textboxUserPasswordWrongsCaptha.Text);
					settings.DaysInactiveUserBlock = int.Parse(textboxDaysInactiveUserBlock.Text);
					settings.UserPasswordRefresh = checkboxUserRefreshPassword.Checked;
					settings.UserPasswordFirstChange = checkboxUserPasswordFirstChange.Checked;
					settings.UserPasswordValidationRegex = textboxUserRegex.Text;
					settings.UserPasswordMessageRegex = textboxMessageUserRegex.Text;
					settings.MinimumAgentPasswordStrength = byte.Parse(dropdownlistAgentPasswordStrength.SelectedValue);
					settings.AgentPasswordExpireDay = int.Parse(textboxAgentPasswordExpireDay.Text);
					settings.AgentPasswordRepeat = checkboxAgentPasswordRepeat.Checked;
					settings.AgentMandatory2FA = checkboxMandatory2FAAgent.Checked;
					settings.AgentPasswordRepeatAmount = int.Parse(textboxAgentPasswordRepeat.Text);
					settings.AgentPasswordWrongsBlock = int.Parse(textboxAgentPasswordWrongBlock.Text);
					settings.AgentPasswordWrongsCaptha = int.Parse(textboxAgentPasswordWrongsCaptha.Text);
					settings.DaysInactiveAgentBlock = int.Parse(textboxDaysInactiveAgentBlock.Text);
					settings.AgentCanChangePassword = checkboxAgentCanChangePassword.Checked;
					settings.AgentPasswordRefresh = checkboxAgentRefreshPassword.Checked;
					settings.AgentPasswordFirstChange = checkboxAgentPasswordFirstChange.Checked;
					settings.AgentPasswordValidationRegex = textboxAgentRegex.Text;
					settings.AgentPasswordMessageRegex = textboxMessageAgentRegex.Text;

					if (license.AllowToConfigureACDBalancing)
					{
						settings.UseACDBalancing = checkboxUseACDBalancing.Checked;
						if (settings.UseACDBalancing)
						{
							settings.ACDBalancingWithQueueLevels = short.Parse(dropdownlistACDBalancingWithQueueLevels.SelectedValue) == 0;
							if (settings.ACDBalancingWithQueueLevels)
								settings.ACDBalancingWithQueueLevelsWorking = checkboxACDBalancingWithQueueLevelsWorking.Checked;
							else
								settings.ACDBalancingWithQueueLevelsWorking = false;
						}
					}

					settings.WebAgentURL = textboxWebAgentURL.Text.Trim();
					settings.WebAgentVersion = hiddenWebAgentVersion.Value;
					settings.WebAgentAllowOutdatedLogins = checkboxWebAgentAllowOutdatedLogins.Checked;

					if (license.AllowedServiceTypes.Contains(DomainModel.ServiceTypes.Chat))
					{
						settings.Chat.CloudSameServer = dropdownlistChatCloudSameServer.SelectedValue == "1";
						if (!settings.Chat.CloudSameServer)
						{
							settings.Chat.CloudOtherServer = textboxChatCloudOtherServer.Text;
							if (license.InTheCloud)
								settings.Chat.CloudOtherServerPortYSocialOverHttps = true;
							else
								settings.Chat.CloudOtherServerPortYSocialOverHttps = dropdownlisCloudOtherServerPortYSocialOverHttps.SelectedValue.Equals("1");
						}
						else
						{
							settings.Chat.CloudOtherServer = null;
							settings.Chat.CloudOtherServerPortYSocialOverHttps = true;
						}
						settings.Chat.CloudPort = short.Parse(textboxChatCloudPort.Text);
						settings.Chat.CloudPortYSocial = short.Parse(textboxChatCloudPortForYSocial.Text);

						if (license.InTheCloud)
						{
							settings.Chat.CloudPortOverHttps = true;
						}
						else
						{
							settings.Chat.CloudPortOverHttps = dropdownlisCloudPortOverHttps.SelectedValue.Equals("1");

							if (license.UseSingleChatServiceOnPremise)
							{
								settings.Chat.ServiceStartedEmail.Emails = textboxChatStartedDestinationEmails.Text;
								settings.Chat.ServiceStartedEmail.Subject = textboxChatStartedEmailSubject.Text;
								settings.Chat.ServiceStartedEmail.Template = textboxChatStartedEmailTemplate.Text;
								settings.Chat.ServiceStoppedEmail.Emails = textboxChatStoppedDestinationEmails.Text;
								settings.Chat.ServiceStoppedEmail.Subject = textboxChatStoppedEmailSubject.Text;
								settings.Chat.ServiceStoppedEmail.Template = textboxChatStoppedEmailTemplate.Text;
								settings.Chat.ServiceCrashedEmail.Emails = textboxChatCrashedDestinationEmails.Text;
								settings.Chat.ServiceCrashedEmail.Subject = textboxChatCrashedEmailSubject.Text;
								settings.Chat.ServiceCrashedEmail.Template = textboxChatCrashedEmailTemplate.Text;
							}
						}
					}

					if (license.AllowedServiceTypes.Any(s => s == ServiceTypes.Telegram))
					{
						if (this.LoggedUserIsSuper)
						{
							settings.Telegram.UrlRtNotifications = textboxTelegramUrlRtNotifications.Text.Trim();
							if (!settings.Telegram.UrlRtNotifications.EndsWith("/api/telegram"))
							{
								if (!settings.Telegram.UrlRtNotifications.EndsWith("/"))
									settings.Telegram.UrlRtNotifications += "/api/telegram";
								else
									settings.Telegram.UrlRtNotifications += "api/telegram";
							}
							else if (!settings.Telegram.UrlRtNotifications.EndsWith("api/telegram"))
							{
								settings.Telegram.UrlRtNotifications += "api/telegram";
							}
						}
					}

					if (license.AllowedServiceTypes.Any(s => s == ServiceTypes.WhatsApp))
					{
						if (this.LoggedUserIsSuper)
						{
							settings.Whatsapp.UrlRtNotifications = textboxWhatsappUrlRtNotifications.Text.Trim();
							if (!settings.Whatsapp.UrlRtNotifications.EndsWith("/api/whatsapp"))
							{
								if (!settings.Whatsapp.UrlRtNotifications.EndsWith("/"))
									settings.Whatsapp.UrlRtNotifications += "/api/whatsapp";
								else
									settings.Whatsapp.UrlRtNotifications += "api/whatsapp";
							}
							else if (!settings.Whatsapp.UrlRtNotifications.EndsWith("api/whatsapp"))
							{
								settings.Whatsapp.UrlRtNotifications += "api/whatsapp";
							}
						}

						settings.Whatsapp.DefaultInternationCode = int.Parse(dropdownlistWhatsappDefaultInternationCode.SelectedValue);
						settings.Whatsapp.CatalogApp = dropdownlistWhatsappServiceCatalogApp.SelectedValue;
						settings.Whatsapp.EmbeddedSignUpApp = dropdownlistWhatsappEmbeddedSignUpApp.SelectedValue;
						settings.Whatsapp.VoiceCallsOnCallBehaviour = (DomainModel.ServiceSettings.WhatsappSettings.VoiceCallsAgentsBehaviours) short.Parse(dropdownlistWhatsAppVoiceCallsOnCallBehaviour.SelectedValue);

						if (this.LoggedUserIsSuper)
						{
							settings.Whatsapp.VoiceCallsIceServers = textboxWhatsAppServiceVoiceCallsIceServers.Text;
							settings.Whatsapp.VoiceCallsRecordingHostname = textboxWhatsAppServiceVoiceCallsRecordingHostname.Text;
							settings.Whatsapp.VoiceCallsRecordingPort = ushort.Parse(textboxWhatsAppServiceVoiceCallsRecordingPort.Text);
							settings.Whatsapp.VoiceCallsRecordingManagementJwtSecret = textboxWhatsAppServiceVoiceCallsRecordingJWTSecret.Text;
							settings.Whatsapp.VoiceCallsRecordingDownloadHostname = textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname.Text;
						}
					}

					settings.EncryptMessages = checkboxEncryptMessages.Checked;
					settings.MaxAssignableMessagesPerUser = short.Parse(textboxMaxAssignableMessagesPerUser.Text);
					settings.AutoMarkAsReadMessages = checkboxAutoMarkAsReadMessages.Checked;
					settings.UseSentDate = checkboxUseSentDate.Checked;
					settings.MarkWhiteListMessagesAsVIM = checkboxMarkWhiteListMessagesAsVIM.Checked;
					settings.PrioritizeVIMOverQueueLevel = dropdownlistPrioritizeVIMOverQueueLevel.SelectedValue.Equals("0");
					settings.SessionTimeOut = short.Parse(textboxSessionTimeOut.Text);
					settings.AgentHistoricMaxMessages = short.Parse(textboxAgentHistoricMaxMessages.Text);
					settings.AllowAgentToBlockUsers = checkboxAllowAgentToBlockUsers.Checked;
					settings.AllowAgentsToReturnMessagesToQueue = checkboxAllowAgentsToReturnMessagesToQueue.Checked;
					if (settings.AllowAgentsToReturnMessagesToQueue)
					{
						settings.AgentMustEnterReturnToQueueReason = checkboxAgentMustEnterReturnToQueueReason.Checked;
						settings.MaximumNumberOfTimesMessageCanBeReturned = short.Parse(textboxMaximumNumberOfTimesMessageCanBeReturned.Text);
					}
					else
					{
						settings.AgentMustEnterReturnToQueueReason = false;
						settings.MaximumNumberOfTimesMessageCanBeReturned = 0;
					}

					settings.AllowAgentsToSelectQueueOnReturnToQueue = checkboxAllowAgentsToSelectQueueOnReturnToQueue.Checked;
					if (settings.AllowAgentsToSelectQueueOnReturnToQueue || settings.AllowAgentsToReturnMessagesToQueue)
						settings.AllowAgentsToReturnMessagesWithRelatedMessagesToQueue = checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.Checked;

					settings.Cases.CheckLastQueueOfOpenCase = checkboxCheckLastQueueOfOpenCase.Checked;
					settings.Cases.IgnoreLastQueueForSLMovedMessage = checkboxIgnoreLastQueueForSLMovedMessage.Checked;
					//settings.Cases.AllowAgentsToMarkCasesAsPending = checkboxAllowAgentsToMarkCasesAsPending.Checked;
					settings.Cases.AllowToAddMessagesToCasesWithMessagesInQueue = checkboxAllowToAddMessagesToCasesWithMessagesInQueue.Checked;
					settings.Cases.AllowNotificationWhenClosePendingCase = checkboxAllowNotificationWhenClosePendingCase.Checked;
					settings.CheckLastQueueByTime = checkboxCheckLastQueueByTime.Checked;
					if (settings.CheckLastQueueByTime)
					{
						settings.CheckLastQueueByTimeValue = int.Parse(textboxLastQueueByTime.Text);
					}
					else
					{
						settings.CheckLastQueueByTimeValue = 0;
					}

					ushort maxElapsedMinutesToCloseCases = ushort.Parse(textboxMaxElapsedMinutesToCloseCases.Text);
					if (maxElapsedMinutesToCloseCases == 0)
					{
						settings.Cases.MaxElapsedMinutesToCloseCases = 1;
					}
					else if (maxElapsedMinutesToCloseCases > 43200)
					{
						settings.Cases.MaxElapsedMinutesToCloseCases = 43200;
					}
					else
					{
						settings.Cases.MaxElapsedMinutesToCloseCases = maxElapsedMinutesToCloseCases;
					}

					if (checkboxReplyInCloseCase.Checked)
					{
						settings.Cases.AutoReplyInCloseCaseText = textboxAutoReplyInCloseCaseText.Text;
						settings.Cases.AllowToAutoReplyCloseCases = true;
					}
					else
					{
						settings.Cases.AllowToAutoReplyCloseCases = false;
					}

					if (!string.IsNullOrEmpty(textboxTagCloseCase.Text))
					{
						settings.Cases.TagOnAutoCloseCase = int.Parse(textboxTagCloseCase.Text);
					}
					else
					{
						settings.Cases.TagOnAutoCloseCase = -1;
					}

					var oldCasesCloseConcurrent = settings.Cases.CasesCloseConcurrent;
					if (this.LoggedUserIsSuper)
						settings.Cases.CasesCloseConcurrent = int.Parse(textboxCasesCloseConcurrent.Text);

					ushort maxElapsedMinutesToCloseHsmCases = ushort.Parse(textboxMaxElapsedMinutesToCloseHsmCases.Text);

					if (maxElapsedMinutesToCloseHsmCases == 0)
					{
						settings.Cases.MaxElapsedMinutesToCloseHsmCases = 1;
					}
					else if (maxElapsedMinutesToCloseHsmCases > 1440)
					{
						settings.Cases.MaxElapsedMinutesToCloseHsmCases = 1440;
					}
					else
					{
						settings.Cases.MaxElapsedMinutesToCloseHsmCases = maxElapsedMinutesToCloseHsmCases;
					}

					if (!string.IsNullOrEmpty(textboxTagOnHsmCases.Text))
					{
						settings.Cases.TagOnAutoCloseHsmCases = int.Parse(textboxTagOnHsmCases.Text);
					}
					else
					{
						settings.Cases.TagOnAutoCloseHsmCases = -1;
					}

					settings.AgentMustEnterDiscardReason = checkboxAgentMustEnterDiscardReason.Checked;
					settings.Cases.TagCasesOnStart = Enum.TryParse<RequiredTagging>(dropdownlistTagCasesOnStart.SelectedValue, out RequiredTagging selectedOnStart) ? selectedOnStart : RequiredTagging.NotRequired;
					settings.Cases.TagCasesOnClose = Enum.TryParse<RequiredTagging>(dropdownlistTagCasesOnClose.SelectedValue, out RequiredTagging selectedOnClose) ? selectedOnClose : RequiredTagging.NotRequired;
					settings.Cases.TagOutgoing = checkboxTagOutgoing.Checked;

					if (checkboxTagOutgoing.Checked)
					{
						settings.Cases.ImportantTagOutgoing = checkboxImportantTagOutgoing.Checked;
					}
					else
					{
						settings.Cases.ImportantTagOutgoing = false;
					}

					if (checkboxImportantTag.Checked)
					{
						settings.Cases.ImportantTag = checkboxImportantTag.Checked;
					}
					else
					{
						settings.Cases.ImportantTag = false;
					}

					settings.Cases.TagCasesOnDiscard = Enum.TryParse<RequiredTagging>(dropdownlistTagCasesOnDiscard.SelectedValue, out RequiredTagging selectedOnDiscard) ? selectedOnDiscard : RequiredTagging.NotRequired;
					settings.Cases.AlwaysUpdateCase = checkboxAlwaysUpdateCase.Checked;

					settings.FilterEmailSettings.Subject = textboxFilterEmailSubject.Text;
					settings.FilterEmailSettings.Template = textboxFilterEmailTemplate.Text;
					if (string.IsNullOrEmpty(textboxFilterEmailEmails.Text))
						settings.FilterEmailSettings.Emails = null;
					else
						settings.FilterEmailSettings.Emails = textboxFilterEmailEmails.Text;
					settings.FilterEmailSettings.SendToAdministrators = checkboxFilterEmailSendToAdministrators.Checked;
					settings.FilterEmailSettings.SendToSupervisors = checkboxFilterEmailSendToSupervisors.Checked;

					// --> Palabras globales a excluir en el corrector del agente

					settings.AllowForwardAction = checkboxAllowForwardAction.Checked;
					settings.ForwardOutsideDomainAvailable = checkboxForwardOutsideDomainAvailable.Checked;
					if (!settings.ForwardOutsideDomainAvailable)
						settings.AvailableDomainsToForward = textboxAvailableDomains.Text;
					settings.ForwardSettings.FacebookSubject = textboxFacebookSubject.Text;
					settings.ForwardSettings.FacebookMessengerSubject = textboxFacebookMessengerSubject.Text;
					settings.ForwardSettings.TwitterSubject = textboxTwitterSubject.Text;
					settings.ForwardSettings.WhatsappSubject = textboxWhatsappSubject.Text;
					settings.ForwardSettings.TelegramSubject = textboxTelegramSubject.Text;
					settings.ForwardSettings.SMSSubject = textboxSMSSubject.Text;
					settings.ForwardSettings.SkypeSubject = textboxSkypeSubject.Text;
					settings.ForwardSettings.InstagramSubject = textboxInstagramSubject.Text;
					settings.ForwardSettings.ChatSubject = textboxChatSubject.Text;
					settings.ForwardSettings.MailMaskSubject = textboxMailMaskSubject.Text;
					settings.ForwardSettings.MailMaskBody = Server.HtmlDecode(textboxMailMaskBody.Text);
					settings.FavoriteMails = textboxFavoriteMails.Text;

					settings.EnableCapi = checkboxEnableCapi.Checked;

					settings.AttachmentsRoute = textboxAttachmentsRoute.Text;
					if (settings.AttachmentsRoute.EndsWith(@"\"))
						settings.AttachmentsRoute = settings.AttachmentsRoute.TrimEnd('\\');
					settings.AttachmentsMinimumFreeSpace = int.Parse(textboxAttachmentsMinimumFreeSpace.Text);
					settings.OutOfDiskSpaceForAttachments.EmailConnection = hiddenFieldOutOfDiskSpaceForAttachmentsConnection.Value;
					settings.OutOfDiskSpaceForAttachments.Subject = textboxOutOfDiskSpaceForAttachmentsEmailSubject.Text;
					settings.OutOfDiskSpaceForAttachments.Emails = textboxOutOfDiskSpaceForAttachmentsEmails.Text;
					settings.OutOfDiskSpaceForAttachments.Template = textboxOutOfDiskSpaceForAttachmentsEmailTemplate.Text;

					settings.OutgoingMessagesEnabled = checkboxOutgoingMessagesEnabled.Checked;

					settings.Twitter.AllowMentionInMultipleServices = checkboxTwitterAllowMentionInMultipleServices.Checked;

					settings.Service.EmailLicenseExpired.EmailConnection = hiddenLicenseExpiredConnection.Value;
					settings.Service.EmailLicenseExpired.Emails = textboxLicenseExpiredEmails.Text;
					settings.Service.EmailLicenseExpired.Subject = textboxLicenseExpiredEmailSubject.Text;
					settings.Service.EmailLicenseExpired.Template = textboxLicenseExpiredEmailTemplate.Text;
					settings.EmailDatabaseProblems.Emails = textboxDatabaseProblemsEmails.Text;
					settings.EmailDatabaseProblems.EmailConnection = hiddenFieldDatabaseProblemsConnection.Value;
					settings.EmailDatabaseProblems.Subject = textboxDatabaseProblemsEmailSubject.Text;
					settings.EmailDatabaseProblems.Template = textboxDatabaseProblemsEmailTemplate.Text;
					settings.EmailOutOfMemory.EmailConnection = hiddenFieldOutOfMemoryConnection.Value;
					settings.EmailOutOfMemory.Emails = textboxOutOfMemoryEmails.Text;
					settings.EmailOutOfMemory.Subject = textboxOutOfMemoryEmailSubject.Text;
					settings.EmailOutOfMemory.Template = textboxOutOfMemoryEmailTemplate.Text;

					if (license.InTheCloud)
					{
						settings.Service.EmailLicenseExpired.Emails = "<EMAIL>";
						settings.EmailDatabaseProblems.Emails = "<EMAIL>";
						settings.EmailOutOfMemory.Emails = "<EMAIL>";
						settings.AttachmentsMinimumFreeSpace = 10;
						settings.OutOfDiskSpaceForAttachments.Emails = "<EMAIL>";
					}

					settings.AgentCreatedLoginInformation.EmailConnection = hiddenFieldAgentCreatedLoginInformationConnection.Value;
					settings.AgentCreatedLoginInformation.Subject = textboxAgentCreatedLoginInformationEmailSubject.Text;
					settings.AgentCreatedLoginInformation.Template = textboxAgentCreatedLoginInformationEmailTemplate.Text;
					settings.AgentPasswordChanged.EmailConnection = hiddenFieldAgentPasswordChangedConnection.Value;
					settings.AgentPasswordChanged.Subject = textboxAgentPasswordChangedEmailSubject.Text;
					settings.AgentPasswordChanged.Template = textboxAgentPasswordChangedEmailTemplate.Text;

					// annoyingUser
					settings.AnnoyingEmailSettings.UseAnnoyingUser = checkboxUseAnnoyingUser.Checked;

					if (settings.AnnoyingEmailSettings.UseAnnoyingUser)
					{
						settings.AnnoyingEmailSettings.AddAnnoyingUserToBlackList = checkboxAddAnnoyingUserToBlackList.Checked;
						settings.AnnoyingEmailSettings.DiscardMessagesFromAnnoyingUser = !dropdownlistDiscardMessagesFromAnnoyingUser.SelectedValue.Equals("0");
						settings.AnnoyingEmailSettings.DiscardMessagesAndCloseCaseFromAnnoyingUser = dropdownlistDiscardMessagesFromAnnoyingUser.SelectedValue.Equals("2");
						settings.AnnoyingEmailSettings.MarkMessageAsVIM = checkboxMarkAnnoyingUserMessageAsVIM.Checked;
						settings.AnnoyingEmailSettings.NotifySupervisorFromScreen = checkboxNotifySupervisorFromScreen.Checked;
						settings.AnnoyingEmailSettings.MaxMessagesAnnoyingUser = int.Parse(textboxMaxMessagesAnnoyingUser.Text);
						settings.AnnoyingEmailSettings.EmailConnection = hiddenFieldAnnoyingUserConnection.Value;
						settings.AnnoyingEmailSettings.Emails = textboxAnnoyingUserEmails.Text;
						settings.AnnoyingEmailSettings.Subject = textboxAnnoyingUserEmailSubject.Text;
						settings.AnnoyingEmailSettings.Template = textboxAnnoyingUserEmailTemplate.Text;
					}

					//Quito UseAsDefault en todas las conexiones
					if (DomainModel.SystemSettings.Instance.EmailConnections != null &&
						DomainModel.SystemSettings.Instance.EmailConnections.Count > 0)
					{
						foreach (var email in DomainModel.SystemSettings.Instance.EmailConnections)
						{
							if (email.Value.UseAsDefault)
							{
								email.Value.UseAsDefault = false;
							}
						}
					}

					//En caso seleccionar una casilla del listado marcamos la misma por default
					if (!string.IsNullOrEmpty(hiddenFieldDefaultEmailConnection.Value) &&
						Guid.TryParse(hiddenFieldDefaultEmailConnection.Value, out Guid emailId))
					{
						if (DomainModel.SystemSettings.Instance.EmailConnections.ContainsKey(emailId))
						{
							DomainModel.SystemSettings.Instance.EmailConnections[emailId].UseAsDefault = true;

							string convertedConnection = JsonConvert.SerializeObject(DomainModel.SystemSettings.Instance.EmailConnections[emailId]);
							DomainModel.SystemSettings.Instance.UpdateDefaultEmailConnection(convertedConnection);

							settings.EmailConnection.Enabled = true;
						}
					}
					else
					{
						settings.EmailConnection.Enabled = false;
					}

					settings.AuthenticationType = (AuthenticationTypes) short.Parse(dropdownlistAuthenticationType.SelectedValue);
					settings.LDAP.UseLDAP = checkboxLdapUseLdap.Checked;
					if (checkboxLdapUseLdap.Checked)
					{
						settings.LDAP.Server = textboxLdapServer.Text;
						settings.LDAP.Port = int.Parse(textboxLdapPort.Text);
						settings.LDAP.RootDN = textboxLdapSearchDN.Text;
						settings.LDAP.User = textboxLdapUser.Text;
						if (checkboxLdapPassword.Checked)
						{
							using (var password = new System.Security.SecureString())
							{
								foreach (var @char in textboxLdapPassword.Text)
									password.AppendChar(@char);
								password.MakeReadOnly();
								settings.LDAP.Password = password;
							}
						}

						settings.LDAP.AllowCreateLocalUsers = checkboxLdapLocalUsers.Checked;
						settings.LDAP.AllowCreateLocalAgents = checkboxLdapLocalAgents.Checked;

						if (checkboxLDAPUseConfigurationParams.Checked)
						{
							settings.LDAP.UseConfigurationParams = true;
							settings.LDAP.ConfigurationParamFirstName = textboxLDAPConfigurationFirstName.Text;
							settings.LDAP.ConfigurationParamLastName = textboxLDAPConfigurationLastName.Text;
							settings.LDAP.ConfigurationParamUserName = textboxLDAPConfigurationUserName.Text;
							settings.LDAP.ConfigurationParamEmail = textboxLDAPConfigurationEmail.Text;
							settings.LDAP.ConfigurationParamLDAP = textboxLDAPConfigurationLDAP.Text;
						}
						else
						{
							settings.LDAP.UseConfigurationParams = false;
							settings.LDAP.ConfigurationParamFirstName = string.Empty;
							settings.LDAP.ConfigurationParamLastName = string.Empty;
							settings.LDAP.ConfigurationParamUserName = string.Empty;
							settings.LDAP.ConfigurationParamEmail = string.Empty;
							settings.LDAP.ConfigurationParamLDAP = string.Empty;
							textboxLDAPConfigurationFirstName.Text = string.Empty;
							textboxLDAPConfigurationLastName.Text = string.Empty;
							textboxLDAPConfigurationUserName.Text = string.Empty;
							textboxLDAPConfigurationEmail.Text = string.Empty;
							textboxLDAPConfigurationLDAP.Text = string.Empty;
						}

						settings.LDAP.UserSearchFilter = textboxLdapUserSearchFilter.Text;
						settings.LDAP.Secure = checkboxLdapUseSecureAuthentication.Checked;

						placeholderLdapPassword.Visible = true;
						checkboxLdapPassword.Checked = false;
						checkboxLdapPassword.Visible = true;
						literalLdapPasswordChangePasswordTitle.Visible = true;
						literalLdapPasswordNewPasswordTitle.Visible = false;
						textboxLdapPassword.Text = string.Empty;
						labelLdapPassword.Text = new string('•', settings.LDAP.PasswordLength);
					}
					else
					{
						placeholderLdapPassword.Visible = false;
						checkboxLdapPassword.Visible = false;
						literalLdapPasswordChangePasswordTitle.Visible = false;
						literalLdapPasswordNewPasswordTitle.Visible = true;

						checkboxLDAPUseConfigurationParams.Checked = false;
						textboxLDAPConfigurationFirstName.Text = string.Empty;
						textboxLDAPConfigurationLastName.Text = string.Empty;
						textboxLDAPConfigurationUserName.Text = string.Empty;
						textboxLDAPConfigurationEmail.Text = string.Empty;
						textboxLDAPConfigurationLDAP.Text = string.Empty;
					}

					settings.GoogleAuth.Enabled = checkboxGoogleAuthEnabled.Checked;
					settings.GoogleAuth.HostedDomain = textboxGoogleAuthHostedDomain.Text;
					settings.GoogleAuth.UseCustom = checkboxGoogleAuthUseCustom.Checked;
					settings.GoogleAuth.ClientId = textboxGoogleAuthClientID.Text;
					settings.GoogleAuth.ClientSecret = textboxGoogleAuthClientSecret.Text;
					settings.GoogleAuth.AllowCreateLocalUsers = checkboxGoogleAuthLocalUsers.Checked;
					settings.GoogleAuth.AllowCreateLocalAgents = checkboxGoogleAuthLocalAgents.Checked;

					settings.SamlAuth.Enabled = checkboxKeycloakAuthEnabled.Checked;
					settings.SamlAuth.ClientEndpoint = textboxKeycloakEndpoint.Text;
					settings.SamlAuth.RealmName = textboxKeycloakRealmName.Text;
					settings.SamlAuth.ClientName = textboxKeycloakClientName.Text;
					settings.SamlAuth.SamlButtonText = textboxSamlButtonText.Text;
					settings.SamlAuth.AllowCreateLocalUsers = checkboxSamlLocalUsers.Checked;
					settings.SamlAuth.AllowCreateLocalAgents = checkboxSamlLocalAgents.Checked;

					if (checkboxBitLyCustomShortener.Checked)
					{
						settings.BitLy.UseCustom = true;
						settings.BitLy.AccessToken = textboxBitLyAccessToken.Text;
					}
					else
					{
						settings.BitLy.UseCustom = false;
					}

					settings.DeleteNotifications = int.Parse(textboxDeleteNotifications.Text);

					settings.GenerateDailyReport = checkboxGenerateDailyReport.Checked;

					if (settings.GenerateDailyReport && this.LoggedUserIsSuper && license.SaveReportsInStorage)
					{
						settings.DailyReportsDeleteLocalFiles = checkboxDailyReportsDeleteLocalCopy.Checked;
					}

					settings.DailyReportsToMantain = int.Parse(textboxDailyReportsToMantain.Text);
					settings.ScheduledReportsToMantain = int.Parse(textboxScheduleReportsToMantain.Text);
					settings.EmailDailyReports.Subject = textboxDailyReportsEmailSubject.Text;
					settings.EmailDailyReports.Emails = textboxDailyReportsEmails.Text;
					settings.EmailDailyReports.Template = textboxDailyReportsEmailTemplate.Text;
					settings.DailyReportsFormat = (DomainModel.Reports.Export.ExportFormats) short.Parse(dropdownlistDailyReportsExportFormat.SelectedValue);
					var reportsToGenerate = new List<DomainModel.Reports.ReportTypes>();
					foreach (ListItem item in checkboxlistDailyReportsToGenerate.Items)
					{
						if (item.Selected)
							reportsToGenerate.Add((DomainModel.Reports.ReportTypes) short.Parse(item.Value));
					}
					settings.DailyReportsToGenerate = reportsToGenerate.ToArray();
					settings.MinutesToAbortExporting = int.Parse(textboxMinutesToAbortExporting.Text);
					settings.EmailExport.Subject = textboxExportEmailSubject.Text;
					settings.EmailExport.Template = textboxExportEmailTemplate.Text;
					settings.EmailExportAborted.Subject = textboxExportEmailAbortedSubject.Text;
					settings.EmailExportAborted.Template = textboxExportEmailAbortedTemplate.Text;
					settings.EnableFtpDailyReports = checkboxEnableFtpDailyReport.Checked;
					if (settings.EnableFtpDailyReports)
					{
						settings.FtpIdDailyReports = listboxFtps.SelectedValue;
						settings.FtpDirectoryDailyReports = textboxFtpDirectoryDailyReports.Text;
						settings.DailyReportsZipExcel = checkboxDailyReportsZipExcel.Checked;
						settings.DailyReportsZipCSV = checkboxDailyReportsZipCSV.Checked;
					}

					if (license.SurveysEnabled)
					{
						settings.EnableSurveys = checkboxEnableSurveys.Checked;
					}
					else
					{
						settings.EnableSurveys = false;
					}

					if (license.AllowCognitiveServices)
					{
						settings.CognitiveServices.Enabled = checkboxUseCognitiveServices.Checked;
						if (settings.CognitiveServices.Enabled)
						{
							if (settings.CognitiveServices.Token == null || !settings.CognitiveServices.Token.Equals(textboxCognitiveServicesToken.Text))
							{
								settings.CognitiveServices.Token = textboxCognitiveServicesToken.Text;
								var token = Social.CognitiveServices.Helper.ParseToken(textboxCognitiveServicesToken.Text);
								settings.CognitiveServices.AllowedServices = token.Services;
							}

							settings.CognitiveServices.TokenSecret = textboxCognitiveServicesTokenSecret.Text;
						}
					}

					var oldRestChatMaxConcurrentSessions = settings.RestChat.MaxConcurrentSessions;
					var oldRestChatMaxConcurrentCallsPerSession = settings.RestChat.MaxConcurrentCallsPerSession;
					if (license.AllowedServiceTypes.Any(s => s == ServiceTypes.IntegrationChat) &&
						this.LoggedUserIsSuper)
					{
						settings.RestChat.MaxConcurrentSessions = int.Parse(textboxRestChatMaxConcurrentSessions.Text);
						settings.RestChat.MaxConcurrentCallsPerSession = int.Parse(textboxRestChatMaxConcurrentCallsPerSession.Text);
					}

					var oldYFlowMaxConcurrentSessionsPendingMessages = settings.YFlow.MaxConcurrentSessionsPendingMessages;
					var oldYFlowMaxConcurrentCallsPerSessionPendingMessages = settings.YFlow.MaxConcurrentCallsPerSessionPendingMessages;
					var oldYFlowPendingReplyCasesConcurrentCalls = settings.YFlow.MaxConcurrentCallsPendingReplyCases;

					var oldPendingMessagesUseCustomCallback = settings.YFlow.PendingMessagesUseCustomCallback;
					var oldPendingMessagesCustomCallbackUrl = settings.YFlow.PendingMessagesCustomCallbackUrl;
					if (license.AllowYFlow)
					{
						settings.YFlow.Enabled = checkboxEnableYFlow.Checked;
						if (settings.YFlow.Enabled)
						{
							settings.YFlow.Url = textboxYFlowUrl.Text.Trim();
							settings.YFlow.UrlApi = textboxYFlowUrlApi.Text.Trim();
							settings.YFlow.UrlWeb = textboxYFlowUrlWeb.Text.Trim();
							settings.YFlow.Timeout = Convert.ToInt32(textboxYFlowTimeout.Text);

							settings.YFlowContingency.Enabled = checkboxEnableYFlowContingency.Checked;
							if (settings.YFlowContingency.Enabled)
							{
								settings.YFlowContingency.Url = textboxYFlowUrl.Text.Trim();
								settings.YFlowContingency.UrlApi = textboxYFlowUrlApi.Text.Trim();
								settings.YFlowContingency.UrlWeb = textboxYFlowUrlWeb.Text.Trim();
								settings.YFlowContingency.Timeout = Convert.ToInt32(textboxYFlowTimeout.Text);
							}

							if (settings.YFlow.Credentials == null)
								settings.YFlow.Credentials = new NetworkCredential();

							settings.YFlow.AuthenticationFailed.EmailConnection = hiddenYFlowAuthenticationFailedConnection.Value;
							settings.YFlow.AuthenticationFailed.Subject = textboxYFlowAuthenticationFailedSubject.Text;
							settings.YFlow.AuthenticationFailed.Template = textboxYFlowAuthenticationFailedTemplate.Text;
							settings.YFlow.AuthenticationFailed.Emails = textboxYFlowAuthenticationFailedEmails.Text;
							settings.YFlow.InvokeFailed.EmailConnection = hiddenYFlowInvokeFailed.Value;
							settings.YFlow.InvokeFailed.Subject = textboxYFlowInvokeFailedSubject.Text;
							settings.YFlow.InvokeFailed.Template = textboxYFlowInvokeFailedTemplate.Text;
							settings.YFlow.InvokeFailed.Emails = textboxYFlowInvokeFailedEmails.Text;
							settings.YFlow.DerivationEnabled = checkboxYFlowDerivationEnabled.Checked;
							settings.YFlow.MaxMinutesForPendingMessages = int.Parse(textboxYFlowMaxMinutesForPendingMessages.Text);
							settings.YFlow.ActionAfterMaxMinutesForPendingMessages = (YFlowSettings.CasePendingMessagesActions) short.Parse(dropdownlistYFlowActionAfterMaxMinutesForPendingMessages.SelectedValue);

							if (!string.IsNullOrEmpty(hiddenYFlowPendingMessagesCallbackEndpoint.Value))
								settings.YFlow.PendingMessagesCallbackEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenYFlowPendingMessagesCallbackEndpoint.Value);
							else
								settings.YFlow.PendingMessagesCallbackEndpoint = null;

							if (this.LoggedUserIsSuper && !license.UseExternalServiceForIncomingMessages)
							{
								settings.YFlow.MaxConcurrentSessionsPendingMessages = int.Parse(textboxYFlowMaxConcurrentSessionsPendingMessages.Text);
								settings.YFlow.MaxConcurrentCallsPerSessionPendingMessages = int.Parse(textboxYFlowMaxConcurrentCallsPerSessionPendingMessages.Text);
								settings.YFlow.MaxConcurrentCallsPendingReplyCases = int.Parse(textboxYFlowPendingReplyCasesConcurrentCalls.Text);
							}

							if (this.LoggedUserIsSuper)
							{
								settings.YFlow.PendingMessagesUseCustomCallback = dropdownlistYFlowPendingMessagesUseCustomCallback.SelectedValue.Equals("1");
								settings.YFlow.PendingMessagesCustomCallbackUrl = textboxYFlowPendingMessagesCustomCallbackUrl.Text;
							}
							settings.Cases.MaxElapsedMinutesToCloseYFlowCases = ushort.Parse(textboxMaxElapsedMinutesToCloseYFlowCases.Text);
							settings.Cases.InvokeYFlowWhenClosedCases = checkboxInvokeYFlowWhenClosedCases.Checked;

							if (!string.IsNullOrEmpty(textboxTagInvocation.Text))
							{
								settings.YFlow.TagInvocationFailed = int.Parse(textboxTagInvocation.Text);
							}
							else
							{
								settings.YFlow.TagInvocationFailed = -1;
							}

							if (!string.IsNullOrEmpty(textboxTagTimeout.Text))
							{
								settings.YFlow.TagTimeoutFailed = int.Parse(textboxTagTimeout.Text);
							}
							else
							{
								settings.YFlow.TagTimeoutFailed = -1;
							}
							if (!string.IsNullOrEmpty(textboxTagLabel.Text))
							{
								settings.YFlowContingency.TagLabelFailed = int.Parse(textboxTagLabel.Text);
							}
							else
							{
								settings.YFlowContingency.TagLabelFailed = -1;
							}

							//this.RegisterJsonVariable("tagInvocationFailed", settings.YFlow.TagInvocationFailed);
							//this.RegisterJsonVariable("tagTimeoutFailed", settings.YFlow.TagTimeoutFailed);
						}
					}
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowChatSummary)
					{
						settings.YSmart.Enabled = checkboxEnableYSmart.Checked;
						if (settings.YSmart.Enabled)
						{
							var projectSelect= Newtonsoft.Json.JsonConvert.DeserializeObject<ProjectsDTO>(hiddenProject.Value);
							settings.YSmart.ProjectId = projectSelect.id;
							hiddenProjectId.Value = settings.YSmart.ProjectId.ToString();
							settings.YSmart.ProjectToken = projectSelect.token;
							settings.YSmart.Timeout = Convert.ToInt32(textboxYSmartTimeout.Text);
							if (!string.IsNullOrEmpty(textboxTagTimeout.Text))
							{
								settings.YSmart.TagTimeoutFailed = int.Parse(textboxTagTimeout1.Text);
							}
							else
							{
								settings.YSmart.TagTimeoutFailed = -1;
							}
						}

					}
							settings.Maintenance.DaysForCases = short.Parse(textboxMaintenanceCases.Text);
					settings.Maintenance.DaysForHistorical = short.Parse(textboxMaintenanceHist.Text);
					settings.Maintenance.DaysForHistoricalByInterval = short.Parse(textboxMaintenanceHistByInterval.Text);

					if (!string.IsNullOrEmpty(dropdownlistPbxIntegrationAgentGroupForNewAgents.SelectedValue))
						settings.PbxIntegrationAgentGroupForNewAgents = int.Parse(dropdownlistPbxIntegrationAgentGroupForNewAgents.SelectedValue);
					else
						settings.PbxIntegrationAgentGroupForNewAgents = null;

					settings.WebAgentUrlLoginSettings.UseUrlLogin = checkboxWebAgentConfigurationAllowUrlLogin.Checked;
					if (settings.WebAgentUrlLoginSettings.UseUrlLogin)
					{
						settings.WebAgentUrlLoginSettings.UserName = textboxWebAgentConfigurationUserNameLoginParameter.Text;
						settings.WebAgentUrlLoginSettings.PasswordRequired = checkboxWebAgentConfigurationPasswordRequired.Checked;
						if (settings.WebAgentUrlLoginSettings.PasswordRequired)
						{
							settings.WebAgentUrlLoginSettings.PasswordParam = textboxWebAgentConfigurationPasswordParameter.Text;
							settings.WebAgentUrlLoginSettings.KeyToDecryptPasswordParam = textboxWebAgentConfigurationKeyToDecryptPassword.Text;
						}
						settings.WebAgentUrlLoginSettings.HashParam = textboxWebAgentConfigurationHashParameter.Text;
						settings.WebAgentUrlLoginSettings.KeyToHash = textboxWebAgentConfigurationKeyToHash.Text;
						settings.WebAgentUrlLoginSettings.RemoveLoginForm = checkboxWebAgentConfigurationRemoveLoginForm.Checked;

						//comportamiento que tendrá el Web Agent al finalizar la sesión
						settings.WebAgentUrlLoginSettings.LogoutAction = (DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum) short.Parse(dropdownlistLogoutAction.SelectedValue);
						if (settings.WebAgentUrlLoginSettings.LogoutAction == DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum.LogoutMessage)
						{
							settings.WebAgentUrlLoginSettings.EndSessionMessage = textboxWebAgentConfigurationLogoutMessage.Text;
						}
						else if (settings.WebAgentUrlLoginSettings.LogoutAction == DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum.UrlToRedirect)
						{
							settings.WebAgentUrlLoginSettings.UrlToRedirect = textboxWebAgentConfigurationRedirectUrl.Text;
						}

						settings.WebAgentUrlLoginSettings.RemoveLogoutButton = checkboxWebAgentConfigurationRemoveLogoutButton.Checked;
					}
					else
					{
						settings.WebAgentUrlLoginSettings.UserName = string.Empty;
						settings.WebAgentUrlLoginSettings.PasswordRequired = false;
						settings.WebAgentUrlLoginSettings.PasswordParam = string.Empty;
						settings.WebAgentUrlLoginSettings.KeyToDecryptPasswordParam = string.Empty;
						settings.WebAgentUrlLoginSettings.HashParam = string.Empty;
						settings.WebAgentUrlLoginSettings.KeyToHash = string.Empty;
						settings.WebAgentUrlLoginSettings.RemoveLoginForm = false;
						settings.WebAgentUrlLoginSettings.UrlToRedirect = string.Empty;
						settings.WebAgentUrlLoginSettings.EndSessionMessage = string.Empty;
						settings.WebAgentUrlLoginSettings.LogoutAction = DomainModel.Settings.WebAgentUrlLoginSettings.LogoutActionEnum.None;
						settings.WebAgentUrlLoginSettings.RemoveLogoutButton = false;
					}

					/* Comienzo de configuración de manejo de estado del agente web mediante mensajería interna */

					settings.WebAgentStateManagementSettings.AllowChangeState = checkboxWebAgentConfigurationAllowChangeState.Checked;

					if (settings.WebAgentStateManagementSettings.AllowChangeState)
					{
						settings.WebAgentStateManagementSettings.AllowAgentsToChangeState = checkboxWebAgentConfigurationAllowAgentsToChangeState.Checked;
						settings.WebAgentStateManagementSettings.SendStateOfChangeByPrivateMessaging = checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging.Checked;
						settings.WebAgentStateManagementSettings.ShowInformativeMessageAfterChangeStateReceived = checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.Checked;

						if (settings.WebAgentStateManagementSettings.ShowInformativeMessageAfterChangeStateReceived)
							settings.WebAgentStateManagementSettings.MessageAfterChangeStateReceived = textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.Text;

						if (string.IsNullOrEmpty(textboxWebAgentConfigurationChangeStateTargetOrigin.Text))
							settings.WebAgentStateManagementSettings.TargetOriginForChangeStateMessaging = "*";
						else
							settings.WebAgentStateManagementSettings.TargetOriginForChangeStateMessaging = textboxWebAgentConfigurationChangeStateTargetOrigin.Text;

					}

					else
					{
						settings.WebAgentStateManagementSettings.AllowChangeState = false;
						settings.WebAgentStateManagementSettings.ShowInformativeMessageAfterChangeStateReceived = false;
						settings.WebAgentStateManagementSettings.AllowAgentsToChangeState = false;
						settings.WebAgentStateManagementSettings.SendStateOfChangeByPrivateMessaging = false;
						settings.WebAgentStateManagementSettings.SendLogoutStateOfChangeByPrivateMessaging = false;
						settings.WebAgentStateManagementSettings.MessageAfterChangeStateReceived = string.Empty;
						settings.WebAgentStateManagementSettings.TargetOriginForChangeStateMessaging = string.Empty;
					}

					settings.WebAgentStateManagementSettings.AllowLogoutInvoke = checkboxWebAgentConfigurationAllowLogoutInvoke.Checked;

					if (settings.WebAgentStateManagementSettings.AllowLogoutInvoke)
					{
						settings.WebAgentStateManagementSettings.IgnoreLogoutAfterError = checkboxWebAgentConfigurationIgnoreLogoutAfterError.Checked;
						settings.WebAgentStateManagementSettings.SendLogoutStateOfChangeByPrivateMessaging = checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging.Checked;

						settings.WebAgentStateManagementSettings.ShowMessageAfterLogoutReceived = checkboxWebAgentConfigurationShowMessageAfterLogoutReceived.Checked;
						if (settings.WebAgentStateManagementSettings.ShowMessageAfterLogoutReceived)
							settings.WebAgentStateManagementSettings.MessageAfterLogoutReceived = textboxWebAgentConfigurationShowMessageAfterLogoutReceived.Text;

						if (string.IsNullOrEmpty(textboxWebAgentConfigurationLogoutInvokeTargetOrigin.Text))
							settings.WebAgentStateManagementSettings.TargetOriginForLogoutMessaging = "*";
						else
							settings.WebAgentStateManagementSettings.TargetOriginForLogoutMessaging = textboxWebAgentConfigurationLogoutInvokeTargetOrigin.Text;
					}
					else
					{
						settings.WebAgentStateManagementSettings.AllowLogoutInvoke = false;
						settings.WebAgentStateManagementSettings.IgnoreLogoutAfterError = false;
						settings.WebAgentStateManagementSettings.SendLogoutStateOfChangeByPrivateMessaging = false;
						settings.WebAgentStateManagementSettings.ShowMessageAfterLogoutReceived = false;
						settings.WebAgentStateManagementSettings.MessageAfterLogoutReceived = string.Empty;
						settings.WebAgentStateManagementSettings.TargetOriginForLogoutMessaging = string.Empty;
					}
					/* Fin de configuración de manejo de estado del agente web mediante mensajería interna */

					if (license.AllowToExtendProfile)
					{
						settings.ExtendedProfilesFields = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ExtendedField[]>(hiddenExtendedProfileFields.Value);
					}
					else
					{
						settings.ExtendedProfilesFields = null;
					}

					if (license.AllowToExtendCase)
					{
						settings.ExtendedCasesFields = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ExtendedField[]>(hiddenExtendedCaseFields.Value);
					}
					else
					{
						settings.ExtendedCasesFields = null;
					}

					if (license.AllowToExtendBusinessData)
					{
						settings.ExtendedProfilesBusinessCodeFields = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ExtendedProfileBusinessCodeField[]>(hiddenExtendedProfileBusinessCodeFields.Value);

						if (license.AllowCustomBusinessDataRegex)
						{
							string regex = textboxBusinessDataRegex.Text;
							if (!regex.StartsWith("^"))
							{
								regex = "^" + regex;
								textboxBusinessDataRegex.Text = regex;
							}
							if (!regex.EndsWith("$"))
							{
								regex = regex + "$";
								textboxBusinessDataRegex.Text = regex;
							}
							settings.BusinessDataRegex = regex;
							settings.BusinessDataWrongInputMessage = textboxBusinessDataWrongInputMessage.Text;
							settings.BusinessDataFormatMessage = textboxBusinessDataFormatMessage.Text;
						}
						else
						{
							settings.BusinessDataRegex = @"^[a-zA-Z0-9]{6,12}(,[a-zA-Z0-9]{6,12})*$";
							settings.BusinessDataWrongInputMessage = "El/los número/s de cliente debe/n ser numérico/s y contar entre 6 y 12 dígitos";
							settings.BusinessDataFormatMessage = "Puede separar varios números de cliente utilizando una coma (sin ingresar espacios)";
						}
					}
					else
					{
						settings.BusinessDataRegex = @"^[a-zA-Z0-9]{6,12}(,[a-zA-Z0-9]{6,12})*$";
						settings.BusinessDataWrongInputMessage = "El/los número/s de cliente debe/n ser numérico/s y contar entre 6 y 12 dígitos";
						settings.BusinessDataFormatMessage = "Puede separar varios números de cliente utilizando una coma (sin ingresar espacios)";
						settings.ExtendedProfilesBusinessCodeFields = null;
					}

					var oldPushNotificationsServiceBusUseWebSockets = settings.PushNotificationsServiceBusUseWebSockets;
					var oldPushNotificationsServiceBusConcurrentMessages = settings.PushNotificationsServiceBusConcurrentMessages;
					var oldPushNotificationsServiceBusConcurrentStatuses = settings.PushNotificationsServiceBusConcurrentStatuses;
					var oldPushNotificationsServiceBusConcurrentMassive = settings.PushNotificationsServiceBusConcurrentMassive;

					if (!license.InTheCloud || this.LoggedUserIsSuper)
					{
						settings.PushNotificationsServiceBusUseWebSockets = dropdownlistPushNotificationsServiceBusProtocol.SelectedValue.Equals("1");
						settings.PushNotificationsServiceBusConcurrentMessages = int.Parse(textboxPushNotificationsServiceBusConcurrentMessages.Text);
						settings.PushNotificationsServiceBusConcurrentStatuses = int.Parse(textboxPushNotificationsServiceBusConcurrentStatuses.Text);
						settings.PushNotificationsServiceBusConcurrentMassive = int.Parse(textboxPushNotificationsServiceBusConcurrentMassive.Text);
					}

					if (license.InTheCloud)
					{
						if (!string.IsNullOrEmpty(textboxCloudIPRestrictionsWeb.Text))
						{
							var ips = new List<string>();
							var lines = textboxCloudIPRestrictionsWeb.Text.Split("\n".ToCharArray());
							for (int i = 0; i < lines.Length; i++)
							{
								var line = lines[i];
								try
								{
									line = line.TrimEnd('\r', '\n');
									if (line.Contains("/"))
									{
										var network = System.Net.IPNetwork.Parse(line);
										ips.Add(line);
									}
									else
									{
										var ip = System.Net.IPAddress.Parse(line);
										ips.Add(line);
									}
								}
								catch { }
							}

							if (ips.Count > 0)
								settings.RestrictedIPsForWeb = ips.ToArray();
							else
								settings.RestrictedIPsForWeb = null;
						}
						else
						{
							settings.RestrictedIPsForWeb = null;
						}

						if (!string.IsNullOrEmpty(textboxCloudIPRestrictionsWebAgent.Text))
						{
							var ips = new List<string>();
							var lines = textboxCloudIPRestrictionsWebAgent.Text.Split("\n".ToCharArray());
							for (int i = 0; i < lines.Length; i++)
							{
								var line = lines[i];
								try
								{
									line = line.TrimEnd('\r', '\n');
									if (line.Contains("/"))
									{
										var network = System.Net.IPNetwork.Parse(line);
										ips.Add(line);
									}
									else
									{
										var ip = System.Net.IPAddress.Parse(line);
										ips.Add(line);
									}
								}
								catch { }
							}

							if (ips.Count > 0)
								settings.RestrictedIPsForWebAgent = ips.ToArray();
							else
								settings.RestrictedIPsForWebAgent = null;
						}
						else
						{
							settings.RestrictedIPsForWebAgent = null;
						}

						if (!string.IsNullOrEmpty(hiddenCloudHeadersToAdd.Value))
						{
							try
							{
								settings.CloudHeadersToAdd = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ExtraHeader[]>(hiddenCloudHeadersToAdd.Value);
							}
							catch
							{
								settings.CloudHeadersToAdd = null;
							}
						}

						if (!string.IsNullOrEmpty(hiddenCloudHeadersToRemove.Value))
						{
							try
							{
								settings.CloudHeadersToRemove = Newtonsoft.Json.JsonConvert.DeserializeObject<string[]>(hiddenCloudHeadersToRemove.Value);
							}
							catch
							{
								settings.CloudHeadersToRemove = null;
							}
						}
					}

					if (this.LoggedUserIsSuper)
					{
						try
						{
							settings.DefaultTimeZone = TimeZoneInfo.FindSystemTimeZoneById(hiddenDefaultTimeZone.Value);
						}
						catch
						{
							settings.DefaultTimeZone = DomainModel.SystemSettings.Instance.LocalTimeZone;
						}

						if (license.AllowToConfigureTimeZones)
						{
							try
							{
								if (!string.IsNullOrEmpty(hiddenTimeZonesToConsolide.Value))
								{
									var timeZonesToConsolide = hiddenTimeZonesToConsolide.Value.Split(",".ToCharArray());
									settings.TimeZonesToConsolide = timeZonesToConsolide.Select(tz => TimeZoneInfo.FindSystemTimeZoneById(tz)).ToArray();
								}
								else
								{
									settings.TimeZonesToConsolide = null;
								}
							}
							catch
							{
								settings.TimeZonesToConsolide = null;
							}
						}
					}

					settings.DeliveryFailedNotification.NotifyByEmail = listboxAlertMessagesDeliveryFailedVia.Items.FindByValue("1").Selected;
					settings.DeliveryFailedNotification.NotifyByNotifications = listboxAlertMessagesDeliveryFailedVia.Items.FindByValue("2").Selected;
					settings.DeliveryFailedNotification.Services = null;
					if (settings.DeliveryFailedNotification.NotifyByEmail || settings.DeliveryFailedNotification.NotifyByNotifications)
					{
						var indices = listboxAlertMessagesDeliveryFailedFromServices.GetSelectedIndices();
						if (indices != null && indices.Length > 0)
						{
							var servicesToNotify = new List<int>();
							foreach (ListItem item in listboxAlertMessagesDeliveryFailedFromServices.Items)
							{
								if (item.Selected)
								{
									var serviceId = int.Parse(item.Value);
									servicesToNotify.Add(serviceId);
								}
							}

							settings.DeliveryFailedNotification.Services = servicesToNotify.ToArray();
						}
					}

					settings.DeliveryFailedNotification.EmailNotificationSettings.Subject = textboxAlertMessagesDeliveryFailedViaMailEmailSubject.Text;
					settings.DeliveryFailedNotification.EmailNotificationSettings.Template = textboxAlertMessagesDeliveryFailedViaMailEmailTemplate.Text;

					settings.MinutesPredictedAht = int.Parse(textboxMinutesPredictedAht.Text);
					settings.SecondsEwt = int.Parse(textboxSecondsEwt.Text);

					settings.AllowToSetASAValueByDefault = checkboxASAPersonalized.Checked;
					if (settings.AllowToSetASAValueByDefault)
					{
						settings.ASADefaultValue = int.Parse(textboxAsaBase.Text);
					}
					else
					{
						settings.ASADefaultValue = 0;
					}

					if (license.AllowAgentsToStartVideoCall)
					{
						settings.VideoCubiqUrl = textboxCubiqUrl.Text;
						settings.VideoCubiqApiKey = textboxCubiqApiKey.Text;
						settings.VideoCubiqSecret = textboxCubiqSecret.Text;
						settings.VideoCubiqRecordingUrl = textboxCubiqRecordingUrl.Text;
					}
					else
					{
						settings.VideoCubiqUrl = "";
						settings.VideoCubiqApiKey = "";
						settings.VideoCubiqSecret = "";
						settings.VideoCubiqRecordingUrl = "";
					}

					if (license.AllowYUsage)
					{
						settings.YUsage.Enabled = checkboxEnableYUsage.Checked;
						settings.YUsage.Url = textboxYUsageUrl.Text;
						settings.YUsage.UrlApi = textboxYUsageUrlApi.Text;
                    }

					var updatedData = DomainModel.SystemSettings.Instance.Retrieve();

					DAL.SystemSettingsDAO.Update();

					if (!this.LoggedUserIsSuper)
					{
						DAL.UserLogDAO.Insert(this.LoggedUser
						  , SystemEntityTypes.SystemParameters
						  , SystemActionTypes.Edit
						  , currentData.Except(updatedData).ToDictionary(key => key.Key, value => value.Value?.ToString())
						  , updatedData.Except(currentData).ToDictionary(key => key.Key, value => value.Value?.ToString())
						  , null
						  , null);
					}

					if (Core.System.Instance.EventsService != null)
					{
						var @event = new DomainModel.Events.DomainObjectSyncEvent();
						@event.Action = SystemActionTypes.Edit;
						@event.EntityType = SystemEntityTypes.SystemParameters;
						await Core.System.Instance.EventsService.PublishEventAsync(@event);
					}

					popupmessageChangesApplied.ShowOnLoad = true;

					if (this.LoggedUserIsSuper &&
						license.AllowYFlow &&
						!license.UseExternalServiceForIncomingMessages)
					{
						if (oldYFlowMaxConcurrentSessionsPendingMessages != settings.YFlow.MaxConcurrentSessionsPendingMessages ||
							oldYFlowMaxConcurrentCallsPerSessionPendingMessages != settings.YFlow.MaxConcurrentCallsPerSessionPendingMessages)
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de mensajes pendientes de yflow concurrentes de service bus. Se actualiza");
							Core.System.Instance.CasesService.ChangeYFlowPendingMessagesUpdateConcurrency(settings.YFlow.MaxConcurrentSessionsPendingMessages, settings.YFlow.MaxConcurrentCallsPerSessionPendingMessages);
						}

						if (oldYFlowPendingReplyCasesConcurrentCalls != settings.YFlow.MaxConcurrentCallsPendingReplyCases)
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de casos pendientes de respuesta de yflow concurrentes de service bus. Se actualiza");
							Core.System.Instance.CasesService.ChangeYFlowPendingReplyCasesUpdateConcurrency(settings.YFlow.MaxConcurrentCallsPendingReplyCases);
						}

						if (settings.YFlow.PendingMessagesUseCustomCallback &&
							(
								oldPendingMessagesUseCustomCallback != settings.YFlow.PendingMessagesUseCustomCallback ||
								oldPendingMessagesCustomCallbackUrl != settings.YFlow.PendingMessagesCustomCallbackUrl
							)
						)
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de callback custom para procesar mensajes pendientes de yflow concurrentes de service bus. Se suscribe");
							var url = $"{settings.YFlow.PendingMessagesCustomCallbackUrl.TrimEnd('/')}/api/subscription";
							await Social.SocialServices.Subscriber.Manager.Subscribe(url);
						}
					}

					if (this.LoggedUserIsSuper)
					{
						if (oldRestChatMaxConcurrentSessions != settings.RestChat.MaxConcurrentSessions ||
							oldRestChatMaxConcurrentCallsPerSession != settings.RestChat.MaxConcurrentCallsPerSession)
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de chat integrado de mensajes concurrentes de service bus. Se actualiza");
							Core.System.Instance.ChatsService.ChangeRestChatConcurrency(settings.RestChat.MaxConcurrentSessions, settings.RestChat.MaxConcurrentCallsPerSession);
						}

						if (oldCasesCloseConcurrent != settings.Cases.CasesCloseConcurrent)
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de casos que se cerrarán en forma concurrent de service bus. Se actualiza");
							Core.System.Instance.CasesService.ChangeCasesToBeClosedUpdateConcurrency(settings.Cases.CasesCloseConcurrent);
						}
					}

					if (!license.InTheCloud || this.LoggedUserIsSuper)
					{
						if (oldPushNotificationsServiceBusUseWebSockets == settings.PushNotificationsServiceBusUseWebSockets)
						{
							if (oldPushNotificationsServiceBusConcurrentMessages != settings.PushNotificationsServiceBusConcurrentMessages)
							{
								Tracer.TraceInfo("Cambiaron los datos de configuración de mensajes concurrentes de service bus. Se actualiza");
								SocialServices.Subscriber.Manager.ChangeConcurrentMessages(settings.PushNotificationsServiceBusConcurrentMessages);
							}

							if (oldPushNotificationsServiceBusConcurrentStatuses != settings.PushNotificationsServiceBusConcurrentStatuses)
							{
								Tracer.TraceInfo("Cambiaron los datos de configuración de estados de mensajes concurrentes de service bus. Se actualiza");
								SocialServices.Subscriber.Manager.ChangeConcurrentStatuses(settings.PushNotificationsServiceBusConcurrentStatuses);
							}

							if (oldPushNotificationsServiceBusConcurrentMassive != settings.PushNotificationsServiceBusConcurrentMassive)
							{
								Tracer.TraceInfo("Cambiaron los datos de configuración de mensajes de whatsapp concurrentes de service bus. Se actualiza");
								SocialServices.Subscriber.Manager.ChangeConcurrentWhatsappHSM(settings.PushNotificationsServiceBusConcurrentMassive);
							}
						}
						else
						{
							Tracer.TraceInfo("Cambiaron los datos de configuración de service bus. Se detiene");
							await SocialServices.Subscriber.Manager.Finish();

							Tracer.TraceInfo("Cambiaron los datos de configuración de service bus. Se vuelve a iniciar");
							await SocialServices.Subscriber.Manager.Initialize();
						}
					}

					if (this.LoggedUserIsSuper && license.AllowWhatsappVoiceCallsRecording)
					{
						var whatsappSettings = DomainModel.SystemSettings.Instance.Whatsapp as DomainModel.Settings.WhatsappSettings;
						try
						{
							if (!await whatsappSettings.IsVoiceCallRecordingAppCreated(license.VoiceCallsMediaServerAppName) ||
								!whatsappSettings.VoiceCallsRecordingAppCreated)
							{
								try
								{
									await whatsappSettings.CreateVoiceCallRecordingApp(license.VoiceCallsMediaServerAppName, license.ClientID);
									Tracer.TraceInfo("Se creó con éxito la aplicación en el servidor de grabación");

									whatsappSettings.VoiceCallsRecordingAppCreated = true;
									DAL.SystemSettingsDAO.Update("Whatsapp.VoiceCallsRecordingAppCreated");
								}
								catch (Exception ex)
								{
									popupmessageWhatsAppServiceVoiceCallRecordingCouldntCreateApp.ShowOnLoad = true;
									Tracer.TraceError("No se pudo crear la aplicación en el servidor de grabación: {0}", ex);
								}
							}
						}
						catch (Exception ex)
						{
							popupmessageWhatsAppServiceVoiceCallRecordingCouldntCheckApp.ShowOnLoad = true;
							Tracer.TraceError("No se pudo verificar si existe la aplicación en el servidor de grabación: {0}", ex);
						}
					}

					Application.Lock();
					Application["SystemSettingsJson"] = null;
					Application.UnLock();

					if (changedtheme)
						Response.Redirect(this.RedirectUrl, false);

				}
				catch (Exception ex)
				{
					Common.Tracer.TraceError("No se pudo grabar los parámetros del sistema: {0}", ex);
				}
			}
		}

		#region Event Handlers

		protected void buttonSave_Click(object sender, EventArgs e)
		{
			RegisterAsyncTask(new PageAsyncTask(SaveAsync));
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object SendTestMailSmtp(string to, string server, string from, bool useCredentials, string user, string password, bool? useSSL, bool? customPort, short? port, bool useCurrentPassword)
		{
			try
			{
				var builder = new MimeKit.BodyBuilder();
				var mimeMessage = new MimeKit.MimeMessage();

				mimeMessage.To.Add(new MimeKit.MailboxAddress(to, to));
				mimeMessage.From.Add(new MimeKit.MailboxAddress(from, from));
				mimeMessage.Subject = "Mail de prueba";

				builder.TextBody = "Mail de prueba";
				mimeMessage.Body = builder.ToMessageBody();

				var mailer = new MailKit.Net.Smtp.SmtpClient();

				mailer.ServerCertificateValidationCallback = (mysender, certificate, chain, sslPolicyErrors) => { return true; };
				mailer.SslProtocols = SslProtocols.Ssl3 | SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12;
				mailer.CheckCertificateRevocation = false;

				int portToUse = 0;
				if (customPort.Value && port.HasValue)
					portToUse = port.Value;
				var options = MailKit.Security.SecureSocketOptions.Auto;
				if (useSSL.Value)
					options = MailKit.Security.SecureSocketOptions.SslOnConnect;
				mailer.Connect(server, portToUse, options);

				if (useCredentials)
				{
					mailer.AuthenticationMechanisms.Remove("XOAUTH2");

					if (useCurrentPassword)
					{
						var credentials = new System.Net.NetworkCredential(user, DomainModel.SystemSettings.Instance.EmailConnection.Credentials.SecurePassword);
						mailer.Authenticate(credentials);
					}
					else
					{
						mailer.Authenticate(user, password);
					}
				}

				mailer.Send(mimeMessage);

				return new
				{
					Success = true
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}
		[System.Web.Services.WebMethod]
		public static object GetProjectsYSmart()
		{
			try
			{
				var _ySmartService = new ySmartService();
				var projectList = Task.Run(async () =>
				{
					return await _ySmartService.GetProjects();
				}).Result;				
				return new
				{
					Success = true,
					Data = projectList

				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}
		[System.Web.Services.WebMethod]
		public static object SendTestMailEws(string to, string server, string from, string user, string password, bool useCurrentPassword, Microsoft.Exchange.WebServices.Data.ExchangeVersion? version)
		{
			try
			{
				var builder = new MimeKit.BodyBuilder();
				var mimeMessage = new MimeKit.MimeMessage();

				mimeMessage.To.Add(new MimeKit.MailboxAddress(to, to));
				mimeMessage.From.Add(new MimeKit.MailboxAddress(from, from));
				mimeMessage.Subject = "Mail de prueba";

				builder.TextBody = "Mail de prueba";
				mimeMessage.Body = builder.ToMessageBody();

				Microsoft.Exchange.WebServices.Data.ExchangeService imp;
				if (version == null)
					imp = new Microsoft.Exchange.WebServices.Data.ExchangeService();
				else
					imp = new Microsoft.Exchange.WebServices.Data.ExchangeService(version.Value);

				System.Net.NetworkCredential credentials;
				int index = user.IndexOf('\\');
				if (index >= 0)
				{
					string domain = user.Substring(0, index);
					user = user.Substring(index + 1);
					if (useCurrentPassword)
					{
						credentials = new System.Net.NetworkCredential(user, DomainModel.SystemSettings.Instance.EmailConnection.Credentials.SecurePassword, domain);
					}
					else
					{
						credentials = new System.Net.NetworkCredential(user, password, domain);
					}
				}
				else
				{
					if (useCurrentPassword)
					{
						credentials = new System.Net.NetworkCredential(user, DomainModel.SystemSettings.Instance.EmailConnection.Credentials.SecurePassword);
					}
					else
					{
						credentials = new System.Net.NetworkCredential(user, password);
					}
				}

				imp.Credentials = new Microsoft.Exchange.WebServices.Data.WebCredentials(credentials);
				imp.Url = new Uri(server);

				var message = new Microsoft.Exchange.WebServices.Data.EmailMessage(imp);

				using (var ms = new System.IO.MemoryStream())
				{
					mimeMessage.WriteTo(ms);
					message.MimeContent = new Microsoft.Exchange.WebServices.Data.MimeContent("UTF-8", ms.ToArray());
					message.Save();
				}

				message.Load(new Microsoft.Exchange.WebServices.Data.PropertySet(Microsoft.Exchange.WebServices.Data.BasePropertySet.FirstClassProperties, Microsoft.Exchange.WebServices.Data.ItemSchema.MimeContent));

				message.Send();

				return new
				{
					Success = true
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object SendTestMailEwsOAuth(string to, string server, string from, string emailAddress, string appId, string clientSecret, string tenantId, Microsoft.Exchange.WebServices.Data.ExchangeVersion? version)
		{
			try
			{
				var builder = new MimeKit.BodyBuilder();
				var mimeMessage = new MimeKit.MimeMessage();

				mimeMessage.To.Add(new MimeKit.MailboxAddress(to, to));
				mimeMessage.From.Add(new MimeKit.MailboxAddress(from, from));
				mimeMessage.Subject = "Mail de prueba";

				builder.TextBody = "Mail de prueba";
				mimeMessage.Body = builder.ToMessageBody();

				Microsoft.Exchange.WebServices.Data.ExchangeService imp;
				if (version == null)
					imp = new Microsoft.Exchange.WebServices.Data.ExchangeService();
				else
					imp = new Microsoft.Exchange.WebServices.Data.ExchangeService(version.Value);

				var cca = Microsoft.Identity.Client.ConfidentialClientApplicationBuilder
						.Create(appId)
						.WithClientSecret(clientSecret)
						.WithTenantId(tenantId)
						.Build();

				// The permission scope required for EWS access
				var ewsScopes = new string[] { "https://outlook.office365.com/.default" };

				//Make the token request
				var authResult = cca.AcquireTokenForClient(ewsScopes).ExecuteAsync().GetAwaiter().GetResult();

				imp.Credentials = new Microsoft.Exchange.WebServices.Data.OAuthCredentials(authResult.AccessToken);

				//Impersonate the mailbox you'd like to access.
				imp.ImpersonatedUserId = new Microsoft.Exchange.WebServices.Data.ImpersonatedUserId(Microsoft.Exchange.WebServices.Data.ConnectingIdType.SmtpAddress, emailAddress);

				//Include x-anchormailbox header
				imp.HttpHeaders.Add("X-AnchorMailbox", emailAddress);

				imp.Url = new Uri(server);

				var message = new Microsoft.Exchange.WebServices.Data.EmailMessage(imp);

				using (var ms = new System.IO.MemoryStream())
				{
					mimeMessage.WriteTo(ms);
					message.MimeContent = new Microsoft.Exchange.WebServices.Data.MimeContent("UTF-8", ms.ToArray());
					message.Save();
				}

				message.Load(new Microsoft.Exchange.WebServices.Data.PropertySet(Microsoft.Exchange.WebServices.Data.BasePropertySet.FirstClassProperties, Microsoft.Exchange.WebServices.Data.ItemSchema.MimeContent));

				message.Send();

				return new
				{
					Success = true
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object TestLdapConfig(string server, string port, string baseDN, string user, string password, bool secure)
		{
			try
			{
				// Si el usuario existe en la BD, valido que exista en LDAP.
				Exception validationException;

				bool valid;
				if (password == null)
				{
					valid = Common.LDAP.ADManager.ValidateUser(server, int.Parse(port), baseDN, user, DomainModel.SystemSettings.Instance.LDAP.Password, secure, out validationException);
				}
				else
				{
					valid = Common.LDAP.ADManager.ValidateUser(server, int.Parse(port), baseDN, user, password, secure, out validationException);
				}

				if (!valid)
				{
					var comException = validationException as System.DirectoryServices.DirectoryServicesCOMException;

					return new
					{
						Success = false,
						Error = new
						{
							Message = validationException.Message,
							StackTrace = validationException.StackTrace,
							MoreDetails = comException == null ? null : new
							{
								ErrorCode = comException.ErrorCode,
								ExtendedError = comException.ExtendedError,
								ExtendedErrorMessage = comException.ExtendedErrorMessage,
								HResult = comException.HResult
							}
						}
					};
				}

				return new
				{
					Success = true
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsPathForAttachValid(string path)
		{
			try
			{
				return new
				{
					Success = true,
					IsValid = Helpers.FilesHelper.HasWritePermissionOnDir(path)
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsAgentApplicationFileValid(string path)
		{
			try
			{
				return new
				{
					Success = true,
#if DEBUG
					IsValid = true,
#else
					IsValid = System.IO.File.Exists(path) && path.EndsWith(".msi", StringComparison.InvariantCultureIgnoreCase)
#endif
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsChatPackageFileValid(string path)
		{
			try
			{
				return new
				{
					Success = true,
#if DEBUG
					IsValid = true,
#else
					IsValid = System.IO.File.Exists(path) && path.EndsWith(".zip", StringComparison.InvariantCultureIgnoreCase)
#endif
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsSurveysValid()
		{
			try
			{
				bool isValid = Task.Run(async () =>
				{
					return await DomainModel.Survey.IsValid(Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID);
				}).Result;

				return new
				{
					Success = true,
					IsValid = isValid
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsCognitiveServicesTokenValid(string token, string tokenSecret)
		{
			try
			{
				try
				{
					var tokenInfo = Social.CognitiveServices.Helper.ParseToken(token);

					return new
					{
						Success = true,
						IsValid = true,
						Info = tokenInfo
					};
				}
				catch (ArgumentException ex)
				{
					return new
					{
						Success = true,
						IsValid = false,
						Error = ex.Message
					};
				}
				catch (AggregateException ex)
				{
					return new
					{
						Success = false,
						Error = ex.InnerException?.Message
					};
				}
				catch (Exception ex)
				{
					return new
					{
						Success = false,
						Error = ex.Message
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsWebAgentURLValid(string url)
		{
			try
			{
				var client = new RestSharp.RestClient(url);

				var request = new RestSharp.RestRequest("api/status", RestSharp.Method.GET);

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("application/json"))
					{
						var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

						return new
						{
							Success = true,
							IsValid = true,
							Version = jContent["version"].ToString()
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = true,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false
					};
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar el agente web: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsWhatsappUrlRtNotificationsValid(string url)
		{
			try
			{
				var client = new RestSharp.RestClient(url);

				var challenge = "grosso";
				var request = new RestSharp.RestRequest("api/whatsapp/status", RestSharp.Method.GET);
				request.AddQueryParameter("hub.mode", "status");
				request.AddQueryParameter("hub.challenge", challenge);
				request.AddQueryParameter("hub.verify_token", "yoizencallback");

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("text/plain"))
					{
						var responseText = response.Content;

						return new
						{
							Success = true,
							IsValid = responseText.Equals(challenge),
							Content = response.Content
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = false,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false,
						Content = response.Content
					};
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar el agente web: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsTelegramUrlRtNotificationsValid(string url)
		{
			try
			{
				var client = new RestSharp.RestClient(url);

				var challenge = "grosso";
				var request = new RestSharp.RestRequest("api/telegram/status", RestSharp.Method.GET);
				request.AddQueryParameter("hub.mode", "status");
				request.AddQueryParameter("hub.challenge", challenge);
				request.AddQueryParameter("hub.verify_token", "yoizencallback");

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("text/plain"))
					{
						var responseText = response.Content;

						return new
						{
							Success = true,
							IsValid = responseText.Equals(challenge),
							Content = response.Content
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = false,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false,
						Content = response.Content
					};
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar el agente web: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GenerateFacebookApplicationAccessToken(string applicationId, string applicationSecret)
		{
			try
			{
				var client = new RestSharp.RestClient("https://graph.facebook.com");

				var request = new RestSharp.RestRequest("oauth/access_token", RestSharp.Method.GET);
				request.AddQueryParameter("client_id", applicationId);
				request.AddQueryParameter("client_secret", applicationSecret);
				request.AddQueryParameter("grant_type", "client_credentials");

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					return new
					{
						Success = true,
						IsValid = true,
						Content = response.Content
					};
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false,
						Content = response.Content,
						ResponseStatus = response.ResponseStatus
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateFacebookApplicationWebhooksUrl(string url)
		{
			try
			{
				var request = System.Net.HttpWebRequest.CreateHttp(url);

				using (var response = (System.Net.HttpWebResponse) request.GetResponse())
				{
					if (response.StatusCode == HttpStatusCode.OK &&
						response.ContentType.StartsWith("application/json", StringComparison.InvariantCultureIgnoreCase))
					{
						using (var stream = response.GetResponseStream())
						using (var sr = new StreamReader(stream))
						using (var jsonReader = new Newtonsoft.Json.JsonTextReader(sr))
						{
							var jResponse = Newtonsoft.Json.Linq.JObject.ReadFrom(jsonReader);
							return new
							{
								Success = true,
								IsValid = jResponse["valid"].ToObject<bool>()
							};
						}
					}
				}

				return new
				{
					Success = true,
					IsValid = false
				};
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar el agente web: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateFacebookApplicationAccessToken(string applicationId, string applicationAccessToken)
		{
			try
			{
				var result = SocialServices.Facebook.FacebookTokens.GetAccessTokenInfo(applicationAccessToken, applicationAccessToken);
				if (result.IsValid && result.AppId == long.Parse(applicationId))
				{
					return new
					{
						Success = true,
						IsValid = true,
						Application = new
						{
							ID = applicationId,
							Name = result.Application
						}
					};
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false
					};
				}

			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}
		[System.Web.Services.WebMethod]
		public static object AccessYFlow(string accessToken, string flowType = "YFlow")
		{
			try
			{
				if (string.IsNullOrEmpty(accessToken))
				{
					return new
					{
						Success = false,
						Error = new
						{
							Code = 0,
							Message = "Invalid accessToken"
						}
					};
				}

				Licensing.LicenseType license = Licensing.LicenseManager.Instance.License;

				// Dependiendo del flowType, cargamos la configuración de YFlow o YFlowContingency
				var yflowSettings = flowType == "YFlowContingency"
					? DomainModel.SystemSettings.Instance.YFlowContingency
					: DomainModel.SystemSettings.Instance.YFlow;

				if (yflowSettings.Enabled)
				{
					yflowSettings.AccessToken = accessToken;
					DAL.SystemSettingsDAO.Update();

					return new
					{
						Success = true,
					};
				}

				return new
				{
					Success = false,
					Error = new
					{
						Code = 0,
						Message = $"{flowType} not enabled"
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object BuildAuthorizationUriForYFlow()
		{
			try
			{
				Licensing.LicenseType license = Licensing.LicenseManager.Instance.License;
				var yflowSettings = DomainModel.SystemSettings.Instance.YFlow;

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow)
				{
					var url = yflowSettings.GetFrontEndUrl(Licensing.LicenseManager.Instance.License.Configuration.YFlowCompany);
					url += url.PadRight(1).Equals('/') ? "" : "/";
					return new
					{
						Success = true,
						Uri = url + "generateaccesstoken?redirect_uri=" + yflowSettings.UrlToken
					};
				}
				return new
				{
					Success = false,
					Error = new
					{
						Code = 0,
						Message = "YFlow not enable"
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object BuildAuthorizationUriForYFlowContingency()
		{
			try
			{
				Licensing.LicenseType license = Licensing.LicenseManager.Instance.License;
				var yflowSettings = DomainModel.SystemSettings.Instance.YFlowContingency;

				if (Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled)
				{
					var url = yflowSettings.GetFrontEndUrl(Licensing.LicenseManager.Instance.License.Configuration.YFlowCompany);
					url += url.PadRight(1).Equals('/') ? "" : "/";
					return new
					{
						Success = true,
						Uri = url + "generateaccesstoken?redirect_uri=" + yflowSettings.UrlToken
					};
				}
				return new
				{
					Success = false,
					Error = new
					{
						Code = 0,
						Message = "YFlow not enable"
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveTags()
		{
			try
			{
				var settings = DomainModel.SystemSettings.Instance;

				return new
				{
					Success = true,
					Tags = BuildTags(settings)
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object YFlowPendingMessagesGenerateJWT()
		{
			try
			{
				DateTime issuedAt = DateTime.UtcNow;
				long issuedAtUnix = Common.Conversions.DateTimeToUnixTime(issuedAt);

				var token = JWT.Builder.JwtBuilder.Create()
					  .WithAlgorithm(new JWT.Algorithms.HMACSHA256Algorithm()) // symmetric
					  .WithSecret("Y01z3n10SAMangiacaprini!")
					  .AddClaim("iat", issuedAtUnix)
					  .AddClaim("cu", Licensing.LicenseManager.Instance.License.Configuration.ClientID.ToLower())
					  .Encode();

				return new
				{
					Success = true,
					Token = token
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateWhatsAppVoiceCallRecording(string hostname, ushort port, string jwtSecret)
		{
			try
			{
				var token = JWT.Builder.JwtBuilder.Create()
					  .WithAlgorithm(new JWT.Algorithms.HMACSHA256Algorithm()) // symmetric
					  .WithSecret(jwtSecret)
					  .Encode();

				var client = new RestSharp.RestClient($"https://{hostname}:{port}");

				var request = new RestSharp.RestRequest("rest/v2/applications/", RestSharp.Method.GET);
				request.AddHeader("ProxyAuthorization", token);

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("application/json"))
					{
						var responseText = response.Content;
						var jResponse = Newtonsoft.Json.Linq.JObject.Parse(responseText);
						var jApplications = (Newtonsoft.Json.Linq.JArray) jResponse["applications"];

						var applications = jApplications.Select(jApplication => jApplication.ToString());

						return new
						{
							Success = true,
							IsValid = true,
							ExistsApp = applications.Contains(Licensing.LicenseManager.Instance.License.Configuration.VoiceCallsMediaServerAppName)
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = false,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false,
						Content = response.Content
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsYFlowPendingMessagesCallbackUrlValid(string url)
		{
			try
			{
				var client = new RestSharp.RestClient(url);

				var challenge = "grosso";
				var request = new RestSharp.RestRequest("api/flow/pending/status", RestSharp.Method.GET);
				request.AddQueryParameter("hub.mode", "status");
				request.AddQueryParameter("hub.challenge", challenge);
				request.AddQueryParameter("hub.verify_token", "yoizencallback");

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("text/plain"))
					{
						var responseText = response.Content;

						return new
						{
							Success = true,
							IsValid = responseText.Equals(challenge),
							Content = response.Content
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = false,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false,
						Content = response.Content
					};
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar la url de callback de casos pendientes de yflow: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

        private bool ValidateYUsageToken(YUsageSettings yUsageSettings)
        {
            string url = textboxYUsageUrlApi.Text;
            if (string.IsNullOrEmpty(url))
                return false;

            try
            {
                var client = new RestClient(url);
                var request = new RestRequest("api/auth/validate_token", Method.POST);
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Authorization", $"Bearer {yUsageSettings.AccessToken}");

                IRestResponse response = client.Execute(request);
                Common.Tracer.TraceVerb("Validación de token de yUsage: {0}", response.Content);

                return response.IsSuccessful;
            }
            catch (Exception ex)
            {
                Common.Tracer.TraceError("Error al validar token de yUsage: {0}", ex.Message);
                return false;
            }
        }



        [System.Web.Services.WebMethod]
		public static object GenerateYUsageToken()
		{
			try
			{
				var settings = DomainModel.SystemSettings.Instance;
				if (!settings.YUsage.Enabled)
				{
					return new
					{
						Success = false,
						Error = new { Code = 0, Message = "YUsage no esta Habilitado" }
					};
				}

				var client = new RestClient(settings.YUsage.UrlApi);
				var request = new RestRequest("api/auth/generate_token", Method.POST);
				request.AddJsonBody(new { clientName = Licensing.LicenseManager.Instance.License.Configuration.ClientID });

				IRestResponse response = client.Execute(request);
                if (response.IsSuccessful)
                {
                    var json = Newtonsoft.Json.Linq.JObject.Parse(response.Content);
                    var token = (string)json["token"];

                    if (!string.IsNullOrEmpty(token))
                    {
                        settings.YUsage.AccessToken = token;
                        DAL.SystemSettingsDAO.Update();

                        return new { Success = true };
                    }
                }


                return new
				{
					Success = false,
					Error = new { Message = "Fallo al generar el token" }
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new { Message = ex.Message, StackTrace = ex.StackTrace }
				};
			}
		}
		#endregion
	}
}