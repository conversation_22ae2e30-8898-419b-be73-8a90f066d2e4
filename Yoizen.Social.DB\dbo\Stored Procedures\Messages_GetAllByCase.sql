﻿CREATE PROCEDURE [dbo].[Messages_GetAllByCase]
	@CaseID bigint
AS
BEGIN
	SET NOCOUNT ON;
	
	IF @CaseID < [dbo].[fInitialValueForCases]()
	BEGIN
		IF EXISTS(SELECT 1 FROM [CasesOld] WITH (NOLOCK) WHERE [CaseID] = @CaseID)
		BEGIN
			SELECT 
				[MessagesOld].*
				FROM [CasesMessages] WITH (NOLOCK)
					INNER JOIN [MessagesOld] WITH (NOLOCK)
						ON [CasesMessages].[MessageID] = [MessagesOld].[MessageID]
				WHERE [CasesMessages].[CaseID] = @CaseID
				ORDER BY [CasesMessages].[Index];

			RETURN;
		END;
	END
	
	SELECT 
		[Messages].*
		FROM [Messages] WITH (NOLOCK,INDEX([IX_Messages_ByCase]))
		WHERE [CaseID] = @CaseID
		ORDER BY [Messages].[MessageID];
END