[CmdletBinding()]
param(
    [string]$Configuration = "ReleaseEncrypted",
    [string]$VersioningScheme = "Semantic",
    [string]$Version = "9.9.9.9",
    [string]$MSBuildPath = "" # Nuevo parámetro opcional
)

function Find-MSBuildPath {
    $possiblePaths = @(
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe"
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            return $path
        }
    }

    try {
        $vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
        if (Test-Path $vswhere) {
            $msbuildPath = & $vswhere -latest -requires Microsoft.Component.MSBuild -find "MSBuild\**\Bin\MSBuild.exe" | Select-Object -First 1
            if ($msbuildPath -and (Test-Path $msbuildPath)) {
                return $msbuildPath
            }
        }
    } catch {
        Write-Warning "No se pudo ejecutar vswhere.exe"
    }

    throw "No se pudo encontrar MSBuild.exe. Asegúrese de tener instaladas las Visual Studio Build Tools."
}

try {
    $msbuildPath = if ($MSBuildPath) { $MSBuildPath } else { Find-MSBuildPath }
    Write-Host "MSBuild encontrado en: $msbuildPath" -ForegroundColor Green

    & $msbuildPath Build-Pipe.proj -t:Version `
        /p:BuildConfiguration=$Configuration `
        /p:VersioningScheme=$VersioningScheme `
        /p:VersionFromPipeline=$Version `
        /p:MSBuildPath=$msbuildPath

    if ($LASTEXITCODE -ne 0) {
        throw "Error al ejecutar MSBuild (Código $LASTEXITCODE)"
    }

    Write-Host "Versión actualizada correctamente." -ForegroundColor Green
} catch {
    Write-Host "ERROR: $_" -ForegroundColor Red
    exit 1
}