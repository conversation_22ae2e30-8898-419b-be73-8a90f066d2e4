trigger:
  branches:
    include:
      - dev

pool:
  vmImage: 'windows-latest'

variables:
  - name: buildConfiguration
    value: 'Release'
  
  - name: buildPlatform
    value: 'AnyCPU'

  - group: StorageSecrets
  - group: global-variables

jobs:

- job: Build_ySocial_Web
  displayName: 'Build ySocial Web'
  steps:
  - task: MSBuild@1
    displayName: 'Clean solution'
    inputs:
      solution: '**/Yoizen.Social.Web.csproj'
      configuration: '$(buildConfiguration)'
      platform: '$(buildPlatform)'
      msbuildArguments: '/t:Clean'
  - task: NuGetCommand@2
    displayName: 'NuGet Restore'
    inputs:
      command: 'restore'
      restoreSolution: '**/Yoizen.Social.sln'
  - task: MSBuild@1
    displayName: 'Build solution'
    inputs:
      solution: '**/Yoizen.Social.Web.csproj'
      configuration: '$(buildConfiguration)'
      platform: '$(buildPlatform)'
      msbuildArguments: '/p:DeployOnBuild=true /p:PublishProfile="Social - Release" /p:publishUrl="$(Build.ArtifactStagingDirectory)\_PublishedWebsites\Yoizen.Social.Web"'

  - task: ArchiveFiles@2
    displayName: 'Create ZIP file'
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/_PublishedWebsites/Yoizen.Social.Web'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip'
      replaceExistingArchive: true
    
  - task: PublishBuildArtifacts@1
    displayName: 'Publish ZIP artifact'
    inputs:
      pathToPublish: '$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip'
      artifactName: 'Social-Web'
  - task: AzureCLI@2
    displayName: 'Upload ZIP to Blob Storage'
    inputs:
      azureSubscription: 'devops-pipeline-sp'
      scriptType: 'ps'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az storage blob upload --account-name "$(StorageAccountName)" --container-name "$(ContainerName)" --file "$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip" --name "dev/Yoizen.Social.Web.zip" --overwrite --auth-mode key --account-key "$(StorageAccountKey)"

- job: Notify_GoogleChat
  displayName: 'Notificar Resultado del Pipeline a Google Chat'
  dependsOn:
    - Build_ySocial_Web
  condition: always()
  steps:
  - checkout: none

  - task: PowerShell@2
    displayName: 'Notificar Éxito a Google Chat'
    condition: succeeded()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "✅ *Pipeline ySocial-Web-CI-DEV completado exitosamente*.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers

  - task: PowerShell@2
    displayName: 'Notificar Falla a Google Chat'
    condition: failed()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "❌ *Pipeline ySocial-Web-CI-DEV falló*. Revisar errores.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers
