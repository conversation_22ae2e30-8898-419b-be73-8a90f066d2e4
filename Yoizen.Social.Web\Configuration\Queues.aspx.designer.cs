﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


	public partial class Queues
	{

		/// <summary>
		/// messageError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageError;

		/// <summary>
		/// messageNoAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoAgents;

		/// <summary>
		/// messageNoSupervisors control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSupervisors;

		/// <summary>
		/// messageNoTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoTags;

		/// <summary>
		/// panelContent control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelContent;

		/// <summary>
		/// panelListado control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelListado;

		/// <summary>
		/// hiddenQueueIDToCopy control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenQueueIDToCopy;

		/// <summary>
		/// checkboxCopyServiceLevel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopyServiceLevel;

		/// <summary>
		/// checkboxCopyUsers control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopyUsers;

		/// <summary>
		/// checkboxCopyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopyTags;

		/// <summary>
		/// placeholderCopySurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderCopySurveys;

		/// <summary>
		/// checkboxCopySurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopySurveys;

		/// <summary>
		/// checkboxCopyEWT control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopyEWT;

		/// <summary>
		/// checkboxCopyAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCopyAutomaticActions;

		/// <summary>
		/// messageCouldntCheckQueueCanBeDeleted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageCouldntCheckQueueCanBeDeleted;

		/// <summary>
		/// messageQueueCannotBeDeleted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageQueueCannotBeDeleted;

		/// <summary>
		/// messageQueueCanBeDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageQueueCanBeDisabled;

		/// <summary>
		/// messageQueueCanBeDeleted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageQueueCanBeDeleted;

		/// <summary>
		/// messageQueueCanBeDeletedAndIsDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageQueueCanBeDeletedAndIsDisabled;

		/// <summary>
		/// messageTaskQueuessThatCanTransferToQueueEmpty control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageTaskQueuessThatCanTransferToQueueEmpty;

		/// <summary>
		/// hiddenActionQueueID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenActionQueueID;

		/// <summary>
		/// hiddenActionName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenActionName;

		/// <summary>
		/// buttonAction control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonAction;

		/// <summary>
		/// messageNoQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoQueues;

		/// <summary>
		/// buttonNew control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonNew;

		/// <summary>
		/// buttonExport control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonExport;

		/// <summary>
		/// panelEdition control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEdition;

		/// <summary>
		/// hiddenSurveyList control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveyList;

		/// <summary>
		/// textboxName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxName;

		/// <summary>
		/// customvalidatorName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorName;

		/// <summary>
		/// textboxDescription control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxDescription;

		/// <summary>
		/// textboxKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxKey;

		/// <summary>
		/// customvalidatorKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorKey;

		/// <summary>
		/// liTabBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabBehaviour;

		/// <summary>
		/// liTabServiceLevel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabServiceLevel;

		/// <summary>
		/// liTabUsers control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabUsers;

		/// <summary>
		/// liTabTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabTags;

		/// <summary>
		/// liTabSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabSurveys;

		/// <summary>
		/// liTabEwt control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabEwt;

		/// <summary>
		/// liTabAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabAutomaticActions;

		/// <summary>
		/// liTabVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabVideo;

		/// <summary>
		/// divQueueBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueBehaviour;

		/// <summary>
		/// textboxMinutesToWaitForAgent control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMinutesToWaitForAgent;

		/// <summary>
		/// textboxMaxReservedMessagesPerAgent control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxReservedMessagesPerAgent;

		/// <summary>
		/// dropdownlistReserveConditions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistReserveConditions;

		/// <summary>
		/// listboxDontReserveWithStatus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxDontReserveWithStatus;

		/// <summary>
		/// checkboxDeleteReservedWithStatus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDeleteReservedWithStatus;

		/// <summary>
		/// checkboxRenewReserveTimeWhenGrouping control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxRenewReserveTimeWhenGrouping;

		/// <summary>
		/// panelReturnMessagesToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelReturnMessagesToQueue;

		/// <summary>
		/// checkboxAllowAgentsToReturnMessagesToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesToQueue;

		/// <summary>
		/// textboxMinutesNotAssignToPreviousAgent control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMinutesNotAssignToPreviousAgent;

		/// <summary>
		/// panelAllowAgentsToReturnMessagesToSpecifiedQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelAllowAgentsToReturnMessagesToSpecifiedQueue;

		/// <summary>
		/// checkboxAllowAgentsToSelectQueueOnReturnToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToSelectQueueOnReturnToQueue;

		/// <summary>
		/// listboxQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxQueues;

		/// <summary>
		/// placeholderQueueWorkingHoursForReceivingMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderQueueWorkingHoursForReceivingMessages;

		/// <summary>
		/// checkboxQueueConnectedAgentsForReceivingMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueConnectedAgentsForReceivingMessages;

		/// <summary>
		/// checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSupervisors control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSupervisors;

		/// <summary>
		/// checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSL control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSL;

		/// <summary>
		/// hiddenQueueWorkingHoursForReceivingMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenQueueWorkingHoursForReceivingMessages;

		/// <summary>
		/// checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors;

		/// <summary>
		/// checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL;

		/// <summary>
		/// dropdownlistMailSignatureBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailSignatureBehaviour;

		/// <summary>
		/// textboxMailSignature control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSignature;

		/// <summary>
		/// messageMailSignature control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailSignature;

		/// <summary>
		/// textboxAgentIdleMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAgentIdleMinutes;

		/// <summary>
		/// checkboxAgentIdleReturnToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAgentIdleReturnToQueue;

		/// <summary>
		/// textboxFinalUserIdleMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFinalUserIdleMinutes;

		/// <summary>
		/// divQueueServiceLevel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueServiceLevel;

		/// <summary>
		/// panelQueueServiceLevel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelQueueServiceLevel;

		/// <summary>
		/// textboxQueueServiceLevelSeconds control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueServiceLevelSeconds;

		/// <summary>
		/// panelServiceLevelMinutesActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl panelServiceLevelMinutesActions;

		/// <summary>
		/// checkboxSLMinutesActionsAssignToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsAssignToQueue;

		/// <summary>
		/// messageSLMinutesActionsAssignToQueueDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSLMinutesActionsAssignToQueueDisabled;

		/// <summary>
		/// dropdownlistSLMinutesActionsAssignToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox dropdownlistSLMinutesActionsAssignToQueue;

		/// <summary>
		/// checkboxSLMinutesActionsNotify control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsNotify;

		/// <summary>
		/// checkboxSLMinutesActionsDiscard control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsDiscard;

		/// <summary>
		/// checkboxSLMinutesActionsDiscardAndCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsDiscardAndCloseCase;

		/// <summary>
		/// checkboxSLMinutesActionsAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsAutoReply;

		/// <summary>
		/// textboxSLMinutesActionsAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLMinutesActionsAutoReply;

		/// <summary>
		/// spanSLMinutesAutoReplyCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanSLMinutesAutoReplyCounter;

		/// <summary>
		/// messageSLMinutesAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSLMinutesAutoReply;

		/// <summary>
		/// checkboxSLMinutesActionsAutoReplyIfAlreadyForCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsAutoReplyIfAlreadyForCase;

		/// <summary>
		/// checkboxSLChatNotify control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLChatNotify;

		/// <summary>
		/// textboxSLChatNotifyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLChatNotifyText;

		/// <summary>
		/// dropdownlistSLChatNotifyAndFinish control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistSLChatNotifyAndFinish;

		/// <summary>
		/// checkboxSLMinutesActionsAddTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsAddTags;

		/// <summary>
		/// textboxSLMinutesActionsTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLMinutesActionsTags;

		/// <summary>
		/// checkboxChatAvailableDaysAndTimes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxChatAvailableDaysAndTimes;

		/// <summary>
		/// messageTimeZoneWarning control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageTimeZoneWarning;

		/// <summary>
		/// hiddenChatAvailableDaysAndTimes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenChatAvailableDaysAndTimes;

		/// <summary>
		/// checkboxSLMinutesActionsVIM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesActionsVIM;

		/// <summary>
		/// panelQueueSLExpired control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelQueueSLExpired;

		/// <summary>
		/// textboxQueueServiceLevelExpired control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueServiceLevelExpired;

		/// <summary>
		/// panelServiceLevelExpiredActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl panelServiceLevelExpiredActions;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsAssignToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsAssignToQueue;

		/// <summary>
		/// messageSLMinutesExpiredActionsAssignToQueueDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSLMinutesExpiredActionsAssignToQueueDisabled;

		/// <summary>
		/// dropdownlistSLMinutesExpiredActionsAssignToQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox dropdownlistSLMinutesExpiredActionsAssignToQueue;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsNotify control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsNotify;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsDiscard control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsDiscard;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsDiscardAndCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsDiscardAndCloseCase;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsAutoReply;

		/// <summary>
		/// textboxSLMinutesExpiredActionsAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLMinutesExpiredActionsAutoReply;

		/// <summary>
		/// spanSLMinutesExpiredAutoReplyCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanSLMinutesExpiredAutoReplyCounter;

		/// <summary>
		/// messageSLMinutesExpiredActionsAutoReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSLMinutesExpiredActionsAutoReply;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase;

		/// <summary>
		/// checkboxSLExpiredChatNotify control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLExpiredChatNotify;

		/// <summary>
		/// textboxSLExpiredChatNotifyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLExpiredChatNotifyText;

		/// <summary>
		/// dropdownlistSLExpiredChatNotifyAndFinish control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistSLExpiredChatNotifyAndFinish;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsAddTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsAddTags;

		/// <summary>
		/// textboxSLMinutesExpiredActionsTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSLMinutesExpiredActionsTags;

		/// <summary>
		/// checkboxSLMinutesExpiredActionsVIM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSLMinutesExpiredActionsVIM;

		/// <summary>
		/// divQueueUsers control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueUsers;

		/// <summary>
		/// listboxSupervisors control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSupervisors;

		/// <summary>
		/// listboxUsers control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxUsers;

		/// <summary>
		/// divQueueTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueTags;

		/// <summary>
		/// panelQueueTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelQueueTags;

		/// <summary>
		/// textboxTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTags;

		/// <summary>
		/// hiddenTagGroups control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenTagGroups;

		/// <summary>
		/// divQueueSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueSurveys;

		/// <summary>
		/// panelQueueSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelQueueSurveys;

		/// <summary>
		/// messageNoQueueSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoQueueSurveys;

		/// <summary>
		/// panelEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEnableSurveys;

		/// <summary>
		/// checkboxEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

		/// <summary>
		/// messageNoSurveysInTable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveysInTable;

		/// <summary>
		/// messageSurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyDisabled;

		/// <summary>
		/// dropdownQueueSurvey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownQueueSurvey;

		/// <summary>
		/// textboxQueueSurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyInvitation;

		/// <summary>
		/// messageFilterEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

		/// <summary>
		/// textboxSurveyInvitationInteractive control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationInteractive;

		/// <summary>
		/// message1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message1;

		/// <summary>
		/// textboxSurveyInvitationButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationButton;

		/// <summary>
		/// message2 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message2;

		/// <summary>
		/// textboxQueueSurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyExpiration;

		/// <summary>
		/// checkboxQueueSurveySendMailIfFailed control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueueSurveySendMailIfFailed;

		/// <summary>
		/// checkboxSurveyEnabledForChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveyEnabledForChat;

		/// <summary>
		/// textboxQueueSurveyEmailFrom control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyEmailFrom;

		/// <summary>
		/// textboxQueueSurveyEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyEmailSubject;

		/// <summary>
		/// textboxQueueSurveyEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyEmailTemplate;

		/// <summary>
		/// messageQueueSurveyEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageQueueSurveyEmailTemplate;

		/// <summary>
		/// textboxQueueSurveySentRate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveySentRate;

		/// <summary>
		/// textboxQueueSurveyTimeToSend control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyTimeToSend;

		/// <summary>
		/// dropdownlistQueueSurveyCloseCondition control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistQueueSurveyCloseCondition;

		/// <summary>
		/// textboxSurveysTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysTags;

		/// <summary>
		/// hiddenSurveyTagGroup control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveyTagGroup;

		/// <summary>
		/// textboxQueueSurveyMessagesCount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyMessagesCount;

		/// <summary>
		/// textboxQueueSurveyCaseDuration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxQueueSurveyCaseDuration;

		/// <summary>
		/// dropdownlistQueueSurveyWithAgentReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistQueueSurveyWithAgentReply;

		/// <summary>
		/// checkboxSurveySendIfNewCaseExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseExists;

		/// <summary>
		/// checkboxSurveySendIfNewCaseHasTag control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseHasTag;

		/// <summary>
		/// checkboxSurveySendIfNewCaseClosedByYflow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseClosedByYflow;

		/// <summary>
		/// textboxSurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// textboxSurveyDontSendTotalSendMonthly control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendTotalSendMonthly;

		/// <summary>
		/// textboxSurveysIgnoreTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysIgnoreTags;

		/// <summary>
		/// hiddenSurveyTagGroupToIgnore control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveyTagGroupToIgnore;

		/// <summary>
		/// divQueueEwt control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueEwt;

		/// <summary>
		/// panelQueueEwt control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelQueueEwt;

		/// <summary>
		/// checkboxConfigEwt control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxConfigEwt;

		/// <summary>
		/// textboxMinutesPredictedAht control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMinutesPredictedAht;

		/// <summary>
		/// textboxSecondsEwt control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondsEwt;

		/// <summary>
		/// checkboxASAPersonalized control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxASAPersonalized;

		/// <summary>
		/// textboxAsaBase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAsaBase;

		/// <summary>
		/// divQueueAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divQueueAutomaticActions;

		/// <summary>
		/// panelFirstAutomaticAction control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelFirstAutomaticAction;

		/// <summary>
		/// checkboxAllowFirstAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowFirstAutomaticActions;

		/// <summary>
		/// textboxFirstAutomaticActionSeconds control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionSeconds;

		/// <summary>
		/// panelAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl panelAutomaticActions;

		/// <summary>
		/// checkboxFirstAutomaticActionsTransferQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFirstAutomaticActionsTransferQueue;

		/// <summary>
		/// messageFirstActionTransferQueueDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstActionTransferQueueDisabled;

		/// <summary>
		/// dropdownlistFirstAutomaticActionQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox dropdownlistFirstAutomaticActionQueues;

		/// <summary>
		/// checkboxFirstAutomaticActionReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFirstAutomaticActionReply;

		/// <summary>
		/// textboxFirstAutomaticActionReplyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyText;

		/// <summary>
		/// spanFirstAutomaticActionReplyTextCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanFirstAutomaticActionReplyTextCounter;

		/// <summary>
		/// messageFirstAutomaticActionReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstAutomaticActionReply;

		/// <summary>
		/// textboxFirstAutomaticActionMinimumEWT control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionMinimumEWT;

		/// <summary>
		/// textboxFirstAutomaticActionMinimumEnqueueMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionMinimumEnqueueMessages;

		/// <summary>
		/// textboxFirstAutomaticActionReplyEwtNoAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyEwtNoAgents;

		/// <summary>
		/// spanFirstAutomaticActionReplyEwtNoAgentsCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanFirstAutomaticActionReplyEwtNoAgentsCounter;

		/// <summary>
		/// messageFirstActionEwtNoAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstActionEwtNoAgents;

		/// <summary>
		/// textboxFirstAutomaticActionReplyEwtNotComputed control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyEwtNotComputed;

		/// <summary>
		/// spanFirstAutomaticActionReplyEwtNotComputedCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanFirstAutomaticActionReplyEwtNotComputedCounter;

		/// <summary>
		/// messageFirstActionEwtNotComputed control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstActionEwtNotComputed;

		/// <summary>
		/// checkboxFirstAutomaticActionNotifyChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFirstAutomaticActionNotifyChat;

		/// <summary>
		/// textboxFirstAutomaticActionReplyChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyChat;

		/// <summary>
		/// messageFirstAutomaticActionReplyChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstAutomaticActionReplyChat;

		/// <summary>
		/// textboxFirstAutomaticActionMinimumEWTChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionMinimumEWTChat;

		/// <summary>
		/// textboxFirstAutomaticActionMinimumEnqueueMessagesChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionMinimumEnqueueMessagesChat;

		/// <summary>
		/// textboxFirstAutomaticActionReplyEwtNoAgentsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyEwtNoAgentsChat;

		/// <summary>
		/// span1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl span1;

		/// <summary>
		/// messageFirstAutomaticActionReplyEwtNoAgentsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstAutomaticActionReplyEwtNoAgentsChat;

		/// <summary>
		/// textboxFirstAutomaticActionReplyEwtNotCalculatedChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionReplyEwtNotCalculatedChat;

		/// <summary>
		/// span2 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl span2;

		/// <summary>
		/// messageFirstAutomaticActionReplyEwtNotCalculatedChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFirstAutomaticActionReplyEwtNotCalculatedChat;

		/// <summary>
		/// dropdownlistFirstAutomaticActionMarkAsFinishChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistFirstAutomaticActionMarkAsFinishChat;

		/// <summary>
		/// checkboxFirstAutomaticActionApplyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFirstAutomaticActionApplyTags;

		/// <summary>
		/// textboxFirstAutomaticActionsTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFirstAutomaticActionsTags;

		/// <summary>
		/// panelSecondAutomaticAction control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelSecondAutomaticAction;

		/// <summary>
		/// checkboxAllowSecondAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowSecondAutomaticActions;

		/// <summary>
		/// textboxSecondAutomaticActionsSeconds control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionsSeconds;

		/// <summary>
		/// panelSecondAutomaticActions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl panelSecondAutomaticActions;

		/// <summary>
		/// checkboxSecondAutomaticActionsTransferQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSecondAutomaticActionsTransferQueue;

		/// <summary>
		/// messageSecondAutomaticActionTransferQueueDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSecondAutomaticActionTransferQueueDisabled;

		/// <summary>
		/// dropdownlistSecondAutomaticActionQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox dropdownlistSecondAutomaticActionQueues;

		/// <summary>
		/// checkboxSecondAutomaticActionReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSecondAutomaticActionReply;

		/// <summary>
		/// textboxSecondAutomaticActionReplyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionReplyText;

		/// <summary>
		/// spanSecondAutomaticActionReplyTextCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanSecondAutomaticActionReplyTextCounter;

		/// <summary>
		/// messageSecondAutomaticActionReply control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSecondAutomaticActionReply;

		/// <summary>
		/// textboxSecondAutomaticActionMinimumEWT control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionMinimumEWT;

		/// <summary>
		/// textboxSecondAutomaticActionMinimumEnqueueMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionMinimumEnqueueMessages;

		/// <summary>
		/// textboxSecondAutomaticActionReplyEwtNoAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionReplyEwtNoAgents;

		/// <summary>
		/// spanSecondAutomaticActionReplyEwtNoAgentsCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanSecondAutomaticActionReplyEwtNoAgentsCounter;

		/// <summary>
		/// messageSecondAutomaticReplyEwtNoAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSecondAutomaticReplyEwtNoAgents;

		/// <summary>
		/// textboxSecondAutomaticActionReplyEwtNotComputed control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionReplyEwtNotComputed;

		/// <summary>
		/// spanSecondAutomaticActionReplyEwtNotComputedCounter control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanSecondAutomaticActionReplyEwtNotComputedCounter;

		/// <summary>
		/// messageSecondAutomaticReplyEwtNotCalculated control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSecondAutomaticReplyEwtNotCalculated;

		/// <summary>
		/// checkboxSecondAutomaticActionNotifyChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSecondAutomaticActionNotifyChat;

		/// <summary>
		/// textboxSecondAutomaticActionChatReplyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionChatReplyText;

		/// <summary>
		/// messageSecondAutomaticActionChatReplyText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSecondAutomaticActionChatReplyText;

		/// <summary>
		/// textboxSecondAutomaticActionMinimumEWTChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionMinimumEWTChat;

		/// <summary>
		/// textboxSecondAutomaticActionMinimumEnqueueMessagesChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionMinimumEnqueueMessagesChat;

		/// <summary>
		/// textboxSecondAutomaticActionReplyEwtNoAgentsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionReplyEwtNoAgentsChat;

		/// <summary>
		/// span3 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl span3;

		/// <summary>
		/// message3 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message3;

		/// <summary>
		/// textboxSecondAutomaticActionReplyEwtNotCalculatedChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionReplyEwtNotCalculatedChat;

		/// <summary>
		/// span4 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl span4;

		/// <summary>
		/// message4 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message4;

		/// <summary>
		/// dropdownlistSecondAutomaticActionMarkAsFinishChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistSecondAutomaticActionMarkAsFinishChat;

		/// <summary>
		/// checkboxSecondAutomaticActionApplyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSecondAutomaticActionApplyTags;

		/// <summary>
		/// textboxSecondAutomaticActionsTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSecondAutomaticActionsTags;

		/// <summary>
		/// panelVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelVideo;

		/// <summary>
		/// checkboxEnableVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableVideo;

		/// <summary>
		/// dropdownlistVideoProject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistVideoProject;

		/// <summary>
		/// textboxVideoDefaultText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxVideoDefaultText;

		/// <summary>
		/// ErrorMessageVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message ErrorMessageVideo;

		/// <summary>
		/// buttonSave control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonSave;

		/// <summary>
		/// buttonCancel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCancel;

		/// <summary>
		/// hiddenExportFormat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenExportFormat;
	}
}
