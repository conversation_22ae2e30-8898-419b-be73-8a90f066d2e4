﻿<?xml version="1.0"?>
<configuration>
	<system.webServer>
		<validation validateIntegratedModeConfiguration="false"/>
		<handlers>
			<add name="ConfigurationServices" verb="*" path="Configuration" type="Yoizen.Social.Web.Services.ConfigurationHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="AgentsServices" verb="*" path="Agents" type="Yoizen.Social.Web.Services.AgentsHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="MessagingServices" verb="*" path="Messaging" type="Yoizen.Social.Web.Services.MessagingHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="ChatServices" verb="*" path="Chat" type="Yoizen.Social.Web.Services.ChatHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="RestChatServices" verb="*" path="RestChat" type="Yoizen.Social.Web.Services.RestChatHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="ReportsServices" verb="*" path="Reports" type="Yoizen.Social.Web.Services.ReportsHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="FlowServices" verb="*" path="Flow" type="Yoizen.Social.Web.Services.FlowHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="CasesServices" verb="*" path="Cases" type="Yoizen.Social.Web.Services.CasesHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="MediaServices" verb="*" path="Media" type="Yoizen.Social.Web.Services.MediaHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="TasksServices" verb="*" path="Tasks" type="Yoizen.Social.Web.Services.TasksHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="SocialUsersServices" verb="*" path="SocialUsers" type="Yoizen.Social.Web.Services.SocialUsersHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="SupervisorsServices" verb="*" path="Supervisors" type="Yoizen.Social.Web.Services.SupervisorsHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="SystemServices" verb="*" path="System" type="Yoizen.Social.Web.Services.SystemHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="StatusServices" verb="*" path="Status" type="Yoizen.Social.Web.Services.StatusHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="CallsServices" verb="*" path="Calls" type="Yoizen.Social.Web.Services.CallsHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
			<add name="EmailContactsHandler" verb="*" path="EmailContacts" type="Yoizen.Social.Web.Services.EmailContactsHandler, Yoizen.Social.Web" resourceType="Unspecified"/>
		</handlers>
		<httpErrors errorMode="Custom" existingResponse="Auto" defaultResponseMode="File">
			<remove statusCode="400" />
			<error statusCode="400" path="../ErrorPages/400.aspx?error=1" responseMode="Redirect" prefixLanguageFilePath="" />
			<remove statusCode="404" />
			<error statusCode="404" path="../ErrorPages/404.aspx?error=1" responseMode="Redirect" prefixLanguageFilePath="" />
			<remove statusCode="401" />
			<error statusCode="401" path="../ErrorPages/404.aspx?error=1" responseMode="Redirect" prefixLanguageFilePath="" />
			<remove statusCode="403" />
			<error statusCode="403" path="../ErrorPages/404.aspx?error=1" responseMode="Redirect" prefixLanguageFilePath="" />
			<remove statusCode="500" />
			<error statusCode="500" path="../ErrorPages/400.aspx?error=1" responseMode="Redirect" prefixLanguageFilePath="" />
		</httpErrors>
	</system.webServer>
</configuration>
