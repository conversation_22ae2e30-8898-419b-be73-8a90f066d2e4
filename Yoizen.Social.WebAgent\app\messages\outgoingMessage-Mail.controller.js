(function () {

    'use strict';

    angular.module('socialApp')
        .controller('OutgoingMessageMailController', OutgoingMessageMailController);

    OutgoingMessageMailController.$inject = [
        'messagesService',
        'agentService',
        'settingsService',
        'toastr',
        'utilsService',
        'outgoingService',
        '$scope',
        '$translate',
        'modalSocialService',
        'integrationsService',
        'statesService',
        '$timeout',
        'commonActionsService',
        'traceService'
    ];

    function OutgoingMessageMailController(messagesService,
        agentService,
        settingsService,
        toastr,
        utilsService,
        outgoingService,
        $scope,
        $translate,
        modalSocialService,
        integrationsService,
        statesService,
        $timeout,
        commonActionsService,
        traceService) {

        $scope.mailServicesAvailableToSelect = [];
        $scope.currentService = null;

        var vm = this;
        var defaultFontSize, defaultFontFamily, noDomainConfigured, signature;
        var mailEditor = null;
        vm.message = {
            mailTo: undefined,
            socialUser: undefined,
            subject: undefined,
            userNotFound: false,
            serviceId: undefined,
            service: undefined,
            text: undefined,
            useCC: false,
            useCCO: false,
            principalQueue: [],
            attachments: [],
            mailCCO: [],
            mailCC: [],
            options: {}
        };
        vm.fontTypes = FontTypes;
        vm.searchMailUsers = searchMailUsers;
        vm.emojiClicked = emojiClicked;
        vm.showFiltersSearch = true;
        vm.showValidationUser = false;
        vm.showResultsSearch = false;
        vm.selectedService = null;
        vm.stepsData = outgoingService.data;
        /*Seteamos estas dos variables para saber el step actual y los steps disponibles*/
        vm.outgoingStep = outgoingService.data.outgoingStep;
        vm.outgoingMessageSteps = outgoingService.data.outgoingMessageSteps;
        vm.writeNewMail = writeNewMail;
        vm.selectedCurrentCase = null;
        vm.socialCase = messagesService.selectedSocialCase;
        vm.showEditCC = undefined;
        vm.showEditBCC = undefined;
        vm.serviceChanged = serviceChanged;
        vm.mailSubject = undefined;
        vm.send = send;
        vm.close = close;
        vm.isFromNormalCase = false;
        vm.allowedServicesIds = [];
        vm.services = [];
        vm.closeNoChangeState = closeNoChangeState;
        vm.agentService = agentService;
        vm.socialParsed = undefined;
        vm.profileExist = undefined;
        vm.showProfile = undefined;
        vm.showPredefinedAnswers = showPredefinedAnswers;
        vm.showEmailContacts = showEmailContacts;
        vm.attachFiles = attachFiles;
        vm.loadServiceSettings = loadServiceSettings;
        vm.loadUsefulEmails = loadUsefulEmails;
        vm.usefulEmails = [];
        vm.inputMailTo = [];

        if (typeof (vm.chkMarkAsPending) === 'undefined') {
            vm.chkMarkAsPending = false;
        }

        activate();

        function activate() {
            if (typeof (vm.socialCase) !== 'undefined' &&
                typeof (vm.socialCase.data) !== 'undefined' &&
                typeof (vm.socialCase.data.parameters) !== 'undefined' &&
                vm.outgoingStep.selectedStep === 'mail') {
                writeNewMail(true);
            }
            else {
                loadServices();
            }

            if (typeof (settingsService.settings.agent.allowedToMarkAsPending) === 'boolean') {
                vm.allowAgentsToMarkCasesAsPending = settingsService.settings.agent.allowedToMarkAsPending;
            }
        }

        function loadServices() {
            let servicesForOutgoingMessages = [];
            if (settingsService.settings.agent.servicesForOutgoingMessages !== null && !vm.isFromNormalCase) {
                servicesForOutgoingMessages = settingsService.settings.agent.servicesForOutgoingMessages.filter(function (s) {
                    return s.type === ServiceTypes.Mail &&
                        s.enabled;
                });
            }

            let mailServices = settingsService.settings.context.services.filter(s => s.enabled && s.type === ServiceTypes.Mail);
            for (let i = 0; i < mailServices.length; i++) {
                let service = mailServices[i];

                if (vm.isFromNormalCase && vm.allowedServicesIds.length > 0) {
                    if (vm.allowedServicesIds.indexOf(service.id) === -1) {
                        continue;
                    }
                }

                if (servicesForOutgoingMessages === null ||
                    servicesForOutgoingMessages.length === 0 ||
                    servicesForOutgoingMessages.findIndex(function (ms) {
                        return ms.id === this;
                    }, service.id) !== -1) {
                    if (typeof (service.displayText) !== 'string') {
                        if (typeof (service.basicConfiguration) === 'object') {
                            service.displayText = `${service.name} (${service.basicConfiguration.email})`;
                        }
                        else {
                            service.displayText = service.name;
                        }
                    }
                    vm.services.push(service);
                }
            }
        }

        function loadServiceSettings() {
            setUsefulEmails();
            vm.favoriteMails = [];
            vm.showEditCC = vm.message.service.settings.useCC;
            vm.showEditBCC = vm.message.service.settings.useBCC;
            vm.allowAttachment = vm.message.service.settings.allowToSendAttachments;
            defaultFontSize = vm.message.service.settings.answersFontSize + "pt";

            let fontFamily = vm.message.service.settings.answersFontType;
            if (fontFamily === 0) {
                defaultFontFamily = utilsService.getEnumPropertyByValue(8, vm.fontTypes);
            }
            else {
                defaultFontFamily = utilsService.getEnumPropertyByValue(fontFamily, vm.fontTypes);
            }

            if (defaultFontFamily === 'ArialNarrow') {
                defaultFontFamily = 'Arial';
            }
            else if (defaultFontFamily === 'ComicSansMs') {
                defaultFontFamily = 'Comic Sans MS';
            }
            else if (defaultFontFamily === 'MicrosoftSansSerif' || defaultFontFamily === 'Serif' || defaultFontFamily === 'WideLatin') {
                defaultFontFamily = 'Sans-Serif';
            }
            else if (defaultFontFamily === 'TrebuchetMs') {
                defaultFontFamily = 'Trebuchet Ms';
            }

            vm.tinymceOptions = {
                plugins: "paste textcolor colorpicker noneditable code autolink autoresize lists table",
                paste_text_sticky: true,
                paste_text_sticky_default: true,
                paste_data_images: true,
                paste_merge_formats: false,
                /*Excel copy-paste Utility :Starts*/
                paste_retain_style_properties: "all",
                paste_strip_class_attributes: "none",
                /*Excel copy-paste Utility :Ends*/
                automatic_uploads: true,
                mode: 'specific_textareas',
                editor_selector: 'editor-mail',
                relative_urls: false,
                convert_urls: false,
                remove_script_host: true,
                height: 150,
                autoresize_max_height: 300,
                autoresize_min_height: 150,
                resize: false,
                menubar: false,
                forced_root_block: 'div',
                forced_root_block_attrs: {
                    'style': 'font-family: "' + defaultFontFamily + '"; font-size: ' + defaultFontSize
                },
                auto_focus: true,
                menu: false,
                statusbar: false,
                toolbar1: "alignleft aligncenter alignright | bold italic underline | forecolor backcolor " + (vm.message.service.settings.fontChangesAvailable ? "| fontselect fontsizeselect formatselect " : "") + " | numlist bullist | code",
                toolbar2: 'table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
                content_style: "span.mce-spellchecker-word { cursor: text } body.mce-content-body { font-family: '" + defaultFontFamily + "'; font-size: " + defaultFontSize + " }",
                browser_spellcheck: true,
                skin_url: 'app/js_components/skins/lightgray',
                setup: function (editor) {
                    if (mailEditor !== null)
                        return;

                    mailEditor = editor;

                    $timeout(function () {
                        mailEditor.focus();
                    }, 500);

                    editor.on("init", function () {
                        //this.execCommand("fontSize", false, defaultFontSize);
                        //this.execCommand("fontName", false, defaultFontFamily);
                        insertSignature(editor);
                    });

                    editor.on("paste", function (e) {
                        let clipboardData = e.clipboardData;
                        let data = clipboardData.getData('text/html');
                        if (data !== null && data.length > 0 && data.startsWith('<html')) {
                            const iframe = document.createElement('iframe');
                            iframe.style.opacity = 0;
                            iframe.style.position = 'absolute';
                            iframe.style.left = -10000;
                            const blob = new Blob([data], { type: 'text/html' });
                            iframe.addEventListener("load", function () {
                                let body = this.contentDocument.body;
                                let els = body.getElementsByTagName("*");

                                let getStyle = function (className, iframe) {
                                    let cssText = "";
                                    if (Array.isArray(iframe.contentDocument.styleSheets) &&
                                        iframe.contentDocument.styleSheets.length > 0) {
                                        let classes = iframe.contentDocument.styleSheets[0].rules || iframe.contentDocument.styleSheets[0].cssRules;
                                        for (let x = 0; x < classes.length; x++) {
                                            if (classes[x].selectorText === className) {
                                                let cssTextWithClassName = classes[x].style.cssText || classes[x].cssText;
                                                if (cssTextWithClassName.indexOf('{') >= 0) {
                                                    cssTextWithClassName = cssTextWithClassName.substring(cssTextWithClassName.indexOf('{') + 1);
                                                    cssTextWithClassName = cssTextWithClassName.substring(0, cssTextWithClassName.indexOf('}'));
                                                }
                                                cssText += cssTextWithClassName;
                                            }
                                        }
                                    }
                                    return cssText;
                                };

                                for (let i = -1, l = els.length; ++i < l;) {
                                    let el = els[i];
                                    if (el.classList !== null && el.classList.length > 0) {
                                        let styles = '';
                                        for (let j = 0; j < el.classList.length; j++) {
                                            if (styles.length > 0 && !styles.endsWith(';')) {
                                                styles += ';';
                                            }

                                            styles += getStyle(`.${el.classList[j]}`, iframe);
                                        }

                                        el.style.cssText += styles;
                                        el.className = '';
                                    }
                                }

                                // Let TinyMCE do the heavy lifting for inserting that content into the editor.
                                editor.execCommand('mceInsertContent', false, body.outerHTML);

                                document.documentElement.removeChild(iframe);
                            });
                            iframe.src = window.URL.createObjectURL(blob);
                            document.documentElement.appendChild(iframe);

                            e.stopPropagation();
                            e.preventDefault();
                        }
                    });
                },
                images_upload_url: "./api/upload"
            };

            let currentLanguage = $translate.preferredLanguage();
            if (currentLanguage === 'pt') {
                vm.tinymceOptions.language = 'pt_BR';
            }
            else if (currentLanguage === 'en') {
                vm.tinymceOptions.language = 'en_US';
            }
            else if (currentLanguage === 'es') {
                vm.tinymceOptions.language = 'es_MX';
            }

            if (typeof (vm.message.service.settings.favoriteMails) !== 'undefined' &&
                vm.message.service.settings.favoriteMails.length > 0) {
                var favMails = vm.message.service.settings.favoriteMails.split(',');
                for (var i = 0; i < favMails.length; i++) {
                    var mail = {
                        id: i,
                        mailName: favMails[i]
                    };
                    vm.favoriteMails.push(mail);
                }
            }
        }

        function loadUsefulEmails(query) {
            var result = [];
            query = query.toLowerCase();
            vm.usefulEmails.forEach(function (email) {
                if (email.text.toLowerCase().indexOf(query) > -1) {
                    result.push(email);
                }
            });

            return result;
        }

        function setUsefulEmails() {
            vm.usefulEmails = [];
            agentService.getSupervisors()
                .then(function (obj) {
                    var supervisors = utilsService.toCamel(obj.data.Result);
                    supervisors.forEach(function (supervisor) {
                        if (supervisor.email !== null && supervisor.email.length > 0) {
                            
							if (!vm.usefulEmails.some(usefulMail => usefulMail.text === supervisor.email)) {
								vm.usefulEmails.push({
									text: supervisor.email,
									name: `${supervisor.firstName} ${supervisor.lastName}`
								});
							}
                        }
                    });

                    if (typeof (vm.message.service) !== 'undefined' &&
                        vm.message.service !== null) {
                        for (let i = 0; i < settingsService.settings.context.services.length; i++) {
                            let service = settingsService.settings.context.services[i];
                            if (service.id === vm.message.service.id) {
                                let favoriteMails = service.settings.favoriteMails;
                                if (typeof (favoriteMails) !== 'undefined' && favoriteMails !== null && favoriteMails.length > 0) {
                                    favoriteMails = favoriteMails.split(',');
									const usefulEmails = vm.usefulEmails;
                                    
									favoriteMails.forEach(mail => {
                                        if (!usefulEmails.some(usefulEmail => usefulEmail.text === mail)) {
                                            usefulEmails.push({ text: mail });
                                        }
                                    });

									vm.usefulEmails = usefulEmails;
                                }
                                break;
                            }
                        }
                    }

                    vm.usefulEmails.sort(function (a, b) {
                        if (a.text < b.text) return -1;
                        if (a.text > b.text) return 1;
                        return 0;
                    });

                }, function (error) {
                    console.log(error);
                });
        }

        function serviceChanged() {
            vm.message.service = vm.services.find(function (service) {
                return service.id === vm.message.serviceId;
            });

            loadServiceSettings();

            if (typeof (mailEditor) !== 'undefined' && mailEditor !== null) {
                mailEditor.setContent('');
                insertSignature(mailEditor);
            }
        }

        function searchMailUsers(writeNewCase) {
            if(vm.inputMailTo.length > 1)
            {
                toastr.error($translate.instant('OUTGOINGMESSAGE_EMAIL_USER_MAX'));
                return;
            }

            if (typeof (vm.inputMailTo[0]) === 'undefined'){
                return;
            }

            vm.message.mailTo = vm.inputMailTo[0].text;
            vm.showResultsSearch = false;
            vm.profileExist = false;
            if (!vm.message.service.id || !vm.message.mailTo || vm.message.mailTo.indexOf(' ') > -1) {
                if (!vm.message.mailTo) {
                    toastr.error($translate.instant('EMPTY_MAIL_OUTGOING_MESSAGE'));
                }
                else if (vm.message.mailTo.indexOf(' ') > -1) {
                    toastr.error($translate.instant('SPACES_MAIL_OUTGOING_MESSAGE'));
                }
                else if (!$scope.currentService.id) {
                    toastr.error($translate.instant('SERVICE_NOT_SELECTED_OUTGOING_MESSAGE'));
                }
                return;
            }

            var regex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            if (!regex.test(vm.message.mailTo)) {
                toastr.error($translate.instant('INCORRECT_MAIL'));
                return;
            }
            vm.showValidationUser = true;

            utilsService.showLoading();
            agentService.searchMailUser(vm.message.mailTo)
                .then(function (response) {
                    utilsService.hideLoading();
                    for (var s = 0; s < vm.services.length; s++) {
                        if (vm.message.service.id === vm.services[s].id) {
                            vm.selectedService = vm.services[s];
                            break;
                        }
                    }
                    vm.showValidationUser = false;
                    //Encontramos un perfil de usuario
                    if (response.data.Result.ID !== -1) {
                        vm.mailUser = utilsService.toCamel(response.data.Result);
                        vm.socialParsed = utilsService.toCamel(response.data.Result);
                        vm.mailUser.dataCase = {
                            service: vm.selectedService,
                            outgoingMessage: true,
                            isTwitterMessage: false
                        };
                        vm.fakeCase = {
                            postedBy: vm.mailUser,
                            'case': {
                                profile: vm.mailUser.profile
                            },
                            data: {
                                service: vm.selectedService
                            },
                            service: vm.selectedService
                        };
                        vm.profileExist = true;
                    }
                    //No se encontro un perfil de usuario
                    else {
                        vm.fakeCase = {
                            id: -1,
                            outgoing: true,
                            createCase: true,
                            socialUser: vm.inputMailTo,
                            service: vm.selectedService,
                            'case': {
                                profile: {
                                    id: -1,
                                    name: vm.message.mailTo,
                                    primaryEmail: vm.message.mailTo,
                                    businessData: '',
                                    extendedProfilesFields: null
                                }
                            },
                            data: {
                                isDirectMessage: false,
                                id: -1,
                                service: vm.selectedService,
                                socialUser: {
                                    displayName: vm.inputMailTo,
                                    email: vm.inputMailTo,
                                    parameters: {
                                        subject: null
                                    }
                                }
                            }
                        };
                        vm.selectedCurrentCase = null;
                        vm.profileExist = false;
                    }
                    writeNewMail(false);
                    vm.showResultsSearch = true;

                }, function () {
                    toastr.error($translate.instant('MAIL_USER_NOT_FOUND'));
                    vm.profileExist = false;
                    utilsService.hideLoading();
                    vm.showResultsSearch = true;
                });
        }

        function writeNewMail(fromNormalCase) {
            var principal;

            if (typeof (vm.stepsData.predefinedText) != 'undefined' &&
                vm.stepsData.predefinedText != null) {
                principal = vm.stepsData.predefinedText;
            }
            else {
                principal = vm.socialCase.data.body;
            }

            if (fromNormalCase) {
                vm.isFromNormalCase = true;
                vm.message = {
                    mailTo: undefined,
                    socialUser: vm.socialCase.data.socialUser,
                    subject: vm.socialCase.data.parameters.subject,
                    userNotFound: true,
                    serviceId: vm.socialCase.data.service.id,
                    service: vm.socialCase.data.service,
                    text: undefined,
                    useCC: false,
                    useCCO: false,
                    principalQueue: [],
                    attachments: [],
                    mailCCO: [],
                    mailCC: [],
                    options: {},
                    principal: principal
                };
                vm.selectedService = vm.socialCase.data.service;

                if (typeof (vm.socialCase.data.attachments) != 'undefined' &&
                    vm.socialCase.data.hasAttach &&
                    outgoingService.generateOutgoingMailIsAttachingOriginalAttachment) {
                    vm.socialCase.data.attachments.forEach(function (attachment) {
                        // --> Este formato lo necesito para la queue y para el FileUploader
                        var f = {
                            _file: {
                                name: attachment.fileName,
                                size: attachment.fileSize,
                                type: attachment.mimeType,
                                index: attachment.index,
                                messageId: messagesService.selectedSocialCase.data.id
                            },
                            _destroy: function() {
                            }
                        };

                        vm.message.principalQueue.push(f);

                        // --> Este formato lo necesito para mandarlo al backend de Social
                        var att = {};
                        att.MimeType = attachment.mimeType;
                        att.FileName = attachment.fileName;
                        att.FileSize = attachment.fileSize;
                        att.Index = attachment.index;
                        att.MessageID = messagesService.selectedSocialCase.data.id;
                        vm.message.attachments.push(att);

                    });
                }

                loadServiceSettings();

                let multipleRepliesBehaviourConfig = vm.message.service.settings.generateMultipleRepliesBehaviour;
                if (multipleRepliesBehaviourConfig === 1 ||
                    multipleRepliesBehaviourConfig === 3) {
                    if (multipleRepliesBehaviourConfig === 3) {
                        if (typeof vm.message.service.settings.servicesForMultipleRepliesGeneration != 'undefined' &&
                            vm.message.service.settings.servicesForMultipleRepliesGeneration.length > 0) {
                            vm.allowedServicesIds = vm.message.service.settings.servicesForMultipleRepliesGeneration;
                        }
                    }
                    vm.allowedServicesIds.push(vm.message.service.id);
                }

                loadServices();
            }

            vm.showFiltersSearch = false;
        }

        function closeNoChangeState() {
            vm.agentService.isOutgoingMailNoChangeState = false;
            integrationsService.executeActions('outgoingmessagefinished');
        }

        function ccValidation() {
            if (vm.message.mailCC.length === 0) {
                return true;
            }
            return domainsAreValid(vm.message.mailCC);
        }

        function ccoValidation() {
            if (vm.message.mailCCO.length === 0) {
                return true;
            }
            return domainsAreValid(vm.message.mailCCO);
        }

        function unescapeHtml(safe) {
            return safe.replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#039;/g, "'");
        }

        function domainsAreValid(field) {
            var settings = vm.message.service.settings;

            if (settings.answerOutsideDomainAvailable || field.length === 0) return true;

            if (!settings.availableDomains) {
                toastr.error(noDomainConfigured);
                return false;
            }

            var allDomainsValid = true;
            var domains = settings.availableDomains.split(',');

            field.forEach(function (mail) {
                var domain = mail.text.split("@")[1];
                if (domains.indexOf(domain) <= -1) {
                    toastr.error($translate.instant('MAIL_CANNOT_BE_USED_DOMAIN_NOT_CONFIGURED') + " : " + mail.text);
                    allDomainsValid = false;
                }
            });
            return allDomainsValid;
        }

        function insertSignature(editor) {
            let mailSettings = vm.message.service.settings;
            let nonEditableClass = "";
            signature = undefined;

            if (mailSettings.signature) {
                signature = unescapeHtml(mailSettings.signature);

                signature = signature.replace("@@USUARIO@@", settingsService.settings.agent.fullName);

                if ((mailSettings.useSignature && (mailSettings.signatureBehaviour === window.SignatureBehaviours.InsertWhenReplyingReadOnly))) {
                    nonEditableClass = "mceNonEditable";
                }
                signature = "<div id=\"yzn_signature_content\" class=\"" + nonEditableClass + "\"> " + signature + "</div>";
                if ((mailSettings.useSignature && (mailSettings.signatureBehaviour === window.SignatureBehaviours.InsertWhenReplyingReadOnly))) {
                    editor.on("keyUp", function (e) {
                        if (e.currentTarget.innerHTML.indexOf("yzn_signature_content") === -1) {
                            editor.editorCommands.execCommand('mceInsertRawHTML', false, signature);
                        }
                    });
                }
            }

            $timeout(function () {
                if (
                    (mailSettings.useSignature && ((mailSettings.signatureBehaviour === window.SignatureBehaviours.InsertWhenReplyingAndAllowEdit) ||
                        (mailSettings.signatureBehaviour === window.SignatureBehaviours.InsertWhenReplyingReadOnly)))
                ) {
                    editor.editorCommands.execCommand('mceInsertRawHTML', true, "<br/>" + signature);
                }

                editor.selection.select(editor.getBody(), true);
                editor.selection.collapse(true);
                editor.focus();
            }, 300);
        }

        function showPredefinedAnswers() {
            var editor = mailEditor;
            modalSocialService.showPredefinedAnswers(vm.fakeCase, settingsService.settings.agent)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (typeof (result) !== 'object' || typeof (result.textAnswer) !== 'string') {
                            return;
                        }

                        var answerBody = result.textAnswer;
                        if (result.isGlobal && result.textAnswer.includes('\n')) {
                            answerBody = result.textAnswer.replaceAll('\n', '<br>');
                        }

                        editor.insertContent("<span style='font-size:" + defaultFontSize + "'>" + answerBody + "</span>");

                        if (typeof (result.attach) === 'object' &&
                            result.attach !== null &&
                            typeof (result.attach.name) === 'string' &&
                            typeof (result.attach.mimeType) === 'string' &&
                            vm.message.service.settings.allowToSendAttachments) {
                            vm.message.attachments = [];
                            vm.message.principalQueue = [];

                            vm.message.predAttachment = result.attach;
                            addSelectedAttachments(vm.message.predAttachment, true);
                            vm.message.principalQueue.push(vm.message.predAttachment);
                        }
                    });
                });
        }

        function showEmailContacts() {
            modalSocialService.showEmailContacts()
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (result && result.textEmailContact) {
                            const email = result.textEmailContact.trim();
                            vm.inputMailTo.push({text: email});
                            searchMailUsers();
                        }
                    });
                    
                });
        }

        function emojiClicked(emoji, $event) {
            var text = emoji.split('-').map(twemoji.convert.fromCodePoint).join('');
            if (typeof (vm.message.principal) === 'undefined' || vm.message.principal === null)
                vm.message.principal = text;
            else
                vm.message.principal += text;
            $event.stopPropagation();
        }

        function attachFiles(principal) {
            var files;
            if (principal) {
                files = vm.message.principalQueue;
            }

            modalSocialService.showFileUploader(files, vm.fakeCase)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (typeof (result) !== 'undefined' &&
                            result != null) {
                            vm.message.principalQueue = result.queue;
                            addSelectedAttachments(result.queue);
                            if (result.queue.length === 0) {
                                vm.message.attachments = [];
                            }
                        }
                    });
                });
        }

        function addSelectedAttachments(queue, isPredAnswerAttach = false) {
            vm.message.attachments = [];

            if (isPredAnswerAttach) {
                let predAttachment = {
                    MimeType: vm.message.predAttachment.mimeType,
                    FileName: vm.message.predAttachment.name,
                    FileSize: 0,
                    isPredAnswerAttach: isPredAnswerAttach
                };

                // Push attachment
                vm.message.attachments.push(predAttachment);
            }
            else {
                // Loop through uploads
                angular.forEach(queue, function (upload) {
                    let attachment = {};
                    if (typeof (upload.isPredAttach) === 'undefined') {
                        // Set up attachment files
                        attachment.MimeType = upload._file.type;
                        attachment.FileName = upload._file.name;
                        attachment.FileSize = upload._file.size;
                        attachment.MessageID = null;
                        if (typeof (upload.isPredAttach) !== 'undefined') {
                            attachment.isPredAnswerAttach = upload.isPredAttach;
                        }

                        if (typeof (upload._file.messageId) === 'number') {
                            attachment.MessageID = upload._file.messageId;
                        }
                        else if (typeof(vm.socialCase) === 'object' &&
                            vm.socialCase !== null &&
                            !vm.socialCase.outgoing &&
                            !vm.socialCase.isMyOutgoingCases) {
                            attachment.MessageID = vm.fakeCase.data.id;
                        }
                        attachment.Index = upload._file.index;

                        // --> Si el adjunto no fue sacado del mensaje original y subido por el usuario en el mensaje saliente..
                        if (upload.file) {
                            // Base64 encode attachment
                            var reader = new FileReader();
                            reader.readAsDataURL(upload._file);
                            reader.onload = function (event) {
                                // Build content
                                attachment.Data = event.target.result.substring(event.target.result.indexOf(',') + 1, event.target.result.length);
                                // Push attachment
                                vm.message.attachments.push(attachment);
                            };
                        }
                        else {
                            vm.message.attachments.push(attachment);
                        }
                    }
                    else {
                        attachment.MimeType = upload._file.type;
                        attachment.FileName = upload._file.fileName;
                        attachment.FileSize = 0;
                        attachment.isPredAnswerAttach = true;
                        // Push attachment
                        vm.message.attachments.push(attachment);
                    }
                });
            }
        }

        function send(sendAndClean, changeAuxReason, closeCase = false) {
            if (vm.inputMailTo.length > 1) {
                toastr.error($translate.instant('OUTGOINGMESSAGE_EMAIL_USER_MAX'));
                return;
            }

            let ccIsValid = vm.message.mailCC && vm.message.useCC ? ccValidation() : true;
            var ccoIsValid = vm.message.mailCCO && vm.message.useCCO ? ccoValidation() : true;

            if (!ccIsValid || !ccoIsValid) {
                return;
            }

            let document = mailEditor.getDoc();
            if (typeof (document) === 'object' &&
                document !== null) {
                let images = document.getElementsByTagName('img');
                for (var i = 0; i < images.length; i++) {
                    let image = images[i];
                    let src = image.getAttribute('src');
                    if (src !== null && src.startsWith('./uploads/')) {
                        image.setAttribute('uploaded', 'true');
                        vm.message.options.insertedImages = true;
                    }
                }
            }

            let body = mailEditor.getBody();
            if (typeof (body) === 'object' &&
                body !== null &&
                typeof (body.innerHTML) === 'string') {
                vm.message.principal = body.innerHTML;
            }

            if (settingsService.getAgent().allowedToDisableEmojis && messagesService.checkIfEmojis(vm.message.text)) {
                toastr.error($translate.instant('HAS_EMOJIS'));
                return false;
            }

            if (vm.message.options.insertedImages) {
                vm.message.principal = '<div>' + vm.message.principal + '</div>';
            }

            let profile = (typeof (vm.socialParsed) === 'object') ? vm.socialParsed.profile : undefined;
            let answerWithOptions = {
                options: {
                    subject: vm.message.subject,
                    cc: vm.message.useCC ? vm.message.mailCC : undefined,
                    bcc: vm.message.useCCO ? vm.message.mailCCO : undefined,
                    isForwaded: false,
                    to: vm.message.mailTo,
                    insertedImages: typeof (vm.message.options.insertedImages) === 'boolean' ? vm.message.options.insertedImages : false,
                    chkCloseCase: closeCase
                },
                data: {
                    profile: profile,
                    principal: vm.message.principal,
                    service: vm.message.service,
                    socialParsed: vm.socialParsed,
                    attachments: vm.message.attachments,
                    name: vm.fakeCase.case.profile.name,
                    phoneNumber: vm.fakeCase.case.profile.primaryPhoneNumber,
                    businessData: vm.fakeCase.case.profile.businessData,
                },
                markCaseAsPending: vm.chkMarkAsPending
            };

			if (typeof(vm.fakeCase.case.profile.parameters === 'object') &&
				vm.fakeCase.case.profile.parameters != null) {
				answerWithOptions.data.profileExtendedFields = vm.fakeCase.case.profile.parameters.ext;
			}

            var applyTag = false;
            var applyImportantTag = false;

            //Si no es desde Generar salientes.
            if(!vm.isFromNormalCase) {
                applyTag = (settingsService.settings.context.systemSettings['cases.TagOutgoing'] ||
                (settingsService.settings.context.systemSettings['cases.TagCasesOnClose'] && closeCase));

                applyImportantTag = (settingsService.settings.context.systemSettings['cases.ImportantTagOutgoing'] ||
                (settingsService.settings.context.systemSettings['cases.ImportantTag'] && closeCase));
            }

            var dataModal = {
                title: $translate.instant('OUTGOINGMESSAGE_MAIL'),
                description: $translate.instant('OUTGOINGMESSAGE_MAIL_MODAL_CONFIRM', { mailUser: vm.message.mailTo })
            };

            if (applyTag) {
                modalSocialService.showCaseDetails(undefined, applyTag, applyImportantTag)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            if (result && result.updated) {
                                answerWithOptions.observations = result.observations;
                                answerWithOptions.tags = result.tags;
                                answerWithOptions.extendedFields = result.extendedFields;
                                if (applyImportantTag) {
                                    answerWithOptions.importantTag = result.importantTag;
                                }

                                commonActionsService.sendMail(answerWithOptions)
                                    .then(function (response) {
                                        utilsService.hideLoading();
                                        toastr.success($translate.instant('SENDING_MESSAGE_SUCCESS'));
                                        if (!sendAndClean) {
                                            vm.message = {
                                                socialUser: undefined,
                                                userNotFound: false,
                                                mailTo: undefined,
                                                subject: undefined,
                                                serviceId: undefined,
                                                service: undefined,
                                                text: undefined,
                                                useCC: false,
                                                useCCO: false,
                                                principalQueue: [],
                                                attachments: [],
                                                mailCCO: [],
                                                mailCC: [],
                                                options: {}
                                            };
                                            vm.showResultsSearch = false;
                                            vm.tinymceOptions = undefined;
                                            mailEditor = null;
                                            vm.chkMarkAsPending = false;
                                        }
                                        else {
                                            if (changeAuxReason) {
                                                close(false);
                                            }
                                            else {
                                                closeNoChangeState();
                                            }
                                        }

                                        if (vm.isFromNormalCase) {
                                            modalSocialService.closeModals();
                                        }
                                    })
                                    .catch(function (error) {
                                        utilsService.hideLoading();
                                        toastr.error($translate.instant('SENDING_MESSAGE_FAILED'));
                                        traceService.warning(`El agente intentó enviar un mensaje saliente pero hubo un error: ${JSON.stringify(error)}`);
                                    });

                            }
                        });
                    });
            }
            else {
                modalSocialService.showConfirmGeneral(dataModal)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (result && result.action === 'confirm') {
                            utilsService.showLoadingMessage($translate.instant('SENDING_MESSAGE_LOADER'));
                            commonActionsService.sendMail(answerWithOptions)
                                .then(function (response) {
                                    utilsService.hideLoading();
                                    toastr.success($translate.instant('SENDING_MESSAGE_SUCCESS'));
                                    if (!sendAndClean) {
                                        vm.message = {
                                            socialUser: undefined,
                                            userNotFound: false,
                                            mailTo: undefined,
                                            subject: undefined,
                                            serviceId: undefined,
                                            service: undefined,
                                            text: undefined,
                                            useCC: false,
                                            useCCO: false,
                                            principalQueue: [],
                                            attachments: [],
                                            mailCCO: [],
                                            mailCC: [],
                                            options: {},
                                        };
                                        vm.showResultsSearch = false;
                                        vm.tinymceOptions = undefined;
                                        mailEditor = null;
                                        vm.chkMarkAsPending = false;
                                    }
                                    else {
                                        if (changeAuxReason) {
                                            close(false);
                                        }
                                        else {
                                            closeNoChangeState();
                                        }
                                    }

                                    if (vm.isFromNormalCase) {
                                        modalSocialService.closeModals();
                                    }
                                })
                                .catch(function (error) {
                                    utilsService.hideLoading();
                                    toastr.error($translate.instant('SENDING_MESSAGE_FAILED'));
                                    traceService.warning(`El agente intentó enviar un mensaje saliente pero hubo un error: ${JSON.stringify(error)}`);
                                });
                        }
                    });
                });
            }
        }

        function close(confirm) {
            if (typeof (confirm) !== 'boolean') {
                confirm = true;
            }

            if (confirm) {
                var dataModal = {
                    title: $translate.instant('MODAL_CONFIRM_LEAVE_OUTBOUND_ASSISTANT_TITLE'),
                    description: $translate.instant('MODAL_CONFIRM_LEAVE_OUTBOUND_ASSISTANT_DESCRIPTION')
                };

                modalSocialService.showConfirmLeaveOutboundAssistant(dataModal)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            if (result && result.action === 'confirm') {
                                statesService.switchStates(outgoingService.statusBeforeOutgoing);
                                integrationsService.executeActions('outgoingmessagefinished');
                            }
                        });
                    });
            }
            else {
                statesService.switchStates(outgoingService.statusBeforeOutgoing);
                integrationsService.executeActions('outgoingmessagefinished');
            }
        }

    }
})();
