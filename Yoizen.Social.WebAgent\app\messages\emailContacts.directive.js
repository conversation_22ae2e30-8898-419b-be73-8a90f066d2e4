(function () {
    'use strict';

    angular
        .module('socialApp')
        .directive('emailContacts', [
            function () {
                return {
                    restrict: 'E',
                    templateUrl: './app/messages/emailContacts.template.html',
                    controller: 'EmailContactsController',
                    bindToController: true,
                    scope: {
                        inModal: '=',
                        darkMode: "="
                    },
                    controllerAs: 'emailContactsCtrl'
                };
            }]);
})();