(function () {

    'use strict';

    angular.module('socialApp')
        .controller('OutgoingMessageWhatsappController', OutgoingMessageWhatsappController);

    OutgoingMessageWhatsappController.$inject = ['$scope',
        'messagesService',
        'agentService',
        'settingsService',
        'toastr',
        'utilsService',
        '$sce',
        '$translate',
        'statesService',
        'integrationsService',
        'modalSocialService',
        'outgoingService',
        'commonActionsService',
        'traceService',
        '$timeout'];

    function OutgoingMessageWhatsappController($scope,
        messagesService,
        agentService,
        settingsService,
        toastr,
        utilsService,
        $sce,
        $translate,
        statesService,
        integrationsService,
        modalSocialService,
        outgoingService,
        commonActionsService,
        traceService,
        $timeout) {
        var vm = this;
        var $editorPrimary;

        vm.services = [];
        vm.templates = [];
        vm.countries = [];

        vm.message = {
            country: undefined,
            phoneNumber: undefined,
            socialUser: undefined,
            userNotFound: false,
            serviceId: undefined,
            service: undefined,
            attachments: undefined,
            template: undefined,
            templateIndex: undefined,
            templateParameters: undefined,
            templateButtons: undefined,
            templateButtonsParameters: undefined,
            templateHeader: undefined,
            templateFooter: undefined,
            templateWithParameters: '',
            headerWithParameters: '',
            phoneNumberParsed: undefined,
            answerType: 'HSM',
            inputHeaderText: undefined
        };
        vm.templateChanged = templateChanged;
        vm.serviceChanged = serviceChanged;
        vm.searchSocialUserPhoneNumber = searchSocialUserPhoneNumber;
        vm.templateParameterChanged = templateParameterChanged;
        vm.templateHeaderChanged = templateHeaderChanged;
        vm.addButtonsParameters = addButtonsParameters;
        vm.attachFiles = attachFiles;
        vm.send = send;
        vm.close = close;
        vm.buttonDefaultText = $translate.instant('SELECT_TEMPLATE');
        vm.uncheckAllText = $translate.instant('UNCHECKALL');
        vm.searchPlaceholderText = $translate.instant('SEARCH');
        vm.templatesSelectionContainer = [];
        vm.templateHeaderMediaSelectionContainer = [];
        vm.closeNoChangeState = closeNoChangeState;
        vm.agentService = agentService;
        vm.allowAgentsToMarkCasesAsPending = false;
        vm.availableToAnswer = false;
        vm.selectedCurrentCase = null;
        vm.sendTemplates = false;
        vm.normalAnswerAndHsm = false;
        vm.validateUserHasCases = validateUserHasCases;
        vm.getCaseByProfile = getCaseByProfile;
        vm.typeOfAnswerChanged = typeOfAnswerChanged;
        vm.emojiClicked = emojiClicked;
        vm.shortUrls = shortUrls;
        vm.answerMessage = answerMessage;
        vm.showPredefinedAnswers = showPredefinedAnswers;
        vm.socialCase = {};
        vm.serviceHasTemplates = true;
        vm.principalText = '';
        vm.principalQueue = [];
        vm.validatePhoneNumber = true;
        vm.invalidPhoneNumber = false;
        vm.invalidPhoneNumberPossibleCountries = null;
        vm.invalidPhoneNumberWrongType = false;
        vm.canValidatePhoneNumber = typeof (libphonenumber) !== 'undefined' &&
            libphonenumber !== null &&
            typeof (libphonenumber.parsePhoneNumberFromString) === 'function';
        vm.answerTypes = [
            {
                id: 0,
                name: "Normal"
            },
            {
                id: 1,
                name: "HSM"
            }
        ];
        vm.attachments = [];
        vm.mediaInputSelect = undefined;
        vm.canCloseCase = settingsService.getAgent().allowedToCloseCases;

        if (typeof (vm.chkMarkAsPending) !== 'boolean') {
            vm.chkMarkAsPending = false;
        }

        if (typeof (vm.sendHSMIfCaseOpenAnyways) !== 'boolean') {
            vm.sendHSMIfCaseOpenAnyways = true;
        }

        if (typeof (vm.isPublicUrl) !== 'boolean') {
            vm.isPublicUrl = true;
        }

        activate();

        function activate() {
            settingsService.reloadCountries();

            let whatsappServices = [];
            if (Array.isArray(settingsService.settings.agent.servicesForOutgoingMessages)) {
                whatsappServices = settingsService.settings.agent.servicesForOutgoingMessages.filter(function (s) {
                    return s.type === ServiceTypes.WhatsApp &&
                        s.enabled;
                });
            }

            for (let i = 0; i < settingsService.settings.context.services.length; i++) {
                let service = settingsService.settings.context.services[i];
                if (service.enabled &&
                    service.socialServiceType === SocialServiceTypes.WhatsApp &&
                    service.settings.integrationType !== 1) {
                    const id = service.id;
                    if (whatsappServices === null ||
                        whatsappServices.length === 0 ||
                        whatsappServices.findIndex(ws => ws.id === id) !== -1) {
                        if (typeof (service.displayText) !== 'string') {
                            if (typeof (service.basicConfiguration) === 'object') {
                                service.displayText = `${service.name} (${service.basicConfiguration.fullPhoneNumber})`;
                            }
                            else {
                                service.displayText = service.name;
                            }
                        }
                        vm.services.push(service);
                    }
                }
            }

            $editorPrimary = $('.editor-whatsapp');

            $timeout(function () {
                $editorPrimary.focus();
            }, 500);


            if (typeof (settingsService.settings.agent.allowedToMarkAsPending) === 'boolean') {
                vm.allowAgentsToMarkCasesAsPending = settingsService.settings.agent.allowedToMarkAsPending;
            }
        }

        function typeOfAnswerChanged() {
            switch (vm.message.answerType) {
                case 0:
                    vm.sendTemplates = false;
                    vm.availableToAnswer = true;
                    break;
                case 1:
                    vm.sendTemplates = true;
                    vm.availableToAnswer = false;
                    break;
            }
        }

        function getCaseByProfile() {
            if (vm.selectedCurrentCase != null &&
                typeof (vm.message.service) != 'undefined' &&
                vm.selectedCurrentCase.status === 2) {
                if (vm.selectedCurrentCase.services.includes(vm.message.service.id)) {
                    if (vm.selectedCurrentCase.id != null &&
                        typeof (vm.selectedCurrentCase.id) !== 'undefined') {
                        messagesService.searchCaseMessages(vm.selectedCurrentCase.id)
                            .then(function (response) {
                                vm.caseMessages = utilsService.toCamel(response.data.Result.Case.Messages.reverse());
                                if (vm.caseMessages.length > 0) {
                                    let message;
                                    let sentDate;

                                    for (let i = vm.caseMessages.length - 1; i >= 0; i--) {
                                        message = vm.caseMessages[i];
                                        if (message.socialServiceType === SocialServiceTypes.WhatsApp && !message.isReply) {
                                            sentDate = message.date;
                                            break;
                                        }
                                    }

                                    let settings = vm.message.service.settings;

                                    if (typeof (sentDate) !== 'undefined' &&
                                        typeof (settings) !== 'undefined' &&
                                        settings !== null) {
                                        let maxMinutesToAnswerMessages = 1440;
                                        if (typeof (settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages']) !== 'undefined')
                                            maxMinutesToAnswerMessages = parseInt(settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages'], 10);

                                        let sentDateMoment = moment(sentDate);
                                        sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                                        let nowMoment = moment();
                                        if (sentDateMoment < nowMoment) {
                                            vm.normalAnswerAndHsm = false;
                                            vm.availableToAnswer = false;
                                            vm.sendTemplates = true;
                                        }
                                        else {
                                            if (!vm.serviceHasTemplates) {
                                                vm.normalAnswerAndHsm = false;
                                                vm.availableToAnswer = true;
                                                vm.sendTemplates = false;
                                            }
                                            else {
                                                vm.normalAnswerAndHsm = true;
                                                vm.availableToAnswer = false;
                                                vm.sendTemplates = false;
                                            }
                                            vm.socialCase.data = vm.selectedCurrentCase;
                                            vm.answer = {
                                                displayName: "",
                                                profile: vm.message.socialUser,
                                                socialParsed: vm.socialCase.data.socialParsed,
                                                service: vm.message.service,
                                                case: vm.socialCase.data,
                                                options: {
                                                    isOutgoing: true,
                                                    isMyOutgoingCases: vm.socialCase.isMyOutgoingCases
                                                },
                                                principalText: "",
                                                actions: {
                                                    messageOptionsAreValid: null
                                                },
                                                isAnswering: null
                                            };
                                        }
                                    }
                                }
                            })
                            .catch(function () {
                                vm.availableToAnswer = false;
                            });
                    }
                }
            }
            else {
                vm.sendTemplates = true;
            }
        }

        function searchSocialUserPhoneNumber() {
            vm.message.socialUser = null;
            vm.availableToAnswer = false;
            vm.message.userNotFound = false;
            vm.invalidPhoneNumber = false;
            vm.invalidPhoneNumberPossibleCountries = [];
            vm.invalidPhoneNumberWrongType = false;

            if (typeof (vm.message.phoneNumber) !== 'undefined' &&
                vm.message.phoneNumber !== null &&
                /^[0-9]{10,18}$/.test(vm.message.phoneNumber)) {
                vm.message.phoneNumberParsed = null;

                if (typeof (libphonenumber) !== 'undefined' &&
                    libphonenumber !== null &&
                    typeof (libphonenumber.parsePhoneNumberFromString) === 'function') {
                    let phoneNumberParsed = libphonenumber.parsePhoneNumberFromString('+' + vm.message.phoneNumber);
                    if (typeof (phoneNumberParsed) !== 'undefined' &&
                        phoneNumberParsed !== null) {
                        vm.message.phoneNumberParsed = phoneNumberParsed;

                        if (!phoneNumberParsed.isValid()) {
                            vm.invalidPhoneNumber = true;
                            vm.invalidPhoneNumberPossibleCountries = phoneNumberParsed.getPossibleCountries();
                            if (vm.validatePhoneNumber) {
                                return;
                            }
                        }

                        let type = phoneNumberParsed.getType();
                        if (type !== 'MOBILE' && type !== 'FIXED_LINE_OR_MOBILE') {
                            vm.invalidPhoneNumber = true;
                            vm.invalidPhoneNumberWrongType = true;
                            if (vm.validatePhoneNumber) {
                                return;
                            }
                        }
                    }
                    else {
                        vm.invalidPhoneNumber = true;
                        if (vm.validatePhoneNumber) {
                            return;
                        }
                    }
                }

                agentService.searchWhatsappUser(vm.message.phoneNumber)
                    .then(function (result) {
                        if (result.data.Success) {
                            var socialUser = utilsService.toCamel(result.data.Result);
                            vm.socialParsed = result.data.Result;
                            if (typeof (socialUser.profile) === 'object' &&
                                socialUser.profile !== null) {
                                vm.message.socialUser = socialUser.profile;
                                vm.message.userNotFound = false;
                                vm.fakeCase = {
                                    postedBy: socialUser,
                                    'case': {
                                        profile: socialUser.profile
                                    }
                                };
                                validateUserHasCases();
                            }
                            else {
                                vm.message.userNotFound = true;
                                vm.message.socialUser = vm.message.phoneNumber;
                                vm.fakeCase = {
                                    postedBy: 1,
                                    'case': {
                                        profile: {
                                            id: -1,
                                            name: vm.message.phoneNumber,
                                            primaryEmail: '',
                                            primaryPhoneNumber: vm.message.phoneNumber,
                                            businessData: '',
                                            extendedProfilesFields: null
                                        }
                                    }
                                };
                                vm.sendTemplates = true;
                                vm.availableToAnswer = false;
                                vm.normalAnswerAndHsm = false;
                            }
                        }
                    })
                    .catch(function (err) {
                        console.log(`Ocurrió un error buscando el usuario ${vm.message.phoneNumber}: ${err}`);
                    });
            }
            else {
                vm.message.socialUser = null;
            }
        }

        function validateUserHasCases() {
            vm.selectedCurrentCase = null;
            if (vm.message.socialUser !== null && typeof (vm.message.socialUser) === 'object') {
                utilsService.showLoading();
                messagesService.searchCases(vm.message.socialUser.id, settingsService.settings.agent.id)
                    .then(function (response) {
                        vm.selectedSocialUserCases = utilsService.toCamel(response.data.Result.Cases);
                        vm.noCasesFound = (vm.selectedSocialUserCases.length === 0);
                        if (vm.selectedSocialUserCases.length > 0) {
                            vm.selectedCurrentCase = vm.selectedSocialUserCases.find(item => {
                                if (item.service !== null && vm.message.service !== null &&
                                    typeof (item.service) !== 'undefined' && typeof (vm.message.service) !== 'undefined' &&
                                    item.service.id === vm.message.service.id)
                                    return item;
                            });
                        }
                        getCaseByProfile();
                        utilsService.hideLoading();
                    })
                    .catch(function () {
                        utilsService.hideLoading();
                        vm.availableToAnswer = false;
                    });
            }
        }

        function serviceChanged() {
            vm.message.service = vm.services.find(function (service) {
                return service.id === vm.message.serviceId;
            });

            vm.sendTemplates = false;
            vm.availableToAnswer = false;
            vm.normalAnswerAndHsm = false;
            vm.serviceHasTemplates = false;

            if (vm.message.service.settings.allowToSendHSM &&
                vm.message.service.settings.allowAgentsToSendHSM &&
                vm.message.service.settings.hSMTemplates !== null &&
                vm.message.service.settings.hSMTemplates.length > 0) {
                vm.templates = vm.message.service.settings.hSMTemplates.map(function (template, index) {
                    return {
                        id: index,
                        label: template.description + ' (' + template.namespace + ' - ' + template.elementName + ')'
                    };
                });
                vm.serviceHasTemplates = true;
                vm.sendTemplates = true;
            }

            vm.message.country = null;
            if (typeof (vm.message.service.basicConfiguration) === 'object') {
                for (var i = 0; i < settingsService.settings.context.countries.length; i++) {
                    var country = settingsService.settings.context.countries[i];
                    if (vm.message.service.basicConfiguration.fullPhoneNumber.startsWith(country.internationalCode.toString())) {
                        vm.message.country = country;
                        break;
                    }
                }
            }

            validateUserHasCases();
            utilsService.scrollToBottom();

            $translate([
                'SELECT_TEMPLATE',
                'UNCHECKALL',
                'SEARCH',
            ]).then(function (translations) {
                vm.buttonDefaultText = translations.SELECT_TEMPLATE;
                vm.uncheckAllText = translations.UNCHECKALL;
                vm.searchPlaceholderText = translations.SEARCH;
            });

        }

        function shortUrls() {
            let input = vm.answer.principalText;
            if (typeof (input) !== 'string' || input.length === 0) {
                return;
            }

            try {
                let promise = agentService.shortUrls(input);
                if (promise === null) {
                    return;
                }

                promise
                    .then(function (response) {
                        response.forEach(function (result) {
                            input = input.replace(result.data.Result.OriginalUrl, result.data.Result.ShortUrl);
                        });
                        vm.answer.principalText = input;
                    })
                    .catch(function () {
                        $log.error(urlsCouldNotBeCut);
                    });
            }
            catch (e) {
                $log.error(urlsCouldNotBeCut);
            }
        }

        function attachFiles() {
            //create fake case beacuse is outgoing
            vm.message.fakeCase = {
                id: -1,
                outgoing: true,
                createCase: true,
                socialUser: null,
                service: vm.message.service,
                data: {
                    isDirectMessage: false,
                    id: -1,
                    socialUser: vm.message.socialUser,
                    socialParsed: vm.message.socialUser,
                    service: vm.message.service
                }
            };

            let files = vm.principalQueue;
            modalSocialService.showFileUploader(files, vm.message.fakeCase) // Saliente no tiene socialCase
                .then(function (modal) {
                    modal.element.modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    modal.element.on('hide.bs.modal', function (e) {
                        if (modalSocialService.blockModalHiding) {
                            e.preventDefault();
                        }
                    });

                    modal.close.then(function (result) {
                        if (typeof (result) !== 'undefined' &&
                            result != null) {
                            vm.principalQueue = result.queue;
                            addSelectedAttachments(result.queue);
                            if (result.queue.length === 0) {
                                vm.attachments = [];
                            }
                        }
                    });
                });
        }

        function addSelectedAttachments(queue, isPredAnswerAttach = false) {
            vm.attachments = [];
            let predAttachment = {};
            // Set Predefined Answer Attach.
            if (isPredAnswerAttach) {
                predAttachment.MimeType = vm.answer.predAttachment.mimeType;
                predAttachment.FileName = vm.answer.predAttachment.name;
                predAttachment.OriginalFileName = vm.answer.predAttachment.originalName;
                predAttachment.FileSize = 0;
                predAttachment.isPredAnswerAttach = isPredAnswerAttach;
                // Push attachment
                vm.attachments.push(predAttachment);
            }
            else {
                // Loop through uploads
                angular.forEach(queue, function (upload) {
                    let attachment = {};
                    if (typeof (upload.isPredAttach) === 'undefined') {

                        // Set up attachment files
                        attachment.MimeType = upload._file.type;
                        attachment.FileName = upload._file.name;
                        attachment.FileSize = upload._file.size;

                        // Base64 encode attachment
                        let reader = new FileReader();
                        reader.readAsDataURL(upload._file);
                        reader.onload = function (event) {
                            if (typeof (attachment.FileName) !== 'undefined') {
                                if (vm.attachments.find(item => item.FileName === attachment.FileName) === undefined) {
                                    // Build content
                                    attachment.Data = event.target.result.substring(event.target.result.indexOf(',') + 1, event.target.result.length);
                                    // Push attachment
                                    vm.attachments.push(attachment);
                                }
                            }
                        };
                    }
                    else {
                        attachment.MimeType = upload._file.type;
                        attachment.FileName = upload._file.fileName;
                        attachment.FileSize = 0;
                        attachment.isPredAnswerAttach = true;
                        // Push attachment
                        vm.attachments.push(attachment);
                    }
                });
            }
        }

        function emojiClicked(emoji, $event) {
            let text = emoji.split('-').map(twemoji.convert.fromCodePoint).join('');
            if (typeof (vm.answer.principalText) === 'undefined' || vm.answer.principalText === null) {
                vm.answer.principalText = text;
            }
            else {
                vm.answer.principalText += text;
            }
            $event.stopPropagation();
        }

        function showPredefinedAnswers() {
            let selectionStart, selectionEnd;
            $editorPrimary = $('.editor-whatsapp');

            selectionStart = $editorPrimary[0].selectionStart;
            selectionEnd = $editorPrimary[0].selectionEnd;
            vm.socialCase.data.service = vm.message.service;
            vm.socialCase.data.case = vm.socialCase.data;
            vm.socialCase.data.postedBy = vm.message.socialUser;
            let data = vm.socialCase.data;

            modalSocialService.showPredefinedAnswers(data, settingsService.settings.agent)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (typeof (result) !== 'object' || typeof (result.textAnswer) !== 'string') {
                            return;
                        }

                        if (typeof (vm.answer.principalText) === 'undefined' || vm.answer.principalText === null || vm.answer.principalText.length === 0) {
                            vm.answer.principalText = result.textAnswer;
                        }
                        else {
                            if (selectionStart === selectionEnd && selectionEnd === vm.answer.principalText.length) {
                                if (!vm.answer.principalText.endsWith(' ') && !result.textAnswer.startsWith(' '))
                                    vm.answer.principalText = vm.answer.principalText + ' ' + result.textAnswer;
                                else
                                    vm.answer.principalText = vm.answer.principalText + result.textAnswer;
                            }
                            else {
                                vm.answer.principalText = vm.answer.principalText.substring(0, selectionStart) + result.textAnswer + vm.answer.principalText.substring(selectionEnd);
                            }
                        }

                        if (typeof (result.attach) === 'object' &&
                            result.attach !== null &&
                            typeof (result.attach.name) === 'string' &&
                            typeof (result.attach.mimeType) === 'string' &&
                            vm.message.service.settings.allowToSendMultimedia) {

                            vm.attachments = [];
                            vm.principalQueue = [];

                            vm.answer.predAttachment = result.attach;
                            addSelectedAttachments(vm.answer.predAttachment, true);
                            vm.principalQueue.push(vm.answer.predAttachment);
                        }
                    });
                });
        }

        let isOutgoing = true;
        let isMyOutgoingCases = false;

        // --> Chequeo el mensaje y lo respondo
        function answerMessage(closeCase) {
            let principal = removeBlackSpaces(vm.answer.principalText);
            if (principal.length === 0) {
                toastr.error(textRequiredInPrincipalAnswer);
                return false;
            }
            if (principal.length > 4096) {
                toastr.error($translate.instant('WHATSAPP_MAX_LENGTH', { length: principal.length }));
                return false;
            }

            let settings;
            let sentDate;
            if (isOutgoing) {
                settings = vm.message.service.settings;
                for (let i = vm.caseMessages.length - 1; i >= 0; i--) {
                    let message = vm.caseMessages[i];
                    if (message.socialServiceType === SocialServiceTypes.WhatsApp && !message.isReply) {
                        sentDate = message.date;
                        break;
                    }
                }
            }
            else {
                if (isMyOutgoingCases) {
                    settings = vm.message.service.settings;
                    sentDate = vm.socialCase.data.date;
                }
                else {
                    settings = vm.message.service.settings;
                    sentDate = messagesService.selectedSocialCase.data.date;
                }
            }

            if (typeof (sentDate) !== 'undefined' &&
                typeof (settings) !== 'undefined' &&
                settings !== null) {
                if (settings.integrationType !== 1) {
                    let maxMinutesToAnswerMessages = 1440;
                    if (typeof (settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages']) !== 'undefined') {
                        maxMinutesToAnswerMessages = settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages'];
                        if (typeof (maxMinutesToAnswerMessages) === 'string') {
                            maxMinutesToAnswerMessages = parseInt(maxMinutesToAnswerMessages, 10);
                        }
                    }

                    let sentDateMoment = moment(sentDate);
                    sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                    let nowMoment = moment();

                    if (sentDateMoment < nowMoment) {
                        return false;
                    }
                }
            }
            vm.answer.options.chkCloseCase = closeCase;
            answerAction();
        }

        function removeBlackSpaces(text) {
            if (typeof (text) !== 'string') {
                return '';
            }

            text = text.trim();
            return text;
        }

        // --> Lógica de respuesta del mensaje de whatsapp
        function answerAction() {
            let principal = removeBlackSpaces(vm.answer.principalText);

            vm.answer.attachments = [];
            if(vm.attachments.length > 0){
                vm.answer.attachments = vm.attachments;
            }

            if (principal.length === 0) {
                toastr.error(textRequiredInPrincipalAnswer);
                return;
            }
            if (principal.length > 4096) {
                toastr.error($translate.instant('WHATSAPP_MAX_LENGTH', { length: principal.length }));
                return;
            }

            vm.answer.actions.messageOptionsAreValid();
            if (!vm.answer.options.validationMessages ||
                vm.answer.options.validationMessages.length === 0) {
                vm.answer.principal = principal;

                let answerWithOptions = {
                    data: vm.answer,
                    options: vm.answer.options,
                    markCaseAsPending: vm.chkMarkAsPending,
                    isOutgoing: true
                };

                answerWithOptions.data.socialParsed = vm.socialParsed;

                if (isOutgoing) {
                    answerWithOptions.options.tagOutgoing = settingsService.settings.context.systemSettings['cases.TagOutgoing'];
                    answerWithOptions.options.importantTagOutgoing = settingsService.settings.context.systemSettings['cases.ImportantTagOutgoing'];
                    //indico que el caso no puede ser re-abierto para que se cree uno nuevo mediante el envio.
                    answerWithOptions.data.case.canBeReopened = false;
                    //valido si el usuario no tiene casos abiertos antes de respoder.
                    utilsService.showLoading();
                    messagesService.searchCases(vm.message.socialUser.id, settingsService.settings.agent.id)
                        .then(function (response) {
                            let cases = utilsService.toCamel(response.data.Result.Cases);
                            let sendMessage = true;
                            if (cases.length > 0) {
                                for (let i = 0; i < cases.length; i++) {
                                    if (cases[i].status === 1 && cases[i].services.includes(vm.message.service.id)) {
                                        toastr.error($translate.instant('USER_HAS_OPEN_CASE'));
                                        sendMessage = false;
                                        utilsService.hideLoading();
                                        return;
                                    }
                                }
                            }
                            if (sendMessage) {
                                commonActionsService.sendSocialNetworkMessage(answerWithOptions, SocialServiceTypes.WhatsApp);
                                vm.attachments = [];
                            }
                            utilsService.hideLoading();
                        })
                        .catch(function () {
                            toastr.error('Error al responder.');
                            vm.availableToAnswer = false;
                            utilsService.hideLoading();
                        });
                }
                else {
                    if (isMyOutgoingCases) {
                        answerWithOptions.case = vm.socialCase.data;
                        answerWithOptions.socialServiceType = vm.socialCase.data.socialServiceTypesEnum;
                        answerWithOptions.service = vm.message.service;
                        answerWithOptions.socialUser = vm.message.socialUser;
                        console.log(`Agregando mensaje al caso ${vm.socialCase.data.id}`);
                        commonActionsService.addMessage(answerWithOptions);
                    }
                    else {
                        commonActionsService.answerMessage(answerWithOptions);
                    }
                }
            }
            else {
                toastr.error(vm.answer.options.validationMessages[0]);
            }
        }

        $scope.onItemSelect = {
            onItemSelect: function (item) {
                templateChanged();
            }
        };

        const HSMTemplateHeaderTypes = {
            none: 0,
            text: 1,
            media: 2
        };
        const HSMTemplateHeaderMediaTypes = {
            none: 0,
            document: 1,
            image: 2,
            video: 3
        };
        const HSMTemplateButtonsTypes = {
            none: 0,
            quickReply: 1,
            callToAction: 2,
            mixed: 3,
            authCode: 4
        };
        const HSMTemplateAuthCodeButtonTypes = {
            authCode: 1
        };
        const HSMTemplateCallToActionButtonTypes = {
            url: 1,
            call: 2,
            offer: 3
        };
        const HSMTemplateCallToActionUrlButtonTypes = {
            fixed: 1,
            dynamic: 2
        };

        if (typeof (vm.mediaInputSelect) === 'undefined' || vm.mediaInputSelect === null) {
            vm.mediaInputSelect = 'url';
        }

        function templateChanged() {
            vm.message.template = undefined;
            let template = vm.message.service.settings.hSMTemplates[vm.templatesSelectionContainer.id];
            vm.message.template = template;
            vm.message.templateWithParameters = $sce.trustAsHtml(template.template.replaceAll('\n', '<br />'));
            if (typeof (template.headerText) === 'string') {
                vm.message.headerWithParameters = $sce.trustAsHtml(template.headerText.replaceAll('\n', '<br />'));
            }
            else {
                vm.message.headerWithParameters = '';
            }

            //Header build
            if (typeof (template.headerType) === 'number') {
                let header = {};
                vm.message.templateHeader = {};
                switch (template.headerType) {
                    case HSMTemplateHeaderTypes.text:
                        header = {
                            type: 'text',
                            text: null
                        };
                        if (template.headerTextParameter !== null) {
                            header.text = {
                                parameter: {
                                    name: template.headerTextParameter.name,
                                    value: ''
                                }
                            };
                        }
                        break;

                    case HSMTemplateHeaderTypes.media:
                        header = {
                            type: 'media',
                            media: {
                                type: 'none'
                            }
                        };

                        switch (template.headerMediaType) {
                            case HSMTemplateHeaderMediaTypes.document:
                                header.media.type = 'document';
                                break;
                            case HSMTemplateHeaderMediaTypes.image:
                                header.media.type = 'image';
                                break;
                            case HSMTemplateHeaderMediaTypes.video:
                                header.media.type = 'video';
                                break;
                            default:
                                break;
                        }
                        if (HSMTemplateHeaderTypes.media) {
                            header.media[header.media.type] = {
                                filename: null,
                                url: template.headerMediaUrl,
                                isPublic: null,
                            };
                        }
                        break;
                }
                vm.message.templateHeader = header;
            }
            else {
                vm.message.templateHeader = null;
            }

            //Parameters Build
            if (template.parameters !== null && template.parameters.length > 0) {
                vm.message.templateParameters = [];
                vm.templateParameters = [];
                for (let i = 0; i < template.parameters.length; i++) {
                    vm.message.templateParameters.push(null);
                    let parameterName = template.parameters[i].substr(0, template.parameters[i].indexOf('='));
                    let parameterDescription = template.parameters[i].substr(template.parameters[i].indexOf('=') + 1);
                    vm.templateParameters.push({
                        name: '{{' + parameterName + '}}',
                        description: parameterDescription
                    });
                }
            }
            else {
                vm.message.templateParameters = null;
                vm.templateParameters = null;
            }

            //Buttons Build
            if (typeof (template.buttonsType) === 'number') {
                let buttons = [];
                vm.message.templateButtons = null;
                vm.templateButtons = null;
                vm.message.templateButtonsParameters = null;
                if (template.buttonsType !== HSMTemplateButtonsTypes.none &&
                    template.buttons.length > 0) {
                    vm.message.templateButtonsParameters = [];
                    for (let j = 0; j < template.buttons.length; j++) {
                        let button;
                        let templateButton = template.buttons[j];
                        switch (template.buttonsType) {
                            case HSMTemplateButtonsTypes.quickReply:
                                button = buildQuickReply(templateButton);
                            break;
    
                            case HSMTemplateButtonsTypes.callToAction:
                                button = buildCallToAction(templateButton);
                            break;
                            case HSMTemplateButtonsTypes.authCode:
                                button = buildAuthCode(templateButton);
                            break;
                            case HSMTemplateButtonsTypes.mixed:
                                button = templateButton.quickReplyParameter != null ? buildQuickReply(templateButton) : buildCallToAction(templateButton);
                            break;
                        }
                        button.index = j;
                        buttons.push(button);
                        vm.message.templateButtons = buttons;
                    }
                }
            }
        }

        function buildCallToAction(templateButton){
            const button = {};
            switch (templateButton.callToActionButtonType) {
                case HSMTemplateCallToActionButtonTypes.url:
                    button.type = 'url';
                    if (templateButton.urlButtonType === HSMTemplateCallToActionUrlButtonTypes.fixed) {
                        button.sub_type = 'fixed';
                    }
                    else {
                        button.sub_type = 'dynamic';
                        button.parameter = {};
                        button.parameter[templateButton.urlParameter.name] = null;  
                    }
                break;
                case HSMTemplateCallToActionButtonTypes.call:
                    button.type = 'call';
                break;
                case HSMTemplateCallToActionButtonTypes.offer:
                    button.type = 'offer';
                    button.parameter = {};
                    button.parameter[templateButton.offerCodeParameter.name] = null;
                break;
            }
            return button;
        }
        function buildAuthCode(templateButton){
            const button = {};
            button.type = 'url';
            button.parameter = {};
            button.parameter[templateButton.authCodeParameter.name] = null;
            return button;
        }
        
        function buildQuickReply(templateButton) {
            const button = {};
            button.type = 'quick_reply';
            button.payload = null;
            button.parameter = {};
            button.parameter[templateButton.quickReplyParameter.name] = null;
            return button;
        }

        //Reload Body with new parameters
        function templateParameterChanged() {
            let text = vm.message.template.template;
            for (let i = 0; i < vm.message.template.parameters.length; i++) {
                let parameter = vm.message.template.parameters[i];
                let parameterName = parameter.substr(0, parameter.indexOf('='));
                let value = vm.message.templateParameters[i];
                if (value !== null && value.length > 0) {
                    text = text.replace('{{' + parameterName + '}}', value);
                }
            }

            vm.message.templateWithParameters = $sce.trustAsHtml(text.replaceAll('\n', '<br />'));
        }

        //Reload Header with new parameters
        function templateHeaderChanged() {
            if (vm.message.template.headerType === HSMTemplateHeaderTypes.text) {
                let text = vm.message.template.headerText;
                let value = vm.message.inputHeaderText;
                let parameterName = vm.message.template.headerTextParameter.name;
                if (value !== null && value.length > 0) {
                    text = text.replace('{{' + parameterName + '}}', value);
                }
                vm.message.headerWithParameters = $sce.trustAsHtml(text.replaceAll('\n', '<br />'));
                vm.message.templateHeader.text.parameter.value = value;
            }
        }

        function addButtonsParameters(buttonIndex) {
            const templateButton = vm.message.templateButtons[buttonIndex];
            let value = vm.message.templateButtonsParameters[buttonIndex];
            const tempButton = vm.message.template.buttons[templateButton.index];

            switch (templateButton.type) {
                case 'quick_reply':
                    templateButton.parameter[tempButton.quickReplyParameter.name] = value;
                    break;
                case 'url':
                     if (templateButton.sub_type === 'dynamic'){
                    templateButton.parameter[tempButton.urlParameter.name] = value;
                    }
                    else{
                         templateButton.parameter[tempButton.authCodeParameter.name] = value;
                    }
                    break;
                case 'offer':
                    templateButton.parameter[tempButton.offerCodeParameter.name] = value;
                    break;
            }
        }

        function isValidTemplateOutgoing() {
            if (typeof (vm.message.phoneNumber) !== 'string' ||
                !/^[0-9]{10,18}$/.test(vm.message.phoneNumber)) {
                toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE'));
                return false;
            }

            vm.message.phoneNumberParsed = null;

            if (vm.validatePhoneNumber) {
                let validatePhoneAgaintsCountry = true;
                if (typeof (libphonenumber) !== 'undefined' &&
                    libphonenumber !== null &&
                    typeof (libphonenumber.parsePhoneNumberFromString) === 'function') {
                    let phoneNumberParsed = libphonenumber.parsePhoneNumberFromString('+' + vm.message.phoneNumber);
                    if (typeof (phoneNumberParsed) !== 'undefined' &&
                        phoneNumberParsed !== null) {
                        validatePhoneAgaintsCountry = false;
                        vm.message.phoneNumberParsed = phoneNumberParsed;

                        if (!phoneNumberParsed.isValid()) {
                            toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE'));
                            return false;
                        }

                        let type = phoneNumberParsed.getType();
                        if (type !== 'MOBILE' && type !== 'FIXED_LINE_OR_MOBILE') {
                            delete phoneNumberParsed.metadata;
                            console.log(`El número de teléfono ingresado ${vm.message.phoneNumber} no es un celular. Datos = ${JSON.stringify(phoneNumberParsed)}. Tipo: ${type}`);
                            toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE_NOT_MOBILE'));
                            return false;
                        }
                    }
                    else {
                        toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE'));
                        return false;
                    }
                }

                if (validatePhoneAgaintsCountry) {
                    let countryFound = false;
                    for (let i = 0; i < settingsService.settings.context.countries.length; i++) {
                        let country = settingsService.settings.context.countries[i];
                        if (vm.message.phoneNumber.startsWith(country.internationalCode.toString())) {
                            countryFound = true;
                            break;
                        }
                    }

                    if (!countryFound) {
                        toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE_COUNTRY'));
                        return false;
                    }
                }
            }

            if (typeof (vm.message.service) === 'undefined' ||
                vm.message.service === null) {
                toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_MUST_SELECT_SERVICE'));
                return false;
            }

            if (typeof (vm.message.template) === 'undefined' ||
                vm.message.template === null) {
                toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_MUST_SELECT_TEMPLATE'));
                return false;
            }

            if (typeof (vm.message.templateHeader) === 'object') {
                let header = vm.message.templateHeader;
                switch (header.type) {
                    case 'text':
                        if (typeof (vm.message.template.headerTextParameter) === 'object' &&
                            vm.message.template.headerTextParameter !== null) {
                                let value = vm.message.templateHeader.text.parameter.value;
                                if (typeof (header.text.parameter.value) !== 'string' ||
                                    header.text.parameter.value.length === 0) {
                                }
                                if (header.text.parameter.value.indexOf('    ') >= 0 ||
                                    header.text.parameter.value.indexOf('\t') >= 0 ||
                                    header.text.parameter.value.indexOf('\n') >= 0) {
                                }
                                if (value === null || value.trim().length === 0) {

                                    toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_HEADER', { parameter: header.text.parameter.name }));
                                    return false;
                                }
                        }
                        break;
                    case 'media':
                        switch (vm.mediaInputSelect) {

                            case 'url':
                                if (header.media[header.media.type].filename === null || header.media[header.media.type].filename.length === 0) {
                                    toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_FILENAME-HEADER'));
                                    return false;
                                }
                                if (header.media[header.media.type].url === null || header.media[header.media.type].url.length === 0) {
                                    toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_URL-HEADER'));
                                    return false;
                                }
                                vm.message.templateHeader.media[header.media.type].isPublic = vm.isPublicUrl;
                            break;

                            case 'file':
                                if (!Array.isArray(vm.attachments) || vm.attachments === 0) {
                                    toastr.error($translate.instant('MUST_SELECT_FILE'));
                                    return false;
                                }

                                switch (header.media.type) {
                                    case 'document':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'application/pdf') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_DOCUMENT'));
                                            return false;
                                        }
                                        break;
                                    case 'image':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'image/jpeg' &&
                                            vm.attachments[0].MimeType.toLowerCase() !== 'image/jpg' &&
                                            vm.attachments[0].MimeType.toLowerCase() !== 'image/png') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_IMAGE'));
                                            return false;
                                        }
                                        break;
                                    case 'video':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'video/mp4') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_VIDEO'));
                                            return false;
                                        }
                                        break;
                                    default:
                                        break;
                                }

                                header.media[header.media.type] = {
                                filename: vm.attachments[0].FileName,
                                file: {
                                    mimeType: vm.attachments[0].MimeType,
                                    data: vm.attachments[0].Data
                                    }
                                };
                            break;
                        }
                        break;
                }
            }

            if (Array.isArray(vm.message.templateButtons)) {
                for (let i = 0; i < vm.message.templateButtons.length; i++) {
                    const templateButton = vm.message.templateButtons[i];
                    const tempButton = vm.message.template.buttons[templateButton.index];
                    let mustToValidate = false;
                    let value;

                    switch (templateButton.type) {
                        case 'quick_reply':
                            value = templateButton.parameter[tempButton.quickReplyParameter.name];
                            mustToValidate = true;
                            break;
                        case 'authCode':
                           
                                value = templateButton.parameter[tempButton.authCodeParameter.name];
                                mustToValidate = true;
                        
                            break;
                        case 'url':
                            if (templateButton.sub_type === 'dynamic') {
                                value = templateButton.parameter[tempButton.urlParameter.name];
                                mustToValidate = true;
                            }
                            else{
                                 value = templateButton.parameter[tempButton.authCodeParameter.name];
                                mustToValidate = true;
                            }
                            break;
                        case 'offer':
                            value = templateButton.parameter[tempButton.offerCodeParameter.name];
                            mustToValidate = true;
                            break;
                    }

                    if (mustToValidate) {
                        if (value === null || value.trim().length === 0) {
                            toastr.error($translate.instant('WHATSAPP_INSERT_VALUE_FOR_BUTTON') + tempButton.text);
                            return false;
                        }

                        if (templateButton.type === 'offer' &&
                            (value.trim().length > 15 || value.indexOf(" ") !== -1)) {
                            toastr.error($translate.instant('WHATSAPP_INSERT_VALUE_FOR_BUTTON_TYPE_OFFER', { buttonName: value }));
                            return false;
                        }
                    } 
                }
            }

            if (typeof (vm.message.template.parameters) !== 'undefined' &&
                vm.message.template.parameters !== null &&
                Array.isArray(vm.message.template.parameters)) {
                let templateParameters = {};
                for (let i = 0; i < vm.message.template.parameters.length; i++) {
                    let parameter = vm.message.template.parameters[i];
                    let value = vm.message.templateParameters[i];
                    let parameterName = parameter.substr(0, parameter.indexOf('='));
                    let parameterDescription = parameter.substr(parameter.indexOf('=') + 1);

                    if (value === null || value.trim().length === 0) {
                        toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_MUST_ENTER_PARAMETER', { parameter: parameterDescription + ' {{' + parameterName + '}}' }));
                        return false;
                    }

                    if (value.indexOf('    ') >= 0 ||
                        value.indexOf('\t') >= 0 ||
                        value.indexOf('\n') >= 0) {
                        toastr.error($translate.instant('MUST_FILL_PARAMETER_VALID', { param_desc: parameterDescription, param_name: parameterName }));
                        return false;
                    }
                    templateParameters[parameterName] = value;
                }

                vm.message.parameters = templateParameters;
            }

            return true;
        }

        function send(closeOutgoing, changeAuxReason) {
            if (!isValidTemplateOutgoing()) {
                return;
            }

            let tagOutgoing = settingsService.settings.context.systemSettings['cases.TagOutgoing'];
            let importantTagOutgoing = settingsService.settings.context.systemSettings['cases.ImportantTagOutgoing'];

            let phoneNumber = vm.message.phoneNumber;
            if (vm.message.phoneNumberParsed !== null &&
                typeof (vm.message.phoneNumberParsed.formatInternational) === 'function') {
                phoneNumber = vm.message.phoneNumberParsed.formatInternational();
            }

            let dataModal = {
                title: $translate.instant('OUTGOINGMESSAGE_WHATSAPP'),
                description: $translate.instant('OUTGOINGMESSAGE_WHATSAPP_MODAL_CONFIRM', { phoneNumber: phoneNumber })
            };

			let options = {
				serviceId: vm.message.serviceId,
				phoneNumber: vm.message.phoneNumber,
				templateData: vm.message.parameters,
				templateName: vm.message.template.elementName,
				templateNamespace: vm.message.template.namespace,
				language: vm.message.template.language,
				markCaseAsPending: vm.chkMarkAsPending,
				header: vm.message.templateHeader,
				buttons: vm.message.templateButtons,
				sendHSMIfCaseOpenAnyways: vm.sendHSMIfCaseOpenAnyways,
				profileId: vm.fakeCase.case.profile.id,
				name: vm.fakeCase.case.profile.name,
				mail: vm.fakeCase.case.profile.primaryEmail,
				businessData: vm.fakeCase.case.profile.businessData
			};

			if (typeof(vm.fakeCase.case.profile.parameters === 'object') &&
				vm.fakeCase.case.profile.parameters != null) {
                options.profileExtendedFields = vm.fakeCase.case.profile.parameters.ext;
            }

            if (tagOutgoing) {
                modalSocialService.showCaseDetails(undefined, tagOutgoing, importantTagOutgoing)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            if (result && result.updated) {
								options.observations = result.observations;
                                options.tags = result.tags;
                                options.extendedFields = result.extendedFields;

                                if (importantTagOutgoing) {
                                    options.importantTag = result.importantTag;
                                }

                                utilsService.showLoadingMessage($translate.instant('SENDING_MESSAGE_LOADER'));
                                commonActionsService.sendWhatsapp(options)
                                    .then(function (response) {
                                        utilsService.hideLoading();
                                        toastr.success($translate.instant('SENDING_MESSAGE_SUCCESS'));

                                        if (!closeOutgoing) {
                                            vm.message = {
                                                phoneNumber: undefined,
                                                socialUser: undefined,
                                                userNotFound: false,
                                                serviceId: undefined,
                                                service: undefined,
                                                template: undefined,
                                                templateIndex: undefined,
                                                templateParameters: undefined,
                                                templateWithParameters: '',
                                                templateHeader: undefined,
                                                templateButtons: undefined,
                                                inputHeaderText: undefined
                                            };
                                            vm.normalAnswerAndHsm = false;
                                            vm.principalQueue = [];
                                            vm.attachments = [];
                                            vm.validatePhoneNumber = true;
                                            vm.invalidPhoneNumber = false;
                                            vm.invalidPhoneNumberPossibleCountries = null;
                                            vm.invalidPhoneNumberWrongType = false;
                                        }
                                        else {
                                            if (changeAuxReason) {
                                                close(false);
                                            }
                                            else {
                                                closeNoChangeState();
                                            }
                                        }

                                        if (vm.chkMarkAsPending) {
                                            settingsService.settings.agent.myPendingMessages.push(response.data.Result);
                                            settingsService.settings.agent.pendingMessages.push(response.data.Result);
                                        }
                                    })
                                    .catch(function (error) {
                                        utilsService.hideLoading();
                                        toastr.error($translate.instant('SENDING_MESSAGE_FAILED'));
                                        traceService.warning(`El agente intentó enviar un mensaje saliente pero hubo un error: ${JSON.stringify(error)}`);
                                    });

                            }
                        });
                    });
            }
            else {
                modalSocialService.showConfirmGeneral(dataModal)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            if (result && result.action === 'confirm') {
                                utilsService.showLoadingMessage($translate.instant('SENDING_MESSAGE_LOADER'));
                                commonActionsService.sendWhatsapp(options)
                                    .then(function (response) {
                                        utilsService.hideLoading();
                                        toastr.success($translate.instant('SENDING_MESSAGE_SUCCESS'));

                                        if (!closeOutgoing) {
                                            vm.message = {
                                                phoneNumber: undefined,
                                                socialUser: undefined,
                                                userNotFound: false,
                                                serviceId: undefined,
                                                service: undefined,
                                                template: undefined,
                                                templateIndex: undefined,
                                                templateParameters: undefined,
                                                templateWithParameters: '',
                                                templateHeader: undefined,
                                                templateButtons: undefined,
                                                inputHeaderText: undefined
                                            };
                                            vm.normalAnswerAndHsm = false;
                                            vm.principalQueue = [];
                                            vm.attachments = [];
                                            vm.validatePhoneNumber = true;
                                            vm.invalidPhoneNumber = false;
                                            vm.invalidPhoneNumberPossibleCountries = null;
                                            vm.invalidPhoneNumberWrongType = false;
                                        }
                                        else {
                                            if (changeAuxReason) {
                                                close(false);
                                            }
                                            else {
                                                closeNoChangeState();
                                            }
                                        }

                                        if (vm.chkMarkAsPending) {
                                            settingsService.settings.agent.myPendingMessages.push(response.data.Result);
                                            settingsService.settings.agent.pendingMessages.push(response.data.Result);
                                        }
                                    })
                                    .catch(function (error) {
                                        utilsService.hideLoading();
                                        if (error.data.ErrorNumber === 272) {
                                            toastr.error($translate.instant('WHATSAPP_CASE_OPEN'));
                                        }
                                        else {
                                            toastr.error($translate.instant('SENDING_MESSAGE_FAILED'));
                                            traceService.warning(`El agente intentó enviar un mensaje saliente pero hubo un error: ${JSON.stringify(error)}`);
                                        }
                                    });

                            }
                        });
                    });
            }
        }

        function close(confirm) {
            if (typeof (confirm) !== 'boolean') {
                confirm = true;
            }

            if (confirm) {
                let dataModal = {
                    title: $translate.instant('MODAL_CONFIRM_LEAVE_OUTBOUND_ASSISTANT_TITLE'),
                    description: $translate.instant('MODAL_CONFIRM_LEAVE_OUTBOUND_ASSISTANT_DESCRIPTION')
                };

                modalSocialService.showConfirmLeaveOutboundAssistant(dataModal)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            if (result && result.action === 'confirm') {
                                statesService.switchStates(outgoingService.statusBeforeOutgoing);
                                integrationsService.executeActions('outgoingmessagefinished');
                            }
                        });
                    });
            }
            else {
                statesService.switchStates(outgoingService.statusBeforeOutgoing);
                integrationsService.executeActions('outgoingmessagefinished');
            }
        }

        function closeNoChangeState() {
            vm.agentService.isOutgoingWhatsappNoChangeState = false;
            integrationsService.executeActions('outgoingmessagefinished');
        }
    }
})();
