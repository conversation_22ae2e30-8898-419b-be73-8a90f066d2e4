﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <DefineConstants Condition="'$(NETCOREAPP=1)'=='1'">$(DefineConstants);NETCOREAPP</DefineConstants>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Yoizen.Social.DomainModel</AssemblyName>
    <RootNamespace>Yoizen.Social.DomainModel</RootNamespace>
    <Company>Yoizen</Company>
    <SignAssembly>True</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
    <InvariantGlobalization>true</InvariantGlobalization>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\GlobalAssemblyInfo.cs" Link="Properties\GlobalAssemblyInfo.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentTagRequiredOptions.cs" Link="AgentTagRequiredOptions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseClosingSources.cs" Link="CaseClosingSources.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Properties\AssemblyInfo.cs" Link="Properties\AssemblyInfo.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AccountLink.cs" Link="AccountLink.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AccountLinkingMessage.cs" Link="AccountLinkingMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Agent.cs" Link="Agent.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentConnectionResult.cs" Link="AgentConnectionResult.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentGroup.cs" Link="AgentGroup.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentLog.cs" Link="AgentLog.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentLogTypes.cs" Link="AgentLogTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotification.cs" Link="AgentNotification.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\AlertMessage.cs" Link="AgentNotifications\AlertMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChangeStatus.cs" Link="AgentNotifications\ChangeStatus.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatAbandoned.cs" Link="AgentNotifications\ChatAbandoned.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatActionAttachment.cs" Link="AgentNotifications\ChatActionAttachment.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatActionMessage.cs" Link="AgentNotifications\ChatActionMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatActionStopTyping.cs" Link="AgentNotifications\ChatActionStopTyping.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatActionTyping.cs" Link="AgentNotifications\ChatActionTyping.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatNotification.cs" Link="AgentNotifications\ChatNotification.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatSupervisedEnd.cs" Link="AgentNotifications\ChatSupervisedEnd.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatSupervisedStart.cs" Link="AgentNotifications\ChatSupervisedStart.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ChatUnasignedAfterDisconnection.cs" Link="AgentNotifications\ChatUnasignedAfterDisconnection.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ConversationEvent.cs" Link="AgentNotifications\ConversationEvent.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\DisconnectedBySupervisor.cs" Link="AgentNotifications\DisconnectedBySupervisor.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\InternalChatMessage.cs" Link="AgentNotifications\InternalChatMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\InternalChatSupervisorDisconnected.cs" Link="AgentNotifications\InternalChatSupervisorDisconnected.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\MessageUnasigned.cs" Link="AgentNotifications\MessageUnasigned.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\NewMessageFromAssignedCase.cs" Link="AgentNotifications\NewMessageFromAssignedCase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\QueueChanged.cs" Link="AgentNotifications\QueueChanged.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ReservedMessage.cs" Link="AgentNotifications\ReservedMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\ServiceChanged.cs" Link="AgentNotifications\ServiceChanged.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\WhatsappMessageStatusChange.cs" Link="AgentNotifications\WhatsappMessageStatusChange.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\WhenPendingCaseAssigned.cs" Link="AgentNotifications\WhenPendingCaseAssigned.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\WhenPendingCaseClosed.cs" Link="AgentNotifications\WhenPendingCaseClosed.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotifications\WhenPendingCaseRemoved.cs" Link="AgentNotifications\WhenPendingCaseRemoved.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\DTO\AgentQueuesExportHelper.cs" Link="DTO\AgentQueuesExportHelper.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AgentNotificationTypes.cs" Link="AgentNotificationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseStartedBySouces.cs" Link="CaseStartedBySouces.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\DownloadedFileContents.cs" Link="DownloadedFileContents.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\FilterActionKeyItemConfig.cs" Link="Filters\FilterActionKeyItemConfig.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\FilterActionConfigField.cs" Link="Filters\FilterActionConfigField.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\FilterActionInvokeResults.cs" Link="Filters\FilterActionInvokeResults.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\FilterActionAssemblyException.cs" Link="Filters\FilterActionAssemblyException.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\IFilterActions.cs" Link="Filters\IFilterActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ISocialServiceTypeCreator.cs" Link="ISocialServiceTypeCreator.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ChatMessageConverter.cs" Link="JSON\ChatMessageConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\CaseInsensitiveDictionaryConverter.cs" Link="JSON\CaseInsensitiveDictionaryConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ChatConverter.cs" Link="JSON\ChatConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\AttachmentConverter.cs" Link="JSON\AttachmentConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\BotConversationMessageConverter.cs" Link="JSON\BotConversationMessageConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\GeoCoordinateConverter.cs" Link="JSON\GeoCoordinateConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ServiceSettingsContractResolver.cs" Link="JSON\ServiceSettingsContractResolver.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\SocialUserProfileConverter.cs" Link="JSON\SocialUserProfileConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\SocialUserConverter.cs" Link="JSON\SocialUserConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\TagAgentConverter.cs" Link="JSON\TagAgentConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\AuxReasonConverter.cs" Link="JSON\AuxReasonConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\TagJsonWriter.cs" Link="JSON\TagJsonWriter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\InternalChatAgentClosed.cs" Link="Notifications\InternalChatAgentClosed.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\InternalChatMessage.cs" Link="Notifications\InternalChatMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\InternalChatAgentDisconnected.cs" Link="Notifications\InternalChatAgentDisconnected.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\TaskProgress.cs" Link="Notifications\TaskProgress.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\GoogleBusinessSettings.cs" Link="ServiceSettings\GoogleBusinessSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\GoogleRBMAttachmentsSettings.cs" Link="ServiceSettings\GoogleRBMAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\GoogleRBMSettings.cs" Link="ServiceSettings\GoogleRBMSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewayIntegration\GatewayIntegrationActionsToExecute.cs" Link="Settings\GatewayIntegration\GatewayIntegrationActionsToExecute.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewayIntegration\GatewayIntegrationCCasSConfiguration.cs" Link="Settings\GatewayIntegration\GatewayIntegrationCCasSConfiguration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewayIntegration\GatewayIntegrationConfiguration.cs" Link="Settings\GatewayIntegration\GatewayIntegrationConfiguration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewayIntegration\GatewayIntegrationTypes.cs" Link="Settings\GatewayIntegration\GatewayIntegrationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewayIntegration\GatewayIntegrationConfigurationJavaScriptConverter.cs" Link="Settings\GatewayIntegration\GatewayIntegrationConfigurationJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GoogleRBMSettings.cs" Link="Settings\GoogleRBMSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\AppleMessagingSettings.cs" Link="ServiceSettings\AppleMessagingSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GatewaySettings.cs" Link="Settings\GatewaySettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\Integration.cs" Link="Settings\Integration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationConditions\IntegrationCondition.cs" Link="Settings\IntegrationConditions\IntegrationCondition.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\RestChatSettings.cs" Link="Settings\RestChatSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SkypeSettings.cs" Link="Settings\SkypeSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\StorageSettings.cs" Link="Settings\StorageSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WFMSettings.cs" Link="Settings\WFMSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\AppleMessagingSettings.cs" Link="Settings\AppleMessagingSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\GooglePlaySettings.cs" Link="ServiceSettings\GooglePlaySettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\VideoCallSettings.cs" Link="ServiceSettings\VideoCallSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\AutomaticExportSettings.cs" Link="Settings\AutomaticExportSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\AutomaticExportByReportSettings.cs" Link="Settings\AutomaticExportByReportSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SFTPConnectionSettings.cs" Link="Settings\SFTPConnectionSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\UploadFileConnectionSettings.cs" Link="Settings\UploadFileConnectionSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\FTPConnectionSettings.cs" Link="Settings\FTPConnectionSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Status\ACDStatus.cs" Link="Status\ACDStatus.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\Status\ContingencyBot.cs" Link="Status\ContingencyBot.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Status\ServiceStatus.cs" Link="Status\ServiceStatus.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\BaseJavaScriptConverter.cs" Link="Historical\BaseJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyAdherence.cs" Link="Historical\DailyAdherence.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailySurvey.cs" Link="Historical\DailySurvey.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\UserSession.cs" Link="Historical\UserSession.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\PendingBasicInfoConverter.cs" Link="JSON\PendingBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\TemplateConverter.cs" Link="JSON\TemplateConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\AgentRequiresHelp.cs" Link="Notifications\AgentRequiresHelp.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\AnnoyingUser.cs" Link="Notifications\AnnoyingUser.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\MessageRequiresAuthorization.cs" Link="Notifications\MessageRequiresAuthorization.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\ServiceCrashed.cs" Link="Notifications\ServiceCrashed.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\DeliveryFailed.cs" Link="Notifications\DeliveryFailed.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\ServiceLevel.cs" Link="Notifications\ServiceLevel.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notifications\MessageFiltered.cs" Link="Notifications\MessageFiltered.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\IntegrationChatAttachmentsSettings.cs" Link="ServiceSettings\IntegrationChatAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\IntegrationChatSettings.cs" Link="ServiceSettings\IntegrationChatSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\MercadoLibreAttachmentSettingsy.cs" Link="ServiceSettings\MercadoLibreAttachmentSettingsy.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\LinkedInAttachmentSettings.cs" Link="ServiceSettings\LinkedInAttachmentSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\YouTubeAttachmentsSettings.cs" Link="ServiceSettings\YouTubeAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\YouTubeSettings.cs" Link="ServiceSettings\YouTubeSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\DeliveryFailedNotificationSettings.cs" Link="Settings\DeliveryFailedNotificationSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\InternalChatSettings.cs" Link="Settings\InternalChatSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WorkingTimesSettings.cs" Link="Settings\WorkingTimesSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WebAgentIFrameIntegrationSettings.cs" Link="Settings\WebAgentIFrameIntegrationSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\TaskParameters.cs" Link="Tasks\TaskParameters.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\Task.cs" Link="Tasks\Task.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\TaskResults.cs" Link="Tasks\TaskResults.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\TaskStatuses.cs" Link="Tasks\TaskStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\TaskTypes.cs" Link="Tasks\TaskTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\WhatsappMassiveHSMRequest.cs" Link="Tasks\WhatsappMassiveHSMRequest.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\ProfilesMassiveUploadTask.cs" Link="Tasks\ProfilesMassiveUploadTask.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\WhatsappMassiveHSMTaskWithoutCaseCreation.cs" Link="Tasks\WhatsappMassiveHSMTaskWithoutCaseCreation.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tasks\WhatsappMassiveHSMTask.cs" Link="Tasks\WhatsappMassiveHSMTask.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Helpers\YourlsClient.cs" Link="Helpers\YourlsClient.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\FilterActionWebServiceException.cs" Link="Filters\FilterActionWebServiceException.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorBase.cs" Link="Filters\WebServiceActionEvaluatorBase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorVIM.cs" Link="Filters\WebServiceActionEvaluatorVIM.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActions.cs" Link="Filters\WebServiceActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceInvokeResults.cs" Link="Filters\WebServiceInvokeResults.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceInvokeSettings.cs" Link="Filters\WebServiceInvokeSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorMoveToQueue.cs" Link="Filters\WebServiceActionEvaluatorMoveToQueue.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorAddParameterToCase.cs" Link="Filters\WebServiceActionEvaluatorAddParameterToCase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorDiscard.cs" Link="Filters\WebServiceActionEvaluatorDiscard.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filters\WebServiceActionEvaluatorAutoReply.cs" Link="Filters\WebServiceActionEvaluatorAutoReply.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyCase.cs" Link="Historical\DailyCase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyFilter.cs" Link="Historical\DailyFilter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\IdToStringConverter.cs" Link="JSON\IdToStringConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\JsonTextWriterEx.cs" Link="JSON\JsonTextWriterEx.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\QueueConverter.cs" Link="JSON\QueueConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\SecureStringJsonConverter.cs" Link="JSON\SecureStringJsonConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\AgentJavaScriptConverter.cs" Link="JSON\AgentJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\PersonJavaScriptConverter.cs" Link="JSON\PersonJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ServiceConverter.cs" Link="JSON\ServiceConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\UserJavaScriptConverter.cs" Link="JSON\UserJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\FilterConditionTypesJavaScriptConverter.cs" Link="JSON\FilterConditionTypesJavaScriptConverter.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\JSON\DomainObjectBasicInfoConverter.cs" Link="JSON\DomainObjectBasicInfoConverter.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\JSON\DomainObjectNameInfoConverter.cs" Link="JSON\DomainObjectNameInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\PrivatePropertiesContractResolver.cs" Link="JSON\PrivatePropertiesContractResolver.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\SurveyAnswerConverter.cs" Link="JSON\SurveyAnswerConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\SurveyConverter.cs" Link="JSON\SurveyConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\ChatSettings.cs" Link="ServiceSettings\ChatSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\InstagramAttachmentsSettings.cs" Link="ServiceSettings\InstagramAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\FacebookMessengerAttachmentsSettings.cs" Link="ServiceSettings\FacebookMessengerAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\FacebookMessengerSettings.cs" Link="ServiceSettings\FacebookMessengerSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\MercadoLibreSettings.cs" Link="ServiceSettings\MercadoLibreSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\YFlowSettings.cs" Link="ServiceSettings\YFlowSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\SkypeAttachmentsSettings.cs" Link="ServiceSettings\SkypeAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\AnnoyingEmailSettings.cs" Link="Settings\AnnoyingEmailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\BaseServiceSettings.cs" Link="Settings\BaseServiceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\GoogleAuthSettings.cs" Link="Settings\GoogleAuthSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\KeycloakAuthSettings.cs" Link="Settings\KeycloakAuthSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\RealTimeSettings.cs" Link="Settings\RealTimeSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\MercadoLibreSettings.cs" Link="Settings\MercadoLibreSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\YFlowSettings.cs" Link="Settings\YFlowSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\YUsageSettings.cs" Link="Settings\YUsageSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\FacebookMessengerSettings.cs" Link="Settings\FacebookMessengerSettings.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\Settings\YSmartSettings.cs" Link="Settings\YSmartSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ServerIntegrationActions\ServerIntegrationActionBaseSettings.cs" Link="Settings\ServerIntegrationActions\ServerIntegrationActionBaseSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ServerIntegrationActions\ServerIntegrationActionSendEmailSettings.cs" Link="Settings\ServerIntegrationActions\ServerIntegrationActionSendEmailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ServerIntegrationActions\ServerIntegrationActionNotifySupervisorsSettings.cs" Link="Settings\ServerIntegrationActions\ServerIntegrationActionNotifySupervisorsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ServerIntegrationActions\ServerIntegrationActionHttpRequestSettings.cs" Link="Settings\ServerIntegrationActions\ServerIntegrationActionHttpRequestSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionPostMessageSettings.cs" Link="Settings\IntegrationActions\IntegrationActionPostMessageSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SubscriptionSettings.cs" Link="Settings\SubscriptionSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\MaintenanceSettings.cs" Link="Settings\MaintenanceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WebAgentStateManagementSettings.cs" Link="Settings\WebAgentStateManagementSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WebAgentUrlLoginSettings.cs" Link="Settings\WebAgentUrlLoginSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WordCloudSettings.cs" Link="Settings\WordCloudSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\BitLySettings.cs" Link="Settings\BitLySettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\FilterEmailSettings.cs" Link="Settings\FilterEmailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionAssemblySettings.cs" Link="Settings\IntegrationActions\IntegrationActionAssemblySettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionMessageBoxSettings.cs" Link="Settings\IntegrationActions\IntegrationActionMessageBoxSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionHttpRequestSettings.cs" Link="Settings\IntegrationActions\IntegrationActionHttpRequestSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionOpenBrowserSettings.cs" Link="Settings\IntegrationActions\IntegrationActionOpenBrowserSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionExecuteSettings.cs" Link="Settings\IntegrationActions\IntegrationActionExecuteSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationsSettings.cs" Link="Settings\IntegrationsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\IntegrationActions\IntegrationActionBaseSettings.cs" Link="Settings\IntegrationActions\IntegrationActionBaseSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SurveysServiceSettings.cs" Link="Settings\SurveysServiceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\SkypeSettings.cs" Link="ServiceSettings\SkypeSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ExporterServiceSettings.cs" Link="Settings\ExporterServiceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\UserMessageStats.cs" Link="Stats\UserMessageStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Surveys\SurveyConfiguration.cs" Link="Surveys\SurveyConfiguration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\AgentBasicInfoConverter.cs" Link="JSON\AgentBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\GeoCoordinateJavaScriptConverter.cs" Link="JSON\GeoCoordinateJavaScriptConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\FacebookAttachmentsSettings.cs" Link="ServiceSettings\FacebookAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\FacebookSettings.cs" Link="ServiceSettings\FacebookSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\InstagramAttachmentSettings.cs" Link="ServiceSettings\InstagramAttachmentSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\LinkedInSettings.cs" Link="ServiceSettings\LinkedInSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\InstagramSettings.cs" Link="ServiceSettings\InstagramSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\MailAttachmentsSettings.cs" Link="ServiceSettings\MailAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\MailSettings.cs" Link="ServiceSettings\MailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\ServiceAttachmentTypes.cs" Link="ServiceSettings\ServiceAttachmentTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\TelegramAttachmentsSettings.cs" Link="ServiceSettings\TelegramAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\SMSSettings.cs" Link="ServiceSettings\SMSSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\TelegramSettings.cs" Link="ServiceSettings\TelegramSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\TwitterAttachmentsSettings.cs" Link="ServiceSettings\TwitterAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\TwitterSettings.cs" Link="ServiceSettings\TwitterSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\WhatsappAttachmentsSettings.cs" Link="ServiceSettings\WhatsappAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\WhatsappSettings.cs" Link="ServiceSettings\WhatsappSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\BaseAttachmentsSettings.cs" Link="ServiceSettings\BaseAttachmentsSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\ServiceSettings.cs" Link="ServiceSettings\ServiceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceSettings\GroupingSettings.cs" Link="ServiceSettings\GroupingSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\LinkedInSettings.cs" Link="Settings\LinkedInSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\InstagramSettings.cs" Link="Settings\InstagramSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\FacebookSettings.cs" Link="Settings\FacebookSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\MailSettings.cs" Link="Settings\MailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SMSSettings.cs" Link="Settings\SMSSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\TwitterSettings.cs" Link="Settings\TwitterSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\WhatsappSettings.cs" Link="Settings\WhatsappSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\TelegramSettings.cs" Link="Settings\TelegramSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\BaseSettings.cs" Link="Settings\BaseSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ChatSettings.cs" Link="Settings\ChatSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\CasesSettings.cs" Link="Settings\CasesSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\EmailConnectionSettings.cs" Link="Settings\EmailConnectionSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\EmailSettings.cs" Link="Settings\EmailSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\CognitiveServicesSettings.cs" Link="Settings\CognitiveServicesSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\ForwardSettings.cs" Link="Settings\ForwardSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\LDAPSettings.cs" Link="Settings\LDAPSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SocialServiceSettings.cs" Link="Settings\SocialServiceSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\CaseConverter.cs" Link="JSON\CaseConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\PersonBasicInfoConverter.cs" Link="JSON\PersonBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ConversationConverter.cs" Link="JSON\ConversationConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\DictionaryWithDomainObjectConverter.cs" Link="JSON\DictionaryWithDomainObjectConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\JsonCreationConverter.cs" Link="JSON\JsonCreationConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\MessageArrayBasicInfoConverter.cs" Link="JSON\MessageArrayBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\MessageBasicInfoConverter.cs" Link="JSON\MessageBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\FilterBasicInfoConverter.cs" Link="JSON\FilterBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\MessageConverter.cs" Link="JSON\MessageConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\QueueBasicInfoConverter.cs" Link="JSON\QueueBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\QueuesWithDistributionValuesConverter.cs" Link="JSON\QueuesWithDistributionValuesConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\ServiceBasicInfoConverter.cs" Link="JSON\ServiceBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\TagBasicInfoConverter.cs" Link="JSON\TagBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\JSON\UserBasicInfoConverter.cs" Link="JSON\UserBasicInfoConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ExportFormats.cs" Link="Reports\Export\ExportFormats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ExportPeriodicity.cs" Link="Reports\Export\ExportPeriodicity.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AutomaticActions.cs" Link="AutomaticActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AutomaticActionsTypes.cs" Link="AutomaticActionsTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AutomaticActionsMethods.cs" Link="AutomaticActionsMethods.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\ReportTypes.cs" Link="Reports\ReportTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\SocialUserReference.cs" Link="Settings\SocialUserReference.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Reports\RTAgentsModels\AgentInfoModel.cs" Link="Reports\RTAgentsModels\AgentInfoModel.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Reports\RTAgentsModels\StatsInfo.cs" Link="Reports\RTAgentsModels\StatsInfo.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Reports\RTAgentsModels\StatusInfo.cs" Link="Reports\RTAgentsModels\StatusInfo.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\QueueExport.cs" Link="Reports\Export\QueueExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\AgentQueuesExport.cs" Link="Reports\Export\AgentQueuesExport.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\RTAgentsExport.cs" Link="Reports\Export\RTAgentsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\WhatsappHSMWithoutCase.cs" Link="Reports\Export\WhatsappHSMWithoutCase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\MessagesSegmentsExport.cs" Link="Reports\Export\MessagesSegmentsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DoNotCallListExport.cs" Link="Reports\Export\DoNotCallListExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\TesterListExport.cs" Link="Reports\Export\TesterListExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ReportScheduled.cs" Link="Reports\Export\ReportScheduled.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\CasesReopeningsExport.cs" Link="Reports\Export\CasesReopeningsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ChatsMessagesExport.cs" Link="Reports\Export\ChatsMessagesExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\MessagesTransfersExport.cs" Link="Reports\Export\MessagesTransfersExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\WhatsappHSMRequestTasksExport.cs" Link="Reports\Export\WhatsappHSMRequestTasksExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\AdherenceDetailedExport.cs" Link="Reports\Export\AdherenceDetailedExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ChatMessagesExport.cs" Link="Reports\Export\ChatMessagesExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\AdherenceConsolidatedExport.cs" Link="Reports\Export\AdherenceConsolidatedExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DetailedBySurveyExport.cs" Link="Reports\Export\DetailedBySurveyExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ReportExportResults.cs" Link="Reports\Export\ReportExportResults.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ReportExportStatus.cs" Link="Reports\Export\ReportExportStatus.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\UserSessionsExport.cs" Link="Reports\Export\UserSessionsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\SocialUserProfilesExport.cs" Link="Reports\Export\SocialUserProfilesExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\TasksExport.cs" Link="Reports\Export\TasksExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\WhatsappHSMTasksExport.cs" Link="Reports\Export\WhatsappHSMTasksExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ConsolidatedWhatsappHSMExport.cs" Link="Reports\Export\ConsolidatedWhatsappHSMExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DetailedWhatsappHSMExport.cs" Link="Reports\Export\DetailedWhatsappHSMExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\BlackListExport.cs" Link="Reports\Export\BlackListExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\WhiteListExport.cs" Link="Reports\Export\WhiteListExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\TagsExport.cs" Link="Reports\Export\TagsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DetailedByServiceExport.cs" Link="Reports\Export\DetailedByServiceExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\AgentsExport.cs" Link="Reports\Export\AgentsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DailyByCaseExport.cs" Link="Reports\Export\DailyByCaseExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\SessionByAgentExport.cs" Link="Reports\Export\SessionByAgentExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\SurveysExport.cs" Link="Reports\Export\SurveysExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\UsersExport.cs" Link="Reports\Export\UsersExport.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\UsersLogExport.cs" Link="Reports\Export\UsersLogExport.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\UsersLogWithoutParametersExport.cs" Link="Reports\Export\UsersLogWithoutParametersExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ChatsExport.cs" Link="Reports\Export\ChatsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DetailedByAgentExport.cs" Link="Reports\Export\DetailedByAgentExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\DetailedByQueueExport.cs" Link="Reports\Export\DetailedByQueueExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\SessionsByAgentExport.cs" Link="Reports\Export\SessionsByAgentExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\SocialUsersExport.cs" Link="Reports\Export\SocialUsersExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\TagsByAgentExport.cs" Link="Reports\Export\TagsByAgentExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\TagsByQueueExport.cs" Link="Reports\Export\TagsByQueueExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ExcelHelper.cs" Link="Reports\Export\ExcelHelper.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\MessagesExport.cs" Link="Reports\Export\MessagesExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\CasesExport.cs" Link="Reports\Export\CasesExport.cs" />
	<Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\VideoCallsExport.cs" Link="Reports\Export\VideoCallsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\ReportExport.cs" Link="Reports\Export\ReportExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\CasesTimesExport.cs" Link="Reports\Export\CasesTimesExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Reports\Export\CallsExport.cs" Link="Reports\Export\CallsExport.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\AgentComputedStats.cs" Link="Stats\AgentComputedStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyService.cs" Link="Historical\DailyService.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyBase.cs" Link="Historical\DailyBase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyTag.cs" Link="Historical\DailyTag.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\DailyTask.cs" Link="Historical\DailyTask.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\Daily.cs" Link="Historical\Daily.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Historical\Session.cs" Link="Historical\Session.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\AgentTimeStats.cs" Link="Stats\AgentTimeStats.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Stats\AgentCasesStats.cs" Link="Stats\AgentCasesStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\AgentCallsStats.cs" Link="Stats\AgentCallsStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\IDataRecordExtensionMethods.cs" Link="Stats\IDataRecordExtensionMethods.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\MessageLogStat.cs" Link="Stats\MessageLogStat.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\QueuePerformanceStats.cs" Link="Stats\QueuePerformanceStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\StatBase.cs" Link="Stats\StatBase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Stats\AgentMessageStats.cs" Link="Stats\AgentMessageStats.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Flows\Flow.cs" Link="Whatsapp\Flows\Flow.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Flows\FlowCategories.cs" Link="Whatsapp\Flows\FlowCategories.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Flows\FlowScreen.cs" Link="Whatsapp\Flows\FlowScreen.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Flows\FlowStatuses.cs" Link="Whatsapp\Flows\FlowStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateFlowParameters.cs" Link="Whatsapp\HSMTemplateFlowParameters.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTypesOfAnswers.cs" Link="Whatsapp\HSMTypesOfAnswers.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateSendDefinition.cs" Link="Whatsapp\HSMTemplateSendDefinition.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateButton.cs" Link="Whatsapp\HSMTemplateButton.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateButtonsTypes.cs" Link="Whatsapp\HSMTemplateButtonsTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateCallToActionUrlButtonTypes.cs" Link="Whatsapp\HSMTemplateCallToActionUrlButtonTypes.cs" />
	  <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateAuthCodeButtonTypes.cs" Link="Whatsapp\HSMTemplateAuthCodeButtonTypes.cs" />
	  <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateCallToActionButtonTypes.cs" Link="Whatsapp\HSMTemplateCallToActionButtonTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateFooterTypes.cs" Link="Whatsapp\HSMTemplateFooterTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateHeaderMediaTypes.cs" Link="Whatsapp\HSMTemplateHeaderMediaTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateHeaderTypes.cs" Link="Whatsapp\HSMTemplateHeaderTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMTemplateParameter.cs" Link="Whatsapp\HSMTemplateParameter.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessage.cs" Link="Whatsapp\InteractiveMessage.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageText.cs" Link="Whatsapp\InteractiveMessageText.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageButton.cs" Link="Whatsapp\InteractiveMessageButton.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageCatalog.cs" Link="Whatsapp\InteractiveMessageCatalog.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageList.cs" Link="Whatsapp\InteractiveMessageList.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageProduct.cs" Link="Whatsapp\InteractiveMessageProduct.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageProductList.cs" Link="Whatsapp\InteractiveMessageProductList.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageFlow.cs" Link="Whatsapp\InteractiveMessageFlow.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageVoiceCall.cs" Link="Whatsapp\InteractiveMessageVoiceCall.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageCallPermissionRequest.cs" Link="Whatsapp\InteractiveMessageCallPermissionRequest.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageTypes.cs" Link="Whatsapp\InteractiveMessageTypes.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageHeaderTypes.cs" Link="Whatsapp\InteractiveMessageHeaderTypes.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageFooterTypes.cs" Link="Whatsapp\InteractiveMessageFooterTypes.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\InteractiveMessageCtaUrl.cs" Link="Whatsapp\InteractiveMessageCtaUrl.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Events\DomainObjectSyncEvent.cs" Link="Events\DomainObjectSyncEvent.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Events\EventBase.cs" Link="Events\EventBase.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Events\EventJsonConverter.cs" Link="Events\EventJsonConverter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Events\EventTypes.cs" Link="Events\EventTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Attachment.cs" Link="Attachment.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AuthenticationTypes.cs" Link="AuthenticationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\AuxReason.cs" Link="AuxReason.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\BiometricMessage.cs" Link="BiometricMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Cache.cs" Link="Cache.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\Call.cs" Link="Call.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\CallEndResponsibles.cs" Link="CallEndResponsibles.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\CallLogTypes.cs" Link="CallLogTypes.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\CallStatuses.cs" Link="CallStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Case.cs" Link="Case.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseClosingResponsibles.cs" Link="CaseClosingResponsibles.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseLog.cs" Link="CaseLog.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseLogTypes.cs" Link="CaseLogTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseMessagePending.cs" Link="CaseMessagePending.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseReopening.cs" Link="CaseReopening.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\CaseStatuses.cs" Link="CaseStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Chat.cs" Link="Chat.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatFinishReasons.cs" Link="ChatFinishReasons.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatLog.cs" Link="ChatLog.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatLogTypes.cs" Link="ChatLogTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatMappingTypes.cs" Link="ChatMappingTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatMessage.cs" Link="ChatMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatMessageTypes.cs" Link="ChatMessageTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatSenders.cs" Link="ChatSenders.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ChatStructuredMessageTypes.cs" Link="ChatStructuredMessageTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ConnectionInfo.cs" Link="ConnectionInfo.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ConnectionStatuses.cs" Link="ConnectionStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ContactReason.cs" Link="ContactReason.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Conversation.cs" Link="Conversation.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Country.cs" Link="Country.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\DataReader.cs" Link="DataReader.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\DiscardSources.cs" Link="DiscardSources.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\DomainObject.cs" Link="DomainObject.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Entities.cs" Link="Entities.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ExtendedField.cs" Link="ExtendedField.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ExtendedProfileBusinessCodeField.cs" Link="ExtendedProfileBusinessCodeField.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ExternalIntegration.cs" Link="ExternalIntegration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ExternalIntegrationSecurity.cs" Link="ExternalIntegrationSecurity.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Filter.cs" Link="Filter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\FilterActions.cs" Link="FilterActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\FilterCondition.cs" Link="FilterCondition.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\FilterConditionCollection.cs" Link="FilterConditionCollection.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\FilterConditionTypes.cs" Link="FilterConditionTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\FontTypes.cs" Link="FontTypes.cs" />
		<Compile Include="..\Yoizen.Social.DomainModel\GeoCoordinate.cs" Link="GeoCoordinate.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\HistSessionsAgentsCasesMessages.cs" Link="HistSessionsAgentsCasesMessages.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\HttpRequestSettings.cs" Link="HttpRequestSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IAgentGrupable.cs" Link="IAgentGrupable.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IAgentSettingsManager.cs" Link="IAgentSettingsManager.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IMessageDistribution.cs" Link="IMessageDistribution.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IntegrationActionTypes.cs" Link="IntegrationActionTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IntegrationChatActions.cs" Link="IntegrationChatActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\IntegrationTypes.cs" Link="IntegrationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ISocialService.cs" Link="ISocialService.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ISocialServiceOperations.cs" Link="ISocialServiceOperations.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ISocialServiceStatus.cs" Link="ISocialServiceStatus.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Localizations.cs" Link="Localizations.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\LoginResponseInformation.cs" Link="LoginResponseInformation.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MailQuoteTypes.cs" Link="MailQuoteTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Message.cs" Link="Message.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageDistributionTypes.cs" Link="MessageDistributionTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageLog.cs" Link="MessageLog.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageLogTypes.cs" Link="MessageLogTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageSegment.cs" Link="MessageSegment.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageStatuses.cs" Link="MessageStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MessageTransfer.cs" Link="MessageTransfer.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MimeTypeMap.cs" Link="MimeTypeMap.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\NameFormatter.cs" Link="NameFormatter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Notification.cs" Link="Notification.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\NotificationTypes.cs" Link="NotificationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ObjectExtensions.cs" Link="ObjectExtensions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\PaymentMessage.cs" Link="PaymentMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\PendingResponseFromCustomerByOrigins.cs" Link="PendingResponseFromCustomerByOrigins.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Permission.cs" Link="Permission.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Person.cs" Link="Person.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\PersonTypes.cs" Link="PersonTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Profile.cs" Link="Profile.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\PublicMessageServiceBusOptions.cs" Link="PublicMessageServiceBusOptions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ExtraHeader.cs" Link="ExtraHeader.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Queue.cs" Link="Queue.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\QueueGroup.cs" Link="QueueGroup.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\QueueSortTypes.cs" Link="QueueSortTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\QueueSurveyConfiguration.cs" Link="QueueSurveyConfiguration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\QueuesWithDistributionValues.cs" Link="QueuesWithDistributionValues.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ReplySources.cs" Link="ReplySources.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\RequestOriginators.cs" Link="RequestOriginators.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\RequiredTagging.cs" Link="RequiredTagging.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServerIntegrationActionTypes.cs" Link="ServerIntegrationActionTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServerIntegrationTypes.cs" Link="ServerIntegrationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServerTimingDurationComponent.cs" Link="ServerTimingDurationComponent.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Service.cs" Link="Service.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceConfiguration.cs" Link="ServiceConfiguration.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceLevel.cs" Link="ServiceLevel.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceLevelActions.cs" Link="ServiceLevelActions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceLevelTypes.cs" Link="ServiceLevelTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ServiceTypes.cs" Link="ServiceTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\ShortGuid.cs" Link="ShortGuid.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SignatureMessage.cs" Link="SignatureMessage.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Site.cs" Link="Site.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialServiceTypes.cs" Link="SocialServiceTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialUser.cs" Link="SocialUser.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialUserParametersByService.cs" Link="SocialUserParametersByService.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialUserProfile.cs" Link="SocialUserProfile.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialUserProfilesList.cs" Link="SocialUserProfilesList.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SocialUserServiceParameters.cs" Link="SocialUserServiceParameters.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SR.cs" Link="SR.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\StorageManager.cs" Link="StorageManager.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\StringExtensions.cs" Link="StringExtensions.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Survey.cs" Link="Survey.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SurveyAnswer.cs" Link="SurveyAnswer.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SurveyStatuses.cs" Link="SurveyStatuses.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SurveyTypes.cs" Link="SurveyTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SystemActionTypes.cs" Link="SystemActionTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SystemEntityTypes.cs" Link="SystemEntityTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SystemSetttings.cs" Link="SystemSetttings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SystemStatus.cs" Link="SystemStatus.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\SystemUpdateSettings.cs" Link="SystemUpdateSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Tag.cs" Link="Tag.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\TaggedBySources.cs" Link="TaggedBySources.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\TagGroup.cs" Link="TagGroup.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Template.cs" Link="Template.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\TracingCommonProperties.cs" Link="TracingCommonProperties.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\User.cs" Link="User.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\UserConnectionInfo.cs" Link="UserConnectionInfo.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\UserLog.cs" Link="UserLog.cs" />
	  <Compile Include="..\Yoizen.Social.DomainModel\VideoCall.cs" Link="VideoCall.cs" />
	  <Compile Include="..\Yoizen.Social.DomainModel\WebAgentIFrameIntegrationTypes.cs" Link="WebAgentIFrameIntegrationTypes.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Movistar\HandoffContext.cs" Link="Whatsapp\Movistar\HandoffContext.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\Movistar\HandoffMotives.cs" Link="Whatsapp\Movistar\HandoffMotives.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Whatsapp\HSMButtonPayload.cs" Link="Whatsapp\HSMButtonPayload.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Settings\MetaApp\MetaAppSettings.cs" Link="Settings\MetaApp\MetaAppSettings.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\Metrics\MetricsAdapter.cs" Link="Metrics\MetricsAdapter.cs" />
    <Compile Include="..\Yoizen.Social.DomainModel\MetricsManager.cs" Link="MetricsManager.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="..\Yoizen.Social.DomainModel\ClassesDefinitions.resx" Link="ClassesDefinitions.resx" />
  </ItemGroup> 

  <ItemGroup>
    <PackageReference Include="Abp.MailKit" Version="9.2.2" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Core.Renci.SshNet" Version="2021.10.2" />
    <PackageReference Include="CsvHelper" Version="31.0.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.1" />
    <PackageReference Include="FluentFTP" Version="50.0.1" />
    <PackageReference Include="Microsoft.Exchange.WebServices" Version="2.2.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="MimeKit" Version="4.6.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Renci.SshNet.Async" Version="1.4.0" />
    <PackageReference Include="SSH.NET" Version="2024.1.0" />
    <PackageReference Include="UAParser" Version="3.1.47" />
  </ItemGroup>

  <ItemGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <ProjectReference Include="..\Yoizen.Common.Net8\Yoizen.Common.Net8.csproj" />
  </ItemGroup>
</Project>
