﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />
/// <reference path="Reports.js" />

$.ajaxSetup({ cache: false });

var $textboxDates;
var $textboxFromDate;
var $textboxToDate;
var $listboxSites;
var $textboxMinutesToConsider;

var $divConsolidatedResults;
var $divConsolidatedLoading;
var $messageConsolidatedNoRecords;
var $messageConsolidatedError;
var $messageConsolidatedTimeout;
var $divConsolidated;
var $tableConsolidated;

var $divConsolidatedChartsContainer;
var $divConsolidatedCharts;

var $buttonExport;
var $selectSectionsToExport;

var $buttonCloseSchedule;

var chartConsolidated = null;

$(function () {
	var $panelFilters = $('#panelFilters');
	if ($panelFilters.length > 0) {
		$textboxDates = $("#textboxFromDate, #textboxToDate");
		var dates = $textboxDates.datepicker({
			changeMonth: true,
			changeYear: true,
			numberOfMonths: 1,
			maxDate: moment().local(true).subtract(1, 'days').toDate(),
			onSelect: function (selectedDate) {
				var option = this.id == "textboxFromDate" ? "minDate" : "maxDate",
				instance = $(this).data("datepicker"),
				date = $.datepicker.parseDate(
					instance.settings.dateFormat ||
					$.datepicker._defaults.dateFormat,
					selectedDate, instance.settings);
				dates.not(this).datepicker("option", option, date);
			}
		});

		AutoFillFilterDates('#textboxFromDate', '#textboxToDate');

		var $tableFilters = $('#tableFilters');

		$textboxFromDate = $("#textboxFromDate");
		$textboxToDate = $("#textboxToDate");
		$listboxSites = $('#listboxSites');
		$listboxSites.multiselect({ multiple: true, noneSelectedText: "Todos los sitios", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
		$textboxMinutesToConsider = $("#textboxMinutesToConsider");
		var $divSideNav = $('#divSideNav');
		var $topBar = $('#topBar');
		var $pageFooter = $('#pageFooter');
		$buttonCloseSchedule = $('#buttonCloseSchedule');

		if (typeof (isScheduled) === 'undefined') {
			isScheduled = false;
		}

		if (typeof (isScheduled) === 'boolean' && isScheduled) {
			$divSideNav.hide();
			$topBar.hide();
			$pageFooter.hide();
			$buttonCloseSchedule.show();

			//para mostrar el msj de error de reporte solicitado
			if (typeof (scheduleDuplicateError) !== 'undefined') {
				$('#divScheduleDuplicateerror').show();
				$('#validationScheduleDuplicate').show();
				$('#divSeccion').addClass("withError");
			}
		}	
	}
	else {
		$divConsolidatedResults = $('#divConsolidatedResults');
		$divConsolidatedLoading = $('#divConsolidatedLoading');
		$messageConsolidatedNoRecords = $('#messageConsolidatedNoRecords');
		$messageConsolidatedError = $('#messageConsolidatedError');
		$messageConsolidatedTimeout = $('#messageConsolidatedTimeout');
		$divConsolidated = $('#divConsolidated');
		$tableConsolidated = $('#tableConsolidated');

		$divConsolidatedChartsContainer = $('#divConsolidatedChartsContainer');
		$divConsolidatedCharts = $('#divConsolidatedCharts');

		$buttonExport = $('#buttonExport');
		$selectSectionsToExport = $('#selectSectionsToExport');
		$selectSectionsToExport.multiselect({ multiple: true, noneSelectedText: "Seleccione las secciones a exportar", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

		LoadHighchartsOptions();
		Highcharts.setOptions({
			global: {
				useUTC: false
			}
		});
		
		$divConsolidatedResults.prop('isLoading', true);
		$divConsolidatedResults.prop('firstTime', true);
		$divConsolidatedResults.prop('lastRecord', null);

		LoadDetailed();
	}
});

function i18nLoaded() {
	if (typeof ($listboxSites) !== 'undefined' &&
		$listboxSites.length > 0) {
		$listboxSites.multiselect('option', 'noneSelectedText', $.i18n("reports-adherence-filters-sites-all_sites"));
	}

	if (typeof($selectSectionsToExport) !== 'undefined' &&
		$selectSectionsToExport.length > 0) {
		$selectSectionsToExport.multiselect('option', 'noneSelectedText', $.i18n("reports-globals-export-sections-select"));
	}
}

function ValidateFilters(sender, e) {
	e.IsValid = false;

	if (typeof (isScheduled) === 'boolean' && !isScheduled) {
		var fromDate = $textboxFromDate.val();
		var toDate = $textboxToDate.val();

		var dateFormat = $textboxFromDate.datepicker('option', 'dateFormat')

		if (fromDate.length == 0 || toDate.length == 0) {
			$(sender).text($.i18n("reports-globals-must_enter_dates"));
			return;
		}

		var from, to;
		try {
			from = $.datepicker.parseDate(dateFormat, fromDate);
			to = $.datepicker.parseDate(dateFormat, toDate);
		}
		catch (ex) {
			$(sender).text($.i18n("reports-globals-must_enter_dates"));
			return;
		}

		if (to < from) {
			$(sender).text($.i18n('reports-globals-to_date_cannot_be_lower_than_from'));
			return;
		}

		var minutes = $textboxMinutesToConsider.val();
		if (minutes === null ||
			minutes.length === 0 ||
			!$.isNumeric(minutes) ||
			!/^[0-9]{1,2}$/.test(minutes)) {
			$(sender).text($.i18n('reports-adherence-filters-minutes-invalid_value'));
			return;
		}
	}

	minutes = parseInt(minutes);
	if (minutes < 0 || minutes > 60) {
		$(sender).text($.i18n('reports-adherence-filters-minutes-invalid_value'));
		return;
	}

	e.IsValid = true;
}

function LoadDetailed() {
	var dataToSend = JSON.stringify({
		fromDate: fromDate,
		toDate: toDate,
		sites: sites,
		agents: agents,
		minutes: minutes
	});

	$.ajax({
		type: "POST",
		url: "Adherence.aspx/LoadMoreResultsDetailed",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (msg) {
			if (msg.d.Success) {
				if (!Array.isArray(msg.d.ConsolidatedRecords) || msg.d.ConsolidatedRecords.length === 0) {
					$messageConsolidatedNoRecords.show();
				}
				else {
					LoadConsolidatedRecords(msg.d.ConsolidatedRecords);
				}
			}
			else {
				console.log('No se pudieron traer más registros: %o', msg.d.Error);
			}

			$divConsolidatedLoading.hide();
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$divConsolidatedLoading.hide();

			if (textStatus == 'timeout') {
				$messageConsolidatedTimeout.show();
			}
			else {
				if (jqXHR.responseText.indexOf('Request timed out') >= 0) {
					$messageConsolidatedTimeout.show();
				}
				else {
					$messageConsolidatedError.show();
				}
			}

			console.error('TextStatus: %s\nError: %s', textStatus, jqXHR.responseText);
		}
	});
}

function LoadConsolidatedRecords(consolidatedRecords) {
	$messageConsolidatedNoRecords.hide();
	$divConsolidated.show();
	if (canExportReport !== undefined
		&& typeof (canExportReport) === 'boolean'
		&& !canExportReport) {
		$buttonExport.parent().hide();
	} else {
		$buttonExport.parent().show();
	}
	$divConsolidatedChartsContainer.show();

	var $tbody = $('tbody', $tableConsolidated);

	for (let i = 0; i < consolidatedRecords.length; i++) {
		let record = consolidatedRecords[i];
		record = {
			interval: record[0],
			info: record[1]
		};
		consolidatedRecords[i] = record;

		for (let j = 0; j < record.info.length; j++) {
			var $tr = $('<tr></tr>');
			$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');

			record.info[j] = {
				site: record.info[j][0],
				count: record.info[j][1]
			};

			if (j === 0) {
				let date = record.interval;

				$tr.append('<td class="nowrap right" rowspan="' + record.info.length + '">' + DisplayDateTime(date, 'L', false) + '</td>');
				$tr.append('<td class="nowrap" rowspan="' + record.info.length + '">' + DisplayDateTime(date, 'HHmm', false) + '</td>');
			}

			let site = allSites.find(function (s) {
				return s.id === record.info[j].site;
			});

			$tr.append('<td class="nowrap">' + he.encode(site.name) + '</td>');
			$tr.append('<td class="nowrap right">' + record.info[j].count + '</td>');

			$tbody.append($tr);
		}
		
	}

	chartConsolidated = LoadConsolidatedChart();
	LoadConsolidatedGraph(consolidatedRecords);

	LoadCompositedElements();
}

function CalculateHourStats(time, value) {
	if (time === 0 || value === 0) {
		return 0;
	}

	var computedValue = (3600) / (time / value);
	return computedValue.toFixed(2);
}

function LoadConsolidatedGraph(records) {
	if (records.length > 0) {
		var seriesData = [];
		for (var i = 0; i < sites.length; i++) {
			seriesData.push([]);
		}

		for (var i = 0; i < records.length; i++) {
			var record = records[i];
			
			var intervalDateTimeMoment = moment.utc(record.interval);
			//intervalDateTimeMoment.add((new Date()).getTimezoneOffset(), 'minutes');
			var date = intervalDateTimeMoment.valueOf();

			for (var j = 0; j < record.info.length; j++) {
				var serieIndex = sites.findIndex(function (s) {
					return s === record.info[j].site;
				});

				seriesData[serieIndex].push([date, record.info[j].count]);
			}
		}

		for (var i = 0; i < sites.length; i++) {
			chartConsolidated.series[i].setData(seriesData[i], false, false, true);
		}
		chartConsolidated.redraw();
	}
}

function GetCurrentDate(record) {
	var intervalDateTime = GetDetailedField(record, 'IntervalDateTime');
	var intervalDateTimeMoment;
	if (byIntervals) {
		intervalDateTimeMoment = moment(intervalDateTime);
	}
	else {
		intervalDateTimeMoment = moment.utc(intervalDateTime);
	}

	if (typeof (window.serverOffset) === 'undefined') {
		window.serverOffset = moment().tz(currentTimeZoneIana).utcOffset();
	}
	if (typeof (window.currentOffset) === 'undefined') {
		window.currentOffset = moment().local().utcOffset();
	}
	if (typeof (window.timezoneOffset) === 'undefined') {
		window.timezoneOffset = (new Date()).getTimezoneOffset();
	}
	if (typeof (window.configuredOffset) === 'undefined') {
		if (typeof (loggedUserSettings) !== 'undefined' &&
			loggedUserSettings !== null &&
			loggedUserSettings.hasOwnProperty('DefaultTimeZoneIana') &&
			loggedUserSettings.DefaultTimeZoneIana !== null) {
			var configuredZone = moment.tz.zone(loggedUserSettings.DefaultTimeZoneIana);
			window.configuredOffset = moment().tz(configuredZone.name).utcOffset();
		}
		else {
			window.configuredOffset = window.currentOffset;
		}
	}

	if (window.configuredOffset !== window.serverOffset) {
		intervalDateTimeMoment.add(window.configuredOffset - window.serverOffset, 'minutes');
	}

	var date = intervalDateTimeMoment.valueOf();

	return date;
}

function AddOrUpdateSeries(serie, date, value) {
	serie.addPoint([date, value], false, false);
}

function LoadConsolidatedChart() {
	LoadHighchartsOptions();

	var lang = Highcharts.getOptions().lang;
	lang['Acumular'] = 'Acumular';
	lang['NoAcumular'] = 'No acumular';
	
	var series = [];
	for (var i = 0; i < sites.length; i++) {
		var site = allSites.find(function (s) {
			return s.id === sites[i];
		});

		var newSerie = {
			name: site.name,
			yAxis: 0,
			data: null
		};
		series.push(newSerie);
	}

	var tickInterval, minorTickInterval, minRange;
	var MILLISECONDS_IN_HOUR = 3600 * 1000;
	var MILLISECONDS_IN_DAY = 24 * MILLISECONDS_IN_HOUR;
	var singleDay = fromDate == toDate;
	var totalDays = dateDiffInDays(new Date(fromDate), new Date(toDate));

	tickInterval = null;
	if (singleDay) {
		minorTickInterval = MILLISECONDS_IN_HOUR;
		minRange = MILLISECONDS_IN_DAY;
	}
	else {
		minorTickInterval = null;
		minRange = 7 * MILLISECONDS_IN_HOUR;
	}

	var title = $.i18n('reports-adherence-consolidated-graph-chart-title');
	var subtitle = null;
	var pointStart = new Date(fromDate);

	var chart = new Highcharts.Chart({
		chart: {
			renderTo: 'divConsolidatedCharts',
			zoomType: singleDay ? '' : 'x',
			spacingRight: 20,
			defaultSeriesType: 'spline',
			width: $('#divConsolidatedCharts').width()
		},
		title: {
			text: title,
			visible: title != null
		},
		subtitle: {
			text: subtitle,
			visible: subtitle != null
		},
		xAxis: {
			title: {
				text: (singleDay) ? $.i18n('reports-adherence-consolidated-graph-chart-intervals') : $.i18n('reports-adherence-consolidated-graph-chart-intervals_and_dates')
			},
			type: 'datetime',
			tickPosition: 'outside',
			tickmarkPlacement: 'on',
			tickInterval: tickInterval,
			minorTickInterval: minorTickInterval,
			minRange: minRange,
			dateTimeLabelFormats: { // don't display the dummy year
				day: '%e de %b',
				month: '%e. %b',
				year: '%b',
				hour: (singleDay ? '%H:%M' : $.i18n('reports-adherence-consolidated-graph-chart-interval_date'))
			},
			labels: {
				rotation: 300,
				align: 'right'
			}
		},
		credits: {
			enabled: false
		},
		yAxis: {
			allowDecimals: false,
			title: {
				text: $.i18n('reports-adherence-consolidated-graph-chart-count')
			},
			min: 0,
			gridLineDashStyle: 'Dot'
		},
		legend: {
			layout: 'horizontal',
			align: 'center',
			verticalAlign: 'bottom',
			shadow: true
		},
		plotOptions: {
			column: {
				pointPadding: 0.2,
				borderWidth: 0,
				dataLabels: {
					enabled: true
				},
				pointStart: pointStart
			}
		},
		tooltip: {
			formatter: function () {
				return $.i18n('reports-adherence-consolidated-graph-chart-point_tooltip', this.series.name,
					DisplayDateTime(this.x, 'L'),
					DisplayDateTime(this.x, 'LT'),
					this.y);
			}
		},
		exporting: {
			enabled: false
		},
		series: series
	});

	return chart;
}

function GetDetailedField(record, field, format, defaultValue) {
	for (var i = 0; i < detailedPropertyNames.length; i++) {
		if (detailedPropertyNames[i] == field) {
			if (record[i] == null)
				return '';

			if (typeof (format) != 'undefined') {
				var n = numeral(record[i]);
				return n.format(format);
			}

			return record[i];
		}
	}

	if (typeof (defaultValue) == 'undefined')
		defaultValue = '';

	return defaultValue;
}

function GetDetailedFieldAsTime(record, field) {
	for (var i = 0; i < detailedPropertyNames.length; i++) {
		if (detailedPropertyNames[i] == field) {
			return ConvertSecondsAsTimeString(record[i], { includeMilliseconds: true });
		}
	}

	return '-';
}

function ShowExportDialog(sections, removeSections) {
	var $divExportStep1 = $('#divExportStep1');
	var $divExportStep2 = $('#divExportStep2');

	$divExportStep1.show();
	$divExportStep2.hide();

	var $options = $('option', $selectSectionsToExport);
	for (var i = $options.length - 1; i >= 0; i--) {
		var $option = $($options[i]);
		$option.attr('selected', 'selected');
	}

	$selectSectionsToExport.multiselect('refresh');

	$inputExportEmail = $('#inputExportEmail');
	if (typeof (loggedUserEmail) != 'undefined' && loggedUserEmail != null)
		$inputExportEmail.val(loggedUserEmail);
	else
		$inputExportEmail.val('');

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divExport",
		width: '800px',
		initialWidth: '800px',
		preloading: false,
		closeButton: false
	});
}

function ExportToMail() {
	var $inputExportEmail = $('#inputExportEmail');
	var $divExportStep1InvalidEmail = $('#divExportStep1InvalidEmail');
	var email = $inputExportEmail.val();

	if (email.length == 0) {
		$divExportStep1InvalidEmail.show();
		$.colorbox.resize();
		return;
	}

	var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
	if (!email.match(pattern)) {
		$divExportStep1InvalidEmail.show();
		$.colorbox.resize();
		return;
	}

	$divExportStep1InvalidEmail.hide();

	var $selectSectionsToExport = $('#selectSectionsToExport');
	var $divExportStep1InvalidSections = $('#divExportStep1InvalidSections');
	var sections = $selectSectionsToExport.val();

	if (sections == null || sections.length == 0) {
		$divExportStep1InvalidSections.show();
		$.colorbox.resize();
		return;
	}

	$divExportStep1InvalidSections.hide();

	$selectExportFormat = $('#selectExportFormat');
	var exportFormat = $selectExportFormat.val();

	var dataToSend = JSON.stringify({
		fromDate: fromDate,
		toDate: toDate,
		sites: sites,
		agents: agents,
		minutes: minutes,
		sections: sections,
		email: email,
		exportFormat: exportFormat
	});

	$.ajax({
		type: "POST",
		url: "Adherence.aspx/Export",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (msg) {
			if (msg.d.Success) {
				var $divExportStep1 = $('#divExportStep1');
				var $divExportStep2 = $('#divExportStep2');

				$divExportStep1.hide();
				$divExportStep2.show();
				$.colorbox.resize();
			}
			else {
				if (typeof (msg.d.AlreadyExists) !== 'undefined' && msg.d.AlreadyExists) {
					AlertDialog($.i18n('reports-globals-export-alert-title'), $.i18n('reports-globals-export-alert-already_solicited'), $.colorbox.close, $.colorbox.close, 'Warning');
				}
				else {
					AlertDialog($.i18n('reports-globals-export-alert-title'), $.i18n('reports-globals-export-alert-error', msg.d.Error.Message));
				}
			}
		}
	});
}