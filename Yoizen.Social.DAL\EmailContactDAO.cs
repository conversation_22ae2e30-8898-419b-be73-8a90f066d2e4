﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.DAL
{
	public class EmailContactDAO
	{
		#region Fields

		public EmailContact EmailContact { get; set; }

		#endregion

		#region Constructors

		public EmailContactDAO(EmailContact emailContact)
		{
			this.EmailContact = emailContact;
		}

		#endregion

		#region Static Methods

		public static List<EmailContact> GetAllByAgentID(int agentId, string text, int? lastEmailContactID, out bool? moreEmailContactsAvailable)
		{
			moreEmailContactsAvailable = false;
			List<EmailContact> emailContacts = new List<EmailContact>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "EmailContacts_GetAllByAgentID";

					cmd.AddParameter("AgentID", DbType.Int32, agentId);
					cmd.AddParameter("LastEmailContactID", DbType.Int32, lastEmailContactID);
					cmd.AddParameter("text", DbType.String, text);

					using (DbDataReader reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							if (emailContacts.Count >= 20)
							{
								moreEmailContactsAvailable = true;
								break;
							}
							var emailContact = new EmailContact(reader);
							emailContacts.Add(emailContact);
						}
					}
				}
			}
			return emailContacts;
		}

		public static bool ExistsByEmailAndAgentID(string email, int agentId)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();
				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "EmailContacts_ExistsByEmailAndAgentID";

					cmd.AddParameter("AgentID", DbType.Int32, agentId);
					cmd.AddParameter("Email", DbType.String, email);

					var existsParam = cmd.CreateParameter();
					existsParam.ParameterName = "Exists";
					existsParam.DbType = DbType.Boolean;
					existsParam.Direction = ParameterDirection.Output;
					cmd.Parameters.Add(existsParam);

					cmd.ExecuteNonQuery();

					return existsParam.Value != DBNull.Value && Convert.ToBoolean(existsParam.Value);
				}
			}
		}

		public static bool DeleteEmailContact(int id, out string errorMessage)
		{
			errorMessage = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();
				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "EmailContacts_DeleteByID";

					cmd.AddParameter("ID", DbType.Int32, id);

					var returnParam = cmd.CreateParameter();
					returnParam.Direction = ParameterDirection.ReturnValue;
					cmd.Parameters.Add(returnParam);

					cmd.ExecuteNonQuery();

					var result = (int)returnParam.Value;
					if (result == 1)
					{
						errorMessage = "El contacto no existe.";
						return false;
					}

					return true;
				}
			}
		}
		#endregion

		#region Public Methods

		public bool Insert(out int? newId, out string errorMessage)
		{
			newId = null;
			errorMessage = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();
				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "EmailContacts_Insert";

					cmd.AddParameter("Name", DbType.String, this.EmailContact.Name);
					cmd.AddParameter("LastName", DbType.String, this.EmailContact.LastName);
					cmd.AddParameter("Email", DbType.String, this.EmailContact.Email);
					cmd.AddParameter("AgentID", DbType.Int32, this.EmailContact.AgentID);

					var outputIdParam = cmd.AddParameter("NewID", DbType.Int32, DBNull.Value);
					outputIdParam.Direction = ParameterDirection.Output;

					var returnParam = cmd.CreateParameter();
					returnParam.Direction = ParameterDirection.ReturnValue;
					cmd.Parameters.Add(returnParam);

					cmd.ExecuteNonQuery();

					var resultCode = (int)returnParam.Value;

					switch (resultCode)
					{
						case 0:
							this.EmailContact.ID = (int)outputIdParam.Value;
							newId = (int)outputIdParam.Value;
							return true;

						case 1:
							errorMessage = "Ya existe un contacto con ese Email y AgentID.";
							return false;

						default:
							errorMessage = "Error inesperado al insertar el contacto.";
							return false;
					}
				}
			}
		}


		public int Update()
		{
			int resultCode;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();
				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "EmailContacts_Update";

					cmd.AddParameter("ID", DbType.Int32, this.EmailContact.ID);
					cmd.AddParameter("Name", DbType.String, this.EmailContact.Name);
					cmd.AddParameter("LastName", DbType.String, this.EmailContact.LastName);
					cmd.AddParameter("Email", DbType.String, this.EmailContact.Email);
					cmd.AddParameter("AgentID", DbType.Int32, this.EmailContact.AgentID);

					var output = cmd.CreateParameter();
					output.ParameterName = "ResultCode";
					output.DbType = DbType.Int32;
					output.Direction = ParameterDirection.Output;
					cmd.Parameters.Add(output);

					cmd.ExecuteNonQuery();
					resultCode = (int)output.Value;
				}
			}
			return resultCode;
		}

		#endregion
	}
}
