﻿<%@ Page Title="" Async="true" Language="C#" MasterPageFile="~/Master.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="ServicesLinkedin.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.ServicesLinkedin" %>

<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.colorpicker.css")%>' rel="stylesheet" type="text/css" />

	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.colorpicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.numeric.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/URI.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.getUrlParam.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.filedrop.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesCommon.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesLinkedin.js")%>'></script>

	<style type="text/css">
		.uiInfoTable .label { width: 150px !important; }
	</style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<asp:Image runat="server" ImageUrl="~/Images/Linkedin.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-serviceslinkedin-title">Linkedin</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<div style="display: none">
        <div id="divServices" class="seccion">
            <div class="title">
                <h2 data-i18n="configuration-serviceslinkedin-copy_attributes">Copiar atributos</h2>
            </div>
            <div class="contents">
                <yoizen:Message runat="server" Type="Information" Text="Seleccione el servicio de LinkedIn del cual se desea copiar los atributos" Small="true" LocalizationKey="configuration-serviceslinkedin-select_service" />
                <div id="divAllServices" style="max-height: 300px; overflow-y: auto; overflow-x: auto; max-width: 100%">
                    <table id="tableAllServices" class="reporte" cellspacing="0" rules="all" border="1" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr class="header">
                                <th style="width: 20px;" scope="col">&nbsp;</th>
                                <th scope="col"><span data-i18n="configuration-services-service">Servicio</span></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="buttons">
                    <label class="uiButton uiButtonLarge uiButtonConfirm">
                        <button type="button" data-i18n="globals-accept" id="buttonCopyAnswerDialogConfirm" onclick="CopyDialogConfirmCommon()">Aceptar</button>
                    </label>
                    <label class="uiButton uiButtonLarge">
                        <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                    </label>
                </div>
            </div>
        </div>
		<div id="divLinkedinWizardLoading">
			<div class="seccion">
				<div class="title">
					<h2><asp:Image runat="server" ImageUrl="~/Images/Icons/Linkedin.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-serviceslinkedin-subtitle">Asistente de configuración de Linkedin</span></h2>
				</div>
				<div class="contents">
					<table width="100%" border="0">
						<tr>
							<td align="center"><span data-i18n="globals-loading">Cargando...</span></td>
						</tr>
						<tr>
							<td align="center">
								<i class="fa fa-3x fa-spinner fa-pulse"></i>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div id="divLinkedinWizard">
			<div class="seccion">
				<div class="title">
					<h2><asp:Image runat="server" ImageUrl="~/Images/Icons/Linkedin.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-serviceslinkedin-subtitle">Asistente de configuración de Linkedin</span></h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="wizard">
						<tr>
							<th class="step">1.</th>
							<td class="tip withImage"><span data-i18n="[html]configuration-serviceslinkedin-auth_acces">
								Ingrese a la siguiente dirección y confirme los permisos que son solicitados para que <span class="productname"></span> tenga acceso a su cuenta.
								Si llegara a ser necesario regístrese en Linkedin.
								La dirección es: </span><a id="hyperlinkLinkedinTokenURL" rel="_blank"></a>
							</td>
							<td class="image">
								<img src="../Images/LinkedinWizard1.png" alt="Linkedin Wizard 1" />
							</td>
						</tr>
						<tr>
							<td colspan="3">
								<div id="divLinkedinAccountData" style="display: none">
									<div class="subseccion">
										<div class="title">
											<h2 data-i18n="configuration-serviceslinkedin-user_data-title">Datos del usuario</h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-serviceslinkedin-access_token">Access Token</span>:</th>
													<td class="data noinput">
														<div id="divLinkedinAccessToken" style="word-break: break-all; max-width: 100%; width: 100%"></div>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-serviceslinkedin-user">Usuario</span>:</th>
													<td class="data noinput">
														<span id="spanLinkedinUser"></span>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-serviceslinkedin-auth_date">Fecha de autorización</span>:</th>
													<td class="data noinput">
														<span id="spanLinkedinAuthorizationDate"></span>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-serviceslinkedin-expiration">Expira en (segundos)</span>:</th>
													<td class="data noinput">
														<span id="spanLinkedinExpiresIn"></span>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-serviceslinkedin-managed_pages">Páginas administradas</span>:</th>
													<td class="data noinput">
														<select id="selectLinkedinPages"></select>
													</td>
												</tr>
											</table>
											<yoizen:Message ID="MessageErrorFanPage" runat="server" ClientIDMode="Static" Type="Error" LocalizationKey="configuration-serviceslinkedin-error_fan_page">Debe ingresar una cuenta que tenga al menos una página administrada</yoizen:Message>
										</div>
									</div>
								</div>
							</td>
						</tr>
					</table>
					<yoizen:Message ID="divLinkedinWizardError" runat="server" ClientIDMode="Static" Type="Error" LocalizationKey="configuration-serviceslinkedin-auth_token_error">Ocurrió un error obteniendo el Access Token de Linkedin</yoizen:Message>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-accept" id="btnConfirmar" onclick="LinkedinValidate()">Aceptar</button>
						</label>
						<label class="uiButton uiButtonLarge">
							<button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="seccion collapsable">
		<div class="title">
			<h2 data-i18n="configuration-serviceslinkedin-basic_configuration-title">Configuración básica</h2>
		</div>
		<div class="contents">
			<yoizen:Message runat="server" Type="Information" Small="true">
				<span data-i18n="configuration-serviceslinkedin-use_config_assistant">Usted puede optar por utilizar el asistente de configuración. Para ello haga clic</span> <a href="javascript:openLinkedinWizard()" data-i18n="globals-here">aquí</a>.
			</yoizen:Message>
			<asp:HiddenField ID="hiddenLinkedInPageData" runat="server" ClientIDMode="Static"></asp:HiddenField>
			<asp:HiddenField ID="hiddenLinkedInExpiresIn" runat="server" ClientIDMode="Static"></asp:HiddenField>
			<asp:HiddenField ID="hiddenLinkedInUserId" runat="server" ClientIDMode="Static"></asp:HiddenField>
			<asp:HiddenField ID="hiddenLinkedInAuthorizationDate" runat="server" ClientIDMode="Static"></asp:HiddenField>
			<table width="100%" border="0" class="uiInfoTable noBorder">
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="configuration-serviceslinkedin-service_name">Nombre de servicio</span>:</th>
					<td class="data">
						<asp:TextBox ID="textboxServiceName" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" MaxLength="50" autotrim="true" />
						<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxServiceName" />
						<asp:CustomValidator ID="customvalidatorServiceName" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceName" OnServerValidate="customvalidatorServiceName_ServerValidate" />
					</td>
				</tr>
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="configuration-serviceslinkedin-access_token">Access Token</span>:</th>
					<td class="data">
						<asp:TextBox ID="textboxLinkedInAccessToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" />
						<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInAccessToken" />
					</td>
				</tr>
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="configuration-serviceslinkedin-page_code">Código de Página</span>:</th>
					<td class="data">
						<asp:TextBox ID="textboxLinkedInPageId" runat="server" Width="200" ClientIDMode="Static" autocomplete="off" />
						<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInPageId" />
					</td>
				</tr>
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="configuration-serviceslinkedin-page_name">Nombre de Página</span>:</th>
					<td class="data">
						<asp:TextBox ID="textboxLinkedInPageName" runat="server" Width="200" autocomplete="off" />
						<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInPageName" />
					</td>
				</tr>
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
					<td class="data">
						<table class="uiGrid" cellspacing="0" cellpadding="0">
							<tbody>
								<tr>
									<td class="vMid prs">
										<asp:TextBox ID="textboxLinkedInFromDate" runat="server" Width="120" ClientIDMode="Static" />
										<asp:CustomValidator runat="server" ControlToValidate="textboxLinkedInFromDate" EnableClientScript="true" ClientValidationFunction="ValidateDateField" />
									</td>
									<td class="vMid pls" data-i18n="configuration-serviceslinkedin-from_date-tip">
										Especifica la fecha a partir de la cual se obtendrán las publicaciones de Linkedin
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</table>
			<yoizen:Message ID="messageLinkedinTokenExpiry" runat="server" Type="Warning" style="display: none; margin-top: 5px" LocalizationKey="[html]configuration-serviceslinkedin-access_token_expiration">
				El access token expira el <span></span>
			</yoizen:Message>
		</div>
	</div>
	<div class="seccion collapsable">
		<div class="title">
			<h2 data-i18n="configuration-serviceslinkedin-email_notifications-title">Notificaciones por email</h2>
		</div>
		<div class="contents">
			<div class="subseccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-serviceslinkedin-access_token_expiration-title">Expiración del Access Token</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceslinkedin-access_token_expiration-notice">
						Este mail se mandará una vez por día para avisar que el Access Token de LinkedIn está a punto de expirar o expiró
					</yoizen:Message>
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
							<td class="data">
								<asp:HiddenField ID="hiddenLinkedInAccessTokenConnection" runat="server" ClientIDMode="Static" />
								<select id="listboxLinkedInAccessTokenConnection">
									<option value="" selected="selected" data-i18n="globals-email_default">Por defecto</option>
								</select>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-serviceslinkedin-subject">Asunto del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxLinkedInAccessTokenExpiresEmailSubject" runat="server" MaxLength="200" Width="90%" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInAccessTokenExpiresEmailSubject" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-serviceslinkedin-recipients">Emails destinatarios</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxLinkedInAccessTokenExpiresEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInAccessTokenExpiresEmails" />
								<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxLinkedInAccessTokenExpiresEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-serviceslinkedin-template">Plantilla del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxLinkedInAccessTokenExpiresEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLinkedInAccessTokenExpiresEmailTemplate" />
								<yoizen:Message ID="messageLinkedInAccessTokenExpiresEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-serviceslinkedin-template_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceslinkedin-edit-template-field-date_and_hour">Indica la fecha y hora que expira el Access Token</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceslinkedin-edit-template-field-service_name">Indica el nombre del servicio que expirará el Access Token</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
	<div class="seccion collapsable">
		<div class="title">
			<h2 data-i18n="configuration-serviceslinkedin-advanced_settings-title">Configuración avanzada</h2>
		</div>
		<div class="contents">
			<table width="95%" border="0" class="uiInfoTable noBorder">
				<tr class="dataRow dataRowSeparator">
					<th class="label"><span data-i18n="configuration-serviceslinkedin-default_queue">Cola por defecto</span>:</th>
					<td class="data">
						<asp:DropDownList ID="dropdownlistLinkedinQueue" runat="server" DataTextField="Name" DataValueField="ID" />
					</td>
				</tr>
			</table>
		</div>
	</div>
	<div class="buttons">
        <asp:HiddenField ID="hiddenServiceToCopy" runat="server" ClientIDMode="Static" />
        <label class="uiButton uiButtonLarge">
            <button type="button" data-i18n="configuration-services-copy_from_service" onclick="ShowCopyServiceCommon('Linkedin')">Copiar desde otro servicio</button>
        </label>
        <asp:Button ID="buttonCopyService" runat="server" Text="Aceptar" OnClick="buttonCopyService_Click" CausesValidation="false" Style="display: none" ClientIDMode="Static" />
		<label class="uiButton uiButtonLarge uiButtonConfirm">
			<asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" data-i18n="globals-accept" />
		</label>
		<label class="uiButton uiButtonLarge">
			<asp:Button ID="buttonCancel" runat="server" Text="Cancelar" OnClick="buttonCancel_Click" CausesValidation="false" data-i18n="globals-cancel" />
		</label>
	</div>
</asp:Content>
