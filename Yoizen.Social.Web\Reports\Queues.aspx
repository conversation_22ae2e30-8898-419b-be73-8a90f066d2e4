﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master.Master" AutoEventWireup="true" CodeBehind="Queues.aspx.cs" Inherits="Yoizen.Social.Web.Reports.Queues" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/underscore-min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.floatThead.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.theme.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.data.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts-3d.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.drilldown.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.funnel.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.exporting.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.no-data-to-display.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.sunburst.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/numeral.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/Reports.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/MessagesQuery.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/CaseInfo.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/MessageInfo.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/CallInfo.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/Queues.js")%>'></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<span data-i18n="reports-queues-title">Consolidado de Colas</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<yoizen:Message ID="messageError" runat="server" Type="Error" Visible="false" />
	<yoizen:Message ID="messageNoQueues" runat="server" Type="Warning" Visible="false" LocalizationKey="reports-user_no_queues">
		El usuario no tiene ninguna cola asignada.
	</yoizen:Message>
	<asp:Panel ID="panelContent" runat="server">
		<asp:Panel ID="panelFilters" runat="server" ClientIDMode="Static">
			<div id="divSeccion" class="seccion">
				<div class="title">
					<h2 data-i18n="reports-queues-filter-title">Filtro</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" LocalizationKey="reports-globals-intervals_day_info">
						La información consolidada está disponible al cierre del intervalo o del día
					</yoizen:Message>
					<yoizen:Message ID="messageTimeZoneWarning" runat="server" ClientIDMode="Static" Type="Warning" style="display: none">
						<span data-i18n="reports-globals-other_utc">Usted utiliza otro huso horario al del servidor</span>.
						<div rel="server"><span data-i18n="reports-globals-server_timezone">El servidor está configurado con</span> <span rel="tz"></span></div>
						<div rel="local"><span data-i18n="reports-globals-user_timezone">Usted se encuentra en</span> <span rel="tz"></span></div>
						<div rel="configured-local" style="display: none"><span data-i18n="reports-globals-user_configured_timezone">Usted configuró utilizar</span> <span rel="tz"></span></div>
					</yoizen:Message>
					<table id="tableFilters" width="100%" border="0" class="uiInfoTable">
						<tbody>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="reports-queues-filter-type">Tipo de consolidación</span>:</th>
								<td class="data">
									<asp:DropDownList ID="dropdownlistByIntervals" runat="server" ClientIDMode="Static">
										<asp:ListItem Value="1" Selected="False" data-i18n="reports-queues-filter-type-by_intervals">Por intervalos</asp:ListItem>
										<asp:ListItem Value="0" Selected="True" data-i18n="reports-queues-filter-type-by_day">Por día</asp:ListItem>
									</asp:DropDownList>
								</td>
							</tr>
							<tr id="trTimeZoneToShow" class="dataRow dataRowSeparator" style="display: none">
								<th class="label" style="width: 200px !important"><span data-i18n="reports-queues-filter-timezone">Huso horario a consultar</span>:</th>
								<td class="data">
									<asp:HiddenField ID="hiddenDailyTimeZoneToShow" runat="server" ClientIDMode="Static" />
									<select id="selectDailyTimeZoneToShow">
										<option value="" data-i18n="reports-queues-filter-timezone-local_time">Horario local del servidor</option>
									</select>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id ="trFromDateFilter" runat="server">
								<th class="label" style="width: 200px !important"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxFromDate" runat="server" Width="100" ClientIDMode="Static" SkinID="NoFocus" autocomplete="nope" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id ="trToDateFilter" runat="server">
								<th class="label"><span data-i18n="globals-to_date">Fecha hasta</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxToDate" runat="server" Width="100" ClientIDMode="Static" autocomplete="nope" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id="trByIntervalsIntervals">
								<th class="label"><span data-i18n="reports-globals-intervals_to_query">Intervalos a incluir</span>:</th>
								<td class="data">
									<asp:DropDownList ID="dropdownlistFromInterval" runat="server" ClientIDMode="Static" />
									<span data-i18n="reports-globals-intervals_to_query-up_to">hasta el</span>
									<asp:DropDownList ID="dropdownlistToInterval" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label noinput"><span data-i18n="reports-queues-filters-search_by">Buscar por</span>:</th>
								<td class="data">
									<asp:DropDownList ID="dropdownlistSearchBy" runat="server" ClientIDMode="Static">
										<asp:ListItem Value="1" Selected="True" Text="Cola" data-i18n="reports-queues-filters-queues" />
										<asp:ListItem Value="2" Text="Grupo de colas" data-i18n="reports-queues-filters-queuegroup" />
									</asp:DropDownList>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" rel="searchByQueues">
								<th class="label"><span data-i18n="reports-queues-filters-queues">Colas</span>:</th>
								<td class="data">
									<asp:ListBox ID="listboxQueues" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderQueueGroups" runat="server">
								<tr class="dataRow dataRowSeparator" rel="searchByQueuesGroups">
									<th class="label noinput"><span data-i18n="reports-queues-filters-queuegroup">Grupo de colas</span>:</th>
									<td class="data">
										<asp:ListBox ID="listboxQueueGroup" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
									</td>
								</tr>
							</asp:PlaceHolder>
							<tr class="dataRow dataRowSeparator">
								<th class="label" data-i18n="reports-queues-filters-sections_to_query">Secciones a consultar:</th>
								<td class="data">
									<asp:ListBox ID="listboxSections" runat="server" ClientIDMode="Static" SelectionMode="Multiple" Width="450px">
										<asp:ListItem Text="Detallado por cola" Value="2" Selected="True" data-i18n="reports-queues-filters-sections_to_query-detailed" />
										<asp:ListItem Text="Actividad de etiquetas por cola" Value="3" Selected="True" data-i18n="reports-queues-filters-sections_to_query-tags" />
									</asp:ListBox>
								</td>
							</tr>
						</tbody>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Debe ingresar fechas" EnableClientScript="true" ClientValidationFunction="ValidateFilters" /></div>
					<div id="divScheduleDuplicateerror" style="display: none" class="validationerror">
						<asp:CustomValidator runat="server" ClientIDMode="Static" Display="Dynamic" ID="validationScheduleDuplicate" SkinID="validationerror" ErrorMessage="Reporte ya solicitado" data-i18n="reports-messages-duplicate-error" />
					</div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<asp:Button ID="buttonOKFilter" runat="server" Text="Buscar" OnClick="buttonOKFilter_Click" data-i18n="globals-search" />
						</label>
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<asp:Button ID="buttonScheduleReport" runat="server" Text="Buscar" OnClick="buttonScheduleReport_Click" data-i18n="globals-accept" ClientIDMode="Static"/>
						</label>
						<label class="uiButton uiButtonLarge">
							<button id="buttonCloseSchedule" type="button" data-i18n="globals-close" onclick="parent.jQuery.colorbox.close()" style="display: none">>Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</asp:Panel>
		<asp:Panel ID="panelResults" runat="server" Visible="false">
			<asp:Panel ID="panelDetailed" runat="server" CssClass="seccion collapsable" ClientIDMode="Static">
				<div class="title"><h2 data-i18n="reports-queues-results-detail-title">Detallado por cola</h2></div>
				<div class="contents">
					<div id="divDetailedLoading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageDetailedNoRecords" runat="server" Type="Information" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-no_records">
						No se encontraron registros para los filtros especificados
					</yoizen:Message>
					<yoizen:Message ID="messageDetailedTotalResults" runat="server" Type="Information" style="display: none" ClientIDMode="Static">
						Mostrando <span class="visiblerecords">0</span> de <span class="totalrecords">0</span> (<span class="totalrecordspercent">0%</span>)
					</yoizen:Message>
					<yoizen:Message ID="messageDetailedError" runat="server" Type="Error" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-an_error_has_ocurred">
						Ocurrió un error trayendo los resultados
					</yoizen:Message>
					<yoizen:Message ID="messageDetailedTimeout" runat="server" Type="Error" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-timeout">
						Ocurrió un error trayendo los resultados debido a que pasó el tiempo máximo de espera. Si lo desea puede exportar el reporte
					</yoizen:Message>
					<div id="divDetailed" style="width: 100%; max-height: 400px; overflow: auto; display: none">
						<table id="tableDetailed" class="reporte" cellspacing="0" rules="all" border="1">
							<thead class="theadB">
								<tr class="headerB">
									<th class="nowrap fixedColumn" rowspan="2" data-i18n="reports-queues-results-header-date">Fecha</th>
									<th id="thInterval" class="nowrap fixedColumn" rowspan="2" data-i18n="reports-queues-results-header-interval">Intervalo</th>
									<th class="nowrap fixedColumn" rowspan="2" data-i18n="reports-queues-results-header-queue">Cola</th>
									<th class="nowrap" colspan="19" data-i18n="reports-queues-results-header-messages">Mensajes</th>
									<th class="nowrap" colspan="8" data-i18n="reports-queues-results-header-sl">Nivel de Servicio</th>
									<th class="nowrap" colspan="8" data-i18n="reports-queues-results-header-sll">Nivel de Servicio Laboral</th>
									<th class="nowrap dependsOnChat" colspan="4" data-i18n="reports-queues-results-header-chats">Chats</th>
									<th id="thCases" class="nowrap" colspan="10" data-i18n="reports-queues-results-header-cases">Casos</th>
									<th id="thAgentTotals" class="nowrap" colspan="8" data-i18n="reports-queues-results-header-agents">Agentes</th>
									<th id="thTimes" class="nowrap" colspan="3" data-i18n="reports-queues-results-header-times">Tiempos</th>
								</tr>
								<tr id="trHeaderColumns" class="headerB">
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-new-tip" data-i18n="reports-queues-results-header-messages-new">Nuevos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-enqueued-tip" data-i18n="reports-queues-results-header-messages-enqueued">Encolados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-peek_enqueued-tip" data-i18n="[html]reports-queues-results-header-messages-peek_enqueued">Max Mensajes<br />Encolados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-hour_peek_enqueued-tip" data-i18n="[html]reports-queues-results-header-messages-hour_peek_enqueued">Hora Max<br />Mensajes Encolados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-discarded-tip" data-i18n="reports-queues-results-header-messages-discarded">Descartados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-discarded-sup-tip" data-i18n="[html]reports-queues-results-header-messages-discarded-sup">Descartados<br />por supervisores</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-discarded-system-tip" data-i18n="[html]reports-queues-results-header-messages-discarded-system">Descartados x<br />Sistema</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-replied-tip" data-i18n="[html]reports-queues-results-header-messages-replied">Mensajes<br />Respondidos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-asa-tip" data-i18n="reports-queues-results-header-messages-asa">ASA</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-auto_replies-tip" data-i18n="[html]reports-queues-results-header-messages-auto_replies">Respuestas<br />automáticas</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-verified_replies-tip" data-i18n="[html]reports-queues-results-header-messages-verified_replies">Respuestas<br />verificadas</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-at_interval_close-tip" data-i18n="[html]reports-queues-results-header-messages-at_interval_close">Mensajes<br />cierre intervalo</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-grouped-tip" data-i18n="[html]reports-queues-results-header-messages-grouped">Mensajes<br />agrupados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-replied-by_agents-tip" data-i18n="[html]reports-queues-results-header-messages-replied-by_agents">Respondidos<br />por agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-attended-by_agents-tip" data-i18n="[html]reports-queues-results-header-messages-attended-by_agents">Atendidos<br />por agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-replied-by_sup-tip" data-i18n="[html]reports-queues-results-header-messages-replied-by_sup">Respondidos<br />por supervisor</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-grouped-by_agents-tip" data-i18n="[html]reports-queues-results-header-messages-grouped-by_agents">Mensajes<br />agrupados x agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-returned-tip" data-i18n="reports-queues-results-header-messages-returned">Retornos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-transfered-in-tip" data-i18n="reports-queues-results-header-messages-transfered-in">Transferencias IN</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-messages-transfered-out-tip" data-i18n="reports-queues-results-header-messages-transfered-out">Transferencias OUT</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-flow-in-tip" data-i18n="reports-queues-results-header-sl-flow-in">Flow IN</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-flow-out-tip" data-i18n="reports-queues-results-header-sl-flow-out">Flow OUT</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-out-tip" data-i18n="reports-queues-results-header-sl-out">Fuera SL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-out_in_queue-tip" data-i18n="[html]reports-queues-results-header-sl-out_in_queue">Fuera SL<br />en Cola</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-out_in_agent-tip" data-i18n="[html]reports-queues-results-header-sl-out_in_agent">Fuera SL<br />en Agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-expired-tip" data-i18n="reports-queues-results-header-sl-expired">Vencidos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-replied_out_sl-tip" data-i18n="[html]reports-queues-results-header-sl-replied_out_sl">Respondidos<br />fuera SL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sl-attended_out_sl-tip" data-i18n="[html]reports-queues-results-header-sl-attended_out_sl">Atendidos<br />fuera SL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-flow-in-tip" data-i18n="reports-queues-results-header-sll-flow-in">Flow IN Laboral</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-flow-out-tip" data-i18n="reports-queues-results-header-sll-flow-out">Flow OUT Laboral</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-out-tip" data-i18n="reports-queues-results-header-sll-out">Fuera SL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-out_in_queue-tip" data-i18n="[html]reports-queues-results-header-sll-out_in_queue">Fuera SLL<br />en Cola</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-out_in_agent-tip" data-i18n="[html]reports-queues-results-header-sll-out_in_agent">Fuera SLL<br />en Agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-expired-tip" data-i18n="reports-queues-results-header-sll-expired">Vencidos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-replied_out_sl-tip" data-i18n="[html]reports-queues-results-header-sll-replied_out_sl">Respondidos<br />fuera SLL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-sll-attended_out_sl-tip" data-i18n="[html]reports-queues-results-header-sll-attended_out_sl">Atendidos<br />fuera SLL</span></th>
									<th class="nowrap dependsOnChat"><span data-i18n-title="reports-queues-results-header-chats-new-tip" data-i18n="reports-queues-results-header-chats-new">Chats nuevos</span></th>
									<th class="nowrap dependsOnChat"><span data-i18n-title="reports-queues-results-header-chats-abandoned-tip" data-i18n="reports-queues-results-header-chats-abandoned">Chats abandonados</span></th>
									<th class="nowrap dependsOnChat"><span data-i18n-title="reports-queues-results-header-chats-finished-by_agent-tip" data-i18n="[html]reports-queues-results-header-chats-finished-by_agent">Chats finalizados<br />x agente</span></th>
									<th class="nowrap dependsOnChat"><span data-i18n-title="reports-queues-results-header-chats-finished-by-user-tip" data-i18n="[html]reports-queues-results-header-chats-finished-by-user">Chats finalizados<br />x usuario</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-new-tip" data-i18n="reports-queues-results-header-cases-new">Casos nuevos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-closed-tip" data-i18n="reports-queues-results-header-cases-closed">Casos cerrados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-closed-by_system-tip" data-i18n="[html]reports-queues-results-header-cases-closed-by_system">Casos cerrados<br />x sistema</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-closed-by_sup-tip" data-i18n="[html]reports-queues-results-header-cases-closed-by_sup">Casos cerrados<br />x supervisores</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-closed-by_agents-tip" data-i18n="[html]reports-queues-results-header-cases-closed-by_agents">Casos cerrados<br />x agentes</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-reopen-tip" data-i18n="reports-queues-results-header-cases-reopen">Casos reabiertos</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-cases-with_messages_out_sl-tip" data-i18n="[html]reports-queues-results-header-cases-with_messages_out_sl">Casos con mensajes<br />fuera SL</span></th>
									<th class="nowrap dependsOnYSurveys"><span data-i18n-title="reports-queues-results-header-cases-surveys-sent-tip" data-i18n="reports-queues-results-header-cases-surveys-sent">Encuestas envíadas</span></th>
									<th class="nowrap dependsOnYSurveys"><span data-i18n-title="reports-queues-results-header-cases-surveys-started-tip" data-i18n="reports-queues-results-header-cases-surveys-started">Encuestas iniciadas</span></th>
									<th class="nowrap dependsOnYSurveys"><span data-i18n-title="reports-queues-results-header-cases-surveys-finished-tip" data-i18n="reports-queues-results-header-cases-surveys-finished">Encuestas finalizadas</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-login-tip" data-i18n="[html]reports-queues-results-header-agents-login">Cantidad<br />Login</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-logout-tip" data-i18n="[html]reports-queues-results-header-agents-logout">Cantidad<br />Logout</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-max_logged-tip" data-i18n="[html]reports-queues-results-header-agents-max_logged">Max Agentes<br />Logueados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-logged_at_interval_close-tip" data-i18n="[html]reports-queues-results-header-agents-logged_at_interval_close">Agentes logueados<br />cierre intervalo</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-assigned-tip" data-i18n="[html]reports-queues-results-header-agents-assigned">Asignados a<br />agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-discarded-tip" data-i18n="[html]reports-queues-results-header-agents-discarded">Descartados<br />por agente</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-returned-tip" data-i18n="[html]reports-queues-results-header-agents-returned">Retornados</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-transfered-tip" data-i18n="reports-queues-results-header-agents-transfered">Transferidos</span></th>
									<th rel="outgoingmessages" class="nowrap"><span data-i18n-title="reports-queues-results-header-agents-outgoing-tip" data-i18n="reports-queues-results-header-agents-outgoing">Salientes</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-times-tmo-tip" data-i18n="reports-queues-results-header-times-tmo">TMO</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-times-tmnl-tip" data-i18n="reports-queues-results-header-times-tmnl">TMNL</span></th>
									<th class="nowrap"><span data-i18n-title="reports-queues-results-header-times-tml-tip" data-i18n="reports-queues-results-header-times-tml">TML</span></th>
									<th rel="outgoingmessages" class="nowrap"><span data-i18n-title="reports-queues-results-header-times-tmos-tip" data-i18n="reports-queues-results-header-times-tmos">TMOS</span></th>
								</tr>
							</thead>
							<tbody></tbody>
							<tfoot>
								<tr class="footer footer-total">
									<td id="tdFooterTotal" data-i18n="reports-agents-include_case_totals">Totales</td>
									<td id="tdInterval" style="text-align: right">-</td>
									<td style="text-align: right">-</td>
									<td rel="NewMessages" style="text-align: right">0</td>
									<td rel="EnqueuedMessages" style="text-align: right">0</td>
									<td rel="PeekEnqueuedMessages" style="text-align: right">0</td>
									<td rel="-" style="text-align: right">-</td>
									<td rel="DiscardedMessages" style="text-align: right">0</td>
									<td rel="DiscardedMessagesByUsers" style="text-align: right">0</td>
									<td rel="SystemDiscardedMessages" style="text-align: right">0</td>
									<td rel="RepliedMessages" style="text-align: right">0</td>
									<td rel="ASA" style="text-align: right">0</td>
									<td rel="AutoRepliedMessages" style="text-align: right">0</td>
									<td rel="VerifiedMessages" style="text-align: right">0</td>
									<td rel="EnqueuedMessagesOnIntervalClose" style="text-align: right">0</td>
									<td rel="GroupedMessages" style="text-align: right">0</td>
									<td rel="RepliedMessagesByAgent" style="text-align: right">0</td>
									<td rel="AttendedMessagesByAgent" style="text-align: right">0</td>
									<td rel="RepliedMessagesByUser" style="text-align: right">0</td>
									<td rel="AgentGroupedMessages" style="text-align: right">0</td>
									<td rel="ReturnedToQueueMessages" style="text-align: right">0</td>
									<td rel="InboundMessages" style="text-align: right">0</td>
									<td rel="OutboundMessages" style="text-align: right">0</td>
									<td rel="FlowIn" style="text-align: right">0</td>
									<td rel="FlowOut" style="text-align: right">0</td>
									<td rel="OutOfSL" style="text-align: right">0</td>
									<td rel="OutOfSLInQueue" style="text-align: right">0</td>
									<td rel="OutOfSLInAgent" style="text-align: right">0</td>
									<td rel="Expired" style="text-align: right">0</td>
									<td rel="MessagesRepliedOutOfSL" style="text-align: right">0</td>
									<td rel="MessagesAttendedOutOfSL" style="text-align: right">0</td>
									<td rel="FlowInSLL" style="text-align: right">0</td>
									<td rel="FlowOutSLL" style="text-align: right">0</td>
									<td rel="OutOfSLL" style="text-align: right">0</td>
									<td rel="OutOfSLLInQueue" style="text-align: right">0</td>
									<td rel="OutOfSLLInAgent" style="text-align: right">0</td>
									<td rel="ExpiredSLL" style="text-align: right">0</td>
									<td rel="MessagesRepliedOutOfSLL" style="text-align: right">0</td>
									<td rel="MessagesAttendedOutOfSLL" style="text-align: right">0</td>
									<td rel="NewChats" style="text-align: right">0</td>
									<td rel="AbandonedMessages" style="text-align: right">0</td>
									<td rel="FinishedMessagesByAgent" style="text-align: right">0</td>
									<td rel="FinishedMessagesByUser" style="text-align: right">0</td>
									<td rel="NewCases" style="text-align: right">0</td>
									<td rel="ClosedCases" style="text-align: right">0</td>
									<td rel="AutoClosedCases" style="text-align: right">0</td>
									<td rel="ClosedCasesByUsers" style="text-align: right">0</td>
									<td rel="ClosedCasesByAgents" style="text-align: right">0</td>
									<td rel="ReopenCases" style="text-align: right">0</td>
									<td rel="CasesOutOfSL" style="text-align: right">0</td>
									<td rel="SurveysSent" style="text-align: right">0</td>
									<td rel="SurveysStarted" style="text-align: right">0</td>
									<td rel="SurveysFinished" style="text-align: right">0</td>
									<td rel="LoggedAgents" style="text-align: right">0</td>
									<td rel="LoggedOutAgents" style="text-align: right">0</td>
									<td rel="PeekLoggedAgents" style="text-align: right">0</td>
									<td rel="LoggedAgentsOnIntervalClose" style="text-align: right">0</td>
									<td rel="AssignedMessages" style="text-align: right">0</td>
									<td rel="DiscardedMessagesByAgents" style="text-align: right">0</td>
									<td rel="ReturnedToQueueMessages" style="text-align: right">0</td>
									<td rel="OutboundMessages" style="text-align: right">0</td>
									<td rel="OutgoingMessages" style="text-align: right">0</td>
									<td rel="TMO" style="text-align: right">0</td>
									<td rel="TMNL" style="text-align: right">0</td>
									<td rel="TML" style="text-align: right">0</td>
								</tr>
							</tfoot>
						</table>
					</div>
					<div id="divDetailedLoadMoreResults" class="more mtm" style="display: none">
						<a href="javascript:LoadDetailed()" data-i18n="reports-globals-load_more_records">Cargar más registros</a>
						<div class="loadingMore">
							<i class="fa fa-3x fa-spinner fa-pulse"></i>
						</div>
					</div>
					<div id="divDetailedChartsContainer" class="subseccion collapsable" style="display: none">
						<div class="title"><h2 data-i18n="reports-queues-results-detail-activity-title">Actividad de mensajes por cola</h2></div>
						<div class="contents">
							<div id="divDetailedCharts" style="width: 100%;"></div>
						</div>
					</div>
				</div>
			</asp:Panel>
			<asp:Panel ID="panelTagsCharts" runat="server" CssClass="seccion collapsable" ClientIDMode="Static">
				<div class="title"><h2 data-i18n="reports-queues-results-tags-title">Actividad de etiquetas por cola</h2></div>
				<div class="contents">
					<div id="divTagsChartsLoading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageTagsChartsNoRecords" runat="server" Type="Information" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-no_records">
						No se encontraron registros para los filtros especificados
					</yoizen:Message>
					<div id="divTagsCharts" style="width: 100%; display: none"></div>
				</div>
			</asp:Panel>
			<div class="buttons">
				<label class="uiButton uiButtonLarge uiButtonConfirm">
					<asp:Button ID="buttonNewSearch" runat="server" Text="Nueva búsqueda" OnClick="buttonClearFilters_Click" data-i18n="globals-new_search" />
				</label>
				<label class="uiButton uiButtonLarge">
					<asp:Button ID="buttonModifyFilters" runat="server" Text="Modificar filtros" OnClick="buttonModifyFilters_Click" data-i18n="globals-modify_filters" />
				</label>
				<label class="uiButton uiButtonLarge" style="display: none">
					<input type="button" id="buttonExport" value="Exportar" onclick="ShowExportDialog()" data-i18n="globals-export" />
				</label>
			</div>
		</asp:Panel>

		<div style="display: none">
			<div class="seccion" id="divExport">
				<div class="title">
					<h2 data-i18n="reports-globals-export-title">Exportación</h2>
				</div>
				<div class="contents">
					<div id="divExportStep1">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="[html]reports-globals-csv_instead_excel">
							Cuando el reporte a generar tiene mucha información es posible que en lugar
							de formato <b>Excel</b> se use <b>Separado por coma (CSV)</b>
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable">
							<tr class="dataRow dataRowSeparator">
								<th class="label" data-i18n="reports-globals-export-format">Formato:</th>
								<td class="data">
									<select id="selectExportFormat">
										<option value="1" data-i18n="reports-globals-export-format-excel">Excel</option>
										<option value="2" data-i18n="reports-globals-export-format-csv">Separados por coma (CSV)</option>
									</select>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="reports-globals-export-email">Email destinatario</span>:</th>
								<td class="data">
									<input type="text" id="inputExportEmail" maxlength="200" style="width: 90%;" class="inputtext" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="reports-globals-export-sections">Secciones a exportar</span>:</th>
								<td class="data">
									<select id="selectSectionsToExport" multiple="multiple" style="width: 500px;">
										<option value="2" data-i18n="reports-queues-filters-sections_to_query-detailed">Detallado por colas</option>
										<option value="3" data-i18n="reports-queues-filters-sections_to_query-tags">Actividad de etiquetas por colas</option>
									</select>
									<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" LocalizationKey="reports-globals-export-one_file_per_section">Se generará un archivo por cada sección seleccionada</yoizen:Message>
								</td>
							</tr>
						</table>
						<div id="divExportStep1InvalidEmail" style="display: none" class="validationerror"><span class="validationerror" data-i18n="reports-globals-must_enter_email">Debe ingresar un email válido</span></div>
						<div id="divExportStep1InvalidSections" style="display: none" class="validationerror"><span class="validationerror" data-i18n="reports-globals-must_select_sections">Debe seleccionar alguna sección a exportar</span></div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<input type="button" value="Exportar" onclick="ExportToMail()" data-i18n="globals-export" />
							</label>
							<label class="uiButton uiButtonLarge">
								<input type="button" value="Cancelar" onclick="$.colorbox.close()" data-i18n="globals-close" />
							</label>
						</div>
					</div>
					<div id="divExportStep2" style="display: none">
						<yoizen:Message ID="messageExportStep2" runat="server" Type="Information" LocalizationKey="reports-globals-export-mail_notification">
							Se enviará un mail a la dirección especificada apenas se encuentre listo el reporte
						</yoizen:Message>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<input type="button" value="Cerrar" onclick="$.colorbox.close()" data-i18n="globals-close" />
							</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</asp:Panel>
</asp:Content>