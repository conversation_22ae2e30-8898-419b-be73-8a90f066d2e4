﻿using System;
using System.Text;
using System.Net.Http;
using Newtonsoft.Json;
using System.Threading;
using System.Threading.Tasks;
using Yoizen.Social.DomainModel.CubiqSettings;
using System.Net;
using System.ServiceModel;
using Yoizen.Common;

namespace Yoizen.Social.Core.Cubiq
{
	public class IssuesRepository : CubiqBaseRepository
	{
		private const string endpoint = "/issues/";
		private const string issue_status_id_endCall = "101";


		public IssuesRepository() : base()
		{

		}

		public async Task<IssuesRoot> Post(string customer_id, string operator_id, string project_id)
		{
			var issue = new IssuesRequest
			{
				customer_id = customer_id,
				operator_id = operator_id,
				project_id = project_id
			};

			var json = JsonConvert.SerializeObject(issue);
			var content = new StringContent(json, Encoding.UTF8, "application/json");

			var response = await this.ExecutePostUrlWithAuthorization(endpoint, content);

			if (string.IsNullOrEmpty(response))
				return null;

			var root = new IssuesRoot();
			try
			{
				root = JsonConvert.DeserializeObject<IssuesRoot>(response);
			}
			catch (JsonException ex)
			{
				Tracer.TraceError($"Error deserializing JSON: {ex.Message}");
				Tracer.TraceError($"JSON: {json}");
			}

			return root;
		}

		public async Task<IssuesRoot> Put(string issue_id)
		{
			var issue = new
			{
				issue_status_id = issue_status_id_endCall
			};

			var json = JsonConvert.SerializeObject(issue);
			var content = new StringContent(json, Encoding.UTF8, "application/json");

			var response = await this.ExecutePutUrlWithAuthorization(endpoint + issue_id, content);

			if (string.IsNullOrEmpty(response))
				return null;

			var root = new IssuesRoot();
			try
			{
				root = JsonConvert.DeserializeObject<IssuesRoot>(response);
			}
			catch (JsonException ex)
			{
				Tracer.TraceError($"Error deserializing JSON: {ex.Message}");
				Tracer.TraceError($"JSON: {json}");
			}

			return root;
		}

		public async Task<Issue> Get(string issue_id)
		{
			var response = await this.ExecuteGetFromUrlWithAuthorization(endpoint + issue_id);

			if (string.IsNullOrEmpty(response))
				return null;

			var issuesRoot = new IssuesRoot();
			try
			{
				issuesRoot = JsonConvert.DeserializeObject<IssuesRoot>(response);
			}
			catch (JsonException ex)
			{
				Tracer.TraceError($"Error deserializing JSON: {ex.Message}");
				Tracer.TraceError($"JSON: {response}");
			}

			return issuesRoot?.data;
		}
	}
}
