﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/moment-with-langs.js" />
/// <reference path="../Scripts/Master.js" />
/// <reference path="Reports.js" />

$.ajaxSetup({ cache: false });

var loadDataAfterTranslations = false;

var $panelFilters;
var $textboxDates;
var $textboxFromDate;
var $textboxToDate;
var $dropdownlistByIntervals;
var $trTimeZoneToShow;
var $hiddenDailyTimeZoneToShow;
var $selectDailyTimeZoneToShow;
var $listboxQueues;
var $listboxQueueGroup;
var $listboxSections;
var $trByIntervalsIntervals;
var $dropdownlistFromInterval;
var $dropdownlistToInterval;

var $panelDetailed;
var $divDetailedLoading;
var $messageDetailedNoRecords;
var $messageDetailedTotalResults;
var $divDetailed;
var $tableDetailed;
var $divDetailedLoadMoreResults;

var $divDetailedChartsContainer;
var $divDetailedCharts;

var $panelTagsCharts;
var $divTagsChartsLoading;
var $messageTagsChartsNoRecords;
var $divTagsCharts;

var $buttonExport;

var sectionsLoaded = null;
var chartDetailed = null;

var $dropdownlistSearchBy;

let columnsData = [];

var $buttonCloseSchedule;

$(function () {
	$panelFilters = $('#panelFilters');
	if ($panelFilters.length > 0) {
		var $divSideNav = $('#divSideNav');
		var $topBar = $('#topBar');
		var $pageFooter = $('#pageFooter');
		$buttonCloseSchedule = $('#buttonCloseSchedule');

		if (typeof (isScheduled) === 'undefined') {
			isScheduled = false;
		}

		if (typeof (isScheduled) === 'boolean' && isScheduled) {
			$divSideNav.hide();
			$topBar.hide();
			$pageFooter.hide();
			$buttonCloseSchedule.show();

			//para mostrar el msj de error de reporte solicitado
			if (typeof (scheduleDuplicateError) !== 'undefined') {
				$('#divScheduleDuplicateerror').show();
				$('#validationScheduleDuplicate').show();
				$('#divSeccion').addClass("withError");
			}
		}

		$textboxDates = $("#textboxFromDate, #textboxToDate");
		var dates = $textboxDates.datepicker({
			changeMonth: true,
			changeYear: true,
			numberOfMonths: 1,
			maxDate: moment().local(true).subtract(1, 'days').toDate(),
			onSelect: function (selectedDate) {
				var option = this.id == "textboxFromDate" ? "minDate" : "maxDate",
				instance = $(this).data("datepicker"),
				date = $.datepicker.parseDate(
					instance.settings.dateFormat ||
					$.datepicker._defaults.dateFormat,
					selectedDate, instance.settings);
				dates.not(this).datepicker("option", option, date);
			}
		});

		AutoFillFilterDates('#textboxFromDate', '#textboxToDate');

		var $tableFilters = $('#tableFilters');

		$textboxFromDate = $("#textboxFromDate");
		$textboxToDate = $("#textboxToDate");
		$dropdownlistByIntervals = $('#dropdownlistByIntervals');
		$dropdownlistByIntervals.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>450' });
		$listboxQueues = $('#listboxQueues');
		$listboxQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
		$listboxQueueGroup = $('#listboxQueueGroup');
		$listboxQueueGroup.multiselect({ multiple: true, noneSelectedText: "Todas los grupos", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
		$listboxSections = $("#listboxSections");
		if (typeof (isScheduled) === 'boolean' && isScheduled) {
			$listboxSections.removeAttr('multiple');
			$listboxSections.children('option[selected="selected"]').removeAttr('selected');
			if (typeof (sectionSelected) === 'undefined') {
				sectionSelected = 2;
			}
			$listboxSections.children('option[value="' + sectionSelected + '"]').attr('selected', 'selected');
			$listboxSections.multiselect({ multiple: false, noneSelectedText: "Seleccione las secciones", selectedList: 6 });
		} else {
			$listboxSections.multiselect({ multiple: true, noneSelectedText: "Seleccione las secciones", selectedList: 6 });
		}
		
		$dropdownlistSearchBy = $('#dropdownlistSearchBy');
		$dropdownlistSearchBy.multiselect({ multiple: false, header: false, selectedList: 1, buttonWidth: '>500' }).multiselectfilter();

		$trTimeZoneToShow = $('#trTimeZoneToShow');
		$hiddenDailyTimeZoneToShow = $('#hiddenDailyTimeZoneToShow');
		$selectDailyTimeZoneToShow = $('#selectDailyTimeZoneToShow');

		var showTimeZoneTr = false;
		if (typeof (timezoneConfiguration) === 'object' && timezoneConfiguration !== null) {
			if (timezoneConfiguration.serverTimeZone !== timezoneConfiguration.defaultTimeZone) {
				var $option = $('<option></option>');
				$option.val(timezoneConfiguration.defaultTimeZone);
				$option.text($.i18n('globals-default_time_zone') + '(' + timezoneConfiguration.defaultTimeZone + ')');
				$option.attr('data-i18n', 'globals-default_time_zone');
				$selectDailyTimeZoneToShow.append($option);
				showTimeZoneTr = true;
			}

			if (timezoneConfiguration.allowToConfigureTimeZones && timezoneConfiguration.consolidedTimesZones !== null) {
				for (var i = 0; i < timezoneConfiguration.consolidedTimesZones.length; i++) {
					var tz = timezoneConfiguration.consolidedTimesZones[i];
					if (timezoneConfiguration.serverTimeZone !== tz &&
						timezoneConfiguration.defaultTimeZone !== tz) {
						showTimeZoneTr = true;
						var $option = $('<option></option>');
						$option.val(tz);
						$option.text(tz);
						$selectDailyTimeZoneToShow.append($option);
					}
				}
			}

			var selectedDailyTimeZoneToShow = $hiddenDailyTimeZoneToShow.val();
			if (selectedDailyTimeZoneToShow !== null && selectedDailyTimeZoneToShow.length > 0) {
				$selectDailyTimeZoneToShow.val(selectedDailyTimeZoneToShow);
			}
			else if (typeof (loggedUserSettings) !== 'undefined' &&
				loggedUserSettings !== null &&
				loggedUserSettings.hasOwnProperty('DefaultTimeZoneIana') &&
				loggedUserSettings.DefaultTimeZoneIana !== null) {
				$selectDailyTimeZoneToShow.val(loggedUserSettings.DefaultTimeZoneIana);
			}
			else if (timezoneConfiguration.allowToConfigureTimeZones && timezoneConfiguration.consolidedTimesZones !== null) {
				var currentOffset = moment().local().utcOffset();
				for (var i = 0; i < timezoneConfiguration.consolidedTimesZones.length; i++) {
					var tz = timezoneConfiguration.consolidedTimesZones[i];
					var offset = moment().tz(tz).utcOffset();
					if (offset === currentOffset) {
						$selectDailyTimeZoneToShow.val(tz);
						break;
					}
				}
			}
		}

		$selectDailyTimeZoneToShow.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>450' });
		$trByIntervalsIntervals = $('#trByIntervalsIntervals');
		$dropdownlistFromInterval = $('#dropdownlistFromInterval');
		$dropdownlistToInterval = $('#dropdownlistToInterval');

		$dropdownlistByIntervals.change({ showTimeZoneTr: showTimeZoneTr }, function (e) {
			if ($dropdownlistByIntervals.val() === "1") {
				$textboxDates.datepicker('option', 'maxDate', moment().local(true).toDate());
				$trTimeZoneToShow.hide();
				$trByIntervalsIntervals.show();
			}
			else {
				$textboxDates.datepicker('option', 'maxDate', moment().local(true).subtract(1, 'days').toDate());
				$trTimeZoneToShow.toggle(e.data.showTimeZoneTr);
				$trByIntervalsIntervals.hide();
			}

			$listboxSections.multiselect('refresh');
		}).trigger('change');

		$selectDailyTimeZoneToShow.change(function () {
			$hiddenDailyTimeZoneToShow.val($selectDailyTimeZoneToShow.val());
		}).trigger('change');

		$dropdownlistSearchBy.change(function () {
			var val = $dropdownlistSearchBy.val();

			var $trSearchByQueues = $('tr[rel=searchByQueues]', $tableFilters);
			var $trSearchByQueuesGroups = $('tr[rel=searchByQueuesGroups]', $tableFilters);

			$trSearchByQueues.toggle(val == 1);
			$trSearchByQueuesGroups.toggle(val == 2);
		}).trigger('change');
	}
	else {
		window.scrollTo(0, 0);

		$panelDetailed = $('#panelDetailed');
		$divDetailedLoading = $('#divDetailedLoading');
		$messageDetailedNoRecords = $('#messageDetailedNoRecords');
		$messageDetailedTotalResults = $('#messageDetailedTotalResults');
		$divDetailed = $('#divDetailed');
		$tableDetailed = $('#tableDetailed');
		$divDetailedLoadMoreResults = $('#divDetailedLoadMoreResults');

		$divDetailedChartsContainer = $('#divDetailedChartsContainer');
		$divDetailedCharts = $('#divDetailedCharts');

		$panelTagsCharts = $('#panelTagsCharts');
		$divTagsChartsLoading = $('#divTagsChartsLoading');
		$messageTagsChartsNoRecords = $('#messageTagsChartsNoRecords');
		$divTagsCharts = $('#divTagsCharts');

		$buttonExport = $('#buttonExport');

		if (canExportReport !== undefined
			&& typeof (canExportReport) === 'boolean'
			&& !canExportReport) {
			$buttonExport.hide();
		}

		LoadHighchartsOptions();
		Highcharts.setOptions({
			global: {
				useUTC: false
			}
		});

		$(document).scroll(function () {
			if (selectedSections.indexOf(2) >= 0) {
				var loaded = $panelDetailed.prop('loaded');
				if (typeof (loaded) == 'undefined' || loaded == null)
					loaded = false;

				if (loaded)
					return;

				var isLoading = $panelDetailed.prop('isLoading');
				if (typeof (isLoading) == 'undefined' || isLoading == null)
					isLoading = false;

				if (!isLoading) {
					if (IsScrolledIntoView($panelDetailed[0])) {
						$panelDetailed.prop('isLoading', true);
						$panelDetailed.prop('firstTime', true);
						$panelDetailed.prop('lastRecord', null);

						LoadDetailed();
					}
				}
			}

			if (selectedSections.indexOf(3) >= 0) {
				var loaded = $panelTagsCharts.prop('loaded');
				if (typeof (loaded) == 'undefined' || loaded == null)
					loaded = false;

				if (loaded)
					return;

				var isLoading = $panelTagsCharts.prop('isLoading');
				if (typeof (isLoading) == 'undefined' || isLoading == null)
					isLoading = false;

				if (!isLoading) {
					if (IsScrolledIntoView($panelTagsCharts[0])) {
						$panelTagsCharts.prop('isLoading', true);
						$panelTagsCharts.prop('firstTime', true);

						LoadTags();
					}
				}
			}
		}).trigger('scroll');
	}
});

function i18nLoaded() {
	if ($panelFilters.length > 0) {
		$dropdownlistByIntervals.multiselect('refresh');
		$selectDailyTimeZoneToShow.multiselect('refresh');
		$listboxQueues.multiselect('option', 'noneSelectedText', $.i18n("reports-queues-filters-queues-all_queues"));
		$listboxQueueGroup.multiselect('option', 'noneSelectedText', $.i18n("reports-queues-all_queues_groups"));
		$listboxSections.multiselect('option', 'noneSelectedText', $.i18n("reports-queues-filters-sections_to_query-select"));
		$listboxSections.multiselect('refresh');
	}
	else {
		if (typeof (loadDataAfterTranslations) === 'boolean' && loadDataAfterTranslations) {
			if (selectedSections.indexOf(2) >= 0) {
				LoadDetailed();
			}

			if (selectedSections.indexOf(3) >= 0) {
				LoadTags();
			}
		}
	}
}

function ValidateFilters(sender, e) {
	e.IsValid = false;

	if (typeof (isScheduled) === 'boolean' && !isScheduled) {
		var fromDate = $textboxFromDate.val();
		var toDate = $textboxToDate.val();

		var dateFormat = $textboxFromDate.datepicker('option', 'dateFormat')

		if (fromDate.length == 0 || toDate.length == 0) {
			$(sender).text($.i18n('reports-globals-must_enter_dates'));
			return;
		}

		var from, to;
		try {
			from = $.datepicker.parseDate(dateFormat, fromDate);
			to = $.datepicker.parseDate(dateFormat, toDate);
		}
		catch (ex) {
			$(sender).text($.i18n('reports-globals-must_enter_dates'));
			return;
		}

		if (to < from) {
			$(sender).text($.i18n('reports-globals-to_date_cannot_be_lower_than_from'));
			return;
		}

		if ($dropdownlistByIntervals.val() === '1') {
			var fromInterval = parseInt($dropdownlistFromInterval.val(), 10);
			var toInterval = parseInt($dropdownlistToInterval.val(), 10);

			if (fromInterval >= toInterval) {
				$(sender).text($.i18n('reports-globals-to_inerval_cannot_be_lower_than_to'));
				return;
			}
		}
	}
	var sections = $listboxSections.val();
	if (sections == null || sections == '') {
		$(sender).text($.i18n('reports-globals-must_select_sections'));
		return;
	}

	e.IsValid = true;
}

var detailedRecords;
var detailedTotalRecords;
var detailedVisibleRecords;

function LoadDetailed() {
	if (typeof (translationsLoaded) !== 'undefined' && !translationsLoaded) {
		loadDataAfterTranslations = true;
		return;
	}

	var firstTime = $panelDetailed.prop('firstTime');

	$('div', $divDetailedLoadMoreResults).show();
	$('a', $divDetailedLoadMoreResults).hide();

	if (firstTime) {
		var dataToSend = JSON.stringify({
			fromDate: fromDate,
			toDate: toDate,
			queues: queues,
			byIntervals: byIntervals,
			timezone: timezoneToQuery,
			intervalsRange: intervalsRange
		});

		$.ajax({
			type: "POST",
			url: "Queues.aspx/LoadMoreResultsDetailed",
			data: dataToSend,
			contentType: "application/json; charset=utf-8",
			dataType: "json",
			success: function (msg) {
				if (msg.d.Success) {
					if (msg.d.Records == null || msg.d.Records.length == 0) {
						$messageDetailedNoRecords.show();
						$messageDetailedTotalResults.hide();
						$divDetailed.hide();
					}
					else {
						detailedRecords = msg.d.Records;
						detailedTotalRecords = msg.d.TotalRecords;
						detailedVisibleRecords = 0;

						LoadNextDetailedRecords(firstTime, 0);
					}
				}
				else {
					if (console)
						console.log('No se pudieron traer más registros: %o', msg.d.Error);
				}

				$divDetailedLoading.hide();
			},
			error: function (jqXHR, textStatus, errorThrown) {
				$divDetailedLoading.hide();

				var $messageDetailedError = $('#messageDetailedError');
				var $messageDetailedTimeout = $('#messageDetailedTimeout');

				if (textStatus == 'timeout') {
					$messageDetailedTimeout.show();
				}
				else {
					if (jqXHR.responseText.indexOf('Request timed out') >= 0)
						$messageDetailedTimeout.show();
					else
						$messageDetailedError.show();
				}

				if (console) {
					console.error('TextStatus: %s\nError: %s', textStatus, jqXHR.responseText);
				}
			}
		});
	}
	else {
		LoadNextDetailedRecords(firstTime, detailedVisibleRecords);
	}
}

function LoadNextDetailedRecords(firstTime, fromIndex) {
	if (firstTime) {
		$messageDetailedNoRecords.hide();
		$messageDetailedTotalResults.show();
		$divDetailed.show();
		if (canExportReport !== undefined
			&& typeof (canExportReport) === 'boolean'
			&& !canExportReport) {
			$buttonExport.hide();
		} else {
			$buttonExport.parent().show();
		}
		$divDetailedChartsContainer.show();

		chartDetailed = LoadDetailedChart();
	}

	if (detailedVisibleRecords + pageSize < detailedTotalRecords) {
		$('div', $divDetailedLoadMoreResults).hide();
		$('a', $divDetailedLoadMoreResults).show();
		$divDetailedLoadMoreResults.show();

		detailedVisibleRecords += pageSize;
	}
	else {
		$divDetailedLoadMoreResults.hide();

		detailedVisibleRecords = detailedTotalRecords;
	}

	LoadDetailedRecords(detailedRecords, fromIndex);
	$divDetailed.show();

	var percentage = detailedVisibleRecords * 100 / detailedTotalRecords;
	$('td.text', $messageDetailedTotalResults).text($.i18n('reports-globals-showing_records', detailedVisibleRecords, detailedTotalRecords, percentage.toFixed(2)));
	$panelDetailed.prop('firstTime', false);

	ShowTotals(CalculateTotals(detailedRecords, fromIndex, columnsData));
}

function LoadDetailedRecords(detailedValues, fromIndex) {
	var $thead = $('thead', $tableDetailed);
	var $thInterval = $('#thInterval', $thead);
	var $thAgentTotals = $('#thAgentTotals', $thead);
	var $thTimes = $('#thTimes', $thead);
	var $tdInterval = $('#tdInterval');
	var $trHeaderColumns = $('#trHeaderColumns', $thead);
	var $thCases = $('#thCases', $thead);
	var $tbody = $('tbody', $tableDetailed);

	$thInterval.toggle(byIntervals);
	$tdInterval.toggle(byIntervals);

	if (outgoingMessagesConfigured) {
		var columns = Number($thAgentTotals.attr('colspan'));
		columns++;
		$thAgentTotals.attr('colspan', columns);

		columns = Number($thTimes.attr('colspan'));
		columns++;
		$thTimes.attr('colspan', columns);
	}
	else {
		var $thsOutgoingMessages = $('th[rel=outgoingmessages]', $thead);
		$thsOutgoingMessages.hide();
	}

	if (typeof (surveysEnabled) === 'undefined' || !surveysEnabled) {
		var columns = Number($thCases.attr('colspan'));
		columns -= 3;
		$thCases.attr('colspan', columns);
	}

	if (fromIndex == 0) {
		var $ths = $('span[data-original-title]', $thead);
		$ths.tooltip({
			container: 'body',
			placement: 'top',
			title: function () {
				var $this = $(this);
				var $title = $('title', $this);
				return $title.html();
			}
		});
	}

	for (var i = fromIndex; i < detailedValues.length && i < fromIndex + pageSize; i++) {
		var record = detailedValues[i];
		if (typeof (record.a) != 'undefined')
			record = record.a;

		var $tr = $('<tr></tr>');
		$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');

		var queueId = GetDetailedField(record, 'QueueID');
		if (queueId == 0)
			$tr.addClass('subtotal');

		var date = GetDetailedField(record, 'Date');
		var interval = GetDetailedField(record, 'Interval');

		var queueHasSLConfigured = GetDetailedField(record, 'QueueHasSLConfigured');
		var queueHasSLLConfigured = GetDetailedField(record, 'QueueHasSLLConfigured');

		var intervalDateTime = GetDetailedField(record, 'IntervalDateTime');
		$tr.append('<td class="nowrap right">' + DisplayDateTime(intervalDateTime, 'L', !byIntervals) + '</td>');
		if (byIntervals)
			$tr.append('<td class="nowrap">' + DisplayDateTime(intervalDateTime, 'HHmm', !byIntervals) + '</td>');
		$tr.append('<td class="nowrap fixedColumn">' + GetDetailedField(record, 'Queue').Name + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'NewMessages'), 1) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'EnqueuedMessages'), 2) + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'PeekEnqueuedMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + BuildPeekMinutesEnqueuedMessages(record, byIntervals) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'DiscardedMessages'), 3) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'DiscardedMessagesByUsers'), 5) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'SystemDiscardedMessages'), 6) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'RepliedMessages'), 4) + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedFieldAsTime(record, 'ASA') + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'AutoRepliedMessages'), 10) + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'VerifiedMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'EnqueuedMessagesOnIntervalClose') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'GroupedMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'RepliedMessagesByAgent'), 7) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'AttendedMessagesByAgent'), 12) + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'RepliedMessagesByUser'), 8) + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'AgentGroupedMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ReturnedToQueueMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'InboundMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'OutboundMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'FlowIn') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'FlowOut') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'OutOfSL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'OutOfSLInQueue') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'OutOfSLInAgent') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'Expired') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'MessagesRepliedOutOfSL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLConfigured ? GetDetailedField(record, 'MessagesAttendedOutOfSL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'FlowInSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'FlowOutSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'OutOfSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'OutOfSLLInQueue') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'OutOfSLLInAgent') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'ExpiredSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'MessagesRepliedOutOfSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right">' + (queueHasSLLConfigured ? GetDetailedField(record, 'MessagesAttendedOutOfSLL') : 'N/A') + '</td>');
		$tr.append('<td class="nowrap right dependsOnChat">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'NewChats'), 11) + '</td>');
		$tr.append('<td class="nowrap right dependsOnChat">' + GetDetailedField(record, 'AbandonedMessages') + '</td>');
		$tr.append('<td class="nowrap right dependsOnChat">' + GetDetailedField(record, 'FinishedMessagesByAgent') + '</td>');
		$tr.append('<td class="nowrap right dependsOnChat">' + GetDetailedField(record, 'FinishedMessagesByUser') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'NewCases') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ClosedCases') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'AutoClosedCases') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ClosedCasesByUsers') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ClosedCasesByAgents') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ReopenCases') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'CasesOutOfSL') + '</td>');
		$tr.append('<td class="nowrap right dependsOnYSurveys">' + GetDetailedField(record, 'SurveysSent') + '</td>');
		$tr.append('<td class="nowrap right dependsOnYSurveys">' + GetDetailedField(record, 'SurveysStarted') + '</td>');
		$tr.append('<td class="nowrap right dependsOnYSurveys">' + GetDetailedField(record, 'SurveysFinished') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'LoggedAgents') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'LoggedOutAgents') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'PeekLoggedAgents') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'LoggedAgentsOnIntervalClose') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'AssignedMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + BuildDetailedMessages(queueId, byIntervals, date, interval, GetDetailedField(record, 'DiscardedMessagesByAgents'), 9) + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'ReturnedToQueueMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedField(record, 'OutboundMessages') + '</td>');
		$tr.append('<td rel="outgoingmessages" class="nowrap right">' + GetDetailedField(record, 'OutgoingMessages') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedFieldAsTime(record, 'TMO') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedFieldAsTime(record, 'TMNL') + '</td>');
		$tr.append('<td class="nowrap right">' + GetDetailedFieldAsTime(record, 'TML') + '</td>');
		$tr.append('<td rel="outgoingmessages" class="nowrap right">' + (queueId == 0 ? GetDetailedFieldAsTime(record, 'TMOS') : '-') + '</td>');

		$tbody.append($tr);
	}

	columnsData = [
		{ name: "NewMessages" },
		{ name: "EnqueuedMessages" },
		{ name: "PeekEnqueuedMessages" },
		{ name: "DiscardedMessages" },
		{ name: "DiscardedMessagesByUsers" },
		{ name: "SystemDiscardedMessages" },
		{ name: "RepliedMessages" },
		{ name: "ASA", type: "time", showAverage: true },
		{ name: "AutoRepliedMessages" },
		{ name: "VerifiedMessages" },
		{ name: "EnqueuedMessagesOnIntervalClose" },
		{ name: "GroupedMessages" },
		{ name: "RepliedMessagesByAgent" },
		{ name: "AttendedMessagesByAgent" },
		{ name: "RepliedMessagesByUser" },
		{ name: "AgentGroupedMessages" },
		{ name: "ReturnedToQueueMessages" },
		{ name: "InboundMessages" },
		{ name: "OutboundMessages" },
		{ name: "FlowIn", isNA: !queueHasSLConfigured },
		{ name: "FlowOut", isNA: !queueHasSLConfigured },
		{ name: "OutOfSL", isNA: !queueHasSLConfigured },
		{ name: "OutOfSLInQueue", isNA: !queueHasSLConfigured },
		{ name: "OutOfSLInAgent", isNA: !queueHasSLConfigured },
		{ name: "Expired", isNA: !queueHasSLConfigured },
		{ name: "MessagesRepliedOutOfSL", isNA: !queueHasSLConfigured },
		{ name: "MessagesAttendedOutOfSL", isNA: !queueHasSLConfigured },
		{ name: "FlowInSLL", isNA: !queueHasSLLConfigured },
		{ name: "FlowOutSLL", isNA: !queueHasSLLConfigured },
		{ name: "OutOfSLL", isNA: !queueHasSLLConfigured },
		{ name: "OutOfSLLInQueue", isNA: !queueHasSLLConfigured },
		{ name: "OutOfSLLInAgent", isNA: !queueHasSLLConfigured },
		{ name: "ExpiredSLL", isNA: !queueHasSLLConfigured },
		{ name: "MessagesRepliedOutOfSLL", isNA: !queueHasSLLConfigured },
		{ name: "MessagesAttendedOutOfSLL", isNA: !queueHasSLLConfigured },
		{ name: "NewChats" },
		{ name: "AbandonedMessages" },
		{ name: "FinishedMessagesByAgent" },
		{ name: "FinishedMessagesByUser" },
		{ name: "NewCases" },
		{ name: "ClosedCases" },
		{ name: "AutoClosedCases" },
		{ name: "ClosedCasesByUsers" },
		{ name: "ClosedCasesByAgents" },
		{ name: "ReopenCases" },
		{ name: "CasesOutOfSL" },
		{ name: "SurveysSent" },
		{ name: "SurveysStarted" },
		{ name: "SurveysFinished" },
		{ name: "LoggedAgents" },
		{ name: "LoggedOutAgents" },
		{ name: "PeekLoggedAgents" },
		{ name: "LoggedAgentsOnIntervalClose" },
		{ name: "AssignedMessages" },
		{ name: "DiscardedMessagesByAgents" },
		{ name: "ReturnedToQueueMessages" },
		{ name: "OutboundMessages" },
		{ name: "OutgoingMessages" },
		{ name: "TMO", type: "time", showAverage: true },
		{ name: "TMNL", type: "time", showAverage: true },
		{ name: "TML", type: "time", showAverage: true },
		{
			name: "TMOS",
			type: "time",
			showAverage: true,
			acumulateIf: function (record) {
				let queueId = GetDetailedField(record, 'QueueID');
				return queueId === 0;
			}
		}
	];

	ShowTotals(CalculateTotals(detailedValues, fromIndex, columnsData));

	var $tdsOutgoingMessages = $('td[rel=outgoingmessages]', $tableDetailed);
	$tdsOutgoingMessages.toggle(outgoingMessagesConfigured);

	if (fromIndex == 0) {
		LoadDetailedGraph(false);
	}

	LoadCompositedElements();
}

function CalculateHourStats(time, value) {
	if (time === 0 || value === 0) {
		return 0;
	}

	var computedValue = (3600) / (time / value);
	return computedValue.toFixed(2);
}

function BuildPeekMinutesEnqueuedMessages(record, byIntervals) {
	var time = GetDetailedField(record, 'PeekMinutesEnqueuedMessages').toString().lpad('0', 4);
	var hours = parseInt(time.substr(0, 2));
	var minutes = parseInt(time.substr(2, 2));
	if (minutes === 60) {
		minutes = 0;
		hours++;
	}

	if (byIntervals) {
		var intervalDateTime = moment(GetDetailedField(record, 'IntervalDateTime')).tz(currentTimeZoneIana);
		intervalDateTime = intervalDateTime.hour(hours).minutes(minutes);
		if (typeof (loggedUserSettings) !== 'undefined' &&
			loggedUserSettings !== null &&
			loggedUserSettings.hasOwnProperty('DefaultTimeZoneIana') &&
			loggedUserSettings.DefaultTimeZoneIana !== null) {
			intervalDateTime = intervalDateTime.tz(loggedUserSettings.DefaultTimeZoneIana);
		}
		else {
			intervalDateTime = intervalDateTime.local();
		}
		hours = intervalDateTime.hour();
		minutes = intervalDateTime.minute();
	}

	return hours.toString().lpad('0', 2) + ':' + minutes.toString().lpad('0', 2);
}

function BuildDetailedMessages(queueId, byIntervals, date, interval, messages, dateType) {
	if (messages == 0)
		return '0';

	//var html = '<a href="javascript:ShowDetailedMessages(' + queueId + ', ' + byIntervals + ', \'' + date + '\', ' + interval + ', ' + dateType + ')">' + messages + '</a>';
	var html = messages.toString();
	return html;
}

function ShowDetailedMessages(queueID, byIntervals, date, interval, dateType) {
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: "MessagesQuery.aspx",
		width: '800px',
		initialWidth: '800px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			LoadMessages({ showBy: 8, queueID: queueID, byIntervals: byIntervals ? 1 : 0, date: date, interval: interval, dateType: dateType });

			$.colorbox.resize();
		}
	});
}

function LoadDetailedGraph(acumulate) {
	var index = 0;
	var newSerie = chartDetailed.series[index++];
	var enqueuedSerie = chartDetailed.series[index++];
	var assignedSerie = chartDetailed.series[index++];
	var repliedSerie = chartDetailed.series[index++];
	var attendedByAgentSerie = chartDetailed.series[index++];
	var discardedSerie = chartDetailed.series[index++];
	var systemRepliedSerie = chartDetailed.series[index++];
	var systemDiscardedSerie = chartDetailed.series[index++];
	var outgoingMessagesSerie = null;
	if (outgoingMessagesConfigured)
		outgoingMessagesSerie = chartDetailed.series[index++];
	var finishedChatMessagesSerie = null;
	var abandonedChatMessagesSerie = null;
	if (licensedSocialServiceTypes.contains(SocialServiceTypes.Chat)) {
		finishedChatMessagesSerie = chartDetailed.series[index++];
		abandonedChatMessagesSerie = chartDetailed.series[index++];
	}

	var newMessages = 0;
	var enqueuedMessages = 0;
	var assignedMessages = 0;
	var repliedMessages = 0;
	var attendedByAgentMessages = 0;
	var discardedMessages = 0;
	var autoRepliedMessages = 0;
	var systemDiscardedMessages = 0;
	var outgoingMessages = 0;
	var finishedChatMessages = 0;
	var abandonedChatMessages = 0;

	if (detailedRecords.length > 0) {
		var i = 0;

		var newSerieData = [];
		var enqueuedSerieData = [];
		var assignedSerieData = [];
		var repliedSerieData = [];
		var attendedByAgentSerieData = [];
		var discardedSerieData = [];
		var systemRepliedSerieData = [];
		var systemDiscardedSerieData = [];
		var outgoingMessagesSerieData = null;
		if (outgoingMessagesConfigured)
			outgoingMessagesSerieData = [];
		var finishedChatMessagesSerieData = null;
		var abandonedChatMessagesSerieData = null;
		if (licensedSocialServiceTypes.contains(SocialServiceTypes.Chat)) {
			finishedChatMessagesSerieData = [];
			abandonedChatMessagesSerieData = [];
		}

		while (i < detailedRecords.length) {
			var record = detailedRecords[i];
			if (typeof (record.a) != 'undefined')
				record = record.a;

			var date = GetCurrentDate(record);

			if (!acumulate) {
				newMessages = 0;
				enqueuedMessages = 0;
				assignedMessages = 0;
				repliedMessages = 0;
				attendedByAgentMessages = 0;
				discardedMessages = 0;
				autoRepliedMessages = 0;
				systemDiscardedMessages = 0;
				outgoingMessages = 0;
				finishedChatMessages = 0;
				abandonedChatMessages = 0;
			}

			while (i < detailedRecords.length && date == GetCurrentDate(record)) {
				newMessages += GetDetailedField(record, 'NewMessages');
				enqueuedMessages += GetDetailedField(record, 'EnqueuedMessages');
				assignedMessages += GetDetailedField(record, 'AssignedMessages');
				repliedMessages += GetDetailedField(record, 'RepliedMessages');
				attendedByAgentMessages += GetDetailedField(record, 'AttendedMessagesByAgent');
				discardedMessages += GetDetailedField(record, 'DiscardedMessages');
				autoRepliedMessages += GetDetailedField(record, 'AutoRepliedMessages');
				systemDiscardedMessages += GetDetailedField(record, 'SystemDiscardedMessages');
				outgoingMessages += GetDetailedField(record, 'OutgoingMessages');
				finishedChatMessages += GetDetailedField(record, 'FinishedMessagesByAgent');
				abandonedChatMessages += GetDetailedField(record, 'AbandonedMessages');

				i++;
				if (i < detailedRecords.length) {
					record = detailedRecords[i];
					if (typeof (record.a) != 'undefined')
						record = record.a;
				}
			}

			newSerieData.push([date, newMessages]);
			enqueuedSerieData.push([date, enqueuedMessages]);
			assignedSerieData.push([date, assignedMessages]);
			repliedSerieData.push([date, repliedMessages]);
			attendedByAgentSerieData.push([date, attendedByAgentMessages]);
			discardedSerieData.push([date, discardedMessages]);
			systemRepliedSerieData.push([date, autoRepliedMessages]);
			systemDiscardedSerieData.push([date, systemDiscardedMessages]);
			if (outgoingMessagesConfigured)
				outgoingMessagesSerieData.push([date, outgoingMessages]);
			if (licensedSocialServiceTypes.contains(SocialServiceTypes.Chat)) {
				finishedChatMessagesSerieData.push([date, finishedChatMessages]);
				abandonedChatMessagesSerieData.push([date, abandonedChatMessages]);
			}
		}

		newSerie.setData(newSerieData, false, false, true);
		enqueuedSerie.setData(enqueuedSerieData, false, false, true);
		assignedSerie.setData(assignedSerieData, false, false, true);
		repliedSerie.setData(repliedSerieData, false, false, true);
		attendedByAgentSerie.setData(attendedByAgentSerieData, false, false, true);
		discardedSerie.setData(discardedSerieData, false, false, true);
		systemRepliedSerie.setData(systemRepliedSerieData, false, false, true);
		systemDiscardedSerie.setData(systemDiscardedSerieData, false, false, true);
		if (outgoingMessagesConfigured)
			outgoingMessagesSerie.setData(outgoingMessagesSerieData, false, false, true);
		if (licensedSocialServiceTypes.contains(SocialServiceTypes.Chat)) {
			finishedChatMessagesSerie.setData(finishedChatMessagesSerieData, false, false, true);
			abandonedChatMessagesSerie.setData(abandonedChatMessagesSerieData, false, false, true);
		}

		var newOptions = {
			exporting: {
				buttons: {
					acumulateButton: {
						enabled: !acumulate
					},
					stopAcumulateButton: {
						enabled: acumulate
					}
				}
			}
		};
		chartDetailed.update(newOptions, false);
		chartDetailed.redraw();
	}
}

function GetCurrentDate(record) {
	var intervalDateTime = GetDetailedField(record, 'IntervalDateTime');
	var intervalDateTimeMoment;
	if (byIntervals) {
		intervalDateTimeMoment = moment(intervalDateTime);
	}
	else {
		intervalDateTimeMoment = moment.utc(intervalDateTime);
	}

	if (typeof (window.serverOffset) === 'undefined') {
		window.serverOffset = moment().tz(currentTimeZoneIana).utcOffset();
	}
	if (typeof (window.currentOffset) === 'undefined') {
		window.currentOffset = moment().local().utcOffset();
	}
	if (typeof (window.timezoneOffset) === 'undefined') {
		window.timezoneOffset = (new Date()).getTimezoneOffset();
	}
	if (typeof (window.configuredOffset) === 'undefined') {
		if (typeof (loggedUserSettings) !== 'undefined' &&
			loggedUserSettings !== null &&
			loggedUserSettings.hasOwnProperty('DefaultTimeZoneIana') &&
			loggedUserSettings.DefaultTimeZoneIana !== null) {
			var configuredZone = moment.tz.zone(loggedUserSettings.DefaultTimeZoneIana);
			window.configuredOffset = moment().tz(configuredZone.name).utcOffset();
		}
		else {
			window.configuredOffset = window.currentOffset;
		}
	}

	if (window.configuredOffset !== window.currentOffset) {
		intervalDateTimeMoment.add(window.configuredOffset - window.currentOffset, 'minutes');
	}

	var date = intervalDateTimeMoment.valueOf();

	return date;
}

function AddOrUpdateSeries(serie, date, value) {
	serie.addPoint([date, value], false, false);
}

function LoadDetailedChart() {
	LoadHighchartsOptions();

	var lang = Highcharts.getOptions().lang;
	lang['Acumular'] = $.i18n('reports-globals-chart-acumulate');
	lang['NoAcumular'] = $.i18n('reports-globals-chart-do_not_acumulate');
	
	var newSerie = { name: $.i18n('reports-queues-results-detail_chart-series-new'), yAxis: 0, data: null };
	var enqueuedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-enqueued'), yAxis: 0, data: null };
	var assignedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-assigned'), yAxis: 0, data: null };
	var repliedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-replied'), yAxis: 0, data: null };
	var attendedByAgentSerie = { name: $.i18n('reports-queues-results-detail_chart-series-attended'), yAxis: 0, data: null };
	var discardedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-discarded'), yAxis: 0, data: null };
	var systemRepliedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-replied-by_system'), yAxis: 0, data: null };
	var systemDiscardedSerie = { name: $.i18n('reports-queues-results-detail_chart-series-discarded-by_system'), yAxis: 0, data: null };
	var outgoingMessagesSerie = { name: $.i18n('reports-queues-results-detail_chart-series-outgoing'), yAxis: 0, data: null };
	var finishedChatMessagesSerie = { name: $.i18n('reports-queues-results-detail_chart-series-chats-finished'), yAxis: 0, data: null };
	var abandonedChatMessagesSerie = { name: $.i18n('reports-queues-results-detail_chart-series-chats-abandoned'), yAxis: 0, data: null };
	var series = [newSerie, enqueuedSerie, assignedSerie, repliedSerie, attendedByAgentSerie,discardedSerie, systemRepliedSerie, systemDiscardedSerie];
	if (outgoingMessagesConfigured)
		series.push(outgoingMessagesSerie);
	if (licensedSocialServiceTypes.contains(SocialServiceTypes.Chat)) {
		series.push(finishedChatMessagesSerie);
		series.push(abandonedChatMessagesSerie);
	}

	var tickInterval, minorTickInterval, minRange;
	var MILLISECONDS_IN_HOUR = 3600 * 1000;
	var MILLISECONDS_IN_DAY = 24 * MILLISECONDS_IN_HOUR;
	var singleDay = fromDate == toDate;
	var totalDays = dateDiffInDays(new Date(fromDate), new Date(toDate));

	if (byIntervals) {
		tickInterval = null;
		if (singleDay) {
			minorTickInterval = MILLISECONDS_IN_HOUR;
			minRange = MILLISECONDS_IN_DAY;
		}
		else {
			minorTickInterval = null;
			minRange = 7 * MILLISECONDS_IN_HOUR;
		}
	}
	else {
		if (singleDay) {
			tickInterval = 2 * MILLISECONDS_IN_DAY;
			minorTickInterval = MILLISECONDS_IN_DAY;
			minRange = 2 * MILLISECONDS_IN_DAY;
		}
		else {
			if (totalDays > 30) {
				tickInterval = 2 * MILLISECONDS_IN_DAY;
				minorTickInterval = MILLISECONDS_IN_DAY;
				minRange = MILLISECONDS_IN_DAY;
			}
			else if (totalDays > 7) {
				tickInterval = MILLISECONDS_IN_DAY;
				minorTickInterval = MILLISECONDS_IN_DAY;
				minRange = MILLISECONDS_IN_DAY;
			}
			else {
				tickInterval = MILLISECONDS_IN_DAY;
				minorTickInterval = 12 * MILLISECONDS_IN_HOUR;
				minRange = MILLISECONDS_IN_DAY;
			}
		}
	}

	var title = $.i18n('reports-queues-results-detail_chart-title');
	var subtitle = null;
	var pointStart = new Date(fromDate);

	var width = $divDetailedCharts.width();

	var chart = new Highcharts.Chart({
		chart: {
			renderTo: 'divDetailedCharts',
			zoomType: singleDay ? '' : 'x',
			spacingRight: 20,
			defaultSeriesType: 'spline',
			width: width
		},
		title: {
			text: title,
			visible: title != null
		},
		subtitle: {
			text: subtitle,
			visible: subtitle != null
		},
		xAxis: {
			title: {
				text: (singleDay) ? ((byIntervals) ? $.i18n('reports-globals-chart-xaxis-only_intervals-title') : null) : ((byIntervals) ? $.i18n('reports-globals-chart-xaxis-by_interval-title') : $.i18n('reports-globals-chart-xaxis-title'))
			},
			type: 'datetime',
			tickPosition: 'outside',
			tickmarkPlacement: 'on',
			tickInterval: tickInterval,
			minorTickInterval: minorTickInterval,
			minRange: minRange,
			dateTimeLabelFormats: { // don't display the dummy year
				day: '%e de %b',
				month: '%e. %b',
				year: '%b',
				hour: (byIntervals ? (singleDay ? '%H:%M' : $.i18n('reports-globals-chart-xaxis-time_interval')) : '')
			},
			labels: {
				rotation: 300,
				align: 'right'
			}
		},
		credits: {
			enabled: false
		},
		yAxis: {
			allowDecimals: false,
			title: {
				text: $.i18n('reports-agents-results-detail_chart-yaxis-title')
			},
			min: 0,
			gridLineDashStyle: 'Dot'
		},
		legend: {
			layout: 'horizontal',
			align: 'center',
			verticalAlign: 'bottom',
			shadow: true
		},
		plotOptions: {
			column: {
				pointPadding: 0.2,
				borderWidth: 0,
				dataLabels: {
					enabled: true
				},
				pointStart: pointStart
			}
		},
		tooltip: {
			formatter: function () {
				if (byIntervals) {
					return $.i18n('reports-globals-chart-tooltip-by_interval-format', this.series.name, DisplayDateTime(this.x, 'L'), DisplayDateTime(this.x, 'LT'), this.y);
				}
				else {
					return $.i18n('reports-globals-chart-tooltip-format', this.series.name, DisplayDateTime(this.x, 'L'), this.y);
				}
			}
		},
		exporting: {
			enabled: true,
			buttons: {
				contextButton: {
					enabled: false
				},
				acumulateButton: {
					text: $.i18n('reports-globals-chart-acumulate'),
					_titleKey: 'Acumular',
					onclick: function () {
						LoadDetailedGraph(true);
					}
				},
				stopAcumulateButton: {
					text: $.i18n('reports-globals-chart-do_not_acumulate'),
					_titleKey: 'NoAcumular',
					onclick: function () {
						LoadDetailedGraph(false);
					}
				}
			}
		},
		series: series
	});

	return chart;
}

function GetDetailedField(record, field, format, defaultValue) {
	for (var i = 0; i < detailedPropertyNames.length; i++) {
		if (detailedPropertyNames[i] == field) {
			if (record[i] == null)
				return '';

			if (typeof (format) != 'undefined') {
				var n = numeral(record[i]);
				return n.format(format);
			}

			return record[i];
		}
	}

	if (typeof (defaultValue) == 'undefined')
		defaultValue = '';

	return defaultValue;
}

function GetDetailedFieldAsTime(record, field, options) {
	if (typeof (options) !== 'object' || options === null) {
		options = {};
	}

	options = $.extend({
		includeMilliseconds: true,
		includeDays: true
	}, options);

	for (var i = 0; i < detailedPropertyNames.length; i++) {
		if (detailedPropertyNames[i] == field) {
			return ConvertSecondsAsTimeString(record[i], options);
		}
	}

	return '-';
}

function LoadTags() {
	if (typeof (translationsLoaded) !== 'undefined' && !translationsLoaded) {
		loadDataAfterTranslations = true;
		return;
	}

	var dataToSend = JSON.stringify({ fromDate: fromDate, toDate: toDate, queues: queues, byIntervals: byIntervals, timezone: timezoneToQuery, intervalsRange: intervalsRange });

	$.ajax({
		type: "POST",
		url: "Queues.aspx/LoadMoreResultsTags",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (msg) {
			if (msg.d.Success) {
				if (msg.d.Records == null || msg.d.Records.length == 0) {
					$messageTagsChartsNoRecords.show();
				}
				else {
					$divTagsCharts.show();
					if (canExportReport !== undefined
						&& typeof (canExportReport) === 'boolean'
						&& !canExportReport) {
						$buttonExport.hide();
					} else {
						$buttonExport.parent().show();
					}

					Highcharts.getOptions().colors.splice(0, 0, 'transparent');
					Highcharts.setOptions({
						lang: {
							drillUpText: "\u25c1 " + $.i18n('globals-back')
						}
					});

					var chart = LoadTagChart(null);

					var tagsData = [];
					tagsData.push({
						id: '0.0',
						parent: '',
						name: null
					});

					var data = msg.d;
					for (var i = 0; i < data.ParentTags.length; i++) {
						if (data.ParentTags[i].Parent === null) {
							tagsData.push({
								id: `${data.ParentTags[i].Level}.${data.ParentTags[i].ID}`,
								parent: '0.0',
								name: data.ParentTags[i].Name
							});
						}
						else {
							tagsData.push({
								id: `${data.ParentTags[i].Level}.${data.ParentTags[i].ID}`,
								parent: `${data.ParentTags[i].Level - 1}.${data.ParentTags[i].Parent}`,
								name: data.ParentTags[i].Name
							});
						}
					}

					for (var i = 0; i < msg.d.Records.length; i++) {
						let tag = msg.d.Records[i];
						if (tag.HasChildTags) {
							/* 
							Si una etiqueta tiene hijos, entonces se le agregaron luego de ser utilizada.
							Le creamos una hija __default__ a la etiqueta para poder mostrarla al mismo nivel que otras posibles hijas
							con datos
							*/
							let id = `${tag.Level}.${tag.ID}`;

							if (tagsData.findIndex(t => t.id === id) === -1) {
								tagsData.push({
									id: id,
									parent: `${tag.Level - 1}.${tag.Parents[tag.Parents.length - 1]}`,
									name: tag.Name,
								});
							}

							tagsData.push({
								id: `${tag.Level + 1}.${tag.ID}_default`,
								parent: id,
								name: $.i18n('globals-tags-default_child'),
								value: tag.Count
							});
						}
						else {
							tagsData.push({
								id: `${tag.Level}.${tag.ID}`,
								parent: `${tag.Level - 1}.${tag.Parents[tag.Parents.length - 1]}`,
								name: tag.Name,
								value: tag.Count
							});
						}
					}

					chart.series[0].setData(tagsData, true, false, true);
				}
			}
			else {
				if (console)
					console.log('No se pudieron traer más registros: %o' + msg.d.Error);
			}

			$divTagsChartsLoading.hide();
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$divTagsChartsLoading.hide();

			if (console)
				console.log(jqXHR.responseText);
		}
	});
}

function LoadTagChart(data) {
	chart = new Highcharts.Chart({
		chart: {
			animation: false,
			renderTo: 'divTagsCharts',
			height: '600px'
		},
		title: {
			text: null
		},
		subtitle: {
			text: null
		},
		credits: {
			enabled: false
		},
		legend: {
			align: 'center',
			floating: false,
			layout: 'horizontal',
			verticalAlign: 'bottom',
			enabled: true,
		},
		tooltip: {
			formatter: function () {
				if (this.key !== '0.0') {
					//return '<b>' + this.point.name + '</b>: ' + this.point.value;
					const point = this.point,
						series = this.series;
					let mode = series.options.custom && series.options.custom.percentage;

					if (typeof (mode) === 'undefined') {
						mode = 'whole';
					}

					const chartTotal = series.__myTotal || (series.__myTotal = series.data.map(p => p.options.value || 0).reduce((a, b) => a + b));

					let percentage;
					switch (mode) {
						case 'whole':
							percentage = point.value / chartTotal;
							break;
						case 'parent':
							const group = point.parent && series.chart.get(point.parent),
								  total = group ? group.value : chartTotal;
							percentage = point.value / total;
							break;
					}

					const val = (percentage === undefined) ? point.value : (percentage * 100).toFixed(2) + '%';
					return '<b>' + this.point.name + '</b>: ' + this.point.value + ' (' + val + ')';
				}

				return false;
			}
		},
		series: [{
			type: "sunburst",
			data: null,
			animation: false,
			allowDrillToNode: true,
			cursor: 'pointer',
			custom: {
				percentage: 'whole',  //'whole' or 'parent'
			},
			traverseUpButton: {
				text: '< ' + $.i18n('globals-back')
			},
			dataLabels: {
				format: '{point.name}',
				filter: {
					property: 'innerArcLength',
					operator: '>',
					value: 16
				},
				rotationMode: 'circular'
			},
			levels: [{
				level: 1,
				levelIsConstant: false
			}, {
				level: 2,
				colorByPoint: true
			},
			{
				level: 3,
				colorVariation: {
					key: 'brightness',
					to: -0.2
				}
			}, {
				level: 4,
				colorVariation: {
					key: 'brightness',
					to: 0.2
				}
			}, {
				level: 5,
				colorVariation: {
					key: 'brightness',
					to: -0.2
				}
			}]
		}]
	});

	return chart;
}

function LoadTagChartWithDrillDown(categoriesData, tagsData) {
	chart = new Highcharts.Chart({
		chart: {
			animation: false,
			renderTo: 'divTagsCharts',
			type: 'pie'
		},
		title: {
			text: null
		},
		subtitle: {
			text: null
		},
		credits: {
			enabled: false
		},
		plotOptions: {
			pie: {
				depth: 35
			},
			series: {
				dataLabels: {
					enabled: true,
					//format: '{point.name}: {point.y:.1f}%'
					formatter: function () {
						return '<b>' + this.point.name + '</b>: ' + this.y + ' (' + Highcharts.numberFormat(this.percentage, 2) + '%)';
					},
					style: {
						fontSize: '13px'
					}
				}
			}
		},
		tooltip: {
			formatter: function () {
				return '<b>' + this.point.name + '</b>: ' + this.y + ' (' + Highcharts.numberFormat(this.percentage, 2) + '%)';
			}
		},
		series: [{
			name: 'Categorías',
			colorByPoint: true,
			data: categoriesData,
		}],
		drilldown: {
			series: tagsData
		}
	});

	return chart;
}

function ShowExportDialog(sections, removeSections) {
	if (typeof (sections) == 'undefined')
		sections = selectedSections;

	if (typeof (removeSections) == 'undefined')
		removeSections = true;

	var $divExportStep1 = $('#divExportStep1');
	var $divExportStep2 = $('#divExportStep2');

	$divExportStep1.show();
	$divExportStep2.hide();

	var $selectSectionsToExport = $('#selectSectionsToExport');
	var $options = $('option', $selectSectionsToExport);
	for (var i = $options.length - 1; i >= 0; i--) {
		var $option = $($options[i]);
		var val = parseInt($option.val(), 10);
		if (sections.indexOf(val) == -1) {
			if (removeSections)
				$option.remove();
			else
				$option.removeAttr('selected');
		}
		else {
			$option.attr('selected', 'selected');
		}
	}

	$selectSectionsToExport.multiselect({ multiple: true, noneSelectedText: $.i18n('reports-globals-export-sections-select'), selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

	$inputExportEmail = $('#inputExportEmail');
	if (typeof (loggedUserEmail) != 'undefined' && loggedUserEmail != null)
		$inputExportEmail.val(loggedUserEmail);
	else
		$inputExportEmail.val('');

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divExport",
		width: '800px',
		initialWidth: '800px',
		preloading: false,
		closeButton: false
	});
}

function ExportToMail() {
	var $inputExportEmail = $('#inputExportEmail');
	var $divExportStep1InvalidEmail = $('#divExportStep1InvalidEmail');
	var email = $inputExportEmail.val();

	if (email.length == 0) {
		$divExportStep1InvalidEmail.show();
		$.colorbox.resize();
		return;
	}

	var pattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
	if (!email.match(pattern)) {
		$divExportStep1InvalidEmail.show();
		$.colorbox.resize();
		return;
	}

	$divExportStep1InvalidEmail.hide();

	var $selectSectionsToExport = $('#selectSectionsToExport');
	var $divExportStep1InvalidSections = $('#divExportStep1InvalidSections');
	var sections = $selectSectionsToExport.val();

	if (sections == null || sections.length == 0) {
		$divExportStep1InvalidSections.show();
		$.colorbox.resize();
		return;
	}

	$divExportStep1InvalidSections.hide();

	$selectExportFormat = $('#selectExportFormat');
	var exportFormat = $selectExportFormat.val();

	var dataToSend = JSON.stringify({
		fromDate: fromDate,
		toDate: toDate,
		queues: queues,
		byIntervals: byIntervals,
		sections: sections,
		email: email,
		exportFormat: exportFormat,
		timezone: timezoneToQuery,
		intervalsRange: intervalsRange
	});

	$.ajax({
		type: "POST",
		url: "Queues.aspx/Export",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (msg) {
			if (msg.d.Success) {
				var $divExportStep1 = $('#divExportStep1');
				var $divExportStep2 = $('#divExportStep2');

				$divExportStep1.hide();
				$divExportStep2.show();
				$.colorbox.resize();
			}
			else {
				if (typeof (msg.d.AlreadyExists) !== 'undefined' && msg.d.AlreadyExists) {
					AlertDialog($.i18n('reports-globals-export-alert-title'), $.i18n('reports-globals-export-alert-already_solicited'), $.colorbox.close, $.colorbox.close, 'Warning');
				}
				else {
					AlertDialog($.i18n('reports-globals-export-alert-title'), $.i18n('reports-globals-export-alert-error', msg.d.Error.Message), undefined, undefined, 'Error');
				}
			}
		}
	});
}