﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Yoizen.Common;

namespace Yoizen.Social.DomainModel.Metrics
{
	public static class MetricsAdapter
	{
		#region Totals Metrics

		/// <summary>
		/// Calcula el tiempo en BOT del caso
		/// </summary>
		/// <param name="parameters">Parameters del caso</param>
		/// <param name="invokedDate">Fecha inicial (en caso de ser null inicializa el parametro con tiempo 0)</param>
		/// <param name="changeDate">fecha de cambio</param>
		/// <param name="updateParameters">variable out para saber si se deben actualizar los parametros del caso o no</param>
		public static void UpdateBotTimeParameter(Dictionary<string, string> parameters, DateTime invokedDate, DateTime changeDate)
		{
			try
			{
				double milisecondsToAdd = 0;
				milisecondsToAdd = (changeDate - invokedDate).TotalMilliseconds;

				MetricsManager.CalculateCaseMetrics(Case.BotTimeParameter, parameters, Math.Round(milisecondsToAdd));
			}
			catch (Exception ex) 
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo en BOT {ex}");
			}
		}

		/// <summary>
		/// Acumula la cantidad de milisegundos especifica en total de tiempo de NO lectura del caso.
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalUnreadMessagesTime(Dictionary<string, string> parameters, DateTime assignedDate, DateTime readDate)
		{
			try
			{
				var milliseconds = Math.Round((readDate - assignedDate).TotalMilliseconds);

				MetricsManager.CalculateCaseMetrics(Case.TotalUnreadMessagesTime, parameters, milliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo sin leer: {ex}");
			}
		}

		/// <summary>
		/// Acumula la cantidad de milisegundos especifica en total de tiempo de lectura del caso.
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalReadingMessagesTime(Dictionary<string, string> parameters, DateTime readDate, DateTime finishReadDate)
		{
			try
			{
				var milliseconds = Math.Round((finishReadDate - readDate).TotalMilliseconds);

				MetricsManager.CalculateCaseMetrics(Case.TotalReadMessagesTime, parameters, milliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo de lectura: {ex}");
			}
		}

		/// <summary>
		/// Calcula la cantidad especifica de milisegundos en cola del primer mensaje del caso
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalSystemTime(Dictionary<string, string> parameters, double seconds)
		{
			try
			{
				var totalMiliseconds = seconds * 1000;
				MetricsManager.CalculateCaseMetrics(Case.TotalSystemTimeParameter, parameters, totalMiliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo de primera respuesta por agente: {ex}");
			}
		}

		/// <summary>
		/// Acumula la cantidad de milisegundos especifica en total de tiempo de Trabajo del caso desde que se encoló hasta que fue finalizado.
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalOperationTimeParameter(Dictionary<string, string> parameters, DateTime finishDate, DateTime enqueueDate)
		{
			try
			{
				var milliseconds = Math.Round((finishDate - enqueueDate).TotalMilliseconds);

				MetricsManager.CalculateCaseMetrics(Case.TotalOperationTimeParameter, parameters, milliseconds);

			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo total de trabajo: {ex}");
			}
		}

		/// <summary>
		/// Acumula la cantidad de milisegundos especifica en total de tiempo de Trabajo del caso desde que se encoló hasta que fue finalizado.
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalWorkingTimeParameter(Dictionary<string, string> parameters, double seconds)
		{
			try
			{
				var totalMiliseconds = seconds * 1000;
				MetricsManager.CalculateCaseMetrics(Case.TotalWorkingTimeParameter, parameters, totalMiliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo total de trabajo: {ex}");
			}
		}

		#endregion

		#region Unique Metrics

		/// <summary>
		/// Calcula la cantidad especifica de milisegundos en cola del primer mensaje del caso
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateFirstMessageReplyByAgentTimeParameter(Dictionary<string, string> parameters, DateTime replyDate, DateTime assginedDate, out bool updateParameters)
		{
			updateParameters = false;
			try
			{
				bool exist = MetricsManager.MetricExist(Case.TimeFirstMessageReplyByAgentTimeParameter, parameters);

				if (exist)
					return;

				updateParameters = true;
				var milliseconds = Math.Round((replyDate - assginedDate).TotalMilliseconds);
				MetricsManager.CalculateCaseMetrics(Case.TimeFirstMessageReplyByAgentTimeParameter, parameters, milliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo de primer mensaje en cola: {ex}");
			}
		}

		/// <summary>
		/// Calcula la cantidad especifica de milisegundos en cola del primer mensaje del caso
		/// </summary>
		/// <param name="parameters">Los parametros del caso actual</param>
		/// <param name="milisecondsToAdd">La cantidad de milisegundos a sumar</param>
		public static void CalculateTotalFirstReplyTimeParameter(Dictionary<string, string> parameters, DateTime interactionMoment, DateTime assignedDate)
		{
			try
			{
				bool exist = MetricsManager.MetricExist(Case.TotalFirstRepliedMessageTimeParameter, parameters);

				if (exist)
					return;

				var milliseconds = Math.Round((interactionMoment - assignedDate).TotalMilliseconds);
				MetricsManager.CalculateCaseMetrics(Case.TotalFirstRepliedMessageTimeParameter, parameters, milliseconds);
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"[Metrics] Falló al actualizar tiempo de primera respuesta por agente: {ex}");
			}
		}


		#endregion
	}
}
