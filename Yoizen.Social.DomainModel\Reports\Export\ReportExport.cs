﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.IO;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Collections;
using Yoizen.Social.DomainModel.Reports.Scheduled;
using Yoizen.Common;
using Newtonsoft.Json.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace Yoizen.Social.DomainModel.Reports.Export
{
	public abstract class ReportExport
	{
		#region Fields

		private bool calledBeforeGeneration = false;

		#endregion

		#region Constants

		protected static readonly string Separator = ";";

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve el código del reporte a exportar
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public int ID { get; private set; }

		/// <summary>
		/// Devuelve el usuario al que le pertenece la el reporte
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public User Owner { get; set; }

		/// <summary>
		/// Devuelve la fecha en la que se generó la solicitud de exportación
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public DateTime DateSolicited { get; private set; }

		/// <summary>
		/// Devuelve la fecha en la que se generó la exportación o null si todavía no se generó
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public DateTime? DateGenerated { get; set; }

		/// <summary>
		/// Devuelve la fecha en la que se inició la exportación o null si todavía no empezó
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public DateTime? DateStarted { get; set; }

		/// <summary>
		/// Devuelve o establece la zona para las fechas
		/// </summary>
		public string OwnerTimeZone { get; set; }

		/// <summary>
		/// Devuelve o establece la zona para la consulta para los reportes diarios
		/// </summary>
		public string TimeZoneToQuery { get; set; }

		/// <summary>
		/// Devuelve el tipo de reporte
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public abstract ReportTypes Type { get; }

		/// <summary>
		/// Devuelve la configuración del reporte
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public abstract string Configuration { get; }

		/// <summary>
		/// Devuelve si el reporte ya se generó
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public bool Generated { get; set; }

		/// <summary>
		/// Devuelve el formato de exportación
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public ExportFormats Format { get; set; }

		/// <summary>
		/// Devuelve el nombre del archivo
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public string Filename { get; set; }

		/// <summary>
		/// Devuelve si se deberá crear la tabla con los filtros cuando se escribe en excel
		/// </summary>
		protected abstract bool ShouldWriteFilters { get; }

		/// <summary>
		/// Devuelve si se deberá crear la fila de totales
		/// </summary>
		protected virtual bool ShouldIncludeTotalsRow { get { return false; } }

		/// <summary>
		/// Devuelve o establece la casilla email a donde se enviará el email cuando se finalice la exportación del reporte
		/// </summary>
		public string Email { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de registros visibles que tiene el reporte cuando se generó
		/// </summary>
		public int TotalResults { get; set; }

		/// <summary>
		/// Devuelve o establece si quedan más registros para obtener (debería haber más registros disponibles cuando
		/// la resta de <see cref="TotalRecords"/> y <see cref="TotalResults"/>) sea mayor a cero)
		/// </summary>
		public bool MoreRecordsAvailable { get; set; }

		/// <summary>
		/// Devuelve o establece si se solicitó la información de registros completas (cuando hay más registros por cargar)
		/// </summary>
		public bool ExportComplete { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de registros que se pueden obtener de este reporte cuando se solicite la información completa
		/// </summary>
		public int TotalRecords { get; set; }

		/// <summary>
		/// Devuelve o establece la url de blob donde se descargara el reporte solicitado
		/// </summary>
		public string BlobUrl { get; set; }

		/// <summary>
		/// Devuelve o establece el path donde esta alojado el reporte
		/// </summary>
		public string BlobPath { get; set; }

		/// <summary>
		/// Devuelve o establece si el reporte es de tipo Diario
		/// </summary>
		public bool Daily { get; set; }

		/// <summary>
		/// Devuelve el hash de la configuración
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public string Hash
		{
			get
			{
				if (this.Configuration == null || string.IsNullOrEmpty(this.Configuration))
					return null;

				var oldTotalResults = this.TotalResults;
				var oldMoreRecordsAvailable = this.MoreRecordsAvailable;
				var oldEmail = this.Email;

				this.TotalResults = 0;
				this.MoreRecordsAvailable = false;
				this.Email = null;

				byte[] data = Encoding.Default.GetBytes(this.Configuration);

				this.TotalResults = oldTotalResults;
				this.MoreRecordsAvailable = oldMoreRecordsAvailable;
				this.Email = oldEmail;

				using (var sha = new System.Security.Cryptography.SHA1CryptoServiceProvider())
				{
					byte[] result = sha.ComputeHash(data);

					return Convert.ToBase64String(result);
				}
			}
		}

		/// <summary>
		/// Devuelve o establece el estado de generación del reporte
		/// </summary>
		public ReportExportStatus Status { get; set; }

		/// <summary>
		/// Devuelve o establece el resultado de generación del reporte cuando <see cref="Status"/> es <see cref="ReportExportStatus.Finished"/>
		/// </summary>
		public ReportExportResults? Result { get; set; }

		/// <summary>
		/// Devuelve o establece el lenguaje que se utilizará para exportar
		/// </summary>
		public string Language { get; set; }

		/// <summary>
		/// Devuelve o establece si se escribirá en el LOG el avance
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public bool WriteToLog { get; set; }

		/// <summary>
		/// Devuelve el código del reporte programado asociado
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public int? IdScheduled { get; set; }

		/// <summary>
		/// Devuelve la fecha de intervalo
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public DateTime? DateInterval { get; set; }

		/// <summary>
		/// Devuelve la periocidad en caso de que sea un reporte generado por programación
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public ReportScheduled.ExportPeriodicity? Periodicity { get; set; }

		/// <summary>
		/// Devuelve o establece parámetros extras del reporte
		/// </summary>
		public Dictionary<string, string> Parameters { get; set; }

		#endregion

		#region Constructors

		static ReportExport()
		{
#if !NETCOREAPP
			if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["CsvDelimiter"]))
			{
				var delimiter = System.Configuration.ConfigurationManager.AppSettings["CsvDelimiter"];
				Separator = delimiter;

				Common.Tracer.TraceInfo("Se utilizará como separador de CSV: {0}", Separator);
			}
#endif
		}

		public ReportExport()
		{
			this.Generated = false;
			this.DateSolicited = DateTime.Now;
			this.DateGenerated = null;
			this.DateStarted = null;
			this.Format = ExportFormats.Excel;
			this.Result = null;
			this.Status = ReportExportStatus.Pending;
			this.WriteToLog = false;
			this.calledBeforeGeneration = false;
			this.IdScheduled = null;
			this.Daily = false;
			this.Parameters = new Dictionary<string, string>();

			try
			{
				this.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
			}
			catch
			{
				this.Language = "es";
			}
		}

		#endregion

		#region Protected Methods

		/// <summary>
		/// Turn a string into a CSV cell output
		/// </summary>
		/// <param name="str">String to output</param>
		/// <returns>The CSV cell formatted string</returns>
		protected static string StringToCSVCell(string str)
		{
			bool mustQuote = (str.Contains(",") || str.Contains("\"") || str.Contains("\r") || str.Contains("\n"));
			if (mustQuote)
			{
				StringBuilder sb = new StringBuilder();
				sb.Append("\"");
				foreach (char nextChar in str)
				{
					sb.Append(nextChar);
					if (nextChar == '"')
						sb.Append("\"");
				}
				sb.Append("\"");
				return sb.ToString();
			}

			return str;
		}

		/// <summary>
		/// Devuelve los nombres de las columnas del reporte
		/// </summary>
		/// <returns>Una enumeración de <see cref="string"/> con los nombres de las columnas</returns>
		protected abstract IEnumerable<string> GetColumnTitles();

		/// <summary>
		/// Es llamado antes del inicio de la generación del reporte
		/// </summary>
		protected virtual void BeforeGeneration()
		{

		}

		/// <summary>
		/// Escribe el formato de las columnas
		/// </summary>
		/// <param name="docWriter">El <see cref="OpenXmlWriter"/> donde se escribirá</param>
		/// <param name="columnStartIndex">El índice de columna donde se empezará a escribir</param>
		/// <param name="columnEndIndex">El índice de columna donde se terminará a escribir</param>
		protected virtual void WriteColumns(OpenXmlWriter docWriter, uint columnStartIndex, uint columnEndIndex)
		{
			Columns cs = new Columns();

			for (uint index = columnStartIndex; index < columnEndIndex; index++)
			{
				Column col = new Column();
				col.Width = 25;
				col.BestFit = true;
				col.Min = index + 1;
				col.Max = index + 1;
				col.CustomWidth = false;
				cs.Append(col);
			}

			docWriter.WriteElement(cs);
		}

		/// <summary>
		/// Escribe los filtros del reporte
		/// </summary>
		/// <param name="docWriter">El <see cref="OpenXmlWriter"/> donde se escribirá</param>
		/// <param name="rowIndex">El índice de la fila en donde se escribirá</param>
		/// <returns>La fila en la que se terminó de escribir</returns>
		protected abstract uint WriteFilters(OpenXmlWriter docWriter, uint rowIndex);

		/// <summary>
		/// Escribe los títulos de la tabla de datos del reporte
		/// </summary>
		/// <param name="docWriter">El <see cref="OpenXmlWriter"/> donde se escribirá</param>
		/// <param name="rowIndex">El índice de la fila en donde se escribirá</param>
		/// <returns>La fila en la que se terminó de escribir</returns>
		protected abstract void WriteDataTitles(OpenXmlWriter docWriter, uint rowIndex);

		/// <summary>
		/// Escribe los valores del registro actual del <see cref="reader"/> en tabla de datos del reporte
		/// </summary>
		/// <param name="docWriter">El <see cref="OpenXmlWriter"/> donde se escribirá</param>
		/// <param name="record">El registro actual que se escribirá de tipo <typeparamref name="T"/></param>
		/// <param name="rowIndex">El índice de la fila en donde se escribirá</param>
		/// <param name="sharedStringTable">La <see cref="SharedStringTable"/> donde se pueden ubicar los strings compartidos</param>
		/// <typeparam name="T">El tipo de registro que se escribirá</typeparam>
		protected abstract void WriteDataRowValues(OpenXmlWriter docWriter, object record, uint rowIndex, SharedStringTable sharedStringTable);

		/// <summary>
		/// Escribe dentro de la tabla de cadenas compartidas los que necesite
		/// </summary>
		/// <param name="sharedStringTable">La <see cref="SharedStringTable"/> donde se escribirá</param>
		protected virtual void WriteSharedStrings(SharedStringTable sharedStringTable)
		{

		}

		/// <summary>
		/// Escribe los valores del registro en el <see cref="CsvHelper.CsvWriter"/>
		/// </summary>
		/// <param name="sw">Un <see cref="CsvHelper.CsvWriter"/> donde se escribirá el registro</param>
		/// <param name="record">El registro actual que se escribirá de tipo <typeparamref name="T"/></param>
		/// <typeparam name="T">El tipo de registro que se escribirá</typeparam>
		protected abstract void WriteDataRowValues<T>(CsvHelper.CsvWriter sw, T record);

		/// <summary>
		/// Escribe los valores del registro en el <see cref="CsvHelper.CsvWriter"/>
		/// </summary>
		/// <param name="sw">Un <see cref="CsvHelper.CsvWriter"/> donde se escribirá el registro</param>
		/// <param name="rowToken">El <see cref="Newtonsoft.Json.Linq.JToken"/> con los valores del registro</param>
		/// <typeparam name="T">El tipo de registro que se escribirá</typeparam>
		protected abstract void WriteDataRowValues(CsvHelper.CsvWriter sw, Newtonsoft.Json.Linq.JToken rowToken);

		/// <summary>
		/// Escribe las columnas que serán totales
		/// </summary>
		/// <param name="tableData">Un <see cref="Table"/> con la definición de la tabla</param>
		protected virtual void WriteTotals(Table tableData)
		{
		}

		/// <summary>
		/// Escribe el registro correspondiente a los totales
		/// </summary>
		/// <param name="docWriter">El <see cref="OpenXmlWriter"/> donde se escribirá</param>
		/// <param name="rowIndex">El índice de la fila en donde se escribirá</param>
		protected virtual void WriteDataRowTotals(OpenXmlWriter docWriter, uint rowIndex)
		{
			IEnumerable<string> titles = this.GetColumnTitles();
			uint columnIndex = 0;
			foreach (var title in titles)
			{
				docWriter.WriteElement(ExcelHelper.CreateCell(columnIndex, string.Empty, rowIndex));
				columnIndex++;
			}
		}

		/// <summary>
		/// Genera el reporte utilizando el <see cref="CsvHelper.CsvWriter"/> especificado
		/// </summary>
		/// <param name="records">Los registros a exportar</param>
		/// <param name="sw">Un <see cref="CsvHelper.CsvWriter"/> donde se escribirá el contenido del reporte</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		/// <remarks>
		/// Este método se utilizará para cuando <see cref="Format"/> sea <see cref="ExportFormats.CSV"/>
		/// </remarks>
		protected abstract void Generate<T>(IEnumerable<T> records, CsvHelper.CsvWriter sw, System.Threading.CancellationToken cancellationToken);

		/// <summary>
		/// Genera el reporte utilizando el <see cref="CsvHelper.CsvWriter"/> especificado
		/// </summary>
		/// <param name="reader">Un <see cref="DataReader"/> con los registros a exportar</param>
		/// <param name="sw">Un <see cref="CsvHelper.CsvWriter"/> donde se escribirá el contenido del reporte</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		/// <remarks>
		/// Este método se utilizará para cuando <see cref="Format"/> sea <see cref="ExportFormats.CSV"/>
		/// </remarks>
		protected abstract void Generate<T>(DataReader<T> reader, CsvHelper.CsvWriter sw, System.Threading.CancellationToken cancellationToken) where T : DomainObject;

		/// <summary>
		/// Graba los registros especificados en <paramref name="records"/> en el documento
		/// </summary>
		/// <param name="document">El <see cref="SpreadsheetDocument"/> donde se guardarán los registros</param>
		/// <param name="records">Una enumeración de registros que se grabarán</param>
		protected void GenerateSpreadSheet(SpreadsheetDocument document, IEnumerable records, System.Threading.CancellationToken cancellationToken)
		{
			//Navigate to the workbook part
			WorkbookPart workbookPart = document.WorkbookPart;
			//open the first worksheet
			WorksheetPart worksheetPart = workbookPart.WorksheetParts.First();

			SharedStringTable sharedStringTable = workbookPart.CreateSharedStringTable();
			workbookPart.CreateStyles();

			this.WriteSharedStrings(sharedStringTable);

			//Get the id of this sheet. We need this because we are going to add a new 
			//worksheet to this workbook, then we are going to delete this worksheet
			//This ID will tell us which one to delete
			string origninalSheetId = workbookPart.GetIdOfPart(worksheetPart);
			//Add the new worksheet
			WorksheetPart replacementPart = workbookPart.AddNewPart<WorksheetPart>();

			//This is the ID of the new worksheet
			string replacementPartId = workbookPart.GetIdOfPart(replacementPart);

			//We are going to read from the original worksheet to get the 
			//templated items that we added to the worksheet in the traditional way
			using (OpenXmlReader docReader = OpenXmlReader.Create(worksheetPart))
			//We are goint to copy the items from the original worksheet by using
			//an XML writer, this overcomes the memory limitations of having
			//an extremely large dataset.
			using (OpenXmlWriter docWriter = OpenXmlWriter.Create(replacementPart))
			{
				IEnumerable<string> titles = this.GetColumnTitles();

				uint tableFiltersStartIndex = 1;
				uint tableFiltersEndIndex = 1;
				uint tableStartIndex = 1;
				uint tableEndIndex = 1;

				//The template does not have any data so we will be creating new rows and cells
				//Then writing them using XML.
				Row row = new Row();
				Cell cell = new Cell();

				while (docReader.Read())
				{
					//This iterates through the sheet data and copies it
					if ((object.ReferenceEquals(docReader.ElementType, typeof(SheetData))))
					{
						//Exit the loop if we hit a sheetdata end element
						if ((docReader.IsEndElement))
							break; // TODO: might not be correct. Was : Exit While

						this.WriteColumns(docWriter, 0, (uint) titles.Count());

						//We create a new sheetdata element (basically this is the reoot container for a sheet)
						docWriter.WriteStartElement(new SheetData());

						uint rowIndex = 1;

						#region Filtros

						if (this.ShouldWriteFilters)
						{
							tableFiltersStartIndex = rowIndex;

							//Start the first row.
							row.RowIndex = rowIndex;
							docWriter.WriteStartElement(row);

							cell = ExcelHelper.CreateCell(0, "Filtros", rowIndex);
							docWriter.WriteElement(cell);
							cell = ExcelHelper.CreateCell(1, "Valores", rowIndex);
							docWriter.WriteElement(cell);

							docWriter.WriteEndElement();
							rowIndex++;

							rowIndex = this.WriteFilters(docWriter, rowIndex);

							tableFiltersEndIndex = rowIndex - 1;

							rowIndex++;
						}

						#endregion

						#region Datos

						tableStartIndex = rowIndex;

						// Títulos
						row.RowIndex = rowIndex;
						docWriter.WriteStartElement(row);
						this.WriteDataTitles(docWriter, rowIndex);
						docWriter.WriteEndElement();
						rowIndex++;

						// Valores

						int recordIndex = 0;
						foreach (var r in records)
						{
							if (cancellationToken.IsCancellationRequested)
							{
								cancellationToken.ThrowIfCancellationRequested();
								break;
							}

							row.RowIndex = rowIndex;
							docWriter.WriteStartElement(row);
							try
							{
								this.WriteDataRowValues(docWriter, r, rowIndex, sharedStringTable);
							}
							catch (Exception ex)
							{
								var type = r.GetType();
								MethodInfo getIdMethod;
								PropertyInfo entityProperty;
								if (r as DomainModel.DomainObject != null)
								{
									var domainObject = r as DomainModel.DomainObject;
									Tracer.TraceError("Ocurrió un error escribiendo el registro {0} con código {1}: {2}", recordIndex, domainObject.GetID(), ex);
								}
								else if ((getIdMethod = type.GetMethod("GetID", BindingFlags.Instance | BindingFlags.Public)) != null)
								{
									var id = getIdMethod.Invoke(r, null);
									Tracer.TraceError("Ocurrió un error escribiendo el registro {0} con código {1}: {2}", recordIndex, id, ex);
								}
								else if ((entityProperty = type.GetProperty("Entity", BindingFlags.Instance | BindingFlags.Public)) != null)
								{
									var domainObject = entityProperty.GetValue(r) as DomainModel.DomainObject;
									if (domainObject != null)
										Tracer.TraceError("Ocurrió un error escribiendo el registro {0} con código {1}: {2}", recordIndex, domainObject.GetID(), ex);
									else
										Tracer.TraceError("Ocurrió un error escribiendo el registro {0} ({2}): {1}", recordIndex, ex, type);
								}
								else if (r as JToken != null)
								{
									var jToken = r as JToken;
									if (jToken["ID"] != null)
										Tracer.TraceError("Ocurrió un error escribiendo el registro {0} con código {1}: {2}", recordIndex, jToken["ID"].ToString(), ex);
									else
										Tracer.TraceError("Ocurrió un error escribiendo el registro {0} ({2}): {1}", recordIndex, ex, type);
								}
								else
								{
									Tracer.TraceError("Ocurrió un error escribiendo el registro {0} ({2}): {1}", recordIndex, ex, type);
								}

								throw;
							}
							docWriter.WriteEndElement();
							rowIndex++;
							recordIndex++;

							if (this.WriteToLog && recordIndex % 1000 == 0)
								Common.Tracer.TraceVerb("Escribiendo registro {0}", recordIndex);

							if (recordIndex == 1048576)
								break;
						}

						if (this.ShouldIncludeTotalsRow)
						{
							row.RowIndex = rowIndex;
							docWriter.WriteStartElement(row);
							this.WriteDataRowTotals(docWriter, rowIndex);
							docWriter.WriteEndElement();
							rowIndex++;
						}

						tableEndIndex = rowIndex - 1;

						#endregion

						// Sheetdata
						docWriter.WriteEndElement();

						if (tableStartIndex != tableEndIndex)
						{
							docWriter.WriteStartElement(new TableParts() { Count = this.ShouldWriteFilters ? (UInt32) 2 : (UInt32) 1 });

							TablePart tablePart = new TablePart() { Id = "dataTable" };
							docWriter.WriteElement(tablePart);

							if (this.ShouldWriteFilters)
							{
								tablePart = new TablePart() { Id = "filtersTable" };
								docWriter.WriteElement(tablePart);
							}

							docWriter.WriteEndElement();
						}
						else if (this.ShouldWriteFilters)
						{
							docWriter.WriteStartElement(new TableParts() { Count = (UInt32) 1 });

							TablePart tablePart = new TablePart() { Id = "filtersTable" };
							docWriter.WriteElement(tablePart);

							docWriter.WriteEndElement();
						}
					}
					else if ((docReader.IsStartElement))
					{
						//Start elements are directly copied
						docWriter.WriteStartElement(docReader);
					}
					else if ((docReader.IsEndElement))
					{
						//End elements are directly copied
						docWriter.WriteEndElement();
					}
				}

				//Close the reader and writer
				docReader.Close();
				docWriter.Close();

				workbookPart.Workbook.Save();

				if (tableStartIndex != tableEndIndex)
				{
					Table tableData = replacementPart.GenerateDataTable(0, tableStartIndex, (uint) titles.Count() - 1, tableEndIndex, titles, this.ShouldIncludeTotalsRow);
					if (this.ShouldIncludeTotalsRow)
						this.WriteTotals(tableData);
				}

				if (this.ShouldWriteFilters)
					replacementPart.GenerateFiltersTable(0, tableFiltersStartIndex, 1, tableFiltersEndIndex);

				//Get the newly created sheet (same id as the old sheet, but it is the first one)
				Sheet sheet = workbookPart.Workbook.Descendants<Sheet>().Where(s => s.Id.Value.Equals(origninalSheetId)).First();
				//Assign it the new sheet id
				sheet.Id.Value = replacementPartId;
				//remove the old sheet
				workbookPart.DeletePart(worksheetPart);

#if !NETCOREAPP
				document.Close();
#endif
			}
		}

		#endregion

		#region Public Methods

		public void UpdateId(int id)
		{
			this.ID = id;
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado
		/// </summary>
		/// <param name="records">Los registros a exportar</param>
		/// <param name="stream">El <see cref="Stream"/> donde se escribirán los bytes del archivo a generar</param>
		public void Generate<T>(IEnumerable<T> records, Stream stream)
		{
			Generate(records, stream, System.Threading.CancellationToken.None);
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado
		/// </summary>
		/// <param name="records">Los registros a exportar</param>
		/// <param name="stream">El <see cref="Stream"/> donde se escribirán los bytes del archivo a generar</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		public void Generate<T>(IEnumerable<T> records, Stream stream, System.Threading.CancellationToken cancellationToken)
		{
			if (this.Owner == null)
			{
#if !NETCOREAPP
				try
				{
					this.Owner = (DomainModel.User) System.Web.HttpContext.Current.Session["Usuario"];
				}
				catch { }
#endif

				if (this.Owner == null)
					this.Owner = DomainModel.Cache.Instance.GetItem<DomainModel.User>(1);

				if (this.Owner == null)
					this.Owner = new User();
			}

			if (!this.calledBeforeGeneration)
			{
				try
				{
					BeforeGeneration();
				}
				catch { }
				finally
				{
					this.calledBeforeGeneration = true;
				}
			}

			switch (this.Format)
			{
				case ExportFormats.Excel:
					string templateFile = ExcelHelper.GenerateTemporaryTemplateFile(this.Language);
					string filename = Path.GetTempFileName();
					File.Delete(filename);
					try
					{
						File.Copy(templateFile, filename);
					}
					catch (Exception ex)
					{
						if (ex is IOException)
						{
							if (File.Exists(filename))
							{
								File.Delete(filename);
								File.Copy(templateFile, filename);
							}
							else
							{
								throw;
							}
						}
						else
						{
							throw;
						}
					}

					using (SpreadsheetDocument document = SpreadsheetDocument.Open(filename, true))
					{
						GenerateSpreadSheet(document, records, cancellationToken);
					}

					using (FileStream fs = new FileStream(filename, FileMode.Open))
					{
						byte[] buffer = new byte[1024];
						int read = fs.Read(buffer, 0, buffer.Length);
						while (read > 0)
						{
							stream.Write(buffer, 0, read);
							read = fs.Read(buffer, 0, buffer.Length);
						}
					}

					File.Delete(filename);

					break;
				case ExportFormats.CSV:
#if NETCOREAPP
					var configuration = new CsvHelper.Configuration.CsvConfiguration(System.Globalization.CultureInfo.InvariantCulture);
#else
					var configuration = new CsvHelper.Configuration.Configuration();
#endif
					configuration.AllowComments = false;
					configuration.Delimiter = Separator;
					using (MemoryStream ms = new MemoryStream())
					using (StreamWriter sw = new StreamWriter(ms, new UTF8Encoding(true)))
					using (CsvHelper.CsvWriter csvWriter = new CsvHelper.CsvWriter(sw, configuration, false))
					{
						this.Generate<T>(records, csvWriter, cancellationToken);

						csvWriter.Flush();
						sw.Flush();

						ms.Position = 0;
						byte[] buffer = ms.ToArray();
						stream.Write(buffer, 0, buffer.Length);
					}
					break;
				default:
					break;
			}

			cancellationToken.ThrowIfCancellationRequested();
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado
		/// </summary>
		/// <param name="reader">Un <see cref="DataReader"/> con los registros a exportar</param>
		/// <param name="stream">El <see cref="Stream"/> donde se escribirán los bytes del archivo a generar</param>
		public void Generate<T>(DataReader<T> reader, Stream stream) where T : DomainObject
		{
			Generate(reader, stream, System.Threading.CancellationToken.None);
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado
		/// </summary>
		/// <param name="reader">Un <see cref="DataReader"/> con los registros a exportar</param>
		/// <param name="stream">El <see cref="Stream"/> donde se escribirán los bytes del archivo a generar</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		public void Generate<T>(DataReader<T> reader, Stream stream, System.Threading.CancellationToken cancellationToken) where T : DomainObject
		{
			if (this.Owner == null)
			{
#if !NETCOREAPP
				try
				{
					this.Owner = (DomainModel.User) System.Web.HttpContext.Current.Session["Usuario"];
				}
				catch { }
#endif

				if (this.Owner == null)
					this.Owner = DomainModel.Cache.Instance.GetItem<DomainModel.User>(1);

				if (this.Owner == null)
					this.Owner = new User();
			}

			if (!this.calledBeforeGeneration)
			{
				try
				{
					BeforeGeneration();
				}
				catch { }
				finally
				{
					this.calledBeforeGeneration = true;
				}
			}

			switch (this.Format)
			{
				case ExportFormats.Excel:
					string templateFile = ExcelHelper.GenerateTemporaryTemplateFile(this.Language);
					string filename = Path.GetTempFileName();
					File.Delete(filename);
					try
					{
						File.Copy(templateFile, filename);
					}
					catch (Exception ex)
					{
						if (ex is IOException)
						{
							if (File.Exists(filename))
							{
								File.Delete(filename);
								File.Copy(templateFile, filename);
							}
							else
							{
								throw;
							}
						}
						else
						{
							throw;
						}
					}

					using (SpreadsheetDocument document = SpreadsheetDocument.Open(filename, true))
					{
						GenerateSpreadSheet(document, reader, cancellationToken);
					}

					using (FileStream fs = new FileStream(filename, FileMode.Open))
					{
						byte[] buffer = new byte[1024];
						int read = fs.Read(buffer, 0, buffer.Length);
						while (read > 0)
						{
							stream.Write(buffer, 0, read);
							read = fs.Read(buffer, 0, buffer.Length);
						}
					}

					File.Delete(filename);

					break;
				case ExportFormats.CSV:
#if NETCOREAPP
					var configuration = new CsvHelper.Configuration.CsvConfiguration(System.Globalization.CultureInfo.InvariantCulture);
#else
					var configuration = new CsvHelper.Configuration.Configuration();
#endif
					configuration.AllowComments = false;
					configuration.Delimiter = Separator;
					using (MemoryStream ms = new MemoryStream())
					using (StreamWriter sw = new StreamWriter(ms, new UTF8Encoding(true)))
					using (CsvHelper.CsvWriter csvWriter = new CsvHelper.CsvWriter(sw, configuration))
					{
						this.Generate<T>(reader, csvWriter, cancellationToken);

						csvWriter.Flush();
						sw.Flush();

						ms.Position = 0;
						byte[] buffer = ms.ToArray();
						stream.Write(buffer, 0, buffer.Length);
					}
					break;
				default:
					break;
			}

			cancellationToken.ThrowIfCancellationRequested();
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado (cuando se envia por mail por exceder la cantidad permitida)
		/// </summary>
		/// <param name="reader">Un <see cref="DataReader"/> con los registros a exportar</param>
		/// <param name="filename">El nombre del archivo a generar</param>
		public void Generate<T>(DataReader<T> reader, string filename) where T : DomainObject
		{
			Generate(reader, filename, System.Threading.CancellationToken.None);
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado (cuando se envia por mail por exceder la cantidad permitida)
		/// </summary>
		/// <param name="reader">Un <see cref="DataReader"/> con los registros a exportar</param>
		/// <param name="filename">El nombre del archivo a generar</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		public void Generate<T>(DataReader<T> reader, string filename, System.Threading.CancellationToken cancellationToken) where T : DomainObject
		{
			if (this.Owner == null)
			{
#if !NETCOREAPP
				try
				{
					this.Owner = (DomainModel.User) System.Web.HttpContext.Current.Session["Usuario"];
				}
				catch { }
#endif

				if (this.Owner == null)
					this.Owner = DomainModel.Cache.Instance.GetItem<DomainModel.User>(1);

				if (this.Owner == null)
					this.Owner = new User();
			}

			if (!this.calledBeforeGeneration)
			{
				try
				{
					BeforeGeneration();
				}
				catch { }
				finally
				{
					this.calledBeforeGeneration = true;
				}
			}

			switch (this.Format)
			{
				case ExportFormats.Excel:
					string templateFile = ExcelHelper.GenerateTemporaryTemplateFile(this.Language);

					try
					{
						File.Copy(templateFile, filename);
					}
					catch (Exception ex)
					{
						if (ex is IOException)
						{
							if (File.Exists(filename))
							{
								File.Delete(filename);
								File.Copy(templateFile, filename);
							}
							else
							{
								throw;
							}
						}
						else
						{
							throw;
						}
					}

					using (SpreadsheetDocument document = SpreadsheetDocument.Open(filename, true))
					{
						GenerateSpreadSheet(document, reader, cancellationToken);
					}
					break;
				case ExportFormats.CSV:
#if NETCOREAPP
					var configuration = new CsvHelper.Configuration.CsvConfiguration(System.Globalization.CultureInfo.InvariantCulture);
#else
					var configuration = new CsvHelper.Configuration.Configuration();
#endif
					configuration.AllowComments = false;
					configuration.Delimiter = Separator;
					using (StreamWriter sw = new StreamWriter(filename, false, new UTF8Encoding(true)))
					using (CsvHelper.CsvWriter csvWriter = new CsvHelper.CsvWriter(sw, configuration))
					{
						this.Generate<T>(reader, csvWriter, cancellationToken);
					}
					break;
				default:
					break;
			}

			cancellationToken.ThrowIfCancellationRequested();
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado (cuando se envia por mail por exceder la cantidad permitida)
		/// </summary>
		/// <param name="records">Un <see cref="IEnumerable{T}"/> con los registros a exportar</param>
		/// <param name="filename">El nombre del archivo a generar</param>
		/// <typeparam name="T">El tipo de registro a exportar</typeparam>
		public void Generate<T>(IEnumerable<T> records, string filename) where T : DomainObject
		{
			Generate(records, filename, System.Threading.CancellationToken.None);
		}

		/// <summary>
		/// Genera el reporte en el archivo especificado (cuando se envia por mail por exceder la cantidad permitida)
		/// </summary>
		/// <param name="records">Un <see cref="IEnumerable{T}"/> con los registros a exportar</param>
		/// <param name="filename">El nombre del archivo a generar</param>
		/// <param name="cancellationToken">Un <see cref="System.Threading.CancellationToken"/> para cancelar la generación del reporte</param>
		/// <typeparam name="T">El tipo de registro a exportar</typeparam>
		public void Generate<T>(IEnumerable<T> records, string filename, System.Threading.CancellationToken cancellationToken) where T : DomainObject
		{
			if (this.Owner == null)
			{
#if !NETCOREAPP
				try
				{
					this.Owner = (DomainModel.User) System.Web.HttpContext.Current.Session["Usuario"];
				}
				catch { }
#endif

				if (this.Owner == null)
					this.Owner = DomainModel.Cache.Instance.GetItem<DomainModel.User>(1);

				if (this.Owner == null)
					this.Owner = new User();
			}

			if (!this.calledBeforeGeneration)
			{
				try
				{
					BeforeGeneration();
				}
				catch { }
				finally
				{
					this.calledBeforeGeneration = true;
				}
			}

			switch (this.Format)
			{
				case ExportFormats.Excel:
					string templateFile = ExcelHelper.GenerateTemporaryTemplateFile(this.Language);

					try
					{
						File.Copy(templateFile, filename);
					}
					catch (Exception ex)
					{
						if (ex is IOException)
						{
							if (File.Exists(filename))
							{
								File.Delete(filename);
								File.Copy(templateFile, filename);
							}
							else
							{
								throw;
							}
						}
						else
						{
							throw;
						}
					}

					using (SpreadsheetDocument document = SpreadsheetDocument.Open(filename, true))
					{
						GenerateSpreadSheet(document, records, cancellationToken);
					}
					break;
				case ExportFormats.CSV:
#if NETCOREAPP
					var configuration = new CsvHelper.Configuration.CsvConfiguration(System.Globalization.CultureInfo.InvariantCulture);
#else
					var configuration = new CsvHelper.Configuration.Configuration();
#endif
					configuration.AllowComments = false;
					configuration.Delimiter = Separator;
					using (StreamWriter sw = new StreamWriter(filename, false, new UTF8Encoding(true)))
					using (CsvHelper.CsvWriter csvWriter = new CsvHelper.CsvWriter(sw, configuration))
					{
						this.Generate<T>(records, csvWriter, cancellationToken);
					}
					break;
				default:
					break;
			}

			cancellationToken.ThrowIfCancellationRequested();
		}

		/// <summary>
		/// Devuelve los valores de la configuración del reporte para ser mostrados en pantalla
		/// </summary>
		/// <returns>Un diccionario con los nombres de los campos y sus valores</returns>
		public Dictionary<string, string> GetConfigurationInfo()
		{
			return GetConfigurationInfo(this.Language, this.Owner);
		}

		/// <summary>
		/// Devuelve los valores de la configuración del reporte para ser mostrados en pantalla
		/// </summary>
		/// <param name="language">Indica el lenguage en el cual se obtiene la información</param>
		/// <param name="user">El usuario que está solicitando la información</param>
		/// <returns>Un diccionario con los nombres de los campos y sus valores</returns>
		public abstract Dictionary<string, string> GetConfigurationInfo(string language, DomainModel.User user);

		#endregion

		#region Static Methods

		public static ReportExport Create(ReportTypes type)
		{
			return Create(type, null, null, ExportFormats.Excel);
		}

		public static ReportExport Create(ReportTypes type, int ownerId, ExportFormats format)
		{
			User owner = null;
			if (ownerId != -1)
				owner = new User() { ID = ownerId };

			return Create(type, owner, null, format);
		}

		public static ReportExport Create(IDataRecord record)
		{
			var type = (ReportTypes) (short) record["IdReportType"];

			var ownerId = (int) record["IdUser"];
			User owner = new User()
			{
				ID = ownerId
			};

			try
			{
				owner = DomainModel.Cache.Instance.GetItem<DomainModel.User>(owner.ID);
			}
			catch { }

			DateTime dateSolicited = record.GetValue<DateTime>("DateSolicited");
			DateTime? dateGenerated = null;
			if (record["DateGenerated"] != DBNull.Value)
				dateGenerated = record.GetValue<DateTime>("DateGenerated");
			string configuration = (string) record["Configuration"];
			bool generated = (bool) record["Generated"];
			ExportFormats format = (ExportFormats) (byte) record["IdReportExportFormat"];
			string filename = null;
			if (record["Filename"] != DBNull.Value)
				filename = (string) record["Filename"];

			DateTime? dateStarted = null;
			try
			{
				if (record["DateStarted"] != DBNull.Value)
					dateStarted = record.GetValue<DateTime?>("DateStarted", null);
			}
			catch { }

			DateTime? dateInterval = null;
			try
			{
				if (record["DateInterval"] != DBNull.Value)
					dateInterval = record.GetValue<DateTime?>("DateInterval", null);
			}
			catch { }

			ReportExport r = Create(type, owner, configuration, format);
			r.ID = (int) record["IdReportExport"];
			r.DateGenerated = dateGenerated;
			r.DateSolicited = dateSolicited;
			r.DateStarted = dateStarted;
			r.DateInterval = dateInterval;
			r.Generated = generated;
			r.Periodicity = (ReportScheduled.ExportPeriodicity?) record.GetValue<byte?>("Periodicity", null);
			if (string.IsNullOrEmpty(r.Owner.TimeZoneId) && !string.IsNullOrEmpty(r.OwnerTimeZone))
			{
				r.Owner.TimeZoneId = r.OwnerTimeZone;
				r.Owner.TimeZoneOffset = TimeZoneInfo.FindSystemTimeZoneById(r.OwnerTimeZone).BaseUtcOffset;
			}
			if (record["IdReportScheduled"] != DBNull.Value)
			{
				r.IdScheduled = (int) record["IdReportScheduled"];
			}
			else
			{
				r.Filename = filename;
			}

			if (record["ReportExportStatusID"] == DBNull.Value)
			{
				r.Status = r.Generated ? ReportExportStatus.Finished : ReportExportStatus.Pending;
				r.Result = r.Generated ? (ReportExportResults?) ReportExportResults.Success : (ReportExportResults?) null;
			}
			else
			{
				r.Status = (ReportExportStatus) record.GetValue<short>("ReportExportStatusID");
				r.Result = (ReportExportResults?) record.GetValue<byte?>("ReportExportResultID", null);
			}

			if (record["BlobUrl"] != DBNull.Value)
				r.BlobUrl = (string) record["BlobUrl"];

			if (record["BlobPath"] != DBNull.Value)
				r.BlobPath = (string) record["BlobPath"];

			if (record["Daily"] != DBNull.Value)
				r.Daily = (bool) record["Daily"];

			var parameters = record.GetValue<string>("Parameters", null);
			if (!string.IsNullOrEmpty(parameters))
				r.Parameters.ParseString(parameters);

			return r;
		}

		public static ReportExport Create(ReportTypes type, User owner, string configuration, ExportFormats format)
		{
			ReportExport reportConfiguration;
			switch (type)
			{
				case ReportTypes.Cases:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new CasesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<CasesExport>(configuration);
					break;
				case ReportTypes.Messages:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new MessagesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<MessagesExport>(configuration);
					break;
				case ReportTypes.SocialUsers:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new SocialUsersExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<SocialUsersExport>(configuration);
					break;
				case ReportTypes.DetailedByQueue:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DetailedByQueueExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DetailedByQueueExport>(configuration);
					break;
				//case ReportTypes.MessagesByQueue:
				//	if (string.IsNullOrEmpty(configuration))
				//		reportConfiguration = new MessagesByQueueExport();
				//	else
				//		reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<MessagesByQueueExport>(configuration);
				//	break;
				//case ReportTypes.ServicesByQueue:
				//	if (string.IsNullOrEmpty(configuration))
				//		reportConfiguration = new ServicesByQueueExport();
				//	else
				//		reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ServicesByQueueExport>(configuration);
				//	break;
				case ReportTypes.TagsByQueue:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new TagsByQueueExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<TagsByQueueExport>(configuration);
					break;
				case ReportTypes.DetailedByAgent:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DetailedByAgentExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DetailedByAgentExport>(configuration);
					break;
				//case ReportTypes.MessagesByAgent:
				//	if (string.IsNullOrEmpty(configuration))
				//		reportConfiguration = new MessagesByAgentExport();
				//	else
				//		reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<MessagesByAgentExport>(configuration);
				//	break;
				//case ReportTypes.ServicesByAgent:
				//	if (string.IsNullOrEmpty(configuration))
				//		reportConfiguration = new ServicesByAgentExport();
				//	else
				//		reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ServicesByAgentExport>(configuration);
				//	break;
				case ReportTypes.TagsByAgent:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new TagsByAgentExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<TagsByAgentExport>(configuration);
					break;
				case ReportTypes.SessionsByAgent:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new SessionsByAgentExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<SessionsByAgentExport>(configuration);
					break;
				case ReportTypes.Chats:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new ChatsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ChatsExport>(configuration);
					break;
				case ReportTypes.Surveys:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new SurveysExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<SurveysExport>(configuration);
					break;
				case ReportTypes.DailyByCase:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DailyByCaseExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DailyByCaseExport>(configuration);
					break;
				case ReportTypes.UsersLog:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new UsersLogExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<UsersLogExport>(configuration);
					break;
				case ReportTypes.Users:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new UsersExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<UsersExport>(configuration);
					break;
				case ReportTypes.Agents:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new AgentsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<AgentsExport>(configuration);
					break;
				case ReportTypes.AgentSession:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new SessionByAgentExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<SessionByAgentExport>(configuration);
					break;
				//case ReportTypes.Conversations:
				//	if (string.IsNullOrEmpty(configuration))
				//		reportConfiguation = new ConversationsExport();
				//	else
				//		reportConfiguation = Newtonsoft.Json.JsonConvert.DeserializeObject<ConversationsExport>(configuration);
				//	break;
				case ReportTypes.DetailedByService:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DetailedByServiceExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DetailedByServiceExport>(configuration);
					break;
				case ReportTypes.Tags:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new TagsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<TagsExport>(configuration);
					break;
				case ReportTypes.WhiteList:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new WhiteListExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<WhiteListExport>(configuration);
					break;
				case ReportTypes.BlackList:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new BlackListExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<BlackListExport>(configuration);
					break;
				case ReportTypes.TesterList:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new TesterListExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<TesterListExport>(configuration);
					break;
				case ReportTypes.DoNotCallList:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DoNotCallListExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DoNotCallListExport>(configuration);
					break;
				case ReportTypes.DetailedWhatsappHSM:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DetailedWhatsappHSMExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DetailedWhatsappHSMExport>(configuration);
					break;
				case ReportTypes.ConsolidatedWhatsappHSM:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new ConsolidatedWhatsappHSMExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ConsolidatedWhatsappHSMExport>(configuration);
					break;
				case ReportTypes.WhatsappHSMTasks:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new WhatsappHSMTasksExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<WhatsappHSMTasksExport>(configuration);
					break;
				case ReportTypes.WhatsappHSMRequestTasks:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new WhatsappHSMRequestTasksExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<WhatsappHSMRequestTasksExport>(configuration);
					break;
				case ReportTypes.ConsolidatedSurvey:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new DetailedBySurveyExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DetailedBySurveyExport>(configuration);
					break;
				case ReportTypes.ChatMessages:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new ChatMessagesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ChatMessagesExport>(configuration);
					break;
				case ReportTypes.AdherenceConsolidated:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new AdherenceConsolidatedExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<AdherenceConsolidatedExport>(configuration);
					break;
				case ReportTypes.AdherenceDetailed:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new AdherenceDetailedExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<AdherenceDetailedExport>(configuration);
					break;
				case ReportTypes.SocialUserProfiles:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new SocialUserProfilesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<SocialUserProfilesExport>(configuration);
					break;
				case ReportTypes.UserSessions:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new UserSessionsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<UserSessionsExport>(configuration);
					break;
				case ReportTypes.MessagesTransfers:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new MessagesTransfersExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<MessagesTransfersExport>(configuration);
					break;
				case ReportTypes.CasesReopenings:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new CasesReopeningsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<CasesReopeningsExport>(configuration);
					break;
				case ReportTypes.ChatsMessages:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new ChatsMessagesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<ChatsMessagesExport>(configuration);
					break;
				case ReportTypes.MessagesSegments:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new MessagesSegmentsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<MessagesSegmentsExport>(configuration);
					break;
				case ReportTypes.Queues:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new QueueExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<QueueExport>(configuration);
					break;
				case ReportTypes.WhatsappHSMWithoutCase:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new WhatsappHSMWithoutCase();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<WhatsappHSMWithoutCase>(configuration);
					break;
				case ReportTypes.AgentQueues:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new AgentQueuesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<AgentQueuesExport>(configuration);
					break;
				case ReportTypes.RTAgents:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new RTAgentsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<RTAgentsExport>(configuration);
					break;

				case ReportTypes.CasesTimes:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new CasesTimesExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<CasesTimesExport>(configuration);
					break;
				case ReportTypes.VideoCalls:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new VideoCallsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<VideoCallsExport>(configuration);
					break;
				case ReportTypes.UsersLogWithoutParameters:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new UsersLogWithoutParametersExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<UsersLogWithoutParametersExport>(configuration);
					break;
				case ReportTypes.Calls:
					if (string.IsNullOrEmpty(configuration))
						reportConfiguration = new CallsExport();
					else
						reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<CallsExport>(configuration);
					break;
				default:
					throw new ArgumentOutOfRangeException(nameof(type));
			}

			reportConfiguration.Owner = owner;
			reportConfiguration.Generated = false;
			reportConfiguration.DateGenerated = null;
			reportConfiguration.DateStarted = null;
			reportConfiguration.DateSolicited = DateTime.Now;
			reportConfiguration.Format = format;
			return reportConfiguration;
		}

		#endregion
	}
}