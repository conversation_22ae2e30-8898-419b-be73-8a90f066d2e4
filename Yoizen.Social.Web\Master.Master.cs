﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using CsQuery.ExtensionMethods.Internal;
using DocumentFormat.OpenXml.Math;
using RestSharp;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.Web
{
	public partial class Master : System.Web.UI.MasterPage
	{
		#region Fields

		private Dictionary<string, string> scripts = new Dictionary<string, string>();

		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			linkFavIcon.Attributes["href"] = Helpers.Fingerprint.Tag(string.Format("{0}/{1}/favicon.ico", ResolveUrl("~/App_Themes"), Page.Theme));
			literalStyleColorbox.Text = string.Format("<link href='{0}' rel='stylesheet' type='text/css' />", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/colorbox.css")));
			literalStylePopup.Text = string.Format("<link href='{0}' rel='stylesheet' type='text/css' />", Helpers.Fingerprint.Tag(ResolveUrl("~/Styles/pnotify.custom.css")));

			literalScriptJQuery.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery-3.5.1.min.js")));
			literalScriptMaster.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/Master.js")));
			literalScriptCryptoJs.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/crypto-js.min.js")));
			literalScriptColorbox.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.colorbox-min.js")));
			literalScriptMenu.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/Dynamic/menu.js")));
			literalScriptEnums.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/Dynamic/enums.js")));
			literalScriptTimeZones.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/Dynamic/timezones.js")));
			literalScriptSocialServiceTypes.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/Dynamic/socialservicetypes.js")));
			literalScriptJSON.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/json2.js")));
			literalScriptTooltip.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/tooltip.js")));
			literalScriptPopover.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/popover.js")));
			literalScriptTitleNotifier.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/title_notifier.js")));
			literalScriptSignalR.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/signalr.min.js")));
			literalScriptPopup.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/pnotify.custom.js")));
			literalScriptRTNotifications.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/RTNotifications.js")));
			literalScriptMomentJs.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/moment-with-langs.js")));
			literalScriptMomentTimeZoneJs.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/moment-timezone-with-data.js")));
			literalScriptDomToImage.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/dom-to-image.min.js")));
			literalScriptHE.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/he.js")));
			literalScriptNumeralJs.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/numeral.min.js")));
			literalScriptJQueryi18n.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.js")));
			literalScriptJQueryi18n_messagestore.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.messagestore.js")));
			literalScriptJQueryi18n_fallbacks.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.fallbacks.js")));
			literalScriptJQueryi18n_parser.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.parser.js")));
			literalScriptJQueryi18n_emitter.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.emitter.js")));
			literalScriptJQueryi18n_language.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/jquery.i18n.language.js")));
			literalScriptPluralRuleParser.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/CLDRPluralRuleParser.js")));

			//literalLinki18nEs.Text = string.Format("<link rel='preload' href='{0}' as='fetch' />", Helpers.Fingerprint.Tag(ResolveUrl("~/i18n/es.json")));
			//literalLinki18nEn.Text = string.Format("<link rel='preload' href='{0}' as='fetch' />", Helpers.Fingerprint.Tag(ResolveUrl("~/i18n/es.json")));
			//literalLinki18nPt.Text = string.Format("<link rel='preload' href='{0}' as='fetch' />", Helpers.Fingerprint.Tag(ResolveUrl("~/i18n/pt.json")));

			if (Licensing.LicenseManager.Instance.IsLicenseValid && Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
			{
				literalScriptJQuery.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js' integrity='sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA==' crossorigin='anonymous' referrerpolicy='no-referrer'></script>";
				literalScriptCryptoJs.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.0.0/crypto-js.min.js' integrity='sha256-6rXZCnFzbyZ685/fMsqoxxZz/QZwMnmwHg+SsNe+C/w=' crossorigin='anonymous'></script>";
				literalScriptTwemoji.Text = "<script type='text/javascript' src='https://cdn.jsdelivr.net/npm/@twemoji/api@14.1.2/dist/twemoji.min.js' integrity='sha256-Wcpm07OAJ/kBIMfbHkm+V01y+ZFK6O8CJXcUNYpCnjE=' crossorigin='anonymous' ></script>";
				literalScriptMomentJs.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment-with-locales.min.js' crossorigin='anonymous'></script>";
				literalScriptMomentTimeZoneJs.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.43/moment-timezone-with-data.min.js' crossorigin='anonymous'></script>";
				literalScriptDomToImage.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/html-to-image/1.11.11/html-to-image.min.js' crossorigin='anonymous'></script>";
				literalScriptNumeralJs.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js' crossorigin='anonymous'></script>";

				if (System.IO.File.Exists(Server.MapPath("~/Scripts/cloud.json")))
				{
					var jCloudScripts = Newtonsoft.Json.Linq.JArray.Parse(System.IO.File.ReadAllText(Server.MapPath("~/Scripts/cloud.json")));
					foreach (var jCloudScriptToken in jCloudScripts)
					{
						if (jCloudScriptToken.Type == Newtonsoft.Json.Linq.JTokenType.Object)
						{
							var jCloudScript = jCloudScriptToken as Newtonsoft.Json.Linq.JObject;

							if (jCloudScript["src"] != null && jCloudScript["src"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							{
								var scriptAttributes = new Dictionary<string, string>();

								var src = jCloudScript["src"].ToString();
								scriptAttributes["src"] = src;

								string key;

								if (jCloudScript["key"] != null && jCloudScript["key"].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									key = jCloudScript["key"].ToString();
								}
								else
								{
									if (src.IndexOf("jquery") >= 0)
										key = "jquery";
									else if (src.IndexOf("crypto-js") >= 0)
										key = "crypto-js";
									else if (src.IndexOf("twemoji") >= 0)
										key = "twemoji";
									else if (src.IndexOf("moment-timezone") >= 0)
										key = "moment-timezone";
									else if (src.IndexOf("moment") >= 0)
										key = "moment";
									else if (src.IndexOf("html-to-image") >= 0)
										key = "html-to-image";
									else if (src.IndexOf("numeral") >= 0)
										key = "numeral";
									else
										key = "other";
								}

								if (jCloudScript["integrity"] != null && jCloudScript["integrity"].Type == Newtonsoft.Json.Linq.JTokenType.String)
									scriptAttributes["integrity"] = jCloudScript["integrity"].ToString();

								if (jCloudScript["crossorigin"] != null && jCloudScript["crossorigin"].Type == Newtonsoft.Json.Linq.JTokenType.String)
									scriptAttributes["crossorigin"] = jCloudScript["crossorigin"].ToString();
								else
									scriptAttributes["crossorigin"] = "anonymous";

								if (jCloudScript["referrerpolicy"] != null && jCloudScript["referrerpolicy"].Type == Newtonsoft.Json.Linq.JTokenType.String)
									scriptAttributes["referrerpolicy"] = jCloudScript["referrerpolicy"].ToString();

								foreach (var jProperty in jCloudScript.Properties())
								{
									var name = jProperty.Name.ToLower();
									if (!name.Equals("src") && 
										!name.Equals("key") &&
										!name.Equals("integrity") &&
										!name.Equals("crossorigin") &&
										!name.Equals("referrerpolicy") &&
										name.IndexOf(" ") == 1)
									{
										scriptAttributes[name] = jProperty.Value.ToString();
									}
								}

								var scriptBuilder = new StringBuilder("<script ");
								foreach (var scriptAttribute in scriptAttributes)
								{
									scriptBuilder.AppendFormat("{0}='{1}' ", scriptAttribute.Key, scriptAttribute.Value);
								}
								scriptBuilder.AppendLine("></script>");

								switch (key)
								{
									case "jquery":
										literalScriptJQuery.Text = scriptBuilder.ToString();
										break;
									case "crypto-js":
										literalScriptCryptoJs.Text = scriptBuilder.ToString();
										break;
									case "twemoji":
										literalScriptTwemoji.Text = scriptBuilder.ToString();
										break;
									case "moment-timezone":
										literalScriptMomentTimeZoneJs.Text = scriptBuilder.ToString();
										break;
									case "moment":
										literalScriptMomentJs.Text = scriptBuilder.ToString();
										break;
									case "html-to-image":
										literalScriptDomToImage.Text = scriptBuilder.ToString();
										break;
									case "numeral":
										literalScriptNumeralJs.Text = scriptBuilder.ToString();
										break;
									default:
										literalScriptOthersCloud.Text += scriptBuilder.ToString();
										break;
								}
							}
							
						}
						else if (jCloudScriptToken.Type == Newtonsoft.Json.Linq.JTokenType.String)
						{
							var script = jCloudScriptToken.ToString();
							if (script.EndsWith(".js"))
							{
								literalScriptOthersCloud.Text += string.Format("<script src='{0}' crossorigin='anonymous'></script>\r\n", script);
							}
							else if (script.StartsWith("<script"))
							{
								literalScriptOthersCloud.Text += script;
							}
						}
					}
				}

				placeholderCloudCss.Visible = true;
			}
			else
			{
				literalScriptTwemoji.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/twemoji.min.js")));
			}

			this.RegisterJsonVariable("theme", Page.Theme);
			this.RegisterJsonVariable("version", Application["SocialVersion"].ToString());

			User user = null;
			try
			{
				user = Session["Usuario"] as User;
			}
			catch { }

			if (!Page.IsPostBack)
			{
				literalYear.Text = DateTime.Now.Year.ToString();

				if (user != null)
				{
					panelYFlowContingencyLink.Visible = Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled
						&& DomainModel.SystemStatus.Instance.ContingencyBot.AllowContingencyBot;

                    panelYUsage.Visible = Licensing.LicenseManager.Instance.License.Configuration.AllowYUsage
                                    && DomainModel.SystemSettings.Instance.YUsage.Enabled
                                    && !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.YUsage.Url)
                                    && !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.YUsage.UrlApi)
                                    && !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.YUsage.AccessToken)
                                    && user.Permissions.Any(p => p.ID == Permissions.YUsageAccess.GetValue());
                }
			}			

			if (!string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.DefaultLanguage))
			{
				var locale = DomainModel.SystemSettings.Instance.DefaultLanguage;
				if (locale.IndexOf("-") >= 0)
					locale = locale.Substring(0, locale.IndexOf("-"));
				this.html.Attributes["lang"] = locale;
			}

			if (user != null)
			{
				if (user.HasPermission(Permissions.SystemStatus))
					this.RegisterJsonVariable("canAccessSystemStatus", true);

				if (user.Settings.ContainsKey(DomainModel.User.Locale))
				{
					var locale = user.Settings[DomainModel.User.Locale];
					if (locale.IndexOf("-") >= 0)
						locale = locale.Substring(0, locale.IndexOf("-"));
					this.html.Attributes["lang"] = locale;
				}

				this.RegisterJsonVariable("notificationsPageURL", ResolveUrl("~/Supervisor/Notifications.aspx"));
			}
		}

		protected override void RenderChildren(HtmlTextWriter writer)
		{
			var scriptsBuilder = new StringBuilder();
			foreach (var item in this.scripts)
			{
				scriptsBuilder.Append(item.Value);
			}

			this.Page.ClientScript.RegisterStartupScript(typeof(BasePage)
				, "mastervariablesregistration"
				, string.Format("<script type='text/javascript' language='javascript'>{0}</script>\r\n", scriptsBuilder.ToString()));

			base.RenderChildren(writer);
		}

		#region Properties

		public bool HasLeftColumn
		{
			get
			{
				if (ViewState["HasLeftColumn"] == null)
					return false;
				return (bool) ViewState["HasLeftColumn"];
			}
			set
			{
				ViewState["HasLeftColumn"] = value;

				if (value)
				{
					this.body.Attributes["class"] = "fbx hasLeftCol";
					this.html.Attributes["class"] = "hasLeftCol";
				}
				else
				{
					this.body.Attributes["class"] = "fbx";
				}

				leftColContainer.Visible = value;
			}
		}

		#endregion

		#region Protected Methods

		protected void RegisterJsonVariable(string variableName, object variableValue)
		{
			string json = Newtonsoft.Json.JsonConvert.SerializeObject(variableValue);
			//this.Page.ClientScript.RegisterStartupScript(typeof(BasePage)
			//	, string.Format("{0}registration", variableName)
			//	, string.Format("<script type='text/javascript' language='javascript'>var {0} = {1};</script>\r\n", variableName, json));
			this.scripts[string.Format("{0}registration", variableName)] = string.Format("var {0} = {1};\r\n", variableName, json);
		}

		#endregion
	}
}
