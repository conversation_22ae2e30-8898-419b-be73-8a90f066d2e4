﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Web;
using System.Threading.Tasks;

namespace FacebookCallback.Controllers
{
	public class MercadoLibreController : ApiController
	{
		#region Constants

		private static readonly string SecretKeyForHashing = "b29780205f1d02e838cf75ff19f64c76";
		
		#endregion

		#region Constructors

		#endregion

		#region Action Methods

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare) 
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			return Request.CreateResponse(HttpStatusCode.OK, new MercadoLibreManager().GetPageLastNews(id));
		}

		// POST: api/MercadoLibre
		[ActionName("DefaultAction")]
		[HttpPost]
		public async Task<HttpResponseMessage> Post()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				MercadoLibreManager mercadoLibreManager = new MercadoLibreManager();
				mercadoLibreManager.GenerateFiles(body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("NewsProcessed")]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			try
			{
				MercadoLibreManager mercadoLibreManager = new MercadoLibreManager();
				mercadoLibreManager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("Restart")]
		[HttpDelete]
		public HttpResponseMessage Restart(string id)
		{
			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se invocó la eliminación de los archivos de novedades de la página {0}", id);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			try
			{
				MercadoLibreManager mercadoLibreManager = new MercadoLibreManager();
				mercadoLibreManager.DeleteAllContents(id);

				Yoizen.Common.Tracer.TraceInfo("Se eliminó los archivos de novedades de la página {0}", id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de novedades de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		#endregion

		#region Private Methods & Utils

		private static string Encode(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (HMACSHA1 myhmacsha1 = new HMACSHA1(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				return myhmacsha1.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		#endregion
	}
}