﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Web;
using System.Threading.Tasks;

namespace FacebookCallback.Controllers
{
	public class WhatsappController : ApiController
	{
		#region Constants

		private static readonly string SecretKeyForHashing = "b29780205f1d02e838cf75ff19f64c76";
		private static readonly string SecretKeyForHashingBotMaker = "ZjrepWGRC7dprfQcVJkHt9UOvxmXb9rlPtmw5qrcGVk=";

		#endregion

		#region Constructors

		static WhatsappController()
		{
			var botMakerSignatureKey = System.Configuration.ConfigurationManager.AppSettings["BotMakerSignatureKey"];
			if (!string.IsNullOrEmpty(botMakerSignatureKey))
			{
				SecretKeyForHashingBotMaker = botMakerSignatureKey;
				Yoizen.Common.Tracer.TraceInfo("Se utilizará la clave de botmaker {0}", SecretKeyForHashingBotMaker);
			}
		}

		#endregion

		#region Action Methods

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			
			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare) 
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			if (HttpContext.Current.Application["LastWhatsappFoldersProcess"] != null)
			{
				HttpContext.Current.Application.Lock();

				var date = (DateTime) HttpContext.Current.Application["LastWhatsappFoldersProcess"];
				if (DateTime.Now.Date > date.Date)
				{
					try
					{
						WhatsappManager.ProcessFolders();
						HttpContext.Current.Application["LastWhatsappFoldersProcess"] = DateTime.Now.Date;
					}
					catch (Exception ex)
					{
						Yoizen.Common.Tracer.TraceError("Ocurrió un error manteniendo las carpetas: {0}", ex);
					}
				}

				HttpContext.Current.Application.UnLock();
			}


			return Request.CreateResponse(HttpStatusCode.OK, new WhatsappManager().GetPageLastNews(id));
		}

		[ActionName("botmaker")]
		[Route("api/whatsapp/botmaker/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostBotMakerWithId(string id)
		{
			var uri = Request.RequestUri.ToString();
			if (uri.Equals("https://fbsocial.azurewebsites.net/api/whatsapp/botmaker/4Z4W4PJ2PQJZ40TXVRID"))
			{
				Yoizen.Common.Tracer.TraceInfo("Ignorando request: {0} {1}", Request.Method, uri);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid account"
				});
			}

			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, uri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, uri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();

				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				var isStatus = false;
				if (jBody["STATUS"] != null && jBody["LAST_MESSAGE"] != null && jBody["LAST_MESSAGE"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					isStatus = true;
				}

				await whatsappManager.GenerateFiles(jBody, "botmaker", isStatus);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de botmaker {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("botmaker")]
		[Route("api/whatsapp/botmaker")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostBotMaker()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			if (!Request.Headers.Contains("X-Hub-Signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque no tiene header X-Hub-Signature", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header"
				});
			}

			string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
			if (!xHubSignatureHeader.StartsWith("sha1="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header"
				});
			}
			xHubSignatureHeader = xHubSignatureHeader.Substring(5);

			string bodyHash = Encode(body, SecretKeyForHashingBotMaker);

			if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
			{
				if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque el header X-Hub-Signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
				else
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header"
				});
			}

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();

				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				var isStatus = false;
				if (jBody["STATUS"] != null && jBody["LAST_MESSAGE"] != null && jBody["LAST_MESSAGE"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					isStatus = true;
				}

				await whatsappManager.GenerateFiles(jBody, "botmaker", isStatus);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de botmaker {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("postback")]
		[Route("api/whatsapp/postback/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostPostbackWithId(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			if (Request.Headers.Contains("X-Hub-Signature"))
			{
				string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
				if (!xHubSignatureHeader.StartsWith("sha1="))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
				xHubSignatureHeader = xHubSignatureHeader.Substring(5);

				var keyForHashing = CalculateSha256(id);

				string bodyHash = Encode(body, keyForHashing);

				if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
				{
					Yoizen.Common.Tracer.TraceInfo("Botmaker (con id={1}) el header X-Hub-Signature={0}", xHubSignatureHeader, id);

					if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback (con id) {0} porque el header X-Hub-Signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
					else
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback (con id) {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
			}

			try
			{
				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				if (jBody["account"] == null)
					jBody["account"] = id;
				WhatsappManager whatsappManager = new WhatsappManager();
				await whatsappManager.GenerateFiles(jBody, "postback", false);

				return new HttpResponseMessage(HttpStatusCode.OK);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de postback {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}
		}

		[ActionName("bf")]
		[Route("api/whatsapp/bf/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostBotFramework(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			if (!Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase) && !Request.Headers.Contains("X-Hub-Signature"))
			{
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "missing signature header"
				});
			}

			if (Request.Headers.Contains("X-Hub-Signature"))
			{
				string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
				if (!xHubSignatureHeader.StartsWith("sha1=") && !xHubSignatureHeader.StartsWith("sha256="))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback {0} porque el header X-Hub-Signature no empieza con sha256= ni con sha1=, si no que fue {1}", body, xHubSignatureHeader);
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
				
				var keyForHashing = CalculateSha256(id);
				string bodyHash;

				if (xHubSignatureHeader.StartsWith("sha1="))
				{
					xHubSignatureHeader = xHubSignatureHeader.Substring(5);
					bodyHash = Encode(body, keyForHashing);
				}
				else
				{
					xHubSignatureHeader = xHubSignatureHeader.Substring(7);
					bodyHash = Encode256(body, keyForHashing);
				}

				if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
				{
					Yoizen.Common.Tracer.TraceInfo("BotFramework (con id={1}) el header X-Hub-Signature={0}", xHubSignatureHeader, id);

					if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback (con id) {0} porque el header X-Hub-Signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
					else
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update de postback (con id) {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
			}

			Newtonsoft.Json.Linq.JObject jBody = null;
			try
			{
				jBody = Newtonsoft.Json.Linq.JObject.Parse(body);

				if (jBody["type"] == null || jBody["type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid json contents"
					});
				}

				var type = jBody["type"].ToString();
				if (!type.Equals("message", StringComparison.InvariantCultureIgnoreCase) &&
					!type.Equals("handoff", StringComparison.InvariantCultureIgnoreCase) &&
					!type.Equals("event", StringComparison.InvariantCultureIgnoreCase))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid json contents. only 'message' or 'handoff' are accepted for type"
					});
				}

				string eventName = null;
				if (type.Equals("event"))
				{
					if (jBody["name"] == null || jBody["name"].Type != Newtonsoft.Json.Linq.JTokenType.String)
					{
						return Request.CreateResponse(HttpStatusCode.BadRequest, new
						{
							error = "invalid json contents. name is required fo type=event"
						});
					}

					eventName = jBody["name"].ToString();
					if (!eventName.Equals("surveyRequest", StringComparison.InvariantCultureIgnoreCase))
					{
						return Request.CreateResponse(HttpStatusCode.BadRequest, new
						{
							error = "invalid json contents. only 'surveyRequest' is accepted for type=event"
						});
					}
				}

				var jContents = new Newtonsoft.Json.Linq.JObject();
				jContents["account"] = id;
				jContents["chat"] = jBody["conversation"]["id"].ToString();
				jContents["from"] = new Newtonsoft.Json.Linq.JObject();
				jContents["from"]["id"] = jBody["from"]["id"].ToString();
				jContents["from"]["name"] = jBody["from"]["name"].ToString();
				jContents["msg"] = new Newtonsoft.Json.Linq.JObject();
				if (type.Equals("message", StringComparison.InvariantCultureIgnoreCase))
					jContents["msg"]["type"] = "chat";
				else if (type.Equals("handoff", StringComparison.InvariantCultureIgnoreCase))
					jContents["msg"]["type"] = "handoff";
				else
					jContents["msg"]["type"] = "survey";
				jContents["msg"]["id"] = jBody["id"].ToString();
				if (jBody["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(DateTime.Parse(jBody["timestamp"].ToString()));
					jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(DateTime.Parse(jBody["timestamp"].ToString()));
				}
				else if (jBody["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.Date)
				{
					jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(jBody["timestamp"].ToObject<DateTime>());
					jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(jBody["timestamp"].ToObject<DateTime>());
				}
				else
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid json contents"
					});
				}

				var withBody = false;
				var withAttachment = false;

				if (jBody["text"] != null && jBody["text"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					jContents["msg"]["body"] = jBody["text"].ToString();
					withBody = true;
				}

				if (jBody["attachments"] != null && jBody["attachments"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
				{
					var jAttachments = (Newtonsoft.Json.Linq.JArray) jBody["attachments"];
					if (jAttachments.Count == 1 && jAttachments[0].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jAttach = (Newtonsoft.Json.Linq.JObject) jAttachments[0];
						var contentType = jAttach["contentType"].ToString();
						if (contentType.StartsWith("image/", StringComparison.InvariantCultureIgnoreCase))
						{
							jContents["msg"]["type"] = "image";
							if (withBody)
							{
								jContents["msg"]["body"] = null;
								jContents["msg"]["caption"] = jBody["text"].ToString();
							}
						}
						else if (contentType.StartsWith("video/", StringComparison.InvariantCultureIgnoreCase))
						{
							jContents["msg"]["type"] = "video";
						}
						else if (contentType.StartsWith("audio/", StringComparison.InvariantCultureIgnoreCase))
						{
							jContents["msg"]["type"] = "audio";
						}
						else
						{
							jContents["msg"]["type"] = "document";
						}

						jContents["msg"]["mimeType"] = contentType;
						jContents["msg"]["url"] = jAttach["contentUrl"].ToString();

						withAttachment = true;
					}
				}

				if (!withBody && !withAttachment && type.Equals("message", StringComparison.InvariantCultureIgnoreCase))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid json contents. either attachment or body must be suplied"
					});
				}

				//var jPayload = new Newtonsoft.Json.Linq.JObject();
				//jContents["msg"]["payload"] = jPayload;
				//jPayload["serviceUrl"] = jBody["serviceUrl"].ToString();
				//if (jBody["replyToId"] != null && jBody["replyToId"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				//	jPayload["replyToId"] = jBody["replyToId"].ToString();
				//jPayload["channelId"] = jBody["channelId"].ToString();

				if (jBody["channelData"] != null && jBody["channelData"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					var jChannelData = (Newtonsoft.Json.Linq.JObject) jBody["channelData"];

					if (jChannelData["handoffMotive"] != null && jChannelData["handoffMotive"].Type == Newtonsoft.Json.Linq.JTokenType.String)
					{
						var jPayload = new Newtonsoft.Json.Linq.JObject();
						jContents["msg"]["payload"] = jPayload;
						jPayload["handoffMotive"] = jChannelData["handoffMotive"].ToString();
					}

					if (jChannelData["user"] != null && jChannelData["user"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						jChannelData = (Newtonsoft.Json.Linq.JObject) jChannelData["user"];

						var jExt = new Newtonsoft.Json.Linq.JObject();
						jContents["from"]["ext"] = jExt;

						var isClient = false;
						if (jChannelData["isClient"] != null && jChannelData["isClient"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
						{
							isClient = jChannelData["isClient"].ToObject<bool>();
							jExt["isClient"] = isClient;
						}

						if (jChannelData["userId"] != null && jChannelData["userId"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							jExt["userId"] = jChannelData["userId"].ToString();
						if (jChannelData["userIdSystem"] != null && jChannelData["userIdSystem"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							jExt["userIdSystem"] = jChannelData["userIdSystem"].ToString();
						if (jChannelData["accountType"] != null && jChannelData["accountType"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							jExt["accountType"] = jChannelData["accountType"].ToString();
						if (jChannelData["contact"] != null && jChannelData["contact"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							jExt["contact"] = jChannelData["contact"].ToString();
						if (jChannelData["business"] != null && jChannelData["business"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							jExt["business"] = jChannelData["business"].ToString();

						if (jChannelData["business_profile"] != null && jChannelData["business_profile"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
						{
							var jBusinessProfile = (Newtonsoft.Json.Linq.JObject) jChannelData["business_profile"];

							var fields = new string[] { "cuit", "businessName", "contactName", "segment", "technicalAdvisor", "commercialAdvisor", "role", "commercialAttentionCode", "technicalAttentionCode" };
							foreach (var field in fields)
							{
								if (jBusinessProfile[field] != null && jBusinessProfile[field].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									var value = jBusinessProfile[field].ToString();
									jExt[field] = value;

									if (field.Equals("cuit") && !string.IsNullOrEmpty(value) && isClient)
										jContents["from"]["businessData"] = value;
								}
							}
						}
					}
				}

				WhatsappManager whatsappManager = new WhatsappManager();
				await whatsappManager.GenerateFiles(jContents, "postback", false);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de botmaker {0}: {1}", body, ex);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("gupshup")]
		[Route("api/whatsapp/gupshup/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostGupShup(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update de botmaker {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			Newtonsoft.Json.Linq.JObject jBody = null;
			try
			{
				jBody = Newtonsoft.Json.Linq.JObject.Parse(body);

				if (jBody["version"] != null && jBody["version"].Type == Newtonsoft.Json.Linq.JTokenType.Integer)
				{
					var version = jBody["version"].ToObject<int>();
					if (version >= 2)
					{
						if (jBody["type"] != null && jBody["type"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						{
							var type = jBody["type"].ToString();

							switch (type)
							{
								case "user-event":
									return new HttpResponseMessage(HttpStatusCode.OK);

								case "message-event":
									{
										WhatsappManager whatsappManager = new WhatsappManager();
										jBody["account"] = id;
										await whatsappManager.GenerateFiles(jBody, "gupshup", true);
									}
									break;

								case "message":
									{
										WhatsappManager whatsappManager = new WhatsappManager();
										jBody["account"] = id;
										await whatsappManager.GenerateFiles(jBody, "gupshup", false);
									}
									break;

								default:
									break;
							}
						}
					}
				}
				
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de gupshup {0}: {1}", body, ex);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("infobip")]
		[Route("api/whatsapp/infobip")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostInfobip()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);

				var jResults = (Newtonsoft.Json.Linq.JArray) jData["results"];
				for (var i = jResults.Count - 1; i >= 0; i--)
				{
					var jResult = (Newtonsoft.Json.Linq.JObject) jResults[i];
					if (jResult["sentAt"] != null &&
						(jResult["sentAt"].Type == Newtonsoft.Json.Linq.JTokenType.String || jResult["sentAt"].Type == Newtonsoft.Json.Linq.JTokenType.Date))
					{
						jResults.RemoveAt(i);
						continue;
					}

					if (jResult["seenAt"] != null &&
						(jResult["seenAt"].Type == Newtonsoft.Json.Linq.JTokenType.String || jResult["seenAt"].Type == Newtonsoft.Json.Linq.JTokenType.Date))
					{
						jResults.RemoveAt(i);
						continue;
					}
				}

				if (jResults.Count > 0)
					await whatsappManager.GenerateFiles(jData, "infobip", false);
				else
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update de infobip {0} porque es un estado", body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de infobip {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("infobip")]
		[Route("api/whatsapp/infobipreports/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostInfobipReports(string id)
		{
			var uri = Request.RequestUri.ToString();
			if (uri.Equals("https://fbsocial.azurewebsites.net/api/whatsapp/infobipreports/*************") ||
				uri.Equals("https://fbsocial.azurewebsites.net/api/whatsapp/infobipreports/*************"))
			{
				Yoizen.Common.Tracer.TraceInfo("Ignorando request: {0} {1}", Request.Method, uri);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid account"
				});
			}

			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, uri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, uri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);

				if (jData["results"] != null &&
					jData["results"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
				{
					var jResults = (Newtonsoft.Json.Linq.JArray) jData["results"];
					foreach (Newtonsoft.Json.Linq.JObject jResult in jResults)
					{
						if (jResult["to"] != null)
						{
							jResult["contact"] = jResult["to"];
						}
						jResult["to"] = id;
					}
					await whatsappManager.GenerateFiles(jData, "infobip", true);
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de infobip {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("twilio")]
		[Route("api/whatsapp/twilio/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostTwilio(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (!Request.Headers.Contains("X-Twilio-Signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Twilio-Signature", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			string expected = Request.Headers.GetValues("X-Twilio-Signature").First();

			var parameters = new Newtonsoft.Json.Linq.JObject();
			foreach (var item in body.Split("&".ToCharArray()))
			{
				var parts = item.Split("=".ToCharArray());
				parameters[parts[0]] = Uri.UnescapeDataString(parts[1]);
			}

//			var validator = new RequestValidator(TwillioKey);
//			var url = Request.RequestUri.ToString();
//#if DEBUG
//			// En caso de la URL ser a malber.ysocial.net, le agregamos el puerto de redirección original
//			if (url.StartsWith("https://malber.ysocial.net"))
//			{
//				url = url.Replace("https://malber.ysocial.net/", "https://malber.ysocial.net:9086/");
//			}
//#endif
//			if (!validator.Validate(url, parameters, expected))
//			{
//				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque la firma {1} es inválida X-Twilio-Signature", body, expected);
//				return new HttpResponseMessage(HttpStatusCode.OK);
//			}

			try
			{
				parameters["account"] = id;
				WhatsappManager whatsappManager = new WhatsappManager();
				await whatsappManager.GenerateFiles(parameters, "twilio", false);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de twilio {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("twilio")]
		[Route("api/whatsapp/twilioreports/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostTwilioReports(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (!Request.Headers.Contains("X-Twilio-Signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Twilio-Signature", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			string expected = Request.Headers.GetValues("X-Twilio-Signature").First();

			var parameters = new Newtonsoft.Json.Linq.JObject();
			foreach (var item in body.Split("&".ToCharArray()))
			{
				var parts = item.Split("=".ToCharArray());
				parameters[parts[0]] = Uri.UnescapeDataString(parts[1]);
			}

			//			var validator = new RequestValidator(TwillioKey);
			//			var url = Request.RequestUri.ToString();
			//#if DEBUG
			//			// En caso de la URL ser a malber.ysocial.net, le agregamos el puerto de redirección original
			//			if (url.StartsWith("https://malber.ysocial.net"))
			//			{
			//				url = url.Replace("https://malber.ysocial.net/", "https://malber.ysocial.net:9086/");
			//			}
			//#endif
			//			if (!validator.Validate(url, parameters, expected))
			//			{
			//				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque la firma {1} es inválida X-Twilio-Signature", body, expected);
			//				return new HttpResponseMessage(HttpStatusCode.OK);
			//			}

			try
			{
				parameters["account"] = id;
				WhatsappManager whatsappManager = new WhatsappManager();
				await whatsappManager.GenerateFiles(parameters, "twilio", true);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de twilio {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("interaxa")]
		[Route("api/whatsapp/interaxa/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostInteraxa(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);
				jData["account"] = id;
				await whatsappManager.GenerateFiles(jData, "interaxa", false);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de interaxa {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("interaxa")]
		[Route("api/whatsapp/interaxareports/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostInteraxaReports(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);
				jData["account"] = id;
				await whatsappManager.GenerateFiles(jData, "interaxa", false);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de infobip {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("yoizen")]
		[Route("api/whatsapp/yoizen/{id}")]
		[Route("api/whatsapp/postback/native/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostYoizen(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);
				jData["account"] = id;

				if (jData["statuses"] != null &&
					jData["statuses"].Type == Newtonsoft.Json.Linq.JTokenType.Array &&
					Request.RequestUri.AbsoluteUri.IndexOf("whatsapp/postback/native", StringComparison.InvariantCultureIgnoreCase) >= 0)
				{
					await whatsappManager.GenerateFiles(jData, "yoizen", true);
				}
				else if (jData["calls"] != null &&
					jData["calls"].Type == Newtonsoft.Json.Linq.JTokenType.Array &&
					Request.RequestUri.AbsoluteUri.IndexOf("whatsapp/postback/native", StringComparison.InvariantCultureIgnoreCase) >= 0)
				{
					await whatsappManager.GenerateFiles(jData, "yoizen", false, true);
				}
				else
				{
					await whatsappManager.GenerateFiles(jData, "yoizen", false);
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de yoizen {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("yoizen")]
		[Route("api/whatsapp/yoizenreports/{id}")]
		[Route("api/whatsapp/yoizen/statuses/{id}")]
		[Route("api/whatsapp/postback/native/statuses/{id}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostYoizenReports(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				var jData = Newtonsoft.Json.Linq.JObject.Parse(body);
				jData["account"] = id;

				if (jData["messages"] != null &&
					jData["messages"].Type == Newtonsoft.Json.Linq.JTokenType.Array &&
					Request.RequestUri.AbsoluteUri.IndexOf("whatsapp/postback/native/statuses", StringComparison.InvariantCultureIgnoreCase) >= 0)
				{
					await whatsappManager.GenerateFiles(jData, "yoizen", false);
				}
				else
				{
					await whatsappManager.GenerateFiles(jData, "yoizen", true);
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de infobip {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("cloudapi")]
		[Route("api/whatsapp/cloudapi/{appId}")]
		[HttpGet]
		public HttpResponseMessage GetCloudApi(
			string appId,
			[FromUri(Name = "hub.mode")] string hubMode,
			[FromUri(Name = "hub.challenge")] string hubChallenge,
			[FromUri(Name = "hub.verify_token")] string hubVerifyToken)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			var cloudApiApp = WhatsappManager.RetrieveRegisteredCloudApiApp(appId);
			if (cloudApiApp == null)
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora la registración porque la app {0} no está registrada", appId);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			if (hubMode.Equals("subscribe") && hubVerifyToken.Equals(cloudApiApp.VerifyToken))
			{
				string result = hubChallenge;
				var resp = new HttpResponseMessage(HttpStatusCode.OK)
				{
					Content = new StringContent(result, Encoding.UTF8, "text/plain")
				};
				return resp;
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("cloudapi")]
		[Route("api/whatsapp/cloudapi/{appId}")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostCloudApi(string appId)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			var cloudApiApp = WhatsappManager.RetrieveRegisteredCloudApiApp(appId);
			if (cloudApiApp == null)
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque la app {1} no está registrada", body, appId);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			var url = Request.RequestUri.AbsoluteUri;

			if (!url.StartsWith("https://dev.ysocial.net") &&
				!url.StartsWith("https://malber.ysocial.net"))
			{
				if (!Request.Headers.Contains("X-Hub-Signature"))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Hub-Signature", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
				if (!xHubSignatureHeader.StartsWith("sha1="))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}
				xHubSignatureHeader = xHubSignatureHeader.Substring(5);

				string bodyHash = Encode(body, cloudApiApp.ClientSecret);

				if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}
			}

			try
			{
				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				if (jBody["object"] == null ||
					jBody["object"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
					!jBody["object"].ToString().Equals("whatsapp_business_account", StringComparison.InvariantCultureIgnoreCase))
				{
					Yoizen.Common.Tracer.TraceError("El update {0} no trae el \"object\"=\"whatsapp_business_account\" como se espera. Se ignora", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				if (jBody["entry"] == null ||
					jBody["entry"].Type != Newtonsoft.Json.Linq.JTokenType.Array)
				{
					Yoizen.Common.Tracer.TraceError("El update {0} no trae el \"entry\"=\"[]\" como se espera. Se ignora", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				var jEntries = (Newtonsoft.Json.Linq.JArray) jBody["entry"];
				if (jEntries.Count == 0)
				{
					Yoizen.Common.Tracer.TraceError("El update {0} trae el \"entry\" como un array vacío. Se ignora", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				WhatsappManager whatsappManager = new WhatsappManager();

				foreach (Newtonsoft.Json.Linq.JObject jEntry in jEntries)
				{
					var id = jEntry["id"].ToString();
					var jChanges = (Newtonsoft.Json.Linq.JArray) jEntry["changes"];
					var index = -1;

					foreach (Newtonsoft.Json.Linq.JObject jChange in jChanges)
					{
						index++;

						var field = jChange["field"].ToString();
						var jValue = (Newtonsoft.Json.Linq.JObject) jChange["value"];

						if (field.Equals("messages"))
						{
							var messagingProduct = jValue["messaging_product"].ToString();
							var jMetadata = (Newtonsoft.Json.Linq.JObject) jValue["metadata"];
							var displayPhoneNumber = jMetadata["display_phone_number"].ToString();
							var phoneNumberId = jMetadata["phone_number_id"].ToString();

							if (displayPhoneNumber.StartsWith("+"))
								displayPhoneNumber = displayPhoneNumber.Substring(1);
							displayPhoneNumber = displayPhoneNumber.Replace("-", string.Empty).Replace(" ", string.Empty);

							jValue.Remove("metadata");
							jValue["account"] = displayPhoneNumber;

							if (jValue["statuses"] != null &&
								jValue["statuses"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
							{
								await whatsappManager.GenerateFiles(jValue, "cloudapi", true);
							}
							else if (jValue["errors"] != null &&
								jValue["errors"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
							{
								var requestBody = jValue.ToString();
								Yoizen.Common.Tracer.TraceVerb($"{displayPhoneNumber} -> errors -> {requestBody}");
							}
							else if (jValue["messages"] != null &&
								jValue["messages"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
							{
								await whatsappManager.GenerateFiles(jValue, "cloudapi", false);
							}
							else
							{
								var requestBody = jValue.ToString();
								Yoizen.Common.Tracer.TraceVerb($"{displayPhoneNumber} -> unknown -> {requestBody}");
							}
						}
						else if (field.Equals("calls"))
						{
							var messagingProduct = jValue["messaging_product"].ToString();
							var jMetadata = (Newtonsoft.Json.Linq.JObject) jValue["metadata"];
							var displayPhoneNumber = jMetadata["display_phone_number"].ToString();
							var phoneNumberId = jMetadata["phone_number_id"].ToString();

							if (displayPhoneNumber.StartsWith("+"))
								displayPhoneNumber = displayPhoneNumber.Substring(1);
							displayPhoneNumber = displayPhoneNumber.Replace("-", string.Empty).Replace(" ", string.Empty);

							jValue.Remove("metadata");
							jValue["account"] = displayPhoneNumber;

							if (jValue["calls"] != null &&
								jValue["calls"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
							{
								await whatsappManager.GenerateFiles(jValue, "cloudapi", false, true);
							}
							else
							{
								var requestBody = jValue.ToString();
								Yoizen.Common.Tracer.TraceVerb($"{displayPhoneNumber} -> unknown -> {requestBody}");
							}
						}
						else if (field.Equals("user_preferences"))
						{
							if (jValue["user_preferences"] != null &&
								jValue["user_preferences"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
							{
								await whatsappManager.GenerateFiles(jValue, "cloudapi", false, false);
							}
						}
						else
						{
							Yoizen.Common.Tracer.TraceError("Se ignora el cambio del índice {0} porque trae como campo {1} que no está programado", index, field);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de cloud api {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("cloudapi")]
		[Route("api/whatsapp/cloudapi/register")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostCloudApiRegister()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			if (!Request.Headers.Contains("x-ysocial-signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header x-ysocial-signature", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					success = false,
					error = "missing signature header"
				});
			}

			string ySocialSignature = Request.Headers.GetValues("x-ysocial-signature").First();
			if (!ySocialSignature.StartsWith("sha256="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header x-ysocial-signature no empieza con sha256=, si no que fue {1}", body, ySocialSignature);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					success = false,
					error = "invalid signature header (expecting sha256)"
				});
			}
			ySocialSignature = ySocialSignature.Substring(7);

			string bodyHash = EncodeBase64(body, SecretKeyForHashing);

			if (!bodyHash.Equals(ySocialSignature, StringComparison.InvariantCultureIgnoreCase))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header x-ysocial-signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					success = false,
					error = "invalid signature header"
				});
			}

			try
			{
				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				if (jBody["appId"] == null || 
					jBody["appId"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
					string.IsNullOrWhiteSpace(jBody["appId"].ToString()))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						success = false,
						error = "expecting appId"
					});
				}

				if (jBody["clientSecret"] == null || 
					jBody["clientSecret"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
					string.IsNullOrWhiteSpace(jBody["clientSecret"].ToString()))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						success = false,
						error = "expecting clientSecret"
					});
				}

				if (jBody["verifyToken"] == null || 
					jBody["verifyToken"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
					string.IsNullOrWhiteSpace(jBody["verifyToken"].ToString()))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						success = false,
						error = "expecting verifyToken"
					});
				}

				var appId = jBody["appId"].ToString();
				var clientSecret = jBody["clientSecret"].ToString();
				var verifyToken = jBody["verifyToken"].ToString();

				WhatsappManager.RegisterCloudApiApp(appId, clientSecret, verifyToken);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de cloud api {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("cloudapi")]
		[Route("api/whatsapp/cloudapi/reloadapps")]
		[HttpPatch]
		public HttpResponseMessage PatchCloudApiReloadApps()
		{
			WhatsappManager.ReloadCloudApiApps();
			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("wavy")]
		[Route("api/whatsapp/wavy")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostWavy()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();

				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				var tryToPush = true;
				var isStatus = false;
				
				await whatsappManager.GenerateFiles(jBody, "wavy", isStatus);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de wavy {0}: {1}", body, ex);

				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid json contents"
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("NewsProcessed")]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				whatsappManager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("MoveTemporalToNews")]
		public HttpResponseMessage MoveTemporalToNews(string id)
		{
			int count = 0, failed = 0;
			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				whatsappManager.MoveTemporalToNews(id, out count, out failed);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return Request.CreateResponse(HttpStatusCode.OK, new
			{
				count = count,
				failed = failed
			});
		}

		[ActionName("DeleteNewsOlderThan")]
		[HttpDelete]
		public HttpResponseMessage DeleteNewsOlderThan(string id, int days)
		{
			if (days <= 0 || days >= 7)
				return new HttpResponseMessage(HttpStatusCode.OK);

			try
			{
				WhatsappManager whatsappManager = new WhatsappManager();
				whatsappManager.DeleteNewsOlderThan(id, days);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("botnumbers")]
		[Route("api/whatsapp/botnumbers")]
		[HttpGet]
		public HttpResponseMessage GetBotNumbers()
		{
			try
			{
				return Request.CreateResponse(HttpStatusCode.OK, WhatsappManager.RetrieveBotNumbers());
			}
			catch
			{
				return Request.CreateResponse(HttpStatusCode.OK, new object[0]);
			}
		}

		[ActionName("botnumbers")]
		[Route("api/whatsapp/botnumbers/register/{phoneNumber}")]
		[HttpPost]
		public HttpResponseMessage PostBotNumbers(string phoneNumber)
		{
			try
			{
				WhatsappManager.RegisterBotNumber(phoneNumber);
				return Request.CreateResponse(HttpStatusCode.OK);
			}
			catch
			{
				return Request.CreateResponse(HttpStatusCode.InternalServerError);
			}
		}

		[ActionName("status")]
		[Route("api/whatsapp/status")]
		[HttpGet]
		public HttpResponseMessage Status(
			[FromUri(Name = "hub.mode")] string hubMode,
			[FromUri(Name = "hub.challenge")] string hubChallenge,
			[FromUri(Name = "hub.verify_token")] string hubVerifyToken)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			if (hubMode.Equals("status") && hubVerifyToken.Equals("yoizencallback"))
			{
				string result = hubChallenge;
				var resp = new HttpResponseMessage(HttpStatusCode.OK)
				{
					Content = new StringContent(result, Encoding.UTF8, "text/plain")
				};
				return resp;
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("wavy")]
		[Route("api/whatsapp/{phoneNumber}/hsm")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostPhoneNumberHsm(string phoneNumber)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (!Request.Headers.Contains("Authorization"))
			{
				return Request.CreateResponse(HttpStatusCode.Unauthorized, new
				{
					Success = false,
					Error = new
					{
						Message = "missing authorization header"
					}
				});
			}

			string authorization = Request.Headers.GetValues("Authorization").First();
			if (string.IsNullOrEmpty(authorization) || !authorization.StartsWith("Bearer "))
			{
				return Request.CreateResponse(HttpStatusCode.Unauthorized, new
				{
					Success = false,
					Error = new
					{
						Message = "missing jwt bearer inside authorization header"
					}
				});
			}

			var jwt = authorization.Substring(authorization.IndexOf("Bearer", 0, StringComparison.InvariantCultureIgnoreCase) + 7);
			IDictionary<string, object> payload;

			try
			{
				payload = JWT.Builder.JwtBuilder.Create()
				  .WithAlgorithm(new JWT.Algorithms.HMACSHA256Algorithm()) // symmetric
				  .WithSecret("Y01z3n10SAMangiacaprini!")
				  //.AddClaim("exp", DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds())
				  .MustVerifySignature()
				  .Decode<IDictionary<string, object>>(jwt);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error validando el jwt: {0}", ex);

				return Request.CreateResponse(HttpStatusCode.Unauthorized, new
				{
					Success = false,
					Error = new
					{
						Message = "invalid jwt"
					}
				});
			}

			if (!payload.ContainsKey("ph"))
			{
				return Request.CreateResponse(HttpStatusCode.Unauthorized, new
				{
					Success = false,
					Error = new
					{
						Message = "invalid jwt"
					}
				});
			}

			var phoneNumberJwt = payload["ph"].ToString();
			if (string.IsNullOrEmpty(phoneNumberJwt) || !phoneNumberJwt.Equals(phoneNumber))
			{
				return Request.CreateResponse(HttpStatusCode.Unauthorized, new
				{
					Success = false,
					Error = new
					{
						Message = "invalid jwt"
					}
				});
			}

			var clientId = SubscriptionManager.Instance.GetClientForService(Yoizen.Social.DomainModel.SocialServiceTypes.WhatsApp, phoneNumber);
			if (clientId == null)
			{
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					Success = false,
					Error = new
					{
						Message = "not registered"
					}
				});
			}

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				return Request.CreateResponse(HttpStatusCode.UnsupportedMediaType, new
				{
					Success = false,
					Error = new
					{
						Message = "invalid content-type"
					}
				});
			}

			Newtonsoft.Json.Linq.JObject jBody;
			try
			{
				jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
			}
			catch
			{
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					Success = false,
					Error = new
					{
						Message = "invalid contents"
					}
				});
			}

			var queueName = $"{clientId}-whatsapp-hsm";
			try
			{
				var jMessage = new Newtonsoft.Json.Linq.JObject();
				jMessage["info"] = jBody;
				jMessage["fromCallback"] = true;
				jMessage["ts"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(DateTime.Now);

				var guid = Guid.NewGuid().ToString();

				jMessage["guid"] = guid;

				await SubscriptionManager.Instance.Publish(queueName, jMessage, clientId);

				return Request.CreateResponse(HttpStatusCode.OK, new
				{
					Success = true,
					Result = guid
				});
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error publicando el HSM en la cola {0}: {1}", queueName, ex);

				return Request.CreateResponse(HttpStatusCode.InternalServerError, new
				{
					Success = false,
					Error = new
					{
						Message = "couldn't publish de message"
					}
				});
			}
		}
		
		[ActionName("cloudapi")]
		[Route("api/whatsapp/cloudapi/apps")]
		[HttpGet]
		public HttpResponseMessage GetCloudApiApps()
		{
			if (!Request.Headers.Contains("x-ysocial"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el request {0} porque no tiene header x-ysocial", Request.RequestUri);
				return Request.CreateResponse(HttpStatusCode.NotFound);
			}

			var ySocialHeader = Request.Headers.GetValues("x-ysocial").First();
			if (!ySocialHeader.Equals("S@cial"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el request {0} porque no el header x-ysocial tiene el valor {1}", Request.RequestUri, ySocialHeader);
				return Request.CreateResponse(HttpStatusCode.NotFound);
			}

			try
			{
				var contents = WhatsappManager.RetriveRegisteredCloudApiApps();
				var response = Request.CreateResponse(HttpStatusCode.OK);
				response.Content = new StringContent(contents, Encoding.UTF8, "application/json");
				return response;
			}
			catch { }

			return Request.CreateResponse(HttpStatusCode.OK, new { });
		}

		#endregion

		#region Private Methods & Utils

		private static string Encode(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA1(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				return myhmacsha1.ComputeHash(bytes).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		private static string Encode256(string input, string key)
		{
			byte[] keyBytes = Encoding.ASCII.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA256(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				return myhmacsha1.ComputeHash(bytes).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		private static string CalculateSha256(string input)
		{
			using (var hash = SHA256.Create())
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				return Convert.ToBase64String(hash.ComputeHash(bytes));
			}
		}

		private static string CalculateSha256Hex(string input)
		{
			using (var hash = SHA256.Create())
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				return hash.ComputeHash(bytes).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		private static string EncodeBase64(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA256(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				var hash = myhmacsha1.ComputeHash(stream);
				return Convert.ToBase64String(hash);
			}
		}

		#endregion
	}
}