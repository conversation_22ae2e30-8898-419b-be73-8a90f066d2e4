﻿
CREATE PROCEDURE [dbo].[EmailContacts_GetAllByAgentID]
    @AgentID INT,
    @LastEmailContactID INT = NULL,
    @text NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT TOP 21 *
		FROM [dbo].[EmailContacts]
			WHERE [AgentID] = @AgentID
				AND (@LastEmailContactID IS NULL OR [ID] < @LastEmailContactID)
				AND (
					@text IS NULL OR
					[Email] LIKE '%' + @text + '%' OR
					[Name] LIKE '%' + @text + '%' OR
					[LastName] LIKE '%' + @text + '%'
				)
			ORDER BY [ID] DESC;
END;