﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace Yoizen.Social.DomainModel
{
	#region Enums

	public enum Permissions
	{
		/// <summary>Gestión de usuarios</summary>
		UsersAdministration = 1,
		/// <summary>Gestión de perfiles</summary>
		ProfilesAdministration = 3,
		/// <summary>Parámetros del sistema</summary>
		SystemSettings = 4,
		/// <summary>Servicios</summary>
		Services = 5,
		/// <summary>Blacklist</summary>
		BlackList = 6,
		/// <summary>Colas crear</summary>
		QueuesEdition = 7,
		/// <summary>Etiquetas</summary>
		Tags = 8,
		/// <summary>Agentes en colas</summary>
		QueueAgents = 11,
		/// <summary>Etiquetas en colas</summary>
		QueueTags = 12,
		/// <summary>Supervisores en colas</summary>
		QueueSupervisors = 13,
		/// <summary>Gestión de Agentes</summary>
		AgentsAdministration = 14,
		/// <summary>Reportes tiempo real</summary>
		RealTimeReports = 15,
		/// <summary>Reportes Histórico</summary>
		HistReports = 16,
		/// <summary>Motivos de Auxiliar</summary>
		AuxReasons = 17,
		/// <summary>Plantillas</summary>
		QueueTemplates = 18,
		/// <summary>Acciones en colas</summary>
		QueueActions = 19,
		/// <summary>Whitelist</summary>
		WhiteList = 20,
		/// <summary>Configuración Avanzada de Servicios</summary>
		AdvancedServices = 21,
		/// <summary>Filtros</summary>
		Filters = 22,
		/// <summary>Respuestas predefinidas</summary>
		Templates = 23,
		/// <summary>Motivos de Contacto</summary>
		ContactReasons = 24,
		/// <summary>Mensajes de Chat para el Supervisor </summary>
		SupervisorChat = 25,
		/// <summary>Listas de Usuarios </summary>
		SocialUserProfilesLists = 26,
		/// <summary>Edición de índices de Service Level en Colas</summary>
		QueueServiceLevel = 27,
		/// <summary>Reportes Generados</summary>
		ExportedReports = 28,
		/// <summary>Reportes Diarios Generados</summary>
		DailyReports = 29,
		/// <summary>Envíos de Encuestas</summary>
		QueueSurveys = 30,
		/// <summary>Gestión de Grupos de Agentes</summary>
		AgentGroupsAdministration = 31,
		/// <summary>Integraciones</summary>
		Integrations = 32,
		/// <summary>Configuración de servicios cognitivos</summary>
		CognitiveServices = 33,
		/// <summary>Configuración de integraciones con terceros</summary>
		ExternalIntegrations = 34,
		/// <summary>Reportes Administrativos</summary>
		AdministrativeReports = 35,
		/// <summary>Whatsapp</summary>
		WhatsappSection = 36,
		/// <summary>Días y horarios laborables</summary>
		WorkingTimes = 37,
		/// <summary>Monitorear agentes</summary>
		InspectAgents = 38,
		/// <summary>Responder mensajes</summary>
		ReplyMessages = 39,
		/// <summary>Desconectar agentes</summary>
		DisconnectAgents = 40,
		/// <summary>Supervisión</summary>
		Supervisor = 41,
		/// <summary>Cambiar contraseña de otro usuario</summary>
		ChangeUserPassword = 42,
		/// <summary>Cambiar contraseña de agentes</summary>
		ChangeAgentPassword = 43,
		/// <summary>Desasignar mensajes de agentes</summary>
		UnnasignMessagesFromAgents = 44,
		/// <summary>Administración de sitios</summary>
		Sites = 45,
		/// <summary>Administración de grupo de colas</summary>
		QueueGroups = 46,
		/// <summary>Estado del sistema</summary>
		SystemStatus = 47,
	    /// <summary>Reporte de WhatsApp</summary>
		WhatsappReports = 48,
		/// <summary>Masivo de HSM sin creacion de caso</summary>
		MassiveHsmWithoutCaseCreation = 49,
		/// <summary>informacion de tarea de HSM sin creacion de caso</summary>
		MassiveHsmWithoutCaseCreationInfo = 50,
		/// <summary>Reportes Programados</summary>
		ScheduledReports = 51,
		/// <summary>Administración de grupos de etiquetas</summary>
		TagGroups = 52,
		/// <summary>Mover mensajes entre colas</summary>
		MoveMessageQueue = 53,
		/// <summary>Acceso a yUsage</summary>
		YUsageAccess = 54
	}

	#endregion

	#region Collections

	[Serializable]
	public sealed class PermissionCollection : List<Permission>
	{
		/// <summary>
		/// Devuelve si un <see cref="Permissions"/> especificado en el <paramref name="permission"/> está en la colección de
		/// permisos
		/// </summary>
		/// <param name="permission">El permiso a buscar</param>
		/// <returns>true si el permiso está en la colección actual; en caso contrario, false</returns>
		public bool HasPermission(Permissions permission)
		{
			foreach (var item in this)
			{
				if (item.PermissionType == permission)
					return true;
			}

			return false;
		}
	}

	#endregion

	[Serializable]
	public class Permission : DomainObject
	{
		#region Properties

		/// <summary>
		/// Devuelve el código del permiso
		/// </summary>
		public short ID { get; private set; }

		/// <summary>
		/// Devuelve el nombre del permiso
		/// </summary>
		public string Name { get; private set; }

		/// <summary>
		/// Devuelve la descripción del permiso
		/// </summary>
		public string Description { get; private set; }

		/// <summary>
		/// Devuelve el <see cref="Permissions"/> correspondiente al permiso actual
		/// </summary>
		public Permissions PermissionType { get { return (Permissions) this.ID; } }

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Permission"/>
		/// </summary>
		public Permission()
		{
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Permission"/>
		/// </summary>
		/// <param name="id">El código de Permiso</param>
		public Permission(short id)
		{
			this.ID = id;
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Profile"/> a partir de los datos de un registro de la base de datos
		/// </summary>
		/// <param name="record">Un <see cref="IDataRecord"/> con los datos de la base de datos</param>
		public Permission(IDataRecord record)
			: this()
		{
			this.ID = Convert.ToInt16(record["PermissionID"]);
			this.Name = (string) record["Name"];
			if (record["Description"] != DBNull.Value)
				this.Description = (string) record["Description"];
		}

		#endregion

		#region Public Methods
		
		/// <summary>
		/// Devuelve el código que identifica a la entidad
		/// </summary>
		/// <returns>Un objeto con el valor del código de la entidad</returns>
		public override object GetID()
		{
			return this.ID;
		}

		/// <summary>
		/// Devuelve una cadena que representa el objeto actual.
		/// </summary>
		/// <returns>Cadena que representa el objeto actual.</returns>
		public override string ToString()
		{
			return this.Name;
		}

		/// <summary>
		/// Determina si el objeto Object especificado es igual al objeto Object actual.
		/// </summary>
		/// <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
		/// <returns>Es true si el objeto Object especificado es igual al objeto Object actual; en caso contrario, es false.</returns>
		public override bool Equals(object obj)
		{
			if (obj == null)
				return false;
			if (!(obj is Permission))
				return false;
			return this.ID == ((Permission) obj).ID;
		}

		/// <summary>
		/// Actúa como función hash para un tipo concreto.
		/// </summary>
		/// <returns>Código hash para la clase Object actual.</returns>
		public override int GetHashCode()
		{
			return this.ID.GetHashCode();
		}

		#endregion
	}
}
