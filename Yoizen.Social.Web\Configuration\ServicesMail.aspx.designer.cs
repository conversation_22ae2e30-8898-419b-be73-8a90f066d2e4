﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


	public partial class ServicesMail
	{

		/// <summary>
		/// messageMailTestIMAPResult control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailTestIMAPResult;

		/// <summary>
		/// messageMailTestPOP3Result control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailTestPOP3Result;

		/// <summary>
		/// messageMailTestEWSResult control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailTestEWSResult;

		/// <summary>
		/// messageMailTestSMTPResult control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailTestSMTPResult;

		/// <summary>
		/// messageMailTestGmailResult control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailTestGmailResult;

		/// <summary>
		/// dropdownlistMailConnectionType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailConnectionType;

		/// <summary>
		/// textboxMailRetrieveServer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveServer;

		/// <summary>
		/// textboxMailRetrievePort control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePort;

		/// <summary>
		/// textboxMailRetrieveUser control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveUser;

		/// <summary>
		/// placeholderMailRetrievePassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderMailRetrievePassword;

		/// <summary>
		/// labelMailRetrievePassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Label labelMailRetrievePassword;

		/// <summary>
		/// literalMailRetrievePasswordChangePasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrievePasswordChangePasswordTitle;

		/// <summary>
		/// literalMailRetrievePasswordNewPasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrievePasswordNewPasswordTitle;

		/// <summary>
		/// checkboxMailRetrievePassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailRetrievePassword;

		/// <summary>
		/// textboxMailRetrievePassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePassword;

		/// <summary>
		/// checkboxEmailRetrieveUseSSL control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailRetrieveUseSSL;

		/// <summary>
		/// dropdownlistEmailRetrieveSSLSecureOptions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistEmailRetrieveSSLSecureOptions;

		/// <summary>
		/// textboxMailRetrievePOP3Server control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePOP3Server;

		/// <summary>
		/// textboxMailRetrievePOP3Port control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePOP3Port;

		/// <summary>
		/// textboxMailRetrievePOP3User control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePOP3User;

		/// <summary>
		/// placeholderMailRetrievePOP3Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderMailRetrievePOP3Password;

		/// <summary>
		/// labelMailRetrievePOP3Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Label labelMailRetrievePOP3Password;

		/// <summary>
		/// literalMailRetrievePOP3PasswordChangePasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrievePOP3PasswordChangePasswordTitle;

		/// <summary>
		/// literalMailRetrievePOP3PasswordNewPasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrievePOP3PasswordNewPasswordTitle;

		/// <summary>
		/// checkboxMailRetrievePOP3Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailRetrievePOP3Password;

		/// <summary>
		/// textboxMailRetrievePOP3Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrievePOP3Password;

		/// <summary>
		/// checkboxEMailRetrievePOP3UseSSL control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEMailRetrievePOP3UseSSL;

		/// <summary>
		/// dropdownlistEmailRetrievePOP3SSLSecureOptions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistEmailRetrievePOP3SSLSecureOptions;

		/// <summary>
		/// textboxMailSendServer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSendServer;

		/// <summary>
		/// textboxMailSendPort control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSendPort;

		/// <summary>
		/// checkboxMailSendUseCredentials control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailSendUseCredentials;

		/// <summary>
		/// textboxMailSendUser control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSendUser;

		/// <summary>
		/// placeholderMailSendPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderMailSendPassword;

		/// <summary>
		/// labelMailSendPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Label labelMailSendPassword;

		/// <summary>
		/// literalMailSendPasswordChangePasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailSendPasswordChangePasswordTitle;

		/// <summary>
		/// literalMailSendPasswordNewPasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailSendPasswordNewPasswordTitle;

		/// <summary>
		/// checkboxMailSendPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailSendPassword;

		/// <summary>
		/// textboxMailSendPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSendPassword;

		/// <summary>
		/// checkboxEmailSendUseSSL control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailSendUseSSL;

		/// <summary>
		/// dropdownlistEmailSendSSLSecureOptions control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistEmailSendSSLSecureOptions;

		/// <summary>
		/// textboxMailRetrieveEWSServer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSServer;

		/// <summary>
		/// dropdownlistMailRetrieveEWSAuthenticationType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailRetrieveEWSAuthenticationType;

		/// <summary>
		/// textboxMailRetrieveEWSUser control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSUser;

		/// <summary>
		/// placeholderMailRetrieveEWSPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderMailRetrieveEWSPassword;

		/// <summary>
		/// labelMailRetrieveEWSPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Label labelMailRetrieveEWSPassword;

		/// <summary>
		/// literalMailRetrieveEWSPasswordChangePasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrieveEWSPasswordChangePasswordTitle;

		/// <summary>
		/// literalMailRetrieveEWSPasswordNewPasswordTitle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Literal literalMailRetrieveEWSPasswordNewPasswordTitle;

		/// <summary>
		/// checkboxMailRetrieveEWSPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailRetrieveEWSPassword;

		/// <summary>
		/// textboxMailRetrieveEWSPassword control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSPassword;

		/// <summary>
		/// textboxMailRetrieveEWSOAuthEmailAddress control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSOAuthEmailAddress;

		/// <summary>
		/// textboxMailRetrieveEWSOAuthAppID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSOAuthAppID;

		/// <summary>
		/// textboxMailRetrieveEWSOAuthClientSecret control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSOAuthClientSecret;

		/// <summary>
		/// textboxMailRetrieveEWSOAuthTenantID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSOAuthTenantID;

		/// <summary>
		/// textboxMailRetrieveEWSTimeout control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveEWSTimeout;

		/// <summary>
		/// dropdownlistMailRetrieveEWSExchangeVersion control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailRetrieveEWSExchangeVersion;

		/// <summary>
		/// textboxMailRetrieveGmailUser control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveGmailUser;

		/// <summary>
		/// textboxMailRetrieveGmailCredentials control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailRetrieveGmailCredentials;

		/// <summary>
		/// checkboxEmailSetAsRead control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailSetAsRead;

		/// <summary>
		/// checkboxEmailSetAsDeleted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailSetAsDeleted;

		/// <summary>
		/// textboxDaysUntilMailsAreDeleted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxDaysUntilMailsAreDeleted;

		/// <summary>
		/// textboxMailFromDate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailFromDate;

		/// <summary>
		/// checkboxMailUseReceivedDateInsteadOfSentDate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailUseReceivedDateInsteadOfSentDate;

		/// <summary>
		/// checkboxMailUseReceivedDateIfSentDateIsInTheFuture control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailUseReceivedDateIfSentDateIsInTheFuture;

		/// <summary>
		/// textboxMailDeliveryErrorFrom control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailDeliveryErrorFrom;

		/// <summary>
		/// checkboxEmailFilterIgnoreXFailedRecipients control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailFilterIgnoreXFailedRecipients;

		/// <summary>
		/// checkboxEmailFilterIgnoreMultipartReport control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailFilterIgnoreMultipartReport;

		/// <summary>
		/// dropdownlistGrouping control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistGrouping;

		/// <summary>
		/// textboxGroupingExcludedSenders control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxGroupingExcludedSenders;

		/// <summary>
		/// dropdownlistCaseHandling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistCaseHandling;

		/// <summary>
		/// checkboxReceivesWebFormsMails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReceivesWebFormsMails;

		/// <summary>
		/// hiddenWebForms control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenWebForms;

		/// <summary>
		/// customvalidatorWebForms control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorWebForms;

		/// <summary>
		/// textboxMailAddress control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailAddress;

		/// <summary>
		/// messageSMTPUserAndReplyUserNotTheSame control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSMTPUserAndReplyUserNotTheSame;

		/// <summary>
		/// textboxMailDisplayName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailDisplayName;

		/// <summary>
		/// textboxMailAddressForReplyTo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailAddressForReplyTo;

		/// <summary>
		/// message1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message1;

		/// <summary>
		/// textboxMailEditSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailEditSubject;

		/// <summary>
		/// messageMailEditSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailEditSubjectFields;

		/// <summary>
		/// checkboxMailAllowToEditSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailAllowToEditSubject;

		/// <summary>
		/// checkboxQueuePriority control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxQueuePriority;

		/// <summary>
		/// dropdownlistMailSignatureBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailSignatureBehaviour;

		/// <summary>
		/// textboxMailSignature control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailSignature;

		/// <summary>
		/// messageMailSignature control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailSignature;

		/// <summary>
		/// dropdownlistMailQuoteType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailQuoteType;

		/// <summary>
		/// textboxServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxServiceName;

		/// <summary>
		/// customvalidatorServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorServiceName;

		/// <summary>
		/// checkboxEmailAddCC control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAddCC;

		/// <summary>
		/// checkboxEmailAddBCC control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAddBCC;

		/// <summary>
		/// checkboxAnswerOutsideDomainAvailable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAnswerOutsideDomainAvailable;

		/// <summary>
		/// textboxAvailableDomains control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAvailableDomains;

		/// <summary>
		/// checkboxReplyOptionsAvailable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReplyOptionsAvailable;

		/// <summary>
		/// placeholderMailCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderMailCheckSpelling;

		/// <summary>
		/// checkboxMailCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailCheckSpelling;

		/// <summary>
		/// checkboxChangeFontAvailable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxChangeFontAvailable;

		/// <summary>
		/// dropdownlistFontType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistFontType;

		/// <summary>
		/// textboxFontSize control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFontSize;

		/// <summary>
		/// textboxFavoriteMails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFavoriteMails;

		/// <summary>
		/// dropdownlistMailQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailQueue;

		/// <summary>
		/// checkboxMailDownloadOriginal control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailDownloadOriginal;

		/// <summary>
		/// checkboxMailDownloadOriginalAgents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxMailDownloadOriginalAgents;

		/// <summary>
		/// dropdownlistMailGenerateMultipleRepliesBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistMailGenerateMultipleRepliesBehaviour;

		/// <summary>
		/// messageMailGenerateMultipleRepliesBehaviourNoOtherServices control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailGenerateMultipleRepliesBehaviourNoOtherServices;

		/// <summary>
		/// panelMailGenerateMultipleRepliesBehaviour control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelMailGenerateMultipleRepliesBehaviour;

		/// <summary>
		/// listboxServicesForMultipleRepliesGeneration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxServicesForMultipleRepliesGeneration;

		/// <summary>
		/// checkboxEmailAllowToSendAttachments control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAllowToSendAttachments;

		/// <summary>
		/// textboxEmailMaxAttachments control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxEmailMaxAttachments;

		/// <summary>
		/// textboxEmailMaxSizeAttachment control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxEmailMaxSizeAttachment;

		/// <summary>
		/// checkboxEmailAcceptedTypeAllFiles control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeAllFiles;

		/// <summary>
		/// checkboxEmailAcceptedTypeImages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeImages;

		/// <summary>
		/// checkboxEmailAcceptedTypeText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeText;

		/// <summary>
		/// checkboxEmailAcceptedTypeAudio control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeAudio;

		/// <summary>
		/// checkboxEmailAcceptedTypePDF control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypePDF;

		/// <summary>
		/// checkboxEmailAcceptedTypeWord control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeWord;

		/// <summary>
		/// checkboxEmailAcceptedTypeExcel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeExcel;

		/// <summary>
		/// checkboxEmailAcceptedTypePPT control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypePPT;

		/// <summary>
		/// checkboxEmailAcceptedTypeZip control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailAcceptedTypeZip;

		/// <summary>
		/// textboxEmailZipOptionsMaxSize control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxEmailZipOptionsMaxSize;

		/// <summary>
		/// checkboxEmailZipOptionsIncludeExe control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEmailZipOptionsIncludeExe;

		/// <summary>
		/// hiddenEmailDefaultExtension control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenEmailDefaultExtension;

		/// <summary>
		/// hiddenSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveys;

		/// <summary>
		/// messageNoSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveys;

		/// <summary>
		/// panelEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEnableSurveys;

		/// <summary>
		/// checkboxEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

		/// <summary>
		/// messageNoSurveysInTable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveysInTable;

		/// <summary>
		/// messageSurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyDisabled;

		/// <summary>
		/// dropdownSurvey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownSurvey;

		/// <summary>
		/// textboxSurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitation;

		/// <summary>
		/// messageSurveyInvitationFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyInvitationFields;

		/// <summary>
		/// textboxSurveyInvitationInteractive control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationInteractive;

		/// <summary>
		/// messageFilterEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

		/// <summary>
		/// textboxSurveyInvitationButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationButton;

		/// <summary>
		/// message2 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message2;

		/// <summary>
		/// textboxSurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyExpiration;

		/// <summary>
		/// textboxSurveySentRate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveySentRate;

		/// <summary>
		/// textboxSurveyTimeToSend control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTimeToSend;

		/// <summary>
		/// textboxSurveyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTags;

		/// <summary>
		/// textboxSurveyMessagesCount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyMessagesCount;

		/// <summary>
		/// textboxSurveyCaseDuration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyCaseDuration;

		/// <summary>
		/// checkboxSurveySendIfNewCaseExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseExists;

		/// <summary>
		/// checkboxSurveySendIfNewCaseHasTag control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseHasTag;

		/// <summary>
		/// checkboxSurveySendIfNewCaseClosedByYflow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseClosedByYflow;

		/// <summary>
		/// textboxSurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// textboxSurveyDontSendTotalSendMonthly control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendTotalSendMonthly;

		/// <summary>
		/// textboxSurveysIgnoreTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysIgnoreTags;

		/// <summary>
		/// informativeMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message informativeMessage;

		/// <summary>
		/// hiddenAuthenticationErrorConnection control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenAuthenticationErrorConnection;

		/// <summary>
		/// textboxMailAuthenticationErrorEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailAuthenticationErrorEmailSubject;

		/// <summary>
		/// textboxMailAuthenticationErrorEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailAuthenticationErrorEmails;

		/// <summary>
		/// textboxMailAuthenticationErrorEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailAuthenticationErrorEmailTemplate;

		/// <summary>
		/// messageMailAuthenticationErrorEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailAuthenticationErrorEmailTemplateFields;

		/// <summary>
		/// textboxMailMinutesForInactivity control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailMinutesForInactivity;

		/// <summary>
		/// hiddenMailInactivityDetectedConnection control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenMailInactivityDetectedConnection;

		/// <summary>
		/// textboxMailInactivityDetectedEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailInactivityDetectedEmailSubject;

		/// <summary>
		/// textboxMailInactivityDetectedEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailInactivityDetectedEmails;

		/// <summary>
		/// textboxMailInactivityDetectedEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMailInactivityDetectedEmailTemplate;

		/// <summary>
		/// messageMailInactivityDetectedEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMailInactivityDetectedEmailTemplateFields;

		/// <summary>
		/// checkboxCasesOverrideSystemSettings control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCasesOverrideSystemSettings;

		/// <summary>
		/// checkboxCheckLastQueueOfOpenCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueOfOpenCase;

		/// <summary>
		/// checkboxIgnoreLastQueueForSLMovedMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIgnoreLastQueueForSLMovedMessage;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseCases;

		/// <summary>
		/// checkboxReplyInCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReplyInCloseCase;

		/// <summary>
		/// textboxAutoReplyInCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyInCloseCaseText;

		/// <summary>
		/// textboxTagCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTagCloseCase;

		/// <summary>
		/// placeholderYFlowCasesRelated control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowCasesRelated;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseYFlowCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseYFlowCases;

		/// <summary>
		/// checkboxInvokeYFlowWhenClosedCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInvokeYFlowWhenClosedCases;

		/// <summary>
		/// hiddenServiceToCopy control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenServiceToCopy;

		/// <summary>
		/// buttonSave control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonSave;

		/// <summary>
		/// buttonCancel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCancel;

		/// <summary>
		/// buttonCopyService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCopyService;
	}
}
