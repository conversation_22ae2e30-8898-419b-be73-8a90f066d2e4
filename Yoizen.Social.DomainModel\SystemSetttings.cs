﻿using System.Data;
using System.Security.Cryptography;
using System.IO;
using Yoizen.Social.DomainModel.Settings;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Threading;
using System.Security.Authentication;
using Yoizen.Common;
using Newtonsoft.Json;
using Microsoft.Exchange;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Yoizen.Social.DomainModel
{
	/// <summary>
	/// Representa la colección de parámetros de configuración que tiene el sistema
	/// </summary>
	public class SystemSettings
	{	
		#region Fields

		private Dictionary<int, DateTime> lastDatabaseProblemsExceptionMailSent;
		private DateTime lastOutOfMemoryExceptionMailSent;

		#endregion

		#region Properties

		/// <summary>
		/// La instancia única de parámetros del sistema
		/// </summary>
		public static SystemSettings Instance { get; private set; }

		/// <summary>
		/// Devuelve o establece si se utilizará la fecha de envíado o la fecha de recibido en el sistema
		/// </summary>
		[LocalizedDescription("SystemSettings_UseSentDate")]
		public bool UseSentDate { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad máxima de mensajes que se le puede asignar a un Agente
		/// </summary>
		[LocalizedDescription("SystemSettings_MaxAssignableMessagesPerUser")]
		public short MaxAssignableMessagesPerUser { get; set; }

		/// <summary>
		/// Devuelve o establece si los mensajes se marcarán automáticamente como leídos en el Agente cuando se le asigne un mensaje.
		/// En caso de que en simultáneo se le asigne más de un mensaje al agente este parámetro no será tenido en cuenta.
		/// </summary>
		[LocalizedDescription("SystemSettings_AutoMarkAsReadMessages")]
		public bool AutoMarkAsReadMessages { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos que utilizará la aplicación para considerar que un agente se desconectó
		/// en forma abrupta (que no manda KeepAlive)
		/// </summary>
		[LocalizedDescription("SystemSettings_SessionTimeOut")]
		public short SessionTimeOut { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema marcará automáticamente los mensajes provenientes de usuarios de la WhiteList
		/// como VIM (Importantes)
		/// </summary>
		[LocalizedDescription("SystemSettings_MarkWhiteListMessagesAsVIM")]
		public bool MarkWhiteListMessagesAsVIM { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad máxima de mensajes que tendrán las conversaciones que se le devuelve a un Agente cuando
		/// se le asignan mensajes
		/// </summary>
		/// <remarks>
		/// El valor máximo puede ser 20
		/// </remarks>
		[LocalizedDescription("SystemSettings_AgentHistoricMaxMessages")]
		public short AgentHistoricMaxMessages { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema de agente advertirá al agente al momento de responder si hay errores
		/// ortográficos
		/// </summary>
		[LocalizedDescription("SystemSettings_WarnAgentForSpellingErrors")]
		public bool WarnAgentForSpellingErrors { get; set; }

		/// <summary>
		/// Devuelve o establece si el agente deberá o no ingresar la razón por la cual descarta mensajes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentMustEnterDiscardReason")]
		public bool AgentMustEnterDiscardReason { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema de agente permitirá a los agentes bloquear usuarios a la hora de descartar
		/// un mensaje
		/// </summary>
		[LocalizedDescription("SystemSettings_AllowAgentToBlockUsers")]
		public bool AllowAgentToBlockUsers { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad máxima de reintentos para enviar mensajes
		/// </summary>
		[LocalizedDescription("SystemSettings_MaxRetriesForOutgoingMessages")]
		public int MaxRetriesForOutgoingMessages { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad máxima de resultados a mostrar en las grillas
		/// </summary>
		[LocalizedDescription("SystemSettings_MaxResultsInGrids")]
		public int MaxResultsInGrids { get; set; }

		/// <summary>
		/// Devuelve los usuarios que pertenecen a la white list
		/// </summary>
		[LocalizedDescription("SystemSettings_VIPSocialUsers")]
		public List<Settings.SocialUserReference> VIPSocialUsers { get; private set; }

		/// <summary>
		/// Devuelve los usuarios que pertenecen a la white list
		/// </summary>
		[LocalizedDescription("SystemSettings_TesterSocialUsers")]
		public List<Settings.SocialUserReference> TesterSocialUsers { get; private set; }

		/// <summary>
		/// Devuelve los usuarios que pertenecen a la Do Not Call list
		/// </summary>
		[LocalizedDescription("SystemSettings_DoNotCallSocialUsers")]
		public List<Settings.SocialUserReference> DoNotCallSocialUsers { get; private set; }

		/// <summary>
		/// Devuelve los usuarios que pertenecen a la black list
		/// </summary>
		[LocalizedDescription("SystemSettings_BlockedSocialUsers")]
		public List<Settings.SocialUserReference> BlockedSocialUsers { get; private set; }

		/// <summary>
		/// Devuelve o establece si los mensajes salientes están habilitados
		/// </summary>
		[LocalizedDescription("SystemSettings_OutgoingMessagesEnabled")]
		public bool OutgoingMessagesEnabled { get; set; }

		/// <summary>
		/// Devuelve o establece el número de motivo de auxiliar que se utilizará para mensajes salientes o null
		/// si no hay ninguno especificado
		/// </summary>
		[LocalizedDescription("SystemSettings_AuxReasonForOutgoingMessages")]
		public short? AuxReasonForOutgoingMessages { get; set; }

		/// <summary>
		/// Devuelve o establece el número de motivo de auxiliar que se utilizará para mensajes salientes o null
		/// si no hay ninguno especificado
		/// </summary>
		[LocalizedDescription("SystemSettings_AuxReasonForMyOutgoingCases")]
		public short? AuxReasonForMyOutgoingCases { get; set; }

		/// <summary>
		/// Devuelve los códigos de servicios para los cuales se advertirá al agente cuando ingresen mensajes y el texto del mensaje
		/// </summary>
		[LocalizedDescription("SystemSettings_ServicesToWarnForMessages")]
		public Dictionary<int, string> ServicesToWarnForMessages { get; private set; }

		/// <summary>
		/// Devuelve la cantidad de intervalos que hay por hora en el sistema
		/// </summary>
		[LocalizedDescription("SystemSettings_IntervalsPerHour")]
		public byte IntervalsPerHour { get; private set; }

		/// <summary>
		/// Devuelve o establece si se permite que los agentes retornen los mensajes a las colas
		/// </summary>
		[LocalizedDescription("SystemSettings_AllowAgentsToReturnMessagesToQueue")]
		public bool AllowAgentsToReturnMessagesToQueue { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes deberán ingresar el motivo cuando devuelven el mensaje a la cola 
		/// (siempre y cuando <see cref="AllowAgentsToReturnMessagesToQueue"/> sea <code>true</code>)
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentMustEnterReturnToQueueReason")]
		public bool AgentMustEnterReturnToQueueReason { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad máxima de veces que un mensaje puede ser devuelto a la cola
		/// (siempre y cuando <see cref="AllowAgentsToReturnMessagesToQueue"/> sea <code>true</code>)
		/// </summary>
		[LocalizedDescription("SystemSettings_MaximumNumberOfTimesMessageCanBeReturned")]
		public short MaximumNumberOfTimesMessageCanBeReturned { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes pueden seleccionar la cola a la cual se devolverá los mensajes
		/// </summary>
		[LocalizedDescription("SystemSettings_AllowAgentsToSelectQueueOnReturnToQueue")]
		public bool AllowAgentsToSelectQueueOnReturnToQueue { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes pueden retornar mensajes que ya tienen respuestas o mensajes salientes relacionados
		/// </summary>
		[LocalizedDescription("SystemSettings_AllowAgentsToReturnMessagesWithRelatedMessagesToQueue")]
		public bool AllowAgentsToReturnMessagesWithRelatedMessagesToQueue { get; set; }

		/// <summary>
		/// Devuelve la configuración de los parámetros de Email
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailConnection")]
		public EmailConnectionSettings EmailConnection { get; private set; }

		/// <summary>
		/// Devuelve la configuración de los parámetros de las conexEmail
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailConnections")]
		public Dictionary<Guid, EmailConnectionSettings> EmailConnections { get; set; }

		/// <summary>
		/// Devuelve la configuración del servicio
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Service")]
		public SocialServiceSettings Service { get; private set; }

		/// <summary>
		/// Devuelve la configuración del servicio de exportación
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_ExporterService")]
		public ExporterServiceSettings ExporterService { get; private set; }

		/// <summary>
		/// Devuelve la configuración del servicio de encuestas
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_SurveysService")]
		public SurveysServiceSettings SurveysService { get; private set; }

		/// <summary>
		/// Devuelve el tipo de autenticación
		/// </summary>
		[LocalizedDescription("SystemSettings_AuthenticationType")]
		public AuthenticationTypes AuthenticationType { get; set; }

		/// <summary>
		/// Devuelve la configuración de LDAP
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_LDAP")]
		public LDAPSettings LDAP { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Google Auth
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_GoogleAuth")]
		public GoogleAuthSettings GoogleAuth { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Auth con SAML
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_SamlAuth")]
		public KeycloakSettings SamlAuth { get; private set; }

		/// <summary>
		/// Devuelve la configuración de BitLy
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_BitLy")]
		public BitLySettings BitLy { get; private set; }

		/// <summary>
		/// Devuelve la configuración de servicios cognitivos
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_CognitiveServices")]
		public CognitiveServicesSettings CognitiveServices { get; private set; }

		/// <summary>
		/// Devuelve la configuración de yFlow
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_YFlow")]
		public YFlowSettings YFlow { get; private set; }

		/// <summary>
		/// Devuelve la configuración de yUsage
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_YUsage")]
		public YUsageSettings YUsage { get; private set; }

		/// <summary>
		/// Devuelve la configuración de yFlow Contingencia
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_YFlowContingency")]
		public YFlowSettings YFlowContingency { get; private set; }

		/// <summary>
		/// Devuelve la configuración de chat integrado
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_RestChat")]
		public RestChatSettings RestChat { get; private set; }

		/// <summary>
		/// Devuelve la configuración de mantenimiento
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Maintenance")]
		public MaintenanceSettings Maintenance { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Casos
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Cases")]
		public CasesSettings Cases { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Chat
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Chat")]
		public ChatSettings Chat { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Chat Interno
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_InternalChat")]
		public InternalChatSettings InternalChat { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Whatsapp
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Whatsapp")]
		public WhatsappSettings Whatsapp { get; private set; }

		/// <summary>
		/// Devuelve la configuración de apple messaging
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_AppleMessaging")]
		public AppleMessagingSettings AppleMessaging { get; private set; }

		/// <summary>
		/// Devuelve la configuración de MercadoLibre
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_MercadoLibre")]
		public MercadoLibreSettings MercadoLibre { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Telegram
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Telegram")]
		public TelegramSettings Telegram { get; private set; }

		/// <summary>
		/// Devuelve la configuración de GoogleRBM
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_GoogleRBM")]
		public GoogleRBMSettings GoogleRBM { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Twitter
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Twitter")]
		public TwitterSettings Twitter { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Facebook
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Facebook")]
		public FacebookSettings Facebook { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Facebook
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_FacebookMessenger")]
		public FacebookMessengerSettings FacebookMessenger { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Instagram
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Instagram")]
		public InstagramSettings Instagram { get; private set; }

		/// <summary>
		/// Devuelve la configuración de LinkedIn
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_LinkedIn")]
		public LinkedInSettings LinkedIn { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Mail
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Mail")]
		public MailSettings Mail { get; private set; }

		/// <summary>
		/// Devuelve la configuración de SMS
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_SMS")]
		public SMSSettings SMS { get; private set; }

		/// <summary>
		/// Devuelve la configuración de Skype
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Skype")]
		public SkypeSettings Skype { get; private set; }

		/// <summary>
		/// Devuelve la configuración de suscripción a los eventos de la nube
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Subscription")]
		public SubscriptionSettings Subscription { get; private set; }

		/// <summary>
		/// Devuelve o establece si se permite reenvio de mensajes a través de acciones
		/// </summary>
		[LocalizedDescription("SystemSettings_AllowForwardAction")]
		public bool AllowForwardAction { get; set; }

		/// <summary>
		/// Devuelve o establece si se pueden forwardear mensajes por fuera del dominio.
		/// </summary>
		[LocalizedDescription("SystemSettings_ForwardOutsideDomainAvailable")]
		public bool ForwardOutsideDomainAvailable { get; set; }

		/// <summary>
		/// Devuelve la ruta donde se guardan los archivos adjuntos de mail y chat
		/// </summary>
		[LocalizedDescription("SystemSettings_AttachmentsRoute")]
		public string AttachmentsRoute { get; set; }

		/// <summary>
		/// Devuelve los parámetros de configuracion para la respuesta de los forward
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_ForwardSettings")]
		public ForwardSettings ForwardSettings { get; set; }

		/// <summary>
		/// Devuelve los mails favoritos especificados para utilizar en el sistema
		/// </summary>
		[LocalizedDescription("SystemSettings_FavoriteMails")]
		public string FavoriteMails { get; set; }

		/// <summary>
		/// Devuelve o establece si se encriptarán los mensajes
		/// </summary>
		[LocalizedDescription("SystemSettings_EncryptMessages")]
		public bool EncryptMessages { get; set; }

		/// <summary>
		/// Devuelve o establece si se obliga a los usuarios a establecer two factor authentication
		/// </summary>
		[LocalizedDescription("SystemSettings_UserMandatory2FA")]
		public bool UserMandatory2FA { get; set; }

		/// <summary>
		/// Devuelve o establece el mínimo nivel de seguridad de la contraseña de los usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_MinimumPasswordStrength")]
		public byte MinimumPasswordStrength { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de dias en que expira la contraseña de los usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordExpireDay")]
		public int UserPasswordExpireDay { get; set; }

		/// <summary>
		/// Devuelve o establece si se puede repetir la contraseña de los usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordRepeat")]
		public bool UserPasswordRepeat { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de veces que los usuarios pueden ingresar la contraseña de forma incorrecta antes que se le bloque la cuenta
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordWrongBlock")]
		public int UserPasswordWrongBlock { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de veces que los usuarios pueden ingresar la contraseña de forma incorrecta antes que se le solicite un captcha
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordWrongsCaptha")]
		public int UserPasswordWrongsCaptha { get; set; }

		/// <summary>
		/// Devuelve o establece el numero de ultimas contraseñas que no pueden ser repetidas para los usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordRepeatAmount")]
		public int UserPasswordRepeatAmount { get; set; }

		/// <summary>
		/// Devuelve o establece si los usuarios pueden blanquear su contraseña
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordRefresh")]
		public bool UserPasswordRefresh { get; set; }

		/// <summary>
		/// Devuelve o establece si los usuarios deben cambiar su contraseña al iniciar sesion por primera vez
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordFirstChange")]
		public bool UserPasswordFirstChange { get; set; }

		/// <summary>
		/// Devuelve o establece la expresión regular que debe validarse para las contraseñas de usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordValidationRegex")]
		public string UserPasswordValidationRegex { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje de error si no cumple con al regex
		/// </summary>
		[LocalizedDescription("SystemSettings_UserPasswordMessageRegex")]
		public string UserPasswordMessageRegex { get; set; }

		/// <summary>
		/// Devuelve o establece los dias de inactividad para bloqueo de usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_DaysInactiveUserBlock")]
		public int DaysInactiveUserBlock { get; set; }

		/// <summary>
		/// Devuelve o establece si se obliga a los usuarios a establecer two factor authentication
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentMandatory2FA")]
		public bool AgentMandatory2FA { get; set; }

		/// <summary>
		/// Devuelve o establece el mínimo nivel de seguridad de la contraseña de los agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_MinimumAgentPasswordStrength")]
		public byte MinimumAgentPasswordStrength { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de dias en que expira la contraseña de los agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordExpireDay")]
		public int AgentPasswordExpireDay { get; set; }

		/// <summary>
		/// Devuelve o establece si se puede repetir la contraseña de los agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordRepeat")]
		public bool AgentPasswordRepeat { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de veces que los agentes pueden ingresar la contraseña de forma incorrecta antes que se le bloque la cuenta
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordWrongsBlock")]
		public int AgentPasswordWrongsBlock { get; set; }

		/// <summary>
		/// Devuelve o establece el score que debera ser validado contra el recaptcha
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordWrongsCaptha")]
		public int AgentPasswordWrongsCaptha { get; set; }

		/// <summary>
		/// Devuelve o establece el numero de ultimas contraseñas que no pueden ser repetidas para los agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordRepeatAmount")]
		public int AgentPasswordRepeatAmount { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes pueden blanquear su contraseña
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordRefresh")]
		public bool AgentPasswordRefresh { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes deben cambiar su contraseña al iniciar sesion por primera vez
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordFirstChange")]
		public bool AgentPasswordFirstChange { get; set; }

		/// <summary>
		/// Devuelve o establece la expresión regular que debe validarse para las contraseñas de agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordValidationRegex")]
		public string AgentPasswordValidationRegex { get; set; }
		
		/// <summary>
		/// Devuelve o establece la expresión regular que debe validarse para las contraseñas de agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentPasswordMessageRegex")]
		public string AgentPasswordMessageRegex { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes pueden cambiar su contrasena
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentCanChangePassword")]
		public bool AgentCanChangePassword { get; set; }

		/// <summary>
		/// Devuelve o establece los dias de inactividad para bloqueo de agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_DaysInactiveAgentBlock")]
		public int DaysInactiveAgentBlock { get; set; }

		/// <summary>
		/// Devuelve o establece los dominios disponibles para envio de cc/cco
		/// </summary>
		[LocalizedDescription("SystemSettings_AvailableDomainsToForward")]
		public string AvailableDomainsToForward { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de días que se guardan las notificaciones para después borrarlas
		/// </summary>
		[LocalizedDescription("SystemSettings_DeleteNotifications")]
		public int DeleteNotifications { get; set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails en los filtros
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_FilterEmailSettings")]
		public FilterEmailSettings FilterEmailSettings { get; private set; }

		/// <summary>
		/// Devuelve o establece el tamaño mínimo libre a considerar para notificar por poco espacio en disco para adjuntos
		/// </summary>
		[LocalizedDescription("SystemSettings_AttachmentsMinimumFreeSpace")]
		public int AttachmentsMinimumFreeSpace { get; set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando el espacio libre en disco es inferior al valor especificado por 
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_OutOfDiskSpaceForAttachments")]
		public EmailSettings OutOfDiskSpaceForAttachments { get; private set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando ocurre un error crítico con la base de datos
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailDatabaseProblems")]
		public EmailSettings EmailDatabaseProblems { get; private set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando ocurre un error de falta de memoria
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailOutOfMemory")]
		public EmailSettings EmailOutOfMemory { get; private set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando se crea un agente
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_AgentCreatedLoginInformation")]
		public EmailSettings AgentCreatedLoginInformation { get; private set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando se modifica la contraseña a un agente
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_AgentPasswordChanged")]
		public EmailSettings AgentPasswordChanged { get; private set; }

		/// <summary>
		/// Devuelve o establece el nombre del archivo MSI del cliente
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentApplicationSetupFile")]
		public string AgentApplicationSetupFile { get; set; }

		/// <summary>
		/// Devuelve o establece el nombre del archivo MSI del cliente
		/// </summary>
		[LocalizedDescription("SystemSettings_ChatPackageSetupFile")]
		public string ChatPackageSetupFile { get; set; }

		/// <summary>
		/// Devuelve o establece la versión del cliente pesado del agente
		/// </summary>
		[LocalizedDescription("SystemSettings_AgentApplicationVersion")]
		public string AgentApplicationVersion { get; set; }

		/// <summary>
		/// Devuelve o establece la versión del agente web
		/// </summary>
		[LocalizedDescription("SystemSettings_WebAgentVersion")]
		public string WebAgentVersion { get; set; }

		/// <summary>
		/// Devuelve o establece la URL del agente web
		/// </summary>
		[LocalizedDescription("SystemSettings_WebAgentURL")]
		public string WebAgentURL { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite a los agentes iniciar sesión con la versión desactualizada de los scripts
		/// </summary>
		[LocalizedDescription("SystemSettings_WebAgentAllowOutdatedLogins")]
		public bool WebAgentAllowOutdatedLogins { get; set; }

		/// <summary>
		/// Devuelve la cantidad máxima de registros que se pueden exportar
		/// </summary>
		[LocalizedDescription("SystemSettings_MaxRecordsToExport")]
		public int MaxRecordsToExport { get; private set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos para abortar un reporte
		/// </summary>
		[LocalizedDescription("SystemSettings_MinutesToAbortExporting")]
		public int MinutesToAbortExporting { get; set; }

		/// <summary>
		/// Devuelve o establece los datos del email que se enviará cuando se haya generado un reporte
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailExport")]
		public EmailSettings EmailExport { get; set; }

		/// <summary>
		/// Devuelve o establece los datos del email que se enviará cuando se haya abortado la generación un reporte
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailExportAborted")]
		public EmailSettings EmailExportAborted { get; set; }

		/// <summary>
		/// Devuelve o establece los datos del email que se enviará cuando se haya generado el reporte diario
		/// de llamadas
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_EmailDailyReports")]
		public EmailSettings EmailDailyReports { get; set; }

		/// <summary>
		/// Devuelve o establece si se generarán reportes diarios
		/// </summary>
		[LocalizedDescription("SystemSettings_GenerateDailyReport")]
		public bool GenerateDailyReport { get; set; }

		/// <summary>
		/// Devuelve o establece si se guardarán los reportes en un ftp
		/// </summary>
		[LocalizedDescription("SystemSettings_EnableFtpDailyReports")]
		public bool EnableFtpDailyReports { get; set; }

		/// <summary>
		/// Devuelve o establece los datos del email que se enviará cuando se haya generado el reporte diario
		/// de llamadas
		/// </summary>
		[LocalizedDescription("SystemSettings_FtpDailyReports")]
		public string FtpIdDailyReports { get; set; }

		/// <summary>
		/// Devuelve o establece los datos del email que se enviará cuando se haya generado el reporte diario
		/// de llamadas
		/// </summary>
		[LocalizedDescription("SystemSettings_FtpDirectoryDailyReports")]
		public string FtpDirectoryDailyReports { get; set; }

		/// <summary>
		/// Devuelve o establece si los reportes diarios exportados en excel a un FTP deberán ser comprimidos
		/// de llamadas
		/// </summary>
		[LocalizedDescription("SystemSettings_DailyReportsZipExcel")]
		public bool DailyReportsZipExcel { get; set; }

		/// <summary>
		/// Devuelve o establece si los reportes diarios exportados en excel a un FTP deberán ser comprimidos
		/// de llamadas
		/// </summary>
		[LocalizedDescription("SystemSettings_DailyReportsZipCSV")]
		public bool DailyReportsZipCSV { get; set; }


		/// <summary>
		/// Devuelve o establece la cantidad archivos diarios históricos de detalle de llamadas serán mantenidos
		/// </summary>
		/// <remarks>
		/// El valor aceptado es entre 1 y 31
		/// </remarks>
		[LocalizedDescription("SystemSettings_DailyReportsToMantain")]
		public int DailyReportsToMantain { get; set; }

		/// <summary>
		/// Devuelve o establece los tipos de reporte que se generarán diariamente
		/// </summary>
		[LocalizedDescription("SystemSettings_DailyReportsToGenerate")]
		public Reports.ReportTypes[] DailyReportsToGenerate { get; set; }

		/// <summary>
		/// Devuelve o establece los formatos de salida de los reportes diarios
		/// </summary>
		[LocalizedDescription("SystemSettings_DailyReportsFormat")]
		public Reports.Export.ExportFormats DailyReportsFormat { get; set; }

		/// <summary>
		/// Devuelve o establece si los archivos de reportes locales se deberán borrar (siempre y cuando el reporte se suba a Storage)
		/// </summary>
		[LocalizedDescription("SystemSettings_DailyReportsDeleteLocalFiles")]
		public bool DailyReportsDeleteLocalFiles { get; set; }

		/// <summary>
		/// Devuelve o establece si se habilita el uso de las encuestas
		/// </summary>
		[LocalizedDescription("SystemSettings_EnableSurveys")]
		public bool EnableSurveys { get; set; }

		/// <summary>
		/// Devuelve o establece la URL donde estará alojado el servicio de Encuestas
		/// </summary>
		[LocalizedDescription("SystemSettings_SurveysURL")]
		public string SurveysURL { get; set; }

		/// <summary>
		/// Devuelve o establece si va a tener más prioridad los mensajes importantes por sobre los niveles
		/// de las colas
		/// </summary>
		[LocalizedDescription("SystemSettings_PrioritizeVIMOverQueueLevel")]
		public bool PrioritizeVIMOverQueueLevel { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema utilizará balanceo de distribución de mensajes entre los agentes
		/// </summary>
		[LocalizedDescription("SystemSettings_UseACDBalancing")]
		public bool UseACDBalancing { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema utilizará balanceo de distribución de mensajes entre los agentes
		/// tomando en cuenta los niveles de colas de cada agente
		/// </summary>
		[LocalizedDescription("SystemSettings_ACDBalancingWithQueueLevels")]
		public bool ACDBalancingWithQueueLevels { get; set; }

		/// <summary>
		/// Devuelve o establece si el sistema priorizará a los agentes en WORKING con nivel X por sobre otro agente
		/// en estado AVAILABLE con nivel mayor a X
		/// </summary>
		/// <remarks>
		/// Depende de que <see cref="ACDBalancingWithQueueLevels"/> sea <code>true</code>
		/// </remarks>
		[LocalizedDescription("SystemSettings_ACDBalancingWithQueueLevelsWorking")]
		public bool ACDBalancingWithQueueLevelsWorking { get; set; }

		/// <summary>
		/// Devuelve o establece los tipos de integraciones
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_Integrations")]
		public IntegrationsSettings Integrations { get; set; }

		/// <summary>
		/// Devuelve la configuración relacionada al envio de mails cuando se supera la cantidad de mensajes configurados para marcar a un usuario como molesto
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_AnnoyingEmailSettings")]
		public AnnoyingEmailSettings AnnoyingEmailSettings { get; private set; }

		/// <summary>
		/// Devuelve o establece la expresión regular utilizada para validar los datos de negocio del usuario
		/// </summary>
		[LocalizedDescription("SystemSettings_BusinessDataRegex")]
		public string BusinessDataRegex { get; set; }

		/// <summary>
		/// Devuelve o establece la definición de los campos extra para el perfil de usuarios
		/// </summary>
		[LocalizedDescription("SystemSettings_ExtendedProfilesFields")]
		public ExtendedField[] ExtendedProfilesFields { get; set; }

		/// <summary>
		/// Devuelve o establece la definición de los campos extra para los casos
		/// </summary>
		[LocalizedDescription("SystemSettings_ExtendedCasesFields")]
		public ExtendedField[] ExtendedCasesFields { get; set; }

		/// <summary>
		/// Devuelve o establece la definición de los campos que forman parte del código de cliente
		/// </summary>
		[LocalizedDescription("SystemSettings_ExtendedProfilesBusinessCodeFields")]
		public ExtendedProfileBusinessCodeField[] ExtendedProfilesBusinessCodeFields { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje de error a mostrar a los agentes/supervisores cuando los datos de negocios ingresados
		/// no respeten la expresión regular
		/// </summary>
		[LocalizedDescription("SystemSettings_BusinessDataFormatMessage")]
		public string BusinessDataFormatMessage { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje de error a mostrar a los agentes/supervisores cuando los datos de negocios ingresados
		/// no respeten la expresión regular
		/// </summary>
		[LocalizedDescription("SystemSettings_BusinessDataWrongInputMessage")]
		public string BusinessDataWrongInputMessage { get; set; }

		/// <summary>
		/// Devuelve la configuración de Login con el Agente Web
		/// </summary>
		[LocalizedDescription("SystemSettings_WebAgentUrlLoginSettings")]
		public WebAgentUrlLoginSettings WebAgentUrlLoginSettings { get; private set; }

		/// <summary>
		/// Devuelve la configuración de manejo de estado mediante mensajería interna del browser
		/// </summary>
		[LocalizedDescription("SystemSettings_WebAgentStateManagementSettings")]
		public WebAgentStateManagementSettings WebAgentStateManagementSettings { get; private set; }

		/// <summary>
		/// Devuelve o establece si para los notificaciones push con Service Bus se utilizará WebSockets (<code>true</code>) o AMPQ (<code>false</code>)
		/// </summary>
		[LocalizedDescription("SystemSettings_PushNotificationsServiceBusUseWebSockets")]
		public bool PushNotificationsServiceBusUseWebSockets { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de mensajes concurrentes que se procesarán del service bus para los notificaciones push
		/// </summary>
		[LocalizedDescription("SystemSettings_PushNotificationsServiceBusConcurrentMessages")]
		public int PushNotificationsServiceBusConcurrentMessages { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de estados de mensajes concurrentes que se procesarán del service bus para los notificaciones push
		/// </summary>
		[LocalizedDescription("SystemSettings_PushNotificationsServiceBusConcurrentStatuses")]
		public int PushNotificationsServiceBusConcurrentStatuses { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de novedades de llamadas de WhatsApp concurrentes que se procesarán del service bus para los notificaciones push
		/// </summary>
		[LocalizedDescription("SystemSettings_PushNotificationsServiceBusConcurrentCalls")]
		public int PushNotificationsServiceBusConcurrentCalls { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de mensajes concurrentes que se procesarán del service bus para los mensajes
		/// que se envían por una tarea masiva
		/// </summary>
		[LocalizedDescription("SystemSettings_PushNotificationsServiceBusConcurrentMassive")]
		public int PushNotificationsServiceBusConcurrentMassive { get; set; }

		/// <summary>
		/// Devuelve o establece la definición de los encabezados extra a agregar por el servidor web
		/// </summary>
		[LocalizedDescription("SystemSettings_CloudHeadersToAdd")]
		public ExtraHeader[] CloudHeadersToAdd { get; set; }

		/// <summary>
		/// Devuelve o establece la definición de los encabezados a remover por el servidor web
		/// </summary>
		[LocalizedDescription("SystemSettings_CloudHeadersToRemove")]
		public string[] CloudHeadersToRemove { get; set; }

		/// <summary>
		/// Devuelve o establece el listado de IPs que tendrán acceso a la plataforma. Cualquier IP fuera de esta lista
		/// no tendrá acceso al sitio web de Administración/Supervisión
		/// </summary>
		/// <remarks>
		/// En caso de <code>null</code> o vacía implica que no hay restricción
		/// </remarks>
		[LocalizedDescription("SystemSettings_RestrictedIPsForWeb")]
		public string[] RestrictedIPsForWeb { get; set; }

		/// <summary>
		/// Devuelve o establece el listado de IPs que tendrán acceso a la plataforma. Cualquier IP fuera de esta lista
		/// no tendrá acceso al sitio web de agentea
		/// </summary>
		/// <remarks>
		/// En caso de <code>null</code> o vacía implica que no hay restricción
		/// </remarks>
		[LocalizedDescription("SystemSettings_RestrictedIPsForWebAgent")]
		public string[] RestrictedIPsForWebAgent { get; set; }

		/// <summary>
		/// Devuelve o establece el código de grupo de agentes que se utilizará para dar de alta agentes provenientes de
		/// integraciones con centrales telefónicas
		/// </summary>
		/// <remarks>
		/// En caso de <code>null</code> indica que no hay ninguno definido y no se utilizará una plantilla para dar de alta agentes
		/// </remarks>
		[LocalizedDescription("SystemSettings_PbxIntegrationAgentGroupForNewAgents")]
		public int? PbxIntegrationAgentGroupForNewAgents { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de días y horarios laborables
		/// </summary>
		[LocalizedDescription("SystemSettings_WorkingTimes")]
		[ExpandableProperty]
		public WorkingTimesSettings WorkingTimes { get; set; }

		/// <summary>
		/// Devuelve o establece la zona horaria por defecto a utilizar
		/// </summary>
		[LocalizedDescription("SystemSettings_DefaultTimeZone")]
		public TimeZoneInfo DefaultTimeZone { get; set; }

		/// <summary>
		/// Devuelve la zona horaria local
		/// </summary>
		[LocalizedDescription("SystemSettings_LocalTimeZone")]
		public TimeZoneInfo LocalTimeZone { get; private set; }

		/// <summary>
		/// Devuelve o establece la configuración regional por defecto a utilizar
		/// </summary>
		[LocalizedDescription("SystemSettings_DefaultLocale")]
		public string DefaultLocale { get; set; }

		/// <summary>
		/// Devuelve o establece las zona horaria que se utilizarán para realizar la consolidación además de <see cref="DefaultTimeZone"/>
		/// </summary>
		[LocalizedDescription("SystemSettings_TimeZonesToConsolide")]
		public TimeZoneInfo[] TimeZonesToConsolide { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración para notificaciones por mensajes que no pudieron enviar
		/// </summary>
		[LocalizedDescription("SystemSettings_DeliveryFailedNotification")]
		public DeliveryFailedNotificationSettings DeliveryFailedNotification { get; set; }

		/// <summary>
		/// Devuelve o establece el idioma principal
		/// </summary>
		[LocalizedDescription("SystemSettings_DefaultLanguage")]
		public string DefaultLanguage { get; set; }
		/// <summary>
		/// Devuelve o establece todos los idiomas disponibles
		/// </summary>
		[LocalizedDescription("SystemSettings_EnabledLanguages")]
		public List<string> EnabledLanguages { get; set; }


		/// <summary>
		/// Devuelve o establece la configuración de los FTPS
		/// </summary>
		[LocalizedDescription("SystemSettings_FTPs")]
		[ExpandableProperty]
		public Dictionary<Guid, Settings.UploadFileConnectionSettings> FTPs { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de los distintos tipos de reportes exportables de los FTPS
		/// </summary>
		[LocalizedDescription("SystemSettings_AutomaticExports")]
		[ExpandableProperty]
		public AutomaticExportSettings AutomaticExportSettings { get; set; }

		/// <summary>
		/// Devuelve o establece el tema a usar
		/// </summary>
		[LocalizedDescription("SystemSettings_Theme")]
		public string Theme { get; set; }

		/// <summary>
		/// Devuelve o establece los minutos para el calculo del predicted Aht
		/// </summary>
		[LocalizedDescription("SystemSettings_MinutesPredictedAht")]
		public int MinutesPredictedAht { get; set; }

		/// <summary>
		/// Devuelve o establece los segundos para el calculo del Ewt
		/// </summary>
		[LocalizedDescription("SystemSettings_SecondsEwt")]
		public int SecondsEwt { get; set; }


        /// <summary>
        /// Establece si hay definido un valor por defecto de ASA para el calculo del AHT
        /// </summary>
		[LocalizedDescription("SystemSettings_AllowToSetASAValueByDefault")]
        public bool AllowToSetASAValueByDefault { get; set; }


        /// <summary>
        /// Devuelve el valor predefinido global medido en segundos del ASA
        /// </summary>
		[LocalizedDescription("SystemSettings_ASADefaultValue")]
        public int ASADefaultValue { get; set; }

		/// <summary>
		/// Establece la URL para poder interactuar con la API de video de Cubiq
		/// </summary>
		[LocalizedDescription("SystemSettings_VideoCubiqUrl")]
		public string VideoCubiqUrl { get; set; }

		/// <summary>
		/// Establece la Api Key para poder autenticar requests a la API de video de Cubiq
		/// </summary>
		[LocalizedDescription("SystemSettings_VideoCubiqApiKey")]
		public string VideoCubiqApiKey { get; set; }

		/// <summary>
		/// Establece el secret necesario para poder autenticar requests a la API de video de Cubiq
		/// </summary>
		[LocalizedDescription("SystemSettings_VideoCubiqSecret")]
		public string VideoCubiqSecret { get; set; }

		/// <summary>
		/// Establece la url del server en el que se encuentran las grabaciones de video de Cubiq
		/// </summary>
		[LocalizedDescription("SystemSettings_VideoCubiqRecordingUrl")]
		public string VideoCubiqRecordingUrl { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de gateway siempre y cuando se trabaje como Gateway en la licencia
		/// </summary>
		[LocalizedDescription("SystemSettings_Gateway")]
		[ExpandableProperty]
		public GatewaySettings Gateway { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de storage
		/// </summary>
		[LocalizedDescription("SystemSettings_Storage")]
		[ExpandableProperty]
		public StorageSettings Storage { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de realtime
		/// </summary>
		[LocalizedDescription("SystemSettings_RealTime")]
		[ExpandableProperty]
		public RealTimeSettings RealTime { get; set; }

		/// <summary>
		/// Devuelve o establece los tipos de reportes programados
		/// </summary>
		[LocalizedDescription("SystemSettings_ScheduledReportsToGenerate")]
		public int ScheduledReportsToGenerate { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad archivos programados históricos serán mantenidos
		/// </summary>
		/// <remarks>
		/// El valor aceptado es entre 1 y 31
		/// </remarks>
		[LocalizedDescription("SystemSettings_ScheduledReportsToMantain")]
		public int ScheduledReportsToMantain { get; set; }

		/// <summary>
		/// Devuelve o establece si un mensaje puede ser movido a la cola donde ingresó el último mensaje
		/// en base a un tiempo asignado (con limite de 10 minutos).
		/// </summary>
		[LocalizedDescription("SystemSettings_CheckLastQueueByTime")]
		public bool CheckLastQueueByTime { get; set; }
		/// <summary>
		/// Devuelve el valor definido de tiempo asignado
		/// </summary>
		[LocalizedDescription("SystemSettings_CheckLastQueueByTimeValue")]
		public int CheckLastQueueByTimeValue { get; set; }
		/// <summary>
		/// Devuelve la configuración de ySmart para resumen de conversaciones
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("SystemSettings_YSmart")]
		public YSmartSettings YSmart { get; private set; }

		/// <summary>
		/// Devuelve el valor definido de tiempo asignado
		/// </summary>
		[LocalizedDescription("SystemSettings_EnableCapi")]
		public bool EnableCapi { get; set; }

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="SystemSettings"/>
		/// </summary>
		static SystemSettings()
		{
			Instance = new SystemSettings();
		}

		private SystemSettings()
		{
			this.LocalTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneInfo.Local.Id);
			this.DefaultTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneInfo.Local.Id);
			this.FTPs = new Dictionary<Guid, UploadFileConnectionSettings>();
			this.AutomaticExportSettings = new Settings.AutomaticExportSettings();
			this.DefaultLanguage = "es";
			this.DefaultLocale = "es";
			this.TimeZonesToConsolide = null;
			this.UseACDBalancing = false;
			this.ACDBalancingWithQueueLevels = false;
			this.ACDBalancingWithQueueLevelsWorking = true;
			this.MaxAssignableMessagesPerUser = 3;
			this.AutoMarkAsReadMessages = false;
			this.AgentHistoricMaxMessages = 10;
			this.UseSentDate = true;
			this.MarkWhiteListMessagesAsVIM = true;
			this.PrioritizeVIMOverQueueLevel = true;
			this.SessionTimeOut = 5;
			this.WarnAgentForSpellingErrors = true;
			this.AgentMustEnterDiscardReason = false;
			this.AllowForwardAction = false;
			this.AllowAgentToBlockUsers = false;
			this.MaxRetriesForOutgoingMessages = 2;
			this.AllowAgentsToReturnMessagesToQueue = true;
			this.AgentMustEnterReturnToQueueReason = true;
			this.MaximumNumberOfTimesMessageCanBeReturned = 0;
			this.AllowAgentsToSelectQueueOnReturnToQueue = true;
			this.AllowAgentsToReturnMessagesWithRelatedMessagesToQueue = false;
			this.ForwardOutsideDomainAvailable = false;
			this.EncryptMessages = false;
			this.AttachmentsMinimumFreeSpace = 5;
#if DEBUG
			this.MaxResultsInGrids = 30;
#else
			this.MaxResultsInGrids = 50;
#endif
			this.VIPSocialUsers = new List<Settings.SocialUserReference>();
			this.TesterSocialUsers = new List<Settings.SocialUserReference>();
			this.DoNotCallSocialUsers = new List<SocialUserReference>();
			this.BlockedSocialUsers = new List<Settings.SocialUserReference>();
			this.ServicesToWarnForMessages = new Dictionary<int, string>();
			this.OutgoingMessagesEnabled = true;
			this.AuxReasonForOutgoingMessages = null;
			this.AuxReasonForMyOutgoingCases = null;
			this.IntervalsPerHour = 2;
			this.BusinessDataRegex = @"^[a-zA-Z0-9]{6,12}(,[a-zA-Z0-9]{6,12})*$";
			this.BusinessDataWrongInputMessage = "El/los número/s de cliente debe/n ser numérico/s y contar entre 6 y 12 dígitos";
			this.BusinessDataFormatMessage = "Puede separar varios números de cliente utilizando una coma (sin ingresar espacios)";
#if DEBUG
			this.MaxRecordsToExport = 50;
#else
			this.MaxRecordsToExport = 2000;
#endif
			this.ScheduledReportsToMantain = 20;
			this.MinutesToAbortExporting = 15;
			this.DailyReportsZipExcel = true;
			this.DailyReportsZipCSV = true;
			this.Cases = new CasesSettings();
			this.EmailConnection = new EmailConnectionSettings();
			this.EmailConnections = new Dictionary<Guid, EmailConnectionSettings>();
			this.Service = new SocialServiceSettings();
			this.ExporterService = new ExporterServiceSettings();
			this.SurveysService = new SurveysServiceSettings();
			this.AuthenticationType = AuthenticationTypes.Local;
			this.LDAP = new LDAPSettings();
			this.GoogleAuth = new GoogleAuthSettings();
			this.SamlAuth = new KeycloakSettings();
			this.BitLy = new BitLySettings();
			this.CognitiveServices = new CognitiveServicesSettings();
			this.YFlow = new Settings.YFlowSettings();
			this.YFlowContingency = new Settings.YFlowSettings("YFlowContingency");
			this.YSmart = new Settings.YSmartSettings();
			this.RestChat = new RestChatSettings();
			this.Maintenance = new MaintenanceSettings();
			this.Chat = new ChatSettings();
			this.InternalChat = new InternalChatSettings();
			this.Whatsapp = new WhatsappSettings();
			this.AppleMessaging = new AppleMessagingSettings();
			this.MercadoLibre = new MercadoLibreSettings();
			this.Telegram = new TelegramSettings();
			this.Twitter = new TwitterSettings();
			this.GoogleRBM = new GoogleRBMSettings();
			this.Facebook = new FacebookSettings();
			this.FacebookMessenger = new FacebookMessengerSettings();
			this.SMS = new SMSSettings();
			this.Skype = new SkypeSettings();
			this.Subscription = new SubscriptionSettings();
			this.Instagram = new InstagramSettings();
			this.LinkedIn = new LinkedInSettings();
			this.Mail = new MailSettings();
			this.ForwardSettings = new ForwardSettings();
			this.FilterEmailSettings = new FilterEmailSettings("FilterEmailSettings");
			this.FilterEmailSettings.Subject = "Arribo de nuevo mensaje a ySocial";
			this.FilterEmailSettings.Template = "<div style=\"font-family: 'Trebuchet MS'\">El @@FECHA@@ el usuario @@USUARIO@@ dijo a través del servicio @@SERVICIO@@ (@@TIPO_DE_SERVICIO@@):</div><div style=\"font-weight: bold; font-family: 'Trebuchet MS'\">@@TEXTO@@</div><div style=\"font-family: 'Trebuchet MS';\">@@COORDENADAS@@</div><div style=\"font-family: 'Trebuchet MS&quot'\">Y este es el caso:</div><div style=\"font-family: 'Trebuchet MS'\">@@CASO@@</div>";
			this.FilterEmailSettings.SendToAdministrators = false;
			this.FilterEmailSettings.SendToSupervisors = true;

			this.EmailExport = new EmailSettings("EmailExport");
			this.EmailExport.Subject = "Se ha generado el reporte de ySocial solicitado";
			this.EmailExport.Template = "<div style=\"font-family: 'Trebuchet MS';\">Se ha generado el reporte solicitado el @@FECHA@@.</div><div style=\"font-family: 'Trebuchet MS';\">Para consultarlo puede acceder @@LINK@@.</div>";

			this.EmailExportAborted = new EmailSettings("EmailExportAborted");
			this.EmailExportAborted.Subject = "Se ha abortado la generación del reporte solicitado";
			this.EmailExportAborted.Template = "<div style=\"font-family: 'Trebuchet MS';\">Se ha abortado la generación del reporte @@TIPO@@ solicitado el @@FECHA@@</div>";

			this.EmailDailyReports = new EmailSettings("EmailDailyReports");
#if !DEBUG
			this.EmailDailyReports.Emails = "<EMAIL>";
#endif
			this.EmailDailyReports.Subject = "Se han generado los reportes diarios de ySocial";
			this.EmailDailyReports.Template = "<div style=\"font-family: 'Trebuchet MS';\">Se han generado los reportes diarios correspondientes al @@FECHA@@.</div><div style=\"font-family: 'Trebuchet MS';\">Para consultarlos puede acceder @@LINK@@.</div><div style=\"font-family: 'Trebuchet MS';\">Los tipos de reporte generados son: @@TIPOS@@</div>";

			this.OutOfDiskSpaceForAttachments = new EmailSettings("OutOfDiskSpaceForAttachments");
#if !DEBUG
			this.OutOfDiskSpaceForAttachments.Emails = "<EMAIL>";
#endif
			this.OutOfDiskSpaceForAttachments.Subject = "Hay poco espacio para archivos adjuntos de ySocial";
			this.OutOfDiskSpaceForAttachments.Template = "<div style=\"font-family: 'Trebuchet MS';\">@@HOSTNAME@@<br />@@IP@@<br />@@RUTA@@<br />@@ESPACIO_LIBRE_MINIMO@@<br />@@ESPACIO_LIBRE@@</div>";

			this.EmailDatabaseProblems = new EmailSettings("EmailDatabaseProblems");
#if !DEBUG
			this.EmailDatabaseProblems.Emails = "<EMAIL>";
#endif
			this.EmailDatabaseProblems.Subject = "Problemas con la base de datos de ySocial";
			this.EmailDatabaseProblems.Template = "<div style=\"font-family: 'Trebuchet MS';\">@@HOSTNAME@@<br />@@IP@@<br />@@FECHA@@<br />@@ERROR@@</div>";

			this.EmailOutOfMemory = new EmailSettings("EmailOutOfMemory");
#if !DEBUG
			this.EmailOutOfMemory.Emails = "<EMAIL>";
#endif
			this.EmailOutOfMemory.Subject = "Problemas de memoria en el servidor de ySocial";
			this.EmailOutOfMemory.Template = "<div style=\"font-family: 'Trebuchet MS';\">@@HOSTNAME@@<br />@@IP@@<br />@@FECHA@@<br />@@ERROR@@</div>";

			this.AgentCreatedLoginInformation = new EmailSettings("AgentCreatedLoginInformation");
			this.AgentCreatedLoginInformation.Subject = "Notificación de creación de agente en ySocial";
			this.AgentCreatedLoginInformation.Template = "<span style=\"font-family: &quot;Trebuchet MS&quot;;\">Estimado @@NOMBRE@@,</span><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\"><br></span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Se ha creado su perfil para poder acceder como agente.</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Sus datos de inicio de sesión son:</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Nombre de usuario: @@USERNAME@@</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Su contraseña: @@CLAVE@@</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\"><br></span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Para ingresar, haga click <a href=\"@@URL@@\">aquí</a></span></div>";

			this.AgentPasswordChanged = new EmailSettings("AgentPasswordChanged");
			this.AgentPasswordChanged.Subject = "Notificación de reseteo de contraseña en ySocial";
			this.AgentPasswordChanged.Template = "<span style=\"font-family: &quot;Trebuchet MS&quot;;\">Estimado @@NOMBRE@@,</span><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\"><br></span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Se ha restablecido su contraseña para ingresar a ySocial.</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Sus datos de inicio de sesión son:</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Nombre de usuario: @@USERNAME@@</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Su contraseña: @@CLAVE@@</span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\"><br></span></div><div><span style=\"font-family: &quot;Trebuchet MS&quot;;\">Para ingresar, haga click <a href=\"@@URL@@\">aquí</a></span></div>";

			this.AnnoyingEmailSettings = new AnnoyingEmailSettings("AnnoyingEmailSettings");
#if !DEBUG
			this.AnnoyingEmailSettings.Emails = "<EMAIL>";
#endif
			this.AnnoyingEmailSettings.Subject = "Problemas con usuarios molestos";
			this.AnnoyingEmailSettings.Template = "<div style=\"font-family: 'Trebuchet MS';\">@@FECHA@@<br />@@FECHA_PRIMER_MENSAJE@@<br />@@USUARIO@@<br />@@SERVICIO@@<br />@@CANTIDAD_DE_MENSAJES@@</div>";

			this.DeleteNotifications = 1;
			this.lastDatabaseProblemsExceptionMailSent = new Dictionary<int, DateTime>();
			this.AgentApplicationSetupFile = null;
			this.ChatPackageSetupFile = null;
			this.EnableSurveys = false;
			this.SurveysURL = "https://survey.ysocial.net/";

			System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
			var fvi = System.Diagnostics.FileVersionInfo.GetVersionInfo(assembly.Location);
			this.AgentApplicationVersion = fvi.FileVersion;

			this.Integrations = new IntegrationsSettings();
			this.WebAgentUrlLoginSettings = new WebAgentUrlLoginSettings();
			this.WebAgentAllowOutdatedLogins = true;
			this.WebAgentStateManagementSettings = new WebAgentStateManagementSettings();
			this.PushNotificationsServiceBusUseWebSockets = false;
			this.PushNotificationsServiceBusConcurrentMessages = 20;
			this.PushNotificationsServiceBusConcurrentStatuses = 5;
			this.PushNotificationsServiceBusConcurrentMassive = 5;
			this.PushNotificationsServiceBusConcurrentCalls = 5;
			this.CloudHeadersToAdd = null;
			this.CloudHeadersToRemove = null;
			this.RestrictedIPsForWeb = null;
			this.RestrictedIPsForWebAgent = null;
			this.PbxIntegrationAgentGroupForNewAgents = null;
			this.WorkingTimes = new WorkingTimesSettings();

			this.MinimumPasswordStrength = 3;
			this.UserPasswordExpireDay = 0;
			this.UserPasswordRepeat = true;
			this.UserMandatory2FA = false;
			this.UserPasswordRepeatAmount = 0;
			this.UserPasswordWrongsCaptha = 0;
			this.UserPasswordWrongBlock = 0;
			this.DaysInactiveUserBlock = 0;
			this.UserPasswordRefresh = false;
			this.UserPasswordFirstChange = false;
			this.UserPasswordValidationRegex = string.Empty;
			this.UserPasswordMessageRegex = string.Empty;

			this.MinimumAgentPasswordStrength = 3;
			this.AgentPasswordExpireDay = 0;
			this.AgentPasswordRepeat = true;
			this.AgentMandatory2FA = false;
			this.AgentPasswordRepeatAmount = 0;
			this.AgentPasswordWrongsCaptha = 0;
			this.AgentPasswordWrongsBlock = 0;
			this.DaysInactiveAgentBlock = 0;
			this.AgentCanChangePassword = false;
			this.AgentPasswordRefresh = false;
			this.AgentPasswordFirstChange = false;
			this.AgentPasswordValidationRegex = string.Empty;
			this.AgentPasswordMessageRegex = string.Empty;

			this.CheckLastQueueByTime = false;
			this.CheckLastQueueByTimeValue = 0;

			this.EnableCapi = false;

			this.DeliveryFailedNotification = new DeliveryFailedNotificationSettings();

			this.EnabledLanguages = new List<string>();
			this.EnabledLanguages.Add("ES");
			this.EnabledLanguages.Add("EN");
			this.EnabledLanguages.Add("PT");

			this.ScheduledReportsToGenerate = 0;
			this.Gateway = new GatewaySettings();
			this.Storage = new StorageSettings();
			this.RealTime = new RealTimeSettings();
			this.YUsage = new YUsageSettings();

			this.DailyReportsDeleteLocalFiles = false;
		}

		#endregion

		#region Public Methods

		public UploadFileConnectionSettings GetOneFtp(Guid guid)
		{
			return this.FTPs[guid];
		}

		/// <summary>
		/// Devuelve si los datos de negocio especificados en <paramref name="businessData"/> son válidos
		/// </summary>
		/// <param name="businessData">Los datos de negocio a validiar</param>
		/// <returns><code>true</code> si los datos de negocio son válidos; en caso contrario, <code>false</code></returns>
		public bool IsBusinessDataValid(string businessData)
		{
			if (string.IsNullOrEmpty(businessData))
				return true;

			if (this.ExtendedProfilesBusinessCodeFields != null &&
				this.ExtendedProfilesBusinessCodeFields.Length > 0)
			{
				var parts = businessData.Split(",".ToCharArray());

				foreach (var part in parts)
				{
					var elements = part.Split("#".ToCharArray());
					if (elements.Length != this.ExtendedProfilesBusinessCodeFields.Length)
					{
						return false;
					}

					for (var i = 0; i < this.ExtendedProfilesBusinessCodeFields.Length; i++)
					{
						var field = this.ExtendedProfilesBusinessCodeFields[i];
						var element = elements[i];

						if (string.IsNullOrEmpty(element))
						{
							return false;
						}

						switch (field.DataType)
						{
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.String:
								if (!string.IsNullOrEmpty(field.String.Regex))
								{
									var regex = new System.Text.RegularExpressions.Regex(field.String.Regex);
									if (!regex.IsMatch(element))
									{
										return false;
									}
								}
								break;
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.Number:
								long number;
								if (!long.TryParse(element, out number))
								{
									return false;
								}

								if (field.Number.Max != null && number > field.Number.Max)
								{
									return false;
								}

								if (field.Number.Min != null && number < field.Number.Min)
								{
									return false;
								}

								break;
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.Dropdown:
								var found = false;
								foreach (var item in field.Dropdown.Items)
								{
									if (item.Value.Equals(element))
									{
										found = true;
										break;
									}
								}

								if (!found)
								{
									return false;
								}

								break;
							default:
								break;
						}
					}
				}
			}
			else
			{
				string regexForBusinessData = @"^[a-zA-Z0-9]{6,12}(,[a-zA-Z0-9]{6,12})*$";
				if (!string.IsNullOrEmpty(this.BusinessDataRegex))
					regexForBusinessData = this.BusinessDataRegex;
				var regex = new System.Text.RegularExpressions.Regex(regexForBusinessData);
				if (!regex.IsMatch(businessData))
				{
					return false;
				}

				try
				{
					string[] parts = businessData.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
					var distinctParts = new List<string>();
					for (var i = 0; i < parts.Length; i++)
					{
						if (string.IsNullOrEmpty(parts[i]))
						{
							return false;
						}

						if (!distinctParts.Contains(parts[i], StringComparer.InvariantCultureIgnoreCase))
						{
							distinctParts.Add(parts[i]);
						}
					}

					businessData = string.Join(",", distinctParts);
				}
				catch
				{
					return false;
				}
			}

			return true;
		}

		#region Mail Methods

		/// <summary>
		/// Maneja una <see cref="System.Exception"/> verificando si se debe tomar alguna acción relacionada al tipo
		/// </summary>
		/// <param name="ex">La <see cref="System.Exception"/> con la información del error</param>
		public void HandleException(System.Exception ex)
		{
#if !NETCOREAPP
			if (ex is System.Data.Common.DbException)
				HandleDatabaseProblemsException((System.Data.Common.DbException) ex);
			else if (ex.InnerException != null && ex.InnerException is System.Data.Common.DbException)
				HandleDatabaseProblemsException((System.Data.Common.DbException) ex.InnerException);
			else if (ex is System.OutOfMemoryException)
				HandleOutOfMemoryException((System.OutOfMemoryException) ex);
#endif
			Common.Tracer.TraceError("Ocurrió un error: {0}", ex);
		}

#if !NETCOREAPP
		/// <summary>
		/// Maneja una <see cref="System.Data.Common.DbException"/> verificando su severidad y evaluando si un mail debe ser enviado
		/// utilizando la configuración de <see cref="EmailDatabaseProblems"/>
		/// </summary>
		/// <param name="ex">La <see cref="System.Data.Common.DbException"/> con la información del error</param>
		public void HandleDatabaseProblemsException(System.Data.Common.DbException ex)
		{
			try
			{
				if (ex is System.Data.SqlClient.SqlException)
				{
					var sqlex = ex as System.Data.SqlClient.SqlException;

					if (sqlex.Class >= 17)
					{
						if (!lastDatabaseProblemsExceptionMailSent.ContainsKey(sqlex.Class) || lastDatabaseProblemsExceptionMailSent[sqlex.Class].Date < DateTime.Now.Date)
						{
							var templateParameters = new Dictionary<string, object>();
							templateParameters.Add("@@FECHA@@", DateTime.Now);
							templateParameters.Add("@@ERROR@@", sqlex.Message);

							Task.Factory.StartNew(() =>
							{
								SendMailMessage(this.EmailDatabaseProblems, templateParameters);
							}, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default);

							lastDatabaseProblemsExceptionMailSent[sqlex.Class] = DateTime.Now;
						}
					}
				}
			}
			catch { }
		}

		/// <summary>
		/// Maneja una <see cref="System.OutOfMemoryException"/>
		/// </summary>
		/// <param name="ex">La <see cref="System.OutOfMemoryException"/> con la información del error</param>
		public void HandleOutOfMemoryException(System.OutOfMemoryException ex)
		{
			try
			{
				if (ex is System.OutOfMemoryException)
				{
					if (lastOutOfMemoryExceptionMailSent < DateTime.Now.AddMinutes(20))
					{
						var templateParameters = new Dictionary<string, object>();
						templateParameters.Add("@@FECHA@@", DateTime.Now);
						templateParameters.Add("@@ERROR@@", ex.Message);

						Task.Factory.StartNew(() =>
						{
							SendMailMessage(this.EmailOutOfMemory, templateParameters);
						}, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default);

						lastOutOfMemoryExceptionMailSent = DateTime.Now;
					}
				}
			}
			catch { }
		}

#endif

		/// <summary>
		/// Devuelve la instancia actual como un <see cref="MimeKit.MimeEntity"/> para poder ser adjuntado en los mails
		/// </summary>
		/// <returns>Un <see cref="MimeKit.MimeEntity"/> con los datos del archivo adjunto para ser envíado por mail</returns>
		public MimeKit.MimePart ConvertAttachmentToMimePart(DomainModel.Attachment attach)
		{
			if (attach.Data == null)
				return null;

			using (var stream = new MemoryStream(attach.Data, false))
			{
				MimeKit.MimePart attachment;

				var contentType = MimeKit.ContentType.Parse(attach.MimeType);
				if (contentType.IsMimeType("text", "*"))
				{
					attachment = new MimeKit.TextPart(contentType.MediaSubtype);
					foreach (var param in contentType.Parameters)
						attachment.ContentType.Parameters.Add(param);
				}
				else
				{
					attachment = new MimeKit.MimePart(contentType);
				}

				attachment.FileName = System.IO.Path.GetFileName(attach.OriginalFileName);
				attachment.IsAttachment = true;

				var content = new MimeKit.IO.MemoryBlockStream();
				var filter = new MimeKit.IO.Filters.BestEncodingFilter();
				var buf = new byte[4096];
				int index, length;
				int nread;

				while ((nread = stream.Read(buf, 0, buf.Length)) > 0)
				{
					filter.Filter(buf, 0, nread, out index, out length);
					content.Write(buf, 0, nread);
				}

				filter.Flush(buf, 0, 0, out index, out length);
				content.Position = 0;

				attachment.ContentTransferEncoding = filter.GetBestEncoding(MimeKit.EncodingConstraint.SevenBit);
				attachment.Content = new MimeKit.MimeContent(content);

				return attachment;
			}
		}

		#region Async Send Mail

#pragma warning disable CS1998 // This async method lacks 'await' operators and will run synchronously. Consider using the 'await' operator to await non-blocking API calls, or 'await Task.Run(...)' to do CPU-bound work on a background thread.
		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="body">El cuerpo del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="body"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string body)
		{
			await SendMailMessageAsync(subject, to, body, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="body">El cuerpo del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="body"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string cc, string bcc, string body)
		{
			await SendMailMessageAsync(subject, to, cc, bcc, body, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string template, Dictionary<string, object> templateParameters)
		{
			await SendMailMessageAsync(subject, to, null, null, template, templateParameters);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string template, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments)
		{
			await SendMailMessageAsync(subject, to, null, null, template, templateParameters, attachments);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string cc, string bcc, string template, Dictionary<string, object> templateParameters)
		{
			await SendMailMessageAsync(subject, to, cc, bcc, template, templateParameters, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public async Task SendMailMessageAsync(string subject, string to, string cc, string bcc, string template, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments)
		{
			if (string.IsNullOrEmpty(subject))
				throw new ArgumentNullException("subject");

			if (string.IsNullOrEmpty(to) && string.IsNullOrEmpty(cc) && string.IsNullOrEmpty(bcc))
				throw new ArgumentException("Al menos uno de to, cc o bcc debe ser especificado");

			if (string.IsNullOrEmpty(template))
				throw new ArgumentNullException("template");

			Settings.EmailSettings settings = new EmailSettings("Email");
			settings.Emails = to;
			settings.EmailsCC = cc;
			settings.EmailsBCC = bcc;
			settings.Subject = subject;
			settings.Template = template;

			await SendMailMessageAsync(settings, templateParameters, attachments);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public async Task SendMailMessageAsync(Settings.EmailSettings settings, Dictionary<string, object> templateParameters)
		{
			await SendMailMessageAsync(settings, templateParameters, null);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public async Task SendMailMessageAsync(Settings.EmailSettings settings, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments)
		{
			await SendMailMessageAsync(settings, templateParameters, attachments, false);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <param name="throwIfFailed">Indica si se debe tirar el <see cref="Exception"/> que ocurre en caso de que haya habido un error</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public async Task SendMailMessageAsync(Settings.EmailSettings settings, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments, bool throwIfFailed)
		{
			await SendMailMessageAsync(settings, null, templateParameters, attachments, throwIfFailed);
		}

#pragma warning restore CS1998 // This async method lacks 'await' operators and will run synchronously. Consider using the 'await' operator to await non-blocking API calls, or 'await Task.Run(...)' to do CPU-bound work on a background thread.

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="subjectParameters">>Un diccionario con los valores a aplicar para cada parámetro del asunto del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <param name="throwIfFailed">Indica si se debe tirar el <see cref="Exception"/> que ocurre en caso de que haya habido un error</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public async Task SendMailMessageAsync(Settings.EmailSettings settings, Dictionary<string, object> subjectParameters, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments, bool throwIfFailed)
		{
			if (settings == null)
				return;

			if (settings.From != null &&
				settings.From.Equals("<EMAIL>", StringComparison.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("El FROM de los <NAME_EMAIL>. No sale la notificación");
				return;
			}

			EmailConnectionSettings connectionToUse;
			if (!string.IsNullOrEmpty(settings.EmailConnection) && Guid.TryParse(settings.EmailConnection, out Guid emailGuid))
			{
				connectionToUse = GetEmailConnectionToUse(emailGuid);
			}
			else
			{
				connectionToUse = this.EmailConnection;
			}

			if (!connectionToUse.Enabled)
			{
				Tracer.TraceInfo("La casilla no esta habilitada. No se envia el correo");
				return;
			}

			MailKit.Net.Smtp.SmtpClient smtpClient = null;
			Microsoft.Exchange.WebServices.Data.ExchangeService exchangeService = null;
			MimeKit.MimeMessage mailMessage;

			try
			{
				mailMessage = this.CreateMailMessage(connectionToUse);
				if (connectionToUse.Protocol == EmailConnectionSettings.Protocols.SMTP)
					smtpClient = await this.CreateMailSmtpClientAsync(connectionToUse);
				else
					exchangeService = this.CreateMailEWSClient(connectionToUse);

				if (!string.IsNullOrEmpty(settings.From))
					mailMessage.From[0] = new MimeKit.MailboxAddress(settings.From, settings.From);

				MimeKit.InternetAddressList internetAddressList;
				if (!string.IsNullOrEmpty(settings.Emails))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.Emails);
					mailMessage.To.AddRange(internetAddressList);
				}
				if (!string.IsNullOrEmpty(settings.EmailsCC))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.EmailsCC);
					mailMessage.Cc.AddRange(internetAddressList);
				}
				if (!string.IsNullOrEmpty(settings.EmailsBCC))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.EmailsBCC);
					mailMessage.Bcc.AddRange(internetAddressList);
				}

				var subject = settings.Subject;
				if (subjectParameters != null)
				{
					foreach (var parameter in subjectParameters)
					{
						string parameterName = parameter.Key;
						if (!parameterName.StartsWith("@@"))
							parameterName = string.Format("@@{0}@@", parameterName);

						if (parameter.Value.GetType() == typeof(DateTime))
							subject = subject.Replace(parameterName, ((DateTime) parameter.Value).ToString("F"));
						else
							subject = subject.Replace(parameterName, parameter.Value.ToString());
					}
				}
				mailMessage.Subject = subject;

				string body = settings.Template;
				if (templateParameters != null)
				{
					foreach (var parameter in templateParameters)
					{
						string parameterName = parameter.Key;
						if (!parameterName.StartsWith("@@"))
							parameterName = string.Format("@@{0}@@", parameterName);

						if (parameter.Value.GetType() == typeof(DateTime))
							body = body.Replace(parameterName, ((DateTime) parameter.Value).ToString("F"));
						else
							body = body.Replace(parameterName, parameter.Value.ToString());
					}
				}

				var bodyBuilder = new MimeKit.BodyBuilder();
				bodyBuilder.HtmlBody = body;
				bodyBuilder.ResolveHostNameAndIP();

				if (attachments != null)
				{
					foreach (var attachment in attachments)
					{
						if (attachment.IsAttachment)
							bodyBuilder.Attachments.Add(attachment);
					}
				}

				mailMessage.Body = bodyBuilder.ToMessageBody();

				if (connectionToUse.Protocol == EmailConnectionSettings.Protocols.SMTP)
				{
					await smtpClient.SendAsync(mailMessage);
				}
				else
				{ 
					var message = new Microsoft.Exchange.WebServices.Data.EmailMessage(exchangeService);

					using (var ms = new System.IO.MemoryStream())
					{
						mailMessage.WriteTo(ms);
						message.MimeContent = new Microsoft.Exchange.WebServices.Data.MimeContent("UTF-8", ms.ToArray());
						message.Save();
					}

					message.Load(new Microsoft.Exchange.WebServices.Data.PropertySet(Microsoft.Exchange.WebServices.Data.BasePropertySet.FirstClassProperties, Microsoft.Exchange.WebServices.Data.ItemSchema.MimeContent));

					message.Send();
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló cuando se enviaba el mail de {0}: {1}", settings.SettingsPath, ex);

				if (throwIfFailed)
					throw;
			}
			finally
			{
				if (smtpClient != null)
				{
					if (smtpClient.IsConnected)
						await smtpClient.DisconnectAsync(true);
					smtpClient.Dispose();
				}
			}
		}

		#endregion

		#region Sync Send Mail

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="body">El cuerpo del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="body"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string body)
		{
			SendMailMessage(subject, to, body, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="body">El cuerpo del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="body"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string cc, string bcc, string body, string emailConnection)
		{
			SendMailMessage(subject, to, cc, bcc, body, null, emailConnection);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string template, Dictionary<string, object> templateParameters)
		{
			SendMailMessage(subject, to, null, null, template, templateParameters, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string template, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments)
		{
			SendMailMessage(subject, to, null, null, template, templateParameters, attachments, null);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string cc, string bcc, string template, Dictionary<string, object> templateParameters, string emailConnection)
		{
			SendMailMessage(subject, to, cc, bcc, template, templateParameters, null, emailConnection);
		}

		/// <summary>
		/// Envía un email
		/// </summary>
		/// <param name="subject">El asunto del email</param>
		/// <param name="to">Las direcciones a la cual se enviará</param>
		/// <param name="cc">Las direcciones a la cual se enviará con copia</param>
		/// <param name="bcc">Las direcciones a la cual se enviará con copia oculta</param>
		/// <param name="template">La plantilla del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="subject"/> o el parámetro
		/// <paramref name="to"/> o el parámetro <paramref name="template"/> son nulos o vacíos</exception>
		public void SendMailMessage(string subject, string to, string cc, string bcc, string template, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments, string emailConnection)
		{
			if (string.IsNullOrEmpty(subject))
				throw new ArgumentNullException("subject");

			if (string.IsNullOrEmpty(to) && string.IsNullOrEmpty(cc) && string.IsNullOrEmpty(bcc))
				throw new ArgumentException("Al menos uno de to, cc o bcc debe ser especificado");

			if (string.IsNullOrEmpty(template))
				throw new ArgumentNullException("template");

			Settings.EmailSettings settings = new EmailSettings("Email");
			settings.EmailConnection = emailConnection;
			settings.Emails = to;
			settings.EmailsCC = cc;
			settings.EmailsBCC = bcc;
			settings.Subject = subject;
			settings.Template = template;

			SendMailMessage(settings, templateParameters, attachments);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public void SendMailMessage(Settings.EmailSettings settings, Dictionary<string, object> templateParameters)
		{
			SendMailMessage(settings, templateParameters, null);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public void SendMailMessage(Settings.EmailSettings settings, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments)
		{
			SendMailMessage(settings, templateParameters, attachments, false);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la
		/// plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <param name="throwIfFailed">Indica si se debe tirar el <see cref="Exception"/> que ocurre en caso de que haya habido un error</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public void SendMailMessage(Settings.EmailSettings settings, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments, bool throwIfFailed)
		{
			SendMailMessage(settings, null, templateParameters, attachments, throwIfFailed);
		}

		/// <summary>
		/// Envía un email utilizando la configuración especificada en <paramref name="settings"/>
		/// </summary>
		/// <param name="settings">Un <see cref="Settings.EmailSettings"/> con la configuración del email que se mandará</param>
		/// <param name="subjectParameters">>Un diccionario con los valores a aplicar para cada parámetro del asunto del email</param>
		/// <param name="templateParameters">Un diccionario con los valores a aplicar para cada parámetro de la plantilla del email</param>
		/// <param name="attachments">La enumeración de archivos adjuntos o null</param>
		/// <param name="throwIfFailed">Indica si se debe tirar el <see cref="Exception"/> que ocurre en caso de que haya habido un error</param>
		/// <exception cref="ArgumentNullException">Ocurre cuando el parámetro <paramref name="settings"/> es <code>null</code></exception>
		public void SendMailMessage(Settings.EmailSettings settings, Dictionary<string, object> subjectParameters, Dictionary<string, object> templateParameters, IEnumerable<MimeKit.MimeEntity> attachments, bool throwIfFailed)
{
			if (settings == null)
				return;

			if (settings.From != null &&
				settings.From.Equals("<EMAIL>", StringComparison.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("El FROM de los <NAME_EMAIL>. No sale la notificación");
				return;
			}
			
			EmailConnectionSettings connectionToUse;
			if (!string.IsNullOrEmpty(settings.EmailConnection) && Guid.TryParse(settings.EmailConnection, out Guid emailGuid))
			{
				connectionToUse = GetEmailConnectionToUse(emailGuid);
			}			
			else
			{
				connectionToUse = this.EmailConnection;
			}

			if (!connectionToUse.Enabled)
			{
				Tracer.TraceInfo("La casilla no esta habilitada. No se envia el correo");
				return;
			}

			MailKit.Net.Smtp.SmtpClient smtpClient = null;
			Microsoft.Exchange.WebServices.Data.ExchangeService exchangeService = null;
			MimeKit.MimeMessage mailMessage = null;

			try
			{
				mailMessage = CreateMailMessage(connectionToUse);
				if (connectionToUse.Protocol == EmailConnectionSettings.Protocols.SMTP)
					smtpClient = this.CreateMailSmtpClient(connectionToUse);
				else
					exchangeService = this.CreateMailEWSClient(connectionToUse);

				if (!string.IsNullOrEmpty(settings.From))
					mailMessage.From[0] = new MimeKit.MailboxAddress(settings.From, settings.From);
				
				MimeKit.InternetAddressList internetAddressList;
				if (!string.IsNullOrEmpty(settings.Emails))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.Emails);
					mailMessage.To.AddRange(internetAddressList);
				}
				if (!string.IsNullOrEmpty(settings.EmailsCC))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.EmailsCC);
					mailMessage.Cc.AddRange(internetAddressList);
				}
				if (!string.IsNullOrEmpty(settings.EmailsBCC))
				{
					internetAddressList = MimeKit.InternetAddressList.Parse(settings.EmailsBCC);
					mailMessage.Bcc.AddRange(internetAddressList);
				}

				var subject = settings.Subject;
				if (subjectParameters != null)
				{
					foreach (var parameter in subjectParameters)
					{
						string parameterName = parameter.Key;
						if (!parameterName.StartsWith("@@"))
							parameterName = string.Format("@@{0}@@", parameterName);

						if (parameter.Value.GetType() == typeof(DateTime))
							subject = subject.Replace(parameterName, ((DateTime) parameter.Value).ToString("F"));
						else
							subject = subject.Replace(parameterName, parameter.Value.ToString());
					}
				}
				mailMessage.Subject = subject;

				string body = settings.Template;
				if (templateParameters != null)
				{
					foreach (var parameter in templateParameters)
					{
						string parameterName = parameter.Key;
						if (!parameterName.StartsWith("@@"))
							parameterName = string.Format("@@{0}@@", parameterName);

						if (parameter.Value.GetType() == typeof(DateTime))
							body = body.Replace(parameterName, ((DateTime) parameter.Value).ToString("F"));
						else
							body = body.Replace(parameterName, parameter.Value.ToString());
					}
				}

				var bodyBuilder = new MimeKit.BodyBuilder();
				bodyBuilder.HtmlBody = body;
				bodyBuilder.ResolveHostNameAndIP();

				if (attachments != null)
				{
					foreach (var attachment in attachments)
					{
						if (attachment.IsAttachment)
							bodyBuilder.Attachments.Add(attachment);
					}
				}

				mailMessage.Body = bodyBuilder.ToMessageBody();

				if (connectionToUse.Protocol == EmailConnectionSettings.Protocols.SMTP)
				{
					smtpClient.Send(mailMessage);
				}
				else
				{
					var message = new Microsoft.Exchange.WebServices.Data.EmailMessage(exchangeService);

					using (var ms = new System.IO.MemoryStream())
					{
						mailMessage.WriteTo(ms);
						message.MimeContent = new Microsoft.Exchange.WebServices.Data.MimeContent("UTF-8", ms.ToArray());
						message.Save();
					}

					message.Load(new Microsoft.Exchange.WebServices.Data.PropertySet(Microsoft.Exchange.WebServices.Data.BasePropertySet.FirstClassProperties, Microsoft.Exchange.WebServices.Data.ItemSchema.MimeContent));

					message.Send();
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló cuando se enviaba el mail de {0}: {1}", settings.SettingsPath, ex);

				if (throwIfFailed)
					throw;
			}
			finally
			{
				if (mailMessage != null && mailMessage.BodyParts != null)
				{
					foreach (var part in mailMessage.BodyParts.OfType<MimeKit.MimePart>())
					{
						try
						{
							part.Content?.Stream.Dispose();
						}
						catch { }
					}
				}

				if (smtpClient != null)
				{
					if (smtpClient.IsConnected)
						smtpClient.Disconnect(true);
					smtpClient.Dispose();
				}
			}
		}

		#endregion

		#endregion

		/// <summary>
		/// Carga los parámetros del sistema a partir de los datos de un diccionario
		/// </summary>
		/// <param name="settings">Un diccionario con las claves y sus respectivos valores que representan los parámetros del sistema</param>
		public void Load(Dictionary<string, object> settings)
		{
			if (settings.ContainsKey("DefaultLanguage"))
				this.DefaultLanguage = (string) settings["DefaultLanguage"];

			if (settings.ContainsKey("Theme"))
				this.Theme = (string) settings["Theme"];

			if (settings.ContainsKey("MinimumPasswordStrength"))
				this.MinimumPasswordStrength = Convert.ToByte(settings["MinimumPasswordStrength"]);

			if (settings.ContainsKey("UserPasswordExpireDay"))
				this.UserPasswordExpireDay = (int) settings["UserPasswordExpireDay"];

			if (settings.ContainsKey("UserPasswordRepeat"))
				this.UserPasswordRepeat = (bool) settings["UserPasswordRepeat"];

			if (settings.ContainsKey("UserMandatory2FA"))
				this.UserMandatory2FA = (bool)settings["UserMandatory2FA"];

			if (settings.ContainsKey("UserPasswordRepeatAmount"))
				this.UserPasswordRepeatAmount = (int) settings["UserPasswordRepeatAmount"];

			if (settings.ContainsKey("UserPasswordWrongsCaptha"))
				this.UserPasswordWrongsCaptha = (int) settings["UserPasswordWrongsCaptha"];

			if (settings.ContainsKey("UserPasswordWrongBlock"))
				this.UserPasswordWrongBlock = (int) settings["UserPasswordWrongBlock"];

			if (settings.ContainsKey("DaysInactiveUserBlock"))
				this.DaysInactiveUserBlock = (int) settings["DaysInactiveUserBlock"];

			if (settings.ContainsKey("UserPasswordRefresh"))
				this.UserPasswordRefresh = (bool) settings["UserPasswordRefresh"];

			if (settings.ContainsKey("UserPasswordFirstChange"))
				this.UserPasswordFirstChange = (bool) settings["UserPasswordFirstChange"];

			if (settings.ContainsKey("UserPasswordValidationRegex"))
				this.UserPasswordValidationRegex = (string) settings["UserPasswordValidationRegex"];

			if (settings.ContainsKey("UserPasswordMessageRegex"))
				this.UserPasswordMessageRegex = (string) settings["UserPasswordMessageRegex"];

			if (settings.ContainsKey("MinimumAgentPasswordStrength"))
				this.MinimumAgentPasswordStrength = Convert.ToByte(settings["MinimumAgentPasswordStrength"]);

			if (settings.ContainsKey("AgentPasswordExpireDay"))
				this.AgentPasswordExpireDay = (int) settings["AgentPasswordExpireDay"];

			if (settings.ContainsKey("AgentPasswordRepeat"))
				this.AgentPasswordRepeat = (bool) settings["AgentPasswordRepeat"];

			if (settings.ContainsKey("AgentMandatory2FA"))
				this.AgentMandatory2FA = (bool)settings["AgentMandatory2FA"];

			if (settings.ContainsKey("AgentPasswordRepeatAmount"))
				this.AgentPasswordRepeatAmount = (int) settings["AgentPasswordRepeatAmount"];

			if (settings.ContainsKey("AgentPasswordWrongsCaptha"))
				this.AgentPasswordWrongsCaptha = (int) settings["AgentPasswordWrongsCaptha"];

			if (settings.ContainsKey("AgentPasswordWrongsBlock"))
				this.AgentPasswordWrongsBlock = (int) settings["AgentPasswordWrongsBlock"];

			if (settings.ContainsKey("DaysInactiveAgentBlock"))
				this.DaysInactiveAgentBlock = (int) settings["DaysInactiveAgentBlock"];

			if (settings.ContainsKey("AgentPasswordRefresh"))
				this.AgentPasswordRefresh = (bool) settings["AgentPasswordRefresh"];

			if (settings.ContainsKey("AgentPasswordFirstChange"))
				this.AgentPasswordFirstChange = (bool) settings["AgentPasswordFirstChange"];

			if (settings.ContainsKey("AgentPasswordValidationRegex"))
				this.AgentPasswordValidationRegex = (string) settings["AgentPasswordValidationRegex"];

			if (settings.ContainsKey("AgentPasswordMessageRegex"))
				this.AgentPasswordMessageRegex = (string) settings["AgentPasswordMessageRegex"];

			if (settings.ContainsKey("AgentCanChangePassword"))
				this.AgentCanChangePassword = (bool) settings["AgentCanChangePassword"];

			if (settings.ContainsKey("UseACDBalancing"))
				this.UseACDBalancing = (bool) settings["UseACDBalancing"];

			if (settings.ContainsKey("ACDBalancingWithQueueLevels"))
				this.ACDBalancingWithQueueLevels = (bool) settings["ACDBalancingWithQueueLevels"];

			if (settings.ContainsKey("ACDBalancingWithQueueLevelsWorking"))
				this.ACDBalancingWithQueueLevelsWorking = (bool) settings["ACDBalancingWithQueueLevelsWorking"];
			else if (settings.ContainsKey("ACDBalancingWithQueueLevels"))
				this.ACDBalancingWithQueueLevelsWorking = false;

			if (settings.ContainsKey("AgentApplicationSetupFile"))
				this.AgentApplicationSetupFile = (string) settings["AgentApplicationSetupFile"];

			if (settings.ContainsKey("AgentApplicationVersion"))
				this.AgentApplicationVersion = (string) settings["AgentApplicationVersion"];

			if (settings.ContainsKey("WebAgentVersion"))
				this.WebAgentVersion = (string) settings["WebAgentVersion"];

			if (settings.ContainsKey("WebAgentURL"))
				this.WebAgentURL = (string) settings["WebAgentURL"];

			if (settings.ContainsKey("WebAgentAllowOutdatedLogins"))
				this.WebAgentAllowOutdatedLogins = (bool) settings["WebAgentAllowOutdatedLogins"];

			if (settings.ContainsKey("ChatPackageSetupFile"))
				this.ChatPackageSetupFile = (string) settings["ChatPackageSetupFile"];

			if (settings.ContainsKey("EncryptMessages"))
				this.EncryptMessages = (bool) settings["EncryptMessages"];

			if (settings.ContainsKey("MaxAssignableMessagesPerUser"))
				this.MaxAssignableMessagesPerUser = Convert.ToInt16(settings["MaxAssignableMessagesPerUser"]);

			if (settings.ContainsKey("AutoMarkAsReadMessages"))
				this.AutoMarkAsReadMessages = (bool) settings["AutoMarkAsReadMessages"];

			if (settings.ContainsKey("AgentHistoricMaxMessages"))
				this.AgentHistoricMaxMessages = Convert.ToInt16(settings["AgentHistoricMaxMessages"]);

			if (settings.ContainsKey("UseSentDate"))
				this.UseSentDate = (bool) settings["UseSentDate"];

			if (settings.ContainsKey("MarkWhiteListMessagesAsVIM"))
				this.MarkWhiteListMessagesAsVIM = (bool) settings["MarkWhiteListMessagesAsVIM"];

			if (settings.ContainsKey("PrioritizeVIMOverQueueLevel"))
				this.PrioritizeVIMOverQueueLevel = (bool) settings["PrioritizeVIMOverQueueLevel"];

			if (settings.ContainsKey("SessionTimeOut"))
				this.SessionTimeOut = Convert.ToInt16(settings["SessionTimeOut"]);

			if (settings.ContainsKey("AllowAgentsToReturnMessagesToQueue"))
				this.AllowAgentsToReturnMessagesToQueue = (bool) settings["AllowAgentsToReturnMessagesToQueue"];

			if (settings.ContainsKey("AgentMustEnterReturnToQueueReason"))
				this.AgentMustEnterReturnToQueueReason = (bool) settings["AgentMustEnterReturnToQueueReason"];

			if (settings.ContainsKey("MaximumNumberOfTimesMessageCanBeReturned"))
				this.MaximumNumberOfTimesMessageCanBeReturned = Convert.ToInt16(settings["MaximumNumberOfTimesMessageCanBeReturned"]);

			if (settings.ContainsKey("AllowAgentsToSelectQueueOnReturnToQueue"))
				this.AllowAgentsToSelectQueueOnReturnToQueue = (bool) settings["AllowAgentsToSelectQueueOnReturnToQueue"];

			if (settings.ContainsKey("AllowAgentsToReturnMessagesWithRelatedMessagesToQueue"))
				this.AllowAgentsToReturnMessagesWithRelatedMessagesToQueue = (bool) settings["AllowAgentsToReturnMessagesWithRelatedMessagesToQueue"];

			if (settings.ContainsKey("WarnAgentForSpellingErrors"))
				this.WarnAgentForSpellingErrors = (bool) settings["WarnAgentForSpellingErrors"];

			if (settings.ContainsKey("AllowAgentToBlockUsers"))
				this.AllowAgentToBlockUsers = (bool) settings["AllowAgentToBlockUsers"];

			if (settings.ContainsKey("AgentMustEnterDiscardReason"))
				this.AgentMustEnterDiscardReason = (bool) settings["AgentMustEnterDiscardReason"];

			if (settings.ContainsKey("OutgoingMessagesEnabled"))
				this.OutgoingMessagesEnabled = (bool) settings["OutgoingMessagesEnabled"];

			if (settings.ContainsKey("MaxRetriesForOutgoingMessages"))
				this.MaxRetriesForOutgoingMessages = Convert.ToInt32(settings["MaxRetriesForOutgoingMessages"]);

			if (settings.ContainsKey("MaxResultsInGrids"))
				this.MaxResultsInGrids = Convert.ToInt32(settings["MaxResultsInGrids"]);

			if (settings.ContainsKey("VIPSocialUsers"))
			{
				string socialUsers = (string) settings["VIPSocialUsers"];
				if (!string.IsNullOrEmpty(socialUsers))
				{
					try
					{
						this.VIPSocialUsers.AddRange(Newtonsoft.Json.JsonConvert.DeserializeObject<Settings.SocialUserReference[]>(socialUsers, new Settings.SocialUserReference.SocialUserReferenceConverter()));
					}
					catch
					{
						string[] twitterSocialUsers = socialUsers.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
						if (twitterSocialUsers != null && twitterSocialUsers.Length > 0)
							this.VIPSocialUsers.AddRange(twitterSocialUsers.Select(t => new Settings.TwitterSocialUserReference(0, t, t)));
					}
				}
			}

			if (settings.ContainsKey("TesterSocialUsers"))
			{
				string socialUsers = (string) settings["TesterSocialUsers"];
				if (!string.IsNullOrEmpty(socialUsers))
				{
					try
					{
						this.TesterSocialUsers.AddRange(Newtonsoft.Json.JsonConvert.DeserializeObject<Settings.SocialUserReference[]>(socialUsers, new Settings.SocialUserReference.SocialUserReferenceConverter()));
					}
					catch
					{
						string[] twitterSocialUsers = socialUsers.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
						if (twitterSocialUsers != null && twitterSocialUsers.Length > 0)
							this.TesterSocialUsers.AddRange(twitterSocialUsers.Select(t => new Settings.TwitterSocialUserReference(0, t, t)));
					}
				}
			}

			if (settings.ContainsKey("DoNotCallSocialUsers"))
			{
				string socialUsers = (string) settings["DoNotCallSocialUsers"];
				if (!string.IsNullOrEmpty(socialUsers))
				{
					try
					{
						this.DoNotCallSocialUsers.AddRange(Newtonsoft.Json.JsonConvert.DeserializeObject<Settings.SocialUserReference[]>(socialUsers, new Settings.SocialUserReference.SocialUserReferenceConverter()));
					}
					catch
					{
						string[] twitterSocialUsers = socialUsers.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
						if (twitterSocialUsers != null && twitterSocialUsers.Length > 0)
							this.DoNotCallSocialUsers.AddRange(twitterSocialUsers.Select(t => new Settings.TwitterSocialUserReference(0, t, t)));
					}
				}
			}

			if (settings.ContainsKey("BlockedSocialUsers"))
			{
				string socialUsers = (string) settings["BlockedSocialUsers"];
				if (!string.IsNullOrEmpty(socialUsers))
				{
					try
					{
						this.BlockedSocialUsers.AddRange(Newtonsoft.Json.JsonConvert.DeserializeObject<Settings.SocialUserReference[]>(socialUsers, new Settings.SocialUserReference.SocialUserReferenceConverter()));
					}
					catch
					{
						string[] twitterSocialUsers = socialUsers.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
						if (twitterSocialUsers != null && twitterSocialUsers.Length > 0)
							this.BlockedSocialUsers.AddRange(twitterSocialUsers.Select(t => new Settings.TwitterSocialUserReference(0, t, t)));
					}
				}
			}

			if (settings.ContainsKey("ServicesToWarnForMessages"))
			{
				try
				{
					this.ServicesToWarnForMessages = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<int, string>>((string) settings["ServicesToWarnForMessages"]);
				}
				catch { }
			}

			if (settings.ContainsKey("AuxReasonForOutgoingMessages"))
				this.AuxReasonForOutgoingMessages = Convert.ToInt16(settings["AuxReasonForOutgoingMessages"]);

			if (settings.ContainsKey("AuxReasonForMyOutgoingCases"))
			{
				this.AuxReasonForMyOutgoingCases = Convert.ToInt16(settings["AuxReasonForMyOutgoingCases"]);
			}

			this.Cases.Load(settings);
			this.EmailConnection.Load(settings);
			this.Service.Load(settings);
			this.ExporterService.Load(settings);
			this.SurveysService.Load(settings);
			this.LDAP.Load(settings);
			if (settings.ContainsKey("AuthenticationType"))
			{
				this.AuthenticationType = (AuthenticationTypes) Convert.ToInt16(settings["AuthenticationType"]);
			}
			else
			{
				if (this.LDAP.UseLDAP)
					this.AuthenticationType = AuthenticationTypes.Domain;
			}
			this.GoogleAuth.Load(settings);
			this.SamlAuth.Load(settings);
			this.BitLy.Load(settings);
			this.CognitiveServices.Load(settings);
			this.YFlow.Load(settings);
			this.YUsage.Load(settings);
			this.YFlowContingency.Load(settings);
			this.YSmart.Load(settings);
			this.RestChat.Load(settings);
			this.Maintenance.Load(settings);
			this.Chat.Load(settings);
			this.InternalChat.Load(settings);
			this.Whatsapp.Load(settings);
			this.AppleMessaging.Load(settings);
			this.MercadoLibre.Load(settings);
			this.Telegram.Load(settings);
			this.Twitter.Load(settings);
			this.GoogleRBM.Load(settings);
			this.Facebook.Load(settings);
			this.FacebookMessenger.Load(settings);
			this.SMS.Load(settings);
			this.Skype.Load(settings);
			this.Subscription.Load(settings);
			this.Instagram.Load(settings);
			this.LinkedIn.Load(settings);
			this.Mail.Load(settings);
			this.ForwardSettings.Load(settings);
			this.FilterEmailSettings.Load(settings);
			this.OutOfDiskSpaceForAttachments.Load(settings);
			this.EmailDatabaseProblems.Load(settings);
			this.EmailOutOfMemory.Load(settings);
			this.AgentCreatedLoginInformation.Load(settings);
			this.AgentPasswordChanged.Load(settings);
			this.AnnoyingEmailSettings.Load(settings);

			if (settings.ContainsKey("IntervalsPerHour"))
				this.IntervalsPerHour = Convert.ToByte(settings["IntervalsPerHour"]);
			if (settings.ContainsKey("AllowForwardAction"))
				this.AllowForwardAction = (bool) settings["AllowForwardAction"];
			if (settings.ContainsKey("ForwardOutsideDomainAvailable"))
				this.ForwardOutsideDomainAvailable = (bool) settings["ForwardOutsideDomainAvailable"];
			if (settings.ContainsKey("AttachmentsRoute"))
				this.AttachmentsRoute = (string) (settings["AttachmentsRoute"]);
			if (settings.ContainsKey("AttachmentsMinimumFreeSpace"))
				this.AttachmentsMinimumFreeSpace = Convert.ToInt32(settings["AttachmentsMinimumFreeSpace"]);
			if (settings.ContainsKey("FavoriteMails"))
				this.FavoriteMails = (string) (settings["FavoriteMails"]);
			if (settings.ContainsKey("AvailableDomainsToForward"))
				this.AvailableDomainsToForward = (string) (settings["AvailableDomainsToForward"]);
			if (settings.ContainsKey("DeleteNotifications"))
				this.DeleteNotifications = Convert.ToInt16(settings["DeleteNotifications"]);
			this.EmailExport.Load(settings);
			this.EmailExportAborted.Load(settings);
			this.EmailDailyReports.Load(settings);

			if (settings.ContainsKey("ScheduledReportsToMantain"))
				this.ScheduledReportsToMantain = Convert.ToInt32(settings["ScheduledReportsToMantain"]);

			if (settings.ContainsKey("GenerateDailyReport"))
				this.GenerateDailyReport = (bool) settings["GenerateDailyReport"];

			if (settings.ContainsKey("FtpIdDailyReports"))
				this.FtpIdDailyReports = (string) settings["FtpIdDailyReports"];

			if (settings.ContainsKey("FtpDirectoryDailyReports"))
				this.FtpDirectoryDailyReports = (string) settings["FtpDirectoryDailyReports"];

			if (settings.ContainsKey("DailyReportsZipExcel"))
				this.DailyReportsZipExcel = (bool) settings["DailyReportsZipExcel"];

			if (settings.ContainsKey("DailyReportsZipCSV"))
				this.DailyReportsZipCSV = (bool) settings["DailyReportsZipCSV"];

			if (settings.ContainsKey("EnableFtpDailyReports"))
				this.EnableFtpDailyReports = (bool) settings["EnableFtpDailyReports"];

			if (settings.ContainsKey("DailyReportsToMantain"))
				this.DailyReportsToMantain = Convert.ToInt32(settings["DailyReportsToMantain"]);

			if (settings.ContainsKey("DailyReportsFormat"))
				this.DailyReportsFormat = (Reports.Export.ExportFormats) Convert.ToInt16(settings["DailyReportsFormat"]);

			if (settings.ContainsKey("DailyReportsDeleteLocalFiles"))
				this.DailyReportsDeleteLocalFiles = (bool) settings["DailyReportsDeleteLocalFiles"];

			if (settings.ContainsKey("DailyReportsToGenerate"))
				this.DailyReportsToGenerate = (from format
												in ((string) settings["DailyReportsToGenerate"]).Split(",".ToCharArray())
											   select (Reports.ReportTypes) short.Parse(format)).ToArray();

			if (settings.ContainsKey("MaxRecordsToExport"))
				this.MaxRecordsToExport = Convert.ToInt32(settings["MaxRecordsToExport"]);

			if (settings.ContainsKey("MinutesToAbortExporting"))
				this.MinutesToAbortExporting = Convert.ToInt32(settings["MinutesToAbortExporting"]);

			if (settings.ContainsKey("EnableSurveys"))
				this.EnableSurveys = (bool) settings["EnableSurveys"];

			if (settings.ContainsKey("SurveysURL"))
				this.SurveysURL = (string) settings["SurveysURL"];

			if (settings.ContainsKey("BusinessDataRegex"))
				this.BusinessDataRegex = (string) settings["BusinessDataRegex"];
			if (settings.ContainsKey("BusinessDataWrongInputMessage"))
				this.BusinessDataWrongInputMessage = (string) settings["BusinessDataWrongInputMessage"];
			if (settings.ContainsKey("BusinessDataFormatMessage"))
				this.BusinessDataFormatMessage = (string) settings["BusinessDataFormatMessage"];
			if (settings.ContainsKey("ExtendedProfilesFields"))
			{
				var fields = (string) settings["ExtendedProfilesFields"];
				if (!string.IsNullOrEmpty(fields))
				{
					this.ExtendedProfilesFields = Newtonsoft.Json.JsonConvert.DeserializeObject<ExtendedField[]>(fields);
				}
			}
			if (settings.ContainsKey("ExtendedCasesFields"))
			{
				var fields = (string) settings["ExtendedCasesFields"];
				if (!string.IsNullOrEmpty(fields))
				{
					this.ExtendedCasesFields = Newtonsoft.Json.JsonConvert.DeserializeObject<ExtendedField[]>(fields);
				}
			}
			if (settings.ContainsKey("ExtendedProfilesBusinessCodeFields"))
			{
				var fields = (string) settings["ExtendedProfilesBusinessCodeFields"];
				if (!string.IsNullOrEmpty(fields))
				{
					this.ExtendedProfilesBusinessCodeFields = Newtonsoft.Json.JsonConvert.DeserializeObject<ExtendedProfileBusinessCodeField[]>(fields);
				}
			}

			this.Integrations.Load(settings);
			this.WebAgentUrlLoginSettings.Load(settings);
			this.WebAgentStateManagementSettings.Load(settings);

			if (settings.ContainsKey("PushNotificationsServiceBusUseWebSockets"))
			{
				try
				{
					this.PushNotificationsServiceBusUseWebSockets = (bool) settings["PushNotificationsServiceBusUseWebSockets"];
				}
				catch { }
			}
			if (settings.ContainsKey("PushNotificationsServiceBusConcurrentMessages"))
			{
				try
				{
					this.PushNotificationsServiceBusConcurrentMessages = Convert.ToInt32(settings["PushNotificationsServiceBusConcurrentMessages"]);
				}
				catch { }
			}
			if (settings.ContainsKey("PushNotificationsServiceBusConcurrentStatuses"))
			{
				try
				{
					this.PushNotificationsServiceBusConcurrentStatuses = Convert.ToInt32(settings["PushNotificationsServiceBusConcurrentStatuses"]);
				}
				catch { }
			}
			if (settings.ContainsKey("PushNotificationsServiceBusConcurrentCalls"))
			{
				try
				{
					this.PushNotificationsServiceBusConcurrentCalls = Convert.ToInt32(settings["PushNotificationsServiceBusConcurrentCalls"]);
				}
				catch { }
			}
			if (settings.ContainsKey("PushNotificationsServiceBusConcurrentMassive"))
			{
				try
				{
					this.PushNotificationsServiceBusConcurrentMassive = Convert.ToInt32(settings["PushNotificationsServiceBusConcurrentMassive"]);
				}
				catch { }
			}
			if (settings.ContainsKey("CloudHeadersToAdd"))
			{
				var headers = (string) settings["CloudHeadersToAdd"];
				if (!string.IsNullOrEmpty(headers))
				{
					this.CloudHeadersToAdd = Newtonsoft.Json.JsonConvert.DeserializeObject<ExtraHeader[]>(headers);
				}
			}
			if (settings.ContainsKey("CloudHeadersToRemove"))
			{
				var headers = (string) settings["CloudHeadersToRemove"];
				if (!string.IsNullOrEmpty(headers))
				{
					this.CloudHeadersToRemove = Newtonsoft.Json.JsonConvert.DeserializeObject<string[]>(headers);
				}
			}
			if (settings.ContainsKey("RestrictedIPsForWeb"))
			{
				var restrictedText = (string) settings["RestrictedIPsForWeb"];
				if (!string.IsNullOrEmpty(restrictedText))
				{
					try
					{
						this.RestrictedIPsForWeb = restrictedText.Split(",".ToCharArray());
					}
					catch { }
				}
			}
			if (settings.ContainsKey("RestrictedIPsForWebAgent"))
			{
				var restrictedText = (string) settings["RestrictedIPsForWebAgent"];
				if (!string.IsNullOrEmpty(restrictedText))
				{
					try
					{
						this.RestrictedIPsForWebAgent = restrictedText.Split(",".ToCharArray());
					}
					catch { }
				}
			}
			if (settings.ContainsKey("PbxIntegrationAgentGroupForNewAgents") &&
				!string.IsNullOrEmpty((string) settings["PbxIntegrationAgentGroupForNewAgents"]))
			{
				this.PbxIntegrationAgentGroupForNewAgents = int.Parse((string) settings["PbxIntegrationAgentGroupForNewAgents"]);
			}

			this.WorkingTimes.Load(settings);

			if (settings.ContainsKey("DefaultTimeZone"))
			{
				var timezoneId = (string) settings["DefaultTimeZone"];
				try
				{
					var timezone = TimeZoneInfo.FindSystemTimeZoneById(timezoneId);
					if (timezone != null)
						this.DefaultTimeZone = timezone;
				}
				catch { }
			}

			if (settings.ContainsKey("DefaultLocale"))
				this.DefaultLocale = (string) settings["DefaultLocale"];

			if (settings.ContainsKey("TimeZonesToConsolide"))
			{
				var timeZonesToConsolideText = (string) settings["TimeZonesToConsolide"];
				if (!string.IsNullOrEmpty(timeZonesToConsolideText))
				{
					var timeZonesToConsolide = timeZonesToConsolideText.Split(",".ToCharArray());
					try
					{
						this.TimeZonesToConsolide = timeZonesToConsolide.Select(tz => TimeZoneInfo.FindSystemTimeZoneById(tz)).ToArray();
					}
					catch { }
				}
				else
				{
					this.TimeZonesToConsolide = null;
				}
			}

			this.DeliveryFailedNotification.Load(settings);

			if (settings.ContainsKey("FTPs"))
			{
				string ftps = (string) settings["FTPs"];
				if (!string.IsNullOrEmpty(ftps))
					this.FTPs = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<Guid, Settings.UploadFileConnectionSettings>>(ftps);
			}

			if (settings.ContainsKey("AutomaticExport"))
			{
				string automaticExports = (string) settings["AutomaticExport"];
				if (!string.IsNullOrEmpty(automaticExports))
					this.AutomaticExportSettings = Newtonsoft.Json.JsonConvert.DeserializeObject<Settings.AutomaticExportSettings>(automaticExports);
			}

			if (settings.ContainsKey("MinutesPredictedAht"))
			{
				this.MinutesPredictedAht = (int) settings["MinutesPredictedAht"];
			}
			else
			{
				this.MinutesPredictedAht = 15;
			}

			if (settings.ContainsKey("SecondsEwt"))
			{
				this.SecondsEwt = (int) settings["SecondsEwt"];
			}
			else
			{
				this.SecondsEwt = 15;
			}

            this.AllowToSetASAValueByDefault = settings.ContainsKey("AllowToSetASAValueByDefault") ? (bool)settings["AllowToSetASAValueByDefault"] : false;
			this.ASADefaultValue = settings.ContainsKey("ASADefaultValue") ? (int)settings["ASADefaultValue"] : 30;

			this.VideoCubiqUrl = settings.ContainsKey("VideoCubiqUrl") ? (string)settings["VideoCubiqUrl"] : "";
			this.VideoCubiqApiKey = settings.ContainsKey("VideoCubiqApiKey") ? (string)settings["VideoCubiqApiKey"] : "";
			this.VideoCubiqSecret = settings.ContainsKey("VideoCubiqSecret") ? (string)settings["VideoCubiqSecret"] : "";
			this.VideoCubiqRecordingUrl = settings.ContainsKey("VideoCubiqRecordingUrl") ? (string)settings["VideoCubiqRecordingUrl"] : "";

			this.Gateway.Load(settings);

			if (settings.ContainsKey("ScheduledReportsToGenerate"))
			{
				this.ScheduledReportsToGenerate = (int) settings["ScheduledReportsToGenerate"];
			}
			
			if (settings.ContainsKey("EmailConnections"))
			{
				string emails = (string)settings["EmailConnections"];
				if (!string.IsNullOrEmpty(emails))
				{
					this.EmailConnections = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<Guid, Settings.EmailConnectionSettings>>(emails);
				}
			}
			else if (this.EmailConnection.From != null && 
					!this.EmailConnection.From.Equals("<EMAIL>", StringComparison.InvariantCultureIgnoreCase))
			{

				string convertedConnection = JsonConvert.SerializeObject(this.EmailConnection);
				var defaultConnection = JsonConvert.DeserializeObject<EmailConnectionSettings>(convertedConnection);
				
				defaultConnection.Name = string.IsNullOrEmpty(this.EmailConnection.From) ? "Conexión por defecto" : this.EmailConnection.From;
				defaultConnection.UseAsDefault = true;

				if (defaultConnection.UseCredentials)
				{
					if (defaultConnection.Credentials.SecurePassword != null &&
						defaultConnection.Credentials.SecurePassword.Length > 0)
					{
						defaultConnection.Credentials.UserName = defaultConnection.Credentials.UserName;
						defaultConnection.Password = defaultConnection.Credentials.SecurePassword;
					}
				}
				
				this.EmailConnections.Add(Guid.NewGuid(), defaultConnection);
			}

			if (settings.ContainsKey("CheckLastQueueByTime"))
				this.CheckLastQueueByTime = (bool) settings["CheckLastQueueByTime"];
			if (settings.ContainsKey("CheckLastQueueByTimeValue"))
				this.CheckLastQueueByTimeValue = (int) settings["CheckLastQueueByTimeValue"];

			if (settings.ContainsKey("EnableCapi"))
				this.EnableCapi = (bool) settings["EnableCapi"];

			this.Storage.Load(settings);
			this.RealTime.Load(settings);

		}

		/// <summary>
		/// Devuelve los parámetros del sistema en un diccionario
		/// </summary>
		/// <returns>Un diccionario con las claves y sus respectivos valores que representan los parámetros del sistema</returns>
		public Dictionary<string, object> Retrieve()
		{
			return Retrieve(false);
		}

		/// <summary>
		/// Devuelve los parámetros del sistema en un diccionario
		/// </summary>
		/// <returns>Un diccionario con las claves y sus respectivos valores que representan los parámetros del sistema</returns>
		public Dictionary<string, object> Retrieve(bool omitIgnores)
		{
			Dictionary<string, object> data = new Dictionary<string, object>();

			if (!omitIgnores)
			{
				data.Add("DefaultLanguage", this.DefaultLanguage);
				data.Add("Theme", this.Theme);
				data.Add("UseACDBalancing", this.UseACDBalancing);
				data.Add("ACDBalancingWithQueueLevels", this.ACDBalancingWithQueueLevels);
				data.Add("ACDBalancingWithQueueLevelsWorking", this.ACDBalancingWithQueueLevelsWorking);
				data.Add("AgentApplicationSetupFile", this.AgentApplicationSetupFile);
				data.Add("ChatPackageSetupFile", this.ChatPackageSetupFile);
				if (this.AgentApplicationVersion != null)
					data.Add("AgentApplicationVersion", this.AgentApplicationVersion.ToString());
				data.Add("WebAgentURL", this.WebAgentURL);
				data.Add("WebAgentAllowOutdatedLogins", this.WebAgentAllowOutdatedLogins);
				data.Add("WebAgentVersion", this.WebAgentVersion);
				data.Add("EncryptMessages", this.EncryptMessages);
				data.Add("AgentHistoricMaxMessages", this.AgentHistoricMaxMessages);
				data.Add("MarkWhiteListMessagesAsVIM", this.MarkWhiteListMessagesAsVIM);
				data.Add("PrioritizeVIMOverQueueLevel", this.PrioritizeVIMOverQueueLevel);
			}

			data.Add("MinimumPasswordStrength", this.MinimumPasswordStrength);
			data.Add("UserPasswordExpireDay", this.UserPasswordExpireDay);
			data.Add("UserPasswordRepeat", this.UserPasswordRepeat);
			data.Add("UserMandatory2FA", this.UserMandatory2FA);
			data.Add("UserPasswordRepeatAmount", this.UserPasswordRepeatAmount);
			data.Add("UserPasswordWrongBlock", this.UserPasswordWrongBlock);
			data.Add("UserPasswordWrongsCaptha", this.UserPasswordWrongsCaptha);
			data.Add("DaysInactiveUserBlock", this.DaysInactiveUserBlock);
			data.Add("UserPasswordRefresh", this.UserPasswordRefresh);
			data.Add("UserPasswordFirstChange", this.UserPasswordFirstChange);
			data.Add("UserPasswordValidationRegex", this.UserPasswordValidationRegex);
			data.Add("UserPasswordMessageRegex", this.UserPasswordMessageRegex);
			data.Add("MinimumAgentPasswordStrength", this.MinimumAgentPasswordStrength);
			data.Add("AgentPasswordExpireDay", this.AgentPasswordExpireDay);
			data.Add("AgentPasswordRepeat", this.AgentPasswordRepeat);
			data.Add("AgentMandatory2FA", this.AgentMandatory2FA);
			data.Add("AgentPasswordRepeatAmount", this.AgentPasswordRepeatAmount);
			data.Add("AgentPasswordWrongsBlock", this.AgentPasswordWrongsBlock);
			data.Add("AgentPasswordWrongsCaptha", this.AgentPasswordWrongsCaptha);
			data.Add("DaysInactiveAgentBlock", this.DaysInactiveAgentBlock);
			data.Add("AgentCanChangePassword", this.AgentCanChangePassword);
			data.Add("AgentPasswordRefresh", this.AgentPasswordRefresh);
			data.Add("AgentPasswordFirstChange", this.AgentPasswordFirstChange);
			data.Add("AgentPasswordValidationRegex", this.AgentPasswordValidationRegex);
			data.Add("AgentPasswordMessageRegex", this.AgentPasswordMessageRegex);

			data.Add("UseSentDate", this.UseSentDate);
			data.Add("AutoMarkAsReadMessages", this.AutoMarkAsReadMessages);
			data.Add("MaxAssignableMessagesPerUser", this.MaxAssignableMessagesPerUser);
			data.Add("SessionTimeOut", this.SessionTimeOut);
			
			data.Add("WarnAgentForSpellingErrors", this.WarnAgentForSpellingErrors);
			data.Add("AllowAgentToBlockUsers", this.AllowAgentToBlockUsers);
			data.Add("AgentMustEnterDiscardReason", this.AgentMustEnterDiscardReason);
			data.Add("AllowAgentsToReturnMessagesToQueue", this.AllowAgentsToReturnMessagesToQueue);
			data.Add("AgentMustEnterReturnToQueueReason", this.AgentMustEnterReturnToQueueReason);
			data.Add("MaximumNumberOfTimesMessageCanBeReturned", this.MaximumNumberOfTimesMessageCanBeReturned);
			data.Add("AllowAgentsToSelectQueueOnReturnToQueue", this.AllowAgentsToSelectQueueOnReturnToQueue);
			data.Add("AllowAgentsToReturnMessagesWithRelatedMessagesToQueue", this.AllowAgentsToReturnMessagesWithRelatedMessagesToQueue);
			data.Add("OutgoingMessagesEnabled", this.OutgoingMessagesEnabled);
			if (!omitIgnores)
			{	
				data.Add("MaxRetriesForOutgoingMessages", this.MaxRetriesForOutgoingMessages);
				data.Add("MaxResultsInGrids", this.MaxResultsInGrids);
				if (this.VIPSocialUsers.Count > 0)
				{
					string json = Newtonsoft.Json.JsonConvert.SerializeObject(this.VIPSocialUsers.Distinct());
					data.Add("VIPSocialUsers", json);
				}
				else
				{
					data.Add("VIPSocialUsers", string.Empty);
				}
				if (this.TesterSocialUsers.Count > 0)
				{
					string json = Newtonsoft.Json.JsonConvert.SerializeObject(this.TesterSocialUsers.Distinct());
					data.Add("TesterSocialUsers", json);
				}
				else
				{
					data.Add("TesterSocialUsers", string.Empty);
				}
				if (this.DoNotCallSocialUsers.Count > 0)
				{
					string json = Newtonsoft.Json.JsonConvert.SerializeObject(this.DoNotCallSocialUsers.Distinct());
					data.Add("DoNotCallSocialUsers", json);
				}
				else
				{
					data.Add("DoNotCallSocialUsers", string.Empty);
				}
				if (this.BlockedSocialUsers.Count > 0)
				{
					string json = Newtonsoft.Json.JsonConvert.SerializeObject(this.BlockedSocialUsers.Distinct());
					data.Add("BlockedSocialUsers", json);
				}
				else
				{
					data.Add("BlockedSocialUsers", string.Empty);
				}
			}
			string jsonServicesToWarnForMessages = Newtonsoft.Json.JsonConvert.SerializeObject(this.ServicesToWarnForMessages);
			data.Add("ServicesToWarnForMessages", jsonServicesToWarnForMessages);
			if (this.AuxReasonForOutgoingMessages != null)
				data.Add("AuxReasonForOutgoingMessages", this.AuxReasonForOutgoingMessages.Value);
			if (this.AuxReasonForMyOutgoingCases != null)
			{
				data.Add("AuxReasonForMyOutgoingCases", this.AuxReasonForMyOutgoingCases.Value);
			}
			data.Add("AuthenticationType", (short) this.AuthenticationType);
			this.LDAP.Save(data, omitIgnores);
			this.GoogleAuth.Save(data, omitIgnores);
			this.SamlAuth.Save(data, omitIgnores);
			this.WebAgentUrlLoginSettings.Save(data, omitIgnores);
			this.WebAgentStateManagementSettings.Save(data, omitIgnores);

			if (!omitIgnores)
			{
				this.EmailConnection.Save(data, omitIgnores);
				this.Service.Save(data, omitIgnores);
				this.ExporterService.Save(data, omitIgnores);
				this.SurveysService.Save(data, omitIgnores);
				this.BitLy.Save(data, omitIgnores);
				this.CognitiveServices.Save(data, omitIgnores);
				this.YFlow.Save(data, omitIgnores);
				this.YUsage.Save(data, omitIgnores);
				this.YFlowContingency.Save(data, omitIgnores);
				this.YSmart.Save(data, omitIgnores);
				this.RestChat.Save(data, omitIgnores);
				this.Maintenance.Save(data, omitIgnores);
			}
			this.Cases.Save(data, omitIgnores);
			this.Chat.Save(data, omitIgnores);
			this.InternalChat.Save(data, omitIgnores);
			this.Whatsapp.Save(data, omitIgnores);
			this.AppleMessaging.Save(data, omitIgnores);
			this.MercadoLibre.Save(data, omitIgnores);
			this.Telegram.Save(data, omitIgnores);
			this.Twitter.Save(data, omitIgnores);
			this.GoogleRBM.Save(data, omitIgnores);
			this.Facebook.Save(data, omitIgnores);
			this.FacebookMessenger.Save(data, omitIgnores);
			this.SMS.Save(data, omitIgnores);
			this.Skype.Save(data, omitIgnores);
			this.Subscription.Save(data, omitIgnores);
			this.Instagram.Save(data, omitIgnores);
			this.LinkedIn.Save(data, omitIgnores);
			this.Mail.Save(data, omitIgnores);	
			this.ForwardSettings.Save(data, omitIgnores);
			if (!omitIgnores)
			{
				this.FilterEmailSettings.Save(data, omitIgnores);
				this.OutOfDiskSpaceForAttachments.Save(data, omitIgnores);
				this.EmailDatabaseProblems.Save(data, omitIgnores);
				this.EmailOutOfMemory.Save(data, omitIgnores);
				this.AgentCreatedLoginInformation.Save(data, omitIgnores);
				this.AgentPasswordChanged.Save(data, omitIgnores);
				this.AnnoyingEmailSettings.Save(data, omitIgnores);
			}
			data.Add("IntervalsPerHour", this.IntervalsPerHour);
			data.Add("AllowForwardAction", this.AllowForwardAction);
			data.Add("ForwardOutsideDomainAvailable", this.ForwardOutsideDomainAvailable);
			if (!omitIgnores)
			{
				data.Add("AttachmentsRoute", this.AttachmentsRoute);
				data.Add("AttachmentsMinimumFreeSpace", this.AttachmentsMinimumFreeSpace);
			}
			data.Add("FavoriteMails", this.FavoriteMails);
			data.Add("AvailableDomainsToForward", this.AvailableDomainsToForward);
			data.Add("DeleteNotifications", this.DeleteNotifications);
			if (!omitIgnores)
			{
				data.Add("ScheduledReportsToMantain", this.ScheduledReportsToMantain);
				data.Add("GenerateDailyReport", this.GenerateDailyReport);
				data.Add("EnableFtpDailyReports", this.EnableFtpDailyReports);
				if (this.EnableFtpDailyReports)
				{
					data.Add("FtpIdDailyReports", this.FtpIdDailyReports);
					data.Add("FtpDirectoryDailyReports", this.FtpDirectoryDailyReports);
					data.Add("DailyReportsZipExcel", this.DailyReportsZipExcel);
					data.Add("DailyReportsZipCSV", this.DailyReportsZipCSV);
				}
				data.Add("DailyReportsToMantain", this.DailyReportsToMantain);
				data.Add("DailyReportsDeleteLocalFiles", this.DailyReportsDeleteLocalFiles);
				data.Add("DailyReportsFormat", ((short) this.DailyReportsFormat).ToString());
				if (this.DailyReportsToGenerate != null && this.DailyReportsToGenerate.Length > 0)
					data.Add("DailyReportsToGenerate", string.Join(",", this.DailyReportsToGenerate.Select(f => (short) f)));
				this.EmailExport.Save(data, omitIgnores);
				this.EmailExportAborted.Save(data, omitIgnores);
				this.EmailDailyReports.Save(data, omitIgnores);
				data.Add("MaxRecordsToExport", this.MaxRecordsToExport);
			}
			data.Add("EnableSurveys", this.EnableSurveys);
			if (!omitIgnores)
				data.Add("SurveysURL", this.SurveysURL);
			data.Add("BusinessDataRegex", this.BusinessDataRegex);
			data.Add("BusinessDataWrongInputMessage", this.BusinessDataWrongInputMessage);
			data.Add("BusinessDataFormatMessage", this.BusinessDataFormatMessage);
			if (this.ExtendedProfilesFields != null && this.ExtendedProfilesFields.Length > 0)
				data.Add("ExtendedProfilesFields", Newtonsoft.Json.JsonConvert.SerializeObject(this.ExtendedProfilesFields));
			else
				data.Add("ExtendedProfilesFields", string.Empty);
			if (this.ExtendedCasesFields != null && this.ExtendedCasesFields.Length > 0)
				data.Add("ExtendedCasesFields", Newtonsoft.Json.JsonConvert.SerializeObject(this.ExtendedCasesFields));
			else
				data.Add("ExtendedCasesFields", string.Empty);
			if (this.ExtendedProfilesBusinessCodeFields != null && this.ExtendedProfilesBusinessCodeFields.Length > 0)
				data.Add("ExtendedProfilesBusinessCodeFields", Newtonsoft.Json.JsonConvert.SerializeObject(this.ExtendedProfilesBusinessCodeFields));
			else
				data.Add("ExtendedProfilesBusinessCodeFields", string.Empty);
			this.Integrations.Save(data, omitIgnores);

			if (!omitIgnores)
			{
				data.Add("PushNotificationsServiceBusUseWebSockets", this.PushNotificationsServiceBusUseWebSockets);
				data.Add("PushNotificationsServiceBusConcurrentMessages", this.PushNotificationsServiceBusConcurrentMessages);
				data.Add("PushNotificationsServiceBusConcurrentStatuses", this.PushNotificationsServiceBusConcurrentStatuses);
				data.Add("PushNotificationsServiceBusConcurrentCalls", this.PushNotificationsServiceBusConcurrentCalls);
				data.Add("PushNotificationsServiceBusConcurrentMassive", this.PushNotificationsServiceBusConcurrentMassive);

				if (this.CloudHeadersToAdd != null && this.CloudHeadersToAdd.Length > 0)
					data.Add("CloudHeadersToAdd", Newtonsoft.Json.JsonConvert.SerializeObject(this.CloudHeadersToAdd));
				else
					data.Add("CloudHeadersToAdd", string.Empty);
				if (this.CloudHeadersToRemove != null && this.CloudHeadersToRemove.Length > 0)
					data.Add("CloudHeadersToRemove", Newtonsoft.Json.JsonConvert.SerializeObject(this.CloudHeadersToRemove));
				else
					data.Add("CloudHeadersToRemove", string.Empty);

				if (this.RestrictedIPsForWeb == null || this.RestrictedIPsForWeb.Length == 0)
					data.Add("RestrictedIPsForWeb", string.Empty);
				else
					data.Add("RestrictedIPsForWeb", string.Join(",", this.RestrictedIPsForWeb.Select(ip => ip.ToString())));

				if (this.RestrictedIPsForWebAgent == null || this.RestrictedIPsForWebAgent.Length == 0)
					data.Add("RestrictedIPsForWebAgent", string.Empty);
				else
					data.Add("RestrictedIPsForWebAgent", string.Join(",", this.RestrictedIPsForWebAgent.Select(ip => ip.ToString())));

				if (this.PbxIntegrationAgentGroupForNewAgents != null)
					data.Add("PbxIntegrationAgentGroupForNewAgents", this.PbxIntegrationAgentGroupForNewAgents.Value.ToString());
				else
					data.Add("PbxIntegrationAgentGroupForNewAgents", string.Empty);
			}

			if (!omitIgnores)
				this.WorkingTimes.Save(data, omitIgnores);

			if (!omitIgnores)
			{
				data.Add("DefaultTimeZone", this.DefaultTimeZone.Id);
				data.Add("DefaultLocale", this.DefaultLocale);
				if (this.TimeZonesToConsolide != null && this.TimeZonesToConsolide.Length > 0)
				{
					data.Add("TimeZonesToConsolide", string.Join(",", this.TimeZonesToConsolide.Select(tz => tz.Id)));
				}
				else
				{
					data.Add("TimeZonesToConsolide", string.Empty);
				}
			}

			if (!omitIgnores)
			{
				this.DeliveryFailedNotification.Save(data, omitIgnores);

				if (this.FTPs != null && this.FTPs.Count > 0)
					data.Add("FTPs", Newtonsoft.Json.JsonConvert.SerializeObject(this.FTPs));
				else
					data.Add("FTPs", string.Empty);

				if (this.EmailConnections != null && this.EmailConnections.Count > 0)
					data.Add("EmailConnections", Newtonsoft.Json.JsonConvert.SerializeObject(this.EmailConnections));
				else
					data.Add("EmailConnections", string.Empty);
				
				data.Add("AutomaticExport", Newtonsoft.Json.JsonConvert.SerializeObject(this.AutomaticExportSettings));
			}

			data.Add("MinutesPredictedAht", this.MinutesPredictedAht);
			data.Add("SecondsEwt", this.SecondsEwt);

			data.Add("AllowToSetASAValueByDefault", this.AllowToSetASAValueByDefault);
			data.Add("ASADefaultValue", this.ASADefaultValue);
			data.Add("VideoCubiqUrl", this.VideoCubiqUrl);
			data.Add("VideoCubiqApiKey", this.VideoCubiqApiKey);
			data.Add("VideoCubiqSecret", this.VideoCubiqSecret);
			data.Add("VideoCubiqRecordingUrl", this.VideoCubiqRecordingUrl);

			data.Add("MinutesToAbortExporting", this.MinutesToAbortExporting);

			if (!omitIgnores)
			{
				this.Gateway.Save(data, omitIgnores);
				this.Storage.Save(data, omitIgnores);
			}

			data.Add("ScheduledReportsToGenerate", this.ScheduledReportsToGenerate);

			data.Add("CheckLastQueueByTime", this.CheckLastQueueByTime);
			data.Add("CheckLastQueueByTimeValue", this.CheckLastQueueByTimeValue);

			data.Add("EnableCapi", this.EnableCapi);

			this.RealTime.Save(data, omitIgnores);

			return data;
		}

#if !NETCOREAPP
		/// <summary>
		/// Devuelve si una IP de un agente está restringida para acceder
		/// </summary>
		/// <param name="address">El <see cref="System.Net.IPAddress"/> con la IP del agente</param>
		/// <returns><code>true</code> si la IP está restringida para acceder; en caso contrario, <code>false</code></returns>
		public bool IsWebAgentIPRestricted(System.Net.IPAddress remoteAddress)
		{
			if (this.RestrictedIPsForWebAgent == null ||
				this.RestrictedIPsForWebAgent.Length == 0)
			{
				return false;
			}

			foreach (var ipOrNetwork in this.RestrictedIPsForWebAgent)
			{
				if (ipOrNetwork.Contains("/"))
				{
					System.Net.IPNetwork network;
					if (System.Net.IPNetwork.TryParse(ipOrNetwork, out network))
					{
						if (network.Contains(remoteAddress))
							return false;
					}
				}
				else
				{
					System.Net.IPAddress address;
					if (System.Net.IPAddress.TryParse(ipOrNetwork, out address))
					{
						if (address.Equals(remoteAddress))
							return false;
					}
				}
			}

			return true;
		}

		/// <summary>
		/// Devuelve si una IP de un usuario está restringida para acceder
		/// </summary>
		/// <param name="address">El <see cref="System.Net.IPAddress"/> con la IP del usuario</param>
		/// <returns><code>true</code> si la IP está restringida para acceder; en caso contrario, <code>false</code></returns>
		public bool IsWebIPRestricted(System.Net.IPAddress remoteAddress)
		{
			if (this.RestrictedIPsForWeb == null ||
				this.RestrictedIPsForWeb.Length == 0)
			{
				return false;
			}

			foreach (var ipOrNetwork in this.RestrictedIPsForWeb)
			{
				if (ipOrNetwork.Contains("/"))
				{
					System.Net.IPNetwork network;
					if (System.Net.IPNetwork.TryParse(ipOrNetwork, out network))
					{
						if (network.Contains(remoteAddress))
							return false;
					}
				}
				else
				{
					System.Net.IPAddress address;
					if (System.Net.IPAddress.TryParse(ipOrNetwork, out address))
					{
						if (address.Equals(remoteAddress))
							return false;
					}
				}
			}

			return true;
		}
#endif

		/// <summary>
		/// Devuelve si el <see cref="Common.Interval"/> es el último intervalo del día de acuerdo al huso horario configurado en <see cref="SystemSettings.DefaultTimeZone"/>
		/// </summary>
		/// <param name="interval">El <see cref="Common.Interval"/> a verificar</param>
		/// <returns><code>true</code> si el intervalo es el último en el huso horario por defecto; en caso contrario, <code>false</code></returns>
		public bool IsIntervalTheLastOfTheDay(Common.Interval interval)
		{
			return IsIntervalTheLastOfTheDay(interval, this.DefaultTimeZone);
		}

		/// <summary>
		/// Devuelve <see cref="EmailConnections"/> en forma de lista
		/// </summary>
		public List<object> GetEmailsConnections()
		{
			var emails = Instance.EmailConnections;
			if (emails == null || emails.Count == 0)
				return new List<object>();

			var emailsAsList = new List<object>();
			foreach (var email in Instance.EmailConnections)
			{
				emailsAsList.Add(new
				{
					ID = email.Key,
					email.Value.UseAsDefault,
					email.Value.Name
				});
			}

			return emailsAsList;
		}

		/// <summary>
		/// Retorna si la conexion de correo coincide con la que utiliza la entidad
		/// </summary>
		/// <param name="emailId">El Guid con el id de conexion</param>
		/// <param name="emailConnection">El string a comparar</param>
		/// <returns></returns>
		public bool CheckEmailConnection(Guid emailId, string emailConnection)
		{
			if (!string.IsNullOrEmpty(emailConnection) && Guid.TryParse(emailConnection, out Guid connectionGuid))
			{
				return emailId == connectionGuid;
			}
			return false;
		}

		/// <summary>
		/// Retorna si alguna <see cref="EmailConnectionSettings"/> esta siendo utilizada en <see cref="SystemSettings"/>
		/// </summary>
		/// <param name="emailId"></param>
		/// <returns></returns>
		public bool HasEmailConnectionInUse(Guid emailId)
		{
			if (CheckEmailConnection(emailId, Instance.OutOfDiskSpaceForAttachments.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.EmailDatabaseProblems.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.EmailOutOfMemory.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.AnnoyingEmailSettings.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.AgentCreatedLoginInformation.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.AgentCreatedLoginInformation.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.AgentPasswordChanged.EmailConnection))
				return true;

			if (CheckEmailConnection(emailId, Instance.FilterEmailSettings.EmailConnection))
				return true;

			if (Instance.YFlow.Enabled)
			{
				if (CheckEmailConnection(emailId, Instance.YFlow.AuthenticationFailed.EmailConnection))
					return true;

				if (CheckEmailConnection(emailId, Instance.YFlow.InvokeFailed.EmailConnection))
					return true;
			}

			if (Instance.YFlowContingency.Enabled)
			{
				if (CheckEmailConnection(emailId, Instance.YFlowContingency.AuthenticationFailed.EmailConnection))
					return true;

				if (CheckEmailConnection(emailId, Instance.YFlowContingency.InvokeFailed.EmailConnection))
					return true;
			}

			if (Instance.DeliveryFailedNotification.NotifyByEmail)
			{ 
				if (CheckEmailConnection(emailId, Instance.DeliveryFailedNotification.EmailNotificationSettings.EmailConnection))
					return true;
			}

			return false;
		}

		/// <summary>
		/// Actualiza la conexion por defecto de param del sistema.
		/// </summary>
		/// <param name="jsonConnection">Json con los datos de conexion</param>
		public void UpdateDefaultEmailConnection (string jsonConnection)
		{
			try
			{
				var newDefaultConnection = JsonConvert.DeserializeObject<EmailConnectionSettings>(jsonConnection);
				Instance.EmailConnection = newDefaultConnection;

				if (newDefaultConnection.UseCredentials && 
					newDefaultConnection.Credentials != null)
				{
					var username = newDefaultConnection.Credentials.UserName;
					var password = newDefaultConnection.Password;
					Instance.EmailConnection.Credentials = new global::System.Net.NetworkCredential(username, password);
				}
			}
			
			catch(Exception ex)
			{
				Tracer.TraceError("Ocurrió un error al configurar la casilla por defecto: {0}", ex.ToString());
			}
		}

		/// <summary>
		/// Devuelve si el <see cref="Common.Interval"/> es el último intervalo del día del huso horario especificado por <paramref name="tz"/>
		/// </summary>
		/// <param name="interval">El <see cref="Common.Interval"/> a verificar</param>
		/// <param name="tz">El <see cref="TimeZoneInfo"/> en el cual se verificará</param>
		/// <returns><code>true</code> si el intervalo es el último en el huso horario especificado; en caso contrario, <code>false</code></returns>
		public bool IsIntervalTheLastOfTheDay(Common.Interval interval, TimeZoneInfo tz)
		{
			if (tz.HasSameRules(this.LocalTimeZone))
			{
				return interval.IsTheLastOfTheDay;
			}

			var sourceDateTimeOffset = new DateTimeOffset(interval.IntervalDateTime, this.LocalTimeZone.BaseUtcOffset);
			var destinationDatetimeOffset = TimeZoneInfo.ConvertTime(sourceDateTimeOffset, tz);
			var destinationInterval = new Yoizen.Common.Interval(destinationDatetimeOffset.DateTime, interval.IntervalsPerHour);
			return destinationInterval.IsTheLastOfTheDay;
		}

		/// <summary>
		/// Genera las fechas desde y hasta de un intervalo para poder realizar consultas dependiendo del horario de las tablas consolidadas
		/// </summary>
		/// <param name="interval">El <see cref="Common.Interval"/> que debería ser el último del día</param>
		/// <param name="tz">El <see cref="TimeZoneInfo"/> del cual debería ser el último intervalo del día el <paramref name="interval"/></param>
		/// <param name="from">Cuando retorna, devuelve la fecha desde para realizar consultas</param>
		/// <param name="to">Cuando retorna, devuelve la fecha hasta para realizar consultas</param>
		public void ExtractDatesFromLastIntervalOfTheDay(Common.Interval interval, TimeZoneInfo tz, out DateTime from, out DateTime to)
		{
			if (!this.IsIntervalTheLastOfTheDay(interval, tz))
				throw new ArgumentException("El intervalo debe ser el último del día para el huso horario especificado", nameof(interval));

			to = interval.Next().IntervalDateTime;
			from = to.AddDays(-1);
			to = to.AddMilliseconds(-1);
		}

		/// <summary>
		/// Convierte una fecha del huso horario del servidor, al huso horario especificado por <paramref name="tz"/>
		/// </summary>
		/// <param name="date">El <see cref="DateTime"/> a convertir</param>
		/// <param name="tz">El <see cref="TimeZoneInfo"/> al cual se convertirá la fecha</param>
		/// <returns>Un <see cref="DateTime"/> convertido al <see cref="TimeZoneInfo"/> especificado</returns>
		public DateTime ConvertServerDateTimeToOther(DateTime date, TimeZoneInfo tz)
		{
			DateTimeOffset offset = new DateTimeOffset(date.Ticks, this.LocalTimeZone.BaseUtcOffset);
			
			return TimeZoneInfo.ConvertTime(offset, tz).DateTime;
		}

		/// <summary>
		/// Genera un intervalo con timezone del SystemSettings
		/// </summary>
		/// <returns>El intervalo con la fecha con timezone del SystemSettings</returns>
		public Interval GetCurrentIntervalInDefaultTimeZone()
		{
			DateTime serverDateNow = DateTime.Now;

			var userDefaulTimeZone = this.DefaultTimeZone;
			var timeZone = TimeZoneInfo.FindSystemTimeZoneById(userDefaulTimeZone.Id);

			DateTime userDateNow = ConvertServerDateTimeToOther(serverDateNow, timeZone);

			return new Interval(userDateNow, 2);
		}

		/// <summary>
		/// Convierte una fecha con horario local del usuario al horario del servidor
		/// </summary>
		/// <param name="date">La fecha a convertir</param>
		/// <returns>La fecha convertida al horario del servidor</returns>
		public DateTime ConvertDateToServerDate(DateTime date)
		{
			var timeZone = TimeZoneInfo.FindSystemTimeZoneById(this.DefaultTimeZone.Id);
			var offset = new DateTimeOffset(date.Ticks, timeZone.BaseUtcOffset);

			return TimeZoneInfo.ConvertTime(offset, DomainModel.SystemSettings.Instance.LocalTimeZone).DateTime;
		}

		#endregion

		#region Private Methods

		/// <summary>
		/// Retorna un <see cref="MimeKit.SmtpClient"/>
		/// </summary>
		/// <param name="connectionToUse">Un <see cref="EmailConnectionSettings"/> con datos de conexión</param>
		/// <returns>Un <see cref="MailKit.Net.Smtp.SmtpClient"/> ya conectado</returns>
		/// <remarks>
		/// Se deberá hacer Dispose del <paramref name="smtpClient"/> devuelto
		/// </remarks>
		private MailKit.Net.Smtp.SmtpClient CreateMailSmtpClient(EmailConnectionSettings connectionToUse)
		{
			var smtpClient = new MailKit.Net.Smtp.SmtpClient();
			smtpClient.ServerCertificateValidationCallback = (mysender, certificate, chain, sslPolicyErrors) => { return true; };
			smtpClient.SslProtocols = SslProtocols.Ssl3 | SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12;
			smtpClient.CheckCertificateRevocation = false;

			int portToUse = 0;
			if (connectionToUse.Port != null)
				portToUse = connectionToUse.Port.Value;
			var options = MailKit.Security.SecureSocketOptions.Auto;
			if (connectionToUse.UseSSL)
				options = MailKit.Security.SecureSocketOptions.SslOnConnect;
			smtpClient.Connect(connectionToUse.Server, portToUse, options);

			if (connectionToUse.UseCredentials)
			{
				smtpClient.AuthenticationMechanisms.Remove("XOAUTH2");
				smtpClient.Authenticate(connectionToUse.Credentials.UserName, connectionToUse.Credentials.Password);
			}

			return smtpClient;
		}

		/// <summary>
		/// Retorna un <see cref="MimeKit.SmtpClient"/>
		/// </summary>
		/// <param name="connectionToUse">Un <see cref="EmailConnectionSettings"/> con datos de conexión</param>
		/// <returns>Un <see cref="MailKit.Net.Smtp.SmtpClient"/> ya conectado</returns>
		/// <remarks>
		/// Se deberá hacer Dispose del <paramref name="smtpClient"/> devuelto
		/// </remarks>
		private async Task<MailKit.Net.Smtp.SmtpClient> CreateMailSmtpClientAsync(EmailConnectionSettings connectionToUse)
		{
			var smtpClient = new MailKit.Net.Smtp.SmtpClient();
			smtpClient.ServerCertificateValidationCallback = (mysender, certificate, chain, sslPolicyErrors) => { return true; };
			smtpClient.SslProtocols = SslProtocols.Ssl3 | SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12;
			smtpClient.CheckCertificateRevocation = false;

			int portToUse = 0;
			if (connectionToUse.Port != null)
				portToUse = connectionToUse.Port.Value;
			var options = MailKit.Security.SecureSocketOptions.Auto;
			if (connectionToUse.UseSSL)
				options = MailKit.Security.SecureSocketOptions.SslOnConnect;
			await smtpClient.ConnectAsync(connectionToUse.Server, portToUse, options);

			if (connectionToUse.UseCredentials)
			{
				smtpClient.AuthenticationMechanisms.Remove("XOAUTH2");
				smtpClient.Authenticate(connectionToUse.Credentials.UserName, connectionToUse.Credentials.Password);
			}

			return smtpClient;
		}

		/// <summary>
		/// Retorna un <see cref="Microsoft.Exchange.WebServices.Data.ExchangeService"/> que se utilizará para enviar el email
		/// </summary>
		/// <param name="connectionToUse">Un <see cref="EmailConnectionSettings"/> con datos de conexión</param>
		/// <returns>Un <see cref="Microsoft.Exchange.WebServices.Data.ExchangeService"/></returns>
		private Microsoft.Exchange.WebServices.Data.ExchangeService CreateMailEWSClient(EmailConnectionSettings connectionToUse)
		{
			Microsoft.Exchange.WebServices.Data.ExchangeService exchangeService;

			if (connectionToUse.EWSVersion == null)
				exchangeService = new Microsoft.Exchange.WebServices.Data.ExchangeService();
			else
				exchangeService = new Microsoft.Exchange.WebServices.Data.ExchangeService((Microsoft.Exchange.WebServices.Data.ExchangeVersion) connectionToUse.EWSVersion.Value);

			if (connectionToUse.EWSAuthenticationType == EmailConnectionSettings.EWSAuthenticationTypes.UserAndPassword)
			{
				exchangeService.Credentials = new Microsoft.Exchange.WebServices.Data.WebCredentials(connectionToUse.Credentials);
			}
			else
			{
				var cca = Microsoft.Identity.Client.ConfidentialClientApplicationBuilder
						.Create(connectionToUse.EWSOAuthAppID)
						.WithClientSecret(connectionToUse.EWSOAuthClientSecret)
						.WithTenantId(connectionToUse.EWSOAuthTenantID)
						.Build();

				// The permission scope required for EWS access
				var ewsScopes = new string[] { "https://outlook.office365.com/.default" };

				//Make the token request
				var authResult = cca.AcquireTokenForClient(ewsScopes).ExecuteAsync().GetAwaiter().GetResult();

				exchangeService.Credentials = new Microsoft.Exchange.WebServices.Data.OAuthCredentials(authResult.AccessToken);

				//Impersonate the mailbox you'd like to access.
				exchangeService.ImpersonatedUserId = new Microsoft.Exchange.WebServices.Data.ImpersonatedUserId(Microsoft.Exchange.WebServices.Data.ConnectingIdType.SmtpAddress, connectionToUse.EWSOAuthEmailAddress);

				//Include x-anchormailbox header
				exchangeService.HttpHeaders.Add("X-AnchorMailbox", connectionToUse.EWSOAuthEmailAddress);
			}

			exchangeService.Url = new Uri(connectionToUse.Server);

			return exchangeService;
		}

		/// <summary>
		/// Retorna un <see cref="MimeKit.MimeMessage"/> para poder editar sus propiedades
		/// </summary>
		/// <returns>Un <see cref="MimeKit.MimeMessage"/></returns>
		/// <remarks>
		/// Se deberá hacer Dispose del <see cref="MimeKit.MimeMessage"/> devuelto
		/// </remarks>
		private MimeKit.MimeMessage CreateMailMessage(EmailConnectionSettings connecttionToUse)
		{
			var mimeMessage = new MimeKit.MimeMessage();

			mimeMessage.From.Add(new MimeKit.MailboxAddress(connecttionToUse.From, connecttionToUse.From));

			return mimeMessage;
		}

		/// <summary>
		/// Devuelve la conexion de correo a utilizar para realizar un envio de mail
		/// </summary>
		/// <param name="emailId">El GUID de la conexion</param>
		/// <returns></returns>
		private EmailConnectionSettings GetEmailConnectionToUse(Guid emailId)
		{
			var aviableConnections = Instance.EmailConnections;
			if (aviableConnections.ContainsKey(emailId))
			{
				string convertedConnection = JsonConvert.SerializeObject(DomainModel.SystemSettings.Instance.EmailConnections[emailId]);
				var connection = JsonConvert.DeserializeObject<EmailConnectionSettings>(convertedConnection);

				if (connection.UseCredentials && connection.Credentials != null)
					connection.Credentials.SecurePassword = connection.Password;

				return connection;
			}
			else
			{
				Tracer.TraceInfo("No se encontró la conexión de correo, se utilizara la conexion por defecto");
				return this.EmailConnection;
			}
		}

		#endregion
	}

	#region Extensions

	public static class SystemSettingsExtensions
	{
		public static void ResolveHostNameAndIP(this System.Net.Mail.MailMessage mailMessage)
		{
			if (mailMessage == null)
				return;

			if (string.IsNullOrEmpty(mailMessage.Body))
				return;

			if (mailMessage.Body.IndexOf("@@HOSTNAME@@", StringComparison.InvariantCultureIgnoreCase) >= 0)
				mailMessage.Body = mailMessage.Body.Replace("@@HOSTNAME@@", System.Environment.MachineName);

			if (mailMessage.Body.IndexOf("@@IP@@", StringComparison.InvariantCultureIgnoreCase) >= 0)
			{
				try
				{
					var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
					foreach (var ip in host.AddressList)
					{
						if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
						{
							mailMessage.Body = mailMessage.Body.Replace("@@IP@@", ip.ToString());
							break;
						}
					}
				}
				catch
				{
					mailMessage.Body = mailMessage.Body.Replace("@@IP@@", "N/A");
				}
			}
		}

		public static void ResolveHostNameAndIP(this MimeKit.BodyBuilder bodyBuilder)
		{
			if (bodyBuilder == null)
				return;

			if (bodyBuilder.HtmlBody == null || bodyBuilder.HtmlBody.Length == 0)
				return;

			if (bodyBuilder.HtmlBody.IndexOf("@@HOSTNAME@@", StringComparison.InvariantCultureIgnoreCase) >= 0)
				bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("@@HOSTNAME@@", System.Environment.MachineName);

			if (bodyBuilder.HtmlBody.IndexOf("@@IP@@", StringComparison.InvariantCultureIgnoreCase) >= 0)
			{
				try
				{
					var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
					foreach (var ip in host.AddressList)
					{
						if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
						{
							bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("@@IP@@", ip.ToString());
							break;
						}
					}
				}
				catch
				{
					bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("@@IP@@", "N/A");
				}
			}
		}
	}

	#endregion
}