﻿using CsQuery.Utility;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using Yoizen.Common;
using Yoizen.Social.DomainModel.CubiqSettings;
using Yoizen.Social.SocialServices.LinkedIn.Wrappers;

namespace Yoizen.Social.Core.Cubiq
{
	public class CubiqBaseRepository
	{

		#region Constructors

		public CubiqBaseRepository()
		{
		}

		#endregion

		#region Private Methods

		private HttpClient CreateHttpClient()
		{
			var httpClientHandler = new HttpClientHandler
			{
				ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true
			};

			return new HttpClient(httpClientHandler)
			{
				Timeout = TimeSpan.FromSeconds(10)
			};
		}

		private async Task<LoginResponse> AuthenticateAsync()
		{
			var url = $"{DomainModel.SystemSettings.Instance.VideoCubiqUrl}/login";
			var bearer = GetBearerSettingsConfigured();

			var json = JsonConvert.SerializeObject(new LoginRequest
			{
				api_key = bearer.api_key,
				api_secret = bearer.api_secret
			});
			var content = new StringContent(json, Encoding.UTF8, "application/json");

			using (var client = CreateHttpClient())
			using (var request = new HttpRequestMessage(HttpMethod.Post, url))
			{
				request.Content = content;
				request.Headers.Add("Accept", "application/json");

				try
				{
					using (var response = await client.SendAsync(request))
					{
						var responseBody = await response.Content.ReadAsStringAsync();
						if (!response.IsSuccessStatusCode)
						{
							Tracer.TraceError($"Error during authentication. Status Code: {response.StatusCode}, Response: {responseBody}");
							return null;
						}

						try
						{
							return JsonConvert.DeserializeObject<LoginResponse>(responseBody);
						}
						catch (JsonException ex)
						{
							Tracer.TraceError($"Error deserializing LoginResponse: {ex.Message}");
							Tracer.TraceError($"Response Body: {responseBody}");
							return null;
						}
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError($"Error Cubiq login: {ex.Message}");
					return null;
				}
				
			}
		}

		private async Task<string> GetToken()
		{
			var token = await AuthenticateAsync();
			return token?.data;
		}

		private BearerSettings GetBearerSettingsConfigured()
		{
			return new BearerSettings
			{
				api_key = DomainModel.SystemSettings.Instance.VideoCubiqApiKey,
				api_secret = DomainModel.SystemSettings.Instance.VideoCubiqSecret
			};
		}

		#endregion

		#region Public Methods

		public async Task<string> ExecutePostUrlWithAuthorization(string endpoint, StringContent content)
		{
			var token = await GetToken();

			if (token == null)
				return string.Empty;

			var uri = $"{DomainModel.SystemSettings.Instance.VideoCubiqUrl}{endpoint}";

			using (var client = CreateHttpClient())
			using (var request = new HttpRequestMessage(HttpMethod.Post, uri))
			{
				request.Content = content;
				request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

				using (var response = await client.SendAsync(request))
				{
					var bodyResponse = await response.Content.ReadAsStringAsync();

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
						return string.Empty;

					return bodyResponse;
				}
			}
		}

		public async Task<string> ExecutePutUrlWithAuthorization(string endpoint, StringContent content)
		{
			var token = await GetToken();

			if(token == null)
				return string.Empty;

			var uri = $"{DomainModel.SystemSettings.Instance.VideoCubiqUrl}{endpoint}";

			using (var client = CreateHttpClient())
			using (var request = new HttpRequestMessage(HttpMethod.Put, uri))
			{
				request.Content = content;
				request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

				using (var response = await client.SendAsync(request))
				{
					var bodyResponse = await response.Content.ReadAsStringAsync();

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
						return string.Empty;

					return bodyResponse;
				}
			}
		}

		public async Task<string> ExecuteGetFromUrlWithAuthorization(string endpoint)
		{
			var token = await GetToken();

			if (token == null)
				return string.Empty;

			var uri = $"{DomainModel.SystemSettings.Instance.VideoCubiqUrl}{endpoint}";

			using (var client = CreateHttpClient())
			using (var request = new HttpRequestMessage(HttpMethod.Get, uri))
			{
				request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

				using (var response = await client.SendAsync(request))
				{
					var bodyResponse = await response.Content.ReadAsStringAsync();

					if (response.StatusCode != HttpStatusCode.OK)
						return string.Empty;

					return bodyResponse;
				}
			}
		}

		#endregion
	}
}
