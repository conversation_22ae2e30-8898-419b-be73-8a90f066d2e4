﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DAL;
using System.IO;
using Yoizen.Social.DomainModel.Historical;
using Yoizen.Social.DomainModel.Reports.Scheduled;


namespace Yoizen.Social.Web.Reports
{
	public partial class Adherence : ReportsBasePage
	{
		protected override string RedirectUrl { get { return "~/Reports/Adherence.aspx"; } }

		protected override string PageDescription { get { return "Reporte de Adherencia"; } }

		protected override string PageDescriptionLocalizationKey { get { return "reports-adherence-title"; } }

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			if (IsScheduled)
			{
				buttonOKFilter.Visible = false;
				buttonScheduleReport.Visible = true;
				trFromDateFilter.Visible = false;
				trToDateFilter.Visible = false;
			}
			else
			{
				buttonScheduleReport.Visible = false;
				buttonOKFilter.Visible = true;
			}

			if (!IsPostBack)
			{
				var sites = DomainModel.Cache.Instance.GetList<DomainModel.Site>();
				if (!sites.Any())
				{
					messageNoSites.Visible = true;
					panelContent.Visible = false;
					return;
				}

				listboxSites.DataSource = sites.OrderBy(s => s.Name);
				listboxSites.DataBind();

				string reportID = Request.QueryString["reportID"];
				if (!string.IsNullOrEmpty(reportID))
				{
					var r = DAL.Reports.Scheduled.ReportScheduledDAO.GetOne(Convert.ToInt32(reportID));
					var reportConfiguration = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.AdherenceConsolidatedExport>(r.Configuration);

					if (reportConfiguration.Sites != null && reportConfiguration.Sites.Count() > 0)
					{
						foreach (ListItem item in listboxSites.Items)
						{
							if (reportConfiguration.Sites.Contains(short.Parse(item.Value)))
							{
								item.Selected = true;
							}
						}
					}
					textboxMinutesToConsider.Text = reportConfiguration.Minutes.ToString();
				}
			}

			this.RegisterJsonVariable("canExportReport", this.LoggedUser.AllPermissions.HasPermission(Permissions.ExportedReports));
		}

		#region Private Methods

		private bool ValidateUser()
		{
			if (!this.LoggedUser.HasPermission(Permissions.HistReports))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			if (this.LoggedUser.Queues.Count == 0)
			{
				messageNoQueues.Visible = true;
				panelContent.Visible = false;
				return false;
			}

			if (!this.LoggedUser.SupervisedAgents.Any())
			{
				messageNoAgents.Visible = true;
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private void RetrieveData(out DateTime fromDate, out DateTime toDate, out short[] sites, out int[] agents, out short minutes)
		{
			fromDate = this.LoggedUser.ParseDate(textboxFromDate.Text);
			toDate = this.LoggedUser.ParseDate(textboxToDate.Text);
			toDate = toDate.AddDays(1).AddMilliseconds(-1);

			fromDate = this.LoggedUser.ConvertDateToServerDate(fromDate);
			toDate = this.LoggedUser.ConvertDateToServerDate(toDate);

			var selectedSites = new List<short>();
			foreach (ListItem item in listboxSites.Items)
			{
				if (item.Selected || listboxSites.SelectedIndex == -1)
					selectedSites.Add(short.Parse(item.Value));
			}

			sites = selectedSites.ToArray();
			agents = this.LoggedUser.SupervisedAgents.Where(a => a.Site != null && selectedSites.IndexOf(a.Site.ID) >= 0).Select(a => a.ID).Distinct().ToArray();

			minutes = short.Parse(textboxMinutesToConsider.Text);
		}

		protected override DomainModel.Reports.Export.ReportExport LoadFilters(bool isScheduled = false)
		{
			var export = (DomainModel.Reports.Export.AdherenceConsolidatedExport) DomainModel.Reports.Export.AdherenceConsolidatedExport.Create(DomainModel.Reports.ReportTypes.AdherenceConsolidated);
			
			var selectedSites = new List<short>();
			foreach (ListItem item in listboxSites.Items)
			{
				if (item.Selected || listboxSites.SelectedIndex == -1)
					selectedSites.Add(short.Parse(item.Value));
			}

			export.Sites = selectedSites.ToArray();
			export.Agents = this.LoggedUser.SupervisedAgents.Where(a => a.Site != null && selectedSites.IndexOf(a.Site.ID) >= 0).Select(a => a.ID).Distinct().ToArray();
			export.Minutes = short.Parse(textboxMinutesToConsider.Text);
			return export;
		}

		#endregion

		#region Button Events

		protected void buttonClearFilters_Click(object sender, EventArgs e)
		{
			Response.Redirect(RedirectUrl);
		}

		protected void buttonModifyFilters_Click(object sender, EventArgs e)
		{
			panelFilters.Visible = true;
			panelResults.Visible = false;
		}

		protected void buttonOKFilter_Click(object sender, EventArgs e)
		{
			if (Page.IsValid)
			{
				panelResults.Visible = true;
				panelFilters.Visible = false;

				DateTime fromDate;
				DateTime toDate;
				int[] agents;
				short minutes;
				short[] sites;
				RetrieveData(out fromDate, out toDate, out sites, out agents, out minutes);

				this.RegisterJsonVariable("fromDate", fromDate);
				this.RegisterJsonVariable("toDate", toDate);
				this.RegisterJsonVariable("agents", agents);
				this.RegisterJsonVariable("minutes", minutes);
				this.RegisterJsonVariable("sites", sites);

				var allSites = DomainModel.Cache.Instance.GetList<DomainModel.Site>();
				this.RegisterJsonVariable("allSites", allSites.Select(s => new { id = s.ID, name = s.Name }));

				int pageSize = DomainModel.SystemSettings.Instance.MaxResultsInGrids;
				if (this.LoggedUser.Settings.ContainsKey(DomainModel.User.PaginationSize))
					int.TryParse(this.LoggedUser.Settings[DomainModel.User.PaginationSize], out pageSize);
				this.RegisterJsonVariable("pageSize", pageSize);

				var type = typeof(DomainModel.Historical.Daily);
				var properties = type.GetProperties();
				var propertyNames = properties.Where(p => !Attribute.IsDefined(p, typeof(Newtonsoft.Json.JsonIgnoreAttribute))).Select(p => p.Name);
				this.RegisterJsonVariable("detailedPropertyNames", propertyNames);
			}
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object LoadMoreResultsDetailed(DateTime fromDate, DateTime toDate, short[] sites, int[] agents, short minutes)
		{
			if (toDate.Subtract(fromDate).TotalDays > 30)
				HttpContext.Current.Server.ScriptTimeout = 600;

			try
			{
				var supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				using (var records = DAL.Historical.DailyDAO.SearchByAgentsAdherence(fromDate, toDate, agents, minutes))
				{
					var consolidatedRecords = DomainModel.Site.ConsolidateAdherence(sites, records, new Common.Interval(fromDate, 2), new Common.Interval(toDate, 2), out int totalRecords);

					return new
					{
						Success = true,
						TotalRecords = totalRecords,
						ConsolidatedRecords = consolidatedRecords.Select(k => new object[] 
						{ 
							k.Key.IntervalDateTime, 
							k.Value.Select(i => new object[] { i.Key, i.Value })
						})
					};
				}
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object Export(DateTime fromDate, DateTime toDate, short[] sites, int[] agents, short minutes, int[] sections, string email, short exportFormat)
		{
			try
			{
				var supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				if (!supervisor.AllPermissions.HasPermission(Permissions.ExportedReports))
					throw new Exception("No tiene permisos para esta sección");

				if (sections.Contains(1))
				{
					var export = (DomainModel.Reports.Export.AdherenceConsolidatedExport) DomainModel.Reports.Export.AdherenceConsolidatedExport.Create(DomainModel.Reports.ReportTypes.AdherenceConsolidated, supervisor.ID, (DomainModel.Reports.Export.ExportFormats) exportFormat);
					export.From = fromDate;
					export.To = toDate;
					export.Agents = agents;
					export.Minutes = minutes;
					export.Sites = sites;
					export.Email = email;
					export.OwnerTimeZone = supervisor.GetTimeZoneToUse();
					export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
					if (supervisor.Settings != null && supervisor.Settings.ContainsKey(DomainModel.User.Locale))
						export.Language = supervisor.Settings[DomainModel.User.Locale];

					if (DAL.Reports.Export.ReportExportDAO.Exists(export))
					{
						return new
						{
							Success = false,
							AlreadyExists = true
						};
					}

					DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
					dao.Insert();
				}

				if (sections.Contains(2))
				{
					var export = (DomainModel.Reports.Export.AdherenceDetailedExport) DomainModel.Reports.Export.AdherenceDetailedExport.Create(DomainModel.Reports.ReportTypes.AdherenceDetailed, supervisor.ID, (DomainModel.Reports.Export.ExportFormats) exportFormat);
					export.From = fromDate;
					export.To = toDate;
					export.Agents = agents;
					export.Minutes = minutes;
					export.Sites = sites;
					export.Email = email;
					export.OwnerTimeZone = supervisor.GetTimeZoneToUse();
					export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
					if (supervisor.Settings != null && supervisor.Settings.ContainsKey(DomainModel.User.Locale))
						export.Language = supervisor.Settings[DomainModel.User.Locale];

					DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
					dao.Insert();
				}

				return new
				{
					Success = true
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		#endregion
	}
}