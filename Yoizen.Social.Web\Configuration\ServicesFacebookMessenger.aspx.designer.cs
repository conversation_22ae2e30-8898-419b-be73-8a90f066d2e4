﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


	public partial class ServicesFacebookMessenger
	{

		/// <summary>
		/// messageFacebookWizardLoadingError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookWizardLoadingError;

		/// <summary>
		/// messageFacebookNoPages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookNoPages;

		/// <summary>
		/// messageFacebookUrlInvalid control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookUrlInvalid;

		/// <summary>
		/// messageFacebookAccountError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookAccountError;

		/// <summary>
		/// messageFacebookCouldntValidateAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookCouldntValidateAccessToken;

		/// <summary>
		/// messageAlreadyExistsAnotherFacebookMessengerService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAlreadyExistsAnotherFacebookMessengerService;

		/// <summary>
		/// messageAnotherFacebookServiceExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAnotherFacebookServiceExists;

		/// <summary>
		/// liTabAdvancedConfigurationYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabAdvancedConfigurationYFlow;

		/// <summary>
		/// liTabVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabVideo;

		/// <summary>
		/// hiddenUserAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenUserAccessToken;

		/// <summary>
		/// hiddenServiceIDToDisable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenServiceIDToDisable;

		/// <summary>
		/// textboxServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxServiceName;

		/// <summary>
		/// customvalidatorServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorServiceName;

		/// <summary>
		/// textboxFacebookPageId control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookPageId;

		/// <summary>
		/// textboxFacebookPageName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookPageName;

		/// <summary>
		/// textboxFacebookPageAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookPageAccessToken;

		/// <summary>
		/// checkboxVerifyAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxVerifyAccessToken;

		/// <summary>
		/// textboxFacebookFromDate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookFromDate;

		/// <summary>
		/// dropdownlistFacebookQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistFacebookQueue;

		/// <summary>
		/// placeholderFacebookCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderFacebookCheckSpelling;

		/// <summary>
		/// checkboxFacebookCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookCheckSpelling;

		/// <summary>
		/// checkboxAutoReplyToChatPluingGetStarted control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyToChatPluingGetStarted;

		/// <summary>
		/// textboxAutoReplyToChatPluingGetStartedText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyToChatPluingGetStartedText;

		/// <summary>
		/// placeholderAllowYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYFlow;

		/// <summary>
		/// dropdownlistUseYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistUseYFlow;

		/// <summary>
		/// placeholderActAsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderActAsChat;

		/// <summary>
		/// checkboxActAsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxActAsChat;

		/// <summary>
		/// messageFacebookTokenExpiry control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookTokenExpiry;

		/// <summary>
		/// textboxWelcomeMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWelcomeMessage;

		/// <summary>
		/// messageWelcomeMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageWelcomeMessage;

		/// <summary>
		/// checkboxUseGetStartedButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxUseGetStartedButton;

		/// <summary>
		/// divAdvancedConfigurationYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAdvancedConfigurationYFlow;

		/// <summary>
		/// hiddenFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlow;

		/// <summary>
		/// hiddenSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveys;

		/// <summary>
		/// divYFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divYFlowContingency;

		/// <summary>
		/// hiddenFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowContingency;

		/// <summary>
		/// hiddenSurveysContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveysContingency;

		/// <summary>
		/// hiddenFlowQueueTransfersByKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowQueueTransfersByKey;

		/// <summary>
		/// listboxFlowShareEnqueuedMessagesFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareEnqueuedMessagesFromQueues;

		/// <summary>
		/// listboxFlowShareConnectedAgentsFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareConnectedAgentsFromQueues;

		/// <summary>
		/// textboxFlowMinutesAfterAgentClosedCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFlowMinutesAfterAgentClosedCase;

		/// <summary>
		/// placeholderAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// checkboxAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// panelYFlowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelYFlowSurveys;

		/// <summary>
		/// messageNoSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveys;

		/// <summary>
		/// panelEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEnableSurveys;

		/// <summary>
		/// checkboxEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

		/// <summary>
		/// messageNoSurveysInTable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveysInTable;

		/// <summary>
		/// messageSurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyDisabled;

		/// <summary>
		/// dropdownSurvey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownSurvey;

		/// <summary>
		/// textboxSurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitation;

		/// <summary>
		/// messageSurveyInvitationFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyInvitationFields;

		/// <summary>
		/// textboxSurveyInvitationInteractive control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationInteractive;

		/// <summary>
		/// messageFilterEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

		/// <summary>
		/// textboxSurveyInvitationButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationButton;

		/// <summary>
		/// message1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message1;

		/// <summary>
		/// textboxSurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyExpiration;

		/// <summary>
		/// textboxSurveySentRate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveySentRate;

		/// <summary>
		/// textboxSurveyTimeToSend control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTimeToSend;

		/// <summary>
		/// textboxSurveyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTags;

		/// <summary>
		/// listboxSurveyTagGroup control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroup;

		/// <summary>
		/// textboxSurveyMessagesCount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyMessagesCount;

		/// <summary>
		/// textboxSurveyCaseDuration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyCaseDuration;

		/// <summary>
		/// checkboxSurveySendIfNewCaseExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseExists;

		/// <summary>
		/// textboxSurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// textboxSurveyDontSendTotalSendMonthly control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendTotalSendMonthly;

		/// <summary>
		/// textboxSurveysIgnoreTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysIgnoreTags;

		/// <summary>
		/// listboxSurveyTagGroupToIgnore control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroupToIgnore;

		/// <summary>
		/// checkboxFacebookAllowToSendMedia control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookAllowToSendMedia;

		/// <summary>
		/// textboxFacebookMaxSizeAttachment control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookMaxSizeAttachment;

		/// <summary>
		/// checkboxFacebookAcceptedTypeImages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookAcceptedTypeImages;

		/// <summary>
		/// checkboxFacebookAcceptedTypeAudio control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookAcceptedTypeAudio;

		/// <summary>
		/// checkboxFacebookAcceptedTypeVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookAcceptedTypeVideo;

		/// <summary>
		/// checkboxFacebookAcceptedTypeAllFiles control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxFacebookAcceptedTypeAllFiles;

		/// <summary>
		/// hiddenConnectionOAuth control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenConnectionOAuth;

		/// <summary>
		/// textboxFacebookOAuthErrorEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookOAuthErrorEmailSubject;

		/// <summary>
		/// textboxFacebookOAuthErrorEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookOAuthErrorEmails;

		/// <summary>
		/// textboxFacebookOAuthErrorEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookOAuthErrorEmailTemplate;

		/// <summary>
		/// messageFacebookOAuthErrorEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookOAuthErrorEmailTemplateFields;

		/// <summary>
		/// hiddenConnectionInactivity control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenConnectionInactivity;

		/// <summary>
		/// textboxFacebookMinutesForInactivity control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookMinutesForInactivity;

		/// <summary>
		/// textboxFacebookInactivityDetectedEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookInactivityDetectedEmailSubject;

		/// <summary>
		/// textboxFacebookInactivityDetectedEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookInactivityDetectedEmails;

		/// <summary>
		/// textboxFacebookInactivityDetectedEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookInactivityDetectedEmailTemplate;

		/// <summary>
		/// messageFacebookInactivityDetectedEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookInactivityDetectedEmailTemplateFields;

		/// <summary>
		/// checkboxAutoReplyBeforeMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeMaxTimeToAnswer;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// messageAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerMinutes;

		/// <summary>
		/// checkboxDiscardAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxAutoReplyBeforeCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeCloseCase;

		/// <summary>
		/// textboxAutoReplyBeforeCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCloseCaseText;

		/// <summary>
		/// textboxAutoReplyBeforeCloseCaseMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCloseCaseMinutes;

		/// <summary>
		/// dropdownlistAllowToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowToSendHSM;

		/// <summary>
		/// dropdownlistAllowAgentsToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowAgentsToSendHSM;

		/// <summary>
		/// hiddenHSMTemplates control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenHSMTemplates;

		/// <summary>
		/// panelSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelSurveys;

		/// <summary>
		/// dropdownlistAllowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowSurveys;

		/// <summary>
		/// divCapiService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCapiService;

		/// <summary>
		/// checkboxEnableCapi control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableCapi;

		/// <summary>
		/// checkboxCasesOverrideSystemSettings control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCasesOverrideSystemSettings;

		/// <summary>
		/// checkboxCheckLastQueueOfOpenCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueOfOpenCase;

		/// <summary>
		/// checkboxIgnoreLastQueueForSLMovedMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIgnoreLastQueueForSLMovedMessage;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseCases;

		/// <summary>
		/// checkboxReplyInCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReplyInCloseCase;

		/// <summary>
		/// textboxAutoReplyInCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyInCloseCaseText;

		/// <summary>
		/// textboxTagCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTagCloseCase;

		/// <summary>
		/// placeholderYFlowCasesRelated control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowCasesRelated;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseYFlowCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseYFlowCases;

		/// <summary>
		/// checkboxInvokeYFlowWhenClosedCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInvokeYFlowWhenClosedCases;

		/// <summary>
		/// panelVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelVideo;

		/// <summary>
		/// checkboxEnableVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableVideo;

		/// <summary>
		/// hiddenServiceToCopy control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenServiceToCopy;

		/// <summary>
		/// buttonCopyService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCopyService;

		/// <summary>
		/// buttonSave control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonSave;

		/// <summary>
		/// buttonCancel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCancel;
	}
}
