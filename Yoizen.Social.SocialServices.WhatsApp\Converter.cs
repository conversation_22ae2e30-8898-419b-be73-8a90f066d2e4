using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Newtonsoft.Json.Linq;
using Yoizen.Social.WhatsApp;
using System.Net;
using System.Threading.Tasks;
using System.Net.Http;
using Yoizen.Social.DomainModel;
using Yoizen.Common;
using Yoizen.Social.DomainModel.Whatsapp;
using Newtonsoft.Json;
using System.Collections.Concurrent;


namespace Yoizen.Social.SocialServices.WhatsApp
{
	public static class Converter
	{
		#region Inner Classes

		public class ConvertResult
		{
			#region Properties

			public IEnumerable<DomainModel.Message> Messages { get; private set; }

			public IEnumerable<DomainModel.Message> StatusMessages { get; private set; }

			#endregion

			#region Constructors

			private ConvertResult()
			{
				this.Messages = null;
				this.StatusMessages = null;
			}

			internal ConvertResult(IEnumerable<DomainModel.Message> messages, IEnumerable<DomainModel.Message> statusMessages)
				: this()
			{
				if (messages != null && messages.Any())
					this.Messages = messages;
				if (statusMessages != null && statusMessages.Any())
					this.StatusMessages = statusMessages;
			}

			#endregion
		}

		#endregion

		#region Fields

		private static readonly DateTime UnixEpoch;
		private static System.Globalization.CultureInfo culture;
		private static System.Net.Http.HttpClient httpClient;

		private static DateTime lastBotNumbersDownloadedTime = DateTime.MinValue;
		private static bool isDownloadingBotNumbers = false;
		private static long[] botPhoneNumbers = null;
		private static Social.Intervals.IntervalsStorageManager intervalStorageManager;
		private static ConcurrentDictionary<string, Message> LockedMessages = new ConcurrentDictionary<string, Message>();

		#endregion

		#region Constructors

		static Converter()
		{
			UnixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, 0);
			culture = System.Globalization.CultureInfo.InvariantCulture;

			var httpClientHandler = new System.Net.Http.HttpClientHandler();
			httpClientHandler.ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true;
			httpClient = new System.Net.Http.HttpClient(httpClientHandler);
			httpClient.Timeout = TimeSpan.FromSeconds(15);

#if NETCOREAPP
			intervalStorageManager = new Social.Intervals.IntervalsStorageManagerDb();
#else
			if (Licensing.LicenseManager.Instance.License.Configuration.UseDatabaseForIntervals)
				intervalStorageManager = new Social.Intervals.IntervalsStorageManagerDb();
			else
				intervalStorageManager = new Social.Intervals.IntervalsStorageManagerFileSystem();
#endif

			intervalStorageManager.Initialize();
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Message"/> a partir de los mensajes de un chat de WhatsApp
		/// </summary>
		/// <param name="jsonItem">Un <see cref="Newtonsoft.Json.Linq.JToken"/> con los datos de mensaje/s a convertir</param>
		/// <param name="service">Un <see cref="DomainModel.Service"/> que se utilizará para convertir el/los mensaje/s</param>
		/// <returns>Una enumeración de <see cref="DomainModel.Message"/> con los mensajes convertidos</returns>
		public static async Task<ConvertResult> Convert(Newtonsoft.Json.Linq.JToken jsonItem, DomainModel.Service service, WhatsAppService whatsAppService)
		{
			if (jsonItem == null)
				throw new ArgumentNullException(nameof(jsonItem));
			if (service == null)
				throw new ArgumentNullException(nameof(service));

			WhatsAppServiceConfiguration configuration;
			if (service.ServiceConfiguration != null)
			{
				configuration = (WhatsAppServiceConfiguration) service.ServiceConfiguration;
			}
			else
			{
				configuration = new WhatsAppServiceConfiguration(service.Configuration);
				service.ServiceConfiguration = configuration;
			}

			var messages = new List<DomainModel.Message>();
			var statusMessages = new List<DomainModel.Message>();

			if (jsonItem.Type == JTokenType.Array)
			{
				foreach (JObject jObject in (JArray) jsonItem)
				{
					var result = await Convert(jObject, service, whatsAppService);
					if (result.Item1 != null)
					{
						result.Item1.Service = service;
						result.Item1.ServiceType = service.Type;
						messages.Add(result.Item1);
					}
					else if (result.Item2 != null)
					{
						statusMessages.Add(result.Item2);
					}
				}
			}
			else if (jsonItem.Type == JTokenType.Object)
			{
				var result = await Convert((JObject) jsonItem, service, whatsAppService);
				if (result.Item1 != null)
				{
					result.Item1.Service = service;
					result.Item1.ServiceType = service.Type;
					messages.Add(result.Item1);
				}
				else if (result.Item2 != null)
				{
					statusMessages.Add(result.Item2);
				}
			}

			if (messages.Count > 0 &&
				Licensing.LicenseManager.Instance.License.Configuration.AllowToMarkAsReadWhatsappMessages &&
				whatsAppService.ServiceConfiguration.MarkAsReadBehaviour == SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.MarkOnReceived)
			{
				foreach (var wpMessage in messages)
				{
					await whatsAppService.MarkMessageAsRead(wpMessage);
				}
			}

			return new ConvertResult(messages, statusMessages);
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Message"/> a partir de los mensajes de un chat de WhatsApp
		/// </summary>
		/// <param name="obj">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con los datos del mensaje</param>
		/// <param name="service">Un <see cref="DomainModel.Service"/> que se utilizará para convertir el mensaje</param>
		/// <returns>Un <see cref="Social.WhatsApp.WhatsAppMessage"/> con el mensaje de Whatsapp convertido</returns>
		public static async Task<(DomainModel.Message, DomainModel.Message)> Convert(Newtonsoft.Json.Linq.JObject obj, DomainModel.Service service, WhatsAppService whatsAppService)
		{
			Social.WhatsApp.WhatsAppMessage message = null;
			DomainModel.Message statusMessage = null;

			WhatsAppServiceConfiguration configuration;
			var whatsappSettings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			if (service.ServiceConfiguration != null)
			{
				configuration = (WhatsAppServiceConfiguration) service.ServiceConfiguration;
			}
			else
			{
				configuration = new WhatsAppServiceConfiguration(service.Configuration);
				service.ServiceConfiguration = configuration;
			}

			var integrationType = configuration.ServiceIntegrationType;
			if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback &&
				obj["provider"] != null &&
				obj["provider"].Type == JTokenType.String &&
				obj["provider"].ToString().Equals("yoizen") &&
				configuration.IntegrationType3UseWhatsappFormat)
			{
				integrationType = DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen;
			}
#if DEBUG
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy &&
				obj["provider"] != null &&
				obj["provider"].Type == JTokenType.String &&
				obj["provider"].ToString().Equals("yoizen"))
			{
				integrationType = DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen;
			}
#endif

			switch (integrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					{
						if (obj["statuses"] != null && obj["statuses"].Type == JTokenType.Array)
						{
							var jStatuses = (JArray) obj["statuses"];
							foreach (JObject jStatus in jStatuses)
							{
								DomainModel.Message sentMessage = null;

								if (jStatus["custom_payload"] != null &&
									jStatus["custom_payload"].Type == JTokenType.String)
								{
									if (long.TryParse(jStatus["custom_payload"].ToString(), out long id))
									{
										sentMessage = DAL.MessageDAO.GetOne(id, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
									}
								}
								else if (jStatus["biz_opaque_callback_data"] != null &&
									jStatus["biz_opaque_callback_data"].Type == JTokenType.String)
								{
									if (long.TryParse(jStatus["biz_opaque_callback_data"].ToString(), out long id))
									{
										sentMessage = DAL.MessageDAO.GetOne(id, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
									}
								}
								else
								{
									string messageId = jStatus["id"].ToString();
									if (jStatus["message_id"] != null &&
										jStatus["message_id"].Type == JTokenType.String)
									{
										messageId = jStatus["message_id"].ToString();
									}

									sentMessage = DAL.MessageDAO.GetOne(messageId, DomainModel.SocialServiceTypes.WhatsApp);
								}

								if (sentMessage != null)
								{
									Message lockedMessage = lockedMessage = LockedMessages.GetOrAdd(sentMessage.ID.ToString(), sentMessage);

                                    lock (lockedMessage)
									{
                                        statusMessage = lockedMessage;

                                        try
                                        {
                                            var jNews = new Newtonsoft.Json.Linq.JObject();
                                            jNews["ID"] = lockedMessage.ID;

                                            if (jStatus["message_id"] != null &&
                                                jStatus["message_id"].Type == JTokenType.String)
                                                lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.WaIdParameter] = jStatus["id"].ToString();

                                            Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", sentMessage.SocialMessageID, sentMessage.ID);

                                            var status = jStatus["status"].ToString();
                                            var timestamp = jStatus["timestamp"].ToObject<long>();
                                            var date = Common.Conversions.UnixTimeToDateTime(timestamp);
                                            var deliveryError = "N/A";
                                            int? deliveryErrorNumber = null;

                                            var mustNotifyTask = false;
                                            var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
                                            DomainModel.Historical.DailyTask infoTask;
                                            infoTask = DomainModel.Historical.DailyTask.CreateForInterval(interval);
                                            infoTask.MessageID = lockedMessage.ID;

                                            if (lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
                                                lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignIdParameter))
                                            {
                                                var totalMinutes = Math.Floor(DateTime.Now.Subtract(lockedMessage.Date).TotalMinutes);

                                                if (totalMinutes < lockedMessage.Service.CasesSettings.MaxElapsedMinutesToCloseHsmCases)
                                                {
                                                    mustNotifyTask = true;
                                                    infoTask.TaskID = int.Parse(lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter]);
                                                }
                                            }

                                            Common.Tracer.TraceVerb("Se informa un evento {0} referente al mensaje {1}", status, lockedMessage.ID);

                                            switch (status)
                                            {
												case "sent":                                                    

                                                    //Algunas veces meta entrega DOS veces el mismo estado, si ya existe no incrementamos la tarea.
                                                    if (mustNotifyTask && !lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
                                                    {
                                                        infoTask.Sent = 1;
                                                        infoTask.AcceptedByBroker = 1;
                                                    }

                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
                                                    jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;

                                                    break;
                                                case "delivered":

                                                    if (mustNotifyTask && !lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
                                                    {
                                                        infoTask.Delivered = 1;
                                                    }

                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
                                                    jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;

                                                    if (!lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
                                                    {
														if (mustNotifyTask)
														{
															infoTask.Sent = 1;
															infoTask.AcceptedByBroker = 1;
														}
                                                        
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
                                                    }
                                                    break;
                                                case "read":

                                                    if (mustNotifyTask && !lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.ReadParameter))
                                                    {
                                                        infoTask.Read = 1;
                                                    }

                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date.ToString("o");
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp.ToString();
                                                    jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date;
                                                    jNews[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp;

                                                    if (!lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
                                                    {
														if (mustNotifyTask)
														{
															infoTask.Sent = 1;
															infoTask.AcceptedByBroker = 1;
														}

                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
                                                    }

                                                    if (!lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
                                                    {
														if (mustNotifyTask)
														{
															infoTask.Delivered = 1;
														}
                                                        
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
                                                        lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
                                                        jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
                                                        jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;
                                                    }
                                                    break;
                                                case "failed":
                                                    lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = false.ToString();
                                                    jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = false;

                                                    if (mustNotifyTask)
                                                    {
                                                        infoTask.RejectedByBroker = 1;
                                                    }

                                                    if (jStatus["errors"] != null &&
                                                        jStatus["errors"].Type == JTokenType.Array)
                                                    {
                                                        var jErrors = (JArray)jStatus["errors"];
                                                        if (jErrors.Count > 0 &&
                                                            jErrors[0].Type == JTokenType.Object)
                                                        {
                                                            var jError = (JObject)jErrors[0];

                                                            if (jError["title"] != null &&
                                                                jError["title"].Type == JTokenType.String)
                                                            {
                                                                deliveryError = jError["title"].ToString();
                                                            }

                                                            if (jError["code"] != null &&
                                                                jError["code"].Type == JTokenType.Integer)
                                                            {
                                                                deliveryErrorNumber = jError["code"].ToObject<int>();
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (jStatus["description"] != null && jStatus["description"].Type == JTokenType.String)
                                                        {
                                                            deliveryError = jStatus["description"].ToString();

                                                            if (jStatus["title"] != null && jStatus["title"].Type == JTokenType.String)
                                                                deliveryError += Environment.NewLine + jStatus["title"].ToString();

                                                            if (jStatus["details"] != null && jStatus["details"].Type == JTokenType.String)
                                                                deliveryError += Environment.NewLine + jStatus["details"].ToString();

                                                            if (jStatus["code"] != null && jStatus["code"].Type == JTokenType.Integer)
                                                                deliveryError += Environment.NewLine + $"Code: {jStatus["code"].ToObject<int>()}";

                                                            if (jStatus["subcode"] != null && jStatus["subcode"].Type == JTokenType.Integer)
                                                                deliveryError += Environment.NewLine + $"Subcode: {jStatus["subcode"].ToObject<int>()}";
                                                        }

                                                        if (jStatus["code"] != null && jStatus["code"].Type == JTokenType.Integer)
                                                            deliveryErrorNumber = jStatus["code"].ToObject<int>();
                                                    }

                                                    break;
                                                default:
                                                    continue;
                                            }

                                            var updateConversation = false;
                                            if (jStatus["conversation"] != null &&
                                                jStatus["conversation"].Type == JTokenType.Object &&
                                                jStatus["conversation"]["id"] != null &&
                                                jStatus["conversation"]["id"].Type == JTokenType.String)
                                            {
                                                var conversationId = jStatus["conversation"]["id"].ToString();
                                                if (string.IsNullOrEmpty(sentMessage.SocialConversationID) ||
                                                    !lockedMessage.SocialConversationID.Equals(conversationId))
                                                {
                                                    lockedMessage.SocialConversationID = conversationId;
                                                    updateConversation = true;
                                                }
                                            }

                                            if (lockedMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter) &&
                                                !bool.Parse(lockedMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter]))
                                            {
                                                var deliveryShouldRetry = false;

                                                if (deliveryErrorNumber == 131000)
                                                {
                                                    if (sentMessage.DeliveryRetries < 10)
                                                    {
                                                        deliveryShouldRetry = true;
                                                        Common.Tracer.TraceVerb("Se marca el mensaje de Whatsapp con código {0} perteneciente al mensaje {1} para reintentar el envío", sentMessage.SocialMessageID, sentMessage.ID);
                                                    }
                                                    else
                                                    {
                                                        Common.Tracer.TraceVerb("No se marca el mensaje de Whatsapp con código {0} perteneciente al mensaje {1} para reintentar el envío porque ya llegó a los 10 reintentos", sentMessage.SocialMessageID, sentMessage.ID);
                                                    }
                                                }

                                                DAL.MessageDAO.UpdateNotDelivered(lockedMessage.ID, deliveryError, deliveryErrorNumber, lockedMessage.Parameters, deliveryShouldRetry);

                                                jNews["DeliveryError"] = deliveryError;
                                                jNews["DeliveryErrorNumber"] = deliveryErrorNumber;
                                                jNews["Delivered"] = false;
                                                jNews["DeliveryShouldRetry"] = deliveryShouldRetry;

                                                if (lockedMessage.DeliveryRetries == null)
                                                    lockedMessage.DeliveryRetries = 1;
                                                else
                                                    lockedMessage.DeliveryRetries++;

                                                Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1} y la información de error de entrega", sentMessage.SocialMessageID, sentMessage.ID);
                                            }
                                            else
                                            {
                                                if (updateConversation)
                                                {
                                                    DAL.MessageDAO.UpdateSocialConversation(lockedMessage);
                                                    Common.Tracer.TraceVerb("Se actualizó el código de conversación y los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", sentMessage.SocialMessageID, sentMessage.ID);
                                                }
                                                else
                                                {
                                                    DAL.MessageDAO.UpdateParameters(lockedMessage);
                                                    Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", sentMessage.SocialMessageID, sentMessage.ID);
                                                }
                                            }

                                            if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
                                                DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
                                            {
                                                DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(lockedMessage, jNews);
                                            }

                                            if (mustNotifyTask)
                                            {
                                                intervalStorageManager.StoreInfo(infoTask, interval);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", sentMessage.SocialMessageID, sentMessage.ID, ex);
                                        }
										finally
										{
                                            Common.Tracer.TraceVerb("Se termina de procesar el mensaje {0}", lockedMessage.ID);

                                            LockedMessages.TryRemove(lockedMessage.ID.ToString(), out _);
										}
                                    }
								}
							}
						}
						else if (obj["messages"] != null && obj["messages"].Type == JTokenType.Array)
						{
							var jMessages = (JArray) obj["messages"];
							JArray jContacts = null;

							if (obj["contacts"] != null && obj["contacts"].Type == JTokenType.Array)
								jContacts = (JArray) obj["contacts"];

							foreach (JObject jMessage in jMessages)
							{
								var type = "text";
								if (jMessage["type"] != null && jMessage["type"].Type == JTokenType.String)
									type = jMessage["type"].ToString();

								Dictionary<string, string> headers = null;

								if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi)
								{
									if (type.Equals("unknown") || type.Equals("system"))
									{
										Common.Tracer.TraceInfo("El tipo {0} no está soportado para Whatsapp", type);
										return (null, null);
									}

									if (type.Equals("image") ||
										type.Equals("video") ||
										type.Equals("document") ||
										type.Equals("voice") ||
										type.Equals("audio") ||
										type.Equals("sticker"))
									{
										var jMedia = (Newtonsoft.Json.Linq.JObject) jMessage[type];
										var mediaId = jMedia["id"].ToString();

										var url = $"https://graph.facebook.com/{configuration.IntegrationType11GraphApiVersion}/{mediaId}";

										using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
										{
											var productValue = new System.Net.Http.Headers.ProductInfoHeaderValue("Yoizen", "1.0");
											request.Headers.UserAgent.Add(productValue);
											request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", configuration.IntegrationType11AccessToken);

											using (var response = await httpClient.SendAsync(request))
											{
												if (response.IsSuccessStatusCode)
												{
													var json = await response.Content.ReadAsStringAsync();
													var jDownloadedMedia = Newtonsoft.Json.Linq.JObject.Parse(json);
													var downloadUrl = jDownloadedMedia["url"].ToString();

													jMedia["url"] = downloadUrl;

													headers = new Dictionary<string, string>();
													headers["Authorization"] = $"Bearer {configuration.IntegrationType11AccessToken}";
													headers["User-Agent"] = $"Yoizen 1.0";
												}
												else
												{
													var errorContents = string.Empty;
													if (response.Content != null)
													{
														errorContents = await response.Content.ReadAsStringAsync();
														Common.Tracer.TraceError("Ocurrió un error leyendo el archivo multimedia {0} de la línea {1} - StatusCode: {2} - Resultado: {3}", mediaId, configuration.FullPhoneNumber, response.StatusCode, errorContents);
													}
													else
													{
														Common.Tracer.TraceError("Ocurrió un error leyendo el archivo multimedia {0} de la línea {1} - StatusCode: {2} - Razón: {3}", mediaId, configuration.FullPhoneNumber, response.StatusCode, response.ReasonPhrase);
													}

													return (null, null);
												}
											}
										}
									}
									else if (type.Equals("interactive"))
									{
										var jInteractive = (Newtonsoft.Json.Linq.JObject) jMessage["interactive"];
										if (jInteractive["type"] != null &&
											jInteractive["type"].Type == JTokenType.String)
										{
											var interactiveType = jInteractive["type"].ToString();
											if (interactiveType.Equals("nfm_reply") &&
												jInteractive["nfm_reply"] != null &&
												jInteractive["nfm_reply"].Type == JTokenType.Object)
											{
												var jNfmReply = (Newtonsoft.Json.Linq.JObject) jInteractive["nfm_reply"];
												if (jNfmReply["response_json"] != null &&
													jNfmReply["response_json"].Type == JTokenType.String)
												{
													var responseJson = jNfmReply["response_json"].ToString();
													var jResponseJson = Newtonsoft.Json.Linq.JObject.Parse(responseJson);

													var defaultFlowsMediaPropertyNames = new string[] { "images", "photo_picker", "documents" };

													var mediaHasChanged = false;
													foreach (var flowMediaPropertyName in defaultFlowsMediaPropertyNames)
													{
														if (jResponseJson[flowMediaPropertyName] != null &&
															jResponseJson[flowMediaPropertyName].Type == JTokenType.Array)
														{
															var jMedias = (Newtonsoft.Json.Linq.JArray) jResponseJson[flowMediaPropertyName];
															var mediaIndex = 0;

															foreach (Newtonsoft.Json.Linq.JObject jMedia in jMedias)
															{
																var mediaId = jMedia["id"].ToString();

																var url = $"https://graph.facebook.com/{configuration.IntegrationType11GraphApiVersion}/{mediaId}";

																using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
																{
																	var productValue = new System.Net.Http.Headers.ProductInfoHeaderValue("Yoizen", "1.0");
																	request.Headers.UserAgent.Add(productValue);
																	request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", configuration.IntegrationType11AccessToken);

																	using (var response = await httpClient.SendAsync(request))
																	{
																		if (response.IsSuccessStatusCode)
																		{
																			var json = await response.Content.ReadAsStringAsync();
																			var jDownloadedMedia = Newtonsoft.Json.Linq.JObject.Parse(json);
																			var downloadUrl = jDownloadedMedia["url"].ToString();

																			jMedia["url"] = downloadUrl;

																			Tracer.TraceVerb("Se descargó el archivo multimedia {0} del índice {1} de la propiedad {2} del flow de la línea {3} y se obtuvo la url {4}", mediaId, mediaIndex, flowMediaPropertyName, configuration.FullPhoneNumber, downloadUrl);

																			if (headers == null)
																				headers = new Dictionary<string, string>();
																			headers["Authorization"] = $"Bearer {configuration.IntegrationType11AccessToken}";
																			headers["User-Agent"] = $"Yoizen 1.0";
																		}
																		else
																		{
																			var errorContents = string.Empty;
																			if (response.Content != null)
																			{
																				errorContents = await response.Content.ReadAsStringAsync();
																				Tracer.TraceError("Ocurrió un error descargando el archivo multimedia {0} del índice {1} de la propiedad {2} del flow de la línea {3} - StatusCode: {4} - Resultado: {5}", mediaId, mediaIndex, flowMediaPropertyName, configuration.FullPhoneNumber, response.StatusCode, errorContents);
																			}
																			else
																			{
																				Tracer.TraceError("Ocurrió un error descargando el archivo multimedia {0} del índice {1} de la propiedad {2} del flow de la línea {3} - StatusCode: {4} - Razón: {5}", mediaId, mediaIndex, flowMediaPropertyName, configuration.FullPhoneNumber, response.StatusCode, response.ReasonPhrase);
																			}

																			//return (null, null);
																		}
																	}
																}

																mediaIndex++;
															}

															mediaHasChanged = true;
														}
													}

													if (mediaHasChanged)
													{
														jNfmReply["response_json"] = jResponseJson.ToString();
													}
												}
											}
										}
									}
								}

								var urlPropertyForMedia = "url";
								if (configuration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback)
								{
									urlPropertyForMedia = "link";
									if (!string.IsNullOrEmpty(configuration.IntegrationType3MediaUrlPropertyName))
										urlPropertyForMedia = configuration.IntegrationType3MediaUrlPropertyName;
								}

								switch (type)
								{
									case "voice":
									case "audio":
										{
											var voiceMessage = new Social.WhatsApp.WhatsAppAudioMessage();
											message = voiceMessage;

											var jAudio = (Newtonsoft.Json.Linq.JObject) jMessage[type];

											await DownloadAttachment(voiceMessage, jAudio[urlPropertyForMedia].ToString(), headers);

											if (voiceMessage.Attachment != null &&
												voiceMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jAudio["mime_type"] != null &&
												jAudio["mime_type"].Type == JTokenType.String)
												voiceMessage.Attachment.MimeType = jAudio["mime_type"].ToString();
										}
										break;
									case "contacts":
										{
											message = new Social.WhatsApp.WhatsAppVCardMessage();

											var jMessageContacts = (JArray) jMessage["contacts"];

											message.HasAttach = true;
											message.Attachments = ConvertContactsToVCard(jMessageContacts);
										}
										break;
									case "document":
										{
											var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();
											message = documentMessage;

											var jDocument = (JObject) jMessage["document"];
											await DownloadAttachment(documentMessage, jDocument[urlPropertyForMedia].ToString(), headers);

											if (documentMessage.Attachments != null &&
												documentMessage.Attachments.Length == 1)
											{
												if (jDocument["filename"] != null &&
													jDocument["filename"].Type == JTokenType.String &&
													jDocument["filename"].ToString().Length > 0)
												{
													documentMessage.Attachment.FileName = jDocument["filename"].ToString();
													CheckAttachmentFileName(documentMessage.Attachment, null);
												}
												else if (jDocument["caption"] != null &&
													jDocument["caption"].Type == JTokenType.String &&
													jDocument["caption"].ToString().Length > 0)
												{
													documentMessage.Attachment.FileName = jDocument["caption"].ToString();
													CheckAttachmentFileName(documentMessage.Attachment, null);
												}
											}
										}
										break;
									case "image":
										{
											var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
											message = imageMessage;

											var jImage = (JObject) jMessage["image"];
											if (jImage["caption"] != null &&
												jImage["caption"].Type == JTokenType.String &&
												jImage["caption"].ToString().Length > 0)
											{
												imageMessage.Body = jImage["caption"].ToString();
												imageMessage.EmptyBody = false;
											}

											await DownloadAttachment(imageMessage, jImage[urlPropertyForMedia].ToString(), headers);

											if (imageMessage.Attachment != null &&
												imageMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jImage["mime_type"] != null &&
												jImage["mime_type"].Type == JTokenType.String)
												imageMessage.Attachment.MimeType = jImage["mime_type"].ToString();
										}
										break;
									case "sticker":
										{
											var stickerMessage = new Social.WhatsApp.WhatsAppStickerMessage();
											message = stickerMessage;

											var jSticker = (JObject) jMessage["sticker"];
											await DownloadAttachment(stickerMessage, jSticker[urlPropertyForMedia].ToString(), headers);

											if (stickerMessage.Attachment != null &&
												stickerMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jSticker["mime_type"] != null &&
												jSticker["mime_type"].Type == JTokenType.String)
												stickerMessage.Attachment.MimeType = jSticker["mime_type"].ToString();

											if (jSticker["metadata"] != null &&
												jSticker["metadata"].Type == JTokenType.Object)
											{
												var jStickerMetadata = (JObject) jSticker["metadata"];
												stickerMessage.Parameters["sticker-pack-id"] = jStickerMetadata["sticker-pack-id"]?.ToString() ?? string.Empty;
												stickerMessage.Parameters["sticker-pack-name"] = jStickerMetadata["sticker-pack-name"]?.ToString() ?? string.Empty;
												stickerMessage.Parameters["sticker-pack-publisher"] = jStickerMetadata["sticker-pack-publisher"]?.ToString() ?? string.Empty;
											}
										}
										break;
									case "location":
										{
											message = new Social.WhatsApp.WhatsAppLocationMessage();
											message.HasCoordinates = true;
											var jLocation = (JObject) jMessage["location"];
											message.Coordinates = new DomainModel.GeoCoordinate(jLocation["latitude"].ToObject<double>(), jLocation["longitude"].ToObject<double>());

											var sbLocation = new StringBuilder();
											if (jLocation["name"] != null &&
												jLocation["name"].Type == JTokenType.String)
											{
												var name = jLocation["name"].ToString();
												if (!string.IsNullOrEmpty(name))
													sbLocation.Append(name);
											}
											if (jLocation["address"] != null &&
												jLocation["address"].Type == JTokenType.String)
											{
												var address = jLocation["address"].ToString();
												if (!string.IsNullOrEmpty(address))
												{
													if (sbLocation.Length > 0)
														sbLocation.Append(Environment.NewLine);
													sbLocation.Append(address);
												}
											}

											if (sbLocation.Length > 0)
												sbLocation.Append(Environment.NewLine);
											sbLocation.AppendFormat("{0},{1}",
												message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
												message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

											message.Body = sbLocation.ToString();
										}
										break;
									case "video":
										{
											var videoMessage = new Social.WhatsApp.WhatsAppVideoMessage();
											message = videoMessage;

											var jVideo = (JObject) jMessage["video"];
											await DownloadAttachment(message, jVideo[urlPropertyForMedia].ToString(), headers);

											if (videoMessage.Attachment != null &&
												videoMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jVideo["mime_type"] != null &&
												jVideo["mime_type"].Type == JTokenType.String)
												videoMessage.Attachment.MimeType = jVideo["mime_type"].ToString();
										}
										break;
									case "unknown":
										Common.Tracer.TraceVerb("Se recibió un tipo de mensaje unknown: {0}", jMessage.ToString());
										break;
									case "interactive":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											var jInteractive = (JObject) jMessage["interactive"];
											var interactiveType = jInteractive["type"].ToString();
											JObject jInteractiveReply;

											switch (interactiveType)
											{
												case "button_reply":
													jInteractiveReply = (JObject) jInteractive["button_reply"];
													interactiveMessage.Payload = jInteractiveReply["id"].ToString();
													interactiveMessage.Body = jInteractiveReply["title"].ToString();
													interactiveMessage.EmptyBody = false;
													break;

												case "list_reply":
													jInteractiveReply = (JObject) jInteractive["list_reply"];
													interactiveMessage.Payload = jInteractiveReply["id"].ToString();
													interactiveMessage.Body = jInteractiveReply["title"].ToString();
													if (jInteractiveReply["description"] != null &&
														jInteractiveReply["description"].Type == JTokenType.String &&
														jInteractiveReply["description"].ToString().Length > 0)
													{
														interactiveMessage.Body += Environment.NewLine + jInteractiveReply["description"].ToString();
													}
													interactiveMessage.EmptyBody = false;
													break;

												case "nfm_reply":
													jInteractiveReply = (JObject) jInteractive["nfm_reply"];

													if (jInteractiveReply["response_json"] != null && 
														jInteractiveReply["response_json"].Type == JTokenType.String)
													{
														var jResponse = JObject.Parse(jInteractiveReply["response_json"].ToString());

														var mediaPropertyNames = new string[] { "images", "photo_picker", "documents" };
														List<DomainModel.Attachment> attachments = null;
														foreach (var mediaPropertyName in mediaPropertyNames)
														{
															if (jResponse[mediaPropertyName] != null &&
																jResponse[mediaPropertyName].Type == JTokenType.Array)
															{
																var jMedia = (JArray) jResponse[mediaPropertyName];
																byte index = 1;
																foreach (var jMediaItem in jMedia)
																{
																	if (jMediaItem["url"] != null &&
																		jMediaItem["url"].Type == JTokenType.String)
																	{
																		var mediaUrl = jMediaItem["url"].ToString();
																		try
																		{
																			var attachment = await DownloadAttachment(mediaUrl, headers);
																			attachment.Index = index;
																			
																			if (attachment != null)
																			{
																				if (attachments == null)
																					attachments = new List<DomainModel.Attachment>();

																				attachments.Add(attachment);

																				Tracer.TraceVerb("Se descargó el multimedia {0} de la propiedad {1} desde la url {2} del mensaje interactivo de flows", index, mediaPropertyName, mediaUrl);
																			}
																			else
																			{
																				Tracer.TraceError("Falló descargar el multimedia {0} de la propiedad {1} desde la url {2} del mensaje interactivo de flows", index, mediaPropertyName, mediaUrl);
																			}
																		}
																		catch (Exception ex)
																		{
																			Tracer.TraceError("Falló descargar el multimedia {0} de la propiedad {1} desde la url {2}: {3} del mensaje interactivo de flows", index, mediaPropertyName, mediaUrl, ex);
																		}

																		index++;
																	}
																}
															}
														}

														if (attachments != null && attachments.Count > 0)
														{
															interactiveMessage.HasAttach = true;
															interactiveMessage.Attachments = attachments.ToArray();
														}

														if (jResponse["flow_token"] != null &&
															jResponse["flow_token"].Type == JTokenType.String)
														{
															string jFlowToken = jResponse["flow_token"].ToString();
															if (!string.IsNullOrEmpty(jFlowToken))
															{
																interactiveMessage.ConvertWhatsappFlowInvokeParams(jFlowToken);
															}

															var flowTokenProperty = jResponse.Property("flow_token");
															if (flowTokenProperty != null)
															{
																flowTokenProperty.Remove();
															}
														}

														interactiveMessage.Payload = jResponse.ToString();
													}

													interactiveMessage.Body = jInteractiveReply["body"].ToString();
													interactiveMessage.EmptyBody = false;
													break;
											}
											
											message = interactiveMessage;
										}
										break;
									case "button":
										{
											var payloadMessage = new Social.WhatsApp.WhatsAppPayloadMessage();
											var jButton = (JObject) jMessage["button"];

											var buttonPayload = GetButtonMessagePayload(jButton["payload"].ToString());

											if (!string.IsNullOrEmpty(buttonPayload.CampaignId))
												payloadMessage.PayloadCampaignId = buttonPayload.CampaignId;
											if (!string.IsNullOrEmpty(buttonPayload.Timestamp))
												payloadMessage.PayloadTimeStamp = buttonPayload.Timestamp;
											payloadMessage.Payload = buttonPayload.Body;											
											payloadMessage.Body = jButton["text"].ToString();
											payloadMessage.EmptyBody = false;

											message = payloadMessage;
										}
										break;
									case "order":
										{
											var orderMessage = new Social.WhatsApp.WhatsAppOrderMessage();
											var jOrder = (JObject) jMessage["order"];
											orderMessage.CatalogId = jOrder["catalog_id"].ToString();
											if (jOrder["text"] != null && jOrder["text"].Type == JTokenType.String)
											{
												orderMessage.Body = jOrder["text"].ToString();
												orderMessage.EmptyBody = false;
											}
											else
											{
												orderMessage.EmptyBody = true;
											}

											var jProductItems = (JArray) jOrder["product_items"];
											orderMessage.ProductItems = jProductItems.ToString();
											orderMessage.Count = jProductItems.Count;

											message = orderMessage;
										}
										break;
									case "text":
										{
											if (jMessage["context"] != null &&
												jMessage["context"].Type == JTokenType.Object)
											{
												var jContext = (JObject) jMessage["context"];
												if (jContext["referred_product"] != null &&
													jContext["referred_product"].Type == JTokenType.Object)
												{
													var referredProductMessage = new Social.WhatsApp.WhatsAppReferredProductMessage();

													var jReferrerProduct = (JObject) jContext["referred_product"];
													referredProductMessage.CatalogId = jReferrerProduct["catalog_id"].ToString();
													referredProductMessage.ProductRetailerId = jReferrerProduct["product_retailer_id"].ToString();

													message = referredProductMessage;
												}
											}

											if (message == null)
											{
												message = new Social.WhatsApp.WhatsAppTextMessage();
											}

											if (jMessage["text"] != null &&
												jMessage["text"].Type == JTokenType.Object &&
												jMessage["text"]["body"] != null &&
												jMessage["text"]["body"].Type == JTokenType.String)
											{
												message.Body = jMessage["text"]["body"].ToString();
												message.EmptyBody = false;
											}
										}
										break;

									case "reaction":
										{
											var jReaction = (JObject) jMessage["reaction"];
											var messageId = jReaction["message_id"].ToString();

											if (jReaction["yoizen_id"] != null && jReaction["yoizen_id"].Type == JTokenType.String)
												messageId = jReaction["yoizen_id"].ToString();

											DomainModel.Message tempMessage = null;

											if (jReaction["custom_payload"] != null &&
												jReaction["custom_payload"].Type == JTokenType.String &&
												int.TryParse(jReaction["custom_payload"].ToString(), out int id))
											{
												tempMessage = DAL.MessageDAO.GetOne(id, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
											}
											else
											{
												tempMessage = DAL.MessageDAO.GetOne(messageId, DomainModel.SocialServiceTypes.WhatsApp);
											}

											if (tempMessage != null)
											{
												tempMessage.Service = service;

												if (jReaction["emoji"] == null)
												{
													if (tempMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.ReactionParameter))
													{
														tempMessage.Parameters.Remove(Social.WhatsApp.WhatsAppMessage.ReactionParameter);
														DAL.MessageDAO.UpdateParameters(tempMessage);

														Common.Tracer.TraceInfo("Se quitó la reacción del mensaje de whatsapp {0} cuyo código de red social es {1}", tempMessage.ID, messageId);
														
														statusMessage = tempMessage;
													}
												}
												else
												{
													var emoji = jReaction["emoji"].ToString();
													tempMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReactionParameter] = emoji;
													DAL.MessageDAO.UpdateParameters(tempMessage);

													Common.Tracer.TraceInfo("Se agregó la reacción {0} al mensaje de whatsapp {0} cuyo código de red social es {1}", emoji, tempMessage.ID, messageId);

													statusMessage = tempMessage;
												}
											}
											else
											{
												Common.Tracer.TraceVerb("No se encontró el mensaje de whatsapp {0} para tratar la reacción", messageId);
											}
										}

										return (null, null);

									default:
										Common.Tracer.TraceInfo("El tipo {0} no está soportado para Whatsapp", type);
										return (null, null);
								}

								if (message != null)
								{
									var from = jMessage["from"].ToString();
									message.SocialConversationID = from;
									message.SocialMessageID = jMessage["id"].ToString();
									message.Date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds((long) jMessage["timestamp"]).ToLocalTime();

									var userId = long.Parse(from);
									string userName = null;

									JObject jContact = null;

									if (jContacts != null && jContacts.Count > 0)
									{
										// Buscamos el contacto dentro del array contacts que tenga el número de teléfono del remitente del mensaje
										var jContactToken = jContacts.FirstOrDefault(jToken => jToken.Type == JTokenType.Object &&
											((JObject) jToken)["wa_id"] != null &&
											((JObject) jToken)["wa_id"].Type == JTokenType.String &&
											((JObject) jToken)["wa_id"].ToString().Equals(from));

										if (jContactToken != null &&
											jContactToken.Type == JTokenType.Object)
										{
											jContact = (Newtonsoft.Json.Linq.JObject) jContactToken;

											if (jContactToken["profile"] != null &&
												jContactToken["profile"].Type == JTokenType.Object &&
												jContactToken["profile"]["name"] != null &&
												jContactToken["profile"]["name"].Type == JTokenType.String)
											{
												userName = jContactToken["profile"]["name"].ToString();
											}
										}
									}

									var user = GetWhatsAppUser(userId, userName, service);
									message.PostedBy = user;

									InstanceParametersByService(message, service);

									if (jMessage["context"] != null &&
										jMessage["context"].Type == JTokenType.Object)
									{
										var jContext = (JObject) jMessage["context"];
										if (jContext["id"] != null &&
											jContext["id"].Type == JTokenType.String)
										{
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = jContext["id"].ToString();
										}

										if (jContext["from"] != null &&
											jContext["from"].Type == JTokenType.String)
										{
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextFromParameter] = jContext["from"].ToString();
										}

										if (jContext["campaign"] != null)
										{
											if (jContext["campaign"].Type == JTokenType.String)
											{
												message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = jContext["campaign"].ToString();
											}
											else if (jContext["campaign"].Type == JTokenType.Object)
											{
												var jCampaign = (JObject) jContext["campaign"];
												message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = jCampaign["name"].ToString();
												message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter] = jCampaign["id"].ToString();
											}
										}

										if (jContext["campaign_id"] != null &&
										   jContext["campaign_id"].Type == JTokenType.Integer)
										{
											message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter] = jContext["campaign_id"].ToString();
										}

										if (jContext["custom_payload"] != null &&
											jContext["custom_payload"].Type == JTokenType.String)
										{
											if (long.TryParse(jContext["custom_payload"].ToString(), out long messageId))
											{
												if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter))
													message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextWhatsappIDParameter] = message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter];
												message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = messageId.ToString();
											}
										}

										if (jContext["source_template"] != null &&
											jContext["source_template"].Type == JTokenType.Object)
										{
											var jSourceTemplate = (JObject) jContext["source_template"];
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceTemplateNameParameter] = jSourceTemplate["name"].ToString();
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceTemplateLanguageParameter] = jSourceTemplate["language"].ToString();

											if (jSourceTemplate["is_hsm_without_case"] != null &&
												jSourceTemplate["is_hsm_without_case"].Type == JTokenType.Boolean)
											{
												message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceIsFromHSMWithoutCase] = jSourceTemplate["is_hsm_without_case"].ToString();
											}

											if (jSourceTemplate["social_parameters"] != null &&
												jSourceTemplate["social_parameters"].Type == JTokenType.String)
											{
												message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceOutgoingTemplateParameters] = jSourceTemplate["social_parameters"].ToString();
											}

											if (jSourceTemplate["social_message_id"] != null &&
												jSourceTemplate["social_message_id"].Type == JTokenType.String)
											{
												message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceOutgoingSocialMessageID] = jSourceTemplate["social_message_id"].ToString();
											}

											if (jSourceTemplate["ts"] != null &&
												jSourceTemplate["ts"].Type == JTokenType.String)
											{
												message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextSourceOutgoingTimeStamp] = jSourceTemplate["ts"].ToString();
											}
										}
										
										//Estados provinientes de un mensaje de campaña (los cuales insertarremos posteriormente)
										if (jContext["statuses"] != null &&
											jContext["statuses"].Type == JTokenType.Array)
										{
											var statuses = (JArray) jContext["statuses"];
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextStatusesForOutgoingMessage] = statuses.ToString();
										}
									}

									if (jMessage["referral"] != null &&
										jMessage["referral"].Type == JTokenType.Object)
									{
										var jReferral = (JObject) jMessage["referral"];
										if (jReferral["source_url"] != null &&
											jReferral["source_url"].Type == JTokenType.String)
											message.Parameters[WhatsAppMessage.ReferralSourceUrlParameter] = jReferral["source_url"].ToString();

										if (jReferral["source_id"] != null &&
											jReferral["source_id"].Type == JTokenType.String)
											message.Parameters[WhatsAppMessage.ReferralSourceIdParameter] = jReferral["source_id"].ToString();

										if (jReferral["source_type"] != null &&
											jReferral["source_type"].Type == JTokenType.String)
											message.Parameters[WhatsAppMessage.ReferralSourceTypeParameter] = jReferral["source_type"].ToString();
									}

									if (Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendProfile &&
										DomainModel.SystemSettings.Instance.ExtendedProfilesFields != null &&
										DomainModel.SystemSettings.Instance.ExtendedProfilesFields.Length > 0 &&
										jContact != null &&
										jContact["ext"] != null && jContact["ext"].Type == JTokenType.Object)
									{
										var jExt = (JObject) jContact["ext"];
										var profileExtendedFields = new Dictionary<string, string>();
										foreach (var extendedProfileField in DomainModel.SystemSettings.Instance.ExtendedProfilesFields)
										{
											if (jExt[extendedProfileField.Name] != null)
											{
												var currentType = jExt[extendedProfileField.Name].Type;
												if (currentType == JTokenType.Null)
													continue;

												switch (extendedProfileField.DataType)
												{
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.String:
														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToString();
														break;
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.Number:
														if (currentType == JTokenType.Integer)
														{
															profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<int>().ToString();
														}
														else if (currentType == JTokenType.Float)
														{
															profileExtendedFields[extendedProfileField.Name] = System.Convert.ToInt32(jExt[extendedProfileField.Name].ToObject<double>()).ToString();
														}
														else if (currentType == JTokenType.String)
														{
															try
															{
																profileExtendedFields[extendedProfileField.Name] = System.Convert.ToInt32(jExt[extendedProfileField.Name].ToString()).ToString();
															}
															catch { }
														}
														break;
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.Dropdown:
														if (currentType != JTokenType.Integer && currentType != JTokenType.String)
															continue;

														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToString();
														break;
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.Float:
														if (currentType == JTokenType.Integer)
														{
															profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<int>().ToString();
														}
														else if (currentType == JTokenType.Float)
														{
															profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<double>().ToString();
														}
														else if (currentType == JTokenType.String)
														{
															try
															{
																profileExtendedFields[extendedProfileField.Name] = System.Convert.ToDouble(jExt[extendedProfileField.Name].ToString()).ToString();
															}
															catch { }
														}
														break;
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.Boolean:
														if (currentType == JTokenType.Boolean)
														{
															profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<bool>().ToString();
														}
														else if (currentType == JTokenType.Integer)
														{
															profileExtendedFields[extendedProfileField.Name] = System.Convert.ToBoolean(jExt[extendedProfileField.Name].ToObject<int>()).ToString();
														}
														else if (currentType == JTokenType.String)
														{
															var boolText = jExt[extendedProfileField.Name].ToString();
															if (boolText.Equals("true", StringComparison.InvariantCulture) || boolText.Equals("1"))
																profileExtendedFields[extendedProfileField.Name] = true.ToString();
															else if (boolText.Equals("false", StringComparison.InvariantCulture) || boolText.Equals("0"))
																profileExtendedFields[extendedProfileField.Name] = false.ToString();
														}
														break;
													case DomainModel.ExtendedField.ExtendedFieldDataTypes.Date:
														DateTime? date = null;
														try
														{
															if (currentType == JTokenType.Integer)
																date = Common.Conversions.UnixTimeToDateTime(jExt[extendedProfileField.Name].ToObject<int>());
															else if (currentType == JTokenType.String)
																date = DateTime.Parse(jExt[extendedProfileField.Name].ToString());
															else if (currentType == JTokenType.Date)
																date = jExt[extendedProfileField.Name].ToObject<DateTime>();

															if (date != null)
																profileExtendedFields[extendedProfileField.Name] = date.Value.ToString("o");
														}
														catch { }
														break;
													default:
														break;
												}
											}
										}

										if (profileExtendedFields.Count > 0)
										{
											Common.Tracer.TraceVerb("Se está informando campos extendidos para el usuario {0}", message.PostedBy.ID);
											message.PostedBy.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter] = Common.Conversions.ConvertDictionaryToString(profileExtendedFields);
										}
										else
										{
											Common.Tracer.TraceVerb("No se pudo obtener los campos extendidos para el usuario {0}: {1}", message.PostedBy.ID, jExt.ToString());
										}
									}

									if (jMessage["events"] != null && jMessage["events"].Type == JTokenType.Array)
									{
										var jEvents = (JArray) jMessage["events"];
										if (jEvents.Count > 0)
											message.Parameters[WhatsAppMessage.PreviousBotEventsParameter] = jEvents.ToString();
									}
								}
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					{
						if (obj["MessageStatus"] != null &&
							obj["MessageStatus"].Type == JTokenType.String &&
							obj["MessageStatus"].ToString().Length > 0)
						{
							string messageId = obj["MessageSid"].ToString();
							var sentMessage = DAL.MessageDAO.GetOne(messageId, DomainModel.SocialServiceTypes.WhatsApp);
							if (sentMessage != null)
							{
								try
								{
									var jNews = new Newtonsoft.Json.Linq.JObject();
									jNews["ID"] = sentMessage.ID;

									Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);

									var status = obj["MessageStatus"].ToString();
									var date = DateTime.Now;
									var timestamp = Common.Conversions.DateTimeToUnixTime(date);
									switch (status)
									{
										case "sent":
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
											}
											break;
										case "delivered":
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}
											}
											break;
										case "read":
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;
												}
											}
											break;
										case "failed":
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = false.ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = false;

											break;
										default:
											break;
									}

									if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter) &&
										!bool.Parse(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter]))
									{
										var parameters = new Dictionary<string, object>();
										parameters["@@FECHA@@"] = DateTime.Now.ToString("dddd d 'de' MMMM 'del' yyyy", new global::System.Globalization.CultureInfo("es-AR"));
										parameters["@@SERVICIO@@"] = service.Name;
										parameters["@@MENSAJE@@"] = sentMessage.ID.ToString();
										parameters["@@CUENTA@@"] = configuration.FullPhoneNumber;
										parameters["@@ERROR[CODIGO]@@"] = "N/A";
										parameters["@@ERROR[TEXTO]@@"] = "N/A";

										string deliveryError = "N/A";
										int? deliveryErrorNumber = null;

										DAL.MessageDAO.UpdateNotDelivered(sentMessage.ID, deliveryError, deliveryErrorNumber, sentMessage.Parameters);

										jNews["DeliveryError"] = deliveryError;
										jNews["DeliveryErrorNumber"] = deliveryErrorNumber;
										jNews["Delivered"] = false;
									}
									else
									{
										DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);
										Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);
									}

									if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
										DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
									{
										DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
									}
								}
								catch (Exception ex)
								{
									Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", messageId, sentMessage.ID, ex);
								}
							}
						}
						else
						{
							if (obj["NumMedia"] != null &&
								obj["NumMedia"].Type == JTokenType.String &&
								int.TryParse(obj["NumMedia"].ToString(), out int numMedia) &&
								numMedia == 1)
							{
								var type = obj["MediaContentType0"].ToString().ToLower();
								if (type.StartsWith("image/"))
								{
									var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
									message = imageMessage;

									if (obj["Body"] != null &&
										obj["Body"].Type == JTokenType.String &&
										obj["Body"].ToString().Length > 0)
									{
										imageMessage.Body = obj["Body"].ToString();
										imageMessage.EmptyBody = false;
									}

									await DownloadAttachment(imageMessage, obj["MediaUrl0"].ToString());
								}
								else if (type.StartsWith("video/"))
								{
									var imageMessage = new Social.WhatsApp.WhatsAppVideoMessage();
									message = imageMessage;

									if (obj["Body"] != null &&
										obj["Body"].Type == JTokenType.String &&
										obj["Body"].ToString().Length > 0)
									{
										imageMessage.Body = obj["Body"].ToString();
										imageMessage.EmptyBody = false;
									}

									await DownloadAttachment(imageMessage, obj["MediaUrl0"].ToString());
								}
								else if (type.StartsWith("audio/"))
								{
									var imageMessage = new Social.WhatsApp.WhatsAppAudioMessage();
									message = imageMessage;

									if (obj["Body"] != null &&
										obj["Body"].Type == JTokenType.String &&
										obj["Body"].ToString().Length > 0)
									{
										imageMessage.Body = obj["Body"].ToString();
										imageMessage.EmptyBody = false;
									}

									await DownloadAttachment(imageMessage, obj["MediaUrl0"].ToString());
								}
								else
								{
									var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();
									message = documentMessage;

									await DownloadAttachment(documentMessage, obj["MediaUrl0"].ToString());

									if (documentMessage.Attachments != null &&
										documentMessage.Attachments.Length == 1)
									{
										if (obj["Body"] != null &&
										obj["Body"].Type == JTokenType.String &&
										obj["Body"].ToString().Length > 0)
										{
											documentMessage.Attachment.FileName = obj["Body"].ToString();
											CheckAttachmentFileName(documentMessage.Attachment, null);
										}
									}
								}
							}
							else if (obj["Latitude"] != null &&
								obj["Latitude"].Type == JTokenType.String &&
								obj["Longitude"] != null &&
								obj["Longitude"].Type == JTokenType.String &&
								double.TryParse(obj["Latitude"].ToString(), System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out double latitude) &&
								double.TryParse(obj["Longitude"].ToString(), System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out double longitude))
							{
								message = new Social.WhatsApp.WhatsAppLocationMessage();
								message.HasCoordinates = true;
								message.Coordinates = new DomainModel.GeoCoordinate(latitude, longitude);

								var sbLocation = new StringBuilder();

								if (sbLocation.Length > 0)
									sbLocation.Append(Environment.NewLine);
								sbLocation.AppendFormat("{0},{1}",
									message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
									message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

								message.Body = sbLocation.ToString();
							}

							if (message == null)
							{
								message = new Social.WhatsApp.WhatsAppTextMessage();
								if (obj["Body"] != null &&
									obj["Body"].Type == JTokenType.String &&
									obj["Body"].ToString().Length > 0)
								{
									message.Body = obj["Body"].ToString();
									message.EmptyBody = false;
								}
								else
								{
									message.EmptyBody = true;
								}
							}

							message.SocialMessageID = obj["SmsMessageSid"].ToString();
							message.Date = DateTime.Now;

							var from = obj["WaId"].ToString();
							message.SocialConversationID = from;

							message.PostedBy = new Social.WhatsApp.User(long.Parse(from));
							if (obj["ProfileName"] != null && obj["ProfileName"].Type == JTokenType.String)
								message.PostedBy.Name = obj["ProfileName"].ToString();
							else
								message.PostedBy.Name = message.PostedBy.ID.ToString();
							message.PostedBy.DisplayName = message.PostedBy.Name;

							var user = GetWhatsAppUser(message.PostedBy.ID, message.PostedBy.DisplayName, service);
							message.PostedBy = user;

							InstanceParametersByService(message, service);

							if (!message.EmptyBody)
							{
								message.Body = message.Body.Replace("+", " ");
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					{
						var type = obj["type"].ToString();
						if (type.Equals("message-event"))
						{
							var jPayload = (JObject) obj["payload"];

							string messageId = jPayload["gsId"].ToString();
							var sentMessage = DAL.MessageDAO.GetOne(messageId, DomainModel.SocialServiceTypes.WhatsApp);
							if (sentMessage != null)
							{
								try
								{
									var jNews = new Newtonsoft.Json.Linq.JObject();
									jNews["ID"] = sentMessage.ID;

									Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);

									var status = jPayload["type"].ToString();
									switch (status)
									{
										case "enqueued":
											break;

										case "sent":
											{
												var timestamp = jPayload["payload"]["ts"].ToObject<long>();
												var date = Common.Conversions.UnixTimeToDateTime(timestamp);

												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
											}
											break;
										case "delivered":
											{
												var timestamp = jPayload["payload"]["ts"].ToObject<long>();
												var date = Common.Conversions.UnixTimeToDateTime(timestamp);

												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}
											}
											break;
										case "read":
											{
												var timestamp = jPayload["payload"]["ts"].ToObject<long>();
												var date = Common.Conversions.UnixTimeToDateTime(timestamp);

												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;
												}
											}
											break;
										case "failed":
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = false.ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = false;

											break;
										default:
											break;
									}

									if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter) &&
										!bool.Parse(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter]))
									{
										string deliveryError = "N/A";
										int? deliveryErrorNumber = null;

										if (jPayload["payload"] != null && jPayload["payload"].Type == JTokenType.Object)
										{
											var jPayloadPayload = (JObject) jPayload["payload"];

											if (jPayloadPayload["code"] != null && jPayloadPayload["code"].Type == JTokenType.Integer)
											{
												deliveryErrorNumber = jPayloadPayload["code"].ToObject<int>();
											}

											if (jPayloadPayload["reason"] != null && jPayloadPayload["reason"].Type == JTokenType.String)
											{
												deliveryError = jPayloadPayload["reason"].ToString();
											}
										}

										DAL.MessageDAO.UpdateNotDelivered(sentMessage.ID, deliveryError, deliveryErrorNumber, sentMessage.Parameters);

										jNews["DeliveryError"] = deliveryError;
										jNews["DeliveryErrorNumber"] = deliveryErrorNumber;
										jNews["Delivered"] = false;
									}
									else
									{
										DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);
										Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);
									}

									if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
										DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
									{
										DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
									}
								}
								catch (Exception ex)
								{
									Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", messageId, sentMessage.ID, ex);
								}
							}
						}
						else if (type.Equals("message"))
						{
							var jMessage = (JObject) obj["payload"];
							var messageType = jMessage["type"].ToString();

							switch (messageType)
							{
								case "voice":
								case "audio":
									{
										var voiceMessage = new Social.WhatsApp.WhatsAppAudioMessage();
										message = voiceMessage;

										var jAudio = (JObject) jMessage["payload"];
										await DownloadAttachment(voiceMessage, jAudio["url"].ToString());
									}
									break;
								case "contact":
									{
										message = new Social.WhatsApp.WhatsAppVCardMessage();

										var jMessageContacts = (JArray) jMessage["payload"]["contacts"];

										message.HasAttach = true;
										message.Attachments = ConvertContactsToVCard(jMessageContacts);
									}
									break;
								case "file":
									{
										var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();
										message = documentMessage;

										var jDocument = (JObject) jMessage["payload"];
										await DownloadAttachment(documentMessage, jDocument["url"].ToString());

										if (documentMessage.Attachments != null &&
											documentMessage.Attachments.Length == 1)
										{
											if (jDocument["caption"] != null &&
												jDocument["caption"].Type == JTokenType.String &&
												jDocument["caption"].ToString().Length > 0)
											{
												documentMessage.Attachment.FileName = jDocument["caption"].ToString();
												CheckAttachmentFileName(documentMessage.Attachment, null);
											}
										}
									}
									break;
								case "sticker":
									{
										var imageMessage = new Social.WhatsApp.WhatsAppStickerMessage();
										message = imageMessage;

										var jImage = (JObject) jMessage["payload"];
										await DownloadAttachment(imageMessage, jImage["url"].ToString());
									}
									break;
								case "image":
									{
										var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
										message = imageMessage;

										var jImage = (JObject) jMessage["payload"];
										if (jImage["caption"] != null &&
											jImage["caption"].Type == JTokenType.String &&
											jImage["caption"].ToString().Length > 0)
										{
											imageMessage.Body = jImage["caption"].ToString();
											imageMessage.EmptyBody = false;
										}

										await DownloadAttachment(imageMessage, jImage["url"].ToString());
									}
									break;
								case "location":
									{
										message = new Social.WhatsApp.WhatsAppLocationMessage();
										message.HasCoordinates = true;
										var jLocation = (JObject) jMessage["payload"];
										message.Coordinates = new DomainModel.GeoCoordinate(double.Parse(jLocation["latitude"].ToString(), System.Globalization.CultureInfo.InvariantCulture), double.Parse(jLocation["longitude"].ToString(), System.Globalization.CultureInfo.InvariantCulture));

										var sbLocation = new StringBuilder();

										if (sbLocation.Length > 0)
											sbLocation.Append(Environment.NewLine);
										sbLocation.AppendFormat("{0},{1}",
											message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
											message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

										message.Body = sbLocation.ToString();
									}
									break;
								case "video":
									{
										var videoMessage = new Social.WhatsApp.WhatsAppVideoMessage();
										message = videoMessage;

										var jVideo = (JObject) jMessage["payload"];
										if (jVideo["caption"] != null &&
											jVideo["caption"].Type == JTokenType.String &&
											jVideo["caption"].ToString().Length > 0)
										{
											videoMessage.Body = jVideo["caption"].ToString();
											videoMessage.EmptyBody = false;
										}

										await DownloadAttachment(message, jVideo["url"].ToString());
									}
									break;
								case "unknown":
									Common.Tracer.TraceVerb("Se recibió un tipo de mensaje unknown: {0}", jMessage.ToString());
									break;
								case "text":
								default:
									message = new Social.WhatsApp.WhatsAppTextMessage();
									if (jMessage["payload"] != null &&
										jMessage["payload"].Type == JTokenType.Object &&
										jMessage["payload"]["text"] != null &&
										jMessage["payload"]["text"].Type == JTokenType.String)
									{
										message.Body = jMessage["payload"]["text"].ToString();
										message.EmptyBody = false;
									}
									break;
							}

							if (message != null)
							{
								var jSender = (JObject) jMessage["sender"];
								var from = jSender["phone"].ToString();
								message.SocialConversationID = from;
								message.SocialMessageID = jMessage["id"].ToString();
								message.Date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds((long) obj["timestamp"]).ToLocalTime();

								message.PostedBy = new Social.WhatsApp.User(long.Parse(from));
								if (jSender["name"] != null && jSender["name"].Type == JTokenType.String)
									message.PostedBy.Name = jSender["name"].ToString();
								else
									message.PostedBy.Name = message.PostedBy.ID.ToString();
								message.PostedBy.DisplayName = message.PostedBy.Name;

								var user = GetWhatsAppUser(message.PostedBy.ID, message.PostedBy.DisplayName, service);
								message.PostedBy = user;

								InstanceParametersByService(message, service);

								if (obj["context"] != null &&
									obj["context"].Type == JTokenType.Object)
								{
									message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = obj["context"]["gsId"].ToString();
								}
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					{
						if (obj["statuses"] != null && obj["statuses"].Type == JTokenType.Array)
						{
							var jStatuses = (JArray) obj["statuses"];
							foreach (JObject jStatus in jStatuses)
							{
								string messageId = jStatus["id"].ToString();
								var sentMessage = DAL.MessageDAO.GetOne(messageId, DomainModel.SocialServiceTypes.WhatsApp);
								if (sentMessage != null)
								{
									try
									{
										var jNews = new Newtonsoft.Json.Linq.JObject();
										jNews["ID"] = sentMessage.ID;

										Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);

										var status = jStatus["status"].ToString();
										var timestamp = jStatus["timestamp"].ToObject<long>();
										var date = Common.Conversions.UnixTimeToDateTime(timestamp);
										switch (status)
										{
											case "sent":
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												break;
											case "delivered":
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}
												break;
											case "read":
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date.ToString("o");
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = date;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp;

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp;
												}

												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp;
												}
												break;
											case "invalid":
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = false.ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = false;

												break;
											default:
												continue;
										}

										if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter) &&
											!bool.Parse(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter]))
										{
											string deliveryError = "N/A";
											int? deliveryErrorNumber = null;
											if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter))
												deliveryErrorNumber = int.Parse(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter]);
											if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentStatusParameter))
												deliveryError = sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusParameter];

											DAL.MessageDAO.UpdateNotDelivered(sentMessage.ID, deliveryError, deliveryErrorNumber, sentMessage.Parameters);

											jNews["DeliveryError"] = deliveryError;
											jNews["DeliveryErrorNumber"] = deliveryErrorNumber;
											jNews["Delivered"] = false;
										}
										else
										{
											DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);
											Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);
										}

										if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
										{
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
										}
									}
									catch (Exception ex)
									{
										Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", messageId, sentMessage.ID, ex);
									}
								}
							}
						}
						else if (obj["messages"] != null && obj["messages"].Type == JTokenType.Array)
						{
							var jMessages = (JArray) obj["messages"];
							JArray jContacts = null;

							if (obj["contacts"] != null && obj["contacts"].Type == JTokenType.Array)
								jContacts = (JArray) obj["contacts"];

							foreach (JObject jMessage in jMessages)
							{
								var type = "text";
								if (jMessage["type"] != null && jMessage["type"].Type == JTokenType.String)
									type = jMessage["type"].ToString();

								switch (type)
								{
									case "voice":
									case "audio":
										{
											var voiceMessage = new Social.WhatsApp.WhatsAppAudioMessage();
											message = voiceMessage;

											JObject jAudio;
											if (type.Equals("voice"))
												jAudio = (JObject) jMessage["voice"];
											else
												jAudio = (JObject) jMessage["audio"];

											await DownloadAttachment(voiceMessage, jAudio["file"].ToString());

											if (voiceMessage.Attachment != null &&
												voiceMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jAudio["mime_type"] != null &&
												jAudio["mime_type"].Type == JTokenType.String)
												voiceMessage.Attachment.MimeType = jAudio["mime_type"].ToString();
										}
										break;
									case "contacts":
										{
											message = new Social.WhatsApp.WhatsAppVCardMessage();

											var jMessageContacts = (JArray) jMessage["contacts"];

											message.HasAttach = true;
											message.Attachments = ConvertContactsToVCard(jMessageContacts);
										}
										break;
									case "document":
										{
											var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();
											message = documentMessage;

											var jDocument = (JObject) jMessage["document"];
											await DownloadAttachment(documentMessage, jDocument["file"].ToString());

											if (documentMessage.Attachments != null &&
												documentMessage.Attachments.Length == 1)
											{
												if (jDocument["filename"] != null &&
													jDocument["filename"].Type == JTokenType.String &&
													jDocument["filename"].ToString().Length > 0)
												{
													documentMessage.Attachment.FileName = jDocument["filename"].ToString();
													CheckAttachmentFileName(documentMessage.Attachment, null);
												}
												else if (jDocument["caption"] != null &&
													jDocument["caption"].Type == JTokenType.String &&
													jDocument["caption"].ToString().Length > 0)
												{
													documentMessage.Attachment.FileName = jDocument["caption"].ToString();
													CheckAttachmentFileName(documentMessage.Attachment, null);
												}
											}
										}
										break;
									case "image":
										{
											var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
											message = imageMessage;

											var jImage = (JObject) jMessage["image"];
											if (jImage["caption"] != null &&
												jImage["caption"].Type == JTokenType.String &&
												jImage["caption"].ToString().Length > 0)
											{
												imageMessage.Body = jImage["caption"].ToString();
												imageMessage.EmptyBody = false;
											}

											await DownloadAttachment(imageMessage, jImage["file"].ToString());

											if (imageMessage.Attachment != null &&
												imageMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jImage["mime_type"] != null &&
												jImage["mime_type"].Type == JTokenType.String)
												imageMessage.Attachment.MimeType = jImage["mime_type"].ToString();
										}
										break;
									case "sticker":
										{
											var stickerMessage = new Social.WhatsApp.WhatsAppStickerMessage();
											message = stickerMessage;

											var jSticker = (JObject) jMessage["sticker"];
											await DownloadAttachment(stickerMessage, jSticker["url"].ToString());

											if (stickerMessage.Attachment != null &&
												stickerMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jSticker["mime_type"] != null &&
												jSticker["mime_type"].Type == JTokenType.String)
												stickerMessage.Attachment.MimeType = jSticker["mime_type"].ToString();

											if (jSticker["metadata"] != null &&
												jSticker["metadata"].Type == JTokenType.Object)
											{
												var jStickerMetadata = (JObject) jSticker["metadata"];
												stickerMessage.Parameters["sticker-pack-id"] = jStickerMetadata["sticker-pack-id"]?.ToString() ?? string.Empty;
												stickerMessage.Parameters["sticker-pack-name"] = jStickerMetadata["sticker-pack-name"]?.ToString() ?? string.Empty;
												stickerMessage.Parameters["sticker-pack-publisher"] = jStickerMetadata["sticker-pack-publisher"]?.ToString() ?? string.Empty;
											}
										}
										break;
									case "location":
										{
											message = new Social.WhatsApp.WhatsAppLocationMessage();
											message.HasCoordinates = true;
											var jLocation = (JObject) jMessage["location"];
											message.Coordinates = new DomainModel.GeoCoordinate(jLocation["latitude"].ToObject<double>(), jLocation["longitude"].ToObject<double>());

											var sbLocation = new StringBuilder();
											if (jLocation["name"] != null &&
												jLocation["name"].Type == JTokenType.String)
											{
												var name = jLocation["name"].ToString();
												if (!string.IsNullOrEmpty(name))
													sbLocation.Append(name);
											}
											if (jLocation["address"] != null &&
												jLocation["address"].Type == JTokenType.String)
											{
												var address = jLocation["address"].ToString();
												if (!string.IsNullOrEmpty(address))
												{
													if (sbLocation.Length > 0)
														sbLocation.Append(Environment.NewLine);
													sbLocation.Append(address);
												}
											}

											if (sbLocation.Length > 0)
												sbLocation.Append(Environment.NewLine);
											sbLocation.AppendFormat("{0},{1}",
												message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
												message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

											message.Body = sbLocation.ToString();
										}
										break;
									case "video":
										{
											var videoMessage = new Social.WhatsApp.WhatsAppVideoMessage();
											message = videoMessage;

											var jVideo = (JObject) jMessage["video"];
											await DownloadAttachment(message, jVideo["file"].ToString());

											if (videoMessage.Attachment != null &&
												videoMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jVideo["mime_type"] != null &&
												jVideo["mime_type"].Type == JTokenType.String)
												videoMessage.Attachment.MimeType = jVideo["mime_type"].ToString();
										}
										break;
									case "unknown":
										Common.Tracer.TraceVerb("Se recibió un tipo de mensaje unknown: {0}", jMessage.ToString());
										break;
									case "interactive":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											var jInteractive = (JObject) jMessage["interactive"];
											var interactiveType = jInteractive["type"].ToString();

											if (interactiveType.Equals("button_reply"))
											{
												var jButtonReply = (JObject) jInteractive["button_reply"];
												interactiveMessage.Payload = jButtonReply["id"].ToString();
												interactiveMessage.Body = jButtonReply["title"].ToString();
												interactiveMessage.EmptyBody = false;
											}
											else if (interactiveType.Equals("list_reply"))
											{
												var jButtonReply = (JObject) jInteractive["list_reply"];
												interactiveMessage.Payload = jButtonReply["id"].ToString();
												interactiveMessage.Body = jButtonReply["title"].ToString();
												if (jButtonReply["description"] != null &&
													jButtonReply["description"].Type == JTokenType.String &&
													jButtonReply["description"].ToString().Length > 0)
												{
													interactiveMessage.Body += Environment.NewLine + jButtonReply["description"].ToString();
												}
												interactiveMessage.EmptyBody = false;
											}

											message = interactiveMessage;
										}
										break;
									case "button":
										{
											if (jMessage["button"] != null &&
												jMessage["button"].Type == JTokenType.Object)
											{
												var payloadMessage = new Social.WhatsApp.WhatsAppPayloadMessage();
												var jButton = (JObject) jMessage["button"];
												var buttonPayload = GetButtonMessagePayload(jButton["payload"].ToString());

												if (!string.IsNullOrEmpty(buttonPayload.CampaignId))
													payloadMessage.PayloadCampaignId = buttonPayload.CampaignId;
												if (!string.IsNullOrEmpty(buttonPayload.Timestamp))
													payloadMessage.PayloadTimeStamp = buttonPayload.Timestamp;
												payloadMessage.Payload = buttonPayload.Body;
												payloadMessage.Body = jButton["text"].ToString();
												payloadMessage.EmptyBody = false;

												message = payloadMessage;
											}
										}
										break;
									case "text":
									default:
										message = new Social.WhatsApp.WhatsAppTextMessage();
										if (jMessage["text"] != null &&
											jMessage["text"].Type == JTokenType.Object &&
											jMessage["text"]["body"] != null &&
											jMessage["text"]["body"].Type == JTokenType.String)
										{
											message.Body = jMessage["text"]["body"].ToString();
											message.EmptyBody = false;
										}
										break;
								}

								if (message != null)
								{
									var from = jMessage["from"].ToString();
									message.SocialConversationID = from;
									message.SocialMessageID = jMessage["id"].ToString();
									message.Date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds((long) jMessage["timestamp"]).ToLocalTime();

									message.PostedBy = new Social.WhatsApp.User(long.Parse(from));

									if (jContacts != null && jContacts.Count > 0)
									{
										// Buscamos el contacto dentro del array contacts que tenga el número de teléfono del remitente del mensaje
										var jContact = jContacts.FirstOrDefault(jToken => jToken.Type == JTokenType.Object &&
											((JObject) jToken)["wa_id"] != null &&
											((JObject) jToken)["wa_id"].Type == JTokenType.String &&
											((JObject) jToken)["wa_id"].ToString().Equals(from));

										if (jContact != null &&
											jContact.Type == JTokenType.Object &&
											jContact["profile"] != null &&
											jContact["profile"].Type == JTokenType.Object &&
											jContact["profile"]["name"] != null &&
											jContact["profile"]["name"].Type == JTokenType.String)
										{
											message.PostedBy.Name = jContact["profile"]["name"].ToString();
											message.PostedBy.DisplayName = message.PostedBy.Name;
										}
									}

									var user = GetWhatsAppUser(message.PostedBy.ID, message.PostedBy.DisplayName, service);
									message.PostedBy = user;

									InstanceParametersByService(message, service);

									if (jMessage["context"] != null &&
										jMessage["context"].Type == JTokenType.Object)
									{
										var jContext = (JObject) jMessage["context"];
										if (jContext["id"] != null && jContext["id"].Type == JTokenType.String)
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = jContext["id"].ToString();
										if (jContext["from"] != null && jContext["from"].Type == JTokenType.String)
											message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextFromParameter] = jContext["from"].ToString();
									}
								}
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					{
						if (obj["message"] != null)
						{
							var jMessage = (JObject) obj["message"];
							if (jMessage["type"] != null && jMessage["type"].Type == JTokenType.String)
							{
								var type = jMessage["type"].ToString();

								switch (type.ToUpper())
								{
									case "TEXT":
										{
											var textMessage = new Social.WhatsApp.WhatsAppTextMessage();
											textMessage.Body = jMessage["messageText"].ToString();
											message = textMessage;
										}
										break;
									case "IMAGE":
										{
											var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
											if (jMessage["caption"] != null &&
												jMessage["caption"].Type == JTokenType.String &&
												jMessage["caption"].ToString().Length > 0)
											{
												imageMessage.Body = jMessage["caption"].ToString();
												imageMessage.EmptyBody = false;
											}
											else
											{
												imageMessage.EmptyBody = true;
											}

											await DownloadAttachment(imageMessage, jMessage["mediaUrl"].ToString());
											if (imageMessage.HasAttach &&
												jMessage["mimeType"] != null &&
												jMessage["mimeType"].Type == JTokenType.String &&
												!imageMessage.Attachment.MimeType.Equals(jMessage["mimeType"].ToString()))
											{
												imageMessage.Attachment.MimeType = jMessage["mimeType"].ToString();
											}

											message = imageMessage;
										}
										break;
									case "AUDIO":
										{
											var audioMessage = new Social.WhatsApp.WhatsAppAudioMessage();

											await DownloadAttachment(audioMessage, jMessage["mediaUrl"].ToString());
											if (audioMessage.HasAttach &&
												jMessage["mimeType"] != null &&
												jMessage["mimeType"].Type == JTokenType.String &&
												!audioMessage.Attachment.MimeType.Equals(jMessage["mimeType"].ToString()))
											{
												audioMessage.Attachment.MimeType = jMessage["mimeType"].ToString();
											}

											message = audioMessage;
										}
										break;
									case "VIDEO":
										{
											var videoMessage = new Social.WhatsApp.WhatsAppVideoMessage();

											await DownloadAttachment(videoMessage, jMessage["mediaUrl"].ToString());
											if (videoMessage.HasAttach &&
												jMessage["mimeType"] != null &&
												jMessage["mimeType"].Type == JTokenType.String &&
												!videoMessage.Attachment.MimeType.Equals(jMessage["mimeType"].ToString()))
											{
												videoMessage.Attachment.MimeType = jMessage["mimeType"].ToString();
											}

											message = videoMessage;
										}
										break;
									case "DOCUMENT":
										{
											var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();

											await DownloadAttachment(documentMessage, jMessage["mediaUrl"].ToString());
											if (documentMessage.HasAttach &&
												jMessage["mimeType"] != null &&
												jMessage["mimeType"].Type == JTokenType.String &&
												!documentMessage.Attachment.MimeType.Equals(jMessage["mimeType"].ToString()))
											{
												documentMessage.Attachment.MimeType = jMessage["mimeType"].ToString();
											}

											message = documentMessage;
										}
										break;
									case "LOCATION":
										{
											var jLocation = (JObject) jMessage["location"];
											var locationMessage = new Social.WhatsApp.WhatsAppLocationMessage();
											message = locationMessage;
											locationMessage.HasCoordinates = true;
											var parts = jLocation["geoPoint"].ToString().Split(",".ToCharArray());
											var latitude = double.Parse(parts[0], System.Globalization.CultureInfo.InvariantCulture);
											var longitude = double.Parse(parts[1], System.Globalization.CultureInfo.InvariantCulture);
											locationMessage.Coordinates = new DomainModel.GeoCoordinate(latitude, longitude);
											locationMessage.Body = jLocation["geoPoint"].ToString();
										}
										break;
									case "CONTACTS":
										{
											message = new Social.WhatsApp.WhatsAppVCardMessage();

											var jMessageContacts = (JArray) jMessage["contacts"];

											message.HasAttach = true;
											message.Attachments = ConvertContactsToVCard(jMessageContacts);
										}
										break;
									case "STICKER":
										{
											var stickerMessage = new Social.WhatsApp.WhatsAppStickerMessage();
											message = stickerMessage;

											await DownloadAttachment(stickerMessage, jMessage["mediaUrl"].ToString());

											if (stickerMessage.Attachment != null &&
												stickerMessage.Attachment.MimeType.Equals("binary/octet-stream") &&
												jMessage["mimeType"] != null &&
												jMessage["mimeType"].Type == JTokenType.String)
												stickerMessage.Attachment.MimeType = jMessage["mimeType"].ToString();
										}
										break;
									case "BUTTON":
									case "INTERACTIVE":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											var jInteractive = (JObject) jMessage["interactive"];
											var interactiveType = jInteractive["type"].ToString();

											if (interactiveType.Equals("BUTTON_REPLY"))
											{
												var jButtonReply = (JObject) jInteractive["buttonReply"];
												if (jButtonReply["payload"] != null && jButtonReply["payload"].Type == JTokenType.String)
													interactiveMessage.Payload = jButtonReply["payload"].ToString();
												else
													interactiveMessage.Payload = jButtonReply["title"].ToString();
												interactiveMessage.Body = jButtonReply["title"].ToString();
												interactiveMessage.EmptyBody = false;
											}
											else if (interactiveType.Equals("LIST_REPLY"))
											{
												var jButtonReply = (JObject) jInteractive["listReply"];
												interactiveMessage.Payload = jButtonReply["rowIdentifier"].ToString();
												interactiveMessage.Body = jButtonReply["rowTitle"].ToString();
												if (jButtonReply["rowDescription"] != null &&
													jButtonReply["rowDescription"].Type == JTokenType.String &&
													jButtonReply["rowDescription"].ToString().Length > 0)
												{
													interactiveMessage.Body += Environment.NewLine + jButtonReply["rowDescription"].ToString();
												}
												interactiveMessage.EmptyBody = false;
											}

											message = interactiveMessage;
										}
										break;
									default:
										break;
								}
							}
						}

						if (message != null)
						{
							message.SocialConversationID = obj["source"].ToString();
							message.SocialMessageID = obj["id"].ToString();
							message.Date = DateTime.Parse(obj["receivedDate"].ToString()).ToLocalTime();

							var userId = long.Parse(obj["source"].ToString());
							string displayName = null;
							if (obj["userProfile"] != null && obj["userProfile"].Type == JTokenType.Object)
								displayName = obj["userProfile"]["name"].ToString();
							var user = GetWhatsAppUser(userId, displayName, service);
							message.PostedBy = user;

							InstanceParametersByService(message, service);

							if (obj["campaignAlias"] != null &&
								obj["campaignAlias"].Type == JTokenType.String)
							{
								message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = obj["campaignAlias"].ToString();
							}
						}
						else
						{
							if (obj["sentStatus"] != null || obj["deliveredStatus"] != null)
							{
								DomainModel.Message sentMessage = null;
								string socialMessageId = obj["id"].ToString();

								if (obj["correlationId"] != null &&
									obj["correlationId"].Type == JTokenType.String &&
									long.TryParse(obj["correlationId"].ToString(), out long messageId))
								{
									sentMessage = DAL.MessageDAO.GetOne(messageId, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
								}

								if (sentMessage != null)
								{
									try
									{
										var jNews = new Newtonsoft.Json.Linq.JObject();
										jNews["ID"] = sentMessage.ID;

										Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", socialMessageId, sentMessage.ID);

										if (obj["sent"] != null && obj["sent"].Type == JTokenType.Boolean)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = obj["sent"].ToObject<bool>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentParameter] = obj["sent"];
										}
										if (obj["sentStatusCode"] != null && obj["sentStatusCode"].Type == JTokenType.Integer)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter] = obj["sentStatusCode"].ToObject<int>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter] = obj["sentStatusCode"];
										}
										if (obj["sentStatus"] != null && obj["sentStatus"].Type == JTokenType.String)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusParameter] = obj["sentStatus"].ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentStatusParameter] = obj["sentStatus"];
										}

										if (obj["sentDate"] != null)
										{
											if (obj["sentDate"].Type == JTokenType.String)
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = obj["sentDate"].ToString();
											}
											else if (obj["sentDate"].Type == JTokenType.Date)
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = obj["sentDate"].ToObject<DateTime>().ToLocalTime().ToString("o");
											}
											jNews[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = obj["sentDate"];
										}

										if (obj["sentAt"] != null && obj["sentAt"].Type == JTokenType.Integer)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = obj["sentAt"].ToObject<long>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = obj["sentAt"];
										}

										if (obj["delivered"] != null && obj["delivered"].Type == JTokenType.Boolean)
										{
											if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter) ||
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter].Equals(false.ToString()))
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = obj["delivered"].ToObject<bool>().ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = obj["delivered"];
											}
										}
										if (obj["deliveredStatusCode"] != null && obj["deliveredStatusCode"].Type == JTokenType.Integer)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredStatusCodeParameter] = obj["deliveredStatusCode"].ToObject<int>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredStatusCodeParameter] = obj["deliveredStatusCode"];
										}
										if (obj["deliveredStatus"] != null && obj["deliveredStatus"].Type == JTokenType.String)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredStatusParameter] = obj["deliveredStatus"].ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredStatusParameter] = obj["deliveredStatus"];
										}
										if (obj["deliveredDate"] != null)
										{
											if (obj["deliveredDate"].Type == JTokenType.String)
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = obj["deliveredDate"].ToString();
											else if (obj["deliveredDate"].Type == JTokenType.Date)
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = obj["deliveredDate"].ToObject<DateTime>().ToLocalTime().ToString("o");
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = obj["deliveredDate"];
										}
										if (obj["deliveredAt"] != null && obj["deliveredAt"].Type == JTokenType.Integer)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = obj["deliveredAt"].ToObject<long>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = obj["deliveredAt"];
										}
										if (obj["read"] != null && obj["read"].Type == JTokenType.Boolean)
										{
											if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.ReadParameter) ||
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter].Equals(false.ToString()))
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = obj["read"].ToObject<bool>().ToString();
												jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = obj["read"];
											}
										}
										if (obj["readDate"] != null)
										{
											if (obj["readDate"].Type == JTokenType.String)
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = obj["readDate"].ToString();
											else if (obj["readDate"].Type == JTokenType.Date)
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = obj["readDate"].ToObject<DateTime>().ToLocalTime().ToString("o");
											jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = obj["readDate"];
										}
										if (obj["readAt"] != null && obj["readAt"].Type == JTokenType.Integer)
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = obj["readAt"].ToObject<long>().ToString();
											jNews[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = obj["readAt"];
										}

										if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentParameter) &&
											!bool.Parse(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentParameter]))
										{
											string deliveryError = "N/A";
											int? deliveryErrorNumber = null;
											if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentStatusParameter))
											{
												deliveryError = sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusParameter];
											}
											if (sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter))
											{
												try
												{
													deliveryErrorNumber = global::System.Convert.ToInt32(sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.SentStatusCodeParameter]);
												}
												catch { }
											}
											DAL.MessageDAO.UpdateNotDelivered(sentMessage.ID, deliveryError, deliveryErrorNumber, sentMessage.Parameters);

											jNews["DeliveryError"] = deliveryError;
											jNews["DeliveryErrorNumber"] = deliveryErrorNumber;
											jNews["Delivered"] = false;
										}
										else
										{
											DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);
											Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", socialMessageId, sentMessage.ID);
										}

										if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
										{
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
										}
									}
									catch (Exception ex)
									{
										Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", socialMessageId, sentMessage.ID, ex);
									}
								}
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					{
						if (obj["sentAt"] != null || obj["seenAt"] != null)
						{
							DomainModel.Message sentMessage = null;
							var socialMessageId = obj["messageId"].ToString();
							long messageId;

							if (obj["callbackData"] != null &&
								obj["callbackData"].Type == JTokenType.String &&
								long.TryParse(obj["callbackData"].ToString(), out messageId))
							{
								sentMessage = DAL.MessageDAO.GetOne(messageId, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
							}
							else if (long.TryParse(socialMessageId, out messageId))
							{
								sentMessage = DAL.MessageDAO.GetOne(messageId, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
							}

							if (sentMessage != null)
							{
								try
								{
									Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", socialMessageId, sentMessage.ID);

									var jNews = new Newtonsoft.Json.Linq.JObject();
									jNews["ID"] = sentMessage.ID;

									var ignore = true;

									if (obj["sentAt"] != null)
									{
										DateTime sentDateTime;
										if (obj["sentAt"].Type == JTokenType.String)
											sentDateTime = DateTime.Parse(obj["sentAt"].ToString()).ToLocalTime();
										else
											sentDateTime = obj["sentAt"].ToObject<DateTime>().ToLocalTime();

										sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
										sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = sentDateTime.ToString("o");
										jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
										jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = sentDateTime;
										ignore = false;
									}

									if (obj["seenAt"] != null)
									{
										DateTime seenDateTime;
										if (obj["seenAt"].Type == JTokenType.String)
											seenDateTime = DateTime.Parse(obj["seenAt"].ToString()).ToLocalTime();
										else
											seenDateTime = obj["seenAt"].ToObject<DateTime>().ToLocalTime();

										if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
										{
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
											sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = seenDateTime.ToString("o");
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
											jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = seenDateTime;
										}

										sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
										sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = seenDateTime.ToString("o");
										jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
										jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = seenDateTime;
										ignore = false;
									}

									if (!ignore)
									{
										DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);

										Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", socialMessageId, sentMessage.ID);
									}

									if (obj["error"] != null &&
										obj["error"].Type == JTokenType.Object &&
										obj["error"]["id"] != null &&
										obj["error"]["id"].Type == JTokenType.Integer)
									{
										var error = obj["error"]["id"].ToObject<int>();
										if (error > 0)
										{
											int errorCode = error;
											var deliveryError = obj["error"]["description"].ToString();
											DAL.MessageDAO.UpdateNotDelivered(sentMessage.ID, deliveryError, error, sentMessage.Parameters);

											jNews["DeliveryError"] = deliveryError;
											jNews["DeliveryErrorNumber"] = error;
											jNews["Delivered"] = false;
										}
									}

									if (!ignore)
									{
										if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
										{
											DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
										}
									}
								}
								catch (Exception ex)
								{
									Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", socialMessageId, sentMessage.ID, ex);
								}
							}
						}
						else
						{
							if (obj["message"] != null &&
								obj["message"].Type == JTokenType.Object)
							{
								var jMessage = (JObject) obj["message"];
								var type = jMessage["type"].ToString();

								var authorizationHeaderValue = global::System.Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", configuration.IntegrationType4User, configuration.IntegrationType4Password)));

								switch (type)
								{
									case "TEXT":
										{
											if (jMessage["context"] != null &&
												jMessage["context"].Type == JTokenType.Object)
											{
												var jContext = (JObject) jMessage["context"];
												if (jContext["referredProduct"] != null &&
													jContext["referredProduct"].Type == JTokenType.Object)
												{
													var referredProductMessage = new Social.WhatsApp.WhatsAppReferredProductMessage();

													var jReferrerProduct = (JObject) jContext["referredProduct"];
													referredProductMessage.CatalogId = jReferrerProduct["catalogId"].ToString();
													referredProductMessage.ProductRetailerId = jReferrerProduct["productRetailerId"].ToString();

													message = referredProductMessage;
												}
											}

											if (message == null)
											{
												message = new Social.WhatsApp.WhatsAppTextMessage();
											}

											if (jMessage["text"] != null &&
												jMessage["text"].Type == JTokenType.String)
											{
												message.Body = jMessage["text"].ToString();
											}
										}
										break;
									case "IMAGE":
										{
											var imageMessage = new Social.WhatsApp.WhatsAppImageMessage();
											message = imageMessage;

											if (jMessage["caption"] != null &&
												jMessage["caption"].Type == JTokenType.String &&
												jMessage["caption"].ToString().Length > 0)
											{
												imageMessage.Body = jMessage["caption"].ToString();
												imageMessage.EmptyBody = false;
											}

											var headers = new Dictionary<string, string>();
											if (configuration.IntegrationType4AuthorizationType == 1)
												headers["Authorization"] = $"Basic {System.Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.IntegrationType4User}:{configuration.IntegrationType4Password}"))}";
											else if (configuration.IntegrationType4AuthorizationType == 2)
												headers["Authorization"] = $"App {configuration.IntegrationType4ApiKey}";
											await DownloadAttachment(imageMessage, jMessage["url"].ToString(), headers);
										}
										break;
									case "DOCUMENT":
										{
											var documentMessage = new Social.WhatsApp.WhatsAppDocumentMessage();
											message = documentMessage;

											var headers = new Dictionary<string, string>();
											if (configuration.IntegrationType4AuthorizationType == 1)
												headers["Authorization"] = $"Basic {System.Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.IntegrationType4User}:{configuration.IntegrationType4Password}"))}";
											else if (configuration.IntegrationType4AuthorizationType == 2)
												headers["Authorization"] = $"App {configuration.IntegrationType4ApiKey}";
											await DownloadAttachment(documentMessage, jMessage["url"].ToString(), headers);

											if (documentMessage.Attachments != null &&
												documentMessage.Attachments.Length == 1 &&
												jMessage["caption"] != null &&
												jMessage["caption"].Type == JTokenType.String &&
												jMessage["caption"].ToString().Length > 0)
											{
												documentMessage.Attachment.FileName = jMessage["caption"].ToString();
												CheckAttachmentFileName(documentMessage.Attachment, null);
											}
										}
										break;
									case "VOICE":
									case "AUDIO":
										{
											var voiceMessage = new Social.WhatsApp.WhatsAppAudioMessage();
											message = voiceMessage;
											var headers = new Dictionary<string, string>();
											if (configuration.IntegrationType4AuthorizationType == 1)
												headers["Authorization"] = $"Basic {System.Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.IntegrationType4User}:{configuration.IntegrationType4Password}"))}";
											else if (configuration.IntegrationType4AuthorizationType == 2)
												headers["Authorization"] = $"App {configuration.IntegrationType4ApiKey}";
											await DownloadAttachment(voiceMessage, jMessage["url"].ToString(), headers);
											try
											{
												if (voiceMessage.Attachment.MimeType.StartsWith("application/octet-stream", StringComparison.InvariantCultureIgnoreCase))
												{
													voiceMessage.Attachment.MimeType = "audio/ogg";
													voiceMessage.Attachment.FileName = "voice.ogg";
												}
												else
												{
													CheckAttachmentFileName(voiceMessage.Attachment, null);
												}
											}
											catch { }
										}
										break;
									case "VIDEO":
										{
											var videoMessage = new Social.WhatsApp.WhatsAppVideoMessage();
											message = videoMessage;
											var headers = new Dictionary<string, string>();
											if (configuration.IntegrationType4AuthorizationType == 1)
												headers["Authorization"] = $"Basic {System.Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.IntegrationType4User}:{configuration.IntegrationType4Password}"))}";
											else if (configuration.IntegrationType4AuthorizationType == 2)
												headers["Authorization"] = $"App {configuration.IntegrationType4ApiKey}";
											await DownloadAttachment(message, jMessage["url"].ToString(), headers);
											try
											{
												if (videoMessage.Attachment.MimeType.StartsWith("application/octet-stream", StringComparison.InvariantCultureIgnoreCase))
												{
													videoMessage.Attachment.MimeType = "video/mp4";
													videoMessage.Attachment.FileName = "video.mp4";
												}
												else
												{
													CheckAttachmentFileName(videoMessage.Attachment, null);
												}
											}
											catch { }
										}
										break;
									case "CONTACT":
										{
											message = new Social.WhatsApp.WhatsAppVCardMessage();

											var jMessageContacts = (JArray) jMessage["contacts"];

											message.HasAttach = true;
											message.Attachments = ConvertContactsToVCard(jMessageContacts);
										}
										break;
									case "LOCATION":
										{
											message = new Social.WhatsApp.WhatsAppLocationMessage();
											message.HasCoordinates = true;
											message.Coordinates = new DomainModel.GeoCoordinate(jMessage["latitude"].ToObject<double>(), jMessage["longitude"].ToObject<double>());
											message.Body = string.Format("{0},{1}",
												message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
												message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

											if (jMessage["name"] != null && jMessage["name"].Type == JTokenType.String)
												message.Parameters[Social.WhatsApp.WhatsAppLocationMessage.LocationNameParameter] = jMessage["name"].ToString();
											if (jMessage["address"] != null && jMessage["address"].Type == JTokenType.String)
												message.Parameters[Social.WhatsApp.WhatsAppLocationMessage.LocationAddressParameter] = jMessage["address"].ToString();
											if (jMessage["url"] != null && jMessage["url"].Type == JTokenType.String)
												message.Parameters[Social.WhatsApp.WhatsAppLocationMessage.LocationUrlParameter] = jMessage["url"].ToString();
										}
										break;
									case "BUTTON":
										{
											if (jMessage["payload"] != null && jMessage["payload"].Type == JTokenType.String)
											{
												message = new Social.WhatsApp.WhatsAppPayloadMessage();
												var buttonPayload = GetButtonMessagePayload(jMessage["payload"].ToString());

												if (!string.IsNullOrEmpty(buttonPayload.CampaignId))
													message.PayloadCampaignId = buttonPayload.CampaignId;
												if (!string.IsNullOrEmpty(buttonPayload.Timestamp))
													message.PayloadTimeStamp = buttonPayload.Timestamp;
												message.Payload = buttonPayload.Body;
											}
										}
										break;
									case "STICKER":
										{
											var stickerMessage = new Social.WhatsApp.WhatsAppStickerMessage();
											message = stickerMessage;

											var headers = new Dictionary<string, string>();
											if (configuration.IntegrationType4AuthorizationType == 1)
												headers["Authorization"] = $"Basic {System.Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.IntegrationType4User}:{configuration.IntegrationType4Password}"))}";
											else if (configuration.IntegrationType4AuthorizationType == 2)
												headers["Authorization"] = $"App {configuration.IntegrationType4ApiKey}";
											await DownloadAttachment(stickerMessage, jMessage["url"].ToString(), headers);
										}
										break;
									case "INTERACTIVE_BUTTON_REPLY":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											interactiveMessage.Payload = jMessage["id"].ToString();
											interactiveMessage.Body = jMessage["title"].ToString();
											interactiveMessage.EmptyBody = false;
											message = interactiveMessage;
										}
										break;
									case "INTERACTIVE_LIST_REPLY":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											interactiveMessage.Payload = jMessage["id"].ToString();
											interactiveMessage.Body = jMessage["title"].ToString();
											if (jMessage["description"] != null &&
												jMessage["description"].Type == JTokenType.String &&
												jMessage["description"].ToString().Length > 0)
											{
												interactiveMessage.Body += Environment.NewLine + jMessage["description"].ToString();
											}
											interactiveMessage.EmptyBody = false;
											message = interactiveMessage;
										}
										break;
									case "INTERACTIVE_FLOW_REPLY":
										{
											var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
											if (jMessage["text"] != null && jMessage["text"].Type == JTokenType.String)
											{
												interactiveMessage.Body = jMessage["text"].ToString();
												interactiveMessage.EmptyBody = false;
											}
											else
											{
												interactiveMessage.EmptyBody = true;
											}

											if (jMessage["respose"] != null &&
												jMessage["respose"].Type == JTokenType.String)
											{
												var jResponse = JObject.Parse(jMessage["respose"].ToString());

												if (jResponse["flow_token"] != null &&
													jResponse["flow_token"].Type == JTokenType.String)
												{
													string jFlowToken = jResponse["flow_token"].ToString();
													if (!string.IsNullOrEmpty(jFlowToken))
													{
														interactiveMessage.ConvertWhatsappFlowInvokeParams(jFlowToken);
													}

													var flowTokenProperty = jResponse.Property("flow_token");
													if (flowTokenProperty != null)
													{
														flowTokenProperty.Remove();
													}
												}

												interactiveMessage.Payload = jResponse.ToString();
											}

											message = interactiveMessage;
										}
										break;
									case "ORDER":
										{
											var orderMessage = new Social.WhatsApp.WhatsAppOrderMessage();
											orderMessage.CatalogId = jMessage["catalogId"].ToString();
											if (jMessage["text"] != null && jMessage["text"].Type == JTokenType.String)
											{
												orderMessage.Body = jMessage["text"].ToString();
												orderMessage.EmptyBody = false;
											}
											else
											{
												orderMessage.EmptyBody = true;
											}

											var jProductItems = (JArray) jMessage["productItems"];
											orderMessage.ProductItems = jProductItems.ToString();
											orderMessage.Count = jProductItems.Count;

											message = orderMessage;
										}
										break;
									case "UNSUPPORTED":
										return (null, null);
									default:
										break;
								}

								if (message == null)
									message = new Social.WhatsApp.WhatsAppTextMessage();

								message.SocialConversationID = obj["from"].ToString();
								message.SocialMessageID = obj["messageId"].ToString();
								message.Date = DateTime.Parse(obj["receivedAt"].ToString());

								if (jMessage["context"] != null &&
									jMessage["context"].Type == JTokenType.Object &&
									jMessage["context"]["id"] != null &&
									jMessage["context"]["id"].Type == JTokenType.String)
								{
									message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = jMessage["context"]["id"].ToString();
								}

								if (jMessage["referral"] != null &&
									jMessage["referral"].Type == JTokenType.Object)
								{
									var jReferral = (JObject) jMessage["referral"];
									if (jReferral["sourceUrl"] != null &&
										jReferral["sourceUrl"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceUrlParameter] = jReferral["sourceUrl"].ToString();

									if (jReferral["sourceId"] != null &&
										jReferral["sourceId"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceIdParameter] = jReferral["sourceId"].ToString();

									if (jReferral["sourceType"] != null &&
										jReferral["sourceType"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceTypeParameter] = jReferral["sourceType"].ToString();
								}

								var userId = long.Parse(obj["from"].ToString());
								string displayName = null;
								if (obj["contact"] != null && obj["contact"].Type == JTokenType.Object)
								{
									var jContact = (Newtonsoft.Json.Linq.JObject) obj["contact"];
									if (jContact["name"] != null && jContact["name"].Type == JTokenType.String)
										displayName = jContact["name"].ToString();
								}
								var user = GetWhatsAppUser(userId, displayName, service);
								message.PostedBy = user;

								InstanceParametersByService(message, service);

								if (string.IsNullOrEmpty(message.Body) &&
									obj["message"]["text"] != null &&
									obj["message"]["text"].Type == JTokenType.String &&
									obj["message"]["text"].ToString().Length > 0)
									message.Body = obj["message"]["text"].ToString();
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
					{
						if (obj["STATUS"] != null)
						{
							if (obj["CLIENT_PAYLOAD"] != null && obj["CLIENT_PAYLOAD"].Type == JTokenType.String)
							{
								if (long.TryParse(obj["CLIENT_PAYLOAD"].ToString(), out long messageId))
								{
									var sentMessage = DAL.MessageDAO.GetOne(messageId, new DAL.MessageDAO.RelatedEntitiesToRead(false), false);
									if (sentMessage != null)
									{
										try
										{
											Common.Tracer.TraceVerb("Se está notificando información de un mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);

											var jNews = new Newtonsoft.Json.Linq.JObject();
											jNews["ID"] = sentMessage.ID;

											var status = obj["STATUS"].ToString();
											DateTime statusDateTime;
											if (obj["STATUS_CHANGE_TIME"].Type == JTokenType.String)
												statusDateTime = DateTime.Parse(obj["STATUS_CHANGE_TIME"].ToString()).ToLocalTime();
											else
												statusDateTime = obj["STATUS_CHANGE_TIME"].ToObject<DateTime>().ToLocalTime();

											var ignore = false;
											if (status.Equals("read"))
											{
												if (!sentMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.DeliveredParameter))
												{
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
													sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = statusDateTime.ToString("o");
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
													jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = statusDateTime;
												}

												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = statusDateTime.ToString("o");

												jNews[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = statusDateTime;
											}
											else if (status.Equals("delivered"))
											{
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
												sentMessage.Parameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = statusDateTime.ToString("o");
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true;
												jNews[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = statusDateTime;
											}
											else
											{
												ignore = true;
											}

											if (!ignore)
											{
												DAL.MessageDAO.UpdateParameters(sentMessage.ID, sentMessage.Parameters);

												Common.Tracer.TraceVerb("Se actualizó los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}", messageId, sentMessage.ID);

												if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
													DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
												{
													DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(sentMessage, jNews);
												}
											}
										}
										catch (Exception ex)
										{
											Common.Tracer.TraceVerb("No se pudo actualizar los parámetros del mensaje enviado por ySocial de Whatsapp con código {0} perteneciente al mensaje {1}: {2}", messageId, sentMessage.ID, ex);
										}
									}
								}
							}
						}
						else
						{
							if (obj["hasAttachment"] != null && obj["hasAttachment"].ToObject<bool>())
							{
								if (obj["image"] != null && obj["image"].Type == JTokenType.String)
								{
									message = new Social.WhatsApp.WhatsAppImageMessage();
									if (obj["caption"] != null &&
										obj["caption"].Type == JTokenType.String &&
										obj["caption"].ToString().Length > 0)
									{
										message.Body = obj["caption"].ToString();
										message.EmptyBody = false;
									}
									await DownloadAttachment(message, obj["image"].ToString());
								}
								else if (obj["video"] != null && obj["video"].Type == JTokenType.String)
								{
									message = new Social.WhatsApp.WhatsAppVideoMessage();
									if (obj["caption"] != null && obj["caption"].Type == JTokenType.String)
										message.Body = obj["caption"].ToString();
									await DownloadAttachment(message, obj["video"].ToString());
								}
								else if (obj["audio"] != null && obj["audio"].Type == JTokenType.String)
								{
									message = new Social.WhatsApp.WhatsAppAudioMessage();
									if (obj["caption"] != null && obj["caption"].Type == JTokenType.String)
										message.Body = obj["caption"].ToString();
									await DownloadAttachment(message, obj["audio"].ToString());
								}
								else if (obj["file"] != null && obj["file"].Type == JTokenType.String)
								{
									message = new Social.WhatsApp.WhatsAppDocumentMessage();
									await DownloadAttachment(message, obj["file"].ToString());
									if (obj["caption"] != null &&
										obj["caption"].Type == JTokenType.String &&
										message.HasAttach)
									{
										message.Attachments[0].FileName = obj["caption"].ToString();
									}
								}
							}
							else if (obj["location"] != null && obj["location"].Type == JTokenType.Object)
							{
								JObject jLocation = (JObject) obj["location"];
								message = new Social.WhatsApp.WhatsAppLocationMessage();
								message.HasCoordinates = true;
								message.Coordinates = new DomainModel.GeoCoordinate(jLocation["latitude"].ToObject<double>(), jLocation["longitude"].ToObject<double>());
								message.Body = string.Format("{0},{1}",
									message.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
									message.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

								if (jLocation["name"] != null && jLocation["name"].Type == JTokenType.String)
									message.Parameters[Social.WhatsApp.WhatsAppLocationMessage.LocationNameParameter] = jLocation["name"].ToString();
								if (jLocation["address"] != null && jLocation["address"].Type == JTokenType.String)
									message.Parameters[Social.WhatsApp.WhatsAppLocationMessage.LocationAddressParameter] = jLocation["address"].ToString();
							}
							else if (obj["contacts"] != null && obj["contacts"].Type == JTokenType.Array)
							{
								message = new Social.WhatsApp.WhatsAppVCardMessage();

								var jMessageContacts = (JArray) obj["contacts"];

								message.HasAttach = true;
								message.Attachments = ConvertContactsToVCard(jMessageContacts);
							}

							if (obj["isButton"] != null &&
								obj["isButton"].Type == JTokenType.Boolean &&
								obj["isButton"].ToObject<bool>() &&
								obj["payload"] != null &&
								obj["payload"].Type == JTokenType.String)
							{
								var payload = obj["payload"].ToString();
								if (!string.IsNullOrEmpty(payload))
								{
									message = new WhatsAppPayloadMessage();
									message.Payload = payload;
								}
							}

							if (message == null)
								message = new Social.WhatsApp.WhatsAppTextMessage();

							message.SocialConversationID = obj["contactId"].ToString();
							if (obj["wappMessageId"] != null && obj["wappMessageId"].Type == JTokenType.String)
								message.SocialMessageID = obj["wappMessageId"].ToString();
							else
								message.SocialMessageID = obj["_id_"].ToString();
							message.Date = DateTime.Parse(obj["date"].ToString()).ToLocalTime();
							var userId = long.Parse(obj["contactId"].ToString());
							string displayName = null;
							if (obj["fromName"] != null && obj["fromName"].Type == JTokenType.String)
								displayName = obj["fromName"].ToString();
							message.PostedBy = GetWhatsAppUser(userId, displayName, service);

							InstanceParametersByService(message, service);

							if (string.IsNullOrEmpty(message.Body) &&
								obj["message"] != null && obj["message"].Type == JTokenType.String)
								message.Body = obj["message"].ToString();

							if (obj["wappInReplyOfMessage"] != null &&
								obj["wappInReplyOfMessage"].Type == JTokenType.String)
							{
								message.Parameters[Social.WhatsApp.WhatsAppMessage.ContextMessageIDParameter] = obj["wappInReplyOfMessage"].ToString();
							}
						}
					}
					break;

				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
				default:
					{
						if (obj["msg"] == null || obj["msg"].Type != JTokenType.Object)
							return (null, null);

						if (obj["chat"].ToString().Contains("@g")) return (null, null);

						var jMessage = obj["msg"];

						//generamos una instancia del mensaje en base al message type.

						if (jMessage != null)
						{
							var type = jMessage["type"].ToString();

							switch (type.ToLower())
							{
								case "chat":
								case "handoff":
								case "survey":
									message = new Social.WhatsApp.WhatsAppTextMessage();
									break;
								case "image":
									message = new Social.WhatsApp.WhatsAppImageMessage();
									break;
								case "video":
									message = new Social.WhatsApp.WhatsAppVideoMessage();
									break;
								case "audio":
								case "ptt":
									message = new Social.WhatsApp.WhatsAppAudioMessage();
									break;
								case "document":
								case "doc":
									message = new Social.WhatsApp.WhatsAppDocumentMessage();
									break;
								case "location":
									message = new Social.WhatsApp.WhatsAppLocationMessage();
									break;
								case "vcard":
									message = new Social.WhatsApp.WhatsAppVCardMessage();
									break;
								case "sticker":
									message = new Social.WhatsApp.WhatsAppStickerMessage();
									break;
								case "interactive":
									{
										var interactiveMessage = new Social.WhatsApp.WhatsAppInteractiveMessage();
										var jInteractive = (JObject) jMessage["interactive"];
										var interactiveType = jInteractive["type"].ToString();

										if (interactiveType.Equals("button_reply"))
										{
											var jButtonReply = (JObject) jInteractive["button_reply"];
											interactiveMessage.Payload = jButtonReply["id"].ToString();
											interactiveMessage.Body = jButtonReply["title"].ToString();
											interactiveMessage.EmptyBody = false;
										}
										else if (interactiveType.Equals("list_reply"))
										{
											var jButtonReply = (JObject) jInteractive["list_reply"];
											interactiveMessage.Payload = jButtonReply["id"].ToString();
											interactiveMessage.Body = jButtonReply["title"].ToString();
											if (jButtonReply["description"] != null &&
												jButtonReply["description"].Type == JTokenType.String &&
												jButtonReply["description"].ToString().Length > 0)
											{
												interactiveMessage.Body += Environment.NewLine + jButtonReply["description"].ToString();
											}
											interactiveMessage.EmptyBody = false;
										}

										message = interactiveMessage;
									}
									break;
								case "button":
									{
										var payloadMessage = new Social.WhatsApp.WhatsAppPayloadMessage();
										var jButton = (JObject) jMessage["button"];
										var buttonPayload = GetButtonMessagePayload(jButton["payload"].ToString());

										if (!string.IsNullOrEmpty(buttonPayload.CampaignId))
											payloadMessage.PayloadCampaignId = buttonPayload.CampaignId;
										if (!string.IsNullOrEmpty(buttonPayload.Timestamp))
											payloadMessage.PayloadTimeStamp = buttonPayload.Timestamp;
										payloadMessage.Payload = buttonPayload.Body;
										payloadMessage.Body = jButton["text"].ToString();
										payloadMessage.EmptyBody = false;

										message = payloadMessage;
									}
									break;
								case "order":
									{
										var orderMessage = new Social.WhatsApp.WhatsAppOrderMessage();
										var jOrder = (JObject) jMessage["order"];
										orderMessage.CatalogId = jOrder["catalog_id"].ToString();
										if (jOrder["text"] != null && jOrder["text"].Type == JTokenType.String)
										{
											orderMessage.Body = jOrder["text"].ToString();
											orderMessage.EmptyBody = false;
										}
										else
										{
											orderMessage.EmptyBody = true;
										}

										var jProductItems = (JArray) jOrder["product_items"];
										orderMessage.ProductItems = jProductItems.ToString();
										orderMessage.Count = jProductItems.Count;

										message = orderMessage;
									}
									break;
								case "payload":
									if (jMessage["payload"] != null && jMessage["payload"].Type == JTokenType.String)
										message = new Social.WhatsApp.WhatsAppPayloadMessage();
									else
										message = new Social.WhatsApp.WhatsAppTextMessage();
									break;
								default:
									Common.Tracer.TraceVerb("El tipo {0} no es un tipo de mensaje válido para convertir. Se ignora", type);
									return (null, null);
							}

							message.SocialConversationID = obj["chat"].ToString();
							message.SocialMessageID = jMessage["id"].ToString();
							message.Date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds((long) jMessage["timestamp"]).ToLocalTime();

							if (jMessage["longtimestamp"] != null && jMessage["longtimestamp"].Type == JTokenType.Integer)
								message.Date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds((long) jMessage["longtimestamp"]).ToLocalTime();

							var jFrom = (JObject) obj["from"];
							if (jMessage["incoming"] == null ||
								(jMessage["incoming"].Type == JTokenType.Boolean && jMessage["incoming"].ToObject<bool>()))
							{
								message.PostedBy = GetWhatsAppUser(jFrom, service);

								if (Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendProfile &&
									DomainModel.SystemSettings.Instance.ExtendedProfilesFields != null &&
									DomainModel.SystemSettings.Instance.ExtendedProfilesFields.Length > 0 &&
									jFrom["ext"] != null && jFrom["ext"].Type == JTokenType.Object)
								{
									var jExt = (JObject) jFrom["ext"];
									var profileExtendedFields = new Dictionary<string, string>();
									foreach (var extendedProfileField in DomainModel.SystemSettings.Instance.ExtendedProfilesFields)
									{
										if (jExt[extendedProfileField.Name] != null)
										{
											var currentType = jExt[extendedProfileField.Name].Type;
											if (currentType == JTokenType.Null)
												continue;

											switch (extendedProfileField.DataType)
											{
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.String:
													profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToString();
													break;
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.Number:
													if (currentType == JTokenType.Integer)
													{
														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<int>().ToString();
													}
													else if (currentType == JTokenType.Float)
													{
														profileExtendedFields[extendedProfileField.Name] = System.Convert.ToInt32(jExt[extendedProfileField.Name].ToObject<double>()).ToString();
													}
													else if (currentType == JTokenType.String)
													{
														try
														{
															profileExtendedFields[extendedProfileField.Name] = System.Convert.ToInt32(jExt[extendedProfileField.Name].ToString()).ToString();
														}
														catch { }
													}
													break;
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.Dropdown:
													if (currentType != JTokenType.Integer && currentType != JTokenType.String)
														continue;

													profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToString();
													break;
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.Float:
													if (currentType == JTokenType.Integer)
													{
														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<int>().ToString();
													}
													else if (currentType == JTokenType.Float)
													{
														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<double>().ToString();
													}
													else if (currentType == JTokenType.String)
													{
														try
														{
															profileExtendedFields[extendedProfileField.Name] = System.Convert.ToDouble(jExt[extendedProfileField.Name].ToString()).ToString();
														}
														catch { }
													}
													break;
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.Boolean:
													if (currentType == JTokenType.Boolean)
													{
														profileExtendedFields[extendedProfileField.Name] = jExt[extendedProfileField.Name].ToObject<bool>().ToString();
													}
													else if (currentType == JTokenType.Integer)
													{
														profileExtendedFields[extendedProfileField.Name] = System.Convert.ToBoolean(jExt[extendedProfileField.Name].ToObject<int>()).ToString();
													}
													else if (currentType == JTokenType.String)
													{
														var boolText = jExt[extendedProfileField.Name].ToString();
														if (boolText.Equals("true", StringComparison.InvariantCulture) || boolText.Equals("1"))
															profileExtendedFields[extendedProfileField.Name] = true.ToString();
														else if (boolText.Equals("false", StringComparison.InvariantCulture) || boolText.Equals("0"))
															profileExtendedFields[extendedProfileField.Name] = false.ToString();
													}
													break;
												case DomainModel.ExtendedField.ExtendedFieldDataTypes.Date:
													DateTime? date = null;
													try
													{
														if (currentType == JTokenType.Integer)
															date = Common.Conversions.UnixTimeToDateTime(jExt[extendedProfileField.Name].ToObject<int>());
														else if (currentType == JTokenType.String)
															date = DateTime.Parse(jExt[extendedProfileField.Name].ToString());
														else if (currentType == JTokenType.Date)
															date = jExt[extendedProfileField.Name].ToObject<DateTime>();

														if (date != null)
															profileExtendedFields[extendedProfileField.Name] = date.Value.ToString("o");
													}
													catch { }
													break;
												default:
													break;
											}
										}
									}

									if (profileExtendedFields.Count > 0)
									{
										Common.Tracer.TraceVerb("Se está informando campos extendidos para el usuario {0}", message.PostedBy.ID);
										message.PostedBy.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter] = Common.Conversions.ConvertDictionaryToString(profileExtendedFields);
									}
									else
									{
										Common.Tracer.TraceVerb("No se pudo obtener los campos extendidos para el usuario {0}: {1}", message.PostedBy.ID, jExt.ToString());
									}
								}

								if (jMessage["payload"] != null)
								{
									if (jMessage["payload"].Type == JTokenType.Object ||
										jMessage["payload"].Type == JTokenType.String)
									{
										var buttonPayload = GetButtonMessagePayload(jMessage["payload"].ToString());

										if (!string.IsNullOrEmpty(buttonPayload.CampaignId))
											message.PayloadCampaignId = buttonPayload.CampaignId;
										if (!string.IsNullOrEmpty(buttonPayload.Timestamp))
											message.PayloadTimeStamp = buttonPayload.Timestamp;
										message.Payload = buttonPayload.Body;
									}
								}

								if (jMessage["referral"] != null &&
									jMessage["referral"].Type == JTokenType.Object)
								{
									var jReferral = (JObject) jMessage["referral"];
									if (jReferral["source_url"] != null &&
										jReferral["source_url"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceUrlParameter] = jReferral["source_url"].ToString();

									if (jReferral["source_id"] != null &&
										jReferral["source_id"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceIdParameter] = jReferral["source_id"].ToString();

									if (jReferral["source_type"] != null &&
										jReferral["source_type"].Type == JTokenType.String)
										message.Parameters[WhatsAppMessage.ReferralSourceTypeParameter] = jReferral["source_type"].ToString();
								}

								if (jMessage["events"] != null && jMessage["events"].Type == JTokenType.Array)
								{
									var jEvents = (JArray) jMessage["events"];
									if (jEvents.Count > 0)
										message.Parameters[WhatsAppMessage.PreviousBotEventsParameter] = jEvents.ToString();
								}

								InstanceParametersByService(message, service);

								if (jMessage["campaign"] != null &&
									jMessage["campaign"].Type == JTokenType.String)
								{
									message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = jMessage["campaign"].ToString();
								}
							}

							var typesWithAttachment = new string[] { "image", "video", "audio", "document", "doc", "ptt", "location", "sticker" };
							if (typesWithAttachment.Contains(type))
							{
								var attachment = await RetrieveAttachmentFromMessage((JObject) jMessage, configuration.ServiceIntegrationType, service);

								if (attachment != null)
								{
									message.HasAttach = true;
									message.Attachments = new DomainModel.Attachment[] { attachment };
								}
								else
								{
									message.HasAttach = false;
									message.Attachments = null;
								}

								if (jMessage["caption"] != null)
								{
									message.Body = jMessage["caption"].ToString();
									if (!string.IsNullOrEmpty(message.Body) && !message.Body.Equals("--Vacío--"))
										message.EmptyBody = false;
								}
								else
								{
									message.Body = null;
								}
							}

							if (type.Equals("chat") || type.Equals("payload"))
							{
								if (jMessage["body"] != null && jMessage["body"].Type == JTokenType.String)
								{
									message.Body = jMessage["body"].ToString();
								}

								if (string.IsNullOrEmpty(message.Body))
								{
									message.Body = "--Vacío--";
									message.EmptyBody = true;
								}
							}
							else if (type.Equals("location"))
							{
								var locationMessage = message as Social.WhatsApp.WhatsAppLocationMessage;
								locationMessage.HasCoordinates = true;
								locationMessage.Coordinates = new DomainModel.GeoCoordinate(jMessage["lat"].ToObject<double>(), jMessage["lng"].ToObject<double>());
								locationMessage.Body = string.Format("{0},{1}",
									locationMessage.Coordinates.Latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
									locationMessage.Coordinates.Longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

								if (locationMessage.Attachment != null)
								{
									locationMessage.Attachment.MimeType = "image/jpeg";
									locationMessage.Attachment.FileName = "Location.jpg";
								}
							}
							else if (type.Equals("vcard"))
							{
								var attachment = new DomainModel.Attachment(0, 1);
								attachment.MimeType = "text/x-vcard";
								attachment.SocialID = null;
								attachment.FileName = "Contact" + DomainModel.MimeTypeMap.GetExtension(attachment.MimeType);
								attachment.Data = Encoding.UTF8.GetBytes(jMessage["body"].ToString());
								attachment.FileSize = attachment.Data.Length;
								message.HasAttach = true;
								message.Attachments = new DomainModel.Attachment[] { attachment };

								if (jMessage["subtype"] != null)
									message.Body = string.Format("Contacto: {0}", jMessage["subtype"]);
							}
							else if (type.Equals("handoff"))
							{
								Common.Tracer.TraceVerb("Vino un handoff en la conversación {0}. Se intenta obtener datos del mensaje que generó la derivación", message.SocialConversationID);

								var handoffMessage = await whatsAppService.RetrieveMovistarPreviousMessage(message.SocialConversationID);

								if (handoffMessage != null)
								{
									var payload = message.Payload;
									string extendedFields = null;
									if (message.PostedBy.Parameters.ContainsKey(DomainModel.SocialUserProfile.ExtendedFieldsParameter))
										extendedFields = message.PostedBy.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter];
									message = handoffMessage;
									message.Payload = payload;
									if (!string.IsNullOrEmpty(extendedFields))
									{
										message.PostedBy.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter] = extendedFields;
										Common.Tracer.TraceVerb("Se obtuvo el mensaje {0} para el handoff para la conversación {1} y nos quedamos con los datos extendidos de perfil del mensaje de handoff", message, message.SocialConversationID);
									}
									else
									{
										Common.Tracer.TraceVerb("Se obtuvo el mensaje {0} para el handoff para la conversación {1} sin datos extendidos", message, message.SocialConversationID);
									}
								}
								else
								{
									Common.Tracer.TraceVerb("No se pudo obtener el mensaje para el handoff para la conversación {0}", message.SocialConversationID);
								}

								message.Parameters[Social.WhatsApp.WhatsAppMessage.IsHandoffParameter] = true.ToString();
							}
							else if (type.Equals("survey"))
							{
								Common.Tracer.TraceVerb("Vino un survey en la conversación {0}. Se intenta obtener datos del mensaje que generó la derivación", message.SocialConversationID);

								var handoffMessage = await whatsAppService.RetrieveMovistarPreviousMessage(message.SocialConversationID);

								if (handoffMessage != null)
								{
									var payload = message.Payload;
									message = handoffMessage;
									message.Payload = payload;
									Common.Tracer.TraceVerb("Se obtuvo el mensaje {0} para el survey de la conversación {1}", message, message.SocialConversationID);
								}
								else
								{
									Common.Tracer.TraceVerb("No se pudo obtener el mensaje para el handoff para la conversación {0}", message.SocialConversationID);
								}

								message.Parameters[Social.WhatsApp.WhatsAppMessage.IsSurveyHandoffParameter] = true.ToString();
							}
						}
					}
					break;
			}

			if (message != null)
			{
				if (DateTime.Now.Date != lastBotNumbersDownloadedTime.Date &&
					!isDownloadingBotNumbers)
				{
					isDownloadingBotNumbers = true;

					await DownloadBotNumbers();

					lastBotNumbersDownloadedTime = DateTime.Now;

					isDownloadingBotNumbers = false;
				}

				if (botPhoneNumbers != null &&
					message.PostedBy != null &&
					botPhoneNumbers.Contains(message.PostedBy.ID))
				{
					Common.Tracer.TraceInfo("Se ignora el mensaje porque proviene del número de teléfono {0} que es un bot", message.PostedBy.ID);
				}
			}

			return (message, statusMessage);
		}

		#endregion

		#region Private Methods

		private static DomainModel.Attachment[] ConvertContactsToVCard(JArray jMessageContacts)
		{
			var attachments = new List<DomainModel.Attachment>();
			foreach (JObject jContact in jMessageContacts)
			{
				var attachment = new DomainModel.Attachment(0, (byte) (attachments.Count + 1));
				attachment.MimeType = "text/x-vcard";
				attachment.SocialID = null;
				attachment.FileName = "Contact" + DomainModel.MimeTypeMap.GetExtension(attachment.MimeType);

#if NETCOREAPP
				var contents = jContact.ToString();
				attachment.Parameters["MustConvertToVCard"] = true.ToString();
#else
				var vcard = new MixERP.Net.VCards.VCard();

				if (jContact["emails"] != null &&
					jContact["emails"].Type == JTokenType.Array)
				{
					var jEmails = (JArray) jContact["emails"];
					if (jEmails.Count > 0)
					{
						var emails = new List<MixERP.Net.VCards.Models.Email>();
						foreach (JObject jEmail in jEmails)
						{
							emails.Add(new MixERP.Net.VCards.Models.Email()
							{
								EmailAddress = jEmail["email"].ToString()
							});
						}

						vcard.Emails = emails;
					}
				}

				if (jContact["birthday"] != null)
				{
					if (jContact["birthday"].Type == JTokenType.Date)
					{
						vcard.BirthDay = jContact["birthday"].ToObject<DateTime>();
					}
					else if (jContact["birthday"].Type == JTokenType.String)
					{
						try
						{
							vcard.BirthDay = DateTime.Parse(jContact["birthday"].ToString());
						}
						catch { }
					}
				}

				if (jContact["addresses"] != null &&
					jContact["addresses"].Type == JTokenType.Array)
				{
					var jAddresses = (JArray) jContact["addresses"];
					if (jAddresses.Count > 0)
					{
						var addresses = new List<MixERP.Net.VCards.Models.Address>();
						foreach (JObject jAddress in jAddresses)
						{
							var phoneType = string.Empty;
							if (jAddress["type"] != null && jAddress["type"].Type == JTokenType.String)
								phoneType = jAddress["type"].ToString();

							addresses.Add(new MixERP.Net.VCards.Models.Address()
							{
								Country = jAddress["country"]?.ToString(),
								Region = jAddress["city"]?.ToString(),
								Street = jAddress["street"]?.ToString(),
								Locality = jAddress["state"]?.ToString(),
								PostalCode = jAddress["zip"]?.ToString(),
								Type = phoneType.Equals("WORK") ?
									MixERP.Net.VCards.Types.AddressType.Work :
									MixERP.Net.VCards.Types.AddressType.Home
							});
						}

						vcard.Addresses = addresses;
					}
				}

				if (jContact["name"] != null &&
					jContact["name"].Type == JTokenType.Object)
				{
					var jName = (JObject) jContact["name"];
					vcard.FirstName = jName["first_name"]?.ToString();
					vcard.LastName = jName["last_name"]?.ToString();
					vcard.MiddleName = jName["middle_name"]?.ToString();
					vcard.FormattedName = jName["formatted_name"]?.ToString();
				}

				if (jContact["phones"] != null &&
					jContact["phones"].Type == JTokenType.Array)
				{
					var jPhones = (JArray) jContact["phones"];
					if (jPhones.Count > 0)
					{
						var phones = new List<MixERP.Net.VCards.Models.Telephone>();
						foreach (JObject jPhone in jPhones)
						{
							var phone = new MixERP.Net.VCards.Models.Telephone();
							phone.Number = jPhone["phone"]?.ToString();
							var phoneType = string.Empty;
							if (jPhone["type"] != null && jPhone["type"].Type == JTokenType.String)
								phoneType = jPhone["type"].ToString();

							switch (phoneType)
							{
								case "WORK":
									phone.Type = MixERP.Net.VCards.Types.TelephoneType.Work;
									break;
								case "HOME":
									phone.Type = MixERP.Net.VCards.Types.TelephoneType.Home;
									break;
								default:
									phone.Type = MixERP.Net.VCards.Types.TelephoneType.Cell;
									break;
							}

							phones.Add(phone);
						}

						vcard.Telephones = phones;
					}
				}

				var contents = MixERP.Net.VCards.Serializer.VCardSerializer.Serialize(vcard);
#endif

				attachment.Data = Encoding.UTF8.GetBytes(contents);
				attachment.FileSize = attachment.Data.Length;
				attachments.Add(attachment);
			}

			return attachments.ToArray();
		}

		private static async Task DownloadBotNumbers()
		{
			try
			{
				Common.Tracer.TraceInfo("Se descargará el listado de números de teléfonos de whatsapp que son Bots");

				using (var response = await httpClient.GetAsync("https://callback.ysocial.net/api/whatsapp/botnumbers"))
				{
					if (response.IsSuccessStatusCode)
					{
						var json = await response.Content.ReadAsStringAsync();
						var jPhoneNumbers = JArray.Parse(json);

						var phoneNumbers = new List<long>();
						foreach (JValue jValue in jPhoneNumbers)
						{
							try
							{
								var phoneNumber = jValue.ToString();
								phoneNumber = phoneNumber.Replace("-", string.Empty).Replace(" ", string.Empty);
								phoneNumbers.Add(long.Parse(phoneNumber));
							}
							catch { }
						}

						botPhoneNumbers = phoneNumbers.ToArray();

						Common.Tracer.TraceInfo("Se descargó el listado de números de teléfonos de whatsapp que son Bots: {0}", string.Join(", ", botPhoneNumbers));
					}
					else
					{
						if (response.Content != null)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string json = await sr.ReadToEndAsync();
								Yoizen.Common.Tracer.TraceError("Ocurrió un error descargando los números de whatsapp que son bots: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
							}
						}
						else
						{
							Yoizen.Common.Tracer.TraceError("Ocurrió un error descargando los números de whatsapp que son bots: {0}-{1}", response.ReasonPhrase, response.StatusCode);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Ocurrió un error descargando los números de whatsapp que son bots: {0}", ex);
			}
		}

		private static async Task DownloadAttachment(WhatsAppMessage message, string url)
		{
			await DownloadAttachment(message, url, null);
		}

		private static async Task DownloadAttachment(WhatsAppMessage message, string url, Dictionary<string, string> headers)
		{
			Yoizen.Common.Tracer.TraceVerb("Se irá a buscar el archivo adjunto a la url: {0}", url);

			var attachment = await DownloadAttachment(url, headers);

			if (attachment != null)
			{
				message.HasAttach = true;
				message.Attachments = new[] { attachment };
			}
		}

		private static async Task<DomainModel.Attachment> DownloadAttachment(string url, Dictionary<string, string> headers)
		{
			Yoizen.Common.Tracer.TraceVerb("Se irá a buscar el archivo adjunto a la url: {0}", url);

			DownloadedFileContents attachContents = null;
			
			try
			{
				var request = new HttpRequestMessage(HttpMethod.Get, url);

				if (headers != null && headers.Count > 0)
				{
					foreach (var header in headers)
					{
						request.Headers.TryAddWithoutValidation(header.Key, header.Value);
					}
				}

				using (HttpResponseMessage response = await httpClient.SendAsync(request))
				{
					if (response.IsSuccessStatusCode)
					{
						var contentType = response.Content.Headers.ContentType?.ToString();
						var contentDisposition = response.Content.Headers.ContentDisposition?.ToString();

						using (var stream = await response.Content.ReadAsStreamAsync())
						using (var ms = new MemoryStream())
						{
							await stream.CopyToAsync(ms);
							attachContents = new DownloadedFileContents()
							{
								Content = ms.ToArray(),
								ContentType = contentType,
								ContentDisposition = contentDisposition
							};
						}
					}
					else
					{
						if (response.Content != null)
						{
							using (var stream = await response.Content.ReadAsStreamAsync())
							using (var reader = new StreamReader(stream))
							{
								var error = await reader.ReadToEndAsync();
								Common.Tracer.TraceError("Ocurrió un error descargando el archivo desde {0}: {1}", url, error);
							}
						}
						else
						{
							Common.Tracer.TraceError("Ocurrió un error descargando el archivo desde {0}: {1}", url, response.StatusCode);
						}

						return null;
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("No se pudo descargar el archivo adjunto: {0}", ex);
				return null;
			}

			if (attachContents != null)
			{
				string fileName = null;
				try
				{
					if (!string.IsNullOrEmpty(attachContents.ContentDisposition) &&
						attachContents.ContentDisposition.StartsWith("attachment; filename=", StringComparison.InvariantCultureIgnoreCase))
					{
						fileName = attachContents.ContentDisposition.Substring(attachContents.ContentDisposition.IndexOf("=") + 1);
					}
				}
				catch { }

				var attachment = new DomainModel.Attachment(0, 1)
				{
					SocialID = null,
					MimeType = attachContents.ContentType,
					FileName = fileName,
					FileSize = attachContents.Content.Length,
					Data = attachContents.Content
				};

				CheckAttachmentFileName(attachment, url);

				return attachment;
			}

			return null;
		}

		/// <summary>
		/// Convierte el payload de un botón en un <see cref="HSMButtonPayload"/>
		/// </summary>
		/// <param name="payload">El payload del botón</param>
		/// <returns>Un <see cref="HSMButtonPayload"/></returns>
		/// <remarks>
		/// Cuando falla la desearilaización del payload, se devuelve un <see cref="HSMButtonPayload"/> con el campo <see cref="HSMButtonPayload.Body"/> con el valor del payload original.
		/// </remarks>
		private static HSMButtonPayload GetButtonMessagePayload(string payload)
		{
			try
			{
				var settings = new JsonSerializerSettings()
				{
					MissingMemberHandling = MissingMemberHandling.Error
				};
				var buttonPayload = JsonConvert.DeserializeObject<HSMButtonPayload>(payload, settings);
				return buttonPayload;
			}
			catch (JsonException ex)
			{
				Tracer.TraceError("El payload no pudo ser deserializado, el mismo no tiene la estructura esperada o es viejo. Se devuelve el original: {0}", ex);
				return new HSMButtonPayload()
				{
					Body = payload
				};
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error inesperado al deserializar el Payload: {0}", ex);
				return new HSMButtonPayload()
				{
					Body = payload
				};
			}
		}

		private static async Task<DomainModel.Attachment> DownloadAttachment(string url)
		{
			return await DownloadAttachment(url, null);
		}

		private static void CheckAttachmentFileName(DomainModel.Attachment attachment, string url)
		{
			if (string.IsNullOrEmpty(attachment.FileName) && !string.IsNullOrEmpty(url))
			{
				Uri uri = new Uri(url);
				try
				{
					attachment.FileName = Path.GetFileName(uri.LocalPath);
				}
				catch { }
			}

			if ((string.IsNullOrEmpty(attachment.FileName) || attachment.FileName.IndexOf(".") == -1) &&
				!string.IsNullOrEmpty(attachment.MimeType))
			{
				string extension = null;
				string defaultFileNameWithoutExtension = null;

				if (attachment.MimeType.StartsWith("image/") ||
					attachment.MimeType.StartsWith("audio/") ||
					attachment.MimeType.StartsWith("video/") ||
					attachment.MimeType.StartsWith("document/") ||
					attachment.MimeType.StartsWith("text/"))
				{
					var index = attachment.MimeType.IndexOf(";");
					if (index >= 0)
					{
						attachment.MimeType = attachment.MimeType.Substring(0, index);
					}
				}

				switch (attachment.MimeType.ToLower())
				{
					case "image/jpeg":
					case "image/jpg":
						extension = ".jpg";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/bmp":
						extension = ".bmp";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/gif":
						extension = ".gif";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/png":
						extension = ".png";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/webp":
						extension = ".webp";
						defaultFileNameWithoutExtension = "image";
						break;
					case "audio/aac":
						extension = ".aac";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/mp4":
						extension = ".mp4";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/mpeg":
						extension = ".mp3";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/ogg":
					case "audio/opus":
						extension = ".ogg";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/wav":
						extension = ".wav";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/x-ms-wma":
						extension = ".wma";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "video/avi":
						extension = ".avi";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/mp4":
						extension = ".mp4";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/mpeg":
						extension = ".mpeg";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/quicktime":
						extension = ".mov";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/x-flv":
						extension = ".flv";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/x-ms-asf":
						extension = ".asf";
						defaultFileNameWithoutExtension = "video";
						break;
					case "text/plain":
						extension = ".txt";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
						extension = ".xlsx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/excel":
					case "application/vnd.ms-excel":
					case "application/x-msexcel":
						extension = ".xls";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/pdf":
						extension = ".pdf";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
						extension = ".docx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/msword":
						extension = ".doc";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.presentationml.slideshow":
					case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
					case "application/vnd.openxmlformats-officedocument.presentationml.slide":
					case "application/powerpoint":
					case "application/mspowerpoint":
					case "application/vnd.ms-powerpoint":
					case "application/x-mspowerpoint":
						extension = ".pptx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/x-compressed":
					case "application/x-zip-compressed":
					case "application/zip":
					case "multipart/x-zip":
						extension = ".zip";
						defaultFileNameWithoutExtension = "document";
						break;
					default:
						break;
				}

				if (extension != null)
				{
					if (string.IsNullOrEmpty(attachment.FileName))
						attachment.FileName = string.Concat(defaultFileNameWithoutExtension, extension);
					else
						attachment.FileName = string.Concat(attachment.FileName, extension);
				}
			}
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="User"/> con los datos de un usuario de whatsapp
		/// </summary>
		/// <param name="from">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la informacion del contacto de WhatsApp</param>
		/// <returns>Un <see cref="User"/> con el usuario de WhatsApp</returns>
		private static DomainModel.SocialUser GetWhatsAppUser(Newtonsoft.Json.Linq.JObject from, DomainModel.Service service)
		{
			var userId = System.Convert.ToInt64(from["id"].ToString().Split('@')[0]);
			string displayName = null;
			if (from["name"] != null &&
				from["name"].Type == JTokenType.String)
				displayName = from["name"].ToString();
			string businessData = null;
			if (from["businessData"] != null &&
				from["businessData"].Type == JTokenType.String)
				businessData = from["businessData"].ToString();

			return GetWhatsAppUser(userId, displayName, service, businessData);
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="User"/> con los datos de un usuario de whatsapp
		/// </summary>
		/// <param name="from">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la informacion del contacto de WhatsApp</param>
		/// <returns>Un <see cref="User"/> con el usuario de WhatsApp</returns>
		private static DomainModel.SocialUser GetWhatsAppUser(long userId, string displayName, DomainModel.Service service, string businessData = null)
		{
			var socialUser = DAL.SocialUserDAO.GetOne(userId, DomainModel.SocialServiceTypes.WhatsApp, false, false);

			if (socialUser != null)
			{
				socialUser.ParametersByService[service.ID] = DAL.SocialUserServiceDAO.GetOneBySocialUserAndService(service.ID, socialUser);
				socialUser.UpdateDisplayName(displayName);

				if (!string.IsNullOrEmpty(businessData))
				{
					if (DomainModel.SystemSettings.Instance.IsBusinessDataValid(businessData))
						socialUser.UpdateBusinessData(businessData);
					else
						Common.Tracer.TraceInfo("Los datos de negocio {0} no son válidos", businessData);
				}
			}
			else
			{
				socialUser = new Social.WhatsApp.User(userId);

				if (!string.IsNullOrEmpty(displayName))
				{
					socialUser.Name = socialUser.DisplayName = displayName;
				}
				else
				{
					socialUser.Name = socialUser.DisplayName = socialUser.ID.ToString();
				}

				if (!string.IsNullOrEmpty(businessData))
				{
					if (DomainModel.SystemSettings.Instance.IsBusinessDataValid(businessData))
						socialUser.BusinessData = businessData;
					else
						Common.Tracer.TraceInfo("Los datos de negocio {0} no son válidos", businessData);
				}			
			}

			return socialUser;
		}

		/// <summary>
		/// Instancia los ParametersByService en caso de ser NULL
		/// </summary>
		/// <param name="message"></param>
		/// <param name="service"></param>
		private static void InstanceParametersByService(Message message, Service service)
		{

			if (!message.PostedBy.ParametersByService.TryGetValue(service.ID, out var parameters) || parameters == null)
			{
				message.PostedBy.ParametersByService[service.ID] = new DomainModel.SocialUserServiceParameters();
			}

			if (!message.PostedBy.ParametersByService[service.ID].ContainsKey(Social.WhatsApp.User.ChatIDParameter) ||
				!message.PostedBy.ParametersByService[service.ID][Social.WhatsApp.User.ChatIDParameter].Equals(message.SocialConversationID))
			{
				message.PostedBy.ParametersByService[service.ID][Social.WhatsApp.User.ChatIDParameter] = message.SocialConversationID;
			}
		}

		/// <summary>
		/// Devuelve un <see cref="DomainModel.Attachment"/> que contiene el archivo adjunto de un mensaje tipo imagen.
		/// </summary>
		/// <param name="msg">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la informacion del mensaje de WhatsApp</param>
		/// <returns>Un <see cref="DomainModel.Attachment"/> con la imagen que contiene el mensaje</returns>
		private static async Task<DomainModel.Attachment> RetrieveAttachmentFromMessage(Newtonsoft.Json.Linq.JObject msg, DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes integrationType, DomainModel.Service service)
		{
			DomainModel.Attachment attachment = null;

			var configuration = service.ServiceConfiguration as WhatsAppServiceConfiguration;

			if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback ||
				integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar)
			{
				if (msg["url"] != null)
				{
					if (msg["url"].Type == JTokenType.String)
					{
						var url = msg["url"].ToString();
						if (!string.IsNullOrEmpty(url))
						{
							attachment = await DownloadAttachment(url);
							
							if (attachment != null &&
								!string.IsNullOrEmpty(attachment.MimeType) &&
								attachment.MimeType.Equals("binary/octet-stream") &&
								msg["mimeType"] != null &&
								msg["mimeType"].Type == JTokenType.String)
							{
								var mimeType = msg["mimeType"].ToString();
								var index = mimeType.IndexOf(";");
								if (index >= 0)
									mimeType = mimeType.Substring(0, index).Trim();

								attachment.MimeType = mimeType;
							}

							if (attachment != null &&
								msg["fileName"] != null &&
								msg["fileName"].Type == JTokenType.String)
							{
								var filename = msg["fileName"].ToString();
								if (!string.IsNullOrEmpty(filename))
									attachment.FileName = msg["fileName"].ToString();
							}
						}
					}
				}
				else
				{
					if (msg["data"] != null)
					{
						var body = msg["data"].ToString();
						if (body.StartsWith("data:"))
						{
							var index = body.IndexOf(",");
							body = body.Substring(index + 1);
						}
						byte[] data = global::System.Convert.FromBase64String(body);

						string mimeType = string.Empty;
						if (msg["mimeType"] != null &&
							msg["mimeType"].Type == JTokenType.String)
						{
							mimeType = msg["mimeType"].ToString();
							var index = mimeType.IndexOf(";");
							if (index >= 0)
								mimeType = mimeType.Substring(0, index).Trim();
						}

						attachment = new DomainModel.Attachment(0, 1);
						attachment.SocialID = null;
						attachment.MimeType = mimeType;
						if (!string.IsNullOrEmpty(mimeType))
							attachment.FileName = string.Format("File{0}", DomainModel.MimeTypeMap.GetExtension(mimeType));
						else
							attachment.FileName = "File";
						
						if (msg["fileName"] != null &&
							msg["fileName"].Type == JTokenType.String)
						{
							var filename = msg["fileName"].ToString();
							if (!string.IsNullOrEmpty(filename))
								attachment.FileName = msg["fileName"].ToString();
						}

						attachment.FileSize = data.Length;
						attachment.Data = data;
					}
				}
			}

			return attachment;
		}

		#endregion
	}
}