﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProductName>ySocial</ProductName>
    <CompanyName>Yoizen</CompanyName>
    <StartYear>2011</StartYear>
    <VersionFile>$(MSBuildProjectDirectory)\.build\version.txt</VersionFile>
    <AssemblyInfoFile>$(MSBuildProjectDirectory)\GlobalAssemblyInfo.cs</AssemblyInfoFile>
    <MSBuildCommunityTasksPath>$(MSBuildProjectDirectory)\.build</MSBuildCommunityTasksPath>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <VersionFromPipeline Condition="'$(VersionFromPipeline)' == ''">$([System.IO.File]::ReadAllText('$(VersionFile)').Trim())</VersionFromPipeline>
    <MSBuildPath Condition="'$(MSBuildPath)' == ''">$(MSBuildBinPath)</MSBuildPath>
  </PropertyGroup>

  <!-- Importar MSBuild Community Tasks -->
  <Import Project="$(MSBuildCommunityTasksPath)\MSBuild.Community.Tasks.targets"/>
  
  <Target Name="Version">
    <!-- Parsear componentes desde el parámetro -->
    <PropertyGroup>
      <Major>$(VersionFromPipeline.Split('.')[0])</Major>
      <Minor>$(VersionFromPipeline.Split('.')[1])</Minor>
      <Build>$(VersionFromPipeline.Split('.')[2])</Build>
      <Revision>$(VersionFromPipeline.Split('.')[3])</Revision>
      
      <Version>$(Major).$(Minor)</Version>
      <FileVersion>$(Major).$(Minor).$(Build).$(Revision)</FileVersion>
      <InformationalVersion>$(FileVersion)</InformationalVersion>
      
      <!-- Calcular versión anterior -->
      <PreviousVersionMinor>$([MSBuild]::Subtract($(Minor), 1))</PreviousVersionMinor>
      <PreviousVersion>$(Major).$(PreviousVersionMinor).0.0</PreviousVersion>
      
      <Year>$([System.DateTime]::Now.Year)</Year>
      <CopyrightText>Copyright $(CompanyName) © $(StartYear)-$(Year). All rights reserved.</CopyrightText>
    </PropertyGroup>

    <!-- Guardar nueva versión -->
    <WriteLinesToFile File="$(VersionFile)" Lines="$(FileVersion)" Overwrite="true"/>

    <!-- Generar AssemblyInfo -->
    <WriteLinesToFile 
      File="$(AssemblyInfoFile)"
      Lines="// &lt;auto-generated/&gt;
[assembly: System.Reflection.AssemblyVersion(&quot;$(Version)&quot;)]
[assembly: System.Reflection.AssemblyFileVersion(&quot;$(FileVersion)&quot;)]
[assembly: System.Reflection.AssemblyInformationalVersion(&quot;$(InformationalVersion)&quot;)]
[assembly: System.Reflection.AssemblyConfiguration(&quot;$(BuildConfiguration)&quot;)]
[assembly: System.Reflection.AssemblyCompany(&quot;$(CompanyName) S.A.&quot;)]
[assembly: System.Reflection.AssemblyProduct(&quot;$(ProductName)&quot;)]
[assembly: System.Reflection.AssemblyCopyright(&quot;$(CopyrightText)&quot;)]
[assembly: System.Reflection.AssemblyTrademark(&quot;&quot;)]
[assembly: System.Reflection.AssemblyCulture(&quot;&quot;)]"
      Overwrite="true"
      Encoding="UTF-8"/>

    <!-- Mostrar información -->
    <Message Text="Versión anterior: $(PreviousVersion)" Importance="high"/>
    <Message Text="Nueva versión: $(FileVersion)" Importance="high"/>

    <!-- Actualizar archivos de configuración -->
    <ItemGroup>
      <FilesToUpdate Include="Yoizen.Social.Web\web.config;Yoizen.Social.Service\app.config;Yoizen.Social.Surveys.Service\app.config;Yoizen.Social.Exporter\app.config;Yoizen.Social.DB\Script.PostDeployment.sql" />
    </ItemGroup>

    <FileUpdate Files="@(FilesToUpdate)"
                Regex="$(PreviousVersion)"
                ReplacementText="$(Version).0.0" />
  </Target>
</Project>