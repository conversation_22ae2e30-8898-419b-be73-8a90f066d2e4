﻿<%@ Page Language="C#" MasterPageFile="~/Master.Master" AutoEventWireup="true" CodeBehind="MyProfile.aspx.cs" Inherits="Yoizen.Social.Web.Administration.MyProfile" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="Server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/UserSelection.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.filedrop.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.ui.widget.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.fileupload.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.fileupload-ui.js")%>'></script>
	<asp:Literal ID="literalScriptZXCVBN" runat="server" />
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Administration/MyProfile.js")%>'></script>
</asp:Content>
<asp:Content ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<span class="fa fa-2x fa-id-card"></span> <span data-i18n="administration-myprofile-title">Mi perfil</span>
</asp:Content>
<asp:Content ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<yoizen:Message ID="messageError" runat="server" Type="Error" Visible="false" />
	<asp:Panel ID="panelContent" runat="server">
		<div style="display: none">
			<div id="divChangePasswordDialog" class="seccion">
				<div class="title">
					<h2 id="h2ChangePasswordTitle" data-i18n="administration-myprofile-change_password-title">Cambiar contraseña</h2>
					<h2 id="h2MandatorySecondFactorTitle" style="display: none" data-i18n="administration-myprofile-mandatory-second-factor-modal-title">Configuración de doble factor</h2>
				</div>
				<div class="contents">
					<div id="divChangePasswordDialogLoading" class="subseccion" style="display:none"> 
						<div class="title">
							<h2 data-i18n="globals-loading">Cargando...</h2>
						</div>
						<div class="contents">
							<div style="text-align: center">
								<i class="fa fa-3x fa-spinner fa-pulse"></i>
							</div>
						</div>
					</div>
					<yoizen:Message ID="messageChangePasswordDialogError" runat="server" ClientIDMode="Static" Type="Error" style="display: none" LocalizationKey="[html]administration-myprofile-change_password-error">No se pudo cambiar la contraseña<span rel="moreinfo"></span></yoizen:Message>
					<yoizen:Message ID="messageChangePasswordDialogInfo" runat="server" ClientIDMode="Static" Type="Information" style="display: none" LocalizationKey="administration-myprofile-change_password-success">Se cambió la contraseña exitosamente</yoizen:Message>
					<yoizen:Message ID="messageFirstLoggin" runat="server" ClientIDMode="Static" Type="Information" style="display: none" LocalizationKey="administration-myprofile-change_password-first_login">Debido a que es su primer logueo, deberá cambiar la contraseña para continuar con el funcionamiento de ySocial</yoizen:Message>
					<yoizen:Message ID="messagePasswordExpired" runat="server" ClientIDMode="Static" Type="Information" style="display: none" LocalizationKey="administration-myprofile-change_password-expired">Su contraseña ha expirado. Para continuar con el funcionamiento de ySocial debe cambiarla</yoizen:Message>
					<yoizen:Message ID="messageMandatory2FA" runat="server" ClientIDMode="Static" Type="Information" style="display: none" LocalizationKey="administration-myprofile-mandatory-second-factor"></yoizen:Message>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" onclick="$.colorbox.close()" data-i18n="globals-close">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
			<div id="divChangeAvatar" class="seccion">
				<div class="title">
					<h2 data-i18n="administration-myprofile-avatar-title">Avatar</h2>
				</div>
				<div class="contents">
					<input type="file" id="fileUserAvatarFile" style="visibility: hidden; position: absolute; top: 0; left: 0" accept="image/*" />
					<input type="hidden" id="hiddenUserAvatarFile" />
					<input type="hidden" id="hiddenUserAvatarFileContentType" />
					<yoizen:Message ID="messageChangeAvatarInvalidFile" runat="server" ClientIDMode="Static" Type="Error" style="display: none" LocalizationKey="administration-myprofile-avatar-invalid_image">El archivo seleccionado no es una imagen cuadrada</yoizen:Message>
					<div id="divUserAvatarDropZone" class="filedropzone mtm"><div class="drag-title" data-i18n="globals-drag_file_here">Arrastre su archivo aquí o presione para buscar y seleccionar un archivo</div><div class="drop-title" data-i18n="globals-drop_file_here">Suelte su archivo aquí</div></div>
					<div id="divUserAvatarFileInfo" class="file file-onlypreview mtm" style="display: none">
						<div class="filename"></div>
						<div class="filepreview">
							<img alt="logo" />
						</div>
						<div class="uploadingprogress">
							<i class="fa fa-lg fa-spinner fa-pulse"></i>
						</div>
						<div class="removefile">
							<i class="fa fa-lg fa-times" title="Remover"></i>
						</div>
						<div class="uploadingfailed">
							<i class="fa fa-lg fa-exclamation-triangle"></i>
						</div>
					</div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" onclick="ChangeAvatarConfirm()" data-i18n="globals-accept">Aceptar</button>
						</label>
						<label class="uiButton uiButtonLarge">
							<button type="button" onclick="$.colorbox.close()" data-i18n="globals-close">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div class="profile-info">
			<asp:Panel ID="panelAvatar" runat="server" CssClass="avatar">
				<asp:Image ID="imageAvatar" runat="server" ClientIDMode="Static" />
				<div class="actions">
					<span class="fa fa-edit" title="Seleccionar foto de perfil" data-i18n-title="administration-myprofile-avatar-select" onclick="EditAvatar()"></span>
					<span class="fa fa-trash" title="Eliminar foto de perfil" data-i18n-title="administration-myprofile-avatar-clear" onclick="DeleteAvatar()"></span>
				</div>
			</asp:Panel>
			<div class="name">
				<asp:Literal ID="literalFullName" runat="server" />
			</div>
			<div class="username">
				<asp:Literal ID="literalUserName" runat="server" />
			</div>
		</div>
		<div class="profile-sections">
			<asp:Panel ID="panelChangePassword" runat="server" CssClass="seccion shadow">
				<div class="title">
					<h2 data-i18n="administration-myprofile-change_password-title">Cambiar contraseña</h2>
				</div>
				<div class="contents">
					<table class="uiInfoTable noBorder" border="0" width="100%">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-current_password">Contraseña actual</span>:</th>
							<td class="data">
								<div class="meter">
									<input type="password" id="textboxChangePasswordCurrent" maxlength="25" autocomplete="off" />
								</div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-new_password">Contraseña nueva</span>:</th>
							<td class="data">
								<div class="meter">
									<input type="password" id="textboxChangePassword" maxlength="25" autocomplete="off" data-i18n-popover='{
										"toggle": "popover",
										"html": true,
										"maxWidth": "400px",
										"trigger": "hover",
										"title": "administration-myprofile-new_password-popover-title",
										"content": "administration-myprofile-new_password-popover-content"
										}'
										data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="focus" title="Seguridad de contraseña" data-content="Debe especificar una contraseña. Utilice mayúsculas, números y/o símbolos para generar una contraseña más segura." />
									<meter min="0" max="4" id="meterChangePasswordStrength" value="0"></meter>
									<span id="spanChangePasswordStrength"></span>
								</div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-new_password_confirm">Confirmación</span>:</th>
							<td class="data">
								<div class="meter">
									<input type="password" id="textboxChangePasswordConf" maxlength="25" autocomplete="off" />
								</div>
							</td>
						</tr>
					</table>
					<div id="divChangePasswordError" class="validationerror" style="display: none"><div></div></div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" onclick="ChangePassword()" data-i18n="globals-accept">Aceptar</button>
						</label>
					</div>
				</div>
			</asp:Panel>
			<asp:Panel ID="panelAccessYFlow" runat="server" CssClass="seccion shadow">
				<div class="title">
					<h2 data-i18n="administration-myprofile-yflow-title">Acceso yFlow</h2>
				</div>
				<div class="contents">
					<table class="uiInfoTable noBorder" border="0" width="100%">
						<tr class="dataRow dataRowSeparator">
							<th class="label">Access Token:</th>
							<td class="data">
								<div class="meter">
									<span id="spanAccessTokenOk" runat="server" ClientIDMode="static" class="fa fa-lg fa-yes"></span>
									<span id="spanAccessTokenError" runat="server" ClientIDMode="static" class="fa fa-lg fa-no"></span>
								</div>
							</td>
						</tr>
					</table>
					<div id="divAccessYFlowError" class="validationerror" style="display: none"><div></div></div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" id="buttonLoginYFlow" data-i18n="administration-myprofile-yflow-login">Iniciar Sesion</button>
						</label>
					</div>
				</div>
			</asp:Panel>
			<div class="seccion shadow">
				<div class="title">
					<h2 data-i18n="administration-myprofile-regional-title">Configuración regional</h2>
				</div>
				<div class="contents">
					<table class="uiInfoTable noBorder" border="0" width="100%">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-regional-time_zone">Huso horario</span>:</th>
							<td class="data">
								<select id="selectDefaultTimeZone"></select>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-regional-language">Idioma y localización</span>:</th>
							<td class="data">
								<select id="selectLocale">
									<option value="es" data-i18n="globals-spanish">Español</option>
									<option value="pt" data-i18n="globals-portuguese">Portugués</option>
									<option value="en" data-i18n="globals-english">Inglés</option>
								</select>
							</td>
						</tr>
					</table>
					<div id="divRegionalSettingsSaveError" class="validationerror" style="display: none"><div></div></div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" onclick="SaveRegionalSettings()" data-i18n="globals-accept">Aceptar</button>
						</label>
					</div>
				</div>
			</div>
			<asp:Panel ID="panelAccessYFlowContingency" runat="server" CssClass="seccion shadow">
				<div class="title">
					<h2 data-i18n="administration-myprofile-yflow-contingency-title">Acceso yFlow Contingencia</h2>
				</div>
				<div class="contents">
					<table class="uiInfoTable noBorder" border="0" width="100%">
						<tr class="dataRow dataRowSeparator">
							<th class="label">Access Token:</th>
							<td class="data">
								<div class="meter">
									<span id="spanAccessTokenContingencyOk" runat="server" ClientIDMode="Static" class="fa fa-lg fa-yes"></span>
									<span id="spanAccessTokenContingencyError" runat="server" ClientIDMode="Static" class="fa fa-lg fa-no"></span>
								</div>
							</td>
						</tr>
					</table>
					<div id="divAccessYFlowContingencyError" class="validationerror" style="display: none"><div></div></div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" id="buttonLoginYFlowContingency" data-i18n="login-login">Iniciar Sesión</button>
						</label>
					</div>
				</div>
			</asp:Panel>
			<div class="seccion shadow">
				<div class="title">
					<h2 data-i18n="administration-myprofile-two_factor-title">Autenticación de 2 factores</h2>
				</div>
				<div class="contents">
					<table class="uiInfoTable noBorder" border="0" width="100%" id="tableTwoFactor">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="administration-myprofile-two_factor-enable">Habilitar</span>:</th>
							<td class="data">
								<input type="checkbox" id="checkboxTwoFactorEnabled" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trTwoFactorKey">
							<th class="label"><span data-i18n="administration-myprofile-two_factor-key">Clave</span>:</th>
							<td class="data">
								<input type="text" readonly="readonly" class="inputtext" id="inputTwoFactorKey" />
								<a onclick="RegenerateKey()"><span class="fa fa-lg fa-redo" data-i18n-title="globals-refresh"></span></a>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="divTwoFactorQrCode">
							<th class="label"></th>
							<td class="data">
								<img id="imageTwoFactorQrCode" />
								<yoizen:Message runat="server" Small="true" Type="Information" LocalizationKey="administration-myprofile-two_factor-link" style="margin-bottom: 0; margin-top: 5px;">
									Escanee la siguiente imagen en la aplicación Google Authenticator para vincular la cuenta
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trTwoFactorPin">
							<th class="label"><span data-i18n="administration-myprofile-two_factor-pin">Pin</span>:</th>
							<td class="data">
								<input type="text" class="inputtext" id="inputTwoFactorPin" />
								<yoizen:Message runat="server" Small="true" Type="Information" LocalizationKey="administration-myprofile-two_factor-pin-info" style="margin-bottom: 0; margin-top: 5px;">
									Luego de registrar en Google Authenticator, ingrese el pin para finalizar la registración
								</yoizen:Message>
							</td>
						</tr>
					</table>
					<div id="divTwoFactorSaveError" class="validationerror" style="display: none"><div></div></div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge">
							<button type="button" onclick="SaveTwoFactorSettings()" data-i18n="globals-accept">Aceptar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
	</asp:Panel>
</asp:Content>
