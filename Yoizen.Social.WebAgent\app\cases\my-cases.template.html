﻿<div ng-if="myCasesCtrl.showFilterCases" class="my-cases-filters">
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'SEARCH_BY' | translate }}:</label>
                <select class="form-control input-sm" ng-model="myCasesCtrl.selectedOptionFilterBy" ng-change="myCasesCtrl.restartFiltersCases()">
                    <option value="1">{{ 'MY_CASES' | translate }}</option>
                    <option value="2">{{ 'CASE_CODE' | translate }}</option>
                    <option value="3">{{ 'CASE_DATERANGE_AND_USERPROFILE' | translate }}</option>
                </select>
            </div>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '1'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'MY_CASES_FILTER_DAYS' | translate }}:</label>
                <input type="number" ng-model="myCasesCtrl.daysToRefresh" maxlength="2" min="1" max="30" class="form-control input-sm refresh" placeholder="{{ 'MY_CASES_FILTER_DAYS' | translate }}">
            </div>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '1' || myCasesCtrl.selectedOptionFilterBy === '3'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'PLACEHOLDER_USER_SEARCH_CASES' | translate }}:</label>
                <div class="input-group">
                    <input type="text" class="form-control input-sm"
                           ng-model="myCasesCtrl.inputProfileUser"
                           ng-model-options="{ debounce: 500}"
                           placeholder="{{'PLACEHOLDER_USER_SEARCH_CASES' | translate}}"
                           readonly="readonly"
                           ng-class="{ 'has-error': myCasesCtrl.userSearchCasesInvalid }" />
                    <div class="input-group-btn">
                        <button type="button"
                                class="btn btn-default btn-sm"
                                ng-click="myCasesCtrl.cleanInputProfiles()"
                                ng-if="myCasesCtrl.selectedUserId !== null"><i class="fa fa-times"></i></button>
                        <button type="button"
                                class="btn btn-default btn-sm"
                                ng-click="myCasesCtrl.searchUserProfiles()"><i class="fa fa-search"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '1' || myCasesCtrl.selectedOptionFilterBy === '3'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'STATUS' | translate }}:</label>
                <select class="form-control input-sm" ng-model="myCasesCtrl.selectFilterByStatus">
                    <option value="1">{{ 'OPEN' | translate }}</option>
                    <option value="2">{{ 'CLOSED' | translate }}</option>
                    <option value="3">{{ 'PREDEFINED_ANSWERS_FILTER_TAGS_ANY' | translate }}</option>
                </select>
            </div>
        </div>
    </div>
    <div class="row" ng-if="(myCasesCtrl.selectedOptionFilterBy === '1' || myCasesCtrl.selectedOptionFilterBy === '3') && myCasesCtrl.selectedUserId !== null">
        <div class="col-md-12">
            <social-user-profile social-case="myCasesCtrl.selectedProfileFakeCase"
                                 view-shown-as="'div'"
                                 show-link="false"
                                 dark-mode="true"
                                 allow-to-edit="false"
                                 is-my-outgoing-cases="false"
                                 class="main-case-panel"
                                 show-extended-info="true"></social-user-profile>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '2'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'CASE_CODE' | translate }}:</label>
                <input type="number" ng-model="myCasesCtrl.caseId" maxlength="20" class="form-control input-sm refresh" placeholder="{{ 'CASE_CODE' | translate }}">
            </div>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '3'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'DATE_FROM' | translate }}:</label>
                <div class="input-group" ng-class="{ 'has-error': myCasesCtrl.dateFromInvalid }">
                    <input type="text"
                           class="form-control input-sm"
                           placeholder="{{ 'DATE_FROM' | translate }}"
                           clear-text="{{ 'DATEPICKER_CLEAR' | translate }}"
                           close-text="{{ 'DATEPICKER_CLOSE' | translate }}"
                           current-text="{{ 'DATEPICKER_TODAY' | translate }}"
                           ng-model="myCasesCtrl.dateFrom"
                           ng-model-options="{ debounce: 500}"
                           uib-datepicker-popup="{{'DATE_FORMAT' | translate}}"
                           is-open="dateFrom.opened"
                           datepicker-options="myCasesCtrl.dateOptionsDateFrom"
                           autocomplete="off"
                           spellcheck="false"/>
                    <div class="input-group-btn">
                        <button type="button" class="btn btn-default btn-sm" ng-click="dateFrom.opened = true"><i class="fa fa-lg fa-calendar"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" ng-if="myCasesCtrl.selectedOptionFilterBy === '3'">
        <div class="col-md-12">
            <div class="form-group">
                <label>{{ 'DATE_TO' | translate }}:</label>
                <div class="input-group" ng-class="{ 'has-error': myCasesCtrl.dateToInvalid }">
                    <input type="text"
                           class="form-control input-sm"
                           placeholder="{{ 'DATE_TO' | translate }}"
                           clear-text="{{ 'DATEPICKER_CLEAR' | translate }}"
                           close-text="{{ 'DATEPICKER_CLOSE' | translate }}"
                           current-text="{{ 'DATEPICKER_TODAY' | translate }}"
                           ng-model="myCasesCtrl.dateTo"
                           uib-datepicker-popup="{{'DATE_FORMAT' | translate}}"
                           is-open="dateTo.opened"
                           datepicker-options="myCasesCtrl.dateOptionsDateTo"
                           autocomplete="off"
                           spellcheck="false"/>
                    <div class="input-group-btn">
                        <button type="button" class="btn btn-default btn-sm" ng-click="dateTo.opened = true"><i class="fa fa-lg fa-calendar"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <button type="button" class="btn btn-default"
                    ng-click="myCasesCtrl.validateFilters()"
                    ng-disabled="(myCasesCtrl.invalidFilters && myCasesCtrl.selectedOptionFilterBy === '3') || (myCasesCtrl.invalidCaseId && myCasesCtrl.selectedOptionFilterBy === '2')">
                <i class="fa fa-search"></i>
                <span class="btn-text">{{'SEARCH' | translate }}</span>
            </button>
        </div>
    </div>
</div>
<div class="my-pending-cases"
     ng-class="{'loading': myCasesCtrl.loading}"
     ng-if="!myCasesCtrl.showFilterCases">
    <div class="loading">
        <i class="fas fa-spinner fa-spin"></i>
    </div>
    <div class="info">
        <div class="with-cases" ng-if="myCasesCtrl.userCases.length > 0">
            <table class="table table-striped table-hover">
                <thead>
                    <tr class="row cases-title">
                        <th>{{'ID' | translate}}</th>
                        <th>{{'START_DATE' | translate}}</th>
                        <th>{{'LAST_UPDATE_ON' | translate}}</th>
                        <th>{{'CHANNELS' | translate}}</th>
                        <th>{{'STATUS' | translate}}</th>
                        <th>{{'PROFILE' | translate}}</th>
                        <th class="text-center">{{'ENQUEUED_MESSAGES' | translate}}</th>
                        <th class="text-center">{{'ASSIGNED_MESSAGES' | translate}}</th>
                        <th>{{'CASE_MORE_INFO' | translate}}</th>
                        <th>{{'LAST_AGENT' | translate}}</th>
                        <th>{{'OBSERVATIONS' | translate}}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="row cases-row"
                        ng-repeat="_case in myCasesCtrl.userCases">
                        <td>{{ _case.id }}</td>
                        <td class="text-nowrap">{{ _case.startedOnFormatted }}</td>
                        <td class="text-nowrap">{{ _case.lastUpdatedOnFormatted }}</td>
                        <td>
                            <div class="case-services">
                                <span ng-repeat="svc in _case.servicesData"
                                      uib-tooltip="{{svc._service.name}}"
                                      class="fa-lg channel {{svc.icon}} {{svc.color}}"></span>
                            </div>
                        </td>
                        <td class="text-nowrap">
                            <span class="case-status"
                                  ng-class="{ 'open': _case.status === 1, 'closed': _case.status === 2 }">
                                {{ myCasesCtrl.getCaseStatus(_case) }}
                            </span>
                        </td>
                        <td class="text-nowrap">
                            {{ _case.profile.name }}
                            <span ng-if="_case.profile.primaryEmail !== null && _case.profile.primaryEmail.length > 0">
                                - <{{ _case.profile.primaryEmail }}>
                            </span>
                            <span ng-if="_case.profile.businessData !== null && _case.profile.businessData.length > 0">
                                ({{ _case.profile.businessData }})
                            </span>
                        </td>
                        <td class="text-center"><span class="fa fa-lg fa-check-circle" ng-if="_case.messagesInQueue"></span></td>
                        <td class="text-center"><span class="fa fa-lg fa-check-circle" ng-if="_case.messagesAssigned"></span></td>
                        <td class="text-nowrap">
                            <div class="parameters">
                                <div class="parameter" ng-repeat="parameter in ::_case.moreInfoParameters">
                                    <span class="bold">{{ ::parameter.name }}:</span>
                                    <span class="value">{{ ::parameter.value }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-nowrap">
                            {{ _case.lastPerson && _case.lastPerson.fullName ? _case.lastPerson.fullName : '' }}
                        </td>
                        <td class="text-nowrap">
                            <span style="cursor: pointer"
                                    tooltip-placement="left"
                                    uib-tooltip="{{ _case.observations }}">
                                {{ _case.observations && _case.observations.length > 100 ? (_case.observations | limitTo:100) + '...' : (_case.observations || '') }}
                            </span>
                        </td>
                        <td class="text-center">
                            <div class="case-actions">
                                <div class="case-see">
                                    <i class="fa fa-search"
                                       ng-click="myCasesCtrl.seeCase(_case)"
                                       style="cursor: pointer"
                                       tooltip-placement="left"
                                       uib-tooltip="{{ 'SEE_CASE' | translate }}"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="buttons" ng-if="myCasesCtrl.showLoadMoreCases">
                <button type="button"
                        class="btn btn-flat btn-sm btn-refresh"
                        ng-click="myCasesCtrl.getMoreCases()">
                    {{'LOAD_MORE' | translate}}
                </button>
            </div>
        </div>

        <div ng-if="myCasesCtrl.userCases.length === 0">
            <div class="alert alert-info">
                <i class="fa fa-exclamation-circle"></i>
                {{ 'OUTGOING_MY_DAILY_CASES_EMPTY' | translate}}
            </div>
        </div>
    </div>
</div>