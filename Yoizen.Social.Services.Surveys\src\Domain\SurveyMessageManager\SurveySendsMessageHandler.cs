using Newtonsoft.Json.Linq;
using Yoizen.Common;
using Yoizen.Social.DomainModel;
using Yoizen.Social.Services.Surveys.Domain.SurveyMessageManager;
using Yoizen.Social.Services.Surveys.Messages;
using Microsoft.Extensions.Logging;
using Yoizen.Social.DAL;
using Yoizen.Social.Intervals;
using Yoizen.Social.Services.Surveys.Infrastructure.Configuration;
using Yoizen.Social.Services.Surveys.Core.BackgroundTask;
using System.Collections.Concurrent;
using System;

namespace Yoizen.Social.Services.MsgMgr.Domain.SocialMessageProcessingService;

public class SurveySendsMessageHandler : IMessageHandler
{
	#region Fields
	private readonly ILogger _logger;
	private readonly int _utcOffset;
	#endregion

	public SurveySendsMessageHandler(ILogger logger)
	{
		_logger = logger;
	}

	#region Methods
	/// <summary>
	/// Procesa el envío de una encuesta que llegaron por el Service Bus
	/// </summary>
	/// <param name="jBody">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la información que vino con la encuesta a través del service bus</param>
	public async Task ProcessMessage(SurveysMessage sMessage, JObject jBody)
	{
		if (!Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled)
			return;

		if (!SystemSettings.Instance.EnableSurveys)
			return;

		var caseId = jBody["caseId"].ToObject<long>();
		var lastIncomingMessageId = jBody["lastIncomingMessageId"].ToObject<long>();
		var serviceId = jBody["serviceId"].ToObject<int>();
		Guid surveyId;
		if (jBody["surveyId"].Type == Newtonsoft.Json.Linq.JTokenType.Guid)
			surveyId = jBody["surveyId"].ToObject<Guid>();
		else
			surveyId = Guid.Parse(jBody["surveyId"].ToString());
		var byQueue = jBody["byQueue"].ToObject<bool>();
		var entityId = jBody["entityId"].ToObject<int>();
		var closedDateTimeStamp = jBody["closedDate"].ToObject<long>();
		var surveyConfiguration = jBody["configuration"].ToObject<QueueSurveyConfiguration>();

		var survey = Cache.Instance.GetItem<Survey>(surveyId.ToString());
		if (survey == null || !survey.Enabled)
		{
			_logger.LogInformation("La encuesta {0} no existe o no está habilitada. NO se puede encuestar el caso {1}", surveyId, caseId);
			return;
		}

		bool shouldGetRelatedInfo = survey.Type == SurveyTypes.ThirdParty;

		var @case = CaseDAO.GetOneWithoutMessages(caseId, shouldGetRelatedInfo);
		if (@case == null)
		{
			_logger.LogInformation("No se encontró el caso {0}, se ignora", caseId);
			return;
		}

		if (@case.Status == CaseStatuses.Open)
		{
			_logger.LogInformation("El caso {0} se encuentra abierto. No se lo puede encuestar", caseId);
			return;
		}

		if (@case.ClosedDate == null)
		{
			_logger.LogInformation("El caso {0} no tiene fecha de cierre. No se lo puede encuestar", caseId);
			return;
		}

		var dateTime = Conversions.UnixTimeToDateTime(closedDateTimeStamp).ToLocalTime();
		var tz = DomainModel.SystemSettings.Instance.DefaultTimeZone;
		DateTime closedDate = dateTime.Add(tz.GetUtcOffset(dateTime));

		if (Math.Abs(closedDate.Subtract(@case.ClosedDate.Value).TotalSeconds) > 2)
		{
			_logger.LogInformation("El caso {0} tiene fecha de cierre {1} pero el pedido de encuesta es con fecha de cierre {2}. No se lo puede encuestar porque seguramente se reabrió y volvió a cerrar", caseId, @case.ClosedDate, closedDate);
			return;
		}

		if (!@case.SurveyShouldSend)
		{
			_logger.LogInformation("El caso {0} indica que no se debe enviar la encuesta", caseId);
			return;
		}

		if (!surveyConfiguration.SendIfNewCaseExists && @case.Next != null)
		{
			if (surveyConfiguration.SendIfNewCaseClosedByYflow)
			{
				@case.Next = CaseDAO.GetOneWithoutMessages(@case.Next.ID);
				//Corroboro que este abierto o (en caso que exista un caso nuevo cerrado) que este haya sido cerrado por yFlow, y en caso que haya sido cerrado por sistema, que no tenga Cola ni Agentes asociados (lo que significa que fue cerrado por yFlow por inactividad)
				if (@case.Next.Status == CaseStatuses.Open)
				{
					_logger.LogInformation("Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} esta abierto",
						@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
					return;
				}
				else if (@case.Next.ClosedBy != null && @case.Next.ClosedBy != CaseClosingResponsibles.YFlow && !(@case.Next.ClosedBy == CaseClosingResponsibles.System && @case.Next.Queue == null && @case.Next.Agents == 0))
				{
					_logger.LogInformation("Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} no fue cerrado por yFlow",
						@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
					return;
				}
			}
			else if (surveyConfiguration.SendIfNewCaseHasTag)
			{
				@case.Next = CaseDAO.GetOneWithoutMessages(@case.Next.ID);
				HashSet<int> allTags = new HashSet<int>();

				if (surveyConfiguration.Tags != null && surveyConfiguration.Tags.Length > 0)
				{
					allTags.UnionWith(surveyConfiguration.Tags);
				}

				if (surveyConfiguration.TagGroups != null && surveyConfiguration.TagGroups.Length > 0)
				{
					foreach (var tg in surveyConfiguration.TagGroups)
					{
						var tagGroup = TagGroupDAO.GetOneFromCache((short) tg);

						if (tagGroup != null)
						{
							if (tagGroup.Tags != null && tagGroup.Tags.Count > 0)
							{
								var arrayGroupTags = tagGroup.Tags;
								allTags.UnionWith(arrayGroupTags);
							}
						}
					}
				}

				if (allTags.Count > 0)
				{
					bool hasTags = allTags.Any(tag => @case.Next.Tags.Contains(tag));
					if (!hasTags)
					{
						Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} ya que el siguiente caso no contiene las etiquetas configuradas", @case.ID, surveyConfiguration.SurveyID);
						return;
					}
				}
			}
			else
			{
				_logger.LogInformation("Se ignora el caso {0} para la encuesta {1} porque ya existe un caso nuevo {2}",
					@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
				return;
			}
		}

		if (surveyConfiguration.DontSendIfLastSurveyAfterMinutes > 0)
		{
			if (@case.Profile == null || !@case.Profile.RetrievedFromDatabase)
				@case.Profile = SocialUserProfileDAO.GetOne(@case.Profile.ID, false);
			  
			if (@case.Profile != null &&
				@case.Profile.LastSurveySent != null)
			{
				var dateNow = DateTime.Now;
				var minutes = dateNow.Subtract(@case.Profile.LastSurveySent.Value).TotalMinutes;
				if (minutes < surveyConfiguration.DontSendIfLastSurveyAfterMinutes)
				{
					_logger.LogInformation("Se ignora el caso {0} para la encuesta {1} porque la última encuesta al perfil {2} fue envíada hace {3} minutos y está configurado no enviar dentro de los {4} minutos", @case.ID, surveyConfiguration.SurveyID, @case.Profile, minutes, surveyConfiguration.DontSendIfLastSurveyAfterMinutes);
					return;
				}
			}
		}

		var service = DomainModel.Cache.Instance.FindItem<DomainModel.Service>(s =>
					s.Enabled &&
					s.ID == serviceId);


		if (service == null || service.SocialService == null)
		{
			_logger.LogError("No se encontró el servicio {0} para ser utilizado para enviar la encuesta del caso {1}", serviceId, caseId);
			return;
		}

		if (@case.SurveySentDate != null)
		{
			_logger.LogInformation("Se ignora el caso {0} para la encuesta {1} porque ya se envió la encuesta el {2}", @case.ID, surveyConfiguration.SurveyID, @case.SurveySentDate.Value.ToString("o"));
			return;
		}

		var socialService = service.SocialService;

		//Handoff movistar para encuestas
		if (survey.Type == SurveyTypes.Movistar)
		{
			_logger.LogInformation("Se envia Handoff a movistar para la encuesta del caso {0}", @case.ID);
			await ProcessMovistarSurvey(@case, survey, socialService);
			return;
		}

		try
		{
			surveyConfiguration.Survey = survey;

			string surveyLink;
			
			//No acortamos link ya que whatsapp nos permite enviar un mensaje url de boton
			bool useInteractive = @case.Service.SocialServiceType == SocialServiceTypes.WhatsApp &&
							!string.IsNullOrEmpty(surveyConfiguration.InteractiveInvitation) &&
							!string.IsNullOrEmpty(surveyConfiguration.InteractiveInvitationButtonText);
			try
			{
				if (survey.Type == SurveyTypes.ThirdParty &&
					survey.Configuration.ImportantTagFullName &&
					@case.ImportantTag != null)
				{
					@case.ImportantTag = Cache.Instance.GetItem<Tag>(@case.ImportantTag.ID);
				}

				surveyLink = await Survey.GetSurveyLink(survey, Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID, @case, surveyConfiguration.Expiration);
			}
			catch (Exception ex)
			{
				_logger.LogError("Se produjo un error al obtener el link para el caso {0}, se continúa con el siguiente caso: {1}", @case.ID, ex);
				return;
			}

			_logger.LogInformation("Se irá a buscar los datos del último mensaje entrante del caso {0} cuyo ID es {1}", @case.ID, lastIncomingMessageId);
			var message = MessageDAO.GetOne(lastIncomingMessageId, new MessageDAO.RelatedEntitiesToRead(false)
			{
				Service = true
			}, false);

			if (message == null)
			{
				_logger.LogInformation("Se produjo un error al obtener el mensaje {0} del caso {0}, se ignora", lastIncomingMessageId, caseId);
				return;
			}

			if (message.SocialUser != null)
			{
				if (message.IsReply)
					message.RepliesToSocialUser = SocialUserDAO.GetOne(message.SocialUser.ID, message.SocialUser.SocialServiceType, false, false);
				else
					message.PostedBy = SocialUserDAO.GetOne(message.SocialUser.ID, message.SocialUser.SocialServiceType, false, false);
				var socialUser = message.SocialUser;

				var messageSurvey = new Message();
				messageSurvey.Body = surveyConfiguration.Invitation.Replace("@@LINK@@", surveyLink);
				if (messageSurvey.Body.Contains("@@USUARIO@@"))
					messageSurvey.Body = messageSurvey.Body.Replace("@@USUARIO@@", socialUser.Name);
				if (messageSurvey.Body.Contains("@@ALIAS@@"))
					messageSurvey.Body = messageSurvey.Body.Replace("@@ALIAS@@", socialUser.DisplayName);
				messageSurvey.IsDirectMessage = true;
				messageSurvey.RepliesToSocialUser = socialUser;
				messageSurvey.RepliesTo = message;
				messageSurvey.SocialConversationID = message.SocialConversationID;
				if (@case.IsMailCase)
				{
					messageSurvey.Parameters = message.Parameters;
					messageSurvey.RepliesToSocialMessageID = message.SocialMessageID;
				}

				//Insertamos parametros extra para crear el mensaje inractivo de boton URL
				if (useInteractive)
				{
					InsertInteractiveParameters(messageSurvey, surveyLink, surveyConfiguration.InteractiveInvitationButtonText);
				}

				bool sent = false;
				try
				{
					_logger.LogInformation("Enviando la encuesta del caso {0} a través del servicio {1}", @case.ID, socialService.Name);
					await socialService.Send(messageSurvey);
					_logger.LogInformation("Se envió la encuesta del caso {0}", @case.ID);
					
					if (socialService.SupportsCaseSurveySent)
						await socialService.CaseSurveySent(@case);

					sent = true;
				}
				catch (Exception ex)
				{
					_logger.LogError("Falló el envío de la encuesta del caso {0}: {1}", @case.ID, ex);

					if (surveyConfiguration.SendMailIfFailed)
					{
						@case.Profile = SocialUserProfileDAO.GetOne(@case.Profile.ID, false);
						if (!string.IsNullOrEmpty(@case.Profile.PrimaryEmail))
						{
							_logger.LogInformation("Se enviará la encuesta por mail al perfil {0}", @case.Profile);

							DomainModel.Settings.EmailSettings settings = new DomainModel.Settings.EmailSettings("Temp");
							settings.Emails = @case.Profile.PrimaryEmail;
							settings.Subject = surveyConfiguration.EmailSubject;
							settings.Template = surveyConfiguration.EmailTemplate;
							settings.From = surveyConfiguration.EmailFrom;

							var templateParameters = new Dictionary<string, object>
							{
								["@@LINK@@"] = surveyLink
							};

							await SystemSettings.Instance.SendMailMessageAsync(settings, templateParameters);
							sent = true;
						}
					}
				}

				if (sent)
				{
					@case.SurveySentDate = DateTime.Now;
					@case.Survey = survey;
					if (byQueue)
						@case.Parameters[Case.SurveyQueueParameter] = entityId.ToString(System.Globalization.CultureInfo.InvariantCulture);
					else
						@case.Parameters[Case.SurveyServiceParameter] = entityId.ToString(System.Globalization.CultureInfo.InvariantCulture);
					CaseDAO.UpdateSurvey(@case, survey.ID);

					var interval = new Interval(@case.SurveySentDate.Value, SystemSettings.Instance.IntervalsPerHour);

					int? queueId = null;
					if (@case.Parameters.ContainsKey(Case.SurveyQueueParameter))
						queueId = int.Parse(@case.Parameters[Case.SurveyQueueParameter], System.Globalization.CultureInfo.InvariantCulture);
					else if (@case.Queue != null)
						queueId = @case.Queue.ID;

					DomainModel.Historical.Daily info;
					DomainModel.Historical.DailySurvey infoSurvey;

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = queueId != null ? queueId.Value : -1;
					info.SurveysSent = 1;
					info.PersonID = 0;
					LegacyLibrariesConfiguration.IntervalsStorageManagerDb.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.SurveysSent = 1;
					info.PersonID = 0;
					LegacyLibrariesConfiguration.IntervalsStorageManagerDb.StoreInfo(info, interval);

					infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
					infoSurvey.QueueID = queueId != null ? queueId.Value : -1;
					infoSurvey.Sent = 1;
					infoSurvey.SurveyID = survey.ID;
					LegacyLibrariesConfiguration.IntervalsStorageManagerDb.StoreInfo(infoSurvey, interval);

					infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
					infoSurvey.QueueID = 0;
					infoSurvey.Sent = 1;
					infoSurvey.SurveyID = survey.ID;
					LegacyLibrariesConfiguration.IntervalsStorageManagerDb.StoreInfo(infoSurvey, interval);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
					{
						if (@case.Queue != null && !@case.Queue.RetrievedFromDatabase)
							@case.Queue = Cache.Instance.GetItem<Queue>(@case.Queue.ID);
						SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, CaseLogTypes.SurveySent);
					}
				}
				else
				{
					CaseDAO.UpdateSurveySentFailed(@case.ID);
				}
			}
		}
		catch (Exception ex)
		{
			_logger.LogError("No se pudo enviar la encuesta para el caso {0}: {1}", caseId, ex);
		}
	}

	/// <summary>
	/// Inserta los parametros para el mensaje interactivo de URL
	/// </summary>
	/// <param name="message"></param>
	/// <param name="link"></param>
	/// <param name="buttonText"></param>
	private void InsertInteractiveParameters(Message message, string link, string buttonText)
	{
		message.Parameters["SurveyInteractiveMsg"] = Newtonsoft.Json.JsonConvert.SerializeObject(new
		{
			displayText = buttonText,
			url = link
		});
	}


	/// <summary>
	/// Procesa una encuesta de Movistar y notifica a movistar mediante CaseSurveySent
	/// </summary>
	/// <param name="case"></param>
	/// <param name="survey"></param>
	/// <param name="socialService"></param>
	/// <returns></returns>

	private async Task ProcessMovistarSurvey(Case @case, Survey survey, ISocialService socialService)
	{
		try
		{
			Tracer.TraceInfo("Se notificará a movistar que debe enviarse la encuesta del caso {0}", @case.ID);

			@case.Parameters["MovistarSurveyButtons"] = Newtonsoft.Json.JsonConvert.SerializeObject(new
			{
				Button1Payload = survey.Configuration.MovistarButton1Payload,
				Button1Text = survey.Configuration.MovistarButton1Text,
				Button2Payload = survey.Configuration.MovistarButton2Payload,
				Button2Text = survey.Configuration.MovistarButton2Text,
				Button3Payload = survey.Configuration.MovistarButton3Payload,
				Button3Text = survey.Configuration.MovistarButton3Text,
				NoAgent = survey.Configuration.MovistarNoAgent,
				LastAgent = @case.LastPerson != null && @case.LastPerson.Type == DomainModel.PersonTypes.Agent ? (int?) @case.LastPerson.ID : null,
			});

			if (socialService.SupportsCaseSurveySent)
				await socialService.CaseSurveySent(@case);
		}

		catch (Exception ex)
		{
			Tracer.TraceError("Ocurrio un error al procesar la encuesta de Movistar", ex);
		}
	}

	#endregion
}