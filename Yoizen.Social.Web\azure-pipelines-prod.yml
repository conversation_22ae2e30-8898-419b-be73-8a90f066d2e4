trigger:
  branches:
    include:
      - refs/tags/version/*

pool:
  vmImage: 'windows-latest'

resources:
  repositories:
    - repository: pipelines-templates
      type: git
      name: infra-cd-aplicaciones/pipeline-templates
      ref: refs/heads/main 

parameters:
  - name: tag
    displayName: 'Tag for version'
    type: string
    default: $(Build.SourceBranchName)

variables:
  - name: buildConfiguration
    value: 'Release'
  
  - name: buildPlatform
    value: 'AnyCPU'

  - group: StorageSecrets
  - group: global-variables
  
jobs:

- job: Build_ySocial_Web
  displayName: 'Build ySocial Web'
  steps:
  - checkout: self
    persistCredentials: true
    
  - task: MSBuild@1
    displayName: "Actualizar versión"
    inputs:
        solution: '$(Build.SourcesDirectory)/Build-pipe.proj'
        msbuildArguments: '/t:Version /p:VersionFromPipeline="${{ parameters.tag }}" /p:BuildConfiguration="Release"'
        platform: 'Any CPU'

  - task: PowerShell@2
    displayName: 'Configure Git'
    inputs:
      targetType: 'inline'
      script: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "Azure Pipeline"

  - task: CmdLine@2
    displayName: 'Create and Push Release Branch'
    inputs:
      script: |
        git remote set-url origin https://oauth2:$(System.AccessToken)@dev.azure.com/yoizen/ySocial/_git/ySocial
        git checkout -b release/${{ parameters.tag }}
        git add .
        git commit -m "Update version to ${{ parameters.tag }}"
        git push origin release/${{ parameters.tag }}

  - task: MSBuild@1
    displayName: 'Clean solution'
    inputs:
      solution: '**/Yoizen.Social.Web.csproj'
      configuration: '$(buildConfiguration)'
      platform: '$(buildPlatform)'
      msbuildArguments: '/t:Clean'

  - task: NuGetCommand@2
    displayName: 'NuGet Restore'
    inputs:
      command: 'restore'
      restoreSolution: '**/Yoizen.Social.sln'

  - task: MSBuild@1
    displayName: 'Build solution'
    inputs:
      solution: '**/Yoizen.Social.Web.csproj'
      configuration: '$(buildConfiguration)'
      platform: '$(buildPlatform)'
      msbuildArguments: '/p:DeployOnBuild=true /p:PublishProfile="Social - Release" /p:publishUrl="$(Build.ArtifactStagingDirectory)\_PublishedWebsites\Yoizen.Social.Web"'

  - task: ArchiveFiles@2
    displayName: 'Create ZIP file'
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/_PublishedWebsites/Yoizen.Social.Web'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip'
      replaceExistingArchive: true
    
  - task: PublishBuildArtifacts@1
    displayName: 'Publish ZIP artifact'
    inputs:
      pathToPublish: '$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip'
      artifactName: 'Social-Web'

  - task: AzureCLI@2
    displayName: 'Upload ZIP to Blob Storage'
    inputs:
        azureSubscription: 'devops-pipeline-sp'
        scriptType: 'ps'
        scriptLocation: 'inlineScript'
        inlineScript: |
            az storage blob upload --account-name "$(StorageAccountName)" --container-name "$(ContainerName)" --file "$(Build.ArtifactStagingDirectory)/Yoizen.Social.Web.zip" --name "prd/${{ parameters.tag }}/Yoizen.Social.Web.zip" --auth-mode key --account-key "$(StorageAccountKey)"
        condition: succeeded()

- job: Generate_Release_Notes
  dependsOn: Build_ySocial_Web
  condition: succeeded()
  displayName: 'Generate Release Notes'

  variables:
    - name: RELEASE_NOTES_URL
      value: 'https://releaseanotes-yoizen-dev.ysocial.net/api/consolidate-release-notes'
    - name: RELEASE_NOTES_FILENAME
      value: 'release-notes-${{ parameters.tag }}.md'
  steps:
  - task: PowerShell@2
    inputs:
      targetType: 'inline'
      script: |
        $body = @{
          version = "${{ parameters.tag }}"
          model = "gemini-2.0-flash"
          useAI = $false
          project = "ySocial"
          repository = "ySocial"
          addCommentToPR = $false
        } | ConvertTo-Json

        $headers = @{
          'Content-Type' = 'application/json'
        }

        try {
          Write-Host "Calling Release Notes API for version ${{ parameters.tag }}"
          $response = Invoke-RestMethod -Uri "$(RELEASE_NOTES_URL)" -Method Post -Body $body -Headers $headers
          Write-Host "Successfully called release notes API for version ${{ parameters.tag }}"

          # Extract content and save to a file
          $markdownContent = $response.content
          $outputFilePath = "$(Build.ArtifactStagingDirectory)/$(RELEASE_NOTES_FILENAME)"
          
          # Remove the "markdown\n" prefix if it exists
          if ($markdownContent.StartsWith("markdown`n")) {
            $markdownContent = $markdownContent.Substring("markdown`n".Length)
          }

          Write-Host "Saving release notes to $outputFilePath"
          Set-Content -Path $outputFilePath -Value $markdownContent -Encoding UTF8
          
          Write-Host "Release notes content:"
          Write-Host $markdownContent

          # Set an output variable for the file path to be used by the next task
          Write-Host "##vso[task.setvariable variable=ReleaseNotesMDPath;isOutput=true]$outputFilePath"
        }
        catch {
          Write-Error "Failed to generate or save release notes: $_"
          # Write out the full response if available for debugging
          if ($_.Exception.Response) {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $streamReader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $streamReader.ReadToEnd()
            $streamReader.Close()
            Write-Error "API Response (Error): $errorBody"
          }
          exit 1
        }
    displayName: 'Generate and Save Release Notes MD' # Ensure this is indented correctly
    name: GenerateAndSaveMD                        # Ensure this is indented correctly

  - task: AzureCLI@2
    displayName: 'Upload Release Notes MD to Blob Storage'
    inputs:
        azureSubscription: 'devops-pipeline-sp'
        scriptType: 'ps'
        scriptLocation: 'inlineScript'
        inlineScript: |
            $mdFilePath = "$(GenerateAndSaveMD.ReleaseNotesMDPath)" # This line needs GenerateAndSaveMD.ReleaseNotesMDPath to be valid
            Write-Host "Uploading $mdFilePath to Blob Storage..."
            az storage blob upload --account-name "$(StorageAccountName)" --container-name "$(ContainerName)" --file "$mdFilePath" --name "prd/${{ parameters.tag }}/$(RELEASE_NOTES_FILENAME)" --auth-mode key --account-key "$(StorageAccountKey)" --overwrite true
    condition: succeeded()

- job: Notify_GoogleChat
  displayName: 'Notificar Resultado del Pipeline a Google Chat'
  dependsOn: Generate_Release_Notes
  condition: always()

  steps:
  - checkout: none
  - template: gchat-notification.yml@pipelines-templates
