﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />
/// <reference path="ServicesCommon.js" />

var $selectEnabled;
var $textboxQueue;
var $selectWithSL;
var $selectWithSurveys;
var $checkboxFilterShowID;

var $textboxName;
var $textboxKey;
var queues = null;
var fields = null;
var datatable = null;

var $anchorFilter;
var $tableQueues;
var $messageNoQueues;
var $divLoadingQueues;
var $hiddenActionQueueID;
var $hiddenActionName;
var $hiddenSurveyList;
var $buttonAction;
var $panelButtons;
var $buttonSave;

var $checkboxAllowAgentsToReturnMessagesToQueue;
var $textboxMinutesNotAssignToPreviousAgent;
var $checkboxAllowAgentsToSelectQueueOnReturnToQueue;
var $listboxQueues;

var $listboxSupervisors;
var $listboxUsers;

var $panelServiceLevelMinutesActions;
var $textboxQueueServiceLevelSeconds;
var $checkboxSLMinutesActionsAssignToQueue;
var $dropdownlistSLMinutesActionsAssignToQueue;
var $checkboxSLMinutesActionsNotify;
var $checkboxSLMinutesActionsDiscard;
var $checkboxSLMinutesActionsAutoReply;
var $textboxSLMinutesActionsAutoReply;
var $checkboxSLMinutesActionsAddTags;

var $checkboxSLChatNotify;
var $textboxSLChatNotifyText;
var $checkboxSLExpiredChatNotify;
var $textboxSLExpiredChatNotifyText;

var $checkboxSLMinutesActionsVIM;
var $checkboxChatAvailableDaysAndTimes;
var $divChatAvailableDaysAndTimesContainer;
var $divChatAvailableDaysAndTimes;
var $hiddenChatAvailableDaysAndTimes;
var $divQueueWorkingHoursForReceivingMessagesContainer;
var $hiddenQueueWorkingHoursForReceivingMessages;
var $panelServiceLevelExpiredActions;
var $textboxQueueServiceLevelExpired;
var $checkboxSLMinutesExpiredActionsAssignToQueue;
var $dropdownlistSLMinutesExpiredActionsAssignToQueue;
var $checkboxSLMinutesExpiredActionsNotify;
var $checkboxSLMinutesExpiredActionsDiscard;
var $checkboxSLMinutesExpiredActionsAutoReply;
var $textboxSLMinutesExpiredActionsAutoReply;
var $checkboxSLMinutesExpiredActionsAddTags;
var $checkboxSLMinutesExpiredActionsVIM;
var $checkboxEnableSurveys;
var $dropdownQueueSurvey;
var $spanQueueSurvey;
var externalSurveySelected;
var movistarSurveySelected;
var $messageSurveyDisabled;
var $checkboxQueueSurveySendMailIfFailed;

var $listboxTagGroups;
var $hiddenTagGroups;
var $hiddenSurveyTagGroup;
var $listboxSurveyTagGroup;
var $hiddenSurveyTagGroupToIgnore;
var $listboxSurveyTagGruopToIgnore;
var $textboxSurveysIgnoreTags;

var $divDeleteQueue;
var $divCanBeDeletedLoading;
var $messageQueueCannotBeDeleted;
var $messageQueueCanBeDeleted;
var $messageQueueCannotBeDeletedAndIsDisabled;
var $messageQueueCanBeDeletedAndIsDisabled;
var $messageCouldntCheckQueueCanBeDeleted;
var $buttonDeleteQueueConfirm;
var $buttonDisableQueueConfirm;

var $hiddenTab;
var $tabsEditQueues;
var oldWebFormOnSubmit;

var currentQueueForTasks;
var $tabsTasks;
var $messageTaskQueuessThatCanTransferToQueueEmpty;
var $divTaskQueuessThatCanTransferToQueueNoEmpty;
var $selectTasksQueuesThatCanTransferToQueue;
var $buttonTasksAccept;


var $dropdownlistMailSignatureBehaviour;
var $divMailSignature;
var $textboxMailSignature;

var $divQueueSurvey;
var $checkboxConfigEwt;
var $messageNewSurveyPopup;
var $buttonNewSurvey;
var $tableSurveys;
var editSurvey = false;
var $messageNoSurveysInTable;
var $divWithSurveys;
var surveyEditID;
var surveyToEdit;
var addSurveyToTable = false;
var $tableSurveys;
var surveyConfiguration;
var $divQueueSurveyError;
var drake;

var $listboxDontReserveWithStatus;

var $divExport;
var $divExportStep1;
var $selectExportFormat;

var $checkboxAllowFirstAutomaticActions;
var $checkboxAllowSecondAutomaticActions;
var $divFirstAutomaticActionConfiguration;
var $panelSecondAutomaticActions;
var $dropdownlistFirstAutomaticActionQueues;
var $dropdownlistSecondAutomaticActionQueues
var $textboxFirstAutomaticActionReplyText;
var $textboxSecondAutomaticActionReplyText;
var $trFirstAutomaticActionReplyEwtNoAgents;
var $trFirstAutomaticActionReplyEwtNotCalculated;
var $trSecondAutomaticActionReplyEwtNoAgents;
var $trSecondAutomaticActionReplyEwtNotCalculated;
var $trFirstAutomaticActionMinimumEWT;
var $trFirstAutomaticActionMinimumEnqueueMessages;
var $trSecondAutomaticActionMinimumEWT;
var $trSecondAutomaticActionMinimumEnqueueMessages;
var $textboxFirstAutomaticActionSeconds;
var $checkboxFirstAutomaticActionNotifyChat
var $checkboxFirstAutomaticActionsTransferQueue
var $textboxFirstAutomaticActionMinimumEWT;
var $textboxFirstAutomaticActionMinimumEnqueueMessages;
var $checkboxFirstAutomaticActionReply;
var $textboxFirstAutomaticActionReplyEwtNoAgents;
var $textboxFirstAutomaticActionReplyEwtNotComputed;

var $dropdownlistFirstAutomaticActionMarkAsFinishChat;
var $textboxFirstAutomaticActionReplyChat;
var $checkboxFirstAutomaticActionApplyTags;
var $textboxSecondAutomaticActionsSeconds;
var $checkboxSecondAutomaticActionsTransferQueue;
var $checkboxSecondAutomaticActionReply;
var $textboxSecondAutomaticActionMinimumEWT;
var $textboxSecondAutomaticActionMinimumEnqueueMessages;
var $textboxSecondAutomaticActionReplyEwtNoAgents;
var $textboxSecondAutomaticActionReplyEwtNotComputed;
var $checkboxSecondAutomaticActionNotifyChat;
var $textboxSecondAutomaticActionChatReplyText;
var $dropdownlistSecondAutomaticActionMarkAsFinishChat;
var $checkboxSecondAutomaticActionApplyTags;
var $tableAllowSecondAutomaticActions;
var $divSecondAutomaticActionConfiguration;
var $trFirstAutomaticActionMinimumEWTChat;
var $textboxFirstAutomaticActionMinimumEWTChat;
var $trFirstAutomaticActionMinimumEnqueueMessagesChat;
var $textboxFirstAutomaticActionMinimumEnqueueMessagesChat;
var $trFirstAutomaticActionReplyEwtNoAgentsChat;
var $textboxFirstAutomaticActionReplyEwtNoAgentsChat;
var $trFirstAutomaticActionReplyEwtNotCalculatedChat;
var $textboxFirstAutomaticActionReplyEwtNotCalculatedChat;
var $trSecondAutomaticActionMinimumEWTChat;
var $textboxSecondAutomaticActionMinimumEWTChat;
var $trSecondAutomaticActionMinimumEnqueueMessagesChat;
var $textboxSecondAutomaticActionMinimumEnqueueMessagesChat;
var $trSecondAutomaticActionReplyEwtNoAgentsChat;
var $textboxSecondAutomaticActionReplyEwtNoAgentsChat;
var $trSecondAutomaticActionReplyEwtNotCalculatedChat;
var $textboxSecondAutomaticActionReplyEwtNotCalculatedChat;

var $checkboxEnableVideo;
var $trQueueVideoProject;
var $trQueueVideoDefaultText;
var $textboxVideoDefaultText;

var $textboxTags;
var $textboxSLMinutesActionsTags;
var $textboxSLMinutesExpiredActionsTags;
var $textboxFirstAutomaticActionsTags;
var $textboxSecondAutomaticActionsTags;

$.fn.dataTable.ext.search.push(
    function (settings, data, dataIndex) {
        var queue = queues[dataIndex];

        var enabled = queue.Enabled;
        var withSurveys = queue.WithSurveys;
        var withSL = queue.WithSL;
        var queueName = queue.Name;
        var key = queue.Key;
        if (key === null) {
            key = '';
        }
        queueName = queueName.toLowerCase();
        key = key.toLowerCase();

        var enabledOption = parseInt($selectEnabled.val(), 10);
        var withSLOption = parseInt($selectWithSL.val(), 10);
        var withSurveysOption = parseInt($selectWithSurveys.val(), 10);
        var queueFilter = '';
        if ($textboxQueue.length > 0) {
            queueFilter = $textboxQueue.val().toLowerCase();
        }

        var show = true;

        if (enabledOption != -1) {
            enabledOption = enabledOption === 1;
            show = enabled == enabledOption;
        }

        if (show && queueFilter.length > 0) {
            if (queueName.indexOf(queueFilter) == -1 &&
                key.indexOf(queueFilter)) {
                show = false;
            }
        }

        if (show && withSLOption !== -1) {
            withSLOption = withSLOption === 1;
            show = withSL === withSLOption;
        }

        if (show && withSurveysOption !== -1) {
            withSurveysOption = withSurveysOption === 1;
            show = withSurveys === withSurveysOption;
        }

        return show;
    }
);

jQuery(document).ready(function () {
    $("#colorbox, #cboxOverlay").appendTo('form:first');

    $.colorbox.settings.trapFocus = false;
    if (typeof (WebForm_OnSubmit) == 'function') {
        oldWebFormOnSubmit = WebForm_OnSubmit;
        WebForm_OnSubmit = MyWebFormOnSubmit;
    }

    $hiddenTab = $('input[type=hidden][id$=hiddenTab]');

    $tabsEditQueues = $('#tabsEditQueues')
    $tabsEditQueues.tabs({
        activate: function (tabs, page) {
            var $divTab;
            if ((page.newPanel instanceof jQuery)) {
                $divTab = page.newPanel;
            }
            else {
                $divTab = $(page.newPanel.selector);
            }
            var tabId = $divTab.get(0).id;

            $hiddenTab.val(tabId);

            if (history.pushState) {
                history.pushState(null, null, '#' + tabId);
            }
            else {
                location.hash = '#' + tabId;
            }
        }
    });

    $selectEnabled = $('#selectEnabled');
    $selectWithSL = $('#selectWithSL');
    $selectWithSurveys = $('#selectWithSurveys');
    $checkboxFilterShowID = $('#checkboxFilterShowID');
    $selectEnabled.multiselect({ multiple: false, selectedList: 1 });
    $selectWithSL.multiselect({ multiple: false, selectedList: 1 });
    $selectWithSurveys.multiselect({ multiple: false, selectedList: 1 });
    $textboxQueue = $('#textboxQueue');
    $selectEnabled.change(FilterQueues);
    $selectWithSL.change(FilterQueues);
    $selectWithSurveys.change(FilterQueues);
    $textboxQueue.keyup(FilterQueues);

    $panelList = $('div[id$=panelListado]');
    if ($panelList.length > 0) {
        $divDeleteQueue = $('#divDeleteQueue', $panelList);
        $divCanBeDeletedLoading = $('#divCanBeDeletedLoading', $divDeleteQueue);
        $messageQueueCannotBeDeleted = $('#messageQueueCannotBeDeleted', $divDeleteQueue);
        $messageQueueCanBeDeleted = $('#messageQueueCanBeDeleted', $divDeleteQueue);
        $messageQueueCannotBeDeletedAndIsDisabled = $('#messageQueueCannotBeDeletedAndIsDisabled', $divDeleteQueue);
        $messageQueueCanBeDeletedAndIsDisabled = $('#messageQueueCanBeDeletedAndIsDisabled', $divDeleteQueue);
        $messageCouldntCheckQueueCanBeDeleted = $('#messageCouldntCheckQueueCanBeDeleted', $divDeleteQueue);
        $buttonDeleteQueueConfirm = $('#buttonDeleteQueueConfirm', $divDeleteQueue);
        $buttonDisableQueueConfirm = $('#buttonDisableQueueConfirm', $divDeleteQueue);
    }
	else {
		let $panelEdition = $('div[id$=panelEdition]');
		if ($panelEdition.length > 0) {
			$checkboxChatAvailableDaysAndTimes = $('#checkboxChatAvailableDaysAndTimes');
			$divChatAvailableDaysAndTimes = $('#divChatAvailableDaysAndTimes');
			$hiddenChatAvailableDaysAndTimes = $('#hiddenChatAvailableDaysAndTimes');
			$divChatAvailableDaysAndTimesContainer = $('#divChatAvailableDaysAndTimesContainer');
			let availableDaysAndTimesText = $hiddenChatAvailableDaysAndTimes.val();
			let availableDaysAndTimes = null;
			if (availableDaysAndTimesText.length > 0) {
				availableDaysAndTimes = JSON.parse(availableDaysAndTimesText);
			}
			$divChatAvailableDaysAndTimesContainer.timetable({
				dayNames: $.datepicker._defaults.dayNames,
				selectedTimes: availableDaysAndTimes,
				allowToConfigureInAnotherTimeZone: true
			});

			$divQueueWorkingHoursForReceivingMessagesContainer = $('#divQueueWorkingHoursForReceivingMessagesContainer');
			$hiddenQueueWorkingHoursForReceivingMessages = $('#hiddenQueueWorkingHoursForReceivingMessages');
			availableDaysAndTimesText = $hiddenQueueWorkingHoursForReceivingMessages.val();
			availableDaysAndTimes = null;
            if (typeof (availableDaysAndTimesText) !== 'undefined' && availableDaysAndTimesText != null && availableDaysAndTimesText.length > 0) {
				availableDaysAndTimes = JSON.parse(availableDaysAndTimesText);
			}
			$divQueueWorkingHoursForReceivingMessagesContainer.timetable({
				dayNames: $.datepicker._defaults.dayNames,
				selectedTimes: availableDaysAndTimes,
				allowToConfigureInAnotherTimeZone: true
			});
		}
    }

    var cleditorOptions = {
        height: 200,
        width: 'auto',
        fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
        bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
        controls:     // controls to add to the toolbar
            "bold italic underline | font size " +
            "style | color highlight removeformat | bullets numbering | outdent " +
            "indent | alignleft center alignright justify | " +
            "image link | cut copy paste pastetext | source",
    };

    $anchorFilter = $('#anchorFilter');
    $tableQueues = $('#tableQueues');
    $messageNoQueues = $('#messageNoQueues');
    $divLoadingQueues = $('#divLoadingQueues');
    $hiddenActionQueueID = $('#hiddenActionQueueID');
    $hiddenActionName = $('#hiddenActionName');
    $buttonAction = $('#buttonAction');
    $buttonSave = $('#buttonSave');
    $panelButtons = $('#panelButtons');

    $textboxName = $('#textboxName');
    $textboxKey = $('#textboxKey');

    $checkboxAllowAgentsToReturnMessagesToQueue = $('input[id$=checkboxAllowAgentsToReturnMessagesToQueue]');
    $textboxMinutesNotAssignToPreviousAgent = $('input[id$=textboxMinutesNotAssignToPreviousAgent]');
    $checkboxAllowAgentsToSelectQueueOnReturnToQueue = $('input[id$=checkboxAllowAgentsToSelectQueueOnReturnToQueue]');
    $listboxQueues = $('#listboxQueues');
    $listboxQueues.multiselect({ multiple: true, noneSelectedText: "Seleccione al menos una cola", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

    $checkboxAllowAgentsToReturnMessagesToQueue.change(function () {
        if ($checkboxAllowAgentsToReturnMessagesToQueue.is(':checked')) {
            $textboxMinutesNotAssignToPreviousAgent.removeAttr('disabled');
        }
        else {
            $textboxMinutesNotAssignToPreviousAgent.attr('disabled', 'disabled');
        }
    }).trigger('change');

    $checkboxAllowAgentsToSelectQueueOnReturnToQueue.change(function () {
        if ($checkboxAllowAgentsToSelectQueueOnReturnToQueue.is(':checked')) {
            $listboxQueues.removeAttr('disabled');
            $listboxQueues.multiselect('enable');
        }
        else {
            $listboxQueues.attr('disabled', 'disabled');
            $listboxQueues.multiselect('disable');
        }
    }).trigger('change');

    var $checkboxQueueConnectedAgentsForReceivingMessages = $('#checkboxQueueConnectedAgentsForReceivingMessages');
    var $divQueueConnectedAgentsForReceivingMessagesExceptions = $('#divQueueConnectedAgentsForReceivingMessagesExceptions');
    $checkboxQueueConnectedAgentsForReceivingMessages.change(function () {
        $divQueueConnectedAgentsForReceivingMessagesExceptions.toggle(this.checked);
    }).trigger('change');

    $dropdownlistMailSignatureBehaviour = $('#dropdownlistMailSignatureBehaviour');
    $divMailSignature = $('#divMailSignature');
    $textboxMailSignature = $('#textboxMailSignature', $divMailSignature);
    $textboxMailSignature.cleditor(cleditorOptions);

    $checkboxEnableVideo = $('#checkboxEnableVideo');
    $trQueueVideoProject = $('#trQueueVideoProject');
    $trQueueVideoDefaultText = $('#trQueueVideoDefaultText');
    $textboxVideoDefaultText = $('#textboxVideoDefaultText');

    $checkboxEnableVideo.change(function () {
        $trQueueVideoProject.toggle(this.checked);
        $trQueueVideoDefaultText.toggle(this.checked);
    }).trigger('change');


    $dropdownlistMailSignatureBehaviour.change(function () {
        var type = parseInt($dropdownlistMailSignatureBehaviour.val(), 10);
        if (type != 0) {
            $divMailSignature.show();

            var editor = $textboxMailSignature.cleditor()[0];
            if (typeof (editor.refreshed) == 'undefined') {
                editor.refresh();
                editor.refreshed = true;
            }
        }
        else {
            $divMailSignature.hide();
        }
    }).trigger('change');

    $listboxDontReserveWithStatus = $('#listboxDontReserveWithStatus');
    $listboxDontReserveWithStatus.multiselect({ multiple: true, noneSelectedText: "No evaluar", selectedList: 4, buttonWidth: '>619.6' }).multiselectfilter();

    $panelServiceLevelMinutesActions = $('#panelServiceLevelMinutesActions');
    $textboxQueueServiceLevelSeconds = $('#textboxQueueServiceLevelSeconds');
    $checkboxSLMinutesActionsAssignToQueue = $('#checkboxSLMinutesActionsAssignToQueue');
    $dropdownlistSLMinutesActionsAssignToQueue = $('#dropdownlistSLMinutesActionsAssignToQueue');
    $dropdownlistSLMinutesActionsAssignToQueue.multiselect({ multiple: false, noneSelectedText: "Seleccione al menos una cola", selectedList: 4 });
    $checkboxSLMinutesActionsNotify = $('#checkboxSLMinutesActionsNotify');
    $checkboxSLMinutesActionsDiscard = $('#checkboxSLMinutesActionsDiscard');
    $checkboxSLMinutesActionsAutoReply = $('#checkboxSLMinutesActionsAutoReply');
    $textboxSLMinutesActionsAutoReply = $('#textboxSLMinutesActionsAutoReply');
    $textboxSLMinutesActionsAutoReply.on('keyup', function () {
        $('#spanSLMinutesAutoReplyCounter').text($textboxSLMinutesActionsAutoReply.val().length);
    }).trigger('keyup');
    $checkboxSLMinutesActionsAddTags = $('#checkboxSLMinutesActionsAddTags');
    $checkboxSLMinutesActionsVIM = $('#checkboxSLMinutesActionsVIM');

	$checkboxSLChatNotify = $('#checkboxSLChatNotify');
	$textboxSLChatNotifyText = $('#textboxSLChatNotifyText');
	$checkboxSLExpiredChatNotify = $('#checkboxSLExpiredChatNotify');
	$textboxSLExpiredChatNotifyText = $('#textboxSLExpiredChatNotifyText');

	$textboxQueueServiceLevelSeconds.change(function () {
		if ($textboxQueueServiceLevelSeconds.val() > 0) {
			$panelServiceLevelMinutesActions.show();
		} else {
			$panelServiceLevelMinutesActions.hide();
		}
	}).trigger('change');
	$checkboxSLMinutesActionsAssignToQueue.change(function () {
		if ($checkboxSLMinutesActionsAssignToQueue.is(':checked')) {
			$checkboxSLMinutesActionsDiscard.attr('disabled', 'disabled');
		} else {
			$checkboxSLMinutesActionsDiscard.removeAttr('disabled');
		}
	}).trigger('change');
	$checkboxSLMinutesActionsDiscard.change(function () {
		if ($checkboxSLMinutesActionsDiscard.is(':checked')) {
			$checkboxSLMinutesActionsAssignToQueue.attr('disabled', 'disabled');
		} else {
			$checkboxSLMinutesActionsAssignToQueue.removeAttr('disabled');
		}
	}).trigger('change');

    $panelServiceLevelExpiredActions = $('#panelServiceLevelExpiredActions');
    $textboxQueueServiceLevelExpired = $('#textboxQueueServiceLevelExpired');
    $checkboxSLMinutesExpiredActionsAssignToQueue = $('#checkboxSLMinutesExpiredActionsAssignToQueue');
    $dropdownlistSLMinutesExpiredActionsAssignToQueue = $('#dropdownlistSLMinutesExpiredActionsAssignToQueue');
    $dropdownlistSLMinutesExpiredActionsAssignToQueue.multiselect({ multiple: false, noneSelectedText: "Seleccione al menos una cola", selectedList: 4 });
    $checkboxSLMinutesExpiredActionsNotify = $('#checkboxSLMinutesExpiredActionsNotify');
    $checkboxSLMinutesExpiredActionsDiscard = $('#checkboxSLMinutesExpiredActionsDiscard');
    $checkboxSLMinutesExpiredActionsAutoReply = $('#checkboxSLMinutesExpiredActionsAutoReply');
    $textboxSLMinutesExpiredActionsAutoReply = $('#textboxSLMinutesExpiredActionsAutoReply');
    $textboxSLMinutesExpiredActionsAutoReply.on('keyup', function () {
        $('#spanSLMinutesExpiredAutoReplyCounter').text($textboxSLMinutesExpiredActionsAutoReply.val().length);
    }).trigger('keyup');
    $checkboxSLMinutesExpiredActionsAddTags = $('#checkboxSLMinutesExpiredActionsAddTags');
    $checkboxSLMinutesExpiredActionsVIM = $('#checkboxSLMinutesExpiredActionsVIM');

    $textboxQueueServiceLevelExpired.change(function () {
        if ($textboxQueueServiceLevelExpired.val() > 0) {
            $panelServiceLevelExpiredActions.show();
        } else {
            $panelServiceLevelExpiredActions.hide();
        }
    }).trigger('change');
    $checkboxSLMinutesExpiredActionsAssignToQueue.change(function () {
        if ($checkboxSLMinutesExpiredActionsAssignToQueue.is(':checked')) {
            $checkboxSLMinutesExpiredActionsDiscard.attr('disabled', 'disabled');
        } else {
            $checkboxSLMinutesExpiredActionsDiscard.removeAttr('disabled');
        }
    }).trigger('change');
    $checkboxSLMinutesExpiredActionsDiscard.change(function () {
        if ($checkboxSLMinutesExpiredActionsDiscard.is(':checked')) {
            $checkboxSLMinutesExpiredActionsAssignToQueue.attr('disabled', 'disabled');
        } else {
            $checkboxSLMinutesExpiredActionsAssignToQueue.removeAttr('disabled');
        }
    }).trigger('change');

    $listboxSupervisors = $('#listboxSupervisors');
    $listboxSupervisors.multiselect({ multiple: true, noneSelectedText: "Seleccione los supervisores", selectedList: 4, buttonWidth: '>600' }).multiselectfilter();
    $listboxUsers = $('#listboxUsers');
    $listboxUsers.multiselect({ multiple: true, noneSelectedText: "Seleccione los usuarios", selectedList: 4, buttonWidth: '>600' }).multiselectfilter();

    $hiddenSurveyList = $('#hiddenSurveyList');
    $divQueueSurvey = $('#divQueueSurvey');
    $checkboxEnableSurveys = $('#checkboxEnableSurveys');
    $messageNewSurveyPopup = $('#messageNewSurveyPopup');
    $divNewSurvey = $('#divNewSurvey');
    $tableSurveys = $('#tableSurveys');
    $messageNoSurveysInTable = $('#messageNoSurveysInTable');
    $divWithSurveys = $('#divWithSurveys');
    $divQueueSurveyError = $('#divQueueSurveyError');

    $checkboxEnableSurveys.change(function () {
        if ($checkboxEnableSurveys.is(':checked')) {
            LoadSurveys();

            $divNewSurvey.show();
        }
        else {
            $messageNoSurveysInTable.hide();
            $divWithSurveys.hide();
            $divNewSurvey.hide();
        }
    }).trigger('change');

    $checkboxConfigEwt = $('#checkboxConfigEwt');
    var $divEwtConfiguration = $('#divEwtConfiguration');
    $checkboxConfigEwt.change(function () {
        if ($checkboxConfigEwt.is(':checked')) {
            $divEwtConfiguration.show();
        }
        else {
            $divEwtConfiguration.hide();
        }
    }).trigger('change');

    var $textboxMinutesPredictedAht = $('#textboxMinutesPredictedAht');
    var $spanMinutesPredictedAht = $('#spanMinutesPredictedAht');
    $textboxMinutesPredictedAht.change(function () {
        if (translationsLoaded) {
            $spanMinutesPredictedAht.text($.i18n('configuration-systemsettings-queues-minutes', $textboxMinutesPredictedAht.val()));
        }
        else {
            $spanMinutesPredictedAht.text($textboxMinutesPredictedAht.val());
        }
    }).trigger('change');

    var $textboxSecondsEwt = $('#textboxSecondsEwt');
    var $spanSecondsEwt = $('#spanSecondsEwt');
    $textboxSecondsEwt.change(function () {
        if (translationsLoaded) {
            $spanSecondsEwt.text($.i18n('configuration-systemsettings-queues-seconds', $textboxSecondsEwt.val()));
        }
        else {
            $spanSecondsEwt.text($textboxSecondsEwt.val());
        }
    }).trigger('change');

    var $checkBoxAsaDefault = $('input[type=checkbox][id$=checkboxASAPersonalized]');
    var $tableASADefault = $('table[rel=tableASABase]');
    var $textboxAsaDefault = $('input[id$=textboxAsaBase]')
    $textboxAsaDefault.prop('required', $checkBoxAsaDefault.is(':checked'));
    if (!$checkBoxAsaDefault.is(':checked')) {
        $textboxAsaDefault.prop('min', '');
        $textboxAsaDefault.prop('max', '');
    }
    $checkBoxAsaDefault.change(function () {
        if (this.checked) {
            $textboxAsaDefault.prop('min', '30');
            $textboxAsaDefault.prop('max', '3600');
            $tableASADefault.show();
        }
        else {
            $textboxAsaDefault.prop('min', '');
            $textboxAsaDefault.prop('max', '');
            $tableASADefault.hide();
        }
        $textboxAsaDefault.prop('required', this.checked);
    }).trigger('change');

    $dropdownQueueSurvey = $('#dropdownQueueSurvey');
    $spanQueueSurvey = $('#spanQueueSurvey');
    $messageSurveyDisabled = $('#messageSurveyDisabled');
    $dropdownQueueSurvey.change(function () {
        HideOptionsExternalSurvey();
    }).trigger('change');

    var $textboxQueueSurveyInvitation = $('#textboxQueueSurveyInvitation');
    var $messageFilterEmailSubjectFields = $('#messageFilterEmailSubjectFields');
    CallValidationFields($textboxQueueSurveyInvitation, $messageFilterEmailSubjectFields);
    $textboxQueueSurveyInvitation.trigger('change');

    $checkboxQueueSurveySendMailIfFailed = $('#checkboxQueueSurveySendMailIfFailed');

    var htmlEditorOptions = {
        height: 200,
        width: 'auto',
        fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
        bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
        controls:     // controls to add to the toolbar
            "bold italic underline | font size " +
            "style | color highlight removeformat | bullets numbering | outdent " +
            "indent | alignleft center alignright justify | " +
            "image link | cut copy paste pastetext | source",
    };

    var $textboxQueueSurveyEmailTemplate = $('#textboxQueueSurveyEmailTemplate');
    $textboxQueueSurveyEmailTemplate.cleditor(htmlEditorOptions);
    var $messageQueueSurveyEmailTemplate = $('#messageQueueSurveyEmailTemplate');
    CallValidationFields($textboxQueueSurveyEmailTemplate, $messageQueueSurveyEmailTemplate);
    var $divQueueSurveyEmail = $('#divQueueSurveyEmail');


    var $messageMailSignatureBody = $('#messageMailSignature');
    CallValidationFields($textboxMailSignature, $messageMailSignatureBody);

    $checkboxQueueSurveySendMailIfFailed.change(function () {
        if ($checkboxQueueSurveySendMailIfFailed.is(':checked')) {
            $divQueueSurveyEmail.show();
            $textboxQueueSurveyEmailTemplate.cleditor()[0].refresh();
        }
        else {
            $divQueueSurveyEmail.hide();
        }
    }).trigger('change');

    $listboxTagGroups = $('#listboxTagGroups');
    $hiddenTagGroups = $('#hiddenTagGroups');
    $hiddenSurveyTagGroup = $('#hiddenSurveyTagGroup');
    $listboxSurveyTagGroup = $('#listboxSurveyTagGroup');
    $hiddenSurveyTagGroupToIgnore = $('#hiddenSurveyTagGroupToIgnore');
    $listboxSurveyTagGruopToIgnore = $('#listboxSurveyTagGruopToIgnore');
    $textboxSurveysIgnoreTags = $('#textboxSurveysIgnoreTags');
    $listboxSLMinutesActionsTags = $('#listboxSLMinutesActionsTags');
    $hiddenSLMinutesActionsTags = $('#hiddenSLMinutesActionsTags');
    $listboxSLMinutesExpiredActionsTags = $('#listboxSLMinutesExpiredActionsTags');
    $hiddenSLMinutesExpiredActionsTags = $('#hiddenSLMinutesExpiredActionsTags');
    $listboxFirstAutomaticActionsTags = $('#listboxFirstAutomaticActionsTags');
    $listboxSecondAutomaticActionsTags = $('#listboxSecondAutomaticActionsTags');
    $hiddenFirstAutomaticActionsTag = $('#hiddenFirstAutomaticActionsTag');
    $hiddenSecondAutomaticActionsTag = $('#hiddenSecondAutomaticActionsTag');
    $dropdownlistFirstAutomaticActionQueues = $('#dropdownlistFirstAutomaticActionQueues');
    $dropdownlistSecondAutomaticActionQueues = $('#dropdownlistSecondAutomaticActionQueues');
    $trFirstAutomaticActionReplyEwtNoAgents = $('#trFirstAutomaticActionReplyEwtNoAgents');
    $trFirstAutomaticActionReplyEwtNotCalculated = $('#trFirstAutomaticActionReplyEwtNotCalculated');
    $trSecondAutomaticActionReplyEwtNoAgents = $('#trSecondAutomaticActionReplyEwtNoAgents');
    $trSecondAutomaticActionReplyEwtNotCalculated = $('#trSecondAutomaticActionReplyEwtNotCalculated');
    $trFirstAutomaticActionMinimumEWT = $('#trFirstAutomaticActionMinimumEWT');
    $trFirstAutomaticActionMinimumEnqueueMessages = $('#trFirstAutomaticActionMinimumEnqueueMessages');
    $trSecondAutomaticActionMinimumEWT = $('#trSecondAutomaticActionMinimumEWT');
    $trSecondAutomaticActionMinimumEnqueueMessages = $('#trSecondAutomaticActionMinimumEnqueueMessages');
    $checkboxFirstAutomaticActionNotifyChat = $('#checkboxFirstAutomaticActionNotifyChat');
    $checkboxFirstAutomaticActionsTransferQueue = $('#checkboxFirstAutomaticActionsTransferQueue');
    $textboxFirstAutomaticActionMinimumEWT = $('#textboxFirstAutomaticActionMinimumEWT');
    $textboxFirstAutomaticActionMinimumEnqueueMessages = $('#textboxFirstAutomaticActionMinimumEnqueueMessages');
    $checkboxFirstAutomaticActionReply = $('#checkboxFirstAutomaticActionReply');
    $textboxFirstAutomaticActionReplyEwtNoAgents = $('#textboxFirstAutomaticActionReplyEwtNoAgents');
    $textboxFirstAutomaticActionReplyEwtNotComputed = $('#textboxFirstAutomaticActionReplyEwtNotComputed');
    $dropdownlistFirstAutomaticActionMarkAsFinishChat = $('#dropdownlistFirstAutomaticActionMarkAsFinishChat');
    $textboxFirstAutomaticActionReplyChat = $('#textboxFirstAutomaticActionReplyChat');
    $checkboxFirstAutomaticActionApplyTags = $('#checkboxFirstAutomaticActionApplyTags');
    $textboxSecondAutomaticActionsSeconds = $('#textboxSecondAutomaticActionsSeconds');
    $checkboxSecondAutomaticActionsTransferQueue = $('#checkboxSecondAutomaticActionsTransferQueue');
    $checkboxSecondAutomaticActionReply = $('#checkboxSecondAutomaticActionReply');
    $textboxSecondAutomaticActionMinimumEWT = $('#textboxSecondAutomaticActionMinimumEWT');
    $textboxSecondAutomaticActionMinimumEnqueueMessages = $('#textboxSecondAutomaticActionMinimumEnqueueMessages');
    $textboxSecondAutomaticActionReplyEwtNoAgents = $('#textboxSecondAutomaticActionReplyEwtNoAgents');
    $textboxSecondAutomaticActionReplyEwtNotComputed = $('#textboxSecondAutomaticActionReplyEwtNotComputed');
    $checkboxSecondAutomaticActionNotifyChat = $('#checkboxSecondAutomaticActionNotifyChat');
    $textboxSecondAutomaticActionChatReplyText = $('#textboxSecondAutomaticActionChatReplyText');
    $dropdownlistSecondAutomaticActionMarkAsFinishChat = $('#dropdownlistSecondAutomaticActionMarkAsFinishChat');
    $checkboxSecondAutomaticActionApplyTags = $('#checkboxSecondAutomaticActionApplyTags');
    $hiddenSecondAutomaticActionsTag = $('#hiddenSecondAutomaticActionsTag');
    $tableAllowSecondAutomaticActions = $('#tableAllowSecondAutomaticActions');
    $trFirstAutomaticActionMinimumEWTChat = $('#trFirstAutomaticActionMinimumEWTChat');
    $textboxFirstAutomaticActionMinimumEWTChat = $('#textboxFirstAutomaticActionMinimumEWTChat');
    $trFirstAutomaticActionMinimumEnqueueMessagesChat = $('#trFirstAutomaticActionMinimumEnqueueMessagesChat');
    $textboxFirstAutomaticActionMinimumEnqueueMessagesChat = $('#textboxFirstAutomaticActionMinimumEnqueueMessagesChat');
    $trFirstAutomaticActionReplyEwtNoAgentsChat = $('#trFirstAutomaticActionReplyEwtNoAgentsChat');
    $textboxFirstAutomaticActionReplyEwtNoAgentsChat = $('#textboxFirstAutomaticActionReplyEwtNoAgentsChat');
    $trFirstAutomaticActionReplyEwtNotCalculatedChat = $('#trFirstAutomaticActionReplyEwtNotCalculatedChat');
    $textboxFirstAutomaticActionReplyEwtNotCalculatedChat = $('#textboxFirstAutomaticActionReplyEwtNotCalculatedChat');
    $trSecondAutomaticActionMinimumEWTChat = $('#trSecondAutomaticActionMinimumEWTChat');
    $textboxSecondAutomaticActionMinimumEWTChat = $('#textboxSecondAutomaticActionMinimumEWTChat');
    $trSecondAutomaticActionMinimumEnqueueMessagesChat = $('#trSecondAutomaticActionMinimumEnqueueMessagesChat');
    $textboxSecondAutomaticActionMinimumEnqueueMessagesChat = $('#textboxSecondAutomaticActionMinimumEnqueueMessagesChat');
    $trSecondAutomaticActionReplyEwtNoAgentsChat = $('#trSecondAutomaticActionReplyEwtNoAgentsChat');
    $textboxSecondAutomaticActionReplyEwtNoAgentsChat = $('#textboxSecondAutomaticActionReplyEwtNoAgentsChat');
    $trSecondAutomaticActionReplyEwtNotCalculatedChat = $('#trSecondAutomaticActionReplyEwtNotCalculatedChat');
    $textboxSecondAutomaticActionReplyEwtNotCalculatedChat = $('#textboxSecondAutomaticActionReplyEwtNotCalculatedChat');
    function ReloadQueueTags() {
        let configuredTags = null;

        if (typeof (queueTags) === 'object' &&
            queueTags !== null) {
            configuredTags = queueTags;
            //configuredTags.push(queueTags);
        }

        $textboxTags = $('#textboxTags');
        $textboxTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
            method: 'POST',
            queryParam: 'q',
            searchDelay: 300,
            minChars: 3,
            //tokenLimit: 1,
            propertyToSearch: 'FullName',
            //jsonContainer: 'd',
            preventDuplicates: true,
            tokenValue: 'ID',
            contentType: 'json',
            resultsLimit: 20,
            excludeCurrent: true,
            styles: {
                dropdown: {
                    'max-height': '200px',
                    'overflow-y': 'auto'
                },
                tokenList: {
                    'width': '100%'
                }
            },
            prePopulate: configuredTags,
            onSend: function (params) {
                params.data = JSON.stringify(params.data);
                params.contentType = "application/json; charset=utf-8";
            },
            onResult: function (results, query, cache_key) {
                var settingsTags = $textboxTags.data('settings');
                if (typeof (settingsTags.local_data) === 'undefined') {
                    delete settingsTags.url;
                    settingsTags.local_data = results.d;

                    return $.grep(settingsTags.local_data, function (row) {
                        return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                    });
                }

                return results;
            }
        });

        let configuredSLMinutesActionTags = null;

        if (typeof (slMinutesActionTags) === 'object' &&
            slMinutesActionTags !== null) {
            configuredSLMinutesActionTags = slMinutesActionTags;
            //configuredSLMinutesActionTags.push(slMinutesActionTags);
        }

        $textboxSLMinutesActionsTags = $('#textboxSLMinutesActionsTags');
        $textboxSLMinutesActionsTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
            method: 'POST',
            queryParam: 'q',
            searchDelay: 300,
            minChars: 3,
            //tokenLimit: 1,
            propertyToSearch: 'FullName',
            //jsonContainer: 'd',
            preventDuplicates: true,
            tokenValue: 'ID',
            contentType: 'json',
            resultsLimit: 20,
            excludeCurrent: true,
            styles: {
                dropdown: {
                    'max-height': '200px',
                    'overflow-y': 'auto'
                },
                tokenList: {
                    'width': '100%'
                }
            },
            prePopulate: configuredSLMinutesActionTags,
            onSend: function (params) {
                params.data = JSON.stringify(params.data);
                params.contentType = "application/json; charset=utf-8";
            },
            onResult: function (results, query, cache_key) {
                var settingsTags = $textboxSLMinutesActionsTags.data('settings');
                if (typeof (settingsTags.local_data) === 'undefined') {
                    delete settingsTags.url;
                    settingsTags.local_data = results.d;

                    return $.grep(settingsTags.local_data, function (row) {
                        return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                    });
                }

                return results;
            }
        });

        let configuredSLMinutesExpiredActionsTags = null;

        if (typeof (slMinutesExpiredActionsTags) === 'object' &&
            slMinutesExpiredActionsTags !== null) {
            configuredSLMinutesExpiredActionsTags = slMinutesExpiredActionsTags;
            //configuredSLMinutesExpiredActionsTags.push(slMinutesExpiredActionsTags);
        }

        $textboxSLMinutesExpiredActionsTags = $('#textboxSLMinutesExpiredActionsTags');
        $textboxSLMinutesExpiredActionsTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
            method: 'POST',
            queryParam: 'q',
            searchDelay: 300,
            minChars: 3,
            //tokenLimit: 1,
            propertyToSearch: 'FullName',
            //jsonContainer: 'd',
            preventDuplicates: true,
            tokenValue: 'ID',
            contentType: 'json',
            resultsLimit: 20,
            excludeCurrent: true,
            styles: {
                dropdown: {
                    'max-height': '200px',
                    'overflow-y': 'auto'
                },
                tokenList: {
                    'width': '100%'
                }
            },
            prePopulate: configuredSLMinutesExpiredActionsTags,
            onSend: function (params) {
                params.data = JSON.stringify(params.data);
                params.contentType = "application/json; charset=utf-8";
            },
            onResult: function (results, query, cache_key) {
                var settingsTags = $textboxSLMinutesExpiredActionsTags.data('settings');
                if (typeof (settingsTags.local_data) === 'undefined') {
                    delete settingsTags.url;
                    settingsTags.local_data = results.d;

                    return $.grep(settingsTags.local_data, function (row) {
                        return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                    });
                }

                return results;
            }
        });

        let configuredFirstAutomaticActionsTags = null;

        if (typeof (firstAutomaticActionsTags) === 'object' &&
            firstAutomaticActionsTags !== null) {
            configuredFirstAutomaticActionsTags = firstAutomaticActionsTags;
            //configuredFirstAutomaticActionsTags.push(firstAutomaticActionsTags);
        }

        $textboxFirstAutomaticActionsTags = $('#textboxFirstAutomaticActionsTags');
        $textboxFirstAutomaticActionsTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
            method: 'POST',
            queryParam: 'q',
            searchDelay: 300,
            minChars: 3,
            //tokenLimit: 1,
            propertyToSearch: 'FullName',
            //jsonContainer: 'd',
            preventDuplicates: true,
            tokenValue: 'ID',
            contentType: 'json',
            resultsLimit: 20,
            excludeCurrent: true,
            styles: {
                dropdown: {
                    'max-height': '200px',
                    'overflow-y': 'auto'
                },
                tokenList: {
                    'width': '100%'
                }
            },
            prePopulate: configuredFirstAutomaticActionsTags,
            onSend: function (params) {
                params.data = JSON.stringify(params.data);
                params.contentType = "application/json; charset=utf-8";
            },
            onResult: function (results, query, cache_key) {
                var settingsTags = $textboxFirstAutomaticActionsTags.data('settings');
                if (typeof (settingsTags.local_data) === 'undefined') {
                    delete settingsTags.url;
                    settingsTags.local_data = results.d;

                    return $.grep(settingsTags.local_data, function (row) {
                        return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                    });
                }

                return results;
            }
        });

        let configuredSecondAutomaticActionsTags = null;

        if (typeof (secondAutomaticActionsTags) === 'object' &&
            secondAutomaticActionsTags !== null) {
            configuredSecondAutomaticActionsTags = secondAutomaticActionsTags;
            //configuredSecondAutomaticActionsTags.push(secondAutomaticActionsTags);
        }

        $textboxSecondAutomaticActionsTags = $('#textboxSecondAutomaticActionsTags');
        $textboxSecondAutomaticActionsTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
            method: 'POST',
            queryParam: 'q',
            searchDelay: 300,
            minChars: 3,
            //tokenLimit: 1,
            propertyToSearch: 'FullName',
            //jsonContainer: 'd',
            preventDuplicates: true,
            tokenValue: 'ID',
            contentType: 'json',
            resultsLimit: 20,
            excludeCurrent: true,
            styles: {
                dropdown: {
                    'max-height': '200px',
                    'overflow-y': 'auto'
                },
                tokenList: {
                    'width': '100%'
                }
            },
            prePopulate: configuredSecondAutomaticActionsTags,
            onSend: function (params) {
                params.data = JSON.stringify(params.data);
                params.contentType = "application/json; charset=utf-8";
            },
            onResult: function (results, query, cache_key) {
                var settingsTags = $textboxSecondAutomaticActionsTags.data('settings');
                if (typeof (settingsTags.local_data) === 'undefined') {
                    delete settingsTags.url;
                    settingsTags.local_data = results.d;

                    return $.grep(settingsTags.local_data, function (row) {
                        return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                    });
                }

                return results;
            }
        });
    }

    $checkboxFilterShowID.change(function () {
        datatable.column(0).visible($checkboxFilterShowID.is(':checked'));
    });

    $tabsTasks = $('#tabsTasks');
    $tabsTasks.tabs({
        activate: function (tabs, page) {
            $.colorbox.resize();

            let $divTab;
            if ((page.newPanel instanceof jQuery)) {
                $divTab = page.newPanel;
            }
            else {
                $divTab = $(page.newPanel.selector);
            }
            let tabId = $divTab.get(0).id;
        }
    });
    $divTaskReturnToQueue = $('#divTaskReturnToQueue');
    $messageTaskQueuessThatCanTransferToQueueEmpty = $('#messageTaskQueuessThatCanTransferToQueueEmpty');
    $divTaskQueuessThatCanTransferToQueueNoEmpty = $('#divTaskQueuessThatCanTransferToQueueNoEmpty');
    $selectTasksQueuesThatCanTransferToQueue = $('#selectTasksQueuesThatCanTransferToQueue');
    $selectTasksQueuesThatCanTransferToQueue.multiselect({ multiple: true, noneSelectedText: "Seleccione al menos una cola", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
    $buttonTasksAccept = $('#buttonTasksAccept');

    if (typeof (tagGroups) !== 'undefined' && tagGroups !== null && tagGroups.length > 0) {

        let addOption = function (tagGroup, $select) {
            let $option = $('<option></option>');
            $option.text(tagGroup.Name);
            $option.val(tagGroup.ID);
            $select.append($option);
        };

        for (var i = 0; i < tagGroups.length; i++) {
            addOption(tagGroups[i], $listboxTagGroups);
            addOption(tagGroups[i], $listboxSurveyTagGroup);
            addOption(tagGroups[i], $listboxSurveyTagGruopToIgnore);
        }

        let selectOptions = function ($hidden, $select) {
            let value = $hidden.val();
            if (typeof (value) === 'string' && value.length > 0) {
                value = value.split(',');
                $select.val(value);
            }
        };

        selectOptions($hiddenTagGroups, $listboxTagGroups);
        selectOptions($hiddenSurveyTagGroup, $listboxSurveyTagGroup);
        selectOptions($hiddenSurveyTagGroupToIgnore, $listboxSurveyTagGruopToIgnore);
    }

    $listboxTagGroups.multiselect({ multiple: true, noneSelectedText: "Seleccione los grupos de etiquetas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
    $listboxSurveyTagGroup.multiselect({ multiple: true, noneSelectedText: "Seleccione los grupos de etiquetas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
    $listboxSurveyTagGruopToIgnore.multiselect({ multiple: true, noneSelectedText: "Seleccione los grupos de etiquetas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
    $dropdownlistFirstAutomaticActionQueues.multiselect({ multiple: false, noneSelectedText: "Seleccione almenos una cola", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
    $dropdownlistSecondAutomaticActionQueues.multiselect({ multiple: false, noneSelectedText: "Seleccione almenos una cola", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

    $divExport = $('#divExport');
    $divExportStep1 = $('#divExportStep1');
    $selectExportFormat = $('#selectExportFormat', $divExportStep1);

    $checkboxAllowFirstAutomaticActions = $('#checkboxAllowFirstAutomaticActions');
    $checkboxAllowSecondAutomaticActions = $('#checkboxAllowSecondAutomaticActions');
    $divFirstAutomaticActionConfiguration = $('#divFirstAutomaticActionConfiguration');
    $divSecondAutomaticActionConfiguration = $('#divSecondAutomaticActionConfiguration');
    $panelSecondAutomaticActions = $('div[id$=panelSecondAutomaticAction]');
    $textboxFirstAutomaticActionSeconds = $('#textboxFirstAutomaticActionSeconds');
    $checkboxAllowFirstAutomaticActions.change(function () {

        if ($checkboxAllowFirstAutomaticActions.is(':checked')) {
            $divFirstAutomaticActionConfiguration.show();

            $panelSecondAutomaticActions.show();
        } else {
            $divFirstAutomaticActionConfiguration.hide();

            $panelSecondAutomaticActions.hide();
            $checkboxAllowSecondAutomaticActions.prop('checked', false);
            $checkboxAllowSecondAutomaticActions.change();
        }
    }).trigger('change');

    $checkboxAllowSecondAutomaticActions.change(function () {

        if ($checkboxAllowSecondAutomaticActions.is(':checked')) {
            $divSecondAutomaticActionConfiguration.show();
        } else {
            $divSecondAutomaticActionConfiguration.hide();
        }
    }).trigger('change');

    $checkboxFirstAutomaticActionsTransferQueue.change(function () {
        if ($checkboxFirstAutomaticActionsTransferQueue.is(':checked')) {
            $checkboxAllowSecondAutomaticActions.prop('checked', false);
            $checkboxAllowSecondAutomaticActions.change();
            $checkboxAllowSecondAutomaticActions.attr('disabled', 'disabled');
        } else {
            $checkboxAllowSecondAutomaticActions.removeAttr('disabled', 'disabled');
        }
    }).trigger('change');

    $textboxFirstAutomaticActionReplyText = $('#textboxFirstAutomaticActionReplyText');
    $textboxSecondAutomaticActionReplyText = $('#textboxSecondAutomaticActionReplyText');

    $textboxFirstAutomaticActionReplyText.change(function () {

        ValidateEwtSelected($textboxFirstAutomaticActionReplyText.val(), true);

    }).trigger('change');
    $textboxSecondAutomaticActionReplyText.change(function () {

        ValidateEwtSelected($textboxSecondAutomaticActionReplyText.val(), false);

    }).trigger('change');

    $textboxFirstAutomaticActionReplyChat.change(function () {

        ValidateEwtSelectedChat($textboxFirstAutomaticActionReplyChat.val(), true);

    }).trigger('change');
    $textboxSecondAutomaticActionChatReplyText.change(function () {

        ValidateEwtSelectedChat($textboxSecondAutomaticActionChatReplyText.val(), false);

    }).trigger('change');

    $textboxFirstAutomaticActionReplyText.on('keyup', function () {
        $('#spanFirstAutomaticActionReplyTextCounter').text($textboxFirstAutomaticActionReplyText.val().length);
    }).trigger('keyup');

    $textboxFirstAutomaticActionReplyEwtNoAgents.on('keyup', function () {
        $('#spanFirstAutomaticActionReplyEwtNoAgentsCounter').text($textboxFirstAutomaticActionReplyEwtNoAgents.val().length);
    }).trigger('keyup');

    $textboxFirstAutomaticActionReplyEwtNotComputed.on('keyup', function () {
        $('#spanFirstAutomaticActionReplyEwtNotComputedCounter').text($textboxFirstAutomaticActionReplyEwtNotComputed.val().length);
    }).trigger('keyup');

    $textboxSecondAutomaticActionReplyText.on('keyup', function () {
        $('#spanSecondAutomaticActionReplyTextCounter').text($textboxSecondAutomaticActionReplyText.val().length);
    }).trigger('keyup');

    $textboxSecondAutomaticActionReplyEwtNoAgents.on('keyup', function () {
        $('#spanSecondAutomaticActionReplyEwtNoAgentsCounter').text($textboxSecondAutomaticActionReplyEwtNoAgents.val().length);
    }).trigger('keyup');

    $textboxSecondAutomaticActionReplyEwtNotComputed.on('keyup', function () {
        $('#spanSecondAutomaticActionReplyEwtNotComputedCounter').text($textboxSecondAutomaticActionReplyEwtNotComputed.val().length);
    }).trigger('keyup');

    let $textboxAgentIdleMinutes = $('#textboxAgentIdleMinutes');
    let $trAgentIdleReturnToQueue = $('#trAgentIdleReturnToQueue');
    $textboxAgentIdleMinutes.change(function () {
        let value = $textboxAgentIdleMinutes.val();
        if (!/^[0-9]{1,2}$/.test(value)) {
            $trAgentIdleReturnToQueue.hide();
            return;
        }
        value = parseInt(value, 10);
        if (isNaN(value) || value <= 0) {
            $trAgentIdleReturnToQueue.hide();
            return;
        }

        $trAgentIdleReturnToQueue.show();
    }).trigger('change');

    $('#checkboxSurveySendIfNewCaseExists').click(function () {
        $('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', false);
        $('#checkboxSurveySendIfNewCaseHasTag').prop('checked', false);
    });

    $('#checkboxSurveySendIfNewCaseClosedByYflow').click(function () {
        $('#checkboxSurveySendIfNewCaseExists').prop('checked', false);
        $('#checkboxSurveySendIfNewCaseHasTag').prop('checked', false);
    });

    $('#checkboxSurveySendIfNewCaseHasTag').click(function () {
        $('#checkboxSurveySendIfNewCaseExists').prop('checked', false);
        $('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', false);
    });

    ReloadQueueTags();
});

function i18nLoaded() {
    $listboxTagGroups.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_tag_groups"));
    $listboxSurveyTagGroup.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_tag_groups"));
    $listboxSurveyTagGruopToIgnore.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_tag_groups"));
    $listboxSLMinutesActionsTags.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_tag"));
    $listboxSLMinutesExpiredActionsTags.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_tag"));
    $selectTasksQueuesThatCanTransferToQueue.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_queues"));
    $dropdownlistSLMinutesActionsAssignToQueue.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_one_queue"));
    $dropdownlistSLMinutesExpiredActionsAssignToQueue.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_one_queue"));
    $listboxQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_one_queue"));
    $listboxSupervisors.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_supervisors"));
    $listboxUsers.multiselect('option', 'noneSelectedText', $.i18n("configuration-queues-select_users"));
    $listboxDontReserveWithStatus.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags-none"));

    if ($panelList.length > 0) {
        RetrieveQueues();
    }
}

function ValidateEwtSelectedChat(value, first) {

    if (typeof first !== 'boolean')
        first = true;

    if (first) {
        $trFirstAutomaticActionReplyEwtNoAgentsChat.hide();
        $trFirstAutomaticActionReplyEwtNotCalculatedChat.hide();
        $trFirstAutomaticActionMinimumEWTChat.hide();
        $trFirstAutomaticActionMinimumEnqueueMessagesChat.hide();
    } else {
        $trSecondAutomaticActionReplyEwtNoAgentsChat.hide();
        $trSecondAutomaticActionReplyEwtNotCalculatedChat.hide();
        $trSecondAutomaticActionMinimumEWTChat.hide();
        $trSecondAutomaticActionMinimumEnqueueMessagesChat.hide();
    }

    if (typeof value !== 'string')
        return;

    if (value.length == 0)
        return;

    if (first) {
        if (value.includes("@@EWT@@")) {
            $trFirstAutomaticActionReplyEwtNoAgentsChat.show();
            $trFirstAutomaticActionReplyEwtNotCalculatedChat.show();
        }

        if (value.includes("@@EWT@@") || value.includes("@@POSMSGCOLA@@")) {
            $trFirstAutomaticActionMinimumEWTChat.show();
            $trFirstAutomaticActionMinimumEnqueueMessagesChat.show();
        }
    } else {

        if (value.includes("@@EWT@@")) {
            $trSecondAutomaticActionReplyEwtNoAgentsChat.show();
            $trSecondAutomaticActionReplyEwtNotCalculatedChat.show();
        }

        if (value.includes("@@EWT@@") || value.includes("@@POSMSGCOLA@@")) {
            $trSecondAutomaticActionMinimumEWTChat.show();
            $trSecondAutomaticActionMinimumEnqueueMessagesChat.show();
        }
    }
};

function ValidateEwtSelected(value, first) {

    if (typeof first !== 'boolean')
        first = true;

    if (first) {
        $trFirstAutomaticActionReplyEwtNoAgents.hide();
        $trFirstAutomaticActionReplyEwtNotCalculated.hide();
        $trFirstAutomaticActionMinimumEWT.hide();
        $trFirstAutomaticActionMinimumEnqueueMessages.hide();
    } else {
        $trSecondAutomaticActionReplyEwtNoAgents.hide();
        $trSecondAutomaticActionReplyEwtNotCalculated.hide();
        $trSecondAutomaticActionMinimumEWT.hide();
        $trSecondAutomaticActionMinimumEnqueueMessages.hide();
    }

    if (typeof value !== 'string')
        return;

    if (value.length == 0)
        return;

    if (first) {
        if (value.includes("@@EWT@@")) {
            $trFirstAutomaticActionReplyEwtNoAgents.show();
            $trFirstAutomaticActionReplyEwtNotCalculated.show();
        }

        if (value.includes("@@EWT@@") || value.includes("@@POSMSGCOLA@@")) {
            $trFirstAutomaticActionMinimumEWT.show();
            $trFirstAutomaticActionMinimumEnqueueMessages.show();
        }
    } else {

        if (value.includes("@@EWT@@")) {
            $trSecondAutomaticActionReplyEwtNoAgents.show();
            $trSecondAutomaticActionReplyEwtNotCalculated.show();
        }

        if (value.includes("@@EWT@@") || value.includes("@@POSMSGCOLA@@")) {
            $trSecondAutomaticActionMinimumEWT.show();
            $trSecondAutomaticActionMinimumEnqueueMessages.show();
        }
    }
};

function RetrieveQueues() {
    if (!translationsLoaded) {
        return;
    }

    $divLoadingQueues.show();
    $tableQueues.hide();
    $panelButtons.hide();

    $.ajax({
        type: "POST",
        url: "Queues.aspx/RetrieveQueues",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        data: null,
        success: function (result) {
            queues = null;

            if (result.d.Success == true) {
                fields = result.d.Fields;
                queues = [];

                if (result.d.Queues !== null) {
                    for (let i = 0; i < result.d.Queues.length; i++) {
                        let queue = {};

                        for (let j = 0; j < fields.length; j++) {
                            queue[fields[j]] = result.d.Queues[i][j];
                        }

                        queues.push(queue);
                    }
                }

                FillTable();
            }
            else {
				AlertDialog($.i18n("configuration-queues-title"), $.i18n("configuration-queues-cannot_recover_queues_list"))
            }

            $divLoadingQueues.hide();
        },
        error: function () {
			AlertDialog($.i18n("configuration-queues-title"), $.i18n("configuration-queues-cannot_recover_queues_list"));
            $divLoadingQueues.hide();
        }
    });
}

function FillTable() {
    if (queues !== null && queues.length > 0) {
        if (datatable !== null &&
            typeof (datatable.destroy) === 'function') {
            datatable.destroy();
            datatable = null;
        }

        datatable = $tableQueues.DataTable({
            ordering: false,
            searching: true,
            paging: true,
            pageLength: 20,
			lengthChange: false,
			deferRender: true,
			data: queues,
			createdRow: function (row, data, dataIndex) {
				let $tr = $(row);
				$tr.prop('queue', data);
			},
			columns: [
				{
					render: function (data, type, queue) {
						return queue.ID;
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.attr('rel', 'id');
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						return queue.Name;
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						let shortenedDescription = queue.Description;

						if (typeof(shortenedDescription) !== 'string') {
							shortenedDescription = '';
						}
						else if (shortenedDescription.length >= 20) {
							shortenedDescription = shortenedDescription.substring(0, 20) + "...";
						}
						return shortenedDescription;
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						return (queue.Key === null ? '' : queue.Key);
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.Agents === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-user-headset"></span> x <a rel="agents">${queue.Agents}</a>`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');

						let $anchor = $('a[rel=agents]', $td);
						$anchor.click(rowData, function (e) {
							ShowQueueAgents(e.data);
						});
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.RelatedServices === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-cogs"></span> x <a rel="services">${queue.RelatedServices}</a>`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');

						let $anchor = $('a[rel=services]', $td);
						$anchor.click(rowData, function (e) {
							ShowQueueRelatedServices(e.data);
						});
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.RelatedQueues === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-cogs"></span> x <a rel="queues">${queue.RelatedQueues}</a>`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');

						let $anchor = $('a[rel=queues]', $td);
						$anchor.click(rowData, function (e) {
							ShowQueueRelatedQueues(e.data);
						});
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.RelatedQueueGroups === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-cogs"></span> x <a rel="queuegroups">${queue.RelatedQueueGroups}</a>`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');

						let $anchor = $('a[rel=queuegroups]', $td);
						$anchor.click(rowData, function (e) {
							ShowQueueRelatedQueueGroups(e.data);
						});
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.WithSL) {
							return '<span class="fa fa-lg fa-yes"></span>';
						}
						else {
							return '';
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');
					},
					defaultContent: ''
                },
                {
                    render: function (data, type, queue) {
                        if (queue.WithAutomaticActions) {
                            return '<span class="fa fa-lg fa-yes"></span>';
                        }
                        else {
                            return '';
                        }
                    },
                    createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
                        let $td = $(td);
                        $td.css('text-align', 'center');
                    },
                    defaultContent: ''
                },
				{
					render: function (data, type, queue) {
						if (queue.SubscribedAgents === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-user-headset"></span> x ${queue.SubscribedAgents}`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.Messages === 0) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-envelope"></span> x ${queue.Messages}`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (!queue.WithSurveys) {
							return '';
						}
						else {
							return `<span class="fa fa-lg fa-yes"></span> x ${queue.SurveysCount}`;
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');
					},
					defaultContent: ''
				},
				{
					render: function (data, type, queue) {
						if (queue.Enabled) {
							return '<span class="fa fa-lg fa-yes"></span>';
						}
						else {
							return '<span class="fa fa-lg fa-no"></span>';
						}
					},
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('text-align', 'center');
					},
					defaultContent: ''
				},
				{
					data: null,
					className: 'icons',
					width: '100px',
					createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
						let $td = $(td);
						$td.css('white-space', 'nowrap');

						let queue = cellData;

						let $anchorCannotBeDisabled = $('a[rel=cannotbedisabled]', $td);
						$anchorCannotBeDisabled.click(queue, function (e) {
							ShowCannotBeDisabled();
						});

						let $anchorDisable = $('a[rel=disable]', $td);
						$anchorDisable.click(queue, function (e) {
							ConfirmDialog({
								title: $.i18n("configuration-queues-disable_queue"),
								message: $.i18n("configuration-queues-disable_queue-confirm", e.data.Name),
								onAccept: function (queueId) {
									$hiddenActionQueueID.val(queueId);
									$hiddenActionName.val('Disable');
									$buttonAction[0].click();
								},
								onCancel: $.colorbox.close,
								acceptArguments: e.data.ID,
								acceptText: $.i18n('globals-accept'),
								cancelText: $.i18n('globals-cancel'),
								width: '500px',
								height: '180px'
							});
						});

						let $anchorCannotBeEnabled = $('a[rel=cannotbeenabled]', $td);
						$anchorCannotBeEnabled.click(queue, function (e) {
							ShowCannotBeEnabled();
						});

						let $anchorEnable = $('a[rel=enable]', $td);
						$anchorEnable.click(queue, function (e) {
							$hiddenActionQueueID.val(e.data.ID);
							$hiddenActionName.val('Enable');
							$buttonAction[0].click();
						});

						let $anchorEdit = $('a[rel=edit]', $td);
						$anchorEdit.click(queue, function (e) {
							$hiddenActionQueueID.val(e.data.ID);
							$hiddenActionName.val('EditQueue');
                            $buttonAction[0].click();
                            ReloadTags();
						});

						let $anchorDelete = $('a[rel=delete]', $td);
						$anchorDelete.click(queue, function (e) {
							ShowDeleteDialog(e.data.ID, e.data.Enabled);
						});

						let $anchorCopy = $('a[rel=copy]', $td);
						$anchorCopy.click(queue, function (e) {
							ShowCopyDialog(e.data.ID);
						});

						let $anchorShowMoreInfo = $('a[rel=more]', $td);
						$anchorShowMoreInfo.click(queue, function (e) {
							ShowQueueConfig(e.data.ID);
						});

						let $anchorTasks = $('a[rel=tasks]', $td);
						$anchorTasks.click(queue, function (e) {
							ShowQueueTasks(e.data);
						});
					},
					render: function (data, type, queue) {
						let html = '';

						if (queue.Messages > 0 || queue.SubscribedAgents > 0) {
						}
						else {
							if (queue.Enabled) {
								if (queue.CannotBeDisabled) {
									html += '<a class="action" rel="cannotbedisabled"><span class="fa fa-lg fa-toggle-off" title="Deshabilitar" data-i18n-title="globals-disable"></span></a>';
								}
								else {
									html += '<a class="action" rel="disable"><span class="fa fa-lg fa-toggle-off" title="Deshabilitar" data-i18n-title="globals-disable"></span></a>';
								}
							}
							else {
								if (queue.CannotBeEnabled) {
									html += '<a class="action" rel="cannotbeenabled"><span class="fa fa-lg fa-toggle-on" title="Habilitar" data-i18n-title="globals-enable"></span></a>';
								}
								else {
									html += '<a class="action" rel="enable"><span class="fa fa-lg fa-toggle-on" title="Habilitar" data-i18n-title="globals-enable"></span></a>';
								}
							}
						}

						html += '<a class="action" rel="edit"><span class="fa fa-lg fa-edit" title="Editar" data-i18n-title="globals-edit"></span></a>';

						if (queue.CanBeDeleted) {
							html += '<a class="action" rel="delete"><span class="fa fa-lg fa-trash" title="Eliminar" data-i18n-title="globals-delete"></span></a>';
						}

						html += '<a class="action" rel="copy"><span class="fa fa-lg fa-clone" title="Copiar" data-i18n-title="globals-copy"></span></a>';

						html += '<a class="action" rel="more"><span class="fa fa-lg fa-search-plus" title="Ver más información" data-i18n-title="globals-viewmore"></span></a>';

						if (queue.Enabled) {
							html += '<a class="action" rel="tasks"><span class="fa fa-lg fa-tasks" title="Tareas masivas" data-i18n-title="globals-tasks"></span></a>';
						}

						return html;
					}
				}
			],
            initComplete: function (settings, json) {
                var $tdTextFilterPlaceholder = $('#tdTextFilterPlaceholder');
                var $currentInput = $('input[type=search]', $tdTextFilterPlaceholder);
                $currentInput.remove();

                var $tableProfiles_filter = $('#tableQueues_filter');
                var $input = $('input[type=search]', $tableProfiles_filter);
                $input.detach();
                $input.addClass('inputtext normal-readonly');
                $tdTextFilterPlaceholder.append($input);
                $tableProfiles_filter.remove();
                $input.attr('autocomplete', 'new-password');
                $input.attr('name', 'queues-search');
                $input.attr('readonly', 'readonly');
                $input.val('');
                $input.focus(function () {
                    this.removeAttribute('readonly');
                });
            }
        });

		datatable.column(0).visible($checkboxFilterShowID.is(':checked'));

		LoadCompositedElements();

		$tableQueues.show();
		$panelButtons.show();
    }
    else {
        $tableQueues.hide();
        $messageNoQueues.show();
        $anchorFilter.hide();
        $panelButtons.show();
    }
}

function LoadSurveys() {
    editSurvey = false;
    surveys = typeof (surveys) === 'undefined' ? [] : surveys;

    if (surveys !== 'undefined' && surveys.length > 0 && surveys[0].Name == "") {
        surveys.splice(0, 1);
    }

    if (surveys == null || surveys.length == 0) {
        $divWithSurveys.hide();
        $messageNoSurveysInTable.show();
        return;
    }

    $messageNoSurveysInTable.hide();

    var $tbody = $('tbody', $tableSurveys);
    $tbody.empty();

    for (var i = 0; i < surveys.length; i++) {
        var $tr = $('<tr></tr>');
        $tr.addClass(i % 2 === 0 ? 'normal' : 'alternate');

        var $td;
        $td = $('<td></td>');
        $td.text(surveys[i].Name);
        $tr.append($td);

        $td = $('<td class="icons" style="white-space: nowrap; width: 100px"></td>');
        $tr.append($td);

        var $anchorEdit = $('<a class="action"><span class="fa fa-lg fa-edit" title="Editar" data-i18n-title="globals-edit"></span></a>');
        $td.append($anchorEdit);
        $anchorEdit.click(surveys[i], function (e) {
            EditSurvey(e.data.ID, surveys);
        });

        var $anchorDelete = $('<a class="action"><span class="fa fa-lg fa-trash" title="Eliminar" data-i18n-title="globals-delete"></span></a>');
        $td.append($anchorDelete);
        $anchorDelete.click(surveys[i], function (e) {
            ShowDeleteSurveyDialog(e.data.ID, e.data.Name);
        });

        $tbody.append($tr);
    }

    LoadCompositedElements();

    if (typeof (drake) === 'undefined') {
        drake = dragula([document.getElementById('bodySurveys')]);

        drake.on('drop', (el, target) => {
            var surveysSorted = [];
            $('#tableSurveys tr').each(function () {
                if ($(this).find("td:first").length > 0) {
                    var actualSurvey = $(this).find("td:first").html();
                    surveysSorted.push(
                        surveys.find(s => s.Name == actualSurvey)
                    );
                }
            });
            surveys = surveysSorted;
        });
    }

    $divWithSurveys.show();
}

function buttonSave() {
    $hiddenSurveyList.val(JSON.stringify(surveys.map(s => s.QueueSurveyConfiguration)));

    var tagGroups = $listboxTagGroups.val();
    if (tagGroups != null && tagGroups.length > 0) {
        $hiddenTagGroups.val(tagGroups.join(','));
    };
    //$('#buttonSaveHidden')[0].click();;
}

function ShowDeleteSurveyDialog(surveyID, surveyName) {
    var message = $.i18n('configuration-surveys-delete_survey', surveyName);
    ConfirmDialog({
        title: $.i18n('configuration-surveys-confirm-title'),
        message: message,
        onAccept: function (args) {
            LoadingDialog({
                title: $.i18n('globals-loading'),
                timeout: 300,
                autoClose: false,
                onTimeout: function () {
                    for (let i = surveys.length - 1; i >= 0; i--) {
                        if (surveys[i].ID === surveyID) {
                            surveys.splice(i, 1);
                        }
                    }
                    $dropdownQueueSurvey.append("<option value='" + surveyID + "'>" + surveyName + "</option>");
                    LoadSurveys();
                    $.colorbox.close();
                }
            });
            $.colorbox.resize();
        },
        onCancel: $.colorbox.close,
        acceptText: $.i18n('globals-accept'),
        cancelText: $.i18n('globals-cancel'),
        width: '500px',
        height: '250px',
        closeRightPanel: true
    });
}

function ReturnTableSurvey() {
    ResetSurveyElements();

    LoadSurveys();

    $.colorbox.close();
}

function ResetSurveyElements() {
    $('#textboxQueueSurveyExpiration').val("");
    $('#timeInfoSurveyExpiration').text("");
    $checkboxQueueSurveySendMailIfFailed.prop('checked', false);
    $('#checkboxSurveyEnabledForChat').prop('checked', false);
    $('#textboxQueueSurveyEmailFrom').val("");
    $('#textboxQueueSurveyEmailSubject').val("");
    $('#textboxQueueSurveyEmailTemplate').val("");
    $('#textboxQueueSurveyInvitation').val("");
    $('#textboxSurveyInvitationInteractive').val("");
    $('#textboxSurveyInvitationButton').val("");
    $('#textboxQueueSurveySentRate').val("100");
    $('#textboxQueueSurveyTimeToSend').val("60");
    $('#timeInfoSurveyTimeToSend').text("(" + $.i18n('configuration-systemsettings-queues-minutes', $('#textboxQueueSurveyTimeToSend').val()) + ")");
    $("#dropdownlistQueueSurveyCloseCondition ").prop('selectedIndex', 0);
    $('#textboxQueueSurveyMessagesCount').val("0");
    $('#textboxQueueSurveyCaseDuration').val("0");
    $('#timeInfoSurveyCaseDuration').text("");
    $('#dropdownlistQueueSurveyWithAgentReply').val("-1");
    $('#checkboxSurveySendIfNewCaseExists').prop('checked', true);
    $('#checkboxSurveySendIfNewCaseHasTag').prop('checked', false);
    $('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', false);
    $('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val("0");
    $('#timeInfoDontSendIfLastSurveyAfterMinutes').text("");
    $('#textboxSurveyDontSendTotalSendMonthly').val("0");
    $('#dropdownQueueSurvey').val("-1");

    LoadSurveysTags(null);

    $hiddenSurveyTagGroup.val('');
    $hiddenSurveyTagGroupToIgnore.val('');
    $listboxSurveyTagGroup.multiselect("resync");
    $listboxSurveyTagGruopToIgnore.multiselect("resync");
    $divQueueSurveyError.hide();

    $dropdownQueueSurvey.hide();
    $spanQueueSurvey.hide();
}

function AddNewSurvey() {
    addSurveyToTable = false;

    ResetSurveyElements();

    if ($dropdownQueueSurvey !== 'undefined') {
        for (var j = 0; j < surveys.length; j++) {
            $dropdownQueueSurvey.find("option[value='" + surveys[j].ID + "']").remove();
        }
    }

    $dropdownQueueSurvey.show();

    $.colorbox({
        transition: 'none',
        speed: 100,
        inline: true,
        href: $divQueueSurvey,
        width: '70%',
        initialWidth: '70%',
        preloading: false,
        showBackButton: false,
        closeButton: false,
        escKey: false,
        overlayClose: false
    });
}

function AddSurveyToTable() {
    addSurveyToTable = true;
    let surveyConfiguration = {
        Invitation: $('#textboxQueueSurveyInvitation').val(),
        Expiration: 0,
        SentRate: parseInt($('#textboxQueueSurveySentRate').val(), 10),
        TimeToSend: parseInt($('#textboxQueueSurveyTimeToSend').val(), 10),
        EnabledForChat: $('#checkboxSurveyEnabledForChat').is(':checked'),
        Tags: null,
        TagsToIgnore: null,
        TagGroups: null,
        TagGroupsToIgnore: null,
        DontSendIfLastSurveyAfterMinutes: parseInt($('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val(), 10),
        MaxSurveySend: parseInt($('#textboxSurveyDontSendTotalSendMonthly').val(), 10),
        MessagesCount: parseInt($('#textboxQueueSurveyMessagesCount').val(), 10),
        CaseDuration: parseInt($('#textboxQueueSurveyCaseDuration').val(), 10),
        CaseWithAgentReply: null,
        SendMailIfFailed: $('#checkboxSurveyEnabledForChat').is(':checked') && $checkboxQueueSurveySendMailIfFailed.is(':checked'),
        EmailFrom: $('#textboxQueueSurveyEmailFrom').val(),
        EmailSubject: $('#textboxQueueSurveyEmailSubject').val(),
        EmailTemplate: $('#textboxQueueSurveyEmailTemplate').val(),
        SendIfNewCaseExists: $('#checkboxSurveySendIfNewCaseExists').is(':checked'),
        SendIfNewCaseHasTag: $('#checkboxSurveySendIfNewCaseHasTag').is(':checked'),
        SendIfNewCaseClosedByYflow: $('#checkboxSurveySendIfNewCaseClosedByYflow').is(':checked'),
        SurveyID: editSurvey ? surveyEditID : $dropdownQueueSurvey.val(),
        CloseCaseCondition: parseInt($('#dropdownlistQueueSurveyCloseCondition').val(), 10)
    };

    if (!externalSurveySelected) {
        surveyConfiguration.Expiration = parseInt($('#textboxQueueSurveyExpiration').val(), 10);
    }

    let selectedTagsToIgnore = $("#textboxSurveysIgnoreTags").val().trim();
    if (selectedTagsToIgnore.length > 0)
        surveyConfiguration.TagsToIgnore = selectedTagsToIgnore.split(',').map(tag => parseInt(tag.trim(), 10));

    let selectedTags = $("#textboxSurveysTags").val().trim();
    if (selectedTags.length > 0)
        surveyConfiguration.Tags = selectedTags.split(',').map(tag => parseInt(tag.trim(), 10));

    if ($listboxSurveyTagGruopToIgnore.val() != null && $listboxSurveyTagGruopToIgnore.val().length > 0) {
        surveyConfiguration.TagGroupsToIgnore = $listboxSurveyTagGruopToIgnore.val();
    }

    let interactiveInvitation = $('#textboxSurveyInvitationInteractive').val().trim();
    if (interactiveInvitation.length > 0) {
        surveyConfiguration.InteractiveInvitation = $('#textboxSurveyInvitationInteractive').val(),
            surveyConfiguration.InteractiveInvitationButtonText = $('#textboxSurveyInvitationButton').val();
    }

    if ($listboxSurveyTagGroup.val() != null && $listboxSurveyTagGroup.val().length > 0) {
        surveyConfiguration.TagGroups = $listboxSurveyTagGroup.val();
    }

    let withAgentReply = parseInt($('#dropdownlistQueueSurveyWithAgentReply').val(), 10)
    if (withAgentReply !== -1) {
        surveyConfiguration.CaseWithAgentReply = withAgentReply === 1;
    }

    let selectedSurvey = availableSurveys.find(s => s.ID === surveyConfiguration.SurveyID);

    if (editSurvey) {
        let survey = surveys.find(s => s.ID === surveyConfiguration.SurveyID);
        survey.QueueSurveyConfiguration = surveyConfiguration;
    }
    else {
        let survey = {
            ID: surveyConfiguration.SurveyID,
            Name: selectedSurvey.Name,
            QueueSurveyConfiguration: surveyConfiguration
        };

        surveys.push(survey);
    }

    ReturnTableSurvey();

    editSurvey = false;
}

function ValidateSurveys(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableSurveys.is(':checked')) {
        return;
    }

    if (typeof (surveys) !== 'object' ||
        !Array.isArray(surveys) ||
        surveys.length === 0) {
        e.IsValid = false;
        return;
    }

    $hiddenSurveyList.val(JSON.stringify(surveys.map(s => s.QueueSurveyConfiguration)));
}

function EditSurvey(surveyID, surveysList) {
    addSurveyToTable = false;
    returnToTable = false;
    if ($dropdownQueueSurvey !== 'undefined') {
        for (var j = 0; j < surveys.length; j++) {
            $dropdownQueueSurvey.find("option[value='" + surveys[j].ID + "']").remove();
        }
    }

    $.colorbox({
        transition: 'none',
        speed: 100,
        inline: true,
        href: $divQueueSurvey,
        width: '70%',
        initialWidth: '70%',
        preloading: false,
        showBackButton: false,
        closeButton: false,
        escKey: false,
        overlayClose: false,
        trapFocus: false,
        onLoad: function () {
            editSurvey = true;
            surveyToEdit = surveysList.find(s => s.ID == surveyID);
            surveyEditID = surveyID;
            $divQueueSurvey.show();
            $('#textboxQueueSurveyExpiration').val(surveyToEdit.QueueSurveyConfiguration.Expiration.toString());
            if ($('#textboxQueueSurveyExpiration').val() != "0")
                $('#timeInfoSurveyExpiration').text("(" + $.i18n('configuration-systemsettings-queues-minutes', $('#textboxQueueSurveyExpiration').val()) + ")");
            $('#textboxQueueSurveyInvitation').val(surveyToEdit.QueueSurveyConfiguration.Invitation);
            $('#textboxSurveyInvitationInteractive').val(surveyToEdit.QueueSurveyConfiguration.InteractiveInvitation);
            $('#textboxSurveyInvitationButton').val(surveyToEdit.QueueSurveyConfiguration.InteractiveInvitationButtonText);
            $('#textboxQueueSurveySentRate').val(surveyToEdit.QueueSurveyConfiguration.SentRate.toString());
            $('#textboxQueueSurveyTimeToSend').val(surveyToEdit.QueueSurveyConfiguration.TimeToSend.toString());
            if ($('#textboxQueueSurveyTimeToSend').val() != "0")
                $('#timeInfoSurveyTimeToSend').text("(" + $.i18n('configuration-systemsettings-queues-minutes', $('#textboxQueueSurveyTimeToSend').val()) + ")");
            $("#dropdownlistQueueSurveyCloseCondition ").prop('selectedIndex', surveyToEdit.QueueSurveyConfiguration.CloseCaseCondition - 1);
            $('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val(surveyToEdit.QueueSurveyConfiguration.DontSendIfLastSurveyAfterMinutes.toString());
            if ($('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val() != "0")
                $('#timeInfoDontSendIfLastSurveyAfterMinutes').text("(" + $.i18n('configuration-systemsettings-queues-minutes', $('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val()) + ")");
            $('#textboxSurveyDontSendTotalSendMonthly').val(surveyToEdit.QueueSurveyConfiguration.MaxSurveySend ? surveyToEdit.QueueSurveyConfiguration.MaxSurveySend.toString() : "0");
            
            $listboxSurveyTagGroup.val(surveyToEdit.QueueSurveyConfiguration.TagGroups);
            $listboxSurveyTagGroup.multiselect("resync");
            $listboxSurveyTagGruopToIgnore.val(surveyToEdit.QueueSurveyConfiguration.TagGroupsToIgnore);
            $listboxSurveyTagGruopToIgnore.multiselect("resync");
            if (surveyToEdit.QueueSurveyConfiguration.MessagesCount != null)
                $('#textboxQueueSurveyMessagesCount').val(surveyToEdit.QueueSurveyConfiguration.MessagesCount.toString());
            if (surveyToEdit.QueueSurveyConfiguration.CaseDuration != null) {
                $('#textboxQueueSurveyCaseDuration').val(surveyToEdit.QueueSurveyConfiguration.CaseDuration.toString());
                if ($('#textboxQueueSurveyCaseDuration').val() != "0")
                    $('#timeInfoSurveyCaseDuration').text("(" + $.i18n('configuration-systemsettings-queues-minutes', $('#textboxQueueSurveyCaseDuration').val()) + ")");
            }

            LoadSurveysTags(surveyToEdit);

            if (surveyToEdit.QueueSurveyConfiguration.CaseWithAgentReply == null)
                $('#dropdownlistQueueSurveyWithAgentReply').val("-1");
            else
                $('#dropdownlistQueueSurveyWithAgentReply').val(surveyToEdit.QueueSurveyConfiguration.CaseWithAgentReply ? "1" : "0");
            $checkboxQueueSurveySendMailIfFailed.prop('checked', surveyToEdit.QueueSurveyConfiguration.SendMailIfFailed);
            $('#checkboxSurveyEnabledForChat').prop('checked', surveyToEdit.QueueSurveyConfiguration.EnabledForChat);
            if (surveyToEdit.QueueSurveyConfiguration.SendMailIfFailed) {
                $('#textboxQueueSurveyEmailFrom').val(surveyToEdit.QueueSurveyConfiguration.EmailFrom);
                $('#textboxQueueSurveyEmailSubject').val(surveyToEdit.QueueSurveyConfiguration.EmailSubject);
                $('#textboxQueueSurveyEmailTemplate').val(surveyToEdit.QueueSurveyConfiguration.EmailTemplate);
            }
            $('#checkboxSurveySendIfNewCaseExists').prop('checked', surveyToEdit.QueueSurveyConfiguration.SendIfNewCaseExists);
            $('#checkboxSurveySendIfNewCaseHasTag').prop('checked', surveyToEdit.QueueSurveyConfiguration.SendIfNewCaseHasTag);
            $('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', surveyToEdit.QueueSurveyConfiguration.SendIfNewCaseClosedByYflow);
            $dropdownQueueSurvey.hide();
            $spanQueueSurvey.text(surveyToEdit.Name);
            $spanQueueSurvey.show();
            HideOptionsExternalSurvey();

            $.colorbox.resize();
        }
    }).focus();
}

function LoadSurveysTags(survey) {

    if (survey == null || typeof (survey) === 'undefined') {
        DrawTags(null, null);
        return;
    }

    let dataToSend = JSON.stringify({
        tagsIds: survey.QueueSurveyConfiguration.Tags,
        tagsToIgnoreIds: survey.QueueSurveyConfiguration.TagsToIgnore
    });

    let configuredSurveysTags = null
    let configuredSurveysTagsToIgnore = null;

    $.ajax({
    type: "POST",
    url: "Queues.aspx/RetrieveTags",
    contentType: "application/json; charset=utf-8",
    data: dataToSend,
    dataType: "json",
    success: function (data) {
        if (data.d.Success) {
            configuredSurveysTags = data.d.Tags;
            configuredSurveysTagsToIgnore = data.d.TagsToIgnore;
            DrawTags(configuredSurveysTags, configuredSurveysTagsToIgnore);
        }
        else {
            if (console)
                console.log('Ocurrió un error al obtener etiquetas' + data.d.Error.Message);
        }
    },
    error: function (jqXHR, textStatus, errorThrown) {
        if (console)
            console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + jqXHR.responseText);
    }
    });
}


function DrawTags(configuredSurveysTags, configuredSurveysTagsToIgnore) {

    $('#textboxSurveysTags').tokenInput('destroy');
    $('#textboxSurveysIgnoreTags').tokenInput('destroy');
    
    $('#textboxSurveysTags').tokenInput("../Reports/Cases.aspx/SearchTags", {
        method: 'POST',
        queryParam: 'q',
        searchDelay: 300,
        minChars: 3,
        //tokenLimit: 1,
        propertyToSearch: 'FullName',
        //jsonContainer: 'd',
        preventDuplicates: true,
        tokenValue: 'ID',
        contentType: 'json',
        resultsLimit: 20,
        excludeCurrent: true,
        zindex: 20000, //Necesario para colorbox
        styles: {
            dropdown: {
                'max-height': '200px',
                'overflow-y': 'auto',
            },
            tokenList: {
                'width': '100%',
            }
        },
        prePopulate: configuredSurveysTags,
        onSend: function (params) {
            params.data = JSON.stringify(params.data);
            params.contentType = "application/json; charset=utf-8";
        },
        onResult: function (results, query, cache_key) {
            var settingsTags = $('#textboxSurveysTags').data('settings');
            if (typeof (settingsTags.local_data) === 'undefined') {
                delete settingsTags.url;
                settingsTags.local_data = results.d;

                return $.grep(settingsTags.local_data, function (row) {
                    return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                });
            }

            return results;
        }
    });

    $('#textboxSurveysIgnoreTags').tokenInput("../Reports/Cases.aspx/SearchTags", {
        method: 'POST',
        queryParam: 'q',
        searchDelay: 300,
        minChars: 3,
        //tokenLimit: 1,
        propertyToSearch: 'FullName',
        //jsonContainer: 'd',
        preventDuplicates: true,
        tokenValue: 'ID',
        contentType: 'json',
        resultsLimit: 20,
        excludeCurrent: true,
        zindex: 20000, //Necesario para colorbox
        styles: {
            dropdown: {
                'max-height': '200px',
                'overflow-y': 'auto',
            },
            tokenList: {
                'width': '100%',
            }
        },
        prePopulate: configuredSurveysTagsToIgnore,
        onSend: function (params) {
            params.data = JSON.stringify(params.data);
            params.contentType = "application/json; charset=utf-8";
        },
        onResult: function (results, query, cache_key) {
            var settingsTags = $('#textboxSurveysIgnoreTags').data('settings');
            if (typeof (settingsTags.local_data) === 'undefined') {
                delete settingsTags.url;
                settingsTags.local_data = results.d;

                return $.grep(settingsTags.local_data, function (row) {
                    return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                });
            }

            return results;
        }
    });
}

function ValidateSurvey() {
    ToggleValidator($divQueueSurveyError, true);

    var $spanError = $('span', $divQueueSurveyError);

    if (!$checkboxEnableSurveys.is(':checked')) {
        return;
    }

    if ($dropdownQueueSurvey.val() == '-1' && !$dropdownQueueSurvey.is(':hidden')) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-surveyselected"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    var invitation = $('#textboxQueueSurveyInvitation').val().trim();
    if (invitation.length === 0) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-invitation"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    var invitationInteractive = $('#textboxSurveyInvitationInteractive').val().trim();
    var interactiveButton = $('#textboxSurveyInvitationButton').val().trim();

    if (invitationInteractive.length > 0 && interactiveButton.length === 0) {
        $spanError.text($.i18n("configuration-queues-survey_invalid_button_length"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    if (interactiveButton.length > 20) {
        $spanError.text($.i18n("configuration-queues-survey_invalid_button_length"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    if (invitationInteractive.length === 0 && interactiveButton.length > 0) {
        $spanError.text($.i18n("configuration-queues-survey_invalid_interactive_invitation_length"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    if (!movistarSurveySelected) {
        if (invitation.indexOf("@@LINK@@") === -1) {
            $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-invitation"));
            ToggleValidator($divQueueSurveyError, false);
            return;
        }
    }

    if (!externalSurveySelected) {
        var expiration = $('#textboxQueueSurveyExpiration').val();

        if (expiration.length === 0) {
            $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-expiration"));
            ToggleValidator($divQueueSurveyError, false);
            return;
        }

        expiration = parseInt(expiration, 10);
        if (isNaN(expiration) || expiration < 0 || expiration > 99999) {
            $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-expiration"));
            ToggleValidator($divQueueSurveyError, false);
            return;
        }
    }

    var sentRate = $('#textboxQueueSurveySentRate').val();
    if (sentRate.length === 0) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-sentrate"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    sentRate = parseInt(sentRate, 10);
    if (isNaN(sentRate) || sentRate < 0 || sentRate > 100) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-sentrate"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    var timeToSend = $('#textboxQueueSurveyTimeToSend').val();
    if (timeToSend.length === 0) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-timetosend"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    timeToSend = parseInt(timeToSend, 10);
    if (isNaN(timeToSend) || timeToSend < 0 || timeToSend > 99999) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-timetosend"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    var messagesCount = $('#textboxQueueSurveyMessagesCount').val();
    if (messagesCount.length === 0) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-messagecount"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    messagesCount = parseInt(messagesCount, 10);
    if (isNaN(messagesCount) || messagesCount < 0 || messagesCount > 9999) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-messagecount"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    var caseDuration = $('#textboxQueueSurveyCaseDuration').val();
    if (caseDuration.length === 0) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-caseduration"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    caseDuration = parseInt(caseDuration, 10);
    if (isNaN(caseDuration) || caseDuration < 0 || caseDuration > 999) {
        $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-caseduration"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    if ($checkboxQueueSurveySendMailIfFailed.is(':checked')) {
        var $textboxQueueSurveyEmailFrom = $('#textboxQueueSurveyEmailFrom');
        var from = $textboxQueueSurveyEmailFrom.val().trim();
        if (from.length > 0) {
            var $textboxQueueSurveyEmailSubject = $('#textboxQueueSurveyEmailSubject');
            var subject = $textboxQueueSurveyEmailSubject.val().trim();
            if (subject.length > 0) {
                var $textboxQueueSurveyEmailTemplate = $('#textboxQueueSurveyEmailTemplate');
                var template = $textboxQueueSurveyEmailTemplate.val().trim();

                if (template.length > 0) {
                    var regexMail = /\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
                    if (!regexMail.test(from)) {
                        $spanError.text($.i18n("configuration-queues-incomplete_mail-error"));
                        ToggleValidator($divQueueSurveyError, false);
                        return;
                    }

                    if (template.indexOf('@@LINK@@') == -1) {
                        $spanError.text($.i18n("configuration-queues-incomplete_mail-error"));
                        ToggleValidator($divQueueSurveyError, false);
                        return;
                    }

                }
            }
            else {
                $spanError.text($.i18n("configuration-queues-incomplete_mail-error"));
                ToggleValidator($divQueueSurveyError, false);
                return;
            }
        }
        else {
            $spanError.text($.i18n("configuration-queues-incomplete_mail-error"));
            ToggleValidator($divQueueSurveyError, false);
            return;
        }
    }

    var minutes = $('#textboxSurveyDontSendIfLastSurveyAfterMinutes').val();
    if (minutes.length === 0) {
        $spanError.text($.i18n("configuration-queues-survey_dontsend_if_last_after_minutes-error"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    minutes = parseInt(minutes, 10);
    if (isNaN(minutes) || minutes < 0 || minutes > 172800) {
        $spanError.text($.i18n("configuration-queues-survey_dontsend_if_last_after_minutes-error"));
        ToggleValidator($divQueueSurveyError, false);
        return;
    }

    if ($('#textboxQueueSurveyDontSendTotalSendMonthly').val() != "0") {
        var value = $('#textboxSurveyDontSendTotalSendMonthly').val();
        value = parseInt(value, 10);

        if (isNaN(value) || value < 0 || value > 30) {
            $spanError.text($.i18n("configuration-servicestwitter-yflow_configuration-error-survey-dontsendtotalsendmonthly"));
            ToggleValidator($divQueueSurveyError, false);
            return;
        }
    }

    AddSurveyToTable();
}

function FilterQueues() {
    datatable.draw();
}

function ValidateMinutesNotAssignToPreviousAgent(sender, e) {
    e.IsValid = false;

    if (!($checkboxAllowAgentsToReturnMessagesToQueue.is(':checked'))) {
        e.IsValid = true;
        return;
    }

    var minutesNotAssignToPreviousAgent = $textboxMinutesNotAssignToPreviousAgent.val();
    if (Math.floor(minutesNotAssignToPreviousAgent) == minutesNotAssignToPreviousAgent && $.isNumeric(minutesNotAssignToPreviousAgent)) {
        minutesNotAssignToPreviousAgent = parseInt(minutesNotAssignToPreviousAgent, 10);
        if (minutesNotAssignToPreviousAgent >= 0 && minutesNotAssignToPreviousAgent <= 120) {
            e.IsValid = true;
            return;
        }
    }
}

function ValidateSelectedQueues(sender, e) {
    e.IsValid = false;

    if (!($checkboxAllowAgentsToReturnMessagesToQueue.is(':checked'))) {
        e.IsValid = true;
        return;
    }

    if (!($checkboxAllowAgentsToSelectQueueOnReturnToQueue.is(':checked'))) {
        e.IsValid = true;
        return;
    }

    var $options = $('option:selected', $listboxQueues);
    if ($options.length > 0) {
        e.IsValid = true;
        return;
    }
}

function ValidateSelectedServiceFunctions(sender, e) {
    e.IsValid = false;

    $('input[type=checkbox][rel=servicefunction]').each(function () {
        if (this.checked)
            e.IsValid = true;
    });
}

function ValidateSelectedSupervisors(sender, e) {
    e.IsValid = false;

    var supervisors = $listboxSupervisors.val();

    if (supervisors != null && supervisors.length > 0) {
        e.IsValid = true;
    }
}

function ValidateSelectedTags(sender, e) {
    e.IsValid = false;

    var tagGroups = $listboxTagGroups.val();

    var tags = $textboxTags.val();

    if ((tags != null && tags.length > 0) || (tagGroups != null && tagGroups.length > 0)) {
        e.IsValid = true;
        $hiddenTagGroups.val(tagGroups.join(','));
    }
}

function NewSurvey() {

    $('td.text', $messageNewSurveyPopup).html($.i18n("configuration-surveys-new_type_survey"));

    $.colorbox({
        transition: 'none',
        speed: 100,
        inline: true,
        href: "#divNewSurveyPopup",
        width: '600px',
        initialWidth: '500px',
        preloading: false,
        closeButton: false
    });
}

function ShowQueueConfig(id) {
    var url = "QueueInfo.aspx?QueueID=" + id;

    $.colorbox({
        transition: 'elastic',
        speed: 100,
        href: url,
        width: '70%',
        initialWidth: '70%',
        height: '600px',
        initialHeight: '600px',
        preloading: false,
        closeButton: false,
        onComplete: function () {
            LoadQueueInfoContents();
        }
    });
}

function ValidateChatDaysAndTimes(sender, e) {
    e.IsValid = true;

    $hiddenChatAvailableDaysAndTimes.val('');

    if ($textboxQueueServiceLevelSeconds.val() > 0) {
        if ($checkboxChatAvailableDaysAndTimes.is(':checked') && !$divChatAvailableDaysAndTimesContainer.timetable('atLeastOneSelected')) {
            e.IsValid = false;
            return;
        }

        var workingDates = $divChatAvailableDaysAndTimesContainer.timetable('retrieveInDefaultTimeZone');
        $hiddenChatAvailableDaysAndTimes.val(JSON.stringify(workingDates));
    }
}

function ValidateSLMinutesActionsAssignToQueue(sender, e) {
    e.IsValid = true;
    if ($textboxQueueServiceLevelSeconds.val() > 0) {
        var values = $dropdownlistSLMinutesActionsAssignToQueue.val() || [];
        if ($checkboxSLMinutesActionsAssignToQueue.is(':checked') && values.length == 0) {
            e.IsValid = false;
        }
    }
}

function ValidateSLMinutesExpiredActionsAssignToQueue(sender, e) {
    e.IsValid = true;
    if ($textboxQueueServiceLevelExpired.val() > 0) {
        var values = $dropdownlistSLMinutesExpiredActionsAssignToQueue.val() || [];
        if ($checkboxSLMinutesExpiredActionsAssignToQueue.is(':checked') && values.length == 0) {
            e.IsValid = false;
        }
    }
}

function ValidateSLMinutesActionsAutoReply(sender, e) {
    e.IsValid = true;
    if ($textboxQueueServiceLevelSeconds.val() > 0 && $checkboxSLMinutesActionsAutoReply.is(':checked')) {
        var text = $textboxSLMinutesActionsAutoReply.val().trim();
        if (text.length == 0) {
            e.IsValid = false;
        }

        if (IsSocialServiceTypeLicensed(SocialServiceTypes.Twitter) && text.length > 240) {
            e.IsValid = false;
        }
    }
}

function ValidateSLChatNotify(sender, e) {
	e.IsValid = true;
	if ($textboxQueueServiceLevelSeconds.val() > 0 && $checkboxSLChatNotify.is(':checked')) {
		let text = $textboxSLChatNotifyText.val().trim();
		if (text.length == 0) {
			e.IsValid = false;
		}
	}
}

function ValidateSLMinutesActionsTags(sender, e) {
    e.IsValid = true;
    $hiddenSLMinutesActionsTags.val('');
    if ($textboxQueueServiceLevelSeconds.val() > 0 && $checkboxSLMinutesActionsAddTags.is(':checked')) {
        var tags = $textboxSLMinutesActionsTags.val();
        e.IsValid = false;
        if (tags != null && tags.length > 0) {
            e.IsValid = true;
            tags = tags.split(',');
        }
    }
}

function ValidateSLExpiredChatNotify(sender, e) {
	e.IsValid = true;
	if ($textboxQueueServiceLevelExpired.val() > 0 && $checkboxSLExpiredChatNotify.is(':checked')) {
		let text = $textboxSLExpiredChatNotifyText.val().trim();
		if (text.length == 0) {
			e.IsValid = false;
		}
	}
}

function ValidateSLMinutesExpiredActionsAutoReply(sender, e) {
    e.IsValid = true;
    if ($textboxQueueServiceLevelExpired.val() > 0 && $checkboxSLMinutesExpiredActionsAutoReply.is(':checked')) {
        var text = $textboxSLMinutesExpiredActionsAutoReply.val().trim();
        if (text.length == 0) {
            e.IsValid = false;
        }

        if (IsSocialServiceTypeLicensed(SocialServiceTypes.Twitter) && text.length > 240) {
            e.IsValid = false;
        }
    }
}

function ValidateSLMinutesExpiredActionsTags(sender, e) {
    e.IsValid = true;
    $hiddenSLMinutesExpiredActionsTags.val('');
    if ($textboxQueueServiceLevelExpired.val() > 0 && $checkboxSLMinutesExpiredActionsAddTags.is(':checked')) {
        var tags = $textboxSLMinutesExpiredActionsTags.val();
        e.IsValid = false;
        if (tags != null && tags.length > 0) {
            e.IsValid = true;
            tags = tags.split(',');
        }
    }
}

function ValidateExpired(sender, e) {
    e.IsValid = true;

    let expiredSeconds = parseInt($textboxQueueServiceLevelExpired.val(), 10) * 60;
    let seconds = parseInt($textboxQueueServiceLevelSeconds.val(), 10);

    if (expiredSeconds > 0) {
        if (seconds === 0) {
            e.IsValid = false;
        }
        else {
            if (expiredSeconds < seconds) {
                e.IsValid = false;
            }
        }
    }
}

function ValidateServiceLevelAtLeastOneAction(sender, e) {
    e.IsValid = true;
    return;

    if ($textboxQueueServiceLevelSeconds.val() == 0) {
        e.IsValid = true;
        return;
    }

    if ($checkboxSLMinutesActionsAddTags.is(':checked') ||
        $checkboxSLMinutesActionsAssignToQueue.is(':checked') ||
        $checkboxSLMinutesActionsAutoReply.is(':checked') ||
        $checkboxSLMinutesActionsDiscard.is(':checked') ||
        $checkboxSLMinutesActionsNotify.is(':checked') ||
        $checkboxSLMinutesActionsVIM.is(':checked'))
        e.IsValid = true;
}

function ValidateServiceLevelExpiredAtLeastOneAction(sender, e) {
    e.IsValid = true;
    return;

    if ($textboxQueueServiceLevelExpired.val() == 0) {
        e.IsValid = true;
        return;
    }

    if ($checkboxSLMinutesExpiredActionsAddTags.is(':checked') ||
        $checkboxSLMinutesExpiredActionsAssignToQueue.is(':checked') ||
        $checkboxSLMinutesExpiredActionsAutoReply.is(':checked') ||
        $checkboxSLMinutesExpiredActionsDiscard.is(':checked') ||
        $checkboxSLMinutesExpiredActionsNotify.is(':checked') ||
        $checkboxSLMinutesExpiredActionsVIM.is(':checked'))
        e.IsValid = true;
}


function ValidateFirstAutomaticActionSeconds(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked')) {

        let minutes = parseFloat($textboxFirstAutomaticActionSeconds.val());

        if (typeof (minutes) !== 'number' || isNaN(minutes) || !Number.isInteger(minutes)) {
            e.IsValid = false;
            return;
        }

        if (minutes < 0 || minutes > 180) {
            e.IsValid = false;
            return;
        }
    }

    return;
}

function ValidateFirstAutomaticActionMinimumEnqueueMessages(sender, e) {

    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked')) {

        if ($checkboxFirstAutomaticActionReply.is(':checked')) {

            let replyTextValue = $textboxFirstAutomaticActionReplyText.val();

            if ((replyTextValue.includes("@@EWT@@") || replyTextValue.includes("@@POSMSGCOLA@@"))) {
                let ewtMin = parseInt($textboxFirstAutomaticActionMinimumEWT.val(), 10);
                let msgMin = parseInt($textboxFirstAutomaticActionMinimumEnqueueMessages.val(), 10);

                if (typeof (ewtMin) !== 'number' || ewtMin <= 0 || isNaN(ewtMin)) {
                    e.IsValid = false;
                    return;
                }

                if (typeof (msgMin) !== 'number' || msgMin <= 0 || isNaN(msgMin)) {
                    e.IsValid = false;
                    return;
                }
            }
        }

        if ($checkboxFirstAutomaticActionNotifyChat.is(':checked')) {

            let replyTextValue = $textboxFirstAutomaticActionReplyChat.val();

            if ((replyTextValue.includes("@@EWT@@") || replyTextValue.includes("@@POSMSGCOLA@@"))) {
                let ewtMin = parseInt($textboxFirstAutomaticActionMinimumEWTChat.val(), 10);
                let msgMin = parseInt($textboxFirstAutomaticActionMinimumEnqueueMessagesChat.val(), 10);

                if (typeof (ewtMin) !== 'number' || ewtMin <= 0 || isNaN(ewtMin)) {
                    e.IsValid = false;
                    return;
                }

                if (typeof (msgMin) !== 'number' || msgMin <= 0 || isNaN(msgMin)) {
                    e.IsValid = false;
                    return;
                }
            }
        }
    }

    return;
}

function ValidateFirstAutomaticActionChatNotify(sender, e) {
    e.IsValid = true;
    if ($checkboxAllowFirstAutomaticActions.is(':checked') && $checkboxFirstAutomaticActionNotifyChat.is(':checked')) {

        let text = $textboxFirstAutomaticActionReplyChat.val();

        if (typeof (text) !== 'string' || text.length === 0) {
            e.IsValid = false;
            return;
        }

        if (text.includes("@@EWT@@")) {

            let replyTextEwtNoAgentsValue = $textboxFirstAutomaticActionReplyEwtNoAgentsChat.val().trim();
            let replyTextEwtNotComputedValue = $textboxFirstAutomaticActionReplyEwtNotCalculatedChat.val().trim();

            if (typeof (replyTextEwtNoAgentsValue) !== 'string' || replyTextEwtNoAgentsValue.length == 0 || replyTextEwtNoAgentsValue.length > 240) {
                e.IsValid = false;
                return;
            }

            if (typeof (replyTextEwtNotComputedValue) !== 'string' || replyTextEwtNotComputedValue.length == 0 || replyTextEwtNotComputedValue.length > 240) {
                e.IsValid = false;
                return;
            }
        }
    }
    return;
}

function ValidateFirstAutomaticActionAssignToQueue(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked') && $checkboxFirstAutomaticActionsTransferQueue.is(':checked')) {

        var values = $dropdownlistFirstAutomaticActionQueues.val() || [];
        if (values.length == 0) {
            e.IsValid = false;
            return;
        }
    }

    return;
}

function ValidateFirstAutomaticActionReplyText(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked') && $checkboxFirstAutomaticActionReply.is(':checked')) {

        let replyTextValue = $textboxFirstAutomaticActionReplyText.val();

        if (typeof (replyTextValue) !== 'string' || replyTextValue.length === 0 || replyTextValue.length > 240) {
            e.IsValid = false;
            return;
        }

        if (replyTextValue.includes("@@EWT@@")) {

            let replyTextEwtNoAgentsValue = $textboxFirstAutomaticActionReplyEwtNoAgents.val().trim();
            let replyTextEwtNotComputedValue = $textboxFirstAutomaticActionReplyEwtNotComputed.val().trim();

            if (typeof (replyTextEwtNoAgentsValue) !== 'string' || replyTextEwtNoAgentsValue.length == 0 || replyTextEwtNoAgentsValue.length > 240) {
                e.IsValid = false;
                return;
            }

            if (typeof (replyTextEwtNotComputedValue) !== 'string' || replyTextEwtNotComputedValue.length == 0 || replyTextEwtNotComputedValue.length > 240) {
                e.IsValid = false;
                return;
            }
        }
    }

    return;
}

function ValidateFirstAutomaticActionsTags(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked') && $checkboxFirstAutomaticActionApplyTags.is(':checked')) {
        e.IsValid = false;

        let tags = $textboxFirstAutomaticActionsTags.val();
        if (tags != null && tags.length > 0) {
            e.IsValid = true;
            tags = tags.split(',');
        }
    }

    return;
}

function ValidateFirstAutomaticAction(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowFirstAutomaticActions.is(':checked')) {

        if (!$checkboxFirstAutomaticActionReply.is(':checked') &&
            !$checkboxFirstAutomaticActionsTransferQueue.is(':checked') &&
            !$checkboxFirstAutomaticActionApplyTags.is(':checked') &&
            !$checkboxFirstAutomaticActionNotifyChat.is(':checked')) {
                e.IsValid = false;
            }
    }

    return;
}


function ValidateSecondAutomaticActionsSeconds(sender, e) {
    e.IsValid = true;

    if (!$checkboxAllowFirstAutomaticActions.is(':checked')) {
        $checkboxAllowSecondAutomaticActions.prop('checked', false);
    }

    if ($checkboxAllowSecondAutomaticActions.is(':checked')) {

        let minutes = parseFloat($textboxSecondAutomaticActionsSeconds.val());

        if (typeof (minutes) !== 'number' || isNaN(minutes) || !Number.isInteger(minutes)) {
            e.IsValid = false;
            return;
        }

        if (minutes < 0 || minutes > 180) {
            e.IsValid = false;
            return;
        }
    }

    return;
}

function ValidateSecondAutomaticActionChatNotify(sender, e) {
    e.IsValid = true;
    if ($checkboxAllowSecondAutomaticActions.is(':checked') && $checkboxSecondAutomaticActionNotifyChat.is(':checked')) {
        let text = $textboxSecondAutomaticActionChatReplyText.val().trim();
        if (text.length == 0) {
            e.IsValid = false;
        }

        if (text.includes("@@EWT@@")) {

            let replyTextEwtNoAgentsValue = $textboxSecondAutomaticActionReplyEwtNoAgentsChat.val().trim();
            let replyTextEwtNotComputedValue = $textboxSecondAutomaticActionReplyEwtNotCalculatedChat.val().trim();

            if (typeof (replyTextEwtNoAgentsValue) !== 'string' || replyTextEwtNoAgentsValue.length == 0 || replyTextEwtNoAgentsValue.length > 240) {
                e.IsValid = false;
                return;
            }

            if (typeof (replyTextEwtNotComputedValue) !== 'string' || replyTextEwtNotComputedValue.length == 0 || replyTextEwtNotComputedValue.length > 240) {
                e.IsValid = false;
                return;
            }
        }
        
    }
}

function ValidateSecondAutomaticActionMinimumEnqueueMessages(sender, e) {
    e.IsValid = true;
   
    if ($checkboxAllowSecondAutomaticActions.is(':checked')) {

        if ($checkboxSecondAutomaticActionReply.is(':checked')) {

            let replyTextValue = $textboxSecondAutomaticActionReplyText.val();

            if ((replyTextValue.includes("@@EWT@@") || replyTextValue.includes("@@POSMSGCOLA@@"))) {
                let ewtMin = parseInt($textboxSecondAutomaticActionMinimumEWT.val(), 10);
                let msgMin = parseInt($textboxSecondAutomaticActionMinimumEnqueueMessages.val(), 10);

                if (typeof (ewtMin) !== 'number' || ewtMin <= 0 || isNaN(ewtMin)) {
                    e.IsValid = false;
                    return;
                }

                if (typeof (msgMin) !== 'number' || msgMin <= 0 || isNaN(msgMin)) {
                    e.IsValid = false;
                    return;
                }
            }
        }

        if ($checkboxSecondAutomaticActionNotifyChat.is(':checked')) {

            let replyTextValue = $textboxSecondAutomaticActionChatReplyText.val();

            if ((replyTextValue.includes("@@EWT@@") || replyTextValue.includes("@@POSMSGCOLA@@"))) {
                let ewtMin = parseInt($textboxSecondAutomaticActionMinimumEWTChat.val(), 10);
                let msgMin = parseInt($textboxSecondAutomaticActionMinimumEnqueueMessagesChat.val(), 10);

                if (typeof (ewtMin) !== 'number' || ewtMin <= 0 || isNaN(ewtMin)) {
                    e.IsValid = false;
                    return;
                }

                if (typeof (msgMin) !== 'number' || msgMin <= 0 || isNaN(msgMin)) {
                    e.IsValid = false;
                    return;
                }
            }
        }
    }
    return;
}

function ValidateSecondAutomaticActionAssignToQueue(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowSecondAutomaticActions.is(':checked')) {

        var values = $dropdownlistSecondAutomaticActionQueues.val() || [];
        if ($checkboxSecondAutomaticActionsTransferQueue.is(':checked') && values.length == 0) {
            e.IsValid = false;
            return false;
        }
    }

    return;
}

function ValidateSecondAutomaticActionReplyText(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowSecondAutomaticActions.is(':checked') && $checkboxSecondAutomaticActionReply.is(':checked')) {

        let replyTextValue = $textboxSecondAutomaticActionReplyText.val();

        if (typeof (replyTextValue) !== 'string' || replyTextValue.length === 0 || replyTextValue.length > 240) {
            e.IsValid = false;
            return;
        }

        if (replyTextValue.includes("@@EWT@@")) {

            let replyTextEwtNoAgentsValue = $textboxSecondAutomaticActionReplyEwtNoAgents.val().trim();
            let replyTextEwtNotComputedValue = $textboxSecondAutomaticActionReplyEwtNotComputed.val().trim();

            if (typeof (replyTextEwtNoAgentsValue) !== 'string' || replyTextEwtNoAgentsValue.length == 0 || replyTextEwtNoAgentsValue.length > 240) {
                e.IsValid = false;
                return;
            }

            if (typeof (replyTextEwtNotComputedValue) !== 'string' || replyTextEwtNotComputedValue.length == 0 || replyTextEwtNotComputedValue.length > 240) {
                e.IsValid = false;
                return;
            }
        }
    }

    return;
}

function ValidateSecondAutomaticActionsTags(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowSecondAutomaticActions.is(':checked') && $checkboxSecondAutomaticActionApplyTags.is(':checked')) {
        let tags = $textboxSecondAutomaticActionsTags.val();
        e.IsValid = false;
        if (tags != null && tags.length > 0) {
            e.IsValid = true;
            tags = tags.split(",");
        }
    }

    return;
}

function ValidateSecondAutomaticAction(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowSecondAutomaticActions.is(':checked')) {

        if (!$checkboxSecondAutomaticActionReply.is(':checked') &&
            !$checkboxSecondAutomaticActionsTransferQueue.is(':checked') &&
            !$checkboxSecondAutomaticActionApplyTags.is(':checked') &&
            !$checkboxSecondAutomaticActionNotifyChat.is(':checked')) {
            e.IsValid = false;
        }
    }

    return;
}

function ValidateSecondActionTimeDiff(sender, e) {
    e.IsValid = true;

    if ($checkboxAllowSecondAutomaticActions.is(':checked')) {
        let firstActionSecond = parseInt($textboxFirstAutomaticActionSeconds.val(), 10);
        let secondActionseconds = parseInt($textboxSecondAutomaticActionsSeconds.val(), 10);
        if (secondActionseconds <= firstActionSecond) {
            e.IsValid = false;
        }
    }

    return;
}


function MyWebFormOnSubmit() {
    var success = oldWebFormOnSubmit();
    if (!success && typeof (Page_Validators) != 'undefined') {
        for (var i = 0; i < Page_Validators.length; i++) {
            var validator = Page_Validators[i];
            if (!validator.isvalid) {
                var $validator = $(validator);
                var $parent = $validator.parents('.ui-tabs-panel');
                if ($parent.length > 0)
                    $tabsEditQueues.tabs('select', $parent.attr('id'));
                break;
            }
        }
    }
    return success;
}

function ShowDeleteDialog(queueId, enabled) {
    $messageQueueCanBeDeleted.hide();
    $messageQueueCanBeDeletedAndIsDisabled.hide();
    $buttonDeleteQueueConfirm.parent().hide();
    $buttonDisableQueueConfirm.parent().hide();
    $messageQueueCannotBeDeleted.hide();
    $('#messageQueueCanBeDisabled').hide();

    $.colorbox({
        transition: 'elastic',
        speed: 200,
        inline: true,
        href: $divDeleteQueue,
        width: '600px',
        initialWidth: '600px',
        height: '400px',
        preloading: false,
        closeButton: false,
        onLoad: function () {
            var dataToSend = JSON.stringify({ queueId: queueId });

            $.ajax({
                type: "POST",
                url: "Queues.aspx/QueueCanBeDeleted",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: dataToSend,
                success: function (result) {
                    $divCanBeDeletedLoading.hide();

                    if (result.d.Success) {
                        if (result.d.CanBeDeleted) {
                            $buttonDeleteQueueConfirm.unbind('click');
                            $buttonDeleteQueueConfirm.bind('click', function () {
                                $hiddenActionQueueID.val(queueId);
                                $hiddenActionName.val('DeleteQueue');
                                $buttonAction[0].click();
                            });
                            $buttonDeleteQueueConfirm.parent().show();

                            if (enabled) {
                                $messageQueueCanBeDeleted.show();

                                $buttonDisableQueueConfirm.unbind('click');
                                $buttonDisableQueueConfirm.bind('click', function () {
                                    $hiddenActionQueueID.val(queueId);
                                    $hiddenActionName.val('Disable');
                                    $buttonAction[0].click();
                                });
                                $buttonDisableQueueConfirm.parent().show();
                            }
                            else {
                                $messageQueueCanBeDeletedAndIsDisabled.show();
                            }
                        }
                        else
                            if (enabled) {
                                $('#messageQueueCanBeDisabled').show();
                            }
                            else {
                                $messageQueueCannotBeDeleted.show();
                            }
                    }
                    else {
                        $messageCouldntCheckQueueCanBeDeleted.show();
                    }

                    $.colorbox.resize();
                },
                error: function () {
                    $divCanBeDeletedLoading.hide();
                    $messageCouldntCheckQueueCanBeDeleted.show();

                    $.colorbox.resize();
                }
            });

        }
    });

    $.colorbox.resize();
}

function ValidateName(sender, e) {
    var name = $textboxName.val().trim();
    if (name.length === 0 || name.trim().length === 0) {
        e.IsValid = true;
        return;
    }

    var dataToSend = JSON.stringify({ name: name, queueId: creatingQueue ? null : editingQueueId });

    $.ajax({
        type: "POST",
        url: "Queues.aspx/IsQueueNameValid",
        data: dataToSend,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
                if (!data.d.IsValid) {
                    $textboxName.focus();
                    AlertDialog($.i18n("configuration-queues-error"), $.i18n("configuration-queues-name_already_used"));
                }
            }
            else {
                if (console)
                    console.error('No se pudo validar el nombre del servicio: %o', data.d.Error);
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateKey(sender, e) {
    var name = $textboxKey.val().trim();
    if (name.length === 0 || name.trim().length === 0) {
        e.IsValid = true;
        return;
    }

    var dataToSend = JSON.stringify({ key: name, queueId: creatingQueue ? null : editingQueueId });

    $.ajax({
        type: "POST",
        url: "Queues.aspx/IsQueueKeyValid",
        data: dataToSend,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
                if (!data.d.IsValid) {
                    $textboxName.focus();
                    AlertDialog($.i18n("configuration-queues-error"), $.i18n("configuration-queues-key_already_used"));
                }
            }
            else {
                if (console)
                    console.error('No se pudo validar el nombre del servicio: %o', data.d.Error);
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ShowCannotBeEnabled() {
    AlertDialog($.i18n('configuration-queues-title'), $.i18n('configuration-queues-cannot_be_enabled'));
}

function ShowCannotBeDisabled() {
    AlertDialog($.i18n('configuration-queues-title'), $.i18n('configuration-queues-cannot_be_disabled'));
}

function ShowQueueAgents(queue) {
	RetrieveQueue(queue.ID, function (loadedQueue) {
		if (loadedQueue !== null) {
			if (typeof (loadedQueue.Agents) === 'object' &&
				loadedQueue.Agents !== null) {
				loadedQueue.Agents.sort((a, b) => a.FullName.localeCompare(b.FullName));

				let $ulRelatedAgents = $('#ulRelatedAgents');
				$ulRelatedAgents.empty();

				for (let i = 0; i < loadedQueue.Agents.length; i++) {
					let $li = $('<li></li>');
					$li.append('<span class="fa fa-lg fa-user-headset"></span><span style="margin: 0 3px">' + loadedQueue.Agents[i].FullName + ' (' + loadedQueue.Agents[i].Username + ')</span>');

					$ulRelatedAgents.append($li);
				}

				$.colorbox({
					transition: 'elastic',
					speed: 200,
					inline: true,
					href: '#divRelatedAgents',
					width: '500px',
					initialWidth: '500px',
					height: '400px',
					preloading: false,
					closeButton: false,
					trapFocus: false,
					showBackButton: false,
					onComplete: function () {
						$.colorbox.resize();
					}
				});
			}
		}
		else {
			AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
		}
	});
}

function ShowQueueRelatedServices(queue) {
	RetrieveQueue(queue.ID, function (loadedQueue) {
		if (loadedQueue !== null) {
			if (typeof (loadedQueue.RelatedServices) === 'object' &&
				loadedQueue.RelatedServices !== null) {
				let keys = Object.keys(loadedQueue.RelatedServices);
				for (let i = 0; i < keys.length; i++) {
					for (let j = 0; j < loadedQueue.RelatedServices[keys[i]].length; j++) {
						loadedQueue.RelatedServices[keys[i]][j] = services.find(s => s.ID === loadedQueue.RelatedServices[keys[i]][j]);
					}
				}

				let $ulRelatedServices = $('#ulRelatedServices');
				$ulRelatedServices.empty();

				for (let i = 0; i < keys.length; i++) {
					let $liQueueConnectionFromServicesType = $('<li></li>');
					$liQueueConnectionFromServicesType.text(DisplayLocalizedEnumValue('QueueConnectionFromServicesTypes', keys[i]));
					$ulRelatedServices.append($liQueueConnectionFromServicesType);
					let $ulQueueConnectionFromServicesType = $('<ul></ul>');
					$liQueueConnectionFromServicesType.append($ulQueueConnectionFromServicesType);
					for (let j = 0; j < loadedQueue.RelatedServices[keys[i]].length; j++) {
						let service = loadedQueue.RelatedServices[keys[i]][j];
						let $li = $('<li></li>');
						$ulQueueConnectionFromServicesType.append($li);

						let $typeSpan = $('<span class="fa-lg fa-fw"></span>');
						if (service.Type !== ServiceTypes.TwitterSearches) {
							$typeSpan.addClass(GetServiceTypeClass(service.Type));
						}
						else {
							$typeSpan.addClass('fa-stack-05');
							$typeSpan.append('<i class="fab fa-twitter-square fa-stack-1x"></i><i class="fa fa-search fa-stack-05x"></i>');
						}

						$li.append($typeSpan);
						$li.append('<span style="margin: 0 3px">' + service.Name + '</span>');
					}
				}

				$.colorbox({
					transition: 'elastic',
					speed: 200,
					inline: true,
					href: '#divRelatedServices',
					width: '600px',
					initialWidth: '600px',
					height: '400px',
					preloading: false,
					closeButton: false,
					trapFocus: false,
					showBackButton: false,
					onComplete: function () {
						$.colorbox.resize();
					}
				});
			}
		}
		else {
			AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
		}
	});
}

function ShowQueueRelatedQueues(queue) {
	RetrieveQueue(queue.ID, function (loadedQueue) {
		if (loadedQueue !== null) {
			if (typeof (loadedQueue.RelatedQueues) === 'object' &&
				loadedQueue.RelatedQueues !== null) {
				let keys = Object.keys(loadedQueue.RelatedQueues);
				for (let i = 0; i < keys.length; i++) {
					for (let j = 0; j < loadedQueue.RelatedQueues[keys[i]].length; j++) {
						loadedQueue.RelatedQueues[keys[i]][j] = queues.find(s => s.ID === loadedQueue.RelatedQueues[keys[i]][j]);
					}
				}

				let $ulRelatedQueues = $('#ulRelatedQueues');
				$ulRelatedQueues.empty();

				for (let i = 0; i < keys.length; i++) {
					let $liQueueConnectionFromQueuesType = $('<li></li>');
					$liQueueConnectionFromQueuesType.text(DisplayLocalizedEnumValue('QueueConnectionFromQueuesTypes', keys[i]));
					$ulRelatedQueues.append($liQueueConnectionFromQueuesType);
					let $ulQueueConnectionFromQueuesType = $('<ul></ul>');
					$liQueueConnectionFromQueuesType.append($ulQueueConnectionFromQueuesType);
					for (let j = 0; j < loadedQueue.RelatedQueues[keys[i]].length; j++) {
						let queue = loadedQueue.RelatedQueues[keys[i]][j];
						let $li = $('<li></li>');
						$ulQueueConnectionFromQueuesType.append($li);

						$li.append('<span class="fa fa-lg fa-inbox"></span><span style="margin: 0 3px">' + queue.Name + '</span>');
					}
				}

				$.colorbox({
					transition: 'elastic',
					speed: 200,
					inline: true,
					href: '#divRelatedQueues',
					width: '600px',
					initialWidth: '600px',
					height: '400px',
					preloading: false,
					closeButton: false,
					trapFocus: false,
					showBackButton: false,
					onComplete: function () {
						$.colorbox.resize();
					}
				});
			}
		}
		else {
			AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
		}
	});
}

function ShowQueueRelatedQueueGroups(queue) {
	RetrieveQueue(queue.ID, function (loadedQueue) {
		if (loadedQueue !== null) {
			if (typeof (loadedQueue.RelatedQueueGroups) === 'object' &&
				loadedQueue.RelatedQueueGroups !== null) {
				
				let $ulRelatedQueueGroups = $('#ulRelatedQueueGroups');
				$ulRelatedQueueGroups.empty();

				for (let i = 0; i < loadedQueue.RelatedQueueGroups.length; i++) {
					let $li = $('<li></li>');
					$li.append('<span class="fa fa-lg fa-layer-group"></span><span style="margin: 0 3px">' + loadedQueue.RelatedQueueGroups[i].Name + '</span>');

					$ulRelatedQueueGroups.append($li);
				}

				$.colorbox({
					transition: 'elastic',
					speed: 200,
					inline: true,
					href: '#divRelatedQueueGroups',
					width: '500px',
					initialWidth: '500px',
					height: '400px',
					preloading: false,
					closeButton: false,
					trapFocus: false,
					showBackButton: false,
					onComplete: function () {
						$.colorbox.resize();
					}
				});
			}
		}
		else {
			AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
		}
	});
}

function HideOptionsExternalSurvey() {
    if (editSurvey || $dropdownQueueSurvey.val() != '-1') {
        $messageSurveyDisabled.hide();

        let surveyId = (editSurvey) ? surveyToEdit.ID : $dropdownQueueSurvey.val();
        let survey = availableSurveys.find(s => s.ID === surveyId);
        $('#queueSurveyExpiration').toggle(survey.Type === SurveyTypes.Yoizen);
        $('#queueSurveySendMailIfFailed').toggle(survey.Type === SurveyTypes.Yoizen);
        $('#surveyEnabledForChat').hide();
        externalSurveySelected = survey.Type !== SurveyTypes.Yoizen;
        movistarSurveySelected = survey.Type === SurveyTypes.Movistar;
    }
}

function RetrieveQueue(queueId, callback) {
	LoadingDialog({
		title: $.i18n('globals-loading'),
		timeout: 300,
		autoClose: false,
		onTimeout: function () {
			$.ajax({
				type: "POST",
				url: "Queues.aspx/RetrieveQueue",
				data: JSON.stringify({ queueId: queueId }),
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					let queue = {};

					for (let j = 0; j < fields.length; j++) {
						queue[fields[j]] = result.d.Queue[j];
					}

					queue.RelatedServices = ConvertKeyValuedArrayToObject(queue.RelatedServices);
					queue.RelatedQueues = ConvertKeyValuedArrayToObject(queue.RelatedQueues);

					callback(queue);
				},
				error: function (jqXHR, textStatus, errorThrown) {
					callback(null);
				}
			});
		}
	});
}

function ShowQueueTasks(queue) {
    var $divTasks = $('#divTasks');
    var $title = $('> .title > h2', $divTasks);
    $title.text($.i18n('configuration-queues-tasks-title', queue.Name));

    let atLeastOneQueue = false;
    $selectTasksQueuesThatCanTransferToQueue.empty();
    for (let i = 0; i < queues.length; i++) {
        if (queues[i].Enabled &&
            queues[i].ID !== queue.ID &&
            queues[i].Transfer) {
            let $option = $('<option />');
            $option.text(queues[i].Name);
            $option.val(queues[i].ID);
            $selectTasksQueuesThatCanTransferToQueue.append($option);
            atLeastOneQueue = true;
        }
    }

    $selectTasksQueuesThatCanTransferToQueue.multiselect('refresh');

    $messageTaskQueuessThatCanTransferToQueueEmpty.toggle(!atLeastOneQueue);
    $divTaskQueuessThatCanTransferToQueueNoEmpty.toggle(atLeastOneQueue);
    $divTaskReturnToQueue.prop('atLeastOneQueue', atLeastOneQueue);
    $buttonTasksAccept.parent().toggle(atLeastOneQueue);
    $tabsTasks.tabs('select', 'divTaskReturnToQueue');

	RetrieveQueue(queue.ID, function (loadedQueue) {
		if (loadedQueue !== null) {
			currentQueueForTasks = loadedQueue;

			if (currentQueueForTasks.RelatedQueuesThatTransferToThisQueue !== null) {
				$selectTasksQueuesThatCanTransferToQueue.val(currentQueueForTasks.RelatedQueuesThatTransferToThisQueue);
				$selectTasksQueuesThatCanTransferToQueue.multiselect('resync');
			}

			$.colorbox({
				transition: 'elastic',
				speed: 200,
				inline: true,
				href: $divTasks,
				width: '80%',
				initialWidth: '80%',
				height: '400px',
				preloading: false,
				closeButton: false,
				trapFocus: false,
				showBackButton: false,
				onComplete: function () {
					$.colorbox.resize();
				}
			});
		}
		else {
			AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
		}
    });
}

function AcceptQueueTasks() {
    let active = $tabsTasks.tabs('option', 'active');
    let tabId = $tabsTasks.tabs('instance').panels[active].id;
    switch (tabId) {
        case 'divTaskReturnToQueue':
            if (!$divTaskReturnToQueue.prop('atLeastOneQueue')) {
                $.colorbox.close();
                return;
            }

            var selectedQueues = $selectTasksQueuesThatCanTransferToQueue.val();
            if (selectedQueues !== null) {
                selectedQueues = selectedQueues.map(function (x) {
                    return parseInt(x, 10);
                });

                selectedQueues.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0));
            }

            if (currentQueueForTasks.RelatedQueuesThatTransferToThisQueue) {
                currentQueueForTasks.RelatedQueuesThatTransferToThisQueue.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0));
            }

            if (selectedQueues === null &&
                (currentQueueForTasks.RelatedQueuesThatTransferToThisQueue === null || currentQueueForTasks.RelatedQueuesThatTransferToThisQueue.length === 0)) {
                AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('configuration-queues-tasks-no_changes'));
                return;
            }

            if (selectedQueues !== null) {
                if (selectedQueues.equals(currentQueueForTasks.RelatedQueuesThatTransferToThisQueue)) {
                    AlertDialog($.i18n('configuration-queues-tasks-title', queue.Name), $.i18n('configuration-queues-tasks-no_changes'));
                    return;
                }
            }

            LoadingDialog({
                title: $.i18n('globals-loading'),
                timeout: 300,
                autoClose: false,
                onTimeout: function () {
                    $.ajax({
                        type: "POST",
                        url: "Queues.aspx/SaveQueuesThatCanTransferToQueue",
                        data: JSON.stringify({ queueId: currentQueueForTasks.ID, queueIdsThatCanTransfer: selectedQueues }),
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (result) {
                            if (result.d.Success == true) {
                                fields = result.d.Fields;
                                queues = [];

                                for (let i = 0; i < result.d.Queues.length; i++) {
                                    let queue = {};

                                    for (let j = 0; j < fields.length; j++) {
                                        queue[fields[j]] = result.d.Queues[i][j];
                                    }

                                    queues.push(queue);
                                }

                                var currentPage = $tableQueues.DataTable().page();
                                FillTable();
                                $tableQueues.DataTable().page(currentPage);
                                $.colorbox.close();
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            AlertDialog($.i18n('configuration-queues-tasks-title', currentQueueForTasks.Name), $.i18n('globals-error'), undefined, undefined, 'Error');
                        }
                    });
                }
            });
            break;
        default:
            break;
    }
}

function ShowCopyDialog(queueID) {
    $hiddenQueueIDToCopy = $('#hiddenQueueIDToCopy');
    $hiddenQueueIDToCopy.val(queueID);

    var $divCopyError = $('#divCopyError');
    ToggleValidator($divCopyError, true);

    $('#checkboxCopyServiceLevel').prop('checked', true);
    $('#checkboxCopyUsers').prop('checked', true);
    $('#checkboxCopyTags').prop('checked', true);
    $('#checkboxCopySurveys').prop('checked', true);
    $('#checkboxCopyEWT').prop('checked', true);

    $.colorbox({
        transition: 'elastic',
        speed: 200,
        inline: true,
        href: $('#divCopy'),
        width: '600px',
        initialWidth: '600px',
        height: '400px',
        preloading: false,
        closeButton: false,
        onComplete: function () {
            $.colorbox.resize();
        }
    });
}

function AcceptCopy() {
    var $divCopyError = $('#divCopyError');

    if (!$('#checkboxCopyServiceLevel').is(':checked') &&
        !$('#checkboxCopyUsers').is(':checked') &&
        !$('#checkboxCopyTags').is(':checked') &&
        !$('#checkboxCopySurveys').is(':checked') &&
        !$('#checkboxCopyEWT').is(':checked')) {
        ToggleValidator($divCopyError, false);
        $.colorbox.resize();
        return;
    }

    ToggleValidator($divCopyError, true);

    $hiddenQueueIDToCopy = $('#hiddenQueueIDToCopy');

    $hiddenActionQueueID.val($hiddenQueueIDToCopy.val());
    $hiddenActionName.val('CopyQueue');
    $buttonAction[0].click();
}

function ValidateQueueWorkingHoursForReceivingMessages(sender, e) {
    e.IsValid = true;

    let workingDates = $divQueueWorkingHoursForReceivingMessagesContainer.timetable('retrieveInDefaultTimeZone');
    $hiddenQueueWorkingHoursForReceivingMessages.val(JSON.stringify(workingDates));
}

/********** Export functions *************/

function Export() {
    var $buttonExportCompleteVisible = $('#buttonExportCompleteVisible', $divExport).parent();
    $divExportStep1.show();
    $buttonExportCompleteVisible.show();
    $.colorbox({
        transition: 'none',
        speed: 100,
        inline: true,
        href: "#divExport",
        width: '600px',
        initialWidth: '200px',
        preloading: false,
        closeButton: false
    });
}

function ExportVisible() {
    var $hiddenExportFormat;

    $hiddenExportFormat = $('#hiddenExportFormat');
    $hiddenExportFormat.val($selectExportFormat.val());
    var $buttonExport = $('input[id$="buttonExport"]');

    $buttonExport.click();
    $.colorbox.close();
}