﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.Core.ActionOptions;
using Yoizen.Social.Core.Enums;
using Yoizen.Social.Core.Exceptions;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.Metrics;
using Yoizen.Social.DomainModel.ServiceSettings;
using Yoizen.Social.DomainModel.Whatsapp;
using Yoizen.Social.SocialServices.Facebook;

namespace Yoizen.Social.Core
{
	public partial class SystemLogic
	{
		#region Fields

		private Azure.Messaging.ServiceBus.ServiceBusClient sbClient = null;
		private Azure.Messaging.ServiceBus.ServiceBusSender sbSenderSends = null;
		private Azure.Messaging.ServiceBus.ServiceBusSender sbSenderTemplateSends = null;
		private Azure.Messaging.ServiceBus.ServiceBusSender sbSenderReplies = null;

		/// <summary>
		/// Lista de usuario bloqueados
		/// </summary>
		private List<DomainModel.SocialUser> blockedUsers = null;

		#endregion

		#region Constructors

		public SystemLogic()
		{
			this.CreateServiceBusIfNotExists();

			if (this.sbSenderReplies == null)
			{
				var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-replies";
				this.sbSenderReplies = this.sbClient.CreateSender(queueName);
			}

			if (this.sbSenderSends == null)
			{
				var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-sends";
				this.sbSenderSends = this.sbClient.CreateSender(queueName);
			}

			if (this.sbSenderTemplateSends == null)
			{
				var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-template-sends";
				this.sbSenderTemplateSends = this.sbClient.CreateSender(queueName);
			}
		}

		#endregion

		#region Private Methods

		private void CreateServiceBusIfNotExists()
		{
			if (this.sbClient == null)
			{
				var clientOptions = new Azure.Messaging.ServiceBus.ServiceBusClientOptions();
				clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
				clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
					Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpWebSockets :
					Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpTcp;

				Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

				this.sbClient = new Azure.Messaging.ServiceBus.ServiceBusClient(DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString, clientOptions);
			}
		}

		private void UpdateImportTagCaseLog(DomainModel.Case @case, DomainModel.Person person, int? importantTagID)
		{
			if (@case.ImportantTag == null && importantTagID == null)
			{
				// No cambió nada, no se actualiza nada
			}

			if (@case.ImportantTag != null && importantTagID != null && @case.ImportantTag.ID == importantTagID.Value)
			{
				// No cambió nada, no se actualiza nada
			}

			if (@case.ImportantTag != null && importantTagID == null)
			{
				// Si antes tenía etiqueta, y la nueva es NULL, significa que la removió
				CaseLogDAO.Insert(@case.ID, @case.Status, @case.Status, CaseLogTypes.RemovedImportantTag, person, null, @case.ImportantTag.ID);
			}
			else if (@case.ImportantTag == null && importantTagID != null)
			{
				// Si antes no tenía etiqueta, y la nueva es distinto de NULL, significa que la agregó
				CaseLogDAO.Insert(@case.ID, @case.Status, @case.Status, CaseLogTypes.AddedImportantTag, person, null, importantTagID);
			}
			else if (@case.ImportantTag != null && importantTagID != null && @case.ImportantTag.ID != importantTagID.Value)
			{
				// Cambió la etiqueta importante
				CaseLogDAO.Insert(@case.ID, @case.Status, @case.Status, CaseLogTypes.AddedImportantTag, person, null, importantTagID);
			}
		}

		private void UpdateDelayAndWaitingTimesOnCaseParameters(IEnumerable<Message> assignedMessages, Dictionary<string, string> parameters, DateTime replyDateTime)
		{
			if (assignedMessages != null)
			{
				DateTime? maxAssignedTime = DateTime.MinValue;
				foreach (Message msg in assignedMessages)
				{
					DateTime? actualAssignedTime = null;
					msg.MessageSegments = MessageSegmentDAO.GetAllByMessage(msg.ID);
					if (msg.MessageSegments != null)
					{
						actualAssignedTime = msg.MessageSegments.Where(ms => ms.AssignedDate.HasValue).Max(ms => ms.AssignedDate);
						if (actualAssignedTime.HasValue && actualAssignedTime > maxAssignedTime)
						{
							maxAssignedTime = actualAssignedTime;
						}
					}
					
				}

				Message firstMessageOnCase = assignedMessages.Where(m => m.MessageSegments.Count > 0).OrderBy(m => m.ID).FirstOrDefault();

				if (firstMessageOnCase != null)
				{
					IEnumerable<MessageSegment> messageSegments = firstMessageOnCase.MessageSegments.Where(ms => ms.EnqueuedDate.HasValue);

					if (messageSegments != null)
					{
						DateTime? firstQueuedTime = messageSegments.Min(ms => ms.EnqueuedDate);

						if (firstQueuedTime.HasValue && maxAssignedTime != DateTime.MinValue)
						{
							var internalQueueTime = maxAssignedTime.Value.Subtract(firstQueuedTime.Value);
							var responseTime = replyDateTime.Subtract(maxAssignedTime.Value);
							parameters[DomainModel.Case.TimeFirstMessageInQueueParameter] = internalQueueTime.TotalSeconds.ToString(global::System.Globalization.CultureInfo.InvariantCulture);
							parameters[DomainModel.Case.TimeDelayFromAssignedToAnswered] = responseTime.TotalSeconds.ToString(global::System.Globalization.CultureInfo.InvariantCulture);
						}
					}
				}
			}
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Publica un mensaje en el service bus para los mensajes que se enviarán
		/// </summary>
		/// <param name="jBody">Un <see cref="Newtonsoft.Json.Linq.JObject jBody"/> con toda la información necesaria para enviar</param>
		/// <returns><code>true</code> si se pudo publicar la respuesta; en caso contrario, <code>false</code></returns>
		public async Task<bool> PublishMessage(PublicMessageServiceBusOptions options)
		{
			try
			{
				var jInfo = new JObject();
				jInfo["messageId"] = options.Message.ID;
				jInfo["serviceId"] = options.ServiceId;
				jInfo["socialUserId"] = options.SocialUserId;
				jInfo["delay"] = options.Delay;
				if (options.Delay == null &&
					options.Message.Parameters.ContainsKey("DelayAfterSend"))
				{
					jInfo["delay"] = int.Parse(options.Message.Parameters["DelayAfterSend"]);
				}
				jInfo["uuid"] = options.Uuid.ToString();
				jInfo["request"] = new JObject();

				var httpRequestMessage = options.HttpRequestMessage;

				jInfo["request"]["method"] = httpRequestMessage.Method.ToString();
				jInfo["request"]["url"] = httpRequestMessage.RequestUri.ToString();

				if (httpRequestMessage.Headers != null)
				{
					foreach (var header in httpRequestMessage.Headers)
					{
						if (jInfo["request"]["headers"] == null)
							jInfo["request"]["headers"] = new JObject();

						jInfo["request"]["headers"][header.Key] = JArray.FromObject(header.Value);
					}
				}

				if (httpRequestMessage.Content != null)
				{
					if (httpRequestMessage.Content.GetType() == typeof(StringContent))
					{
						var stringContent = (StringContent) httpRequestMessage.Content;
						jInfo["request"]["content"] = new JObject();
						jInfo["request"]["content"]["content"] = await stringContent.ReadAsStringAsync();
						jInfo["request"]["content"]["headers"] = new JObject();

						foreach (var header in stringContent.Headers)
						{
							jInfo["request"]["content"]["headers"][header.Key] = JArray.FromObject(header.Value);
						}
					}
				}

				var messageToSend = new Azure.Messaging.ServiceBus.ServiceBusMessage();
				messageToSend.Body = new BinaryData(jInfo.ToString());
				messageToSend.ContentType = "application/json";

				if (options.IsTemplate)
				{
					await this.sbSenderTemplateSends.SendMessageAsync(messageToSend);
				}
				else
				{
					messageToSend.SessionId = $"{options.ServiceId}_{options.SocialUserId}";

					await this.sbSenderSends.SendMessageAsync(messageToSend);
				}

				return true;
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error publicando el mensaje {0} para ser enviado al service bus: {1}", options.Message.ID, ex);
				return false;
			}
		}

		/// <summary>
		/// Publica un mensaje para ser enviado luego para su reintento
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se reenviará</param>
		public async Task PublishReply(DomainModel.Message message)
		{
			if (message == null)
				return;

			if (message.DeliveryRetries != null &&
				message.DeliveryRetries.Value >= 10)
			{
				Tracer.TraceInfo("No se reintentará el mensaje {0} porque ya llegó a {1} reintentos", message, message.DeliveryRetries.Value);
				return;
			}

			this.CreateServiceBusIfNotExists();

			if (this.sbSenderReplies == null)
			{
				var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-replies";
				this.sbSenderReplies = this.sbClient.CreateSender(queueName);
			}

			try
			{
				var jBody = new Newtonsoft.Json.Linq.JObject();
				jBody["messageId"] = message.ID;
				jBody["serviceId"] = message.Service.ID;
				if (message.Case != null)
					jBody["caseId"] = message.Case.ID;
				var messageToSend = new Azure.Messaging.ServiceBus.ServiceBusMessage();
				messageToSend.Body = new BinaryData(jBody.ToString());
				messageToSend.ContentType = "application/json";
				messageToSend.ScheduledEnqueueTime = new DateTimeOffset(DateTime.UtcNow).AddSeconds(30);

				await this.sbSenderReplies.SendMessageAsync(messageToSend);

				Tracer.TraceError("Se envió el mensaje {0} a la cola de respuesta para ser reenviado luego", message);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error enviando el mensaje {0} a la cola de respuesta para ser reenviado luego: {1}", message, ex);
			}
		}

		/// <summary>
		/// Blanquea los usuarios bloqueados así se vuelven a cargar
		/// </summary>
		public void ResetBlockedUsers()
		{
			this.blockedUsers = null;
		}

		/// <summary>
		/// Devuelve si un usuario de red social está bloqueado por Social+
		/// </summary>
		/// <param name="userId">El código de usuario de red social</param>
		/// <param name="blockedUsers">La lista de usuarios bloqueados por Social+</param>
		/// <returns>true si el usuario se encuentra bloqueado; en caso contrario, false</returns>
		public bool IsSocialUserBlocked(DomainModel.SocialUser user)
		{
			if (this.blockedUsers == null)
			{
				try
				{
					this.blockedUsers = DAL.SocialUserDAO.GetAllByBlockedStatus(true);
				}
				catch { }
			}

			if (this.blockedUsers != null)
			{
				foreach (var blockedUser in this.blockedUsers)
				{
					if (blockedUser.SocialServiceType == user.SocialServiceType)
					{
						if (user.SocialServiceType == DomainModel.SocialServiceTypes.Mail)
						{
							if (blockedUser.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase))
								return true;
						}
						else
						{
							if (blockedUser.ID == user.ID)
								return true;
						}
					}
				}
			}

			DomainModel.Settings.SocialUserReference socialUserReference = user.ToSocialUserReference();
			if (DomainModel.SystemSettings.Instance.BlockedSocialUsers.Contains(socialUserReference))
			{
				return true;
			}

			return false;
		}

		/// <summary>
		/// Inserta un mensaje saliente en un caso el cual fue enviado por un procesador externo
		/// </summary>
		/// <param name="incomingMessage">El mensjae entrante por el cual se genera el mensaje saliente</param>
		/// <param name="service">el servicio perteneciente a ambos mensjes</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException"></exception>
		public long InsertWhatsappFromExternalProcessor(Message incomingMessage, Service service)
		{
			if (incomingMessage == null)
				throw new ArgumentNullException("message", "El mensaje no puede ser nulo");

			if (incomingMessage == null)
				throw new ArgumentNullException("service", "El mensaje no puede ser nulo");

			if (!incomingMessage.Parameters.ContainsKey(WhatsApp.WhatsAppMessage.ContextSourceTemplateNameParameter))
			{
				throw new ArgumentNullException("templateName", "El parametro del mensaje 'templateName' no puede ser nulo");
			}

			var serviceSettings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			var templateName = incomingMessage.Parameters[WhatsApp.WhatsAppMessage.ContextSourceTemplateNameParameter];
			var templateLanguage = incomingMessage.Parameters[WhatsApp.WhatsAppMessage.ContextSourceTemplateLanguageParameter];
			var template = serviceSettings.FindTemplate(templateName, templateLanguage);

			if (template == null)
			{
				throw new ArgumentNullException("HSMTemplate", "El Template no puede ser nulo");
			}

			//No validamos parametros porque puede ser un template de solo texto
			string bodyParameters = string.Empty;
			if (incomingMessage.Parameters.ContainsKey(WhatsApp.WhatsAppMessage.ContextSourceOutgoingTemplateParameters))
			{
				bodyParameters = incomingMessage.Parameters[WhatsApp.WhatsAppMessage.ContextSourceOutgoingTemplateParameters];
			}

			string body = serviceSettings.GenerateBodyFromParameters(bodyParameters, template);

			if (string.IsNullOrEmpty(body))
			{
				throw new ArgumentNullException("body", "el body del mensaje saliente no puede ser vacio y/o nulo");
			}

			DateTime date = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
			if (incomingMessage.Parameters.ContainsKey(WhatsApp.WhatsAppMessage.ContextSourceOutgoingTimeStamp))
			{
				long messageTimeStamp = long.Parse(incomingMessage.Parameters[WhatsApp.WhatsAppMessage.ContextSourceOutgoingTimeStamp]);
				date = date.AddSeconds(messageTimeStamp).ToLocalTime();
			}
			else
			{
				date = DateTime.Now;
			}

			string socialMessageId = string.Empty;
			if (incomingMessage.Parameters.ContainsKey(WhatsApp.WhatsAppMessage.ContextSourceOutgoingSocialMessageID))
			{
				socialMessageId = incomingMessage.Parameters[WhatsApp.WhatsAppMessage.ContextSourceOutgoingSocialMessageID];
			}

			var caseParameters = new Dictionary<string, string>();
			caseParameters[DomainModel.Case.LastReplyTimeParameter] = date.ToString("o");
			caseParameters[DomainModel.Case.LastReplyMessageIDParameter] = "@@INSERTED_MESSAGE_ID@@";
			caseParameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
			caseParameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = true.ToString();
			caseParameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
			caseParameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;

			var messageParameters = new Dictionary<string, string>();

			messageParameters[WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter] = template.Namespace;
			messageParameters[WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter] = template.ElementName;
			messageParameters[WhatsApp.WhatsAppMessage.HSMTemplateLanguage] = template.Language;
			messageParameters[WhatsApp.WhatsAppMessage.HSMParameter] = true.ToString();
			messageParameters[WhatsApp.WhatsAppMessage.HSMTemplateDataParameter] = bodyParameters;
			messageParameters[WhatsApp.WhatsAppMessage.ContextSourceIsFromHSMWithoutCase] = true.ToString();

			if (incomingMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignParameter))
			{
				string campaignIdStringParameter = incomingMessage.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter].ToString();
				messageParameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = campaignIdStringParameter;
			}

			if (incomingMessage.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.ContextStatusesForOutgoingMessage))
			{
				var statuses = JArray.Parse(incomingMessage.Parameters[Social.WhatsApp.WhatsAppMessage.ContextStatusesForOutgoingMessage]);

				if (statuses != null && statuses.Count > 0)
				{
					foreach (JObject status in statuses)
					{
						var timestamp = status["ts"].ToObject<long>();
						var statusDate = Common.Conversions.UnixTimeToDateTime(timestamp);
						statusDate = statusDate.ToLocalTime();

						if (status["type"] != null &&
							status["type"].Type == JTokenType.String)
						{
							var statusType = status["type"].ToString();
							switch (statusType)
							{
								case "sent":
									{
										messageParameters[Social.WhatsApp.WhatsAppMessage.SentParameter] = true.ToString();
										messageParameters[Social.WhatsApp.WhatsAppMessage.SentDateParameter] = statusDate.ToString("o");
										messageParameters[Social.WhatsApp.WhatsAppMessage.SentAtParameter] = timestamp.ToString();
									}
									break;
								case "delivered":
									{
										messageParameters[Social.WhatsApp.WhatsAppMessage.DeliveredParameter] = true.ToString();
										messageParameters[Social.WhatsApp.WhatsAppMessage.DeliveredDateParameter] = date.ToString("o");
										messageParameters[Social.WhatsApp.WhatsAppMessage.DeliveredAtParameter] = timestamp.ToString();
									}
									break;
								case "read":
									{
										messageParameters[Social.WhatsApp.WhatsAppMessage.ReadParameter] = true.ToString();
										messageParameters[Social.WhatsApp.WhatsAppMessage.ReadDateParameter] = statusDate.ToString("o");
										messageParameters[Social.WhatsApp.WhatsAppMessage.ReadAtParameter] = timestamp.ToString();
									}
									break;
								default:
									break;
							}
						}
					}
				}
			}

			var insertedMessageId = MessageDAO.SendExternalWhatsapp(null
				, incomingMessage.SocialUser.ID
				, incomingMessage.SocialUser.Profile.ID
				, incomingMessage.Service.ID
				, body
				, null
				, messageParameters
				, true
				, null
				, caseParameters
				, -1
				, date
				, socialMessageId
				, CaseStartedBySources.WhatsappHSM
				, out bool newCaseCreated
				, out DomainModel.Case @case);

			var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

			if (@case != null && !newCaseCreated)
			{
				/*
				* Si el caso no es nuevo, entonces no se actualizaron los parámetros en el Stored Procedure del caso.
				* Por lo que se editan y se vuelven a grabar
				*/

				@case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
				@case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageId.ToString();
				@case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
				@case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = true.ToString();
				@case.Parameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
				@case.Parameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;

				DAL.CaseDAO.UpdateParameters(@case);
			}

			DomainModel.Historical.Daily info;
			DomainModel.Historical.DailyService infoService;

			info = DomainModel.Historical.Daily.CreateForInterval(interval);
			info.QueueID = 0;
			info.PersonID = 0;
			info.NewCases = 1;
			info.OutgoingMessages = 1;
			Core.System.Instance.IntervalService.StoreInfo(info, interval);

			infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
			infoService.ServiceID = service.ID;
			infoService.QueueID = 0;
			infoService.PersonID = 0;
			infoService.OutboundMessages = 1;
			infoService.MessagesPayment = 1;
			infoService.NewCases = newCaseCreated ? 1 : 0;
			Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

			if (newCaseCreated)
			{
				DomainModel.Historical.DailyCase infoCase;
				infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
				infoCase.CasesStarted = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);
			}

			var relatedEntitiesToRead = new MessageDAO.RelatedEntitiesToRead(false)
			{
				RepliesTo = true,
				AssociatedMessage = false,
				Service = true,
				Attachments = false,
				RepliedBy = true,
				RepliesToSocialUser = true
			};

			if (incomingMessage.Case == null || @case != null)
				incomingMessage.Case = @case;

			if (incomingMessage.Case != null)
				incomingMessage.Case.AddReply(incomingMessage);

			return insertedMessageId;
		}

		/// <summary>
		/// Devuelve si datos de negocio son válidos de acuerdo a la definición del formato de los datos de negocio
		/// </summary>
		/// <param name="businessData">Los datos de negocio a verificar</param>
		/// <returns><code>true</code> si los datos son válidos; en caso contrario, <code>false</code></returns>
		public bool IsBusinessDataValid(string businessData)
		{
			if (string.IsNullOrEmpty(businessData))
				return true;

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendProfile &&
				DomainModel.SystemSettings.Instance.ExtendedProfilesBusinessCodeFields != null &&
				DomainModel.SystemSettings.Instance.ExtendedProfilesBusinessCodeFields.Length > 0)
			{
				var parts = businessData.Split(",".ToCharArray());

				foreach (var part in parts)
				{
					var elements = part.Split("#".ToCharArray());
					if (elements.Length != DomainModel.SystemSettings.Instance.ExtendedProfilesBusinessCodeFields.Length)
					{
						return false;
					}

					for (var i = 0; i < DomainModel.SystemSettings.Instance.ExtendedProfilesBusinessCodeFields.Length; i++)
					{
						var field = DomainModel.SystemSettings.Instance.ExtendedProfilesBusinessCodeFields[i];
						var element = elements[i];

						if (string.IsNullOrEmpty(element))
						{
							return false;
						}

						switch (field.DataType)
						{
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.String:
								if (!string.IsNullOrEmpty(field.String.Regex))
								{
									var regex = new global::System.Text.RegularExpressions.Regex(field.String.Regex);
									if (!regex.IsMatch(element))
									{
										return false;
									}
								}
								break;
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.Number:
								long number;
								if (!long.TryParse(element, out number))
								{
									return false;
								}

								if (field.Number.Max != null && number > field.Number.Max)
								{
									return false;
								}

								if (field.Number.Min != null && number < field.Number.Min)
								{
									return false;
								}

								break;
							case ExtendedProfileBusinessCodeField.ExtendedProfileBusinessCodeFieldDataTypes.Dropdown:
								var found = false;
								foreach (var item in field.Dropdown.Items)
								{
									if (item.Value.Equals(element))
									{
										found = true;
										break;
									}
								}

								if (!found)
								{
									return false;
								}

								break;
							default:
								break;
						}
					}
				}
			}
			else
			{
				string regexForBusinessData = @"^[a-zA-Z0-9]{6,12}(,[a-zA-Z0-9]{6,12})*$";
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowCustomBusinessDataRegex && !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.BusinessDataRegex))
					regexForBusinessData = DomainModel.SystemSettings.Instance.BusinessDataRegex;
				var regex = new global::System.Text.RegularExpressions.Regex(regexForBusinessData);
				if (!regex.IsMatch(businessData))
				{
					return false;
				}

				try
				{
					string[] parts = businessData.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
					var distinctParts = new List<string>();
					for (var i = 0; i < parts.Length; i++)
					{
						if (string.IsNullOrEmpty(parts[i]))
						{
							return false;
						}

						if (!distinctParts.Contains(parts[i], StringComparer.InvariantCultureIgnoreCase))
						{
							distinctParts.Add(parts[i]);
						}
					}

					businessData = string.Join(",", distinctParts);
				}
				catch
				{
					return false;
				}
			}

			return true;
		}



		/// <summary>
		/// Devuelve si los datos extendidos del caso son válidos de acuerdo a la definición del formato de los datos extendidos de casos
		/// </summary>
		/// <param name="extendedCaseFieldsText">Los datos extendidos de casos a verificar</param>
		/// <returns><code>true</code> si los datos son válidos; en caso contrario, <code>false</code></returns>
		public bool IsExtendedCaseValid(string extendedCaseFieldsText)
		{
			if (string.IsNullOrEmpty(extendedCaseFieldsText))
				return true;

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendCase &&
				DomainModel.SystemSettings.Instance.ExtendedCasesFields != null &&
				DomainModel.SystemSettings.Instance.ExtendedCasesFields.Length > 0)
			{
				var extendedCaseFields = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(extendedCaseFieldsText);
				return this.IsExtendedCaseValid(extendedCaseFields);
			}

			return true;
		}

		/// <summary>
		/// Devuelve si los datos extendidos del caso son válidos de acuerdo a la definición del formato de los datos extendidos de casos
		/// </summary>
		/// <param name="extendedCaseFields">Los datos extendidos de casos a verificar</param>
		/// <returns><code>true</code> si los datos son válidos; en caso contrario, <code>false</code></returns>
		public bool IsExtendedCaseValid(Dictionary<string, string> extendedCaseFields)
		{
			if (extendedCaseFields == null || !extendedCaseFields.Any())
				return true;

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendCase &&
				DomainModel.SystemSettings.Instance.ExtendedCasesFields != null &&
				DomainModel.SystemSettings.Instance.ExtendedCasesFields.Length > 0)
			{
				foreach (var part in extendedCaseFields)
				{
					for (var i = 0; i < DomainModel.SystemSettings.Instance.ExtendedCasesFields.Length; i++)
					{
						var field = DomainModel.SystemSettings.Instance.ExtendedCasesFields[i];
						if (part.Key.Contains(field.Name))
						{
							switch (field.DataType)
							{
								case ExtendedField.ExtendedFieldDataTypes.String:
									if (!string.IsNullOrEmpty(field.String.Regex))
									{
										var regex = new global::System.Text.RegularExpressions.Regex(field.String.Regex);
										if (!regex.IsMatch(part.Value))
										{
											return false;
										}
									}
									break;
								case ExtendedField.ExtendedFieldDataTypes.Number:
									long number;
									if (!long.TryParse(part.Value, out number))
									{
										return false;
									}

									if (field.Number.Max != null && number > field.Number.Max)
									{
										return false;
									}

									if (field.Number.Min != null && number < field.Number.Min)
									{
										return false;
									}

									break;
								case ExtendedField.ExtendedFieldDataTypes.Dropdown:
									var found = false;
									foreach (var item in field.Dropdown.Items)
									{
										if (item.Value.Equals(part.Value))
										{
											found = true;
											break;
										}
									}

									if (!found)
									{
										return false;
									}

									break;
								case ExtendedField.ExtendedFieldDataTypes.Float:
									float isFloat;
									string value;
									value = part.Value.Replace('.', ',');
									if (!float.TryParse(value, out isFloat))
									{
										return false;
									}

									if (field.Float.Max != null && isFloat > field.Float.Max)
									{
										return false;
									}

									if (field.Float.Min != null && isFloat < field.Float.Min)
									{
										return false;
									}
									break;
								case ExtendedField.ExtendedFieldDataTypes.Boolean:
									try
									{
										bool.Parse(part.Value);
									}
									catch
									{
										return false;
									}
									break;
								case ExtendedField.ExtendedFieldDataTypes.Date:

									DateTime maxDate = DateTime.Today;
									DateTime minDate = DateTime.Today;
									DateTime myDate;

									try
									{
										myDate = DateTime.Parse(part.Value);
									}
									catch
									{
										return false;
									}


									if (field.Date.Max != null && field.Date.Max.Length > 0)
									{
										if (field.Date.Max != "@@HOY@@")
										{
											maxDate = DateTime.Parse(field.Date.Max);
										}
									}

									if (field.Date.Min != null && field.Date.Min.Length > 0)
									{
										if (field.Date.Min != "@@HOY@@")
										{
											minDate = DateTime.Parse(field.Date.Min);
										}
									}

									if (myDate < minDate || myDate > maxDate)
									{
										return false;
									}


									break;
								default:
									break;
							}
						}
					}
				}
			}

			return true;
		}

		/// <summary>
		/// Crea (si no existe uno abierto) o continua (en caso de existir uno abierto) un caso asignando el mensaje al 
		/// caso
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se asignará al caso</param>
		/// <param name="currentProfileCase">Un <see cref="DomainModel.Case"/> con el caso actual en cola del perfil/servicio o <code>null</code> si no hay un caso en cola</param>
		/// <param name="caseStartedBySource">Un valor de la enumeración <see cref="DomainModel.CaseStartedBySources"/> indicando el origen de la creación del nuevo caso</param>
		public bool CreateOrContinueCase(DomainModel.Message message, DomainModel.Case currentProfileCase, DomainModel.CaseStartedBySources caseStartedBySource = CaseStartedBySources.IncomingMessage)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			Tracer.TraceInfo("Se creará o continuará un caso con el mensaje {0}", message);

			try
			{
				var @case = CaseDAO.CreateOrContinueCase(message, currentProfileCase, caseStartedBySource, out bool newCaseCreated);
				message.PostedBy.Profile = @case.Profile;
				message.Case = @case;
				if (newCaseCreated)
					@case.CaseStartedBySource = caseStartedBySource;

				if (message.PostedBy.Anonymous)
				{
					if (message.Parameters.ContainsKey(Social.Chat.ChatMessage.AnonymousMailParameter))
						message.PostedBy.Email = message.Parameters[Social.Chat.ChatMessage.AnonymousMailParameter];

					if (message.Parameters.ContainsKey(Social.Chat.ChatMessage.AnonymousNameParameter))
						message.PostedBy.Name = message.Parameters[Social.Chat.ChatMessage.AnonymousNameParameter];

					if (message.Parameters.ContainsKey(Social.Chat.ChatMessage.AnonymousDisplayNameParameter))
						message.PostedBy.DisplayName = message.Parameters[Social.Chat.ChatMessage.AnonymousDisplayNameParameter];

					if (message.Parameters.ContainsKey(Social.Chat.ChatMessage.AnonymousPhoneParameter))
						message.PostedBy.Parameters["Phone"] = message.Parameters[Social.Chat.ChatMessage.AnonymousPhoneParameter];

					message.PostedBy.Profile.Name = message.PostedBy.Name;
				}

				if (newCaseCreated)
				{
					DomainModel.Historical.DailyCase infoCase;
					var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.CasesStarted = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

#if !NETCOREAPP
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases) &&
						Core.System.Instance.AutomaticExportService != null)
					{
						Core.System.Instance.AutomaticExportService.StoreCase(@case);
					}
#endif
				}

				return newCaseCreated;
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se creaba o continuaba un caso con el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Guarda etiquetas para un mensaje luego de aplicar un filtro
		/// </summary>
		/// <param name="case">El caso en donde se aplicará las etiquetas</param>
		/// <param name="tags">Los códigos de etiquetas a aplicar</param>
		public void ApplyTags(DomainModel.Case @case, IEnumerable<int> tags, DomainModel.TaggedBySources taggedBy)
		{
			ApplyTags(@case, tags, taggedBy, null);
		}

		/// <summary>
		/// Guarda una etiqueta para un mensaje luego de aplicar un filtro
		/// </summary>
		/// <param name="case">El caso en donde se aplicará la etiqueta</param>
		/// <param name="tags">El código de etiqueta a aplicar</param>
		public void ApplyTag(DomainModel.Case @case, int tag, DomainModel.TaggedBySources taggedBy)
		{
			ApplyTags(@case, new int[] { tag }, taggedBy, null);
		}

		/// <summary>
		/// Guarda etiquetas para un mensaje luego de aplicar un filtro
		/// </summary>
		/// <param name="case">El caso en donde se aplicará las etiquetas</param>
		/// <param name="tags">Los códigos de etiquetas a aplicar</param>
		/// <param name="person">El <see cref="DomainModel.Person"/> que aplicó la etiqueta o <code>null</code> para indicar que fue el sistema</param>
		public void ApplyTags(DomainModel.Case @case, IEnumerable<int> tags, DomainModel.TaggedBySources taggedBy, DomainModel.Person person)
		{
			if (@case == null)
				throw new ArgumentNullException(nameof(@case), "El caso no puede ser nulo");

			if (tags == null || !tags.Any())
				throw new ArgumentException("Debe especificar las etiquetas", nameof(tags));

			Tracer.TraceInfo("Se están aplicando las etiquetas al caso {0}", @case);

			try
			{
				CaseDAO.ApplyTags(@case, tags, person, taggedBy);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

#if !NETCOREAPP
				SystemQueue queue = null;
				if (@case.Queue != null)
					queue = Core.System.Instance.QueueService.GetQueueById(@case.Queue.ID);
#endif

				foreach (var tagId in tags)
				{
					var tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(tagId);
					if (tag != null)
					{
						if (!@case.Tags.Contains(tag))
							@case.Tags.Add(tag);

						tag.Used = true;

#if !NETCOREAPP
						if (queue != null)
						{
							if (!queue.RealTimeInfo.Tags.ContainsKey(tag))
								queue.RealTimeInfo.Tags.Add(tag, 1);
							else
								queue.RealTimeInfo.Tags[tag]++;
						}
#endif
					}

					if (@case.Queue != null)
					{
						var infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
						infoTag.QueueID = @case.Queue.ID;
						infoTag.PersonID = 0;
						infoTag.TagID = tagId;
						infoTag.Count = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);

						if (person != null)
						{
							infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
							infoTag.QueueID = @case.Queue.ID;
							infoTag.PersonID = person.ID;
							infoTag.TagID = tagId;
							infoTag.Count = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);
						}
					}
					else
					{
						var infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
						infoTag.QueueID = 0;
						infoTag.PersonID = 0;
						infoTag.TagID = tagId;
						infoTag.Count = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se aplicaban etiquetas al caso {0}: {1}", @case, ex);
				throw;
			}
		}

		/// <summary>
		/// Actualiza las etiquetas y observaciones de un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a actualizar</param>
		/// <param name="person">La persona que está actualizando el caso o null para indicar que fue el sistema</param>
		/// <param name="tags">Una enumeración de los códigos de etiquetas a aplicar</param>
		/// <param name="observations">Las observaciones del caso</param>
		/// <param name="parameters">Parámetros extra</param>
		/// <param name="taggedBy">Indica quién realizó el etiquetado</param>
		public void UpdateCase(DomainModel.Case @case, DomainModel.Person person, IEnumerable<int> tags, string observations, Dictionary<string, string> parameters, DomainModel.TaggedBySources taggedBy, int? importantTagID)
		{
			if (@case == null)
				throw new ArgumentNullException(nameof(@case), "El caso no puede ser nulo");

			try
			{
				UpdateImportTagCaseLog(@case, person, importantTagID);
				@case.ImportantTag = importantTagID == null ? null : Cache.Instance.GetItem<Tag>(importantTagID);
				CaseDAO dao = new CaseDAO(@case);
				dao.Update(person == null ? (int?) null : person.ID, tags, observations, parameters, taggedBy, importantTagID);
				@case.Observations = observations;
				if (parameters != null)
					@case.Parameters = parameters;

				// Actualizamos las estadísticas de etiquetas de tiempo real de las colas
				if (tags != null)
				{
#if !NETCOREAPP
					SystemQueue queue = null;
					if (@case.Queue != null)
						queue = Core.System.Instance.QueueService.GetQueueById(@case.Queue.ID);
#endif

					var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

					foreach (var tagId in tags)
					{
						DomainModel.Tag tag = DAL.TagDAO.GetOneFromCache(tagId);
						if (tag != null)
						{
							if (!@case.Tags.Contains(tag))
								@case.Tags.Add(tag);

							tag.Used = true;
#if !NETCOREAPP
							if (queue != null)
							{
								if (!queue.RealTimeInfo.Tags.ContainsKey(tag))
									queue.RealTimeInfo.Tags.Add(tag, 1);
								else
									queue.RealTimeInfo.Tags[tag]++;
							}
#endif
						}

						var infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
						if (@case.Queue != null)
							infoTag.QueueID = @case.Queue.ID;
						else
							infoTag.QueueID = 0;

						infoTag.PersonID = 0;
						infoTag.TagID = tagId;
						infoTag.Count = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);

						if (person != null)
						{
							infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
							if (@case.Queue != null)
								infoTag.QueueID = @case.Queue.ID;
							else
								infoTag.QueueID = 0;

							infoTag.PersonID = person.ID;
							infoTag.TagID = tagId;
							infoTag.Count = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se actualizaba el caso {0}: {1}", @case, ex);
				throw;
			}
		}

		/// <summary>
		/// Computa estadísticas consolidadas para un mensaje nuevo que no es encolado
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que no se encoló</param>
		/// <param name="newCaseCreated">Indica si para el mensaje <paramref name="message"/> se acaba de crear un caso nuevo</param>
		public void ComputeNewMessage(DomainModel.Message message, bool newCaseCreated)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			try
			{
				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.NewMessages = 1;
				info.NewChats = (message.SocialServiceType == SocialServiceTypes.Chat) ? 1 : 0;
				info.NewCases = newCaseCreated ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.NewCases = newCaseCreated ? 1 : 0;
				infoService.NewMessages = 1;
				if (message.SocialServiceType == SocialServiceTypes.Twitter ||
					message.SocialServiceType == SocialServiceTypes.Facebook)
				{
					infoService.NewMessagesPrivate = message.IsDirectMessage ? 1 : 0;
					infoService.NewMessagesPublic = message.IsDirectMessage ? 0 : 1;
				}
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

#if !NETCOREAPP
				var systemService = Core.System.Instance.ServicesServices.GetServiceById(message.Service.ID);
				if (systemService != null)
					systemService.RealTimeInfo.NewCases++;
#endif
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se computaba la información de mensaje nuevo del mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Envía un mensaje saliente de whatsapp
		/// </summary>
		/// <param name="options">El <see cref="ActionOptions.SendWhatsappOptions"/> con las opciones de envío del mensaje de whatsapp</param>
		/// <returns>Cuando retorna, devuelve el código de mensaje envíado</returns>
		public async Task<long> SendWhatsapp(ActionOptions.SendWhatsappOptions options, Boolean sendByYFlow = false)
		{
			if (options == null)
				throw new ArgumentNullException(nameof(options), "options no puede ser null");

			if (options.Service == null)
				throw new ArgumentNullException(nameof(options.Service), "Debe especificar el servicio");

			if (string.IsNullOrEmpty(options.Text) && options.Parameters == null)
				throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(options.Text));

			if (options.Person != null)
			{
				if (options.Person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} está enviando un whatsapp saliente", options.Person);
				else
					Tracer.TraceInfo("El supervisor {0} está enviando un whatsapp saliente", options.Person);
			}
			else
			{
				Tracer.TraceInfo("El sistema está enviando un whatsapp saliente");
			}

			var whatsappSettings = options.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			//TODO: Ver si  corresponde saltear esto o hay que validarlo.
			if (!whatsappSettings.AllowToSendHSM && !sendByYFlow)
				throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM");

			if (options.Person != null && options.Person.Type == PersonTypes.Agent && !whatsappSettings.AllowAgentsToSendHSM && !sendByYFlow)
				throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM por parte de los agentes");

			try
			{
				var socialUser = DAL.SocialUserDAO.GetOne(options.PhoneNumber, SocialServiceTypes.WhatsApp, false, false);
				int? socialUserProfileId;
				if (socialUser == null)
				{
					socialUser = new WhatsApp.User(options.PhoneNumber);
					socialUser.Name = options.PhoneNumber.ToString();
					socialUser.DisplayName = options.PhoneNumber.ToString();
					if (options.BusinessDataOption != OptionsBussinesData.NoneAction)
					{
						if (options.BusinessDataOption == OptionsBussinesData.DeleteAll)
							socialUser.BusinessData = null;
						else if (options.BusinessDataOption == OptionsBussinesData.Replace && Core.System.Instance.Logic.IsBusinessDataValid(options.BusinessData))
							socialUser.BusinessData = options.BusinessData;
					}

					var socialUserDAO = new SocialUserDAO(socialUser);
					socialUserDAO.Insert(out socialUserProfileId);
				}
				else
				{
					socialUserProfileId = socialUser.Profile.ID;

					if (options.BusinessDataOption != OptionsBussinesData.NoneAction)
					{
						var updateProfile = false;

						switch (options.BusinessDataOption)
						{
							case OptionsBussinesData.DeleteAll:
								socialUser.BusinessData = null;
								socialUser.Profile.BusinessData = null;
								updateProfile = true;
								break;

							case OptionsBussinesData.Replace:
								if (!string.IsNullOrEmpty(options.BusinessData))
								{
									socialUser.BusinessData = options.BusinessData;
									socialUser.Profile.BusinessData = options.BusinessData;
									updateProfile = true;
								}
								break;

							case OptionsBussinesData.Concat:
								if (!string.IsNullOrEmpty(options.BusinessData))
								{
									var newBusinessData = string.Concat(socialUser.BusinessData, ",", options.BusinessData);
									newBusinessData = string.Join(",", newBusinessData.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).Distinct());

									socialUser.BusinessData = newBusinessData;
									socialUser.Profile.BusinessData = newBusinessData;
									updateProfile = true;
								}
								break;

							case OptionsBussinesData.DeleteOne:
								if (!string.IsNullOrEmpty(options.BusinessData))
								{
									var arrBusinessData = socialUser.BusinessData.Split(',');
									var filteredBusinessDataArr = arrBusinessData.Where(val => val != options.BusinessData).ToArray();
									var filteredBusinessData = string.Join(",", filteredBusinessDataArr);

									socialUser.BusinessData = filteredBusinessData;
									socialUser.Profile.BusinessData = filteredBusinessData;
									updateProfile = true;
								}
								break;

							default:
								break;
						}

						if (updateProfile)
						{
							SocialUserProfileDAO.UpdateBusinessData(socialUser.Profile.ID, socialUser.BusinessData, null);
						}
					}
				}

				var isHSM = false;
				if (options.Parameters != null &&
					options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
					bool.Parse(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter]))
					isHSM = true;

				WhatsappSettings.HSMTemplate template = options.Template;
				if (isHSM)
				{
					if (template == null)
					{
						if (options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
						{
							var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);
							if (sendDefinition != null)
							{
								template = options.Template;
								if (template == null)
								{
									template = whatsappSettings.HSMTemplates.FirstOrDefault(t =>
										t.Namespace.Equals(sendDefinition.Namespace) &&
										t.ElementName.Equals(sendDefinition.ElementName) &&
										t.Language.Equals(sendDefinition.Language));

									options.Template = template;
								}

								if (options.Template != null && sendDefinition != null)
								{
									if (string.IsNullOrEmpty(options.Text))
									{
										options.Text = options.Template.Template;

										if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
										{
											foreach (var templateParameter in sendDefinition.Parameters)
											{
												options.Text = options.Text.Replace($"{{{{{templateParameter.Name}}}}}", templateParameter.Value);
											}
										}
									}
								}
							}
						}
						else
						{
							template = whatsappSettings.HSMTemplates.FirstOrDefault(t =>
								t.Namespace.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter]) &&
								t.ElementName.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter]) &&
								t.Language.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage]));
							if (template != null)
							{
								options.Text = template.Template;
								if (template.Parameters != null && template.Parameters.Length > 0)
								{
									var parameterValues = Newtonsoft.Json.Linq.JArray.Parse(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
									for (var i = 0; i < template.Parameters.Length; i++)
									{
										var parameterName = template.Parameters[i].Substring(0, template.Parameters[i].IndexOf('='));
										options.Text = options.Text.Replace("{{" + parameterName + "}}", parameterValues[i].ToString());
									}
								}
								else
								{
									if (options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
									{
										options.Parameters.Remove(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter);
									}
								}
							}
						}
					}
				}

				var caseParameters = new Dictionary<string, string>();
				caseParameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
				caseParameters[DomainModel.Case.LastReplyMessageIDParameter] = "@@INSERTED_MESSAGE_ID@@";
				caseParameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
				caseParameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = isHSM.ToString();

				if (isHSM)
				{
					caseParameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
					caseParameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;
					caseParameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) options.Source).ToString();
					switch (options.Source)
					{
						case ReplySources.Agent:
						case ReplySources.Supervisor:
							caseParameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = options.Person.ID.ToString();
							break;
						case ReplySources.ExternalIntegration:
							if (options.SourceId != null)
							{
								caseParameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = options.SourceId.Value.ToString();
							}
							else
							{
								caseParameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = string.Empty;
							}
							break;
						case ReplySources.YFlow:
							break;
						default:
							break;
					}
				}

				DomainModel.CaseStartedBySources caseStartedBySource;
				if (isHSM)
				{
					switch (options.Source)
					{
						case ReplySources.Agent:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByAgent;
							break;
						case ReplySources.Supervisor:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMBySupervisor;
							break;
						case ReplySources.YFlow:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByYFlow;
							break;
						case ReplySources.ExternalIntegration:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByIntegration;
							break;
						default:
							caseStartedBySource = CaseStartedBySources.WhatsappHSM;
							break;
					}
				}
				else
				{
					caseStartedBySource = CaseStartedBySources.OutgoingMessage;
				}

				var insertedMessageId = MessageDAO.SendWhatsapp(options.Person
					, socialUser.ID
					, socialUserProfileId.Value
					, options.Service.ID
					, options.Text
					, options.Attachments
					, options.Parameters
					, isHSM
					, options.Tags
					, caseParameters
					, options.TaskID
					, caseStartedBySource
					, out bool newCaseCreated
					, out DomainModel.Case @case);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				if (@case != null)
				{
					@case.LastPerson = options.Person ?? @case.LastPerson;

					/*
					 * Si el caso no es nuevo, entonces no se actualizaron los parámetros en el Stored Procedure del caso.
					 * Por lo que se editan y se vuelven a grabar
					 */
					if (!newCaseCreated)
					{
						@case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
						@case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageId.ToString();
						@case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
						@case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = isHSM.ToString();

						if (isHSM)
						{
							@case.Parameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
							@case.Parameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;
							@case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) options.Source).ToString();
							switch (options.Source)
							{
								case ReplySources.Agent:
								case ReplySources.Supervisor:
									@case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = options.Person.ID.ToString();
									break;
								case ReplySources.ExternalIntegration:
									if (options.SourceId != null)
									{
										@case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = options.SourceId.Value.ToString();
									}
									else
									{
										@case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = string.Empty;
									}
									break;
								default:
									break;
							}
						}

						if (options.ExtendedCaseDataOption != OptionsExtendedCaseData.NoneAction)
						{
							switch (options.ExtendedCaseDataOption)
							{
								case OptionsExtendedCaseData.Replace:
									{
										@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = options.ExtendedCaseData;
#if !NETCOREAPP
										var enqueuedOrAssignedMessages = System.Instance.QueueService.GetMessagesEnqueuedOrAssigned(@case.ID, null);
										foreach (var message in enqueuedOrAssignedMessages)
										{
											message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = options.ExtendedCaseData;
										}
#endif
									}
									break;

								case OptionsExtendedCaseData.Concat:
									{
										if (!@case.Parameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter))
										{
											@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = options.ExtendedCaseData;
#if !NETCOREAPP
											var enqueuedOrAssignedMessages = System.Instance.QueueService.GetMessagesEnqueuedOrAssigned(@case.ID, null);
											foreach (var message in enqueuedOrAssignedMessages)
											{
												message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = options.ExtendedCaseData;
											}
#endif
										}
										else
										{
											var currentExtendedFieldsDictionary = Common.Conversions.ConvertStringToDictionary(@case.Parameters[DomainModel.Case.ExtendedFieldsParameter]);

											var extendedFieldsDictionary = Common.Conversions.ConvertStringToDictionary(options.ExtendedCaseData);
											foreach (var element in extendedFieldsDictionary)
											{
												currentExtendedFieldsDictionary[element.Key] = element.Value;
											}

											var serializedExtendedFields = Common.Conversions.ConvertDictionaryToString(currentExtendedFieldsDictionary);
											@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = serializedExtendedFields;
#if !NETCOREAPP
											var enqueuedOrAssignedMessages = System.Instance.QueueService.GetMessagesEnqueuedOrAssigned(@case.ID, null);
											foreach (var message in enqueuedOrAssignedMessages)
											{
												message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = serializedExtendedFields;
											}
#endif
										}
									}
									break;
								default:
									break;
							}
						}
					}
					else
					{
						if (options.ExtendedCaseDataOption != OptionsExtendedCaseData.NoneAction)
						{
							var extendedFieldsDictionary = Common.Conversions.ConvertStringToDictionary(options.ExtendedCaseData);

							//no tiene sentido analizar la accion ya que el caso se acaba de crear y solo puedo pisar todo.
							@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = Common.Conversions.ConvertDictionaryToString(extendedFieldsDictionary);
						}
					}

					if (options.Tags != null)
					{
						// Actualizamos las estadísticas de etiquetas de tiempo real de las colas
						foreach (var tag in options.Tags)
						{
							if (!@case.Tags.Contains(tag))
								@case.Tags.Add(tag);

							tag.Used = true;

							var infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
							infoTag.QueueID = 0;
							infoTag.PersonID = 0;
							infoTag.TagID = tag.ID;
							infoTag.Count = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);

							if (options.Person != null)
							{
								infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
								infoTag.QueueID = 0;
								infoTag.PersonID = options.Person.ID;
								infoTag.TagID = tag.ID;
								infoTag.Count = 1;
								Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);
							}

							this.ApplyTags(@case, options.Tags.Select(t => t.ID), TaggedBySources.Agent, options.Person);
							Tracer.TraceInfo("Se agregaron etiquetas al caso {0}", @case.ID);
						}

						this.UpdateCase(@case, null, null, options.Observations, @case.Parameters, TaggedBySources.Agent, options.ImportantTag);

					}

					DAL.CaseDAO.UpdateParameters(@case);
				}

				if (options.Person != null)
				{
					if (options.Person.Type == DomainModel.PersonTypes.Agent)
						Tracer.TraceInfo("El agente {0} envió el mensaje saliente", options.Person);
					else
						Tracer.TraceInfo("El supervisor {0} envió el mensaje saliente", options.Person);
				}
				else
				{
					Tracer.TraceInfo("Se envió el mensaje saliente", options.Person);
				}

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.NewCases = 1;
				info.OutgoingMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				if (options.Person != null)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = options.Person.ID;
					info.OutgoingMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

#if !NETCOREAPP
					if (options.Person.Type == DomainModel.PersonTypes.Agent)
					{
						var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) options.Person);
						if (connectionInfo.Status == ConnectionStatuses.Aux &&
							DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages != null &&
							connectionInfo.AuxReason == DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages.Value)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = 0;
							info.PersonID = options.Person.ID;
							info.AgentOutTime = connectionInfo.GetSecondsSinceLastStatusChange();
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

						HistSessionsAgentsCasesMessagesDAO.Insert(insertedMessageId, @case.ID, connectionInfo.SessionID, options.Person.ID, false);
					}
#endif
				}

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = options.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.OutboundMessages = 1;
				infoService.MessagesPayment = isHSM ? 1 : 0;
				infoService.NewCases = newCaseCreated ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (options.Person != null)
				{
					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = options.Person.ID;
					infoService.OutboundMessages = 1;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					infoService.NewCases = newCaseCreated ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				if (newCaseCreated)
				{
					DomainModel.Historical.DailyCase infoCase;
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.CasesStarted = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);
				}

				var insertedMessage = await Reply(insertedMessageId,
					@case,
					options.SendInBackground,
					null,
					socialUser,
					options.Person,
					false,
					options.Attachments != null && options.Attachments.Any());
				options.InsertedMessage = insertedMessage;

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(insertedMessage);
				}

				if (newCaseCreated || @case.LastIncomingMessageID == null)
				{
					await Core.System.Instance.CasesService.RegisterCaseToBeClosed(@case, options.Service.CasesSettings.MaxElapsedMinutesToCloseHsmCases, CaseClosingSources.WhatsappHSM);
				}
				else
				{
					await Core.System.Instance.CasesService.RegisterCaseToBeClosed(@case, options.Service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);
				}

				return insertedMessageId;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló cuando se envíaba el mensaje saliente {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Cierra un caso
		/// </summary>
		/// <param name="options">Un <see cref="ActionOptions.CloseCaseOptions"/> con los datos de las opciones para cerrar el caso</param>
		public async Task CloseCaseAsync(ActionOptions.CloseCaseOptions options)
		{
			if (options == null)
				throw new ArgumentNullException(nameof(options));

			if ((options.ClosedBy == CaseClosingResponsibles.Agent || options.ClosedBy == CaseClosingResponsibles.Supervisor) &&
				options.Person == null)
				throw new ArgumentNullException(nameof(options.Person), "El parámetro person es requerido cuando el descarte lo hace un agente o un supervisor");

			var @case = options.Case;
			if (@case != null)
				Tracer.TraceInfo("Se cerrará el caso {0}", @case);

			try
			{
				var now = DateTime.Now;
				DomainModel.Queue queue = null;

				if (@case != null)
				{
					if (options.ClosedBy == CaseClosingResponsibles.ExternalIntegration &&
						options.ExternalIntegration != null)
					{
						@case.Parameters[DomainModel.Case.ClosedByExternalIntegrationParameter] = $"{options.ExternalIntegration.ID} - {options.ExternalIntegration.Name}";
					}

					queue = @case.Queue;
					if (@case.Parameters.ContainsKey(DomainModel.Case.YFlowPendingCaseMessageParameter))
						@case.Parameters.Remove(DomainModel.Case.YFlowPendingCaseMessageParameter);

					if (options.StoreInDatabase)
						CaseDAO.Close(@case, options.ClosedBy, options.Person, options.ExternalIntegration, now);
					@case.Status = DomainModel.CaseStatuses.Closed;
					@case.ClosedBy = options.ClosedBy;
					@case.ClosedDate = now;
					@case.LastUpdatedOn = now;
					@case.LastPerson = options.Person ?? @case.LastPerson;

					/*Si se cierra el caso, no saco las marcas de pendientes
					if (@case.PendingPerson != null)
					{
						Core.System.Instance.CasesService.RemovePendingCase(@case, @case.PendingPerson);
						@case.PendingPerson = null;
					}
					*/

					if (@case.PendingReplyFromCustomerAt != null)
					{
						if (@case.Parameters.ContainsKey(DomainModel.Case.MarkedPendingResponseFromCustomerByParameter))
							@case.Parameters.Remove(DomainModel.Case.MarkedPendingResponseFromCustomerByParameter);
						@case.Parameters[DomainModel.Case.YFlowPendingResponseTypeParameter] = string.Empty;
						@case.PendingReplyFromCustomerAt = null;
						DAL.CaseDAO.UpdatePendingReplyFromCustomer(@case, null);
						await Core.System.Instance.CasesService.RemovePendingReplyCase(@case);
					}

					if (@case.PendingMessages)
					{
						@case.PendingMessages = false;
#if !NETCOREAPP
						Core.System.Instance.CasesService.RemoveYFlowPendingCase(@case);
#endif
					}

					var tempCase = DAL.CaseDAO.GetOneWithoutMessages(@case.ID, false);
					@case.WorkingTime = tempCase.WorkingTime;
					@case.LastUpdatedOn = tempCase.LastUpdatedOn;
					@case.QueueTime = tempCase.QueueTime;

					@case.Close();

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow)
					{
#if NETCOREAPP
						await this.NotifyClosedCasesYFlow(@case.ID);
#else
						global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async ct =>
						{
							await this.NotifyClosedCasesYFlow(@case.ID);
						});
#endif
					}
				}

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;
				DomainModel.Historical.DailyCase infoCase;
				if (options.Interval.IsNull)
					options.Interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				var interval = options.Interval;
				var person = options.Person;
				bool metricsChanged = false;

				switch (options.ClosedBy)
				{
					case CaseClosingResponsibles.System:
					case CaseClosingResponsibles.Filter:
					case CaseClosingResponsibles.ServiceLevel:
					case CaseClosingResponsibles.YFlow:
						if (queue != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.PersonID = 0;
							info.QueueID = queue.ID;
							info.ClosedCases = 1;
							info.AutoClosedCases = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.PersonID = 0;
						info.QueueID = 0;
						info.ClosedCases = 1;
						info.AutoClosedCases = 1;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						break;

					case CaseClosingResponsibles.Agent:
						if (queue != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.PersonID = person.ID;
							info.QueueID = queue.ID;
							info.ClosedCases = 1;
							info.ClosedCasesByAgents = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.PersonID = 0;
							info.QueueID = queue.ID;
							info.ClosedCases = 1;
							info.ClosedCasesByAgents = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							if (@case != null && @case.LastService != null)
							{
								infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
								infoService.PersonID = 0;
								infoService.QueueID = queue.ID;
								infoService.ClosedCases = 1;
								infoService.ClosedCasesOnlyWithYFlow = @case.ContainsOnlyYFlowReplies ? 1 : 0;
								infoService.CasesInvokedYflow = @case.RepliesByYFlow > 0 ? 1 : 0;
								infoService.ServiceID = @case.LastService.ID;
								Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
							}
						}

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = person.ID;
						info.ClosedCases = 1;
						info.ClosedCasesByAgents = 1;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						break;

					case CaseClosingResponsibles.Supervisor:
						if (queue != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.PersonID = person.ID;
							info.QueueID = queue.ID;
							info.ClosedCases = 1;
							info.ClosedCasesByUsers = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.PersonID = 0;
							info.QueueID = queue.ID;
							info.ClosedCases = 1;
							info.ClosedCasesByUsers = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.PersonID = person.ID;
						info.QueueID = 0;
						info.ClosedCases = 1;
						info.ClosedCasesByUsers = 1;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);
						break;
					default:
						break;
				}

				try
				{
					bool mustConsolidateBotTime = (options.ClosedBy == CaseClosingResponsibles.YFlow) || (options.ClosedBy == CaseClosingResponsibles.System && @case.Agents == 0 && @case.Queue == null && @case.RepliesByYFlow > 0);

					if (mustConsolidateBotTime)
					{
						if (@case.Parameters.ContainsKey(DomainModel.Case.LastInvokeYflowDateParameter) &&
							DateTime.TryParse(@case.Parameters[DomainModel.Case.LastInvokeYflowDateParameter], out DateTime paramDate))
						{
							@case.Parameters.Remove(DomainModel.Case.LastInvokeYflowDateParameter);
							MetricsAdapter.UpdateBotTimeParameter(@case.Parameters, paramDate, DateTime.Now);
							CaseDAO.UpdateParameters(@case);
						}
					}
				}

				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}

				infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
				infoCase.ClosedCases = 1;
				infoCase.ClosedCasesBySL = (options.ClosedBy == CaseClosingResponsibles.ServiceLevel) ? 1 : 0;
				infoCase.ClosedCasesByYFlow = (options.ClosedBy == CaseClosingResponsibles.YFlow) ? 1 : 0;
				infoCase.AutoClosedCasesAfterYFlow = (options.ClosedBy == CaseClosingResponsibles.System && @case.ContainsOnlyYFlowReplies) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

				if (@case != null && @case.LastService != null)
				{
					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.PersonID = 0;
					infoService.QueueID = 0;
					infoService.ClosedCases = 1;
					infoService.ClosedCasesOnlyWithYFlow = @case.ContainsOnlyYFlowReplies ? 1 : 0;
					infoService.CasesInvokedYflow = @case.RepliesByYFlow > 0 ? 1 : 0;
					infoService.ServiceID = @case.LastService.ID;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				Core.System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.CaseClosed, new Services.ServerIntegrationsService.ExecuteParameters()
				{
					Message = options.Message,
					Case = @case,
					Agent = (person != null && person.Type == PersonTypes.Agent) ? (DomainModel.Agent) person : null,
					Supervisor = (person != null && person.Type == PersonTypes.User) ? (DomainModel.User) person : null
				});

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
				{
					if (@case != null)
					{
						DomainModel.CaseLogTypes caseLogType;
						switch (options.ClosedBy)
						{
							case CaseClosingResponsibles.Filter:
								caseLogType = CaseLogTypes.FilterClose;
								break;
							case CaseClosingResponsibles.Agent:
								caseLogType = CaseLogTypes.AgentClose;
								break;
							case CaseClosingResponsibles.Supervisor:
								caseLogType = CaseLogTypes.SupervisorClose;
								break;
							case CaseClosingResponsibles.ServiceLevel:
								caseLogType = CaseLogTypes.ServiceLevelClose;
								break;
							case CaseClosingResponsibles.YFlow:
								caseLogType = CaseLogTypes.YFlowClose;
								break;
							case CaseClosingResponsibles.ExternalIntegration:
								caseLogType = CaseLogTypes.ExternalIntegrationClose;
								break;
							case CaseClosingResponsibles.System:
							default:
								caseLogType = CaseLogTypes.SystemClose;
								break;
						}

						Core.System.Instance.AutomaticExportService.StoreCaseNews(@case, caseLogType);
					}
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
				{
#if !NETCOREAPP
					try
					{
						Core.System.Instance.GatewayService.Gateway.HandleClosedCase(@case);
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("No se pudo notificar al gateway del caso {0} que fue cerrado: {1}", @case, ex);
					}
#endif
				}
				else
				{
#if !NETCOREAPP
					if (queue != null)
					{
						var systemQueue = Core.System.Instance.QueueService.GetQueueById(queue.ID);
						systemQueue.RealTimeInfo.ClosedCases++;
					}

					if (options.ClosedBy == CaseClosingResponsibles.Agent &&
						options.Person != null &&
						options.Person.Type == PersonTypes.Agent)
					{
						var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) options.Person);
						if (connectionInfo != null)
							connectionInfo.CasesStats.ClosedCases++;
					}
#endif
				}

				DomainModel.Service lastService = null;
				long? caseId = null;
				var message = options.Message;
				if (message != null)
				{
					lastService = message.Service;
					caseId = message.Case.ID;
				}
				else if (@case != null && @case.LastService != null)
				{
					lastService = @case.LastService;
					caseId = @case.ID;
				}

				if (lastService != null)
				{
					this.EnsureServiceInstance(lastService);
				}

				try
				{
					if (lastService != null)
					{
#if !NETCOREAPP
						if (options.InvokeServiceOperations)
						{
							var socialServiceOperations = lastService.SocialService as DomainModel.ISocialServiceOperations;
							if (socialServiceOperations != null)
							{
								if (socialServiceOperations.SupportsCaseClosed)
								{
									global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async ct =>
									{
										Tracer.TraceVerb("Se esperará 5 segundos para notificar al servicio que se cerró el caso: {0}", @case);
										await Task.Delay(5000);
										try
										{
											Tracer.TraceVerb("Se notificará al servicio que se cerró el caso: {0}", @case);
											await socialServiceOperations.CaseClosed(@case);
										}
										catch (Exception ex)
										{
											Tracer.TraceError("No se pudo notificar al servicio que se cerró el caso {0}: {1}", @case, ex);
										}
									});
								}
							}
						}
#endif

						if (lastService.Type == ServiceTypes.WhatsApp)
						{
							var configuration = lastService.ServiceConfiguration as SocialServices.WhatsApp.WhatsAppServiceConfiguration;
							if (configuration != null &&
								configuration.ServiceIntegrationType == WhatsappSettings.IntegrationTypes.Postback &&
								configuration.IntegrationType3NotifyClosedCases &&
								configuration.IntegrationType3CloseCaseEndpoint != null)
							{
								var serviceInstance = lastService.SocialService as SocialServices.WhatsApp.WhatsAppService;
								if (serviceInstance != null)
								{
									var closeCaseEndpointParameters = new Dictionary<string, string>();
									closeCaseEndpointParameters["@@NUMERO_TELEFONO@@"] = configuration.FullPhoneNumber.ToString();
									closeCaseEndpointParameters["@@SERVICIO[CODIGO]@@"] = lastService.ID.ToString();
									closeCaseEndpointParameters["@@CASO[CODIGO]@@"] = caseId.Value.ToString();
									closeCaseEndpointParameters["@@CASO[CERRADO_POR]@@"] = ((short) options.ClosedBy).ToString();
									closeCaseEndpointParameters["@@USUARIO[TELEFONO]@@"] = string.Empty;
									if (@case != null && @case.Profile != null && @case.Profile.RetrievedFromDatabase)
									{
										closeCaseEndpointParameters["@@USUARIO[TELEFONO]@@"] = @case.Profile.PrimaryPhoneNumber.ToString();
									}
									else if (message != null && message.SocialUser != null)
									{
										closeCaseEndpointParameters["@@USUARIO[TELEFONO]@@"] = message.SocialUser.ID.ToString();
									}
									else if (@case != null && @case.LastIncomingMessageID != null)
									{
										var lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new MessageDAO.RelatedEntitiesToRead(false)
										{
											PostedBy = true
										}, false);
										if (lastIncomingMessage != null && lastIncomingMessage.PostedBy != null)
										{
											closeCaseEndpointParameters["@@USUARIO[TELEFONO]@@"] = lastIncomingMessage.PostedBy.ID.ToString();
										}
									}

#if NETCOREAPP
									await serviceInstance.NotifyClosedCase(closeCaseEndpointParameters);
									Tracer.TraceInfo("Se notificó correctamente al servicio de Whatsapp {0} el cierre del caso {1}", lastService.Name, caseId.Value);
#else
									global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async (ct) =>
									{
										await serviceInstance.NotifyClosedCase(closeCaseEndpointParameters);
										Tracer.TraceInfo("Se notificó correctamente al servicio de Whatsapp {0} el cierre del caso {1}", lastService.Name, caseId.Value);
									});
#endif
								}
								else
								{
									Tracer.TraceVerb("No se puede notificar al servicio de Whatsapp {0} del cierre del caso", lastService.Name);
								}
							}
						}

#if !NETCOREAPP
						var systemService = Core.System.Instance.ServicesServices.GetServiceById(lastService.ID);
						if (systemService != null)
							systemService.RealTimeInfo.ClosedCases++;
#endif
					}
				}
				catch { }

				if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
					DomainModel.SystemSettings.Instance.EnableSurveys)
				{
					await Core.System.Instance.SurveysService.ProcessClosedCase(@case, options.ClosedBy == CaseClosingResponsibles.System ||
						options.ClosedBy == CaseClosingResponsibles.ExternalIntegration);

					if (!@case.SurveyShouldSend &&
						!options.InvokeServiceOperations &&
						lastService.SocialService.SupportsCaseSurveyIgnored)
					{
						await lastService.SocialService.CaseSurveyIgnored(@case);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se cerraba el caso {0}: {1}", @case, ex);
				throw;
			}
		}

		/// <summary>
		/// Responde un mensaje
		/// </summary>
		/// <param name="messageId">El código de mensaje a responde</param>
		/// <returns>El <see cref="DomainModel.Message"/> que se está enviando</returns>
		internal async Task<DomainModel.Message> Reply(long messageId,
			DomainModel.Case @case,
			bool sendInBackground = true,
			DomainModel.Message repliedMessage = null,
			DomainModel.SocialUser socialUser = null,
			DomainModel.Person person = null,
			bool readAssociatedMessage = true,
			bool readAttachments = true,
			int? applyDelayAfterSend = null)
		{
			var relatedEntitiesToRead = new MessageDAO.RelatedEntitiesToRead(false)
			{
				RepliesTo = repliedMessage == null,
				AssociatedMessage = readAssociatedMessage,
				Service = true,
				Attachments = readAttachments,
				RepliedBy = person == null,
				RepliesToSocialUser = repliedMessage == null && socialUser == null
			};

			var message = DAL.MessageDAO.GetOne(messageId, relatedEntitiesToRead, true);

			if (repliedMessage != null)
			{
				message.RepliesTo = repliedMessage;
				if (message.RepliesToSocialUser == null && repliedMessage.PostedBy != null)
				{
					message.RepliesToSocialUser = repliedMessage.PostedBy;
				}
				else if (message.RepliesToSocialUser.DisplayName == null)
				{
					socialUser = DAL.SocialUserDAO.GetOne(message.RepliesToSocialUser.ID, message.RepliesToSocialUser.SocialServiceType);
				}
			}

			if (socialUser != null)
			{
				message.RepliesToSocialUser = socialUser;
			}

			if (person != null)
			{
				message.RepliedBy = person;
			}

			if (applyDelayAfterSend != null &&
				applyDelayAfterSend > 0 &&
				applyDelayAfterSend <= 2000)
				message.Parameters["DelayAfterSend"] = applyDelayAfterSend.Value.ToString();

			if (message.Case == null || @case != null)
				message.Case = @case;

			if (message.Case != null)
				message.Case.AddReply(message);

			if (message.RequiresAuthorization == true &&
				(message.Authorized == null || message.Authorized == false))
			{
				Tracer.TraceInfo("El mensaje {0} no se puede enviar directamente porque requiere autorización", message);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					Core.System.Instance.AutomaticExportService.StoreMessage(message);
				}
			}
			else
			{
				if (sendInBackground)
				{
#if NETCOREAPP
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
					Reply(message);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
#else
					global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async (ct) => await Reply(message));
#endif
				}
				else
				{
					await Reply(message);
				}
			}


			return message;
		}

		/// <summary>
		/// Responde un mensaje
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> que se enviará</param>
		internal async Task Reply(DomainModel.Message message)
		{
			if (message == null)
				return;

			Tracer.TraceInfo("Se enviará el mensaje {0} a través del servicio {1}", message, message.Service);

			try
			{
				var service = message.Service;

				this.EnsureServiceInstance(service);

				if (service.ServiceConfiguration != null && service.SocialService != null)
				{
					int relatedSocialServiceId;
					if (service.SocialService.RequiresRelatedSocialService(out relatedSocialServiceId) && !service.SocialService.HasRelatedSocialService)
					{
						var relatedService = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(relatedSocialServiceId);
						if (relatedService != null)
						{
							this.EnsureServiceInstance(relatedService);
							if (relatedService.SocialService != null &&
								relatedService.SocialService.Initialized)
							{
								var connector = new SocialServiceConnector(relatedService.SocialService);
								service.SocialService.SetRelatedSocialService(connector);
							}
							else
							{
								return;
							}
						}
					}

					Tracer.TraceInfo("Enviando el mensaje {0} - {1}", message.ID, message);
					message.SocialMessageID = await service.SocialService.Reply(message);

					if (service.SocialService.ShouldPersistSocialMessageIDInStorage())
					{
						DomainModel.StorageManager.Instance.SaveOutgoingMessage(message);
					}

					Tracer.TraceInfo("Actualizando el mensaje {0} con el código de mensaje de la red social {1}", message.ID, message.SocialMessageID);
					DAL.MessageDAO.UpdateSocialMessage(message.ID, message.SocialMessageID, message.Parameters);

					message.Delivered = true;
					message.DeliveryError = null;
					message.DeliveryErrorNumber = null;
					message.DeliveryShouldRetry = null;

					try
					{
						DateTime now = DateTime.Now;

						if (service.Status == null)
						{
							var status = DAL.ServiceDAO.GetStatus<Social.Business.SocialServiceStatus>(service.ID);
							if (status == null)
								status = new SocialServiceStatus();
							service.Status = status.GetStatus();
						}

						if (service.ServiceStatus == null)
						{
							service.ServiceStatus = new SocialServiceStatus(service.Status);
						}

						service.ServiceStatus[DomainModel.Service.LastReplyMessageDateStatus] = now.ToString("o");
						service.ServiceStatus[DomainModel.Service.LastReplyMessageStatus] = message.ID.ToString();

						if (service.ShouldRefreshStatus())
						{
							ServiceDAO.UpdateStatus(service, service.ServiceStatus);

							var status = DAL.ServiceDAO.GetStatus<Social.Business.SocialServiceStatus>(service.ID);
							if (status != null)
							{
								service.Status = status.GetStatus();
								service.ServiceStatus = status;
							}
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError("No se pudo actualizar el estado del servicio {0}: {1}", service, ex);
					}
				}

				if (message.Case != null &&
					message.Case.AlreadyReplyOnAutoClose)
					DAL.CaseDAO.UpdateMarkReplyOnCloseCase(false, message.Case.ID);
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}", message.ID, message.Service.Name, ex);

				if (ex.GetType() == typeof(Business.Exceptions.ReplyException))
				{
					var rex = ex as Business.Exceptions.ReplyException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, rex.ErrorMessage, rex.ErrorCode, rex.ShouldRetry);
					message.Delivered = false;
					message.DeliveryError = rex.ErrorMessage;
					message.DeliveryErrorNumber = rex.ErrorCode;
					message.DeliveryShouldRetry = rex.ShouldRetry;
				}
				else if (ex.GetType() == typeof(Business.Exceptions.ServiceException))
				{
					var sex = ex as Business.Exceptions.ServiceException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, sex.ErrorMessage, sex.ErrorCode, false);
					message.Delivered = false;
					message.DeliveryError = sex.ErrorMessage;
					message.DeliveryErrorNumber = sex.ErrorCode;
					message.DeliveryShouldRetry = false;
				}
				else
				{
					DAL.MessageDAO.UpdateDelivered(message.ID, false, ex.Message, true);
					message.Delivered = false;
					message.DeliveryError = ex.Message;
					message.DeliveryErrorNumber = null;
					message.DeliveryShouldRetry = true;
				}
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
				DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
			{
				Core.System.Instance.AutomaticExportService.StoreMessage(message);
			}

			if (message.Delivered == false &&
				message.DeliveryShouldRetry == true)
			{
				await this.PublishReply(message);
			}
		}

		/// <summary>
		/// Descarta un mensaje
		/// </summary>
		/// <param name="options">Un valor de <see cref="ActionOptions.DiscardOptions"/> con la información para descartar</param>
		public async Task DiscardAsync(ActionOptions.DiscardOptions options)
		{
			if (options == null)
				throw new ArgumentNullException(nameof(options), "Las opciones no pueden ser null");

			if (options.Message == null)
				throw new ArgumentNullException(nameof(options.Message), "El mensaje no puede ser null");

			var message = options.Message;
			if (message.Status == MessageStatuses.Discarded)
				return;

			var person = options.Person;
			if (person != null)
			{
				if (person.Type == DomainModel.PersonTypes.Agent)
				{
					if (message.Status != DomainModel.MessageStatuses.Assigned)
						throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser descartado por el agente", message.Status), nameof(message));

					if (message.AssignedTo.ID != person.ID)
						throw new ArgumentException("El mensaje tiene que ser descartado por el agente al que fue asignado", nameof(person));
				}
				else
				{
					if (message.Status != DomainModel.MessageStatuses.NotAssigned)
						throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser descartado por el supervisor", message.Status), nameof(message));
				}
			}

			if (person == null)
				Tracer.TraceInfo("El sistema está descartando el mensaje {0}", message);
			else if (person.Type == DomainModel.PersonTypes.Agent)
				Tracer.TraceInfo("El agente {0} está descartando el mensaje {1}", person, message);
			else
				Tracer.TraceInfo("El usuario {0} está descartando el mensaje {1}", person, message);

			try
			{
				DateTime now = DateTime.Now;

				if (options.Parameters != null && options.Parameters.ContainsKey(DomainModel.Message.RelatedMessagesParameter))
					message.Parameters[DomainModel.Message.RelatedMessagesParameter] = options.Parameters[DomainModel.Message.RelatedMessagesParameter];

				MessageDAO.Discard(message, person, options.Reason, options.AddToBlackList, options.CloseCase, options.DiscardSource);
				message.Status = DomainModel.MessageStatuses.Discarded;
				message.FinishedDate = now;
				message.DiscardSource = options.DiscardSource;
				message.DiscardReason = options.Reason;

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedDate = now;
					if (person != null)
						lastSegment.Person = person;
				}

#if !NETCOREAPP
				if (options.RemoveFromQueue)
				{
					if (options.SystemQueue == null && message.Queue != null)
					{
						options.SystemQueue = Core.System.Instance.QueueService.GetQueueById(message.Queue.ID);
					}

					if (options.SystemQueue != null)
					{
						options.SystemQueue.Discard(message, person, options.ShouldComputeDiscardMessageAsChatMode);
					}
				}
#endif

				if (message.Case != null && person != null)
				{
					message.Case.LastPerson = person;
				}

#if !NETCOREAPP
				if (options.CloseCase)
				{
					Core.System.Instance.CasesService.RemovePendingCase(message.Case, person);
				}

				if (message.IsGrouping && message.Service.Settings != null && message.Service.Settings.Grouping != null)
				{
					Tracer.TraceVerb("Se procesarán los mensajes agrupados del mensaje: {0}", message);
					global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(ct => ProcessGroupedMessages(message, message.Service.Settings.Grouping, null, null, options.GroupedMessageReplies));
				}

				if (message.SocialServiceType == SocialServiceTypes.Chat)
				{
					/*
					 * La única forma de descartar un chat en cuando está en cola. Se deberá informar a NodeJs que se descartó el chat
					 */
					await Core.System.Instance.ChatsService.Discard(message);
				}
#endif

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

				if (person != null)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = person.ID;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						info.DiscardedMessages = 1;
						info.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						info.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					if (person.Type == PersonTypes.Agent)
					{
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = person.ID;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						info.DiscardedMessages = 1;
						info.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						info.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					if (person.Type == PersonTypes.Agent)
					{
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = 0;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						info.DiscardedMessages = 1;
						info.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						info.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					if (person.Type == PersonTypes.Agent)
					{
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = person.ID;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						infoService.DiscardedMessages = 1;
						infoService.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						infoService.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = person.ID;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						infoService.DiscardedMessages = 1;
						infoService.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						infoService.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = 0;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						infoService.DiscardedMessages = 1;
						infoService.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						infoService.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						infoService.DiscardedMessages = 1;
						infoService.DiscardedMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
						infoService.DiscardedMessagesByAgents = (person.Type == PersonTypes.Agent) ? 1 : 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}
				else
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					if (message.Queue != null)
					{
						info.QueueID = message.Queue.ID;
						info.PersonID = 0;
						if (!options.ShouldComputeDiscardMessageAsChatMode)
						{
							info.DiscardedMessages = 1;
							info.SystemDiscardedMessages = 1;
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = message.Service.ID;
						infoService.QueueID = message.Queue.ID;
						infoService.PersonID = 0;
						if (!options.ShouldComputeDiscardMessageAsChatMode)
						{
							infoService.DiscardedMessages = 1;
							infoService.SystemDiscardedMessages = 1;
						}
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
					}

					info.QueueID = 0;
					info.PersonID = 0;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						info.DiscardedMessages = 1;
						info.SystemDiscardedMessages = 1;
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					if (!options.ShouldComputeDiscardMessageAsChatMode)
					{
						infoService.DiscardedMessages = 1;
						infoService.SystemDiscardedMessages = 1;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				try
				{
					if (message.Case?.Parameters != null && lastSegment.EnqueuedDate.HasValue && lastSegment.FinishedDate.HasValue)
					{
						MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, lastSegment.FinishedDate.Value, lastSegment.EnqueuedDate.Value);

						message.ComputeTimes();
						if (message.SystemTime.HasValue)
						{
							MetricsAdapter.CalculateTotalSystemTime(message.Case?.Parameters, message.SystemTime.Value);
						}

						if (message.WorkingTime.HasValue)
						{
							MetricsAdapter.CalculateTotalWorkingTimeParameter(message.Case?.Parameters, message.WorkingTime.Value);
						}

						CaseDAO.UpdateParameters(message.Case);
					}
				}

				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}

				if (options.CloseCase)
				{
					CaseClosingResponsibles caseClosedBy;
					switch (options.DiscardSource)
					{
						case DiscardSources.Filter:
							caseClosedBy = CaseClosingResponsibles.Filter;
							break;
						case DiscardSources.Agent:
							caseClosedBy = CaseClosingResponsibles.Agent;
							break;
						case DiscardSources.Supervisor:
							caseClosedBy = CaseClosingResponsibles.Supervisor;
							break;
						case DiscardSources.ServiceLevel:
							caseClosedBy = CaseClosingResponsibles.ServiceLevel;
							break;
						case DiscardSources.YFlow:
							caseClosedBy = CaseClosingResponsibles.YFlow;
							break;
						case DiscardSources.Grouping:
						case DiscardSources.DeletedInSource:
						default:
							caseClosedBy = CaseClosingResponsibles.System;
							break;
					}

					await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
					{
						Message = message,
						Case = message.Case,
						ClosedBy = caseClosedBy,
						Person = person,
						Interval = interval,
						StoreInDatabase = false,
						Queue = null
					});
				}
				else
				{
					if (options.DiscardSource != DiscardSources.Grouping)
					{
						await Core.System.Instance.CasesService.RegisterCaseToBeClosed(options.Message.Case, options.Message.Service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);
					}
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(message);
				}

				if (options.CurrentlyEnqueuedMessageOfCase != null && options.CurrentlyEnqueuedMessageOfCase.Case != null)
				{
					options.CurrentlyEnqueuedMessageOfCase.Case.Discards++;
					var discardedMessageOfCase = options.CurrentlyEnqueuedMessageOfCase.Case.Messages.FirstOrDefault(m => m.ID == options.Message.ID);
					if (discardedMessageOfCase != null)
					{
						discardedMessageOfCase.Status = MessageStatuses.Discarded;
						try
						{
							if (discardedMessageOfCase.IsGrouping && discardedMessageOfCase.Groups != null)
							{
								foreach (var discardedGroupedMessage in discardedMessageOfCase.Groups)
								{
									var discardedGroupedMessageOfCase = options.CurrentlyEnqueuedMessageOfCase.Case.Messages.FirstOrDefault(m => m.ID == discardedGroupedMessage.ID);
									if (discardedGroupedMessageOfCase != null)
										discardedGroupedMessageOfCase.Status = MessageStatuses.Discarded;
								}
							}
						}
						catch { }
					}
				}

				if (person == null)
					Tracer.TraceInfo("El sistema descartó el mensaje {0}", message);
				else if (person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} descartó el mensaje {1}", person, message);
				else
					Tracer.TraceInfo("El usuario {0} descartó el mensaje {1}", person, message);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se descartaba el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Responde un mensaje
		/// </summary>
		/// <param name="options">Un <see cref="ActionOptions.ReplyOptions"/> con los parámetros para realizar la respuesta</param>
		public async Task ReplyAsync(ActionOptions.ReplyOptions options)
		{
			#region Validaciones

			if (options == null)
				throw new ArgumentNullException(nameof(options), "Las opciones no puede ser nulo");

			if (options.Message == null)
				throw new ArgumentNullException(nameof(options.Message), "El código de mensaje no existe");

			if (options.Person == null && !options.IsPendingReplyFromCustomerReply)
				throw new ArgumentNullException(nameof(options.Person), "La persona no puede ser null");

			if (options.Person != null)
			{
				if (options.Person.Type == DomainModel.PersonTypes.Agent)
				{
					var agent = (DomainModel.Agent) options.Person;
					if (options.Message.Status != DomainModel.MessageStatuses.Assigned && options.Message.Status != DomainModel.MessageStatuses.Grouped)
					{
						if (!options.IsMyOutgoingCases && !options.IsPendingReplyFromCustomerReply)
							throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser respondido", options.Message.Status), "options.Message");
					}

					if (options.Message.Status == DomainModel.MessageStatuses.Assigned && options.Message.AssignedTo != options.Person)
						throw new ArgumentException("El mensaje tiene que ser respondido por el agente al que fue asignado", "options.Person");

					if (options.MarkCaseAsPending && !agent.AllowedToMarkAsPending)
					{
						throw new ArgumentException("El agente no tiene permitido marcar casos como pendientes", "options.Person");
					}
				}
			}

			if (string.IsNullOrWhiteSpace(options.Text))
				options.Text = null;

			if (string.IsNullOrWhiteSpace(options.AssociatedText))
				options.AssociatedText = null;

			var isHSM = false;

			if (options.Message.SocialServiceType == DomainModel.SocialServiceTypes.Twitter)
			{
				if (options.Message.IsDirectMessage)
				{
					if (string.IsNullOrEmpty(options.Text))
					{
						if (string.IsNullOrEmpty(options.AssociatedText))
							throw new ArgumentException("Debe especificar un texto para la mensaje público asociado", "options.AssociatedText");

						if (options.AssociatedText.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
							throw new ArgumentOutOfRangeException(nameof(options.AssociatedText), string.Format("El texto del mensaje asociado público no puede superar los {1} caracteres: {0}", options.AssociatedText, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
					}
					else
					{
						if (!string.IsNullOrEmpty(options.AssociatedText) && options.AssociatedText.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
							throw new ArgumentOutOfRangeException(nameof(options.AssociatedText), string.Format("El texto del mensaje público asociado no puede superar los {1} caracteres: {0}", options.AssociatedText, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
					}
				}
				else
				{
					if (options.Text.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
						throw new ArgumentOutOfRangeException(nameof(options.Text), string.Format("El texto del mensaje público no puede superar los {1} caracteres: {0}", options.Text, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
				}
			}
			else if (options.Message.SocialServiceType == SocialServiceTypes.WhatsApp)
			{
				if (options.Parameters != null && options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
					Convert.ToBoolean(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter]))
					isHSM = true;

				if (!isHSM)
				{
					if (string.IsNullOrEmpty(options.Text))
						throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(options.Text));
				}

				if (isHSM)
				{
					if (!options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
					{
						var settings = options.Message.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;

						if (!settings.AllowToSendHSM)
							throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM");

						if (options.Person != null && options.Person.Type == PersonTypes.Agent && !settings.AllowAgentsToSendHSM)
							throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM por parte de los agentes");

						var template = settings.HSMTemplates.FirstOrDefault(t =>
							t.Namespace.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter]) &&
							t.ElementName.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter]) &&
							t.Language.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage]));
						if (template != null)
						{
							DomainModel.Whatsapp.HSMTemplateSendDefinition sendDefinition = null;
							sendDefinition = new DomainModel.Whatsapp.HSMTemplateSendDefinition();

							var parameters = new List<DomainModel.Whatsapp.HSMTemplateParameter>();

							options.Text = template.Template;
							if (template.Parameters != null && template.Parameters.Length > 0)
							{
								var parameterValues = Newtonsoft.Json.Linq.JArray.Parse(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
								for (var i = 0; i < template.Parameters.Length; i++)
								{
									var parameterName = template.Parameters[i].Substring(0, template.Parameters[i].IndexOf('='));
									options.Text = options.Text.Replace("{{" + parameterName + "}}", parameterValues[i].ToString());

									parameters.Add(new DomainModel.Whatsapp.HSMTemplateParameter()
									{
										Name = parameterName,
										Value = parameterValues[i].ToString()
									});
								}

								sendDefinition.Parameters = parameters.ToArray();
							}
							else
							{
								if (options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
								{
									options.Parameters.Remove(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter);
								}
							}

							if (options.Parameters.ContainsKey("TemplateParameters"))
							{
								var jHSM = Newtonsoft.Json.Linq.JObject.Parse(options.Parameters["TemplateParameters"]);

								sendDefinition.Namespace = options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
								sendDefinition.ElementName = options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
								sendDefinition.Language = options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

								var jHeader = jHSM["header"];
								sendDefinition.HeaderType = template.HeaderType;
								switch (template.HeaderType)
								{
									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text:
										if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException("HSM Template header missing");
										}

										if (jHeader["type"] == null ||
											jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
											!jHeader["type"].ToString().Equals("text"))
										{
											throw new InvalidOperationException("HSM Template header's type must be 'text'");
										}

										if (template.HeaderTextParameter != null)
										{
											var jText = jHeader["text"];
											if (jText == null || jText.Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												throw new InvalidOperationException("HSM Template header is missing object 'text'");
											}

											var jParameter = jText["parameter"];
											if (jParameter == null || jParameter.Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												throw new InvalidOperationException("HSM Template header.text is missing object 'parameter'");
											}

											if (jParameter["name"] == null ||
												jParameter["name"].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												throw new InvalidOperationException("HSM Template header.text.parameter.name is missing");
											}

											if (jParameter["value"] == null || jParameter["value"].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												throw new InvalidOperationException("HSM Template header.text.parameter.name is missing");
											}

											var parameterName = jParameter["name"].ToString();
											var parameterValue = jParameter["value"].ToString();

											if (!template.HeaderTextParameter.Name.Equals(parameterName))
											{
												throw new InvalidOperationException("HSM Template header.text.parameter.name is not the expected");
											}

											if (string.IsNullOrEmpty(parameterValue))
											{
												throw new InvalidOperationException("HSM Template header.text.parameter.value must be a non empty string");
											}

											sendDefinition.HeaderTextParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
											{
												Name = parameterName,
												Value = parameterValue
											};
										}
										break;

									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media:
										if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException("HSM Template header missing");
										}

										if (jHeader["type"] == null ||
											jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
											!jHeader["type"].ToString().Equals("media"))
										{
											throw new InvalidOperationException("HSM Template header.type must be 'media'");
										}

										var jMedia = jHeader["media"];
										if (jMedia == null || jMedia.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException("HSM Template header.media is missing");
										}

										if (jMedia["type"] == null ||
											jMedia["type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
										{
											throw new InvalidOperationException("HSM Template header.media.type is missing");
										}

										var mediaType = jMedia["type"].ToString();

										string mediaPropertyName = null;
										sendDefinition.HeaderMediaType = template.HeaderMediaType;
										switch (template.HeaderMediaType)
										{
											case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
												mediaPropertyName = "document";
												break;
											case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
												mediaPropertyName = "image";
												break;
											case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
												mediaPropertyName = "video";
												break;
											default:
												break;
										}

										if (mediaPropertyName != null)
										{
											if (!mediaType.Equals(mediaPropertyName))
											{
												throw new InvalidOperationException(string.Format("HSM Template header.media.type must be '{0}'", mediaPropertyName));
											}

											if (jMedia[mediaPropertyName] == null || jMedia[mediaPropertyName].Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												throw new InvalidOperationException(string.Format("HSM Template header.media.{0} is missing", mediaPropertyName));
											}

											var jMediaObject = (Newtonsoft.Json.Linq.JObject) jMedia[mediaPropertyName];

											if (jMediaObject["filename"] == null || jMediaObject["filename"].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.filename is missing", mediaPropertyName));
											}

											var filename = jMediaObject["filename"].ToString();
											if (string.IsNullOrEmpty(filename))
											{
												throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.filename must be a non empty string", mediaPropertyName));
											}

											if (jMediaObject["url"] != null && jMediaObject["url"].Type == Newtonsoft.Json.Linq.JTokenType.String)
											{
												sendDefinition.HeaderMediaUrl = jMediaObject["url"].ToString();
												if (string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl))
												{
													throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.url must be a valid url", mediaPropertyName));
												}

												if (!Uri.TryCreate(sendDefinition.HeaderMediaUrl, UriKind.Absolute, out Uri headerMediaUrl))
												{
													throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.url must be a valid url", mediaPropertyName));
												}

												sendDefinition.HeaderMediaUrlIsPublic = true;
												if (jMediaObject["isPublic"] != null && jMediaObject["isPublic"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
												{
													sendDefinition.HeaderMediaUrlIsPublic = jMediaObject["isPublic"].ToObject<bool>();
												}

												if (sendDefinition.HeaderMediaUrlIsPublic.Value)
												{
													if (!headerMediaUrl.Scheme.Equals("https"))
													{
														throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.url must be secure for public urls", mediaPropertyName));
													}
												}
												else
												{
													if (!headerMediaUrl.Scheme.Equals("https") && !headerMediaUrl.Scheme.Equals("http"))
													{
														throw new InvalidOperationException(string.Format("HSM Template header.media.{0}.url must be http or https for urls", mediaPropertyName));
													}
												}
											}
											else if (jMediaObject["media"] != null && jMediaObject["media"].Type == Newtonsoft.Json.Linq.JTokenType.String)
											{

											}
										}

										break;
									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location:
										if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException("HSM Template header missing");
										}

										if (jHeader["type"] == null ||
											jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
											!jHeader["type"].ToString().Equals("location"))
										{
											throw new InvalidOperationException("HSM Template header.type must be 'location'");
										}

										var jLocation = jHeader["location"];
										if (jLocation == null || jLocation.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException("HSM Template header.location is missing");
										}

										if (jLocation["latitude"] == null ||
											(
											jLocation["latitude"].Type != Newtonsoft.Json.Linq.JTokenType.Float &&
											jLocation["latitude"].Type != Newtonsoft.Json.Linq.JTokenType.Integer
											))
										{
											throw new InvalidOperationException("HSM Template header.location.latitude is missing");
										}

										if (jLocation["longitude"] == null ||
											(
											jLocation["longitude"].Type != Newtonsoft.Json.Linq.JTokenType.Float &&
											jLocation["longitude"].Type != Newtonsoft.Json.Linq.JTokenType.Integer
											))
										{
											throw new InvalidOperationException("HSM Template header.location.longitude is missing");
										}

										var latitude = jLocation["latitude"].ToObject<double>();
										var longitude = jLocation["longitude"].ToObject<double>();

										if (latitude < -90 || latitude > 90)
										{
											throw new InvalidOperationException("HSM Template header.location.latitude must be between -90 and 90");
										}

										if (longitude < -180 || longitude > 180)
										{
											throw new InvalidOperationException("HSM Template header.location.longitude must be between -180 and 180");
										}

										sendDefinition.HeaderLocationLatitude = latitude;
										sendDefinition.HeaderLocationLongitude = longitude;
										break;
									default:
										break;
								}

								if (template.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None)
								{
									if (jHSM["buttons"] == null || jHSM["buttons"].Type != Newtonsoft.Json.Linq.JTokenType.Array)
									{
										throw new InvalidOperationException("HSM Template buttons is missing and must be an array of objects");
									}

									var jButtons = (Newtonsoft.Json.Linq.JArray) jHSM["buttons"];

									if (jButtons.Count != template.Buttons.Length)
									{
										throw new InvalidOperationException(string.Format("HSM Template buttons length must be {0}", template.Buttons.Length));
									}

									sendDefinition.Buttons = new DomainModel.Whatsapp.HSMTemplateButton[template.Buttons.Length];

									int index = 0;
									foreach (var jButton in jButtons)
									{
										if (jButton.Type != Newtonsoft.Json.Linq.JTokenType.Object)
										{
											throw new InvalidOperationException(string.Format("HSM Template buttons[{0}] must be an object", index));
										}

										if (jButton["type"] == null || jButton["type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
										{
											throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type is missing", index));
										}

										if (jButton["index"] == null || jButton["index"].Type != Newtonsoft.Json.Linq.JTokenType.Integer)
										{
											throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].index is missing", index));
										}

										if (index != jButton["index"].ToObject<int>())
										{
											throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].index must be {1}", index, index));
										}

										var type = jButton["type"].ToString();
										var button = template.Buttons[index];

										sendDefinition.ButtonsType = template.ButtonsType;

										if ((template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
											template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
											button.QuickReplyParameter != null)
										{
											if (!type.Equals("quick_reply"))
											{
												throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type must be 'quick_reply'", index));
											}

											if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter is missing", index));
											}

											var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
											if (jParameter[button.QuickReplyParameter.Name] == null || jParameter[button.QuickReplyParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.QuickReplyParameter.Name));
											}

											var value = jParameter[button.QuickReplyParameter.Name].ToString();
											if (string.IsNullOrEmpty(value))
											{
												throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.QuickReplyParameter.Name));
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												QuickReplyParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
												{
													Name = button.QuickReplyParameter.Name,
													Value = value
												}
											};
										}

										else if ((template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
												  template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
												  button.CallToActionButtonType != null)
										{
											switch (button.CallToActionButtonType.Value)
											{
												case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
													if (!type.Equals("url"))
													{
														throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type must be 'url'", index));
													}

													if (jButton["sub_type"] == null || jButton["sub_type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
													{
														throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].sub_type is missing", index));
													}

													var subType = jButton["sub_type"].ToString();

													switch (button.UrlButtonType.Value)
													{
														case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed:
															if (!subType.Equals("fixed"))
															{
																throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].sub_type must be 'fixed'", index));
															}

															sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
															{
																CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
																UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed
															};

															break;

														case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic:
															{
																if (!subType.Equals("dynamic"))
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].sub_type must be 'dynamic'", index));
																}

																if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter is missing", index));
																}

																var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
																if (jParameter[button.UrlParameter.Name] == null || jParameter[button.UrlParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.UrlParameter.Name));
																}

																var value = jParameter[button.UrlParameter.Name].ToString();
																if (string.IsNullOrEmpty(value))
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.UrlParameter.Name));
																}

																sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
																{
																	CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
																	UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic,
																	UrlParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
																	{
																		Name = button.UrlParameter.Name,
																		Value = value
																	}
																};
															}

															break;

														default:
															break;
													}
													break;

												case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call:
													if (!type.Equals("call"))
													{
														throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type must be 'call'", index));
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call
													};
													break;

												case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
													{
														if (!type.Equals("offer"))
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type must be 'call'", index));
														}

														if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter is missing", index));
														}

														var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
														if (jParameter[button.OfferCodeParameter.Name] == null || jParameter[button.OfferCodeParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.OfferCodeParameter.Name));
														}

														var value = jParameter[button.OfferCodeParameter.Name].ToString();
														if (string.IsNullOrEmpty(value))
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.OfferCodeParameter.Name));
														}

														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode,
															OfferCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
															{
																Name = button.OfferCodeParameter.Name,
																Value = value
															}
														};
													}
													break;
												case HSMTemplateCallToActionButtonTypes.Flow:
													if (button.FlowParameter != null)
													{
														var screens = settings.FindFlowScreensData(button.FlowParameter.FlowID, button.FlowParameter.NavigateScreen);

														var flowParameter = new DomainModel.Whatsapp.HSMTemplateFlowParameters()
														{
															Name = button.FlowParameter.Name
														};

														flowParameter.ActionData = new ActionPayload();

														if (screens != null && screens.Length > 0)
														{
															var screenData = screens[0].Data;

															if (screenData != null && screenData.HasValues)
															{
																if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter is missing", index));
																}

																var jFlowParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
																if (jFlowParameter[button.FlowParameter.Name] == null || jFlowParameter[button.FlowParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.Object)
																{
																	throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} must be an object", index, button.FlowParameter.Name));
																}

																var jFlowValue = (JObject) jFlowParameter[button.FlowParameter.Name];
																flowParameter.ActionData.Data = jFlowValue;
															}

															sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
															{
																CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow,
																FlowParameter = flowParameter,
															};
														}
													}
													break;
												default:
													break;
											}
										}
										else if ((template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode) &&
												  button.AuthCodeButtonType != null)
										{
											switch (button.AuthCodeButtonType.Value)
											{
												case DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode:
													{
														if (!type.Equals("url"))
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].type must be 'url'", index));
														}

														if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter is missing", index));
														}

														var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
														if (jParameter[button.AuthCodeParameter.Name] == null || jParameter[button.AuthCodeParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.AuthCodeParameter.Name));
														}

														var value = jParameter[button.AuthCodeParameter.Name].ToString();
														if (string.IsNullOrEmpty(value))
														{
															throw new InvalidOperationException(string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.AuthCodeParameter.Name));
														}

														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															AuthCodeButtonType = DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode,
															AuthCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
															{
																Name = button.AuthCodeParameter.Name,
																Value = value
															}
														};
													}
													break;
											}
										}
										index++;
									}

								}

								var sendDefObj = Newtonsoft.Json.JsonConvert.SerializeObject(sendDefinition);
								options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter] = sendDefObj;
							}
						}
					}
				}
				else
				{
					if (DateTime.Now.Subtract(options.Message.Date).TotalMinutes >= DomainModel.SystemSettings.Instance.Whatsapp.MaxMinutesToAnswerMessages)
					{
						throw new InvalidOperationException("El mensaje de whatsapp fue envíado hace más de 24 horas. No se puede responder");
					}
				}
			}
			else if (options.Message.SocialServiceType == SocialServiceTypes.FacebookMessenger)
			{
				if (options.Parameters != null && options.Parameters.ContainsKey(Social.Facebook.Messenger.Message.TagParameter) &&
					Convert.ToBoolean(options.Parameters[Social.Facebook.Messenger.Message.TagParameter]))
					isHSM = true;

				if (!isHSM)
				{
					if (string.IsNullOrEmpty(options.Text))
						throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(options.Text));
				}

				var settings = options.Message.Service.Settings as DomainModel.ServiceSettings.FacebookMessengerSettings;
				if (isHSM)
				{
					if (!settings.AllowToSendTags)
						throw new InvalidOperationException("El servicio no permite el envío de mensajes de etiquetas");

					if (options.Person != null && options.Person.Type == PersonTypes.Agent && !settings.AllowAgentsToSendTags)
						throw new InvalidOperationException("El servicio no permite el envío de mensajes de etiquetas por parte de los agentes");

					var template = settings.TagTemplates.SingleOrDefault(t =>
						t.ID.Equals(options.Parameters[Social.Facebook.Messenger.Message.TagIDParameter]));
					if (template != null)
					{
						options.Text = template.Template;
						if (template.Parameters != null && template.Parameters.Length > 0)
						{
							var parameterValues = Newtonsoft.Json.Linq.JArray.Parse(options.Parameters[Social.Facebook.Messenger.Message.TagTemplateDataParameter]);
							for (var i = 0; i < template.Parameters.Length; i++)
							{
								var parameterName = template.Parameters[i].Substring(0, template.Parameters[i].IndexOf('='));
								options.Text = options.Text.Replace("{{" + parameterName + "}}", parameterValues[i].ToString());
							}
						}
					}
				}
				else
				{
					if (DateTime.Now.Subtract(options.Message.Date).TotalMinutes >= DomainModel.SystemSettings.Instance.FacebookMessenger.MaxMinutesToAnswerMessages)
					{
						throw new InvalidOperationException("El mensaje de whatsapp fue envíado hace más de 24 horas. No se puede responder");
					}
				}
			}
			else
			{
				if (string.IsNullOrEmpty(options.Text))
					throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(options.Text));
			}

			#endregion

			if (options.Person != null)
			{
				if (options.Person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} está respondiendo el mensaje {1}", options.Person, options.Message);
				else
					Tracer.TraceInfo("El usuario {0} está respondiendo el mensaje {1}", options.Person, options.Message);
			}
			else
			{
				Tracer.TraceInfo("Se está respondiendo el mensaje {0}", options.Message);
			}

			try
			{
				DateTime now = DateTime.Now;

#if !NETCOREAPP
				if (!string.IsNullOrEmpty(options.Text))
					options.Text = GetReplyTextForMessage(options.Text, options.Message);
				if (!string.IsNullOrEmpty(options.AssociatedText))
					options.AssociatedText = GetReplyTextForMessage(options.AssociatedText, options.Message);

				if (options.Message.SocialServiceType == SocialServiceTypes.Mail &&
					options.Parameters != null &&
					options.Parameters.ContainsKey("InsertedImages") &&
					options.Parameters["InsertedImages"].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					var text = options.Text;

					if (options.Attachments == null)
						options.Attachments = new List<DomainModel.Attachment>();

					ProcessEmbededMailImages(ref text, options.Attachments, options.Parameters);
					options.Text = text;
				}
#endif

				long? insertedMessageID, insertedAssociatedMessageID;
				if (options.Message.SocialServiceType == DomainModel.SocialServiceTypes.Facebook &&
					options.Message.IsDirectMessage &&
					options.Message.IsGrouping &&
					!string.IsNullOrEmpty(options.AssociatedText))
				{
					MessageDAO.Reply(options.Message, options.Person, options.Text, false, null, options.CloseCase, options.Attachments, options.Parameters, null, null, out insertedMessageID, out insertedAssociatedMessageID, options.IsMyOutgoingCases || options.IsPendingReplyFromCustomerReply, options.Source, now);
				}
				else if (options.Message.SocialServiceType == DomainModel.SocialServiceTypes.GoogleMyBusiness)
				{
					MessageDAO.Reply(options.Message, options.Person, options.Text, false, options.AssociatedText, options.CloseCase, options.Attachments, options.Message.Parameters, null, options.AssociatedAttachments, out insertedMessageID, out insertedAssociatedMessageID, options.IsMyOutgoingCases || options.IsPendingReplyFromCustomerReply, options.Source, now);
				}
				else
				{
					MessageDAO.Reply(options.Message, options.Person, options.Text, false, options.AssociatedText, options.CloseCase, options.Attachments, options.Parameters, null, options.AssociatedAttachments, out insertedMessageID, out insertedAssociatedMessageID, options.IsMyOutgoingCases || options.IsPendingReplyFromCustomerReply, options.Source, now);
				}

				options.Message.FinishedDate = now;
				options.Message.ReplySource = options.Source;

				var lastSegment = options.Message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedDate = now;
					if (options.Person != null)
						lastSegment.Person = options.Person;
				}

				if (options.Message.Case != null)
				{
					options.Message.Case.LastPerson = options.Person ?? options.Message.Case.LastPerson;
					options.Message.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					if (insertedMessageID != null)
						options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageID.Value.ToString();
					else if (insertedAssociatedMessageID != null)
						options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedAssociatedMessageID.Value.ToString();
					options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = isHSM.ToString();
					options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) options.Message.SocialServiceType).ToString();

					if (isHSM)
					{
						options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
						options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
						if (options.Person != null)
						{
							options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) (options.Person.Type == PersonTypes.Agent ? ReplySources.Agent : ReplySources.Supervisor)).ToString();
							options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = options.Person.ID.ToString();
						}
						else
						{
							options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) ReplySources.YFlow).ToString();
							options.Message.Case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByIDParameter] = string.Empty;
						}
					}

					if (options.Message.IsDirectMessage ||
						(!options.Message.IsDirectMessage && !string.IsNullOrEmpty(options.AssociatedText)))
					{
						options.Message.Case.Parameters[DomainModel.Case.HasPrivateAnswerParameter] = true.ToString();
					}

					DateTime? firstAssginedMessageDate = options.Message.LastSegment.AssignedDate;

					if (options.Person != null && options.Person.Type == PersonTypes.Agent)
					{
						if (!options.Message.Case.Parameters.ContainsKey(DomainModel.Case.FirstRepliedMessageByAgentParameter) &&
							options.Message.LastSegment.ActionTime != null)
						{
							options.Message.Case.Parameters[DomainModel.Case.FirstRepliedMessageByAgentParameter] = options.Message.ID.ToString();
							options.Message.Case.Parameters[DomainModel.Case.FirstRepliedMessageByAgentWorkingTimeParameter] = options.Message.LastSegment.ActionTime.Value.ToString(global::System.Globalization.CultureInfo.InvariantCulture);
						}

						if (options.Message.Case.Parameters[DomainModel.Case.FirstRepliedMessageByAgentParameter] == options.Message.ID.ToString() &&
							!options.Message.Case.Parameters.ContainsKey(DomainModel.Case.TimeFirstMessageInQueueParameter))
						{
							UpdateDelayAndWaitingTimesOnCaseParameters(options.Message.Case.Messages.Where(m => m.Queue != null), options.Message.Case.Parameters, now);
						}

						if (options.Message.Case.FirstAgentReply == null)
						{
							options.Message.Case.FirstAgentReply = (Agent) options.Person;
						}

						if (!options.Message.Case.Parameters.ContainsKey(Case.FirstReplyByAgentDateParameter))
						{
							options.Message.Case.Parameters[Case.FirstReplyByAgentDateParameter] = now.ToString("o");
						}

						options.Message.Case.LastAgentReply = (Agent) options.Person;
						options.Message.Case.Parameters[Case.LastReplyByAgentDateParameter] = now.ToString("o");

						try
						{
							if (options.Message.Case?.Parameters != null && lastSegment.EnqueuedDate.HasValue && lastSegment.FinishedDate.HasValue)
							{
								MetricsAdapter.CalculateTotalOperationTimeParameter(options.Message.Case?.Parameters, lastSegment.FinishedDate.Value, lastSegment.EnqueuedDate.Value);
							}

							if (firstAssginedMessageDate.HasValue)
							{
								MetricsAdapter.CalculateFirstMessageReplyByAgentTimeParameter(options.Message.Case?.Parameters, now, firstAssginedMessageDate.Value, out bool mustUpdateParameters);
							}
						}
						catch (Exception ex)
						{
							Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
						}
					}

					if (firstAssginedMessageDate.HasValue)
					{
						MetricsAdapter.CalculateTotalFirstReplyTimeParameter(options.Message.Case?.Parameters, now, firstAssginedMessageDate.Value);
					}
						
					DAL.CaseDAO.UpdateParameters(options.Message.Case);
				}


				if (options.Parameters != null && options.Parameters.ContainsKey(DomainModel.Message.RelatedMessagesParameter))
				{
					options.Message.Parameters.Add(DomainModel.Message.RelatedMessagesParameter, options.Parameters[DomainModel.Message.RelatedMessagesParameter]);
					MessageDAO.UpdateParameters(options.Message.ID, options.Message.Parameters);
				}

				if (options.MarkCaseAsPendingReply)
				{
					if (!options.CloseCase)
					{
						if (options.Person == null)
							options.Message.Case.Parameters[DomainModel.Case.MarkedPendingResponseFromCustomerByParameter] = ((short) DomainModel.PendingResponseFromCustomerByOrigins.System).ToString();
						else if (options.Person.Type == PersonTypes.Agent)
							options.Message.Case.Parameters[DomainModel.Case.MarkedPendingResponseFromCustomerByParameter] = ((short) DomainModel.PendingResponseFromCustomerByOrigins.Agent).ToString();
						else
							options.Message.Case.Parameters[DomainModel.Case.MarkedPendingResponseFromCustomerByParameter] = ((short) DomainModel.PendingResponseFromCustomerByOrigins.Supervisor).ToString();
						options.Message.Case.Parameters[DomainModel.Case.YFlowPendingResponseTypeParameter] = string.Empty;
						options.Message.Case.PendingReplyFromCustomerAt = now;
						await Core.System.Instance.CasesService.AddPendingReplyCaseAsync(options.Message.Case, options.Message);
						CaseDAO.UpdatePendingReplyFromCustomer(options.Message.Case, options.Person);
					}
				}
				else if (options.Message.Case.PendingReplyFromCustomerAt != null)
				{
					options.Message.Case.Parameters[DomainModel.Case.YFlowPendingResponseTypeParameter] = string.Empty;
					if (options.Message.Case.Parameters.ContainsKey(DomainModel.Case.MarkedPendingResponseFromCustomerByParameter))
						options.Message.Case.Parameters.Remove(DomainModel.Case.MarkedPendingResponseFromCustomerByParameter);
					options.Message.Case.PendingReplyFromCustomerAt = null;
					CaseDAO.UpdatePendingReplyFromCustomer(options.Message.Case, null);
					await Core.System.Instance.CasesService.RemovePendingReplyCase(options.Message.Case);
				}

#if !NETCOREAPP
				if (options.Message.Status != DomainModel.MessageStatuses.Grouped &&
					options.Message.IsGrouping &&
					options.Message.Service.Settings != null &&
					options.Message.Service.Settings.Grouping != null)
				{
					Tracer.TraceVerb("Se procesarán los mensajes agrupados del mensaje: {0}", options.Message);
					/*
					 * Cuando el servicio al que pertenece el mensaje soporta Agrupamiento y tiene configurado agrupamiento para el
					 * tipo de servicio al que pertenece el mensaje se realizan las siguientes acciones
					 * a) Se descartan todos los mensajes privados
					 * b) Si está configurado que se descarten los mensajes, se descartan los mensajes agrupados
					 * c) Si está configurado que se respondan, se responden los mensajes agrupados
					 * 
					 * Cuando se trata de un mensaje privado de Facebook, y el agente eligió responder también por público,
					 * el mensaje público agrupado ya fue respondido y se deberá evitar responder 2 veces
					 */

					var messagesToAvoidReply = new List<long>();
					var socialConversationsToAvoidReply = new List<string>();

					if (options.Message.SocialServiceType == DomainModel.SocialServiceTypes.Facebook &&
						options.Message.IsDirectMessage &&
						options.Message.IsGrouping &&
						!string.IsNullOrEmpty(options.AssociatedText))
					{
						var groupedMessageToReply = options.Message.Groups.Last(m => !m.IsDirectMessage && m.SocialServiceType == DomainModel.SocialServiceTypes.Facebook);
						messagesToAvoidReply.Add(groupedMessageToReply.ID);

						if (!string.IsNullOrEmpty(groupedMessageToReply.SocialConversationID))
							socialConversationsToAvoidReply.Add(groupedMessageToReply.SocialConversationID);
					}

					global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(ct => ProcessGroupedMessages(options.Message, options.Message.Service.Settings.Grouping, messagesToAvoidReply, socialConversationsToAvoidReply, options.GroupedMessageReplies));
				}
#endif

				if (options.Message.Case != null)
				{
					options.Message.Case.Replies++;
					options.Message.Case.RepliesByAgents += (options.Person != null && options.Person.Type == PersonTypes.Agent) ? (short) 1 : (short) 0;
					options.Message.Case.RepliesByUsers += (options.Person != null && options.Person.Type == PersonTypes.User) ? (short) 1 : (short) 0;
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

#if !NETCOREAPP
				if (options.Person != null)
				{
					if (options.Message.Queue != null)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = options.Message.Queue.ID;
						info.PersonID = options.Person.ID;
						info.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
						info.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
						info.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
						if (!options.IsMyOutgoingCases && !options.IsPendingReplyFromCustomerReply)
						{
							info.MessagesRepliedOutOfSL = (options.Message.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesRepliedOutOfSLL = (options.Message.OutOfServiceLevel == true && options.Message.OutOfSLL == true) ? 1 : 0;
							if (options.Person.Type == PersonTypes.Agent && options.Message.LastSegment != null)
							{
								info.AgentTime = options.Message.LastSegment.ActionTime ?? 0;
								info.UnreadTime = options.Message.LastSegment.UnreadTime ?? 0;
								info.ReadTime = options.Message.LastSegment.ReadTime ?? 0;
							}
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);
					}

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = options.Person.ID;
					info.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
					info.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
					info.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
					if (!options.IsMyOutgoingCases && !options.IsPendingReplyFromCustomerReply)
					{
						info.MessagesRepliedOutOfSL = (options.Message.OutOfServiceLevel == true) ? 1 : 0;
						info.MessagesRepliedOutOfSLL = (options.Message.OutOfServiceLevel == true && options.Message.OutOfSLL == true) ? 1 : 0;

						if (options.Person.Type == PersonTypes.Agent && options.Message.LastSegment != null)
						{
							info.AgentTime = options.Message.LastSegment.ActionTime ?? 0;
							info.UnreadTime = options.Message.LastSegment.UnreadTime ?? 0;
							info.ReadTime = options.Message.LastSegment.ReadTime ?? 0;
						}
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					if (options.Message.Queue != null)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = options.Message.Queue.ID;
						info.PersonID = 0;
						info.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
						info.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
						info.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
						if (!options.IsMyOutgoingCases && !options.IsPendingReplyFromCustomerReply)
						{
							info.MessagesRepliedOutOfSL = (options.Message.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesRepliedOutOfSLL = (options.Message.OutOfServiceLevel == true && options.Message.OutOfSLL == true) ? 1 : 0;
							if (options.Message.EnqueuedDate != null)
							{
								info.ReplyTime = Convert.ToInt32(now.Subtract(options.Message.EnqueuedDate.Value).TotalSeconds);
							}
							if (options.Person.Type == PersonTypes.Agent && options.Message.LastSegment != null)
							{
								info.AgentTime = options.Message.LastSegment.ActionTime ?? 0;
								info.UnreadTime = options.Message.LastSegment.UnreadTime ?? 0;
								info.ReadTime = options.Message.LastSegment.ReadTime ?? 0;
							}
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = options.Message.Service.ID;
						infoService.QueueID = options.Message.Queue.ID;
						infoService.PersonID = options.Person.ID;
						infoService.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
						infoService.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
						infoService.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
						infoService.MessagesPayment = isHSM ? 1 : 0;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
					}

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = options.Person.ID;
					infoService.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
					infoService.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
					infoService.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Message.Service.ID;
					infoService.QueueID = options.Message.Queue.ID;
					infoService.PersonID = 0;
					infoService.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
					infoService.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
					infoService.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.RepliedMessages = options.IsMyOutgoingCases ? 0 : 1;
					infoService.MyOutgoingCases = options.IsMyOutgoingCases ? 1 : 0;
					infoService.RepliedMessagesByUser = (options.Person.Type == PersonTypes.User) ? 1 : 0;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					DomainModel.ConnectionInfo connectionInfo = System.Instance.AgentsService.GetConnectionInfo(options.Person.ID);
					if (connectionInfo != null && insertedMessageID != null)
					{
						HistSessionsAgentsCasesMessagesDAO.Insert((int) insertedMessageID, options.Message.Case.ID, connectionInfo.SessionID, options.Person.ID, false);
					}
				}
				else
#endif
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = options.Message.Queue == null ? 0 : options.Message.Queue.ID;
					info.PersonID = 0;
					info.RepliedMessages = 1;
					info.AutoRepliedMessages = 1;
					if (!options.IsMyOutgoingCases && !options.IsPendingReplyFromCustomerReply)
					{
						info.MessagesRepliedOutOfSL = (options.Message.OutOfServiceLevel == true) ? 1 : 0;
						info.MessagesRepliedOutOfSLL = (options.Message.OutOfServiceLevel == true && options.Message.OutOfSLL == true) ? 1 : 0;
					}

					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Message.Service.ID;
					infoService.QueueID = options.Message.Queue == null ? 0 : options.Message.Queue.ID;
					infoService.PersonID = 0;
					infoService.RepliedMessages = 1;
					infoService.AutoRepliedMessages = 1;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.RepliedMessages = 1;
					infoService.AutoRepliedMessages = 1;
					infoService.MessagesPayment = isHSM ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages) &&
					!options.IsPendingReplyFromCustomerReply)
				{
					System.Instance.AutomaticExportService.StoreMessage(options.Message);
				}

				if (insertedMessageID != null)
				{
					EnsureServiceInstance(options.Message.Service);
					var insertedMessage = await Reply(insertedMessageID.Value, options.Message.Case, sendInBackground: !options.Message.Service.SocialService.SupportsPublishToServiceBus());
					if (options.CurrentlyEnqueuedMessageOfCase != null && options.CurrentlyEnqueuedMessageOfCase.Case != null)
					{
						options.CurrentlyEnqueuedMessageOfCase.Case.AddReply(insertedMessage, options.Source, options.Message.ID);
					}
				}
				if (insertedAssociatedMessageID != null)
				{
					EnsureServiceInstance(options.Message.Service);
					var insertedMessage = await Reply(insertedAssociatedMessageID.Value, options.Message.Case, sendInBackground: !options.Message.Service.SocialService.SupportsPublishToServiceBus());
					if (options.CurrentlyEnqueuedMessageOfCase != null && options.CurrentlyEnqueuedMessageOfCase.Case != null)
					{
						options.CurrentlyEnqueuedMessageOfCase.Case.AddReply(insertedMessage, options.Source, options.Message.ID);
					}
				}

				if (options.CloseCase)
				{
					var caseClosedBy = CaseClosingResponsibles.Filter;
					if (options.Person != null)
						caseClosedBy = options.Person.Type == PersonTypes.Agent ? CaseClosingResponsibles.Agent : CaseClosingResponsibles.Supervisor;
					
					await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
					{
						Message = options.Message,
						Case = options.Message.Case,
						ClosedBy = caseClosedBy,
						Person = options.Person,
						Interval = interval,
						StoreInDatabase = false,
						Queue = null
					});
				}
				else
				{
					await Core.System.Instance.CasesService.RegisterCaseToBeClosed(options.Message.Case, options.Message.Service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);
				}

				if (options.Person != null)
				{
					if (options.Person.Type == DomainModel.PersonTypes.Agent)
						Tracer.TraceInfo("El agente {0} respondió el mensaje {1}", options.Person, options.Message);
					else
						Tracer.TraceInfo("El usuario {0} respondió el mensaje {1}", options.Person, options.Message);
				}
				else
				{
					Tracer.TraceInfo("Se respondió el mensaje {0}", options.Message);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando respondía el mensaje {0}: {1}", options.Message, ex);
				throw;
			}
		}

		/// <summary>
		/// Encola un caso que tiene un mensaje pendiente
		/// </summary>
		/// <param name="pending">El <see cref="DomainModel.CaseMessagePending"/> con el caso y mensaje pendiente</param>
		/// <param name="queueKey">La clave de una cola para realizar el encolamiento</param>
		public async Task EnqueueCurrentPendingMessage(DomainModel.CaseMessagePending pending, string queueKey = null)
		{
			if (pending == null)
				throw new ArgumentNullException(nameof(pending));

			try
			{
				var message = pending.Message;
				if (!message.RetrievedFromDatabase)
				{
					var entitiedToRead = new MessageDAO.RelatedEntitiesToRead(false)
					{
						PostedBy = true,
						GroupedMessages = false,
						Service = true,
						Queue = true,
						Attachments = true,
						MessageSegments = false,
						Chat = false,
					};
					message = MessageDAO.GetOne(message.ID, entitiedToRead, false);
				}

				if (message.Case == null)
				{
					message.Case = pending.Case;
				}

				Tracer.TraceVerb("El caso {0} del mensaje {1} se registrará para no invocar más a yFlow", message.Case, message);
				message.Case.Parameters[DomainModel.Case.YFlowDoNotInvokeParameter] = true.ToString();
				CaseDAO.UpdateParameters(message.Case);

				// Quitamos el mensaje de los pendientes, dejandolo No asignado
				await this.MarkMessageAsPending(message, false);

#if NETCOREAPP
				await this.ForwardMessage(message);
#else
				// Volvemos a encolar el mensaje
				Core.System.Instance.QueueService.EnqueueMessage(message, new EnqueueSettings()
				{
					CanInvokeYFlow = false,
					DeriveKey = queueKey,
					DerivedFromYFlowPendingMessage = true
				});
#endif

				Tracer.TraceInfo("Se encoló el mensaje pendiente {0} del caso {1}", pending.Message, pending.Case);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se encolaba mensaje pendiente del case {0}: {1}", pending.Case, ex);
				throw;
			}
		}

		/// <summary>
		/// Remueve un caso que tiene un mensaje pendiente
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> con un mensaje pendiente</param>
		public async Task RemoveCurrentPendingMessage(DomainModel.Case @case, bool closeCase, IEnumerable<int> tags)
		{
			if (@case == null)
				throw new ArgumentNullException(nameof(@case));

			try
			{
				if (Core.System.Instance.CasesService.ContainsYFlowPendingCase(@case, out DomainModel.Message currentPendingMessage) &&
					currentPendingMessage != null)
				{
					if (tags != null && tags.Any())
					{
						try
						{
							this.UpdateCase(@case, null, tags, null, null, TaggedBySources.ExternalIntegration, null);
						}
						catch (Exception ex)
						{
							Tracer.TraceInfo("No se pudo actualizar las etiquetas del caso {0}: {1}", @case.ID, ex);
						}
					}

					if (@case.Parameters.ContainsKey(DomainModel.Case.YFlowPendingCaseMessageParameter))
						@case.Parameters.Remove(DomainModel.Case.YFlowPendingCaseMessageParameter);
					@case.PendingMessages = false;
					
					if (currentPendingMessage.Case == null ||
						!currentPendingMessage.Case.RetrievedFromDb)
					{
						currentPendingMessage.Case = @case;
					}

					await this.DiscardAsync(new DiscardOptions()
					{
						CloseCase = closeCase,
						Message = currentPendingMessage,
						DiscardSource = DiscardSources.PendingMessageDiscarded,
						Person = null,
						RemoveFromQueue = false,
						Parameters = null
					});

#if !NETCOREAPP
					Core.System.Instance.CasesService.RemoveYFlowPendingCase(@case);
#endif
				}

				Tracer.TraceInfo("Se quitó el caso {0} de casos con mensajes pendientes", @case);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se quitaba mensaje pendiente del case {0}: {1}", @case, ex);
				throw;
			}
		}

		/// <summary>
		/// Retorna un caso que tiene un mensaje pendiente a yFlow
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> con un mensaje pendiente</param>
		/// <param name="transfer">La indicación de transferencia del mensaje</param>
		public async Task ReturnCurrentPendingMessageToYFlow(Case @case, DomainModel.ServiceSettings.YFlowSettings.ReturnFromAgent transfer)
		{
			if (@case == null)
				throw new ArgumentNullException(nameof(@case));

			if (transfer == null)
				throw new ArgumentNullException(nameof(transfer));

			try
			{
				if (Core.System.Instance.CasesService.ContainsYFlowPendingCase(@case, out DomainModel.Message currentPendingMessage) &&
					currentPendingMessage != null)
				{
					var invokeParameters = new Social.YFlow.InvokeParameters(currentPendingMessage);
					invokeParameters.TransferedFromPendingCase = true;
					invokeParameters.ReturnFromPendingCase = transfer;
					invokeParameters.ThrowOnError = true;
					invokeParameters.AllowedToDerive = true;

					try
					{
						var message = currentPendingMessage;
						MessageLogDAO.Insert(message, null, MessageLogTypes.MessageTransferedToYFlow, transfer.Description);

						var invokeResults = await this.InvokeYFlowAsync(invokeParameters);

						var now = DateTime.Now;

						var lastSegment = currentPendingMessage.LastSegment;
						if (lastSegment != null)
						{
							lastSegment.FinishedDate = now;

							lastSegment = currentPendingMessage.AddSegment();
						}

						if (invokeResults.Queue == null)
						{
							// Si destQueue es null, entonces lo respondió yFlow
							if (message.Parameters.ContainsKey(DomainModel.Message.YFlowInvokedParameter))
								message.Parameters.Remove(DomainModel.Message.YFlowInvokedParameter);

							if (message.Case.Parameters.ContainsKey(DomainModel.Case.YFlowDoNotInvokeParameter))
							{
								message.Case.Parameters.Remove(DomainModel.Case.YFlowDoNotInvokeParameter);
								invokeResults.MustUpdateCaseParameters = true;
							}

							var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

							Tracer.TraceInfo("Se transfirió el mensaje pendiente {0} a yflow", message);
						}
						else
						{
							Tracer.TraceError("YFlow indica que hay que volver a derivar. Se lo toma como error");
							throw new Exception("YFlow no devolvió ningún mensaje para devolver");
						}

						if (invokeResults.MustUpdateCaseParameters)
							DAL.CaseDAO.UpdateParameters(message.Case);
						if (invokeResults.MustUpdateMessageParameters)
							DAL.MessageDAO.UpdateParameters(message);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error al querer invocar a yFlow luego de una transferencia de mensaje pendiente. Error: {0}", ex);
						throw;
					}
				}

				if (@case.Parameters.ContainsKey(DomainModel.Case.YFlowPendingCaseMessageParameter))
					@case.Parameters.Remove(DomainModel.Case.YFlowPendingCaseMessageParameter);
				DAL.CaseDAO.UpdatePendingMessages(@case, false);
				@case.PendingMessages = false;
#if !NETCOREAPP
				Core.System.Instance.CasesService.RemoveYFlowPendingCase(@case);
#endif

				Tracer.TraceInfo("Se quitó el caso {0} de casos con mensajes pendientes", @case);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se quitaba mensaje pendiente del caso {0}: {1}", @case, ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como pendiente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está marcando como pendiente</param>
		public async Task MarkMessageAsPending(DomainModel.Message message, bool pending)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			try
			{
				if (pending)
				{
					message.Status = MessageStatuses.Pending;
					message.Case.PendingMessages = true;
					message.Case.Parameters[DomainModel.Case.YFlowPendingCaseMessageParameter] = message.ID.ToString();
					DAL.MessageDAO.MarkAsPending(message, true);

					if (Core.System.Instance.CasesService.ContainsYFlowPendingCase(message.Case, out DomainModel.Message currentPendingMessage) &&
						currentPendingMessage != null)
					{
						await this.DiscardAsync(new DiscardOptions()
						{
							CloseCase = false,
							Message = currentPendingMessage,
							DiscardSource = DiscardSources.PendingMessageDiscarded,
							Person = null,
							RemoveFromQueue = false
						});
					}

#if !NETCOREAPP
					Core.System.Instance.CasesService.AddYFlowPendingMessage(message);
#endif
				}
				else
				{
					message.Status = MessageStatuses.NotAssigned;
					if (message.Case.Parameters.ContainsKey(DomainModel.Case.YFlowPendingCaseMessageParameter))
						message.Case.Parameters.Remove(DomainModel.Case.YFlowPendingCaseMessageParameter);
					message.Case.PendingMessages = false;
					DAL.MessageDAO.MarkAsPending(message, false);

#if !NETCOREAPP
					Core.System.Instance.CasesService.RemoveYFlowPendingCase(message.Case);
#endif
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se marcaba el mensaje {0} como pendiente: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Agrega un mensaje a un caso
		/// </summary>
		/// <param name="options"></param>
		/// <remarks>
		/// Recordar que al modificar el método, se debe debe aplicar el cambio en el método AddMessageToCase
		/// </remarks>
		public async Task<long> AddMessageToCaseAsync(AddMessageOptions options)
		{
			#region Validaciones

			if (options == null)
				throw new ArgumentNullException(nameof(options), "Las opciones no puede ser nulo");

			if (options.Service == null)
				throw new ArgumentNullException(nameof(options.Service), "El servicio no puede ser nulo");

			if (options.Service.SocialServiceType != options.SocialServiceType)
				throw new ArgumentNullException(nameof(options.SocialServiceType), "El tipo de red social no coincide con el servicio");

			if (options.SocialUserKey == null)
				throw new ArgumentNullException(nameof(options.SocialUserKey), "El usuario es requerido");

			if (string.IsNullOrWhiteSpace(options.Text))
				options.Text = null;

			if (options.SocialServiceType == DomainModel.SocialServiceTypes.Twitter)
			{
				if (options.Text.Length > DomainModel.SystemSettings.Instance.Twitter.DmTextCharacterLimit)
					throw new ArgumentOutOfRangeException(nameof(options.Text), string.Format("El texto del mensaje privado de twitter no puede superar los {1} caracteres: {0}", options.Text, DomainModel.SystemSettings.Instance.Twitter.DmTextCharacterLimit));
			}

			if (string.IsNullOrEmpty(options.Text) && (options.Attachments == null || options.Attachments.Count == 0))
			{
				if (options.Parameters == null || !options.Parameters.ContainsKey(DomainModel.Message.ReplyParameter))
					throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(options.Text));
			}

			this.EnsureServiceInstance(options.Service);

			#endregion

			if (options.Person != null)
			{
				if (options.Person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} está agregando un mensaje al caso {1}", options.Person, options.CaseId);
				else
					Tracer.TraceInfo("El usuario {0} está agregando un mensaje al caso {1}", options.Person, options.CaseId);
			}
			else
			{
				Tracer.TraceInfo("Se está agregando un mensaje al caso {0}", options.CaseId);
			}

			try
			{
				DateTime now = DateTime.Now;
				bool caseInvokedWithoutMessages = false;

#if !NETCOREAPP
				if (!string.IsNullOrEmpty(options.Text) && options.AssignedMessage != null)
					options.Text = GetReplyTextForMessage(options.Text, options.AssignedMessage);

				if (options.SocialServiceType == SocialServiceTypes.Mail &&
					options.Parameters != null &&
					options.Parameters.ContainsKey("InsertedImages") &&
					options.Parameters["InsertedImages"].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					var text = options.Text;

					if (options.Attachments == null)
						options.Attachments = new List<DomainModel.Attachment>();

					ProcessEmbededMailImages(ref text, options.Attachments, options.Parameters);
					options.Text = text;
				}
#endif

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				if (options.SocialServiceType == SocialServiceTypes.WhatsApp &&
					options.Parameters != null &&
					options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
					options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound)
						throw new InvalidOperationException("No hay licencia para enviar HSM");

					var whatsappSettings = options.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
					if (!whatsappSettings.AllowToSendHSM)
						throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM");

					if (options.Person != null && options.Person.Type == PersonTypes.Agent && !whatsappSettings.AllowAgentsToSendHSM)
						throw new InvalidOperationException("El servicio no permite el envío de mensajes HSM por parte de los agentes");

					var socialUserProfileId = options.SocialUser.Profile.ID;

					WhatsappSettings.HSMTemplate template = options.Template;

					if (template == null)
					{
						if (options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
						{
							var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);
							if (sendDefinition != null)
							{
								template = options.Template;
								if (template == null)
								{
									template = whatsappSettings.HSMTemplates.FirstOrDefault(t =>
										t.Namespace.Equals(sendDefinition.Namespace) &&
										t.ElementName.Equals(sendDefinition.ElementName) &&
										t.Language.Equals(sendDefinition.Language));

									options.Template = template;
								}

								if (options.Template != null && sendDefinition != null)
								{
									if (string.IsNullOrEmpty(options.Text))
									{
										options.Text = options.Template.Template;

										if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
										{
											foreach (var templateParameter in sendDefinition.Parameters)
											{
												options.Text = options.Text.Replace($"{{{templateParameter.Name}}}", templateParameter.Value);
											}
										}
									}
								}
							}
						}
						else
						{
							template = whatsappSettings.HSMTemplates.FirstOrDefault(t =>
								t.Namespace.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter]) &&
								t.ElementName.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter]) &&
								t.Language.Equals(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage]));
							if (template != null)
							{
								options.Text = template.Template;
								if (template.Parameters != null && template.Parameters.Length > 0)
								{
									var parameterValues = Newtonsoft.Json.Linq.JArray.Parse(options.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
									for (var i = 0; i < template.Parameters.Length; i++)
									{
										var parameterName = template.Parameters[i].Substring(0, template.Parameters[i].IndexOf('='));
										options.Text = options.Text.Replace("{{" + parameterName + "}}", parameterValues[i].ToString());
									}
								}
								else
								{
									if (options.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
									{
										options.Parameters.Remove(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter);
									}
								}
							}
						}
					}

					var caseParameters = new Dictionary<string, string>();
					caseParameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					caseParameters[DomainModel.Case.LastReplyMessageIDParameter] = "@@INSERTED_MESSAGE_ID@@";
					caseParameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
					caseParameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = true.ToString();
					caseParameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
					caseParameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;
					caseParameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) options.ReplySource).ToString();

					DomainModel.CaseStartedBySources caseStartedBySource;
					switch (options.ReplySource.Value)
					{
						case ReplySources.Agent:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByAgent;
							break;
						case ReplySources.Supervisor:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMBySupervisor;
							break;
						case ReplySources.YFlow:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByYFlow;
							break;
						case ReplySources.ExternalIntegration:
							caseStartedBySource = CaseStartedBySources.WhatsappHSMByIntegration;
							break;
						default:
							caseStartedBySource = CaseStartedBySources.WhatsappHSM;
							break;
					}

					var insertedMessageId = MessageDAO.SendWhatsapp(options.Person
						, options.SocialUser.ID
						, options.SocialUser.Profile.ID
						, options.Service.ID
						, options.Text
						, options.Attachments
						, options.Parameters
						, true
						, null
						, caseParameters
						, null
						, caseStartedBySource
						, out bool newCaseCreated
						, out DomainModel.Case @case);

					bool wasAttended = false;
#if !NETCOREAPP
					if (options.AssignedMessage != null)
					{
						if (options.AssignedMessage.LastSegment != null)
						{
							if (options.AssignedMessage.LastSegment.FinishedReadDate == null)
								this.FinishedRead(options.AssignedMessage, options.AssignedMessage.AssignedTo);

							// Quitamos la marca de inactivo en el agente, porque acaba de agregar un mensaje al caso
							options.AssignedMessage.LastSegment.InactiveByAgent = false;
							options.AssignedMessage.LastSegment.InactiveByAgentDate = null;
						}

						if (options.AssignedMessage.Service != null &&
							options.AssignedMessage.Service.Settings.ActAsChat &&
							!options.AssignedMessage.Parameters.ContainsKey(DomainModel.Message.AttendedParameter))
						{
							options.AssignedMessage.Parameters[DomainModel.Message.AttendedParameter] = true.ToString();
							options.AssignedMessage.Parameters[DomainModel.Message.AttendedAtParameter] = now.ToString("o");
							if (options.AssignedMessage.LastSegment != null &&
								options.AssignedMessage.LastSegment.AssignedDate != null)
							{
								options.AssignedMessage.Parameters[DomainModel.Message.AttendedTimeParameter] = now.Subtract(options.AssignedMessage.LastSegment.AssignedDate.Value).TotalSeconds.ToString("R", global::System.Globalization.CultureInfo.InvariantCulture);
							}

							if (options.AssignedMessage.Queue != null)
							{
								options.AssignedMessage.RepliedDate = now;
								var queueToGroup = Core.System.Instance.QueueService.GetQueueById(options.AssignedMessage.Queue.ID);
								queueToGroup.ReplyAsChat(options.AssignedMessage, now, options.Person);
							}

							wasAttended = true;
							DAL.MessageDAO.UpdateParameters(options.AssignedMessage);
						}
					}
#endif

					options.Case = @case;

					if (options.Case != null)
					{
						options.Case.LastPerson = options.Person ?? @case.LastPerson;

						if (options.Person != null && options.Person.Type == PersonTypes.Agent)
						{
							if (options.Case.FirstAgentReply == null)
							{
								options.Case.FirstAgentReply = (Agent) options.Person;
							}

							if (!options.Case.Parameters.ContainsKey(Case.FirstReplyByAgentDateParameter))
							{
								options.Case.Parameters[Case.FirstReplyByAgentDateParameter] = now.ToString("o");
							}

							options.Case.LastAgentReply = (Agent) options.Person;
							options.Case.Parameters[Case.LastReplyByAgentDateParameter] = now.ToString("o");
						}

						options.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
						options.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageId.ToString();
						options.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
						options.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = true.ToString();
						options.Case.Parameters[DomainModel.Case.LastReplyMessageHSMTemplateParameter] = template.ElementName;
						options.Case.Parameters[DomainModel.Case.LastReplyMessageHSMNamespaceParameter] = template.Namespace;
						options.Case.Parameters[DomainModel.Case.LastReplyMessageHSMSentByParameter] = ((short) options.ReplySource).ToString();

						if (options.UpdateCaseParameters != null)
							options.UpdateCaseParameters(options.Case);

						DAL.CaseDAO.UpdateParameters(options.Case);
					}

					if (options.Person != null)
					{
						if (options.Person.Type == DomainModel.PersonTypes.Agent)
							Tracer.TraceInfo("El agente {0} envió el mensaje saliente", options.Person);
						else
							Tracer.TraceInfo("El supervisor {0} envió el mensaje saliente", options.Person);
					}
					else
					{
						Tracer.TraceInfo("Se envió el mensaje saliente", options.Person);
					}

					DomainModel.Historical.Daily info;
					DomainModel.Historical.DailyService infoService;

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = 0;
					info.NewCases = 1;
					info.OutgoingMessages = 1;
					if (wasAttended)
					{
						info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
						info.AttendedMessagesByAgent = 1;
						info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
						info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
					}
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					if (options.Person != null)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = options.Person.ID;
						info.OutgoingMessages = 1;
						if (wasAttended)
						{
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						if (wasAttended && options.AssignedMessage.Queue != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = options.AssignedMessage.Queue.ID;
							info.PersonID = options.Person.ID;
							info.OutgoingMessages = 1;
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = options.AssignedMessage.Queue.ID;
							info.PersonID = 0;
							info.OutgoingMessages = 1;
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

#if !NETCOREAPP
						if (options.Person.Type == DomainModel.PersonTypes.Agent)
						{
							var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) options.Person);
							if (connectionInfo.Status == ConnectionStatuses.Aux &&
								DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages != null &&
								connectionInfo.AuxReason == DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages.Value)
							{
								info = DomainModel.Historical.Daily.CreateForInterval(interval);
								info.QueueID = 0;
								info.PersonID = options.Person.ID;
								info.AgentOutTime = connectionInfo.GetSecondsSinceLastStatusChange();
								Core.System.Instance.IntervalService.StoreInfo(info, interval);
							}

							HistSessionsAgentsCasesMessagesDAO.Insert(insertedMessageId, @case.ID, connectionInfo.SessionID, options.Person.ID, false);
						}
#endif
					}

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = options.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.OutboundMessages = 1;
					infoService.MessagesPayment = 1;
					infoService.NewCases = newCaseCreated ? 1 : 0;
					if (wasAttended)
					{
						infoService.AttendedMessagesByAgent = 1;
					}
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					if (options.Person != null)
					{
						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = options.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = options.Person.ID;
						infoService.OutboundMessages = 1;
						infoService.MessagesPayment = 1;
						infoService.NewCases = newCaseCreated ? 1 : 0;
						if (wasAttended)
						{
							infoService.AttendedMessagesByAgent = 1;
						}
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						if (wasAttended && options.AssignedMessage.Queue != null)
						{
							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = options.Service.ID;
							infoService.QueueID = options.AssignedMessage.Queue.ID;
							infoService.PersonID = options.Person.ID;
							infoService.OutboundMessages = 1;
							infoService.MessagesPayment = 1;
							infoService.NewCases = newCaseCreated ? 1 : 0;
							infoService.AttendedMessagesByAgent = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = options.Service.ID;
							infoService.QueueID = options.AssignedMessage.Queue.ID;
							infoService.PersonID = 0;
							infoService.OutboundMessages = 1;
							infoService.MessagesPayment = 1;
							infoService.NewCases = newCaseCreated ? 1 : 0;
							infoService.AttendedMessagesByAgent = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						}
					}
					if (options.Case == null ||
						!options.Case.RetrievedFromDb)
					{
						options.Case = DAL.CaseDAO.GetOneWithoutMessages(options.CaseId);
					}

					if (options.CloseCase)
					{
						var caseClosedBy = CaseClosingResponsibles.Filter;
						if (options.Person != null)
							caseClosedBy = options.Person.Type == PersonTypes.Agent ? CaseClosingResponsibles.Agent : CaseClosingResponsibles.Supervisor;
						
						await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
						{
							Message = null,
							Case = options.Case,
							ClosedBy = caseClosedBy,
							Person = options.Person,
							Interval = interval,
							StoreInDatabase = true,
							Queue = null
						});

#if !NETCOREAPP
						Core.System.Instance.CasesService.RemovePendingCase(options.Case, options.Person);
#endif
					}

					await Reply(insertedMessageId, options.Case, sendInBackground: !options.Service.SocialService.SupportsPublishToServiceBus());
					Tracer.TraceInfo("Se agregó el mensaje {0} al caso {1}", insertedMessageId, options.CaseId);

					return insertedMessageId;
				}
				else
				{

#if NETCOREAPP
					if (options.Case == null ||
						!options.Case.RetrievedFromDb)
					{
						options.Case = DAL.CaseDAO.GetOneWithoutMessages(options.CaseId);
					}
#else
					DomainModel.Message messageEnqueuedOrAssigned = null;
					if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway &&
						Core.System.Instance.QueueService.AreCaseMessagesEnqueuedOrAssigned(options.CaseId, null, out DomainModel.Case @case, out messageEnqueuedOrAssigned))
					{
						options.CloseCase = false;
						options.Case = @case;

						if (!options.Service.Settings.ActAsChat && !DomainModel.SystemSettings.Instance.Cases.AllowToAddMessagesToCasesWithMessagesInQueue && !options.IsTransferMessage)
						{
							Tracer.TraceInfo("Se intentó agregar un mensaje al caso {0} que tiene mensajes en cola", @case.ID);
							throw new Exception("El sistema no permite agregar mensajes a un caso con mensajes en cola");
						}
					}
					else
					{
						if (options.Case == null ||
							!options.Case.RetrievedFromDb)
						{
							options.Case = DAL.CaseDAO.GetOneWithoutMessages(options.CaseId);
							caseInvokedWithoutMessages = true;
						}
					}
#endif

					if (options.Case == null)
						throw new ArgumentException("El caso no existe", nameof(options.CaseId));
					if (options.Case.Status == CaseStatuses.Closed)
						throw new ArgumentException("El caso está cerrado", nameof(options.CaseId));

					if (options.Parameters == null)
						options.Parameters = new Dictionary<string, string>();
					//options.Parameters[DomainModel.Message.SentByMyCasesParameter] = true.ToString();

					DomainModel.Message lastIncomingMessage = null;
					bool messageIsPrivate = true;
					int messsagesPublicReply = 0;
					int messsagesPublicNotReply = 0;
					if (options.Case.Messages != null)
					{
						lastIncomingMessage = options.Case.Messages.SingleOrDefault(m => m.ID == options.Case.LastIncomingMessageID.Value);

						foreach (var message in options.Case.Messages)
						{
							if (message.IsDirectMessage)
							{
								if (message.IsReply)
								{
									messsagesPublicReply++;
								}
								else
								{
									messsagesPublicNotReply++;
								}
							}
						}
					}

					if (lastIncomingMessage == null && options.Case.LastIncomingMessageID.HasValue)
						lastIncomingMessage = DAL.MessageDAO.GetOne(options.Case.LastIncomingMessageID.Value, new MessageDAO.RelatedEntitiesToRead(false), false);

					if (Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway &&
						lastIncomingMessage != null &&
						!lastIncomingMessage.IsReply &&
						!lastIncomingMessage.IsDirectMessage)
					{
						messageIsPrivate = false;
					}

					var insertedMessageID = await MessageDAO.AddMessageToCaseAsync(options.Case,
						options.Person,
						options.Service,
						options.Text,
						options.SocialUserKey,
						options.CloseCase,
						options.Attachments,
						options.Parameters,
						options.SocialServiceType,
						messageIsPrivate,
						options.ReplySource,
						options.UpadateCaseInterval,
						options.AssignedMessage?.Queue,
						now);

					if (options.Case != null)
					{
						options.Case.LastPerson = options.Person ?? options.Case.LastPerson;
						options.Case.LastService = options.Service;


						if (options.Person != null && options.Person.Type == PersonTypes.Agent)
						{
							if (options.Case.FirstAgentReply == null)
							{
								options.Case.FirstAgentReply = (Agent) options.Person;
							}

							if (!options.Case.Parameters.ContainsKey(Case.FirstReplyByAgentDateParameter))
							{
								options.Case.Parameters[Case.FirstReplyByAgentDateParameter] = now.ToString("o");
							}

							options.Case.Parameters[Case.LastReplyByAgentDateParameter] = now.ToString("o");
							options.Case.LastAgentReply = (Agent) options.Person;
						}
						
						options.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
						options.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageID.ToString();
						options.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();
						options.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) options.SocialServiceType).ToString();
						options.Case.Parameters[DomainModel.Case.HasPrivateAnswerParameter] = true.ToString();

						if (options.Case.Parameters != null && options.Case.Messages != null && (options.Case.Replies == 0 || options.Case.RepliesByAgents == 0))
						{
							try
							{
								if (lastIncomingMessage.LastSegment.AssignedDate.HasValue)
								{
									if (options.Person != null && options.Person.Type == DomainModel.PersonTypes.Agent)
									{
										MetricsAdapter.CalculateFirstMessageReplyByAgentTimeParameter(options.Case.Parameters, now, lastIncomingMessage.LastSegment.AssignedDate.Value, out bool mustUpdateParameters);
									}

									MetricsAdapter.CalculateTotalFirstReplyTimeParameter(options.Case.Parameters, now, lastIncomingMessage.LastSegment.AssignedDate.Value);
								}
							}

							catch (Exception ex)
							{
								Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
							}
						}

						if (options.Person != null && options.Person.Type == PersonTypes.Agent)
						{
							if (!options.Case.Parameters.ContainsKey(DomainModel.Case.FirstRepliedMessageByAgentParameter))
							{
								if (options.Case.LastIncomingMessageID != null)
								{
									options.Case.Parameters[DomainModel.Case.FirstRepliedMessageByAgentParameter] = options.Case.LastIncomingMessageID.Value.ToString();

									if (options.Case.Messages != null)
										lastIncomingMessage = options.Case.Messages.SingleOrDefault(m => m.ID == options.Case.LastIncomingMessageID.Value);

									if (lastIncomingMessage == null)
										lastIncomingMessage = DAL.MessageDAO.GetOne(options.Case.LastIncomingMessageID.Value, new MessageDAO.RelatedEntitiesToRead(false), false);

									if (lastIncomingMessage != null)
									{
										var time = DateTime.Now.Subtract(lastIncomingMessage.InsertedDate);
										options.Case.Parameters[DomainModel.Case.FirstRepliedMessageByAgentWorkingTimeParameter] = time.TotalSeconds.ToString(global::System.Globalization.CultureInfo.InvariantCulture);
									}
								}

								if (!options.Case.Parameters.ContainsKey(DomainModel.Case.TimeFirstMessageInQueueParameter))
								{
									if (caseInvokedWithoutMessages)
									{
										options.Case.Messages = MessageDAO.GetAllByCase(options.Case, false);
									}
									UpdateDelayAndWaitingTimesOnCaseParameters(options.Case.Messages.Where(m => m.Queue != null), options.Case.Parameters, now);
								}
							}
						}

						if (options.UpdateCaseParameters != null)
							options.UpdateCaseParameters(options.Case);

						DAL.CaseDAO.UpdateParameters(options.Case);
					}

					bool wasAttended = false;
#if !NETCOREAPP
					if (options.AssignedMessage != null)
					{
						if (options.AssignedMessage.LastSegment != null)
						{
							if (options.AssignedMessage.LastSegment.FinishedReadDate == null)
								this.FinishedRead(options.AssignedMessage, options.AssignedMessage.AssignedTo);

							// Quitamos la marca de inactivo en el agente, porque acaba de agregar un mensaje al caso
							options.AssignedMessage.LastSegment.InactiveByAgent = false;
							options.AssignedMessage.LastSegment.InactiveByAgentDate = null;
						}

						if (options.AssignedMessage.Service != null &&
							options.AssignedMessage.Service.Settings.ActAsChat &&
							!options.AssignedMessage.Parameters.ContainsKey(DomainModel.Message.AttendedParameter))
						{
							options.AssignedMessage.Parameters[DomainModel.Message.AttendedParameter] = true.ToString();
							options.AssignedMessage.Parameters[DomainModel.Message.AttendedAtParameter] = DateTime.Now.ToString("o");
							if (options.AssignedMessage.LastSegment != null &&
								options.AssignedMessage.LastSegment.AssignedDate != null)
							{
								options.AssignedMessage.Parameters[DomainModel.Message.AttendedTimeParameter] = now.Subtract(options.AssignedMessage.LastSegment.AssignedDate.Value).TotalSeconds.ToString("R", global::System.Globalization.CultureInfo.InvariantCulture);
							}
							DAL.MessageDAO.UpdateParameters(options.AssignedMessage);

							if (options.AssignedMessage.Queue != null)
							{
								options.AssignedMessage.RepliedDate = now;
								var queueToGroup = Core.System.Instance.QueueService.GetQueueById(options.AssignedMessage.Queue.ID);
								queueToGroup.ReplyAsChat(options.AssignedMessage, now, options.Person);
							}

							wasAttended = true;
							DAL.MessageDAO.UpdateParameters(options.AssignedMessage);
						}
					}
#endif

					options.Case.Replies++;
					options.Case.RepliesByAgents += (options.Person != null && options.Person.Type == PersonTypes.Agent) ? (short) 1 : (short) 0;
					options.Case.RepliesByUsers += (options.Person != null && options.Person.Type == PersonTypes.User) ? (short) 1 : (short) 0;

					DomainModel.Historical.Daily info;
					DomainModel.Historical.DailyService infoService;

					if (options.Person != null)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = options.Person.ID;
						info.MyOutgoingCases = 1;
						if (wasAttended)
						{
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						if (wasAttended && options.AssignedMessage.Queue != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = options.AssignedMessage.Queue.ID;
							info.PersonID = options.Person.ID;
							info.MyOutgoingCases = 1;
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = options.AssignedMessage.Queue.ID;
							info.PersonID = 0;
							info.MyOutgoingCases = 1;
							info.ReplyTime = options.AssignedMessage.ReplyDelay.Value;
							info.AttendedMessagesByAgent = 1;
							info.MessagesAttendedOutOfSL = (options.AssignedMessage.OutOfServiceLevel == true) ? 1 : 0;
							info.MessagesAttendedOutOfSLL = (options.AssignedMessage.OutOfServiceLevel == true && options.AssignedMessage.OutOfSLL == true) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = options.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = options.Person.ID;
						infoService.MyOutgoingCases = 1;
						infoService.AttendedMessagesByAgent = wasAttended ? 1 : 0;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = options.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = 0;
						infoService.MyOutgoingCases = 1;
						infoService.AttendedMessagesByAgent = wasAttended ? 1 : 0;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						if (wasAttended && options.AssignedMessage.Queue != null)
						{
							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = options.Service.ID;
							infoService.QueueID = options.AssignedMessage.Queue.ID;
							infoService.PersonID = options.Person.ID;
							infoService.MyOutgoingCases = 1;
							infoService.AttendedMessagesByAgent = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = options.Service.ID;
							infoService.QueueID = options.AssignedMessage.Queue.ID;
							infoService.PersonID = 0;
							infoService.MyOutgoingCases = 1;
							infoService.AttendedMessagesByAgent = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						}

#if !NETCOREAPP
						if (options.AssignedMessage != null &&
							options.AssignedMessage.Queue != null &&
							options.Parameters != null &&
							options.Parameters.TryGetValue("CallInvite", out var callInvite) &&
							bool.TryParse(callInvite, out bool hasCallInvite) && hasCallInvite)
						{
							var queue = System.Instance.QueueService.GetQueueById(options.AssignedMessage.Queue.ID);
							queue.RealTimeInfo.InvitedCalls++;
						}
#endif
					}

					if (options.CloseCase)
					{
						var caseClosedBy = CaseClosingResponsibles.Filter;
						if (options.Person != null)
							caseClosedBy = options.Person.Type == PersonTypes.Agent ? CaseClosingResponsibles.Agent : CaseClosingResponsibles.Supervisor;

						await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
						{
							Case = options.Case,
							ClosedBy = caseClosedBy,
							Person = options.Person,
							Interval = interval,
							StoreInDatabase = false,
							Queue = null,
							Message = null
						});

#if !NETCOREAPP
						Core.System.Instance.CasesService.RemovePendingCase(options.Case, options.Person);
#endif
					}

					var insertedMessage = await Reply(insertedMessageID, options.Case, sendInBackground: !options.Service.SocialService.SupportsPublishToServiceBus(), lastIncomingMessage);
					Tracer.TraceInfo("Se agregó el mensaje {0} al caso {1}", insertedMessageID, options.CaseId);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(insertedMessage);
					}

					return insertedMessageID;
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se agregaba un mensaje al caso {0}: {1}", options.CaseId, ex);
				throw;
			}
		}

		#endregion
	}
}