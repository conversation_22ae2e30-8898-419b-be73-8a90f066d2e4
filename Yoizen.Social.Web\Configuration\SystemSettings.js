/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />

var $divLoading;
var $tabsSystemSettings;
var $hiddenTab;
var $divBehavior;
var $textboxChatPackageFile;
var $checkboxAllowAgentsToReturnMessagesToQueue;
var $checkboxAgentMustEnterReturnToQueueReason;
var $textboxMaximumNumberOfTimesMessageCanBeReturned;
var $checkboxAllowAgentsToSelectQueueOnReturnToQueue;
var $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue;
var $divSecurityPolitics
var $textboxWebAgentURL;
var $textboxWhatsappUrlRtNotifications;
var $textboxTelegramUrlRtNotifications;

var $listboxAlertMessagesDeliveryFailedVia;
var $trAlertMessagesDeliveryFailedFromServices;
var $listboxAlertMessagesDeliveryFailedFromServices;
var $divAlertMessagesDeliveryFailedViaMail;
var $textboxAlertMessagesDeliveryFailedViaMailEmailSubject;
var $textboxAlertMessagesDeliveryFailedViaMailEmailTemplate;

var $divCases;
var $checkboxCheckLastQueueOfOpenCase;
var $checkboxIgnoreLastQueueForSLMovedMessage;
var $textboxMaxElapsedMinutesToCloseCases;
var $textboxAutoReplyInCloseCaseText;
var $listboxTagsForHsmClose;
var $listboxTags;
var $caseTags;
var $textboxAttachmentsRoute;
var $divEmails;
var oldWebFormOnSubmit;
var $divGlobalConfig;
var $divLdap;
var $tableLdap;
var $checkboxLdapUseLdap;
var $textboxLdapServer;
var $textboxLdapPort;
var $textboxLdapSearchDN;
var $textboxLdapUser;
var $checkboxLdapPassword;
var $labelLdapPassword;
var $textboxLdapPassword;
var $textboxLdapUserSearchFilter;
var $divLdapTestFailed;
var $divLdapTestButtons;
var $divGlobalConfigTestFailed;
var $divBitLy;
var $tableBitLy;
var $checkboxBitLyCustomShortener;
var $trAvailableDomains;
var $checkboxForwardOutsideDomainAvailable;
var $divAllowForwardAction;
var $checkboxAllowForwardAction;
var $trsAllowForwardAction;
var $textboxMailMaskBody;
var $tableGenerateDailyReport;
var $checkboxGenerateDailyReport;
var $trDailyReportFtpCredentials;
var $trsGenerateDailyReport;
var $checkboxEnableFtpDailyReport;
var $listboxFtps;
var $divYFlow;
var $checkboxEnableYFlow;
var $textboxYFlowUrl;
var $textboxYFlowUrlApi;
var $textboxYFlowUrlWeb;
var $textboxYFlowUsername;
var $checkboxYFlowPassword;
var $textboxYFlowPassword;
var $textboxYFlowTimeout;
var $textboxYFlowAuthenticationFailedSubject;
var $textboxYFlowAuthenticationFailedEmails;
var $textboxYFlowAuthenticationFailedTemplate;
var $textboxYFlowInvokeFailedSubject;
var $textboxYFlowInvokeFailedEmails;
var $textboxYFlowInvokeFailedTemplate;
var $checkboxEnableSurveys;
var $checkboxUseCognitiveServices;
var $textboxCognitiveServicesToken;
var $textboxCognitiveServicesTokenSecret;
var $trUseCognitiveServices;
var $checkboxLDAPUseConfigurationParams;
var $textboxLDAPConfigurationFirstName;
var $textboxLDAPConfigurationLastName;
var $textboxLDAPConfigurationUserName;
var $textboxLDAPConfigurationEmail;
var $textboxLDAPConfigurationLDAP;
var $textboxBusinessDataRegex;
var $divBusinessDataRegexVisualizer;
var $divUserRegexVisualizer;
var $divAgentRegexVisualizer;
var $checkboxCheckLastQueueByTime;
var $textboxLastQueueByTime;
var $tableLastQueueByTime;

var $tableAnnoyingUser;
var $checkboxUseAnnoyingUser;
var $trsAnnoyingUser;
var $textboxMaxMessagesAnnoyingUser;
var $textboxAnnoyingUserEmailSubject;
var $textboxAnnoyingUserEmails;
var $textboxAnnoyingUserEmailTemplate;
var $checkboxAddAnnoyingUserToBlackList;

var $panelWebAgentConfigurationAllowUrlLogin;

var $checkboxWebAgentConfigurationAllowUrlLogin;
var $checkboxWebAgentConfigurationPasswordRequired;
var $checkboxWebAgentConfigurationRemoveLoginForm;
var $checkboxWebAgentConfigurationRemoveLogoutButton;

var $trsWebAgentConfiguration;
var $trWebAgentConfigurationPasswordParameter;
var $trWebAgentConfigurationKeyToDecryptPassword;
var $trWebAgentConfigurationRedirectUrl;
var $trWebAgentConfigurationLogoutMessage;
var $messageWebAgentConfigurationAllowUrlLoginCloud;

var $textboxWebAgentConfigurationUserNameLoginParameter;
var $textboxWebAgentConfigurationPasswordParameter;
var $textboxWebAgentConfigurationKeyToDecryptPassword;
var $textboxWebAgentConfigurationHashParameter;
var $textboxWebAgentConfigurationKeyToHash;
var $textboxWebAgentConfigurationRedirectUrl;
var $textboxWebAgentConfigurationLogoutMessage;
var $dropdownlistLogoutAction;

/*variables de manejo de estado en el agente web*/
var $panelWebAgentConfigurationStateManagement;
var $checkboxWebAgentConfigurationAllowChangeState;
var $checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived;
var $checkboxWebAgentConfigurationAllowAgentsToChangeState;
var $checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging;
var $checkboxWebAgentConfigurationAllowLogoutInvoke;
var $checkboxWebAgentConfigurationShowMessageAfterLogoutReceived;
var $checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging;
var $checkboxWebAgentConfigurationIgnoreLogoutAfterError;
var $textboxWebAgentConfigurationTargetOrigin;
var $textboxWebAgentConfigurationShowMessageAfterLogoutReceived;
var $textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived;
var $textboxWebAgentConfigurationLogoutInvokeTargetOrigin
var $textboxWebAgentConfigurationChangeStateTargetOrigin;

var $trsWebAgentConfigurationAllowStateManagementByInternalMessaging;
var $trsWebAgentConfigurationAllowChangeState;
var $trsWebAgentConfigurationAllowLogoutInvocation;
var $trWebAgentConfigurationIgnoreLogoutAfterError;
var $trWebAgentConfigurationShowMessageAfterLogoutReceived;
var $trWebAgentConfigurationShowMessageAfterChangeStateReceived;
var $trWebAgentConfigurationAllowAgentsToChangeState;
var $divWebAgentConfigurationStateManagement;
var $divWebAgentConfigurationLogoutInvoke;
var $dropdownlistTagCasesOnClose;

var $aExtendedProfileFieldsAdd;
var $tableExtendedProfileFields;
var $hiddenExtendedProfileFields;

var $aExtendedProfileBusinessCodeFieldsAdd;
var $tableExtendedProfileBusinessCodeFields;
var $hiddenExtendedProfileBusinessCodeFields;

var $aExtendedCaseFieldsAdd;
var $tableExtendedCaseFields;
var $hiddenExtendedCaseFields;

var $dropdownlistAuthenticationType;
var $divGoogleAuth;
var $checkboxGoogleAuthEnabled;
var $checkboxGoogleAuthUseCustom;
var $trGoogleAuthClientID;
var $textboxGoogleAuthClientID;
var $trGoogleAuthClientSecret;
var $textboxGoogleAuthClientSecret;

var $divLoginWithKeycloak;
var $checkboxKeycloakAuthEnabled;

var $checkboxUseACDBalancing;
var $trACDBalancingWithQueueLevels;
var $trACDBalancingWithQueueLevelsAndWorking;
var $$dropdownlistACDBalancingWithQueueLevels;

var $dropdownlistChatCloudSameServer;
var $trChatCloudOtherServer;
var $textboxChatCloudOtherServer;
var $dropdownlisCloudOtherServerPortYSocialOverHttps;

var checkboxUserPasswordRepeat;
var textboxUserPasswordRepeat;
var trUserPasswordRepeat;
var checkboxAgentPasswordRepeat;
var textboxAgentPasswordRepeat;
var trAgentPasswordRepeat;

var $panelTimeZoneConfiguration;
var $hiddenDefaultTimeZone;
var $selectDefaultTimeZone;
var $divTimeZonesForConsolidation;
var $hiddenTimeZonesToConsolide;

var $spanAccessTokenError;
var $spanAccessTokenOk;
var $spanAccessTokenContingencyError;
var $spanAccessTokenContingencyOk;

var $textboxTagOnHsmCases;
var $textboxTagCloseCase;
var $textboxTagInvocation;
var textboxTagInvocationTimeOut;

var $checkboxEnableYUsage;
var $textboxYUsageUrl;
var $buttonGenerateYUsageToken;
var $spanYUsageTokenOk;
var $spanYUsageTokenError;

var $divYFlowPendingMessagesCallbackEndpoint;

// "pages_manage_engagement", "pages_manage_posts", "pages_read_engagement", "pages_read_user_content", "pages_show_list", "pages_messaging", "pages_manage_metadata"
var facebookPermissions = [
    {
        key: "pages_manage_engagement",
        required: true,
        description: "Permite suscribir a la página para recibir webhooks acerca de la actividad de una página"
    },
    {
        key: "pages_read_engagement",
        required: true,
        description: "La aplicación leer contenido de publicaciones."
    },
    {
        key: "pages_manage_posts",
        required: false,
        description: "Permite manejar las publicaciones"
    },
    {
        key: "pages_read_user_content",
        required: false,
        description: "Permite leer contenido generado por los usuarios"
    },
    {
        key: "pages_show_list",
        required: true,
        description: "Permite el acceso para mostrar la lista de las páginas que administras."
    },
    {
        key: "pages_messaging",
        required: false,
        description: "Te permite enviar y recibir mensajes a través de una página de Facebook. No se puede usar este permiso para enviar contenido de promociones o anuncios. Las conversaciones a través de esta API solo pueden comenzar cuando alguien indica (ya sea a través del plugin de Messenger o mediante el envío de un mensaje directamente a ti) que quiere recibir mensajes tuyos."
    },
    {
        key: "pages_manage_metadata",
        required: false,
        description: "Permite suscribir a la página a eventos del webhooks relacionadas a actividad de la página."
    }
];

var facebookMessengerPermissions = [
    {
        key: "manage_pages",
        required: true,
        description: "Permite a tu aplicación obtener los tokens de acceso de página para las páginas y aplicaciones administradas por una persona. Una aplicación necesita los permisos manage_pages y publish_pages para poder publicar como una página."
    },
    {
        key: "pages_messaging",
        required: true,
        description: "Te permite enviar y recibir mensajes a través de una página de Facebook. No se puede usar este permiso para enviar contenido de promociones o anuncios. Las conversaciones a través de esta API solo pueden comenzar cuando alguien indica (ya sea a través del plugin de Messenger o mediante el envío de un mensaje directamente a ti) que quiere recibir mensajes tuyos."
    },
    {
        key: "pages_show_list",
        required: true,
        description: "Permite el acceso para mostrar la lista de las páginas que administras."
    }
];

function getEnumValue(enumObj, key) {
    return enumObj[key] ?? null;
}
function getEnumKeyByValue(enumObj, value) {
    return Object.keys(enumObj).find(key => enumObj[key] === value) || null;
}

if (typeof window.YFlowInstances === 'undefined') {
    window.YFlowInstances = Object.freeze({
        YFlow: 0,
        YFlowContingency: 1
    });
}

var YflowInstance = getEnumValue(YFlowInstances, 'YFlow'); 

var $hiddenCloudHeadersToAdd;
var $divCloudHeadersToAdd;
var $hiddenCloudHeadersToRemove;
var $divCloudHeadersToRemove;

jQuery(document).ready(function () {
    $checkboxUserPasswordRepeat = $('#checkboxUserPasswordRepeat');
    $textboxUserPasswordRepeat = $('#textboxUserPasswordRepeat');
    $trUserPasswordRepeat = $('#trUserPasswordRepeat');

    $checkboxAgentPasswordRepeat = $('#checkboxAgentPasswordRepeat');
    $textboxAgentPasswordRepeat = $('#textboxAgentPasswordRepeat');
    $trAgentPasswordRepeat = $('#trAgentPasswordRepeat');

    $spanAccessTokenError = $('#spanAccessTokenError');
    $spanAccessTokenOk = $('#spanAccessTokenOk');
    $spanAccessTokenContingencyError = $('#spanAccessTokenContingencyError');
    $spanAccessTokenContingencyOk = $('#spanAccessTokenContingencyOk');

    $listboxFtps = $('#listboxFtps');
    $listboxFtps.multiselect({ multiple: false, noneSelectedText: "Seleccionar", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();

    if ($checkboxUserPasswordRepeat[0].checked) {
        $trUserPasswordRepeat.hide();
    }

    if ($checkboxAgentPasswordRepeat[0].checked) {
        $trAgentPasswordRepeat.hide();
    }

    $checkboxAgentPasswordRepeat.change(function () {
        if (this.checked) {
            $textboxAgentPasswordRepeat.val('0');
            $trAgentPasswordRepeat.hide();
        } else {
            $trAgentPasswordRepeat.show();
        }
    });

    $checkboxUserPasswordRepeat.change(function () {
        if (this.checked) {
            $textboxUserPasswordRepeat.val('0');
            $trUserPasswordRepeat.hide();
        } else {
            $trUserPasswordRepeat.show();
        }
    });

    if (!inTheCloud) {
        $('#trUserCaptcha').hide();
        $('#trAgentCaptcha').hide();
    }

    $hiddenTab = $('input[type=hidden][id$=hiddenTab]');

    $divLoading = $('#divLoading');

    $('#buttonLdapTestConfig').click(TestLdapConfig);

    $divBehavior = $('#divBehavior');
    $checkboxAllowAgentsToReturnMessagesToQueue = $('input[type=checkbox][id$=checkboxAllowAgentsToReturnMessagesToQueue]', $divBehavior);
    $checkboxAgentMustEnterReturnToQueueReason = $('input[type=checkbox][id$=checkboxAgentMustEnterReturnToQueueReason]', $divBehavior);
    $textboxMaximumNumberOfTimesMessageCanBeReturned = $('input[id$=textboxMaximumNumberOfTimesMessageCanBeReturned]', $divBehavior);
    $checkboxAllowAgentsToSelectQueueOnReturnToQueue = $('input[type=checkbox][id$=checkboxAllowAgentsToSelectQueueOnReturnToQueue]', $divBehavior);
    $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue = $('input[type=checkbox][id$=checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue]', $divBehavior);

    $divSecurityPolitics = $('#divSecurityPolitics');
    $textbox8 = $('input[id$=textbox8]', $divSecurityPolitics);

    $divCases = $('#divCases');
    $textboxMaxElapsedMinutesToCloseCases = $('input[id$=textboxMaxElapsedMinutesToCloseCases]', $divCases);
    $checkboxCheckLastQueueOfOpenCase = $('input[type=checkbox][id$=checkboxCheckLastQueueOfOpenCase]', $divCases);
    $checkboxIgnoreLastQueueForSLMovedMessage = $('input[type=checkbox][id$=checkboxIgnoreLastQueueForSLMovedMessage]', $divCases);
    $checkboxReplyInCloseCase = $('input[type=checkbox][id$=checkboxReplyInCloseCase]', $divCases);
    $textboxAutoReplyInCloseCaseText = $('#textboxAutoReplyInCloseCaseText', $divCases);

    let $dropdownlistYFlowActionAfterMaxMinutesForPendingMessages = $('#dropdownlistYFlowActionAfterMaxMinutesForPendingMessages');
    $dropdownlistYFlowActionAfterMaxMinutesForPendingMessages.multiselect({ multiple: false, selectedList: 1 });

    let $dropdownlistYFlowPendingMessagesUseCustomCallback = $('#dropdownlistYFlowPendingMessagesUseCustomCallback');
    let $trFlowPendingMessagesCustomCallbackUrl = $('#trFlowPendingMessagesCustomCallbackUrl');
    $divYFlowPendingMessagesCallbackEndpoint = $('#divYFlowPendingMessagesCallbackEndpoint');
    BuildDynamicHttpRequestInfo($divYFlowPendingMessagesCallbackEndpoint, {
        canEdit: true,
        canRemove: true,
        onEdit: function () {
            ShowEditHttpRequest('yflow_pending_messages_callback', $divYFlowPendingMessagesCallbackEndpoint);
        },
		onRemove: function () {
            $divYFlowPendingMessagesCallbackEndpoint.setInfo(null);
        }
    });

    $dropdownlistYFlowPendingMessagesUseCustomCallback.change(function () {
        $trFlowPendingMessagesCustomCallbackUrl.toggle($dropdownlistYFlowPendingMessagesUseCustomCallback.val() === '1');
    }).trigger('change');

    let $textboxYFlowPendingMessagesCustomCallbackUrl = $('#textboxYFlowPendingMessagesCustomCallbackUrl');
    $textboxYFlowPendingMessagesCustomCallbackUrl.blur(function () {
        var $this = $(this);
        var $parentDiv = $this.parent();
        var url = $this.val();

        if (url.length > 0) {
            var dataToSend = JSON.stringify({ url: url });

            $parentDiv.removeClass('ok');
            $parentDiv.removeClass('error');
            $parentDiv.addClass('spin');

            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/IsYFlowPendingMessagesCallbackUrlValid",
                data: dataToSend,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    $parentDiv.removeClass('spin');
                    if (data.d.Success && data.d.IsValid) {
                        $parentDiv.addClass('ok');
                        $('.more-info', $parentDiv).text(data.d.Version);
                    }
                    else {
                        $parentDiv.addClass('error');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $parentDiv.removeClass('spin');
                    $parentDiv.addClass('error');
                }
            });
        }
    }).trigger('blur');

    let $listboxActionsTags = $("#listboxActionsTags");
    $checkboxReplyInCloseCase.change(function () {
        $textboxAutoReplyInCloseCaseText.toggle(this.checked);
        $listboxActionsTags.toggle(this.checked);
    }).trigger('change');

    var $dropdownlistTagCasesOnClose = $('select[ID$=dropdownlistTagCasesOnClose]');
    var loadImportant = $dropdownlistTagCasesOnClose.val();
    var $trImportantTag = $('#importantTag', $divCases);
    $trImportantTag.toggle(loadImportant === '1' || loadImportant === '2');

    $dropdownlistTagCasesOnClose.change(function () {
        var value = $dropdownlistTagCasesOnClose.val();
        if (value === '1' || value === '2') {
            $trImportantTag.toggle(true);
        }
        else {
            $trImportantTag.toggle(false);
        }
    });

    var $checkboxTagOutgoing = $('input[type=checkbox][id$=checkboxTagOutgoing]', $divCases);
    var $trImportantTagOutgoing = $('#importantTagOutgoing', $divCases);

    $checkboxTagOutgoing.change(function () {
        $trImportantTagOutgoing.toggle(this.checked);
    }).trigger('change');

    $divAllowForwardAction = $('#divAllowForwardAction', $divBehavior);
    $checkboxAllowForwardAction = $('input[type=checkbox][id$=checkboxAllowForwardAction]', $divAllowForwardAction);
    $trsAllowForwardAction = $('tr[rel=trAllowForwardAction]', $divAllowForwardAction);
    $checkboxForwardOutsideDomainAvailable = $('input[type=checkbox][id$=checkboxForwardOutsideDomainAvailable]', $divBehavior);
    $trAvailableDomains = $('#trAvailableDomains', $divAllowForwardAction);
    $textboxMailMaskBody = $('textarea[id$=textboxMailMaskBody]', $divAllowForwardAction);

    // --> Esconder o mostrar dominios habilitados para cc/cco dependiendo si está habilitado
    $checkboxForwardOutsideDomainAvailable.change(function () {
        if (this.checked || !$checkboxForwardOutsideDomainAvailable.is(':visible'))
            $trAvailableDomains.hide();
        else
            $trAvailableDomains.show();
    }).trigger('change');

    $checkboxAllowForwardAction.change(function () {
        $trsAllowForwardAction.toggle(this.checked);
        $checkboxForwardOutsideDomainAvailable.trigger('change');

        if (this.checked) {
            var editors = $textboxMailMaskBody.cleditor();
            if (editors.length > 0) {
                editors[0].refresh();
            }
        }
    }).trigger('change');

    $textboxAttachmentsRoute = $('input[id$=textboxAttachmentsRoute]', $divBehavior);
    $textboxChatPackageFile = $('input[id$=textboxChatPackageFile]');

    $checkboxAllowAgentsToReturnMessagesToQueue.change(function () {
        if ($checkboxAllowAgentsToReturnMessagesToQueue.is(':checked')) {
            $checkboxAgentMustEnterReturnToQueueReason.removeAttr('disabled');
            $textboxMaximumNumberOfTimesMessageCanBeReturned.removeAttr('disabled');
            $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.removeAttr('disabled');
        }
        else {
            $checkboxAgentMustEnterReturnToQueueReason.attr('disabled', 'disabled');
            $textboxMaximumNumberOfTimesMessageCanBeReturned.attr('disabled', 'disabled');
            if (!$checkboxAllowAgentsToSelectQueueOnReturnToQueue.is(':checked')) {
                $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.attr('disabled', 'disabled');
            }
        }
    }).trigger('change');

    $checkboxAllowAgentsToSelectQueueOnReturnToQueue.change(function () {
        if (!$checkboxAllowAgentsToSelectQueueOnReturnToQueue.is(':checked')) {
            $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.removeAttr('disabled');
        }
        else {
            if (!$checkboxAllowAgentsToReturnMessagesToQueue.is(':checked')) {
                $checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue.attr('disabled', 'disabled');
            }
        }
    }).trigger('change');

    $checkboxEnableSurveys = $('#checkboxEnableSurveys');

    $divGlobalConfig = $('#divGlobalConfig');

    $divEmails = $('#divEmails', $divGlobalConfig);

    $divLdap = $('#divLdap', $divSecurityPolitics);
    $tableLdap = $('#tableLdap', $divLdap);
    $checkboxLdapUseLdap = $('#checkboxLdapUseLdap', $tableLdap);
    $textboxLdapServer = $('#textboxLdapServer', $tableLdap);
    $textboxLdapPort = $('#textboxLdapPort', $tableLdap);
    $textboxLdapSearchDN = $('#textboxLdapSearchDN', $tableLdap);
    $textboxLdapUser = $('#textboxLdapUser', $tableLdap);
    $checkboxLdapPassword = $('#checkboxLdapPassword', $tableLdap);
    $labelLdapPassword = $('#labelLdapPassword', $tableLdap);
    $textboxLdapPassword = $('#textboxLdapPassword', $tableLdap);
    $textboxLdapUserSearchFilter = $('#textboxLdapUserSearchFilter', $tableLdap);
    $divLdapTestFailed = $('#divLdapTestFailed', $divLdap);
    $divLdapTestButtons = $('#divLdapTestButtons', $divLdap);

    $checkboxLDAPUseConfigurationParams = $('#checkboxLDAPUseConfigurationParams');
    $trLDAPUseConfigurationParams = $("#trLDAPUseConfigurationParams");

    $checkboxLDAPUseConfigurationParams.change(function () {
        $trLDAPUseConfigurationParams.toggle($checkboxLDAPUseConfigurationParams.is(':checked'));
    }).trigger('change');

    $textboxLDAPConfigurationFirstName = $("#textboxLDAPConfigurationFirstName");
    $textboxLDAPConfigurationLastName = $("#textboxLDAPConfigurationLastName");
    $textboxLDAPConfigurationUserName = $("#textboxLDAPConfigurationUserName");
    $textboxLDAPConfigurationEmail = $("#textboxLDAPConfigurationEmail");
    $textboxLDAPConfigurationLDAP = $("#textboxLDAPConfigurationLDAP");

    $checkboxLdapUseLdap.change(function () {
        var $trs = $('tr[rel=useldap]', $tableLdap);
        var $inputs = $('input', $trs).not('#checkboxLdapPassword');

        if (this.checked) {
            $inputs.removeAttr('disabled');
            $divLdapTestButtons.show();
            $trs.show();
            $trLDAPUseConfigurationParams.toggle($checkboxLDAPUseConfigurationParams.is(':checked'));
        }
        else {
            $inputs.attr('disabled', 'disabled');
            $divLdapTestFailed.hide();
            $divLdapTestButtons.hide();
            $trs.hide();
            $trLDAPUseConfigurationParams.toggle(this.checked);
        }
    }).trigger('change');

    $checkboxLdapPassword.change(function () {
        if (this.checked) {
            $textboxLdapPassword.removeAttr('disabled');
        }
        else {
            $textboxLdapPassword.attr('disabled', 'disabled');
            $textboxLdapPassword.val('');
        }
    }).trigger('change');

    $textboxBusinessDataRegex = $('#textboxBusinessDataRegex');
    $divBusinessDataRegexVisualizer = $('#divBusinessDataRegexVisualizer');
    var $divBusinessDataRegexVisualizerContents = $('div.contents', $divBusinessDataRegexVisualizer);

    $textboxBusinessDataRegex.change(function () {
        var val = $textboxBusinessDataRegex.val();
        if (val !== null && val.length > 0) {
            $divBusinessDataRegexVisualizer.hide();
            $divBusinessDataRegexVisualizerContents.empty();

            var url = 'https://jex.im/regulex/#!cmd=export&flags=&re=' + encodeURI(val);
            var $iframe = $('<iframe frameborder="0" width="100%" height="307"></iframe>');
            $divBusinessDataRegexVisualizerContents.append($iframe);
            $iframe.attr('src', url);
            $iframe.on('load', function () {
                $divBusinessDataRegexVisualizer.show();
            });
        }
    }).trigger('change');

    $textboxUserRegex = $('#textboxUserRegex');
    $divUserRegexVisualizer = $('#divUserRegexVisualizer');
    var $divUserRegexVisualizerContents = $('div.contents', $divUserRegexVisualizer);

    $textboxUserRegex.change(function () {
        var val = $textboxUserRegex.val();
        if (val !== null && val.length > 0) {
            $divUserRegexVisualizer.hide();
            $divUserRegexVisualizerContents.empty();

            var url = 'https://jex.im/regulex/#!cmd=export&flags=&re=' + encodeURI(val);
            var $iframe = $('<iframe frameborder="0" width="100%" height="307"></iframe>');
            $divUserRegexVisualizerContents.append($iframe);
            $iframe.attr('src', url);
            $iframe.on('load', function () {
                $divUserRegexVisualizer.show();
            });
        }
    }).trigger('change');

    $textboxAgentRegex = $('#textboxAgentRegex');
    $divAgentRegexVisualizer = $('#divAgentRegexVisualizer');
    var $divAgentRegexVisualizerContents = $('div.contents', $divAgentRegexVisualizer);

    $textboxAgentRegex.change(function () {
        var val = $textboxAgentRegex.val();
        if (val !== null && val.length > 0) {
            $divAgentRegexVisualizer.hide();
            $divAgentRegexVisualizerContents.empty();

            var url = 'https://jex.im/regulex/#!cmd=export&flags=&re=' + encodeURI(val);
            var $iframe = $('<iframe frameborder="0" width="100%" height="307"></iframe>');
            $divAgentRegexVisualizerContents.append($iframe);
            $iframe.attr('src', url);
            $iframe.on('load', function () {
                $divAgentRegexVisualizer.show();
            });
        }
    }).trigger('change');

    /*Seteo de varaibles de AnnoyingUser*/
    $tableAnnoyingUser = $('#tableAnnoyingUser');
    $checkboxUseAnnoyingUser = $("#checkboxUseAnnoyingUser", $tableAnnoyingUser);
    $trsAnnoyingUser = $('tr[rel=annoyinguser]', $tableAnnoyingUser);
    $textboxMaxMessagesAnnoyingUser = $("#textboxMaxMessagesAnnoyingUser");
    $textboxAnnoyingUserEmailSubject = $("#textboxAnnoyingUserEmailSubject");
    $textboxAnnoyingUserEmails = $("#textboxAnnoyingUserEmails");
    $textboxAnnoyingUserEmailTemplate = $("#textboxAnnoyingUserEmailTemplate");

    $checkboxUseAnnoyingUser.change(function () {
        $trsAnnoyingUser.toggle($checkboxUseAnnoyingUser.is(':checked'));

        if (this.checked) {
            if (typeof ($textboxAnnoyingUserEmailTemplate.cleditor) === 'function') {
                var editors = $textboxAnnoyingUserEmailTemplate.cleditor();
                if (editors.length > 0) {
                    editors[0].refresh();
                }
            }
        }
    }).trigger('change')

    $divBitLy = $('#divBitLy', $divGlobalConfig);
    $tableBitLy = $('#tableBitLy', $divBitLy);
    $checkboxBitLyCustomShortener = $('input[type=checkbox][id$=checkboxBitLyCustomShortener]', $tableBitLy);

    $checkboxBitLyCustomShortener.change(function () {
        var $trs = $('tr[rel=usecustombitly]', $tableBitLy);
        var $inputs = $('input', $trs);

        if (this.checked) {
            ToggleValidatorsForInputs($inputs, true);
            $inputs.removeAttr('disabled');
            $trs.show();
        }
        else {
            ToggleValidatorsForInputs($inputs, false);
            $inputs.attr('disabled', 'disabled');
            $trs.hide();
        }
    }).trigger('change');

    if (typeof (WebForm_OnSubmit) == 'function') {
        oldWebFormOnSubmit = WebForm_OnSubmit;
        WebForm_OnSubmit = MyWebFormOnSubmit;
    }

    var htmlEditorOptions = {
        height: 200,
        width: 'auto',
        fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
        bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
        controls:     // controls to add to the toolbar
            "bold italic underline | font size " +
            "style | color highlight removeformat | bullets numbering | outdent " +
            "indent | alignleft center alignright justify | " +
            "image link | cut copy paste pastetext | source",
    };

    $('textarea[id$=textboxServiceStartedEmailTemplate], textarea[id$=textboxServiceStoppedEmailTemplate], textarea[id$=textboxServiceShutdownEmailTemplate], textarea[id$=textboxServiceCrashedEmailTemplate], textarea[id$=textboxExporterServiceStartedEmailTemplate], textarea[id$=textboxExporterServiceStoppedEmailTemplate], textarea[id$=textboxExporterServiceShutdownEmailTemplate], textarea[id$=textboxExporterServiceCrashedEmailTemplate], textarea[id$=textboxSurveysServiceStartedEmailTemplate], textarea[id$=textboxSurveysServiceStoppedEmailTemplate], textarea[id$=textboxSurveysServiceShutdownEmailTemplate], textarea[id$=textboxSurveysServiceCrashedEmailTemplate], textarea[id$=textboxMailMaskBody], textarea[id$=textboxFilterEmailTemplate], textarea[id$=textboxLicenseExpiredEmailTemplate], textarea[id$=textboxOutOfDiskSpaceForAttachmentsEmailTemplate], textarea[id$=textboxDatabaseProblemsEmailTemplate], textarea[id$=textboxDailyReportsEmailTemplate], textarea[id$=textboxOutOfMemoryEmailTemplate],textarea[id$=textboxAnnoyingUserEmailTemplate],#textboxAgentCreatedLoginInformationEmailTemplate,#textboxAgentPasswordChangedEmailTemplate,#textboxExportEmailTemplate,#textboxExportEmailAbortedTemplate').each(function () {
        var $this = $(this);
        var width = $this.parent().width();
        if (width > 0)
            htmlEditorOptions.width = $this.parent().width();
        $this.cleditor(htmlEditorOptions);
    });

    var $messageMailMaskBody = $('#messageMailMaskBodyFields');
    CallValidationFields($textboxMailMaskBody, $messageMailMaskBody);

    var $textboxServiceStartedEmailTemplate = $('textarea[id$=textboxServiceStartedEmailTemplate]');
    var $messageServiceStartedEmailTemplateFields = $('#messageServiceStartedEmailTemplateFields');
    CallValidationFields($textboxServiceStartedEmailTemplate, $messageServiceStartedEmailTemplateFields);

    var $hiddenFieldServiceStoppedConnection = $('#hiddenFieldServiceStoppedConnection');
    var $listboxServiceStoppedConnection = $('#listboxServiceStoppedConnection');
    var $textboxServiceStoppedEmailTemplate = $('textarea[id$=textboxServiceStoppedEmailTemplate]');
    var $messageServiceStoppedEmailTemplateFields = $('#messageServiceStoppedEmailTemplateFields');
    CallValidationFields($textboxServiceStoppedEmailTemplate, $messageServiceStoppedEmailTemplateFields);

    var $textboxServiceShutdownEmailTemplate = $('textarea[id$=textboxServiceShutdownEmailTemplate]');
    var $messageServiceShutdownEmailTemplateFields = $('#messageServiceShutdownEmailTemplateFields');
    CallValidationFields($textboxServiceShutdownEmailTemplate, $messageServiceShutdownEmailTemplateFields);

    var $textboxServiceCrashedEmailTemplate = $('textarea[id$=textboxServiceCrashedEmailTemplate]');
    var $messageServiceCrashedEmailTemplateFields = $('#messageServiceCrashedEmailTemplateFields');
    CallValidationFields($textboxServiceCrashedEmailTemplate, $messageServiceCrashedEmailTemplateFields);

    var $textboxExporterServiceStartedEmailTemplate = $('textarea[id$=textboxExporterServiceStartedEmailTemplate]');
    var $messageExporterServiceStartedEmailTemplateFields = $('#messageExporterServiceStartedEmailTemplateFields');
    CallValidationFields($textboxExporterServiceStartedEmailTemplate, $messageExporterServiceStartedEmailTemplateFields);

    var $textboxExporterServiceStoppedEmailTemplate = $('textarea[id$=textboxExporterServiceStoppedEmailTemplate]');
    var $messageExporterServiceStoppedEmailTemplateFields = $('#messageExporterServiceStoppedEmailTemplateFields');
    CallValidationFields($textboxExporterServiceStoppedEmailTemplate, $messageExporterServiceStoppedEmailTemplateFields);

    var $textboxExporterServiceShutdownEmailTemplate = $('textarea[id$=textboxExporterServiceShutdownEmailTemplate]');
    var $messageExporterServiceShutdownEmailTemplateFields = $('#messageExporterServiceShutdownEmailTemplateFields');
    CallValidationFields($textboxExporterServiceShutdownEmailTemplate, $messageExporterServiceShutdownEmailTemplateFields);

    var $textboxExporterServiceCrashedEmailTemplate = $('textarea[id$=textboxExporterServiceCrashedEmailTemplate]');
    var $messageExporterServiceCrashedEmailTemplateFields = $('#messageExporterServiceCrashedEmailTemplateFields');
    CallValidationFields($textboxExporterServiceCrashedEmailTemplate, $messageExporterServiceCrashedEmailTemplateFields);

    var $textboxSurveysServiceStartedEmailTemplate = $('textarea[id$=textboxSurveysServiceStartedEmailTemplate]');
    var $messageSurveysServiceStartedEmailTemplateFields = $('#messageSurveysServiceStartedEmailTemplateFields');
    CallValidationFields($textboxSurveysServiceStartedEmailTemplate, $messageSurveysServiceStartedEmailTemplateFields);

    var $textboxSurveysServiceStoppedEmailTemplate = $('textarea[id$=textboxSurveysServiceStoppedEmailTemplate]');
    var $messageSurveysServiceStoppedEmailTemplateFields = $('#messageSurveysServiceStoppedEmailTemplateFields');
    CallValidationFields($textboxSurveysServiceStoppedEmailTemplate, $messageSurveysServiceStoppedEmailTemplateFields);

    var $textboxSurveysServiceShutdownEmailTemplate = $('textarea[id$=textboxSurveysServiceShutdownEmailTemplate]');
    var $messageSurveysServiceShutdownEmailTemplateFields = $('#messageSurveysServiceShutdownEmailTemplateFields');
    CallValidationFields($textboxSurveysServiceShutdownEmailTemplate, $messageSurveysServiceShutdownEmailTemplateFields);

    var $textboxSurveysServiceCrashedEmailTemplate = $('textarea[id$=textboxSurveysServiceCrashedEmailTemplate]');
    var $messageSurveysServiceCrashedEmailTemplateFields = $('#messageSurveysServiceCrashedEmailTemplateFields');
    CallValidationFields($textboxSurveysServiceCrashedEmailTemplate, $messageSurveysServiceCrashedEmailTemplateFields);

    var $hiddenLicenseExpiredConnection = $('#hiddenLicenseExpiredConnection');
    var $listboxLicenseExpiredConnection = $('#listboxLicenseExpiredConnection');
    var $textboxLicenseExpiredEmailTemplate = $('textarea[id$=textboxLicenseExpiredEmailTemplate]');
    var $messageLicenseExpiredEmailTemplateFields = $('#messageLicenseExpiredEmailTemplateFields');
    CallValidationFields($textboxLicenseExpiredEmailTemplate, $messageLicenseExpiredEmailTemplateFields);

    var $textboxFilterEmailSubject = $('input[id$=textboxFilterEmailSubject]');
    var $messageFilterEmailSubjectFields = $('#messageFilterEmailSubjectFields');
    CallValidationFields($textboxFilterEmailSubject, $messageFilterEmailSubjectFields);

    var $textboxFilterEmailTemplate = $('textarea[id$=textboxFilterEmailTemplate]');
    var $messageFilterEmailTemplateFields = $('#messageFilterEmailTemplateFields');
    CallValidationFields($textboxFilterEmailTemplate, $messageFilterEmailTemplateFields);

    var $hiddenFieldOutOfDiskSpaceForAttachmentsConnection = $('#hiddenFieldOutOfDiskSpaceForAttachmentsConnection');
    var $listboxOutOfDiskSpaceForAttachmentsConnection = $('#listboxOutOfDiskSpaceForAttachmentsConnection');
    var $textboxOutOfDiskSpaceForAttachmentsEmailTemplate = $('textarea[id$=textboxOutOfDiskSpaceForAttachmentsEmailTemplate]');
    var $messageOutOfDiskSpaceForAttachmentsEmailTemplateFields = $('#messageOutOfDiskSpaceForAttachmentsEmailTemplateFields');
    CallValidationFields($textboxOutOfDiskSpaceForAttachmentsEmailTemplate, $messageOutOfDiskSpaceForAttachmentsEmailTemplateFields);

    var $hiddenFieldDatabaseProblemsConnection = $('#hiddenFieldDatabaseProblemsConnection');
    var $listboxDatabaseProblemsConnection = $('#listboxDatabaseProblemsConnection');
    var $textboxDatabaseProblemsEmailTemplate = $('textarea[id$=textboxDatabaseProblemsEmailTemplate]');
    var $messageDatabaseProblemsEmailTemplateFields = $('#messageDatabaseProblemsEmailTemplateFields');
    CallValidationFields($textboxDatabaseProblemsEmailTemplate, $messageDatabaseProblemsEmailTemplateFields);

    var $hiddenFieldOutOfMemoryConnection = $('#hiddenFieldOutOfMemoryConnection');
    var $listboxOutOfMemoryConnection = $('#listboxOutOfMemoryConnection');
    var $textboxOutOfMemoryEmailTemplate = $('textarea[id$=textboxOutOfMemoryEmailTemplate]');
    var $messageOutOfMemoryEmailTemplateFields = $('#messageOutOfMemoryEmailTemplateFields');
    CallValidationFields($textboxOutOfMemoryEmailTemplate, $messageOutOfMemoryEmailTemplateFields);

    var $hiddenFieldAnnoyingUserConnection = $('#hiddenFieldAnnoyingUserConnection');
    var $listboxAnnoyingUserConnection = $('#listboxAnnoyingUserConnection');
    var $messageAnnoyingUserEmailTemplateFields = $('#messageAnnoyingUserEmailTemplateFields');
    CallValidationFields($textboxAnnoyingUserEmailTemplate, $messageAnnoyingUserEmailTemplateFields);

    var $hiddenFieldAgentCreatedLoginInformationConnection = $('#hiddenFieldAgentCreatedLoginInformationConnection');
    var $listboxAgentCreatedLoginInformationConnection = $('#listboxAgentCreatedLoginInformationConnection');
    var $textboxAgentCreatedLoginInformationEmailTemplate = $('#textboxAgentCreatedLoginInformationEmailTemplate');
    var $messageAgentCreatedLoginInformationEmailTemplateFields = $('#messageAgentCreatedLoginInformationEmailTemplateFields');
    CallValidationFields($textboxAgentCreatedLoginInformationEmailTemplate, $messageAgentCreatedLoginInformationEmailTemplateFields);

    var $hiddenFieldAgentPasswordChangedConnection = $('#hiddenFieldAgentPasswordChangedConnection');
    var $listboxAgentPasswordChangedConnection = $('#listboxAgentPasswordChangedConnection');
    var $textboxAgentPasswordChangedEmailTemplate = $('#textboxAgentPasswordChangedEmailTemplate');
    var $messageAgentPasswordChangedEmailTemplateFields = $('#messageAgentPasswordChangedEmailTemplateFields');
    CallValidationFields($textboxAgentPasswordChangedEmailTemplate, $messageAgentPasswordChangedEmailTemplateFields);

    $tableGenerateDailyReport = $('#tableGenerateDailyReport');
    $checkboxGenerateDailyReport = $('#checkboxGenerateDailyReport', $tableGenerateDailyReport);
    $trsGenerateDailyReport = $('tr[rel=generatedailyreport]', $tableGenerateDailyReport);
    $trDailyReportFtpCredentials = $('#trDailyReportFtpCredentials');
    $checkboxEnableFtpDailyReport = $('#checkboxEnableFtpDailyReport');

    var $textboxDailyReportsEmailSubject = $('#textboxDailyReportsEmailSubject');
    var $messageDailyReportsEmailSubject = $('#messageDailyReportsEmailSubject');
    CallValidationFields($textboxDailyReportsEmailSubject, $messageDailyReportsEmailSubject);

    var $textboxDailyReportsEmailTemplate = $('#textboxDailyReportsEmailTemplate');
    var $messageDailyReportsEmailTemplate = $('#messageDailyReportsEmailTemplate');
    CallValidationFields($textboxDailyReportsEmailTemplate, $messageDailyReportsEmailTemplate);

    var $textboxExportEmailSubject = $('#textboxExportEmailSubject');
    var $messageExportEmailSubject = $('#messageExportEmailSubject');
    CallValidationFields($textboxExportEmailSubject, $messageExportEmailSubject);

    var $textboxExportEmailTemplate = $('#textboxExportEmailTemplate');
    var $messageExportEmailTemplate = $('#messageExportEmailTemplate');
    CallValidationFields($textboxExportEmailTemplate, $messageExportEmailTemplate);

    var $textboxExportEmailAbortedSubject = $('#textboxExportEmailAbortedSubject');
    var $messageExportEmailAbortedSubject = $('#messageExportEmailAbortedSubject');
    CallValidationFields($textboxExportEmailAbortedSubject, $messageExportEmailAbortedSubject);

    var $textboxExportEmailAbortedTemplate = $('#textboxExportEmailAbortedTemplate');
    var $messageExportEmailAbortedTemplate = $('#messageExportEmailAbortedTemplate');
    CallValidationFields($textboxExportEmailAbortedTemplate, $messageExportEmailAbortedTemplate);

    var $hiddenDeliveryFailedConnection = $('#hiddenDeliveryFailedConnection');
    var $listboxDeliveryFailedConnection = $('#listboxDeliveryFailedConnection');

    var $hiddenFieldDefaultEmailConnection = $('#hiddenFieldDefaultEmailConnection');
    var $listboxDefaultEmailConnection = $('#listboxDefaultEmailConnection');

    var $hiddenYFlowAuthenticationFailedConnection = $('#hiddenYFlowAuthenticationFailedConnection');
    var $listboxYFlowAuthenticationFailedConnection = $('#listboxYFlowAuthenticationFailedConnection');

    var $hiddenYFlowInvokeFailed = $('#hiddenYFlowInvokeFailed');
    var $listboxYFlowInvokeFailedConnection = $('#listboxYFlowInvokeFailedConnection');

    if (typeof (emails) !== 'undefined' && emails !== null && emails.length > 0) {
        let addOption = function (email, $select) {
            let $option = $('<option></option>');
            $option.text(email.Name);
            $option.val(email.ID);
            $select.append($option);
        };

        let selectOptions = function ($hidden, $select) {
            let value = $hidden.val();
            if (typeof (value) === 'string' && value.length > 0) {
                $select.val(value);
            }
        };

        for (let i = 0; i < emails.length; i++) {
            //No listamos la casilla por defult ya que al enviar el valor en null se utilizara la por defecto
            if (!emails[i].UseAsDefault) {
                addOption(emails[i], $listboxDeliveryFailedConnection);
                addOption(emails[i], $listboxServiceStoppedConnection);
                addOption(emails[i], $listboxLicenseExpiredConnection);
                addOption(emails[i], $listboxOutOfDiskSpaceForAttachmentsConnection);
                addOption(emails[i], $listboxDatabaseProblemsConnection);
                addOption(emails[i], $listboxOutOfMemoryConnection);
                addOption(emails[i], $listboxAnnoyingUserConnection);
                addOption(emails[i], $listboxAgentCreatedLoginInformationConnection);
                addOption(emails[i], $listboxAgentPasswordChangedConnection);
                addOption(emails[i], $listboxYFlowAuthenticationFailedConnection);
                addOption(emails[i], $listboxYFlowInvokeFailedConnection);
            }

            addOption(emails[i], $listboxDefaultEmailConnection);
        }
        selectOptions($hiddenDeliveryFailedConnection, $listboxDeliveryFailedConnection);
        selectOptions($hiddenFieldServiceStoppedConnection, $listboxServiceStoppedConnection);
        selectOptions($hiddenLicenseExpiredConnection, $listboxLicenseExpiredConnection);
        selectOptions($hiddenFieldOutOfDiskSpaceForAttachmentsConnection, $listboxOutOfDiskSpaceForAttachmentsConnection);
        selectOptions($hiddenFieldDatabaseProblemsConnection, $listboxDatabaseProblemsConnection);
        selectOptions($hiddenFieldOutOfMemoryConnection, $listboxOutOfMemoryConnection);
        selectOptions($hiddenFieldAnnoyingUserConnection, $listboxAnnoyingUserConnection);
        selectOptions($hiddenFieldAgentCreatedLoginInformationConnection, $listboxAgentCreatedLoginInformationConnection);
        selectOptions($hiddenFieldAgentPasswordChangedConnection, $listboxAgentPasswordChangedConnection);
        selectOptions($hiddenFieldDefaultEmailConnection, $listboxDefaultEmailConnection);
        selectOptions($hiddenYFlowAuthenticationFailedConnection, $listboxYFlowAuthenticationFailedConnection);
        selectOptions($hiddenYFlowInvokeFailed, $listboxYFlowInvokeFailedConnection);
    }

    var hiddenFields = [
        $hiddenDeliveryFailedConnection,
        $hiddenFieldServiceStoppedConnection,
        $hiddenLicenseExpiredConnection,
        $hiddenFieldOutOfDiskSpaceForAttachmentsConnection,
        $hiddenFieldDatabaseProblemsConnection,
        $hiddenFieldOutOfMemoryConnection,
        $hiddenFieldAnnoyingUserConnection,
        $hiddenFieldAgentCreatedLoginInformationConnection,
        $hiddenFieldAgentPasswordChangedConnection,
        $hiddenFieldDefaultEmailConnection,
        $hiddenYFlowAuthenticationFailedConnection,
        $hiddenYFlowInvokeFailed
    ];

    var multiselects = [
        $listboxDeliveryFailedConnection,
        $listboxServiceStoppedConnection,
        $listboxLicenseExpiredConnection,
        $listboxOutOfDiskSpaceForAttachmentsConnection,
        $listboxDatabaseProblemsConnection,
        $listboxOutOfMemoryConnection,
        $listboxAnnoyingUserConnection,
        $listboxAgentCreatedLoginInformationConnection,
        $listboxAgentPasswordChangedConnection,
        $listboxDefaultEmailConnection,
        $listboxYFlowAuthenticationFailedConnection,
        $listboxYFlowInvokeFailedConnection
    ];

    for (let i = 0; i < hiddenFields.length; i++) {
        let hiddenField = hiddenFields[i];
        let multiselect = multiselects[i];

        multiselect.multiselect({
            multiple: false,
            noneSelectedText: "Por defecto (actual en parámetros del sistema)",
            selectedList: 1,
            buttonWidth: '>500'
        }).multiselectfilter();

        multiselect.change(function () {
            var value = multiselect.val();
            hiddenField.val('');
            if (value != null) {
                hiddenField.val(value);
            }
        });
    }

    $checkboxGenerateDailyReport.change(function () {
        $trsGenerateDailyReport.toggle($checkboxGenerateDailyReport.is(':checked'));
        if ($checkboxGenerateDailyReport.is(':checked')) {
            $textboxDailyReportsEmailTemplate.cleditor()[0].refresh();
        }
    }).trigger('change');

    $checkboxEnableFtpDailyReport.change(function () {
        $trDailyReportFtpCredentials.toggle($checkboxEnableFtpDailyReport.is(':checked'));
    }).trigger('change');

    $listboxAlertMessagesDeliveryFailedVia = $('#listboxAlertMessagesDeliveryFailedVia');
    $listboxAlertMessagesDeliveryFailedVia.multiselect({ multiple: true, noneSelectedText: "No notificar", selectedList: 4, buttonWidth: '>450' }).multiselectfilter();
    $trAlertMessagesDeliveryFailedFromServices = $('#trAlertMessagesDeliveryFailedFromServices');
    $listboxAlertMessagesDeliveryFailedFromServices = $('#listboxAlertMessagesDeliveryFailedFromServices');
    $listboxAlertMessagesDeliveryFailedFromServices.multiselect({ multiple: true, noneSelectedText: "Todos los servicios", selectedList: 4, buttonWidth: '>450' }).multiselectfilter();
    $listboxAlertMessagesDeliveryFailedFromServices.multiselect("widget").find("label").each(function (index) {
        var $label = $(this);
        var $input = $('input', $label);
        var $textSpan = $('span', $label);

        var $option = $('option:nth-child(' + (index) + ')', $listboxAlertMessagesDeliveryFailedFromServices);

        var $typeSpan = $('<span class="fa-lg" style="margin-left: 5px;"></span>');

        var type = parseInt($option.attr('type'), 10);
        if (type != ServiceTypes.TwitterSearches) {
            $typeSpan.addClass(GetServiceTypeClass(type));
        }
        else {
            $typeSpan.addClass('fa-stack-05');
            $typeSpan.append('<i class="fab fa-twitter-square fa-stack-1x"></i><i class="fa fa-search fa-stack-05x"></i>');
        }

        $typeSpan.insertAfter($textSpan);
    });
    $divAlertMessagesDeliveryFailedViaMail = $('#divAlertMessagesDeliveryFailedViaMail');
    $textboxAlertMessagesDeliveryFailedViaMailEmailSubject = $('#textboxAlertMessagesDeliveryFailedViaMailEmailSubject');
    $textboxAlertMessagesDeliveryFailedViaMailEmailTemplate = $('#textboxAlertMessagesDeliveryFailedViaMailEmailTemplate');
    $textboxAlertMessagesDeliveryFailedViaMailEmailTemplate.cleditor(htmlEditorOptions);
    var $messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields = $('#messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields');
    CallValidationFields($textboxAlertMessagesDeliveryFailedViaMailEmailTemplate, $messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields);

    $listboxAlertMessagesDeliveryFailedVia.change(function () {
        var values = $listboxAlertMessagesDeliveryFailedVia.val();
        $trAlertMessagesDeliveryFailedFromServices.toggle(values !== null && values.length >= 0);

        $divAlertMessagesDeliveryFailedViaMail.toggle(values !== null && values.length >= 0 && values.indexOf("1") >= 0);
        var editors = $textboxAlertMessagesDeliveryFailedViaMailEmailTemplate.cleditor();
        if (editors.length > 0) {
            editors[0].refresh();
        }
    }).trigger('change');

    $divYFlow = $('#divYFlow');
    $checkboxEnableYFlow = $('#checkboxEnableYFlow', $divYFlow);
    var $trsUseYFlow = $('tr[rel=useYFlow]', $divYFlow);
    var $divYFlowNotifications = $('#divYFlowNotifications', $divYFlow);
    $textboxYFlowUrl = $('#textboxYFlowUrl', $divYFlow);
    $textboxYFlowUrlApi = $('#textboxYFlowUrlApi', $divYFlow);
    $textboxYFlowUrlWeb = $('#textboxYFlowUrlWeb', $divYFlow);
    $textboxYFlowUsername = $('#textboxYFlowUsername', $divYFlow);
    $checkboxYFlowPassword = $('#checkboxYFlowPassword', $divYFlow);
    $textboxYFlowPassword = $('#textboxYFlowPassword', $divYFlow);
    $textboxYFlowTimeout = $('#textboxYFlowTimeout', $divYFlow);
    $textboxYFlowAuthenticationFailedSubject = $('#textboxYFlowAuthenticationFailedSubject', $divYFlow);
    $textboxYFlowAuthenticationFailedEmails = $('#textboxYFlowAuthenticationFailedEmails', $divYFlow);
    $textboxYFlowAuthenticationFailedTemplate = $('#textboxYFlowAuthenticationFailedTemplate', $divYFlow);
    var $messageYFlowAuthenticationFailedTemplateFields = $('#messageYFlowAuthenticationFailedTemplateFields', $divYFlow);
    $textboxYFlowAuthenticationFailedTemplate.cleditor(htmlEditorOptions);
    CallValidationFields($textboxYFlowAuthenticationFailedTemplate, $messageYFlowAuthenticationFailedTemplateFields);
    $textboxYFlowInvokeFailedSubject = $('#textboxYFlowInvokeFailedSubject', $divYFlow);
    $textboxYFlowInvokeFailedEmails = $('#textboxYFlowInvokeFailedEmails', $divYFlow);
    $textboxYFlowInvokeFailedTemplate = $('#textboxYFlowInvokeFailedTemplate', $divYFlow);
    var $messageYFlowInvokeFailedTemplateFields = $('#messageYFlowInvokeFailedTemplateFields', $divYFlow);
    $textboxYFlowInvokeFailedTemplate.cleditor(htmlEditorOptions);
    var $trMaxElapsedMinutesToCloseYFlowCases = $('#trMaxElapsedMinutesToCloseYFlowCases');
    CallValidationFields($textboxYFlowInvokeFailedTemplate, $messageYFlowInvokeFailedTemplateFields);
    $checkboxEnableYFlow.change(function () {
        $trsUseYFlow.toggle(this.checked);
        $divYFlowNotifications.toggle(this.checked);
        $trMaxElapsedMinutesToCloseYFlowCases.toggle(this.checked);

        if (this.checked) {
            var $cleditors = $('textarea[id$=Template]', $divYFlow);
            $cleditors.each(function (index) {
                var $this = $(this);
                if (typeof ($this.cleditor) === 'function') {
                    var editors = $this.cleditor();
                    if (editors.length > 0) {
                        editors[0].refresh();
                    }
                }
            });
        }
    }).trigger('change');
    $checkboxYFlowPassword.change(function () {
        if (this.checked) {
            $textboxYFlowPassword.removeAttr('disabled');
        }
        else {
            $textboxYFlowPassword.attr('disabled', 'disabled');
            $textboxYFlowPassword.val('');
        }
    }).trigger('change');

    $divYSmart = $('#divYSmart');
    $checkboxEnableYSmart = $('#checkboxEnableYSmart', $divYSmart);
    var $trsUseYSmart = $('tr[rel=useYSmart]', $divYSmart);
    var $divYSmartProjects = $('#divYSmartProjects', $divYSmart);
    $textboxYSmartTimeout = $('#textboxYSmartTimeout', $divYSmart);
    $anchorProjectReload = $('#anchorProjectReload');
    $hiddenProject = $('#hiddenProject');
    $selectProjectToUse = $('#selectProjectToUse');
    $spanProjectID = $('#spanProjectID');
    $spanProjectServiceName = $('#spanProjectServiceName');
    $spanProjectLastChange = $('#spanProjectLastChange');
    $checkboxEnableYSmart.change(function () {
        $trsUseYSmart.toggle(this.checked);
        $divYSmartProjects.toggle(this.checked);
        if (this.checked) {
            ReloadProjects();
            var $cleditors = $('textarea[id$=Template]', $divYSmart);
            $cleditors.each(function (index) {
                var $this = $(this);
                if (typeof ($this.cleditor) === 'function') {
                    var editors = $this.cleditor();
                    if (editors.length > 0) {
                        editors[0].refresh();
                    }
                }
            });
        }
    }).trigger('change');
    $selectProjectToUse.change(function () {
        let $option = $("option:selected", $selectProjectToUse);
        let project = $option.prop("definition");
        if (project != null) {
            $spanProjectID.text(project.id);
            $spanProjectServiceName.text(project.projectService.name);
            $spanProjectLastChange.text(project.lastUpdate);
            $hiddenProject.val(JSON.stringify(project));
        }
    });
    $anchorProjectReload.click(ReloadProjects);

    let currentProject = $hiddenProject.val();

    if (typeof (currentProject) !== 'undefined' && currentProject !== null && currentProject.length > 0) {
        currentProject = JSON.parse(currentProject);
        $spanProjectID.text(currentProject.id);
        $spanProjectServiceName.text(currentProject.name);
        $spanProjectLastChange.text(currentProject.version);
    }
    $textboxCognitiveServicesToken = $('#textboxCognitiveServicesToken');
    $textboxCognitiveServicesTokenSecret = $('#textboxCognitiveServicesTokenSecret');
    $checkboxUseCognitiveServices = $('#checkboxUseCognitiveServices');
    $trUseCognitiveServices = $('#trUseCognitiveServicesToken,#trUseCognitiveServicesTokenSecret');
    $checkboxUseCognitiveServices.change(function () {
        $trUseCognitiveServices.toggle($checkboxUseCognitiveServices.is(':checked'));
    }).trigger('change');

    //Login por url del WebAgent
    $panelWebAgentConfigurationAllowUrlLogin = $("#panelWebAgentConfigurationAllowUrlLogin");
    $checkboxWebAgentConfigurationAllowUrlLogin = $('input[type=checkbox][id$=checkboxWebAgentConfigurationAllowUrlLogin]', $panelWebAgentConfigurationAllowUrlLogin);
    $checkboxWebAgentConfigurationPasswordRequired = $('input[type=checkbox][id$=checkboxWebAgentConfigurationPasswordRequired]', $panelWebAgentConfigurationAllowUrlLogin);
    $checkboxWebAgentConfigurationRemoveLoginForm = $('input[type=checkbox][id$=checkboxWebAgentConfigurationRemoveLoginForm]', $panelWebAgentConfigurationAllowUrlLogin);
    $checkboxWebAgentConfigurationBehaviorLogout = $('input[type=checkbox][id$=checkboxWebAgentConfigurationBehaviorLogout]', $panelWebAgentConfigurationAllowUrlLogin);
    $checkboxWebAgentConfigurationRemoveLogoutButton = $('input[type=checkbox][id$=checkboxWebAgentConfigurationRemoveLogoutButton]', $panelWebAgentConfigurationAllowUrlLogin);

    $dropdownlistLogoutAction = $("#dropdownlistLogoutAction", $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationUserNameLoginParameter = $('#textboxWebAgentConfigurationUserNameLoginParameter', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationPasswordParameter = $('#textboxWebAgentConfigurationPasswordParameter', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationKeyToDecryptPassword = $('#textboxWebAgentConfigurationKeyToDecryptPassword', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationHashParameter = $('#textboxWebAgentConfigurationHashParameter', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationKeyToHash = $('#textboxWebAgentConfigurationKeyToHash', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationRedirectUrl = $('#textboxWebAgentConfigurationRedirectUrl', $panelWebAgentConfigurationAllowUrlLogin);
    $textboxWebAgentConfigurationLogoutMessage = $('#textboxWebAgentConfigurationLogoutMessage', $panelWebAgentConfigurationAllowUrlLogin);

    $trsWebAgentConfiguration = $('tr[rel=trWebAgentConfiguration]', $panelWebAgentConfigurationAllowUrlLogin);
    $trWebAgentConfigurationPasswordParameter = $("#trWebAgentConfigurationPasswordParameter");
    $trWebAgentConfigurationKeyToDecryptPassword = $("#trWebAgentConfigurationKeyToDecryptPassword");
    $trWebAgentConfigurationRedirectUrl = $("#trWebAgentConfigurationRedirectUrl");
    $trWebAgentConfigurationLogoutMessage = $("#trWebAgentConfigurationLogoutMessage");
    $messageWebAgentConfigurationAllowUrlLoginCloud = $('#messageWebAgentConfigurationAllowUrlLoginCloud');

    /*Manejo de estado a través de mensajería interna del browser en el agente web*/
    $panelWebAgentConfigurationStateManagement = $("#panelWebAgentConfigurationStateManagement");
    $checkboxWebAgentConfigurationAllowChangeState = $('input[type=checkbox][id$=checkboxWebAgentConfigurationAllowChangeState]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived = $('input[type=checkbox][id$=checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationAllowAgentsToChangeState = $('input[type=checkbox][id$=checkboxWebAgentConfigurationAllowAgentsToChangeState]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging = $('input[type=checkbox][id$=checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationAllowLogoutInvoke = $('input[type=checkbox][id$=checkboxWebAgentConfigurationAllowLogoutInvoke]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationShowMessageAfterLogoutReceived = $('input[type=checkbox][id$=checkboxWebAgentConfigurationShowMessageAfterLogoutReceived]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging = $('input[type=checkbox][id$=checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging]', $panelWebAgentConfigurationStateManagement);
    $checkboxWebAgentConfigurationIgnoreLogoutAfterError = $('input[type=checkbox][id$=checkboxWebAgentConfigurationIgnoreLogoutAfterError]', $panelWebAgentConfigurationStateManagement);
    $textboxWebAgentConfigurationTargetOrigin = $('#textboxWebAgentConfigurationTargetOrigin', $panelWebAgentConfigurationStateManagement);

    $trsWebAgentConfigurationAllowStateManagementByInternalMessaging = $('tr[rel=trWebAgentConfigurationAllowStateManagementByInternalMessaging]', $panelWebAgentConfigurationStateManagement);
    $trsWebAgentConfigurationAllowChangeState = $('tr[rel=trWebAgentConfigurationAllowChangeState]');
    $trsWebAgentConfigurationAllowLogoutInvocation = $('tr[rel=trWebAgentConfigurationAllowLogoutInvocation]');
    //$trWebAgentConfigurationIgnoreLogoutAfterError = $('tr[rel=trWebAgentConfigurationIgnoreLogoutAfterError]');
    $trWebAgentConfigurationShowMessageAfterLogoutReceived = $('tr[rel=trWebAgentConfigurationShowMessageAfterLogoutReceived]');
    $trWebAgentConfigurationShowMessageAfterChangeStateReceived = $('tr[rel=trWebAgentConfigurationShowMessageAfterChangeStateReceived]');
    $trWebAgentConfigurationAllowAgentsToChangeState = $('tr[rel=trWebAgentConfigurationAllowAgentsToChangeState]');
    $divWebAgentConfigurationStateManagement = $("#divWebAgentConfigurationStateManagement");
    $divWebAgentConfigurationLogoutInvoke = $("#divWebAgentConfigurationLogoutInvoke");

    $textboxWebAgentConfigurationLogoutInvokeTargetOrigin = $('input[id$=textboxWebAgentConfigurationLogoutInvokeTargetOrigin]', $panelWebAgentConfigurationStateManagement);
    $textboxWebAgentConfigurationChangeStateTargetOrigin = $('input[id$=textboxWebAgentConfigurationChangeStateTargetOrigin]', $panelWebAgentConfigurationStateManagement);

    $textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived = $('input[id$=textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived],textarea', $divWebAgentConfigurationStateManagement);
    $textboxWebAgentConfigurationShowInformativeMessageAfterLogoutReceived = $('input[id$=textboxWebAgentConfigurationShowInformativeMessageAfterLogoutReceived],textarea', $divWebAgentConfigurationLogoutInvoke);

    $checkboxWebAgentConfigurationRemoveLoginForm.change(function () {
        if (this.checked) {
            if ($checkboxWebAgentConfigurationAllowChangeState.is(':checked') && !$checkboxWebAgentConfigurationAllowAgentsToChangeState.is(':checked')) {
                $trWebAgentConfigurationAllowAgentsToChangeState.show();
            }
        }
        else {
            $trWebAgentConfigurationAllowAgentsToChangeState.hide();
        }
    }).trigger('change');


    $divYUsage = $('#divYUsage');
    $checkboxEnableYUsage = $('#checkboxEnableYUsage', $divYUsage);
    $textboxYUsageUrl = $('#textboxYUsageUrl', $divYUsage);
    $buttonGenerateYUsageToken = $('#buttonGenerateYUsageToken', $divYUsage);
    $spanYUsageTokenOk = $('#spanYUsageTokenOk', $divYUsage);
    $spanYUsageTokenError = $('#spanYUsageTokenError', $divYUsage);

    $checkboxEnableYUsage.change(function () {
        var $trsUseYUsage = $('tr[rel=useYUsage]', $divYUsage);
        $trsUseYUsage.toggle(this.checked);

        if (this.checked) {
            $textboxYUsageUrl.focus();
        }
    }).trigger('change');

    $textboxYUsageUrl.blur(function () {
        var $this = $(this);
        var $parentDiv = $this.parent();
        var url = $this.val();

        if (url.length > 0) {
            ValidateYUsageUrl($parentDiv, url);
        }
    }).trigger('blur');

    $buttonGenerateYUsageToken.click(function () {
        generateYUsageToken();
    });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/BuildAuthorizationUriForYFlow",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                $('#buttonLoginYFlow').click(data.d.Uri, function (event) {
                    PopupCenter(event.data, 'login', 900, 450);
                });
            }
            else {
                if (console)
                    console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + data.d.Error.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (console)
                console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + jqXHR.responseText);
        }
    });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/BuildAuthorizationUriForYFlowContingency",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                $('#buttonLoginYFlowContingency').click(data.d.Uri, function (event) {
                    PopupCenter(event.data, 'login', 900, 450);
                });
            }
            else {
                if (console)
                    console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + data.d.Error.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (console)
                console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + jqXHR.responseText);
        }
    });

    $("#buttonLoginYFlowContingency").on("click", function () {
        YflowInstance = getEnumValue(YFlowInstances, 'YFlowContingency');
    });
    $("#buttonLoginYFlow").on("click", function () {
        YflowInstance = getEnumValue(YFlowInstances, 'YFlow');
    });

    if (window.addEventListener) {
        addEventListener("message", function (event) {
            if (YflowInstance == getEnumValue(YFlowInstances, 'YFlowContingency')) {
                FacebookTokenCallbackMessage(
                    event,
                    "SystemSettings.aspx/AccessYFlowContingency",
                    "Acceso a YFlow Contingencia",
                    "Se ha configurado el acceso a YFlow Contingencia de forma correcta.",
                    "Acceso a YFlow Contingencia",
                    "Credenciales incorrectas"
                );
            } else {
                FacebookTokenCallbackMessage(
                    event,
                    "SystemSettings.aspx/AccessYFlow",
                    "Acceso a YFlow",
                    "Se ha configurado el acceso a YFlow de forma correcta.",
                    "Acceso a YFlow",
                    "Credenciales incorrectas"
                );
            }
        }, false);
    } else {
        attachEvent("onmessage", function (event) {
            if (YflowInstance == getEnumValue(YFlowInstances, 'YFlowContingency')) {
                FacebookTokenCallbackMessage(
                    event,
                    "SystemSettings.aspx/AccessYFlowContingency",
                    "Acceso a YFlow Contingencia",
                    "Se ha configurado el acceso a YFlow Contingencia de forma correcta.",
                    "Acceso a YFlow Contingencia",
                    "Credenciales incorrectas"
                );
            } else {
                FacebookTokenCallbackMessage(
                    event,
                    "SystemSettings.aspx/AccessYFlow",
                    "Acceso a YFlow",
                    "Se ha configurado el acceso a YFlow de forma correcta.",
                    "Acceso a YFlow",
                    "Credenciales incorrectas"
                );
            }
        });
    }

    $dropdownlistLogoutAction.change(function () {
        var logoutBehaviorValue = parseInt($dropdownlistLogoutAction.val());
        //Configurar mensaje al finalizar la sesión
        if (logoutBehaviorValue === 1) {
            $trWebAgentConfigurationRedirectUrl.hide();
            $trWebAgentConfigurationLogoutMessage.show();
        }
        //Configurar url a redireccionar
        else if (logoutBehaviorValue === 2) {
            $trWebAgentConfigurationLogoutMessage.hide();
            $trWebAgentConfigurationRedirectUrl.show();
        }
        //Ninguno
        else {
            $trWebAgentConfigurationLogoutMessage.hide();
            $trWebAgentConfigurationRedirectUrl.hide();
        }
    }).trigger('change');

    $checkboxWebAgentConfigurationPasswordRequired.change(function () {
        if (this.checked) {
            $trWebAgentConfigurationPasswordParameter.show();
            $trWebAgentConfigurationKeyToDecryptPassword.show();
        }
        else {
            $trWebAgentConfigurationPasswordParameter.hide();
            $trWebAgentConfigurationKeyToDecryptPassword.hide();
        }
    }).trigger('change');

    $checkboxWebAgentConfigurationAllowUrlLogin.change(function () {
        if (this.checked) {
            $trsWebAgentConfiguration.show();
            $checkboxWebAgentConfigurationPasswordRequired.trigger('change');
            $dropdownlistLogoutAction.trigger('change');
            $messageWebAgentConfigurationAllowUrlLoginCloud.show();
        }
        else {
            $trsWebAgentConfiguration.hide();
            $messageWebAgentConfigurationAllowUrlLoginCloud.hide();
        }
    }).trigger('change');
    /*stateManagement control events*/
    $checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.change(function () {
        if (this.checked) {
            $trWebAgentConfigurationShowMessageAfterChangeStateReceived.show();
        }
        else {
            $trWebAgentConfigurationShowMessageAfterChangeStateReceived.hide();
        }
    }).trigger('change');

    $checkboxWebAgentConfigurationShowMessageAfterLogoutReceived.change(function () {
        if (this.checked) {
            $trWebAgentConfigurationShowMessageAfterLogoutReceived.show();
        }
        else {
            $trWebAgentConfigurationShowMessageAfterLogoutReceived.hide();
        }
    }).trigger('change');

    $checkboxWebAgentConfigurationAllowLogoutInvoke.change(function () {
        if (this.checked) {
            $trsWebAgentConfigurationAllowLogoutInvocation.show();
            $checkboxWebAgentConfigurationShowMessageAfterLogoutReceived.trigger('change');
        }
        else {
            $trsWebAgentConfigurationAllowLogoutInvocation.hide();
            $trWebAgentConfigurationShowMessageAfterLogoutReceived.hide();
        }

    }).trigger('change');

    $checkboxWebAgentConfigurationAllowAgentsToChangeState.change(function () {
        if (this.checked) {
            $trWebAgentConfigurationAllowAgentsToChangeState.hide();
        }
        else {
            if ($checkboxWebAgentConfigurationRemoveLoginForm.is(':checked'))
                $trWebAgentConfigurationAllowAgentsToChangeState.show();
        }
    }).trigger('change');

    $checkboxWebAgentConfigurationAllowChangeState.change(function () {
        if (this.checked) {
            $trsWebAgentConfigurationAllowChangeState.show();
            $checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.trigger('change');
            $checkboxWebAgentConfigurationAllowAgentsToChangeState.trigger('change');
        }
        else {
            $trsWebAgentConfigurationAllowChangeState.hide();
            $trWebAgentConfigurationShowMessageAfterChangeStateReceived.hide();
            $trWebAgentConfigurationAllowAgentsToChangeState.hide();
        }
    }).trigger('change');

    $aExtendedProfileFieldsAdd = $('#aExtendedProfileFieldsAdd');
    $tableExtendedProfileFields = $('#tableExtendedProfileFields');
    $hiddenExtendedProfileFields = $('#hiddenExtendedProfileFields');
    $aExtendedProfileFieldsAdd.click(AddExtendedProfileField);

    if ($hiddenExtendedProfileFields.length > 0) {
        var fields = $hiddenExtendedProfileFields.val();
        if (fields !== null && fields.length > 0) {
            fields = JSON.parse(fields);
            if (fields !== null) {
                for (var i = 0; i < fields.length; i++) {
                    AddExtendedProfileField(fields[i]);
                }
            }
        }
    }

    $aExtendedCaseFieldsAdd = $('#aExtendedCaseFieldsAdd');
    $tableExtendedCaseFields = $('#tableExtendedCaseFields');
    $hiddenExtendedCaseFields = $('#hiddenExtendedCaseFields');
    $aExtendedCaseFieldsAdd.click(AddExtendedCaseField);

    if ($hiddenExtendedCaseFields.length > 0) {
        var fields = $hiddenExtendedCaseFields.val();
        if (fields !== null && fields.length > 0) {
            fields = JSON.parse(fields);
            if (fields !== null) {
                for (var i = 0; i < fields.length; i++) {
                    AddExtendedCaseField(fields[i]);
                }
            }
        }
    }

    $hiddenExtendedProfileBusinessCodeFields = $('#hiddenExtendedProfileBusinessCodeFields');
    $tableExtendedProfileBusinessCodeFields = $('#tableExtendedProfileBusinessCodeFields');
    $aExtendedProfileBusinessCodeFieldsAdd = $('#aExtendedProfileBusinessCodeFieldsAdd');
    $aExtendedProfileBusinessCodeFieldsAdd.click(AddExtendedProfileBusinessCodeFields);

    if ($hiddenExtendedProfileBusinessCodeFields.length > 0) {
        var fields = $hiddenExtendedProfileBusinessCodeFields.val();
        if (fields !== null && fields.length > 0) {
            fields = JSON.parse(fields);
            if (fields != null) {
                for (var i = 0; i < fields.length; i++) {
                    AddExtendedProfileBusinessCodeFields(fields[i]);
                }
            }
        }
    }

    $dropdownlistAuthenticationType = $('#dropdownlistAuthenticationType');
    $divGoogleAuth = $('#divGoogleAuth');
    $checkboxGoogleAuthEnabled = $('#checkboxGoogleAuthEnabled');
    $checkboxGoogleAuthUseCustom = $('#checkboxGoogleAuthUseCustom');
    $trGoogleAuthClientID = $('#trGoogleAuthClientID');
    $textboxGoogleAuthClientID = $('#textboxGoogleAuthClientID');
    $trGoogleAuthClientSecret = $('#trGoogleAuthClientSecret');
    $textboxGoogleAuthClientSecret = $('#textboxGoogleAuthClientSecret');
    $divLoginWithKeycloak = $('#divLoginWithKeycloak');
    $checkboxKeycloakAuthEnabled = $('#checkboxKeycloakAuthEnabled');

    $dropdownlistAuthenticationType.change(function () {
        var value = $dropdownlistAuthenticationType.val();
        $checkboxLdapUseLdap.prop('checked', value === '2').trigger('change');
        $divLdap.toggle(value === '2');
        $checkboxGoogleAuthEnabled.prop('checked', value === '3');
        $divGoogleAuth.toggle(value === '3');
        $checkboxKeycloakAuthEnabled.prop('checked', value === '4');
        $divLoginWithKeycloak.toggle(value === '4');
    }).trigger('change');

    $checkboxGoogleAuthUseCustom.change(function () {
        $trGoogleAuthClientID.toggle($checkboxGoogleAuthEnabled.is(':checked') && $checkboxGoogleAuthUseCustom.is(':checked'));
        $trGoogleAuthClientSecret.toggle($checkboxGoogleAuthEnabled.is(':checked') && $checkboxGoogleAuthUseCustom.is(':checked'));
    }).trigger('change');

    $checkboxUseACDBalancing = $('#checkboxUseACDBalancing');
    $trACDBalancingWithQueueLevels = $('#trACDBalancingWithQueueLevels');
    $trACDBalancingWithQueueLevelsAndWorking = $('#trACDBalancingWithQueueLevelsAndWorking');
    $dropdownlistACDBalancingWithQueueLevels = $('#dropdownlistACDBalancingWithQueueLevels');

    $checkboxUseACDBalancing.change(function () {
        $trACDBalancingWithQueueLevels.toggle(this.checked);
        $trACDBalancingWithQueueLevelsAndWorking.toggle(this.checked && $dropdownlistACDBalancingWithQueueLevels.val() === '0');
    }).trigger('change');

    $dropdownlistACDBalancingWithQueueLevels.change(function () {
        $trACDBalancingWithQueueLevelsAndWorking.toggle($checkboxUseACDBalancing.is(':checked') && $dropdownlistACDBalancingWithQueueLevels.val() === '0');
    }).trigger('change');

    $dropdownlistChatCloudSameServer = $('#dropdownlistChatCloudSameServer');
    $trChatCloudOtherServer = $('#trChatCloudOtherServer');
    $textboxChatCloudOtherServer = $('#textboxChatCloudOtherServer');
    $dropdownlisCloudOtherServerPortYSocialOverHttps = $('#dropdownlisCloudOtherServerPortYSocialOverHttps');
    $dropdownlistChatCloudSameServer.change(function () {
        var isInTheSameServer = parseInt($dropdownlistChatCloudSameServer.val()) === 1;
        $trChatCloudOtherServer.toggle(!isInTheSameServer);

        if (isInTheSameServer) {
            $dropdownlisCloudOtherServerPortYSocialOverHttps.val('0');
            $dropdownlisCloudOtherServerPortYSocialOverHttps.attr('disabled', 'disabled');
        }
        else {
            $dropdownlisCloudOtherServerPortYSocialOverHttps.removeAttr('disabled');
        }
    }).trigger('change');

    $panelTimeZoneConfiguration = $('#panelTimeZoneConfiguration');
    if ($panelTimeZoneConfiguration.length > 0) {
        $divTimeZonesForConsolidation = $('#divTimeZonesForConsolidation');
        $hiddenDefaultTimeZone = $('#hiddenDefaultTimeZone');
        $selectDefaultTimeZone = $('#selectDefaultTimeZone');
        $hiddenTimeZonesToConsolide = $('#hiddenTimeZonesToConsolide');

        var defaultTimeZone = $hiddenDefaultTimeZone.val();
        for (var i = 0; i < timeZones.length; i++) {
            var $option = $('<option></option>');
            $option.val(timeZones[i].Id);
            $option.text(timeZones[i].DisplayName);

            if (timeZones[i].Id === defaultTimeZone) {
                $option.attr('selected', 'selected');
            }

            $selectDefaultTimeZone.append($option);
        }

        $selectDefaultTimeZone.change(function () {
            $hiddenDefaultTimeZone.val($selectDefaultTimeZone.val());
        });

        var timeZonesToConsolide = $hiddenTimeZonesToConsolide.val();
        if (typeof (timeZonesToConsolide) !== 'undefined' &&
            timeZonesToConsolide !== null) {
            var initialData = [];
            if (timeZonesToConsolide.length > 0) {
                timeZonesToConsolide = timeZonesToConsolide.split(',');
                for (var i = 0; i < timeZonesToConsolide.length; i++) {
                    initialData.push({ timezone: timeZonesToConsolide[i] });
                }
            }
            BuildDynamicTable({
                showHeaders: false,
                maxRows: 3,
                columns: [{
                    type: 'select',
                    options: timeZones,
                    valueField: 'Id',
                    textField: 'DisplayName',
                    key: 'timezone',
                    defaultValue: currentTimeZone.Id
                }],
                container: $divTimeZonesForConsolidation,
                initialData: initialData
            });
        }
    }

    $textboxWebAgentURL = $('#textboxWebAgentURL');
    $textboxWebAgentURL.blur(function () {
        var $this = $(this);
        var $parentDiv = $this.parent();
        var url = $this.val();

        if (url.length > 0) {
            var dataToSend = JSON.stringify({ url: url });

            $parentDiv.removeClass('ok');
            $parentDiv.removeClass('error');
            $parentDiv.addClass('spin');

            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/IsWebAgentURLValid",
                data: dataToSend,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    $parentDiv.removeClass('spin');
                    if (data.d.Success && data.d.IsValid) {
                        $parentDiv.addClass('ok');
                        $('.more-info', $parentDiv).text(data.d.Version);
                    }
                    else {
                        $parentDiv.addClass('error');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $parentDiv.removeClass('spin');
                    $parentDiv.addClass('error');
                }
            });
        }
    }).trigger('blur');

    $textboxWhatsappUrlRtNotifications = $('#textboxWhatsappUrlRtNotifications');
    $textboxWhatsappUrlRtNotifications.blur(function () {
        var $this = $(this);
        var $parentDiv = $this.parent();
        var url = $this.val();

        if (url.length > 0) {
            var dataToSend = JSON.stringify({ url: url });

            $parentDiv.removeClass('ok');
            $parentDiv.removeClass('error');
            $parentDiv.addClass('spin');

            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/IsWhatsappUrlRtNotificationsValid",
                data: dataToSend,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    $parentDiv.removeClass('spin');
                    if (data.d.Success && data.d.IsValid) {
                        $parentDiv.addClass('ok');
                    }
                    else {
                        $parentDiv.addClass('error');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $parentDiv.removeClass('spin');
                    $parentDiv.addClass('error');
                }
            });
        }
    }).trigger('blur');

    $textboxTelegramUrlRtNotifications = $('#textboxTelegramUrlRtNotifications');
    $textboxTelegramUrlRtNotifications.blur(function () {
        var $this = $(this);
        var $parentDiv = $this.parent();
        var url = $this.val();

        if (url.length > 0) {
            var dataToSend = JSON.stringify({ url: url });

            $parentDiv.removeClass('ok');
            $parentDiv.removeClass('error');
            $parentDiv.addClass('spin');

            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/IsTelegramUrlRtNotificationsValid",
                data: dataToSend,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    $parentDiv.removeClass('spin');
                    if (data.d.Success && data.d.IsValid) {
                        $parentDiv.addClass('ok');
                    }
                    else {
                        $parentDiv.addClass('error');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $parentDiv.removeClass('spin');
                    $parentDiv.addClass('error');
                }
            });
        }
    }).trigger('blur');

    $hiddenCloudHeadersToAdd = $('#hiddenCloudHeadersToAdd');
    $divCloudHeadersToAdd = $('#divCloudHeadersToAdd');
    $hiddenCloudHeadersToRemove = $('#hiddenCloudHeadersToRemove');
    $divCloudHeadersToRemove = $('#divCloudHeadersToRemove');

    if ($hiddenCloudHeadersToAdd.length > 0) {
        var initialData = null;
        var data = $hiddenCloudHeadersToAdd.val();
        if (data !== null && data.length > 0) {
            initialData = JSON.parse(data);
        }
        BuildDynamicTable({
            showHeaders: true,
            columns: [
                {
                    header: {
                        title: 'Nombre',
                        'data-i18n': 'globals-name'
                    },
                    key: 'name',
                    type: 'text',
                    placeholder: 'Nombre del encabezado HTTP',
                    'data-i18n-placeholder': 'configuration-systemsettings-cloud_headers-name-placeholder'
                },
                {
                    header: {
                        title: 'Valor',
                        'data-i18n': 'configuration-systemsettings-value'
                    },
                    key: 'value',
                    type: 'text',
                    placeholder: 'Valor del encabezado',
                    'data-i18n-placeholder': 'configuration-systemsettings-cloud_headers-value-placeholder'
                }
            ],
            container: $divCloudHeadersToAdd,
            initialData: initialData
        });
    }

    if ($hiddenCloudHeadersToRemove.length > 0) {
        var initialData = null;
        var data = $hiddenCloudHeadersToRemove.val();
        if (data !== null && data.length > 0) {
            initialData = JSON.parse(data);
        }
        BuildDynamicTable({
            showHeaders: true,
            columns: [
                {
                    header: {
                        title: 'Nombre',
                        'data-i18n': 'globals-name'
                    },
                    key: 'name',
                    type: 'text',
                    placeholder: 'Nombre del encabezado HTTP',
                    'data-i18n-placeholder': 'configuration-systemsettings-cloud_headers-name-placeholder'
                }
            ],
            container: $divCloudHeadersToRemove,
            initialData: initialData
        });
    }


    $tabsSystemSettings = $('#tabsSystemSettings')
    $tabsSystemSettings.tabs({
        activate: function (tabs, page) {
            var $divTab;
            if ((page.newPanel instanceof jQuery)) {
                $divTab = page.newPanel;
            }
            else {
                $divTab = $(page.newPanel.selector);
            }
            var tabId = $divTab.get(0).id;
            var $cleditors = $('textarea[id$=Template],textarea[id$=MailMaskBody]', $divTab);
            $cleditors.each(function (index) {
                var $this = $(this);
                if (typeof ($this.cleditor) === 'function') {
                    var editors = $this.cleditor();
                    if (editors.length > 0) {
                        editors[0].refresh();
                    }
                }
            });

            $hiddenTab.val(tabId);

            if (history.pushState) {
                history.pushState(null, null, '#' + tabId);
            }
            else {
                location.hash = '#' + tabId;
            }
        }
    });

    var param = $(document).getUrlParam('tab');
    if (param && param != '') {
        $tabsSystemSettings.tabs('select', 'div' + param);
    }
    else {
        if ($hiddenTab.val() != '') {
            $tabsSystemSettings.tabs('select', $hiddenTab.val());
        }
    }

    var $textboxMinutesPredictedAht = $('#textboxMinutesPredictedAht');
    var $spanMinutesPredictedAht = $('#spanMinutesPredictedAht');
    $textboxMinutesPredictedAht.change(function () {
        if (translationsLoaded) {
            $spanMinutesPredictedAht.text($.i18n('configuration-systemsettings-queues-minutes', $textboxMinutesPredictedAht.val()));
        }
        else {
            $spanMinutesPredictedAht.text($textboxMinutesPredictedAht.val());
        }
    }).trigger('change');

    var $textboxSecondsEwt = $('#textboxSecondsEwt');
    var $spanSecondsEwt = $('#spanSecondsEwt');
    $textboxSecondsEwt.change(function () {
        if (translationsLoaded) {
            $spanSecondsEwt.text($.i18n('configuration-systemsettings-queues-seconds', $textboxSecondsEwt.val()));
        }
        else {
            $spanSecondsEwt.text($textboxSecondsEwt.val());
        }
    }).trigger('change');

    var $checkBoxAsaDefault = $('input[type=checkbox][id$=checkboxASAPersonalized]');
    var $tableASADefault = $('table[rel=tableASABase]');
    var $textboxAsaDefault = $('input[id$=textboxAsaBase]')
    $textboxAsaDefault.prop('required', $checkBoxAsaDefault.is(':checked'));
    if (!$checkBoxAsaDefault.is(':checked')) {
        $textboxAsaDefault.prop('min', '');
    }
    $checkBoxAsaDefault.change(function () {
        if (this.checked) {
            $textboxAsaDefault.prop('min', '30');
            $tableASADefault.show();

        }
        else {
            $textboxAsaDefault.prop('min', '');
            $tableASADefault.hide();
        }
        $textboxAsaDefault.prop('required', this.checked);

    }).trigger('change');

    var $checkboxEnableCapi = $('input[type=checkbox][id$=checkboxEnableCapi]');
    var $subseccionCapi = $('div[rel=subseccionCapi]');

    $checkboxEnableCapi.change(function () {
        if (this.checked) {
            $subseccionCapi.show();

        }
        else {
            $subseccionCapi.hide();
        }

    }).trigger('change');

    $checkboxCheckLastQueueByTime = $('input[type=checkbox][id$=checkboxCheckLastQueueByTime]');
    $tableLastQueueByTime = $('table[rel=tableLastQueueByTime]');
    $textboxLastQueueByTime = $('input[id$=textboxLastQueueByTime]');
    $textboxLastQueueByTime.prop('required', $checkboxCheckLastQueueByTime.is(':checked'));
    if (!$checkboxCheckLastQueueByTime.is(':checked')) {
        $textboxLastQueueByTime.prop('min', '');
        $textboxLastQueueByTime.prop('max', '');
    }
    $checkboxCheckLastQueueByTime.change(function () {
        if (this.checked) {
            $textboxLastQueueByTime.prop('min', '60');
            $textboxLastQueueByTime.prop('max', '600');
            $tableLastQueueByTime.show();
        }
        else {
            $textboxLastQueueByTime.prop('min', '');
            $textboxLastQueueByTime.prop('max', '');
            $tableLastQueueByTime.hide();
        }
        $textboxLastQueueByTime.prop('required', this.checked);
    }).trigger('change');

    //$hiddenTagInvocation.val(tagInvocationFailed);
    //$hiddenTagTimeout.val(tagTimeoutFailed);

    var $datalistTickmarks = $('#datalistTickmarks');
    var $textboxPushNotificationsServiceBusConcurrentMessages = $('#textboxPushNotificationsServiceBusConcurrentMessages');
    var max = parseInt($textboxPushNotificationsServiceBusConcurrentMessages.attr('max'), 10);
    var min = parseInt($textboxPushNotificationsServiceBusConcurrentMessages.attr('min'), 10);
    for (let i = min; i <= max; i++) {
        if (i % 10 === 0) {
            let $option = $('<option />');
            $option.val(i.toString());
            $option.attr('label', i.toString());
            $datalistTickmarks.append($option);
        }
    }

    $textboxPushNotificationsServiceBusConcurrentMessages.attr('list', 'datalistTickmarks');
    var $spanPushNotificationsServiceBusConcurrentMessages = $('#spanPushNotificationsServiceBusConcurrentMessages');
    $textboxPushNotificationsServiceBusConcurrentMessages.change(function () {
        $spanPushNotificationsServiceBusConcurrentMessages.text($textboxPushNotificationsServiceBusConcurrentMessages.val());
    }).trigger('change');

    var $textboxCasesCloseConcurrent = $('#textboxCasesCloseConcurrent');
    $textboxCasesCloseConcurrent.attr('list', 'datalistTickmarks');
    var $spanCasesCloseConcurrent = $('#spanCasesCloseConcurrent');
    $textboxCasesCloseConcurrent.change(function () {
        $spanCasesCloseConcurrent.text($textboxCasesCloseConcurrent.val());
    }).trigger('change');

    var $textboxPushNotificationsServiceBusConcurrentStatuses = $('#textboxPushNotificationsServiceBusConcurrentStatuses');
    $textboxPushNotificationsServiceBusConcurrentStatuses.attr('list', 'datalistTickmarks');
    var $spanPushNotificationsServiceBusConcurrentStatuses = $('#spanPushNotificationsServiceBusConcurrentStatuses');
    $textboxPushNotificationsServiceBusConcurrentStatuses.change(function () {
        $spanPushNotificationsServiceBusConcurrentStatuses.text($textboxPushNotificationsServiceBusConcurrentStatuses.val());
    }).trigger('change');

    var $textboxPushNotificationsServiceBusConcurrentMassive = $('#textboxPushNotificationsServiceBusConcurrentMassive');
    $textboxPushNotificationsServiceBusConcurrentMassive.attr('list', 'datalistTickmarks');
    var $spanPushNotificationsServiceBusConcurrentMassive = $('#spanPushNotificationsServiceBusConcurrentMassive');
    $textboxPushNotificationsServiceBusConcurrentMassive.change(function () {
        $spanPushNotificationsServiceBusConcurrentMassive.text($textboxPushNotificationsServiceBusConcurrentMassive.val());
    }).trigger('change');

    var $textboxYFlowMaxConcurrentSessionsPendingMessages = $('#textboxYFlowMaxConcurrentSessionsPendingMessages');
    $textboxYFlowMaxConcurrentSessionsPendingMessages.attr('list', 'datalistTickmarks');
    var $spanYFlowMaxConcurrentSessionsPendingMessages = $('#spanYFlowMaxConcurrentSessionsPendingMessages');
    $textboxYFlowMaxConcurrentSessionsPendingMessages.change(function () {
        $spanYFlowMaxConcurrentSessionsPendingMessages.text($textboxYFlowMaxConcurrentSessionsPendingMessages.val());
    }).trigger('change');

    var $textboxYFlowMaxConcurrentCallsPerSessionPendingMessages = $('#textboxYFlowMaxConcurrentCallsPerSessionPendingMessages');
    $textboxYFlowMaxConcurrentCallsPerSessionPendingMessages.attr('list', 'datalistTickmarks');
    var $spanYFlowMaxConcurrentCallsPerSessionPendingMessages = $('#spanYFlowMaxConcurrentCallsPerSessionPendingMessages');
    $textboxYFlowMaxConcurrentCallsPerSessionPendingMessages.change(function () {
        $spanYFlowMaxConcurrentCallsPerSessionPendingMessages.text($textboxYFlowMaxConcurrentCallsPerSessionPendingMessages.val());
    }).trigger('change');

    var $textboxYFlowPendingReplyCasesConcurrentCalls = $('#textboxYFlowPendingReplyCasesConcurrentCalls');
    $textboxYFlowPendingReplyCasesConcurrentCalls.attr('list', 'datalistTickmarks');
    var $spanYFlowPendingReplyCasesConcurrentCalls = $('#spanYFlowPendingReplyCasesConcurrentCalls');
    $textboxYFlowPendingReplyCasesConcurrentCalls.change(function () {
        $spanYFlowPendingReplyCasesConcurrentCalls.text($textboxYFlowPendingReplyCasesConcurrentCalls.val());
    }).trigger('change');

    var $textboxRestChatMaxConcurrentSessions = $('#textboxRestChatMaxConcurrentSessions');
    $textboxRestChatMaxConcurrentSessions.attr('list', 'datalistTickmarks');
    var $spanRestChatMaxConcurrentSessions = $('#spanRestChatMaxConcurrentSessions');
    $textboxRestChatMaxConcurrentSessions.change(function () {
        $spanRestChatMaxConcurrentSessions.text($textboxRestChatMaxConcurrentSessions.val());
    }).trigger('change');

    var $textboxRestChatMaxConcurrentCallsPerSession = $('#textboxRestChatMaxConcurrentCallsPerSession');
    $textboxRestChatMaxConcurrentCallsPerSession.attr('list', 'datalistTickmarks');
    var $spanRestChatMaxConcurrentCallsPerSession = $('#spanRestChatMaxConcurrentCallsPerSession');
    $textboxRestChatMaxConcurrentCallsPerSession.change(function () {
        $spanRestChatMaxConcurrentCallsPerSession.text($textboxRestChatMaxConcurrentCallsPerSession.val());
    }).trigger('change');

    var $textboxWhatsAppServiceVoiceCallsIceServers = $('#textboxWhatsAppServiceVoiceCallsIceServers');
    $textboxWhatsAppServiceVoiceCallsIceServers.blur(function () {
        let value = $textboxWhatsAppServiceVoiceCallsIceServers.val();
        try {
            value = JSON.stringify(JSON.parse(value), null, '  ');
            $textboxWhatsAppServiceVoiceCallsIceServers.val(value)
        }
        catch (e) { }
    });

    var $listboxTags = $('#listboxTags');
    $listboxTags.multiselect({ multiple: true, noneSelectedText: $.i18n("Seleccionar"), selectedList: 20, buttonWidth: '>500' }).multiselectfilter();

    var $listboxGroupTags = $('#listboxGroupTags');
    $listboxGroupTags.multiselect({ multiple: true, noneSelectedText: $.i18n("Seleccionar"), selectedList: 20, buttonWidth: '>500' }).multiselectfilter();

    var $listboxMetaEvents = $('#listboxMetaEvents');
    $listboxMetaEvents.multiselect({ multiple: false, noneSelectedText: $.i18n("Seleccionar"), selectedList: 1, buttonWidth: '>500' }).multiselectfilter();

    RetrieveTags();
});

let usedMetaEvents = new Set();
let usedTagIds = new Set();
let usedGroupTagIds = new Set(); // Track used group tag IDs

// Helper function to get individual tag IDs from a Group Tag
function getIndividualTagIdsFromGroupTag(groupTagId, callback) {
    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/GetTagsByGroupTag",
        data: JSON.stringify({ groupTagId: parseInt(groupTagId) }),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                callback(data.d.TagIds || []);
            } else {
                console.error('Error getting tags for group:', data.d.Error);
                callback([]);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error('AJAX error getting tags for group:', errorThrown);
            callback([]);
        }
    });
}

function acceptValues() {
    const $metaSelect = $('#listboxMetaEvents');
    const $tagsSelect = $('#listboxTags');
    const $groupTagsSelect = $('#listboxGroupTags');

    const metaEventText = $metaSelect.find("option:selected").text();
    const metaEventVal = $metaSelect.val();

    const selectedTags = $tagsSelect.find("option:selected").map(function () {
        return { text: $(this).text(), value: $(this).val() };
    }).get();

    const selectedGroupTags = $groupTagsSelect.find("option:selected").map(function () {
        return { text: $(this).text(), value: $(this).val() };
    }).get();

    console.log('acceptValues - metaEventVal:', metaEventVal, 'type:', typeof metaEventVal); // Debug
    console.log('acceptValues - usedMetaEvents contains:', Array.from(usedMetaEvents)); // Debug

    // Validation: Must select META event and either individual tags OR group tags (not both)
    if (!metaEventVal) {
        alert("Debes seleccionar un evento de Meta.");
        return;
    }

    if (selectedTags.length === 0 && selectedGroupTags.length === 0) {
        alert("Debes seleccionar al menos una etiqueta individual o un grupo de etiquetas.");
        return;
    }

    if (selectedTags.length > 0 && selectedGroupTags.length > 0) {
        alert("Debes seleccionar SOLO etiquetas individuales O grupos de etiquetas, no ambos.");
        return;
    }

    // Evitar duplicados - Asegurar que comparamos strings con strings
    const metaEventValStr = String(metaEventVal);
    if (usedMetaEvents.has(metaEventValStr)) {
        console.log('Duplicate detected - metaEventVal already used:', metaEventValStr); // Debug
        return;
    }

    // Handle individual tags association
    if (selectedTags.length > 0) {
        // Check for conflicts with existing associations
        const conflictingTags = selectedTags.filter(tag => usedTagIds.has(String(tag.value)));
        if (conflictingTags.length > 0) {
            alert(`Las siguientes etiquetas ya están asociadas: ${conflictingTags.map(t => t.text).join(', ')}`);
            return;
        }

        // Create association with individual tags
        createIndividualTagsAssociation(metaEventValStr, metaEventText, selectedTags, $metaSelect, $tagsSelect);
    }
    // Handle group tags association
    else if (selectedGroupTags.length > 0) {
        // For group tags, we need to check conflicts asynchronously
        checkGroupTagsConflictsAndCreate(metaEventValStr, metaEventText, selectedGroupTags, $metaSelect, $groupTagsSelect);
    }
}

function createIndividualTagsAssociation(metaEventValStr, metaEventText, selectedTags, $metaSelect, $tagsSelect) {
    // Agregar a tabla
    const tagsText = selectedTags.map(t => t.text).join(", ");
    const row = `<tr data-meta-id="${metaEventValStr}" data-tag-ids='${JSON.stringify(selectedTags.map(t => t.value))}' data-association-type="individual">
      <td><a href="#" class="deleteAssociation"><span class="fa fa-lg fa-minus-square" aria-hidden="true"></span></a></td>
      <td>${metaEventText}</td>
      <td>${tagsText}</td>
    </tr>`;
    $('#createdAssociationsTable tbody').append(row);

    // Registrar como usados - Asegurar que almacenamos como strings
    usedMetaEvents.add(metaEventValStr);
    selectedTags.forEach(tag => usedTagIds.add(String(tag.value)));

    console.log('Added to usedMetaEvents:', metaEventValStr, 'Set now contains:', Array.from(usedMetaEvents)); // Debug

    // Eliminar opciones seleccionadas
    $metaSelect.find(`option[value='${metaEventValStr}']`).remove();
    selectedTags.forEach(tag => $tagsSelect.find(`option[value='${tag.value}']`).remove());

    // Refrescar multiselects
    $metaSelect.multiselect('refresh');
    $tagsSelect.multiselect('refresh');

    // Reset
    resetValues();
}

function checkGroupTagsConflictsAndCreate(metaEventValStr, metaEventText, selectedGroupTags, $metaSelect, $groupTagsSelect) {
    // We need to check each group tag for conflicts
    let totalGroupTagsToCheck = selectedGroupTags.length;
    let checkedGroupTags = 0;
    let allIndividualTagIds = [];
    let conflictingTags = [];
    let groupTagsData = [];

    selectedGroupTags.forEach(groupTag => {
        getIndividualTagIdsFromGroupTag(groupTag.value, function(individualTagIds) {
            checkedGroupTags++;

            // Store group tag data
            groupTagsData.push({
                groupTagId: groupTag.value,
                groupTagText: groupTag.text,
                individualTagIds: individualTagIds
            });

            // Check for conflicts
            const conflictingIds = individualTagIds.filter(tagId => usedTagIds.has(String(tagId)));
            if (conflictingIds.length > 0) {
                conflictingTags.push(`${groupTag.text} (contiene etiquetas ya asociadas)`);
            }

            // Accumulate all individual tag IDs
            allIndividualTagIds = allIndividualTagIds.concat(individualTagIds);

            // When all group tags have been checked
            if (checkedGroupTags === totalGroupTagsToCheck) {
                if (conflictingTags.length > 0) {
                    alert(`Los siguientes grupos de etiquetas contienen etiquetas ya asociadas: ${conflictingTags.join(', ')}`);
                    return;
                }

                // No conflicts, create the association
                createGroupTagsAssociation(metaEventValStr, metaEventText, selectedGroupTags, allIndividualTagIds, groupTagsData, $metaSelect, $groupTagsSelect);
            }
        });
    });
}

function createGroupTagsAssociation(metaEventValStr, metaEventText, selectedGroupTags, allIndividualTagIds, groupTagsData, $metaSelect, $groupTagsSelect) {
    // Agregar a tabla - for group tags, we store group tag info differently
    const groupTagsText = selectedGroupTags.map(t => t.text).join(", ");
    const row = `<tr data-meta-id="${metaEventValStr}" data-group-tag-ids='${JSON.stringify(selectedGroupTags.map(t => t.value))}' data-individual-tag-ids='${JSON.stringify(allIndividualTagIds)}' data-association-type="group">
      <td><a href="#" class="deleteAssociation"><span class="fa fa-lg fa-minus-square" aria-hidden="true"></span></a></td>
      <td>${metaEventText}</td>
      <td><strong>[Grupos]</strong> ${groupTagsText}</td>
    </tr>`;
    $('#createdAssociationsTable tbody').append(row);

    // Registrar como usados
    usedMetaEvents.add(metaEventValStr);
    selectedGroupTags.forEach(groupTag => usedGroupTagIds.add(String(groupTag.value)));
    allIndividualTagIds.forEach(tagId => usedTagIds.add(String(tagId)));

    console.log('Added group tags association - metaEvent:', metaEventValStr, 'groupTags:', selectedGroupTags.map(t => t.value), 'individualTags:', allIndividualTagIds); // Debug

    // Eliminar opciones seleccionadas
    $metaSelect.find(`option[value='${metaEventValStr}']`).remove();
    selectedGroupTags.forEach(groupTag => $groupTagsSelect.find(`option[value='${groupTag.value}']`).remove());

    // Refrescar multiselects
    $metaSelect.multiselect('refresh');
    $groupTagsSelect.multiselect('refresh');

    // Reset
    resetValues();
}

function resetValues() {
    $('#listboxMetaEvents').val([]).multiselect('refresh');
    $('#listboxTags').val([]).multiselect('refresh');
    $('#listboxGroupTags').val([]).multiselect('refresh');
}

$('#btnAccept').on('click', acceptValues);
$('#btnCancel').on('click', resetValues);

// Debug: Test simple para verificar que los eventos funcionan
$(document).ready(function() {
    console.log('SystemSettings.js loaded - jQuery ready');

    // Test adicional para verificar que el selector funciona
    setTimeout(function() {
        var deleteButtons = $('#createdAssociationsTable .deleteAssociation');
        console.log('Found delete buttons:', deleteButtons.length);
    }, 1000);
});

// Event handler para eliminar asociaciones - usando múltiples selectores para asegurar que funcione
$(document).on('click', '#createdAssociationsTable .deleteAssociation', function (e) {
    e.preventDefault(); // Prevenir el comportamiento por defecto del enlace
    e.stopPropagation(); // Detener la propagación del evento

    console.log('Delete button clicked!'); // Debug log

    const $row = $(this).closest('tr');
    const metaId = $row.data('meta-id');
    const associationType = $row.data('association-type');

    console.log('Meta ID:', metaId, 'Association Type:', associationType); // Debug adicional

    // Restaurar evento META - corregido: usar la segunda columna (índice 2)
    const metaText = $row.find('td:nth-child(2)').text();
    $('#listboxMetaEvents').append(`<option value="${metaId}">${metaText}</option>`);

    // Handle deletion based on association type
    if (associationType === 'individual') {
        deleteIndividualTagsAssociation($row);
    } else if (associationType === 'group') {
        deleteGroupTagsAssociation($row);
    } else {
        // Fallback for existing associations without type (assume individual)
        deleteIndividualTagsAssociation($row);
    }

    // Eliminar fila y limpiar sets - CORREGIDO: Convertir a string para consistencia
    $row.remove();
    const metaIdStr = String(metaId); // Convertir a string
    usedMetaEvents.delete(metaIdStr);
    console.log('Removed from usedMetaEvents:', metaIdStr, 'Set now contains:', Array.from(usedMetaEvents)); // Debug

    // Refrescar multiselects
    $('#listboxMetaEvents').multiselect('refresh');

    return false; // Asegurar que no se ejecute el comportamiento por defecto
});

function deleteIndividualTagsAssociation($row) {
    const tagIds = $row.data('tag-ids');

    console.log('Deleting individual tags association - Tag IDs:', tagIds); // Debug

    // Restaurar tags - corregido: usar la tercera columna (índice 3)
    const tagsText = $row.find('td:nth-child(3)').text().split(', ');
    tagIds.forEach((tagId, idx) => {
        const tagText = tagsText[idx];
        $('#listboxTags').append(`<option value="${tagId}">${tagText}</option>`);

        // Remove from used tags
        const tagIdStr = String(tagId);
        usedTagIds.delete(tagIdStr);
    });

    // Refrescar multiselect
    $('#listboxTags').multiselect('refresh');

    console.log('usedTagIds now contains:', Array.from(usedTagIds)); // Debug
}

function deleteGroupTagsAssociation($row) {
    const groupTagIds = $row.data('group-tag-ids');
    const individualTagIds = $row.data('individual-tag-ids');

    console.log('Deleting group tags association - Group Tag IDs:', groupTagIds, 'Individual Tag IDs:', individualTagIds); // Debug

    // Restaurar group tags - extract group tag names from the display text
    const displayText = $row.find('td:nth-child(3)').text();
    const groupTagsText = displayText.replace('[Grupos] ', '').split(', ');

    groupTagIds.forEach((groupTagId, idx) => {
        const groupTagText = groupTagsText[idx];
        $('#listboxGroupTags').append(`<option value="${groupTagId}">${groupTagText}</option>`);

        // Remove from used group tags
        const groupTagIdStr = String(groupTagId);
        usedGroupTagIds.delete(groupTagIdStr);
    });

    // Remove individual tag IDs from used tags (these were added when the group was associated)
    individualTagIds.forEach(tagId => {
        const tagIdStr = String(tagId);
        usedTagIds.delete(tagIdStr);
    });

    // Refrescar multiselect
    $('#listboxGroupTags').multiselect('refresh');

    console.log('usedGroupTagIds now contains:', Array.from(usedGroupTagIds)); // Debug
    console.log('usedTagIds now contains:', Array.from(usedTagIds)); // Debug
}

function RetrieveTags() {
    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/RetrieveTags",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                LoadTags(data.d.Tags);
            }
            else {
                if (console)
                    console.log('Ocurrió un error al obtener etiquetas' + data.d.Error.Message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (console)
                console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + jqXHR.responseText);
        }
    });
}
function LoadTags(tags) {

    if (typeof (tags) === 'undefinded' || tags == null) {
        tags = {};
    }

    const tagConfigs = [
        { selector: '#textboxTagCloseCase', property: 'TagOnAutoCloseCase' },
        { selector: '#textboxTagOnHsmCases', property: 'TagOnAutoCloseHsmCases' },
        { selector: '#textboxTagInvocation', property: 'TagInvocationFailed' },
        { selector: '#textboxTagTimeout', property: 'TagTimeoutFailed' },
        { selector: '#textboxTagLabel', property: 'TagInvocationFailedLabel' }
    ];

    tagConfigs.forEach(config => {
        const tagData = getTagData(tags[config.property]);
        populateTag($(config.selector), tagData);
    });
}

function getTagData(tag) {
    if (typeof tag === 'object' && tag !== null) {
        return [tag];
    }
    return null;
}

function populateTag($tag, failedTag) {
    $tag.tokenInput("../Reports/Cases.aspx/SearchTags", {
        method: 'POST',
        queryParam: 'q',
        searchDelay: 300,
        minChars: 3,
        tokenLimit: 1,
        propertyToSearch: 'FullName',
        preventDuplicates: true,
        tokenValue: 'ID',
        contentType: 'json',
        resultsLimit: 20,
        excludeCurrent: true,
        styles: {
            dropdown: {
                'max-height': '200px',
                'overflow-y': 'auto'
            },
            tokenList: {
                'width': '100%'
            }
        },
        prePopulate: failedTag,
        onSend: function (params) {
            params.data = JSON.stringify(params.data);
            params.contentType = "application/json; charset=utf-8";
        },
        onResult: function (results, query) {
            const settingsTags = $tag.data('settings');
            if (typeof settingsTags.local_data === 'undefined') {
                delete settingsTags.url;
                settingsTags.local_data = results.d;

                return settingsTags.local_data.filter(row =>
                    row[settingsTags.propertyToSearch].toLowerCase().includes(query.toLowerCase())
                );
            }
            return results;
        }
    });
}
function RetrieveProjects(callbackSuccess, callbackError) {
    LoadingDialog({
        title: $.i18n('configuration-systemsettings-ysmart-retrieving_projects'),
        autoClose: false,
        onTimeout: function () {
            $.ajax({
                url: "SystemSettings.aspx/GetProjectsYSmart",
                method: "POST",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify({}),
                success: function (response) {

                    if (response.d.Success) {
                        callbackSuccess(response.d.Data);
                    } else {
                        callbackError(response.d.Data);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error("Error en AJAX:", {
                        status: jqXHR.status,
                        responseText: jqXHR.responseText,
                        textStatus: textStatus,
                        errorThrown: errorThrown
                    });
                    callbackError(errorThrown)
                }
            });
        },
        timeout: 500
    });
}
function ReloadProjects() {
    
    $('span.fa', $anchorProjectReload).addClass('fa-spin');
    RetrieveProjects(function (projects) {
        if (projects !== null && projects.length > 0) {
            let currentProject = $hiddenProject.val();
            let currentId = $('#hiddenProjectId').val();
            if (typeof (currrentProject) !== 'undefined' && currentProject !== null && currentProject.length > 0) {
                    currentProject = JSON.parse(currentProject);
                } else if (currentId) {
                   
                    currentProject = projects.find(p => p.id === +currentId) || null;
                }
                else {
                    currentProject = null;
                }
                $selectProjectToUse.empty();
                for (var i = 0; i < projects.length; i++) {
                    var $option = $('<option />');
                    $option.val(projects[i].id);
                    $option.text(projects[i].name);
                    $option.prop('definition', projects[i]);
                    if (currentProject && projects[i].id === currentProject.id) {
                        $option.prop('selected', true); 
                    }
                    $selectProjectToUse.append($option);
                }

            if (currentProject !== null) {
                if (typeof (currentProject.id) !== 'undefined') {
                    $selectProjectToUse.val(currentProject.id);
                    }
                    else {
                    $selectProjectToUse.val(currentProject.id);
                }
                $hiddenProject.val(JSON.stringify(currentProject));
                }
                $selectProjectToUse.trigger('change');
                $.colorbox.close();
            }
            else {
            AlertDialog('Project', $.i18n("configuration-systemsettings-ysmart-projects_available"));
            }

            $('span.fa', $anchorProjectReload).removeClass('fa-spin');
        },
        function (err) {
            console.error('Ocurrió un error intentando obtener el listado de Projectos de ySmart: %o', err);
            AlertDialog('Project', $.i18n("configuration-systemsettings-ysmart-projects_error"), undefined, undefined, 'error');
            $('span.fa', $anchorProjectReload).removeClass('fa-spin');
        }
    );
}
function ValidateSelectProjectToUse(sender, e) {
    e.IsValid = true;

    if (!UsingYSmart()) {
        return;
    }

    var $selectProjectToUse = $('#selectProjectToUse');
    var value = $selectProjectToUse.val();

    if (value === null || value.length === 0) {
        e.IsValid = false;
        return;
    }
}
function UsingYSmart() {
    if (typeof ($checkboxEnableYSmart.checked) !== 'undefined' &&
        $checkboxEnableYSmart.checked &&
        $checkboxEnableYSmart.val() === "true") {
        return true;
    }

    return false;
}
function i18nLoaded() {
    $listboxFtps.multiselect('option', 'noneSelectedText', $.i18n('globals-select'));
    $listboxAlertMessagesDeliveryFailedFromServices.multiselect('option', 'noneSelectedText', $.i18n('reports-chats-all_services'));
    $tabsSystemSettings.show();

    var $cleditors = $('textarea[id$=Template],textarea[id$=MailMaskBody]');
    $cleditors.each(function (index) {
        var $this = $(this);
        if (typeof ($this.cleditor) === 'function') {
            var editors = $this.cleditor();
            if (editors.length > 0) {
                editors[0].refresh();
            }
        }
    });
}

function ConfigureEmailConnection() {
    var url = "EmailConnections.aspx";
    window.open(url);
}

function validateConfigurationParams(sender, e) {
    e.IsValid = true;
    var $sender = $(sender);
    if ($checkboxLDAPUseConfigurationParams.is(':checked')) {
        if ($textboxLDAPConfigurationFirstName.val().length === 0 || $textboxLDAPConfigurationFirstName.val().indexOf(' ') > -1) {
            e.IsValid = false;
            return;
        }

        if ($textboxLDAPConfigurationLastName.val().length === 0 || $textboxLDAPConfigurationLastName.val().indexOf(' ') > -1) {
            e.IsValid = false;
            return;
        }

        if ($textboxLDAPConfigurationUserName.val().length === 0 || $textboxLDAPConfigurationUserName.val().indexOf(' ') > -1) {
            e.IsValid = false;
            return;
        }

        if ($textboxLDAPConfigurationEmail.val().length === 0 || $textboxLDAPConfigurationEmail.val().indexOf(' ') > -1) {
            e.IsValid = false;
            return;
        }

        if ($textboxLDAPConfigurationLDAP.val().length === 0 || $textboxLDAPConfigurationLDAP.val().indexOf(' ') > -1) {
            e.IsValid = false;
            return;
        }
    }
}

function ValidateWebAgentLoginConfiguration(sender, e) {
    e.IsValid = true;
    var sender = $(sender);

    if ($checkboxWebAgentConfigurationAllowUrlLogin.is(':checked')) {
        var regex = /^[A-Za-z0-9]{5,20}$/;
        var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
        var configurationUserNameLoginParameterValue = $textboxWebAgentConfigurationUserNameLoginParameter.val();
        var configurationHashParameter = $textboxWebAgentConfigurationHashParameter.val();
        var configurationKeyToHash = $textboxWebAgentConfigurationKeyToHash.val();
        var configurationLogoutActionValue = parseInt($dropdownlistLogoutAction.val());

        if (!regex.test(configurationUserNameLoginParameterValue)) {
            e.IsValid = false;
            $(sender).text('El nombre del parámetro Username debe contener de 5 a 20 caracteres alfanuméricos y no se admiten espacios en blanco');
            return;
        }

        if ($checkboxWebAgentConfigurationPasswordRequired.is(':checked')) {
            var configurationPasswordParameter = $textboxWebAgentConfigurationPasswordParameter.val();
            var configurationKeyToDecryptPassword = $textboxWebAgentConfigurationKeyToDecryptPassword.val();

            if (configurationPasswordParameter === configurationUserNameLoginParameterValue) {
                e.IsValid = false;
                $(sender).text('El nombre del parámetro Password debe ser distinto al nombre del parámetro Username');
                return;
            }

            if (!regex.test(configurationPasswordParameter)) {
                e.IsValid = false;
                $(sender).text('El nombre del parámetro Password debe contener de 5 a 20 caracteres alfanuméricos');
                return;
            }

            if (!regex.test(configurationKeyToDecryptPassword)) {
                e.IsValid = false;
                $(sender).text('El nombre del parámetro Key para desencriptar el password debe contener de 5 a 20 caracteres alfanuméricos');
                return;
            }
        }

        if (!regex.test(configurationHashParameter)) {
            e.IsValid = false;
            $(sender).text('El nombre del parámetro Hash debe contener de 5 a 20 caracteres alfanuméricos');
            return;
        }

        if (!regex.test(configurationKeyToHash)) {
            e.IsValid = false;
            $(sender).text('El nombre del parámetro Clave para hashear debe contener de 5 a 20 caracteres alfanuméricos');
            return;
        }

        //Configurar mensaje al finalizar la sesión
        if (configurationLogoutActionValue === 1) {
            var configurationLogoutMessage = $textboxWebAgentConfigurationLogoutMessage.val();

            if (configurationLogoutMessage.length === 0 || configurationLogoutMessage.length < 10 || configurationLogoutMessage.length > 80) {
                e.IsValid = false;
                $(sender).text('El mensaje configurado debe contener entre 10 y 80 caracteres');
                return;
            }

        }
        //Configurar url a redireccionar
        else if (configurationLogoutActionValue === 2) {
            var configurationRedirectUrl = $textboxWebAgentConfigurationRedirectUrl.val();

            if (!urlRegex.test(configurationRedirectUrl)) {
                e.IsValid = false;
                $(sender).text('Error en la url ingresada');
                return;
            }
        }
    }
}

function ValidateWebAgentStateManagementConfiguration(sender, e) {
    e.IsValid = true;
    var sender = $(sender);
    var urlRegex = /^(http|https|ftp)\:\/\/[a-z0-9\.-]+\.[a-z]{2,4}/;

    if ($checkboxWebAgentConfigurationAllowChangeState.is(':checked')) {
        if ($checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.is(':checked')) {
            var informativeMessageAfterChangeStateReceived = $textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived.val();
            if (informativeMessageAfterChangeStateReceived.length === 0 || informativeMessageAfterChangeStateReceived.length < 10 || informativeMessageAfterChangeStateReceived.length > 200) {
                e.IsValid = false;
                $(sender).text('El mensaje configurado debe contener entre 10 y 200 caracteres');
                return;
            }
        }

        var changeStateTargetOrigin = $textboxWebAgentConfigurationChangeStateTargetOrigin.val();
        if (!urlRegex.test(changeStateTargetOrigin) && changeStateTargetOrigin !== '*') {
            e.IsValid = false;
            $(sender).text('Target Origin inválido');
            return;
        }
    }

    if ($checkboxWebAgentConfigurationAllowLogoutInvoke.is(':checked')) {
        if ($checkboxWebAgentConfigurationShowMessageAfterLogoutReceived.is(':checked')) {
            var informativeMessageAfterAfterLogoutReceived = $textboxWebAgentConfigurationShowInformativeMessageAfterLogoutReceived.val();
            if (informativeMessageAfterAfterLogoutReceived.length === 0 || informativeMessageAfterAfterLogoutReceived.length < 10 || informativeMessageAfterAfterLogoutReceived.length > 200) {
                e.IsValid = false;
                $(sender).text('El mensaje configurado debe contener entre 10 y 200 caracteres');
                return;
            }
        }

        var logoutInvokeTargetOrigin = $textboxWebAgentConfigurationLogoutInvokeTargetOrigin.val();
        if (!urlRegex.test(logoutInvokeTargetOrigin) && logoutInvokeTargetOrigin !== '*') {
            e.IsValid = false;
            $(sender).text('Target Origin inválido');
            return;
        }
    }

}

function ToggleValidatorsForInputs($inputs, enable) {
    for (var i = 0; i < Page_Validators.length; i++) {
        var validator = Page_Validators[i];
        for (var j = 0; j < $inputs.length; j++) {
            if (validator.controltovalidate == $inputs.get(j).id) {
                if (enable) {
                    validator.enabled = true;
                }
                else {
                    validator.enabled = false;
                    validator.isvalid = true;
                    ValidatorUpdateDisplay(validator);
                }
                break;
            }
        }
    }
}

function MyWebFormOnSubmit() {
    var success = oldWebFormOnSubmit();
    if (!success && typeof (Page_Validators) != 'undefined') {
        for (var i = 0; i < Page_Validators.length; i++) {
            var validator = Page_Validators[i];
            var $validator = $(validator);
            if (!validator.isvalid) {
                var $parent = $validator.parents('.ui-tabs-panel');
                if ($parent.length > 0) {
                    $tabsSystemSettings.tabs('select', $parent.attr('id'));
                }
                ToggleValidator($validator, false);
                break;
            }
            else {
                ToggleValidator($validator, true);
            }
        }
    }

    return success;
}

function ValidateMailAvailableDomains(sender, e) {
    if (!$checkboxAllowForwardAction.is(':checked')) {
        e.IsValid = true;
        return;
    }

    if ($checkboxForwardOutsideDomainAvailable.is(':checked')) {
        e.IsValid = true;
        return;
    }

    var value = $('textarea[id$=textboxAvailableDomains]').val();
    if (value.length == 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
    if (!regex.test(value)) {
        e.IsValid = false;
        return;
    }

    e.IsValid = true;
}

function TestLdapConfig() {
    var $checkboxLdapUseSecureAuthentication = $('input[id$=checkboxLdapUseSecureAuthentication]', $divGlobalConfig);
    var $buttonTestLdapConfig = $('#buttonTestLdapConfig', $divGlobalConfig);

    var server, port, user, password, baseDN, secure;
    server = $textboxLdapServer.val().trim();
    port = $textboxLdapPort.val().trim();
    user = $textboxLdapUser.val().trim();
    if ($checkboxLdapPassword.length == 0 || $checkboxLdapPassword.is(':checked'))
        password = $textboxLdapPassword.val().trim();
    else
        password = null;
    baseDN = $textboxLdapSearchDN.val().trim();
    secure = $checkboxLdapUseSecureAuthentication.is(':checked');

    if (server.length === 0 || port.length === 0 || user.length === 0 || (password !== null && password.length === 0))
        return;

    if (baseDN.length === 0) {
        baseDN = null;
    }

    var dataToSend = JSON.stringify({ server: server, port: port, baseDN: baseDN, user: user, password: password, secure: secure });
    var success = true;

    $.colorbox({
        transition: 'elastic',
        //speed: 200,
        inline: true,
        href: $divLoading,
        width: '400px',
        initialWidth: '400px',
        height: '130px',
        //preloading: true,
        closeButton: false,
        overlayClose: false,
        escKey: false,
        onComplete: function () {
            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/TestLdapConfig",
                data: dataToSend,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    if (data.d.Success) {
                        $divLdapTestFailed.hide();
                        setTimeout(function () {
                            AlertDialog('Test de Configuración', 'Prueba exitosa', '$.colorbox.close()');
                        }, 500);
                    }
                    else {
                        success = false;
                        $divLdapTestFailed.show();
                        $.colorbox.close();

                        if (console)
                            console.error('Ocurrió un error validando los parámetros de LDAP: %o', data.d.Error);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    success = false;
                    $divLdapTestFailed.show();
                    $.colorbox.close();
                }
            });
        }
    });
}

function ValidateMaximumNumberOfTimesMessageCanBeReturned(sender, e) {
    e.IsValid = false;

    if (typeof (workAsGateway) === 'boolean' && workAsGateway) {
        e.IsValid = true;
        return;
    }

    if (!($checkboxAllowAgentsToReturnMessagesToQueue.is(':checked'))) {
        e.IsValid = true;
        return;
    }

    var maximumNumberOfTimesMessageCanBeReturned = $textboxMaximumNumberOfTimesMessageCanBeReturned.val();
    if (Math.floor(maximumNumberOfTimesMessageCanBeReturned) == maximumNumberOfTimesMessageCanBeReturned && $.isNumeric(maximumNumberOfTimesMessageCanBeReturned)) {
        minutesNotAssignToPreviousAgent = parseInt(maximumNumberOfTimesMessageCanBeReturned, 10);
        if (minutesNotAssignToPreviousAgent >= 1 && maximumNumberOfTimesMessageCanBeReturned <= 100) {
            e.IsValid = true;
            return;
        }
    }
}

function ValidateAttachmentsRoute(sender, e) {
    var value = $textboxAttachmentsRoute.val();

    if (value.length == 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^[a-zA-Z]:\\(?:[^\\/:*?<>|\r\n]+\\)*[^\\/:*?<>|\r\n]*$/;
    if (!value.match(regex)) {
        e.IsValid = false;
        return;
    }

    var dataToSend = JSON.stringify({ path: value });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsPathForAttachValid",
        data: dataToSend,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateSearchFtp(sender, e) {
    e.IsValid = true;

    if ($checkboxEnableFtpDailyReport.is(':checked') && $listboxFtps.val() === null) {
        e.IsValid = false;
    }
}

function ValidateMailMaskBody(sender, e) {
    e.IsValid = true;

    if (!$checkboxAllowForwardAction.is(':checked')) {
        return;
    }

    if ($textboxMailMaskBody.val().length == 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateSurvey(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableSurveys.is(':checked'))
        return;

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsSurveysValid",
        data: null,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateLdapServer(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    var value = $textboxLdapServer.val().trim();

    if (value.length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$)|(^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$)/;
    if (!regex.test(value)) {
        e.IsValid = false;
        return;
    }
}

function ValidateLdapPort(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    var value = $textboxLdapPort.val().trim();

    if (value.length === 0 || !$.isNumeric(value)) {
        e.IsValid = false;
        return;
    }

    var regex = /^\d{1,5}$/;
    if (!regex.test(value)) {
        e.IsValid = false;
        return;
    }
}

function ValidateLdapSearchDN(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    var value = $textboxLdapSearchDN.val().trim();

    if (value.length === 0) {
        //e.IsValid = false;
        return;
    }
}

function ValidateLdapUser(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    var value = $textboxLdapUser.val().trim();

    if (value.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateLdapPassword(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    if ($checkboxLdapPassword.length > 0 && !$checkboxLdapPassword.is(':checked'))
        return;

    var value = $textboxLdapPassword.val().trim();

    if (value.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateLdapUserSearchFilter(sender, e) {
    e.IsValid = true;

    if (!$checkboxLdapUseLdap.is(':checked'))
        return;

    var value = $textboxLdapUserSearchFilter.val().trim();

    if (value.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateCognitiveServicesToken(sender, e) {
    if (!$checkboxUseCognitiveServices.is(':checked')) {
        e.IsValid = true;
        return;
    }

    var token = $textboxCognitiveServicesToken.val();
    var tokenSecret = $textboxCognitiveServicesTokenSecret.val();

    if (token.trim().length === 0 || tokenSecret.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsCognitiveServicesTokenValid",
        data: JSON.stringify({ token: token, tokenSecret: tokenSecret }),
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        beforeSend: function () {
            $.colorbox({
                transition: 'none',
                speed: 0,
                inline: true,
                href: $divLoading,
                width: '400px',
                initialWidth: '400px',
                height: '130px',
                preloading: false,
                closeButton: false,
                overlayClose: false,
                escKey: false,
                open: true,
                onComplete: function () {
                    $.colorbox.resize();
                }
            });
        },
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
                console.log('Values: %s', data.d.Info);
            }
            else {
                e.IsValid = false;
            }

            $.colorbox.close();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
            $.colorbox.close();
        }
    });
}

function ValidateDailyReportsSettings(sender, e) {
    e.IsValid = true;

    if (!$checkboxGenerateDailyReport.is(':checked'))
        return;

    var $textboxDailyReportsToMantain = $('#textboxDailyReportsToMantain');
    if ($textboxDailyReportsToMantain.length > 0) {
        var dailyReportsToMantain = parseInt($textboxDailyReportsToMantain.val(), 10);
        if (isNaN(dailyReportsToMantain) || dailyReportsToMantain < 1) {
            e.IsValid = false;
            return;
        }

        if (inTheCloud && loggedUserID === 1) {
            if (dailyReportsToMantain > 100) {
                e.IsValid = false;
                return;
            }
        }
        else {
            if (dailyReportsToMantain > 31) {
                e.IsValid = false;
                return;
            }
        }
    }

    var $checkboxlistDailyReportsToGenerate = $('table[id$=_checkboxlistDailyReportsToGenerate]');
    var $checkboxes = $('input[type=checkbox]', $checkboxlistDailyReportsToGenerate);

    var atLeastOneCheckboxSelected = false;
    $checkboxes.each(function (index) {
        if (this.checked) {
            atLeastOneCheckboxSelected = true;
            return;
        }
    });

    if (!atLeastOneCheckboxSelected) {
        e.IsValid = false;
        return;
    }

    var $textboxDailyReportsEmails = $('#textboxDailyReportsEmails');
    var emails = $textboxDailyReportsEmails.val();
    var regex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
    if (emails.length == 0 || !regex.test(emails)) {
        e.IsValid = false;
        return;
    }

    var $textboxDailyReportsEmailTemplate = $('#textboxDailyReportsEmailTemplate');
    var template = $textboxDailyReportsEmailTemplate.val();
    if (template.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateAlertMessagesDeliveryFailedViaMail(sender, e) {
    e.IsValid = true;
    var values = $listboxAlertMessagesDeliveryFailedVia.val();
    if (values === null || values.length === 0) {
        return;
    }

    var value;
    value = $textboxAlertMessagesDeliveryFailedViaMailEmailSubject.val().trim();
    if (value.length === 0) {
        e.IsValid = false;
        return;
    }

    value = $textboxAlertMessagesDeliveryFailedViaMailEmailTemplate.val().trim();
    if (value.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateAnnoyingUser(sender, e) {
    e.IsValid = true;
    if (!$checkboxUseAnnoyingUser.is(':checked'))
        return;

    var maxMessages = $textboxMaxMessagesAnnoyingUser.val();
    if (!$.isNumeric(maxMessages)) {
        e.IsValid = false;
        return;
    }

    maxMessages = parseInt(maxMessages, 10);
    if (isNaN(maxMessages) || maxMessages < 20 || maxMessages > 500) {
        e.IsValid = false;
        return;
    }

    var value;
    value = $textboxAnnoyingUserEmailSubject.val().trim();
    if (value.length === 0) {
        e.IsValid = false;
        return;
    }

    value = $textboxAnnoyingUserEmails.val().trim();
    regex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
    if (value.length === 0 || !regex.test(value)) {
        e.IsValid = false;
        return;
    }

    value = $textboxAnnoyingUserEmailTemplate.val().trim();
    if (value.length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateBusinessDataRegex(sender, e) {
    e.IsValid = true;

    var value = $textboxBusinessDataRegex.val();
    if (value == null || value.length === 0) {
        e.IsValid = false;
        return;
    }

    try {
        var regex = new RegExp(value);
    }
    catch (error) {
        e.IsValid = false;
        return;
    }
}

function ValidateAgentRegex(sender, e) {
    e.IsValid = true;

    var value = $textboxAgentRegex.val();
    if (value == null || value.length === 0) {
        e.IsValid = false;
        return;
    }

    try {
        var regex = new RegExp(value);
    }
    catch (error) {
        e.IsValid = false;
        return;
    }
}

function ValidateUserRegex(sender, e) {
    e.IsValid = true;

    var value = $textboxUserRegex.val();
    if (value == null || value.length === 0) {
        e.IsValid = false;
        return;
    }

    try {
        var regex = new RegExp(value);
    }
    catch (error) {
        e.IsValid = false;
        return;
    }
}

function ValidateAutoReplyMessage(sender, e) {
    e.IsValid = true;

    var value = $textboxAutoReplyInCloseCaseText.val();
    if ($checkboxReplyInCloseCase.is(':checked')) {
        if (value == null || value.length === 0 || value == 'undefined') {
            e.IsValid = false;
            return;
        }

        if (value.length >= 1000) {
            e.IsValid = false;
            return;
        }
    }
}

function ValidateWebAgentURL(sender, e) {
    e.IsValid = true;

    if (typeof (workAsGateway) === 'boolean' && workAsGateway) {
        return;
    }

    var $hiddenWebAgentVersion = $('#hiddenWebAgentVersion');
    var url = $textboxWebAgentURL.val();

    if (url.length === 0) {
        return;
    }

    var dataToSend = JSON.stringify({ url: url });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsWebAgentURLValid",
        data: dataToSend,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
                if (e.IsValid && typeof (data.d.Version) !== 'undefined') {
                    $hiddenWebAgentVersion.val(data.d.Version);
                }
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateLastQueueByTime(sender, e) {
    e.IsValid = true;
    
    $textboxLastQueueByTime = $('input[id$=textboxLastQueueByTime]');
    $checkboxCheckLastQueueByTime = $('input[type=checkbox][id$=checkboxCheckLastQueueByTime]');
    var $validateLastQueueByTimeID = $('#ValidateLastQueueByTimeID');
    var value = $textboxLastQueueByTime.val();
    if ($checkboxCheckLastQueueByTime.is(':checked')) {
        if (value == null || value.length === 0 || value == 'undefined') {
            e.IsValid = false;
            return;
        }
        if (value.length >= 1000) {
            e.IsValid = false;
            return;
        }
        if (value > 600) {
            if ($(sender).hasClass('validationerror'))
                $(sender).text($.i18n("configuration-systemsettings-over_limit_number"));
            e.IsValid = false;
            return;
        }
        if (value < 60) {
            if ($(sender).hasClass('validationerror'))
                $(sender).text($.i18n("configuration-systemsettings-under_limit_number"));
            e.IsValid = false;
            return;
        }
    }
}

function AddExtendedProfileField(field) {
    var $tbody = $('> tbody', $tableExtendedProfileFields);
    var $lastTr = $('tr:last-child', $tbody);
    var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
    var $newTr = $('<tr></tr>');
    $newTr.addClass(newTrClass);
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputName = $('<input type="text" rel="name" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputName);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $td = $('<td></td>');
    $newTr.append($td);

    var $selectDataType = $('<select rel="datatype"><option value="string">Texto</option><option value="number">Numérico</option><option value="float">Decimal</option><option value="date">Fecha</option><option value="boolean">Si/No</option><option value="dropdown">Lista</option></select>');
    $td.append($selectDataType);

    $td = $('<td></td>');
    $newTr.append($td);

    $inputGroup = $('<div rel="required" class="advanced-input-group-checkbox"><label><input type="checkbox" class="noswitch" rel="required" /> Requerido </label></div>');
    var $inputRequired = $('input', $inputGroup);
    $td.append($inputGroup);

    var $divString = $('<div rel="string" />');
    $td.append($divString);

    var $inputGroup = $('<div class="advanced-input-group"><span>Expresión regular para validar:</span></div>');
    var $inputStringTextRegex = $('<input type="text" rel="stringregex" class="inputtext advanced-input-control" placeholder="Expresión regular" />');
    $inputStringTextRegex.popover({
        html: true,
        trigger: 'focus',
        title: 'Aclaración',
        placement: 'left',
        template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
        content: 'La expresión regular debería validar que no se ingresen espacios, comas o caracteres especiales y una longitud máxima'
    });
    $inputGroup.append($inputStringTextRegex)
    $divString.append($inputGroup);

    var $divNumber = $('<div rel="number" />');
    $td.append($divNumber);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor mínimo:</span></div>');
    var $inputNumberMinimumValue = $('<input type="text" rel="numberminimum" class="inputtext advanced-input-control" placeholder="Valor mínimo" />');
    $inputGroup.append($inputNumberMinimumValue);
    $divNumber.append($inputGroup);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor máximo:</span></div>');
    var $inputNumberMaximumValue = $('<input type="text" rel="numbermaximum" class="inputtext advanced-input-control" placeholder="Valor máximo" />');
    $inputGroup.append($inputNumberMaximumValue);
    $divNumber.append($inputGroup);

    var $divDropdown = $('<div rel="dropdown" />');
    $td.append($divDropdown);

    $inputGroup = $('<div class="advanced-input-group"><span>Ítems de la lista:</span></div>');
    var $tableItems = $('<table rel="dropdownitems" class="advanced-input-table"><thead><tr><th style="width: 20px"></th><th style="width: 100px">Valor</th><th style="max-width: 200px">Texto</th></tr></thead><tbody /><tfoot><tr><td style="text-align: center"><a rel="dropdownitems-add"><span class="fa fa-lg fa-plus-square"></span></a></td><td colspan="3"></td></tr></tfoot></table>');
    $inputGroup.append($tableItems);
    $divDropdown.append($inputGroup);

    var $anchorDropdownItemsAdd = $('a[rel=dropdownitems-add]', $tableItems);
    $anchorDropdownItemsAdd.tooltip({ title: 'Agregar nuevo ítem', html: true });
    $anchorDropdownItemsAdd.click(AddExtendedProfileFieldDropdownItem);

    $selectDataType.change(function () {
        var $this = $(this);
        var $tr = $this.parent().parent();
        var val = $this.val();
        var $divString = $('div[rel=string]', $tr);
        var $divDropdown = $('div[rel=dropdown]', $tr);
        var $divNumber = $('div[rel=number]', $tr);
        var $divRequired = $('div[rel=required]', $tr);

        $divString.toggle(val === 'string');
        $divDropdown.toggle(val === 'dropdown');
        $divNumber.toggle(val === 'number' || val === 'float' || val === 'date');
        $divRequired.toggle(val !== 'dropdown' && val !== 'boolean');

        $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
        $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
        if (val === 'number' || val === 'float') {
            $inputNumberMinimumValue.popover('destroy');
            $inputNumberMaximumValue.popover('destroy');
            $inputNumberMinimumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor mínimo'
            });
            $inputNumberMaximumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor máximo'
            });
            $inputNumberMinimumValue.removeAttr('placeholder');
            $inputNumberMaximumValue.removeAttr('placeholder');
        }
        else if (val === 'date') {
            $inputNumberMinimumValue.popover('destroy');
            $inputNumberMaximumValue.popover('destroy');
            $inputNumberMinimumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor mínimo. Puede utilizar @@HOY@@ y se tomará como valor el día actual'
            });
            $inputNumberMaximumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor máximo. Puede utilizar @@HOY@@ y se tomará como valor el día actual'
            });

            var localeData = moment.localeData();
            var longDateFormat = localeData.longDateFormat('L');
            $inputNumberMinimumValue.attr('placeholder', longDateFormat);
            $inputNumberMaximumValue.attr('placeholder', longDateFormat);
        }
    }).trigger('change');

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();

        var $trs = $('tr', $tbody);
        for (var i = 0; i < $trs.length; i++) {
            var $tr = $($trs.get(i));
            $tr.removeClass('normal alternate');
            $tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
        }
    });

    if (typeof (field) !== 'undefined' && field !== null) {
        $inputName.val(field.name);
        $inputDescription.val(field.description);
        $inputRequired.prop('checked', field.required);
        $selectDataType.val(field.dataType);
        $selectDataType.trigger('change');

        switch (field.dataType) {
            case 'number':
                if (typeof (field.number) !== 'undefined' && field.number !== null) {
                    $inputNumberMaximumValue.val(field.number.max);
                    $inputNumberMinimumValue.val(field.number.min);
                }
                break;
            case 'float':
                if (typeof (field.float) !== 'undefined' && field.float !== null) {
                    $inputNumberMaximumValue.val(field.float.max);
                    $inputNumberMinimumValue.val(field.float.min);
                } break;
            case 'date':
                if (typeof (field.date) !== 'undefined' && field.date !== null) {
                    if (field.date.max === '@@HOY@@') {
                        $inputNumberMaximumValue.val(field.date.max);
                    }
                    else {
                        var maxMoment = moment(field.date.max, moment.HTML5_FMT.DATE);
                        if (!maxMoment.isValid()) {
                            maxMoment = moment(field.date.max, 'L');
                        }
                        $inputNumberMaximumValue.val(maxMoment.format('L'));
                    }

                    if (field.date.min === '@@HOY@@') {
                        $inputNumberMinimumValue.val(field.date.min);
                    }
                    else {
                        var minMoment = moment(field.date.min, moment.HTML5_FMT.DATE);
                        if (!minMoment.isValid()) {
                            minMoment = moment(field.date.min, 'L');
                        }
                        $inputNumberMinimumValue.val(minMoment.format('L'));
                    }
                } break;
            case 'string':
                if (typeof (field.string) !== 'undefined' && field.string !== null) {
                    $inputStringTextRegex.val(field.string.regex);
                } break;
            case 'dropdown':
                if (typeof (field.dropdown) !== 'undefined' && field.dropdown !== null) {
                    for (var i = 0; i < field.dropdown.items.length; i++) {
                        AddExtendedProfileFieldDropdownItem(null, $tableItems, field.dropdown.items[i]);
                    }
                } break;
            default:
                break;
        }
    }

    return $newTr;
}

function AddExtendedProfileFieldDropdownItem(event, $table, item) {
    if (event !== null) {
        var $this = $(this);
        $table = $this.parent().parent().parent().parent();
    }

    var $tbody = $('tbody', $table);
    var $newTr = $('<tr></tr>');
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputValue = $('<input type="text" rel="dropdown-item-value" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputValue);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="dropdown-item-description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();
    });

    $inputDescription.keydown(function (e) {
        if (e.key === 'Tab' && !e.shiftKey) {
            var $tr = $(this).parent().parent();
            var $table = $tr.parent().parent();
            var $inputValue = $('input[rel=dropdown-item-value]', $tr);
            var $inputDescription = $('input[rel=dropdown-item-description]', $tr);
            var value = $inputValue.val();
            var description = $inputDescription.val();

            if (value.trim().length > 0 && description.trim().length > 0) {
                if ($tr.closest("tr").is(":last-child")) {
                    var $addedTr = AddExtendedProfileFieldDropdownItem(null, $table, null);
                    $addedTr.prop('autoadded', true);
                    var $firstInput = $('input[rel=dropdown-item-value]', $addedTr).focus();
                    e.preventDefault();
                }
            }
            else if (value.length === 0 && description.length === 0 && $tr.prop('autoadded') === true) {
                $tr.remove();
            }
        }
    });

    if (typeof (item) !== 'undefined' && item !== null && typeof (item.value) !== 'undefined') {
        $inputValue.val(item.value);
        $inputDescription.val(item.description);
    }

    return $newTr;
}

function ValidateExtendedProfileFields(sender, e) {
    var $sender = $(sender);
    var $tbody = $('> tbody', $tableExtendedProfileFields);
    var $trs = $('> tr', $tbody);

    if ($trs.length === 0) {
        $hiddenExtendedProfileFields.val('');
        e.IsValid = true;
        return;
    }

    var fields = [];

    for (var i = 0; i < $trs.length; i++) {
        var $tr = $($trs.get(i));

        var field = {
            name: null,
            description: null,
            dataType: null,
            required: false,
            string: null,
            number: null,
            float: null,
            date: null,
            dropdown: null
        };

        var $inputName = $('input[rel=name]', $tr);
        var name = $inputName.val();
        var $inputDescription = $('input[rel=description]', $tr);
        var description = $inputDescription.val().trim();
        var $selectDataType = $('select[rel=datatype]', $tr);
        var dataType = $selectDataType.val();
        var $inputRequired = $('input[rel=required]', $tr);

        var regexName = /^[A-Za-z0-9_-]{1,30}$/;
        if (!regexName.test(name)) {
            $sender.text('El nombre del campo ' + (i + 1) + ' debe contener como máximo 20 caracteres, sin incluir espacios ni caracteres especiales');
            e.IsValid = false;
            return;
        }

        field.name = name;
        field.required = $inputRequired.prop('checked');

        if (description.length === 0) {
            $sender.text('La descripción del campo ' + name + ' es inválido');
            e.IsValid = false;
            return;
        }

        field.description = description;
        field.dataType = dataType;

        switch (dataType) {
            case 'dropdown':
                {
                    field.dropdown = {};

                    var $inputDropDownDependsOn = $('input[rel=dropdowndependson]', $tr);
                    if ($inputDropDownDependsOn.length > 0) {
                        var dependsOn = $inputDropDownDependsOn.val();

                        if (dependsOn.length > 0) {
                            var isTheFirstDropdownField = true;
                            var foundTheOtherList = false;
                            for (var j = 0; j < fields.length; j++) {
                                if (fields[j].dataType === 'dropdown') {
                                    isTheFirstDropdownField = false;

                                    if (fields[j].name === dependsOn)
                                        foundTheOtherList = true;
                                }
                            }

                            if (isTheFirstDropdownField) {
                                $sender.text('El campo ' + name + ' de tipo Lista no puede depender de otra lista dado que no existe otro campo Lista anterior a este');
                                e.IsValid = false;
                                return;
                            }

                            if (!foundTheOtherList) {
                                $sender.text('El campo ' + name + ' de tipo Lista hace referencia a un campo de tipo Lista que no existe');
                                e.IsValid = false;
                                return;
                            }

                            field.dropdown.dependsOn = dependsOn;
                        }
                        else {
                            field.dropdown.dependsOn = null;
                        }
                    }
                    else {
                        field.dropdown.dependsOn = null;
                    }

                    field.dropdown.items = [];
                    var $tableItems = $('table[rel=dropdownitems]', $tr);
                    var $tbodyTableItems = $('tbody', $tableItems);
                    var $trsTableItems = $('tr', $tbodyTableItems);

                    for (var j = 0; j < $trsTableItems.length; j++) {
                        var $trTableItem = $($trsTableItems.get(j));

                        var $inputTableItemValue = $('input[rel=dropdown-item-value]', $trTableItem);
                        var $inputTableItemDescription = $('input[rel=dropdown-item-description]', $trTableItem);
                        var $inputTableItemDependsOn = $('input[rel=dropdown-item-dependson]', $trTableItem);

                        var itemValue = $inputTableItemValue.val();
                        var itemDescription = $inputTableItemDescription.val();
                        var itemDependsOn = $inputTableItemDependsOn.val();

                        if (itemValue.length === 0) {
                            $sender.text('El ítem ' + (j + 1) + ' de la lista del campo ' + name + ' no tiene definido el valor');
                            e.IsValid = false;
                            return;
                        }

                        if (itemDescription.length === 0) {
                            $sender.text('El ítem ' + itemValue + ' de la lista del campo ' + name + ' no tiene definida la descripción');
                            e.IsValid = false;
                            return;
                        }

                        if (field.dropdown.dependsOn === null)
                            itemDependsOn = null;

                        var item = {
                            value: itemValue,
                            description: itemDescription,
                            dependsOn: itemDependsOn
                        };

                        field.dropdown.items.push(item);
                    }
                }
                break;
            case 'number':
                {
                    field.number = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    if (min === null || min.length === 0) {
                        min = null;
                    }
                    else {
                        if (!$.isNumeric(min)) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        min = parseInt(min);
                        if (min < 0) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }
                    }

                    if (max === null || max.length === 0) {
                        max = null;
                    }
                    else {
                        if (!$.isNumeric(max)) {
                            $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        max = parseInt(max);
                        if (min != null && max < min) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                            e.IsValid = false;
                            return;
                        }
                    }

                    field.number.max = max;
                    field.number.min = min;
                }
                break;
            case 'float':
                {
                    field.float = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    if (min === null || min.length === 0) {
                        min = null;
                    }
                    else {
                        if (!$.isNumeric(min)) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        min = parseFloat(min);
                        if (min < 0) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }
                    }

                    if (max === null || max.length === 0) {
                        max = null;
                    }
                    else {
                        if (!$.isNumeric(max)) {
                            $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        max = parseFloat(max);
                        if (min != null && max < min) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                            e.IsValid = false;
                            return;
                        }
                    }

                    field.float.max = max;
                    field.float.min = min;
                }
                break;
            case 'date':
                {
                    field.date = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    var minMoment = null;
                    if (min === '@@HOY@@') {
                        minMoment = moment();
                    }
                    else if (min.length > 0) {
                        minMoment = moment(min, 'L');
                    }

                    if (minMoment !== null && !minMoment.isValid()) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    var maxMoment = null;
                    if (max === '@@HOY@@') {
                        maxMoment = moment();
                    }
                    else if (max.length > 0) {
                        maxMoment = moment(max, 'L');
                    }

                    if (maxMoment !== null && !maxMoment.isValid()) {
                        $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    if (minMoment !== null && maxMoment !== null && minMoment >= maxMoment) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                        e.IsValid = false;
                        return;
                    }

                    if (maxMoment !== null && max !== '@@HOY@@') {
                        max = maxMoment.format(moment.HTML5_FMT.DATE);
                    }

                    if (minMoment !== null && min !== '@@HOY@@') {
                        min = minMoment.format(moment.HTML5_FMT.DATE);
                    }

                    field.date.max = max;
                    field.date.min = min;
                }
                break;
            case 'string':
                {
                    field.string = {};
                    var $inputStringRegex = $('input[rel=stringregex]', $tr);
                    var value = $inputStringRegex.val();

                    if (value == null || value.length === 0) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    try {
                        var regex = new RegExp(value);
                    }
                    catch (error) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    field.string.regex = value;
                }
                break;
            default:
                break;
        }

        fields.push(field);
    }

    $hiddenExtendedProfileFields.val(JSON.stringify(fields));
    e.IsValid = true;
}

function AddExtendedCaseField(field) {
    var $tbody = $('> tbody', $tableExtendedCaseFields);
    var $lastTr = $('tr:last-child', $tbody);
    var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
    var $newTr = $('<tr></tr>');
    $newTr.addClass(newTrClass);
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputName = $('<input type="text" rel="name" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputName);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $td = $('<td></td>');
    $newTr.append($td);

    var $selectDataType = $('<select rel="datatype"><option value="string">Texto</option><option value="number">Numérico</option><option value="float">Decimal</option><option value="date">Fecha</option><option value="boolean">Si/No</option><option value="dropdown">Lista</option></select>');
    $td.append($selectDataType);

    $td = $('<td></td>');
    $newTr.append($td);

    $inputGroup = $('<div rel="required" class="advanced-input-group-checkbox"><label><input type="checkbox" class="noswitch" rel="required" /> Requerido </label></div>');
    var $inputRequired = $('input', $inputGroup);
    $td.append($inputGroup);

    var $divString = $('<div rel="string" />');
    $td.append($divString);

    var $inputGroup = $('<div class="advanced-input-group"><span>Expresión regular para validar:</span></div>');
    var $inputStringTextRegex = $('<input type="text" rel="stringregex" class="inputtext advanced-input-control" placeholder="Expresión regular" />');
    $inputStringTextRegex.popover({
        html: true,
        trigger: 'focus',
        title: 'Aclaración',
        placement: 'left',
        template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
        content: 'La expresión regular debería validar que no se ingresen espacios, comas o caracteres especiales y una longitud máxima'
    });
    $inputGroup.append($inputStringTextRegex)
    $divString.append($inputGroup);

    var $divNumber = $('<div rel="number" />');
    $td.append($divNumber);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor mínimo:</span></div>');
    var $inputNumberMinimumValue = $('<input type="text" rel="numberminimum" class="inputtext advanced-input-control" placeholder="Valor mínimo" />');
    $inputGroup.append($inputNumberMinimumValue);
    $divNumber.append($inputGroup);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor máximo:</span></div>');
    var $inputNumberMaximumValue = $('<input type="text" rel="numbermaximum" class="inputtext advanced-input-control" placeholder="Valor máximo" />');
    $inputGroup.append($inputNumberMaximumValue);
    $divNumber.append($inputGroup);

    var $divDropdown = $('<div rel="dropdown" />');
    $td.append($divDropdown);

    $inputGroup = $('<div class="advanced-input-group"><span>Ítems de la lista:</span></div>');
    var $tableItems = $('<table rel="dropdownitems" class="advanced-input-table"><thead><tr><th style="width: 20px"></th><th style="width: 100px">Valor</th><th style="max-width: 200px">Texto</th></tr></thead><tbody /><tfoot><tr><td style="text-align: center"><a rel="dropdownitems-add"><span class="fa fa-lg fa-plus-square"></span></a></td><td colspan="3"></td></tr></tfoot></table>');
    $inputGroup.append($tableItems);
    $divDropdown.append($inputGroup);

    var $anchorDropdownItemsAdd = $('a[rel=dropdownitems-add]', $tableItems);
    $anchorDropdownItemsAdd.tooltip({ title: 'Agregar nuevo ítem', html: true });
    $anchorDropdownItemsAdd.click(AddExtendedCaseFieldDropdownItem);

    $selectDataType.change(function () {
        var $this = $(this);
        var $tr = $this.parent().parent();
        var val = $this.val();
        var $divString = $('div[rel=string]', $tr);
        var $divDropdown = $('div[rel=dropdown]', $tr);
        var $divNumber = $('div[rel=number]', $tr);
        var $divRequired = $('div[rel=required]', $tr);

        $divString.toggle(val === 'string');
        $divDropdown.toggle(val === 'dropdown');
        $divNumber.toggle(val === 'number' || val === 'float' || val === 'date');
        $divRequired.toggle(val !== 'dropdown' && val !== 'boolean');

        $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
        $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
        if (val === 'number' || val === 'float') {
            $inputNumberMinimumValue.popover('destroy');
            $inputNumberMaximumValue.popover('destroy');
            $inputNumberMinimumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor mínimo'
            });
            $inputNumberMaximumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor máximo'
            });
        }
        else if (val === 'date') {
            $inputNumberMinimumValue.popover('destroy');
            $inputNumberMaximumValue.popover('destroy');
            $inputNumberMinimumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor mínimo. Puede utilizar @@HOY@@ y se tomará como valor el día actual'
            });
            $inputNumberMaximumValue.popover({
                html: true,
                trigger: 'focus',
                title: 'Aclaración',
                placement: 'left',
                template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
                content: 'Puede dejar el valor en blanco y no se validará un valor máximo. Puede utilizar @@HOY@@ y se tomará como valor el día actual'
            });
        }
    }).trigger('change');

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();

        var $trs = $('tr', $tbody);
        for (var i = 0; i < $trs.length; i++) {
            var $tr = $($trs.get(i));
            $tr.removeClass('normal alternate');
            $tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
        }
    });

    if (typeof (field) !== 'undefined' && field !== null) {
        $inputName.val(field.name);
        $inputDescription.val(field.description);
        $inputRequired.prop('checked', field.required);
        $selectDataType.val(field.dataType);
        $selectDataType.trigger('change');

        switch (field.dataType) {
            case 'number':
                if (typeof (field.number) !== 'undefined' && field.number !== null) {
                    $inputNumberMaximumValue.val(field.number.max);
                    $inputNumberMinimumValue.val(field.number.min);
                }
                break;
            case 'float':
                if (typeof (field.float) !== 'undefined' && field.float !== null) {
                    $inputNumberMaximumValue.val(field.float.max);
                    $inputNumberMinimumValue.val(field.float.min);
                } break;
            case 'date':
                if (typeof (field.date) !== 'undefined' && field.date !== null) {
                    $inputNumberMaximumValue.val(field.date.max);
                    $inputNumberMinimumValue.val(field.date.min);
                } break;
            case 'string':
                if (typeof (field.string) !== 'undefined' && field.string !== null) {
                    $inputStringTextRegex.val(field.string.regex);
                } break;
            case 'dropdown':
                if (typeof (field.dropdown) !== 'undefined' && field.dropdown !== null) {
                    for (var i = 0; i < field.dropdown.items.length; i++) {
                        AddExtendedCaseFieldDropdownItem(null, $tableItems, field.dropdown.items[i]);
                    }
                } break;
            default:
                break;
        }
    }

    return $newTr;
}

function AddExtendedCaseFieldDropdownItem(event, $table, item) {
    if (event !== null) {
        var $this = $(this);
        $table = $this.parent().parent().parent().parent();
    }

    var $tbody = $('tbody', $table);
    var $newTr = $('<tr></tr>');
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputValue = $('<input type="text" rel="dropdown-item-value" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputValue);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="dropdown-item-description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();
    });

    $inputDescription.keydown(function (e) {
        if (e.key === 'Tab' && !e.shiftKey) {
            var $tr = $(this).parent().parent();
            var $table = $tr.parent().parent();
            var $inputValue = $('input[rel=dropdown-item-value]', $tr);
            var $inputDescription = $('input[rel=dropdown-item-description]', $tr);
            var value = $inputValue.val();
            var description = $inputDescription.val();

            if (value.trim().length > 0 && description.trim().length > 0) {
                if ($tr.closest("tr").is(":last-child")) {
                    var $addedTr = AddExtendedCaseFieldDropdownItem(null, $table, null);
                    $addedTr.prop('autoadded', true);
                    var $firstInput = $('input[rel=dropdown-item-value]', $addedTr).focus();
                    e.preventDefault();
                }
            }
            else if (value.length === 0 && description.length === 0 && $tr.prop('autoadded') === true) {
                $tr.remove();
            }
        }
    });

    if (typeof (item) !== 'undefined' && item !== null && typeof (item.value) !== 'undefined') {
        $inputValue.val(item.value);
        $inputDescription.val(item.description);
    }

    return $newTr;
}

function ValidateExtendedCaseFields(sender, e) {
    var $sender = $(sender);
    var $tbody = $('> tbody', $tableExtendedCaseFields);
    var $trs = $('> tr', $tbody);

    if ($trs.length === 0) {
        $hiddenExtendedCaseFields.val('');
        e.IsValid = true;
        return;
    }

    var fields = [];

    for (var i = 0; i < $trs.length; i++) {
        var $tr = $($trs.get(i));

        var field = {
            name: null,
            description: null,
            dataType: null,
            required: false,
            string: null,
            number: null,
            float: null,
            date: null,
            dropdown: null
        };

        var $inputName = $('input[rel=name]', $tr);
        var name = $inputName.val();
        var $inputDescription = $('input[rel=description]', $tr);
        var description = $inputDescription.val().trim();
        var $selectDataType = $('select[rel=datatype]', $tr);
        var dataType = $selectDataType.val();
        var $inputRequired = $('input[rel=required]', $tr);

        var regexName = /^[A-Za-z0-9_-]{1,20}$/;
        if (!regexName.test(name)) {
            $sender.text('El nombre del campo ' + (i + 1) + ' debe contener como máximo 20 caracteres, sin incluir espacios ni caracteres especiales');
            e.IsValid = false;
            return;
        }

        field.name = name;
        field.required = $inputRequired.prop('checked');

        if (description.length === 0) {
            $sender.text('La descripción del campo ' + name + ' es inválido');
            e.IsValid = false;
            return;
        }

        field.description = description;
        field.dataType = dataType;

        switch (dataType) {
            case 'dropdown':
                {
                    field.dropdown = {};

                    var $inputDropDownDependsOn = $('input[rel=dropdowndependson]', $tr);
                    if ($inputDropDownDependsOn.length > 0) {
                        var dependsOn = $inputDropDownDependsOn.val();

                        if (dependsOn.length > 0) {
                            var isTheFirstDropdownField = true;
                            var foundTheOtherList = false;
                            for (var j = 0; j < fields.length; j++) {
                                if (fields[j].dataType === 'dropdown') {
                                    isTheFirstDropdownField = false;

                                    if (fields[j].name === dependsOn)
                                        foundTheOtherList = true;
                                }
                            }

                            if (isTheFirstDropdownField) {
                                $sender.text('El campo ' + name + ' de tipo Lista no puede depender de otra lista dado que no existe otro campo Lista anterior a este');
                                e.IsValid = false;
                                return;
                            }

                            if (!foundTheOtherList) {
                                $sender.text('El campo ' + name + ' de tipo Lista hace referencia a un campo de tipo Lista que no existe');
                                e.IsValid = false;
                                return;
                            }

                            field.dropdown.dependsOn = dependsOn;
                        }
                        else {
                            field.dropdown.dependsOn = null;
                        }
                    }
                    else {
                        field.dropdown.dependsOn = null;
                    }

                    field.dropdown.items = [];
                    var $tableItems = $('table[rel=dropdownitems]', $tr);
                    var $tbodyTableItems = $('tbody', $tableItems);
                    var $trsTableItems = $('tr', $tbodyTableItems);

                    for (var j = 0; j < $trsTableItems.length; j++) {
                        var $trTableItem = $($trsTableItems.get(j));

                        var $inputTableItemValue = $('input[rel=dropdown-item-value]', $trTableItem);
                        var $inputTableItemDescription = $('input[rel=dropdown-item-description]', $trTableItem);
                        var $inputTableItemDependsOn = $('input[rel=dropdown-item-dependson]', $trTableItem);

                        var itemValue = $inputTableItemValue.val();
                        var itemDescription = $inputTableItemDescription.val();
                        var itemDependsOn = $inputTableItemDependsOn.val();

                        if (itemValue.length === 0) {
                            $sender.text('El ítem ' + (j + 1) + ' de la lista del campo ' + name + ' no tiene definido el valor');
                            e.IsValid = false;
                            return;
                        }

                        if (itemDescription.length === 0) {
                            $sender.text('El ítem ' + itemValue + ' de la lista del campo ' + name + ' no tiene definida la descripción');
                            e.IsValid = false;
                            return;
                        }

                        if (field.dropdown.dependsOn === null)
                            itemDependsOn = null;

                        var item = {
                            value: itemValue,
                            description: itemDescription,
                            dependsOn: itemDependsOn
                        };

                        field.dropdown.items.push(item);
                    }
                }
                break;
            case 'number':
                {
                    field.number = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    if (min === null || min.length === 0) {
                        min = null;
                    }
                    else {
                        if (!$.isNumeric(min)) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        min = parseInt(min);
                        if (min < 0) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }
                    }

                    if (max === null || max.length === 0) {
                        max = null;
                    }
                    else {
                        if (!$.isNumeric(max)) {
                            $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        max = parseInt(max);
                        if (min != null && max < min) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                            e.IsValid = false;
                            return;
                        }
                    }

                    field.number.max = max;
                    field.number.min = min;
                }
                break;
            case 'float':
                {
                    field.float = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    if (min === null || min.length === 0) {
                        min = null;
                    }
                    else {
                        if (!$.isNumeric(min)) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        min = parseFloat(min);
                        if (min < 0) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }
                    }

                    if (max === null || max.length === 0) {
                        max = null;
                    }
                    else {
                        if (!$.isNumeric(max)) {
                            $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                            e.IsValid = false;
                            return;
                        }

                        max = parseFloat(max);
                        if (min != null && max < min) {
                            $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                            e.IsValid = false;
                            return;
                        }
                    }

                    field.float.max = max;
                    field.float.min = min;
                }
                break;
            case 'date':
                {
                    field.date = {};
                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    var minMoment = null;
                    if (min === '@@HOY@@') {
                        minMoment = moment();
                    }
                    else if (min.length > 0) {
                        minMoment = moment(min, 'L');
                    }

                    if (minMoment !== null && !minMoment.isValid()) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    var maxMoment = null;
                    if (max === '@@HOY@@') {
                        maxMoment = moment();
                    }
                    else if (max.length > 0) {
                        maxMoment = moment(max, 'L');
                    }

                    if (maxMoment !== null && !maxMoment.isValid()) {
                        $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    if (minMoment !== null && maxMoment !== null && minMoment >= maxMoment) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                        e.IsValid = false;
                        return;
                    }

                    field.date.max = max;
                    field.date.min = min;
                }
                break;
            case 'string':
                {
                    field.string = {};
                    var $inputStringRegex = $('input[rel=stringregex]', $tr);
                    var value = $inputStringRegex.val();

                    if (value == null || value.length === 0) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    try {
                        var regex = new RegExp(value);
                    }
                    catch (error) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    field.string.regex = value;
                }
                break;
            default:
                break;
        }

        fields.push(field);
    }

    $hiddenExtendedCaseFields.val(JSON.stringify(fields));
    e.IsValid = true;
}

function AddExtendedProfileBusinessCodeFields(field) {
    var $tbody = $('> tbody', $tableExtendedProfileBusinessCodeFields);
    var $lastTr = $('tr:last-child', $tbody);
    var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
    var $newTr = $('<tr></tr>');
    $newTr.addClass(newTrClass);
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputName = $('<input type="text" rel="name" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputName);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $td = $('<td></td>');
    $newTr.append($td);

    var $selectDataType = $('<select rel="datatype"><option value="string">Texto</option><option value="number">Numérico</option><option value="dropdown">Lista</option></select>');
    $td.append($selectDataType);

    $td = $('<td></td>');
    $newTr.append($td);

    var $divString = $('<div rel="string" />');
    $td.append($divString);

    var $inputGroup = $('<div class="advanced-input-group"><span>Expresión regular para validar:</span></div>');
    var $inputStringTextRegex = $('<input type="text" rel="stringregex" class="inputtext advanced-input-control" placeholder="Expresión regular" />');
    $inputStringTextRegex.popover({
        html: true,
        trigger: 'focus',
        title: 'Aclaración',
        placement: 'left',
        template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
        content: 'La expresión regular debería validar que no se ingresen espacios, comas o caracteres especiales y una longitud máxima. Permitir datos arbitrarios puede generar que los códigos de cliente no sean válidos y no se graben'
    });
    $inputGroup.append($inputStringTextRegex)
    $divString.append($inputGroup);

    var $divNumber = $('<div rel="number" />');
    $td.append($divNumber);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor mínimo:</span></div>');
    var $inputNumberMinimumValue = $('<input type="text" rel="numberminimum" class="inputtext advanced-input-control" placeholder="Valor mínimo" />');
    $inputGroup.append($inputNumberMinimumValue);
    $divNumber.append($inputGroup);

    $inputGroup = $('<div class="advanced-input-group"><span>Valor máximo:</span></div>');
    var $inputNumberMaximumValue = $('<input type="text" rel="numbermaximum" class="inputtext advanced-input-control" placeholder="Valor máximo" />');
    $inputGroup.append($inputNumberMaximumValue);
    $divNumber.append($inputGroup);

    var $divDropdown = $('<div rel="dropdown" />');
    $td.append($divDropdown);

    $inputGroup = $('<div class="advanced-input-group invisible-first-row"><span>Depende de:</span></div>');
    var $inputDropDownDependsOn = $('<input type="text" rel="dropdowndependson" class="inputtext advanced-input-control" placeholder="Nombre de otro campo" />');
    $inputDropDownDependsOn.popover({
        html: true,
        trigger: 'focus',
        title: 'Aclaración',
        placement: 'left',
        template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
        content: 'Debe ingresar el nombre de otro campo de tipo lista definido anteriormente'
    });
    $inputGroup.append($inputDropDownDependsOn);
    $divDropdown.append($inputGroup);

    $inputGroup = $('<div class="advanced-input-group"><span>Ítems de la lista:</span></div>');
    var $tableItems = $('<table rel="dropdownitems" class="advanced-input-table hide-last-column"><thead><tr><th style="width: 20px"></th><th style="width: 100px">Valor</th><th style="max-width: 200px">Texto</th><th style="width: 100px">Depende de valor</th></tr></thead><tbody /><tfoot><tr><td style="text-align: center"><a rel="dropdownitems-add"><span class="fa fa-lg fa-plus-square"></span></a></td><td colspan="3"></td></tr></tfoot></table>');
    $inputGroup.append($tableItems);
    $divDropdown.append($inputGroup);

    var $anchorDropdownItemsAdd = $('a[rel=dropdownitems-add]', $tableItems);
    $anchorDropdownItemsAdd.tooltip({ title: 'Agregar nuevo ítem', html: true });
    $anchorDropdownItemsAdd.click(AddExtendedProfileBusinessCodeFieldsDropdownItem);

    $inputDropDownDependsOn.blur(function () {
        var $this = $(this);
        var $parent = $this.parent().parent();
        var $table = $('table[rel=dropdownitems]', $parent);

        var value = $this.val();
        if (value === null || value.length === 0) {
            if (!$table.hasClass('hide-last-column'))
                $table.addClass('hide-last-column');
        }
        else {
            if ($table.hasClass('hide-last-column'))
                $table.removeClass('hide-last-column');
        }
    });

    $selectDataType.change(function () {
        var $this = $(this);
        var $tr = $this.parent().parent();
        var val = $this.val();
        var $divString = $('div[rel=string]', $tr);
        var $divDropdown = $('div[rel=dropdown]', $tr);
        var $divNumber = $('div[rel=number]', $tr);

        $divString.toggle(val === 'string');
        $divDropdown.toggle(val === 'dropdown');
        $divNumber.toggle(val === 'number');
    }).trigger('change');

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();

        var $trs = $('tr', $tbody);
        for (var i = 0; i < $trs.length; i++) {
            var $tr = $($trs.get(i));
            $tr.removeClass('normal alternate');
            $tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
        }
    });

    if (typeof (field) !== 'undefined' && field !== null && typeof (field.name) !== 'undefined') {
        $inputName.val(field.name);
        $inputDescription.val(field.description);
        $selectDataType.val(field.dataType);
        $selectDataType.trigger('change');

        switch (field.dataType) {
            case 'number':
                $inputNumberMaximumValue.val(field.number.max);
                $inputNumberMinimumValue.val(field.number.min);
                break;
            case 'string':
                $inputStringTextRegex.val(field.string.regex);
                break;
            case 'dropdown':
                $inputDropDownDependsOn.val(field.dropdown.dependsOn);
                $inputDropDownDependsOn.trigger('blur');
                for (var i = 0; i < field.dropdown.items.length; i++) {
                    AddExtendedProfileBusinessCodeFieldsDropdownItem(null, $tableItems, field.dropdown.items[i]);
                }
                break;
            default:
                break;
        }
    }

    return $newTr;
}

function AddExtendedProfileBusinessCodeFieldsDropdownItem(event, $table, item) {
    if (event !== null) {
        var $this = $(this);
        $table = $this.parent().parent().parent().parent();
    }

    var $tbody = $('tbody', $table);
    var $newTr = $('<tr></tr>');
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este campo', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputValue = $('<input type="text" rel="dropdown-item-value" class="inputtext" maxlength="50" style="width: 100%" />');
    $td.append($inputValue);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="dropdown-item-description" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDependsOn = $('<input type="text" rel="dropdown-item-dependson" class="inputtext" style="width: 100%" />');
    $td.append($inputDependsOn);
    $inputDependsOn.popover({
        html: true,
        trigger: 'focus',
        title: 'Aclaración',
        placement: 'left',
        template: $.fn.popover.Constructor.DEFAULTS.template.replace('class="popover"', 'class="popover" style="max-width: 300px !important"'),
        content: 'En caso de ingresar un valor, se evaluará contra el valor actual del campo definido en <span class="mono">Depende de</span>. Si no se ingresa valor, este ítem estará siempre visible'
    });

    $inputDescription.keydown(function (e) {
        if (e.key === 'Tab' && !e.shiftKey) {
            var $tr = $(this).parent().parent();
            var $table = $tr.parent().parent();
            if ($table.hasClass('hide-last-column')) {
                var $inputValue = $('input[rel=dropdown-item-value]', $tr);
                var $inputDescription = $('input[rel=dropdown-item-description]', $tr);
                var value = $inputValue.val();
                var description = $inputDescription.val();

                if (value.trim().length > 0 && description.trim().length > 0) {
                    if ($tr.closest("tr").is(":last-child")) {
                        var $addedTr = AddExtendedProfileBusinessCodeFieldsDropdownItem(null, $table, null);
                        $addedTr.prop('autoadded', true);
                        var $firstInput = $('input[rel=dropdown-item-value]', $addedTr).focus();
                        e.preventDefault();
                    }
                }
                else if (value.length === 0 && description.length === 0 && $tr.prop('autoadded') === true) {
                    $tr.remove();
                }
            }
        }
    });

    $inputDependsOn.keydown(function (e) {
        if (e.key === 'Tab' && !e.shiftKey) {
            var $tr = $(this).parent().parent();
            var $table = $tr.parent().parent();
            if (!$table.hasClass('hide-last-column')) {
                var $inputValue = $('input[rel=dropdown-item-value]', $tr);
                var $inputDescription = $('input[rel=dropdown-item-description]', $tr);
                var value = $inputValue.val();
                var description = $inputDescription.val();

                if (value.trim().length > 0 && description.trim().length > 0) {
                    if ($tr.closest("tr").is(":last-child")) {
                        var $addedTr = AddExtendedProfileBusinessCodeFieldsDropdownItem(null, $table, null);
                        $addedTr.prop('autoadded', true);
                        var $firstInput = $('input[rel=dropdown-item-value]', $addedTr).focus();
                        e.preventDefault();
                    }
                }
                else if (value.length === 0 && description.length === 0 && $tr.prop('autoadded') === true) {
                    $tr.remove();
                }
            }
        }
    });

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();
    });

    if (typeof (item) !== 'undefined' && item !== null && typeof (item.value) !== 'undefined') {
        $inputValue.val(item.value);
        $inputDescription.val(item.description);
        $inputDependsOn.val(item.dependsOn);
    }

    return $newTr;
}

function ValidateExtendedProfileBusinessCodeFields(sender, e) {
    var $sender = $(sender);
    var $tbody = $('> tbody', $tableExtendedProfileBusinessCodeFields);
    var $trs = $('> tr', $tbody);

    if ($trs.length === 0) {
        $hiddenExtendedProfileBusinessCodeFields.val('');
        e.IsValid = true;
        return;
    }

    var fields = [];

    for (var i = 0; i < $trs.length; i++) {
        var $tr = $($trs.get(i));

        var field = {
            name: null,
            description: null,
            dataType: null,
            string: {
                regex: null
            },
            number: {
                min: null,
                max: null
            },
            dropdown: {
                dependsOn: null,
                items: null
            }
        };

        var $inputName = $('input[rel=name]', $tr);
        var name = $inputName.val();
        var $inputDescription = $('input[rel=description]', $tr);
        var description = $inputDescription.val().trim();
        var $selectDataType = $('select[rel=datatype]', $tr);
        var dataType = $selectDataType.val();

        var regexName = /^[A-Za-z0-9_-]{1,20}$/;
        if (!regexName.test(name)) {
            $sender.text('El nombre del campo ' + (i + 1) + ' debe contener como máximo 20 caracteres, sin incluir espacios ni caracteres especiales');
            e.IsValid = false;
            return;
        }

        field.name = name;

        if (description.length === 0) {
            $sender.text('La descripción del campo ' + name + ' es inválido');
            e.IsValid = false;
            return;
        }

        field.description = description;
        field.dataType = dataType;

        switch (dataType) {
            case 'dropdown':
                {
                    field.string = null;
                    field.number = null;

                    var $inputDropDownDependsOn = $('input[rel=dropdowndependson]', $tr);
                    var dependsOn = $inputDropDownDependsOn.val();
                    var otherList = null;

                    if (dependsOn.length > 0) {
                        var isTheFirstDropdownField = true;
                        var foundTheOtherList = false;
                        for (var j = 0; j < fields.length; j++) {
                            if (fields[j].dataType === 'dropdown') {
                                isTheFirstDropdownField = false;

                                if (fields[j].name === dependsOn) {
                                    foundTheOtherList = true;
                                    otherList = fields[j];
                                }
                            }
                        }

                        if (isTheFirstDropdownField) {
                            $sender.text('El campo ' + name + ' de tipo Lista no puede depender de otra lista dado que no existe otro campo Lista anterior a este');
                            e.IsValid = false;
                            return;
                        }

                        if (!foundTheOtherList) {
                            $sender.text('El campo ' + name + ' de tipo Lista hace referencia a un campo de tipo Lista que no existe');
                            e.IsValid = false;
                            return;
                        }

                        field.dropdown.dependsOn = dependsOn;
                    }
                    else {
                        field.dropdown.dependsOn = null;
                    }

                    field.dropdown.items = [];
                    var $tableItems = $('table[rel=dropdownitems]', $tr);
                    var $tbodyTableItems = $('tbody', $tableItems);
                    var $trsTableItems = $('tr', $tbodyTableItems);

                    for (var j = 0; j < $trsTableItems.length; j++) {
                        var $trTableItem = $($trsTableItems.get(j));

                        var $inputTableItemValue = $('input[rel=dropdown-item-value]', $trTableItem);
                        var $inputTableItemDescription = $('input[rel=dropdown-item-description]', $trTableItem);
                        var $inputTableItemDependsOn = $('input[rel=dropdown-item-dependson]', $trTableItem);

                        var itemValue = $inputTableItemValue.val().trim();
                        var itemDescription = $inputTableItemDescription.val().trim();
                        var itemDependsOn = $inputTableItemDependsOn.val();

                        if (itemValue.length === 0) {
                            $sender.text('El ítem ' + (j + 1) + ' de la lista del campo ' + name + ' no tiene definido el valor');
                            e.IsValid = false;
                            return;
                        }

                        if (itemDescription.length === 0) {
                            $sender.text('El ítem ' + itemValue + ' de la lista del campo ' + name + ' no tiene definida la descripción');
                            e.IsValid = false;
                            return;
                        }

                        if (field.dropdown.dependsOn === null) {
                            itemDependsOn = null;
                        }
                        else {
                            var found = false;
                            for (var k = 0; k < otherList.dropdown.items.length; k++) {
                                if (otherList.dropdown.items[k].value === itemDependsOn) {
                                    found = true;
                                    break;
                                }
                            }

                            if (!found) {
                                $sender.text('El ítem ' + itemValue + ' de la lista del campo ' + name + ' depende de un valor que no existe en la lista de la cual depende');
                                e.IsValid = false;
                                return;
                            }
                        }

                        var item = {
                            value: itemValue,
                            description: itemDescription,
                            dependsOn: itemDependsOn
                        };

                        field.dropdown.items.push(item);
                    }
                }
                break;
            case 'number':
                {
                    field.string = null;
                    field.dropdown = null;

                    var $inputNumberMinimumValue = $('input[rel=numberminimum]', $tr);
                    var min = $inputNumberMinimumValue.val();
                    var $inputNumberMaximumValue = $('input[rel=numbermaximum]', $tr);
                    var max = $inputNumberMaximumValue.val();

                    if (min === null || min.length === 0 || !$.isNumeric(min)) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    min = parseInt(min);
                    if (min < 0) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    if (max === null || max.length === 0 || !$.isNumeric(max)) {
                        $sender.text('El valor máximo a comparar del campo ' + name + ' es inválido');
                        e.IsValid = false;
                        return;
                    }

                    max = parseInt(max);
                    if (max < min) {
                        $sender.text('El valor mínimo a comparar del campo ' + name + ' es inválido. Debe ser mayor al mínimo');
                        e.IsValid = false;
                        return;
                    }

                    field.number.max = max;
                    field.number.min = min;
                }
                break;
            case 'string':
                {
                    field.number = null;
                    field.dropdown = null;

                    var $inputStringRegex = $('input[rel=stringregex]', $tr);
                    var value = $inputStringRegex.val();

                    if (value == null || value.length === 0) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    try {
                        var regex = new RegExp(value);
                    }
                    catch (error) {
                        $sender.text('La expresión regular del campo ' + name + ' es inválida');
                        e.IsValid = false;
                        return;
                    }

                    field.string.regex = value;
                }
                break;
            default:
                break;
        }

        fields.push(field);
    }

    $hiddenExtendedProfileBusinessCodeFields.val(JSON.stringify(fields));
    e.IsValid = true;
}

function ValidatePushNotificationsUrlForPost(sender, e) {
    var url = $textboxPushNotificationsUrlForPost.val().toLowerCase();

    e.IsValid = true;

    if (url.length === 0) {
        return;
    }

    if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
        if (!url.startsWith('https://') && !url.startsWith('http://localhost')) {
            e.IsValid = false;
            return;
        }
        if (url.startsWith('http://localhost')) {
            return;
        }
    }
    else {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            e.IsValid = false;
            return;
        }
    }

    if (!url.startsWith('http://localhost')) {
        var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:127|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
        if (!urlRegex.test(url)) {

            e.IsValid = false;
            return;
        }
    }
}

function ValidateYFlowUrlApi(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var url = $textboxYFlowUrlApi.val();

    if (url.length === 0) {
        e.IsValid = false;
        $(sender).text('La URL de yFlow es inválida');
        return;
    }

    if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
        if (!url.startsWith('https://') && !url.startsWith('http://localhost')) {
            e.IsValid = false;
            return;
        }
        if (url.startsWith('http://localhost')) {
            return;
        }
    }
    else {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            e.IsValid = false;
            return;
        }
    }

    if (!url.startsWith('http://localhost')) {
        var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
        if (!urlRegex.test(url)) {

            e.IsValid = false;
            return;
        }
    }

    var username = $textboxYFlowUsername.val();
    if (username.length === 0) {
        e.IsValid = false;
        $(sender).text('El usuario para conectarse a yFlow es inválido');
        return;
    }

    var useCurrentPassword = true;
    var password = null;
    if ($checkboxYFlowPassword.is(':checked')) {
        useCurrentPassword = false;
        password = $textboxYFlowPassword.val();
        if (password.length === 0) {
            e.IsValid = false;
            $(sender).text('La contraseña para conectarse a yFlow es inválida');
            return;
        }
    }

    var timeout = $textboxYFlowTimeout.val();
    var timeoutRegex = /^\d\d$/;
    if (timeout === null || timeout.length === 0 || !$.isNumeric(timeout) || !timeoutRegex.test(timeout)) {
        e.IsValid = false;
        $(sender).text('El timeout es inválido');
        return;
    }

    timeout = parseInt(timeout);
    if (timeout < 0) {
        e.IsValid = false;
        $(sender).text('El timeout es inválido');
        return;
    }

    var dataToSend = { url: url, username: username, useCurrentPassword: (useCurrentPassword ? 1 : 0), password: password };

    $.ajax({
        type: "POST",
        url: "../Services/Flow/Validate",
        data: dataToSend,
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.Success) {
                e.IsValid = data.Result;
            }
            else {
                if (console)
                    console.error('No se pudo validar la url de yFlow: %o', data.d.Error);
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateYFlowUrlWeb(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var url = $textboxYFlowUrlWeb.val();

    if (url.length === 0) {
        e.IsValid = false;
        $(sender).text('La URL de yFlow es inválida');
        return;
    }

    if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
        if (!url.startsWith('https://') && !url.startsWith('http://localhost')) {
            e.IsValid = false;
            return;
        }
        if (url.startsWith('http://localhost')) {
            return;
        }
    }
    else {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            e.IsValid = false;
            return;
        }
    }

    if (!url.startsWith('http://localhost')) {
        var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
        if (!urlRegex.test(url)) {

            e.IsValid = false;
            return;
        }
    }

    var username = $textboxYFlowUsername.val();
    if (username.length === 0) {
        e.IsValid = false;
        $(sender).text('El usuario para conectarse a yFlow es inválido');
        return;
    }

    var useCurrentPassword = true;
    var password = null;
    if ($checkboxYFlowPassword.is(':checked')) {
        useCurrentPassword = false;
        password = $textboxYFlowPassword.val();
        if (password.length === 0) {
            e.IsValid = false;
            $(sender).text('La contraseña para conectarse a yFlow es inválida');
            return;
        }
    }

    var timeout = $textboxYFlowTimeout.val();
    var timeoutRegex = /^\d\d$/;
    if (timeout === null || timeout.length === 0 || !$.isNumeric(timeout) || !timeoutRegex.test(timeout)) {
        e.IsValid = false;
        $(sender).text('El timeout es inválido');
        return;
    }

    timeout = parseInt(timeout);
    if (timeout < 0) {
        e.IsValid = false;
        $(sender).text('El timeout es inválido');
        return;
    }

    var dataToSend = { url: url, username: username, useCurrentPassword: (useCurrentPassword ? 1 : 0), password: password };

    $.ajax({
        type: "POST",
        url: "../Services/Flow/Validate",
        data: dataToSend,
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.Success) {
                e.IsValid = data.Result;
            }
            else {
                if (console)
                    console.error('No se pudo validar la url de yFlow: %o', data.d.Error);
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}


function ValidateYFlowAuthenticationFailedEmail(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var value = $textboxYFlowAuthenticationFailedSubject.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    value = $textboxYFlowAuthenticationFailedEmails.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
    if (!regex.test(value)) {
        e.IsValid = false;
        return;
    }

    value = $textboxYFlowAuthenticationFailedTemplate.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }
}

function ValidateYFlowInvokeFailedEmail(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var value = $textboxYFlowInvokeFailedSubject.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    value = $textboxYFlowInvokeFailedEmails.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
    if (!regex.test(value)) {
        e.IsValid = false;
        return;
    }

    value = $textboxYFlowInvokeFailedTemplate.val();
    if (value.trim().length === 0) {
        e.IsValid = false;
        return;
    }
}

function AddPushNotificationsExtraHeader(field) {
    var $tbody = $('> tbody', $tablePushNotificationsExtraHeaders);
    var $lastTr = $('tr:last-child', $tbody);
    var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
    var $newTr = $('<tr></tr>');
    $newTr.addClass(newTrClass);
    $tbody.append($newTr);

    var $td = $('<td style="text-align: center"></td>');
    $newTr.append($td);

    var $anchorRemove = $('<a><span class="fa fa-lg fa-minus-square"></span></a>');
    $td.append($anchorRemove);
    $anchorRemove.tooltip({ title: 'Quitar este encabezado', html: true });

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputName = $('<input type="text" rel="name" class="inputtext" maxlength="100" style="width: 100%" />');
    $td.append($inputName);

    $td = $('<td></td>');
    $newTr.append($td);

    var $inputDescription = $('<input type="text" rel="value" class="inputtext" style="width: 100%" />');
    $td.append($inputDescription);

    $anchorRemove.click(function () {
        var $tr = $(this).parent().parent();
        $tr.remove();

        var $trs = $('tr', $tbody);
        for (var i = 0; i < $trs.length; i++) {
            var $tr = $($trs.get(i));
            $tr.removeClass('normal alternate');
            $tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
        }
    });

    if (typeof (field) !== 'undefined' && field !== null) {
        $inputName.val(field.name);
        $inputDescription.val(field.value);
    }

    return $newTr;
}

function ValidatePushNotificationsExtraHeaders(sender, e) {
    var $sender = $(sender);
    var $tbody = $('> tbody', $tablePushNotificationsExtraHeaders);
    var $trs = $('> tr', $tbody);

    if ($trs.length === 0) {
        $hiddenPushNotificationsExtraHeaders.val('');
        e.IsValid = true;
        return;
    }

    var fields = [];

    for (var i = 0; i < $trs.length; i++) {
        var $tr = $($trs.get(i));

        var field = {
            name: null,
            value: null
        };

        var $inputName = $('input[rel=name]', $tr);
        var name = $inputName.val();
        var $inputValue = $('input[rel=value]', $tr);
        var value = $inputValue.val().trim();

        var regexName = /^[A-Za-z0-9_-]{1,50}$/;
        if (!regexName.test(name)) {
            $sender.text('El nombre del campo ' + (i + 1) + ' debe contener como máximo 50 caracteres, sin incluir espacios ni caracteres especiales');
            e.IsValid = false;
            return;
        }

        field.name = name;

        if (value.length === 0) {
            $sender.text('El valor del campo ' + name + ' es inválido');
            e.IsValid = false;
            return;
        }

        field.value = value;

        fields.push(field);
    }

    $hiddenPushNotificationsExtraHeaders.val(JSON.stringify(fields));
    e.IsValid = true;
}

function ValidateMaxElapsedMinutesToCloseYFlowCases(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var $textboxMaxElapsedMinutesToCloseYFlowCases = $('#textboxMaxElapsedMinutesToCloseYFlowCases');
    var minutes = $textboxMaxElapsedMinutesToCloseYFlowCases.val();

    if (minutes.length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\d{1,5}$/;
    if (!regex.test(minutes)) {
        e.IsValid = false;
        return;
    }

    var minutes = parseInt(minutes);
    if (minutes < 0 || minutes > 43200) {
        e.IsValid = false;
        return;
    }
}

function ValidateYFlowMaxMinutesForPendingMessages(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    var $textboxYFlowMaxMinutesForPendingMessages = $('#textboxYFlowMaxMinutesForPendingMessages');
    var minutes = $textboxYFlowMaxMinutesForPendingMessages.val();

    if (minutes.length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\d{1,5}$/;
    if (!regex.test(minutes)) {
        e.IsValid = false;
        return;
    }

    var minutes = parseInt(minutes, 10);
    if (minutes < 1 || minutes > 60) {
        e.IsValid = false;
        return;
    }
}

function ValidateCloudIPRestrictionsWeb(sender, e) {
    var $textboxCloudIPRestrictionsWeb = $('#textboxCloudIPRestrictionsWeb');
    var text = $textboxCloudIPRestrictionsWeb.val();

    if (text.length === 0) {
        e.IsValid = true;
        return;
    }

    var lines = text.split('\n');
    var ipRegex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))?$/;
    for (var i = 0; i < lines.length; i++) {
        var line = lines[i];
        if (line.length === 0 || !ipRegex.test(line)) {
            e.IsValid = false;
            return;
        }
    }

    e.IsValid = true;
}

function ValidateCloudIPRestrictionsWebAgent(sender, e) {
    var $textboxCloudIPRestrictionsWebAgent = $('#textboxCloudIPRestrictionsWebAgent');
    var text = $textboxCloudIPRestrictionsWebAgent.val();

    if (text.length === 0) {
        e.IsValid = true;
        return;
    }

    var lines = text.split('\n');
    var ipRegex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))?$/;
    for (var i = 0; i < lines.length; i++) {
        var line = lines[i];
        if (line.length === 0 || !ipRegex.test(line)) {
            e.IsValid = false;
            return;
        }
    }

    e.IsValid = true;
}

function ValidateGoogleAuth(sender, e) {
    if (!$checkboxGoogleAuthEnabled.is(':checked')) {
        e.IsValid = true;
        return;
    }

    var $textboxGoogleAuthHostedDomain = $('#textboxGoogleAuthHostedDomain');
    var domain = $textboxGoogleAuthHostedDomain.val();
    if (domain.length === 0 || domain.trim().length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /^\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
    if (!regex.test(domain)) {
        e.IsValid = false;
        return;
    }

    if (!$checkboxGoogleAuthUseCustom.is(':checked')) {
        e.IsValid = true;
        return;
    }

    var clientid = $textboxGoogleAuthClientID.val();
    var clientsecret = $textboxGoogleAuthClientSecret.val();

    if (clientid.length === 0 || clientsecret.length === 0) {
        e.IsValid = false;
        return;
    }

    e.IsValid = true;
}

function ValidateChatCloudOtherServer(sender, e) {
    var isInTheSameServer = parseInt($dropdownlistChatCloudSameServer.val()) === 1;
    if (isInTheSameServer) {
        e.IsValid = true;
        return;
    }

    var host = $textboxChatCloudOtherServer.val();
    if (host.length === 0) {
        e.IsValid = false;
        return;
    }

    var regex = /(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$)|(^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$)/;
    if (!regex.test(host)) {
        e.IsValid = false;
        return;
    }

    e.IsValid = true;
}

function ValidateTimeZones(sender, e) {
    e.IsValid = true;

    var $dynamicTable = $divTimeZonesForConsolidation.prop('dynamicTable');
    var values = $dynamicTable.getValues();
    if (values === null || values.length === 0) {
        $hiddenTimeZonesToConsolide.val('');
        e.IsValid = true;
        return;
    }

    var defaultTimeZone = timeZones.find(tz => tz.Id === $selectDefaultTimeZone.val());
    var timezone1 = timeZones.find(tz => tz.Id === values[0].timezone);

    if (timezone1.Id === defaultTimeZone.Id ||
        timezone1.BaseUtcOffset === defaultTimeZone.BaseUtcOffset) {
        e.IsValid = false;
        return;
    }

    if (values.length > 1) {
        var timezone2 = timeZones.find(tz => tz.Id === values[1].timezone);

        if (timezone1.Id === timezone2.Id ||
            timezone1.BaseUtcOffset === timezone2.BaseUtcOffset ||
            timezone2.Id === defaultTimeZone.Id ||
            timezone2.BaseUtcOffset === defaultTimeZone.BaseUtcOffset) {
            e.IsValid = false;
            return;
        }

        if (values.length > 2) {
            var timezone3 = timeZones.find(tz => tz.Id === values[2].timezone);

            if (timezone1.Id === timezone3.Id ||
                timezone1.BaseUtcOffset === timezone3.BaseUtcOffset ||
                timezone2.Id === timezone3.Id ||
                timezone2.BaseUtcOffset === timezone3.BaseUtcOffset ||
                timezone3.Id === defaultTimeZone.Id ||
                timezone3.BaseUtcOffset === defaultTimeZone.BaseUtcOffset) {
                e.IsValid = false;
                return;
            }
        }
    }

    var timeZonesToConsolide = '';
    for (var i = 0; i < values.length; i++) {
        if (i > 0) {
            timeZonesToConsolide += ',';
        }

        timeZonesToConsolide += values[i].timezone;
    }
    $hiddenTimeZonesToConsolide.val(timeZonesToConsolide);
}

function PopupCenter(url, title, w, h) {
    // Fixes dual-screen position                         Most browsers      Firefox
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
    var top = ((height / 2) - (h / 2)) + dualScreenTop;
    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

    // Puts focus on the newWindow
    if (window.focus) {
        newWindow.focus();
    }
}

function manageMsjToken(status, action) {
    const mapping = {
        Ok: {
            [YFlowInstances.YFlow]: $spanAccessTokenOk,
            [YFlowInstances.YFlowContingency]: $spanAccessTokenContingencyOk,
        },
        Error: {
            [YFlowInstances.YFlow]: $spanAccessTokenError,
            [YFlowInstances.YFlowContingency]: $spanAccessTokenContingencyError,
        }
    };

    const element = mapping[status][YflowInstance];
    if (element) {
        if (action === "Show") {
            element.show();
        } else if (action === "Hide") {
            element.hide();
        }
    }
}

function FacebookTokenCallbackMessage(event, url, successTitle, successMessage, errorTitle, errorMessage) {
    // Valores por defecto
    url = url || "SystemSettings.aspx/AccessYFlow";
    successTitle = successTitle || "Acceso a YFlow";
    successMessage = successMessage || "Se ha configurado el acceso a YFlow de forma correcta.";
    errorTitle = errorTitle || "Acceso a YFlow";
    errorMessage = errorMessage || "Credenciales incorrectas";
    

    try {
        var urlParams = new URLSearchParams(event.data.split('?')[1]);
        var accessToken = urlParams.get('access_token');
        var flowType = getEnumKeyByValue(YFlowInstances, YflowInstance);
    } catch (e) {
        return;
    }

    LoadingDialog({
        title: successTitle,
        onClose: function () {
            var dataToSend = JSON.stringify({
                accessToken: accessToken,
                flowType: flowType
            });

            manageMsjToken("Ok", "Hide")
            manageMsjToken("Error", "Hide")

            $.ajax({
                type: "POST",
                url: url,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: dataToSend,
                async: false,
                success: function (result) {
                    if (result.d.Success) {
                        AlertDialog(successTitle, successMessage);
                        manageMsjToken("Ok", "Show");
                    } else {
                        manageMsjToken("Error", "Show");
                        AlertDialog(errorTitle, errorMessage, null, null, 'Error');
                    }
                },
                error: function () {
                    manageMsjToken("Error", "Show");
                    AlertDialog(errorTitle, 'Ocurrió un error.', null, null, 'Error');
                }
            });
        },
        timeout: 500
    });
}
function ValidateChatCloudPort(sender, e) {
    e.IsValid = true;

    var $textboxChatCloudPort = $('#textboxChatCloudPort');
    var value = $textboxChatCloudPort.val();

    if (value.length === 0 || !/^[0-9]{2,5}$/.test(value)) {
        e.IsValid = false;
        return;
    }

    value = parseInt(value, 10);
    if (isNaN(value)) {
        e.IsValid = false;
        return;
    }

    if (value === 80 || value === 443) {
        return;
    }

    if (value < 8000) {
        e.IsValid = false;
        return;
    }
}

function ValidateChatCloudPortForYSocial(sender, e) {
    e.IsValid = true;

    var $textboxChatCloudPortForYSocial = $('#textboxChatCloudPortForYSocial');
    var value = $textboxChatCloudPortForYSocial.val();

    var $textboxChatCloudPort = $('#textboxChatCloudPort');
    var anotherValue = $textboxChatCloudPort.val();

    if (value.length === 0 || !/^[0-9]{2,5}$/.test(value)) {
        e.IsValid = false;
        return;
    }

    value = parseInt(value, 10);
    if (isNaN(value)) {
        e.IsValid = false;
        return;
    }

    if (value === 80 || value === 443) {
        return;
    }

    if (value < 8000) {
        e.IsValid = false;
        return;
    }
}

function ValidateCloudHeadersToAdd(sender, e) {
    e.IsValid = true;

    var $sender = $(sender);
    $hiddenCloudHeadersToAdd.val('');

    var $dynamicTable = $divCloudHeadersToAdd.prop('dynamicTable');
    var values = $dynamicTable.getValues();
    if (values === null || values.length === 0) {
        e.IsValid = true;
        return;
    }

    var headers = [];
    for (var i = 0; i < values.length; i++) {
        var name = values[i].name.trim();
        var value = values[i].value.trim();

        var regexName = /^[A-Za-z0-9_-]{1,50}$/;
        if (!regexName.test(name)) {
            $sender.text($.i18n('configuration-systemsettings-cloud_headers-invalid_name', i + 1));
            e.IsValid = false;
            return;
        }

        if (value.length === 0) {
            $sender.text($.i18n('configuration-systemsettings-cloud_headers-invalid_value', name));
            e.IsValid = false;
            return;
        }

        headers.push({ name: name, value: value });
    }

    $hiddenCloudHeadersToAdd.val(JSON.stringify(headers));
}

function ValidateCloudHeadersToRemove(sender, e) {
    e.IsValid = true;

    var $sender = $(sender);
    $hiddenCloudHeadersToRemove.val('');

    var $dynamicTable = $divCloudHeadersToRemove.prop('dynamicTable');
    var values = $dynamicTable.getValues();
    if (values === null || values.length === 0) {
        e.IsValid = true;
        return;
    }

    var headers = [];
    for (var i = 0; i < values.length; i++) {
        var name = values[i].Name.trim();

        var regexName = /^[A-Za-z0-9_-]{1,50}$/;
        if (!regexName.test(name)) {
            $sender.text($.i18n('configuration-systemsettings-cloud_headers-invalid_name', i + 1));
            e.IsValid = false;
            return;
        }

        if (value.length === 0) {
            $sender.text($.i18n('configuration-systemsettings-cloud_headers-invalid_value', name));
            e.IsValid = false;
            return;
        }

        headers.push({ Name: name, Value: value });
    }

    $hiddenCloudHeadersToRemove.val(JSON.stringify(headers));
}

function ValidateWhatsappUrlRtNotifications(sender, e) {
    e.IsValid = true;

    var url = $textboxWhatsappUrlRtNotifications.val();

    if (url.length === 0)
        return;

    var dataToSend = JSON.stringify({ url: url });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsWhatsappUrlRtNotificationsValid",
        data: dataToSend,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateTelegramUrlRtNotifications(sender, e) {
    e.IsValid = true;

    var url = $textboxTelegramUrlRtNotifications.val();

    if (url.length === 0)
        return;

    var dataToSend = JSON.stringify({ url: url });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsTelegramUrlRtNotificationsValid",
        data: dataToSend,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function YFlowPendingMessagesGenerateJWT() {
    LoadingDialog({
        title: 'JWT',
        onTimeout: function () {
            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/YFlowPendingMessagesGenerateJWT",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: null,
                success: function (result) {
                    if (result.d.Success) {
                        AlertDialog({
                            title: 'JWT',
                            message: `El JWT para invocar al callback de casos pendientes es:<br /><b>${result.d.Token}</b>`,
                            messageIsHtml: true,
                            width: '700px',
                            messageSpanStyles: {
                                'word-break': 'break-all'
                            }
                        });
                    }
                    else {
                        AlertDialog('JWT', 'No se pudo generar el JWT', null, null, 'Error');
                    }
                },
                error: function () {
                    manageMsjToken("Error", "Show");
                    AlertDialog('JWT', 'Ocurrió un error generando el JWT', null, null, 'Error');
                }
            });
        },
        timeout: 500,
        autoClose: false
    });
}

function ValidateYFlowPendingMessagesCustomCallbackUrl(sender, e) {
    e.IsValid = false;

    let $dropdownlistYFlowPendingMessagesUseCustomCallback = $('#dropdownlistYFlowPendingMessagesUseCustomCallback');
    if ($dropdownlistYFlowPendingMessagesUseCustomCallback.val() === '0') {
        e.IsValid = true;
        return;
    }

    let $textboxYFlowPendingMessagesCustomCallbackUrl = $('#textboxYFlowPendingMessagesCustomCallbackUrl');
    let url = $textboxYFlowPendingMessagesCustomCallbackUrl.val().trim();

    if (url.length === 0) {
        e.IsValid = false;
        return;
    }

    var dataToSend = JSON.stringify({ url: url });

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/IsYFlowPendingMessagesCallbackUrlValid",
        data: dataToSend,
        async: false,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            if (data.d.Success) {
                e.IsValid = data.d.IsValid;
            }
            else {
                e.IsValid = false;
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            e.IsValid = false;
        }
    });
}

function ValidateWhatsAppServiceVoiceCallsIceServers(sender, e) {
    e.IsValid = false;

    let $textboxWhatsAppServiceVoiceCallsIceServers = $('#textboxWhatsAppServiceVoiceCallsIceServers');
    let value = $textboxWhatsAppServiceVoiceCallsIceServers.val().trim();

    if (value.length === 0) {
        e.IsValid = true;
        return;
    }

    try {
        let servers = JSON.parse(value);
        if (typeof (servers) !== 'object' ||
            !Array.isArray(servers) ||
            servers.length === 0) {
            return;
        }

        for (let i = 0; i < servers.length; i++) {
            let server = servers[i];
            if (typeof (server) !== 'object' ||
                server === null) {
                return;
            }

            if (typeof (server.urls) !== 'string' ||
                server.urls.length === 0) {
                if (typeof (server.urls) === 'object' &&
                    Array.isArray(server.urls)) {
                    if (server.urls.length > 0) {
                        for (let j = 0; j < server.urls.length; j++) {
                            if (typeof (server.urls[j]) !== 'string' ||
                                server.urls[j].length === 0) {
                                return;
                            }
                        }
                    }
                    else {
                        return;
                    }
                }
                else {
                    return;
                }
            }

            if (typeof (server.username) === 'string' &&
                server.username.length === 0) {
                return;
            }

            if (typeof (server.credential) === 'string' &&
                server.credential.length === 0) {
                return;
            }
        }

        e.IsValid = true;
    }
    catch (e) { }
}

function ValidateWhatsAppServiceVoiceCallsRecordingHostname(sender, e) {
    e.IsValid = true;

    let $textboxWhatsAppServiceVoiceCallsRecordingHostname = $('#textboxWhatsAppServiceVoiceCallsRecordingHostname');

}

function ValidateWhatsAppServiceVoiceCallsRecordingDownloadHostname(sender, e) {
    e.IsValid = true;

    let $textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname = $('#textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname');
}

function ValidateWhatsAppVoiceCallRecording() {
    let $textboxWhatsAppServiceVoiceCallsRecordingHostname = $('#textboxWhatsAppServiceVoiceCallsRecordingHostname');
    let $textboxWhatsAppServiceVoiceCallsRecordingPort = $('#textboxWhatsAppServiceVoiceCallsRecordingPort');
    let $textboxWhatsAppServiceVoiceCallsRecordingJWTSecret = $('#textboxWhatsAppServiceVoiceCallsRecordingJWTSecret');

    let hostname = $textboxWhatsAppServiceVoiceCallsRecordingHostname.val().trim();
    let port = $textboxWhatsAppServiceVoiceCallsRecordingPort.val().trim();
    let jwtSecret = $textboxWhatsAppServiceVoiceCallsRecordingJWTSecret.val().trim();

    if (hostname.length === 0 ||
        port.length === 0 ||
		jwtSecret.length === 0) {
		return;
    }

    port = parseInt(port, 10);
	if (isNaN(port) ||
		port < 1 ||
		port > 65535) {
		return;
    }

	if (jwtSecret.length < 32) {
		return;
    }

    LoadingDialog({
        title: 'Grabación de llamadas de WhatsApp',
        onTimeout: function () {
            $.ajax({
                type: "POST",
                url: "SystemSettings.aspx/ValidateWhatsAppVoiceCallRecording",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify({ hostname, port, jwtSecret }),
                success: function (result) {
                    if (result.d.Success) {
                        if (result.d.IsValid) {
                            if (result.d.ExistsApp) {
                                AlertDialog({
                                    title: 'Grabación de llamadas de WhatsApp',
                                    message: `Se verificaron bien los datos y se encontró la aplicación de grabación ya creada`,
                                    messageIsHtml: true,
                                    width: '700px',
                                    messageSpanStyles: {
                                        'word-break': 'break-all'
                                    }
                                });
                            }
                            else {
                                AlertDialog({
                                    title: 'Grabación de llamadas de WhatsApp',
                                    message: `Se verificaron bien los datos pero no se encontró la aplicación de grabación`,
                                    messageIsHtml: true,
                                    width: '700px',
                                    messageSpanStyles: {
                                        'word-break': 'break-all'
                                    }
                                });
                            }
                        }
                        else {
                            AlertDialog('Grabación de llamadas de WhatsApp', 'No se pudo verificar los datos', null, null, 'Warning');
                        }
                    }
                    else {
                        AlertDialog('Grabación de llamadas de WhatsApp', 'No se pudo verificar los datos', null, null, 'Error');
                    }
                },
                error: function () {
                    $spanAccessTokenError.show();
                    AlertDialog('Grabación de llamadas de WhatsApp', 'Ocurrió un error verificando los datos', null, null, 'Error');
                }
            });
        },
        timeout: 500,
        autoClose: false
    });
}

var $divEditRequest = null;
function ShowEditHttpRequest(type, $divInfo) {
    let $divRightPanelContainer = $('#divRightPanelContainer');
    if (typeof ($divRightPanelContainer.prop('last-type')) === 'undefined' ||
        $divRightPanelContainer.prop('last-type') !== type) {
        $divRightPanelContainer.empty();
        $divEditRequest = null;
    }

    $divRightPanelContainer.prop('last-type', type);

    let defaultMethod, canEditMethod, hashKeyRequired, hashKeyRegex, canEditBody, fields, canEditHeaders;
    switch (type) {
        case 'yflow_pending_messages_callback':
            defaultMethod = 'POST';
            canEditMethod = false;
            hashKeyRequired = false;
            hashKeyRegex = null;
            canEditBody = false;
            canEditHeaders = false;
            break;
        default:
            defaultMethod = 'GET';
            canEditMethod = true;
            hashKeyRequired = false;
            hashKeyRegex = null;
            canEditBody = true;
            canEditHeaders = true;
            break;
    }

    if ($divEditRequest === null) {
        $divEditRequest = BuildDynamicHttpRequestDefinition({
            id: 'divRequest',
            container: $divRightPanelContainer,
            urlFields: fields,
            headersFields: fields,
            bodyFields: fields,
            defaultMethod: defaultMethod,
            canEditMethod: canEditMethod,
            canEditHeaders: canEditHeaders,
            hashKeyRequired: hashKeyRequired,
            hashKeyRegex: hashKeyRegex,
            canEditBody: canEditBody,
            onAccept: function (values) {
                if (!values.valid) {
                    ConfirmDialog({
                        title: $.i18n('configuration-httprequest-title'),
                        message: $.i18n('configuration-httprequest-invalid-question'),
                        onAccept: function (args) {
                            $divInfo.setInfo(args);
                            ShowRightPanel(false);
                            $.colorbox.close();
                        },
                        acceptArguments: values,
                        closeRightPanel: false
                    });
                }
                else {
                    $divInfo.setInfo(values);
                    ShowRightPanel(false);
                }
            },
            onCancel: function () {
                ShowRightPanel(false);
            }
        });
    }

    let info = $divInfo.getInfo();
    if (info !== null) {
        $divEditRequest.setValues(info);
    }

    ShowRightPanel(true);

    $('#divRightPanelContainer').scrollTop(0);
}

function ValidateYFlowPendingMessagesCallbackEndpoint(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYFlow.is(':checked')) {
        return;
    }

    let info = $divYFlowPendingMessagesCallbackEndpoint.getInfo();
    if (!ValidateEndpoint(info)) {
        $divYFlowPendingMessagesCallbackEndpoint.addClass('invalid');
        e.IsValid = false;
        return;
    }

    $divYFlowPendingMessagesCallbackEndpoint.removeClass('invalid');
}

function ValidateEndpoint(info) {
    if (info === null) {
        return true;
    }

    if (typeof (info.valid) === 'boolean' && !info.valid) {
        return false;
    }

    let url = info.url;
    let hashKey = info.hashKey;

    if (url === null ||
        url.length === 0) {
        return false;
    }

    let urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:127|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/;
    if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
        urlRegex = /^(?:(?:(?:https|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:127|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/;
    }

    if (!urlRegex.test(url)) {
        if (!url.startsWith('http://localhost')) {
            return false;
        }
    }

    let hashKeyRegex = /^[a-zA-Z0-9]{10,}$/;
    if (hashKey.length > 0 &&
        !hashKeyRegex.test(hashKey)) {
        return false;
    }

    return true;
}

function ValidateYUsageUrl(sender, e) {
    e.IsValid = true;

    if (!$checkboxEnableYUsage.is(':checked')) {
        return;
    }

    var url = $textboxYUsageUrl.val();

    if (url.length === 0) {
        e.IsValid = false;
        $(sender).text('La URL de yUsage es inválida');
        return;
    }

    if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
        if (!url.startsWith('https://') && !url.startsWith('http://localhost')) {
            e.IsValid = false;
            return;
        }
        if (url.startsWith('http://localhost')) {
            return;
        }
    }
    else {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            e.IsValid = false;
            return;
        }
    }

    if (!url.startsWith('http://localhost')) {
        var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
        if (!urlRegex.test(url)) {

            e.IsValid = false;
            return;
        }
    }
}

function GenerateYUsageToken() {
    $("#spinnerYUsageToken").show();

    $.ajax({
        type: "POST",
        url: "SystemSettings.aspx/GenerateYUsageToken",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (response) {
            const data = response.d;

            if (data.Success) {
                $("#spanYUsageTokenOk").show();
                $("#spanYUsageTokenError").hide();
            } else {
                $("#spanYUsageTokenOk").hide();
                $("#spanYUsageTokenError").show();

                if (console)
                    console.log("Error al generar token: " + (data.Error?.Message || "Error desconocido"));
            }

            $("#spinnerYUsageToken").hide();
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $("#spanYUsageTokenOk").hide();
            $("#spanYUsageTokenError").show();

            if (console)
                console.log("Error en generateYUsageToken: " + jqXHR.responseText);

            $("#spinnerYUsageToken").hide();
        }
    });
}


