﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;

namespace Yoizen.Social.DomainModel.Whatsapp
{
	/// <summary>
	/// Representa un mensaje interactivo que se puede enviar a WhatsApp
	/// </summary>
	[Newtonsoft.Json.JsonConverter(typeof(InteractiveMessageConverter))]
	public abstract class InteractiveMessage
	{
		#region Inner Classes

		public class InteractiveMessageConverter : Yoizen.Social.DomainModel.JSON.JsonCreationConverter<InteractiveMessage>
		{
			#region Inner Classes

			protected override InteractiveMessage Create(Type objectType, JObject jObject)
			{
				var type = (InteractiveMessageTypes) (short) jObject["type"];
				switch (type)
				{
					case InteractiveMessageTypes.Text:
						return new InteractiveMessageText();
					case InteractiveMessageTypes.Button:
						return new InteractiveMessageButton();
					case InteractiveMessageTypes.Catalog:
						return new InteractiveMessageCatalog();
					case InteractiveMessageTypes.List:
						return new InteractiveMessageList();
					case InteractiveMessageTypes.Product:
						return new InteractiveMessageProduct();
					case InteractiveMessageTypes.ProductList:
						return new InteractiveMessageProductList();
					case InteractiveMessageTypes.Flow:
						return new InteractiveMessageFlow();
					case InteractiveMessageTypes.VoiceCall:
						return new InteractiveMessageVoiceCall();
					case InteractiveMessageTypes.CallPermissionRequest:
						return new InteractiveMessageCallPermissionRequest();
					case InteractiveMessageTypes.CtaUrl:
						return new InteractiveMessageCtaUrl();
					default:
						throw new Newtonsoft.Json.JsonException($"El tipo {type} no está soportado");
				}
			}

			#endregion
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve el tipo de mensaje interactivo
		/// </summary>
		[Newtonsoft.Json.JsonProperty("type")]
		public abstract InteractiveMessageTypes Type { get; }

		/// <summary>
		/// Devuelve o establece el tipo de encabezado
		/// </summary>
		[Newtonsoft.Json.JsonProperty("headerType")]
		public InteractiveMessageHeaderTypes HeaderType { get; set; }

		/// <summary>
		/// Devuelve o esstablece el texto del encabezado para cuando <see cref="HeaderType"/>
		/// sea <see cref="InteractiveMessageHeaderTypes.Text"/>
		/// </summary>
		[Newtonsoft.Json.JsonProperty("headerText")]
		public string HeaderText { get; set; }

		/// <summary>
		/// Devuelve o esstablece el texto del encabezado para cuando <see cref="HeaderType"/>
		/// sea <see cref="InteractiveMessageHeaderTypes.Text"/>
		/// </summary>
		[Newtonsoft.Json.JsonProperty("headerSubText")]
		public string HeaderSubText { get; set; }

		/// <summary>
		/// Devuelve o establece la URL de la imagen del encabezado, para cuando <see cref="HeaderType"/>
		/// sea <see cref="InteractiveMessageHeaderTypes.Image"/> o <see cref="InteractiveMessageHeaderTypes.Video"/> o 
		/// <see cref="InteractiveMessageHeaderTypes.Document"/>
		/// </summary>
		[Newtonsoft.Json.JsonProperty("headerMediaUrl")]
		public string HeaderMediaUrl { get; set; }

		/// <summary>
		/// Devuelve o establece el texto del mensaje interactivo
		/// </summary>
		[Newtonsoft.Json.JsonProperty("body")]
		public string Body { get; set; }

		/// <summary>
		/// Devuelve o establece el tipo de pie
		/// </summary>
		[Newtonsoft.Json.JsonProperty("footerType")]
		public InteractiveMessageFooterTypes FooterType { get; set; }

		/// <summary>
		/// Devuelve o establece el pie del mensaje interactivo
		/// </summary>
		[Newtonsoft.Json.JsonProperty("footerText")]
		public string FooterText { get; set; }

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de la clase <see cref="InteractiveMessage"/>
		/// </summary>
		protected InteractiveMessage()
		{
			this.HeaderType = InteractiveMessageHeaderTypes.None;
			this.FooterType = InteractiveMessageFooterTypes.None;
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Genera el JSON del mensaje interactivo para enviar por Whatsapp
		/// </summary>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public virtual JObject GenerateJsonBody()
		{
			throw new NotImplementedException();
		}

		/// <summary>
		/// Genera el componente header
		/// </summary>
		/// <param name="jInteractive">El Objeto del mensaje interactivo</param>
		protected void AddHeaderComponent(JObject jInteractive)
		{
			if (this.HeaderType == InteractiveMessageHeaderTypes.None)
				return;
			
			var jHeader = new JObject();
			bool isMedia = true;
			string mediaType = "";

			switch (this.HeaderType)
			{
				case InteractiveMessageHeaderTypes.Text:
					isMedia = false;
					jHeader["type"] = "text";
					jHeader["text"] = this.HeaderText;
					break;
				case InteractiveMessageHeaderTypes.Image:
					mediaType = "image";
					break;
				case InteractiveMessageHeaderTypes.Document:
					mediaType = "document";
					break;
				case InteractiveMessageHeaderTypes.Video:
					mediaType = "video";
					break;
			}
			
			if (isMedia)
			{
				jHeader["type"] = mediaType;
				jHeader[mediaType] = new JObject
				{
					["link"] = this.HeaderMediaUrl
				};
			}

			jInteractive["header"] = jHeader;
		}

		/// <summary>
		/// Genera el componente footer
		/// </summary>
		/// <param name="jInteractive">El Objeto del mensaje interactivo</param>
		protected void AddFooterComponent(JObject jInteractive)
		{
			if (this.FooterType == InteractiveMessageFooterTypes.None)
				return;

			var jFooter = new JObject();
			switch (this.FooterType)
			{
				case InteractiveMessageFooterTypes.Text:
					jFooter["text"] = this.FooterText;
					break;
			}

			jInteractive["footer"] = jFooter;
		}
		#endregion
	}
}
