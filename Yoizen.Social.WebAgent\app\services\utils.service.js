(function () {

    angular
        .module('socialApp')
        .factory('utilsService', utilsService);

    utilsService.$inject = [
        '$timeout',
        '$window',
        'blockUI',
        '$translate',
        'CONFIG_INFO',
        '$injector'
    ];

    function utilsService($timeout,
                          $window,
                          blockUI,
                          $translate,
                          CONFIG_INFO,
                          $injector) {
        var service = {
            toCamel: toCamel,
            getErrorByCode: getErrorByCode,
            secondsToTime: secondsToTime,
            getKeyByValue: getKeyByValue,
            getObjectByIdInArray: getObjectByIdInArray,
            stringStartsWith: stringStartsWith,
            displayNotification: displayNotification,
            displayMessageNotification: displayMessageNotification,
            getSocialMessageTypeIcon: getSocialMessageTypeIcon,
            scrollToBottom: scrollToBottom,
            getStringCommaSeparated: getStringCommaSeparated,
            replaceAllInString: replaceAllInString,
            replaceNthOccurrenceInString: replaceNthOccurrenceInString,
            onlyUnique: onlyUnique,
            getEnumPropertyByValue: getEnumPropertyByValue,
            removeHtmlTags: removeHtmlTags,
            showLoading: showLoading,
            hideLoading: hideLoading,
            removeUrlFromString: removeUrlFromString,
            removeBlackSpaces: removeBlackSpaces,
            showLoadingMessage: showLoadingMessage,
            changeLoadingMessage: changeLoadingMessage,
            showLoadingHtml: showLoadingHtml,
            playSound: playSound,
            signUrl: signUrl,
            generateNonce: generateNonce,
            generateTimeStamp: generateTimeStamp,
            loadCustomFonts: loadCustomFonts,
            disconnect: disconnect,
            updatePlaybackRate: updatePlaybackRate,
            updateSendMode: updateSendMode
        };

        service.pNotifyDefaultStackSet = false;

        return service;


        ///////////////////// Service Functions
        function updateSendMode(mode) {
            if (typeof (mode) === 'undefined') {
                var settingsService = $injector.get('settingsService');
                mode = settingsService.settings.agent.settings.sendMode;
            }
        }
        function updatePlaybackRate(rate) {
            if (typeof (rate) === 'undefined') {
                var settingsService = $injector.get('settingsService');
                rate = settingsService.settings.agent.settings.playbackRate;
            }

            if (typeof(rate) === 'string') {
                rate = parseFloat(rate);
            }

            let audioElements = document.getElementsByTagName('audio');
            for (let i = 0; i < audioElements.length; i++) {
                audioElements[i].playbackRate = rate;
            }

            let videoElements = document.getElementsByTagName('video');
            for (let i = 0; i < videoElements.length; i++) {
                videoElements[i].playbackRate = rate;
            }
        }

        function playSound(sound, deviceId) {
            let audioElement = new Audio(`app/content/audio/${sound}.mp3`);

            if (typeof (deviceId) === 'string' &&
                deviceId !== 'default') {
                if (typeof audioElement.setSinkId === 'undefined') {
                    audioElement.play().catch(err => console.error('Error playing sound:', err));
                    return;
                }

                audioElement.setSinkId(deviceId)
                    .then(() => {
                        console.log(`Audio output device set to: ${deviceId}`);
                        audioElement.play().catch(err => console.error('Error playing sound:', err));
                    })
                    .catch(err => console.error('Error setting audio output device:', err));
            }
            else {
                audioElement.play().catch(err => console.error('Error playing sound:', err));
            }
        }

        function removeUrlFromString(text) {
            return text.replace(/(?:https?|ftp):\/\/[\n\S]+/g, '');
        }

        function removeHtmlTags(str) {
            return str.replace(/<(?:.|\n)*?>/gm, '');
        }

        function showLoading() {
            blockUI.start({
                message: 'Cargando...'
            });
            //console.log('SHOW - BlockUI status: ' + JSON.stringify(blockUI.state()));
        }

        function hideLoading() {
            blockUI.stop();
            //console.log('HIDE - BlockUI status: ' + JSON.stringify(blockUI.state()));
        }

        function changeLoadingMessage(message) {
            blockUI.message(message);
        }

        function showLoadingMessage(message) {
            blockUI.start({
                message: message
            });
            //console.log('SHOW - BlockUI status: ' + JSON.stringify(blockUI.state()));
        }

        function showLoadingHtml() {
            blockUI.start({
                message: false,
                html: '<i class="fa fa-2x fa-spinner fa-pulse"></i>'
            });
        }

        function toCamel(o) {
            var build, key, destKey, value;

            if (o instanceof Array) {
                build = [];
                for (key in o) {
                    value = o[key];

                    if (typeof value === "object") {
                        value = toCamel(value);
                    }
                    build.push(value);
                }
            }
            else {
                build = {};
                for (key in o) {
                    if (o.hasOwnProperty(key)) {
                        if (key !== 'ID' && key !== 'IDStr') {
                            destKey = (key.charAt(0).toLowerCase() + key.slice(1) || key).toString();
                        }
                        else {
                            if (key === 'IDStr') {
                                destKey = 'idStr';
                            }
                            else {
                                destKey = 'id';
                            }
                        }
                        value = o[key];
                        if (value !== null && typeof value === "object") {
                            value = toCamel(value);
                        }

                        build[destKey] = value;
                    }
                }
            }
            return build;
        }

        function getErrorByCode(code) {
            if (typeof(window.ServiceApiErrorCodes) !== 'object' &&
                window.ServiceApiErrorCodes === null) {
                return null;
            }

            let error = null;
            switch (code) {
                case window.ServiceApiErrorCodes.AgentInvalidOrNotConnected:
                    error = {
                        description: $translate.instant('ERROR_AGENTINVALIDORNOTCONNECTED'),
                        type: 'critical'
                    };
                    break;

                case window.ServiceApiErrorCodes.CantChangeAgentStatus:
                    error = {
                        description: $translate.instant('ERROR_CANTCHANGEAGENTSTATUS'),
                        type: 'normal'
                    };
                    break;

                case window.ServiceApiErrorCodes.SocialTokenExpected:
                case window.ServiceApiErrorCodes.SocialTokenInvalid:
                    error = {
                        description: $translate.instant('ERROR_CONNECTIONERROR'),
                        type: 'critical'
                    };
                    break;
            }

            return error;
        }

        function secondsToTime(value, includeMiliseconds) {
            if (typeof (includeMiliseconds) !== 'boolean') {
                includeMiliseconds = false;
            }

            return moment.duration(value, "seconds").format(includeMiliseconds ? "hh:mm:ss.SSS" : "hh:mm:ss", {
                trim: false
            });
        }

        function getKeyByValue(value) {
            for (var prop in MessageStatuses) {
                if (MessageStatuses.hasOwnProperty(prop)) {
                    if (MessageStatuses[prop] === value)
                        return prop;
                }
            }
        }

        function getEnumPropertyByValue(value, obj) {
            for (var key in obj) {
                if (obj.hasOwnProperty(key) &&
                    obj[key] == value) {
                    return key;
                }
            }
            return null;
        }

        function getObjectByIdInArray(vec, id) {
            var objSearched = null;
            if (vec) {
                vec.forEach(function (obj) {
                    if (obj.id === id) {
                        objSearched = obj;
                    }
                });
            }

            return objSearched;
        }

        function stringStartsWith(string, prefix) {
            if (typeof(string) === 'string' && string !== null) {
                return string.slice(0, prefix.length) === prefix;
            }

            return false;
        }

        /**
         * Muestra una notificación
         * @param {string} text El texto a mostrar
         * @param {string} title El título a mostrar
         */
        function displayNotification(text, title) {
            if (!service.pNotifyDefaultStackSet) {
                PNotify.desktop.permission();

                PNotify.prototype.options.stack = {
                    firstpos1: 65,
                    dir1: "down",
                    dir2: "left",
                    push: "bottom",
                    spacing1: 36,
                    spacing2: 36,
                    context: $("body"),
                    modal: false
                };

                service.pNotifyDefaultStackSet = true;
            }

            if (typeof(title) !== 'string') {
                title = 'WARNING';
            }
            title = $translate.instant(title);

            text = $translate.instant(text);

            var $notificationBox = new PNotify({
                title: title,
                mouseReset: false,
                hide: true,
                addclass: 'custom-newmessage',
                text: text,
                icon: 'fa fa-info-circle',
                delay: 3000,
                desktop: {
                    desktop: true,
                    fallback: true,
                    text: text,
                    icon: 'app/content/images/logo-social-desktop-notif.png'
                },
                buttons: {
                    closer: true,
                    closerHover: true,
                    sticker: false
                }
            });

            $notificationBox.get().click(function () {
                window.focus();
            });

            var integrationsService = $injector.get('integrationsService');
            integrationsService.executeActions('displaynotification', {
                title: title,
                text: text,
                icon: 'app/content/images/logo-social-desktop-notif.png'
            });
        }

        function displayMessageNotification(message) {
            var messageIcon = getSocialMessageTypeIcon(message.socialServiceType);
            var body = message.socialServiceType === SocialServiceTypes.Mail ? message.parameters.bodyPlainText : message.body;
            var continues = body.length > 100 ? "..." : "";
            body = body.substr(0, 100) + continues;
            body = twemoji.parse(body, twemoji.defaultOptions);
            var displayName = message.postedBy.id !== -1 ? message.postedBy.fullName : message.parameters.anonymousDisplayName;

            if (!service.pNotifyDefaultStackSet) {
                PNotify.desktop.permission();

                PNotify.prototype.options.stack = {
                    firstpos1: 65,
                    dir1: "down",
                    dir2: "left",
                    push: "bottom",
                    spacing1: 36,
                    spacing2: 36,
                    context: $("body"),
                    modal: false
                };

                service.pNotifyDefaultStackSet = true;
            }

            var $notificationBox = new PNotify({
                title: $translate.instant('NEW_MESSAGE'),
                mouseReset: false,
                hide: true,
                addclass: 'custom-newmessage',
                //text: '<img src="' + messageIcon + '"/><span class="ui-pnotify-fullname"> ' + displayName + ' </span> <br> <div class="ui-pnotify-principal-text"> ' + body + '</div><hr><div class="ui-pnotify-queue"><i class="fa fa-inbox"></i> Cola: <span>' + message.queue.name + '</span></div> ',
                text: $translate.instant('NOTIFICATION_BOX_TEXT', {icon: messageIcon, displayName: displayName, body: body, queue: message.queue.name} ),
                icon: 'fa fa-info-circle',
                delay: 1500,
                desktop: {
                    desktop: true,
                    fallback: false,
                    text: body,
                    icon: 'app/content/images/logo-social-desktop-notif.png'
                },
                buttons: {
                    closer: true,
                    closerHover: true,
                    sticker: false
                }
            });

            $notificationBox.get().click(function () {
                window.focus();
            });

            var integrationsService = $injector.get('integrationsService');
            integrationsService.executeActions('displaynotification', {
                title: $translate.instant('NEW_MESSAGE'),
                text: body,
                icon: 'app/content/images/logo-social-desktop-notif.png'
            });
        }

        /**
         * Firma una URL para ser pasada a Social
         * @param {String} url La url a firmar
         * @param {String} key La clave a utilizar para generar la firma
         */
        function signUrl(url, key) {
            if (url.indexOf('?') === -1) {
                url = url + '?ts=' + moment().unix();
            }

            //url += 'nonce=' + getRandomInt(10000, 9999999);
            var queryString = url.substr(url.indexOf('?') + 1);

            var signatureHash = CryptoJS.HmacSHA1(queryString, key);
            var signature = CryptoJS.enc.Hex.stringify(signatureHash).toUpperCase();

            if (url.indexOf('?') === -1) {
                url += '?';
            }
            else {
                url += '&';
            }

            url += 'sig=' + signature;

            return url;
        }

        function getSocialMessageTypeIcon(service) {
            switch (service) {
                case SocialServiceTypes.Twitter:
                    return 'app/content/images/Twitter.png';
                case SocialServiceTypes.Facebook:
                    return 'app/content/images/Facebook.png';
                case SocialServiceTypes.Mail:
                    return 'app/content/images/Mail.png';
                case SocialServiceTypes.Chat:
                    return 'app/content/images/Chat.png';
                case SocialServiceTypes.WhatsApp:
                    return 'app/content/images/Whatsapp.png';
                case SocialServiceTypes.SMS:
                    return 'app/content/images/SMS.png';
                case SocialServiceTypes.Telegram:
                    return 'app/content/images/Telegram.png';
                case SocialServiceTypes.Instagram:
                    return 'app/content/images/Instagram.png';
                case SocialServiceTypes.LinkedIn:
                    return 'app/content/images/Linkedin.png';
                case SocialServiceTypes.FacebookMessenger:
                    return 'app/content/images/FacebookMessenger.png';
                case SocialServiceTypes.Skype:
                    return 'app/content/images/Skype.png';
                default:
                    return '';
            }
        }

        function scrollToBottom() {
            var $container = $('.selected-message-container-all-case > div');
            if ($container.length > 0) {
                $timeout(function () {
                    $container.animate({
                        scrollTop: $container.get(0).scrollHeight
                    }, 500);
                }, 100);
            }
        }

        function getStringCommaSeparated(objToParse, separator) {
            if (typeof(separator) === 'undefined') {
                separator = ',';
            }

            return objToParse.map(function (elem) {
                return elem.text;
            }).join(separator);
        }

        function replaceAllInString(str, find, replace) {
            return str.replace(new RegExp(find, 'g'), replace);
        }

        function replaceNthOccurrenceInString(str, find, replace, nth) {
            var n = 0;
            str = str.replace(new RegExp("\\b" + find + "\\b", "gi"), function (match, i, original) {
                n++;
                return (n === nth) ? replace : match;
            });

            return str;
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        function removeBlackSpaces(text) {
            if (!text)
                return '';

            if (text.indexOf('<div>&nbsp;</div>') > -1) {
                text = text.replace('<div>&nbsp;</div>', '').trim();
            }

            text = text.replace("&nbsp;", '').trim();
            text = text.replace('<div>&nbsp;</div>', '').trim();
            text = text.replace('<div></div>', '').trim();
            return text;
        }

        function generateNonce() {
            var array = new Uint32Array(1);
            var cryptoObj = window.crypto || window.msCrypto; // para IE 11
            cryptoObj.getRandomValues(array);
            return array[0];
        }

        function generateTimeStamp() {
            return moment().unix();
        }

        /**
         * Realiza la carga de tipografías custom desde Google Fonts
         * @param {String} fontFamily
         */
        function loadCustomFonts(fontFamily) {
            if (typeof(fontFamily) === 'undefined' ||
                fontFamily == null ||
                fontFamily.length === 0) {
                fontFamily = 'Fira Sans';
            }

            var standardFonts = ['san francisco', 'segoe ui', 'arial', 'garamond', 'lucida sans unicode', 'lucida grande', 'tahoma', 'verdana'];
            if (standardFonts.indexOf(fontFamily.toLowerCase()) === -1) {
                if (typeof(WebFont) !== 'undefined') {
                    WebFont.load({
                        google: {
                            families: [fontFamily]
                        },
                        loading: function() {
                            var root = document.documentElement;
                            root.style.setProperty('--default-font', '"' + fontFamily + '", ' + getComputedStyle(root).getPropertyValue('--default-font-base'));
                            root.style.setProperty('--default-condensed-font', '"' + fontFamily + '", ' + getComputedStyle(root).getPropertyValue('--default-condensed-font-base'));
                        }
                    });
                }
            }
        }

        function disconnect(notificationText) {
            var messagesService = $injector.get('messagesService');
            var settingsService = $injector.get('settingsService');
            var authenticationService = $injector.get('authenticationService');
            var supervisorChatService = $injector.get('supervisorChatService');
            var $location = $injector.get('$location');

            if (settingsService.settings.agent.chatSupervisor) {
                settingsService.settings.agent.chatSupervisor.stop();
            }
            messagesService.agentMessages.forEach(function (msg) {
                if (msg.message.chat) {
                    if(typeof(msg.message.chat.connection) !== 'undefined') {
                        msg.message.chat.connection.stop();
                    }
                }
            });

            authenticationService.clearCredentials(true, notificationText);
            settingsService.clearData();
            messagesService.clearData();
            supervisorChatService.clearData();

            authenticationService.dataLoading = false;
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $location.path('/login');
        }
    }
})();
