﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{162DB3D5-4AB9-408D-B306-B43CE00375A4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Yoizen.Social.DAL</RootNamespace>
    <AssemblyName>Yoizen.Social.DAL</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseEncrypted|AnyCPU'">
    <OutputPath>bin\ReleaseEncrypted\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <ProjectReference Include="..\Yoizen.Common\Yoizen.Common.csproj">
      <Project>{e50d2ee2-4fcc-4e80-8c19-b9f1933dad74}</Project>
      <Name>Yoizen.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\GlobalAssemblyInfo.cs">
      <Link>Properties\GlobalAssemblyInfo.cs</Link>
    </Compile>
    <Compile Include="AgentServiceDAO.cs" />
    <Compile Include="CallDAO.cs" />
    <Compile Include="CallLogDAO.cs" />
    <Compile Include="CaseMessagePendingDAO.cs" />
    <Compile Include="EmailContactDAO.cs" />
    <Compile Include="ExternalIntegrationSecurityDAO.cs" />
    <Compile Include="Historical\DailyCaseByDayDAO.cs" />
    <Compile Include="Historical\UserSessionDAO.cs" />
    <Compile Include="HistSessionsAgentsCasesMessagesDAO.cs" />
    <Compile Include="HistoryPasswordDAO.cs" />
    <Compile Include="AgentGroupDAO.cs" />
    <Compile Include="Historical\DailySurveyByDayDAO.cs" />
    <Compile Include="Historical\DailySurveyDAO.cs" />
    <Compile Include="CaseReopeningDAO.cs" />
    <Compile Include="SocialUserPreferenceDAO.cs" />
    <Compile Include="TaskLogDAO.cs" />
    <Compile Include="MessageTransferDAO.cs" />
    <Compile Include="TagGroupDAO.cs" />
    <Compile Include="QueueGroupDAO.cs" />
    <Compile Include="Reports\Scheduled\ReportScheduledDAO.cs" />
    <Compile Include="TaskDAO.cs" />
    <Compile Include="IndexesDAO.cs" />
    <Compile Include="Historical\DailyByDayDAO.cs" />
    <Compile Include="Historical\DailyCaseDAO.cs" />
    <Compile Include="Historical\DailyFilterDAO.cs" />
    <Compile Include="Historical\DailyServiceByDayDAO.cs" />
    <Compile Include="Historical\DailyTagByDayDAO.cs" />
    <Compile Include="Historical\HistoricalDAO.cs" />
    <Compile Include="MessagePaymentDAO.cs" />
    <Compile Include="MessageSegmentDAO.cs" />
    <Compile Include="SurveyAnswerDAO.cs" />
    <Compile Include="SurveyDAO.cs" />
    <Compile Include="NotificationDAO.cs" />
    <Compile Include="ChatLogDAO.cs" />
    <Compile Include="AgentLogDAO.cs" />
    <Compile Include="CaseDAO.cs" />
    <Compile Include="ChatMessageDAO.cs" />
    <Compile Include="ChatDAO.cs" />
    <Compile Include="AgentDAO.cs" />
    <Compile Include="AuxReasonDAO.cs" />
    <Compile Include="CaseLogDAO.cs" />
    <Compile Include="Reports\Export\ReportExportDAO.cs" />
    <Compile Include="SocialServiceTypeDAO.cs" />
    <Compile Include="SocialUserServiceDAO.cs" />
    <Compile Include="SocialUserProfileDAO.cs" />
    <Compile Include="AttachmentDAO.cs" />
    <Compile Include="ContactReasonDAO.cs" />
    <Compile Include="SystemStatusDAO.cs" />
    <Compile Include="SocialUserProfilesListDAO.cs" />
    <Compile Include="ExternalIntegrationDAO.cs" />
    <Compile Include="SiteDAO.cs" />
    <Compile Include="TemplateDAO.cs" />
    <Compile Include="Historical\DailyServiceDAO.cs" />
    <Compile Include="Historical\SessionDAO.cs" />
    <Compile Include="Historical\DailyTagDAO.cs" />
    <Compile Include="Historical\DailyDAO.cs" />
    <Compile Include="MessageLogDAO.cs" />
    <Compile Include="PersonDAO.cs" />
    <Compile Include="PermissionDAO.cs" />
    <Compile Include="FilterDAO.cs" />
    <Compile Include="StatsDAO.cs" />
    <Compile Include="TagDAO.cs" />
    <Compile Include="MessageLogicDAO.cs" />
    <Compile Include="SystemSettingsDAO.cs" />
    <Compile Include="ProfileDAO.cs" />
    <Compile Include="QueueDAO.cs" />
    <Compile Include="SocialUserDAO.cs" />
    <Compile Include="MessageDAO.cs" />
    <Compile Include="DbManager.cs" />
    <Compile Include="ServiceDAO.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UserDAO.cs" />
    <Compile Include="CountryDAO.cs" />
    <Compile Include="UserLogDAO.cs" />
    <Compile Include="VideoCallDAO.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Yoizen.Social.AppleMessaging\Yoizen.Social.AppleMessaging.csproj">
      <Project>{cab6f453-4de7-4a04-a067-39c2e0e43cf5}</Project>
      <Name>Yoizen.Social.AppleMessaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Chat\Yoizen.Social.Chat.csproj">
      <Project>{ce119bce-4147-4882-a0b2-1753b95b62da}</Project>
      <Name>Yoizen.Social.Chat</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.DomainModel\Yoizen.Social.DomainModel.csproj">
      <Project>{5FC41DA8-0AFF-48ED-83AF-4C309BA0BF20}</Project>
      <Name>Yoizen.Social.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Facebook\Yoizen.Social.Facebook.csproj">
      <Project>{8504a80b-495f-4b35-9ade-2fc9c6893ea9}</Project>
      <Name>Yoizen.Social.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleBusiness\Yoizen.Social.GoogleBusiness.csproj">
      <Project>{abe15d57-1544-40a9-966a-edf366ced7a5}</Project>
      <Name>Yoizen.Social.GoogleBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GooglePlay\Yoizen.Social.GooglePlay.csproj">
      <Project>{ce444c2e-935b-412b-823d-e46218633e3c}</Project>
      <Name>Yoizen.Social.GooglePlay</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleRBM\Yoizen.Social.GoogleRBM.csproj">
      <Project>{bc8ae046-1e86-40ff-bcd2-e3af4bcf89b2}</Project>
      <Name>Yoizen.Social.GoogleRBM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Instagram\Yoizen.Social.Instagram.csproj">
      <Project>{d3f34bf2-ddc7-497d-a163-9d42b20de75f}</Project>
      <Name>Yoizen.Social.Instagram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Licensing\Yoizen.Social.Licensing.csproj">
      <Project>{0E6DF3BC-8269-466B-9617-C7F84EB9BF0B}</Project>
      <Name>Yoizen.Social.Licensing</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.LinkedIn\Yoizen.Social.LinkedIn.csproj">
      <Project>{eca16318-99d5-4a3f-9abc-eb12ebdb9a59}</Project>
      <Name>Yoizen.Social.LinkedIn</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Mail\Yoizen.Social.Mail.csproj">
      <Project>{809eda6e-48e4-42a1-aad7-b3a6b5d51cf0}</Project>
      <Name>Yoizen.Social.Mail</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.MercadoLibre\Yoizen.Social.MercadoLibre.csproj">
      <Project>{fc98f3f1-85b5-4a6e-92b9-0695928bef79}</Project>
      <Name>Yoizen.Social.MercadoLibre</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Skype\Yoizen.Social.Skype.csproj">
      <Project>{93fa2d4d-6c73-42f3-bffc-10fdfffffeb9}</Project>
      <Name>Yoizen.Social.Skype</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SMS\Yoizen.Social.SMS.csproj">
      <Project>{2b7e8299-a4bf-4c7d-8ef2-ad06896d8e31}</Project>
      <Name>Yoizen.Social.SMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Telegram\Yoizen.Social.Telegram.csproj">
      <Project>{50366855-af3b-4cb5-b814-d2249d8375e3}</Project>
      <Name>Yoizen.Social.Telegram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Twitter\Yoizen.Social.Twitter.csproj">
      <Project>{bb066b75-ba4f-46d9-a057-990147d845b6}</Project>
      <Name>Yoizen.Social.Twitter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.WhatsApp\Yoizen.Social.WhatsApp.csproj">
      <Project>{ca445328-e362-419c-8285-256228ed3bdf}</Project>
      <Name>Yoizen.Social.WhatsApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.YouTube\Yoizen.Social.YouTube.csproj">
      <Project>{98057068-7231-4784-a794-7c2ddfb34663}</Project>
      <Name>Yoizen.Social.YouTube</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\Build\SmartAssembly.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>