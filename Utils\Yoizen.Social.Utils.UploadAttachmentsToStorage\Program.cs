﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.Utils.UploadAttachmentsToStorage
{
	class Program
	{
		static void Main(string[] args)
		{
			if (!Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				Tracer.TraceInfo("La licencia es inválida");
				return;
			}

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowToSaveAttachmentsInAzureStorage)
			{
				Tracer.TraceInfo("La licencia indica que los adjuntos NO se grabarán en Storage. Se aborta");
				return;
			}

			DAL.SystemSettingsDAO.GetAll();

			if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["AttachmentsRootPath"]))
			{
				var attachmentsRootPath = System.Configuration.ConfigurationManager.AppSettings["AttachmentsRootPath"];
				if (Directory.Exists(attachmentsRootPath))
				{
					Tracer.TraceInfo("Se utilizará como carpeta de adjuntos la carpeta {0}", attachmentsRootPath);
					DomainModel.SystemSettings.Instance.AttachmentsRoute = attachmentsRootPath;
				}
			}

			var storage = DomainModel.StorageManager.Instance;

			var deleteAttachmentsFromFileSystem = true;
			if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["DeleteAttachmentsFromFileSystem"]) &&
				bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["DeleteAttachmentsFromFileSystem"], out deleteAttachmentsFromFileSystem))
			{
				if (!deleteAttachmentsFromFileSystem)
					Tracer.TraceInfo("No se borrarán los adjuntos");
			}

			bool processMessages = true, processChats = true, processAvatars = true, includeUrlInParameters = false, generateSql = false, fixWrongAttachments = false;
			if (args != null && args.Contains("--noprocessmessages", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("No se procesarán los adjuntos de los mensajes");
				processMessages = false;
			}
			if (args != null && args.Contains("--noprocesschats", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("No se procesarán los adjuntos de los chats");
				processChats = false;
			}
			if (args != null && args.Contains("--noprocessavatars", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("No se procesarán los ávatars");
				processAvatars = false;
			}
			if (args != null && args.Contains("--includeurlinparameters", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("Se agregará el parámetro Url en parámeters");
				includeUrlInParameters = false;
			}
			if (args != null && args.Contains("--generatesql", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("Se generará SQL en lugar de actualizar");
				generateSql = true;
			}
			if (args != null && args.Contains("--fixwrongattachments", StringComparer.InvariantCultureIgnoreCase))
			{
				Tracer.TraceInfo("Se corregirá los adjuntos que estén en file system pero sus datos en la DB sean erróneos");
				fixWrongAttachments = true;
			}

			if (fixWrongAttachments)
			{
				Spider(storage, new DirectoryInfo(DomainModel.SystemSettings.Instance.AttachmentsRoute), deleteAttachmentsFromFileSystem, includeUrlInParameters);
				Tracer.TraceInfo("Terminó");
				return;
			}

			if (generateSql)
			{
				if (processMessages)
				{
					using (var sw = System.IO.File.CreateText("Updates Messages.sql"))
					{
						Process(storage, deleteAttachmentsFromFileSystem, true, false, false, includeUrlInParameters, generateSql, sw);
					}
				}

				if (processChats)
				{
					using (var sw = System.IO.File.CreateText("Updates Chats.sql"))
					{
						Process(storage, deleteAttachmentsFromFileSystem, false, true, false, includeUrlInParameters, generateSql, sw);
					}
				}

				if (processAvatars)
				{
					using (var sw = System.IO.File.CreateText("Updates Avatars.sql"))
					{
						Process(storage, deleteAttachmentsFromFileSystem, false, false, true, includeUrlInParameters, generateSql, sw);
					}
				}
			}
			else
			{
				Process(storage, deleteAttachmentsFromFileSystem, processMessages, processChats, processAvatars, includeUrlInParameters, false, old: false);
				Process(storage, deleteAttachmentsFromFileSystem, processMessages, processChats, processAvatars, includeUrlInParameters, false, old: true);
			}

			Tracer.TraceInfo("Terminó");
		}

		private static void Process(StorageManager storage, bool deleteAttachmentsFromFileSystem, bool processMessages, bool processChats, bool processAvatars, bool includeUrlInParameters, bool generateSql = false, System.IO.StreamWriter sw = null, bool old = false)
		{
			if (generateSql && sw == null)
				throw new ArgumentNullException(nameof(sw));

			if (processMessages)
			{
				Tracer.TraceInfo("Se procesarán los adjuntos de los mensajes");

				var query = "SELECT [Attachments].*, [Messages].[ServiceID], [Messages].[Date] AS [MessageDate] FROM [Attachments] WITH (NOLOCK) INNER JOIN [Messages] WITH (NOLOCK) ON [Attachments].[MessageID] = [Messages].[MessageID] WHERE [FileSystemRoute] NOT LIKE 'ySocial%'";
				if (long.TryParse(System.Configuration.ConfigurationManager.AppSettings["MaxMessageID"], out long maxMessageId))
					query += $" AND [Messages].[MessageID] < {maxMessageId}";
				if (long.TryParse(System.Configuration.ConfigurationManager.AppSettings["MinMessageID"], out long minMessageId))
					query += $" AND [Messages].[MessageID] > {minMessageId}";
				if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["MaxResults"], out int maxResults))
					query = query.Replace("SELECT ", $"SELECT TOP {maxResults} ");
				if (bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["OrderByDesc"], out bool orderByDesc) && orderByDesc)
					query += $" ORDER BY [Messages].[MessageID] DESC";
				else
					query += $" ORDER BY [Messages].[MessageID]";

				if (old)
					query = query.Replace("[Messages]", "[MessagesOld]").Replace("[Attachments]", "[AttachmentsOld]");

				Tracer.TraceInfo("Se ejecutará la query: {0}", query);

				using (var conn = DAL.DbManager.CreateConnection())
				{
					conn.Open();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = System.Data.CommandType.Text;
						cmd.CommandText = query;
						cmd.CommandTimeout = 0;

						using (var reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var attachment = new DomainModel.Attachment(reader);

								if (!attachment.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) ||
									!bool.TryParse(attachment.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter], out bool savedInAzureStorage) ||
									!savedInAzureStorage)
								{
									try
									{
										var route = storage.SaveAttachment(attachment, Convert.ToInt32(reader["ServiceID"]), Convert.ToDateTime(reader["MessageDate"]));

										if (deleteAttachmentsFromFileSystem)
											storage.DeleteAttachment(attachment);

										attachment.Path = route;

										if (includeUrlInParameters && !attachment.Parameters.ContainsKey("Url"))
										{
											var url = attachment.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
											attachment.Parameters["Url"] = url;
										}

										if (generateSql)
										{
											sw.WriteLine("PRINT 'Se guardará el adjunto {0} del mensaje {1} en azure storage: {2}';", attachment.Index, attachment.MessageID, route);
											sw.WriteLine("EXEC Attachments_UpdateParameters @MessageID={0}, @Index={1}, @Parameters='{2}', @FileSystemRoute='{3}';", attachment.MessageID, attachment.Index, Common.Conversions.ConvertDictionaryToString(attachment.Parameters).Replace("'", "''"), route);
											sw.WriteLine("GO");
										}
										else
										{
											DAL.AttachmentDAO.UpdateParameters(attachment);
										}

										Tracer.TraceInfo("Se guardó el adjunto {0} del mensaje {1} en azure storage: {2}", attachment.Index, attachment.MessageID, route);
									}
									catch (Exception ex)
									{
										Tracer.TraceInfo("No se pudo guardar el adjunto {0} del mensaje {1} en azure storage: {2}", attachment.Index, attachment.MessageID, ex);
									}
								}
							}
						}
					}
				}
			}

			if (processChats)
			{
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat))
				{
					Tracer.TraceInfo("Se procesarán los adjuntos de chat");

					var query = "SELECT [ChatMessages].*, [Chats].[MessageID], [Chats].[ServiceID] FROM [ChatMessages] WITH (NOLOCK) INNER JOIN [Chats] WITH (NOLOCK) ON [ChatMessages].[ChatID] = [Chats].[ChatID] WHERE [ChatMessageType] IN (2, 6) AND CONVERT(VARCHAR(MAX), [Attachment], 0) NOT LIKE 'ySocial%'";
					if (long.TryParse(System.Configuration.ConfigurationManager.AppSettings["MaxMessageID"], out long maxMessageId))
						query += $" AND [Chats].[MessageID] < {maxMessageId}";
					if (long.TryParse(System.Configuration.ConfigurationManager.AppSettings["MinMessageID"], out long minMessageId))
						query += $" AND [Chats].[MessageID] > {minMessageId}";
					if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["MaxResults"], out int maxResults))
						query = query.Replace("SELECT ", $"SELECT TOP {maxResults} ");
					if (bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["OrderByDesc"], out bool orderByDesc) && orderByDesc)
						query += $" ORDER BY [Chats].[MessageID] DESC";

					if (old)
						query = query.Replace("[Messages]", "[MessagesOld]").Replace("[Attachments]", "[AttachmentsOld]").Replace("[ChatMessages]", "[ChatMessagesOld]").Replace("[Chats]", "[ChatsOld]");

					Tracer.TraceInfo("Se ejcutará la query: {0}", query);

					using (var conn = DAL.DbManager.CreateConnection())
					{
						conn.Open();

						using (var cmd = conn.CreateCommand())
						{
							cmd.CommandType = System.Data.CommandType.Text;
							cmd.CommandText = query;
							cmd.CommandTimeout = 0;

							using (var reader = cmd.ExecuteReader())
							{
								while (reader.Read())
								{
									var chatMessage = new DomainModel.ChatMessage(reader);

									if (!chatMessage.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) ||
										!bool.TryParse(chatMessage.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter], out bool savedInAzureStorage) ||
										!savedInAzureStorage)
									{
										var messageId = Convert.ToInt32(reader["MessageID"]);

										try
										{
											var route = storage.SaveAttachment(chatMessage, messageId, Convert.ToInt32(reader["ServiceID"]), Convert.ToDateTime(reader["Date"]));

											if (deleteAttachmentsFromFileSystem)
												storage.DeleteAttachment(chatMessage);

											chatMessage.AttachmentPath = route;

											if (includeUrlInParameters && !chatMessage.Parameters.ContainsKey("Url"))
											{
												var url = chatMessage.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
												chatMessage.Parameters["Url"] = url;
											}

											if (generateSql)
											{
												sw.WriteLine("PRINT 'Se guardará el adjunto {0} del chat {1} del mensaje {2} en azure storage: {3}';", chatMessage.Index, chatMessage.ChatID, messageId, route);
												sw.WriteLine("EXEC ChatMessages_Update @ChatID={0}, @Index={1}, @Attachment=0x{2}, @Parameters='{3}';", chatMessage.ChatID, chatMessage.Index, BitConverter.ToString(Encoding.UTF8.GetBytes(chatMessage.AttachmentPath)).Replace("-", "").ToLower(), Common.Conversions.ConvertDictionaryToString(chatMessage.Parameters).Replace("'", "''"));
												sw.WriteLine("GO");
											}
											else
											{
												DAL.ChatMessageDAO.Update(chatMessage);
											}

											Tracer.TraceInfo("Se guardó el adjunto {0} del chat {1} del mensaje {2} en azure storage: {3}", chatMessage.Index, chatMessage.ChatID, messageId, route);
										}
										catch (Exception ex)
										{
											Tracer.TraceInfo("No se pudo guardar el adjunto {0} del chat {1} del mensaje {2} en azure storage: {3}", chatMessage.Index, chatMessage.ChatID, messageId, ex);
										}
									}
								}
							}
						}
					}
				}
			}

			if (processAvatars)
			{
				Tracer.TraceInfo("Se procesarán los ávatars de servicios");

				var query = "SELECT * FROM [Services] WHERE [Parameters] NOT LIKE '%\"AzureStorage\":\"True\"%'";

				using (var conn = DAL.DbManager.CreateConnection())
				{
					conn.Open();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = System.Data.CommandType.Text;
						cmd.CommandText = query;

						using (var reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								var service = new DomainModel.Service(reader);

								if (!service.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) ||
									!bool.TryParse(service.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter], out bool savedInAzureStorage) ||
									!savedInAzureStorage)
								{
									try
									{
										var fullname = storage.RetrieveAvatarFileName(service);
										if (System.IO.File.Exists(fullname))
										{
											service.Avatar = System.IO.File.ReadAllBytes(fullname);

											var route = storage.SaveAvatar(service);
											if (!string.IsNullOrEmpty(route))
											{
												if (generateSql)
												{
													sw.WriteLine("PRINT 'Se guardará el ávatar del servicio: {0}';", service.ID);
													sw.WriteLine("EXEC Services_UpdateParameters @ServiceID={0}, @Parameters='{1}';", service.ID, Common.Conversions.ConvertDictionaryToString(service.Parameters).Replace("'", "''"));
													sw.WriteLine("GO");
												}
												else
												{
													DAL.ServiceDAO.UpdateParameters(service);
												}

												storage.DeleteAvatar(service);

												Tracer.TraceInfo("Se guardó el ávatar del servicio {0} en azure storage: {1}", service, route);
											}
										}
									}
									catch (Exception ex)
									{
										Tracer.TraceInfo("No se pudo guardar el ávatar del servicio {0} en azure storage: {1}", service, ex);
									}
								}
							}
						}
					}
				}

				Tracer.TraceInfo("Se procesarán los ávatars de personas");

				query = "SELECT [Persons].*, [Users].[UserID], [Agents].[AgentID] FROM [Persons] LEFT OUTER JOIN [Users] ON [Persons].[PersonID] = [Users].[UserID] LEFT OUTER JOIN [Agents] ON [Persons].[PersonID] = [Agents].[AgentID] WHERE [Parameters] NOT LIKE '%\"AzureStorage\":\"True\"%'";

				using (var conn = DAL.DbManager.CreateConnection())
				{
					conn.Open();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = System.Data.CommandType.Text;
						cmd.CommandText = query;

						using (var reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								try
								{
									var person = DomainModel.Person.Create(reader);

									if (!person.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) ||
										!bool.TryParse(person.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter], out bool savedInAzureStorage) ||
										!savedInAzureStorage)
									{
										var fullname = storage.RetrieveAvatarFileName(person);
										if (System.IO.File.Exists(fullname))
										{
											person.Avatar = System.IO.File.ReadAllBytes(fullname);

											var route = storage.SaveAvatar(person);
											if (!string.IsNullOrEmpty(route))
											{
												if (person.Type == PersonTypes.Agent)
												{
													if (generateSql)
													{
														sw.WriteLine("PRINT 'Se guardará el ávatar del agente: {0}';", person.ID);
														sw.WriteLine("EXEC Agents_UpdateParameters @AgentID={0}, @Parameters='{1}';", person.ID, Common.Conversions.ConvertDictionaryToString(person.Parameters).Replace("'", "''"));
														sw.WriteLine("GO");
													}
													else
													{
														DAL.AgentDAO.UpdateParameters(person as DomainModel.Agent);
													}
												}
												else
												{
													if (generateSql)
													{
														sw.WriteLine("PRINT 'Se guardará el ávatar del usuario: {0}';", person.ID);
														sw.WriteLine("EXEC Users_UpdateParameters @UserID={0}, @Parameters='{1}';", person.ID, Common.Conversions.ConvertDictionaryToString(person.Parameters).Replace("'", "''"));
														sw.WriteLine("GO");
													}
													else
													{
														DAL.UserDAO.UpdateParameters(person as DomainModel.User);
													}
												}

												storage.DeleteAvatar(person);

												Tracer.TraceInfo("Se guardó el ávatar de la persona {0} en azure storage: {1}", person, route);
											}
										}
									}
								}
								catch (Exception ex)
								{
									Tracer.TraceInfo("No se pudo guardar el ávatar de la persona {0} en azure storage: {1}", reader.GetValue<int>("PersonID"), ex);
								}
							}
						}
					}
				}
			}
		}

		private static void Spider(StorageManager storage, DirectoryInfo directoryInfo, bool deleteAttachmentsFromFileSystem, bool includeUrlInParameters)
		{
			var types = Enum.GetNames(typeof(DomainModel.SocialServiceTypes));
			var minDate = new DateTime(2020, 1, 1);
			var now = DateTime.Now.Date;

			foreach (var type in types)
			{
				/**
				 * Los adjuntos de CHAT difieren de los adjuntos del resto de los mensajes, porque todos los adjuntos están en la misma carpeta, pero el nombre del archivo
				 * empieza con "{indice}_{nombre}".
				 * Los adjuntos del resto de los canales, cada mensaje tiene directorios con los índices de los adjuntos y ahí adentro está el archivo
				 */
				if (type.Equals("Chat"))
				{
					var path = Path.Combine(DomainModel.SystemSettings.Instance.AttachmentsRoute, type);
					if (Directory.Exists(path))
					{
						Tracer.TraceInfo("Se procesará la carpeta {0}", path);
						
						var date = minDate;
						while (date < now)
						{
							var datedirectory = Path.Combine(path, date.Year.ToString(), date.Month.ToString(), date.Day.ToString());
							if (Directory.Exists(datedirectory))
							{
								var messagesDirectories = Directory.EnumerateDirectories(datedirectory);
								foreach (var messagesDirectory in messagesDirectories)
								{
									var files = Directory.GetFiles(messagesDirectory);
									foreach (var file in files)
									{
										var relativePath = file.Substring(DomainModel.SystemSettings.Instance.AttachmentsRoute.Length).TrimStart('\\');
										var nameParts = relativePath.Split("\\".ToArray());
										var year = int.Parse(nameParts[1]);
										var month = int.Parse(nameParts[2]);
										var day = int.Parse(nameParts[3]);
										var chatId = long.Parse(nameParts[4]);

										var index = int.Parse(nameParts[5].Substring(0, nameParts[5].IndexOf("_")));
										var filename = nameParts[5].Substring(nameParts[5].IndexOf("_") + 1);

										var chatMessage = RetrieveChatMessage(chatId, index, out long? messageId, out int? serviceId);
										if (chatMessage != null && chatMessage.ChatMessageType == ChatMessageTypes.Attachment)
										{
											if (string.IsNullOrEmpty(chatMessage.AttachmentPath))
											{
												Tracer.TraceInfo("El adjunto {0} no tenía bien los datos en la tabla. Se lo actualiza", relativePath);

												try
												{
													chatMessage.AttachmentPath = relativePath.Substring(0, relativePath.LastIndexOf('\\'));
												
													var route = storage.SaveAttachment(chatMessage, messageId.Value, serviceId.Value, new DateTime(year, month, day));
												
													if (deleteAttachmentsFromFileSystem)
														storage.DeleteAttachment(chatMessage);

													chatMessage.AttachmentPath = route;
												
													if (includeUrlInParameters && !chatMessage.Parameters.ContainsKey("Url"))
													{
														var url = chatMessage.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
														chatMessage.Parameters["Url"] = url;
													}

													DAL.ChatMessageDAO.Update(chatMessage);

													Tracer.TraceInfo("Se guardó el adjunto {0} del chat {1} del mensaje {2} en azure storage: {3}", chatMessage.Index, chatMessage.ChatID, messageId, route);
												}
												catch (Exception ex)
												{
													Tracer.TraceInfo("No se pudo guardar el adjunto {0} del chat {1} del mensaje {2} en azure storage: {3}", chatMessage.Index, chatMessage.ChatID, messageId, ex);
												}
											}
											else
											{
												if (deleteAttachmentsFromFileSystem)
												{
													if (chatMessage.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) &&
														bool.Parse(chatMessage.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter]))
													{
														Tracer.TraceInfo("El adjunto {0} del mensaje {1} del chat {2} ya está en azure. Se borra el archivo", relativePath, messageId, chatId);

														try
														{
															storage.DeleteAttachmentFromFileSystem(relativePath);
														}
														catch (Exception ex)
														{
															Tracer.TraceInfo("No se pudo borrar el adjunto {0} del chat {1} del mensaje {2}: {3}", chatMessage.Index, chatMessage.ChatID, messageId, ex);
														}
													}
												}
											}
										}
									}
								}
							}

							date = date.AddDays(1);
						}
					}
				}
				else
				{
					var path = Path.Combine(DomainModel.SystemSettings.Instance.AttachmentsRoute, type);
					if (Directory.Exists(path))
					{
						Tracer.TraceInfo("Se procesará la carpeta {0}", path);

						var date = minDate;
						while (date < now)
						{
							var datedirectory = Path.Combine(path, date.Year.ToString(), date.Month.ToString(), date.Day.ToString());
							if (Directory.Exists(datedirectory))
							{
								var messagesDirectories = Directory.EnumerateDirectories(datedirectory);
								foreach (var messagesDirectory in messagesDirectories)
								{
									var indexesDirectories = Directory.EnumerateDirectories(messagesDirectory);
									foreach (var indexDirectory in indexesDirectories)
									{
										var files = Directory.GetFiles(indexDirectory);
										foreach (var file in files)
										{
											Tracer.TraceVerb("Archivo {0}", file);
											var relativePath = file.Substring(DomainModel.SystemSettings.Instance.AttachmentsRoute.Length).TrimStart('\\');
											var nameParts = relativePath.Split("\\".ToArray());
											var year = int.Parse(nameParts[1]);
											var month = int.Parse(nameParts[2]);
											var day = int.Parse(nameParts[3]);
											var messageId = long.Parse(nameParts[4]);
											var index = byte.Parse(nameParts[5]);
											var filename = nameParts[6];

											var attachment = RetrieveAttachment(messageId, index, out int? serviceId);
											if (attachment != null)
											{
												if (string.IsNullOrEmpty(attachment.Path))
												{
													Tracer.TraceInfo("El adjunto {0} no tenía bien los datos en la tabla. Se lo actualiza", relativePath);

													try
													{
														attachment.Path = relativePath.Substring(0, relativePath.LastIndexOf('\\'));

														var route = storage.SaveAttachment(attachment, serviceId.Value, new DateTime(year, month, day));

														if (deleteAttachmentsFromFileSystem)
															storage.DeleteAttachment(attachment);

														attachment.Path = route;

														if (includeUrlInParameters && !attachment.Parameters.ContainsKey("Url"))
														{
															var url = attachment.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
															attachment.Parameters["Url"] = url;
														}

														DAL.AttachmentDAO.UpdateParameters(attachment);

														Tracer.TraceInfo("Se guardó el adjunto {0} del mensaje {1} en azure storage: {2}", attachment.Index, attachment.MessageID, route);
													}
													catch (Exception ex)
													{
														Tracer.TraceInfo("No se pudo guardar el adjunto {0} del mensaje {1} en azure storage: {2}", attachment.Index, attachment.MessageID, ex);
													}
												}
												else
												{
													if (deleteAttachmentsFromFileSystem)
													{
														if (attachment.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) &&
															bool.Parse(attachment.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter]))
														{
															Tracer.TraceInfo("El adjunto {0} del mensaje {1} ya está en azure. Se borra el archivo", relativePath, messageId);

															try
															{
																storage.DeleteAttachmentFromFileSystem(relativePath);
															}
															catch (Exception ex)
															{
																Tracer.TraceInfo("No se pudo borrar el adjunto {0} del mensaje {1}: {2}", attachment.Index, attachment.MessageID, ex);
															}
														}
													}
												}
											}
										}
									}
								}
							}

							date = date.AddDays(1);
						}
					}
				}
			}
		}

		private static ChatMessage RetrieveChatMessage(long chatId, int index, out long? messageId, out int? serviceId)
		{
			messageId = null;
			serviceId = null;

			var query = $"SELECT [ChatMessages].*, [Chats].[MessageID], [Chats].[ServiceID] FROM [ChatMessages] WITH (NOLOCK) INNER JOIN [Chats] WITH (NOLOCK) ON [ChatMessages].[ChatID] = [Chats].[ChatID] WHERE [ChatMessages].[ChatID] = {chatId} AND [ChatMessages].[Index] = {index}";
			if (chatId < 500000000)
				query = $"SELECT [ChatMessagesOld].*, [ChatsOld].[MessageID], [ChatsOld].[ServiceID] FROM [ChatMessagesOld] WITH (NOLOCK) INNER JOIN [ChatsOld] WITH (NOLOCK) ON [ChatMessagesOld].[ChatID] = [ChatsOld].[ChatID] WHERE [ChatMessagesOld].[ChatID] = {chatId} AND [ChatMessagesOld].[Index] = {index}";

			using (var conn = DAL.DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = System.Data.CommandType.Text;
					cmd.CommandText = query;
					cmd.CommandTimeout = 0;

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							var chatMessage = new DomainModel.ChatMessage(reader);

							messageId = Convert.ToInt64(reader["MessageID"]);
							serviceId = Convert.ToInt32(reader["ServiceID"]);

							return chatMessage;
						}
					}
				}
			}

			return null;
		}

		private static Attachment RetrieveAttachment(long messageId, int index, out int? serviceId)
		{
			serviceId = null;

			var query = $"SELECT [Attachments].*, [Messages].[ServiceID], [Messages].[Date] FROM [Attachments] WITH (NOLOCK) INNER JOIN [Messages] WITH (NOLOCK) ON [Attachments].[MessageID] = [Messages].[MessageID] WHERE [Attachments].[MessageID] = {messageId} AND [Attachments].[Index] = {index}";
			if (messageId < 2000000000)
				query = $"SELECT [AttachmentsOld].*, [MessagesOld].[ServiceID], [MessagesOld].[Date] FROM [AttachmentsOld] WITH (NOLOCK) INNER JOIN [MessagesOld] WITH (NOLOCK) ON [AttachmentsOld].[MessageID] = [MessagesOld].[MessageID] WHERE [AttachmentsOld].[MessageID] = {messageId} AND [AttachmentsOld].[Index] = {index}";

			using (var conn = DAL.DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = System.Data.CommandType.Text;
					cmd.CommandText = query;
					cmd.CommandTimeout = 0;

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							var attachment = new DomainModel.Attachment(reader);

							serviceId = Convert.ToInt32(reader["ServiceID"]);

							return attachment;
						}
					}
				}
			}

			return null;
		}
	}
}
