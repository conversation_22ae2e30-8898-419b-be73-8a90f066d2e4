﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yoizen.Social.DomainModel.Whatsapp
{
	[Flags]
	/// <summary>
	/// Enumeración con los posibles tipo de botones
	/// </summary>
	public enum HSMTemplateButtonsTypes
	{
		/// <summary>
		/// Sin botones
		/// </summary>
		None = 0,

		/// <summary>
		/// Botón con un payload
		/// </summary>
		QuickReply = 1,

		/// <summary>
		/// Botón que realiza una acción
		/// </summary>
		CallToAction = 2,

		/// <summary>
		/// Indica que utiliza todos los tipos de botones
		/// </summary>
		Mixed = 3,

		/// <summary>
		/// Botón con authentication code
		/// </summary>
		AuthCode = 4
	}
}
