(function () {

    'use strict';

    angular
        .module('socialApp')
        .controller('EmailContactsController', EmailContactsController);

    EmailContactsController.$inject = [
        'settingsService',       
        'toastr',
        '$scope',
        'modalSocialService',
        '$translate',
        'emailContactsService',
        'utilsService'
    ];

    function EmailContactsController(settingsService,
                                         toastr,
                                         $scope,
                                         modalSocialService,
                                         $translate,
                                         emailContactsService,
                                         utilsService) {

        var vm = this;
        if (typeof(vm.darkMode) === 'undefined') {
            vm.darkMode = false;
        }


        vm.loadEmailContacts = loadEmailContacts;
        vm.loadMore = loadMore;
        vm.setEmailContact = setEmailContact;
        vm.deleteEmailContact = deleteEmailContact;
        vm.showInsertUpdateModal = showInsertUpdateModal;
        vm.loading = false;
        vm.emailContacts = [];
        vm.noEmailContacts = false;
        vm.agentSettings = settingsService.settings.agent;
        vm.searchText = "";
        vm.moreAvailable = false;
        vm.emailContactsService = emailContactsService;

        loadEmailContacts();

        function loadEmailContacts() {
            vm.loading = true;
        
            vm.emailContactsService.getEmailContacts(vm.agentSettings.id, vm.searchText)
                .then(function (response) {
                    vm.emailContacts = utilsService.toCamel(response.data.Result.items);
        
                    if (Array.isArray(vm.emailContacts) && vm.emailContacts.length > 0) {
                        vm.noEmailContacts = false;
                        vm.moreAvailable = response.data.Result.moreAvailable;
                    } else {
                        vm.noEmailContacts = true;
                        vm.moreAvailable = false;
                    }
                })
                .catch(function () {
                    toastr.error($translate.instant('CANNOT_GET_EMAIL_CONTACTS'));
                    vm.noEmailContacts = true;
                })
                .finally(function () {
                    vm.loading = false;
                });
        }        
        
        function loadMore() {
            vm.loading = true;
            let lastId = vm.emailContacts[vm.emailContacts.length - 1]?.id;
            vm.emailContactsService.getEmailContacts(vm.agentSettings.id, vm.searchText,lastId)
                .then(function (response) {
                    vm.emailContacts.push(...utilsService.toCamel(response.data.Result.items));
                    vm.moreAvailable = response.data.Result.moreAvailable;                    
                })
                .catch(function () {
                    toastr.error($translate.instant('CANNOT_GET_EMAIL_CONTACTS'));
                })
                .finally(function () {
                    vm.loading = false;
                });
        } 

        function setEmailContact(contact) {
            $scope.$parent.setEmailContact(contact);            
        }

        function deleteEmailContact (contact) {
            modalSocialService.showConfirmGeneral({
                title: $translate.instant('EMAIL_CONTACTS_DELETE_TITLE'),
                description: $translate.instant('EMAIL_CONTACTS_DELETE_DESCRIPTION'),
                negativeButtonShown: false,
            }).then(function (modal) {
                modal.element.modal();
                modal.close.then(function (result) {
                    if (typeof (result) !== 'object' || typeof (result.action) !== 'string') {
                        return;
                    }

                    if (result.action === "confirm"){
                        vm.emailContactsService.deleteContact(contact.id)
                            .then(function (response) {
                                toastr.success($translate.instant('EMAIL_CONTACTS_DELETE_SUCCESS'));
                    
                                vm.emailContacts = vm.emailContacts.filter(function (c) {
                                    return c.id !== contact.id;
                                });
                    
                                vm.noEmailContacts = !vm.emailContacts || vm.emailContacts.length === 0;
                            })
                            .catch(function (error) {
                                toastr.error(
                                    error?.data?.message || $translate.instant('EMAIL_CONTACTS_DELETE_ERROR')
                                );
                            });
                    }
                    return;
                });
            });
            
        }

        function showInsertUpdateModal(contact){
            modalSocialService.showActionEmailContact(contact).then(
                function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result){
                        if (typeof (result) !== 'object' || typeof (result.action) !== 'string') {
                            return;
                        }

                        if (result.action === 'insert'){
                            vm.emailContacts.unshift(result.contact);
                        }

                        if (result.action === 'update'){
                            let index = vm.emailContacts.findIndex(c => c.id === result.contact.id);
                            if (index != -1){
                                vm.emailContacts[index] = result.contact;
                            }
                        }
                        return;
                    });
                }
            );
        }
    }    
})();
