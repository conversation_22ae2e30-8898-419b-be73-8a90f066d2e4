﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Yoizen.Social.DomainModel.Settings
{
	/// <summary>
	/// Contiene las configuraciones base de los servicios
	/// </summary>
	public abstract class BaseServiceSettings : BaseSettings
	{
		#region Properties

		/// <summary>
		/// Devuelve el nombre del servicio
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// Devuelve el nombre amistoso del servicio para ser usado en mensajes y configuraciones
		/// </summary>
		public abstract string FriendlyName { get; }

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una instancia de <see cref="SocialServiceSettings"/>
		/// </summary>
		public BaseServiceSettings() : base()
		{
		}

		#endregion

		#region Internal Methods

		/// <summary>
		/// Inicializa los parámetros de la clase a partir de los datos del parámetro <paramref name="data"/>
		/// </summary>
		/// <param name="data">El diccionario con los datos de configuración</param>
		/// <exception cref="ArgumentNullException">Cuando <paramref name="data"/> es null</exception>
		internal override void Load(Dictionary<string, object> data)
		{
			base.Load(data);

			string settingsKey = string.Format("{0}.Name", this.SettingsPrefix);
			if (data.ContainsKey(settingsKey))
				this.Name = (string) data[settingsKey];
		}

		/// <summary>
		/// Persiste los datos de configuración dentro del diccionario especificado por el parámetro <paramref name="data"/>
		/// </summary>
		/// <param name="data">El diccionario donde se persistirán los datos</param>
		/// <param name="omitIgnores">Indica si se deberá ignorar parámetros no importantes</param>
		/// <exception cref="ArgumentNullException">Cuando <paramref name="data"/> es null</exception>
		internal override void Save(Dictionary<string, object> data, bool omitIgnores)
		{
			if (data == null)
				throw new ArgumentNullException("data", "El diccionario no puede ser null");

			string settingsKey = string.Format("{0}.Name", this.SettingsPrefix);
			data.Add(settingsKey, this.Name);
		}

		#endregion
	}
}