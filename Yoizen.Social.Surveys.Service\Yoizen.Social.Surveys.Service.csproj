﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{3A49F562-B5FB-465D-8C07-3B78F5D75FC8}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Yoizen.Social.Surveys.Service</RootNamespace>
    <AssemblyName>Yoizen.Social.Surveys.Service</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;RUNASPROGRAM;SURVEYS</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;SURVEYS</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Yoizen.Social.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseEncrypted|x86'">
    <OutputPath>bin\ReleaseEncrypted\</OutputPath>
    <DefineConstants>TRACE;SURVEYS</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\Dependencies\Twitterizer2\Twitterizer2.csproj">
      <Project>{2fdc3492-6b9e-4771-9755-7892c9cb1e96}</Project>
      <Name>Twitterizer2</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utils\Yoizen.Social.SocialServices.Twitter.Apps\Yoizen.Social.SocialServices.Twitter.Apps.csproj">
      <Project>{9a99b471-355d-4e20-acec-2eed8ed97fc9}</Project>
      <Name>Yoizen.Social.SocialServices.Twitter.Apps</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.AppleMessaging\Yoizen.Social.AppleMessaging.csproj">
      <Project>{cab6f453-4de7-4a04-a067-39c2e0e43cf5}</Project>
      <Name>Yoizen.Social.AppleMessaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Facebook\Yoizen.Social.Facebook.csproj">
      <Project>{8504a80b-495f-4b35-9ade-2fc9c6893ea9}</Project>
      <Name>Yoizen.Social.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleBusiness\Yoizen.Social.GoogleBusiness.csproj">
      <Project>{abe15d57-1544-40a9-966a-edf366ced7a5}</Project>
      <Name>Yoizen.Social.GoogleBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GooglePlay\Yoizen.Social.GooglePlay.csproj">
      <Project>{ce444c2e-935b-412b-823d-e46218633e3c}</Project>
      <Name>Yoizen.Social.GooglePlay</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleRBM\Yoizen.Social.GoogleRBM.csproj">
      <Project>{bc8ae046-1e86-40ff-bcd2-e3af4bcf89b2}</Project>
      <Name>Yoizen.Social.GoogleRBM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Instagram\Yoizen.Social.Instagram.csproj">
      <Project>{d3f34bf2-ddc7-497d-a163-9d42b20de75f}</Project>
      <Name>Yoizen.Social.Instagram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.LinkedIn\Yoizen.Social.LinkedIn.csproj">
      <Project>{eca16318-99d5-4a3f-9abc-eb12ebdb9a59}</Project>
      <Name>Yoizen.Social.LinkedIn</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Mail\Yoizen.Social.Mail.csproj">
      <Project>{809eda6e-48e4-42a1-aad7-b3a6b5d51cf0}</Project>
      <Name>Yoizen.Social.Mail</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Skype\Yoizen.Social.Skype.csproj">
      <Project>{93fa2d4d-6c73-42f3-bffc-10fdfffffeb9}</Project>
      <Name>Yoizen.Social.Skype</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SMS\Yoizen.Social.SMS.csproj">
      <Project>{2b7e8299-a4bf-4c7d-8ef2-ad06896d8e31}</Project>
      <Name>Yoizen.Social.SMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.AppleMessaging\Yoizen.Social.SocialServices.AppleMessaging.csproj">
      <Project>{861df304-42d0-454b-9dc9-904866e18316}</Project>
      <Name>Yoizen.Social.SocialServices.AppleMessaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Chat\Yoizen.Social.SocialServices.Chat.csproj">
      <Project>{907b4153-b3a8-46f1-9a96-5b02413c90e3}</Project>
      <Name>Yoizen.Social.SocialServices.Chat</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Facebook\Yoizen.Social.SocialServices.Facebook.csproj">
      <Project>{081e6a87-39ca-4678-830b-07998f3bdd6c}</Project>
      <Name>Yoizen.Social.SocialServices.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GoogleBusiness\Yoizen.Social.SocialServices.GoogleBusiness.csproj">
      <Project>{c4aa24f5-691d-4b32-b3f6-fb19e65c19b8}</Project>
      <Name>Yoizen.Social.SocialServices.GoogleBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GooglePlay\Yoizen.Social.SocialServices.GooglePlay.csproj">
      <Project>{9de9cbdd-f748-406a-b3d8-6b2442126c0e}</Project>
      <Name>Yoizen.Social.SocialServices.GooglePlay</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GoogleRBM\Yoizen.Social.SocialServices.GoogleRBM.csproj">
      <Project>{037a8c55-d414-4fe1-8245-464111e5c17d}</Project>
      <Name>Yoizen.Social.SocialServices.GoogleRBM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Instagram\Yoizen.Social.SocialServices.Instagram.csproj">
      <Project>{38a173ca-df7c-4209-ae45-e044482ca992}</Project>
      <Name>Yoizen.Social.SocialServices.Instagram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.LinkedIn\Yoizen.Social.SocialServices.LinkedIn.csproj">
      <Project>{931cc905-b81e-4d2d-8830-b6e0eb8ffa52}</Project>
      <Name>Yoizen.Social.SocialServices.LinkedIn</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Mail\Yoizen.Social.SocialServices.Mail.csproj">
      <Project>{c395915e-4f30-4410-ab07-749c5cae936a}</Project>
      <Name>Yoizen.Social.SocialServices.Mail</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.MercadoLibre\Yoizen.Social.SocialServices.MercadoLibre.csproj">
      <Project>{b986d66c-5a3c-45c8-a290-77fd12b3094d}</Project>
      <Name>Yoizen.Social.SocialServices.MercadoLibre</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Skype\Yoizen.Social.SocialServices.Skype.csproj">
      <Project>{15a25c7e-4c8d-4ef8-8b88-fde7af80296a}</Project>
      <Name>Yoizen.Social.SocialServices.Skype</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.SMS\Yoizen.Social.SocialServices.SMS.csproj">
      <Project>{d56efd3c-0fe3-4800-bee2-26c202573352}</Project>
      <Name>Yoizen.Social.SocialServices.SMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Subscriber\Yoizen.Social.SocialServices.Subscriber.csproj">
      <Project>{804b9b56-8c6b-4d21-b6a0-6cdf8af1cc05}</Project>
      <Name>Yoizen.Social.SocialServices.Subscriber</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Telegram\Yoizen.Social.SocialServices.Telegram.csproj">
      <Project>{c74a16cb-8a20-4d65-8a6d-33f8ee85fa4a}</Project>
      <Name>Yoizen.Social.SocialServices.Telegram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Twitter\Yoizen.Social.SocialServices.Twitter.csproj">
      <Project>{0392f986-cb8a-49fc-9322-94fb3077b164}</Project>
      <Name>Yoizen.Social.SocialServices.Twitter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.VideoCall\Yoizen.Social.SocialServices.VideoCall.csproj">
      <Project>{d7b18476-8184-45a1-a612-61dbb0b29df8}</Project>
      <Name>Yoizen.Social.SocialServices.VideoCall</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.WhatsApp\Yoizen.Social.SocialServices.WhatsApp.csproj">
      <Project>{34584168-0afb-4df2-9788-7128d7ef0841}</Project>
      <Name>Yoizen.Social.SocialServices.WhatsApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.YouTube\Yoizen.Social.SocialServices.YouTube.csproj">
      <Project>{0d2b40d5-a308-4c37-b7ad-93bcd1e2a239}</Project>
      <Name>Yoizen.Social.SocialServices.YouTube</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Telegram\Yoizen.Social.Telegram.csproj">
      <Project>{50366855-af3b-4cb5-b814-d2249d8375e3}</Project>
      <Name>Yoizen.Social.Telegram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Twitter\Yoizen.Social.Twitter.csproj">
      <Project>{bb066b75-ba4f-46d9-a057-990147d845b6}</Project>
      <Name>Yoizen.Social.Twitter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.VideoCall\Yoizen.Social.VideoCall.csproj">
      <Project>{4ea5410f-a260-4c11-a799-6d1b299db775}</Project>
      <Name>Yoizen.Social.VideoCall</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.WhatsApp\Yoizen.Social.WhatsApp.csproj">
      <Project>{ca445328-e362-419c-8285-256228ed3bdf}</Project>
      <Name>Yoizen.Social.WhatsApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.YouTube\Yoizen.Social.YouTube.csproj">
      <Project>{98057068-7231-4784-a794-7c2ddfb34663}</Project>
      <Name>Yoizen.Social.YouTube</Name>
    </ProjectReference>
    <Reference Include="Azure.Core, Version=1.44.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.44.0\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Core.Amqp, Version=1.3.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.Amqp.1.3.1\lib\netstandard2.0\Azure.Core.Amqp.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Messaging.ServiceBus, Version=7.18.2.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Messaging.ServiceBus.7.18.2\lib\netstandard2.0\Azure.Messaging.ServiceBus.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EAGetMail40, Version=4.5.1.2, Culture=neutral, PublicKeyToken=e10a0812eb29cf94, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\EAGetMail40.dll</HintPath>
    </Reference>
    <Reference Include="Facebook, Version=6.0.10.0, Culture=neutral, PublicKeyToken=58cb4f2111d1e6de, processorArchitecture=MSIL">
      <HintPath>..\packages\Facebook.7.0.6\lib\net45\Facebook.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis, Version=1.69.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.69.0\lib\net462\Google.Apis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.AndroidPublisher.v3, Version=1.68.0.3576, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.AndroidPublisher.v3.1.68.0.3576\lib\net462\Google.Apis.AndroidPublisher.v3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.69.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.69.0\lib\net462\Google.Apis.Auth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.69.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.69.0\lib\net462\Google.Apis.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Apis.YouTube.v3, Version=1.68.0.3608, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.YouTube.v3.1.68.0.3608\lib\net462\Google.Apis.YouTube.v3.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.11.71.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.11.71\lib\Net45\HtmlAgilityPack.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="LinkedInNET.ApiV2, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\LinkedInNET.ApiV2.6.0.2\lib\net472\LinkedInNET.ApiV2.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MailKit, Version=1.2.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Amqp, Version=2.4.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Amqp.2.6.7\lib\net45\Microsoft.Azure.Amqp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.7.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Exchange.WebServices.2.2\lib\40\Microsoft.Exchange.WebServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices.Auth, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Exchange.WebServices.2.2\lib\40\Microsoft.Exchange.WebServices.Auth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Extensions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=69c3241e6f0468ca, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Model.Extensions.2.0.1459.0\lib\Microsoft.IdentityModel.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MixERP.Net.VCards, Version=1.0.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MixERP.Net.VCards.1.0.7\lib\net461\MixERP.Net.VCards.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NeoSmart.Unicode, Version=2.0.0.0, Culture=neutral, PublicKeyToken=001e289ac32dc6d6, processorArchitecture=MSIL">
      <HintPath>..\packages\Unicode.net.2.0.0\lib\net40\NeoSmart.Unicode.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OpenGraphNet, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\OpenGraph-Net.3.2.8\lib\net472\OpenGraphNet.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.1.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.1.0\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.CodeDom.7.0.0\lib\net462\System.CodeDom.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.7.0.0\lib\net462\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encoding.CodePages, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.0\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Web" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml" />
    <ProjectReference Include="..\Yoizen.Common\Yoizen.Common.csproj">
      <Project>{e50d2ee2-4fcc-4e80-8c19-b9f1933dad74}</Project>
      <Name>Yoizen.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\GlobalAssemblyInfo.cs">
      <Link>Properties\GlobalAssemblyInfo.cs</Link>
    </Compile>
    <Compile Include="ProjectInstaller.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProjectInstaller.Designer.cs">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </Compile>
    <Compile Include="Service.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Service.Designer.cs">
      <DependentUpon>Service.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServicePreshutdownBase.cs">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="Yoizen.Social.ico" />
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
    <None Include="Register Service.cmd">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Yoizen.Social.Business\Yoizen.Social.Business.csproj">
      <Project>{13E5DDD0-A917-4714-9BD5-C10DEC38AC77}</Project>
      <Name>Yoizen.Social.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.DAL\Yoizen.Social.DAL.csproj">
      <Project>{162DB3D5-4AB9-408D-B306-B43CE00375A4}</Project>
      <Name>Yoizen.Social.DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.DomainModel\Yoizen.Social.DomainModel.csproj">
      <Project>{5FC41DA8-0AFF-48ED-83AF-4C309BA0BF20}</Project>
      <Name>Yoizen.Social.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Intervals\Yoizen.Social.Intervals.csproj">
      <Project>{85098912-02d9-4989-9fd8-e34c8fc74950}</Project>
      <Name>Yoizen.Social.Intervals</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Licensing\Yoizen.Social.Licensing.csproj">
      <Project>{0E6DF3BC-8269-466B-9617-C7F84EB9BF0B}</Project>
      <Name>Yoizen.Social.Licensing</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ProjectInstaller.resx">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service.resx">
      <DependentUpon>Service.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\Build\SmartAssembly.targets" />
  <PropertyGroup>
    <PreBuildEvent>xcopy "$(SolutionDir)Yoizen.Social.License.xml" "$(TargetDir)" /y</PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>IF $(ConfigurationName)==ReleaseEncrypted SET result=true
IF $(ConfigurationName)==Release SET result=true
 
IF "%25result%25" == "true" DEL "$(TargetDir)*.pdb" /Q
IF "%25result%25" == "true" DEL "$(TargetDir)*.xml" /Q
IF "%25result%25" == "true" DEL "$(TargetDir)*vshost*" /Q

IF "%25result%25" == "true" DEL /q "$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\*"
IF "%25result%25" == "true" FOR /d %25%25x IN ("$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\*") DO @rd /s /q "%25%25x"
IF "%25result%25" == "true" XCOPY "$(TargetDir)*.*" "$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\" /y
IF $(ConfigurationName)==Release DEL "$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\DocumentFormat.OpenXml.dll" /Q
IF $(ConfigurationName)==Release DEL "$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\BouncyCastle.dll" /Q
IF $(ConfigurationName)==Release DEL "$(SolutionDir)Deploy\$(ConfigurationName)\SurveysService\NeoSmart.Unicode.dll" /Q</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>