﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Web;
using System.Threading.Tasks;

namespace FacebookCallback.Controllers
{
	public class MessengerController : ApiController
	{
		#region Constants

		private const string SecretKeyForHashing = "d4061499a5eb796b73f4569c0df91988";
		
		#endregion

		#region Action Methods

		// GET: api/messenger
		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(
			[FromUri(Name = "hub.mode")] string hubMode,
			[FromUri(Name = "hub.challenge")] string hubChallenge,
			[FromUri(Name = "hub.verify_token")] string hubVerifyToken)
		{
			if (hubMode.Equals("subscribe") && hubVerifyToken.Equals(MessengerManager.VerifyToken))
			{
				string result = hubChallenge;
				var resp = new HttpResponseMessage(HttpStatusCode.OK)
				{
					Content = new StringContent(result, Encoding.UTF8, "text/plain")
				};
				return resp;
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare)
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			return Request.CreateResponse(HttpStatusCode.OK, new MessengerManager().GetPageLastNews(id));
		}

		// POST: api/messenger
		[ActionName("DefaultAction")]
		[HttpPost]
		public async Task<HttpResponseMessage> Post()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			if (!MessengerManager.IgnoreXHubSignatureHeader)
			{
				if (!Request.Headers.Contains("X-Hub-Signature"))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Hub-Signature", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
				if (!xHubSignatureHeader.StartsWith("sha1="))
				{
					Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}
				xHubSignatureHeader = xHubSignatureHeader.Substring(5);

				string bodyHash = Encode(body, MessengerManager.ApplicationSecret);

				if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
				{
					if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
					else
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);

					return new HttpResponseMessage(HttpStatusCode.OK);
				}
			}

			try
			{
				MessengerManager MessengerManager = new MessengerManager();
				await MessengerManager.GenerateFiles(body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("NewsProcessed")]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			try
			{
				MessengerManager MessengerManager = new MessengerManager();
				MessengerManager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("Restart")]
		[HttpDelete]
		public HttpResponseMessage Restart(string id)
		{
			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se invocó la eliminación de los archivos de novedades de la página {0}", id);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			try
			{
				MessengerManager MessengerManager = new MessengerManager();
				MessengerManager.DeleteAllContents(id);

				Yoizen.Common.Tracer.TraceInfo("Se eliminó los archivos de novedades de la página {0}", id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de novedades de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		#endregion

		#region Private Methods & Utils

		private static string Encode(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (HMACSHA1 myhmacsha1 = new HMACSHA1(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				return myhmacsha1.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		#endregion
	}
}