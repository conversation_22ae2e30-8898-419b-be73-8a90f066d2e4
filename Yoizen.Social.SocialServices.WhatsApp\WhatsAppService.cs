﻿using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Amqp.Framing;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.Business.Exceptions;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.AgentNotifications;
using Yoizen.Social.DomainModel.Whatsapp;
using Yoizen.Social.DomainModel.Whatsapp.Movistar;

namespace Yoizen.Social.SocialServices.WhatsApp
{
	/// <summary>
	/// Implementación de un <see cref="ISocialService"/> para WhatsApp
	/// </summary>
	public class WhatsAppService : Business.SocialService
	{
		#region Fields

		private Dictionary<string, string> postbackParameters;
		private DateTime lastInactivityDetectedMailDate = DateTime.MinValue;

		private string integrationType6AccessToken = null;
		private DateTime integrationType6ExpiresAt = DateTime.MinValue;
		private bool integrationType6RefreshingAccessToken = false;
		private string integrationType7AccessToken = null;
		private DateTime integrationType7ExpiresAt = DateTime.MinValue;
		private bool integrationType7RefreshingAccessToken = false;

		private HttpClient client;

		private ServiceBusClient sbClient = null;
		private ServiceBusSender sbSender = null;
		private ServiceBusSender sbSenderHSM = null;
		private ServiceBusSender sbSenderHSMSingle = null;

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="WhatsAppService"/>
		/// </summary>
		public WhatsAppService()
			: base()
		{
			this.Initialized = false;
			this.client = null;
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve la configuración del servicio
		/// </summary>
		internal WhatsAppServiceConfiguration ServiceConfiguration
		{
			get
			{
				return this.Configuration as WhatsAppServiceConfiguration;
			}
		}

		/// <summary>
		/// Devuelve los parámetros del servicio de Whatsapp
		/// </summary>
		protected DomainModel.ServiceSettings.WhatsappSettings ServiceSettings { get { return this.service.Settings as DomainModel.ServiceSettings.WhatsappSettings; } }

		#endregion

		#region Movistar

		private async Task RefreshIntegrationType6AccessToken()
		{
			if (this.ServiceConfiguration.ServiceIntegrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar)
				return;

			if (this.integrationType6RefreshingAccessToken)
				return;

			this.integrationType6RefreshingAccessToken = true;

			try
			{
				if (this.integrationType6AccessToken == null ||
					DateTime.Now.AddHours(1) > this.integrationType6ExpiresAt)
				{
					Tracer.TraceInfo("Se actualizará el access token del servicio de whatsapp {0} que expira el {1}", this.Name, this.integrationType6ExpiresAt);

					try
					{
						var baseUrl = $"{this.ServiceConfiguration.IntegrationType6UrlBase}";
						if (!baseUrl.EndsWith("/"))
							baseUrl += "/";
						var url = string.Format("{0}v1/oauth2/token", baseUrl);

						if (!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType6Login))
						{
							url = string.Format("{0}{1}", baseUrl, this.ServiceConfiguration.IntegrationType6Login);
						}

						var content = new StringContent($"client_id={Uri.EscapeDataString(this.ServiceConfiguration.IntegrationType6ClientID)}" +
											$"&client_secret={Uri.EscapeDataString(this.ServiceConfiguration.IntegrationType6ClientSecret)}" +
											"&grant_type=client_credentials&scope=scope1", Encoding.UTF8, "application/x-www-form-urlencoded");

						using (var response = await client.PostAsync(url, content))
						{
							if (response.IsSuccessStatusCode)
							{

								string jsonResponse = await response.Content.ReadAsStringAsync();
								var jResponse = JObject.Parse(jsonResponse);

								this.integrationType6AccessToken = jResponse["access_token"].ToString();
								this.integrationType6ExpiresAt = DateTime.Now.AddSeconds(jResponse["expires_in"].ToObject<int>());

								Common.Tracer.TraceError($"Se obtuvo el access token del servicio de whatsapp {this.Name} que expira el {this.integrationType6ExpiresAt}");
							}
							else
							{
								if (response.Content != null)
								{
									using (var stream = await response.Content.ReadAsStreamAsync())
									using (var reader = new StreamReader(stream))
									{
										var error = await reader.ReadToEndAsync();
										Common.Tracer.TraceError("Falló obtener el access token: {0}", error);
									}
								}
								else
								{
									Common.Tracer.TraceError("Falló obtener el access token: {0}", response.StatusCode);
								}

								throw new Exception();
							}
						}
					}
					catch (Exception ex)
					{
						Common.Tracer.TraceError("Falló obtener el access token: {0}", ex);
						throw new Exception();
					}
				}
			}
			finally
			{
				this.integrationType6RefreshingAccessToken = false;
			}
		}

		internal async Task<Social.WhatsApp.WhatsAppMessage> RetrieveMovistarPreviousMessage(string socialConversationID)
		{
			await RefreshIntegrationType6AccessToken();

			if (string.IsNullOrEmpty(this.integrationType6AccessToken))
				throw new ServiceException("No se tiene un access token para poder enviar el mensaje");

			string baseUrl = this.ServiceConfiguration.IntegrationType6UrlBase;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}plataforma-bot/v1/conversations/{1}/activities", baseUrl, socialConversationID);

			if (!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType6ConversationActivities))
			{
				url = string.Format("{0}{1}", baseUrl,
					this.ServiceConfiguration.IntegrationType6ConversationActivities
						.Replace("@@CONVERSATIONID@@", socialConversationID)
						.Replace("@@CHANNELID@@", this.ServiceConfiguration.IntegrationType6ChannelID));
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Get, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.integrationType6AccessToken);
					request.Headers.Add("X-IBM-Client-Id", this.ServiceConfiguration.IntegrationType6ClientID);

				Yoizen.Common.Tracer.TraceInfo("Se realizará un GET a movistar a la URL {0} para obtener la actividad: {1}", url, socialConversationID);

					using (HttpResponseMessage response = await client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							var jsonResponse = await response.Content.ReadAsStringAsync();
							var jResponse = JObject.Parse(jsonResponse);

							if (jResponse["activities"] != null && jResponse["activities"].Type == JTokenType.Array)
							{
								var jActivities = (JArray) jResponse["activities"];
								if (jActivities.Count >= 1)
								{
									try
									{
										jActivities = new JArray(jActivities.OrderBy(a => a["timestamp"]));
										Tracer.TraceVerb("Se ordenó el array de la conversación {0} por timestmap", socialConversationID);
									}
									catch { }

								int i = jActivities.Count - 1;
								while (i >= 0)
								{
									var jBody = jActivities[i];
									if (jBody.Type == JTokenType.Object)
									{
										if (jBody["type"] != null &&
											jBody["type"].Type == JTokenType.String &&
											i > 0)
										{
											var isDerivation = false;
											var type = jBody["type"].ToString();

											if (type.Equals("handoff"))
											{
												isDerivation = true;
											}
											else if (type.Equals("event") &&
												jBody["name"] != null &&
												jBody["name"].Type == JTokenType.String)
											{
												var eventName = jBody["name"].ToString();
												if (eventName.Equals("surveyRequest", StringComparison.InvariantCultureIgnoreCase))
												{
													isDerivation = true;
												}
											}

												if (isDerivation)
												{
													for (int j = i - 1; j >= 0; j--)
													{
														jBody = jActivities[j];
														if (jBody.Type == JTokenType.Object &&
															jBody["type"] != null &&
															jBody["type"].Type == JTokenType.String &&
															jBody["type"].ToString().Equals("message") &&
															jBody["from"] != null &&
															jBody["from"].Type == JTokenType.Object &&
															jBody["from"]["id"] != null &&
															jBody["from"]["id"].Type == JTokenType.String &&
															long.TryParse(jBody["from"]["id"].ToString(), out long id))
														{
															Yoizen.Common.Tracer.TraceInfo("Nos quedamos con el índice {0} de las actividades de la conversación {1} con el JSON {2}", i, socialConversationID, jBody.ToString());

														var jContents = new JObject();
														jContents["account"] = this.ServiceConfiguration.FullPhoneNumber;
														jContents["chat"] = jBody["conversation"]["id"].ToString();
														jContents["from"] = new Newtonsoft.Json.Linq.JObject();
														jContents["from"]["id"] = jBody["from"]["id"].ToString();
														jContents["from"]["name"] = jBody["from"]["name"].ToString();
														jContents["msg"] = new Newtonsoft.Json.Linq.JObject();
														jContents["msg"]["type"] = "chat";
														jContents["msg"]["id"] = jBody["id"].ToString();
														if (jBody["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.String)
														{
															jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(DateTime.Parse(jBody["timestamp"].ToString()));
															jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(DateTime.Parse(jBody["timestamp"].ToString()));
														}
														else if (jBody["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.Date)
														{
															jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(jBody["timestamp"].ToObject<DateTime>());
															jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(jBody["timestamp"].ToObject<DateTime>());
														}

														var withBody = false;

														if (jBody["text"] != null && jBody["text"].Type == Newtonsoft.Json.Linq.JTokenType.String)
														{
															jContents["msg"]["body"] = jBody["text"].ToString();
															withBody = true;
														}

														if (jBody["attachments"] != null && jBody["attachments"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
														{
															var jAttachments = (Newtonsoft.Json.Linq.JArray) jBody["attachments"];
															if (jAttachments.Count == 1 && jAttachments[0].Type == Newtonsoft.Json.Linq.JTokenType.Object)
															{
																var jAttach = (Newtonsoft.Json.Linq.JObject) jAttachments[0];
																var contentType = jAttach["contentType"].ToString();
																if (contentType.StartsWith("image/", StringComparison.InvariantCultureIgnoreCase))
																{
																	jContents["msg"]["type"] = "image";
																	if (withBody)
																	{
																		jContents["msg"]["body"] = null;
																		jContents["msg"]["caption"] = jBody["text"].ToString();
																	}
																}
																else if (contentType.StartsWith("video/", StringComparison.InvariantCultureIgnoreCase))
																{
																	jContents["msg"]["type"] = "video";
																}
																else if (contentType.StartsWith("audio/", StringComparison.InvariantCultureIgnoreCase))
																{
																	jContents["msg"]["type"] = "audio";
																}
																else
																{
																	jContents["msg"]["type"] = "document";
																}

																jContents["msg"]["mimeType"] = contentType;
																jContents["msg"]["url"] = jAttach["contentUrl"].ToString();
															}
														}

														if (jBody["channelData"] != null && jBody["channelData"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
														{
															var jChannelData = (Newtonsoft.Json.Linq.JObject) jBody["channelData"];

															if (jChannelData["user"] != null && jChannelData["user"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
															{
																jChannelData = (Newtonsoft.Json.Linq.JObject) jChannelData["user"];

																var jExt = new Newtonsoft.Json.Linq.JObject();
																jContents["from"]["ext"] = jExt;

																var isClient = false;
																if (jChannelData["isClient"] != null && jChannelData["isClient"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
																{
																	isClient = jChannelData["isClient"].ToObject<bool>();
																	jExt["isClient"] = isClient;
																}

																if (jChannelData["userId"] != null && jChannelData["userId"].Type == Newtonsoft.Json.Linq.JTokenType.String)
																	jExt["userId"] = jChannelData["userId"].ToString();
																if (jChannelData["userIdSystem"] != null && jChannelData["userIdSystem"].Type == Newtonsoft.Json.Linq.JTokenType.String)
																	jExt["userIdSystem"] = jChannelData["userIdSystem"].ToString();
																if (jChannelData["accountType"] != null && jChannelData["accountType"].Type == Newtonsoft.Json.Linq.JTokenType.String)
																	jExt["accountType"] = jChannelData["accountType"].ToString();
																if (jChannelData["contact"] != null && jChannelData["contact"].Type == Newtonsoft.Json.Linq.JTokenType.String)
																	jExt["contact"] = jChannelData["contact"].ToString();
																if (jChannelData["business"] != null && jChannelData["business"].Type == Newtonsoft.Json.Linq.JTokenType.String)
																	jExt["business"] = jChannelData["business"].ToString();

																if (jChannelData["business_profile"] != null && jChannelData["business_profile"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
																{
																	var jBusinessProfile = (Newtonsoft.Json.Linq.JObject) jChannelData["business_profile"];

																	var fields = new string[] { "cuit", "businessName", "contactName", "segment", "technicalAdvisor", "commercialAdvisor", "role", "commercialAttentionCode", "technicalAttentionCode" };
																	foreach (var field in fields)
																	{
																		if (jBusinessProfile[field] != null && jBusinessProfile[field].Type == Newtonsoft.Json.Linq.JTokenType.String)
																		{
																			var value = jBusinessProfile[field].ToString();
																			jExt[field] = value;

																			if (field.Equals("cuit") && !string.IsNullOrEmpty(value) && isClient)
																				jContents["from"]["businessData"] = value;
																		}
																	}
																}
															}
														}

															Tracer.TraceVerb("Se realizará la conversación del mensaje de movistar de la conversación {0} con datos {1}", socialConversationID, jContents.ToString());

															var result = await Converter.Convert((JObject) jContents, this.service, this);
															return (Social.WhatsApp.WhatsAppMessage) result.Item1;
														}
													}
												}
											}
										}

										i--;
									}
								}
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var stream = await response.Content.ReadAsStreamAsync())
								using (var reader = new StreamReader(stream))
								{
									var error = await reader.ReadToEndAsync();
									Common.Tracer.TraceError("Falló al obtener el mensaje de handOff de la conversación {0}: {1}", socialConversationID, error);
								}
							}
							else
							{
								Common.Tracer.TraceError("Falló al obtener el mensaje de handOff de la conversación {0}: {1}", socialConversationID, response.StatusCode);
							}

							return null;
						}
					}
				}

				return null;
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al obtener el mensaje de handOff de la conversación {0}: {1}", socialConversationID, ex);
				return null;
			}
		}

		private JObject ConvertMovistarActivityToToken(JObject jActivity)
		{
			var jContents = new JObject();
			jContents["account"] = this.ServiceConfiguration.FullPhoneNumber;
			jContents["chat"] = jActivity["conversation"]["id"].ToString();
			jContents["from"] = new Newtonsoft.Json.Linq.JObject();
			jContents["from"]["id"] = jActivity["from"]["id"].ToString();
			if (jActivity["from"]["name"] != null)
				jContents["from"]["name"] = jActivity["from"]["name"].ToString();

			if (jActivity["recipient"] != null && jActivity["recipient"].Type == JTokenType.Object)
			{
				jContents["recipient"] = new Newtonsoft.Json.Linq.JObject();
				jContents["recipient"]["id"] = jActivity["recipient"]["id"].ToString();
				if (jActivity["recipient"]["name"] != null)
					jContents["recipient"]["name"] = jActivity["recipient"]["name"].ToString();
			}

			jContents["msg"] = new Newtonsoft.Json.Linq.JObject();
			jContents["msg"]["type"] = "chat";
			jContents["msg"]["socialServiceType"] = (short) SocialServiceTypes.WhatsApp;
			jContents["msg"]["id"] = jActivity["id"].ToString();
			if (jActivity["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.String)
			{
				jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(DateTime.Parse(jActivity["timestamp"].ToString()));
				jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(DateTime.Parse(jActivity["timestamp"].ToString()));
			}
			else if (jActivity["timestamp"].Type == Newtonsoft.Json.Linq.JTokenType.Date)
			{
				jContents["msg"]["timestamp"] = Yoizen.Common.Conversions.DateTimeToUnixTime(jActivity["timestamp"].ToObject<DateTime>());
				jContents["msg"]["longtimestamp"] = Yoizen.Common.Conversions.DateTimeToUnixMilliseconds(jActivity["timestamp"].ToObject<DateTime>());
			}

			if (long.TryParse(jContents["from"]["id"].ToString(), out long fromId))
				jContents["msg"]["incoming"] = true;
			else
				jContents["msg"]["incoming"] = false;

			var withBody = false;

			if (jActivity["text"] != null && jActivity["text"].Type == Newtonsoft.Json.Linq.JTokenType.String)
			{
				jContents["msg"]["body"] = jActivity["text"].ToString();
				withBody = true;
			}

			if (jActivity["attachments"] != null && jActivity["attachments"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
			{
				var jAttachments = (Newtonsoft.Json.Linq.JArray) jActivity["attachments"];
				if (jAttachments.Count == 1 && jAttachments[0].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					var jAttach = (Newtonsoft.Json.Linq.JObject) jAttachments[0];
					var contentType = jAttach["contentType"].ToString();
					if (contentType.StartsWith("image/", StringComparison.InvariantCultureIgnoreCase))
					{
						jContents["msg"]["type"] = "image";
						if (withBody)
						{
							jContents["msg"]["body"] = null;
							jContents["msg"]["caption"] = jActivity["text"].ToString();
						}
					}
					else if (contentType.StartsWith("video/", StringComparison.InvariantCultureIgnoreCase))
					{
						jContents["msg"]["type"] = "video";
					}
					else if (contentType.StartsWith("audio/", StringComparison.InvariantCultureIgnoreCase))
					{
						jContents["msg"]["type"] = "audio";
					}
					else
					{
						jContents["msg"]["type"] = "document";
					}

					jContents["msg"]["mimeType"] = contentType;
					jContents["msg"]["url"] = jAttach["contentUrl"].ToString();
				}
			}

			if (jActivity["channelData"] != null && jActivity["channelData"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
			{
				var jChannelData = (Newtonsoft.Json.Linq.JObject) jActivity["channelData"];

				if (jChannelData["user"] != null && jChannelData["user"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					jChannelData = (Newtonsoft.Json.Linq.JObject) jChannelData["user"];

					var jExt = new Newtonsoft.Json.Linq.JObject();
					jContents["from"]["ext"] = jExt;

					var isClient = false;
					if (jChannelData["isClient"] != null && jChannelData["isClient"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
					{
						isClient = jChannelData["isClient"].ToObject<bool>();
						jExt["isClient"] = isClient;
					}

					if (jChannelData["userId"] != null && jChannelData["userId"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						jExt["userId"] = jChannelData["userId"].ToString();
					if (jChannelData["userIdSystem"] != null && jChannelData["userIdSystem"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						jExt["userIdSystem"] = jChannelData["userIdSystem"].ToString();
					if (jChannelData["accountType"] != null && jChannelData["accountType"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						jExt["accountType"] = jChannelData["accountType"].ToString();
					if (jChannelData["contact"] != null && jChannelData["contact"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						jExt["contact"] = jChannelData["contact"].ToString();
					if (jChannelData["business"] != null && jChannelData["business"].Type == Newtonsoft.Json.Linq.JTokenType.String)
						jExt["business"] = jChannelData["business"].ToString();

					if (jChannelData["business_profile"] != null && jChannelData["business_profile"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jBusinessProfile = (Newtonsoft.Json.Linq.JObject) jChannelData["business_profile"];

						var fields = new string[] { "cuit", "businessName", "contactName", "segment", "technicalAdvisor", "commercialAdvisor", "role", "commercialAttentionCode", "technicalAttentionCode" };
						foreach (var field in fields)
						{
							if (jBusinessProfile[field] != null && jBusinessProfile[field].Type == Newtonsoft.Json.Linq.JTokenType.String)
							{
								var value = jBusinessProfile[field].ToString();
								jExt[field] = value;

								if (field.Equals("cuit") && !string.IsNullOrEmpty(value) && isClient)
									jContents["from"]["businessData"] = value;
							}
						}
					}
				}
			}

			return jContents;
		}

		/// <summary>
		/// Devuelve la enumeración de mensajes previos a un mensaje específico
		/// </summary>
		/// <param name="socialConversationID">La conversación a la cual pertenece el mensaje</param>
		/// <param name="date">La fecha del mensaje, para obtener todos los mensajes previos</param>
		/// <param name="socialMessageID">Código de mensaje específico para obtener los previos</param>
		/// <returns></returns>
		public async Task<IEnumerable<DomainModel.Message>> RetrieveMovistarPreviousMessages(string socialConversationID, DateTime date, string socialMessageID)
		{
			await RefreshIntegrationType6AccessToken();

			if (string.IsNullOrEmpty(this.integrationType6AccessToken))
				throw new ServiceException("No se tiene un access token para poder enviar el mensaje");

			string baseUrl = this.ServiceConfiguration.IntegrationType6UrlBase;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}plataforma-bot/v1/conversations/{1}/activities", baseUrl, socialConversationID);

			if (!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType6ConversationActivities))
			{
				url = string.Format("{0}{1}", baseUrl,
					this.ServiceConfiguration.IntegrationType6ConversationActivities
						.Replace("@@CONVERSATIONID@@", socialConversationID)
						.Replace("@@CHANNELID@@", this.ServiceConfiguration.IntegrationType6ChannelID));
			}

			Yoizen.Common.Tracer.TraceInfo("Se realizará un GET a movistar a la URL {0} para obtener las actividades: {1}", url, socialConversationID);

			var messages = new List<DomainModel.Message>();

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Get, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.integrationType6AccessToken);
					request.Headers.Add("X-IBM-Client-Id", this.ServiceConfiguration.IntegrationType6ClientID);

					using (HttpResponseMessage response = await client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							var jsonResponse = await response.Content.ReadAsStringAsync();
							var jResponse = JObject.Parse(jsonResponse);

						if (jResponse["activities"] != null && jResponse["activities"].Type == JTokenType.Array)
						{
							var jActivities = (JArray) jResponse["activities"];
							if (jActivities.Count > 0)
							{
								try
								{
									jActivities = new JArray(jActivities.OrderBy(a => a["timestamp"]));
								}
								catch { }

								foreach (JObject jActivity in jActivities)
								{
									DateTime timestamp = DateTime.MinValue;
									try
									{
										if (jActivity["localTimestamp"].Type == JTokenType.Date)
											timestamp = jActivity["localTimestamp"].ToObject<DateTime>();
										else if (jActivity["localTimestamp"].Type == JTokenType.String)
											timestamp = DateTime.Parse(jActivity["localTimestamp"].ToString());
									}
									catch { }

									var messageId = jActivity["id"].ToString();

									if (timestamp < date && !messageId.Equals(socialMessageID))
									{
										if (jActivity["type"] != null &&
											jActivity["type"].Type == JTokenType.String &&
											!jActivity["type"].ToString().Equals("handoff"))
										{
											var jContents = this.ConvertMovistarActivityToToken(jActivity);

#if DEBUG
											Tracer.TraceVerb("Se convertirá la actividad: {0}", jContents.ToString(Newtonsoft.Json.Formatting.Indented));
#endif

											var result = await Converter.Convert(jContents, this.service, this);
											if (result.Item1 != null)
											{
												result.Item1.Service = this.service;
												messages.Add(result.Item1);
											}
										}
									}
								}
							}
						}

							return messages;
						}
						else
						{
							if (response.Content != null)
							{
								using (var stream = await response.Content.ReadAsStreamAsync())
								using (var reader = new StreamReader(stream))
								{
									var error = await reader.ReadToEndAsync();
									Common.Tracer.TraceError("Falló al obtener el mensaje de handOff: {0}", error);
								}
							}
							else
							{
								Common.Tracer.TraceError("Falló al obtener el mensaje de handOff: {0}", response.StatusCode);
							}
							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al obtener el mensaje de handOff: {0}", ex);
				throw new Exception();
			}
		}

		#endregion

		#region Private Methods

		private async Task RefreshIntegrationType7AccessToken()
		{
			if (this.ServiceConfiguration.ServiceIntegrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa)
				return;

			if (this.integrationType7RefreshingAccessToken)
				return;

			this.integrationType7RefreshingAccessToken = true;

			try
			{
				if (this.integrationType7AccessToken == null ||
					DateTime.Now.AddHours(1) > this.integrationType7ExpiresAt)
				{
					Tracer.TraceInfo("Se actualizará el access token del servicio de whatsapp {0} que expira el {1}", this.Name, this.integrationType7ExpiresAt);

					var url = $"{this.ServiceConfiguration.IntegrationType7BaseUrl}";
					if (!url.EndsWith("/"))
						url += "/";
					url += "api/v2/users/sign_in";

					var requestData = new { username = this.ServiceConfiguration.IntegrationType7User, password = this.ServiceConfiguration.IntegrationType7Password };
					var json = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);

					var content = new StringContent(json, Encoding.UTF8, "application/json");

					try
					{
						using (var response = await client.PostAsync(url, content))
						{
							if (response.IsSuccessStatusCode)
							{
								var jsonResponse = await response.Content.ReadAsStringAsync();
								var jResponse = JObject.Parse(jsonResponse);

								this.integrationType7AccessToken = jResponse["jwt"].ToString();
								this.integrationType7ExpiresAt = DateTime.Now.AddDays(7);

								Console.WriteLine($"Se obtuvo el access token del servicio de whatsapp {this.Name} con valor {this.integrationType7AccessToken} que expira el {this.integrationType7ExpiresAt}");
							}
							else
							{
								if (response.Content != null)
								{
									using (var stream = await response.Content.ReadAsStreamAsync())
									using (var reader = new StreamReader(stream))
									{
										var error = await reader.ReadToEndAsync();
										Common.Tracer.TraceError("Falló obtener el access token: {0}", error);
									}
								}
								else
								{
									Common.Tracer.TraceError("Falló obtener el access token: {0}", response.StatusCode);
								}

								throw new Exception();
							}
						}
					}
					catch (Exception ex)
					{
						Yoizen.Common.Tracer.TraceError("Falló obtener el access token: {0}", ex);
						throw new Exception();
					}
				}
			}
			finally
			{
				this.integrationType7RefreshingAccessToken = false;
			}
		}

		/// <summary>
		/// Crea un <see cref="JObject"/> con datos del cuerpo d lemensaje para realizar una acción sobre una llamaba de voz para enviar a Whatsapp (formato nativo)
		/// </summary>
		/// <param name="action">La acción que se creará en el cuerpo</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Información de la sesión de WebRtc</param>
		/// <param name="customPayload">Datos adicionales a enviar en el cuerpo</param>
		/// <returns>Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la estructura del mensaje a enviar</returns>
		private JObject CreateWhatsappCall(string action, string callId, string sdp = null, string customPayload = null)
		{
			var jBody = new Newtonsoft.Json.Linq.JObject();
			jBody["call_id"] = callId;
			jBody["action"] = action;
			jBody["messaging_product"] = "whatsapp";
			if (!string.IsNullOrEmpty(customPayload))
				jBody["custom_payload"] = customPayload;

			if (sdp != null)
			{
				var jSession = new JObject();
				jBody["session"] = jSession;
				jSession["sdp"] = sdp;
				switch (action)
				{
					case "pre_accept":
					case "accept":
						jSession["sdp_type"] = "answer";
						break;
					default:
						break;
				} 
			}

			return jBody;
		}

		/// <summary>
		/// Crea un mensaje para enviar a Whatsapp (formato nativo)
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con información pre populada del mensaje</param>
		/// <param name="message">El <see cref="DomainModel.Message"/> del cual se generará el mensaje a enviar</param>
		/// <param name="hasAttach">Cuando retorna, devuelve <code>true</code> si el mensaje a enviar tiene multimedia; en caso contrario, <code>false</code></param>
		/// <returns>Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la estructura del mensaje a enviar</returns>
		private async Task<(JObject,bool)> CreateWhatsappMessage(JObject data, DomainModel.Message message)
		{
			var hasMultimedia = false;

			var jDataMessage = data["msg"];

			var jBody = new Newtonsoft.Json.Linq.JObject();
			var jMessage = jBody;
			jMessage["to"] = data["number"].ToString();

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					jMessage["type"] = "template";
					jMessage["recipient_type"] = "individual";
					var jTemplate = new Newtonsoft.Json.Linq.JObject();
					jMessage["template"] = jTemplate;

					jTemplate["name"] = sendDefinition.ElementName;

					var jLanguage = new Newtonsoft.Json.Linq.JObject();
					jTemplate["language"] = jLanguage;
					jLanguage["policy"] = "deterministic";
					jLanguage["code"] = sendDefinition.Language;

					var jComponents = new Newtonsoft.Json.Linq.JArray();
					var addComponents = false;

					var jHeader = new Newtonsoft.Json.Linq.JObject();
					jHeader["type"] = "header";
					var jHeaderParameters = new Newtonsoft.Json.Linq.JArray();
					jHeader["parameters"] = jHeaderParameters;
					var jHeaderParameter = new Newtonsoft.Json.Linq.JObject();
					jHeaderParameters.Add(jHeaderParameter);

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jHeaderParameter["type"] = "text";
						jHeaderParameter["text"] = sendDefinition.HeaderTextParameter.Value;
						jComponents.Add(jHeader);
						addComponents = true;
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						hasMultimedia = true;

						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jHeaderParameter["type"] = "document";
								var jDocument = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["document"] = jDocument;
								jDocument["filename"] = sendDefinition.HeaderMediaFileName;
								jDocument["link"] = fileUrl;
								jComponents.Add(jHeader);
								addComponents = true;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jHeaderParameter["type"] = "image";
								var jImage = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["image"] = jImage;
								jImage["filename"] = sendDefinition.HeaderMediaFileName;
								jImage["link"] = fileUrl;
								jComponents.Add(jHeader);
								addComponents = true;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jHeaderParameter["type"] = "video";
								var jVideo = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["video"] = jVideo;
								jVideo["filename"] = sendDefinition.HeaderMediaFileName;
								jVideo["link"] = fileUrl;
								jComponents.Add(jHeader);
								addComponents = true;
								break;
							default:
								break;
						}
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						jHeaderParameter["type"] = "location";
						var jLocation = new Newtonsoft.Json.Linq.JObject();
						jHeaderParameter["location"] = jLocation;
						jLocation["latitude"] = sendDefinition.HeaderLocationLatitude;
						jLocation["longitude"] = sendDefinition.HeaderLocationLongitude;
						jLocation["name"] = sendDefinition.HeaderLocationName;
						jLocation["address"] = sendDefinition.HeaderLocationAddress;
						jComponents.Add(jHeader);
						addComponents = true;
					}

					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					{
						var jBodyComponent = new Newtonsoft.Json.Linq.JObject();
						jComponents.Add(jBodyComponent);
						addComponents = true;
						jBodyComponent["type"] = "body";
						var jBodyParameters = new Newtonsoft.Json.Linq.JArray();
						jBodyComponent["parameters"] = jBodyParameters;
						foreach (var parameter in sendDefinition.Parameters)
						{
							var jBodyParameter = new Newtonsoft.Json.Linq.JObject();
							jBodyParameters.Add(jBodyParameter);
							jBodyParameter["type"] = "text";
							jBodyParameter["text"] = parameter.Value;
						}
					}

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						for (var i = 0; i < sendDefinition.Buttons.Length; i++)
						{
							var button = sendDefinition.Buttons[i];
							var jButtonParameters = new Newtonsoft.Json.Linq.JArray();
							var jButton = new Newtonsoft.Json.Linq.JObject();
							var jButtonParameter = new Newtonsoft.Json.Linq.JObject();

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed ||
								 sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply) &&
								 button.QuickReplyParameter != null)
							{
								jButton["type"] = "button";
								jButton["sub_type"] = "quick_reply";
								jButton["index"] = i.ToString();
								jButton["parameters"] = jButtonParameters;

								jButtonParameters.Add(jButtonParameter);
								jButtonParameter["type"] = "payload";

								var buttonPayload = GenerateButtonPayload(button.QuickReplyParameter.Value, message);
								jButtonParameter["payload"] = JsonConvert.SerializeObject(buttonPayload);
								addComponents = true;
								jComponents.Add(jButton);
							}

							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed ||
									  sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction) &&
									  button.CallToActionButtonType != null)
							{
								switch (button.CallToActionButtonType)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											jButton["type"] = "button";
											jButton["sub_type"] = "url";
											jButton["index"] = i.ToString();
											jButton["parameters"] = jButtonParameters;

											jButtonParameters.Add(jButtonParameter);
											jButtonParameter["type"] = "text";
											jButtonParameter["text"] = button.UrlParameter.Value;
											addComponents = true;
											jComponents.Add(jButton);
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										jButton["type"] = "button";
										jButton["sub_type"] = "copy_code";
										jButton["index"] = i.ToString();
										jButton["parameters"] = jButtonParameters;

										jButtonParameters.Add(jButtonParameter);
										jButtonParameter["type"] = "coupon_code";
										jButtonParameter["coupon_code"] = button.OfferCodeParameter.Value;
										addComponents = true;
										jComponents.Add(jButton);
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow:
										jButton["type"] = "button";
										jButton["sub_type"] = "flow";
										jButton["index"] = i.ToString();
										jButton["parameters"] = jButtonParameters;
										
										var jButtonAction = new JObject();
										
										if (button.FlowParameter.ActionData != null)
										{
											if (!string.IsNullOrEmpty(button.FlowParameter.ActionData.FlowToken))
											{
												jButtonAction["flow_token"] = button.FlowParameter.ActionData.FlowToken;
											}
											
											if (button.FlowParameter.ActionData.Data != null)
											{
												jButtonAction["flow_action_data"] = button.FlowParameter.ActionData.Data.ToString();
											}
										}

										jButtonParameters.Add(jButtonParameter);
										jButtonParameter["type"] = "action";
										jButtonParameter["action"] = jButtonAction;
										addComponents = true;
										jComponents.Add(jButton);
										break;
									default:
										break;
								}
							}
							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode) &&
									  button.AuthCodeButtonType != null)
							{
								switch (button.AuthCodeButtonType)
								{
									
									case DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode:
										jButton["type"] = "button";
										jButton["sub_type"] = "url";
										jButton["index"] = i.ToString();
										jButton["parameters"] = jButtonParameters;

										jButtonParameters.Add(jButtonParameter);
										jButtonParameter["type"] = "text";
										jButtonParameter["text"] = button.AuthCodeParameter.Value;
										addComponents = true;
										jComponents.Add(jButton);
										break;
									default:
										break;
								}
							}
						}
					}

					if (addComponents)
					{
						jTemplate["components"] = jComponents;
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen de HSM: {0}", jMessage.ToString());
				}
				else if (data["parameters"] != null &&
					data["parameters"].Type == JTokenType.Object)
				{
					jMessage["type"] = "template";
					jMessage["recipient_type"] = "individual";
					var jTemplate = new Newtonsoft.Json.Linq.JObject();
					jMessage["template"] = jTemplate;

					var jParameters = (JObject) data["parameters"];
					jTemplate["name"] = jParameters["templateName"].ToString();

					var jLanguage = new Newtonsoft.Json.Linq.JObject();
					jTemplate["language"] = jLanguage;
					jLanguage["policy"] = "deterministic";
					jLanguage["code"] = jParameters["language"].ToString();

					if (jParameters["templateData"] != null &&
						jParameters["templateData"].Type == JTokenType.Array)
					{
						var jComponents = new Newtonsoft.Json.Linq.JArray();
						jTemplate["components"] = jComponents;

						var jComponent = new Newtonsoft.Json.Linq.JObject();
						jComponents.Add(jComponent);

						jComponent["type"] = "body";
						var jComponentsParameters = new Newtonsoft.Json.Linq.JArray();
						jComponent["parameters"] = jComponentsParameters;

						var jHSMParameters = (Newtonsoft.Json.Linq.JArray) jParameters["templateData"];
						foreach (var jHSMParameter in jHSMParameters)
						{
							Newtonsoft.Json.Linq.JObject jParameter = null;
							if (jHSMParameter.Type == Newtonsoft.Json.Linq.JTokenType.String)
							{
								jParameter = new Newtonsoft.Json.Linq.JObject();
								jParameter["type"] = "text";
								jParameter["text"] = jHSMParameter.ToString();
							}
							else if (jHSMParameter.Type == Newtonsoft.Json.Linq.JTokenType.Object)
							{
								if (jHSMParameter["default"] != null && jHSMParameter["default"].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									jParameter = new Newtonsoft.Json.Linq.JObject();
									jParameter["type"] = "text";
									jParameter["text"] = jHSMParameter["default"].ToString();
								}
							}

							if (jParameter != null)
							{
								jComponentsParameters.Add(jParameter);
							}
						}
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen de HSM: {0}", jMessage.ToString());
				}
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var jDataAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jDataAttach["type"] != null && jDataAttach["type"].Type == JTokenType.String)
				{
					string type = jDataAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						var jLocation = new Newtonsoft.Json.Linq.JObject();
						jMessage["location"] = jLocation;
						jMessage["type"] = "location";
						jMessage["recipient_type"] = "individual";
						jLocation["longitude"] = jDataAttach["longitude"].ToObject<double>();
						jLocation["latitude"] = jDataAttach["latitude"].ToObject<double>();
						if (jDataAttach["name"] != null && jDataAttach["name"].Type == JTokenType.String)
							jLocation["name"] = jDataAttach["name"].ToString();
						if (jDataAttach["address"] != null && jDataAttach["address"].Type == JTokenType.String)
							jLocation["address"] = jDataAttach["address"].ToString();

						Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen con una ubicación: {0}", jMessage.ToString());
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jDataAttach["isPublicUrl"] != null &&
						jDataAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jDataAttach["isPublicUrl"].ToObject<bool>() &&
						jDataAttach["url"] != null &&
						jDataAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jDataAttach["url"].ToString();
					}
					else
					{
						if (jDataAttach["mimeType"].ToString().Equals("audio/ogg"))
						{
							jDataAttach["mimeType"] = "audio/ogg; codecs=opus";
						}
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					var type = DomainModel.Attachment.AttachmentType.File;
					var mimeType = jDataAttach["mimeType"].ToString();
					try
					{
						type = (DomainModel.Attachment.AttachmentType) jDataAttach["type"].ToObject<short>();

						if (type == Attachment.AttachmentType.Image &&
							mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
						{
							type = Attachment.AttachmentType.Sticker;
						}
					}
					catch
					{
						if (mimeType.StartsWith("image"))
						{
							type = DomainModel.Attachment.AttachmentType.Image;

							if (mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
							{
								type = Attachment.AttachmentType.Sticker;
							}
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = DomainModel.Attachment.AttachmentType.Audio;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = DomainModel.Attachment.AttachmentType.Video;
						}
					}

					jMessage["recipient_type"] = "individual";

					switch (type)
					{
						case DomainModel.Attachment.AttachmentType.Image:
							jMessage["type"] = "image";
							jMessage["image"] = new Newtonsoft.Json.Linq.JObject();
							jMessage["image"]["link"] = fileUrl;
							if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
							{
								var body = jDataMessage["body"].ToString();
								if (string.IsNullOrEmpty(body) &&
									message.Parameters.ContainsKey(DomainModel.Message.ReplyParameter) &&
									!string.IsNullOrEmpty(message.Body))
								{
									body = message.Body;
								}

								if (!string.IsNullOrEmpty(body))
									jMessage["image"]["caption"] = body;
							}
							break;
						case DomainModel.Attachment.AttachmentType.Sticker:
							jMessage["type"] = "sticker";
							jMessage["sticker"] = new Newtonsoft.Json.Linq.JObject();
							jMessage["sticker"]["link"] = fileUrl;
							break;
						case DomainModel.Attachment.AttachmentType.Audio:
							jMessage["type"] = "audio";
							jMessage["audio"] = new Newtonsoft.Json.Linq.JObject();
							jMessage["audio"]["link"] = fileUrl;
							break;
						case DomainModel.Attachment.AttachmentType.Video:
							jMessage["type"] = "video";
							jMessage["video"] = new Newtonsoft.Json.Linq.JObject();
							jMessage["video"]["link"] = fileUrl;
							break;
						default:
							jMessage["type"] = "document";
							jMessage["document"] = new Newtonsoft.Json.Linq.JObject();
							jMessage["document"]["link"] = fileUrl;
							if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
								jMessage["document"]["caption"] = jDataMessage["body"].ToString();
							jMessage["document"]["filename"] = jDataAttach["name"];
							break;
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen con un archivo adjunto y datos: {0}", jBody.ToString());

					hasMultimedia = true;
				}
			}
			else if (jDataMessage["type"] != null &&
				jDataMessage["type"].Type == JTokenType.String &&
				jDataMessage["type"].ToString().Equals("interactive"))
			{
				var jInteractive = (JObject) jDataMessage["interactive"];
				jMessage["type"] = "interactive";
				jMessage["recipient_type"] = "individual";
				jMessage["interactive"] = jInteractive;

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen con un mensaje interactivo y datos: {0}", jBody.ToString());
			}
			else
			{
				jMessage["type"] = "text";
				jMessage["recipient_type"] = "individual";
				jMessage["text"] = new Newtonsoft.Json.Linq.JObject();

				var body = jDataMessage["body"].ToString();
				jMessage["text"]["body"] = body;

				if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi ||
					this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen)
				{
					if (this.ServiceConfiguration.PreviewUrlForTextMessages)
					{
						if (body.IndexOf("https://") >= 0)
						{
							jMessage["text"]["preview_url"] = true;
							hasMultimedia = true;
						}
					}
				}

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a yoizen con datos: {0}", jBody.ToString());
			}

			return (jBody, hasMultimedia);
		}

		/// <summary>
		/// Realiza la invocación a BotMaker para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToBotMaker(JObject data, DomainModel.Message message)
		{
			string replyId = null;

			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jMessage["chatPlatform"] = "whatsapp";
			jMessage["chatChannelNumber"] = data["account"].ToString();
			jMessage["platformContactId"] = data["number"].ToString();
			jMessage["clientPayload"] = message.ID.ToString();
			if (data["msg"] != null && data["msg"].Type == JTokenType.Object &&
				data["msg"]["id"] != null && data["msg"]["id"].Type == JTokenType.String)
				jMessage["clientPayload"] = data["msg"]["id"].ToString();

			string url = "https://go.botmaker.com/api/v1.0/message/v3";

			var jDataMessage = data["msg"];

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);
					jMessage["ruleNameOrId"] = sendDefinition.ElementName;

					var jParams = new Newtonsoft.Json.Linq.JObject();
					var withParams = false;

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jParams[sendDefinition.HeaderTextParameter.Name] = sendDefinition.HeaderTextParameter.Value;
						withParams = true;
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jParams["headerDocumentUrl"] = fileUrl;
								jParams["headerDocumentCaption"] = sendDefinition.HeaderMediaFileName;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jParams["headerImageUrl"] = fileUrl;
								jParams["headerImageCaption"] = sendDefinition.HeaderMediaFileName;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jParams["headerVideoUrl"] = fileUrl;
								jParams["headerVideoCaption"] = sendDefinition.HeaderMediaFileName;
								break;
							default:
								break;
						}

						withParams = true;
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						throw new NotImplementedException("BotMaker no soporta ubicación como encabezado de HSM");
					}

					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					{
						foreach (var parameter in sendDefinition.Parameters)
						{
							jParams[parameter.Name] = parameter.Value;
						}

						withParams = true;
					}

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						foreach (var button in sendDefinition.Buttons)
						{

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
								 sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
								 button.QuickReplyParameter != null)
							{
								jParams[button.QuickReplyParameter.Name] = button.QuickReplyParameter.Value;
								withParams = true;
							}
							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
									  sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
									  button.CallToActionButtonType != null)
							{
								switch (button.CallToActionButtonType.Value)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											jParams[button.UrlParameter.Name] = button.UrlParameter.Value;
											withParams = true;
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										//No esta implementado por el broker
										throw new NotImplementedException();
									default:
										break;
								}
							}
							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode) &&
									  button.AuthCodeButtonType != null)
							{
								switch (button.AuthCodeButtonType.Value)
								{
									case DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode:
										if (button.AuthCodeButtonType.Value == DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode)
										{
											//jParams[button.AuthCodeParameter.Name] = button.AuthCodeParameter.Value;
											//withParams = true;
											//analizar si broker implementar
											throw new NotImplementedException();
										}
										break;
									default:
										break;
								}
							}
						}
					}

					if (withParams)
					{
						jMessage["params"] = jParams;
					}
				}
				else
				{
					jMessage["ruleNameOrId"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
					{
						var @namespace = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
						var elementName = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
						var language = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

						var template = this.ServiceSettings.FindTemplate(@namespace, elementName, language);
						if (template != null)
						{
							var templateParameters = template.TemplateParameters;
							var jHSMTemplateData = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);

							if (jHSMTemplateData.Count > 0)
							{
								if (jHSMTemplateData.Count == templateParameters.Length)
								{
									var jTemplateData = new Newtonsoft.Json.Linq.JObject();
									jMessage["params"] = jTemplateData;
									for (var i = 0; i < templateParameters.Length; i++)
									{
										jTemplateData[templateParameters[i].Name] = jHSMTemplateData[i].ToString();
									}
								}
								else
								{
									throw new Exception("Los parámetros proporcionados al HSM no coinciden con su definición");
								}
							}
						}
						else
						{
							throw new Exception("No se puede enviar el mensaje porque no se encontró el template");
						}
					}
				}

				url = "https://go.botmaker.com/api/v1.0/intent/v2";
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var page = data["account"].ToString();

				var jAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jAttach["type"] != null && jAttach["type"].Type == JTokenType.String)
				{
					string type = jAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						jMessage["locationLongitude"] = jAttach["longitude"].ToObject<double>();
						jMessage["locationLatitude"] = jAttach["latitude"].ToObject<double>();
						if (jAttach["name"] != null && jAttach["name"].Type == JTokenType.String)
							jMessage["locationName"] = jAttach["name"].ToString();
						if (jAttach["address"] != null && jAttach["address"].Type == JTokenType.String)
							jMessage["locationAddress"] = jAttach["address"].ToString();

						Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a botmaker con una ubicación: {0}", jMessage.ToString());
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jAttach["isPublicUrl"] != null &&
						jAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jAttach["isPublicUrl"].ToObject<bool>() &&
						jAttach["url"] != null &&
						jAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jAttach["url"].ToString();
					}
					else
					{
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					short type = 3;
					try
					{
						type = jAttach["type"].ToObject<short>();
					}
					catch
					{
						var mimeType = jAttach["mimeType"].ToString();
						if (mimeType.StartsWith("image"))
						{
							type = 2;
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = 6;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = 7;
						}
					}

					switch (type)
					{
						case 2 /*Image*/:
							jMessage["imageURL"] = fileUrl;
							if (jDataMessage["body"] != null &&
								jDataMessage["body"].Type == JTokenType.String)
							{
								jMessage["messageText"] = jDataMessage["body"].ToString();
							}
							break;
						case 6 /*Audio*/:
							jMessage["audioURL"] = fileUrl;
							break;
						case 7 /*Video*/:
							jMessage["videoURL"] = fileUrl;
							break;
						case 3 /*Text*/:
						case 4 /*PdfDocument*/:
						case 5 /*WordDocument*/:
						default:
							jMessage["fileURL"] = fileUrl;
							break;
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a botmaker con un archivo adjunto: {0}", jMessage.ToString());
				}
			}
			else
			{
				jMessage["messageText"] = jDataMessage["body"].ToString();
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Add("access-token", data["integrationType2AccessToken"].ToString());
					request.Content = new StringContent(jMessage.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								replyId = jResponse["id"].ToString();

								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0}", replyId);
								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw new Exception();
			}
		}

		/// <summary>
		/// Realiza la invocación a Infobip para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToInfobip(JObject data, DomainModel.Message message)
		{
			string replyId = null;

			string baseUrl = data["integrationType4BaseUrl"].ToString();
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}omni/1/advanced", baseUrl);

			var jDataMessage = data["msg"];

			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jMessage["to"] = data["number"].ToString();
			jMessage["from"] = this.ServiceConfiguration.FullPhoneNumber;
			jMessage["callbackData"] = message.ID.ToString();
			jMessage["messageId"] = message.ID.ToString();
			var jWhatsapp = new Newtonsoft.Json.Linq.JObject();
			jMessage["content"] = jWhatsapp;

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				url = string.Format("{0}whatsapp/1/message/template", baseUrl);

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					var jHSM = jMessage;

					jMessage = new Newtonsoft.Json.Linq.JObject();
					var jMessages = new Newtonsoft.Json.Linq.JArray();
					jMessages.Add(jHSM);
					jMessage["messages"] = jMessages;

					jWhatsapp["templateName"] = sendDefinition.ElementName;
					jWhatsapp["language"] = sendDefinition.Language;

					var jMediaTemplateData = new Newtonsoft.Json.Linq.JObject();
					jWhatsapp["templateData"] = jMediaTemplateData;

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
						jMediaTemplateData["header"]["type"] = "TEXT";
						jMediaTemplateData["header"]["placeholder"] = sendDefinition.HeaderTextParameter.Value;
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();

						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jMediaTemplateData["header"]["type"] = "DOCUMENT";
								jMediaTemplateData["header"]["mediaUrl"] = fileUrl;
								jMediaTemplateData["header"]["filename"] = sendDefinition.HeaderMediaFileName;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jMediaTemplateData["header"]["type"] = "IMAGE";
								jMediaTemplateData["header"]["mediaUrl"] = fileUrl;
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jMediaTemplateData["header"]["type"] = "VIDEO";
								jMediaTemplateData["header"]["mediaUrl"] = fileUrl;
								break;
							default:
								break;
						}
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
						jMediaTemplateData["header"]["type"] = "LOCATION";
						jMediaTemplateData["header"]["latitude"] = sendDefinition.HeaderLocationLatitude;
						jMediaTemplateData["header"]["longitude"] = sendDefinition.HeaderLocationLongitude;
					}

					jMediaTemplateData["body"] = new Newtonsoft.Json.Linq.JObject();
					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
						jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray(sendDefinition.Parameters.Select(p => p.Value));
					else
						jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray();

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						var jButtons = new Newtonsoft.Json.Linq.JArray();
						jMediaTemplateData["buttons"] = jButtons;
						for (var i = 0; i < sendDefinition.Buttons.Length; i++)
						{
							var button = sendDefinition.Buttons[i];

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
								 sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
								 button.QuickReplyParameter != null)
							{
								var jButton = new Newtonsoft.Json.Linq.JObject();
								jButton["type"] = "QUICK_REPLY";
								//jButton["parameter"] = button.QuickReplyParameter.Value;

								var buttonPayload = GenerateButtonPayload(button.QuickReplyParameter.Value, message);
								jButton["parameter"] = JsonConvert.SerializeObject(buttonPayload);

								jButtons.Add(jButton);
							}

							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
									  sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
									  button.CallToActionButtonType != null)
							{
								var jButton = new Newtonsoft.Json.Linq.JObject();
								switch (button.CallToActionButtonType)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											
											jButton["type"] = "URL";
											jButton["parameter"] = button.UrlParameter.Value;
											jButtons.Add(jButton);
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										jButton["type"] = "COPY_CODE";
										jButton["parameter"] = button.OfferCodeParameter.Value;
										jButtons.Add(jButton);
										break;
									default:
										break;
								}
							}
						}
					}
				}
				else
				{
					var jHSM = jMessage;

					jMessage = new Newtonsoft.Json.Linq.JObject();
					var jMessages = new Newtonsoft.Json.Linq.JArray();
					jMessages.Add(jHSM);
					jMessage["messages"] = jMessages;

					jWhatsapp["templateName"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jWhatsapp["language"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

					var jMediaTemplateData = new Newtonsoft.Json.Linq.JObject();
					jWhatsapp["templateData"] = jMediaTemplateData;

					jMediaTemplateData["body"] = new Newtonsoft.Json.Linq.JObject();
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
						jMediaTemplateData["body"]["placeholders"] = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
					else
						jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray();
				}

				Common.Tracer.TraceInfo("Se realizará un POST a infobip a la URL {0} con un HSM: {1}", url, jMessage.ToString());
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var page = data["account"].ToString();

				var jAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jAttach["type"] != null && jAttach["type"].Type == JTokenType.String)
				{
					string type = jAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						url = string.Format("{0}whatsapp/1/message/location", baseUrl);

						jWhatsapp["longitude"] = jAttach["longitude"].ToObject<double>();
						jWhatsapp["latitude"] = jAttach["latitude"].ToObject<double>();
						if (jAttach["name"] != null && jAttach["name"].Type == JTokenType.String)
							jWhatsapp["name"] = jAttach["name"].ToString();
						if (jAttach["address"] != null && jAttach["address"].Type == JTokenType.String)
							jWhatsapp["address"] = jAttach["address"].ToString();

						Common.Tracer.TraceInfo("Se realizará un POST a infobip a la URL {0} con una ubicación: {1}", url, jMessage.ToString());
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jAttach["isPublicUrl"] != null &&
						jAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jAttach["isPublicUrl"].ToObject<bool>() &&
						jAttach["url"] != null &&
						jAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jAttach["url"].ToString();
					}
					else
					{
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					var type = DomainModel.Attachment.AttachmentType.File;
					var mimeType = jAttach["mimeType"].ToString();
					try
					{
						type = (DomainModel.Attachment.AttachmentType) jAttach["type"].ToObject<short>();

						if (type == Attachment.AttachmentType.Image &&
							mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
						{
							type = Attachment.AttachmentType.Sticker;
						}
					}
					catch
					{
						if (mimeType.StartsWith("image"))
						{
							type = DomainModel.Attachment.AttachmentType.Image;

							if (mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
							{
								type = Attachment.AttachmentType.Sticker;
							}
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = DomainModel.Attachment.AttachmentType.Audio;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = DomainModel.Attachment.AttachmentType.Video;
						}
					}

					switch (type)
					{
						case DomainModel.Attachment.AttachmentType.Image:
							url = string.Format("{0}whatsapp/1/message/image", baseUrl);
							jWhatsapp["mediaUrl"] = fileUrl;
							if (jDataMessage["body"] != null &&
								jDataMessage["body"].Type == JTokenType.String &&
								jDataMessage["body"].ToString().Length > 0)
							{
								jWhatsapp["caption"] = jDataMessage["body"].ToString();
							}
							break;
						case DomainModel.Attachment.AttachmentType.Sticker:
							url = string.Format("{0}whatsapp/1/message/sticker", baseUrl);
							jWhatsapp["mediaUrl"] = fileUrl;
							break;
						case DomainModel.Attachment.AttachmentType.Audio:
							url = string.Format("{0}whatsapp/1/message/audio", baseUrl);
							jWhatsapp["mediaUrl"] = fileUrl;
							break;
						case DomainModel.Attachment.AttachmentType.Video:
							url = string.Format("{0}whatsapp/1/message/video", baseUrl);
							jWhatsapp["mediaUrl"] = fileUrl;
							if (jDataMessage["body"] != null &&
								jDataMessage["body"].Type == JTokenType.String &&
								jDataMessage["body"].ToString().Length > 0)
							{
								jWhatsapp["caption"] = jDataMessage["body"].ToString();
							}
							break;
						default:
							url = string.Format("{0}whatsapp/1/message/document", baseUrl);
							jWhatsapp["mediaUrl"] = fileUrl;
							if (jDataMessage["body"] != null &&
								jDataMessage["body"].Type == JTokenType.String &&
								jDataMessage["body"].ToString().Length > 0)
							{
								jWhatsapp["caption"] = jDataMessage["body"].ToString();
							}
							if (jAttach["name"] != null &&
								jAttach["name"].Type == JTokenType.String &&
								jAttach["name"].ToString().Length > 0)
								jWhatsapp["filename"] = jAttach["name"].ToString();
							break;
					}

					Common.Tracer.TraceInfo("Se realizará un POST a infobip a la URL {0} con un archivo adjunto: {1}", url, jMessage.ToString());
				}
			}
			else if (jDataMessage["type"] != null &&
				jDataMessage["type"].Type == JTokenType.String &&
				jDataMessage["type"].ToString().Equals("interactive"))
			{
				var jInteractive = (JObject) jDataMessage["interactive"];
				var interactiveType = jInteractive["type"].ToString();
				if (interactiveType.Equals("button"))
				{
					url = string.Format("{0}whatsapp/1/message/interactive/buttons", baseUrl);

					jWhatsapp = jInteractive;

					if (jWhatsapp["header"] != null &&
						jWhatsapp["header"].Type == JTokenType.Object)
					{
						var jHeader = (JObject) jWhatsapp["header"];
						var type = jHeader["type"].ToString();
						switch (type)
						{
							case "image":
								jHeader["type"] = "IMAGE";
								jHeader["mediaUrl"] = jHeader[type]["link"];

								break;
							case "video":
								jHeader["type"] = "VIDEO";
								jHeader["mediaUrl"] = jHeader[type]["link"];
								break;
							case "document":
								jHeader["type"] = "DOCUMENT";
								jHeader["mediaUrl"] = jHeader[type]["link"];
								if (jHeader[type]["filename"] != null && jHeader[type]["filename"].Type == JTokenType.String)
									jHeader["filename"] = jHeader[type]["filename"];
								break;
							case "text":
								jHeader["type"] = "TEXT";
								break;
							default:
								break;
						}

						if (jHeader[type] != null && jHeader[type].Type == JTokenType.Object)
							jHeader.Remove(type);
					}

					var jButtons = (JArray) jWhatsapp["action"]["buttons"];
					foreach (JObject jButton in jButtons)
					{
						var type = jButton["type"].ToString();
						switch (type)
						{
							case "reply":
								jButton["type"] = "REPLY";
								jButton["id"] = jButton["reply"]["id"].ToString();
								jButton["title"] = jButton["reply"]["title"].ToString();
								jButton.Remove("reply");
								break;
							default:
								break;
						}
					}
				}
				else if (interactiveType.Equals("list"))
				{
					url = string.Format("{0}whatsapp/1/message/interactive/list", baseUrl);

					jWhatsapp = jInteractive;

					if (jWhatsapp["header"] != null &&
						jWhatsapp["header"].Type == JTokenType.Object)
					{
						var jHeader = (JObject) jWhatsapp["header"];
						var type = jHeader["type"].ToString();
						switch (type)
						{
							case "image":
								jHeader["type"] = "IMAGE";
								break;
							case "video":
								jHeader["type"] = "VIDEO";
								break;
							case "document":
								jHeader["type"] = "DOCUMENT";
								break;
							case "text":
								jHeader["type"] = "TEXT";
								break;
							default:
								break;
						}
					}

					var jAction = (JObject) jWhatsapp["action"];
					jAction["title"] = jAction["button"];
					jAction.Remove("button");
				}
				else if (interactiveType.Equals("product"))
				{
					url = string.Format("{0}whatsapp/1/message/interactive/product", baseUrl);

					jWhatsapp = jInteractive;

					var jAction = (JObject) jWhatsapp["action"];
					jAction["catalogId"] = jAction["catalog_id"];
					jAction["productRetailerId"] = jAction["product_retailer_id"];
					jAction.Remove("catalog_id");
					jAction.Remove("product_retailer_id");
				}
				else if (interactiveType.Equals("product_list"))
				{
					url = string.Format("{0}whatsapp/1/message/interactive/multi-product", baseUrl);

					jWhatsapp = jInteractive;

					if (jWhatsapp["header"] != null &&
						jWhatsapp["header"].Type == JTokenType.Object)
					{
						var jHeader = (JObject) jWhatsapp["header"];
						var type = jHeader["type"].ToString();
						switch (type)
						{
							case "image":
								jHeader["type"] = "IMAGE";
								break;
							case "video":
								jHeader["type"] = "VIDEO";
								break;
							case "document":
								jHeader["type"] = "DOCUMENT";
								break;
							case "text":
								jHeader["type"] = "TEXT";
								break;
							default:
								break;
						}
					}

					var jAction = (JObject) jWhatsapp["action"];
					jAction["catalogId"] = jAction["catalog_id"];
					var jSections = (JArray) jAction["sections"];
					foreach (JObject jSection in jSections)
					{
						var jProductRetailerIds = new JArray();
						jSection["productRetailerIds"] = jProductRetailerIds;

						var jProductItems = (JArray) jSection["product_items"];
						foreach (JObject jProductItem in jProductItems)
						{
							jProductRetailerIds.Add(jProductItem["product_retailer_id"].ToString());
						}

						jSection.Remove("product_items");
					}

					jAction.Remove("catalog_id");
				}
				else if (interactiveType.Equals("flow"))
				{
					url = string.Format("{0}whatsapp/1/message/interactive/flow", baseUrl);

					jWhatsapp = jInteractive;

					//TODO probar con infobip
					if (jWhatsapp["action"] != null &&
						jWhatsapp["action"].Type == JTokenType.Object)
					{
						var jAction = (JObject) jWhatsapp["action"];

						if (jAction["parameters"] != null &&
							jAction["parameters"].Type == JTokenType.Object)
						{
							var jParameters = jAction["parameters"];

							jAction["mode"] = "PUBLISHED";
							jAction["flowMessageVersion"] = 3;
							jAction["flowToken"] = jParameters["flow_token"];
							jAction["flowId"] = jParameters["flow_id"]; ;
							jAction["callToActionButton"] = jParameters["flow_cta"];
							jAction["flowAction"] = "NAVIGATE";

							JObject jPayload = new JObject();
							jPayload["screen"] = jParameters["flow_action_payload"]["screen"];
							
							if (jParameters["flow_action_payload"]["data"] != null &&
								jParameters["flow_action_payload"]["data"].Type == JTokenType.Object)
							{
								jPayload["data"] = jParameters["flow_action_payload"]["data"];
							}
							
							jAction["flowActionPayload"] = jPayload;
						}
					}
				}
				else if (interactiveType.Equals("cta_url"))
				{
					jWhatsapp = jInteractive;

					url = string.Format("{0}whatsapp/1/message/interactive/url-button", baseUrl);

					var jAction = (JObject) jWhatsapp["action"];

					var jParameters = jAction["parameters"];
					
					jAction["displayText"] = jParameters["display_text"];
					jAction["url"] = jParameters["url"];
					jAction.Remove("parameters");
				}
				else
				{
					throw new NotSupportedException("El tipo de mensaje interactivo no es soportado");
				}

				jWhatsapp.Remove("type");
				jMessage["content"] = jWhatsapp;

				Common.Tracer.TraceInfo("Se realizará un POST a infobip a la URL {0} con un mensaje interactivo y datos: {1}", url, jMessage.ToString());
			}
			else
			{
				url = string.Format("{0}whatsapp/1/message/text", baseUrl);
				var body = jDataMessage["body"].ToString();
				jWhatsapp["text"] = body;

				if (this.ServiceConfiguration.PreviewUrlForTextMessages)
				{
					if (body.IndexOf("https://") >= 0)
						jWhatsapp["previewUrl"] = true;
				}

				Common.Tracer.TraceInfo("Se realizará un POST a infobip a la URL {0} con un mensaje de texto y datos: {1}", url, jMessage.ToString());
			}

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					if (this.ServiceConfiguration.IntegrationType4AuthorizationType == 1)
						requestMessage.Headers.Add("Authorization", $"Basic {Convert.ToBase64String(Encoding.ASCII.GetBytes($"{this.ServiceConfiguration.IntegrationType4User}:{this.ServiceConfiguration.IntegrationType4Password}"))}");
					else if (this.ServiceConfiguration.IntegrationType4AuthorizationType == 2)
						requestMessage.Headers.Add("Authorization", $"App {this.ServiceConfiguration.IntegrationType4ApiKey}");
					requestMessage.Content = new StringContent(jMessage.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						this.PublishToServiceBus != null)
					{
						var uuid = new ShortGuid(Guid.NewGuid());

						var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
						{
							HttpRequestMessage = requestMessage,
							Message = message,
							ServiceId = this.ID,
							Uuid = uuid,
							SocialUserId = data["number"].ToString(),
							Delay = null
						});

						if (published)
							return uuid;
					}

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);

								try
								{
									if (jResponse["messageId"] != null && jResponse["messageId"].Type == JTokenType.String)
									{
										replyId = jResponse["messageId"].ToString();
									}
									else if (jResponse["messages"] != null && jResponse["messages"].Type == JTokenType.Array)
									{
										var jMessages = (JArray) jResponse["messages"];
										replyId = jMessages[0]["messageId"].ToString();
									}

									Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0}", replyId);
								}
								catch
								{
									Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente pero no se pudo obtener el código");
									replyId = string.Empty;
								}

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como leído utilizando la integración de Infobip
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a marcar como leído</param>
		private async Task MarkMessageAsReadInfobip(DomainModel.Message message)
		{
			string baseUrl = this.ServiceConfiguration.IntegrationType4BaseUrl;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = $"{baseUrl}whatsapp/1/senders/{message.PostedBy.ID}/message/{message.SocialMessageID}/read";

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					if (this.ServiceConfiguration.IntegrationType4AuthorizationType == 1)
						requestMessage.Headers.Add("Authorization", $"Basic {Convert.ToBase64String(Encoding.ASCII.GetBytes($"{this.ServiceConfiguration.IntegrationType4User}:{this.ServiceConfiguration.IntegrationType4Password}"))}");
					else if (this.ServiceConfiguration.IntegrationType4AuthorizationType == 2)
						requestMessage.Headers.Add("Authorization", $"App {this.ServiceConfiguration.IntegrationType4ApiKey}");

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se marcó el mensaje {0} como leído exitosamente", message);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2} - {3}", message, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2}", message, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}", message, ex);
			}
		}

		/// <summary>
		/// Realiza la invocación a Movistar para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToMovistar(JObject data, DomainModel.Message message)
		{
			await RefreshIntegrationType6AccessToken();

			if (string.IsNullOrEmpty(this.integrationType6AccessToken))
				throw new ServiceException("No se tiene un access token para poder enviar el mensaje");

			string replyId = Guid.NewGuid().ToString("D");

			var conversationId = message.SocialConversationID;
			if (string.IsNullOrEmpty(conversationId))
			{
				if (message.SocialUser != null &&
					message.SocialUser.ParametersByService != null &&
					message.SocialUser.ParametersByService.ContainsKey(this.ID) &&
					message.SocialUser.ParametersByService[this.ID].ContainsKey(Social.WhatsApp.User.ChatIDParameter))
				{
					conversationId = message.SocialUser.ParametersByService[this.ID][Social.WhatsApp.User.ChatIDParameter];
				}
			}

			if (string.IsNullOrEmpty(conversationId))
				throw new InvalidOperationException("No se puede enviar la encuesta porque no se tiene el código de conversación");

			string baseUrl = this.ServiceConfiguration.IntegrationType6UrlBase;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}plataforma-bot/v1/conversations/{1}/activities", baseUrl, conversationId);

			if (!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType6SendMessage))
			{
				url = string.Format("{0}{1}", baseUrl,
					this.ServiceConfiguration.IntegrationType6SendMessage
						.Replace("@@CONVERSATIONID@@", conversationId)
						.Replace("@@CHANNELID@@", this.ServiceConfiguration.IntegrationType6ChannelID));
			}

			var jDataMessage = data["msg"];

			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jMessage["type"] = "message";
			jMessage["serviceUrl"] = "https://ysocialmoviarcallback.azurewebsites.net/api/whatsapp/bf/5491159740925";
			jMessage["channelId"] = this.ServiceConfiguration.IntegrationType6ChannelID; //"wapp_b2b";

			var jRecipient = new Newtonsoft.Json.Linq.JObject();
			jMessage["recipient"] = jRecipient;
			jRecipient["id"] = data["number"].ToString();

			var jConversation = new Newtonsoft.Json.Linq.JObject();
			jMessage["conversation"] = jConversation;
			jConversation["id"] = conversationId;

			var jFrom = new Newtonsoft.Json.Linq.JObject();
			jMessage["from"] = jFrom;
			jFrom["id"] = this.ServiceConfiguration.IntegrationType6FromID; //"ce_wapp_b2b";
			jFrom["name"] = this.ServiceConfiguration.IntegrationType6FromName; //"Canal Escrito B2B";

			jMessage["inputHint"] = "acceptingInput";
			jMessage["channelData"] = new Newtonsoft.Json.Linq.JObject();
			jMessage["id"] = replyId;
			jMessage["timestamp"] = DateTime.UtcNow.ToString("u");
			jMessage["localTimestamp"] = DateTime.Now.ToString("u");

			if (message.Parameters.ContainsKey("MovistarSurveyButtons"))
			{
				var jSurveyConfiguration = Newtonsoft.Json.Linq.JObject.Parse(message.Parameters["MovistarSurveyButtons"]);

				DomainModel.Agent agent = null;
				if (jSurveyConfiguration["LastAgent"] != null &&
					jSurveyConfiguration["LastAgent"].Type == JTokenType.Integer)
				{
					var lastAgentId = jSurveyConfiguration["LastAgent"].ToObject<int>();
					agent = DAL.AgentDAO.GetOne(lastAgentId);
				}

				var jSuggestedActions = new Newtonsoft.Json.Linq.JObject();
				jMessage["suggestedActions"] = jSuggestedActions;
				var jActions = new Newtonsoft.Json.Linq.JArray();
				jSuggestedActions["actions"] = jActions;

				{
					var jButton1 = new Newtonsoft.Json.Linq.JObject();
					jButton1["title"] = jSurveyConfiguration["Button1Text"].ToString();
					var button1Payload = jSurveyConfiguration["Button1Payload"].ToString();
					button1Payload = button1Payload.Replace("{{CONVERSACION}}", conversationId);
					if (agent != null)
					{
						button1Payload = button1Payload.Replace("{{AGENTE[APELLIDO]}}", agent.LastName);
						button1Payload = button1Payload.Replace("{{AGENTE[USERNAME]}}", agent.UserName);
						button1Payload = button1Payload.Replace("{{AGENTE[ID]}}", agent.ID.ToString());
					}
					else
					{
						button1Payload = button1Payload.Replace("{{AGENTE[APELLIDO]}}", jSurveyConfiguration["NoAgent"].ToString());
						button1Payload = button1Payload.Replace("{{AGENTE[USERNAME]}}", jSurveyConfiguration["NoAgent"].ToString());
						button1Payload = button1Payload.Replace("{{AGENTE[ID]}}", jSurveyConfiguration["NoAgent"].ToString());
					}
					jButton1["value"] = button1Payload;
					jActions.Add(jButton1);
				}

				if (jSurveyConfiguration["Button2Text"] != null &&
					jSurveyConfiguration["Button2Text"].Type == JTokenType.String &&
					jSurveyConfiguration["Button2Text"].ToString().Length > 0)
				{
					var jButton2 = new Newtonsoft.Json.Linq.JObject();
					jButton2["title"] = jSurveyConfiguration["Button2Text"].ToString();
					var button2Payload = jSurveyConfiguration["Button2Payload"].ToString();
					button2Payload = button2Payload.Replace("{{CONVERSACION}}", conversationId);
					if (agent != null)
					{
						button2Payload = button2Payload.Replace("{{AGENTE[APELLIDO]}}", agent.LastName);
						button2Payload = button2Payload.Replace("{{AGENTE[USERNAME]}}", agent.UserName);
						button2Payload = button2Payload.Replace("{{AGENTE[ID]}}", agent.ID.ToString());
					}
					else
					{
						button2Payload = button2Payload.Replace("{{AGENTE[APELLIDO]}}", jSurveyConfiguration["NoAgent"].ToString());
						button2Payload = button2Payload.Replace("{{AGENTE[USERNAME]}}", jSurveyConfiguration["NoAgent"].ToString());
						button2Payload = button2Payload.Replace("{{AGENTE[ID]}}", jSurveyConfiguration["NoAgent"].ToString());
					}
					jButton2["value"] = button2Payload;
					jActions.Add(jButton2);
				}

				if (jSurveyConfiguration["Button3Text"] != null &&
					jSurveyConfiguration["Button3Text"].Type == JTokenType.String &&
					jSurveyConfiguration["Button3Text"].ToString().Length > 0)
				{
					var jButton3 = new Newtonsoft.Json.Linq.JObject();
					jButton3["title"] = jSurveyConfiguration["Button3Text"].ToString();
					var button3Payload = jSurveyConfiguration["Button3Payload"].ToString();
					button3Payload = button3Payload.Replace("{{CONVERSACION}}", conversationId);
					if (agent != null)
					{
						button3Payload = button3Payload.Replace("{{AGENTE[APELLIDO]}}", agent.LastName);
						button3Payload = button3Payload.Replace("{{AGENTE[USERNAME]}}", agent.UserName);
						button3Payload = button3Payload.Replace("{{AGENTE[ID]}}", agent.ID.ToString());
					}
					else
					{
						button3Payload = button3Payload.Replace("{{AGENTE[APELLIDO]}}", jSurveyConfiguration["NoAgent"].ToString());
						button3Payload = button3Payload.Replace("{{AGENTE[USERNAME]}}", jSurveyConfiguration["NoAgent"].ToString());
						button3Payload = button3Payload.Replace("{{AGENTE[ID]}}", jSurveyConfiguration["NoAgent"].ToString());
					}
					jButton3["value"] = button3Payload;
					jActions.Add(jButton3);
				}
			}

			if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var page = data["account"].ToString();

				var jDataAttach = (JObject) jDataMessage["attach"];

				var jAttachments = new Newtonsoft.Json.Linq.JArray();
				jMessage["attachments"] = jAttachments;
				var jAttach = new Newtonsoft.Json.Linq.JObject();
				jAttachments.Add(jAttach);

				string fileUrl;
				if (jDataAttach["isPublicUrl"] != null &&
					jDataAttach["isPublicUrl"].Type == JTokenType.Boolean &&
					jDataAttach["isPublicUrl"].ToObject<bool>() &&
					jDataAttach["url"] != null &&
					jDataAttach["url"].Type == JTokenType.String)
				{
					fileUrl = jDataAttach["url"].ToString();
				}
				else
				{
					fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
				}

				jAttach["contentUrl"] = fileUrl;
				jAttach["contentType"] = jDataAttach["mimeType"];
				jAttach["name"] = jDataAttach["name"].ToString();

				if (jDataMessage["body"] != null && jDataMessage["body"].Type == JTokenType.String)
					jMessage["text"] = jDataMessage["body"].ToString();

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a movistar a la URL {0} con un archivo adjunto y datos: {1}", url, jMessage.ToString());
			}
			else
			{
				jMessage["text"] = jDataMessage["body"].ToString();

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a movistar a la URL {0} con datos: {1}", url, jMessage.ToString());
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.integrationType6AccessToken);
					request.Headers.Add("X-IBM-Client-Id", this.ServiceConfiguration.IntegrationType6ClientID);

					request.Content = new StringContent(jMessage.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var jsonResponse = await sr.ReadToEndAsync();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza la invocación a Interaxa para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToInteraxa(JObject data, DomainModel.Message message)
		{
			await RefreshIntegrationType7AccessToken();

			if (string.IsNullOrEmpty(this.integrationType7AccessToken))
				throw new ServiceException("No se tiene un access token para poder enviar el mensaje");

			string baseUrl = this.ServiceConfiguration.IntegrationType7BaseUrl;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}api/v2/messages", baseUrl);

			var jDataMessage = data["msg"];

			var jBody = new Newtonsoft.Json.Linq.JObject();
			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jBody["message"] = jMessage;
			jMessage["to"] = string.Format("+{0}", data["number"].ToString());
			jMessage["media_id"] = this.ServiceConfiguration.IntegrationType7MediaID;

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					jMessage["type"] = "template";
					jMessage["recipient_type"] = "individual";
					var jTemplate = new Newtonsoft.Json.Linq.JObject();
					jMessage["template"] = jTemplate;

					jTemplate["namespace"] = sendDefinition.Namespace;
					jTemplate["name"] = sendDefinition.ElementName;

					var jLanguage = new Newtonsoft.Json.Linq.JObject();
					jTemplate["language"] = jLanguage;
					jLanguage["policy"] = "deterministic";
					jLanguage["code"] = sendDefinition.Language;

					var jComponents = new Newtonsoft.Json.Linq.JArray();
					jTemplate["components"] = jComponents;

					var jHeader = new Newtonsoft.Json.Linq.JObject();
					jHeader["type"] = "header";
					var jHeaderParameters = new Newtonsoft.Json.Linq.JArray();
					jHeader["parameters"] = jHeaderParameters;
					var jHeaderParameter = new Newtonsoft.Json.Linq.JObject();
					jHeaderParameters.Add(jHeaderParameter);

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jHeaderParameter["type"] = "text";
						jHeaderParameter["text"] = sendDefinition.HeaderTextParameter.Value;
						jComponents.Add(jHeader);
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jHeaderParameter["type"] = "document";
								var jDocument = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["document"] = jDocument;
								jDocument["filename"] = sendDefinition.HeaderMediaFileName;
								jDocument["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jHeaderParameter["type"] = "image";
								var jImage = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["image"] = jImage;
								jImage["filename"] = sendDefinition.HeaderMediaFileName;
								jImage["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jHeaderParameter["type"] = "video";
								var jVideo = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["video"] = jVideo;
								jVideo["filename"] = sendDefinition.HeaderMediaFileName;
								jVideo["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							default:
								break;
						}
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						jHeaderParameter["type"] = "location";
						var jLocation = new Newtonsoft.Json.Linq.JObject();
						jHeaderParameter["location"] = jLocation;
						jLocation["latitude"] = sendDefinition.HeaderLocationLatitude;
						jLocation["longitude"] = sendDefinition.HeaderLocationLongitude;
						jComponents.Add(jHeader);
					}

					var jBodyComponent = new Newtonsoft.Json.Linq.JObject();
					jComponents.Add(jBodyComponent);
					jBodyComponent["type"] = "body";
					var jBodyParameters = new Newtonsoft.Json.Linq.JArray();
					jBodyComponent["parameters"] = jBodyParameters;

					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					{
						foreach (var parameter in sendDefinition.Parameters)
						{
							var jBodyParameter = new Newtonsoft.Json.Linq.JObject();
							jBodyParameters.Add(jBodyParameter);
							jBodyParameter["type"] = "text";
							jBodyParameter["text"] = parameter.Value;
						}
					}

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						for (var i = 0; i < sendDefinition.Buttons.Length; i++)
						{
							var button = sendDefinition.Buttons[i];

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
								 sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
								 button.QuickReplyParameter != null)
							{
								var jButton = new Newtonsoft.Json.Linq.JObject();
								jButton["type"] = "button";
								jButton["sub_type"] = "quick_reply";
								jButton["index"] = i.ToString();
								var jButtonParameters = new Newtonsoft.Json.Linq.JArray();
								jButton["parameters"] = jButtonParameters;
								var jButtonParameter = new Newtonsoft.Json.Linq.JObject();
								jButtonParameters.Add(jButtonParameter);
								jButtonParameter["type"] = "payload";
								//jButtonParameter["payload"] = button.QuickReplyParameter.Value;

								var buttonPayload = GenerateButtonPayload(button.QuickReplyParameter.Value, message);
								jButtonParameter["payload"] = JsonConvert.SerializeObject(buttonPayload);

								jComponents.Add(jButton);
							}

							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
									  sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
									  button.CallToActionButtonType != null)
							{
								switch (button.CallToActionButtonType.Value)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											var jButton = new Newtonsoft.Json.Linq.JObject();
											jButton["type"] = "button";
											jButton["sub_type"] = "url";
											jButton["index"] = i.ToString();
											var jButtonParameters = new Newtonsoft.Json.Linq.JArray();
											jButton["parameters"] = jButtonParameters;
											var jButtonParameter = new Newtonsoft.Json.Linq.JObject();
											jButtonParameters.Add(jButtonParameter);
											jButtonParameter["type"] = "text";
											jButtonParameter["text"] = button.UrlParameter.Value;
											jComponents.Add(jButton);
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										//No esta implementado por el broker
										throw new NotImplementedException();
									default:
										break;
								}
							}
						}
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa de HSM: {0}", jMessage.ToString());
				}
				else if (data["parameters"] != null && data["parameters"].Type == JTokenType.Object)
				{
					jMessage["type"] = "hsm";
					jMessage["recipient_type"] = "individual";
					var jHsm = new Newtonsoft.Json.Linq.JObject();
					jMessage["hsm"] = jHsm;

					var jParameters = (JObject) data["parameters"];
					jHsm["namespace"] = jParameters["templateNamespace"].ToString();
					jHsm["element_name"] = jParameters["templateName"].ToString();

					var jLanguage = new Newtonsoft.Json.Linq.JObject();
					jHsm["language"] = jLanguage;
					jLanguage["policy"] = "deterministic";
					jLanguage["code"] = jParameters["language"].ToString();

					jHsm["localizable_params"] = jParameters["templateData"];

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa de HSM: {0}", jMessage.ToString());
				}
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var jDataAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jDataAttach["type"] != null && jDataAttach["type"].Type == JTokenType.String)
				{
					string type = jDataAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						var jLocation = new Newtonsoft.Json.Linq.JObject();
						jMessage["location"] = jLocation;
						jMessage["type"] = "location";
						jMessage["recipient_type"] = "individual";
						jLocation["longitude"] = jDataAttach["longitude"].ToObject<double>();
						jLocation["latitude"] = jDataAttach["latitude"].ToObject<double>();
						if (jDataAttach["name"] != null && jDataAttach["name"].Type == JTokenType.String)
							jLocation["name"] = jDataAttach["name"].ToString();
						if (jDataAttach["address"] != null && jDataAttach["address"].Type == JTokenType.String)
							jLocation["address"] = jDataAttach["address"].ToString();

						Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa con una ubicación: {0}", jMessage.ToString());
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jDataAttach["isPublicUrl"] != null &&
						jDataAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jDataAttach["isPublicUrl"].ToObject<bool>() &&
						jDataAttach["url"] != null &&
						jDataAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jDataAttach["url"].ToString();
					}
					else
					{
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					short type = 3;
					var mimeType = jDataAttach["mimeType"].ToString();
					try
					{
						type = jDataAttach["type"].ToObject<short>();
					}
					catch
					{
						if (mimeType.StartsWith("image"))
						{
							type = 2;
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = 6;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = 7;
						}
					}

					jMessage["url"] = fileUrl;
					jMessage["content_type"] = mimeType;
					jMessage["recipient_type"] = "individual";

					switch (type)
					{
						case 2 /*Image*/:
							jMessage["type"] = "image";
							if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
								jMessage["caption"] = jDataMessage["body"].ToString();
							break;
						case 6 /*Audio*/:
							jMessage["type"] = "audio";
							break;
						case 7 /*Video*/:
							jMessage["type"] = "video";
							break;
						case 3 /*Text*/:
						case 4 /*PdfDocument*/:
						case 5 /*WordDocument*/:
						default:
							jMessage["type"] = "document";
							jMessage["caption"] = jDataAttach["name"];
							break;
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa a la URL {0} con un archivo adjunto y datos: {1}", url, jBody.ToString());
				}
			}
			else if (jDataMessage["type"] != null &&
				jDataMessage["type"].Type == JTokenType.String &&
				jDataMessage["type"].ToString().Equals("interactive"))
			{
				var jInteractive = (JObject) jDataMessage["interactive"];
				jMessage["type"] = "interactive";
				jMessage["recipient_type"] = "individual";
				jMessage["interactive"] = jInteractive;

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa a la URL {0} con un mensaje interactivo y datos: {1}", url, jBody.ToString());
			}
			else
			{
				jMessage["type"] = "text";
				jMessage["text"] = jDataMessage["body"].ToString();

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a interaxa a la URL {0} con datos: {1}", url, jBody.ToString());
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.integrationType7AccessToken);
					request.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								var replyId = jResponse["message"]["id"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								message.SocialMessageID = replyId;
								DomainModel.StorageManager.Instance.SaveOutgoingMessage(message);

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza la invocación a Yoizen para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToYoizen(JObject data, DomainModel.Message message)
		{
			string baseUrl = this.ServiceConfiguration.IntegrationType10BaseUrl;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = $"{baseUrl}api/v1/{this.ServiceConfiguration.FullPhoneNumber}/message";

			var (jBody, hasMultimedia) = await this.CreateWhatsappMessage(data, message);
			jBody["custom_payload"] = message.ID.ToString();

			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignParameter))
				jBody["campaign"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter];

			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignIdParameter) &&
				int.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter], out int campaingId))
				jBody["campaign_id"] = campaingId;

			if (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0)
			{
				jBody["delay"] = this.ServiceConfiguration.DelayBetweenReplies;
			}

			if (this.ServiceConfiguration.IntegrationType10SendToServiceBus &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10AccountID) &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10LineID) &&
				this.sbSender != null)
			{
				try
				{
					var uuid = new ShortGuid(Guid.NewGuid()).ToString();
					var phoneNumber = this.ServiceConfiguration.FullPhoneNumber;

					jBody["waid"] = phoneNumber;
					jBody["uuid"] = uuid;
					jBody["ysocial"] = true;

					var type = string.Empty;
					if (jBody["type"] != null &&
						jBody["type"].Type == JTokenType.String)
						type = jBody["type"].ToString();

					var messageToSend = new Azure.Messaging.ServiceBus.ServiceBusMessage();
					messageToSend.Body = new BinaryData(jBody.ToString());
					messageToSend.ContentType = "application/json";
					messageToSend.ApplicationProperties["type"] = "message";
					messageToSend.ApplicationProperties["waid"] = phoneNumber;
					messageToSend.ApplicationProperties["lineid"] = this.ServiceConfiguration.IntegrationType10LineID;

					if (type.Equals("template") ||
						type.Equals("hsm"))
					{
						if (this.ServiceConfiguration.IntegrationType10UseSessionForHsmInServiceBus)
						{
							if (this.ServiceConfiguration.IntegrationType10UseSessionForHsmWithPhoneNumberInServiceBus)
							{
								var to = jBody["to"].ToString();
								messageToSend.SessionId = $"{phoneNumber}_{to}";
							}
							else
							{
								messageToSend.SessionId = phoneNumber;
							}
						}

						if (this.ServiceConfiguration.IntegrationType10UseSeparateQueueForSingle &&
							!message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignIdParameter))
						{
							await this.sbSenderHSMSingle.SendMessageAsync(messageToSend);
						}
						else
						{
							await this.sbSenderHSM.SendMessageAsync(messageToSend);
						}
					}
					else
					{
						if (this.ServiceConfiguration.IntegrationType10UseSessionInServiceBus)
						{
							if (this.ServiceConfiguration.IntegrationType10UseSessionWithPhoneNumberInServiceBus)
							{
								var to = jBody["to"].ToString();
								messageToSend.SessionId = $"{phoneNumber}_{to}";
							}
							else
							{
								messageToSend.SessionId = phoneNumber;
							}
						}

						await this.sbSender.SendMessageAsync(messageToSend);
					}

					Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente a través del ServiceBus con código {0}", uuid);

					return uuid;
				}
				catch (Exception ex)
				{
					if (this.ServiceConfiguration.IntegrationType10UseSeparateQueueForSingle &&
							!message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignIdParameter))
						Tracer.TraceError("Ocurrió un error al intentar publicar el mensaje en la cola {0} del service bus: {1}", this.sbSenderHSMSingle.EntityPath, ex);
					else
						Tracer.TraceError("Ocurrió un error al intentar publicar el mensaje en la cola {0} del service bus: {1}", this.sbSenderHSM.EntityPath, ex);
				}
			}

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Add("Authorization", $"Bearer {this.ServiceConfiguration.IntegrationType10AccessToken}");
					requestMessage.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						this.PublishToServiceBus != null)
					{
						var uuid = new ShortGuid(Guid.NewGuid());

						var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
						{
							HttpRequestMessage = requestMessage,
							Message = message,
							ServiceId = this.ID,
							Uuid = uuid,
							SocialUserId = jBody["to"].ToString(),
							Delay = (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0) ? this.ServiceConfiguration.DelayAfterMultimedia : (int?) null
						});

						if (published)
							return uuid;
					}

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								var replyId = jResponse["id"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								if (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0)
								{
									Tracer.TraceVerb("Se aguardarán {0} milisegundos porque se acaba de enviar un mensaje con multimedia", this.ServiceConfiguration.DelayAfterMultimedia);
									await Task.Delay(this.ServiceConfiguration.DelayBetweenReplies);
								}

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como leído utilizando la integración de Yoizen
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a marcar como leído</param>
		private async Task MarkMessageAsReadYoizen(DomainModel.Message message)
		{
			string baseUrl = this.ServiceConfiguration.IntegrationType10BaseUrl;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = $"{baseUrl}api/v1/{this.ServiceConfiguration.FullPhoneNumber}/messages/{Uri.EscapeUriString(message.SocialMessageID)}/read";

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Put, url))
				{
					requestMessage.Headers.Add("Authorization", $"Bearer {this.ServiceConfiguration.IntegrationType10AccessToken}");

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se marcó el mensaje {0} como leído exitosamente", message);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2} - {3}", message, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2}", message, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}", message, ex);
			}
		}

		/// <summary>
		/// Realiza una acción para llamadas de voz utilizando la integración de Yoizen
		/// </summary>
		/// <param name="caseId">El código de caso asociado a la llamada</param>
		/// <param name="action">La acción que se creará en el cuerpo</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Información de la sesión de WebRtc</param>
		/// <param name="customPayload">Datos adicionales a enviar en el cuerpo</param>
		private async Task VoiceCallActionYoizen(string action, string callId, string sdp = null, long? caseId = null, string customPayload = null)
		{
			string baseUrl = this.ServiceConfiguration.IntegrationType10BaseUrl;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = $"{baseUrl}api/v1/{this.ServiceConfiguration.FullPhoneNumber}/calls";

			var jBody = this.CreateWhatsappCall(action, callId, sdp, customPayload);

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Add("Authorization", $"Bearer {this.ServiceConfiguration.IntegrationType10AccessToken}");
					requestMessage.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se envió la acción {0} de la llamada {1} del caso {2}", action, callId, caseId);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4} - {5}", action, callId, caseId, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4}", action, callId, caseId, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}", action, callId, caseId, ex);
			}
		}

		/// <summary>
		/// Realiza la invocación a Yoizen para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToCloudApi(JObject data, DomainModel.Message message)
		{
			string url = $"https://graph.facebook.com/{this.ServiceConfiguration.IntegrationType11GraphApiVersion}/{this.ServiceConfiguration.IntegrationType11PhoneNumberId}/messages";

			var (jBody, hasMultimedia) = await this.CreateWhatsappMessage(data, message);
			jBody["biz_opaque_callback_data"] = message.ID.ToString();

			var to = jBody["to"].ToString();
#if DEBUG
			if (to.StartsWith("54911"))
			{
				to = $"541115{to.Substring(5)}";
				jBody["to"] = to;
			}

            if (to.StartsWith("549223"))
            {
                to = $"54223{to.Substring(6)}";
                jBody["to"] = to;
            }
#endif

			if (this.ServiceConfiguration.IntegrationType11TestingAccount &&
				this.ServiceConfiguration.IntegrationType11TestingMapping != null &&
				this.ServiceConfiguration.IntegrationType11TestingMapping.Count > 0)
			{
				foreach (var mapping in this.ServiceConfiguration.IntegrationType11TestingMapping)
				{
					if (to.StartsWith(mapping.Key))
					{
						to = $"{mapping.Value}{to.Substring(mapping.Key.Length)}";
						jBody["to"] = to;
						break;
					}
				}
			}

			if (jBody["template"] != null && jBody["template"].Type == JTokenType.Object)
			{
				try
				{
					var jTemplate = (Newtonsoft.Json.Linq.JObject) jBody["template"];
					if (jTemplate["namespace"] != null)
						jTemplate.Remove("namespace");
				}
				catch { }

				if (jBody["template"]["components"] != null &&
					jBody["template"]["components"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
				{
					var jComponents = (Newtonsoft.Json.Linq.JArray) jBody["template"]["components"];
					if (jComponents.Count > 0)
					{
						var jComponent = jComponents[0];
						if (jComponent != null &&
							jComponent.Type == Newtonsoft.Json.Linq.JTokenType.Object &&
							jComponent["type"] != null &&
							jComponent["type"].Type == Newtonsoft.Json.Linq.JTokenType.String &&
							jComponent["type"].ToString().Equals("header") &&
							jComponent["parameters"] != null &&
							jComponent["parameters"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
						{
							var jParameters = (Newtonsoft.Json.Linq.JArray) jComponent["parameters"];
							if (jParameters.Count == 1 &&
								jParameters[0].Type == Newtonsoft.Json.Linq.JTokenType.Object)
							{
								var jParameter = (Newtonsoft.Json.Linq.JObject) jParameters[0];
								if (jParameter["type"] != null &&
									jParameter["type"].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									var headerType = jParameter["type"].ToString();
									if (!headerType.Equals("document") && !headerType.Equals("text"))
									{
										if (jParameter[headerType] != null &&
											jParameter[headerType].Type == Newtonsoft.Json.Linq.JTokenType.Object)
										{
											var jHeader = (Newtonsoft.Json.Linq.JObject) jParameter[headerType];
											if (jHeader["filename"] != null)
												jHeader.Remove("filename");
										}
									}
								}
							}
						}
					}
				}
			}

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("text/javascript"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("*/*"));

					var productValue = new ProductInfoHeaderValue("Yoizen", "1.0");
					requestMessage.Headers.UserAgent.Add(productValue);
					requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.ServiceConfiguration.IntegrationType11AccessToken);

					jBody["messaging_product"] = "whatsapp";
					requestMessage.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						this.PublishToServiceBus != null)
					{
						var uuid = new ShortGuid(Guid.NewGuid());

						var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
						{
							HttpRequestMessage = requestMessage,
							Message = message,
							ServiceId = this.ID,
							Uuid = uuid,
							SocialUserId = jBody["to"].ToString(),
							Delay = (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0) ? this.ServiceConfiguration.DelayAfterMultimedia : (int?) null
						});

						if (published)
							return uuid;
					}

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);
								var replyId = ((Newtonsoft.Json.Linq.JArray) jResponse["messages"])[0]["id"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								message.SocialMessageID = replyId;
								await DomainModel.StorageManager.Instance.SaveOutgoingMessageAsync(message);

								if (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0)
								{
									Tracer.TraceVerb("Se aguardarán {0} milisegundos porque se acaba de enviar un mensaje con multimedia", this.ServiceConfiguration.DelayAfterMultimedia);
									await Task.Delay(this.ServiceConfiguration.DelayBetweenReplies);
								}

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como leído utilizando la integración de Cloud API
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a marcar como leído</param>
		private async Task MarkMessageAsReadCloudApi(DomainModel.Message message)
		{
			string url = $"https://graph.facebook.com/{this.ServiceConfiguration.IntegrationType11GraphApiVersion}/{this.ServiceConfiguration.IntegrationType11PhoneNumberId}/messages";

			var jBody = new Newtonsoft.Json.Linq.JObject();
			jBody["messaging_product"] = "whatsapp";
			jBody["status"] = "read";
			jBody["message_id"] = message.SocialMessageID;

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("text/javascript"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("*/*"));

					var productValue = new ProductInfoHeaderValue("Yoizen", "1.0");
					requestMessage.Headers.UserAgent.Add(productValue);
					requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.ServiceConfiguration.IntegrationType11AccessToken);
					requestMessage.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se marcó el mensaje {0} como leído exitosamente", message);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2} - {3}", message, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}-{2}", message, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló marcar el mensaje {0} como leído: {1}", message, ex);
			}
		}

		/// <summary>
		/// Realiza una acción para llamadas de voz utilizando la integración de Cloud Api
		/// </summary>
		/// <param name="caseId">El código de caso asociado a la llamada</param>
		/// <param name="action">La acción que se creará en el cuerpo</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Información de la sesión de WebRtc</param>
		/// <param name="customPayload">Datos adicionales a enviar en el cuerpo</param>
		private async Task VoiceCallActionCloudApi(string action, string callId, string sdp = null, long? caseId = null, string customPayload = null)
		{
			string url = $"https://graph.facebook.com/{this.ServiceConfiguration.IntegrationType11GraphApiVersion}/{this.ServiceConfiguration.IntegrationType11PhoneNumberId}/calls";

			var jBody = this.CreateWhatsappCall(action, callId, sdp, customPayload);

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("text/javascript"));
					requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("*/*"));

					var productValue = new ProductInfoHeaderValue("Yoizen", "1.0");
					requestMessage.Headers.UserAgent.Add(productValue);
					requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.ServiceConfiguration.IntegrationType11AccessToken);
					requestMessage.Content = new StringContent(jBody.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se envió la acción {0} de la llamada {1} del caso {2}", action, callId, caseId);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4} - {5}", action, callId, caseId, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4}", action, callId, caseId, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}", action, callId, caseId, ex);
			}
		}

		/// <summary>
		/// Realiza la invocación a un Postback para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToPostback(JObject data, DomainModel.Message message)
		{
			var parameters = new Dictionary<string, string>();
			parameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
			parameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();
			var url = this.ServiceConfiguration.IntegrationType3ReplyEndpoint.GetUrl(parameters);

			var (jBody, hasMultimedia) = await this.CreateWhatsappMessage(data, message);
			jBody["biz_opaque_callback_data"] = message.ID.ToString();

			if (this.ServiceConfiguration.IntegrationType3IncludeFromWhenUsingWhatsappFormat)
			{
				jBody["from"] = this.ServiceConfiguration.FullPhoneNumber;
			}

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					var body = jBody.ToString();

					requestMessage.Content = new StringContent(body, global::System.Text.Encoding.UTF8, "application/json");

					this.ServiceConfiguration.IntegrationType3ReplyEndpoint.ApplyHeaders(requestMessage, this.postbackParameters);

					var keyForHashing = this.ServiceConfiguration.IntegrationType3ReplyEndpoint.HashKey;
					var hashHmacHex = HashHMACHex(keyForHashing, body);
					requestMessage.Headers.Add("hash", hashHmacHex);

					if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						this.PublishToServiceBus != null)
					{
						var uuid = new ShortGuid(Guid.NewGuid());

						var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
						{
							HttpRequestMessage = requestMessage,
							Message = message,
							ServiceId = this.ID,
							Uuid = uuid,
							SocialUserId = jBody["to"].ToString(),
							Delay = (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0) ? this.ServiceConfiguration.DelayAfterMultimedia : (int?) null
						});

						if (published)
							return uuid;
					}

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								string replyId;

								if (jResponse["id"] != null && jResponse["id"].Type == JTokenType.String)
								{
									replyId = jResponse["id"].ToString();
									Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);
								}
								else if (jResponse["messageId"] != null && jResponse["messageId"].Type == JTokenType.String)
								{
									replyId = jResponse["messageId"].ToString();
									Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);
								}
								else
								{
									replyId = Guid.NewGuid().ToString();
									Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente y no se pudo obtener su código. Se asigna código {0}. Respuesta {1}", replyId, jsonResponse);
								}
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								if (hasMultimedia && this.ServiceConfiguration.DelayAfterMultimedia > 0)
								{
									Tracer.TraceVerb("Se aguardarán {0} milisegundos porque se acaba de enviar un mensaje con multimedia", this.ServiceConfiguration.DelayAfterMultimedia);
									await Task.Delay(this.ServiceConfiguration.DelayBetweenReplies);
								}

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza una acción para llamadas de voz utilizando la integración de Postback con formato nativo
		/// </summary>
		/// <param name="caseId">El código de caso asociado a la llamada</param>
		/// <param name="action">La acción que se creará en el cuerpo</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Información de la sesión de WebRtc</param>
		/// <param name="customPayload">Datos adicionales a enviar en el cuerpo</param>
		private async Task VoiceCallActionPostback(string action, string callId, string sdp = null, long? caseId = null, string customPayload = null)
		{
			var parameters = new Dictionary<string, string>();
			parameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
			parameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();
			var url = this.ServiceConfiguration.IntegrationType3VoiceCallEndpoint.GetUrl(parameters);

			var jBody = this.CreateWhatsappCall(action, callId, sdp, customPayload);

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					var body = jBody.ToString();

					requestMessage.Content = new StringContent(body, global::System.Text.Encoding.UTF8, "application/json");

					this.ServiceConfiguration.IntegrationType3ReplyEndpoint.ApplyHeaders(requestMessage, this.postbackParameters);

					var keyForHashing = this.ServiceConfiguration.IntegrationType3VoiceCallEndpoint.HashKey;
					var hashHmacHex = HashHMACHex(keyForHashing, body);
					requestMessage.Headers.Add("hash", hashHmacHex);

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se envió la acción {0} de la llamada {1} del caso {2}", action, callId, caseId);
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4} - {5}", action, callId, caseId, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}-{4}", action, callId, caseId, response.ReasonPhrase, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Falló enviar la acción {0} de la llamada {1} del caso {2}: {3}", action, callId, caseId, ex);
			}
		}

		/// <summary>
		/// Realiza la invocación a Gupshup para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToGupshup(JObject data, DomainModel.Message message)
		{
			string url = "https://api.gupshup.io/sm/api/v1/msg";

			var jDataMessage = data["msg"];

			var bodyParameters = new Dictionary<string, string>();
			bodyParameters["channel"] = "whatsapp";
			bodyParameters["destination"] = data["number"].ToString();
			bodyParameters["source"] = this.ServiceConfiguration.FullPhoneNumber;
			bodyParameters["src.name"] = this.ServiceConfiguration.IntegrationType8AppName;

			var jMessage = new Newtonsoft.Json.Linq.JObject();

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				jMessage["isHSM"] = "true";
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					//var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					//jWhatsapp["templateName"] = sendDefinition.ElementName;
					//jWhatsapp["language"] = sendDefinition.Language;

					//var jMediaTemplateData = new Newtonsoft.Json.Linq.JObject();
					//jWhatsapp["mediaTemplateData"] = jMediaTemplateData;

					//if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
					//	sendDefinition.HeaderTextParameter != null)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
					//	jMediaTemplateData["header"]["textPlaceholder"] = sendDefinition.HeaderTextParameter.Value;
					//}
					//else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();

					//	string fileUrl = sendDefinition.HeaderMediaUrl;
					//	if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
					//		!sendDefinition.HeaderMediaUrlIsPublic.Value)
					//	{
					//		var jFileUpload = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
					//		jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

					//		var bytes = DownloadFile(sendDefinition.HeaderMediaUrl, out string mimeType);

					//		jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(bytes);
					//		jFileUpload["msg"]["attach"]["mimeType"] = mimeType;
					//		jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

					//		fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
					//	}
					//	else if (sendDefinition.HeaderMediaFromAttachment &&
					//		message.HasAttach)
					//	{
					//		var jFileUpload = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
					//		jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
					//		jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
					//		jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

					//		fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
					//	}

					//	switch (sendDefinition.HeaderMediaType)
					//	{
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
					//			jMediaTemplateData["header"]["documentUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["documentFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
					//			jMediaTemplateData["header"]["imageUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["imageFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
					//			jMediaTemplateData["header"]["videoUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["videoFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		default:
					//			break;
					//	}
					//}
					//else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
					//	jMediaTemplateData["header"]["latitude"] = sendDefinition.HeaderLocationLatitude;
					//	jMediaTemplateData["header"]["longitude"] = sendDefinition.HeaderLocationLongitude;
					//}

					//jMediaTemplateData["body"] = new Newtonsoft.Json.Linq.JObject();
					//if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					//	jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray(sendDefinition.Parameters.Select(p => p.Value));
					//else
					//	jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray();

					//if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
					//	sendDefinition.Buttons != null &&
					//	sendDefinition.Buttons.Length > 0)
					//{
					//	var jButtons = new Newtonsoft.Json.Linq.JArray();
					//	jMediaTemplateData["buttons"] = jButtons;
					//	for (var i = 0; i < sendDefinition.Buttons.Length; i++)
					//	{
					//		var button = sendDefinition.Buttons[i];
					//		jButtons.Add(new Newtonsoft.Json.Linq.JObject());
					//		switch (sendDefinition.ButtonsType)
					//		{
					//			case DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply:
					//				if (button.QuickReplyParameter != null)
					//				{
					//					jButtons[i]["quickReplyPayload"] = button.QuickReplyParameter.Value;
					//				}
					//				break;
					//			case DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction:
					//				if (button.CallToActionButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url &&
					//					button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
					//				{
					//					jButtons[i]["urlPlaceholder"] = button.UrlParameter.Value;
					//				}
					//				break;
					//			default:
					//				break;
					//		}
					//	}
					//}
				}
				else if (data["parameters"] != null && data["parameters"].Type == JTokenType.Object)
				{
					//jMessage["type"] = "hsm";
					//var jHsm = new Newtonsoft.Json.Linq.JObject();
					//jMessage["hsm"] = jHsm;
					//
					//var jParameters = (JObject) data["parameters"];
					//jHsm["namespace"] = jParameters["templateNamespace"].ToString();
					//jHsm["element_name"] = jParameters["templateName"].ToString();
					//
					//var jLanguage = new Newtonsoft.Json.Linq.JObject();
					//jHsm["language"] = jLanguage;
					//jLanguage["policy"] = "deterministic";
					//jLanguage["code"] = jParameters["language"].ToString();
					//
					//jHsm["localizable_params"] = jParameters["templateData"];
					//jMessage["recipient_type"] = "individual";
				}
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var jDataAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jDataAttach["type"] != null && jDataAttach["type"].Type == JTokenType.String)
				{
					string type = jDataAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						jMessage["type"] = "location";
						jMessage["longitude"] = jDataAttach["longitude"].ToObject<double>();
						jMessage["latitude"] = jDataAttach["latitude"].ToObject<double>();
						if (jDataAttach["name"] != null && jDataAttach["name"].Type == JTokenType.String)
							jMessage["name"] = jDataAttach["name"].ToString();
						if (jDataAttach["address"] != null && jDataAttach["address"].Type == JTokenType.String)
							jMessage["address"] = jDataAttach["address"].ToString();
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jDataAttach["isPublicUrl"] != null &&
						jDataAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jDataAttach["isPublicUrl"].ToObject<bool>() &&
						jDataAttach["url"] != null &&
						jDataAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jDataAttach["url"].ToString();
					}
					else
					{
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					short type = 3;
					var mimeType = jDataAttach["mimeType"].ToString();
					try
					{
						type = jDataAttach["type"].ToObject<short>();
					}
					catch
					{
						if (mimeType.StartsWith("image"))
						{
							type = 2;
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = 6;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = 7;
						}
					}

					switch (type)
					{
						case 2 /*Image*/:
							jMessage["originalUrl"] = fileUrl;
							jMessage["previewUrl"] = fileUrl;
							jMessage["type"] = "image";
							if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
								jMessage["caption"] = jDataMessage["body"].ToString();
							break;
						case 6 /*Audio*/:
							jMessage["type"] = "audio";
							jMessage["url"] = fileUrl;
							break;
						case 7 /*Video*/:
							jMessage["type"] = "video";
							jMessage["url"] = fileUrl;
							if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
								jMessage["caption"] = jDataMessage["body"].ToString();
							break;
						case 3 /*Text*/:
						case 4 /*PdfDocument*/:
						case 5 /*WordDocument*/:
						default:
							jMessage["type"] = "file";
							jMessage["url"] = fileUrl;
							jMessage["filename"] = jDataAttach["name"];
							break;
					}
				}
			}
			else
			{
				jMessage["isHSM"] = "false";
				jMessage["type"] = "text";
				jMessage["text"] = jDataMessage["body"].ToString();
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Add("apikey", this.ServiceConfiguration.IntegrationType8ApiKey);

					bodyParameters["message"] = jMessage.ToString();
					var sbBody = new StringBuilder();
					foreach (var bodyParameter in bodyParameters)
					{
						if (sbBody.Length > 0)
							sbBody.Append("&");

						sbBody.AppendFormat("{0}={1}", bodyParameter.Key, HttpUtility.UrlEncode(bodyParameter.Value));
					}

					var body = sbBody.ToString();

					request.Content = new StringContent(body, global::System.Text.Encoding.UTF8, "application/x-www-form-urlencoded");

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								var replyId = jResponse["messageId"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza la invocación a Twilio para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToTwilio(JObject data, DomainModel.Message message)
		{
			string url = $"https://api.twilio.com/2010-04-01/Accounts/{this.ServiceConfiguration.IntegrationType9AccountSid}/Messages.json";

			var bodyParameters = new Dictionary<string, string>();
			bodyParameters["To"] = $"whatsapp:+{data["number"].ToString()}";
			bodyParameters["From"] = $"whatsapp:+{this.ServiceConfiguration.FullPhoneNumber}";

			var jDataMessage = data["msg"];

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				//jMessage["isHSM"] = "true";
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					//var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					//jWhatsapp["templateName"] = sendDefinition.ElementName;
					//jWhatsapp["language"] = sendDefinition.Language;

					//var jMediaTemplateData = new Newtonsoft.Json.Linq.JObject();
					//jWhatsapp["mediaTemplateData"] = jMediaTemplateData;

					//if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
					//	sendDefinition.HeaderTextParameter != null)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
					//	jMediaTemplateData["header"]["textPlaceholder"] = sendDefinition.HeaderTextParameter.Value;
					//}
					//else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();

					//	string fileUrl = sendDefinition.HeaderMediaUrl;
					//	if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
					//		!sendDefinition.HeaderMediaUrlIsPublic.Value)
					//	{
					//		var jFileUpload = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
					//		jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

					//		var bytes = DownloadFile(sendDefinition.HeaderMediaUrl, out string mimeType);

					//		jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(bytes);
					//		jFileUpload["msg"]["attach"]["mimeType"] = mimeType;
					//		jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

					//		fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
					//	}
					//	else if (sendDefinition.HeaderMediaFromAttachment &&
					//		message.HasAttach)
					//	{
					//		var jFileUpload = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
					//		jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
					//		jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
					//		jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
					//		jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

					//		fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
					//	}

					//	switch (sendDefinition.HeaderMediaType)
					//	{
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
					//			jMediaTemplateData["header"]["documentUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["documentFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
					//			jMediaTemplateData["header"]["imageUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["imageFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
					//			jMediaTemplateData["header"]["videoUrl"] = fileUrl;
					//			jMediaTemplateData["header"]["videoFilename"] = sendDefinition.HeaderMediaFileName;
					//			break;
					//		default:
					//			break;
					//	}
					//}
					//else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					//{
					//	jMediaTemplateData["header"] = new Newtonsoft.Json.Linq.JObject();
					//	jMediaTemplateData["header"]["latitude"] = sendDefinition.HeaderLocationLatitude;
					//	jMediaTemplateData["header"]["longitude"] = sendDefinition.HeaderLocationLongitude;
					//}

					//jMediaTemplateData["body"] = new Newtonsoft.Json.Linq.JObject();
					//if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					//	jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray(sendDefinition.Parameters.Select(p => p.Value));
					//else
					//	jMediaTemplateData["body"]["placeholders"] = new Newtonsoft.Json.Linq.JArray();

					//if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
					//	sendDefinition.Buttons != null &&
					//	sendDefinition.Buttons.Length > 0)
					//{
					//	var jButtons = new Newtonsoft.Json.Linq.JArray();
					//	jMediaTemplateData["buttons"] = jButtons;
					//	for (var i = 0; i < sendDefinition.Buttons.Length; i++)
					//	{
					//		var button = sendDefinition.Buttons[i];
					//		jButtons.Add(new Newtonsoft.Json.Linq.JObject());
					//		switch (sendDefinition.ButtonsType)
					//		{
					//			case DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply:
					//				if (button.QuickReplyParameter != null)
					//				{
					//					jButtons[i]["quickReplyPayload"] = button.QuickReplyParameter.Value;
					//				}
					//				break;
					//			case DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction:
					//				if (button.CallToActionButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url &&
					//					button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
					//				{
					//					jButtons[i]["urlPlaceholder"] = button.UrlParameter.Value;
					//				}
					//				break;
					//			default:
					//				break;
					//		}
					//	}
					//}
				}
				else if (data["parameters"] != null && data["parameters"].Type == JTokenType.Object)
				{
					//jMessage["type"] = "hsm";
					//var jHsm = new Newtonsoft.Json.Linq.JObject();
					//jMessage["hsm"] = jHsm;
					//
					//var jParameters = (JObject) data["parameters"];
					//jHsm["namespace"] = jParameters["templateNamespace"].ToString();
					//jHsm["element_name"] = jParameters["templateName"].ToString();
					//
					//var jLanguage = new Newtonsoft.Json.Linq.JObject();
					//jHsm["language"] = jLanguage;
					//jLanguage["policy"] = "deterministic";
					//jLanguage["code"] = jParameters["language"].ToString();
					//
					//jHsm["localizable_params"] = jParameters["templateData"];
					//jMessage["recipient_type"] = "individual";
				}
			}
			else if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
			{
				var jDataAttach = (JObject) jDataMessage["attach"];

				var sendAsAttach = true;
				if (jDataAttach["type"] != null && jDataAttach["type"].Type == JTokenType.String)
				{
					string type = jDataAttach["type"].ToString();
					if (type.Equals("location"))
					{
						sendAsAttach = false;
						bodyParameters["PersistentAction"] = $"{jDataAttach["latitude"].ToObject<double>()},{jDataAttach["longitude"].ToObject<double>()}";
						if (jDataAttach["name"] != null && jDataAttach["name"].Type == JTokenType.String)
							bodyParameters["Body"] = jDataAttach["name"].ToString();
						if (jDataAttach["address"] != null && jDataAttach["address"].Type == JTokenType.String)
							bodyParameters["PersistentAction"] = bodyParameters["PersistentAction"] + $"|{jDataAttach["address"].ToString()}";
					}
				}

				if (sendAsAttach)
				{
					string fileUrl;
					if (jDataAttach["isPublicUrl"] != null &&
						jDataAttach["isPublicUrl"].Type == JTokenType.Boolean &&
						jDataAttach["isPublicUrl"].ToObject<bool>() &&
						jDataAttach["url"] != null &&
						jDataAttach["url"].Type == JTokenType.String)
					{
						fileUrl = jDataAttach["url"].ToString();
					}
					else
					{
						fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
					}

					short type = 3;
					var mimeType = jDataAttach["mimeType"].ToString();
					try
					{
						type = jDataAttach["type"].ToObject<short>();
					}
					catch
					{
						if (mimeType.StartsWith("image"))
						{
							type = 2;
						}
						else if (mimeType.StartsWith("audio"))
						{
							type = 6;
						}
						else if (mimeType.StartsWith("video"))
						{
							type = 7;
						}
					}

					bodyParameters["MediaUrl"] = fileUrl;
				}
			}
			else
			{
				bodyParameters["Body"] = jDataMessage["body"].ToString();
			}

			try
			{
				var sbBody = new StringBuilder();
				foreach (var urlParameter in bodyParameters)
				{
					if (sbBody.Length > 0)
						sbBody.Append("&");
					sbBody.Append($"{urlParameter.Key}={HttpUtility.UrlEncode(urlParameter.Value)}");
				}

				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

					var base64authorization = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{this.ServiceConfiguration.IntegrationType9AccountSid}:{this.ServiceConfiguration.IntegrationType9AuthToken}"));
					request.Headers.Authorization = new AuthenticationHeaderValue("Basic", base64authorization);

					var body = sbBody.ToString();
					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a gupshup a la URL {0} con datos: {1}", url, body.ToString());

					request.Content = new StringContent(body, global::System.Text.Encoding.UTF8, "application/x-www-form-urlencoded");

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = sr.ReadToEnd();
								var jResponse = JObject.Parse(jsonResponse);
								var replyId = jResponse["sid"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = sr.ReadToEnd();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza la invocación a Wavy para informar de un mensaje saliente
		/// </summary>
		/// <param name="data">Un <see cref="JObject"/> con la información de la respuesta</param>
		private async Task<string> PostToWavy(JObject data, DomainModel.Message message)
		{
			string replyId = null;

			var jDataMessage = data["msg"];

			var jMessage = new Newtonsoft.Json.Linq.JObject();

			var jDestinations = new Newtonsoft.Json.Linq.JArray();
			jMessage["destinations"] = jDestinations;

			var jDestination = new Newtonsoft.Json.Linq.JObject();
			jDestination["destination"] = data["number"].ToString();
			jDestination["correlationId"] = jDataMessage["id"].ToString();
			jDestinations.Add(jDestination);

			var jMessageInfo = new Newtonsoft.Json.Linq.JObject();
			jMessage["message"] = jMessageInfo;

			var isHSM = false;
			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter))
			{
				bool.TryParse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter], out isHSM);
			}

			if (isHSM)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					var jHSM = new JObject();
					jMessageInfo["template"] = jHSM;
					jHSM["elementName"] = sendDefinition.ElementName;
					jHSM["namespace"] = sendDefinition.Namespace;
					jHSM["languageCode"] = sendDefinition.Language;

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jHSM["header"] = new Newtonsoft.Json.Linq.JObject();
						jHSM["header"]["parameters"] = new Newtonsoft.Json.Linq.JArray(sendDefinition.HeaderTextParameter.Value);
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						jHSM["header"] = new Newtonsoft.Json.Linq.JObject();

						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jHSM["header"]["document"] = new Newtonsoft.Json.Linq.JObject();
								jHSM["header"]["document"]["url"] = fileUrl;
								jHSM["header"]["document"]["type"] = "PDF";
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jHSM["header"]["image"] = new Newtonsoft.Json.Linq.JObject();
								jHSM["header"]["image"]["url"] = fileUrl;
								jHSM["header"]["image"]["type"] = "JPG";
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jHSM["header"]["video"] = new Newtonsoft.Json.Linq.JObject();
								jHSM["header"]["video"]["url"] = fileUrl;
								jHSM["header"]["video"]["type"] = "MP4";
								break;
							default:
								break;
						}
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						throw new NotImplementedException("No está implementado el envío de ubicaciones para HSM");
					}

					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
						jHSM["bodyParameters"] = new Newtonsoft.Json.Linq.JArray(sendDefinition.Parameters.Select(p => p.Value));

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						var jButtons = new Newtonsoft.Json.Linq.JArray();
						jHSM["buttons"] = jButtons;
						for (var i = 0; i < sendDefinition.Buttons.Length; i++)
						{
							var button = sendDefinition.Buttons[i];

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
								sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
								button.QuickReplyParameter != null)
							{
								var jButton = new Newtonsoft.Json.Linq.JObject();
								jButton["type"] = "QUICK_REPLY";
								//jButton["replyPayload"] = button.QuickReplyParameter.Value;

								var buttonPayload = GenerateButtonPayload(button.QuickReplyParameter.Value, message);
								jButton["replyPayload"] = JsonConvert.SerializeObject(buttonPayload);
								jButtons.Add(jButton);
							}

							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
									  sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
									  button.CallToActionButtonType != null)
							{
								switch (button.CallToActionButtonType.Value)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											var jButton = new Newtonsoft.Json.Linq.JObject();
											jButton["type"] = "URL";
											jButton["url"] = button.UrlParameter.Value;
											jButtons.Add(jButton);
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										//No esta implementado por el broker
										throw new NotImplementedException();
									default:
										break;
								}
							}
						}
					}
				}
				else
				{
					var jHSM = new JObject();
					jMessageInfo["hsm"] = jHSM;
					jHSM["elementName"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jHSM["namespace"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
						jHSM["parameters"] = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
					else
						jHSM["parameters"] = null;
					jHSM["languageCode"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];
				}
			}
			else
			{
				if (jDataMessage["attach"] != null && jDataMessage["attach"].Type == JTokenType.Object)
				{
					var page = data["account"].ToString();

					var jAttach = (JObject) jDataMessage["attach"];

					var sendAsAttach = true;
					if (jAttach["type"] != null && jAttach["type"].Type == JTokenType.String)
					{
						string type = jAttach["type"].ToString();
						if (type.Equals("location"))
						{
							sendAsAttach = false;
							jMessageInfo["location"] = new Newtonsoft.Json.Linq.JObject();
							jMessageInfo["location"]["geoPoint"] = string.Format("{0},{1}", jAttach["latitude"].ToObject<double>().ToString(NumberFormatInfo.InvariantInfo), jAttach["longitude"].ToObject<double>().ToString(NumberFormatInfo.InvariantInfo));
							if (jAttach["name"] != null && jAttach["name"].Type == JTokenType.String)
								jMessageInfo["location"]["name"] = jAttach["name"].ToString();
							if (jAttach["address"] != null && jAttach["address"].Type == JTokenType.String)
								jMessageInfo["location"]["address"] = jAttach["address"].ToString();

							Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a wavy con una ubicación: {0}", jMessage.ToString());
						}
					}

					if (sendAsAttach)
					{
						string fileUrl;
						if (jAttach["isPublicUrl"] != null &&
							jAttach["isPublicUrl"].Type == JTokenType.Boolean &&
							jAttach["isPublicUrl"].ToObject<bool>() &&
							jAttach["url"] != null &&
							jAttach["url"].Type == JTokenType.String)
						{
							fileUrl = jAttach["url"].ToString();
						}
						else
						{
							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, data);
						}

						var type = DomainModel.Attachment.AttachmentType.File;
						var mimeType = jAttach["mimeType"].ToString();
						try
						{
							type = (DomainModel.Attachment.AttachmentType) jAttach["type"].ToObject<short>();

							if (type == Attachment.AttachmentType.Image &&
								mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
							{
								type = Attachment.AttachmentType.Sticker;
							}
						}
						catch
						{
							if (mimeType.StartsWith("image"))
							{
								type = Attachment.AttachmentType.Image;

								if (mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
								{
									type = Attachment.AttachmentType.Sticker;
								}
							}
							else if (mimeType.StartsWith("audio"))
							{
								type = DomainModel.Attachment.AttachmentType.Audio;
							}
							else if (mimeType.StartsWith("video"))
							{
								type = DomainModel.Attachment.AttachmentType.Video;
							}
						}

						var jMessageAttach = new Newtonsoft.Json.Linq.JObject();
						jMessageAttach["url"] = fileUrl;

						switch (type)
						{
							case DomainModel.Attachment.AttachmentType.Image:
								if (jDataMessage["body"] != null && jDataMessage["body"].Type != JTokenType.Null)
									jMessageAttach["caption"] = jDataMessage["body"].ToString();

								if (mimeType.Equals("image/png", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "PNG";
								else
									jMessageAttach["type"] = "JPG";

								jMessageInfo["image"] = jMessageAttach;
								break;
							case DomainModel.Attachment.AttachmentType.Sticker:
								jMessageInfo["sticker"] = jMessageAttach;
								break;
							case DomainModel.Attachment.AttachmentType.Audio:
								if (mimeType.Equals("audio/aac", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "AAC";
								else if (mimeType.Equals("audio/mp4", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "MP4";
								else if (mimeType.Equals("audio/amr", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "AMR";
								else if (mimeType.Equals("audio/mpeg", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "MP3";
								else if (mimeType.Equals("audio/ogg", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "OGG";
								else
									jMessageAttach["type"] = "MP3";

								jMessageInfo["audio"] = jMessageAttach;
								break;
							case DomainModel.Attachment.AttachmentType.Video:
								jMessageAttach["caption"] = jAttach["name"].ToString();
								jMessageAttach["type"] = "MP4";
								jMessageInfo["video"] = jMessageAttach;
								break;
							default:
								jMessageAttach["caption"] = jAttach["name"].ToString();

								if (mimeType.Equals("application/pdf", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "PDF";
								else if (mimeType.Equals("application/msword", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "DOC";
								else if (mimeType.Equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "DOCX";
								else if (mimeType.Equals("application/vnd.ms-powerpoint", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "PPT";
								else if (mimeType.Equals("application/vnd.openxmlformats-officedocument.presentationml.presentation", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "PPTX";
								else if (mimeType.Equals("application/vnd.ms-excel", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "XLS";
								else if (mimeType.Equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "XLSX";
								else if (mimeType.Equals("text/plain", StringComparison.InvariantCultureIgnoreCase))
									jMessageAttach["type"] = "TXT";

								jMessageInfo["document"] = jMessageAttach;
								break;
						}

						Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a wavy con un archivo adjunto: {0}", jMessage.ToString());
					}
				}
				else if (jDataMessage["type"] != null &&
					jDataMessage["type"].Type == JTokenType.String &&
					jDataMessage["type"].ToString().Equals("interactive"))
				{
					var jInteractive = (JObject) jDataMessage["interactive"];
					var interactiveType = jInteractive["type"].ToString();
					if (interactiveType.Equals("button"))
					{
						var jMessageInfoInteractive = new Newtonsoft.Json.Linq.JObject();
						jMessageInfo["interactive"] = jMessageInfoInteractive;
						jMessageInfoInteractive["messageInteractiveType"] = "REPLY_BUTTON";

						var jWhatsapp = jInteractive;

						if (jWhatsapp["header"] != null &&
							jWhatsapp["header"].Type == JTokenType.Object)
						{
							jMessageInfoInteractive["header"] = new Newtonsoft.Json.Linq.JObject();

							var jHeader = (JObject) jWhatsapp["header"];
							var type = jHeader["type"].ToString();
							switch (type)
							{
								case "image":
									jMessageInfoInteractive["header"]["image"] = new Newtonsoft.Json.Linq.JObject();
									jMessageInfoInteractive["header"]["image"]["type"] = "JPG";
									jMessageInfoInteractive["header"]["image"]["url"] = jHeader["link"];
									break;
								case "video":
									jMessageInfoInteractive["header"]["video"] = new Newtonsoft.Json.Linq.JObject();
									jMessageInfoInteractive["header"]["video"]["type"] = "MP4";
									jMessageInfoInteractive["header"]["video"]["url"] = jHeader["link"];
									break;
								case "document":
									jMessageInfoInteractive["header"]["document"] = new Newtonsoft.Json.Linq.JObject();
									jMessageInfoInteractive["header"]["document"]["type"] = "PDF";
									jMessageInfoInteractive["header"]["document"]["url"] = jHeader["link"];
									break;
								case "text":
									jMessageInfoInteractive["header"]["text"] = jHeader["text"].ToString();
									break;
								default:
									break;
							}
						}

						jMessageInfoInteractive["body"] = jWhatsapp["body"];

						if (jWhatsapp["footer"] != null && jWhatsapp["footer"].Type == JTokenType.Object)
							jMessageInfoInteractive["footer"] = jWhatsapp["footer"];

						var jButtons = (JArray) jWhatsapp["action"]["buttons"];
						jMessageInfoInteractive["replyButtonAction"] = new Newtonsoft.Json.Linq.JObject();

						var jMessageInfoInteractiveButtons = new Newtonsoft.Json.Linq.JArray();
						jMessageInfoInteractive["replyButtonAction"]["buttons"] = jMessageInfoInteractiveButtons;

						foreach (JObject jButton in jButtons)
						{
							var type = jButton["type"].ToString();
							switch (type)
							{
								case "reply":
									var jMessageInfoInteractiveButton = new Newtonsoft.Json.Linq.JObject();
									jMessageInfoInteractiveButtons.Add(jMessageInfoInteractiveButton);
									jMessageInfoInteractiveButton["reply"] = new Newtonsoft.Json.Linq.JObject();
									jMessageInfoInteractiveButton["reply"]["payload"] = jButton["reply"]["id"].ToString();
									jMessageInfoInteractiveButton["reply"]["title"] = jButton["reply"]["title"].ToString();
									break;
								default:
									break;
							}
						}
					}
					else if (interactiveType.Equals("list"))
					{
						var jMessageInfoInteractive = new Newtonsoft.Json.Linq.JObject();
						jMessageInfo["interactive"] = jMessageInfoInteractive;
						jMessageInfoInteractive["messageInteractiveType"] = "LIST";

						var jWhatsapp = jInteractive;

						jMessageInfoInteractive["body"] = jWhatsapp["body"];
						jMessageInfoInteractive["listAction"] = new Newtonsoft.Json.Linq.JObject();
						jMessageInfoInteractive["listAction"]["button"] = jWhatsapp["action"]["button"];
						jMessageInfoInteractive["listAction"]["sections"] = jWhatsapp["action"]["sections"];

						foreach (Newtonsoft.Json.Linq.JObject jSection in (Newtonsoft.Json.Linq.JArray) jMessageInfoInteractive["listAction"]["sections"])
						{
							foreach (Newtonsoft.Json.Linq.JObject jRow in (Newtonsoft.Json.Linq.JArray) jSection["rows"])
							{
								jRow["identifier"] = jRow["id"];
								jRow.Remove("id");
							}
						}
					}
					else
					{
						throw new NotSupportedException("El tipo de mensaje interactivo no es soportado");
					}

					Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a wavy con un mensaje interactivo y datos: {0}", jMessage.ToString());
				}
				else
				{
					var body = jDataMessage["body"].ToString();
					jMessageInfo["messageText"] = body;

					//if (body.IndexOf("https://") >= 0)
					//	jMessageInfo["previewFirstUrl"] = true;
				}
			}

			string url = "https://api-messaging.wavy.global/v1/whatsapp/send";

			try
			{
				using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, url))
				{
					requestMessage.Headers.Add("UserName", data["integrationType5Username"].ToString());
					requestMessage.Headers.Add("AuthenticationToken", data["integrationType5Token"].ToString());
					requestMessage.Content = new StringContent(jMessage.ToString(), global::System.Text.Encoding.UTF8, "application/json");

					if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						this.PublishToServiceBus != null)
					{
						var uuid = new ShortGuid(Guid.NewGuid());

						var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
						{
							HttpRequestMessage = requestMessage,
							Message = message,
							ServiceId = this.ID,
							Uuid = uuid,
							SocialUserId = data["number"].ToString(),
							Delay = null,
							IsTemplate = isHSM
						});

						if (published)
							return uuid;
					}

					using (var response = await this.client.SendAsync(requestMessage))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = JObject.Parse(jsonResponse);
								var jResponseDestinations = (JArray) jResponse["destinations"];
								replyId = jResponseDestinations[0]["id"].ToString();
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0}", replyId);

								return replyId;
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									string json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}", ex);
				throw;
			}
		}

		private async Task<bool> FinishProcessingNews()
		{
			var url = this.ServiceConfiguration.IntegrationType3PostNewsProcessedEndpoint.GetUrl(this.postbackParameters);

			var now = DateTime.Now;
			if (url.IndexOf("?") == -1)
			{
				url += $"?ts={now.Ticks}";
			}
			else
			{
				url += $"&ts={now.Ticks}";
			}

			return await base.FinishProcessingNews(this.client, url, (HttpRequestMessage request) =>
			{
				var hashKey = this.ServiceConfiguration.IntegrationType3PostNewsProcessedEndpoint.HashKey;
				var hashHmacHex = HashHMACHex(hashKey, new Uri(url).Query);
				request.Headers.Add("hash", hashHmacHex);

				this.ServiceConfiguration.IntegrationType3PostNewsProcessedEndpoint.ApplyHeaders(request, this.postbackParameters);
			});
		}

		/// <summary>
		/// Extrae la información que retornó yFlow para un mensaje y forma un <see cref="JObject"/> con los datos para enviar
		/// </summary>
		/// <param name="jReplyInfo">La información que retornó yFlow para un mensaje</param>
		/// <param name="message">El <see cref="DomainModel.Message"/> al cual pertenece la información de respuesta</param>
		/// <returns>Un <see cref="JObject"/> con los datos preparados</returns>
		private async Task<JObject> CreateMessageFromReplyToken(JObject jReplyInfo, DomainModel.Message message)
		{
			var jMessage = new Newtonsoft.Json.Linq.JObject();

			var integrationType = this.ServiceConfiguration.ServiceIntegrationType;
			jMessage["id"] = message.ID.ToString();

			if (jReplyInfo["text"] != null)
			{
				jMessage["body"] = jReplyInfo["text"].ToString();
				jMessage["type"] = "text";
			}
			else if (jReplyInfo["attachment"] != null)
			{
				jMessage["body"] = string.Empty;
				jMessage["type"] = "attach";

				var attachType = jReplyInfo["attachment"]["type"].ToString();

				if (attachType.Equals("location"))
				{
					if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback &&
						!this.ServiceConfiguration.IntegrationType3UseWhatsappFormat)
					{
						jMessage["type"] = "location";
						jMessage["latitude"] = jReplyInfo["attachment"]["latitude"].ToObject<double>();
						jMessage["longitude"] = jReplyInfo["attachment"]["longitude"].ToObject<double>();

						if (jReplyInfo["attachment"]["name"] != null && jReplyInfo["attachment"]["name"].Type == JTokenType.String)
							jMessage["name"] = jReplyInfo["attachment"]["name"].ToString();
						if (jReplyInfo["attachment"]["address"] != null && jReplyInfo["attachment"]["address"].Type == JTokenType.String)
							jMessage["address"] = jReplyInfo["attachment"]["address"].ToString();
					}
					else
					{
						var jAttach = new Newtonsoft.Json.Linq.JObject();
						jMessage["attach"] = jAttach;

						jAttach["type"] = "location";
						jAttach["latitude"] = jReplyInfo["attachment"]["latitude"].ToObject<double>();
						jAttach["longitude"] = jReplyInfo["attachment"]["longitude"].ToObject<double>();

						if (jReplyInfo["attachment"]["name"] != null && jReplyInfo["attachment"]["name"].Type == JTokenType.String)
							jAttach["name"] = jReplyInfo["attachment"]["name"].ToString();
						if (jReplyInfo["attachment"]["address"] != null && jReplyInfo["attachment"]["address"].Type == JTokenType.String)
							jAttach["address"] = jReplyInfo["attachment"]["address"].ToString();
					}
				}
				else
				{
					var jAttach = new Newtonsoft.Json.Linq.JObject();
					jMessage["attach"] = jAttach;

					if (attachType.Equals("url") &&
						jReplyInfo["attachment"]["isPublicUrl"] != null &&
						jReplyInfo["attachment"]["isPublicUrl"].Type == JTokenType.Boolean &&
						jReplyInfo["attachment"]["isPublicUrl"].ToObject<bool>())
					{
						var name = jReplyInfo["attachment"]["name"].ToString();
						var mimeType = jReplyInfo["attachment"]["mimeType"].ToString();
						if (!Path.HasExtension(name))
						{
							name = string.Format("{0}{1}", name, DomainModel.MimeTypeMap.GetExtension(mimeType));
						}
						jAttach["name"] = name;
						jAttach["mimeType"] = mimeType;
						jAttach["url"] = jReplyInfo["attachment"]["url"].ToString();
						jAttach["isPublicUrl"] = true;
					}
					else
					{
						YFlow.YFlowResponse tempFileResponse = null;
						if (attachType.Equals("url"))
						{
							var urlToInvoke = jReplyInfo["attachment"]["url"].ToString();

							try
							{
								var downloadedContents = await DownloadFile(urlToInvoke, this.client);
								tempFileResponse = YFlow.YFlowResponse.CreateSuccess(downloadedContents.Content);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Ocurrió un error descargando el archivo adjunto desde la url {0}: {1}", urlToInvoke, ex);
								throw new ReplyException("No se pudo recuperar el archivo adjunto desde la url", false);
							}
						}
						else if (attachType.Equals("file") ||
							attachType.Equals("sticker"))
						{
							tempFileResponse = await YFlow.Manager.Instance.GetTempFile(message.Service.YFlow.Value, jReplyInfo["attachment"]["filename"].ToString(), message.Service.SocialServiceType);
							if (!tempFileResponse.Succeed)
							{
								Tracer.TraceError("Ocurrió un error descargando el archivo adjunto desde yFlow: {0}", tempFileResponse.Exception);
								throw new ReplyException("No se pudo recuperar el archivo adjunto desde yFlow", false);
							}
						}

						if (tempFileResponse != null)
						{
							var name = jReplyInfo["attachment"]["name"].ToString();
							var mimeType = jReplyInfo["attachment"]["mimeType"].ToString();
							if (!Path.HasExtension(name))
							{
								name = string.Format("{0}{1}", name, DomainModel.MimeTypeMap.GetExtension(mimeType));
							}
							jAttach["name"] = name;
							jAttach["mimeType"] = mimeType;
							jAttach["size"] = tempFileResponse.RawContent.Length;

							var jUploadData = new Newtonsoft.Json.Linq.JObject();
							jUploadData["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jUploadData["msg"] = new Newtonsoft.Json.Linq.JObject();
							jUploadData["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jUploadData["msg"]["attach"]["name"] = name;
							jUploadData["msg"]["attach"]["mimeType"] = mimeType;
							jUploadData["msg"]["attach"]["size"] = tempFileResponse.RawContent.Length;
							jUploadData["msg"]["attach"]["data"] = Convert.ToBase64String(tempFileResponse.RawContent);

							try
							{
								jAttach["url"] = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jUploadData);
								jAttach["isPublicUrl"] = true;
							}
							catch (Exception ex)
							{
								Tracer.TraceError("No se pudo subir el archivo de yFlow al repositorio de archivos: {0}", ex);
								throw new ReplyException("No se puede enviar el mensaje porque no se pudo subir el archivo de yFlow al repositorio de archivos", false);
							}
						}
						else
						{
							Tracer.TraceError("No se puede enviar el mensaje porque no hay información del archivo adjunto");
							throw new ReplyException("No se puede enviar el mensaje porque no hay información del archivo adjunto", false);
						}
					}

					if (jAttach != null)
					{
						var attachmentType = DomainModel.Attachment.AttachmentType.File;
						if (jAttach["mimeType"] != null && jAttach["mimeType"].Type == JTokenType.String)
						{
							var mimeType = jAttach["mimeType"].ToString();
							if (!string.IsNullOrEmpty(mimeType))
							{
								if (mimeType.StartsWith("image/"))
								{
									attachmentType = DomainModel.Attachment.AttachmentType.Image;

									if (mimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
									{
										attachmentType = Attachment.AttachmentType.Sticker;
									}
								}
								else if (mimeType.StartsWith("audio/"))
								{
									attachmentType = DomainModel.Attachment.AttachmentType.Audio;
								}
								else if (mimeType.StartsWith("video/"))
								{
									attachmentType = DomainModel.Attachment.AttachmentType.Video;
								}
							}
						}

						jAttach["type"] = (short) attachmentType;
					}
				}
			}
			else if (jReplyInfo["interactive"] != null)
			{
				jMessage["type"] = "interactive";
				jMessage["interactive"] = jReplyInfo["interactive"];

				if (jMessage["interactive"]["header"] != null &&
					jMessage["interactive"]["header"].Type == JTokenType.Object)
				{
					var jHeader = (Newtonsoft.Json.Linq.JObject) jMessage["interactive"]["header"];
					if (jHeader["type"] != null &&
						jHeader["type"].Type == JTokenType.String)
					{
						var type = jHeader["type"].ToString();
						if ((type.Equals("image") ||
							type.Equals("video") ||
							type.Equals("document")) &&
							jHeader[type] != null &&
							jHeader[type].Type == JTokenType.Object)
						{
							var jHeaderMedia = (JObject) jHeader[type];

							// TODO: ver de subir los archivos
							if (jHeaderMedia["type"] != null &&
								jHeaderMedia["type"].Type == JTokenType.String &&
								jHeaderMedia["type"].ToString().Equals("binary"))
							{
								var tempFileResponse = await YFlow.Manager.Instance.GetTempFile(message.Service.YFlow.Value, jHeaderMedia["download"].ToString(), message.Service.SocialServiceType);
								if (!tempFileResponse.Succeed)
								{
									Tracer.TraceError("Ocurrió un error descargando el archivo adjunto desde yFlow: {0}", tempFileResponse.Exception);
									throw new ReplyException("No se pudo recuperar el archivo adjunto desde yFlow", false);
								}

								var jFileUpload = new Newtonsoft.Json.Linq.JObject();
								jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
								jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
								jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
								jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(tempFileResponse.RawContent);
								jFileUpload["msg"]["attach"]["mimeType"] = tempFileResponse.ResponseHeaders[HttpRequestHeader.ContentType];

								jFileUpload["msg"]["attach"]["name"] = "file";
								var contentDisposition = tempFileResponse.ResponseHeaders["Content-Disposition"];
								if (!string.IsNullOrEmpty(contentDisposition))
								{
									if (contentDisposition.StartsWith("attachment", StringComparison.InvariantCultureIgnoreCase))
									{
										try
										{
											contentDisposition = contentDisposition.Substring(contentDisposition.IndexOf("filename=", 0, StringComparison.InvariantCultureIgnoreCase) + 9);
											contentDisposition = contentDisposition.Replace("\"", string.Empty);
											jFileUpload["msg"]["attach"]["name"] = contentDisposition;
										}
										catch { }
									}
								}

								jHeaderMedia["link"] = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
								jHeaderMedia.Remove("type");
								jHeaderMedia.Remove("download");
							}
							else if (jHeaderMedia["isPublicUrl"] != null &&
								jHeaderMedia["isPublicUrl"].Type == JTokenType.Boolean &&
								!jHeaderMedia["isPublicUrl"].ToObject<bool>())
							{
								var jFileUpload = new Newtonsoft.Json.Linq.JObject();
								jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
								jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
								jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

								var fileContents = await DownloadFile(jHeaderMedia["link"].ToString());

								if (string.IsNullOrEmpty(fileContents.ContentDisposition))
								{
									fileContents.ContentDisposition = "file";
									try
									{
										var uri = new Uri(jHeaderMedia["link"].ToString());
										fileContents.ContentDisposition = System.IO.Path.GetFileName(uri.LocalPath);
									}
									catch { }

									jFileUpload["msg"]["attach"]["name"] = fileContents.ContentDisposition;
								}
								else
								{
									if (fileContents.ContentDisposition.StartsWith("attachment", StringComparison.InvariantCultureIgnoreCase))
									{
										try
										{
											fileContents.ContentDisposition = fileContents.ContentDisposition.Substring(fileContents.ContentDisposition.IndexOf("filename=", 0, StringComparison.InvariantCultureIgnoreCase) + 9);
											fileContents.ContentDisposition = fileContents.ContentDisposition.Replace("\"", string.Empty);
											jFileUpload["msg"]["attach"]["name"] = fileContents.ContentDisposition;
										}
										catch { }
									}
								}

								jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
								jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;

								jHeaderMedia["link"] = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
								jHeaderMedia.Remove("isPublicUrl");
							}
						}
					}
				}
			}

			return jMessage;
		}

		private async Task<string> SendMessage(JObject jMessage, DomainModel.Message message)
		{
			string replyId = null;
			var integrationType = this.ServiceConfiguration.ServiceIntegrationType;
			var jData = new Newtonsoft.Json.Linq.JObject();
			jData["account"] = this.ServiceConfiguration.FullPhoneNumber;
			
			if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker)
			{
				jData["integrationType2AccessToken"] = this.ServiceConfiguration.IntegrationType2AccessToken;
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;

					var @namespace = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					var elementName = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					var language = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];
					jDataParameters["templateName"] = elementName;
					jDataParameters["templateNamespace"] = @namespace;
					jDataParameters["language"] = language;

					jDataParameters["templateData"] = null;
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
					{
						var template = this.ServiceSettings.FindTemplate(@namespace, elementName, language);
						if (template != null)
						{
							var templateParameters = template.TemplateParameters;
							var jHSMTemplateData = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);

							if (jHSMTemplateData.Count > 0)
							{
								if (jHSMTemplateData.Count == templateParameters.Length)
								{
									var jTemplateData = new Newtonsoft.Json.Linq.JObject();
									jDataParameters["templateData"] = jTemplateData;
									for (var i = 0; i < templateParameters.Length; i++)
									{
										jTemplateData[templateParameters[i].Name] = jHSMTemplateData[i].ToString();
									}
								}
								else
								{
									throw new Exception("Los parámetros proporcionados al HSM no coinciden con su definición");
								}
							}
						}
						else
						{
							throw new Exception("No se puede enviar el mensaje porque no se encontró el template");
						}
					}
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip)
			{
				jData["integrationType4Authorization"] = Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", this.ServiceConfiguration.IntegrationType4User, this.ServiceConfiguration.IntegrationType4Password)));
				jData["integrationType4BaseUrl"] = this.ServiceConfiguration.IntegrationType4BaseUrl;
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;
					jDataParameters["templateName"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jDataParameters["templateNamespace"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					jDataParameters["language"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
						jDataParameters["templateData"] = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
					else
						jDataParameters["templateData"] = new Newtonsoft.Json.Linq.JArray();
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy)
			{
				jData["integrationType5Username"] = this.ServiceConfiguration.IntegrationType5Username;
				jData["integrationType5Token"] = this.ServiceConfiguration.IntegrationType5Token;
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;
					jDataParameters["templateName"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jDataParameters["templateNamespace"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					jDataParameters["language"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
						jDataParameters["templateData"] = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar)
			{
				jData["integrationType6ClientId"] = this.ServiceConfiguration.IntegrationType6ClientID;
				jData["integrationType6BaseUrl"] = this.ServiceConfiguration.IntegrationType6UrlBase;
				jData["integrationType6ClientSecret"] = this.ServiceConfiguration.IntegrationType6ClientSecret;
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;
					jDataParameters["templateName"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jDataParameters["templateNamespace"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					jDataParameters["language"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
						jDataParameters["templateData"] = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
					else
						jDataParameters["templateData"] = new Newtonsoft.Json.Linq.JArray();
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa ||
				integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen ||
				integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi ||
				(integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback && this.ServiceConfiguration.IntegrationType3UseWhatsappFormat))
			{
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;

					var @namespace = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					var elementName = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					var language = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];
					jDataParameters["templateName"] = elementName;
					jDataParameters["templateNamespace"] = @namespace;
					jDataParameters["language"] = language;

					jDataParameters["templateData"] = null;
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
					{
						var template = this.ServiceSettings.FindTemplate(@namespace, elementName, language);
						if (template != null)
						{
							var templateParameters = template.TemplateParameters;
							var jHSMTemplateData = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);

							if (jHSMTemplateData.Count > 0)
							{
								if (jHSMTemplateData.Count == templateParameters.Length)
								{
									var jTemplateData = new Newtonsoft.Json.Linq.JArray();
									jDataParameters["templateData"] = jTemplateData;
									for (var i = 0; i < templateParameters.Length; i++)
									{
										var jParameter = new Newtonsoft.Json.Linq.JObject();
										jParameter["default"] = jHSMTemplateData[i].ToString();

										jTemplateData.Add(jParameter);
									}
								}
								else
								{
									throw new Exception("Los parámetros proporcionados al HSM no coinciden con su definición");
								}
							}
						}
						else
						{
							throw new Exception("No se puede enviar el mensaje porque no se encontró el template");
						}
					}
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup)
			{
				jData["parameters"] = null;

				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jDataParameters = new Newtonsoft.Json.Linq.JObject();
					jData["parameters"] = jDataParameters;

					var @namespace = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					var elementName = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					var language = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];
					jDataParameters["templateName"] = elementName;
					jDataParameters["templateNamespace"] = @namespace;
					jDataParameters["language"] = language;

					jDataParameters["templateData"] = null;
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
					{
						var template = this.ServiceSettings.FindTemplate(@namespace, elementName, language);
						if (template != null)
						{
							var templateParameters = template.TemplateParameters;
							var jHSMTemplateData = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);

							if (jHSMTemplateData.Count > 0)
							{
								if (jHSMTemplateData.Count == templateParameters.Length)
								{
									var jTemplateData = new Newtonsoft.Json.Linq.JArray();
									jDataParameters["templateData"] = jTemplateData;
									for (var i = 0; i < templateParameters.Length; i++)
									{
										var jParameter = new Newtonsoft.Json.Linq.JObject();
										jParameter["default"] = jHSMTemplateData[i].ToString();

										jTemplateData.Add(jParameter);
									}
								}
								else
								{
									throw new Exception("Los parámetros proporcionados al HSM no coinciden con su definición");
								}
							}
						}
						else
						{
							throw new Exception("No se puede enviar el mensaje porque no se encontró el template");
						}
					}
				}
			}
			else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter))
				{
					var sendDefinition = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.HSMTemplateSendDefinition>(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter]);

					var jTemplate = new Newtonsoft.Json.Linq.JObject();
					jData["template"] = jTemplate;
					jTemplate["namespace"] = sendDefinition.Namespace;
					jTemplate["name"] = sendDefinition.ElementName;
					jTemplate["language"] = new Newtonsoft.Json.Linq.JObject();
					jTemplate["language"]["policy"] = "deterministic";
					jTemplate["language"]["code"] = sendDefinition.Language;

					var jComponents = new Newtonsoft.Json.Linq.JArray();
					jTemplate["components"] = jComponents;

					var jHeader = new Newtonsoft.Json.Linq.JObject();
					jHeader["type"] = "header";
					var jHeaderParameters = new Newtonsoft.Json.Linq.JArray();
					jHeader["parameters"] = jHeaderParameters;
					var jHeaderParameter = new Newtonsoft.Json.Linq.JObject();
					jHeaderParameters.Add(jHeaderParameter);

					if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text &&
						sendDefinition.HeaderTextParameter != null)
					{
						jHeaderParameter["type"] = "text";
						jHeaderParameter["text"] = sendDefinition.HeaderTextParameter.Value;
						jComponents.Add(jHeader);
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media)
					{
						string fileUrl = sendDefinition.HeaderMediaUrl;
						if (!string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl) &&
							!sendDefinition.HeaderMediaUrlIsPublic.Value)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();

							var fileContents = await DownloadFile(sendDefinition.HeaderMediaUrl);

							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(fileContents.Content);
							jFileUpload["msg"]["attach"]["mimeType"] = fileContents.ContentType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}
						else if (sendDefinition.HeaderMediaFromAttachment &&
							message.HasAttach)
						{
							var jFileUpload = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["account"] = this.ServiceConfiguration.FullPhoneNumber;
							jFileUpload["msg"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"] = new Newtonsoft.Json.Linq.JObject();
							jFileUpload["msg"]["attach"]["data"] = Convert.ToBase64String(message.Attachments[0].Data);
							jFileUpload["msg"]["attach"]["mimeType"] = message.Attachments[0].MimeType;
							jFileUpload["msg"]["attach"]["name"] = sendDefinition.HeaderMediaFileName;

							fileUrl = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jFileUpload);
						}

						switch (sendDefinition.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								jHeaderParameter["type"] = "document";
								var jDocument = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["document"] = jDocument;
								jDocument["filename"] = sendDefinition.HeaderMediaFileName;
								jDocument["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								jHeaderParameter["type"] = "image";
								var jImage = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["image"] = jImage;
								jImage["filename"] = sendDefinition.HeaderMediaFileName;
								jImage["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								jHeaderParameter["type"] = "video";
								var jVideo = new Newtonsoft.Json.Linq.JObject();
								jHeaderParameter["video"] = jVideo;
								jVideo["filename"] = sendDefinition.HeaderMediaFileName;
								jVideo["link"] = fileUrl;
								jComponents.Add(jHeader);
								break;
							default:
								break;
						}
					}
					else if (sendDefinition.HeaderType == DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location)
					{
						jHeaderParameter["type"] = "location";
						var jLocation = new Newtonsoft.Json.Linq.JObject();
						jHeaderParameter["location"] = jLocation;
						jLocation["latitude"] = sendDefinition.HeaderLocationLatitude;
						jLocation["longitude"] = sendDefinition.HeaderLocationLongitude;
						jComponents.Add(jHeader);
					}

					var jBody = new Newtonsoft.Json.Linq.JObject();
					jComponents.Add(jBody);
					jBody["type"] = "body";
					var jBodyParameters = new Newtonsoft.Json.Linq.JArray();
					jBody["parameters"] = jBodyParameters;

					if (sendDefinition.Parameters != null && sendDefinition.Parameters.Length > 0)
					{
						foreach (var parameter in sendDefinition.Parameters)
						{
							var jBodyParameter = new Newtonsoft.Json.Linq.JObject();
							jBodyParameters.Add(jBodyParameter);
							jBodyParameter["type"] = "text";
							jBodyParameter["text"] = parameter.Value;
						}
					}

					if (sendDefinition.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None &&
						sendDefinition.Buttons != null &&
						sendDefinition.Buttons.Length > 0)
					{
						for (var i = 0; i < sendDefinition.Buttons.Length; i++)
						{
							var button = sendDefinition.Buttons[i];
							var jButtonParameters = new Newtonsoft.Json.Linq.JArray();
							var jButton = new Newtonsoft.Json.Linq.JObject();
							var jButtonParameter = new Newtonsoft.Json.Linq.JObject();

							if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed ||
								sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply) &&
								button.QuickReplyParameter != null)
							{
								jButton["type"] = "button";
								jButton["sub_type"] = "quick_reply";
								jButton["index"] = i.ToString();
								jButton["parameters"] = jButtonParameters;

								jButtonParameters.Add(jButtonParameter);
								jButtonParameter["type"] = "payload";

								var buttonPayload = GenerateButtonPayload(button.QuickReplyParameter.Value, message);
								jButtonParameter["payload"] = JsonConvert.SerializeObject(buttonPayload);

								jComponents.Add(jButton);
							}

							else if ((sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed ||
									 sendDefinition.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction) &&
									 button.CallToActionButtonType != null)
							{
								switch (button.CallToActionButtonType)
								{
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
										if (button.UrlButtonType.Value == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic)
										{
											jButton["type"] = "button";
											jButton["sub_type"] = "url";
											jButton["index"] = i.ToString();

											jButton["parameters"] = jButtonParameters;
											jButtonParameters.Add(jButtonParameter);
											jButtonParameter["type"] = "text";
											jButtonParameter["text"] = button.UrlParameter.Value;
											jComponents.Add(jButton);

										}
										break;
									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
										jButton["type"] = "button";
										jButton["sub_type"] = "copy_code";
										jButton["index"] = i.ToString();

										jButton["parameters"] = jButtonParameters;
										jButtonParameters.Add(jButtonParameter);
										jButtonParameter["type"] = "coupon_code";
										jButtonParameter["coupon_code"] = button.OfferCodeParameter.Value;
										jComponents.Add(jButton);
										break;

									case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow:
										jButton["type"] = "button";
										jButton["sub_type"] = "flow";
										jButton["index"] = i.ToString();

										var jButtonAction = new JObject();
										if (button.FlowParameter.ActionData != null)
										{
											jButtonAction["flow_token"] = button.FlowParameter.ActionData.FlowToken;
											jButtonAction["flow_action_data"] = button.FlowParameter.ActionData.Data.ToString();
										}

										jButton["parameters"] = jButtonParameters;
										jButtonParameters.Add(jButtonParameter);
										jButtonParameter["type"] = "action";
										jButtonParameter["action"] = jButtonAction;
										jComponents.Add(jButton);
										break;
									default:
										break;
								}
							}
						}
					}
				}
				else if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter) &&
					message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage))
				{
					var jTemplate = new Newtonsoft.Json.Linq.JObject();
					jData["template"] = jTemplate;
					jTemplate["namespace"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter];
					jTemplate["name"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter];
					jTemplate["language"] = new Newtonsoft.Json.Linq.JObject();
					jTemplate["language"]["policy"] = "deterministic";
					jTemplate["language"]["code"] = message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage];

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter))
					{
						var jParameters = Newtonsoft.Json.Linq.JArray.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateDataParameter]);
						if (jParameters.Count > 0)
						{
							var jComponents = new Newtonsoft.Json.Linq.JArray();
							jTemplate["components"] = jComponents;

							var jBody = new Newtonsoft.Json.Linq.JObject();
							jComponents.Add(jBody);
							jBody["type"] = "body";
							var jBodyParameters = new Newtonsoft.Json.Linq.JArray();
							jBody["parameters"] = jBodyParameters;

							foreach (var parameter in jParameters)
							{
								var jBodyParameter = new Newtonsoft.Json.Linq.JObject();
								jBodyParameters.Add(jBodyParameter);
								jBodyParameter["type"] = "text";
								jBodyParameter["text"] = parameter.ToString();
							}
						}
					}
				}
			}

			try
			{
				if (message.RepliesTo != null)
				{
					jData["chat"] = message.RepliesTo.SocialConversationID;
					jData["replyId"] = message.RepliesTo.SocialMessageID;
				}
				else if (message.RepliesToSocialUser != null)
				{
					jData["chat"] = message.RepliesToSocialUser.ParametersByService[this.ID][Social.WhatsApp.User.ChatIDParameter];
				}
			}
			catch
			{
				jData["chat"] = string.Format("{0}@c.us", message.SocialUser.ID);
			}

			if (message.RepliesToSocialUser != null)
				jData["number"] = message.RepliesToSocialUser.IDStr;
			else if (message.SocialUser != null)
				jData["number"] = message.SocialUser.IDStr;
			jData["msg"] = jMessage;

			if (jData["replyId"] == null && !string.IsNullOrEmpty(message.RepliesToSocialMessageID))
				jData["replyId"] = message.RepliesToSocialMessageID;

			if (integrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback ||
				this.ServiceConfiguration.IntegrationType3UseWhatsappFormat)
			{
				try
				{
					if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker)
						replyId = await this.PostToBotMaker(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip)
						replyId = await this.PostToInfobip(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy)
						replyId = await this.PostToWavy(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar)
						replyId = await this.PostToMovistar(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa)
						replyId = await this.PostToInteraxa(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup)
						replyId = await this.PostToGupshup(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio)
						replyId = await this.PostToTwilio(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen)
						replyId = await this.PostToYoizen(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi)
						replyId = await this.PostToCloudApi(jData, message);
					else if (integrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback)
						replyId = await this.PostToPostback(jData, message);
				}
				catch (Exception ex)
				{
					Tracer.TraceError("WhatsApp {0}: Falló al responder mensaje de WhatsApp: {1}", this.ServiceConfiguration.FullPhoneNumber, ex);
					throw new ReplyException(string.Format("Falló el envío del mensaje de WhatsApp. Resultado: {0}", ex), false);
				}
			}
			else
			{
				var parameters = new Dictionary<string, string>();
				parameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
				parameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();
				var url = this.ServiceConfiguration.IntegrationType3ReplyEndpoint.GetUrl(parameters);

				try
				{
					using (var request = new HttpRequestMessage(HttpMethod.Post, url))
					{
						var body = jData.ToString();
						request.Content = new StringContent(body, global::System.Text.Encoding.UTF8, "application/json");

						var keyForHashing = this.ServiceConfiguration.IntegrationType3ReplyEndpoint.HashKey;
						this.ServiceConfiguration.IntegrationType3ReplyEndpoint.ApplyHeaders(request, this.postbackParameters);

						var hashHmacHex = HashHMACHex(keyForHashing, body);
						request.Headers.Add("hash", hashHmacHex);

						if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
							this.PublishToServiceBus != null)
						{
							var uuid = new ShortGuid(Guid.NewGuid());

							var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
							{
								HttpRequestMessage = request,
								Message = message,
								ServiceId = this.ID,
								Uuid = uuid,
								SocialUserId = jData["number"].ToString(),
								Delay = null
							});

							if (published)
								return uuid;
						}

						using (var response = await this.client.SendAsync(request))
						{
							if (response.IsSuccessStatusCode)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var jsonResponse = await sr.ReadToEndAsync();
									try
									{
										var jResponse = JObject.Parse(jsonResponse);
										replyId = jResponse["id"].ToString();
										Tracer.TraceInfo("WhatsApp {0}: Se respodió exitosamente el mensaje de WhatsApp {1} generando el mensaje con código {2}", this.ServiceConfiguration.FullPhoneNumber, message, replyId);
									}
									catch
									{
										Tracer.TraceInfo("WhatsApp {0}: Se respodió exitosamente el mensaje de WhatsApp {1}", this.ServiceConfiguration.FullPhoneNumber, message);

										if (string.IsNullOrEmpty(replyId))
										{
											if (message.RepliesTo != null)
												replyId = string.Format("{0}_reply", message.RepliesTo.SocialMessageID);
											else
												replyId = string.Format("{0}_reply", Guid.NewGuid());
										}
									}

									return replyId;
								}
							}
							else
							{
								if (response.Content != null)
								{
									using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
									{
										var json = await sr.ReadToEndAsync();
										Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);
									}
								}
								else
								{
									Yoizen.Common.Tracer.TraceError("Falló al responder mensaje de WhatsApp: {0}-{1}", response.ReasonPhrase, response.StatusCode);
								}

								throw new ReplyException("Falló el envío del mensaje de WhatsApp", false);
							}
						}
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("WhatsApp {0}: Falló al responder mensaje de WhatsApp: {1}", this.ServiceConfiguration.FullPhoneNumber, ex);
					throw new ReplyException("Falló el envío del mensaje de WhatsApp", false);
				}
			}

			if (string.IsNullOrEmpty(replyId))
			{
				if (message.RepliesTo != null)
					replyId = string.Format("{0}_reply", message.RepliesTo.SocialMessageID);
				else
					replyId = string.Format("{0}_reply", Guid.NewGuid());
			}

			return replyId;
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Realiza la invocación a Movistar para informar de un cierre de un caso
		/// </summary>
		/// <param name="socialConversation">El código de conversación que se está cerrado</param>
		/// <param name="socialUserId">El código de usuario</param>
		public async Task PostToMovistarHandOff(string socialConversation, long socialUserId, HandoffMotives motive, HandoffContext handoffContext)
		{
			await RefreshIntegrationType6AccessToken();

			if (string.IsNullOrEmpty(this.integrationType6AccessToken))
				throw new ServiceException("No se tiene un access token para poder enviar el mensaje");

			string replyId = Guid.NewGuid().ToString("D");

			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jMessage["type"] = "handoff";
			jMessage["serviceUrl"] = "https://ysocialmoviarcallback.azurewebsites.net/api/whatsapp/bf/5491159740925";
			jMessage["channelId"] = this.ServiceConfiguration.IntegrationType6ChannelID; //"wapp_b2b";

			var jRecipient = new Newtonsoft.Json.Linq.JObject();
			jMessage["recipient"] = jRecipient;
			jRecipient["id"] = socialUserId.ToString();

			var jConversation = new Newtonsoft.Json.Linq.JObject();
			jMessage["conversation"] = jConversation;
			jConversation["id"] = socialConversation;

			var jFrom = new Newtonsoft.Json.Linq.JObject();
			jMessage["from"] = jFrom;
			jFrom["id"] = this.ServiceConfiguration.IntegrationType6FromID; //"ce_wapp_b2b";
			jFrom["name"] = this.ServiceConfiguration.IntegrationType6FromName; //"Canal Escrito B2B";

			var jChannelData = new Newtonsoft.Json.Linq.JObject();
			jMessage["inputHint"] = "acceptingInput";
			jMessage["channelData"] = jChannelData;
			jMessage["id"] = replyId;
			jMessage["timestamp"] = DateTime.UtcNow.ToString("u");
			jMessage["localTimestamp"] = DateTime.Now.ToString("u");
			
			jChannelData["toChannelId"] = "bot";
			jChannelData["handoffMotive"] = motive.GetDescription();


			JsonSerializerSettings settings = new JsonSerializerSettings
			{
				NullValueHandling = NullValueHandling.Ignore
			};

			JObject jHandoffContext = JObject.FromObject(handoffContext, JsonSerializer.Create(settings));
			jChannelData["handoffContext"] = jHandoffContext;

			string baseUrl = this.ServiceConfiguration.IntegrationType6UrlBase;
			if (!baseUrl.EndsWith("/"))
				baseUrl += "/";
			string url = string.Format("{0}plataforma-bot/v1/conversations/{1}/activities", baseUrl, socialConversation);

			if (!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType6EndOfConversation))
			{
				url = string.Format("{0}{1}", baseUrl,
					this.ServiceConfiguration.IntegrationType6EndOfConversation
						.Replace("@@CONVERSATIONID@@", socialConversation)
						.Replace("@@CHANNELID@@", this.ServiceConfiguration.IntegrationType6ChannelID));
			}

			try
			{
				using (var request = new HttpRequestMessage(HttpMethod.Post, url))
				{
					request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
					request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", this.integrationType6AccessToken);
					request.Headers.Add("X-IBM-Client-Id", this.ServiceConfiguration.IntegrationType6ClientID);

				request.Content = new StringContent(jMessage.ToString(), global::System.Text.Encoding.UTF8, "application/json");

				Yoizen.Common.Tracer.TraceInfo("Se realizará un POST a movistar a la URL {0} de tipo HandOff para la conversación: {1} con body {2}", url, socialConversation, jMessage.ToString());

					using (var response = await this.client.SendAsync(request))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var jsonResponse = await sr.ReadToEndAsync();
								Yoizen.Common.Tracer.TraceInfo("Se envió el cierre de conversación exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Yoizen.Common.Tracer.TraceError("Falló al cerrar la conversación {0} de WhatsApp: {1}-{2} - {3}", socialConversation, response.ReasonPhrase, response.StatusCode, json);
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceError("Falló al cerrar la conversación {0} de WhatsApp: {1}-{2}", socialConversation, response.ReasonPhrase, response.StatusCode);
							}

							throw new Exception();
						}
					}
				}
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Falló al cerrar la conversación {0} de WhatsApp: {1}", socialConversation, ex);
				throw new Exception();
			}				
		}

		/// <summary>
		/// Realiza la invocación para notificar del cierre de un caso para el tipo de integración con Postback
		/// </summary>
		/// <param name="closeCaseEndpointParameters">Los parámetros para proporcionar el cierre del caso</param>
		public async Task NotifyClosedCase(Dictionary<string, string> closeCaseEndpointParameters)
		{
			if (this.ServiceConfiguration.ServiceIntegrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback)
				return;

			await this.ServiceConfiguration.IntegrationType3CloseCaseEndpoint.InvokeAsync(this.client, closeCaseEndpointParameters, (string value) =>
			{
				if (string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType3CloseCaseEndpoint.HashKey))
					return null;

				var header = new HttpRequestSettings.HttpRequestHeader();
				header.Name = "hash";
				header.Value = HashHMACHex(this.ServiceConfiguration.IntegrationType3CloseCaseEndpoint.HashKey, value);
				return header;
			});
		}

		/// <summary>
		/// Marca un mensaje como leído
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a marcar como leído</param>
		public async Task MarkMessageAsRead(DomainModel.Message message)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowToMarkAsReadWhatsappMessages ||
				this.ServiceConfiguration.MarkAsReadBehaviour == WhatsAppServiceConfiguration.MarkAsReadBehaviours.DoNotMark)
				return;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					await MarkMessageAsReadInfobip(message);
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					await MarkMessageAsReadYoizen(message);
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					await MarkMessageAsReadCloudApi(message);
					break;
				default:
					break;
			}
		}

		/// <summary>
		/// Rechaza una llamada de voz de WhatsApp que está asociada a un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a la cual la llamada está relacionada o <code>null</code> si no tiene relación con ningún caso</param>
		/// <param name="callId">El código de llamada de voz de WhatsApp</param>
		/// <returns>Un <see cref="Task"/></returns>
		public async Task VoiceCallReject(string callId, DomainModel.Case @case = null)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls ||
				!this.ServiceSettings.VoiceCallsEnabled)
				return;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					await VoiceCallActionPostback("reject", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					await VoiceCallActionYoizen("reject", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					await VoiceCallActionCloudApi("reject", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				default:
					break;
			}
		}

		/// <summary>
		/// Termina una llamada de voz de WhatsApp que está asociada a un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a la cual la llamada está relacionada</param>
		/// <param name="callId">El código de llamada de voz de WhatsApp</param>
		/// <returns>Un <see cref="Task"/></returns>
		public async Task VoiceCallTerminate(string callId, DomainModel.Case @case = null)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls ||
				!this.ServiceSettings.VoiceCallsEnabled)
				return;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					await VoiceCallActionPostback("terminate", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					await VoiceCallActionYoizen("terminate", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					await VoiceCallActionCloudApi("terminate", callId, null, @case?.ID, @case?.ID.ToString());
					break;
				default:
					break;
			}
		}

		/// <summary>
		/// Pre-acepta una llamada de voz de WhatsApp que está asociada a un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a la cual la llamada está relacionada</param>
		/// <param name="callId">El código de llamada de voz de WhatsApp</param>
		/// <returns>Un <see cref="Task"/></returns>
		public async Task VoiceCallPreAccept(string callId, string sdp, DomainModel.Case @case = null)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls ||
				!this.ServiceSettings.VoiceCallsEnabled)
				return;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					await VoiceCallActionPostback("pre_accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					await VoiceCallActionYoizen("pre_accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					await VoiceCallActionCloudApi("pre_accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				default:
					break;
			}
		}

		/// <summary>
		/// Acepta una llamada de voz de WhatsApp que está asociada a un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a la cual la llamada está relacionada</param>
		/// <param name="callId">El código de llamada de voz de WhatsApp</param>
		/// <returns>Un <see cref="Task"/></returns>
		public async Task VoiceCallAccept(string callId, string sdp, DomainModel.Case @case = null)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls ||
				!this.ServiceSettings.VoiceCallsEnabled)
				return;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					await VoiceCallActionPostback("accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					await VoiceCallActionYoizen("accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					await VoiceCallActionCloudApi("accept", callId, sdp, @case?.ID, @case?.ID.ToString());
					break;
				default:
					break;
			}
		}

		#endregion

		#region ISocialService Methods

		/// <summary>
		/// Devuelve el <see cref="System.Type"/> de la configuración del servicio
		/// </summary>
		public override Type ConfigurationType { get { return typeof(WhatsAppServiceConfiguration); } }

		/// <summary>
		/// Devuelve si el servicio necesita de otro servicio para realizar sus operaciones
		/// </summary>
		/// <param name="relatedSocialServiceId">Cuando el servicio necesita de otro devuelve el código del servicio</param>
		/// <returns>true en caso de necesitar de otro servicio; en caso contrario, false</returns>
		public override bool RequiresRelatedSocialService(out int relatedSocialServiceId)
		{
			relatedSocialServiceId = 0;
			return false;
		}

		/// <summary>
		/// Establece el servicio relacionado
		/// </summary>
		/// <param name="connector">Un <see cref="SocialServiceConnector"/> que se utilizará para realizar las operaciones
		/// del <see cref="ISocialServiceOperations"/></param>
		public override void SetRelatedSocialService(DomainModel.ISocialServiceOperations connector)
		{
			throw new NotSupportedException("El servicio no necesita de ningún servicio relacionado");
		}

		/// <summary>
		/// Devuelve si un mensaje fue enviado por el usuario configurado en el servicio
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje</param>
		/// <returns>true si el mensaje fue enviado por el usuario configurado; en caso contrario, false</returns>
		public override bool DoesMessageBelongsToConfiguredUser(DomainModel.Message message)
		{
			if (message == null)
				throw new ArgumentNullException("message");

			return message.PostedBy.ID.ToString().Equals(this.ServiceConfiguration.FullPhoneNumber);
		}

		/// <summary>
		/// Inicializa el servicio a partir de los datos de configuración
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con la información del servicio</param>
		/// <param name="serviceConfiguration">Los datos de configuración del servicio</param>
		/// <param name="serviceStatus">El estado inicial del servicio</param>
		/// <param name="validUntil">Especifica la fecha hasta la cual el servicio es válido o <code>null</code> para indicar que no vence</param>
		/// <param name="pathForFiles">Especifica el directorio donde se pueden grabar archivos</param>
		public override void Initialize(DomainModel.Service service, DomainModel.IServiceConfiguration serviceConfiguration, DomainModel.ISocialServiceStatus serviceStatus, DateTime? validUntil, string pathForFiles)
		{
			base.Initialize(service, serviceConfiguration, serviceStatus, validUntil, pathForFiles);

			this.postbackParameters = new Dictionary<string, string>();
			this.postbackParameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
			this.postbackParameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();

			if (this.client != null)
				this.client.Dispose();

			var httpClientHandler = new System.Net.Http.HttpClientHandler();
			httpClientHandler.ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true;

			if (httpClientHandler.MaxConnectionsPerServer < DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages)
			{
				var oldMaxConnectionsPerServer = httpClientHandler.MaxConnectionsPerServer;
				httpClientHandler.MaxConnectionsPerServer = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages;
				Tracer.TraceVerb("Se cambió el MaxConnectionsPerServer a {0} (valor anterior {1})", httpClientHandler.MaxConnectionsPerServer, oldMaxConnectionsPerServer);
			}

			this.client = new HttpClient(httpClientHandler);
#if NETCOREAPP
#else
			if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"]) &&
				int.TryParse(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"], out int timeout) &&
				timeout > 0)
			{
				Tracer.TraceInfo("Se utilizará un timeout de {0} segundos", timeout);
				this.client.Timeout = TimeSpan.FromSeconds(timeout);
			}
			else
			{
				this.client.Timeout = TimeSpan.FromSeconds(4);
			}
#endif

			if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen &&
				this.ServiceConfiguration.IntegrationType10SendToServiceBus &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10AccountID) &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10LineID))
			{
				this.sbClient = new ServiceBusClient(DomainModel.SystemSettings.Instance.Whatsapp.YoizenServiceBusConnectionString);

				this.sbSender = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies");
				this.sbSenderHSM = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies-bulk");
				this.sbSenderHSMSingle = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies-template");
			}
		}

		/// <summary>
		/// Reconfigura el servicio a partir de los datos de configuración
		/// </summary>
		/// <param name="serviceConfiguration">Los datos de configuración del servicio</param>
		/// <remarks>
		/// Este método es llamado cuando cambian los datos del usuario
		/// </remarks>
		public override void Reconfigure(DomainModel.Service service, DomainModel.IServiceConfiguration serviceConfiguration)
		{
			if (!this.ServiceConfiguration.FullPhoneNumber.Equals(((WhatsAppServiceConfiguration) serviceConfiguration).FullPhoneNumber))
			{
				this.Status.Clear();

				this.postbackParameters = new Dictionary<string, string>();
				this.postbackParameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
				this.postbackParameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();
			}

			var oldIntegrationType10AccountID = this.ServiceConfiguration.IntegrationType10AccountID;

			base.Reconfigure(service, serviceConfiguration);

			if (this.client != null)
				this.client.Dispose();

			var httpClientHandler = new System.Net.Http.HttpClientHandler();
			httpClientHandler.ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true;

			if (httpClientHandler.MaxConnectionsPerServer < DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages)
			{
				var oldMaxConnectionsPerServer = httpClientHandler.MaxConnectionsPerServer;
				httpClientHandler.MaxConnectionsPerServer = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages;
				Tracer.TraceVerb("Se cambió el MaxConnectionsPerServer a {0} (valor anterior {1})", httpClientHandler.MaxConnectionsPerServer, oldMaxConnectionsPerServer);
			}

			this.client = new HttpClient(httpClientHandler);
#if NETCOREAPP
#else
			if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"]) &&
				int.TryParse(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"], out int timeout) &&
				timeout > 5)
			{
				Tracer.TraceInfo("Se utilizará un timeout de {0} segundos", timeout);
				this.client.Timeout = TimeSpan.FromSeconds(timeout);
			}
			else
			{
				this.client.Timeout = TimeSpan.FromSeconds(5);
			}
#endif

			if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen &&
				this.ServiceConfiguration.IntegrationType10SendToServiceBus &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10AccountID) &&
				!string.IsNullOrEmpty(this.ServiceConfiguration.IntegrationType10LineID))
			{
				this.sbClient = new ServiceBusClient(DomainModel.SystemSettings.Instance.Whatsapp.YoizenServiceBusConnectionString);

				if (!string.IsNullOrEmpty(oldIntegrationType10AccountID) &&
					!oldIntegrationType10AccountID.Equals(this.ServiceConfiguration.IntegrationType10AccountID))
				{
					if (this.sbSender != null)
					{
						if (!this.sbSender.IsClosed)
							this.sbSender.CloseAsync().Wait();

						this.sbSender = null;
					}

					if (this.sbSenderHSM != null)
					{
						if (!this.sbSenderHSM.IsClosed)
							this.sbSenderHSM.CloseAsync().Wait();

						this.sbSenderHSM = null;
					}

					if (this.sbSenderHSMSingle != null)
					{
						if (!this.sbSenderHSMSingle.IsClosed)
							this.sbSenderHSMSingle.CloseAsync().Wait();

						this.sbSenderHSMSingle = null;
					}
				}

				if (this.sbSender == null)
					this.sbSender = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies");

				if (this.sbSenderHSM == null)
					this.sbSenderHSM = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies-bulk");

				if (this.sbSenderHSMSingle == null)
					this.sbSenderHSMSingle = this.sbClient.CreateSender($"{this.ServiceConfiguration.IntegrationType10AccountID}-replies-template");
			}
		}

		public override Task<DomainModel.Message> Query(string messageId)
		{
			return Task.FromResult((DomainModel.Message) null);
		}

		/// <summary>
		/// Obtiene los mensajes de whatsapp
		/// </summary>
		/// <returns>Un <see cref="List<DomainModel.Message>"/> con los mensajes de whatsapp</returns>
		public override async Task<IEnumerable<DomainModel.Message>> Query()
		{
			if (this.ServiceConfiguration.ServiceIntegrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback ||
				this.ServiceConfiguration.IntegrationType3PullType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationWithPostbackPullTypes.SpecificUrls)
				return null;

			var messages = new List<DomainModel.Message>();

			JArray jsonArray = null;
			var now = DateTime.Now;

			try
			{
				var parameters = new Dictionary<string, string>();
				parameters["@@NUMERO_TELEFONO@@"] = this.ServiceConfiguration.FullPhoneNumber;
				parameters["@@SERVICIO[CODIGO]@@"] = this.ID.ToString();
				var url = this.ServiceConfiguration.IntegrationType3GetNewsEndpoint.GetUrl(parameters);

				if (url.IndexOf("?") == -1)
				{
					url += $"?ts={now.Ticks}";
				}
				else
				{
					url += $"&ts={now.Ticks}";
				}

				var hashKey = this.ServiceConfiguration.IntegrationType3GetNewsEndpoint.HashKey;

				using (var request = new HttpRequestMessage(HttpMethod.Get, url))
				{
					var hashHmacHex = HashHMACHex(hashKey, request.RequestUri.Query);
					request.Headers.Add("hash", hashHmacHex);

					this.ServiceConfiguration.IntegrationType3ReplyEndpoint.ApplyHeaders(request, this.postbackParameters);

#if DEBUG
					using (var source = new System.Threading.CancellationTokenSource(TimeSpan.FromMinutes(10)))
#else
					using (var source = new System.Threading.CancellationTokenSource(TimeSpan.FromMinutes(5)))
#endif
					using (var response = await client.SendAsync(request, source.Token))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var stream = await response.Content.ReadAsStreamAsync())
							using (var sr = new StreamReader(stream))
							using (var reader = new Newtonsoft.Json.JsonTextReader(sr))
							{
								jsonArray = (Newtonsoft.Json.Linq.JArray) Newtonsoft.Json.Linq.JArray.ReadFrom(reader);
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var stream = await response.Content.ReadAsStreamAsync())
								using (var reader = new StreamReader(stream))
								{
									var error = await reader.ReadToEndAsync();
									Tracer.TraceError("WhatsApp {0}: Falló obtener mensajes desde el callback de azure: {1}", this.ServiceConfiguration.FullPhoneNumber, error);
								}
							}
							else
							{
								Tracer.TraceError("WhatsApp {0}: Falló obtener mensajes desde el callback de azure: {1}", this.ServiceConfiguration.FullPhoneNumber, response.StatusCode);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("WhatsApp {0}: Falló obtener mensajes desde el callback de azure: {1}", this.ServiceConfiguration.FullPhoneNumber, ex);
			}

			if (jsonArray != null)
			{
				foreach (JToken obj in jsonArray)
				{

					try
					{
						var result = await Converter.Convert((JObject) obj, this.service, this);

						if (result.Item1 != null)
						{
							if (result.Item1.Date >= this.ServiceConfiguration.FromDate)
								messages.Add(result.Item1);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError("WhatsApp {0}: Falló procesar la novedad de WhatsApp {1}: {2}", this.ServiceConfiguration.FullPhoneNumber, obj.ToString(), ex);
					}
				}

				if (jsonArray.Count > 0)
				{
					this.Status[LastActivityStatus] = DateTime.Now.ToString("o");
					await FinishProcessingNews();
				}
				else
				{
					if (!this.Status.ContainsKey(LastActivityStatus))
						this.Status[LastActivityStatus] = DateTime.Now.ToString("o");

#if !NETCOREAPP
					DateTime lastActivityStatusDateTime = DateTime.Parse(this.Status[LastActivityStatus]);
					int inactivityMinutes = Convert.ToInt32(now.Subtract(lastActivityStatusDateTime).TotalMinutes);
					if (inactivityMinutes > this.ServiceSettings.MinutesForInactivity)
					{
						if (now.Subtract(this.lastInactivityDetectedMailDate).TotalMinutes > 60)
						{
							var shouldSendMail = true;

							if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow &&
								this.service.UsesYFlow)
							{
								var status = DAL.ServiceDAO.GetStatus<Social.Business.SocialServiceStatus>(this.ID);
								if (status.ContainsKey(DomainModel.Service.LastPushedMessageDateStatus) &&
									DateTime.TryParse(status[DomainModel.Service.LastPushedMessageDateStatus], out DateTime lastPushedMessageDate) &&
									now.Subtract(lastPushedMessageDate).TotalMinutes < this.ServiceSettings.MinutesForInactivity)
								{
									Tracer.TraceInfo("Whatsapp {0}: se detectó que no hay actividad en los últimos {1} minutos pero los mensajes siguen ingresando por push. No se envia alerta", this.ServiceConfiguration.FullPhoneNumber, inactivityMinutes);
									shouldSendMail = false;
								}
							}

							if (shouldSendMail)
							{
								Tracer.TraceInfo("Whatsapp {0}: se detectó que no hay actividad en los últimos {1} minutos. Se enviará un mail", this.ServiceConfiguration.FullPhoneNumber, inactivityMinutes);

								Dictionary<string, object> parameters = new Dictionary<string, object>();

								parameters["@@FECHA@@"] = DateTime.Now.ToString("dddd d 'de' MMMM 'del' yyyy", new System.Globalization.CultureInfo("es-AR"));
								parameters["@@SERVICIO@@"] = this.Name;
								parameters["@@MINUTOS@@"] = inactivityMinutes;

								await DomainModel.SystemSettings.Instance.SendMailMessageAsync(this.ServiceSettings.InactivityDetected, parameters);

								this.lastInactivityDetectedMailDate = now;
							}
						}
					}
#endif
				}
			}

			return messages.OrderBy(m => m.Date);
		}

		/// <summary>
		/// Responde un mensaje
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje que se responderá</param>
		/// <returns>El código del mensaje respondido</returns>
		public override async Task<string> Reply(DomainModel.Message message)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Common.Tracer.TraceInfo("La licencia indica que se trabaja en modo lectura. No se envía");
				return Guid.NewGuid().ToString();
			}

			if (message.Parameters.ContainsKey(DomainModel.Message.ReplyParameter))
			{
				var replyParameter = message.Parameters[DomainModel.Message.ReplyParameter];
				var jReplyInfo = JToken.Parse(replyParameter);

				if (jReplyInfo.Type == JTokenType.Object)
				{
					var jMessage = await this.CreateMessageFromReplyToken((Newtonsoft.Json.Linq.JObject) jReplyInfo, message);
					return await SendMessage(jMessage, message);
				}
				else if (jReplyInfo.Type == JTokenType.Array)
				{
					string firstMessageId = null;
					int index = 0;
					foreach (JToken jItem in (JArray) jReplyInfo)
					{
						if (jItem.Type == JTokenType.Object)
						{
							if (index > 0 && this.ServiceConfiguration.DelayBetweenReplies > 0 && this.ServiceConfiguration.DelayBetweenReplies <= 2000)
							{
								Tracer.TraceVerb("Se aguardarán {0} milisegundos antes del próximo envío", this.ServiceConfiguration.DelayBetweenReplies);
								await Task.Delay(this.ServiceConfiguration.DelayBetweenReplies);
							}

							var jMessage = await this.CreateMessageFromReplyToken((Newtonsoft.Json.Linq.JObject) jItem, message);
							jMessage["id"] = string.Format("{0}_{1}", message.ID.ToString(), index);

							if (firstMessageId == null)
							{
								firstMessageId = await SendMessage(jMessage, message);
								Tracer.TraceInfo("Se envió exitosamente el mensaje {0} del array de respuesta", index);
							}
							else
							{
								try
								{
									var messageId = await SendMessage(jMessage, message);
									Tracer.TraceInfo("Se envió exitosamente el mensaje {0} del array de respuesta", index);
								}
								catch (Exception ex)
								{
									Tracer.TraceError("Falló enviar el mensaje {0}: {1}", index, ex);
								}
							}
						}
						else
						{
							Tracer.TraceError("El mensaje contiene un ítem dentro de la respuestas que no es un JObject. No se envía. Índice={0} - Contenido={1}", index, jItem.ToString());
						}

						index++;
					}

					if (firstMessageId != null)
						return firstMessageId;
				}

				throw new ReplyException(string.Format("No se pudo enviar el mensaje {0}", message), false);
			}
			else
			{
				var integrationType = this.ServiceConfiguration.ServiceIntegrationType;
				string[] attachmentsUrls = null;

				if (message.HasAttach && message.Attachments != null && message.Attachments.Length > 0)
				{
					attachmentsUrls = new string[message.Attachments.Length];
					for (var i = 0; i < message.Attachments.Length; i++)
					{
						var attach = message.Attachments[i];

						if (attach.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) &&
							bool.Parse(attach.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter]) &&
							attach.Parameters.ContainsKey(DomainModel.Attachment.AzureStorageUrlInlineParameter) &&
							!string.IsNullOrEmpty(attach.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter]))
						{
							attachmentsUrls[i] = attach.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
						}
						else if (attach.Parameters.ContainsKey("Url"))
						{
							attachmentsUrls[i] = attach.Parameters["Url"];
						}
						else
						{
							try
							{
								var jData = new Newtonsoft.Json.Linq.JObject();
								jData["account"] = this.ServiceConfiguration.FullPhoneNumber;
								var jMsg = new Newtonsoft.Json.Linq.JObject();
								jData["msg"] = jMsg;
								var jAttach = new Newtonsoft.Json.Linq.JObject();
								jMsg["attach"] = jAttach;
								jAttach["name"] = attach.OriginalFileName;
								jAttach["mimeType"] = attach.MimeType;
								jAttach["size"] = attach.FileSize;
								jAttach["type"] = (short) attach.Type;
								jAttach["data"] = Convert.ToBase64String(attach.Data);

								attachmentsUrls[i] = await this.UploadFile(this.client, "whatsapp", this.ServiceConfiguration.FullPhoneNumber, jData);

								Tracer.TraceVerb("Se subió el archivo adjunto {0} del mensaje {1} a la nube a la url {2}", i, message, attachmentsUrls[i]);
							}
							catch (Exception ex)
							{
								Tracer.TraceVerb("No se pudo subir el archivo adjunto {0} del mensaje {1} a la nube: {2}", i, message, ex);
								attachmentsUrls[i] = null;
							}
						}
					}
				}

				var jMessage = new Newtonsoft.Json.Linq.JObject();
				var sendTextMessage = true;
				string messageId = null;

				if (message.HasAttach && message.Attachments != null && message.Attachments.Length > 1)
				{
					jMessage["body"] = message.Body;
					jMessage["id"] = message.ID.ToString();
					messageId = await SendMessage(jMessage, message);

					int index = 0;
					foreach (var attach in message.Attachments)
					{
						var jAttachMessage = new Newtonsoft.Json.Linq.JObject();
						var jAttach = new Newtonsoft.Json.Linq.JObject();
						jAttachMessage["id"] = message.ID.ToString();
						jAttachMessage["attach"] = jAttach;
						jAttach["name"] = attach.OriginalFileName;
						jAttach["mimeType"] = attach.MimeType;
						jAttach["size"] = attach.FileSize;
						jAttach["type"] = (short) attach.Type;

						if (attachmentsUrls != null && attachmentsUrls[index] != null)
						{
							jAttach["url"] = attachmentsUrls[index];
							jAttach["isPublicUrl"] = true;
						}
						else
						{
							jAttach["data"] = Convert.ToBase64String(attach.Data);
						}

						try
						{
							await SendMessage(jAttachMessage, message);
						}
						catch (Exception ex)
						{
							Tracer.TraceInfo("Falló el envío del adjunto {0} del mensaje {1}: {2}", attach.Index, message, ex);
						}

						index++;
					}

					sendTextMessage = false;
				}
				else if (message.HasAttach && message.Attachments != null && message.Attachments.Length == 1)
				{
					var jAttachMessage = new Newtonsoft.Json.Linq.JObject();
					var attach = message.Attachments[0];
					var jAttach = new Newtonsoft.Json.Linq.JObject();
					jAttachMessage["id"] = message.ID.ToString();
					jAttachMessage["attach"] = jAttach;
					jAttach["name"] = attach.OriginalFileName;
					jAttach["mimeType"] = attach.MimeType;
					jAttach["size"] = attach.FileSize;
					jAttach["type"] = (short) attach.Type;

					if (attachmentsUrls != null && attachmentsUrls[0] != null)
					{
						jAttach["url"] = attachmentsUrls[0];
						jAttach["isPublicUrl"] = true;
					}
					else
					{
						jAttach["data"] = Convert.ToBase64String(attach.Data);
					}

					if (attach.Type == Attachment.AttachmentType.Image &&
						!string.IsNullOrEmpty(message.Body) &&
						message.Body.Length < 1024 &&
						!attach.MimeType.Equals("image/webp", StringComparison.InvariantCultureIgnoreCase))
					{
						jAttachMessage["body"] = message.Body;
						sendTextMessage = false;
					}
					else if (attach.Type == Attachment.AttachmentType.Audio &&
						string.IsNullOrEmpty(message.Body))
					{
						sendTextMessage = false;
					}

					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
						bool.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter]))
					{
						sendTextMessage = false;
					}

					messageId = await SendMessage(jAttachMessage, message);
				}

				if (sendTextMessage)
				{
					if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.HSMParameter) &&
						bool.Parse(message.Parameters[Social.WhatsApp.WhatsAppMessage.HSMParameter]))
					{
						if (!string.IsNullOrEmpty(message.Body))
							jMessage["body"] = message.Body;
						jMessage["id"] = message.ID.ToString();
						messageId = await SendMessage(jMessage, message);
					}
					else if (!string.IsNullOrEmpty(message.Body))
					{
						jMessage["body"] = message.Body;
						jMessage["id"] = message.ID.ToString();
						messageId = await SendMessage(jMessage, message);
					}
					else
					{
						throw new ReplyException($"No se encontró el texto de la respuesta a enviar para el mensaje: {message}");
					}
				}

				return messageId;
			}
		}

		/// <summary>
		/// Envía un mensaje
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje que se enviará</param>
		/// <returns>El código del mensaje enviado</returns>
		/// <remarks>
		/// A diferencia de <see cref="ISocialServiceOperations.Reply(DomainModel.Message)"/> este método sirve para enviar mensajes que no estén relacionados a ningún
		/// otro ni que respondan a otro mensaje
		/// </remarks>
		public override async Task<string> Send(DomainModel.Message message)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Common.Tracer.TraceInfo("La licencia indica que se trabaja en modo lectura. No se envía");
				return Guid.NewGuid().ToString();
			}

			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (message.RepliesToSocialUser == null)
				throw new ArgumentOutOfRangeException(nameof(message), "Se debe especificar el usuario al que se mandará el mensaje");

			if (message.RepliesToSocialUser.LastInteractionDate != null &&
				DateTime.Now.Subtract(message.RepliesToSocialUser.LastInteractionDate.Value).TotalMinutes >= 1440)
				throw new ArgumentOutOfRangeException(nameof(message), "No se puede enviar encuesta al usuario porque pasaron más de 1440 minutos desde su última interacción");

			var jMessage = new Newtonsoft.Json.Linq.JObject();
			jMessage["body"] = message.Body;
			jMessage["id"] = message.ID.ToString();

			if (message.HasAttach && message.Attachments != null && message.Attachments.Length == 1)
			{
				var attach = message.Attachments[0];
				var jAttach = new Newtonsoft.Json.Linq.JObject();
				jMessage["attach"] = jAttach;
				jAttach["name"] = attach.OriginalFileName;
				jAttach["mimeType"] = attach.MimeType;
				jAttach["size"] = attach.FileSize;
				jAttach["type"] = (short) attach.Type;
				jAttach["data"] = Convert.ToBase64String(attach.Data);
			}

			if (message.Parameters.ContainsKey("SurveyInteractiveMsg"))
			{
				var jButton = JObject.Parse(message.Parameters["SurveyInteractiveMsg"]);
				jMessage["type"] = "interactive";
				
				var jInteractive = new JObject();
				jInteractive["type"] = "cta_url";
				jInteractive["body"] = new JObject();
				jInteractive["body"]["text"] = message.Body;
				jInteractive["action"] = new JObject();
				jInteractive["action"]["name"] = "cta_url";
				jInteractive["action"]["parameters"] = new JObject();
				jInteractive["action"]["parameters"]["display_text"] = jButton["displayText"].ToString();
				jInteractive["action"]["parameters"]["url"] = jButton["url"].ToString();
				jMessage["interactive"] = jInteractive;
			}

			return await SendMessage(jMessage, message);
		}

		/// <summary>
		/// Devuelve si el servicio soporta la operación <see cref="CaseClosed(Case)"/>
		/// </summary>
		public override bool SupportsCaseClosed { get { return this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar; } }

		/// <summary>
		/// Método para tomar acciones ante el cierre de un caso específico
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> que se está cerrando</param>
		public override async Task CaseClosed(Case @case)
		{
			if (@case == null)
				return;

			if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar)
			{
				if (@case.LastIncomingMessageID != null)
				{
					DomainModel.Message lastIncomingMessage = null;
					if (@case.Messages != null)
						lastIncomingMessage = @case.Messages.FirstOrDefault(m => m.ID == @case.LastIncomingMessageID.Value);

					if (lastIncomingMessage == null || !lastIncomingMessage.RetrievedFromDatabase)
						lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new DAL.MessageDAO.RelatedEntitiesToRead(false)
						{
							PostedBy = true,
							Queue = true
						});

					if (lastIncomingMessage != null && this.ServiceConfiguration.IntegrationType6SendEndOfConversation)
					{
						bool hasSurvey = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
																DomainModel.SystemSettings.Instance.EnableSurveys &&
																lastIncomingMessage.Queue != null &&
																lastIncomingMessage.Queue.SurveyEnabled &&
																this.ServiceSettings.AllowSurveys &&
																@case.SurveyShouldSend;
						if (!hasSurvey)
						{
							try
							{
								Tracer.TraceError("Se informa cierre de conversacion por CaseClosed a movistar");
								var handoffContext = new HandoffContext(@case);
								var handoffMotive = HandoffContext.GetHandoffMotive(hasSurvey, @case);
								handoffContext.Keyword = HandoffContext.GetKeywordByQueue(@case.Queue);
								
								await PostToMovistarHandOff(lastIncomingMessage.SocialConversationID, lastIncomingMessage.PostedBy.ID, handoffMotive, handoffContext);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Ocurrio un error al realizar el cierre de conversacion a Movistar {0}", ex);
							}
						}
					}
					
					else
					{
						Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
					}
				}
				else
				{
					Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
				}
			}
		}

		/// <summary>
		/// Devuelve si el servicio soporta la operación <see cref="CaseSurveySent(Case)"/>
		/// </summary>
		public override bool SupportsCaseSurveySent { get { return this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar; } }

		/// <summary>
		/// Método para tomar acciones ante la marca para no enviar la encuesta para un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> que se está marcando para ignorar el envío de la encuesta</param>
		public override async Task CaseSurveyIgnored(Case @case)
		{
			if (@case == null)
				return;

			if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar &&
				this.ServiceConfiguration.IntegrationType6SendEndOfConversation)
			{
				if (@case.LastIncomingMessageID != null)
				{
					DomainModel.Message lastIncomingMessage = null;
					if (@case.Messages != null)
						lastIncomingMessage = @case.Messages.FirstOrDefault(m => m.ID == @case.LastIncomingMessageID.Value);

					if (lastIncomingMessage == null || !lastIncomingMessage.RetrievedFromDatabase)
						lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new DAL.MessageDAO.RelatedEntitiesToRead(false)
						{
							PostedBy = true,
							Queue = true
						});

					if (lastIncomingMessage != null && this.ServiceConfiguration.IntegrationType6SendEndOfConversation)
					{
						bool hasSurvey = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
																DomainModel.SystemSettings.Instance.EnableSurveys &&
																lastIncomingMessage.Queue.SurveyEnabled &&
																this.ServiceSettings.AllowSurveys;

						if (hasSurvey)
						{
							try
							{
								Tracer.TraceError("Se informa cierre de conversacion a movistar por CaseSurveyIgnored con envio de encuesta: {0}", hasSurvey);
								var handoffContext = new HandoffContext(@case);
								var handoffMotive = HandoffContext.GetHandoffMotive(hasSurvey, @case);
								handoffContext.Keyword = HandoffContext.GetKeywordByQueue(@case.Queue);
								await PostToMovistarHandOff(lastIncomingMessage.SocialConversationID, lastIncomingMessage.PostedBy.ID, handoffMotive, handoffContext);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Ocurrio un error al realizar el cierre de conversacion a Movistar {0}", ex);
							}
						}
					}
					else
					{
						Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
					}
				}
				else
				{
					Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
				}
			}
		}

		/// <summary>
		/// Devuelve si el servicio soporta la operación <see cref="CaseSurveyIgnored(Case)"/>
		/// </summary>
		public override bool SupportsCaseSurveyIgnored { get { return this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar; } }

		/// <summary>
		/// Método para tomar acciones ante el envío de una encuesta para un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> al que se está enviando la encuesta</param>
		public override async Task CaseSurveySent(Case @case)
		{
			if (@case == null)
				return;

			if (this.ServiceConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar &&
				this.ServiceConfiguration.IntegrationType6SendEndOfConversation)
			{
				if (@case.LastIncomingMessageID != null)
				{
					DomainModel.Message lastIncomingMessage = null;
					if (@case.Messages != null)
						lastIncomingMessage = @case.Messages.FirstOrDefault(m => m.ID == @case.LastIncomingMessageID.Value);

					if (lastIncomingMessage == null || !lastIncomingMessage.RetrievedFromDatabase)
						lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new DAL.MessageDAO.RelatedEntitiesToRead(false)
						{
							PostedBy = true,
							Queue = true
						});

					if (lastIncomingMessage != null && this.ServiceConfiguration.IntegrationType6SendEndOfConversation)
					{
						try
						{
							Tracer.TraceError("Se realizara un cierre de conversacion por CaseSurveySent a movistar {0}");
							var handoffContext = new HandoffContext(@case);

							if (@case.Parameters.ContainsKey("MovistarSurveyButtons"))
							{
								var jSurveyConfiguration = Newtonsoft.Json.Linq.JObject.Parse(@case.Parameters["MovistarSurveyButtons"]);
								handoffContext.Keyword = HandoffContext.GetKeyword(jSurveyConfiguration);
							}

							var handoffMotive = HandoffContext.GetHandoffMotive(true, @case);
							await PostToMovistarHandOff(lastIncomingMessage.SocialConversationID, lastIncomingMessage.PostedBy.ID, handoffMotive, handoffContext);
						}
						catch (Exception ex)
						{
							Tracer.TraceError("Ocurrio un error al realizar el cierre de conversacion a Movistar {0}", ex);
						}
					}
					else
					{
						Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
					}
				}
				else
				{
					Tracer.TraceVerb("No se encontró el último mensaje entrante del caso {0}. No se puede informar del cierre de conversación", @case.ID);
				}
			}
		}

		/// <summary>
		/// Chequea si el mensaje especificado por <paramref name="message"/> debe ser asignado a alguna cola específica en base a la configuración
		/// del servicio
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a verificar</param>
		/// <param name="ignoreRuleForPreviosMessages">Cuando retorna, devuelve <code>true</code> para ignorar si se debe tener en cuenta a qué colas fueron
		/// los mensajes previos del mismo caso; o <code>false</code> para verificar esto</param>
		/// <returns>El código de cola o <code>null</code> si no especifica ninguna cola</returns>
		public override int? CheckQueueForMessage(DomainModel.Message message, out bool ignoreRuleForPreviosMessages)
		{
			ignoreRuleForPreviosMessages = false;

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				return null;

			if (this.ServiceConfiguration.ServiceIntegrationType != DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback)
				return null;

			if (this.ServiceConfiguration.IntegrationType3UsePayload &&
				this.ServiceConfiguration.IntegrationType3PayloadProperties != null &&
				this.ServiceConfiguration.IntegrationType3PayloadProperties.Any(p => p.UseForDerivation) &&
				this.ServiceConfiguration.IntegrationType3QueueTransfersByKey != null &&
				this.ServiceConfiguration.IntegrationType3QueueTransfersByKey.Count > 0)
			{
				if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.PayloadParameter))
				{
					var payload = message.Parameters[Social.WhatsApp.WhatsAppMessage.PayloadParameter];
					if (!string.IsNullOrEmpty(payload))
					{
						try
						{
							var jPayload = Newtonsoft.Json.Linq.JObject.Parse(payload);
							var property = this.ServiceConfiguration.IntegrationType3PayloadProperties.FirstOrDefault(p => p.UseForDerivation);
							if (property != null &&
								jPayload[property.Name] != null &&
								jPayload[property.Name].Type == JTokenType.String)
							{
								var propertyValue = jPayload[property.Name].ToString();

								var transfer = this.ServiceConfiguration.IntegrationType3QueueTransfersByKey.FirstOrDefault(x => string.Equals(x.Key, propertyValue, StringComparison.InvariantCultureIgnoreCase));
								var queue = transfer.Value;

								Common.Tracer.TraceVerb("Se encontró en el payload del mensaje {0} en la propiedad {1} el valor {2} que indica mover a la cola {3}", message, property.Name, propertyValue, queue);

								ignoreRuleForPreviosMessages = this.ServiceConfiguration.IntegrationType3IgnorePreviousQueues;
								return queue;
							}
						}
						catch { }
					}
				}
			}

			return null;
		}

		/// <summary>
		/// Extrae el código de mensaje luego de un envío exitoso de mensaje
		/// </summary>
		/// <param name="jResponse">El <see cref="Newtonsoft.Json.Linq.JObject"/> que retornó la invocación a enviar mensaje</param>
		/// <returns>El código de mensaje</returns>
		public override string ExtractMessageIDFromResponse(Newtonsoft.Json.Linq.JObject jResponse)
		{
			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					{
						if (jResponse["id"] != null &&
							jResponse["id"].Type == JTokenType.String)
							return jResponse["id"].ToString();
					}
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					{
						if (jResponse["messageId"] != null && jResponse["messageId"].Type == JTokenType.String)
						{
							return jResponse["messageId"].ToString();
						}
						else if (jResponse["messages"] != null && jResponse["messages"].Type == JTokenType.Array)
						{
							var jMessages = (JArray) jResponse["messages"];
							return jMessages[0]["messageId"].ToString();
						}
					}
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					{
						var jResponseDestinations = (JArray) jResponse["destinations"];
						return jResponseDestinations[0]["id"].ToString();
					}
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					return jResponse["id"].ToString();
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					{
						var jMessages = (JArray) jResponse["messages"];
						return jMessages[0]["id"].ToString();
					}
				default:
					break;
			}

			return new DomainModel.ShortGuid(Guid.NewGuid());
		}

		/// <summary>
		/// Extrae información de error de una respuesta luego del envío de un mensaje
		/// </summary>
		/// <param name="statusCode">El <see cref="System.Net.HttpStatusCode"/> de la respuesta</param>
		/// <param name="jResponse">El <see cref="Newtonsoft.Json.Linq.JObject"/> que retornó la invocación a enviar mensaje</param>
		/// <param name="errorMessage">Cuando retorna, devuelve el mensaje de error</param>
		/// <param name="errorCode">Cuando retorna, devuelve el código de error o <code>null</code> si no se encontró</param>
		/// <param name="shouldRetry">Cuando retorna, devuelve si se debe reintentar el envío o no</param>
		public override void ExtractErrorInfoFromResponse(System.Net.HttpStatusCode statusCode, Newtonsoft.Json.Linq.JObject jResponse, out string errorMessage, out int? errorCode, out bool shouldRetry)
		{
			errorCode = null;
			errorMessage = null;
			shouldRetry = false;

			if ((int) statusCode >= 500)
				shouldRetry = true;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					try
					{
						if (jResponse["requestError"] != null && jResponse["requestError"].Type == JTokenType.Object)
						{
							var jRequestError = (JObject) jResponse["requestError"];
							if (jRequestError["serviceException"] != null && jRequestError["serviceException"].Type == JTokenType.Object)
							{
								var jServiceException = (JObject) jRequestError["serviceException"];
								if (jServiceException["text"] != null && jServiceException["text"].Type == JTokenType.String)
									errorMessage = jServiceException["text"].ToString();
							}
						}
					}
					catch { }
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					try
					{
						if (jResponse["errorMessage"] != null && jResponse["errorMessage"].Type == JTokenType.String)
							errorMessage = jResponse["errorMessage"].ToString();

						if (jResponse["errorCode"] != null && jResponse["errorCode"].Type == JTokenType.Integer)
							errorCode = jResponse["errorCode"].ToObject<int>();
					}
					catch { }
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					try
					{
						if (jResponse["error"] != null && jResponse["error"].Type == JTokenType.Object)
						{
							var jError = (Newtonsoft.Json.Linq.JObject) jResponse["error"];
							errorMessage = jError["message"].ToString();
							errorCode = jError["number"].ToObject<int>();
						}
					}
					catch { }
					break;
				default:
					break;
			}
		}

		/// <summary>
		/// Devuelve si el servicio dada su configuración actual permite el envío directo a una cola del service bus en vez de publicar directamente en la red social
		/// </summary>
		/// <returns><code>true</code> si lo permite; en caso contrario, <code>false</code></returns>
		public override bool SupportsPublishToServiceBus()
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend)
				return false;

			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					return true;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					return true;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					return true;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					return true;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					return true;
				default:
					break;
			}

			return false;
		}

		/// <summary>
		/// Devuelve si el código de mensaje de red social generado para un mensaje debe ser persistido en el Storage
		/// </summary>
		/// <returns><code>true</code> si el código debe ser persistido; en caso contrario, <code>false</code></returns>
		public override bool ShouldPersistSocialMessageIDInStorage()
		{
			switch (this.ServiceConfiguration.ServiceIntegrationType)
			{
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
					return true;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
					break;
				case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
					break;
				default:
					break;
			}

			return false;
		}

		#endregion

		#region Crypthography

		private static string HashHMACHex(string keyHex, string message)
		{
			var bytes = System.Text.Encoding.ASCII.GetBytes(message);
			var key = Common.Conversions.ConvertHexStringToByteArray(keyHex);

			using (var hmacsha256 = new HMACSHA256(key))
			{
				var hash = hmacsha256.ComputeHash(bytes);
				return BitConverter.ToString(hash).Replace("-", "").ToLower();
			}
		}

		#endregion

		#region Private Methods

		/// <summary>
		/// Genera un payload para botones de Whatsapp Indicando el TimeStamp del envio
		/// </summary>
		/// <param name="payload">el body del payload</param>
		/// <param name="message">el mensaje para obtener parameters</param>
		/// <returns></returns>
		private HSMButtonPayload GenerateButtonPayload(string payload, Message message)
		{
			var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

			var hsmButtonPayload = new HSMButtonPayload()
			{
				Body = payload,
				Timestamp = timestamp.ToString(),
				CampaignId = null
			};

			if (message.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.CampaignIdParameter))
			{
				hsmButtonPayload.CampaignId = message.Parameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter];
			}

			return hsmButtonPayload;
		}

		#endregion
	}
}