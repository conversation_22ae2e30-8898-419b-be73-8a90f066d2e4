﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Threading.Tasks;
using System.Web.Http.Results;
using System.Web;
using System.ServiceModel.Channels;
using Newtonsoft.Json.Linq;

namespace FacebookCallback.Controllers
{
	[ExceptionHandling]
	public class TelegramController : ApiController
	{
		#region Constants

		private const string SecretKeyForHashing = "b29780205f1d02e838cf75ff19f64c76";

		#endregion

		#region Fields

		private static string[] telegramIPs = new string[] { "*************/20", "**********/22" };
		private static string[] headersForRequestRemoteIPAddress = null;

		#endregion

		#region Constructors

		static TelegramController()
		{
			try
			{
				if (!string.IsNullOrEmpty(global::System.Configuration.ConfigurationManager.AppSettings["HeadersForRequestRemoteIPAddress"]))
				{
					var headersForRequestRemoteIPAddress = global::System.Configuration.ConfigurationManager.AppSettings["HeadersForRequestRemoteIPAddress"];
					TelegramController.headersForRequestRemoteIPAddress = headersForRequestRemoteIPAddress.Split(",".ToCharArray());
				}
			}
			catch { }

			if (TelegramController.headersForRequestRemoteIPAddress == null)
				TelegramController.headersForRequestRemoteIPAddress = new string[] { "cf-connecting-ip", "X-Real-IP", "X-Forwarded-For" };

			Yoizen.Common.Tracer.TraceInfo("Se utilizará para verificar las IP remotas de los requests los siguientes headers: {0}", string.Join(", ", TelegramController.headersForRequestRemoteIPAddress));
		}

		#endregion

		#region Action Methods

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare)
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			if (HttpContext.Current.Application["LastTelegramFoldersProcess"] != null)
			{
				HttpContext.Current.Application.Lock();

				var date = (DateTime) HttpContext.Current.Application["LastTelegramFoldersProcess"];
				if (DateTime.Now.Date > date.Date)
				{
					try
					{
						TelegramManager.ProcessFolders();
						HttpContext.Current.Application["LastTelegramFoldersProcess"] = DateTime.Now.Date;
					}
					catch (Exception ex)
					{
						Yoizen.Common.Tracer.TraceError("Ocurrió un error manteniendo las carpetas: {0}", ex);
					}
				}

				HttpContext.Current.Application.UnLock();
			}


			return Request.CreateResponse(HttpStatusCode.OK, new TelegramManager().GetPageLastNews(id));
		}

		/// <summary>
		/// POST: api/telegram
		/// Receive a message from a user and reply to it
		/// </summary>
		[ActionName("DefaultAction")]
		[HttpPost]
		public async Task<HttpResponseMessage> Post([FromUri] string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceVerb("Se ignora el update de telegram {0} porque no trae Content-Type: application/json", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			var remoteIP = GetRemoteIPAddress(Request);
			if (remoteIP != null && 
				!IPAddress.IsLoopback(remoteIP) &&
				!IsIPValid(remoteIP))
			{
				Yoizen.Common.Tracer.TraceWarning("Se quiso invocar desde la IP: {0}", remoteIP);
				var returnValue = new
				{
					success = false,
					error = string.Format("Invalid IP: {0}", remoteIP)
				};
				return Request.CreateResponse(HttpStatusCode.OK, returnValue);
			}

			try
			{
				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				var sessionId = $"{Yoizen.Social.DomainModel.SocialServiceTypes.Telegram}_{id}";

				if (jBody["callback_query"] != null && jBody["callback_query"].Type == JTokenType.Object)
				{
					var jCallbackQuery = (JObject) jBody["callback_query"];
					if (jCallbackQuery["from"] != null && jCallbackQuery["from"].Type == JTokenType.Object)
						sessionId = $"{id}_{jCallbackQuery["from"]["id"].ToString()}";
				}
				else if (jBody["message"] != null && jBody["message"].Type == JTokenType.Object)
				{
					var jMesssage = (JObject) jBody["message"];
					var ignoredServiceMessages = new string[] { "delete_chat_photo", "group_chat_created", "supergroup_chat_created", "channel_chat_created" };
					foreach (var item in ignoredServiceMessages)
					{
						if (jMesssage[item] != null && jMesssage[item].Type == JTokenType.Boolean)
						{
							Yoizen.Common.Tracer.TraceVerb("Se ignora el update de telegram {0}", body);
							return new HttpResponseMessage(HttpStatusCode.OK);
						}
					}

					var ignoredTypes = new string[] { "dice", "game", "poll", "venue", "new_chat_members", "invoice", "successful_payment", "proximity_alert_triggered" };
					foreach (var item in ignoredTypes)
					{
						if (jMesssage[item] != null && jMesssage[item].Type == JTokenType.Object)
						{
							Yoizen.Common.Tracer.TraceVerb("Se ignora el update de telegram {0}", body);
							return new HttpResponseMessage(HttpStatusCode.OK);
						}
					}

					if (jMesssage["from"] != null && jMesssage["from"].Type == JTokenType.Object)
						sessionId = $"{id}_{jMesssage["from"]["id"].ToString()}";
				}
				else
				{
					Yoizen.Common.Tracer.TraceVerb("Se ignora el update de telegram {0}", body);
					return new HttpResponseMessage(HttpStatusCode.OK);
				}

				var manager = new TelegramManager();
				await manager.WriteMessage(id, body, sessionId);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update de telegram {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("status")]
		[Route("api/telegram/status")]
		[HttpGet]
		public HttpResponseMessage Status(
			[FromUri(Name = "hub.mode")] string hubMode,
			[FromUri(Name = "hub.challenge")] string hubChallenge,
			[FromUri(Name = "hub.verify_token")] string hubVerifyToken)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			if (hubMode.Equals("status") && hubVerifyToken.Equals("yoizencallback"))
			{
				string result = hubChallenge;
				var resp = new HttpResponseMessage(HttpStatusCode.OK)
				{
					Content = new StringContent(result, Encoding.UTF8, "text/plain")
				};
				return resp;
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("NewsProcessed")]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			try
			{
				var telegramManager = new TelegramManager();
				telegramManager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		#endregion

		#region Private Methods

		/// <summary>
		/// Retorna la IP desde donde está llegando el <see cref="global::System.Web.HttpRequest"/>
		/// </summary>
		/// <param name="request">El <see cref="global::System.Web.HttpRequest"/></param>
		/// <returns>Un <see cref="global::System.Net.IPAddress"/> con la IP desde donde está llegando el request</returns>
		public System.Net.IPAddress GetRemoteIPAddress(HttpRequestMessage request)
		{
			foreach (var header in headersForRequestRemoteIPAddress)
			{
				if (request.Headers.Contains(header))
				{
					var value = request.Headers.GetValues(header).FirstOrDefault();

					if (!string.IsNullOrEmpty(value))
					{
						try
						{
							if (header.Equals("X-Forwarded-For", StringComparison.InvariantCultureIgnoreCase))
							{
								var xForwardedForHeader = value;
								var xForwardedForHeaderParts = xForwardedForHeader.Split(",".ToCharArray());

								var parsedHeaderIP = System.Net.IPAddress.Parse(xForwardedForHeaderParts[0].Trim());
								return parsedHeaderIP;
							}
							else
							{
								var parsedHeaderIP = System.Net.IPAddress.Parse(value);
								return parsedHeaderIP;
							}
						}
						catch { }
					}
				}
			}

			return GetClientIp(request);
		}

		private System.Net.IPAddress GetClientIp(HttpRequestMessage request)
		{
			if (request.Properties.ContainsKey("MS_HttpContext"))
			{
				return IPAddress.Parse(((HttpContextWrapper) request.Properties["MS_HttpContext"]).Request.UserHostAddress);
			}
			else if (request.Properties.ContainsKey(RemoteEndpointMessageProperty.Name))
			{
				RemoteEndpointMessageProperty prop = (RemoteEndpointMessageProperty) request.Properties[RemoteEndpointMessageProperty.Name];
				return IPAddress.Parse(prop.Address);
			}
			else if (HttpContext.Current != null)
			{
				return IPAddress.Parse(HttpContext.Current.Request.UserHostAddress);
			}

			return null;
		}

		/// <summary>
		/// Devuelve si una IP de un agente está restringida para acceder
		/// </summary>
		/// <param name="address">El <see cref="System.Net.IPAddress"/> con la IP del agente</param>
		/// <returns><code>true</code> si la IP está restringida para acceder; en caso contrario, <code>false</code></returns>
		public bool IsIPValid(System.Net.IPAddress remoteAddress)
		{
			foreach (var ipOrNetwork in telegramIPs)
			{
				if (ipOrNetwork.Contains("/"))
				{
					System.Net.IPNetwork network;
					if (System.Net.IPNetwork.TryParse(ipOrNetwork, out network))
					{
						if (network.Contains(remoteAddress))
							return true;
					}
				}
				else
				{
					System.Net.IPAddress address;
					if (System.Net.IPAddress.TryParse(ipOrNetwork, out address))
					{
						if (address.Equals(remoteAddress))
							return true;
					}
				}
			}

			return false;
		}

		#endregion
	}
}