(function () {

    angular
        .module('socialApp')
        .factory('modalSocialService', modalSocialService);

    modalSocialService.$inject = ['ModalService', '$timeout'];

    function modalSocialService(ModalService, $timeout) {
        var service = {
            showGroupingMessageAlert: showGroupingMessageAlert,
            showAgentStatistics: showAgentStatistics,
            showAgentChangePassword: showAgentChangePassword,
            showCaseDetails: showCaseDetails,
            showAgentSettings: showAgentSettings,
            showTwoFactorSettings: showTwoFactorSettings,
            showPredefinedAnswers: showPredefinedAnswers,
            showEmailContacts: showEmailContacts,
            showForms: showForms,
            showMyPredefinedAnswers: showMyPredefinedAnswers,
            showAttachment: showAttachment,
            showConfirmDiscard: showConfirmDiscard,
            showConfirmFinish: showConfirmFinish,
            showConfirmReturn: showConfirmReturn,
            showModalSocialUserProfile: showModalSocialUserProfile,
            showConfirmUpdateBusinessData: showConfirmUpdateBusinessData,
            showFileUploader: showFileUploader,
            showFilterUserProfiles: showFilterUserProfiles,
            showConfirmLeaveOutboundAssistant: showConfirmLeaveOutboundAssistant,
            showModalNotQuitable: showModalNotQuitable,
            showAlert: showAlert,
            showCustom: showCustom,
            showOutgoingType: showOutgoingType,
            showOutgoing: showOutgoing,
            blockModalHiding: true,
            showAnswerGroupedMessage: showAnswerGroupedMessage,
            showConfirmGeneral: showConfirmGeneral,
            showConfirmWebToSpeech: showConfirmWebToSpeech,
            showCase: showCase,
            showEditProfile: showEditProfile,
            showConfirmUpdateProfileName: showConfirmUpdateProfileName,
            showMergingProfilesModal: showMergingProfilesModal,
            areVisibleModals: areVisibleModals,
            closeModals: closeModals,
            showActionEmailContact: showActionEmailContact,
        };

        return service;

        function areVisibleModals() {
            if (typeof(ModalService.openModals) !== 'undefined' &&
                Array.isArray(ModalService.openModals)) {
                return ModalService.openModals.length > 0;
            }

            return false;
        }

        function closeModals() {
            ModalService.closeModals();
        }

        function showAlert(description, title, callback) {
            if (typeof (description) !== 'string' ||
                description.length === 0) {
                return;
            }

            if (typeof (title) !== 'string') {
                title = description;
            }

            service.showModalNotQuitable({
                title: title,
                description: description
            })
                .then(function (modal) {
                    modal.element.modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    modal.close.then(function (result) {
                        if (typeof (callback) === 'function') {
                            callback(result);
                        }
                    });
                });
        }

        function showModalNotQuitable(data) {
            let modalProperties = {
                templateUrl: getTemplateUrl('notQuitable'),
                controller: 'NotQuitableModalController',
                inputs: data
            };

            return showModal(modalProperties);
        }

        function showCase(socialCase, caseReadOnly, showExtendedInfo) {
            if (typeof(showExtendedInfo) === "undefined"){
                showExtendedInfo = false;
            }
            var modalProperties = {
                templateUrl: getTemplateUrl('case'),
                controller: 'CaseModalController as caseModalCtrl',
                inputs: {
                    socialCase: socialCase,
                    caseReadOnly: caseReadOnly,
                    showExtendedInfo: showExtendedInfo
                }
            };

            return showModal(modalProperties);
        }


        function showAnswerGroupedMessage(socialCase, message) {
            var modalProperties = {
                templateUrl: getTemplateUrl('answerGroupedMessage'),
                controller: 'AnswerGroupedMessageModalController as answerGroupedCtrl',
                inputs: {
                    socialCase: socialCase,
                    message: message
                },
                controllerAs: 'modalGpdAnswerCtrl'
            };

            return showModal(modalProperties);
        }

        function showOutgoing() {
            var modalProperties = {
                templateUrl: getTemplateUrl('outgoing'),
                controller: 'OutgoingModalController'
            };

            return showModal(modalProperties);
        }

        function showGroupingMessageAlert(data) {
            var modalProperties = {
                templateUrl: getTemplateUrl('groupingMessage'),
                controller: 'GroupingMessageModalController',
                inputs: {
                    title: data.title,
                    content: data.description
                }
            };

            return showModal(modalProperties);
        }

        function showAgentStatistics() {
            var modalProperties = {
                templateUrl: getTemplateUrl('agentStatistics'),
                controller: 'AgentStatisticsController'
            };

            return showModal(modalProperties);
        }

        function showAgentSettings() {
            var modalProperties = {
                templateUrl: getTemplateUrl('agentSettings'),
                controller: 'AgentSettingsController'
            };

            return showModal(modalProperties);
        }

        function showTwoFactorSettings() {
            var modalProperties = {
                templateUrl: getTemplateUrl('twoFactorSettings'),
                controller: 'TwoFactorSettingsController'
            };

            return showModal(modalProperties);
        }

        function showAgentChangePassword() {
            var modalProperties = {
                templateUrl: getTemplateUrl('agentChangePassword'),
                controller: 'AgentChangePasswordController'
            };

            return showModal(modalProperties);
        }

        function showActionEmailContact(contact) {
            var modalProperties = {
                templateUrl: getTemplateUrl('emailContacts-action'),
                controller: 'EmailContactsActionModalController',
                inputs: {
                    previousContact : contact
                }
            };

            return showModal(modalProperties);
        }

        function showPredefinedAnswers(selectedSocialCase, agentSettings, principalAnswer) {
            var modalProperties = {
                templateUrl: getTemplateUrl('predefinedAnswers'),
                controller: 'PredefinedAnswersModalController',
                inputs: {
                    socialCase: selectedSocialCase,
                    principalAnswer: principalAnswer
                }
            };

            return showModal(modalProperties);
        }

        function showEmailContacts(){
            var modalProperties = {
                templateUrl: getTemplateUrl('emailContacts'),
                controller: 'EmailContactsModalController'
            };

            return showModal(modalProperties);
        }

        function showForms(forms) {
            var modalProperties = {
                templateUrl: getTemplateUrl('appleForms'),
                controller: 'AppleFormsModalController',
                inputs: {
                    forms: forms
                }
            };

            return showModal(modalProperties);
        }

        function showMyPredefinedAnswers() {
            var modalProperties = {
                templateUrl: getTemplateUrl('myPredefinedAnswers'),
                controller: 'MyPredefinedAnswersModalController',
                inputs: {
                }
            };

            return showModal(modalProperties);
        }

        function showCaseDetails(selectedSocialCase, mustApplyTags, tagImportant) {
            let modalProperties;

            if (typeof(mustApplyTags) === 'undefined') {
                mustApplyTags = true;
            }
            if (typeof(tagImportant) === 'undefined') {
                tagImportant = false;
            }

            if(typeof(selectedSocialCase) === 'undefined' ) {
                selectedSocialCase = null;
                modalProperties = {
                    templateUrl: getTemplateUrl('caseDetails'),
                    controller: 'CaseDetailsModalController',
                    inputs: {
                        selectedSocialCase: null,
                        mustApplyTags: mustApplyTags,
                        tagImportant: tagImportant,
                        outgoing: true
                    }
                };
            }
            else {
                modalProperties = {
                    templateUrl: getTemplateUrl('caseDetails'),
                    controller: 'CaseDetailsModalController',
                    inputs: {
                        selectedSocialCase: selectedSocialCase,
                        mustApplyTags: mustApplyTags,
                        tagImportant: tagImportant,
                        outgoing: false
                    }
                };
            }


            return showModal(modalProperties);
        }

        function showAttachment(message, attachment) {
            var modalProperties = {
                templateUrl: getTemplateUrl('attachment'),
                controller: 'AttachmentDownloadController',
                inputs: {
                    message: message,
                    attachment: attachment
                }
            };

            return showModal(modalProperties);

        }

        function showModalSocialUserProfile(previousCase) {
            var modalProperties = {
                templateUrl: getTemplateUrl('socialUserProfile'),
                controller: 'SocialUserProfileController',
                inputs: {
                    previousCase: previousCase
                }
            };

            return showModal(modalProperties);
        }

        function showFilterUserProfiles(parameters) {
            var modalProperties = {
                templateUrl: getTemplateUrl('searchUserProfiles'),
                controller: 'SearchUserProfilesController',
                inputs: {
                    parameters: parameters
                }
            };

            return showModal(modalProperties);
        }

        function showFileUploader(queue, selectedSocialCase, forPrimary) {
            if (typeof(forPrimary) === 'undefined') {
                forPrimary = true;
            }

            var modalProperties = {
                templateUrl: getTemplateUrl('uploadFiles'),
                controller: 'UploadFilesModalController',
                animation: false,
                inputs: {
                    queue: queue,
                    selectedSocialCase: selectedSocialCase,
                    forPrimary: forPrimary,
                }
            };

            return showModal(modalProperties);
        }

        function showOutgoingType(params) {
            var modalProperties = {
                templateUrl: getTemplateUrl('outgoing-type'),
                controller: 'OutgoingTypeController',
                inputs: {
                    params: params
                }
            };

            return showModal(modalProperties);
        }

        function showConfirmReturn(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmUpdateBusinessData(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmUpdateProfileName(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmDiscard(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmFinish(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmLeaveOutboundAssistant(data) {
            return showConfirmGeneral(data);
        }

        function showConfirmGeneral(data) {
            var modalProperties = {
                templateUrl: getTemplateUrl('confirm-general'),
                controller: 'ConfirmGeneralController',
                inputs: {
                    title: data.title,
                    description: data.description,
                    negativeButtonShown: data.negativeButtonShown,
                    acceptButton: null,
                    cancelButton: null,
                    iconClass: data.iconClass || 'fa-exclamation-circle'
                }
            };

            if (typeof(data.acceptButton) !== 'undefined' &&
                data.acceptButton !== null)
                modalProperties.inputs.acceptButton = data.acceptButton;

            if (typeof(data.cancelButton) !== 'undefined' &&
                data.cancelButton !== null)
                modalProperties.inputs.cancelButton = data.cancelButton;

            return showModal(modalProperties);
        }

        function showConfirmWebToSpeech(data) {
            var modalProperties = {
                templateUrl: getTemplateUrl('confirm-webtospeech'),
                controller: 'ConfirmWebToSpeechController',
                inputs: {
                    title: data.title,
                    description: data.description,
                    negativeButtonShown: data.negativeButtonShown,
                    acceptButton: null,
                    cancelButton: null,
                    iconClass: data.iconClass || 'fa fa-headphones',
                    recognition: data.recognition
                }
            };

            if (typeof(data.acceptButton) !== 'undefined' &&
                data.acceptButton !== null)
                modalProperties.inputs.acceptButton = data.acceptButton;

            if (typeof(data.cancelButton) !== 'undefined' &&
                data.cancelButton !== null)
                modalProperties.inputs.cancelButton = data.cancelButton;

            return showModal(modalProperties);
        }

        function showCustom(data) {
            var modalProperties = {
                templateUrl: getTemplateUrl('custom'),
                controller: 'CustomModalController',
                inputs: {
                    params: data
                }
            };

            return showModal(modalProperties);
        }

        function showEditProfile(data, readOnly) {
            if (typeof(readOnly) === 'undefined') {
                readOnly = false;
            }

            var modalProperties = {
                templateUrl: getTemplateUrl('profileEdit'),
                controller: 'ProfileEditController',
                inputs: {
                    profile: data.profile,
                    readOnly: readOnly
                }
            };

            return showModal(modalProperties);
        }

        function showMergingProfilesModal(data) {
            var modalProperties = {
                templateUrl: getTemplateUrl('profiles-merging-confirm'),
                controller: 'ProfilesMergingConfirmController',
                inputs: {
                    userProfiles: data.userProfiles,
                    actualProfile: data.actualProfile
                }
            };

            return showModal(modalProperties);
        }

        /////////// Private functions

        function showModal(modalProperties) {
            if (typeof(modalProperties.preClose) === 'undefined') {
                modalProperties.preClose = function (modal) {
                    $timeout(function (modal) {
                        modal.element.modal('hide');
                        let $modalbackdrop = $('.modal-backdrop');
                        if ($modalbackdrop.length === 1) {
                            $modalbackdrop.remove();
                            $('body').removeClass('modal-open');
                        }
                        else {
                            $($modalbackdrop.get($modalbackdrop.length - 1)).remove();
                        }
                    }, 500, true, modal);
                };
            }
            return ModalService.showModal(modalProperties);
        }

        function getTemplateUrl(fileName) {
            return './app/modals/' + fileName + '-modal.template.html';
        }

    }

})();
