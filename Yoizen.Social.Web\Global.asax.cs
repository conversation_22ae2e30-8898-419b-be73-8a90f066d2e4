﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Web;
using Yoizen.Common;
using Yoizen.Social.Core;
using Yoizen.Social.Core.Services;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.Web
{
	public class Global : System.Web.HttpApplication
	{
		private bool initialized = false;
		
		public Global()
		{
			this.initialized = false;
		}

		protected void Application_Start(object sender, EventArgs e)
		{
#if !DEBUG && !ENCRYPTED
			Tracer.TraceToMicrosoftAzureApplicationInsights = true;
#else
			Tracer.TraceInfo("========================================================================");
#endif

			System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(
					delegate
					{
						return true;
					}
				);

			System.Net.ServicePointManager.SecurityProtocol |= System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;

			// Code that runs on application startup
			System.Threading.Tasks.TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

			var jsonSchemaLicense = "4194-pFhTUaILTpj9AIg3PLkTWOYCIYUWx7eJLePlz/ErjK9dT+fAOdLI34llu/jfBO14v9CFXKfbqMOqC9rJZvX37c6Bi04hxVk93IKlMt0qHZwGiBD/P2wIIy+0/gs1+6Zq8WxPU8jnMypkrkdnT4pqRW6oqDV9rDTxJvR5dl0qwZZ7IklkIjo0MTk0LCJFeHBpcnlEYXRlIjoiMjAyMS0wMy0yN1QxNjoxNDo0OS4yOTMyMzE5WiIsIlR5cGUiOiJKc29uU2NoZW1hSW5kaWUifQ==";
			if (!string.IsNullOrEmpty(ConfigurationManager.AppSettings["JsonSchemaLicense"]))
			{
				jsonSchemaLicense = ConfigurationManager.AppSettings["JsonSchemaLicense"];
			}
			Newtonsoft.Json.Schema.License.RegisterLicense(jsonSchemaLicense);

			if (Licensing.LicenseManager.Instance.IsLicenseValid)
			{
#if !DEBUG && !ENCRYPTED
				Tracer.TraceCustomer = Licensing.LicenseManager.Instance.License.Configuration.ClientID;
				Tracer.TraceSource = "Web";
#endif
				/*
				 * El siguiente código debe ser el mismo que se ejecuta al tocar el botón ACTUALIZAR en la página License.aspx
				 */
				DAL.SystemSettingsDAO.GetAll();
				DAL.SystemStatusDAO.GetAll();
			}

			try
			{
				string emojiZipFile = Server.MapPath("~/Images/svg/emojis.zip");
				if (File.Exists(emojiZipFile))
				{
					var directoryName = Path.GetDirectoryName(emojiZipFile);
					var directory = new DirectoryInfo(directoryName);
					if (!directory.EnumerateFiles("*.svg").Any())
					{
						System.IO.Compression.ZipFile.ExtractToDirectory(emojiZipFile, directoryName);

						Tracer.TraceInfo("Se descompimió el archivo de emojis: {0}", emojiZipFile);
					}
				}
				else
				{
					Tracer.TraceInfo("No se encontró el archivo de emojis: {0}", emojiZipFile);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo descompimir los emojis: {0}", ex);
			}

			System.Web.UI.WebControls.WebControl.DisabledCssClass = string.Empty;

			try
			{
				var asm = Assembly.GetExecutingAssembly();
				var fvi = System.Diagnostics.FileVersionInfo.GetVersionInfo(asm.Location);
				Application["SocialVersion"] = fvi.FileVersion.ToString();
			}
			catch 
			{
				Application["SocialVersion"] = "*******";
			}

			global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async (ct) =>
			{
				Core.System.Instance.Initialize();

				if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["Host"]))
				{
					if (bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["Https"], out bool https))
						Core.System.Instance.Https = https;
					Core.System.Instance.Host = System.Configuration.ConfigurationManager.AppSettings["Host"];
					Core.System.Instance.SiteRoot = string.Format("{0}://{1}", Core.System.Instance.Https ? "https" : "http", Core.System.Instance.Host);
				}
				else
				{
					throw new Exception("Falta el appSettings 'Host' dentro del web.config");
				}

				Core.System.Instance.SiteRootWithApplication = $"{Core.System.Instance.SiteRoot}{System.Web.Hosting.HostingEnvironment.ApplicationVirtualPath}";

				DomainModel.Message.SiteRootWithApplication = Core.System.Instance.SiteRootWithApplication;
				Tracer.TraceInfo("Se estableció como URL base del sitio: {0} y la url absoluta: {1}", Core.System.Instance.SiteRoot, Core.System.Instance.SiteRootWithApplication);

				if (!Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
				{
					await SocialServices.Subscriber.Manager.Initialize();

					if (!await SocialServices.Subscriber.Manager.IsSubscribed())
					{
						await SocialServices.Subscriber.Manager.Subscribe();
					}
				}

				Core.System.Instance.Ready += async () =>
				{
					if (!Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
					{
						await SocialServices.Subscriber.Manager.Start();
					}
				};

				Core.System.Instance.Start();
			});

			this.initialized = true;
		}

		protected void Application_End(object sender, EventArgs e)
		{
			Tracer.TraceInfo("------------------------------------------------------------------------");
			Tracer.TraceInfo("Deteniendo la web");

			Core.System.Instance.Dispose();
			Tracer.TraceInfo("Se liberó los recursos del sistema");

			SocialServices.Subscriber.Manager.Finish().GetAwaiter().GetResult();
			Tracer.TraceInfo("Se detuvo el Manager del suscriber");
		
			try
			{
				HttpRuntime runtime = (HttpRuntime) typeof(System.Web.HttpRuntime).InvokeMember("_theRuntime",
					BindingFlags.NonPublic | BindingFlags.Static | BindingFlags.GetField, null, null, null);

				if (runtime == null)
					return;

				string shutDownMessage = (string) runtime.GetType().InvokeMember("_shutDownMessage",
					BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.GetField, null, runtime, null);

				string shutDownStack = (string) runtime.GetType().InvokeMember("_shutDownStack",
					BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.GetField, null, runtime, null);

				Tracer.TraceInfo("ShutDownMessage = {0}", shutDownMessage);
				Tracer.TraceInfo("ShutDownStack = {0}", shutDownStack);
			}
			catch { }

#if !DEBUG && !ENCRYPTED
			Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.Active.TelemetryChannel.Flush();
#endif
		}

		protected void Session_Start(object sender, EventArgs e)
		{
			if (Request.Url.AbsolutePath.EndsWith("agents/connect", StringComparison.InvariantCultureIgnoreCase))
			{
				Session["PersonType"] = DomainModel.PersonTypes.Agent;
				return;
			}

#if DEBUG
			if (!Request.Url.AbsolutePath.EndsWith("/Login.aspx", StringComparison.InvariantCultureIgnoreCase))
			{
				while (!Core.System.Instance.Initialized &&
					!Core.System.Instance.IsReady)
				{
					Tracer.TraceVerb("Se aguarda 1000 milisegundos para esperar que termine de iniciar");
					System.Threading.Thread.Sleep(1000);
					
					if (Core.System.Instance.InitializationFailed)
					{
						Tracer.TraceVerb($"No se esperará mas tiempo ya que el inicio de la web falló");
						return;
					}
				}

				var user = UserDAO.GetOneFromCache(1);

				if (Core.System.Instance.UsersService == null)
				{
					Core.System.Instance.UsersService = new UsersService();
					Core.System.Instance.UsersService.Initialize();
				}

				var connectionInfo = Core.System.Instance.UsersService.UserLoggedIn(user, HttpContext.Current.Request, Session);
				Session["ConnectionInfo"] = connectionInfo;
				Session["PersonType"] = DomainModel.PersonTypes.User;
				Session["Usuario"] = user;
				Session["IsSuper"] = true;

				user.Permissions.AddRange(DAL.PermissionDAO.GetAll());
				var allQueues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
				foreach (var queue in allQueues)
				{
					if (!user.Queues.Contains(queue))
						user.Queues.Add(queue);
				}
				var supervisorProfile = DomainModel.Cache.Instance.GetItem<DomainModel.Profile>(2);
				if (!user.Profiles.Contains(supervisorProfile))
				{
					user.Profiles.Add(supervisorProfile);
				}
			}
#endif
		}

		protected void Session_End(object sender, EventArgs e)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) Session["Usuario"];
				if (user != null)
				{
					Core.System.Instance.UsersService.UserLoggedOut(user, Session.SessionID);
					Tracer.TraceInfo("Finalizó la sesión del usuario: {0}", user);
				}

				DomainModel.Agent agent = (DomainModel.Agent) Session["Agent"];
				if (agent != null)
				{
					Tracer.TraceInfo("Finalizó la sesión del agente: {0}", agent);
				}
			}
			catch { }
		}

		/*protected void Application_PreRequestHandlerExecute(Object sender, EventArgs e)
		{
			/// only apply session cookie persistence to requests requiring session information
			if (Context.Handler is System.Web.SessionState.IRequiresSessionState || Context.Handler is System.Web.SessionState.IReadOnlySessionState)
			{
				var sessionState = ConfigurationManager.GetSection("system.web/sessionState") as System.Web.Configuration.SessionStateSection;
				var cookieName = sessionState != null && !string.IsNullOrEmpty(sessionState.CookieName)
				  ? sessionState.CookieName
				  : "ASP.NET_SessionId";

				/// Ensure ASP.NET Session Cookies are accessible throughout the subdomains.
				if (Request.Cookies[cookieName] != null && Session != null && Session.SessionID != null)
				{
					var appPath = Request.ApplicationPath;
					if (!string.IsNullOrEmpty(appPath))
					{
						if (!appPath.EndsWith("/"))
							appPath += "/";
					}
					else
					{
						appPath = "/";
					}
					Response.Cookies[cookieName].Path = appPath;
				}
			}
		}*/

		protected void Application_BeginRequest(object sender, EventArgs e)
		{
			if (!Licensing.LicenseManager.Instance.IsLicenseValid)
				return;

			Response.AddOnSendingHeaders(c =>
			{
				try
				{
					c.Response.Headers.Remove("X-AspNet-Version");
					c.Response.Headers.Remove("X-AspNetMvc-Version");

					if (Licensing.LicenseManager.Instance.IsLicenseValid &&
						Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
					{
						if (DomainModel.SystemSettings.Instance.CloudHeadersToRemove != null)
						{
							foreach (var header in DomainModel.SystemSettings.Instance.CloudHeadersToRemove)
							{
								try
								{
									c.Response.Headers.Remove(header);
								}
								catch { }
							}
						}

						if (DomainModel.SystemSettings.Instance.CloudHeadersToAdd != null)
						{
							foreach (var header in DomainModel.SystemSettings.Instance.CloudHeadersToAdd)
							{
								try
								{
									c.Response.Headers.Set(header.Name, header.Value);
								}
								catch { }
							}
						}
					}

					var server = c.Response.Headers.Get("Server");
					if (server == null ||
						!server.Equals("ySocial Server", StringComparison.InvariantCultureIgnoreCase))
					{
						c.Response.Headers.Set("Server", "ySocial Server");
					}
				}
				catch { }
			});

			bool validateHosts = true;
			if (!string.IsNullOrEmpty(ConfigurationManager.AppSettings["ValidateHosts"]) &&
				bool.TryParse(ConfigurationManager.AppSettings["ValidateHosts"], out bool validateHostsTemp))
			{
				validateHosts = validateHostsTemp;
			}

			if (validateHosts)
			{
				var host = Request.ServerVariables["HTTP_HOST"].ToLower();
				var index = host.IndexOf(":");
				if (index >= 0)
					host = host.Substring(0, index);

				if (!host.Equals("localhost") &&
					!host.Equals("127.0.0.1") &&
					!host.Equals(System.Configuration.ConfigurationManager.AppSettings["Host"], StringComparison.InvariantCultureIgnoreCase))
				{
					string[] allowedHosts = null;
					if (!string.IsNullOrEmpty(ConfigurationManager.AppSettings["AllowedHosts"]))
					{
						try
						{
							allowedHosts = ConfigurationManager.AppSettings["AllowedHosts"].Split(",".ToCharArray());
						}
						catch { }
					}

					if (allowedHosts == null ||
						!allowedHosts.Contains(host, StringComparer.InvariantCultureIgnoreCase))
					{
						Tracer.TraceError("El HOST {0} es inválido. No se permite continuar", Request.ServerVariables["HTTP_HOST"]);

						Response.StatusCode = (int) HttpStatusCode.Forbidden;
						if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["InvalidHostStatusCode"], out int invalidHostStatusCode) &&
							invalidHostStatusCode >= 400 &&
							invalidHostStatusCode < 500)
							Response.StatusCode = invalidHostStatusCode;
						Response.TrySkipIisCustomErrors = true;
						Response.End();
						return;
					}

					if (!Core.System.Instance.IsReady)
					{
#if DEBUG
						if (Core.System.Instance.InitializationFailed)
						{
							Server.Transfer("~/InitError.aspx");
							return;
						}
#endif
						if (!Request.Url.LocalPath.EndsWith(".css", StringComparison.InvariantCultureIgnoreCase) &&
							!Request.Url.LocalPath.EndsWith(".js", StringComparison.InvariantCultureIgnoreCase) &&
							Request.Url.LocalPath.IndexOf("/services/", StringComparison.InvariantCultureIgnoreCase) == -1)
						{
							Server.Transfer("~/Wait.aspx");
						}
					}
				}
			}
		}

		protected void Application_Error(object sender, EventArgs e)
		{
			if (!this.initialized)
				return;

			bool doNotRedirectOnErrors = false;
			if (bool.TryParse(ConfigurationManager.AppSettings["DoNotRedirectOnErrors"], out doNotRedirectOnErrors) && doNotRedirectOnErrors)
				return;

			Exception ex = Server.GetLastError();
			Application.Lock();
			if (ex != null)
			{
				Tracer.TraceException(ex);
				Application["LastError"] = ex;
			}
			else
			{
				Tracer.TraceError("Ocurrió un error inesperado");
				Application["LastError"] = null;
			}
			Application.UnLock();

#if !DEBUG
			Server.ClearError();
			if (!Response.IsRequestBeingRedirected)
				Response.Redirect("~/Errors.aspx");
#endif
		}

		#region Private Methods

		private void TaskScheduler_UnobservedTaskException(object sender, System.Threading.Tasks.UnobservedTaskExceptionEventArgs e)
		{
			e.SetObserved();
			e.Exception.Flatten().Handle(ex =>
			{
				try
				{
					Tracer.TraceError("Ocurrió un error no manejado en una tarea en background: {0}", ex);
					return true;
				}
				catch { return true; }
			});
		}

		#endregion
	}
}