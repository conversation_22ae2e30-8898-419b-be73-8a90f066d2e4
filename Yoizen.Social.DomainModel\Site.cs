﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.IO;
using Newtonsoft.Json.Linq;

namespace Yoizen.Social.DomainModel
{
	/// <summary>
	/// Se utiliza para sitios
	/// </summary>
	public class Site : DomainObjectWithName<short>, ICacheable
	{
		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Site"/>
		/// </summary>
		public Site()
			: base()
		{
		}

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="Site"/> a partir de los datos de un registro de la base de datos
		/// </summary>
		/// <param name="record">Un <see cref="IDataRecord"/> con los datos de la base de datos</param>
		public Site(IDataRecord record)
			: base(record)
		{
			this.ID = record.GetValue<short>("SiteID");
		}

		#endregion

		#region ICacheable Members

		/// <summary>
		/// Devuelve el ID del ítem para ser usado como clave en el caché
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public string CacheID
		{
			get { return string.Format("{0}_{1}", this.GetType().ToString(), this.ID.ToString()); }
		}

		#endregion

		#region Static Methods

		/// <summary>
		/// Consolida registros históricos para calcular la adhesión de agentes por intervalo
		/// </summary>
		/// <param name="sites">Una enumeación con los códigos de sitios</param>
		/// <param name="records">Una enumeración de <see cref="DomainModel.Historical.Daily"/> con los registros históricos</param>
		/// <param name="fromInterval">Un <see cref="Common.Interval"/> a partir del cual se procesará la adhesión</param>
		/// <param name="toInterval">Un <see cref="Common.Interval"/> hasta el cual se procesará la adhesión</param>
		/// <returns>Un <see cref="IDictionary{TKey, TValue}"/> con la cantidad de agentes por intervalo</returns>
		public static IDictionary<Common.Interval, IDictionary<short, int>> ConsolidateAdherence(
			short[] sites,
			DomainModel.Historical.Daily.DailyDataReader reader, 
			Common.Interval fromInterval, 
			Common.Interval toInterval,
			out int totalRecords)
		{
			totalRecords = 0;
			
			var consolidatedRecords = new SortedDictionary<Common.Interval, IDictionary<short, int>>();
			
			while (fromInterval <= toInterval)
			{
				var siteInfo = new Dictionary<short, int>();
				consolidatedRecords[fromInterval] = siteInfo;
				foreach (var site in sites)
				{
					siteInfo[site] = 0;
				}

				fromInterval = fromInterval.Next();
			}

			while (reader.Read())
			{
				totalRecords++;

				if (reader.Entity.Person.Site != null)
				{
					Dictionary<short, int> siteInfo;
					if (!consolidatedRecords.ContainsKey(reader.Entity.CurrentInterval))
					{
						siteInfo = new Dictionary<short, int>();
						consolidatedRecords[reader.Entity.CurrentInterval] = siteInfo;
					}
					else
					{
						siteInfo = (Dictionary<short, int>) consolidatedRecords[reader.Entity.CurrentInterval];
					}

					if (!siteInfo.ContainsKey(reader.Entity.Person.Site.ID))
						siteInfo[reader.Entity.Person.Site.ID] = 1;
					else
						siteInfo[reader.Entity.Person.Site.ID]++;
				}
			}

			return consolidatedRecords;
		}

		/// <summary>
		/// Consolida registros históricos para calcular la adhesión de agentes por intervalo
		/// </summary>
		/// <param name="sites">Una enumeación con los códigos de sitios</param>
		/// <param name="reader">Un <see cref="DomainModel.Historical.Daily.DailyDataReader"/> con los registros históricos</param>
		/// <param name="fromInterval">Un <see cref="Common.Interval"/> a partir del cual se procesará la adhesión</param>
		/// <param name="toInterval">Un <see cref="Common.Interval"/> hasta el cual se procesará la adhesión</param>
		/// <returns>Una enumeración de <see cref="DomainModel.Historical.DailyAdherence"/> con la cantidad de agentes por sitio por intervalo</returns>
		public static IEnumerable<DomainModel.Historical.DailyAdherence> ConsolidateAdherence(
			short[] sites,
			DomainModel.Historical.Daily.DailyDataReader reader,
			Common.Interval fromInterval,
			Common.Interval toInterval)
		{
			var consolidatedRecords = new SortedDictionary<Common.Interval, IDictionary<short, int>>();

			while (fromInterval <= toInterval)
			{
				var siteInfo = new Dictionary<short, int>();
				consolidatedRecords[fromInterval] = siteInfo;
				foreach (var site in sites)
				{
					siteInfo[site] = 0;
				}

				fromInterval = fromInterval.Next();
			}

			while (reader.Read())
			{
				if (reader.Entity.Person.Site == null)
					continue;

				Dictionary<short, int> siteInfo;
				if (!consolidatedRecords.ContainsKey(reader.Entity.CurrentInterval))
				{
					siteInfo = new Dictionary<short, int>();
					consolidatedRecords[reader.Entity.CurrentInterval] = siteInfo;
				}
				else
				{
					siteInfo = (Dictionary<short, int>) consolidatedRecords[reader.Entity.CurrentInterval];
				}

				if (!siteInfo.ContainsKey(reader.Entity.Person.Site.ID))
					siteInfo[reader.Entity.Person.Site.ID] = 1;
				else
					siteInfo[reader.Entity.Person.Site.ID]++;
			}

			var infoToReturn = new List<DomainModel.Historical.DailyAdherence>();
			foreach (var record in consolidatedRecords)
			{
				foreach (var siteInfo in record.Value)
				{
					var adherence = new DomainModel.Historical.DailyAdherence();
					adherence.Interval = record.Key.IntervalTime;
					adherence.Date = record.Key.IntervalDate;
					adherence.SiteID = siteInfo.Key;
					adherence.Count = siteInfo.Value;
					infoToReturn.Add(adherence);
				}
			}

			return infoToReturn;
		}

		#endregion
	}
}