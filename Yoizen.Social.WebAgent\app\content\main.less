﻿// out: main.css, sourcemap: true, compress: false
@import "variables.less";
@import "mixins.less";

@font-face {
  font-family: 'yoizen';
  src:  url('../webfonts/yoizen.eot?b8leih');
  src:  url('../webfonts/yoizen.eot?b8leih#iefix') format('embedded-opentype'),
  url('../webfonts/yoizen.ttf?b8leih') format('truetype'),
  url('../webfonts/yoizen.woff?b8leih') format('woff'),
  url('../webfonts/yoizen.svg?b8leih#yoizen') format('svg');
  font-weight: normal;
  font-style: normal;
}

:root {
  --default-font-base: @fontName;
  --default-font: @fontName;
  --default-condensed-font-base: @fontNameCondensed;
  --default-condensed-font: @fontNameCondensed;
  --default-mono-font: @fontNameMono;
}

div.inChangePassword {
  div.changePassword-controls-form {
    padding-top: 35px;

    div.form-group {
      input.form-control {
        height: 42px;
      }

      i.form-control-feedback {
        font-size: 20px;
        top: 5px;
      }
    }
  }
  div.changePassword-controls-errors {
    padding-top: 20px;
  }
}

html {
  font-size: @fontSize;
  &.inLogin, &.has-messages-to-answer, &.no-messages-to-answer, &.with-chat-message, &.outgoing-message {
    height: 100%;
    overflow-y: hidden;
  }

  div.inLogin, div.has-messages-to-answer, div.no-messages {
    display: none;
  }

  .outdated-browser {
    display: none;
  }

  .connection-problems {
    display: none;
    flex-direction: row;
    align-items: center;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000000;
    background-color: @red;
    padding: 8px 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
    color: @white;
    opacity: 0.9;
    will-change: opacity;
    transition: opacity 1s cubic-bezier(0.19, 1, 0.22, 1) 0s;

    &:hover {
      opacity: 1;
    }

    .fa-wifi, .fa-wifi-slash {
      display: none;
    }

    .connecting {
      display: none;
      margin-left: 5px;
    }

    .error {
      display: none;
      margin-left: 5px;
    }
  }

  &.browser-outdated {
    .outdated-browser {
      display: block;
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 10000;
      max-width: 350px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);

      .alert {
        margin: 0;
        padding: 5px 10px;
        display: flex;
        flex-direction: row;
        align-items: center;

        &.alert-dismissable {
          position: relative;
          padding-right: 15px;
        }

        & > .fa {
          margin-right: 10px;
          font-size: 150%;
        }

        & > .close {
          position: absolute;
          right: 3px;
          top: 0;
        }
      }
    }
  }

  &.inLogin {
    body {
      header.main-header {
        display: none;
      }

      .outdated-browser {
        display: none;
      }

      .inLogin {
        display: block;

        .login-background {
          height: 100%;
          width: 100%;
          overflow: hidden;
          position: absolute;
          padding-right: 496px;
          background-color: #EAEAEA;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &.initializing {
          div.login-background {
            padding-right: 0;
            left: 0;
            top: 0;
          }
        }

        .login-controls {
          width: 496px;
          display: block;
          position: relative;
          float: right;
          height: 100%;
          background-color: #fff;
          padding: 80px 70px;

          div.login-controls-logo {
            img {
              max-width: 213px;
              max-height: 100px;
            }
          }

          div.login-controls-form {
            padding-top: 35px;

            div.form-group {
              input.form-control {
                height: 42px;
              }

              i.form-control-feedback {
                font-size: 20px;
                top: 5px;
              }
            }

            button.btn {
              height: 42px;
              padding-left: 20px;
              padding-right: 20px;
              background-color: @socialColor;
              color: #fff;
            }

            .login-controls-google {
              display: flex;
              flex-direction: row;
              justify-content: center;
            }

            .login-controls-form-separator {
              display: flex;
              align-items: center;
              text-align: center;
              color: @defaultColor;
              font-size: 17px;
              font-weight: 300;
              line-height: 23px;
              margin-bottom: 10px;
              margin-top: 10px;
              flex-grow: 1;
              flex-shrink: 1;
              width: 100%;

              &:before {
                content: "";
                flex: 1;
                border-bottom: 1px solid @defaultColor;
                margin-right: .5em;
              }

              &:after {
                content: "";
                flex: 1;
                border-bottom: 1px solid @defaultColor;
                margin-left: .5em;
              }
            }
          }

          .login-controls-errors {
            .alert {
              margin-bottom: 0;
              margin-top: 5px;

              &:first-child {
                margin-top: 0;
              }
            }
          }

          .login-controls-loading {
            margin-top: 5px;
          }

          .copyright {
            position: absolute;
            bottom: 0;
            padding-bottom: 20px;
            left: 0;
            width: 100%;

            img {
              margin: 0 auto;
              padding-bottom: 10px;
            }

            & > div {
              font: 10px var(--default-font);
              color: #96969e;
              text-align: center;
              bottom: 0;
            }
          }

          .progress-bar {
            background-color: @socialColor;
          }
        }

        @media screen and (max-width: 1400px) {
          width: 100%;
          display: flex !important;
          flex-direction: row;
          align-items: center;
          justify-content: center;

          .login-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding-right: 0;
            img {
              width: initial;
              height: initial;
              max-width: 853px;
              max-height: 922px;
              position: absolute;
              bottom: 0;
              right: 0;
            }
          }

          .login-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            float: initial;
            height: initial;
            padding: 20px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
            max-width: 496px;
            min-width: 496px;
            width: initial;

            .login-controls-logo {
              flex-grow: 0;
              flex-shrink: 0;
              width: 100%;
              text-align: center;
            }

            .login-controls-form {
              flex-grow: 0;
              flex-shrink: 0;
              width: 100%;
            }

            .login-controls-errors {
              flex-grow: 0;
              flex-shrink: 0;
              width: 100%;
            }

            .login-controls-loading {
              flex-grow: 0;
              flex-shrink: 0;
              width: 100%;
            }

            .copyright {
              position: fixed;
            }
          }
        }

        @media screen and (max-width: 550px) {
          .login-controls {
            min-width: 70%;
            max-width: 80%;
          }
        }
      }
    }
  }

  &.has-messages-to-answer {
    div.has-messages-to-answer {
      display: block;
    }
  }

  &.no-messages-to-answer {
    div.no-messages-to-answer {
      display: block;
    }
  }

  &.inLogin, &.has-messages-to-answer, &.no-messages-to-answer, &.with-chat-message, &.outgoing-message {
    body {
      height: 100%;
      margin-top: 0;
      overflow-y: hidden;

      div.wrapper {
        height: calc(100% ~"-" @headerHeight);
        min-height: 0;
        margin-top: @headerHeight;
        background-color: rgba(0, 0, 0, 0);
        overflow-y: auto;

        div.content-wrapper {
          height: 100%;
          padding: 15px;
          section.content {
            padding: 0;
            height: 100%;
            min-height: 100%;
            div.view {
              height: 100%;
              display: block;
              width: 100%;
              & > div.selected-message, & > div.has-messages-to-answer, & > div.no-messages, & > div.inLogin {
                height: 100%;
                display: block;
                width: 100%;

                & > div.row {
                  margin-bottom: 10px;
                }

                div.selected-message-container {
                  height: 100%;

                  div.selected-message-container-chat {
                    height: 100%;
                    position: relative;

                    .connection-problems-container, .connecting-container {
                      position: absolute;
                      z-index: 10000;
                      top: 0;
                      right: 0;
                      bottom: 0;
                      left: 0;
                      height: 0;
                      overflow: hidden;
                      opacity: 0;
                      filter: alpha(opacity=00);
                      -webkit-transition: height 0s linear 200ms, opacity 200ms ease 0s;
                      transition: height 0s linear 200ms, opacity 200ms ease 0s;

                      .overlay {
                        width: 100%;
                        height: 100%;
                        opacity: 0.5;
                        filter: alpha(opacity=50);
                        background-color: white;
                      }

                      .message-container {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 0;
                        right: 0;
                        text-align: center;
                        z-index: 10001;

                        .message {
                          display: inline-block;
                          text-align: left;
                          background-color: #333;
                          color: #f5f5f5;
                          padding: 20px;
                          border-radius: 4px;
                          font-size: 20px;
                          font-weight: bold;
                          box-shadow: 0 0 20px rgb(0 0 0);
                          min-width: 500px;

                          .connecting, .error, .reconnecting, .waiting {
                            display: none;
                          }

                          .moreinfo {
                            margin-top: 10px;
                            font-size: 14px;
                            font-weight: normal;
                            max-width: 500px;
                          }
                        }
                      }
                    }

                    &.with-connection-problems {
                      .connection-problems-container {
                        height: 100%;
                        opacity: 1;
                      }

                      &.connecting {
                        .message-container {
                          .message {
                            .reconnecting {
                              display: block;
                            }
                          }
                        }
                      }

                      &.disconnected {
                        &:not(.connecting) {
                          .message-container {
                            .message {
                              .error {
                                display: block;
                              }
                            }
                          }
                        }
                      }
                    }

                    &.connecting:not(.with-connection-problems) {
                      .connecting-container {
                        height: 100%;
                        opacity: 1;

                        .message-container {
                          .message {
                            .connecting {
                              display: block;
                            }
                          }
                        }
                      }
                    }

                    &.waiting:not(.with-connection-problems):not(.connecting) {
                      .connecting-container {
                        height: 100%;
                        opacity: 1;

                        .message-container {
                          .message {
                            .waiting {
                              display: block;
                            }
                          }
                        }
                      }
                    }

                    div.chat-conversation {
                      height: 100%;
                      padding: 10px;
                      display: flex;
                      flex-direction: column;

                      div.chat-conversation-messages {
                        width: 100%;

                        .chat-conversation();
                      }

                      div.chat-conversation-compose {
                        flex-shrink: 0;
                        width: 100%;

                        .chat-compose-buttons-container {
                          display: flex;
                          flex-direction: row;
                          .btn {
                            margin-right: 3px;
                            align-items: center;
                            justify-content: center;

                            & > i {
                              padding: 0;
                            }

                            &:last-child {
                              margin-right: 0;
                            }
                          }

                          .chat-compose-buttons-edition {
                            flex-grow: 0;
                            flex-shrink: 0;
                            display: flex;
                            flex-direction: row;

                            & > .dropdown {
                              width: 30px;
                              .btn {
                                height: 100%;
                              }
                            }
                          }

                          .chat-compose-buttons-actions {
                            flex-grow: 1;
                            flex-shrink: 1;
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-end;
                          }
                        }

                        div.emoji-selector {
                          overflow-y: scroll;
                          height: 120px;

                          li.emoji {
                            display: inline;
                            width: 20px;

                            a {
                              font-size: 20px;
                              width: 30px;
                              display: inline-block;
                              text-align: center;
                            }
                          }
                        }
                      }

                      div.chat-conversation-returnToQueue {
                        position: relative;
                        bottom: 0;
                        width: 100%;
                        margin-top: 5px;
                      }

                      .chat-conversation-body {
                        // overflow-y:scroll;
                        padding-right: 10px !important;
                      }

                      div.chat-compose {
                        background-color: #dddddd;
                        padding: 7px;

                        .chat-compose-input {
                          display: flex;
                          flex-direction: row;
                          align-items: center;
                          margin-bottom: 5px;

                          .chat-compose-input-send {
                            padding-left: 15px;
                            padding-right: 3px;
                          }

                          #textAreaChatAnswer {
                            resize: none;
                            height: 34px;

                            @media (min-height: 400px) {
                              height: 56px;
                            }
                          }

                          .chat-compose-input-send-loading {
                            display: none;
                            flex-shrink: 0;
                            flex-grow: 0;
                            margin-left: 3px;
                            padding: 0 3px;
                            width: 24px;
                            height: 24px;
                            flex-direction: row;
                            align-items: center;
                            justify-content: center;
                            font-size: 20px;
                          }
                        }

                        &.loading {
                          button, a {
                            pointer-events: none;
                            cursor: not-allowed;
                          }

                          .chat-compose-input {
                            a {
                              display: none;
                            }

                            textarea {
                              background-color: #eee;
                              opacity: 1;
                              cursor: not-allowed;
                            }

                            .chat-compose-input-send-loading {
                              display: flex;
                            }
                          }
                        }
                      }
                    }

                    div.chat-info {
                      height: 100%;
                      position: relative;
                      padding: 0;

                      & > div {
                        position: absolute;
                        left: 0;
                        height: 100%;
                        width: 100%;
                        overflow-y: auto;
                        padding: 10px;
                        overflow-x: hidden;
                        display: flex;
                        flex-direction: column;
                        background-color: @navbarDarkBackgroundColor !important;
                        border-left: 1px solid @navbarDarkBorderColor !important;

                        .scrollbar-dark();

                        .alert-message {
                          margin-bottom: 5px;
                          .alert {
                            margin-bottom: 0;
                            &.alert-info {
                              background-color: #00adb5 !important;
                              border-color: #00adb5;
                            }
                          }
                        }

                        .chat-info-profile {
                          flex-shrink: 0;

                          .chat-info-profile-info {
                            padding: 20px;
                            background-color: @backgroundHeaderUserProfileColor;
                            color: @white;

                            .chat-info-profile-info-name {
                              font-size: 22px;
                              font-weight: bold;

                              a {
                                color: @white;
                                text-decoration: underline;
                              }
                            }

                            .chat-info-profile-info-email, .chat-info-profile-info-id, .chat-info-profile-info-extraparameters {
                              font-size: 120%;
                            }

                            .chat-info-profile-info-extraparameters {
                              background-color: @white;
                              color: #4F4F50;
                              padding: 10px;
                              margin-top: 10px;
                            }

                            &.annonymous {
                              position: relative;
                              &::before {
                                font-size: 40px;
                                font-family: "Font Awesome 5 Pro";
                                content: "\f21b";
                                color: @white;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                              }

                              .chat-info-profile-info-name, .chat-info-profile-info-email {
                                margin-left: 60px;
                              }
                            }
                          }
                        }

                        .box-widget-caseinfo {
                          margin-bottom: 10px;
                        }

                        .chat-info-predefinedanswers {
                          flex-grow: 1;
                          flex-shrink: 1;
                          max-height: 100%;
                          display: flex;
                          flex-direction: column;

                          .box-widget {
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            flex-grow: 1;
                            flex-shrink: 1;

                            .box-widget-header {
                              flex-shrink: 0;
                            }

                            .box-widget-body {
                              flex-grow: 1;
                              flex-shrink: 1;
                              height: 100%;
                              display: flex;
                              flex-direction: column;

                              & > predefined-answers {
                                height: 100%;
                                display: flex;
                                flex-grow: 1;
                                flex-shrink: 1;
                                flex-direction: column;

                                .predefined-answers {
                                  height: 100%;
                                  display: flex;
                                  flex-direction: column;
                                  flex-grow: 1;
                                  flex-shrink: 1;

                                  .predefined-answers-header {
                                    flex-shrink: 0;
                                    margin-bottom: 10px;
                                    & > div.input-group {
                                      width: 100%;
                                    }
                                  }

                                  .predefined-answers-results {
                                    width: 100%;
                                    max-height: 100%;
                                    flex-grow: 1;
                                    flex-shrink: 1;
                                    display: flex;
                                    flex-direction: column;

                                    & > div {
                                      max-height: 100%;
                                      flex-grow: 1;
                                      flex-shrink: 1;
                                      position: relative;
                                      overflow-y: auto;

                                      .predefined-answers-results-rows {
                                        width: 100%;
                                        position: absolute;
                                        top: 0;
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }

              & > div.selected-message, & > div.has-messages-to-answer, & > div.no-messages {
                display: flex;
                flex-direction: column;
              }

              & > div.has-messages-to-answer, & > div.no-messages {
                align-items: center;
                justify-content: center;
              }
            }
          }
        }
      }
    }
  }

  &.inLogin {
    body {
      div.wrapper {
        height: 100%;
        margin-top: 0;

        div.content-wrapper {
          padding: 0;
        }
      }
    }
  }

  &.with-chat-message {
    body {
      div.wrapper {
        height: calc(100% ~"-" @headerHeight);
        margin-top: @headerHeight;

        div.content-wrapper {
          padding: 0;
        }
      }
    }
  }

  &.with-connection-problems {
    &:not(.inLogin) {
      &.connecting {
        .connection-problems {
          display: flex;
          background-color: @yellow;
          color: @defaultColor;

          .connecting {
            display: block;
          }

          .fa-wifi {
            display: block;
          }
        }
      }

      &.disconnected {
        &:not(.connecting) {
          .connection-problems {
            display: flex;
            background-color: @red;
            color: @white;

            .error {
              display: block;
            }

            .fa-wifi-slash {
              display: block;
            }
          }
        }
      }
    }
  }

  &.outgoing-message, &.outgoing-message-writing {
    body {
      div.wrapper {
        height: calc(100% ~"-" @headerHeight);
        margin-top: @headerHeight;

        div.content-wrapper {
          height: 100%;
          padding: 15px;

          .content {
            padding: 0;

            div.outgoing-message-container {
              height: 100%;

              .outgoing-message-definition();
            }
          }
        }
      }
    }
  }

  &:not(.outgoing-message) {
    body {
      min-height: 100%;

      div.wrapper {
        min-height: calc(100% ~"-" @headerHeight);
        margin-top: @headerHeight;

        div.outgoing-message-container {
          min-height: 100%;

          div.outgoing-message {
            min-height: 100%;
          }
        }
      }
    }
  }

  &.with-selected-message {
    height: 100%;
    overflow-y: hidden;
    body {
      height: 100%;
      margin: 0;
      overflow-y: hidden;
      div.wrapper {
        height: calc(100% ~"-" @headerHeight);
        margin-top: @headerHeight;

        div.content-wrapper {
          height: 100%;
          width: 100%;

          .content {
            height: 100%;
            width: 100%;
            padding: 0;
            margin: 0;

            .view {
              height: 100%;
              width: 100%;

              .selected-message {
                height: 100%;
                width: 100%;

                .selected-message-container {
                  height: 100%;
                  width: 100%;

                  div.selected-message-container-all {
                    height: 100%;
                    width: 100%;
                    display: flex;
                    flex-direction: row;

                    div.selected-message-container-all-case {
                      flex-grow: 1;
                      flex-shrink: 1;
                      width: 60%;
                      transition: width 0.5s, height 0.5s;
                    }

                    &:not(.act-as-chat) {
                      div.selected-message-container-all-case {
                        height: 100%;
                        position: relative;
                        padding: 0 15px;

                        & > div {
                          position: absolute;
                          left: 0;
                          height: 100%;
                          width: 100%;
                          overflow-y: auto;
                          padding: 10px;
                          overflow-x: hidden;

                          .scrollbar();
                        }
                      }
                    }

                    div.selected-message-container-all-profile {
                      height: 100%;
                      position: relative;
                      padding: 0;
                      flex-grow: 1;
                      flex-shrink: 1;
                      width: 40%;
                      transition: width 0.5s, height 0.5s;

                      & > div {
                        position: absolute;
                        left: 0;
                        height: 100%;
                        width: 100%;
                        overflow-y: auto;
                        padding: 10px;
                        overflow-x: hidden;
                        background-color: @navbarDarkBackgroundColor !important;
                        border-left: 1px solid @navbarDarkBorderColor !important;

                        .alert-message {
                          margin-bottom: 5px;
                          .alert {
                            margin-bottom: 0;
                            &.alert-info {
                              background-color: #00adb5 !important;
                              border-color: #00adb5;
                            }
                          }
                        }

                        .scrollbar-dark();
                      }
                    }

                    &.act-as-chat {
                      .selected-message-container-all-case {
                        display: flex;
                        height: 100%;
                        padding: 0 15px;

                        & > div {
                          flex-shrink: 1;
                          flex-grow: 1;
                          display: flex;
                          flex-direction: column;
                          padding: 10px 0;
                          height: 100%;

                          .voice-call {
                            background-color: #000;
                            padding: 10px;
                            margin-bottom: 5px;
                            color: #fff;
                            border-radius: 4px;
                            position: relative;

                            .voice-call-icon {
                              position: absolute;
                              opacity: 0.6;
                              left: 10px;
                              top: 50%;
                              transform: translateY(-50%);

                              span {
                                font-size: 50px;
                              }
                            }

                            .voice-call-title {
                              display: flex;
                              flex-direction: row;
                              align-items: center;
                              justify-content: center;
                              font-weight: bold;
                              font-size: 150%;
                              margin-bottom: 10px;
                            }

                            .voice-call-totaltime {
                              position: absolute;
                              opacity: 0.6;
                              right: 10px;
                              top: 50%;
                              transform: translateY(-50%);

                              span {
                                font-size: 30px;
                                font-family: @fontNameMono;
                              }
                            }

                            .voice-call-buttons {
                              display: flex;
                              flex-direction: row;
                              align-items: center;
                              justify-content: center;

                              .btn {
                                line-height: 18px;
                                height: 30px;
                                margin-right: 5px;

                                &:last-child {
                                  margin-right: 0;
                                }

                                &.btn-voice-call-accept {
                                  background-color: rgb(0, 122, 255);
                                  border-color: rgb(0, 122, 255);
                                  color: #fff;
                                }

                                &.btn-voice-call-reject {
                                  background-color: rgb(255, 39, 42);
                                  border-color: rgb(255, 39, 42);
                                  color: #fff;
                                }
                              }
                            }
                          }

                          .social-case {
                            flex-shrink: 1;
                            flex-grow: 1;
                            display: flex;
                            height: 100%;

                            .main-case-panel {
                              flex-shrink: 1;
                              flex-grow: 1;
                              display: flex;
                              height: 100%;

                              & > div {
                                flex-shrink: 1;
                                flex-grow: 1;
                                display: flex;
                                height: 100%;

                                .box-widget-case-header {
                                  display: none !important;
                                }

                                .social-case-conversation {
                                  flex-shrink: 1;
                                  flex-grow: 1;
                                  display: flex;
                                  flex-direction: column;
                                  height: 100%;

                                  .social-case-conversation-header {
                                    flex-shrink: 0;
                                    flex-grow: 0;

                                    a {
                                      color: @defaultColor;
                                      text-decoration: underline;
                                    }
                                  }

                                  .box-widget-body {
                                    flex-shrink: 1;
                                    flex-grow: 1;
                                    display: flex;
                                    flex-direction: column;
                                    height: 100%;
                                    padding: 0;
                                    position: relative;
                                    background-color: rgb(229, 221, 213);

                                    .case-conversation-background {
                                      background-repeat: repeat;
                                      position: absolute;
                                      left: 0;
                                      top: 0;
                                      height: 100%;
                                      width: 100%;

                                      &.whatsapp {
                                        background-image: url(images/BackgroundWhatsappChat.png);
                                        opacity: 0.06;
                                      }

                                      &.telegram {
                                        background-image: url(images/BackgroundTelegramChat.png);
                                        background-position: top right;
                                        background-size: 510px auto;
                                      }
                                    }

                                    case-conversation {
                                      flex-shrink: 1;
                                      flex-grow: 1;
                                      overflow-y: auto;
                                      height: 100%;
                                      position: relative;
                                      padding: 0;

                                      .case-timeline {
                                        position: absolute;
                                        left: 0;
                                        height: 100%;
                                        width: 100%;
                                        overflow-y: auto;
                                        padding: 10px;
                                        overflow-x: hidden;
                                        background-color: transparent;

                                        & > div {
                                          .case-conversation-item-date {
                                            display: flex;
                                            flex-direction: row;
                                            justify-content: center;
                                            margin: 3px 0;

                                            .text {
                                              font-size: 80%;
                                              padding: 6px 12px;
                                              background-color: rgba(225, 245, 254, 0.92);
                                              border-radius: 10px;
                                            }
                                          }

                                          .case-conversation-item {
                                            width: 100%;
                                            margin-left: 0;
                                            margin-right: 0;
                                            align-items: flex-end;

                                            .reaction {
                                              display: none;
                                              position: absolute;
                                            }

                                            &.with-reaction {
                                              padding-bottom: 15px;
                                              position: relative;

                                              .reaction {
                                                display: flex;
                                                flex-direction: row;
                                                align-items: center;
                                                justify-content: center;
                                                bottom: 0;
                                                z-index: 1000;
                                                border-radius: 10px;
                                                padding: 1px 2px;
                                                box-shadow: 0 1px 0 rgb(0 0 0 / 7%), 0 0 3px rgb(0 0 0 / 4%);
                                                width: 25px;
                                                border: 1px solid rgba(0, 0, 0, 0.05);

                                                & > div {
                                                  height: 16px;

                                                  .emoji {
                                                    width: 16px;
                                                    height: 16px;
                                                  }
                                                }
                                              }
                                            }

                                            .header {
                                              display: flex;
                                              flex-direction: row;

                                              .date-case {
                                                text-decoration: underline;
                                                font-size: 80%;
                                                color: #000;
                                              }

                                              .icons {
                                                display: flex;
                                                flex-direction: row;
                                                margin-left: 3px;
                                                transform: scale(0.3);
                                                opacity: 0;
                                                transition: opacity 200ms cubic-bezier(0.2, 0.7, 0.5, 1), transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1), -webkit-transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1);

                                                i, a {
                                                  margin-right: 3px;
                                                  color: black;
                                                }
                                              }
                                            }

                                            .conversation-avatar {
                                              width: 20px;
                                              .case-images {
                                                width: 16px;
                                                height: 16px;
                                              }
                                            }

                                            .conversation-message {
                                              padding: 3px 10px;

                                              .context-message-definition();
                                            }

                                            &.client {
                                              .conversation-message {
                                                text-align: left;
                                                /* min-width: 30%; */
                                                width: initial;
                                                max-width: 60%;
                                                flex-grow: 0;
                                                flex-shrink: 0;
                                                background-color: white;

                                                .context-message {
                                                  background-color: darken(white, 10%);
                                                }
                                              }

                                              .header {
                                                margin-left: 3px;
                                              }

                                              .reaction {
                                                left: 30px;
                                                background-color: white;
                                              }
                                            }

                                            &.summary-conversation {
                                              .conversation-message {
                                                text-align: right;
                                                width: initial;
                                                max-width: 60%;
                                                flex-grow: 0;
                                                flex-shrink: 0;
                                                position: relative;
                                                background-color: @color-summary;
                                                border-color: rgb(220, 248, 198);
                                                box-shadow: rgba(0, 0, 0, 0.13) 0 1px 0.5px 0;
                                              }
                                            }
                                            &.system, &.agent, &.supervisor {
                                              .conversation-message {
                                                text-align: right;
                                                /* min-width: 30%; */
                                                width: initial;
                                                max-width: 60%;
                                                flex-grow: 0;
                                                flex-shrink: 0;
                                                position: relative;
                                                background-color: rgb(220, 248, 198);
                                                border-color: rgb(220, 248, 198);
                                                box-shadow: rgba(0, 0, 0, 0.13) 0 1px 0.5px 0;

                                                .contents {
                                                  .body {
                                                    &.yflow {
                                                      & > .title {
                                                        display: none;
                                                      }

                                                      .sm-definition {
                                                        padding: 0;

                                                        & > .sm {
                                                          .contents {
                                                            max-width: initial;
                                                            background-color: transparent;
                                                            border-radius: 0;
                                                            min-height: initial;
                                                            min-width: initial;
                                                            color: #000;

                                                            .text {
                                                              padding: 0;
                                                              white-space: break-spaces;
                                                            }

                                                            .received-message {
                                                              .image {
                                                                margin-bottom: 3px;
                                                                max-height: 100px;
                                                              }
                                                              .title, .subtitle {
                                                                padding: 0;
                                                              }
                                                            }

                                                            .location {
                                                              padding-left: 20px;

                                                              &.with-preview {
                                                                padding-left: 0;

                                                                .location-preview {
                                                                  img {
                                                                    width: 150px;
                                                                    height: 100px;
                                                                  }
                                                                }
                                                              }

                                                              .marker {
                                                                font-size: 100%;
                                                                width: 20px;
                                                              }

                                                              .info {
                                                                font-size: 100%;
                                                              }
                                                            }

                                                            .rich-link {
                                                              .rich-link-image {
                                                                img {
                                                                  width: 150px;
                                                                  height: 100px;
                                                                }
                                                              }

                                                              .rich-link-video {
                                                                video {
                                                                  max-height: 200px;
                                                                  max-width: 100%;
                                                                }
                                                              }

                                                              .info {
                                                                font-size: 100%;
                                                              }
                                                            }

                                                            img {

                                                            }
                                                          }

                                                          .buttons {
                                                            max-width: initial;
                                                            flex-direction: column;

                                                            .button {
                                                              min-width: initial;
                                                              background-color: lighten(rgb(220, 248, 198), 5%);
                                                              border: 1px solid darken(rgb(220, 248, 198), 15%);
                                                              border-radius: 5px;
                                                              padding: 2px 8px;
                                                              color: #000;
                                                              line-height: 24px;
                                                              margin-bottom: 2px;

                                                              &:last-child {
                                                                margin-bottom: 0;
                                                              }
                                                            }
                                                          }

                                                          &:last-child {
                                                            margin-bottom: 0;
                                                          }
                                                        }
                                                      }
                                                    }
                                                  }
                                                }
                                              }

                                              .header {
                                                margin-right: 3px;

                                                .status-icon {
                                                  margin-left: 3px;
                                                }
                                              }

                                              .reaction {
                                                right: 30px;
                                                background-color: rgb(220, 248, 198);
                                              }
                                            }

                                            &:hover {
                                              .header {
                                                .icons {
                                                  transform: scale(1);
                                                  opacity: 1;
                                                }
                                              }
                                            }

                                            &.continues {
                                              margin-bottom: 2px;
                                              .conversation-avatar {
                                                .case-images {
                                                  display: none;
                                                }
                                              }
                                            }

                                            &:not(.continues).same-author {
                                              .conversation-avatar {
                                                .case-images {
                                                  display: block;
                                                }

                                                a {
                                                  display: block;
                                                }
                                              }
                                            }
                                          }
                                        }

                                        &.typing {
                                          .typing-indicator {
                                            background-color: white;
                                          }
                                        }

                                        .scrollbar();
                                      }
                                    }

                                    .social-case-reply-as-chat {
                                      flex-shrink: 0;
                                      flex-grow: 0;
                                      display: flex;
                                      flex-direction: column;
                                      border-top: 1px solid @grayBorders;
                                      border-bottom: 1px solid @grayBorders;
                                      padding: 8px 10px;
                                      background-color: @white;
                                      z-index: 1000;

                                      .social-case-reply-as-chat-context {
                                        display: flex;
                                        flex-direction: row;
                                        align-items: center;
                                        justify-content: center;

                                        .context-message-definition();

                                        .context-message {
                                          flex-grow: 1;
                                          flex-shrink: 1;
                                          background-color: #eee;
                                        }

                                        .remove {
                                          flex-grow: 0;
                                          flex-shrink: 0;
                                          margin-left: 3px;
                                          padding: 0 3px;
                                          width: 30px;
                                          display: flex;
                                          flex-direction: row;
                                          align-items: center;
                                          justify-content: center;

                                          a {
                                            color: black;
                                          }
                                        }
                                      }

                                      .social-case-reply-as-chat-compose {
                                        flex-shrink: 1;
                                        flex-grow: 1;
                                        display: flex;
                                        flex-direction: row;
                                        align-items: center;

                                        textarea {
                                          resize: none;
                                          flex-shrink: 1;
                                          flex-grow: 1;
                                        }

                                        a {
                                          flex-shrink: 0;
                                          flex-grow: 0;
                                          margin-left: 3px;
                                          padding: 0 3px;
                                          width: 24px;
                                          height: 24px;
                                        }

                                        .social-case-reply-as-chat-send-loading {
                                          display: none;
                                          flex-shrink: 0;
                                          flex-grow: 0;
                                          margin-left: 3px;
                                          padding: 0 3px;
                                          width: 24px;
                                          height: 24px;
                                          flex-direction: row;
                                          align-items: center;
                                          justify-content: center;
                                          font-size: 20px;
                                        }
                                      }

                                      .social-case-reply-as-chat-buttons {
                                        flex-shrink: 0;
                                        flex-grow: 0;
                                        display: flex;
                                        flex-direction: row;
                                        align-items: center;
                                        margin-top: 5px;

                                        .btn {
                                          margin-right: 3px;
                                          & > i {
                                            padding: 0;
                                          }

                                          &:last-child {
                                            margin-right: 0;
                                          }
                                        }
                                      }

                                      &.loading {
                                        button, a {
                                          pointer-events: none;
                                          cursor: not-allowed;
                                        }

                                        .social-case-reply-as-chat-compose {
                                          a {
                                            display: none;
                                          }

                                          textarea {
                                            background-color: #eee;
                                            opacity: 1;
                                            cursor: not-allowed;
                                          }

                                          .social-case-reply-as-chat-send-loading {
                                            display: flex;
                                          }
                                        }

                                        .social-case-reply-as-chat-buttons {
                                          cursor: not-allowed;
                                        }
                                      }
                                    }

                                    .social-case-conversation-buttons {
                                      margin-top: 0;
                                      padding: 10px;
                                      background-color: @white;
                                      z-index: 1000;
                                      border-bottom-left-radius: 4px;
                                      border-bottom-right-radius: 4px;

                                      &:not(:has(div .btn, div .btn-group)) {
                                        display: none;
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }

                      &.with-actions {
                        .selected-message-container-all-case {
                          height: 100%;
                          position: relative;
                          padding: 0;

                          & > div {
                            display: block;
                            position: absolute;
                            left: 0;
                            height: 100%;
                            width: 100%;
                            overflow-y: auto;
                            padding: 10px;
                            overflow-x: hidden;

                            .scrollbar();

                            .social-case-conversation-buttons {
                              display: none;
                            }
                          }
                        }
                      }
                    }

                    & > .separator {
                      flex-grow: 0;
                      flex-shrink: 0;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      padding: 0 3px;

                      & > div {
                        cursor: pointer;
                      }

                      &.horizontal {
                        flex-direction: column;

                        & > div {
                          margin-bottom: 5px;

                          &:last-child {
                            margin-bottom: 0;
                          }
                        }
                      }

                      &.vertical {
                        display: none;
                        flex-direction: row;

                        & > div {
                          margin-right: 5px;

                          &:last-child {
                            margin-right: 0;
                          }
                        }
                      }
                    }

                    &.expanded-left {
                      & > .selected-message-container-all-case {
                        width: 80%;
                      }

                      & > .selected-message-container-all-profile {
                        width: 20%;

                        .box {
                          &.box-widget {
                            .box-widget-header {
                              font-size: 100%;

                              &.vip {
                                padding-left: 3px;
                                padding-right: 3px;
                                padding-top: 40px;

                                &::before {
                                  position: absolute;
                                  left: 50%;
                                  top: 0;
                                  transform: translateX(-50%);
                                }
                              }

                              .box-widget-userprofile-header-field, .more-info {
                                font-size: 100%;

                                .box-widget-userprofile-header-field-title {
                                  &.small {
                                    display: block;
                                  }

                                  &.normal {
                                    display: none;
                                  }
                                }
                              }
                            }

                            &.box-widget-caseinfo {
                              .box-widget-body {
                                .case-fields {
                                  .case-field {
                                    flex: 100%;
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }

                    &.expanded-right {
                      & > .selected-message-container-all-case {
                        width: 50%;
                      }

                      & > .selected-message-container-all-profile {
                        width: 50%;
                      }
                    }
                  }
                }

                .outgoing-message-container {
                  .outgoing-message {
                    padding: 10px;

                    .outgoing-message-title {
                      font-size: 150%;
                      font-weight: bold;
                    }

                    .outgoing-message-filters {
                      display: flex;
                      width: 100%;
                      flex-direction: row;
                      flex-wrap: wrap;
                      justify-content: center;

                      & > .box-widget {
                        max-width: 48%;
                        margin-right: 10px;
                        min-width: 450px;
                        margin-top: 10px;
                        max-height: 400px;
                        min-height: 200px;

                        &:last-child {
                          margin-right: 0;
                        }

                        &.box-widget-othertypes {
                          display: flex;
                          flex-direction: column;

                          .box-widget-header {
                            flex-grow: 0;
                            flex-shrink: 0;
                          }

                          .box-widget-body {
                            flex-grow: 1;
                            flex-shrink: 1;
                            display: flex;

                            .outgoing-othertype-buttons {
                              display: flex;
                              flex-direction: row;
                              flex-grow: 1;
                              flex-shrink: 1;
                              justify-content: center;
                              align-items: center;

                              .btn {
                                width: 120px;
                                height: 120px;
                                background-color: rgb(221, 221, 221);
                                margin-right: 5px;
                                display: flex;
                                flex-direction: column;

                                &:last-child {
                                  margin-right: 0;
                                }

                                &:hover {
                                  background-color: lighten(rgb(221, 221, 221), 3%);
                                }

                                .icon {
                                  text-align: center;
                                  font-size: 40px;
                                  color: @socialColor;
                                  flex-grow: 0;
                                  flex-shrink: 0;
                                  display: flex;
                                  flex-direction: row;
                                  justify-content: center;
                                  width: 100%;
                                  height: 50%;
                                  align-items: flex-end;
                                  padding-bottom: 3px;
                                }

                                .title {
                                  text-align: center;
                                  flex-grow: 1;
                                  flex-shrink: 1;
                                  white-space: normal;
                                  padding-top: 3px;
                                }
                              }
                            }
                          }
                        }

                        &.box-widget-searchcases {
                          .form-group {
                            margin-bottom: 3px;

                            .control-label {
                              padding-left: 3px;
                              padding-right: 3px;
                            }

                            & > div {
                              padding-left: 3px;
                            }
                          }

                          .form-buttons {
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-end;
                          }

                          .search-found {
                            margin-top: 5px;

                            .form-buttons {
                              margin-top: 5px;
                            }
                          }

                          .search-didnt-find {
                            margin-top: 5px;
                          }
                        }

                        &.box-widget-searchcasebyid {
                          .form-group {
                            margin-bottom: 3px;

                            .control-label {
                              padding-left: 3px;
                              padding-right: 3px;
                            }

                            & > div {
                              padding-left: 3px;
                            }
                          }
                          .form-buttons {
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-end;
                          }

                          .search-found {
                            margin-top: 5px;

                            .form-buttons {
                              margin-top: 5px;
                            }
                          }

                          .search-didnt-find {
                            margin-top: 5px;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      &.custom-font-size-11, &.custom-font-size-12 {
        @media (max-width: 1300px) {
          div.wrapper {
            div.content-wrapper {
              .content {
                .view {
                  .selected-message {
                    .selected-message-container {
                      div.selected-message-container-all {
                        div.selected-message-container-all-profile {
                          font-size: 10pt;
                        }

                        div.selected-message-container-all-case {
                          .conversation-message {
                            .header {
                              font-size: 10pt;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      div.modal {
        @media (min-width: 1400px) {
          .modal-lg {
            width: 90%;
          }
        }

        @media (min-width: 1920px) {
          .modal-lg {
            width: 1400px;
          }
        }

        .outgoing-message-definition();
      }

      .outgoing-profiles-container();
    }
  }

  &.outgoing-message-whatsapp {
    .whatsapp-outgoing-message {
      margin-top: 10px;

      .whatsapp-outgoing-message-template {
        .alert {
          color: #383d41;
          background-color: #e2e3e5;
          border-color: #d6d8db;
        }
      }
    }
  }

  &.outgoing-message-twitter {
    .whatsapp-outgoing-message {
      margin-top: 10px;
      margin-bottom: 10px;

      .alert-user-not-found {
        color: #383d41;
        background-color: #e2e3e5;
        border-color: #d6d8db;
        margin-bottom: 10px;

        hr {
          margin-top: 10px;
          margin-bottom: 10px;
        }

        .outgoing-message-twitter-info-profile {
          //height: calc(100% ~"-" 60px);
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .avatar {
            width: 60px;
            height: 60px;
          }
        }
      }

      .buttons-footer {
        margin-top: 5px;
      }
    }
  }

  &.outgoing-message-facebook {
    .facebook-outgoing-message {
      margin-top: 10px;
      margin-bottom: 10px;
      .outgoing-message-facebook-container {
        .outgoing-message-facebook-container-header {
          display: flex;
        }

        .outgoing-message-facebook-container-item {
          padding: 4px 0;
          display: flex;
          cursor: pointer;

        }

        .outgoing-message-facebook-container-item:hover {
          background: #e2e3e5;
        }
      }


      .alert-user-not-found {
        color: #383d41;
        background-color: #e2e3e5;
        border-color: #d6d8db;
        margin-bottom: 10px;

        .outgoing-message-facebook-info-profile {
          //height: calc(100% ~"-" 60px);
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .avatar {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
  }

  &.outgoing-message-telegram {
    .telegram-outgoing-message {
      margin-top: 10px;
      margin-bottom: 10px;
      .outgoing-message-telegram-container {
        .outgoing-message-telegram-container-header {
          display: flex;
        }

        .outgoing-message-telegram-container-item {
          padding: 4px 0;
          display: flex;
          cursor: pointer;

        }

        .outgoing-message-telegram-container-item:hover {
          background: #e2e3e5;
        }
      }


      .alert-user-not-found {
        color: #383d41;
        background-color: #e2e3e5;
        border-color: #d6d8db;
        margin-bottom: 10px;

        .outgoing-message-telegram-info-profile {
          //height: calc(100% ~"-" 60px);
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .avatar {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
  }

  &.outgoing-message-instagram {
    .instagram-outgoing-message {
      margin-top: 10px;
      margin-bottom: 10px;
      .outgoing-message-instagram-container {
        .outgoing-message-instagram-container-header {
          display: flex;
        }

        .outgoing-message-instagram-container-item {
          padding: 4px 0;
          display: flex;
          cursor: pointer;

        }

        .outgoing-message-instagram-container-item:hover {
          background: #e2e3e5;
        }
      }


      .alert-user-not-found {
        color: #383d41;
        background-color: #e2e3e5;
        border-color: #d6d8db;
        margin-bottom: 10px;

        .outgoing-message-instagram-info-profile {
          //height: calc(100% ~"-" 60px);
          margin-bottom: 0;
          display: flex;
          align-items: center;

          .avatar {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
  }

  &:not(.inLogin) {
    body {
      .toast-top-left, .toast-top-right {
        top: 50pt !important;
      }
    }
  }

  &.inLogin {
    body {
      div.wrapper {
        min-height: 100%;
        margin-top: 0;
      }
    }
  }
}

a {
  cursor: pointer;
}

body {
  font-family: var(--default-font);
  font-size: @fontSize;
  color: @defaultColor !important;
  font-feature-settings: "ss01" on;

  &.custom-font-size-8 {
    font-size: 8pt;

    .dropdown-menu {
      font-size: 8pt;
    }
  }
  &.custom-font-size-9 {
    font-size: 9pt;

    .dropdown-menu {
      font-size: 9pt;
    }
  }
  &.custom-font-size-10 {
    font-size: 10pt;

    .dropdown-menu {
      font-size: 10pt;
    }
  }
  &.custom-font-size-11 {
    font-size: 11pt;

    .dropdown-menu {
      font-size: 11pt;
    }
  }
  &.custom-font-size-12 {
    font-size: 12pt;

    .dropdown-menu {
      font-size: 12pt;
    }
  }

  div.emoji-picker {
    z-index: 2000;
    display: flex;
    flex-direction: column;

    .emoji-picker__search-container, .emoji-picker__preview {
      flex-grow: 0;
      flex-shrink: 0;
      height: initial;
    }

    .emoji-picker__content {
      flex-grow: 1;
      flex-shrink: 1;
      height: initial;

      .emoji-picker__tabs-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .emoji-picker__tabs {
          flex-grow: 0;
          flex-shrink: 0;
        }

        & > div {
          flex-grow: 1;
          flex-shrink: 1;

          .emoji-picker__tab-body {
            &.active {
              display: flex;
              flex-direction: column;

              h2 {
                flex-grow: 0;
                flex-shrink: 0;
              }

              .emoji-picker__emojis {
                flex-grow: 1;
                flex-shrink: 1;
                height: 227px;
              }
            }
          }
        }
      }
    }

    .emoji-picker__search-container {
      height: 35px;
      margin: 0;
      padding: 3px 5px;

      .emoji-picker__search {
        padding: 0 5px;
        font-family: var(--default-font);
      }

      .emoji-picker__search-icon {
        width: initial;
        height: initial;
        top: calc(50% - 9px);
      }
    }

    .emoji-picker__preview {
      height: 50px;
    }
  }

  header.main-header {
    @backgroundColor: @navbarBackgroundColor;
    height: @headerHeight;
    width: 100%;
    position: fixed;
    font-size: 14px;
    top: 0;
    left: 0;
    background-color: @backgroundColor !important;
    box-shadow: 0 1px 4px -1px darken(@backgroundColor, 40%);

    .navbar {
      height: @headerHeight;
      display: flex;
      flex-direction: row;
      padding: 0 10px;
      margin-bottom: 0;
      border-width: 0;

      .navbar-header {
        flex-grow: 1;
        flex-shrink: 1;
        height: 100%;
        display: flex;
        flex-direction: row;

        .product-logo {
          display: flex;
          align-items: center;
          flex-direction: row;
          margin-right: 30px;

          img {
            width: 40px;
            height: 40px;
          }
        }

        .social {
          margin: 0;
          padding: 0;
          display: flex;
          height: 100%;

          ul {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: row;

            li {
              margin-right: 3px;
              list-style: none outside none;
              display: flex;
              align-items: center;

              .new-case {
                animation: new-case 1.5s infinite;
              }

              @keyframes new-case {
                0% { opacity: .1;}
                50% {opacity: .7;}
                100% {opacity: 1;}
              }

              &:last-child {
                margin-right: 0;
              }

              &.assigned-messages {
                i {
                  line-height: inherit !important;
                  padding-top: 5% !important;
                }

                .navigation-bar-button-more-info {
                  margin-left: 5px;
                  margin-right: 5px;
                  padding: 2px 0;
                  height: 100%;

                  .voice-call {
                    background-color: #000;
                    padding: 2px 5px;
                    color: #fff;
                    position: relative;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    height: 100%;

                    .voice-call-icon {
                      position: absolute;
                      opacity: 0.6;
                      left: 3px;
                      top: 50%;
                      transform: translateY(-50%);

                      span {
                        font-size: 15px;
                      }
                    }

                    .voice-call-title {
                      display: none;
                    }

                    .voice-call-totaltime {
                      margin-left: 8px;
                      font-size: 12px;
                      font-family: @fontNameMono;
                      opacity: 0.6;
                    }

                    .voice-call-callout {
                      position: absolute;
                      padding: 0;
                      width: 0;
                      height: 0;
                      margin: 0;
                      left: -5px;
                      bottom: 18px;
                      border-style: solid;
                      border-right-width: 5px;
                      border-right-color: #000;
                      border-top-width: 5px;
                      border-top-color: transparent;
                      border-left-width: 0;
                      border-left-color: transparent;
                      border-bottom-width: 5px;
                      border-bottom-color: transparent;
                    }

                    .voice-call-buttons {
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      justify-content: center;
                      margin-left: 25px;

                      .btn {
                        line-height: 18px;
                        height: 30px;
                        width: 30px;
                        margin-right: 5px;
                        border-radius: 15px;

                        &:last-child {
                          margin-right: 0;
                        }

                        &.btn-voice-call-accept {
                          background-color: rgb(0, 122, 255);
                          border-color: rgb(0, 122, 255);
                          color: #fff;
                        }

                        &.btn-voice-call-reject {
                          background-color: rgb(255, 39, 42);
                          border-color: rgb(255, 39, 42);
                          color: #fff;
                        }
                      }
                    }
                  }
                }
              }

              &.slots {
                div.slots {
                  background-color: @slotsColor;
                  width: 56px;
                  height: 50px;
                  text-align: center;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: center;

                  & > i {
                    font-size: 28px;
                    color: white;
                  }
                }
              }
            }
          }
        }
      }

      .navbar-custom-menu {
        height: 100%;
        flex-grow: 0;
        flex-shrink: 0;

        .navbar-nav {
          display: flex;
          flex-direction: row;
          height: 100%;

          & > div {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-left: 0;
            height: 100%;
            cursor: pointer;
            padding: 0 10px;

            &:first-child {
              margin-left: 0;
            }

            &:hover, &.open {
              background-color: darken(@backgroundColor, 5%);
            }

            .user-image {
              width: 32px;
              height: 32px;
            }

            &.relative {
              position: relative;

              .dropdown-toggle {
                height: 100%;
              }

              .dropdown-menu {
                border-top-right-radius: 0;
                border-top-left-radius: 0;
                background-color: @backgroundColor;
                margin-top: 1px;
                border-left: 1px solid darken(@backgroundColor, 5%);
                border-right: 1px solid darken(@backgroundColor, 5%);
                border-bottom: 1px solid darken(@backgroundColor, 5%);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);

                .menu-outgoing {
                  max-height: 400px;
                  overflow: auto;
                  list-style: none;
                  padding: 5px;
                  margin-bottom: 0;

                  .menu-outgoing-item {
                    background-color: @backgroundColor;
                    border-bottom: 1px solid darken(@backgroundColor, 5%);

                    &:hover {
                      background-color: darken(@backgroundColor, 5%);
                      cursor: pointer;
                    }

                    &:last-child {
                      border-bottom-style: none;
                    }

                    a {
                      border-bottom: 0 !important;
                      color: #4F4F50;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      display: table;
                      height: 30px;
                      width: 100%;

                      & > div {
                        height: 30px;
                        display: flex;
                        flex-direction: row;
                        padding-left: 3px;
                        padding-right: 3px;
                        width: 100%;
                        align-items: center;

                        .icon {
                          flex-grow: 0;
                          flex-shrink: 0;
                        }

                        .text {
                          flex-grow: 1;
                          flex-shrink: 1;
                          padding-left: 5px;
                        }
                      }
                    }
                  }
                }

                .menu-states {
                  max-height: 400px;
                  overflow: auto;
                  list-style: none;
                  padding: 5px;
                  margin-bottom: 0;

                  .agentState {
                    background-color: @backgroundColor;
                    &:hover {
                      background-color: darken(@backgroundColor, 5%);
                      cursor: pointer;
                    }
                    a {
                      border-bottom: 0 !important;
                      color: #4F4F50;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      display: table;
                      height: 30px;
                      width: 100%;
                      & > div {
                        height: 30px;
                        display: table;
                        padding-left: 3px;
                        padding-right: 3px;
                        width: 100%;
                        border-bottom: 1px solid darken(@backgroundColor, 5%);
                        div.state-icon {
                          display: table-cell;
                          height: 100%;
                          vertical-align: middle;
                          width: 22px;
                        }
                        div.state-name {
                          display: table-cell;
                          vertical-align: middle;
                          padding-left: 5px;
                        }
                      }
                    }
                    &:last-child > a > div {
                      border-bottom: 0 solid #dddddd;
                    }
                  }
                }

                &.user-info {
                  position: absolute;
                  right: 0;
                  left: auto;
                  width: 280px;

                  .user-header {
                    padding: 10px;
                    text-align: center;

                    p {
                      margin-bottom: 2px;

                      &.username {
                        font-weight: bold;
                      }

                      &.version {
                        font-style: italic;
                        font-size: 0.9em;
                      }

                      &:last-child {
                        margin-bottom: 0;
                      }
                    }

                    img {
                      width: 60px;
                      margin-bottom: 5px;
                    }
                  }

                  .user-body {
                    border-top: 1px solid darken(@backgroundColor, 5%);

                    .row {
                      margin-left: 0;
                      margin-right: 0;
                      .button-profile {
                        padding-top: 14px;
                        padding-bottom: 14px;
                        text-align: center;
                      }
                    }
                  }
                }
              }

              &.open {
                .dropdown-menu {
                  position: absolute;
                  float: left;
                  margin: 2px 0 0;
                }
              }
            }

            a {
              color: @defaultColor;
              padding: 0;
            }

            .button {
              display: flex;
              flex-direction: row;
              align-items: center;
              height: 100%;

              .title {
                margin-left: 5px;
                line-height: 22px;
              }

              timer {
                .title {
                  font-family: var(--default-mono-font);
                }
              }
            }
          }
        }

        .divided-circle {
          width: 22px;
          height: 22px;

          div {
            width: 22px;
            height: 22px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position-y: -1px;
          }
        }
      }

      @media screen and (max-width: 550px) {
        .navbar-header {
          .product-logo {
            display: none;
          }

          .social {
            ul {

              li {
                margin-right: 2px;

                .new-case {
                   animation: new-case 2s infinite;
                }

                @keyframes new-case {
                  0%{opacity: .1;}
                  50%{opacity: .7;}
                  100%{opacity: 1;}
                }

                .navigation-bar-button {
                  width: 30px;

                  & > div {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;

                    .navigation-bar-icons {
                      font-size: 16px;
                    }

                    & > :not(.navigation-bar-icons) {
                      font-size: 10px;
                    }

                    .unread, .label-chat-unread, .label-chat-disconnected {
                      right: -3px;
                      top: -3px;
                    }

                    .vim {
                      right: -3px;
                      bottom: -3px;
                    }

                    .verified {
                      left: -3px;
                      bottom: -3px;
                    }
                  }
                }

                &.slots {
                  div.slots {
                    width: 30px;

                    & > i {
                      font-size: 16px;
                    }
                  }
                }
              }
            }

            .navigation-messages-popover {
              display: none !important;
            }
          }
        }

        .navbar-custom-menu {
          .navbar-nav {
            & > div {
              margin-left: 3px;
              padding: 0 5px;
            }
          }

          .divided-circle {
            width: 16px;
            height: 16px;

            div {
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }

  div.wrapper {
    margin-top: @headerHeight;

    div.content-wrapper {
      section.content {
        padding-top: 0;
        //overflow-y:scroll;
        //overflow-x:hidden;
      }
    }
  }

  &.background-outgoing-message, &.background-body-color, &.background-selected-message {
    background-color: @backgroundSelectedSocialCaseColor !important;
    div.wrapper {
      div.content-wrapper {
        background-color: @backgroundSelectedSocialCaseColor !important;
      }
    }
  }
}

div.social-case {
  .social-case-conversation {
    box-shadow: initial;
    margin-bottom: 0;

    .case-timeline {
      @media (min-width: 991px) {
        background-color: #fff;
        color: #000;
      }

      .case-timeline();
    }

    .social-case-conversation-buttons {
      border-radius: initial;
      display: flex;
      flex-direction: row;

      .social-case-conversation-buttons-left {
        flex-grow: 0;
        flex-shrink: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;

        .btn {
          max-height: initial;
          line-height: 18px;
          height: 30px;
        }
      }

      .social-case-conversation-buttons-right {
        flex-grow: 1;
        flex-shrink: 1;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        flex-wrap: wrap;
        margin-top: -5px;

        .btn {
          margin-left: 5px;
          margin-top: 5px;
          max-height: initial;
          line-height: 18px;
          height: 30px;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }
}

div.social-case-reply {
  margin-top: 10px;
  border-radius: initial;
  border: 0 solid @grayBackgroundStates;

  & > div {
    border: 1px solid @grayBackgroundStates;
    margin-bottom: 0;
    box-shadow: initial;
    border-radius: initial;

    & > div:first-child {
      border-radius: initial;
    }
  }
}

html.has-messages-to-answer {
  .hey-message {
    font-size: 40px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 0;
  }

  .has-messages-to-answer {
    font-size: 24px;
    text-align: center;
    font-weight: bold;
    margin-top: 5px;
  }
}

html.no-messages-to-answer {
  .no-messages-title {
    font-size: 24px;
    text-align: center;
    font-weight: bold;
  }
}

/* H1 - H6 font */
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: var(--default-font);
  font-weight: bold;
}

.emoji-popup-container {
  padding: 0;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
  div.emoji-popup {
    max-height: 250px;
    overflow-y: auto;
    width: 250px;
    padding: 5px;
    div.emoji-category {
      .emoji-category-title {
        font-weight: bold;
        border-bottom: 1px solid rgba(0, 0, 0, 0.25);
        display: block;
        padding-top: 5px;
        padding-bottom: 5px;
      }
      .emoji-category-items {
        .emoji-item {
          border: 0;
          background: transparent no-repeat center;
          background-size: 1.25em 1.25em;
          border-radius: 2px;
          box-sizing: content-box;
          font-size: 14px;
          height: 1.25em;
          margin: 1.5px;
          padding: 3.5px;
          transition: background-size .2s;
          vertical-align: top;
          width: 1.25em;

          &:hover {
            background-size: 1.7em 1.7em;
          }
        }
      }
    }
  }
}

/* Dropdown navegacion de estados de agente */

.row-eq-height {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.user-image-profile {
  margin-top: 6px;
}

i.channel.fa-facebook-square, i.channel.fa-facebook {
  color: @facebookColor;
}

i.channel.fa-twitter-square {
  color: @twitterColor;
}

i.channel.fa-envelope {
  color: #000;
}

/* Estilos para los casos */

.fa-stack {
  font-size: 14px !important;
}

.fa-circle {
  color: @gray !important;
}

.timeline {
  & > li {
    /*			&>.timeline-item > .time {
    font-size:15px !important;
    }*/
    & > .timeline-item > .timeline-header {
      padding: 7px !important;
    }
    & > .img-case-message {
      width: 30px;
      height: 30px;
      font-size: 15px;
      line-height: 30px;
      position: absolute;
      color: #666;
      background: #d2d6de;
      border-radius: 50%;
      text-align: center;
      left: 18px;
      top: 0;
    }
  }
}

.icon-margin {
  margin-right: 5px !important;
}

.callout.callout-social {
  background-color: @orange !important;
  border-color: @orangeStrong;
  color: @white;
}

.button-footer-case {
  float: right;
}

/* Estilos para la barra de navegacion */

.separator-right {
  margin-right: 10px;
}

/* Estilos para los modals */

.modal {
  .modal-dialog {
    .modal-content {
      .modal-header {
        &.modal-header-social {
          border-bottom: 1px solid @grayBorders !important;
          background-color: @grayBackgroundStates;
        }

        @media (max-height: 550px) {
          padding: 5px 15px;
        }
      }

      .modal-body {
        .nav-tabs-custom {
          .nav-tabs {
            li {
              a {
                padding: 5px 15px;
              }
            }
          }
        }
      }
    }

    &.modal-predefined-answers {
      height: 90%;

      .modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-header {
          flex-grow: 0;
          flex-shrink: 0;
        }

        .modal-body {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;

          .alert {
            margin-bottom: 5px;
            padding: 5px 10px;
            flex-shrink: 0;
            flex-grow: 0;
          }

          predefined-answers {
            flex-shrink: 1;
            flex-grow: 1;

            .predefined-answers {
              display: flex;
              flex-direction: column;
              height: 100%;

              .predefined-answers-header {
                flex-shrink: 0;
                flex-grow: 0;
                margin-bottom: 5px;

                & > .input-group {
                  width: 100%;

                  .input-group-btn, .input-group-addon {
                    border-radius: 0;

                    .btn {
                      width: 100%;
                      height: 34px;
                      border-radius: 0;
                      margin-right: 0;
                    }
                  }

                  .input-group-addon {
                    .btn {
                      height: 32px;
                    }
                  }
                }
              }

              .predefined-answers-results {
                flex-shrink: 1;
                flex-grow: 1;
                position: relative;
                height: 100%;
                width: 100%;

                & > div {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 0;
                  left: 0;
                  overflow-y: auto;

                  .predefined-answers-results-rows {
                    width: 100%;

                    .predefined-answer-row(10px);
                  }
                }
              }
            }
          }
        }
      }
    }

    &.modal-email-contact {
      height: 90%;
    
      .modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;
    
        .modal-header {
          flex-grow: 0;
          flex-shrink: 0;
        }
    
        .modal-body {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;
    
          .alert {
            margin-bottom: 5px;
            padding: 5px 10px;
            flex-shrink: 0;
            flex-grow: 0;
          }
    
          email-contacts {
            flex-shrink: 1;
            flex-grow: 1;
    
            .email-contact-container {
              display: flex;
              flex-direction: column;
              height: 100%;
    
              .email-contact-header {
                flex-shrink: 0;
                flex-grow: 0;
                margin-bottom: 5px;
    
                > .input-group {
                  width: 100%;
    
                  .input-group-btn,
                  .input-group-addon {
                    border-radius: 0;
    
                    .btn {
                      width: 100%;
                      height: 34px;
                      border-radius: 0;
                      margin-right: 0;
                    }
                  }
    
                  .input-group-addon {
                    .btn {
                      height: 32px;
                    }
                  }
                }
              }
    
              .email-contact-results {
                flex-shrink: 1;
                flex-grow: 1;
                position: relative;
                height: 100%;
                width: 100%;
    
                > div {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 0;
                  left: 0;
                  overflow-y: auto;
    
                  .email-contact-results-rows {
                    width: 100%;
    
                    .email-contact-row {
                      padding: 10px;
                      margin: 0;
    
                      &:hover {
                        background-color: #ecf0f1;
                      }
    
                      &.odd {
                        background-color: #dddddd;
                      }
    
                      > div:first-child {
                        white-space: nowrap;
                        padding-left: 5px;
                        padding-right: 5px;
    
                        i.fa,
                        i.far,
                        i.fal {
                          font-size: 16px;
                          color: #000;
                        }
    
                        .email-contact-row-action i.fa,
                        .email-contact-row-action i.far,
                        .email-contact-row-action i.fal {
                          color: #E0CE24;
                        }
                      }
    
                      .email-contact-row-body {
                        -ms-text-overflow: ellipsis;
                        text-overflow: ellipsis;
                        overflow-x: hidden;
                        width: 100%;
                        white-space: nowrap;
    
                        &:hover {
                          -ms-text-overflow: unset;
                          text-overflow: unset;
                          white-space: unset;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }    

    &.modal-my-predefined-answers {
      .modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-header {
          flex-grow: 0;
          flex-shrink: 0;
        }
        .my-predefined-answer-modal {
          resize: vertical;
          max-height: 300px;
          min-height: 80px;
        }
      }
    }

    &.modal-case {
      height: 90%;

      .modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-header {
          flex-grow: 0;
          flex-shrink: 0;

          .case-closed {
            background-color: @redColor;
            margin-left: 5px;
            padding: 3px 7px;
            color: white;
            border-radius: 3px;
          }
        }

        .modal-body {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;

          & > div {
            height: 100%;
            width: 100%;
          }

          div.selected-message-container-all-case {
            height: 100%;
            position: relative;
            padding: 0;

            & > div {
              margin-right: 5px;
              position: absolute;
              left: 0;
              height: 100%;
              width: 100%;
              overflow-y: auto;
              padding: 0;
              overflow-x: hidden;

              .scrollbar();
            }
          }

          div.selected-message-container-all-profile {
            height: 100%;
            position: relative;
            padding: 0;

            & > div {
              margin-left: 5px;
              position: absolute;
              left: 0;
              height: 100%;
              width: 100%;
              overflow-y: auto;
              padding: 10px;
              overflow-x: hidden;
              background-color: @navbarDarkBackgroundColor !important;
              border-left: 1px solid @navbarDarkBorderColor !important;

              .alert-message {
                margin-bottom: 5px;
                .alert {
                  margin-bottom: 0;
                }
              }

              .scrollbar-dark();
            }
          }

          div.chat-conversation {
            height: 100%;
            padding: 0;

            & > div {
              height: 100%;
              margin-right: 5px;
              display: flex;
              flex-direction: column;

              .chat-conversation-messages {
                width: 100%;
                .chat-conversation();

                .chat-conversation-messages-info {
                  display: none;
                }
              }
            }
          }

          div.chat-info {
            height: 100%;
            padding: 10px;
            background-color: @navbarDarkBackgroundColor !important;
            border-left: 1px solid @navbarDarkBorderColor !important;
          }
        }

        .modal-footer {
          padding-top: 0;
        }
      }
    }

    &.modal-outgoing-mail {
      height: 80%;
      @media (min-width: 768px) {
        height: calc(100% - 60px);
      }

      .modal-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-header {
          flex-grow: 0;
          flex-shrink: 0;
        }

        .modal-body {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;
          overflow-y: auto;

          .main-case-panel {
            flex-grow: 1;
            flex-shrink: 1;
            display: flex;
            flex-direction: column;

            .outgoing-message {
              flex-grow: 1;
              flex-shrink: 1;
              display: flex;
              flex-direction: column;

              .outgoing-message-title, .outgoing-message-navigation {
                flex-grow: 0;
                flex-shrink: 0;
              }

              .outgoing-message-controls {
                flex-grow: 1;
                flex-shrink: 1;
                display: flex;
                flex-direction: column;
                overflow-y: auto;

                outgoing-message-cases {
                  flex-grow: 1;
                  flex-shrink: 1;
                  display: flex;
                  flex-direction: column;

                  .outgoing-message-cases {
                    flex-grow: 1;
                    flex-shrink: 1;
                    display: flex;
                    flex-direction: column;

                    .outgoing-message-cases-filter {
                      flex-grow: 0;
                      flex-shrink: 0;
                    }

                    .outgoing-message-cases-profilecases {
                      flex-grow: 1;
                      flex-shrink: 1;
                      display: flex;
                      flex-direction: column;

                      .outgoing-message-cases-profilecases-info {
                        flex-grow: 1;
                        flex-shrink: 1;
                        display: flex;
                        flex-direction: column;

                        .row {
                          flex-grow: 1;
                          flex-shrink: 1;
                          display: flex;
                          flex-direction: row;
                          margin: 0;

                          & > div {
                            flex-grow: 1;
                            flex-shrink: 1;
                            position: relative;
                            height: 100% !important;
                            max-height: 100% !important;
                            padding-top: 5px;
                            padding-bottom: 5px;

                            &:first-child {
                              margin-right: 5px;
                            }

                            &:last-child {
                              margin-left: 5px;
                            }

                            .outgoing-cases-results, .outgoing-cases-details {
                              top: 5px;
                              left: 0;
                              width: 100%;
                              height: calc(100% - 10px) !important;
                              max-height: calc(100% - 10px) !important;
                              overflow-y: auto;
                              padding: 10px;
                              margin: 0;
                              overflow-x: hidden;

                              .scrollbar();
                            }
                          }
                        }
                      }

                      .outgoing-message-cases-profilecases-buttons {
                        flex-grow: 0;
                        flex-shrink: 0;
                      }
                    }
                  }

                  .outgoing-message-cases-compose {
                    flex-grow: 1;
                    flex-shrink: 1;
                    display: flex;
                    flex-direction: column;

                    & > .alert {
                      flex-grow: 0;
                      flex-shrink: 0;
                      margin-bottom: 5px;
                    }

                    .outgoing-message-cases-compose-composer {
                      flex-grow: 1;
                      flex-shrink: 1;
                      display: flex;
                      flex-direction: column;

                      .box-widget-reply {
                        flex-grow: 1;
                        flex-shrink: 1;
                        display: flex;
                        flex-direction: column;

                        .box-widget-header {
                          flex-grow: 0;
                          flex-shrink: 0;
                        }

                        .box-widget-body {
                          flex-grow: 1;
                          flex-shrink: 1;
                          height: 100%;
                          position: relative;

                          & > form {
                            top: 10px;
                            left: 10px;
                            height: calc(100% - 20px);
                            width: calc(100% - 20px);
                            overflow-y: auto;
                            overflow-x: hidden;
                            position: absolute;
                            padding-left: 10px;
                            padding-right: 10px;
                          }
                        }
                      }
                    }
                  }
                }

                outgoing-message-mail {
                  flex-grow: 1;
                  flex-shrink: 1;
                  display: flex;
                  flex-direction: column;

                  .outgoing-message-mail {
                    flex-grow: 1;
                    flex-shrink: 1;
                    display: flex;
                    flex-direction: column;

                    .outgoing-message-mail-alert {
                      flex-grow: 0;
                      flex-shrink: 0;

                      .alert {
                        margin-bottom: 5px;
                      }
                    }

                    .outgoing-message-mail-composer {
                      flex-grow: 1;
                      flex-shrink: 1;
                      display: flex;
                      flex-direction: column;

                      & > div {
                        flex-grow: 1;
                        flex-shrink: 1;
                        display: flex;
                        flex-direction: column;

                        .box-widget-reply {
                          flex-grow: 1;
                          flex-shrink: 1;
                          display: flex;
                          flex-direction: column;

                          .box-widget-header {
                            flex-grow: 0;
                            flex-shrink: 0;
                          }

                          .box-widget-body {
                            flex-grow: 1;
                            flex-shrink: 1;
                            height: 100%;
                            position: relative;

                            & > form {
                              top: 10px;
                              left: 10px;
                              height: calc(100% - 20px);
                              width: calc(100% - 20px);
                              overflow-y: auto;
                              overflow-x: hidden;
                              position: absolute;
                              padding-left: 10px;
                              padding-right: 10px;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    &.modal-speechtotext {
      .modal-body {
        .alert {
          margin-bottom: 0;
          padding: 5px;
          width: 100%;
        }

        .icons {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          height: 150px;

          .icon {
            padding: 10px;
            border-radius: 25px;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #aaa;
            cursor: pointer;
            position: relative;

            .circle_ripple {
              height: 50px;
              width: 50px;
              background: #36B37E;
              border-radius: 50%;
              animation: ripple 2s infinite;
              position: absolute;
              left: 0;
              top: 0;
              z-index: 0;
              display: none;
            }
            .circle_ripple-2 {
              height: 50px;
              width: 50px;
              background: #36B37E;
              border-radius: 50%;
              animation: ripple-2 2s infinite;
              position: absolute;
              left: 0;
              top: 0;
              z-index: 1;
              display: none;
            }

            &.listening {
              .circle_ripple, .circle_ripple-2 {
                display: block;
              }
            }

            .circle {
              border-radius: 50%;
              background: #EAEAEA;
              position: absolute;
              left: 0;
              top: 0;
              height: 50px;
              width: 50px;

              .circle-2 {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #ffffff;
                position: absolute;
                left: 5px;
                top: 5px;
                box-shadow: 0 0 8px rgba(0, 0, 0, 0.16);
                z-index: 2;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;

                &:hover {
                  background: #eeeeee;
                }

                i {
                  font-size: 20px;
                }
              }
            }


            @keyframes ripple {
              0% {
                transform:scale(1);
              }
              50% {
                transform:scale(2);
                opacity:0.3;
              }
              100% {
                transform:scale(1);
              }
            }

            @keyframes ripple-2 {
              0% {
                transform:scale(1);
              }
              50% {
                transform:scale(2.5);
                opacity:0.3;
              }
              100% {
                transform:scale(1);
              }
            }
          }
        }

        .with-transcript {
          .title {
            font-weight: bold;
          }
          .speech {
            padding: 7px 15px;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            word-wrap: break-word;
            font-family: @fontNameMono;
            border: 1px solid #EAEAEA;

            .scrollbar();
          }
        }
      }
    }
  }
}

.fa-orange {
  color: @orange;
}

.bold {
  font-weight: bold;
}

.main-case-panel {
  border: 0;
}

.fa-edit-white {
  color: white !important;
}

.hr-without-margin {
  margin: 4px;
}

.italic {
  font-style: italic;
}

.fixed {
  position: fixed;
}

.fa-vip {
  color: @white;
  font-size: 70px;
  float: right;
}

.padding-top-10 {
  padding-top: 10px;
}

.fa-facebook-tab.active {
  border-top-color: @facebookColor !important;
}

.fa-youtube-tab.active {
  border-top-color: @YouTubeColor !important;
}

.fa-mercadolibre-tab.active {
  border-top-color: @MercadoLibreColor !important;
}

.fa-instagram-tab.active {
  border-top-color: @instagramColor !important;
}

.fa-skype-tab.active {
  border-top-color: @skypeColor !important;
}

.fa-whatsapp-tab.active, .fa-whatsapp-square-tab.active {
  border-top-color: @whatsappColor !important;
}

.fa-chat-tab.active {
  border-top-color: @chatColor !important;
}

.fa-linkedin-tab.active {
  border-top-color: @linkedinColor !important;
}

.fa-mail-tab.active {
  border-top-color: @mailColor !important;
}

.fa-twitter-tab.active {
  border-top-color: @twitterColor !important;
}

.fa-sms-tab.active {
  border-top-color: @smsColor !important;
}

.fa-telegram-tab.active {
  border-top-color: @telegramColor !important;
}

.fa-apple-tab.active {
  border-top-color: @appleColor !important;
}

.fa-android-tab.active {
  border-top-color: @androidColor !important;
}

.box-header-case-details {
  padding: 0;
}

.a-icon-edit-case {
  font-size: 18px;
}

span.tag {
  font-weight: bold;
  border: 1px solid;
  padding: 3px;
  font-size: 10px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.margin-bottom-7 {
  margin-bottom: 7px;
}

.bold {
  font-weight: bold;
}

a.profile-list {
  padding: 5px 15px !important;
}

.padding-top-7 {
  padding-top: 7px;
}

.chat-attachment-drag {
  border: dashed gray;
  opacity: 0.5;
}

.text-right {
  text-align: right;
}

.margin-8 {
  margin: 8px;
}

.margin-top-10 {
  margin-top: 10px !important;
}

.margin-top-5 {
  margin-top: 5px !important;
}

.widget-user-header {
  &.user-header-social-network {
    padding: 7px !important;
    height: 58px !important;
  }
}

.widget-user-image {
  &.user-image-social-network {
    top: 32% !important;
    left: 10% !important;
  }
}

.widget-user-username {
  &.username-social-network {
    margin-left: 2px !important;
  }
  &.username-social-profile {
    padding-top: 7px !important;
    font-weight: bold;
  }
}

.box-footer {
  &.box-footer-profile {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

.margin-bottom-0 {
  margin-bottom: 0;
}

.orange {
  color: @orange !important;
}

.nav-tabs-custom > .tab-content {
  &.tab-content-profile {
    padding-bottom: 1px !important;
    padding-top: 5px;
  }
}

.tab-content {
  margin-top: 15px;
}

.fa-stack.fa-lg {
  &.social-profile {
    padding-top: 20px !important;
  }
}

.fa.fa-circle-thin.fa-stack-2x {
  &.social-profile {
    font-size: 5em !important;
  }
}

.fa.fa-user.fa-stack-1x {
  &.social-profile {
    font-size: 5em !important;
    padding-top: 0 !important;
    left: -40% !important;
    top: 0 !important;
  }
}

.bg-light-orange {
  background-color: #FFE0B2 !important
}

.upload-drop-zone.drop {
  color: #222;
  border-color: #222;
}

.padding-bottom-0 {
  padding-bottom: 0 !important;
}

.small-box > .inner.inner-messages {
  text-align: center;
}

.box-border {
  border: 0.2pt solid @grayBackgroundStates !important;
}

.small-box-footer.small-font {
  font-size: 12px;
}

.color-white {
  color: white !important;
}

.color-red {
  color: @redColor;
}

.cursor-initial {
  cursor: default;
}

.background-orange {
  background: #f39c12 !important;
}

.nav-tabs-custom.nav-tabs-orange {
  & > .nav-tabs {
    & > li.active {
      border-top-color: @orange;
    }
  }
}

h4 {
  &.margin-1 {
    margin-bottom: 1px;
    margin-top: 1px;
  }
  &.margin-5 {
    margin-bottom: 5px;
    margin-top: 5px;
  }
}

.margin-bottom-5 {
  margin-bottom: 5px !important;
}

.private-message {
  background-color: #FFFF8B !important;
  &:after {
    content: "\f023";
    font-family: "Font Awesome 5 Pro";
    font-size: 40px;
    position: absolute;
    top: 24px;
    right: 10px;
    z-index: 2;
    color: rgba(202, 184, 42, 0.3);
    transform: rotate(45deg);
  }
}

.grouped-message {
  background-color: #A6D6FF !important;
}

.tableUserCases {
  margin-bottom: 4px;
  margin-left: 2px;
  margin-right: 2px;
  border-bottom-style: inset;
  border-bottom-width: 2px;
  &:hover {
    background-color: navajowhite;
  }
}

.tableCasesOutgoing {
  border-bottom-width: 1px;
  border-bottom-style: double;
  cursor: pointer;
  margin-bottom: 3px;
  &.selectedCase {
    background-color: mistyrose !important;
  }
  &:hover {
    background-color: navajowhite;
  }
}

.small-box-messages {
  margin-bottom: 11px !important;
}

ul {
  &.nav.nav-pills {
    margin-bottom: 7px;
  }
}

.nav-pills {
  & > li.active {
    & > a,
    a:hover,
    a:focus {
      color: white;
      background-color: @backgroundBlue;
    }
  }
}

.small-box {
  & > .inner {
    padding: 0;
  }
  & h3 {
    margin: 0;
    font-size: 24px !important;
  }
}

.info-box {
  &.info-box-messages {
    padding: 0 !important;
    margin-left: 0 !important;
    text-align: center;
    min-height: 50px !important;
  }
}

.info-box-icon {
  &.info-box-messages {
    height: 50px !important;
    width: 60px !important;
    font-size: 30px !important;
    line-height: 50px !important;
  }
}

.info-box-content {
  &.info-box-messages {
    line-height: 50px !important;
    margin-left: 0 !important;
    padding: 0 !important;
  }
}

.margin-right-10 {
  margin-right: 10px !important;
}

.margin-right-5 {
  margin-right: 5px;
}

.centered {
  text-align: center;
}

.dash-separator {
  border-right: 1px dashed #333;
}

.description-block {
  &.description-block-profile {
    margin: 0 0 !important;
  }
}

#socialImageProduct {
  padding-top: 7px;
  & > img {
    height: 42px;
    width: 42px;
  }
}

.background-blue {
  background-color: @backgroundBlue !important;
}

#socialLogo {
  text-align: left;
  margin-left: 20px;
}

.user-profile-image {
  top: 10px !important;
}

.dimming.open {
  background: none !important;
  /*  z-index: 1 !important;*/
}

.box-gray {
  border-top-color: @caseHeaderBackgroundColor;
}

.fa-twitter {
  color: @twitterColor;
}

.fa-facebook {
  color: @facebookColor;
}

.fa-youtube {
  color: @YouTubeColor;
}

i.fa-mercadolibre{
  background-image: url('../../Images/mercadolibre.ico');
  background-size: contain;
  height: 150%;
  background-repeat: no-repeat;
  color: @MercadoLibreColor;
}

.fa-handshake{
  color: @MercadoLibreColor;
}

.fa-whatsapp-square {
  color: @whatsappColor;
}

.icon-message-case {
  vertical-align: middle !important;
}

.border-top-grey {
  border-top: 1px solid #ddd;
}

.tooltip {
  font-family: var(--default-font);
  &.fade.left.in {
    & > div.tooltip-inner {
      white-space: nowrap !important;
      max-width: none !important;
    }
  }

  &.fade.bottom.in {
    & > div.tooltip-inner {
      white-space: nowrap !important;
      max-width: none !important;
    }
  }

  &.fade.top.in {
    & > div.tooltip-inner {
      white-space: nowrap !important;
      max-width: none !important;
      z-index: 99;
    }
  }
}

.tag-template .left-panel {
  float: left;
}

.tag-template .left-panel img {
  width: 24px;
  height: 24px;
  vertical-align: middle;
}

.tag-template .right-panel {
  float: left;
  margin-left: 5px;
}

.autocomplete-template .left-panel {
  float: left;
}

.autocomplete-template .left-panel img {
  width: 48px;
  height: 48px;
  vertical-align: middle;
}

.autocomplete-template .right-panel {
  float: left;
  margin-left: 5px;
  margin-top: -5px;
}

.autocomplete-template .right-panel span:first-child {
  font-size: 16px;
}

.autocomplete-template .right-panel span:nth-child(2) {
  font-size: 14px;
  color: gray;
}

.autocomplete-template .right-panel span:last-child {
  display: block;
  font-size: 14px;
  font-style: italic;
}

.font-size-2em {
  font-size: 2em;
}

.no-resize {
  resize: none;
}

.dropdown-menu-custom {
  overflow: auto !important;
  height: auto !important;
}

.dropdown-menu-custom-up {
  overflow: auto !important;
  top: auto !important;
  bottom: 100% !important;
  height: 250px;
}


div.internal-chat-window {
  .internal-chat {
    display: flex;
    flex-direction: column;
    border: 2px solid @navbarBackgroundColor;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    width: 300px;
    max-height: 300px;
    z-index: 10000;

    &.left {
      left: 10px;
      -webkit-box-shadow: 2px -2px 5px 0 rgba(0,0,0,0.5);
      -moz-box-shadow: 2px -2px 5px 0 rgba(0,0,0,0.5);
      box-shadow: 2px -2px 5px 0 rgba(0,0,0,0.5);
    }

    &.right {
      right: 10px;
      -webkit-box-shadow: -2px -2px 5px 0 rgba(0,0,0,0.5);
      -moz-box-shadow: -2px -2px 5px 0 rgba(0,0,0,0.5);
      box-shadow: -2px -2px 5px 0 rgba(0,0,0,0.5);
    }

    .internal-chat-header {
      flex-grow: 0;
      flex-shrink: 0;
      order: 0;
      height: 35px;
      display: flex;
      justify-items: center;
      align-items: center;
      border-bottom: 2px solid @navbarBackgroundColor;
      background-color: @navbarBackgroundColor;
      padding: 3px;

      .internal-chat-header-title {
        font-size: 15px;
        flex-grow: 1;
        flex-shrink: 1;
        padding-left: 5px;
      }

      .internal-chat-header-buttons {
        flex-grow: 0;
        flex-shrink: 0;
        button {
          color: #4F4F50;
        }
      }
    }

    &.collapsed {
      .internal-chat-header {
        opacity: 0.8;
        height: 30px;
      }
    }

    .internal-chat-body {
      padding: 5px !important;
      font-size: 11px;
      flex-grow: 1;
      flex-shrink: 1;
      order: 1;
      max-height: 100%;
      height: 200px;
      overflow-y: auto;
      overflow-x: hidden;

      .internal-chat-body-conversation {
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        .internal-chat-body-conversation-message {
          &.agent {
            .chatmessage_bubble_right(#c6e7f8, #000000);
            .time {
              left: -31px;
            }
          }

          &.supervisor {
            .chatmessage_bubble_left(#f1f0f0, #000000);
            .time {
              right: -31px;
            }
          }
        }
      }
    }

    .internal-chat-footer {
      width: 100%;
      flex-grow: 0;
      flex-shrink: 0;
      order: 2;
      height: 35px;
      border-top: 2px solid @navbarBackgroundColor;
      background-color: @navbarBackgroundColor;
      padding: 3px;

      .internal-chat-footer-online {
        width: 100%;
        display: flex;
        justify-items: center;
        align-items: center;

        .internal-chat-footer-input {
          flex-grow: 1;
          flex-shrink: 1;

          input {
            font-family: var(--default-font);
            font-weight: 400;
            width: 100%;
            resize: none;
            padding: 3px;
            height: 100%;
            font-size: 11px;
          }
        }

        .internal-chat-footer-buttons {
          flex-grow: 0;
          flex-shrink: 0;

          a {
            margin-left: 10px;
          }
        }
      }

      .internal-chat-footer-offline {
        width: 100%;
        display: flex;
        justify-items: center;
        align-items: center;

        .alert {
          padding: 5px;
          margin-bottom: 0;
          flex-grow: 1;
          flex-shrink: 1;
        }
      }
    }
  }
}

hr.optional {
  border: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.75), rgba(0, 0, 0, 0));
}

.padding-right-0 {
  padding-right: 0 !important;
}

.label-notification {
  top: -8px !important;
  right: 7px;
  text-align: center;
  font-size: 11px;
  padding: 1px 3px;
  line-height: 0.9;
}

div .mce-tinymce.mce-container.mce-panel {
  border: 2pt solid @grayBorders;
  border-radius: 3px;
}

/* Selection Model Styles */

hr.gradient {
  border: 0;
  height: 1px;
  background-image: -webkit-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
  background-image: -moz-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
  background-image: -ms-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
  background-image: -o-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
}

div[contenteditable] {
  position: relative;
}

div[contenteditable] span {
  position: absolute;
  margin-left: 1em;
}

.box-footer.box-footer-small {
  padding: 6px !important;
}

.description-block-small {
  margin: 7px 0 !important;
}

.mce-widget.mce-notification.mce-notification-info.mce-has-close {
  display: none;
}

/*---------------------------------------------------*/

.contentwrapper {
  background-color: #F9F9F9 !important;
}

.loginusername:hover,
.loginusername:focus,
.loginpassword:hover,
.loginpassword:focus {
  border: 0.2px solid #E99800 !important;
}

#usu:hover span,
#usu:focus span,
#pass:hover span,
#pass:focus span {
  color: #E99800;
}

.body-user-profile {
  background-color: @grayBackgroundStates;
}

.button-profile {
  text-align: center;
  padding-top: 14px;
  padding-bottom: 14px;
}

.button-profile:hover {
  background-color: @grayd8;
}

.nav-pills > li {
  margin-right: 12px;
  width: 22%;
  text-align: center;
}

.gray-title {
  color: @grayTitles;
}

.orange-title {
  color: @orangeTitle;
}

#messageSegmentsTab > ul > li {
  width: 27%;
}

.yzn-bg-light-blue {
  background-color: @backgroundBlue !important;
}

.yzn-light-blue {
  color: @backgroundBlue !important;
}

.border-light-blue {
  border: 0.2pt solid @backgroundBlue !important;
}

.margin-9-20 {
  margin-top: 9px !important;
  margin-bottom: 20px !important;
}

.center {
  text-align: center;
}

.margin-21-52 {
  margin-top: 21px !important;
  margin-bottom: 52px !important;
}

:not(.input-group-btn):not(.input-group-addon):not(.uib-month) {
  & > .btn {
    font-variant: initial;
    font-family: var(--default-font);
    text-transform: uppercase;
    padding: 4px 7px;
    background-color: #f4f4f4;
    color: #444;
    border-color: #ddd;
    cursor: pointer;
    font-size: 100%;
    max-height: 32px;

    &:not(.btn-icon):not(.btn-box-tool):not(.btn-checkbox):not(.btn-circle) {
      min-width: 60px;
    }
    &:hover {
      background-color: darken(#f4f4f4, 10%);
    }
    &.btn-flat {
      border-radius: 0;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;
      border-width: 1px;
    }
    &.btn-lg {
      font-size: 110%;
      &.btn-action, &.btn-selected {
        & > i {
          font-size: 1.1rem;
        }
      }
    }
    &.btn-sm {
      font-size: 0.9rem;
      padding: 2px 5px;
    }
    &.btn-action, &.btn-selected {
      background-color: @socialColor;
      border-color: darken(@socialColor, 3%);
      // color: @white;
      &:hover {
        background-color: darken(@socialColor, 5%);
        border-color: darken(@socialColor, 8%);
      }
    }
    &:not(.btn-circle) > i, &:not(.btn-circle) > span:not(.caret) {
      padding-right: 3px;
      padding-left: 3px;
    }

    &.btn-box-tool {
      background-color: transparent;
      border-color: transparent;
      padding: 0;

      & > i {
        padding: 0;
      }

      &:hover {
        color: darken(#444, 10%);
      }
    }

    &.btn-icon {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 30px;
      & > i, & > span {
        padding: 0;
      }
    }

    &.btn-checkbox {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      width: 25px;
      height: 25px;
      justify-content: center;
      & > i, & > span {
        padding: 0;
      }
    }

    &.btn-pressed {
      background-color: #CCC;
    }

    &[disabled] {
      cursor: default;
      pointer-events: none;
      background-color: #f4f4f4;
    }
  }
}

button.navigation-facebook {
  background-color: @facebookColor;
}

button.navigation-youtube {
    background-color: @YouTubeColor;
}

button.navigation-googleplay {
  background-color: @GooglePlayColor;
}

button.navigation-mercadolibre {
  background-color: @MercadoLibreColor;
}

button.navigation-twitter {
  background-color: @twitterColor;
}

button.navigation-envelope {
  background-color: @mailColor;
}

button.navigation-comment {
  background-color: @chatColor;
}

button.navigation-sms {
  background-color: @smsColor;
}

button.navigation-whatsapp {
  background-color: @whatsappColor;
}

button.navigation-telegram {
  background-color: @telegramColor;
}

button.navigation-apple {
  background-color: @appleColor;
}

button.navigation-googlerbm {
  background-color: @androidColor; 
}

button.navigation-googlemybusiness {
  background-color: @googleMBColor; 
}

button.navigation-linkedin {
  background-color: @linkedinColor;
}

button.navigation-instagram {
  background-color: @instagramColor;
}

button.navigation-facebook-messenger {
  background-color: @facebookMessenger;
}

button.navigation-skype {
  background-color: @skypeColor;
}

.color-gray-borders {
  color: @grayBorders;
}

.color-green {
  color: #27ae60;
}

.color-red {
  color: #e74c3c;
}

.background-color-gray-background-states {
  background-color: @grayBackgroundStates;
}

.background-color-blue {
  background-color: @backgroundBlue;
}

.color-gray-title {
  color: @grayTitles;
}

.color-orange {
  color: @orange;
}

.color-selected-message {
  color: @selectedSocialCaseColor;
}

.background-gray-light {
  background-color: @grayBackgroundStates !important;
}

.color-facebook {
  color: @facebookColor !important;
}

.color-instagram {
  color: @instagramColor !important;
}

.color-skype {
  color: @skypeColor !important;
}

.color-whatsapp {
  color: @whatsappColor !important;
}

.color-chat {
  color: @chatColor !important;
}

.color-linkedin {
  color: @linkedinColor !important;
}

.color-mail {
  color: @mailColor !important;
}

.color-twitter {
  color: @twitterColor !important;
}

.color-sms {
  color: @smsColor !important;
}

.color-telegram {
  color: @telegramColor !important;
}

.color-apple {
  color: @appleColor !important;
}

.color-facebook-messenger {
  color: @facebookMessenger !important;
}

.color-youtube {
  color: @YouTubeColor !important;
}

.color-mercadolibre {
  color: @MercadoLibreColor !important;
}

.font-medium {
  font-weight: 500;
}

.font-light {
  font-weight: 300;
}

.no-margins {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.font-size-18 {
  font-size: 18px;
}

.text-align-right {
  text-align: right;
}

.case-images {
  width: 35px;
  height: 35px;
}

.font-size-16 {
  font-size: 16px !important;
}

.font-italic {
  font-style: italic;
}

.right-align {
  text-align: right;
}

.left-align {
  text-align: left;
}

.margin-top-6 {
  margin-top: 6px;
}

.float-right {
  float: right;
}

.margin-right-15 {
  margin-right: 15px;
}

.answer-title {
  font-size: 14px;
  font-weight: bold;
  color: @selectedSocialCaseColor;
}

.margin-right-1-percent {
  margin-right: 1%;
}

.padding-20 {
  padding: 20px !important;
}

.padding-left-0 {
  padding-left: 0 !important;
}

.no-border-bottom {
  border-bottom: 0 !important;
}

.no-border-top {
  border-top: 0 !important;
}

.no-padding-left {
  padding-left: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.navigation-messages-popover {
  top: @headerHeight !important;
}

.popover-message {
  padding: 8px 15px;
  font-size: 12px;
  font-family: var(--default-font);
  & > hr {
    margin: 7px -15px;
  }
}

.popover {
  max-width: 500px;
  font-family: var(--default-font) !important;
  z-index: 1200;
}

.background-white {
  background-color: @white !important;
}

.padding-bottom-20 {
  padding-bottom: 20px;
}

.padding-top-20 {
  padding-top: 20px;
}

.box {
  box-shadow: none;
  margin-bottom: 0;
}

.box-header-user-details {
  padding: 15px 20px !important;
}

.color-blue-light {
  color: @backgroundBlue !important;
}

.button-search {
  background-color: @grayTitles;
  color: @white;
  &:hover {
    color: @grayBorders !important;
  }
  & > i {
    border: 0 !important;
  }
}

div.table-interactions {
  padding: 10px 20px 10px 20px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  font-size: 13px !important;
}

.table-interactions-title {
  & > div {
    & > span, a {
      color: @backgroundBlue;
    }
    & > a:hover {
      color: @socialColor;
    }
  }
  font-weight: bold;
  border-bottom: 2pt solid @backgroundBlue;
}

.table-interactions-body {
  border-top: 1pt solid @grayBorders;
}

.icon-message-interactions {
  font-size: 18px !important;
}

.font-size-14 {
  font-size: 14px !important;
}

.align-right {
  text-align: right;
}

.business-information {
  & > input {
    margin-left: -1px;
    padding: 22px;
  }
  & > span {
    padding: 15px;
  }
  & > input,
  & > span {
    border-radius: 3px;
  }
}

.padding-top-5 {
  padding-top: 5px;
}

.background-light-blue {
  background-color: @lightBlueColor !important;
}

.font-size-28 {
  font-size: 28px !important;
}

.margin-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.padding-left-30 {
  padding-left: 30px !important;
}

.font-size-12 {
  font-size: 12px;
}

.width-100-percent {
  width: 100%;
}

.margin-left-minus-14 {
  margin-left: -14px;
}

.margin-left-2 {
  margin-left: 2px;
}

.padding-right-40 {
  padding-right: 40px !important;
}

.padding-left-40 {
  padding-left: 40px !important;
}

.margin-left-5 {
  margin-left: 5px;
}

.btn-circle {
  width: 49px;
  height: 49px;
  text-align: center;
  padding: 5px 0;
  font-size: 20px;
  line-height: 2.00;
  border-radius: 30px;
  background-color: @backgroundSelectedSocialCaseColor;
  &:hover {
    background-color: @lightGrayButtonBackground;
  }
}

.opacity-80 {
  opacity: 0.8;
}

.yzn-callout {
  border-radius: 3px;
}

.padding-top-8 {
  padding-top: 8px;
}

.padding-15 {
  padding: 15px;
}

.vcenter {
  font-size: 13px;
  display: inline-block;
  vertical-align: middle;
  float: none;
}

.padding-right-10 {
  padding-right: 10px;
}

.height-20 {
  height: 20px
}

.padding-top-7 {
  padding-top: 7px;
}

.background-red-light {
  background-color: @lightRed;
}

.color-light-blue {
  color: @lightBlueColor;
}

span.point-blue {
  font-weight: bold;
  color: @lightBlueColor;
}

span.point-gray {
  font-weight: bold;
  color: @grayBorders;
}

span.point-black {
  font-weight: bold;
  color: black;
}

div.mce-tinymce.mce-container.mce-panel {
  border-right: 2px solid @grayBorders !important;
}

#dropdownTwitterServices,
#dropdownMailServices {
  & > div.multiselect-parent {
    width: 115%;
    & > button {
      width: 100%;
      padding-top: 15px;
      padding-bottom: 16px;
      font-size: 12px;
      white-space: pre-wrap;
    }
  }
}

.yzn-select {
  padding: 10px;
  border: 2px solid @lightBlueColor;
}

div.row-profiles:hover {
  & > a {
    color: #000 !important;
  }
}

.input-border-radius {
  border-radius: 3px;
}

.btn-attachment-options {
  padding-left: 36%;
  padding-top: 31%;
  padding-bottom: 19%;
}

@media screen and (min-width: 992px) {
  .modal-lg {
    width: 900px;
    /* New width for large modal */
  }
}
@media screen and (min-width: 1100px) {
  .modal-lg {
    width: 90%;
  }
}

.tooltip-inner {
  max-width: 400px;
}

.tooltip-400max .tooltip-inner {
  max-width: 400px;
}

.chat-buttons-area > div > a {
  padding-top: 8px;
  padding-bottom: 8px;
}

.case-header-user-profile {
  padding-left: 3%;
  background-color: @mailColor;
  padding-top: 13px;
  color: @white !important;
  font-size: 18px;
}

.case-header-col-star {
  text-align: center;
}

.case-header-user-profile {
  & > div.row > a {
    text-decoration: underline !important;
    color: @white !important;
  }
}

.popover-user-profile {
  width: 500px;
  margin: 0;
  padding: 0;
}

.buttons-footer {
  & > a,
  button {
    margin-right: 4px;
  }
}

.margin-right-4 {
  margin-right: 4px;
}

.callout-information {
  border: 1px solid @grayTitles !important;
}

.margin-top-20 {
  margin-top: 20px;
}

.caret-up {
  /* Safari */
  -webkit-transform: rotate(-180deg);
  /* Firefox */
  -moz-transform: rotate(-180deg);
  /* IE */
  -ms-transform: rotate(-180deg);
  /* Opera */
  -o-transform: rotate(-180deg);
  /* Internet Explorer */
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=6);
}

.drop-up {
  top: auto;
  bottom: 100% !important;
}

.drop-left {
  left: auto;
  right: 0;
}

.mail-item-body {
  max-height: 400px;
  overflow-y: auto;
}

.color-inicio {
  color: @colorInicio;
}

.color-etiquetas {
  color: @colorEtiquetas;
}

.color-cola {
  color: @colorCola;
}

.color-observaciones {
  color: @colorObservaciones;
}

.color-mensajes {
  color: @colorMensajes;
}

.color-lightseagreen {
  color: lightseagreen;
}

.color-lightsalmon {
  color: lightsalmon;
}

.color-lightslategray {
  color: lightslategray;
}

.color-lightcoral {
  color: lightcoral;
}

.color-salmon {
  color: salmon;
}

.margin-top-3 {
  margin-top: 3px;
}

.margin-top-2 {
  margin-top: 2px;
}

.padding-10 {
  padding: 10px;
}

#dropdownQueues > div > ul {
  top: inherit;
  bottom: 2%;
}

#dropdownTemplates > div > ul{
  top: inherit;
  bottom: 2%;
}

.selected-chat-button {
  font-weight: bold;
  color: green;
}

.chat-json-results {
  padding: 5px 10px 10px;
  max-height: 100px;
  border: 1px solid #eee;
  overflow: auto;
  margin-right: 13px;
  margin-top: 10px;
  background-color: @white;
}

.color-black {
  color: black;
}

.navigation-bar-button {
  height: 50px;
  width: 50px;
  border: none;
  border-bottom-width: 6px;
  border-bottom-style: solid;
  border-bottom-color: transparent;

  &.selected-message {
    border-bottom: 6px solid @socialColor !important;
    opacity: 0.6;
  }

  &:hover {
    opacity: 0.6;
  }

  & > div {
    position: relative;
  }

  .unread {
    display: none;
    color: @orangeColor;
    right: 0;
    top: 0;
    text-shadow: 0 0 4px black;
    position: absolute;
  }



  .vim {
    display: none;
    color: #333;
    right: 0;
    bottom: 0;
    text-shadow: 0 0 4px #aaa;
    position: absolute;
  }

  .verified {
    display: none;
    color: #333;
    left: 0;
    bottom: 0;
    text-shadow: 0 0 4px #aaa;
    position: absolute;
  }

  .with-activity {
    display: none;
    color: @orangeColor;
    left: 0;
    top: 0;
    text-shadow: 0 0 4px #aaa;
    position: absolute;
    --fa-secondary-color: @orangeColor;
    --fa-secondary-opacity: 1.0;
    --fa-primary-color: white;
  }

  .timer-expired {
    display: none;
    color: @redColor;
    left: 0;
    top: 0;
    text-shadow: 0 0 4px #aaa;
    position: absolute;
    --fa-secondary-color: @redColor;
    --fa-secondary-opacity: 1.0;
    --fa-primary-color: white;
  }

  &.vim {
    .vim {
      display: block;
    }
  }

  &.unread {
    .unread {
      display: block;
    }
  }

  &.verified {
    .verified {
      display: block;
    }
  }

  &.with-activity {
    .with-activity {
      display: block;
    }
  }

  &.timer-expired {
    .timer-expired{
      display: block;
    }
  }

  .navigation-bar-icons {
    color: @white;
    font-size: 2em;
  }

  .label-chat-unread {
    position: absolute;
    right: 0;
    top: 0;
  }

  .label-chat-disconnected {
    position: absolute;
    right: 0;
    top: 0;
  }
}

@-moz-document url-prefix() {
  div.input-group-profile > input,
  div.input-group.business-information > input {
    padding: 8px;
    height: 3.6em;
  }
}

i.form-control-feedback {
  font-size: 20px;
  top: 8px;
}

.color-light-blue {
  color: @backgroundBlue;
}

#profilesTabContainer {
  height: 100%;
  padding: 10px;
  margin-bottom: 50px;
}

.color-blue {
  color: @backgroundBlue;
}

.margin-5 {
  margin: 5px;
}

.inline-block {
  display: inline-block;
}

.min-width-10 {
  min-width: 10px;
}

.margin-10 {
  margin: 10px;
}

.popup-height-container {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
}

.margin-left-12-percent {
  margin-left: 12%;
}

.margin-right-12-percent {
  margin-right: 12%;
}

/* Upload Attachment Modal */

hr.classic {
  margin-top: 5px;
  margin-bottom: 5px;
  width: 80%;
}

.icon-remove-attachment {
  font-size: 1.2vw;
  color: #39393a;
}

/***** *****/

.break-word {
  word-wrap: break-word;
}

.btn-refresh {
  background-color: @grayTitles;
  color: @white;
  &:hover,
  &:focus {
    background-color: #39393a;
    color: @white;
  }
  padding-top: 5px;
  padding-bottom: 5px;

  & > .loading {
    display: none;
  }

  &.loading {
    & > .loading {
      display: inline-block;
    }
  }
}

.predefined-answer-row(@padding) {
  .predefined-answer-row {
    padding: @padding;
    margin: 0;

    &:hover {
      background-color: #ecf0f1;
    }

    &.odd {
      background-color: #dddddd;
    }

    & > div:first-child {
      white-space: nowrap;
      padding-left: 5px;
      padding-right: 5px;

      i.fa, i.far, i.fal {
        font-size: 16px;
        color: #000;
      }

      .predefined-answer-row-favourite {
        i.fa, i.far, i.fal {
          color: #E0CE24;
        }
      }
    }

    div.predefined-answer-row-name {
      -ms-text-overflow: ellipsis;
      text-overflow: ellipsis;
      overflow-x: hidden;
      width: 100%;
      white-space: nowrap;
    }

    div.predefined-answer-row-body {
      -ms-text-overflow: ellipsis;
      text-overflow: ellipsis;
      overflow-x: hidden;
      width: 100%;
      white-space: nowrap;
    }

    div.predefined-answer-row-body:hover{
      -ms-text-overflow: unset;
      text-overflow: unset;
      white-space: unset;
    }
  }
}

.chat-user-profile-image {
  height: 40px;
  width: 40px;
}

#userProfileChat {
  height: 110px;
  max-height: 264px;
  border-radius: 3px;
}

.input-custom-editing {
  background: none;
  border: none;
  &:focus {
    outline: none;
  }
}

.border-right-warning {
  border-right: 2px solid #f39c12;
}

.pointer {
  cursor: pointer;
}

a.chatInPause {
  color: hotpink
}

img.emoji {
  height: 1em;
  width: 1em;
  margin: 0 .05em 0 .1em;
  vertical-align: -0.1em;
}

.social-button-chat-enabled {
  background-color: #CCC;
}

.selected-outgoing-case {
  background-color: lighten(@backgroundBlue,20%);
}

.input-group-btn {
  &.open {
    ul.dropdown-menu {
      li.active-item {
        a::before {
          content: "\f00c";
          font-family: "Font Awesome 5 Pro";
          font-size: 100%;
          margin-right: 5px;
        }
      }
    }
  }
}

.form-control:focus::-moz-placeholder {
  color: inherit;
  opacity: 0.7;
}
.form-control:focus:-ms-input-placeholder {
  color: inherit;
  opacity: 0.7;
}
.form-control:focus::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.7;
}
.form-control::-moz-placeholder {
  color: inherit;
  opacity: 0.33;
}
.form-control:-ms-input-placeholder {
  color: inherit;
  opacity: 0.33;
}
.form-control::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.33;
}

.ui-pnotify {
  border: 1px solid #ebeced;
  .ui-pnotify-container {
    background-color: #fff;

    .ui-pnotify-icon {
      span.fa {
        font-size: 20px;
      }
    }

    .ui-pnotify-text {
      & > img {
        width: 16px;
        height: 16px;
      }

      & > hr {
        border-top-color:#ebeced;
        margin-top:10px;
        margin-bottom:10px;
      }

      span.ui-pnotify-fullname {
        color: #5290fc;
        font-weight: bold;
      }

      div.ui-pnotify-principal-text {
        color: #4f4f50;
        text-align: center;
        font-size: 13px;
        margin-top: 10px;
        font-style: italic;
        word-break: break-all;
      }

      div.ui-pnotify-queue {
        color:#ababb0;
      }
    }
  }
}

.modal-reopen-case {
  height: 80%;
  width: 90% !important;
  @media (min-width: 768px) {
    height: calc(100% - 60px);
  }

  .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .modal-header, .modal-footer {
      flex-grow: 0;
      flex-shrink: 0;
    }

    .modal-body {
      padding-left: 20px;
      padding-right: 20px;
      flex-grow: 1;
      flex-shrink: 1;

      & > div {
        height: 100%;
        display: flex;
        flex-direction: column;

        .alert {
          flex-grow: 0;
          flex-shrink: 0;
          margin-bottom: 5px;
          padding: 5px 10px;
          display: flex;
          flex-direction: row;
          align-items: center;

          & > .fa {
            margin-right: 5px;
          }

          & > div {
            display: flex;
            flex-direction: column;
          }
        }

        .current-and-previous-cases {
          display: flex;
          flex-direction: row;
          flex-grow: 1;
          flex-shrink: 1;

          & > .separator {
            flex-shrink: 0;
            flex-grow: 0;
            width: 10px;
          }

          & > .current-case, & > .previous-case {
            flex-shrink: 0;
            flex-grow: 0;
            width: calc(50% - 5px);
            display: flex;
            max-height: 100%;
            overflow-x: hidden;
            overflow-y: hidden;

            & > .box.box-widget {
              flex-grow: 1;
              flex-shrink: 1;
              display: flex;
              flex-direction: column;

              .box-widget-header {
                flex-shrink: 0;
                flex-grow: 0;
              }

              .box-widget-body {
                flex-grow: 1;
                flex-shrink: 1;
                margin-bottom: 5px;
                margin-top: 5px;
                padding: 0;
                display: flex;
                flex-direction: column;

                & > .previous-case-messages, & > .current-case-messages {
                  position: relative;
                  height: 100%;
                  overflow-y: auto;
                  flex-shrink: 1;
                  flex-grow: 1;

                  .scrollbar();

                  case-conversation {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;

                    .case-timeline();
                  }
                }

                .box-widget-body-footer {
                  flex-grow: 0;
                  flex-shrink: 0;

                  &.social-case-conversation-buttons {
                    border-radius: 0 0 4px 4px;
                    display: flex;
                    flex-direction: row;
                    padding-left: 5px;
                    padding-right: 5px;
                    padding-bottom: 0;

                    .social-case-conversation-buttons-left {
                      flex-grow: 0;
                      flex-shrink: 0;
                      display: flex;
                      flex-direction: row;
                      justify-content: flex-start;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.modal-case-edit {
  height: 80%;
  @media (min-width: 768px) {
    height: calc(100% - 60px);
  }

  .modal-content {
    height: 100%;

    .modal-body {
      padding-left: 20px;
      padding-right: 20px;

      .nav-tabs-custom {
        margin-bottom: 0;

        .nav-tabs {
          li {
            &.active {
              a {
                border-right-color: #f4f4f4;
                border-left-color: #f4f4f4;
              }
            }
          }
        }

        .tab-content {
          margin-top: 0;
          padding: 10px 10px 0 10px;
          border: 1px solid #f4f4f4;
          border-top-color: transparent;

          .tab-pane {
            & > .row {
              @media (min-width: 1100px) {
                display: flex;
              }
            }
          }

          .extended-fields {
            div.form-group, div.checkbox {
              &.required {
                label {
                  &::after {
                    content: '*';
                    padding-left: 5px;
                  }
                }
              }
            }
          }

          .form-group {
            margin-bottom: 7px;
          }

          hr {
            margin-top: 0;
            margin-bottom: 7px;
          }

          .case-tags {
            border-radius: 3px;
            border: 1px solid #d2d6de;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            color: @grayTitles;
            margin-bottom: 5px;

            .alert {
              margin-bottom: 0;
            }

            & > div {
              & > ul.ivh-treeview {
                margin-bottom: 0;

                li.ivh-treeview-node {
                  & > div {
                    & > .ivh-treeview-node-container {
                      height: 24px;
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      border-top: 1px solid #eee;
                    }
                  }

                  &.ivh-treeview-node-leaf {
                    & > div {
                      & > .ivh-treeview-node-container {
                        & > div {
                          display: flex;
                          flex-direction: row;
                          align-items: center;

                          & > label {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            margin-bottom: 0;
                            max-width: initial;
                            font-weight: normal;

                            div[ivh-treeview-checkbox] {
                              margin-right: 3px;
                              height: 24px;
                              display: flex;
                              flex-direction: row;
                              align-items: center;

                              & > span {
                                height: 24px;
                                line-height: 24px;

                                input[type=checkbox] {
                                  margin: 0;
                                }
                              }
                            }

                            .ivh-treeview-node-label {
                              height: auto;
                            }
                          }
                        }
                      }
                    }
                  }

                  &:not(.ivh-treeview-node-leaf) {
                    & > div {
                      & > .ivh-treeview-node-container {
                        & > .ivh-treeview-toggle {
                          display: flex;
                          flex-direction: row;
                          align-items: center;

                          & > span {
                            display: flex;
                            flex-direction: row;
                            align-items: center;

                            .ivh-treeview-twistie {
                              display: flex;
                              flex-direction: row;
                              align-items: center;
                            }
                          }
                        }

                        & > .ivh-treeview-node-label {
                          font-size: 12px;
                        }
                      }
                    }
                  }
                }

                & > li.ivh-treeview-node:first-child {
                  & > div {
                    & > .ivh-treeview-node-container {
                      border-top-width: 0;
                    }
                  }
                }
              }
              .alert {
                margin-bottom: 0;
              }
            }
          }

          .case-tags-selected {
            .title {
              & > i {
                margin-right: 3px;
              }
            }

            & > div {
              display: flex;
              flex-direction: row;
              align-items: center;
              flex-wrap: wrap;

              .title {
                margin-right: 3px;
              }
            }

            .badge {
              &.badge-tags {
                font-weight: 400;
                padding: 7px;
                margin-right: 3px;
                overflow-x: hidden;
                text-overflow: ellipsis;

                &.badge-tags-old {
                  background-color: @caseHeaderBackgroundColor;
                  color: #000;
                }

                &.badge-tags-new {
                  background-color: @socialColor;
                  color: @textColorOnSocialColor;

                  & > a {
                    margin-left: 3px;
                    color: @textColorOnSocialColor;
                  }
                }
              }
            }

            .alert {
              margin-bottom: 0;
              padding: 5px;
              width: 100%;
            }
          }
        }
      }

      textarea {
        resize: none;
      }
    }
  }

  & {
    width: 90% !important;

    .modal-content {
      display: flex;
      flex-direction: column;

      .modal-header {
        flex-grow: 0;
        flex-shrink: 0;
      }

      .modal-body {
        flex-grow: 1;
        flex-shrink: 1;

        & > .nav-tabs-custom {
          height: 100%;
          display: flex;
          flex-direction: column;

          & > .nav-tabs {
            flex-grow: 0;
            flex-shrink: 0;
          }

          & > .tab-content {
            flex-grow: 1;
            flex-shrink: 1;
            max-height: 100%;

            .tab-pane {
              height: 100%;

              &.tab-pane-tags {
                & > .row {
                  display: block;
                  height: 100%;

                  & > div {
                    height: 100%;

                    &:first-child {
                      display: flex;
                      flex-direction: column;

                      .observations {
                        flex-grow: 0;
                        flex-shrink: 0;
                      }

                      .tags {
                        flex-grow: 1;
                        flex-shrink: 1;
                        display: flex;
                        flex-direction: column;

                        & > .row {
                          &:last-child {
                            flex-grow: 1;
                            flex-shrink: 1;

                            & > div {
                              height: 100%;
                              display: flex;

                              .case-tags {
                                overflow-y: auto;
                                flex-shrink: 1;
                                flex-grow: 1;
                                max-height: initial;
                                position: relative;

                                .tags-selection {
                                  position: absolute;
                                  top: 0;
                                  left: 0;
                                  width: 100%;
                                  height: 100%;
                                  overflow-y: auto;
                                  padding: 10px;
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }

            .tab-pane-extended-fields {

            }
          }
        }
      }

      .modal-footer {
        flex-grow: 0;
        flex-shrink: 0;
      }

      @media (max-height: 550px) {
        .modal-body {
          padding: 5px 15px;

          .nav-tabs-custom {
            .tab-content {
              padding: 5px 10px;

              .case-tags {
                .tags-selection {
                  padding: 5px 10px !important;
                }
              }
            }
          }
        }

        .modal-footer {
          padding: 5px 15px;
        }
      }
    }
  }

  &.with-iframe {
    width: 90% !important;
  }
}

.modal-agent-stats {
  height: 80%;
  width: 90% !important;
  @media (min-width: 768px) {
    height: calc(100% - 60px);
  }

  .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .modal-header, .modal-footer {
      flex-grow: 0;
      flex-shrink: 0;
    }

    .modal-body {
      padding-left: 20px;
      padding-right: 20px;
      flex-grow: 1;
      flex-shrink: 1;

      .nav-tabs-custom {
        margin-bottom: 0;
        height: 100%;
        display: flex;
        flex-direction: column;

        .nav-tabs {
          flex-grow: 0;
          flex-shrink: 0;
          li {
            &.active {
              a {
                border-right-color: #f4f4f4;
                border-left-color: #f4f4f4;
              }
            }
          }
        }

        .tab-content {
          margin-top: 0;
          padding: 10px 10px 0 10px;
          border: 1px solid #f4f4f4;
          border-top-color: transparent;
          flex-grow: 1;
          flex-shrink: 1;
          max-height: 100%;

          .tab-pane {
            height: 100%;

            & > .row {
              height: 100%;

              & > div {
                height: 100%;
              }

              &.interval-charts {
                height: 100%;
                display: flex;
                flex-direction: column;

                & > div {
                  flex-grow: 1;
                  flex-shrink: 1;
                  display: flex;
                  flex-direction: column;
                  position: relative;
                  & > div {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                  }
                }
              }

              &.times-charts {
                .box {
                  height: 100%;
                  display: flex;
                  flex-direction: column;

                  & > .box-widget-header {
                    flex-grow: 0;
                    flex-shrink: 0;
                  }

                  & > .box-widget-body {
                    flex-grow: 1;
                    flex-shrink: 1;
                    position: relative;
                    padding: 0;

                    & > div {
                      height: 100%;
                      width: 100%;
                      position: absolute;
                    }
                  }
                }
              }

              &.messages-totals {
                .box {
                  height: 100%;
                  display: flex;
                  flex-direction: column;

                  & > .box-widget-header {
                    flex-grow: 0;
                    flex-shrink: 0;
                  }

                  & > .box-widget-body {
                    flex-grow: 1;
                    flex-shrink: 1;
                    position: relative;
                    padding: 0;

                    & > div {
                      position: absolute;
                      height: 100%;
                      width: 100%;
                      overflow-y: auto;
                      padding: 5px 10px;

                      .info-box {
                        min-height: 60px;

                        .info-box-icon {
                          width: 60px;
                          height: 60px;
                          line-height: 60px;
                        }

                        .info-box-content {
                          margin-left: 60px;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.modal-profiles-search {
  width: 60% !important;
  @media (min-width: 768px) {
    width: 500px;
  }

  .input-spinner-container {
    position: relative;
  }

  .input-spinner-container .fa-spinner {
    position: absolute;
    right: 10px;
    top: 29%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  &.with-results {
    height: 80%;
    @media (min-width: 768px) {
      height: calc(100% - 60px);
    }

    .modal-content {
      display: flex;
      height: 100%;
      flex-direction: column;

      .modal-header {
        flex-grow: 0;
        flex-shrink: 0;
      }

      .modal-body {
        flex-grow: 1;
        flex-shrink: 1;
        display: flex;
        height: 100%;
        flex-direction: column;

        .filters {
          flex-grow: 0;
          flex-shrink: 0;
        }

        .profiles-result {
          flex-grow: 1;
          flex-shrink: 1;
          margin-top: 10px;
          height: 100%;

          & > div.row {
            height: 100%;
            margin-bottom: 0;
            position: relative;

            & > div {
              width: 80%;
              margin-left: 10%;
            }

            & > div {
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              overflow-y: auto;
              padding-right: 0;
              padding-left: 0;

              .scrollbar();

              .outgoing-profiles-container();
            }
          }
        }
      }

      .modal-footer {
        flex-grow: 0;
        flex-shrink: 0;
      }

      @media (max-height: 550px) {
        .modal-body {
          padding: 5px 15px;
          overflow-y: auto;
        }

        .modal-footer {
          padding: 5px 15px;
        }
      }
    }
  }
}

.modal-profile-edit {
  height: 80%;
  @media (min-width: 768px) {
    height: calc(100% - 60px);
  }

  .modal-content {
    height: 100%;

    .modal-body {
      padding-left: 20px;
      padding-right: 20px;

      .nav-tabs-custom {
        margin-bottom: 0;

        .nav-tabs {
          li {
            &.active {
              a {
                border-right-color: #f4f4f4;
                border-left-color: #f4f4f4;
              }
            }
          }
        }

        .tab-content {
          margin-top: 0;
          padding: 10px 10px 0 10px;
          border: 1px solid #f4f4f4;
          border-top-color: transparent;

          .tab-pane {
            & > .row {
              @media (min-width: 1100px) {
                display: flex;
              }
            }
          }

          div.form-group, div.checkbox {
            margin-bottom: 7px;
          }

          .alert {
            margin-bottom: 7px;
            padding: 7px;
          }

          .business-code {
            &.business-code-extended {
              .panel-heading {
                h3 {
                  font-size: 14px;
                }
              }
              .panel-body {
                .business-code-extended-table {
                  display: block;
                  width: 100%;

                  .title {
                    font-weight: bold;
                    white-space: nowrap;
                    display: block;
                    width: 100%;

                    & > div {
                      display: block;
                      float: left;
                    }
                  }
                  .elements {
                    white-space: nowrap;
                    display: block;
                    width: 100%;

                    & > div {
                      padding-right: 10px;
                      float: left;
                      margin-bottom: 5px;

                      &:last-child {
                        padding-right: 0;
                        text-align: center;

                        a.remove-row {
                          background-color: red;
                          padding-left: 7px;
                          padding-right: 7px;
                          color: #fff;
                          min-width: 30px;

                          &:hover {
                            background-color: lighten(red, 20%);
                          }
                        }
                      }
                    }
                  }
                }
                .footer {
                  a.add-row {
                    background-color: green;
                    color: #fff;
                    padding-left: 7px;
                    padding-right: 7px;
                    min-width: 30px;

                    &:hover {
                      background-color: lighten(green, 10%);
                    }
                  }
                }
              }
            }
          }

          .extended-fields {
            div.form-group, div.checkbox {
              &.required {
                label {
                  &::after {
                    content: '*';
                    padding-left: 5px;
                  }
                }
              }
            }
          }

          .user-profile-merge {
            .outgoing-profiles-container {
              .row {
                margin-right: 0;
                margin-left: 0;
              }
            }

            .buttons {
              margin-top: 10px;
            }
          }
        }
      }
    }
  }

  & {
    width: 90% !important;

    .modal-content {
      display: flex;
      flex-direction: column;

      .modal-header {
        flex-grow: 0;
        flex-shrink: 0;
      }

      .modal-body {
        flex-grow: 1;
        flex-shrink: 1;

        & > .nav-tabs-custom {
          height: 100%;
          display: flex;
          flex-direction: column;

          & > .nav-tabs {
            flex-grow: 0;
            flex-shrink: 0;
          }

          & > .tab-content {
            flex-grow: 1;
            flex-shrink: 1;
            max-height: 100%;

            .tab-pane {
              height: 100%;
            }

            .tab-pane-extended-fields {

            }
          }
        }
      }

      .modal-footer {
        flex-grow: 0;
        flex-shrink: 0;
      }

      @media (max-height: 550px) {
        .modal-body {
          padding: 5px 15px;
          overflow-y: auto;

          .nav-tabs-custom {
            .tab-content {
              padding: 5px 10px;
            }
          }
        }

        .modal-footer {
          padding: 5px 15px;
        }
      }
    }
  }

  &.with-iframe {
    width: 90% !important;
  }
}

.modal-attachment {
  &.modal-lg {
    height: 80%;
    @media (min-width: 768px) {
      height: calc(100% - 60px);
    }

    .modal-content {
      display: flex;
      flex-direction: column;
      height: 100%;

      .modal-header {
        flex-grow: 0;
        flex-shrink: 0;
      }

      .modal-body {
        flex-grow: 1;
        flex-shrink: 1;
        display: flex;
        flex-direction: column;
        height: 100%;

        & > .alert {
            margin-bottom: 0;
        }

        & > div {
          flex-grow: 1;
          flex-shrink: 1;
          height: 100%;

          embed, iframe {
            height: 100%;
          }
        }
      }

      .modal-footer {
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }
}

.modal-uploadfiles {
  .modal-content {
    .modal-body {
      .drop-info {
        display: none;
      }

      &.over {
        border: 2px solid @socialColor;
        background-color: fadeout(@socialColor, 70%);

        .drop-info {
          margin-top: 30px;
          display: flex;
          align-items: center;
          flex-direction: column;

          .drop-info-icon {
            color: @socialColor;
          }

          .drop-info-text {
            background-color: @socialColor;
            padding: 15px;
            margin-top: 10px;
            color: @white;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
          }
        }
      }

      .nofiles {
        .alert {
          margin-bottom: 0;
        }
      }

      .uploadedfiles {
        width: 100%;

        .uploadedfiles-file {
          padding: 10px;
          margin: 0;
          display: flex;
          flex-direction: row;

          &:hover {
            background-color: #ecf0f1;
          }

          &.odd {
            background-color: #dddddd;
          }

          div.uploadedfiles-file-name {
            flex-grow: 1;
            flex-shrink: 1;
            cursor: pointer;

            & > div {
              -ms-text-overflow: ellipsis;
              text-overflow: ellipsis;
              overflow-x: hidden;
              width: 100%;
              white-space: nowrap;
            }
          }

          div.uploadedfiles-file-remove {
            flex-grow: 0;
            flex-shrink: 0;
            i {
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}

.modal-twofactorsettings {
  .qrcode {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  .buttons-options {
    display: flex;
    align-items: center;
    justify-content: center;

    .button-option {
      padding-left: 10px;
      padding-right: 10px;

      button {
        width: 150px;
        height: 100px;
        background-color: rgb(221, 221, 221);
        max-height: initial;

        &:hover {
          background-color: lighten(rgb(221, 221, 221), 3%);
        }

        .icon {
          width: 100%;
          text-align: center;
          font-size: 30px;
          color: @defaultColor;
        }

        .title {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}

.box {
  &.box-widget {
    background-color: #fff;
    border: 1px solid @navbarBorderColor;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
    box-shadow: 0 1px 1px rgba(0,0,0,.05);

    .box-widget-header {
      font-size: 18px;
      font-family: var(--default-font);
      font-weight: bold;
      padding: 10px 15px;
      border-bottom: 1px solid @navbarBorderColor;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      color: @defaultColor;
      background-color: #f5f5f5;
      border-color: @navbarBorderColor;

      @media screen and (max-width: 500px) {
        padding: 5px 10px;
      }

      .toggle-collapse {
        display: none;
        float: right;
      }

      &.with-actions {
        & > div {
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 100%;

          .title {
            flex-shrink: 1;
            flex-grow: 1;
          }

          .action {
            flex-shrink: 0;
            flex-grow: 0;
            text-decoration: none;
            color: @defaultColor;
          }
        }
      }
    }

    .box-widget-body {
      padding: 10px;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;

      .alert {
        margin-bottom: 5px;
      }

      .box-widget-body-footer {
        margin-top: 10px;
        text-align: right;
      }
    }

    &.box-widget-userprofile {
      margin-bottom: 10px;

      .box-widget-header {
        &.vip {
          padding-left: 40px;
          position: relative;

          &::before {
            position: absolute;
            left: 0;
            top: 50%;
            color: #e3cf7a;
            content: '\f005';
            font-family: "Font Awesome 5 Pro";
            font-size: 30px;
            font-weight: bold;
            padding-left: 2px;
            -webkit-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
          }
        }

        &.annonymous {
          padding-left: 50px;
          position: relative;

          &::before {
            position: absolute;
            left: 0;
            top: 50%;
            //color: #e3cf7a;
            content: "\f21b";
            font-family: "Font Awesome 5 Pro";
            font-size: 30px;
            font-weight: bold;
            width: 50px;
            text-align: center;
            -webkit-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
          }
        }

        .box-widget-userprofile-header-name {
          display: flex;
          flex-direction: row;
          align-items: center;

          a.profile-name, span.profile-name {
            text-decoration: underline;
            font-size: 23px;
            font-weight: bold;
            color: @defaultColor;
            flex-grow: 1;
            flex-shrink: 1;
          }

          a.profile-edit {
            flex-grow: 0;
            flex-shrink: 0;
            text-decoration: underline;
            color: @defaultColor;
          }
        }

        &.editable {
          .box-widget-userprofile-header-name {
            a.profile-edit {
              display: inline-block;
            }
          }
        }

        .box-widget-userprofile-header-field {
          font-size: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;

          &.bigger {
            font-size: 110%;
          }

          .box-widget-userprofile-header-field-title {
            display: inline-block;
            font-weight: bold;
            flex-grow: 0;
            flex-shrink: 0;

            &.small {
              display: none;
            }

            &.normal {
              display: block;
            }
          }

          .box-widget-userprofile-header-field-value {
            margin-left: 3px;
            overflow-x: hidden;
            text-overflow: ellipsis;
            font-weight: normal;
            flex-grow: 1;
            flex-shrink: 1;
          }

          &.clipboard {
            .box-widget-userprofile-header-field-value {
              position: relative;

              span.copy {
                display: none;
                position: absolute;
                top: 0;
                right: 0;
                cursor: pointer;
                opacity: 0.4;

                &:hover {
                  opacity: 1;
                }
              }
            }

            &:hover {
              .box-widget-userprofile-header-field-value {
                padding-right: 15px;
                span.copy {
                  display: block;
                }
              }
            }
          }
        }

        .more-info {
          display: flex;
          flex-direction: row;
          align-items: center;

          & > span {
            &.bold {
              margin-right: 3px;
            }

            &:not(.bold) {
              font-weight: normal;
            }

            &.separator {
              margin-right: 3px;
              margin-left: 3px;
            }
          }
        }
      }

      .box-widget-body {
        color: #3C4249;
        background-color: #fff;
        margin: 0;

        .userprofile-fields {
          .field {
            .field-title {
              display: inline-block;
              font-weight: bold;
            }

            .field-value {

            }
          }
        }

        .associated-accounts {
          margin-top: 5px;

          .nav-tabs-custom-profile {
            margin-bottom: 0;
            border-radius: 0;

            .nav-tabs-user-profile {
              border-bottom-width: 0;
              .li-user-profile {
                text-align: center;
                a {
                  padding: 2px 8px;
                  border-color: @navbarBorderColor;
                }

                &.active {
                  a {
                    border-bottom-color: #fff;
                  }
                }
              }
            }

            .tab-content-profile {
              padding: 5px 10px;
              border: 1px solid @navbarBorderColor;
            }
          }

          .tab-content-profile {
            .user-info {
              .avatar {
                width: 20px;
                height: 20px;
              }
            }
          }
        }

        .integration-panel {
          margin-top: 5px;

          .profile-iframe {
            iframe {
              width: 100%;
              height: 200px;
              overflow-y: auto;
            }
          }
        }

        .previous-cases {
          margin-top: 5px;

          .loading {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            .fa-spinner {
              font-size: 30px;
            }
          }

          .alert {
            margin-bottom: 3px;
            padding: 5px;
            & > i  {
              margin-right: 3px;
            }
          }

          .with-cases {
            .cases {
              .cases-table();

              padding: 5px;
              max-height: 100px;
              overflow-y: auto;

              .scrollbar();
            }

            .buttons {
              margin-top: 3px;
            }
          }
        }

        .profiles-merge {
          margin-top: 5px;

          .loading {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            .fa-spinner {
              font-size: 30px;
            }
          }

          .alert {
            margin-bottom: 3px;
            padding: 5px;
            & > i  {
              margin-right: 3px;
            }
          }

          .with-suggestions {
            .suggestions {
              max-height: 150px;
              overflow-y: auto;
              overflow-x: hidden;
              .scrollbar();

              .outgoing-profiles-container-header {
                margin: 0;
                font-weight: bold;
                border-bottom: 1pt solid @navbarBorderColor;
                padding: 2px 0;
              }

              .outgoing-profiles-container-item {
                border-top-style: none;
                margin: 0;
                border-bottom: 1pt dotted @navbarBorderColor;
                padding: 2px 0;
                min-height: 30px;

                & > .col-xs-6:first-child {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                &:last-child {
                  border-bottom-width: 0;
                }

                &:hover {
                  background-color: darken(@navbarBackgroundColor, 5%);
                }
                &.selected-profile {
                  background-color: darken(@navbarBackgroundColor, 15%);
                }
              }
            }

            .buttons {
              margin-top: 3px;
            }
          }
        }
      }
    }

    &.box-widget-case-header {
      margin-bottom: 5px;

      & > .box-widget-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        a {
          color: @defaultColor;
          text-decoration: underline;
          flex-grow: 1;
          flex-shrink: 1;
        }

        .icons {
          flex-grow: 0;
          flex-shrink: 0;
          display: flex;
          flex-direction: row;
          align-items: center;

          a {
            text-decoration: none;
            margin-left: 3px;

            &:first-child {
              margin-left: 0;
            }
          }
        }
      }

      .box-widget-body {
        .case-tags {
          background-color: @caseHeaderBackgroundColor;
          padding: 5px;
          color: #000;
          font-weight: 400;
          margin-right: 5px;
          margin-bottom: 2px;
          overflow-x: hidden;
          text-overflow: ellipsis;

          direction: rtl;
          text-align: left;

          .fa-tag {
            float: left;
          }
        }

        .box-widget-userprofile {
          margin-bottom: 0;

          .box-widget-header {
            .box-widget-userprofile-header-name {
              a.profile-name, span.profile-name {
                font-size: 100%;
              }
            }
          }
        }
      }
    }

    &.box-widget-profilesmerge {
      color: @white;
      background-color: @backgroundHeaderUserProfileColor;

      .box-widget-body {
        color: #3C4249;
        background-color: #EBECED;

        .box-widget-body-footer {
          a.btn, button.btn {
            background-color: @backgroundHeaderUserProfileColor;
            color: @white;
          }
        }
      }
    }

    &.box-widget-integration-panel {
      height: 100%;
      display: flex;
      flex-direction: column;

      .box-widget-header {
        flex-grow: 0;
        flex-shrink: 0;
      }

      .box-widget-body {
        color: #3C4249;
        background-color: #FFF;
        margin: 0;
        flex-grow: 1;
        flex-shrink: 1;
        display: flex;
        .profile-iframe, .case-iframe {
          flex-grow: 1;
          flex-shrink: 1;
          iframe {
            width: 100%;
            height: 100%;
            overflow-y: auto;
          }
        }
      }
    }

    &.box-widget-predefinedanswers {
      color: @white;
      background-color: @backgroundBlue;

      .box-widget-header {
        padding: 5px 10px;
      }

      .box-widget-body {
        color: #3C4249;
        background-color: #FFF;
      }
    }

    &.box-widget-caseinfo {
      .box-widget-header {
        padding: 5px 10px;

        a.case-edit, a.case-see, a.case-download {
          display: none;
          float: right;
          color: @defaultColor;
          text-decoration: underline;
          cursor: pointer;
        }

        &.editable {
          .box-widget-caseinfo-header {
            a.case-edit {
              display: inline-block;
            }
          }
        }

        &.downloadable {
          .box-widget-caseinfo-header {
            a.case-download {
              display: inline-block;
            }
          }
        }

        &.seeable {
          .box-widget-caseinfo-header {
            a.case-see {
              display: inline-block;
            }
          }
        }
      }

      .box-widget-body {
        color: #3C4249;
        background-color: #FFF;
        margin: 0;

        .case-fields {
          padding-top: 3px;
          padding-bottom: 3px;
          margin: 0;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          .case-field {
            flex: 50%;

            .case-field-title {
              font-weight: bold;
            }

            .case-field-value {
              .caseinfo-tags {
                display: flex;
                flex-wrap: wrap;

                .case-tags {
                  margin-top: 1px;
                  background-color: @caseHeaderBackgroundColor;
                  padding: 5px;
                  color: #000;
                  font-weight: 400;
                  margin-right: 5px;
                  overflow-x: hidden;
                  text-overflow: ellipsis;

                  direction: rtl;
                  text-align: left;

                  .fa-tag {
                    float: left;
                  }
                }
              }

              &.scrollable {
                max-height: 100px;
                overflow-y: auto;
                text-overflow: ellipsis;
                word-break: break-all;
                overflow-x: hidden;
                border: 1px solid #3C4249;
                color: #000;
                padding: 2px 5px;
                font-family: var(--default-mono-font);
                background-color: @caseHeaderBackgroundColor;

                .scrollbar();
              }
            }

            &.case-field-flex {
              display: flex;
              flex-direction: row;
              align-items: center;
              flex: 100%;

              .case-field-title {
                flex-shrink: 0;
                flex-grow: 0;
                margin-right: 3px;
              }

              .case-field-value {
                -ms-text-overflow: ellipsis;
                text-overflow: ellipsis;
                overflow-x: hidden;
                width: 100%;
              }
            }
          }
        }

        .case-extended-fields {
          margin-top: 5px;
        }

        .integration-panel {
          margin-top: 5px;

          .profile-iframe, .case-iframe {
            iframe {
              width: 100%;
              height: 200px;
              overflow-y: auto;
            }
          }
        }
      }
    }

    &.box-widget-returntoqueue, &.box-widget-discard {
      .box-widget-body {
        color: #3C4249;
        background-color: #fff;
        margin: 0;

        .form-group {
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    &.box-widget-reply {
      .box-widget-header {
        font-size: 14px;
        padding: 5px 10px;
      }

      .box-widget-body {
        color: #3C4249;
        background-color: #FFF;
        margin: 0;

        form {
          .row {
            margin-left: 0;
            margin-right: 0;

            & > div {
              padding: 0;
              margin-right: 5px;

              &:last-child {
                margin-right: 0;
              }

              .form-group {
                margin-bottom: 5px;
              }

              .buttons-footer {
                margin-top: 5px;
                .btn {
                  font-family: var(--default-condensed-font);
                  .btn-text {
                    display: none;
                  }
                }

                &.with-signature.with-attachment {
                  @media (min-width: 1860px) {
                    .btn {
                      .btn-text {
                        display: inline-block;
                      }
                    }
                  }
                }

                &.with-attachment:not(.with-signature) {
                  @media (min-width: 1720px) {
                    .btn {
                      .btn-text {
                        display: inline-block;
                      }
                    }
                  }
                }

                &:not(.with-attachment):not(.with-signature) {
                  @media (min-width: 1450px) {
                    .btn {
                      .btn-text {
                        display: inline-block;
                      }
                    }
                  }
                }
              }
            }

            .whatsapp-message-template {
              .alert {
                color: #383d41;
                background-color: #e2e3e5;
                border-color: #d6d8db;
              }
            }
          }
        }
      }

      .modal-case & {
        .btn {
          .btn-text {
            display: none !important;
          }
        }
      }
    }

    &.box-widget-predefinedanswers {
      .box-widget-body {
        & > predefined-answers {
          .predefined-answers {
            .predefined-answers-header {
              margin-bottom: 5px;
              & > .input-group {
                width: 100%;

                .input-group-btn, .input-group-addon {
                  border-radius: 0;

                  .btn {
                    width: 100%;
                    height: 34px;
                    border-radius: 0;
                    margin-right: 0;
                  }
                }

                .input-group-addon {
                  .btn {
                    height: 32px;
                  }
                }
              }
            }

            .predefined-answers-results {
              & > div {
                .predefined-answers-results-rows {
                  .predefined-answer-row(5px);
                }
              }
            }
          }
        }
      }
    }

    &.box-widget-pendingcases {
      display: flex;
      flex-direction: column;

      .box-widget-header {
        flex-grow: 0;
        flex-shrink: 0;
        padding: 5px 10px;

        .refreshMyCases {
          float: right;
          .btn {
            min-width: 40px !important;
            width: 40px !important;
          }
        }
      }

      .box-widget-body {
        flex-grow: 1;
        flex-shrink: 1;
        display: flex;
        flex-direction: column;

        .filters {
          margin-bottom: 3px;
        }

        my-cases, my-pending-cases {
          flex-grow: 1;
          flex-shrink: 1;
          display: flex;
          flex-direction: column;

          .my-pending-cases, .pending-cases {
            flex-grow: 1;
            flex-shrink: 1;

            & > .loading {
              display: none;
              flex-direction: row;
              align-items: center;
              justify-content: center;

              .fa-spinner {
                font-size: 30px;
              }
            }

            & > .info {
              width: 100%;
              flex-grow: 1;
              flex-shrink: 1;
              position: relative;

              .with-cases {
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                position: absolute;

                .scrollbar();

                .table {
                  margin-bottom: 0;

                  td {
                    .fa.fa-check-circle {
                      color: #36ba4d;
                    }

                    .case-services {
                      white-space: nowrap;
                      & > span {
                        margin-left: 2px;

                        &:first-child {
                          margin-left: 0;
                        }
                      }
                    }
                  }
                }

                .buttons {
                  margin-top: 5px;
                }
              }
            }

            &.loading {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;

              .loading {
                display: flex;
              }

              .info {
                display: none;
              }
            }

            &:not(.loading) {
              display: flex;
              flex-direction: row;
            }
          }
        }
      }
    }

    &.social-case-conversation {
      .box-widget-body {
        color: #3C4249;
        background-color: #FFF;
        margin: 0;
      }
    }

    &.box-widget-nested {
      .box-widget-header {
        padding: 4px 15px;
        font-size: 14px;

        &::after {
          height: 50%;
          opacity: 0.25;
          background-color: rgba(0,0,0,0.5);
          border-bottom: 1px solid black;
          pointer-events: none;
        }
      }

      .box-widget-body {
        padding: 5px;
      }
    }

    &.box-widget-collapsable {
      .box-widget-header {
        cursor: pointer;
      }

      &:not(.collapsed) {
        .box-widget-header {
          .toggle-collapse.collapse {
            display: none;
          }

          .toggle-collapse.expand {
            display: inline-block;
          }
        }

        .box-widget-body {
          display: block;
        }
      }

      &.collapsed {
        .box-widget-header {
          border-bottom-color: transparent;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;

          .toggle-collapse.collapse {
            display: inline-block;
          }

          .toggle-collapse.expand {
            display: none;
          }
        }

        .box-widget-body {
          display: none;
        }
      }
    }

    &.box-widget-dark {
      background-color: @navbarDarkBackgroundColor;
      border: 1px solid @navbarDarkBorderColor;
      color: @defaultDarkColor;

      .box-widget-header {
        background-color: @navbarDarkHeaderBackgroundColor;
        color: @defaultDarkColor;
        border-bottom: 1px solid @navbarDarkBorderColor;
        border-color: @navbarDarkBorderColor;


        a, span {
          color: @defaultDarkColor !important;
        }
      }

      .box-widget-body {
        .box-widget-nested {
          background-color: @navbarDarkHeaderBackgroundColor;
          border-color: @navbarDarkBorderColor;
        }

        .alert {
          &.alert-info {
            background-color: #00adb5 !important;
            border-color: #00adb5;
          }
        }

        .color-mail {
          color: #fff !important;
        }

        .li-user-profile {
          &.active {
            .color-mail {
              color: #000 !important;
            }
          }
        }

        .buttons {
          .btn-refresh {
            background-color: #eeeeee;
            color: #000;
            &:hover,
            &:focus {
              background-color: darken(#eeeeee, 10%);
              color: #000;
            }
          }
        }
      }

      &.box-widget-caseinfo {
        .box-widget-body {
          background-color: @navbarWidgetBodyDarkBackgroundColor;
          color: @defaultDarkColor;
        }
      }

      &.box-widget-predefinedanswers {
        .box-widget-body {
          background-color: @navbarWidgetBodyDarkBackgroundColor;
          color: @defaultDarkColor;

          & > predefined-answers {
            .predefined-answers {
              .predefined-answers-header {

              }

              .predefined-answers-results {
                & > div {
                  .scrollbar-dark();

                  .predefined-answers-results-rows {

                    .predefined-answer-row {
                      border-bottom: 1px solid @navbarDarkBorderColor;

                      &.odd {
                        background-color: @navbarDarkBackgroundColor;
                      }

                      &:hover {
                        background-color: lighten(@navbarDarkBackgroundColor, 5%);
                      }

                      & > div:first-child {
                        i.fa, i.far, i.fal {
                          color: @defaultDarkColor;
                        }

                        .predefined-answer-row-favourite {
                          i.fa, i.far, i.fal {
                            color: #E0CE24;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      &.box-widget-userprofile {
        .box-widget-body {
          background-color: @navbarWidgetBodyDarkBackgroundColor;
          color: @defaultDarkColor;

          .associated-accounts {
            .nav-tabs-custom-profile {
              background-color: transparent;
              .nav-tabs-user-profile {
                .li-user-profile {
                  a {
                    border-color: @navbarDarkBorderColor;
                  }

                  &.active {
                    a {
                      border-bottom-color: @navbarDarkBackgroundColor;
                    }
                  }
                }
              }

              .tab-content-profile {
                border-color: @navbarDarkBorderColor;
              }
            }
          }

          .integration-panel {
          }

          .previous-cases {
            .with-cases {
              .cases {
                .scrollbar-dark();

                .cases-title {
                  border-bottom-color: @navbarDarkBorderColor;
                }

                .cases-row {
                  border-bottom-color: @navbarDarkBorderColor;

                  &:hover {
                    background-color: lighten(@navbarDarkBackgroundColor, 5%);
                  }
                }
              }
            }
          }

          .profiles-merge {
            .with-suggestions {
              .suggestions {
                .scrollbar-dark();

                .outgoing-profiles-container-header {
                  border-bottom-color: @navbarDarkBorderColor;
                }

                .outgoing-profiles-container-item {
                  border-bottom-color: @navbarDarkBorderColor;

                  &:hover {
                    background-color: lighten(@navbarDarkBackgroundColor, 5%);
                  }
                  &.selected-profile {
                    background-color: lighten(@navbarDarkBackgroundColor, 15%);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.box-user-profile {
  margin-bottom: 5px;

  .box-user-profile-header {
    color: @white;
    padding: 10px;
    background-color: @backgroundHeaderUserProfileColor;

    &.vip {
      padding-left: 60px;
      position: relative;

      &::before {
        position: absolute;
        left: 0;
        top: 50%;
        content: '\f005';
        font-family: "Font Awesome 5 Pro";
        font-size: 50px;
        padding-left: 10px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
      }
    }

    .box-user-profile-header-name {
      a.profile-name, span.profile-name {
        color: #fff;
        text-decoration: underline;
        font-size: 23px;
        font-weight: bold;
      }

      a.profile-edit {
        display: none ;
        float: right;
        color: #fff;
        text-decoration: underline;
      }
    }

    &.editable {
      .box-user-profile-header-name {
        a.profile-edit {
          display: inline-block;
        }
      }
    }

    .box-user-profile-header-field {
      .box-user-profile-header-field-title {
        display: inline-block;
        text-decoration: underline;
      }

      .box-user-profile-header-field-value {

      }
    }

    .header-user-profile {
      margin-right: 0;
      margin-left: 0;
      background-color: @mailColor;
    }

    &.widget-user-header > div {
      padding: 12px;
      font-size: 20px;
      & > i {
        margin-right: 10px;
      }
    }
  }

  img.user-profile-header-image {
    height: 62px;
    width: 62px;
  }

  span.user-profile-header-image {
    font-size: 66px;
  }
}

.li-user-profile {
  opacity: 0.4;

  &.active {
    opacity: 1;
  }
}

.popover {
  font-family: var(--default-font);
  font-size: @fontSize;
}

textarea {
  &.editor-whatsapp, &.editor-twitter-primary, &.editor-twitter-secondary, &.editor-facebook-primary, &.editor-facebook-secondary, &.editor-facebookmessenger, &.editor-telegram, &.editor-answer-grouped, &.editor-instagram-primary, &.editor-instagram-secondary, &.editor-skype, &.editor-youtube, &.editor-mercadolibre, &.editor-linkedin, &.editor-googleplay, &.editor-apple,  &.editor-googlemybusiness {
    display: block;
    width: 100%;
    padding: 10px;
    height: @answerControlHeight;
    resize: none;
    overflow:auto;
    // max-height: 300px;
    // min-height: @answerControlHeight;
  }
}

.alert {
  &.alert-info {
    &.alert-info-answer {
      min-height: @answerControlHeight;
      display: flex;
      align-items: center;
      padding: 10px;

      span.fa {
        margin-right: 5px;
      }

      a {
        display: block;
        &:hover {
          color: #eee;
        }
      }
    }
  }
}

.sm-definition {
  &:not(.chat) {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 5px;
    text-align: left;

    .scrollbar();

    & > .sm {
      display: block;
      margin-bottom: 15px;

      & > .contents {
        & > .text {
          white-space: break-spaces;
        }

        & > img {
          max-height: 250px;
        }
      }

      &.text {
        .contents {
          max-width: 50%;
          background-color: #fff;
          border: 0;
          color: #444950;
          min-height: 22px;
          min-width: 14px;
          overflow: hidden;
          position: relative;
          white-space: pre-wrap;
          border-bottom-right-radius: 12px;
          border-top-right-radius: 12px;
          overflow-wrap: break-word;
          margin: 0;

          & > .text {
            padding: 5px 8px 6px;
            white-space: break-spaces;
          }
        }
      }

      &.template {
        &.template-button {
          .contents {
            max-width: 50%;
            background-color: #fff;
            border: 0;
            color: #444950;
            min-height: 22px;
            min-width: 14px;
            overflow: hidden;
            position: relative;
            white-space: pre-wrap;
            border-bottom-right-radius: 12px;
            border-top-right-radius: 12px;
            overflow-wrap: break-word;
            margin: 0;

            & > .text {
              padding: 5px 8px 6px;
            }
          }

          .buttons {
            max-width: 50%;
            border-bottom-right-radius: 12px;
            border-top-right-radius: 12px;
            border: 1px solid rgba(0, 0, 0, .18);
            overflow: hidden;
            box-sizing: border-box;
            color: rgba(0, 0, 0, 1);
            line-height: 1.28em;
            margin-top: 1px;
            text-align: center;

            .button {
              font-weight: 600;
              color: #365899;
              cursor: pointer;
              text-decoration: none;
              border-top: 1px solid rgba(0, 0, 0, .10);
              line-height: 32px;

              &:first-child {
                border-top: none;
              }
            }
          }
        }

        &.template-generic {
          overflow: hidden;
          width: 100%;
          max-width: 100%;
          position: relative;

          .move-left, .move-right {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            height: 30px;
            width: 30px;
            background: #fff;
            border: 1px solid #dddfe2;
            border-radius: 3px 0 0 3px;
            display: block;
            outline: none;
            vertical-align: middle;
            z-index: 1000;
            cursor: pointer;

            & > div {
              display: table;
              margin: 0 auto;
              height: 100%;
              width: 100%;

              & > span {
                display: table-cell;
                vertical-align: middle;
                text-align: center;
              }
            }
          }

          .move-left {
            left: 0;
          }

          .move-right {
            right: 0;
          }

          .contents {
            display: flex;
            white-space: nowrap;
            flex-direction: row;
            margin: 0;
            position: relative;
            transition: left 500ms ease-out;
            left: 0;

            .item {
              display: inline-block;
              vertical-align: middle;
              max-width: 500px;
              flex-grow: 0;
              flex-shrink: 0;
              margin-bottom: 9px;
              border: 1px solid rgba(0, 0, 0, .1);
              border-radius: 4px;
              margin-top: 1px;
              overflow: hidden;
              text-decoration: none;
              width: 100%;
              margin-right: 10px;

              .image {
                img {
                  max-width: 100%;
                  height: 250px;
                }
              }

              .title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                font-weight: 600;
              }

              .subtitle {
                display: inline-block;
                overflow: hidden;
                position: relative;
                text-overflow: ellipsis;
                white-space: pre-line;
                width: 100%;
                word-break: break-word;
                color: rgba(0, 0, 0, .40);
              }

              .url {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;

                a {
                  color: rgba(0, 0, 0, .40) !important;
                }
              }

              .buttons {
                max-width: 100%;
                border-top: 1px solid rgba(0, 0, 0, .1);
                overflow: hidden;
                box-sizing: border-box;
                color: rgba(0, 0, 0, 1);
                line-height: 1.28em;
                margin-top: 1px;
                text-align: center;

                .button {
                  font-weight: 600;
                  color: #365899;
                  cursor: pointer;
                  text-decoration: none;
                  border-top: 1px solid rgba(0, 0, 0, .10);
                  line-height: 32px;

                  &:first-child {
                    border-top: none;
                  }
                }
              }
            }
          }
        }

        &.attach {
          .contents {
            max-width: 50%;
            background-color: #fff;
            border: 0;
            color: #444950;
            min-height: 22px;
            min-width: 14px;
            overflow: hidden;
            position: relative;
            white-space: pre-wrap;
            border-bottom-right-radius: 12px;
            border-top-right-radius: 12px;
            overflow-wrap: break-word;
            margin: 0;

            & > .text {
              padding: 5px 8px 6px;
              font-weight: bold;
            }
          }

          &.attach-image {
            max-height: 450px;

            .contents {
              height: 100%;

              img {
                border-bottom-right-radius: 12px;
                border-top-right-radius: 12px;
                max-width: 100%;
                width: 100%;
                max-height: 450px;
              }
            }
          }
        }
      }

      &.location {
        .contents {
          max-width: 50%;
          background-color: rgb(241, 240, 240);
          border: 0;
          color: #444950;
          min-height: 22px;
          min-width: 14px;
          overflow: hidden;
          position: relative;
          border-bottom-right-radius: 12px;
          border-top-right-radius: 12px;
          overflow-wrap: break-word;
          margin: 0;

          .location {
            position: relative;
            text-decoration: none;
            padding-left: 60px;
            cursor: pointer;

            .marker {
              position: absolute;
              left: 0;
              top: 0;
              font-size: 30px;
              width: 60px;
              height: 100%;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
            }

            .info {
              font-size: 120%;
              font-weight: bold;
            }

            .name {
              font-weight: bold;
            }

            .address {
              font-style: italic;
            }
          }
        }
      }

      &.qr {
        .qr {
          display: none;
        }
      }

      &:last-child {
        .qr {
          display: flex;
          flex-direction: row;
          align-content: center;
          padding-top: 10px;
          padding-bottom: 10px;
          width: 100%;
          justify-content: center;

          .button {
            background: #fff;
            border-radius: 1.3em;
            color: #0084ff;
            display: inline-block;
            padding-left: 8px;
            padding-right: 8px;
            border: 1px solid rgba(0, 0, 0, .20);
            line-height: 24px;
          }
        }
      }

      &.sm-whatsapp {
        & > .contents {
          border-bottom-left-radius: 12px;
          border-top-left-radius: 12px;
        }

        &.template {
          .buttons {
            max-width: 50%;
            border-radius: 0;
            margin-top: 3px;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            border: 0;
            padding: 0;

            .button {
              min-width: calc(50% - 2px);
              border: 0;
              border-radius: 12px;
              background-color: rgb(241, 240, 240);
              overflow: hidden;
              box-sizing: border-box;
              color: rgba(0, 0, 0, 1);
              line-height: 30px;
              text-align: center;
              height: 30px;
              vertical-align: middle;
              color: #365899;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  &.chat {
    padding: 0;

    & > .sm {
      display: block;

      &.text {
        .contents {
          position: relative;
          white-space: pre-wrap;
        }
      }

      &.template {
        &.template-button {
          .contents {
            overflow: hidden;
            position: relative;
            white-space: pre-wrap;
            overflow-wrap: break-word;
            margin: 0;

            & > .text {
            }
          }

          .buttons {
            box-sizing: border-box;
            color: rgba(0, 0, 0, 1);
            line-height: 1.28em;
            margin-top: 5px;
            text-align: center;

            .button {
              border-radius: 12px;
              background-color: lighten(@color-bot, 10%);
              font-weight: 600;
              color: @fontcolor-bot;
              cursor: pointer;
              text-decoration: none;
              line-height: 32px;
              margin-top: 5px;
              padding: 0 10px;

              &:first-child {
                margin-top: 0;
              }
            }
          }
        }

        &.embededvideo {
          .contents {
            overflow: hidden;
            iframe {
              width: 100%;
              height: 100%;
            }
          }
        }

        &.template-generic {
          overflow: hidden;
          width: 100%;
          max-width: 100%;
          position: relative;

          .move-left, .move-right {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            height: 30px;
            width: 30px;
            background: #fff;
            border: 1px solid #dddfe2;
            border-radius: 3px 0 0 3px;
            display: block;
            outline: none;
            vertical-align: middle;
            z-index: 1000;
            cursor: pointer;

            & > div {
              display: table;
              margin: 0 auto;
              height: 100%;
              width: 100%;

              & > span {
                display: table-cell;
                vertical-align: middle;
                text-align: center;
              }
            }
          }

          .move-left {
            left: 0;
          }

          .move-right {
            right: 0;
          }

          .contents {
            display: flex;
            white-space: nowrap;
            flex-direction: row;
            margin: 0;
            position: relative;
            transition: left 500ms ease-out;
            left: 0;

            .item {
              display: inline-block;
              vertical-align: middle;
              max-width: 240px;
              min-width: 240px;
              flex-grow: 0;
              flex-shrink: 0;
              margin-bottom: 9px;
              border-radius: 4px;
              margin-top: 1px;
              overflow: hidden;
              text-decoration: none;
              width: 100%;
              margin-right: 10px;

              .image {
                img {
                  max-width: 100%;
                  height: 125px;
                }
              }

              .title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                font-weight: 600;
              }

              .subtitle {
                display: inline-block;
                overflow: hidden;
                position: relative;
                text-overflow: ellipsis;
                white-space: pre-line;
                width: 100%;
                word-break: break-word;
                color: rgba(0, 0, 0, .40);
              }

              .url {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;

                a {
                  color: rgba(0, 0, 0, .40) !important;
                }
              }

              .buttons {
                box-sizing: border-box;
                color: rgba(0, 0, 0, 1);
                line-height: 1.28em;
                margin-top: 5px;
                text-align: center;

                .button {
                  border-radius: 12px;
                  background-color: lighten(@color-bot, 10%);
                  font-weight: 600;
                  color: @fontcolor-bot;
                  cursor: pointer;
                  text-decoration: none;
                  line-height: 32px;
                  margin-top: 5px;
                  padding: 0 10px;

                  &:first-child {
                    margin-top: 0;
                  }
                }
              }
            }
          }
        }

        &.attach {
          .contents {
            & > .text {
              font-weight: normal;
            }
          }

          &.attach-image {
            .contents {
              overflow: hidden;
              position: relative;
              white-space: pre-wrap;
              margin: 0;

              img {
                max-width: 100%;
                width: 100%;
                max-height: 450px;
              }
            }
          }
        }
      }

      &.qr {
        .qr {
          display: none;
        }
      }

      &:last-child {
        .qr {
          display: flex;
          flex-direction: row;
          align-content: center;
          padding-top: 10px;
          padding-bottom: 10px;
          width: 100%;
          justify-content: center;

          .button {
            background: #fff;
            border-radius: 1.3em;
            color: #0084ff;
            display: inline-block;
            padding-left: 8px;
            padding-right: 8px;
            border: 1px solid rgba(0, 0, 0, .20);
            line-height: 24px;
          }
        }
      }
    }
  }
}

.yzn-ySocial, .yzn-yFlow, .yzn-ySmart, .yzn-ySurvey, .yzn-ySurveyISO, .yzn-ySurveySquare, .yzn-ySmartISO, .yzn-ySmartSquare, .yzn-yFlowISO, .yzn-yFlowSquare, .yzn-ySocialISO, .yzn-ySocialSquare {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yoizen' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-block;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.yzn-ySocial:before {
  content: "\e908";
}
.yzn-yFlow:before {
  content: "\e909";
}
.yzn-ySmart:before {
  content: "\e90a";
}
.yzn-ySurvey:before {
  content: "\e90b";
}
.yzn-ySurveyISO:before {
  content: "\e904";
  background-color: #FF6B6B;
  color: #fff;
}
.yzn-ySurveySquare:before {
  content: "\e905";
}
.yzn-ySmartISO:before {
  content: "\e906";
  background-color: #510E3B;
  color: #fff;
}
.yzn-ySmartSquare:before {
  content: "\e907";
}
.yzn-yFlowISO:before {
  content: "\e900";
  background-color: #0D7499;
  color: #fff;
}
.yzn-yFlowSquare:before {
  content: "\e901";
}
.yzn-ySocialISO:before {
  content: "\e902";
}
.yzn-ySocialSquare:before {
  content: "\e903";
}

.fa-check-double {
  &.whatsapp-delivered {

  }
  &.whatsapp-read {
    color: rgb(83,196,247);
  }
}

.panels {
  margin-top: @headerHeight !important;
  height: calc(100% ~"-" @headerHeight) !important;

  .pane-head {
    .close {
      margin-top: 10px;
      margin-right: 10px;
    }
  }

  .pane-body {
    .panel-body-userprofile {
      padding: 40px 10px 5px;
    }

    .nav-tabs-message-details {
      margin-bottom: 0;

      .tab-content {
        margin-top: 0;
        .tab-pane {
          .box {
            border: none;
            .box-body {
              border-radius: 0;
              padding: 0 10px;
              .title {
                display: flex;
                flex-direction: column;
                align-items: center;

                .icon {
                  font-size: 28px;

                  & > .fa.fa-circle {
                    color: @caseHeaderBackgroundColor !important;
                  }
                }

                h3 {
                  font-size: 21px;
                  margin-top: 5px;
                  text-align: center;
                }
              }
            }

            &.box-message-info {
              .list-group {
                margin-bottom: 10px;

                .list-group-item {
                  display: flex;
                  flex-direction: row;
                  align-items: center;

                  .title {
                    flex-grow: 0;
                    flex-shrink: 0;
                    font-weight: bold;
                    margin-right: 10px;
                  }

                  .value {
                    flex-grow: 1;
                    flex-shrink: 1;
                    padding: 0 3px;
                    text-overflow: ellipsis;
                    overflow-x: hidden;
                    max-width: 100%;
                    white-space: nowrap;
                    text-align: right;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

div.myCases {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;

  & > .separator {
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    & > div {
      cursor: pointer;
    }

    &.horizontal {
      flex-direction: column;

      & > div {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &.vertical {
      display: none;
      flex-direction: row;

      & > div {
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  & > .pending, & > .my-cases {
    flex-grow: 1;
    flex-shrink: 1;
    display: flex;
    flex-direction: column;
    transition: width 0.5s, height 0.5s;

    & > div {
      flex-grow: 1;
      flex-shrink: 1;

      .alert {
        margin-bottom: 0 !important;
        padding: 7px 15px;
      }
    }
  }

  & > .pending {
    width: 50%;
    padding-right: 5px;
  }

  & > .my-cases {
    width: 50%;
    padding-left: 5px;

    .info {
      .with-cases {
        .table {
          .parameters {
            display: flex;
            flex-direction: column;

            .parameter {
              display: flex;
              flex-direction: row;
              align-items: center;

              .value {
                margin-left: 3px;
                max-width: 300px;
                overflow-x: hidden;
                text-overflow: ellipsis;
                word-break: break-all;
                display: inline-block;
              }
            }
          }
        }
      }
    }
  }

  &.expanded-left {
    & > .pending {
      width: 75%;
    }

    & > .my-cases {
      width: 25%;
    }
  }

  &.expanded-right {
    & > .pending {
      width: 25%;
    }

    & > .my-cases {
      width: 75%;
    }
  }

  @media screen and (max-width: 1400px) {
    flex-direction: column;

    & > .pending {
      width: 100%;
      height: 50%;
      padding-bottom: 5px;
      padding-right: 0;
    }

    & > .my-cases {
      width: 100%;
      height: 50%;
      padding-top: 5px;
      padding-left: 0;
    }

    &.expanded-left {
      & > .pending {
        height: 75%;
        width: 100%;
      }

      & > .my-cases {
        height: 25%;
        width: 100%;
      }
    }

    &.expanded-right {
      & > .pending {
        height: 25%;
        width: 100%;
      }

      & > .my-cases {
        height: 75%;
        width: 100%;
      }
    }

    & > .separator {
      &.horizontal {
        display: none;
      }

      &.vertical {
        display: flex;
      }
    }
  }
}

html {
  &.with-selected-message {
    div.myCases {
      padding: 15px;
    }
  }
}

.leader-line {
  z-index: 1000;
}

.dropdown-multiselect {
  min-width: 300px;

  .dropdown-menu {
    box-shadow: 1px 1px 4px rgba(0, 0, 0, .5);

    .dropdown-header {
      &.dropdown-header-search {
        input[type=search], input[type=text] {
          padding: 3px 6px;
        }
      }
    }

    li {
      a {
        padding: 3px 10px;

        .fa {
          margin-right: 3px;
        }
      }
    }
  }
}

.form-group {
  &.form-group-flex {
    display: flex;
    flex-direction: row;
    align-items: center;

    label {
      margin-right: 3px;
      margin-bottom: 0;
      flex-grow: 0;
      flex-shrink: 0;
    }

    ng-dropdown-multiselect {
      flex-grow: 1;
      flex-shrink: 1;

      .dropdown-multiselect {
        width: 100%;
        min-width: initial;

        .dropdown-toggle {
          width: 100%;
          text-align: start;
        }
      }
    }
  }
}

.loading-ellipsis {
  &:after {
    overflow: hidden;
    display: inline-block;
    vertical-align: bottom;
    -webkit-animation: ellipsis steps(4,end) 900ms infinite;
    animation: ellipsis steps(4,end) 900ms infinite;
    content: "\2026"; /* ascii code for the ellipsis character */
    width: 0;
  }

  @keyframes ellipsis {
    to {
      width: 1.25em;
    }
  }

  @-webkit-keyframes ellipsis {
    to {
      width: 1.25em;
    }
  }
}

.button-progress {
  display: inline-block;
  position: relative;

  .button-progress-remaining {
    display: inline-block;
    margin-left: 5px;
  }

  .button-progress-bar {
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: black;
  }
}

div.my-cases-profiles {
  flex-grow: 1;
  flex-shrink: 1;
  margin-top: 10px;
  height: 100%;

  & > div.row {
    height: 100%;
    margin-bottom: 0;
    position: relative;

    & > div {
      width: 80%;
      margin-left: 10%;
    }

    & > div {
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      overflow-y: auto;
      padding-right: 0;
      padding-left: 0;

      .scrollbar();

      .outgoing-profiles-container();
    }
  }
}

.whatsapp-hsm {
	display: flex;
	flex-direction: column;
	width: 100%;
	min-height: 30px;
	padding: 2px;
	border-radius: 5px;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);

	.hsm-header-media {
		display: none;
		background-color: rgb(204, 208, 213);
		border-radius: 3px;

		.fa-image, .fa-file-alt, .fa-play-circle, .fa-map-marker-alt {
			display: none;
			font-size: 40pt;
			color: #fff;
		}
	}

	.hsm-contents {
		.hsm-header-text, .hsm-footer {
			display: none;
			padding: 3px 5px;
		}

		.hsm-header-text {
			font-size: 120%;
			font-weight: bold;
		}

		.hsm-body {
			padding: 3px 5px;
			word-break: break-all;

			em {
				font-style: normal;
				font-weight: bold;
			}
		}

		.hsm-footer {
			font-size: 70%;
		}
	}

	.hsm-buttons {
		display: none;

		.hsm-button2, .hsm-button3 {
			display: none;
		}
	}

	&.with-header {
		&.header-media {
			.hsm-header-media {
				display: flex;
				height: 100px;
				align-items: center;
				justify-content: center;
			}

			&.header-media-document {
				.fa-file-alt {
					display: block;
				}
			}

			&.header-media-video {
				.fa-play-circle {
					display: block;
				}
			}

			&.header-media-image {
				.fa-image {
					display: block;
				}
			}
		}

		&.header-text {
			.hsm-contents {
				.hsm-header-text {
					display: block;
				}
			}
		}
	}

	&.with-footer {
		&.footer-text {
			.hsm-contents {
				.hsm-footer {
					display: block;
				}
			}
		}
	}

	&.with-buttons {
		.hsm-buttons {
			display: flex;
			flex-direction: column;
			border-top: 1px solid rgb(218, 221, 225);

			.hsm-button1, .hsm-button2, .hsm-button3, .hsm-button-all {
				padding: 5px 0;
				text-align: center;
				color: rgb(13, 172, 241);
				font-weight: bold;

				.fa {
					color: rgb(13, 172, 241);
					margin-right: 5px;
				}
			}
		}

		&.with-buttons-2 {
			.hsm-buttons {
				.hsm-button2 {
					display: block;
				}
			}
		}

		&.with-buttons-3 {
			.hsm-buttons {
				.hsm-button3 {
					display: block;
				}
			}
		}

    &.with-buttons-all {
			.hsm-buttons {
				.hsm-button-all {
					display: block;
				}
			}
		}
	}
}
