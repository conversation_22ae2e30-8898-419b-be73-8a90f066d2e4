﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yoizen.Social.DomainModel
{
	/// <summary>
	/// Represents a single META Event association with either individual tags or group tags
	/// </summary>
	public class MetaEventAssociation
	{
		#region Properties

		/// <summary>
		/// Gets or sets the META Event identifier
		/// </summary>
		public string MetaEvent { get; set; }

		/// <summary>
		/// Gets or sets the list of individual tag IDs (used when AssociationType is Individual)
		/// </summary>
		public List<int> TagIds { get; set; }

		/// <summary>
		/// Gets or sets the list of group tag IDs (used when AssociationType is Group)
		/// </summary>
		public List<int> GroupTagIds { get; set; }

		/// <summary>
		/// Gets or sets the type of association (Individual or Group)
		/// </summary>
		public MetaEventAssociationType AssociationType { get; set; }

		#endregion

		#region Constructors

		/// <summary>
		/// Initializes a new instance of MetaEventAssociation
		/// </summary>
		public MetaEventAssociation()
		{
			this.TagIds = new List<int>();
			this.GroupTagIds = new List<int>();
			this.AssociationType = MetaEventAssociationType.Individual;
		}

		#endregion
	}

	/// <summary>
	/// Defines the type of META Event association
	/// </summary>
	public enum MetaEventAssociationType
	{
		/// <summary>
		/// Association with individual tags
		/// </summary>
		Individual = 0,

		/// <summary>
		/// Association with group tags
		/// </summary>
		Group = 1
	}
}
