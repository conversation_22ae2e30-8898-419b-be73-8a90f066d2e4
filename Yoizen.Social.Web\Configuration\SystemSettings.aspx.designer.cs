﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


    public partial class SystemSettings
    {

        /// <summary>
        /// messageError control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageError;

        /// <summary>
        /// panelContent control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelContent;

        /// <summary>
        /// popupmessageChangesApplied control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.PopupMessage popupmessageChangesApplied;

        /// <summary>
        /// popupmessageChangesAppliedButCouldntSubscribe control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.PopupMessage popupmessageChangesAppliedButCouldntSubscribe;

        /// <summary>
        /// popupmessageWhatsAppServiceVoiceCallRecordingCouldntCreateApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.PopupMessage popupmessageWhatsAppServiceVoiceCallRecordingCouldntCreateApp;

        /// <summary>
        /// popupmessageWhatsAppServiceVoiceCallRecordingCouldntCheckApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.PopupMessage popupmessageWhatsAppServiceVoiceCallRecordingCouldntCheckApp;

        /// <summary>
        /// hiddenTab control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenTab;

        /// <summary>
        /// liSocialServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl liSocialServices;

        /// <summary>
        /// liMoreServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl liMoreServices;

        /// <summary>
        /// dropdownlistDefaultLanguage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistDefaultLanguage;

        /// <summary>
        /// placeholderTheme control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderTheme;

        /// <summary>
        /// dropdownlistTheme control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistTheme;

        /// <summary>
        /// hiddenWebAgentVersion control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenWebAgentVersion;

        /// <summary>
        /// textboxWebAgentURL control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentURL;

        /// <summary>
        /// labelWebAgentVersion control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label labelWebAgentVersion;

        /// <summary>
        /// checkboxWebAgentAllowOutdatedLogins control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentAllowOutdatedLogins;

        /// <summary>
        /// placeholderEncryptMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderEncryptMessages;

        /// <summary>
        /// checkboxEncryptMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEncryptMessages;

        /// <summary>
        /// checkboxUseSentDate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUseSentDate;

        /// <summary>
        /// textboxDeleteNotifications control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDeleteNotifications;

        /// <summary>
        /// checkboxMarkWhiteListMessagesAsVIM control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxMarkWhiteListMessagesAsVIM;

        /// <summary>
        /// dropdownlistPrioritizeVIMOverQueueLevel control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistPrioritizeVIMOverQueueLevel;

        /// <summary>
        /// panelAttachments control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelAttachments;

        /// <summary>
        /// textboxAttachmentsRoute control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAttachmentsRoute;

        /// <summary>
        /// textboxMaxAssignableMessagesPerUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaxAssignableMessagesPerUser;

        /// <summary>
        /// placeholderAllowToConfigureACDBalancing control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowToConfigureACDBalancing;

        /// <summary>
        /// checkboxUseACDBalancing control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUseACDBalancing;

        /// <summary>
        /// dropdownlistACDBalancingWithQueueLevels control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistACDBalancingWithQueueLevels;

        /// <summary>
        /// checkboxACDBalancingWithQueueLevelsWorking control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxACDBalancingWithQueueLevelsWorking;

        /// <summary>
        /// checkboxAutoMarkAsReadMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAutoMarkAsReadMessages;

        /// <summary>
        /// textboxSessionTimeOut control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxSessionTimeOut;

        /// <summary>
        /// textboxAgentHistoricMaxMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentHistoricMaxMessages;

        /// <summary>
        /// checkboxAgentMustEnterDiscardReason control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentMustEnterDiscardReason;

        /// <summary>
        /// checkboxAllowForwardAction control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowForwardAction;

        /// <summary>
        /// checkboxForwardOutsideDomainAvailable control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxForwardOutsideDomainAvailable;

        /// <summary>
        /// textboxAvailableDomains control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAvailableDomains;

        /// <summary>
        /// placeholderActionsFacebookSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsFacebookSubject;

        /// <summary>
        /// textboxFacebookSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFacebookSubject;

        /// <summary>
        /// placeholderActionsFacebookMessengerSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsFacebookMessengerSubject;

        /// <summary>
        /// textboxFacebookMessengerSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFacebookMessengerSubject;

        /// <summary>
        /// placeholderActionsTwitterSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsTwitterSubject;

        /// <summary>
        /// textboxTwitterSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTwitterSubject;

        /// <summary>
        /// placeholderActionsWhatsappSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsWhatsappSubject;

        /// <summary>
        /// textboxWhatsappSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsappSubject;

        /// <summary>
        /// placeholderActionsChatSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsChatSubject;

        /// <summary>
        /// textboxChatSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatSubject;

        /// <summary>
        /// placeholderActionsTelegramSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsTelegramSubject;

        /// <summary>
        /// textboxTelegramSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTelegramSubject;

        /// <summary>
        /// placeholderActionsInstagramSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsInstagramSubject;

        /// <summary>
        /// textboxInstagramSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxInstagramSubject;

        /// <summary>
        /// placeholderActionsSMSSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsSMSSubject;

        /// <summary>
        /// textboxSMSSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxSMSSubject;

        /// <summary>
        /// placeholderActionsSkypeSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsSkypeSubject;

        /// <summary>
        /// textboxSkypeSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxSkypeSubject;

        /// <summary>
        /// placeholderActionsMailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderActionsMailSubject;

        /// <summary>
        /// textboxMailMaskSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMailMaskSubject;

        /// <summary>
        /// textboxMailMaskBody control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMailMaskBody;

        /// <summary>
        /// messageMailMaskBodyFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageMailMaskBodyFields;

        /// <summary>
        /// textboxFavoriteMails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFavoriteMails;

        /// <summary>
        /// checkboxAllowAgentToBlockUsers control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentToBlockUsers;

        /// <summary>
        /// placeholderAllowAgentsToReturnMessagesToQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesToQueue;

        /// <summary>
        /// checkboxAllowAgentsToReturnMessagesToQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesToQueue;

        /// <summary>
        /// checkboxAgentMustEnterReturnToQueueReason control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentMustEnterReturnToQueueReason;

        /// <summary>
        /// textboxMaximumNumberOfTimesMessageCanBeReturned control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaximumNumberOfTimesMessageCanBeReturned;

        /// <summary>
        /// placeholderAllowAgentsToReturnMessagesToSpecifiedQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesToSpecifiedQueue;

        /// <summary>
        /// checkboxAllowAgentsToSelectQueueOnReturnToQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToSelectQueueOnReturnToQueue;

        /// <summary>
        /// placeholderAllowAgentsToReturnMessagesWithRelatedMessagesToQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesWithRelatedMessagesToQueue;

        /// <summary>
        /// checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue;

        /// <summary>
        /// checkboxOutgoingMessagesEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxOutgoingMessagesEnabled;

        /// <summary>
        /// panelPbxIntegration control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelPbxIntegration;

        /// <summary>
        /// dropdownlistPbxIntegrationAgentGroupForNewAgents control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistPbxIntegrationAgentGroupForNewAgents;

        /// <summary>
        /// panelWebAgentConfigurationAllowUrlLogin control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelWebAgentConfigurationAllowUrlLogin;

        /// <summary>
        /// checkboxWebAgentConfigurationAllowUrlLogin control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationAllowUrlLogin;

        /// <summary>
        /// textboxWebAgentConfigurationUserNameLoginParameter control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationUserNameLoginParameter;

        /// <summary>
        /// checkboxWebAgentConfigurationPasswordRequired control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationPasswordRequired;

        /// <summary>
        /// textboxWebAgentConfigurationPasswordParameter control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationPasswordParameter;

        /// <summary>
        /// textboxWebAgentConfigurationKeyToDecryptPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationKeyToDecryptPassword;

        /// <summary>
        /// textboxWebAgentConfigurationHashParameter control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationHashParameter;

        /// <summary>
        /// textboxWebAgentConfigurationKeyToHash control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationKeyToHash;

        /// <summary>
        /// checkboxWebAgentConfigurationRemoveLoginForm control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationRemoveLoginForm;

        /// <summary>
        /// checkboxWebAgentConfigurationRemoveLogoutButton control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationRemoveLogoutButton;

        /// <summary>
        /// dropdownlistLogoutAction control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistLogoutAction;

        /// <summary>
        /// textboxWebAgentConfigurationRedirectUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationRedirectUrl;

        /// <summary>
        /// textboxWebAgentConfigurationLogoutMessage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationLogoutMessage;

        /// <summary>
        /// messageWebAgentConfigurationAllowUrlLoginCloud control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageWebAgentConfigurationAllowUrlLoginCloud;

        /// <summary>
        /// panelWebAgentConfigurationStateManagement control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelWebAgentConfigurationStateManagement;

        /// <summary>
        /// checkboxWebAgentConfigurationAllowChangeState control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationAllowChangeState;

        /// <summary>
        /// checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived;

        /// <summary>
        /// textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived;

        /// <summary>
        /// checkboxWebAgentConfigurationAllowAgentsToChangeState control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationAllowAgentsToChangeState;

        /// <summary>
        /// checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging;

        /// <summary>
        /// textboxWebAgentConfigurationChangeStateTargetOrigin control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationChangeStateTargetOrigin;

        /// <summary>
        /// checkboxWebAgentConfigurationAllowLogoutInvoke control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationAllowLogoutInvoke;

        /// <summary>
        /// checkboxWebAgentConfigurationShowMessageAfterLogoutReceived control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationShowMessageAfterLogoutReceived;

        /// <summary>
        /// textboxWebAgentConfigurationShowMessageAfterLogoutReceived control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationShowMessageAfterLogoutReceived;

        /// <summary>
        /// checkboxWebAgentConfigurationIgnoreLogoutAfterError control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationIgnoreLogoutAfterError;

        /// <summary>
        /// checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging;

        /// <summary>
        /// textboxWebAgentConfigurationLogoutInvokeTargetOrigin control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWebAgentConfigurationLogoutInvokeTargetOrigin;

        /// <summary>
        /// textboxMinutesPredictedAht control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMinutesPredictedAht;

        /// <summary>
        /// textboxSecondsEwt control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxSecondsEwt;

        /// <summary>
        /// checkboxASAPersonalized control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxASAPersonalized;

        /// <summary>
        /// textboxAsaBase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAsaBase;

        /// <summary>
        /// checkboxCheckLastQueueOfOpenCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueOfOpenCase;

        /// <summary>
        /// checkboxIgnoreLastQueueForSLMovedMessage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxIgnoreLastQueueForSLMovedMessage;

        /// <summary>
        /// textboxMaxElapsedMinutesToCloseCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseCases;

        /// <summary>
        /// checkboxReplyInCloseCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxReplyInCloseCase;

        /// <summary>
        /// textboxAutoReplyInCloseCaseText control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyInCloseCaseText;

        /// <summary>
        /// textboxTagCloseCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagCloseCase;

        /// <summary>
        /// messageTagAutoCloseCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageTagAutoCloseCase;

        /// <summary>
        /// placeholderYFlowCasesRelated control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowCasesRelated;

        /// <summary>
        /// textboxMaxElapsedMinutesToCloseYFlowCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseYFlowCases;

        /// <summary>
        /// checkboxInvokeYFlowWhenClosedCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxInvokeYFlowWhenClosedCases;

        /// <summary>
        /// textboxMaxElapsedMinutesToCloseHsmCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseHsmCases;

        /// <summary>
        /// textboxTagOnHsmCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagOnHsmCases;

        /// <summary>
        /// messageTagCloseCaseHsm control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageTagCloseCaseHsm;

        /// <summary>
        /// placeholderCasesCloseConcurrent control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderCasesCloseConcurrent;

        /// <summary>
        /// textboxCasesCloseConcurrent control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCasesCloseConcurrent;

        /// <summary>
        /// dropdownlistTagCasesOnStart control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistTagCasesOnStart;

        /// <summary>
        /// dropdownlistTagCasesOnClose control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistTagCasesOnClose;

        /// <summary>
        /// checkboxImportantTag control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxImportantTag;

        /// <summary>
        /// dropdownlistTagCasesOnDiscard control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistTagCasesOnDiscard;

        /// <summary>
        /// checkboxTagOutgoing control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxTagOutgoing;

        /// <summary>
        /// checkboxImportantTagOutgoing control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxImportantTagOutgoing;

        /// <summary>
        /// checkboxAlwaysUpdateCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAlwaysUpdateCase;

        /// <summary>
        /// checkboxAllowToAddMessagesToCasesWithMessagesInQueue control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowToAddMessagesToCasesWithMessagesInQueue;

        /// <summary>
        /// checkboxAllowNotificationWhenClosePendingCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAllowNotificationWhenClosePendingCase;

        /// <summary>
        /// checkboxCheckLastQueueByTime control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueByTime;

        /// <summary>
        /// textboxLastQueueByTime control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLastQueueByTime;

        /// <summary>
        /// textboxFilterEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFilterEmailSubject;

        /// <summary>
        /// messageFilterEmailSubjectFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

        /// <summary>
        /// textboxFilterEmailEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFilterEmailEmails;

        /// <summary>
        /// checkboxFilterEmailSendToAdministrators control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxFilterEmailSendToAdministrators;

        /// <summary>
        /// checkboxFilterEmailSendToSupervisors control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxFilterEmailSendToSupervisors;

        /// <summary>
        /// textboxFilterEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFilterEmailTemplate;

        /// <summary>
        /// messageFilterEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageFilterEmailTemplateFields;

        /// <summary>
        /// divSocialServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divSocialServices;

        /// <summary>
        /// divTwitterService control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divTwitterService;

        /// <summary>
        /// checkboxTwitterAllowMentionInMultipleServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxTwitterAllowMentionInMultipleServices;

        /// <summary>
        /// divRestChatService control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divRestChatService;

        /// <summary>
        /// textboxRestChatMaxConcurrentSessions control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxRestChatMaxConcurrentSessions;

        /// <summary>
        /// textboxRestChatMaxConcurrentCallsPerSession control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxRestChatMaxConcurrentCallsPerSession;

        /// <summary>
        /// divChatService control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divChatService;

        /// <summary>
        /// dropdownlistChatCloudSameServer control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistChatCloudSameServer;

        /// <summary>
        /// textboxChatCloudOtherServer control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCloudOtherServer;

        /// <summary>
        /// textboxChatCloudPort control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCloudPort;

        /// <summary>
        /// textboxChatCloudPortForYSocial control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCloudPortForYSocial;

        /// <summary>
        /// placeholderChatOnPremise control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderChatOnPremise;

        /// <summary>
        /// dropdownlisCloudPortOverHttps control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlisCloudPortOverHttps;

        /// <summary>
        /// dropdownlisCloudOtherServerPortYSocialOverHttps control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlisCloudOtherServerPortYSocialOverHttps;

        /// <summary>
        /// panelChatOnPremiseAlerts control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelChatOnPremiseAlerts;

        /// <summary>
        /// textboxChatStartedEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStartedEmailSubject;

        /// <summary>
        /// textboxChatStartedDestinationEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStartedDestinationEmails;

        /// <summary>
        /// textboxChatStartedEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStartedEmailTemplate;

        /// <summary>
        /// messageChatStartedEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageChatStartedEmailTemplateFields;

        /// <summary>
        /// textboxChatStoppedEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStoppedEmailSubject;

        /// <summary>
        /// textboxChatStoppedDestinationEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStoppedDestinationEmails;

        /// <summary>
        /// textboxChatStoppedEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatStoppedEmailTemplate;

        /// <summary>
        /// messageChatStoppedEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageChatStoppedEmailTemplateFields;

        /// <summary>
        /// textboxChatCrashedEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCrashedEmailSubject;

        /// <summary>
        /// textboxChatCrashedDestinationEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCrashedDestinationEmails;

        /// <summary>
        /// textboxChatCrashedEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxChatCrashedEmailTemplate;

        /// <summary>
        /// messageChatCrashedEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageChatCrashedEmailTemplateFields;

        /// <summary>
        /// divWhatsappService control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divWhatsappService;

        /// <summary>
        /// textboxWhatsappUrlRtNotifications control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsappUrlRtNotifications;

        /// <summary>
        /// dropdownlistWhatsappDefaultInternationCode control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsappDefaultInternationCode;

        /// <summary>
        /// placeholderWhatsAppServiceVoiceCalls control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderWhatsAppServiceVoiceCalls;

        /// <summary>
        /// dropdownlistWhatsAppVoiceCallsOnCallBehaviour control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsAppVoiceCallsOnCallBehaviour;

        /// <summary>
        /// placeholderWhatsAppServiceVoiceCallsSuperYoizen control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderWhatsAppServiceVoiceCallsSuperYoizen;

        /// <summary>
        /// textboxWhatsAppServiceVoiceCallsIceServers control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppServiceVoiceCallsIceServers;

        /// <summary>
        /// placeholderWhatsappServiceCatalogApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderWhatsappServiceCatalogApp;

        /// <summary>
        /// dropdownlistWhatsappServiceCatalogApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsappServiceCatalogApp;

        /// <summary>
        /// placeholderWhatsappEmbeddedSignUpApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderWhatsappEmbeddedSignUpApp;

        /// <summary>
        /// dropdownlistWhatsappEmbeddedSignUpApp control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsappEmbeddedSignUpApp;

        /// <summary>
        /// panelWhatsAppServiceVoiceCallsRecording control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelWhatsAppServiceVoiceCallsRecording;

        /// <summary>
        /// textboxWhatsAppServiceVoiceCallsRecordingHostname control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppServiceVoiceCallsRecordingHostname;

        /// <summary>
        /// textboxWhatsAppServiceVoiceCallsRecordingPort control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppServiceVoiceCallsRecordingPort;

        /// <summary>
        /// textboxWhatsAppServiceVoiceCallsRecordingJWTSecret control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppServiceVoiceCallsRecordingJWTSecret;

        /// <summary>
        /// textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname;

        /// <summary>
        /// divTelegramService control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divTelegramService;

        /// <summary>
        /// textboxTelegramUrlRtNotifications control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTelegramUrlRtNotifications;

		/// <summary>
		/// divCapiService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCapiService;

		/// <summary>
		/// checkboxEnableCapi control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableCapi;

		/// <summary>
		/// listboxMetaEvents control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxMetaEvents;

		/// <summary>
		/// listboxTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxTags;

		/// <summary>
		/// listboxGroupTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxGroupTags;

		/// <summary>
		/// listboxAlertMessagesDeliveryFailedVia control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxAlertMessagesDeliveryFailedVia;

        /// <summary>
        /// listboxAlertMessagesDeliveryFailedFromServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.ListBox listboxAlertMessagesDeliveryFailedFromServices;

        /// <summary>
        /// hiddenDeliveryFailedConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenDeliveryFailedConnection;

        /// <summary>
        /// textboxAlertMessagesDeliveryFailedViaMailEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAlertMessagesDeliveryFailedViaMailEmailSubject;

        /// <summary>
        /// textboxAlertMessagesDeliveryFailedViaMailEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAlertMessagesDeliveryFailedViaMailEmailTemplate;

        /// <summary>
        /// messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields;

		/// <summary>
		/// panelAlertsOtherProblems control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelAlertsOtherProblems;

        /// <summary>
        /// hiddenLicenseExpiredConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenLicenseExpiredConnection;

        /// <summary>
        /// textboxLicenseExpiredEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLicenseExpiredEmailSubject;

        /// <summary>
        /// textboxLicenseExpiredEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLicenseExpiredEmails;

        /// <summary>
        /// textboxLicenseExpiredEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLicenseExpiredEmailTemplate;

        /// <summary>
        /// messageLicenseExpiredEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageLicenseExpiredEmailTemplateFields;

        /// <summary>
        /// textboxAttachmentsMinimumFreeSpace control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAttachmentsMinimumFreeSpace;

        /// <summary>
        /// hiddenFieldOutOfDiskSpaceForAttachmentsConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldOutOfDiskSpaceForAttachmentsConnection;

        /// <summary>
        /// textboxOutOfDiskSpaceForAttachmentsEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfDiskSpaceForAttachmentsEmailSubject;

        /// <summary>
        /// textboxOutOfDiskSpaceForAttachmentsEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfDiskSpaceForAttachmentsEmails;

        /// <summary>
        /// textboxOutOfDiskSpaceForAttachmentsEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfDiskSpaceForAttachmentsEmailTemplate;

        /// <summary>
        /// messageOutOfDiskSpaceForAttachmentsEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageOutOfDiskSpaceForAttachmentsEmailTemplateFields;

        /// <summary>
        /// hiddenFieldDatabaseProblemsConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldDatabaseProblemsConnection;

        /// <summary>
        /// textboxDatabaseProblemsEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDatabaseProblemsEmailSubject;

        /// <summary>
        /// textboxDatabaseProblemsEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDatabaseProblemsEmails;

        /// <summary>
        /// textboxDatabaseProblemsEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDatabaseProblemsEmailTemplate;

        /// <summary>
        /// messageDatabaseProblemsEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageDatabaseProblemsEmailTemplateFields;

        /// <summary>
        /// hiddenFieldOutOfMemoryConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldOutOfMemoryConnection;

        /// <summary>
        /// textboxOutOfMemoryEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfMemoryEmailSubject;

        /// <summary>
        /// textboxOutOfMemoryEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfMemoryEmails;

        /// <summary>
        /// textboxOutOfMemoryEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxOutOfMemoryEmailTemplate;

        /// <summary>
        /// messageOutOfMemoryEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageOutOfMemoryEmailTemplateFields;

        /// <summary>
        /// checkboxUseAnnoyingUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUseAnnoyingUser;

        /// <summary>
        /// textboxMaxMessagesAnnoyingUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaxMessagesAnnoyingUser;

        /// <summary>
        /// checkboxAddAnnoyingUserToBlackList control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAddAnnoyingUserToBlackList;

        /// <summary>
        /// dropdownlistDiscardMessagesFromAnnoyingUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistDiscardMessagesFromAnnoyingUser;

        /// <summary>
        /// checkboxNotifySupervisorFromScreen control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxNotifySupervisorFromScreen;

        /// <summary>
        /// checkboxMarkAnnoyingUserMessageAsVIM control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxMarkAnnoyingUserMessageAsVIM;

        /// <summary>
        /// hiddenFieldAnnoyingUserConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldAnnoyingUserConnection;

        /// <summary>
        /// textboxAnnoyingUserEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAnnoyingUserEmailSubject;

        /// <summary>
        /// textboxAnnoyingUserEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAnnoyingUserEmails;

        /// <summary>
        /// textboxAnnoyingUserEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAnnoyingUserEmailTemplate;

        /// <summary>
        /// messageAnnoyingUserEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageAnnoyingUserEmailTemplateFields;

        /// <summary>
        /// hiddenFieldAgentCreatedLoginInformationConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldAgentCreatedLoginInformationConnection;

        /// <summary>
        /// textboxAgentCreatedLoginInformationEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentCreatedLoginInformationEmailSubject;

        /// <summary>
        /// textboxAgentCreatedLoginInformationEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentCreatedLoginInformationEmailTemplate;

        /// <summary>
        /// messageAgentCreatedLoginInformationEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageAgentCreatedLoginInformationEmailTemplateFields;

        /// <summary>
        /// hiddenFieldAgentPasswordChangedConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldAgentPasswordChangedConnection;

        /// <summary>
        /// textboxAgentPasswordChangedEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordChangedEmailSubject;

        /// <summary>
        /// textboxAgentPasswordChangedEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordChangedEmailTemplate;

        /// <summary>
        /// messageAgentPasswordChangedEmailTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageAgentPasswordChangedEmailTemplateFields;

        /// <summary>
        /// hiddenFieldDefaultEmailConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenFieldDefaultEmailConnection;

        /// <summary>
        /// checkboxGenerateDailyReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxGenerateDailyReport;

        /// <summary>
        /// checkboxEnableFtpDailyReport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableFtpDailyReport;

        /// <summary>
        /// listboxFtps control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.ListBox listboxFtps;

        /// <summary>
        /// textboxFtpDirectoryDailyReports control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxFtpDirectoryDailyReports;

        /// <summary>
        /// checkboxDailyReportsZipExcel control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxDailyReportsZipExcel;

        /// <summary>
        /// checkboxDailyReportsZipCSV control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxDailyReportsZipCSV;

        /// <summary>
        /// placeholderDailyReportsToMantain control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderDailyReportsToMantain;

        /// <summary>
        /// textboxDailyReportsToMantain control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDailyReportsToMantain;

        /// <summary>
        /// checkboxlistDailyReportsToGenerate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBoxList checkboxlistDailyReportsToGenerate;

        /// <summary>
        /// textboxDailyReportsEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDailyReportsEmailSubject;

        /// <summary>
        /// messageDailyReportsEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageDailyReportsEmailSubject;

        /// <summary>
        /// textboxDailyReportsEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDailyReportsEmails;

        /// <summary>
        /// textboxDailyReportsEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDailyReportsEmailTemplate;

        /// <summary>
        /// messageDailyReportsEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageDailyReportsEmailTemplate;

        /// <summary>
        /// dropdownlistDailyReportsExportFormat control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistDailyReportsExportFormat;

        /// <summary>
        /// placeholderDailyReportsDeleteLocalCopy control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderDailyReportsDeleteLocalCopy;

        /// <summary>
        /// checkboxDailyReportsDeleteLocalCopy control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxDailyReportsDeleteLocalCopy;

        /// <summary>
        /// literalMaxRecordsToExport control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Literal literalMaxRecordsToExport;

        /// <summary>
        /// placeholderMinutesToAbortExporting control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderMinutesToAbortExporting;

        /// <summary>
        /// textboxMinutesToAbortExporting control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMinutesToAbortExporting;

        /// <summary>
        /// textboxExportEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxExportEmailSubject;

        /// <summary>
        /// messageExportEmailSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageExportEmailSubject;

        /// <summary>
        /// textboxExportEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxExportEmailTemplate;

        /// <summary>
        /// messageExportEmailTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageExportEmailTemplate;

        /// <summary>
        /// textboxExportEmailAbortedSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxExportEmailAbortedSubject;

        /// <summary>
        /// messageExportEmailAbortedSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageExportEmailAbortedSubject;

        /// <summary>
        /// textboxExportEmailAbortedTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxExportEmailAbortedTemplate;

        /// <summary>
        /// messageExportEmailAbortedTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageExportEmailAbortedTemplate;

        /// <summary>
        /// textboxScheduleReportsToMantain control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxScheduleReportsToMantain;

        /// <summary>
        /// divBitLy control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divBitLy;

        /// <summary>
        /// checkboxBitLyCustomShortener control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxBitLyCustomShortener;

        /// <summary>
        /// textboxBitLyAccessToken control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxBitLyAccessToken;

        /// <summary>
        /// requiredfieldvalidatorBitLyAcessToken control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.RequiredFieldValidator requiredfieldvalidatorBitLyAcessToken;

        /// <summary>
        /// divAllowToExtendBusinessData control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAllowToExtendBusinessData;

        /// <summary>
        /// hiddenExtendedProfileBusinessCodeFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenExtendedProfileBusinessCodeFields;

        /// <summary>
        /// customvalidatorExtendedProfileBusinessCodeFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CustomValidator customvalidatorExtendedProfileBusinessCodeFields;

        /// <summary>
        /// divBusinessDataRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divBusinessDataRegex;

        /// <summary>
        /// textboxBusinessDataRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxBusinessDataRegex;

        /// <summary>
        /// textboxBusinessDataFormatMessage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxBusinessDataFormatMessage;

        /// <summary>
        /// textboxBusinessDataWrongInputMessage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxBusinessDataWrongInputMessage;

        /// <summary>
        /// divAllowToExtendProfile control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAllowToExtendProfile;

        /// <summary>
        /// hiddenExtendedProfileFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenExtendedProfileFields;

        /// <summary>
        /// divAllowToExtendCase control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAllowToExtendCase;

        /// <summary>
        /// hiddenExtendedCaseFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenExtendedCaseFields;

        /// <summary>
        /// divPushNotifications control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divPushNotifications;

        /// <summary>
        /// dropdownlistPushNotificationsServiceBusProtocol control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistPushNotificationsServiceBusProtocol;

        /// <summary>
        /// placeholderPushNotificationsServiceBusConcurrentMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderPushNotificationsServiceBusConcurrentMessages;

        /// <summary>
        /// textboxPushNotificationsServiceBusConcurrentMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxPushNotificationsServiceBusConcurrentMessages;

        /// <summary>
        /// placeholderPushNotificationsServiceBusConcurrentStatuses control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderPushNotificationsServiceBusConcurrentStatuses;

        /// <summary>
        /// textboxPushNotificationsServiceBusConcurrentStatuses control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxPushNotificationsServiceBusConcurrentStatuses;

        /// <summary>
        /// placeholderPushNotificationsServiceBusConcurrentMassive control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderPushNotificationsServiceBusConcurrentMassive;

        /// <summary>
        /// textboxPushNotificationsServiceBusConcurrentMassive control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxPushNotificationsServiceBusConcurrentMassive;

        /// <summary>
        /// panelMaintenance control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelMaintenance;

        /// <summary>
        /// textboxMaintenanceCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaintenanceCases;

        /// <summary>
        /// textboxMaintenanceHist control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaintenanceHist;

        /// <summary>
        /// textboxMaintenanceHistByInterval control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMaintenanceHistByInterval;

        /// <summary>
        /// panelTimeZoneConfiguration control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelTimeZoneConfiguration;

        /// <summary>
        /// literalTimeZoneServer control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Literal literalTimeZoneServer;

        /// <summary>
        /// literalTimeZoneServerDST control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Literal literalTimeZoneServerDST;

        /// <summary>
        /// hiddenDefaultTimeZone control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenDefaultTimeZone;

        /// <summary>
        /// panelTimeZoneConsolidation control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelTimeZoneConsolidation;

        /// <summary>
        /// hiddenTimeZonesToConsolide control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenTimeZonesToConsolide;

        /// <summary>
        /// divMoreServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divMoreServices;

        /// <summary>
        /// placeholderAllowYFlow control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYFlow;

        /// <summary>
        /// checkboxEnableYFlow control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableYFlow;

        /// <summary>
        /// textboxYFlowUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowUrl;

        /// <summary>
        /// textboxYFlowUrlWeb control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowUrlWeb;

        /// <summary>
        /// textboxYFlowUrlApi control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowUrlApi;

        /// <summary>
        /// panelAccessYFlow control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelAccessYFlow;

        /// <summary>
        /// spanAccessTokenOk control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanAccessTokenOk;

        /// <summary>
        /// spanAccessTokenError control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanAccessTokenError;

        /// <summary>
        /// textboxYFlowTimeout control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowTimeout;

        /// <summary>
        /// checkboxYFlowDerivationEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxYFlowDerivationEnabled;

        /// <summary>
        /// textboxTagInvocation control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagInvocation;

        /// <summary>
        /// messageFirstAutomaticActionReply control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageFirstAutomaticActionReply;

        /// <summary>
        /// textboxTagTimeout control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagTimeout;

        /// <summary>
        /// messageTagTimeout control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageTagTimeout;

        /// <summary>
        /// placeholderAllowYFlowContingency control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYFlowContingency;

        /// <summary>
        /// checkboxEnableYFlowContingency control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableYFlowContingency;

        /// <summary>
        /// textboxYFlowContingencyUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowContingencyUrl;

        /// <summary>
        /// textboxYFlowContingencyUrlWeb control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowContingencyUrlWeb;

        /// <summary>
        /// textboxYFlowContingencyUrlApi control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowContingencyUrlApi;

        /// <summary>
        /// panelAccessYFlowContingency control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelAccessYFlowContingency;

        /// <summary>
        /// spanAccessTokenContingencyOk control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanAccessTokenContingencyOk;

        /// <summary>
        /// spanAccessTokenContingencyError control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanAccessTokenContingencyError;

        /// <summary>
        /// textboxYFlowContingencyTimeout control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowContingencyTimeout;

        /// <summary>
        /// textboxTagLabel control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagLabel;

        /// <summary>
        /// textboxYFlowMaxMinutesForPendingMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowMaxMinutesForPendingMessages;

        /// <summary>
        /// dropdownlistYFlowActionAfterMaxMinutesForPendingMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistYFlowActionAfterMaxMinutesForPendingMessages;

        /// <summary>
        /// hiddenYFlowPendingMessagesCallbackEndpoint control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenYFlowPendingMessagesCallbackEndpoint;

        /// <summary>
        /// placeholderYFlowMaxConcurrentCallsPerSessionPendingMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowMaxConcurrentCallsPerSessionPendingMessages;

        /// <summary>
        /// textboxYFlowMaxConcurrentSessionsPendingMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowMaxConcurrentSessionsPendingMessages;

        /// <summary>
        /// textboxYFlowMaxConcurrentCallsPerSessionPendingMessages control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowMaxConcurrentCallsPerSessionPendingMessages;

        /// <summary>
        /// placeholderYFlowPendingMessagesConfig control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowPendingMessagesConfig;

        /// <summary>
        /// dropdownlistYFlowPendingMessagesUseCustomCallback control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistYFlowPendingMessagesUseCustomCallback;

        /// <summary>
        /// textboxYFlowPendingMessagesCustomCallbackUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowPendingMessagesCustomCallbackUrl;

        /// <summary>
        /// panelYFlowPendingReplyCases control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelYFlowPendingReplyCases;

        /// <summary>
        /// textboxYFlowPendingReplyCasesConcurrentCalls control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowPendingReplyCasesConcurrentCalls;

        /// <summary>
        /// hiddenYFlowAuthenticationFailedConnection control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenYFlowAuthenticationFailedConnection;

        /// <summary>
        /// textboxYFlowAuthenticationFailedSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowAuthenticationFailedSubject;

        /// <summary>
        /// textboxYFlowAuthenticationFailedEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowAuthenticationFailedEmails;

        /// <summary>
        /// textboxYFlowAuthenticationFailedTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowAuthenticationFailedTemplate;

        /// <summary>
        /// messageYFlowAuthenticationFailedTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageYFlowAuthenticationFailedTemplateFields;

        /// <summary>
        /// hiddenYFlowInvokeFailed control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenYFlowInvokeFailed;

        /// <summary>
        /// textboxYFlowInvokeFailedSubject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowInvokeFailedSubject;

        /// <summary>
        /// textboxYFlowInvokeFailedEmails control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowInvokeFailedEmails;

        /// <summary>
        /// textboxYFlowInvokeFailedTemplate control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYFlowInvokeFailedTemplate;

        /// <summary>
        /// messageYFlowInvokeFailedTemplateFields control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageYFlowInvokeFailedTemplateFields;

        /// <summary>
        /// placeholderAllowYSmart control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYSmart;

        /// <summary>
        /// checkboxEnableYSmart control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableYSmart;

        /// <summary>
        /// textboxYSmartTimeout control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYSmartTimeout;

        /// <summary>
        /// textboxTagTimeout1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxTagTimeout1;

        /// <summary>
        /// messageTagTimeout1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageTagTimeout1;

        /// <summary>
        /// hiddenProject control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenProject;

        /// <summary>
        /// hiddenProjectId control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenProjectId;

        /// <summary>
        /// placeholderCognitiveServicesEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderCognitiveServicesEnabled;

        /// <summary>
        /// checkboxUseCognitiveServices control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUseCognitiveServices;

        /// <summary>
        /// textboxCognitiveServicesToken control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCognitiveServicesToken;

        /// <summary>
        /// textboxCognitiveServicesTokenSecret control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCognitiveServicesTokenSecret;

        /// <summary>
        /// placeholderSurveysEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderSurveysEnabled;

        /// <summary>
        /// checkboxEnableSurveys control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

        /// <summary>
        /// messageSurveysDisabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Yoizen.Web.UI.Message messageSurveysDisabled;

        /// <summary>
        /// placeholderVideoCallsEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderVideoCallsEnabled;

        /// <summary>
        /// divCubiq control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCubiq;

        /// <summary>
        /// textboxCubiqUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCubiqUrl;

        /// <summary>
        /// textboxCubiqApiKey control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCubiqApiKey;

        /// <summary>
        /// textboxCubiqSecret control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCubiqSecret;

        /// <summary>
        /// textboxCubiqRecordingUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCubiqRecordingUrl;

        /// <summary>
        /// placeholderAllowYUsage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYUsage;

        /// <summary>
        /// checkboxEnableYUsage control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxEnableYUsage;

        /// <summary>
        /// textboxYUsageUrl control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYUsageUrl;

        /// <summary>
        /// textboxYUsageUrlApi control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxYUsageUrlApi;

        /// <summary>
        /// spanYUsageTokenOk control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanYUsageTokenOk;

        /// <summary>
        /// spanYUsageTokenError control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl spanYUsageTokenError;

        /// <summary>
        /// divSecurityPolitics control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divSecurityPolitics;

        /// <summary>
        /// dropdownlistAuthenticationType control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistAuthenticationType;

        /// <summary>
        /// checkboxLdapUseLdap control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLdapUseLdap;

        /// <summary>
        /// textboxLdapServer control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapServer;

        /// <summary>
        /// textboxLdapPort control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapPort;

        /// <summary>
        /// textboxLdapSearchDN control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapSearchDN;

        /// <summary>
        /// textboxLdapUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapUser;

        /// <summary>
        /// placeholderLdapPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.PlaceHolder placeholderLdapPassword;

        /// <summary>
        /// labelLdapPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Label labelLdapPassword;

        /// <summary>
        /// literalLdapPasswordChangePasswordTitle control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Literal literalLdapPasswordChangePasswordTitle;

        /// <summary>
        /// literalLdapPasswordNewPasswordTitle control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Literal literalLdapPasswordNewPasswordTitle;

        /// <summary>
        /// checkboxLdapPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLdapPassword;

        /// <summary>
        /// textboxLdapPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapPassword;

        /// <summary>
        /// checkboxLdapUseSecureAuthentication control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLdapUseSecureAuthentication;

        /// <summary>
        /// textboxLdapUserSearchFilter control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLdapUserSearchFilter;

        /// <summary>
        /// checkboxLdapLocalUsers control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLdapLocalUsers;

        /// <summary>
        /// checkboxLdapLocalAgents control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLdapLocalAgents;

        /// <summary>
        /// checkboxLDAPUseConfigurationParams control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxLDAPUseConfigurationParams;

        /// <summary>
        /// textboxLDAPConfigurationFirstName control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLDAPConfigurationFirstName;

        /// <summary>
        /// textboxLDAPConfigurationLastName control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLDAPConfigurationLastName;

        /// <summary>
        /// textboxLDAPConfigurationUserName control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLDAPConfigurationUserName;

        /// <summary>
        /// textboxLDAPConfigurationEmail control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLDAPConfigurationEmail;

        /// <summary>
        /// textboxLDAPConfigurationLDAP control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxLDAPConfigurationLDAP;

        /// <summary>
        /// divGoogleAuth control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divGoogleAuth;

        /// <summary>
        /// checkboxGoogleAuthEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxGoogleAuthEnabled;

        /// <summary>
        /// textboxGoogleAuthHostedDomain control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxGoogleAuthHostedDomain;

        /// <summary>
        /// checkboxGoogleAuthLocalUsers control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxGoogleAuthLocalUsers;

        /// <summary>
        /// checkboxGoogleAuthLocalAgents control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxGoogleAuthLocalAgents;

        /// <summary>
        /// checkboxGoogleAuthUseCustom control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxGoogleAuthUseCustom;

        /// <summary>
        /// textboxGoogleAuthClientID control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxGoogleAuthClientID;

        /// <summary>
        /// textboxGoogleAuthClientSecret control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxGoogleAuthClientSecret;

        /// <summary>
        /// divLoginWithKeycloak control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divLoginWithKeycloak;

        /// <summary>
        /// divKeycloakConfig control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divKeycloakConfig;

        /// <summary>
        /// checkboxKeycloakAuthEnabled control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxKeycloakAuthEnabled;

        /// <summary>
        /// textboxKeycloakEndpoint control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxKeycloakEndpoint;

        /// <summary>
        /// textboxKeycloakClientName control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxKeycloakClientName;

        /// <summary>
        /// textboxKeycloakRealmName control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxKeycloakRealmName;

        /// <summary>
        /// textboxSamlButtonText control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxSamlButtonText;

        /// <summary>
        /// checkboxSamlLocalUsers control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxSamlLocalUsers;

        /// <summary>
        /// checkboxSamlLocalAgents control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxSamlLocalAgents;

        /// <summary>
        /// divCloudIPRestrictions control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCloudIPRestrictions;

        /// <summary>
        /// textboxCloudIPRestrictionsWeb control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCloudIPRestrictionsWeb;

        /// <summary>
        /// textboxCloudIPRestrictionsWebAgent control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxCloudIPRestrictionsWebAgent;

        /// <summary>
        /// divUserSecurityPolitics control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divUserSecurityPolitics;

        /// <summary>
        /// checkboxMandatory2FAUser control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxMandatory2FAUser;

        /// <summary>
        /// dropdownlistPasswordStrength control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistPasswordStrength;

        /// <summary>
        /// textboxUserPasswordExpireDay control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxUserPasswordExpireDay;

        /// <summary>
        /// checkboxUserPasswordRepeat control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUserPasswordRepeat;

        /// <summary>
        /// textboxUserPasswordRepeat control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxUserPasswordRepeat;

        /// <summary>
        /// textboxUserPasswordWrongBlock control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxUserPasswordWrongBlock;

        /// <summary>
        /// textboxUserPasswordWrongsCaptha control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxUserPasswordWrongsCaptha;

        /// <summary>
        /// checkboxUserRefreshPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUserRefreshPassword;

        /// <summary>
        /// textboxDaysInactiveUserBlock control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDaysInactiveUserBlock;

        /// <summary>
        /// checkboxUserPasswordFirstChange control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxUserPasswordFirstChange;

        /// <summary>
        /// textboxUserRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxUserRegex;

        /// <summary>
        /// textboxMessageUserRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMessageUserRegex;

        /// <summary>
        /// divAgentSecurityPolitics control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAgentSecurityPolitics;

        /// <summary>
        /// checkboxMandatory2FAAgent control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxMandatory2FAAgent;

        /// <summary>
        /// dropdownlistAgentPasswordStrength control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList dropdownlistAgentPasswordStrength;

        /// <summary>
        /// textboxAgentPasswordExpireDay control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordExpireDay;

        /// <summary>
        /// checkboxAgentPasswordRepeat control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentPasswordRepeat;

        /// <summary>
        /// textboxAgentPasswordRepeat control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordRepeat;

        /// <summary>
        /// textboxAgentPasswordWrongBlock control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordWrongBlock;

        /// <summary>
        /// textboxAgentPasswordWrongsCaptha control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentPasswordWrongsCaptha;

        /// <summary>
        /// textboxDaysInactiveAgentBlock control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxDaysInactiveAgentBlock;

        /// <summary>
        /// checkboxAgentCanChangePassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentCanChangePassword;

        /// <summary>
        /// checkboxAgentRefreshPassword control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentRefreshPassword;

        /// <summary>
        /// checkboxAgentPasswordFirstChange control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.CheckBox checkboxAgentPasswordFirstChange;

        /// <summary>
        /// textboxAgentRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxAgentRegex;

        /// <summary>
        /// textboxMessageAgentRegex control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.TextBox textboxMessageAgentRegex;

        /// <summary>
        /// panelCloudHeaders control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Panel panelCloudHeaders;

        /// <summary>
        /// hiddenCloudHeadersToAdd control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenCloudHeadersToAdd;

        /// <summary>
        /// hiddenCloudHeadersToRemove control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hiddenCloudHeadersToRemove;

        /// <summary>
        /// buttonSave control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.Button buttonSave;

        /// <summary>
        /// Master property.
        /// </summary>
        /// <remarks>
        /// Auto-generated property.
        /// </remarks>
        public new Yoizen.Social.Web.Master Master
        {
            get
            {
                return ((Yoizen.Social.Web.Master)(base.Master));
            }
        }
    }
}
