<div class="email-contact-container">
    <div class="email-contact-header">
        <div class="input-group">
            <span class="input-group-addon">{{'EMAIL_CONTACTS_DEST_LABEL' | translate}}</span>
            <input type="text"
                   class="form-control"
                   ng-model="emailContactsCtrl.searchText"
                   placeholder="{{ 'SEARCH' | translate }}"
                   autocomplete="off"
                   spellcheck="false"
                   autocorrect="off"
                   tabindex="1" 
                   ng-keypress="($event.which === 13) && emailContactsCtrl.loadEmailContacts()"/>
            <span class="input-group-btn">
                <a role="button"
                    uib-tooltip="{{'MY_PREDEFINED_ANSWER_INSERT' | translate}}"
                    tooltip-placement="left-top"
                    class="btn btn-default"
                    ng-click="emailContactsCtrl.loadEmailContacts()">
                        <span class="fa fa-search"></span>
                </a></span>
            <span class="input-group-btn">
                <a role="button"
                    uib-tooltip="{{'MY_PREDEFINED_ANSWER_INSERT' | translate}}"
                    tooltip-placement="left-top"
                    class="btn btn-default"
                    ng-click="emailContactsCtrl.showInsertUpdateModal()">
                        <span class="fas fa-plus"></span>
                </a>
            </span>
        </div>
    </div>
    

    <div class="email-contact-results" ng-hide="emailContactsCtrl.noEmailContacts">
        <div>
            <div class="email-contact-results-rows">
                <div class="row email-contact-row header">
                    <div class="col-sm-2"><strong>Acciones</strong></div>
                    <div class="col-sm-3"><strong>Nombre completo</strong></div>
                    <div class="col-sm-7"><strong>Email</strong></div>
                </div>
                <div class="row email-contact-row" ng-class-odd="'odd'" ng-repeat="contact in emailContactsCtrl.emailContacts">
                    <div class="col-sm-2">
                        <a role="button"
                           ng-click="emailContactsCtrl.setEmailContact(contact)"
                           uib-tooltip="{{'EMAIL_CONTACTS_SELECTED' | translate}}"
                           tooltip-placement="right-top"
                           tooltip-append-to-body="true">
                            <i class="fa fa-check fa-fw"></i>
                        </a>
                        <a role="button"
                           ng-click="emailContactsCtrl.showInsertUpdateModal(contact)"
                           uib-tooltip="{{'EMAIL_CONTACTS_EDIT' | translate}}"
                           tooltip-placement="right-top"
                           tooltip-append-to-body="true">
                            <i class="fal fa-pencil-alt fa-fw"></i>
                        </a>
                        <a ng-click="emailContactsCtrl.deleteEmailContact(contact)"
                           uib-tooltip="{{'EMAIL_CONTACTS_DELETE' | translate}}"
                           tooltip-placement="right-top"
                           tooltip-append-to-body="true">
                            <i class="fa fa-trash-alt fa-fw"></i>
                        </a>
                    </div>
                    <div class="col-sm-3">
                        <div ng-bind="contact.name + ' ' + contact.lastName" class="email-contact-row-body"></div>
                    </div>
                    <div class="col-sm-7">
                        <div ng-bind-html="contact.email" class="email-contact-row-body"></div>
                    </div>
                </div>
            </div>
            <div ng-if="emailContactsCtrl.moreAvailable">
                <button type="button"
                        class="btn btn-flat btn-sm btn-refresh"
                        ng-click="emailContactsCtrl.loadMore()">
                    {{'LOAD_MORE' | translate}}
                </button>
            </div>
        </div>
    </div>
    <div ng-if="emailContactsCtrl.noEmailContacts">
        <div class="alert alert-info">
            <span class="fa fa-exclamation-circle" aria-hidden="true"></span>
            <span class="sr-only">{{'INFORMATION' | translate}}</span>
            {{'NO_EMAIL_CONTACTS' | translate }}
        </div>
    </div>
</div>
