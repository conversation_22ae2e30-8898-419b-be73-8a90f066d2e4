﻿<%@ Page Async="true" Language="C#" MasterPageFile="~/Master.Master" AutoEventWireup="true" CodeBehind="Queues.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.Queues" MaintainScrollPositionOnPostback="true" ValidateRequest="false" %>

<%@ Register TagPrefix="cc1" Namespace="Yoizen.Web.UI" Assembly="Yoizen.Web.UI" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/datatables.min.css")%>' rel="stylesheet" type="text/css" />
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/UserSelection.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/datatables.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/QueueInfo.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/Queues.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.timetable.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/dragula.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
    <style type="text/css">
        span.title {
            cursor: pointer;
            color: #666;
            font-weight: bold;
            vertical-align: middle;
        }

        .uiInfoTable .label {
            width: 150px !important;
        }
    </style>
</asp:Content>
<asp:Content ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
    <span class="fa fa-inbox"></span> 
    <span data-i18n="configuration-queues-title">Colas</span>
</asp:Content>
<asp:Content ContentPlaceHolderID="contentplaceholderRightPanel" runat="server">
	<div id="divFilters" class="seccion">
		<div class="title">
			<h2 data-i18n="configuration-services-filters-title">Filtros</h2>
		</div>
		<div class="contents">
			<div class="filter-table" id="tableFilters">
				<div class="row">
					<div class="cell">
						<div class="group" id="tdTextFilterPlaceholder">
							<div class="group-title"><span data-i18n="configuration-queues-name_or_key">Nombre</span>:</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="cell">
						<div class="group">
							<div class="group-title"><span data-i18n="configuration-queues-state">Estado</span>:</div>
							<select id="selectEnabled">
								<option value="-1" data-i18n="configuration-queues-all">Todos</option>
								<option value="1" selected="selected" data-i18n="globals-enabled">Habilitados</option>
								<option value="0" data-i18n="globals-disabled">No habilitados</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="cell">
						<div class="group">
							<div class="group-title"><span data-i18n="configuration-queues-queues-header-with_service_level">Con Service Level</span>:</div>
							<select id="selectWithSL">
								<option value="-1" selected="selected" data-i18n="globals-any">Indistinto</option>
								<option value="1" data-i18n="globals-yes">Si</option>
								<option value="0" data-i18n="globals-no">No</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="cell">
						<div class="group">
							<div class="group-title"><span data-i18n="configuration-queues-queues-header-with_service_level">Con Service Level</span>:</div>
							<select id="selectWithAutomaticActions">
								<option value="-1" selected="selected" data-i18n="globals-any">Indistinto</option>
								<option value="1" data-i18n="globals-yes">Si</option>
								<option value="0" data-i18n="globals-no">No</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row dependsOnYSurveys">
					<div class="cell">
						<div class="group">
							<div class="group-title"><span data-i18n="configuration-queues-queues-header-with_survey">Con Encuesta</span>:</div>
							<select id="selectWithSurveys">
								<option value="-1" selected="selected" data-i18n="globals-any">Indistinto</option>
								<option value="1" data-i18n="globals-yes">Si</option>
								<option value="0" data-i18n="globals-no">No</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="cell">
						<div class="group">
							<div class="group-title"><span data-i18n="globals-show_id">Mostrar código</span>:</div>
							<input type="checkbox" id="checkboxFilterShowID" />
						</div>
					</div>
				</div>
			</div>
			<div class="buttons">
				<label class="uiButton uiButtonLarge uiButtonConfirm">
					<button type="button" data-i18n="globals-close" onclick="ShowRightPanel(false)">Cerrar</button>
				</label>
			</div>
		</div>
	</div>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderContenido" runat="Server">
	<yoizen:Message ID="messageError" runat="server" Type="Error" Visible="false" />
	<yoizen:Message ID="messageNoAgents" runat="server" Type="Warning" Visible="false">
		<span data-i18n="configuration-queues-no_agents-warning">No existen agentes dados de alta. Para darlos de alta ingrese</span> <asp:HyperLink runat="server" NavigateUrl="~/Configuration/Agents.aspx?Startup" Text="aquí" data-i18n="globals-here" />.
	</yoizen:Message>
	<yoizen:Message ID="messageNoSupervisors" runat="server" Type="Warning" Visible="false">
		<span data-i18n="configuration-queues-no_supervisors-warning">No existen supervisores dados de alta. Para darlos de alta ingrese</span> <asp:HyperLink runat="server" NavigateUrl="~/Administration/Users.aspx?Startup" Text="aquí" data-i18n="globals-here" />.
	</yoizen:Message>
	<yoizen:Message ID="messageNoTags" runat="server" Type="Warning" Visible="false">
		<span data-i18n="configuration-queues-no_tags-warning">No existen etiquetas dadas de alta. Para darlas de alta ingrese</span> <asp:HyperLink runat="server" NavigateUrl="~/Configuration/Tags.aspx?Startup" Text="aquí" data-i18n="globals-here" />.
	</yoizen:Message>
	<asp:Panel ID="panelContent" runat="server">
		<asp:Panel ID="panelListado" runat="server">
			<div style="display: none">
				<div id="divCopy" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-copy-title">Copiar Cola</h2>
					</div>
					<div class="contents">
						<asp:HiddenField ID="hiddenQueueIDToCopy" runat="server" ClientIDMode="Static" />

						<table class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-copy-service_level">Copiar Nivel de servicio</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxCopyServiceLevel" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-queues-copy-users">Copiar usuarios</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxCopyUsers" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-queues-copy-tags">Copiar etiquetas</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxCopyTags" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderCopySurveys" runat="server">
								<tr class="dataRow dataRowSeparator">
									<th class="label"><span data-i18n="configuration-queues-copy-surveys">Copiar encuestas</span>:</th>
									<td class="data">
										<asp:CheckBox ID="checkboxCopySurveys" runat="server" ClientIDMode="Static" />
									</td>
								</tr>
							</asp:PlaceHolder>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-queues-copy-ewt">Copiar EWT</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxCopyEWT" runat="server" ClientIDMode="Static" />
								</td>
							</tr>

							<tr class="dataRow dataRowSeparator">
								<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-copy-service_level">Copiar Nivel de servicio</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxCopyAutomaticActions" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
						</table>
						<div class="validationerror" style="display: none" id="divCopyError">
							<span data-i18n="configuration-queues-copy-error">Debe seleccionar por lo menos un criterio</span>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-accept" onclick="AcceptCopy()" id="buttonCopyAccept">Aceptar</button>
							</label>
							<label class="uiButton uiButtonLarge">
								<button type="button" value="Cancelar" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divDeleteQueue" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-delete_queue-title">Borrar cola</h2>
					</div>
					<div class="contents">
						<div id="divCanBeDeletedLoading" class="subseccion" style="display: none">
							<div class="title">
								<h2 data-i18n="globals-loading">Cargando...</h2>
							</div>
							<div class="contents">
								<div style="text-align: center">
									<i class="fa fa-3x fa-spinner fa-pulse"></i>
								</div>
							</div>
						</div>
						<yoizen:Message ID="messageCouldntCheckQueueCanBeDeleted" runat="server" ClientIDMode="Static" Type="Error" Style="display: none" LocalizationKey="configuration-queues-couldnt_check_queue_can_be_deleted">No se pudo verificar si la cola puede ser eliminada.</yoizen:Message>
						<yoizen:Message ID="messageQueueCannotBeDeleted" runat="server" ClientIDMode="Static" Type="Error" Style="display: none" LocalizationKey="configuration-queues-queue_cannot_be_deleted">La cola no puede ser ni eliminada ni deshabilitada ya que se encuentra activa.</yoizen:Message>
						<yoizen:Message ID="messageQueueCanBeDisabled" runat="server" ClientIDMode="Static" Type="Question" Style="display: none" LocalizationKey="configuration-queues-queue_cannot_be_deleted_related">La cola no puede ser eliminada ya que posee servicios, colas o grupo de colas asociadas</yoizen:Message>
						<yoizen:Message ID="messageQueueCanBeDeleted" runat="server" ClientIDMode="Static" Type="Question" Style="display: none" LocalizationKey="configuration-queues-queue_can_be_deleted">Usted puede optar por borrar la cola definitivamente o bien deshabilitarlo. ¿Qué opción desea realizar?</yoizen:Message>
						<yoizen:Message ID="messageQueueCanBeDeletedAndIsDisabled" runat="server" ClientIDMode="Static" Type="Question" Style="display: none" LocalizationKey="configuration-queues-can_be_deleted_and_is_disabled">¿Está seguro que desea eliminar la cola?</yoizen:Message>
						<div class="buttons">
							<label class="uiButton uiButtonLarge" style="display: none">
								<button type="button" data-i18n="globals-delete" id="buttonDeleteQueueConfirm">Eliminar</button>
							</label>
							<label class="uiButton uiButtonLarge" style="display: none">
								<button type="button" data-i18n="globals-disable" id="buttonDisableQueueConfirm">Deshabilitar</button>
							</label>
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divTasks" class="seccion">
					<div class="title">
						<h2>Tareas masivas</h2>
					</div>
					<div class="contents">
						<div id="tabsTasks">
							<ul>
								<li>
									<a href="#divTaskReturnToQueue" data-i18n="configuration-queues-tasks-return_to_queue">Devolver a cola</a>
								</li>
							</ul>
							<div id="divTaskReturnToQueue">
								<yoizen:Message ID="messageTaskQueuessThatCanTransferToQueueEmpty" runat="server" Type="Information" LocalizationKey="configuration-queues-tasks-return_to_queue-no_queues" ClientIDMode="Static">
									No hay colas que puedan transferir a la cola actual
								</yoizen:Message>
								<div id="divTaskQueuessThatCanTransferToQueueNoEmpty">
									<table width="100%" border="0" class="uiInfoTable noBorder">
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-queues-tasks-return_to_queue-queues">Colas que pueden transferir</span>:</th>
											<td class="data">
												<select id="selectTasksQueuesThatCanTransferToQueue" multiple="multiple"/>
											</td>
										</tr>
									</table>
								</div>
							</div>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-accept" onclick="AcceptQueueTasks()" id="buttonTasksAccept">Aceptar</button>
							</label>
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divRelatedServices" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-queues-header-related_services">Servicios relacionados</h2>
					</div>
					<div class="contents">
						<div class="scrollable-y">
							<ul id="ulRelatedServices" class="multiple"></ul>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divRelatedQueues" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-queues-header-related_queues">Colas relacionadas</h2>
					</div>
					<div class="contents">
						<div class="scrollable-y">
							<ul id="ulRelatedQueues" class="multiple"></ul>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divRelatedQueueGroups" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-queues-header-related_queuegroups">Grupos de colas relacionados</h2>
					</div>
					<div class="contents">
						<div class="scrollable-y">
							<ul id="ulRelatedQueueGroups" class="multiple"></ul>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
							</label>
						</div>
					</div>
				</div>
				<div id="divRelatedAgents" class="seccion">
					<div class="title">
						<h2 data-i18n="configuration-queues-queues-header-agents">Agentes</h2>
					</div>
					<div class="contents">
						<div class="scrollable-y">
							<ul id="ulRelatedAgents" class="multiple"></ul>
						</div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
							</label>
						</div>
					</div>
				</div>
			</div>
			<div class="seccion">
				<div class="title">
					<h2>
						<span data-i18n="configuration-queues-queue_list-title">Listado de colas</span>
						<a href="javascript:ShowRightPanel(true)" id="anchorFilter"><span class="fa fa-lg fa-filter" data-i18n-title="globals-show_filter_options"></span></a>
					</h2>
				</div>
				<div class="contents">
					<div>
						<asp:HiddenField ID="hiddenActionQueueID" runat="server" ClientIDMode="Static" />
						<asp:HiddenField ID="hiddenActionName" runat="server" ClientIDMode="Static" />
						<asp:Button ID="buttonAction" runat="server" Text="Realizar acción" CausesValidation="false" OnClick="buttonAction_Click" style="display: none" ClientIDMode="Static" />
						<div id="divLoadingQueues" class="subseccion">
							<div class="title">
								<h2 data-i18n="globals-loading">Cargando...</h2>
							</div>
							<div class="contents">
								<div style="text-align: center">
									<i class="fa fa-3x fa-spinner fa-pulse"></i>
								</div>
							</div>
						</div>
						<table id="tableQueues" class="reporte" cellspacing="0" rules="all" border="1" style="width:100%;border-collapse:collapse;display:none">
							<thead>
								<tr class="header">
									<th scope="col" rel="id" data-i18n="globals-id" style="width: 80px">Código</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-name">Nombre</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-description" style="width: 200px">Descripción</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-key" style="width: 80px">Clave</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-agents" style="width: 80px">Agentes</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-related_services" style="width: 80px">Servicios Relacionados</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-related_queues" style="width: 80px">Colas Relacionadas</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-related_queuegroups" style="width: 80px">Grupos de Colas Relacionados</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-with_service_level" style="width: 50px">Con Service Level</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-with_automatic_actions" style="width: 50px">Con Aciones Automaticas</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-with_connected_agents" style="width: 50px">Agentes conectados</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-with_messages" style="width: 50px">Mensajes en cola</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-with_survey" style="width: 50px">Con encuesta</th>
									<th scope="col" data-i18n="configuration-queues-queues-header-enabled" style="width: 50px">Habilitado</th>
									<th scope="col" style="width: 100px">&nbsp;</th>
								</tr>
							</thead>
							<tbody></tbody>
						</table>
						<yoizen:Message ID="messageNoQueues" runat="server" Type="Information" style="display: none" LocalizationKey="configuration-queues-no_queues_created" ClientIDMode="Static">
							No existen colas dadas de alta
						</yoizen:Message>
					</div>
					<div class="buttons" id="panelButtons">
						<label class="uiButton uiButtonLarge uiButtonConfirm" runat="server">
							<asp:Button ID="buttonNew" runat="server" Text="Nuevo" OnClick="buttonNew_Click" data-i18n="globals-new" OnClientClick="course(); return false;"/>
						</label>
						<label class="uiButton uiButtonLarge">
							<button type="button" value="Exportar" data-i18n="globals-export" onclick="Export()">Exportar</button>
							<asp:Button ID="buttonExport" runat="server" Text="Exportar" CausesValidation="false" OnClick="buttonExport_Click" Style="display: none" ClientIDMode="Static" />
						</label>
					</div>
				</div>
			</div>
		</asp:Panel>
		<asp:Panel ID="panelEdition" runat="server" Visible="false">
			<asp:HiddenField ID="hiddenSurveyList" runat="server" ClientIDMode="Static" />
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-queues-queue_data-title">Datos de la cola</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="globals-name">Nombre</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxName" runat="server" MaxLength="100" Width="90%" ClientIDMode="Static" autotrim="true" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxName" />
								<asp:CustomValidator ID="customvalidatorName" runat="server" OnServerValidate="customvalidatorName_OnServerValidate" EnableClientScript="true" ClientValidationFunction="ValidateName" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="globals-description">Descripción</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxDescription" runat="server" MaxLength="255" Width="90%" ClientIDMode="Static" autotrim="true" TextMode="MultiLine" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-queues-queues-header-key">Clave</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxKey" runat="server" MaxLength="100" Width="90%" ClientIDMode="Static" autotrim="true" />
								<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxKey" ValidationExpression="^[a-zA-Z0-9]{1,20}$" />
								<asp:CustomValidator ID="customvalidatorKey" runat="server" OnServerValidate="customvalidatorKey_OnServerValidate" EnableClientScript="true" ClientValidationFunction="ValidateKey" />
								<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-queues-queues-header-key-tip">
									La clave de las colas permite especificar un código que puede ser utilizado luego por otras configuraciones.
									El valor no podrá repetirse entre las colas que están habilitadas.
									Ingrese únicamente números y letras, sin espacios, con un máximo de 20 caracteres.
									El valor es opcional
								</yoizen:Message>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div id="tabsEditQueues">
				<ul>
					<li id="liTabBehaviour" runat="server" visible="false">
						<a href="#divQueueBehaviour" data-i18n="configuration-queues-general">General</a>
					</li>
					<li id="liTabServiceLevel" runat="server" visible="false">
						<a href="#divQueueServiceLevel" data-i18n="configuration-queues-service_level">Nivel de Servicio (SL)</a>
					</li>
					<li id="liTabUsers" runat="server" visible="false">
						<a href="#divQueueUsers" data-i18n="configuration-queues-user">Usuarios</a>
					</li>
					<li id="liTabTags" runat="server" visible="false">
						<a href="#divQueueTags" data-i18n="configuration-queues-tags">Etiquetas</a>
					</li>
					<li id="liTabSurveys" runat="server" visible="false">
						<a href="#divQueueSurveys" data-i18n="configuration-queues-surveys">Encuestas</a>
					</li>
					<li id="liTabEwt" runat="server" visible="false">
						<a href="#divQueueEwt" data-i18n="configuration-queues-ewt">Ewt</a>
					</li>
					<li id="liTabAutomaticActions" runat="server" visible="false">
						<a href="#divQueueAutomaticActions" data-i18n="configuration-queues-automatic_actions">Acciones Automaticas</a>
					</li>
					<li id="liTabVideo" runat="server" visible="false">
						<a href="#divQueueVideo" data-i18n="configuration-queues-video">Video</a>
					</li>
				</ul>
				<div id="divQueueBehaviour" runat="server" clientidmode="static">
					<div class="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-message_reservation-title">Reservas de mensajes</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-time_to_reserve_agent">Minutos para reservar al mismo Agente</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxMinutesToWaitForAgent" runat="server" MaxLength="4" Width="50" autocomplete="off" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMinutesToWaitForAgent" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxMinutesToWaitForAgent" Type="Integer" MinimumValue="0" MaximumValue="9999" />
														<div class="timeinfo" related="textboxMinutesToWaitForAgent"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-time_to_reserve_agent-tip">
														Cuando un mensaje pertenece a un caso, el sistema lo reserva al último agente que participó del caso siempre y cuando el agente esté 
														conectado. Esta reserva se libera en caso de que el agente no esté disponible de acuerdo al tiempo establecido en este campo. 
														A partir de ese momento el mensaje queda disponible para ser gestionado por cualquier agente de la Cola. 
														El valor tiene que estar entre 0 y 9999. Especifique cero para indicar que la cola no utilizará la función
														de reservar mensajes
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-max_reserve">Cantidad máxima de mensajes reservados por Agente</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">

													 <asp:TextBox ID="textboxMaxReservedMessagesPerAgent" runat="server" MaxLength="4" Width="50" autocomplete="off" />
													 <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaxReservedMessagesPerAgent" />
													 <asp:RangeValidator runat="server" ControlToValidate="textboxMaxReservedMessagesPerAgent" Type="Integer" MinimumValue="0" MaximumValue="9999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-max_reserve_conditions">
														Establece si habrá una cantidad máxima de mensajes reservados por agente. El valor tiene que estar entre 0 y 9999. 
														Especifique cero para indicar que la cola no tendrá cantidad máxima de mensajes reservados.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label"><span data-i18n="configuration-queues-reserve_conditions">Condiciones para Reservar</span>:</th>
									<td class="data">
										<asp:DropDownList ID="dropdownlistReserveConditions" runat="server">
											<asp:ListItem Value="1" Selected="True" Text="Siempre" data-i18n="configuration-queues-condition1" />
											<asp:ListItem Value="2" Text="Reservar únicamente si el mensaje pertenece a un caso abierto" data-i18n="configuration-queues-condition2" />
										</asp:DropDownList>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label"><span data-i18n="configuration-queues-dont_reserve_with_status">Estados en los cuales los mensajes no serán reservados</span>:</th>
									<td class="data">
										<asp:ListBox ID="listboxDontReserveWithStatus" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="300" />
										<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSelectedQueues" />
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-dont_reserve_release_with_status">Liberar mensajes reservados con estado auxiliar sin reserva</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:CheckBox ID="checkboxDeleteReservedWithStatus" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-dont_reserve_release_with_status_conditions">
														Este parámetro especifica si los mensajes reservados de un agente se liberarán cuando este se pase a cualquier 
														estado auxiliar que no permita reservas.

													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-renew_reserve_time_when_grouping">Renovar reserva al agrupar</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:CheckBox ID="checkboxRenewReserveTimeWhenGrouping" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-renew_reserve_time_when_grouping_conditions">
														Este parámetro especifica si el tiempo de reserva será renovado cada vez que se agrupe un mensaje nuevo.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
							<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-queues-reserve_conditions-tip">
								En caso de utilizar la funcionalidad de reservas de mensaje se podrá optar bajo qué condiciones se reservará los mensajes. Cuando
								seleccione la opción <span class="italic">Siempre</span> el sistema reservará el mensaje al último agente sin evaluar ninguna condición.
								En caso de seleccionar la opción <span class="italic">Reservar únicamente si el mensaje pertenece a un caso abierto o que pueda ser reabierto</span> 
								el sistema reservará el mensaje al último agente siempre y cuando el caso al que pertenezca el mensaje se encuentre abierto o también cuando
								esté cerrado pero con la condición de poder ser reabierto.
							</yoizen:Message>
						</div>
					</div>
					<asp:Panel ID="panelReturnMessagesToQueue" runat="server" CssClass="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-return_messages_to_queue-title">Devolver mensajes a cola</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-allow_to_return_messages">Permitir retornar mensajes</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAllowAgentsToReturnMessagesToQueue" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-allow_to_return_messages-tip">
														Este parámetro especifica si los mensajes pertenecientes a esta cola podrán ser devueltos por los agentes.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-queues-minutes_to_not_assign_to_previous_agent">Minutos para reasignar mensaje devuelto a la cola</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo" style="width: 100px !important">
														<asp:TextBox ID="textboxMinutesNotAssignToPreviousAgent" runat="server" MaxLength="3" Width="50" autocomplete="off" />
														<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMinutesNotAssignToPreviousAgent" />
														<div class="timeinfo" related="textboxMinutesNotAssignToPreviousAgent"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-minutes_to_not_assign_to_previous_agent-tip">
														Puede especificar que un mensaje que fue retonado o transferido a una cola no sea asignado al mismo agente. Este parámetro especifica la máxima cantidad de minutos que puede pasar para que un mensaje no sea asignado nuevamente al agente. Vencido este plazo se volverá a asignar el mensaje al agente, si es que no hay otros agentes disponibles. 
														Puede indicar 0 para establecer que no se debe aplicar esta regla. 
														El valor debe estar entre 0 y 120
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
						</div>
					</asp:Panel>
					<asp:Panel ID="panelAllowAgentsToReturnMessagesToSpecifiedQueue" runat="server" CssClass="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-transfer_messages_to_queue-title">Transferir mensajes a otra cola</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-allow_transfers">Permitir transferencias</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAllowAgentsToSelectQueueOnReturnToQueue" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-allow_transfers-tip">
														Este parámetro especifica si los agentes podrán seleccionar la cola de destino para transferir mensajes. 
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label"><span data-i18n="configuration-queues-transference_queues">Colas para transferir</span>:</th>
									<td class="data">
										<asp:ListBox ID="listboxQueues" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="300" />
										<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSelectedQueues" />
									</td>
								</tr>
							</table>
						</div>
					</asp:Panel>
					<asp:PlaceHolder ID="placeholderQueueWorkingHoursForReceivingMessages" runat="server">
						<div class="seccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-queues-connected_agents_for_receiving_messages-title">Agentes conectados para recibir retornos o transferencias</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-queues-connected_agents_for_receiving_messages-restriction">¿Agentes conectados a la cola?</span>:</th>
										<td class="data withdescription">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs checkbox">
															<asp:CheckBox ID="checkboxQueueConnectedAgentsForReceivingMessages" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-queues-connected_agents_for_receiving_messages-restriction-tip">
															Si se establece esta opción, la cola permitirá recibir mensajes en retornos o transferencias únicamente cuando
															hayan agentes conectados
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
								<div id="divQueueConnectedAgentsForReceivingMessagesExceptions" class="subseccion">
									<div class="title">
										<h2 data-i18n="configuration-queues-connected_agents_for_receiving_messages-exceptions-title">Excepciones</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-queues-connected_agents_for_receiving_messages-exceptions-supervisors">Supervisores</span>:</th>
												<td class="data withdescription">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs checkbox">
																	<asp:CheckBox ID="checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSupervisors" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-queues-connected_agents_for_receiving_messages-exceptions-supervisors-tip">
																	Si se establece esta opción, los supervisores podrán realizar las transferencias aún cuando no hayan
																	agentes conectados
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-queues-connected_agents_for_receiving_messages-exceptions-service_level">Nivel de Servicio</span>:</th>
												<td class="data withdescription">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs checkbox">
																	<asp:CheckBox ID="checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSL" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-queues-connected_agents_for_receiving_messages-exceptions-service_level-tip">
																	Si se establece esta opción, las acciones de Service Level de mover de cola podrán ser ejecutadas aún cuando 
																	no hayan agentes conectados
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="seccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-queues-working_hours_for_receiving_messages-title">Horarios permitidos para recibir retornos o transferencias</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Warning" rel="time-zone-warning" style="display: none" data-ignoreconfigured="true" LocalizationKey="[html]configuration-serviceschat-day_and_time_configuration-tip">
									La configuración se realizará utilizando el huso horario al del servidor. 
									<div rel="server">El servidor está configurado con <span rel="tz"></span></div>
									<div rel="local">Usted se encuentra en <span rel="tz"></span></div>
								</yoizen:Message>
								<asp:HiddenField ID="hiddenQueueWorkingHoursForReceivingMessages" runat="server" ClientIDMode="Static"></asp:HiddenField>
								<div id="divQueueWorkingHoursForReceivingMessagesContainer"></div>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateQueueWorkingHoursForReceivingMessages" ErrorMessage="Debe seleccionar al menos un día" SkinID="validationerror" data-i18n="configuration-serviceschat-must_select_date-error" />
								</div>
								<div class="subseccion">
									<div class="title">
										<h2 data-i18n="configuration-queues-working_hours_for_receiving_messages-exceptions-title">Excepciones</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-queues-working_hours_for_receiving_messages-exceptions-supervisors">Supervisores</span>:</th>
												<td class="data withdescription">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs checkbox">
																	<asp:CheckBox ID="checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-queues-working_hours_for_receiving_messages-exceptions-supervisors-tip">
																	Si se establece esta opción, los supervisores podrán realizar las transferencias aún cuando se encuentra fuera de horario
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-queues-working_hours_for_receiving_messages-exceptions-service_level">Nivel de Servicio</span>:</th>
												<td class="data withdescription">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs checkbox">
																	<asp:CheckBox ID="checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-queues-working_hours_for_receiving_messages-exceptions-service_level-tip">
																	Si se establece esta opción, las acciones de Service Level de mover de cola podrán ser ejecutadas aún cuando se
																	encuentra fuera de horario
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>
						</div>
					</asp:PlaceHolder>
					<div class="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-mail_signature-title">Firma para servicios de mail</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-servicesmail-signature">Firma</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs checkbox">
														<asp:DropDownList ID="dropdownlistMailSignatureBehaviour" runat="server" ClientIDMode="Static">
															<asp:ListItem Value="0" Text="No utilizar firma" Selected="True" data-i18n="configuration-servicesmail-signature_behaviour-0" />
															<asp:ListItem Value="1" Text="Incluir firma en forma automática en el envío de mails" data-i18n="configuration-servicesmail-signature_behaviour-1" />
															<asp:ListItem Value="2" Text="Incluir firma al momento de redactar el mail permitiendo editarla" data-i18n="configuration-servicesmail-signature_behaviour-2" />
															<asp:ListItem Value="3" Text="Incluir firma al momento de redactar el mail sin posibilidad de editarla" data-i18n="configuration-servicesmail-signature_behaviour-3" />
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="[html]configuration-servicesmail-signature-tip">
														Este parámetro indica si se agregará una firma que se mostrará al responder un email desde <span class="productname"></span>
													</td>
												</tr>
											</tbody>
										</table>
										<div class="subseccion" id="divMailSignature">
											<div class="title">
												<h2 data-i18n="configuration-servicesmail-signature">Firma</h2>
											</div>
											<div class="contents">
												<asp:TextBox ID="textboxMailSignature" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
												<yoizen:Message ID="messageMailSignature" ClientIDMode="Static" runat="server" Type="Information" Small="true" style="margin-top: 10px;">
													<span data-i18n="configuration-servicesmail-mail_signature_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
													<span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-mail_signature-field-user_name">Indica el usuario que está respondiendo el mail</span>
												</yoizen:Message>
											</div>
										</div>
									</td>
								</tr>
							</table>
							<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-queues-mail_warning-tip">
								Esta firma solo aplica para el servicio de mail.
							</yoizen:Message>
						</div>
					</div>
                    <div class="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-chat_mode_idle">Configuración de inactividad en conversaciones</h2>
						</div>
						<div class="contents">
							<table class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
								<tr class="dataRow dataRowSeparator">
									<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-chat_mode_idle_agent">Inactividad en respuesta del agente</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxAgentIdleMinutes" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentIdleMinutes" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxAgentIdleMinutes" Type="Integer" MinimumValue="0" MaximumValue="60" />
														<div class="timeinfo" related="textboxAgentIdleMinutes"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-chat_mode_idle_agent_tip">
														Indica el límite de tiempo en el que la conversación debe obtener una respuesta por parte del agente expresado en minutos. 
														En caso de que el agente demore en responder más del tiempo especificado, el mensaje se marcará como inactivo. 
														El valor tiene que estar entre 0 y 60. Especifique cero para indicar que la cola no utilizará la función.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" id="trAgentIdleReturnToQueue" style="display: none">
									<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-chat_mode_idle_agent_return_to_queue">Desasignar y encolar mensaje ante inactividad en respuesta del agente</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:CheckBox ID="checkboxAgentIdleReturnToQueue" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-chat_mode_idle_agent_return_to_queue-tip">
														Si se selecciona esta opción, el mensaje será desasignado y encolado nuevamente en la plataforma de manera automática
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-chat_mode_idle_finalUser">Inactividad en respuesta del usuario</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxFinalUserIdleMinutes" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFinalUserIdleMinutes" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxFinalUserIdleMinutes" Type="Integer" MinimumValue="0" MaximumValue="60" />
														<div class="timeinfo" related="textboxFinalUserIdleMinutes"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-chat_mode_idle_finalUser_tip">
														Indica el límite de tiempo en el que la conversación debe obtener una respuesta por parte del cliente expresado en minutos. 
														En caso de que el cliente demore más del tiempo especificado, el mensaje será descartado de manera automática en la plataforma. 
														El valor tiene que estar entre 0 y 60. Especifique cero para indicar que la cola no utilizará la función.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
							<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="configuration-queues-chat_mode_idle_tip">
								Esta funcionalidad sólo aplica para los canales que posean y tengan activada la funcionalidad de "Modo chat". 
								Actualmente los servicios que lo permiten son: WhatsApp, Facebook Messenger y Telegram.
							</yoizen:Message>
						</div>
                    </div>
				</div>
				<div id="divQueueServiceLevel" runat="server" clientidmode="static">
					<asp:Panel ID="panelQueueServiceLevel" runat="server" CssClass="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-service_level-title">Nivel de Servicio (SL)</h2>
						</div>
						<div class="contents">
							<table class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
								<tr class="dataRow dataRowSeparator">
									<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-service_level_seconds">Objetivo Nivel de Servicio (segundos)</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxQueueServiceLevelSeconds" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxQueueServiceLevelSeconds" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxQueueServiceLevelSeconds" Type="Integer" MinimumValue="0" MaximumValue="604800" />
														<div class="secondinfo" related="textboxQueueServiceLevelSeconds"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-service_level_seconds-tip">
														Indica el objetivo de tiempo en que el cliente debe ser atendido expresado en segundos.
														El valor tiene que estar entre 0 y 604800. Especifique cero para indicar que la cola no utilizará la función
														de analizar el nivel de servicio de los mensajes
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
							<div id="panelServiceLevelMinutesActions" runat="server" class="subseccion" style="display: none" clientidmode="Static">
								<div class="title">
									<h2 data-i18n="configuration-queues-minutes_to_action-title">Realizar acción al superar el umbral de servicio</h2>
								</div>
								<div class="contents">
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsAssignToQueue" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-move_to_queue-title">Mover a cola</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message ID="messageSLMinutesActionsAssignToQueueDisabled" runat="server" Visible="false" Type="Warning" LocalizationKey="configuration-queues-selected_queue_disabled">
												La cola seleccionada previamente se encuentra deshabilitada. Seleccione otra cola
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow">
													<th class="label withdescription"><span data-i18n="configuration-queues-queue">Cola</span>:</th>
													<td class="data">
														<table class="uiGrid" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="vMid prs" style="width: 40%">
																		<asp:ListBox ID="dropdownlistSLMinutesActionsAssignToQueue" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" Width="300" />
																	</td>
																	<td class="vMid pls" data-i18n="configuration-queues-selected_queue_disabled-tip">
																		Esta será la cola a la cual se asignará el mensaje cuando sea impuntual
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesActionsAssignToQueue" ErrorMessage="Debe seleccionar al menos una cola" SkinID="validationerror" data-i18n="configuration-queues-must_select_queue-error" />
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox" style="display: none">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsNotify" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-notify_supervisor-title">Notificar Supervisor</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-nothing_to_configure">
												No hay que configurar nada
											</yoizen:Message>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsDiscard" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-discard-title">Descartar</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-discard-no_chat">
												Los descartes aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-close_case">Cerrar el caso</span>:</th>
													<td class="data">
														<asp:CheckBox ID="checkboxSLMinutesActionsDiscardAndCloseCase" runat="server" />
													</td>
												</tr>
											</table>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsAutoReply" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-automatic_response-title">Responder Automáticamente</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-automatic_response-no_chat">
												Las respuestas automáticas aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta</span>:</th>
													<td class="data">
														<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
															<asp:TextBox ID="textboxSLMinutesActionsAutoReply" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
															<span id="spanSLMinutesAutoReplyCounter" clientidmode="Static" runat="server"></span>
														</div>
														<yoizen:Message ID="messageSLMinutesAutoReply" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic_response-tip">
															Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
															Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
															puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
														</yoizen:Message>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-automatic_response-reply_if_already_for_case">Responder aún si ya hubo respuesta de SL en el caso</span>:</th>
													<td class="data">
														<asp:CheckBox ID="checkboxSLMinutesActionsAutoReplyIfAlreadyForCase" runat="server" />
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesActionsAutoReply" SkinID="validationerror" data-i18n="[html]configuration-queues-automatic_response-error">
													Debe incluir el parámetro @@USUARIO@@ en la respuesta<span class="dependsOnTwitter"> y la longitud no debe superar los 240 caracteres</span>
												</asp:CustomValidator>
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLChatNotify" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-chat_notify-title">Notificar al canal de chat</span> <span class="fa fa-lg fa-comments" style="margin-left: 3px"></span></h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-chat_notify-text">Mensaje a notificar</span>:</th>
													<td class="data">
														<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
															<asp:TextBox ID="textboxSLChatNotifyText" runat="server" ClientIDMode="Static" TextMode="MultiLine" Rows="2" style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
														</div>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-chat_notify-finish">Finalizar el chat</span>:</th>
													<td class="data">
														<asp:DropDownList ID="dropdownlistSLChatNotifyAndFinish" runat="server">
															<asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
															<asp:ListItem Value="1" data-i18n="configuration-queues-chat_notify-yes_only_not_attended">Únicamente si no fue atendido</asp:ListItem>
															<asp:ListItem Value="2" data-i18n="globals-yes">No</asp:ListItem>
														</asp:DropDownList>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLChatNotify" SkinID="validationerror" data-i18n="configuration-queues-chat_notify-error">
													Debe ingresar el mensaje a notificar al chat
												</asp:CustomValidator>
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsAddTags" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-add_tags-title">Aplicar etiquetas</span></h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow">
													<th class="label withdescription"><span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
													<td class="data">
														<table class="uiGrid" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="vMid prs" style="width: 40%">
																		<asp:TextBox ID="textboxSLMinutesActionsTags" runat="server" ClientIDMode="Static" Width="100%" />
																		<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;" LocalizationKey="configuration-queues-select_tags">
																			Seleccione las etiquetas que se asignarán a los mensajes. Deberá seleccionar al menos una etiqueta
																		</yoizen:Message>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesActionsTags" ErrorMessage="Debe seleccionar al menos una etiqueta" SkinID="validationerror" data-i18n="configuration-queues-must_select_tag-error" />
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxChatAvailableDaysAndTimes" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-serviceschat-available_days_and_times">Habilitar Días y Horarios</span>
											</h2>
										</div>
										<div class="contents">
											<div class="subseccion" id="divChatAvailableDaysAndTimes">
												<div class="title">
													<h2 data-i18n="configuration-serviceschat-day_and_time_configuration-title">Configuración de días y horarios</h2>
												</div>
												<div class="contents">
													<yoizen:Message ID="messageTimeZoneWarning" runat="server" ClientIDMode="Static" Type="Warning" style="display: none" data-ignoreconfigured="true" LocalizationKey="[html]configuration-serviceschat-day_and_time_configuration-tip">
														La configuración se realizará utilizando el huso horario al del servidor. 
														<div rel="server">El servidor está configurado con <span rel="tz"></span></div>
														<div rel="local">Usted se encuentra en <span rel="tz"></span></div>
													</yoizen:Message>
													<asp:HiddenField ID="hiddenChatAvailableDaysAndTimes" runat="server" ClientIDMode="Static"></asp:HiddenField>
													<div id="divChatAvailableDaysAndTimesContainer"></div>
													<div class="validationerror" style="display: none">
														<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateChatDaysAndTimes" ErrorMessage="Debe seleccionar al menos un día" SkinID="validationerror" data-i18n="configuration-serviceschat-must_select_date-error" />
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesActionsVIM" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-vim-title">Marcar mensanje como VIM</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-nothing_to_configure">
												No hay que configurar nada
											</yoizen:Message>
										</div>
									</div>
									<div class="validationerror" style="display: none">
										<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceLevelAtLeastOneAction" ErrorMessage="Debe seleccionar al menos una acción a realizar" SkinID="validationerror" data-i18n="configuration-queues-must_select_action-error" />
									</div>
								</div>
							</div>
						</div>
					</asp:Panel>
					<asp:Panel ID="panelQueueSLExpired" runat="server" CssClass="seccion collapsable">
						<div class="title">
							<h2 data-i18n="configuration-queues-queue_message_expiration-title">Vencimiento de Mensajes en Cola</h2>
						</div>
						<div class="contents">
							<table class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
								<tr class="dataRow dataRowSeparator">
									<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-minutes_to_expiration">Minutos para umbral de vencimiento</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxQueueServiceLevelExpired" runat="server" MaxLength="4" Width="50" autocomplete="off" ClientIDMode="Static" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxQueueServiceLevelExpired" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxQueueServiceLevelExpired" Type="Integer" MinimumValue="0" MaximumValue="9999" />
														<div class="timeinfo" related="textboxQueueServiceLevelExpired"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-minutes_to_expiration-tip">
														Indica el tiempo máximo expresado en minutos en que los mensajes deberían ser respondidos.
														Debe ser mayor al tiempo del Nivel de Servicio.
														El valor tiene que estar entre 0 y 9999. Especifique cero para indicar que la cola no utilizará la función
														de analizar el vencimiento de los mensajes
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
							<div class="validationerror" style="display: none">
								<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateExpired" ErrorMessage="El valor debe ser mayor que el tiempo de service level" SkinID="validationerror" data-i18n="configuration-queues-minutes_to_expiration-error" />
							</div>
							<div id="panelServiceLevelExpiredActions" runat="server" class="subseccion" style="display: none" clientidmode="Static">
								<div class="title">
									<h2 data-i18n="configuration-queues-action_when_expired-title">Realizar acción al superar el umbral de vencimiento</h2>
								</div>
								<div class="contents">
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsAssignToQueue" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-move_to_queue-title">Mover a cola</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message ID="messageSLMinutesExpiredActionsAssignToQueueDisabled" runat="server" Visible="false" Type="Warning" LocalizationKey="configuration-queues-selected_queue_disabled">
												La cola seleccionada previamente se encuentra deshabilitada. Seleccione otra cola
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow">
													<th class="label withdescription"><span data-i18n="configuration-queues-queue">Cola</span>:</th>
													<td class="data">
														<table class="uiGrid" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="vMid prs" style="width: 40%">
																		<asp:ListBox ID="dropdownlistSLMinutesExpiredActionsAssignToQueue" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" Width="300" />
																	</td>
																	<td class="vMid pls" data-i18n="configuration-queues-move_to_queue-tip">
																		Esta será la cola a la cual se asignará el mensaje cuando sea impuntual
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesExpiredActionsAssignToQueue" ErrorMessage="Debe seleccionar al menos una cola" SkinID="validationerror" data-i18n="configuration-queues-select_queue-error" />
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox" style="display: none">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsNotify" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-notify_supervisor-title">Notificar Supervisor</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-nothing_to_configure">
												No hay que configurar nada
											</yoizen:Message>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsDiscard" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-discard-title">Descartar</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-discard-no_chat">
												Los descartes aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-close_case">Cerrar el caso</span>:</th>
													<td class="data">
														<asp:CheckBox ID="checkboxSLMinutesExpiredActionsDiscardAndCloseCase" runat="server" />
													</td>
												</tr>
											</table>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsAutoReply" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-automatic_response-title">Responder Automáticamente</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-automatic_response-no_chat">
												Las respuestas automáticas aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
											</yoizen:Message>
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta</span>:</th>
													<td class="data">
														<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
															<asp:TextBox ID="textboxSLMinutesExpiredActionsAutoReply" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" runat="server" style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
															<span id="spanSLMinutesExpiredAutoReplyCounter" clientidmode="Static" runat="server"></span>
														</div>
														<yoizen:Message ID="messageSLMinutesExpiredActionsAutoReply" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic_response-tip">
															Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
															Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
															puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
														</yoizen:Message>
														<div class="validationerror" style="display: none">
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesExpiredActionsAutoReply" SkinID="validationerror" data-i18n="[html]configuration-queues-automatic_response-error">
																Debe incluir el parámetro @@USUARIO@@ en la respuesta<span class="dependsOnTwitter"> y la longitud no debe superar los 240 caracteres</span>
															</asp:CustomValidator>
														</div>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-automatic_response-reply_if_already_for_case">Responder aún si ya hubo respuesta de SL en el caso</span>:</th>
													<td class="data">
														<asp:CheckBox ID="checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase" runat="server" />
													</td>
												</tr>
											</table>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLExpiredChatNotify" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-chat_notify-title">Notificar al canal de chat</span> <span class="fa fa-lg fa-comments" style="margin-left: 3px"></span></h2>
										</div>
										<div class="contents">
											<table border="0" class="uiInfoTable noBorder" width="100%">
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-chat_notify-text">Mensaje a notificar</span>:</th>
													<td class="data">
														<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
															<asp:TextBox ID="textboxSLExpiredChatNotifyText" runat="server" ClientIDMode="Static" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" TextMode="MultiLine">
															</asp:TextBox>
														</div>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-queues-chat_notify-finish">Finalizar el chat</span>:</th>
													<td class="data">
														<asp:DropDownList ID="dropdownlistSLExpiredChatNotifyAndFinish" runat="server">
															<asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
															<asp:ListItem Value="1" data-i18n="configuration-queues-chat_notify-yes_only_not_attended">Únicamente si no fue atendido</asp:ListItem>
															<asp:ListItem Value="2" data-i18n="globals-yes">No</asp:ListItem>
														</asp:DropDownList>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" ClientValidationFunction="ValidateSLExpiredChatNotify" data-i18n="configuration-queues-chat_notify-error" EnableClientScript="true" SkinID="validationerror">
													Debe ingresar el mensaje a notificar al chat
												</asp:CustomValidator>
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsAddTags" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-add_tags-title">Aplicar etiquetas</span></h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow">
													<th class="label withdescription"><span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
													<td class="data">
														<table class="uiGrid" cellspacing="0" cellpadding="0">
															<tbody>
																<tr>
																	<td class="vMid prs" style="width: 40%">
																		<asp:TextBox ID="textboxSLMinutesExpiredActionsTags" runat="server" ClientIDMode="Static" Width="100%" />
																		<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;" LocalizationKey="configuration-queues-select_tags">
																				Seleccione las etiquetas que se asignarán a los mensajes. Deberá seleccionar al menos una etiqueta
																		</yoizen:Message>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none">
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSLMinutesExpiredActionsTags" ErrorMessage="Debe seleccionar al menos una etiqueta" SkinID="validationerror" data-i18n="configuration-queues-must_select_tag-error" />
											</div>
										</div>
									</div>
									<div class="subsubseccion collapseWithCheckbox">
										<div class="title">
											<h2>
												<asp:CheckBox ID="checkboxSLMinutesExpiredActionsVIM" runat="server" ClientIDMode="Static" />
												<span data-i18n="configuration-queues-vim-title">Marcar mensaje como VIM</span></h2>
										</div>
										<div class="contents">
											<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-nothing_to_configure">
												No hay que configurar nada
											</yoizen:Message>
										</div>
									</div>
									<div class="validationerror" style="display: none">
										<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceLevelExpiredAtLeastOneAction" ErrorMessage="Debe seleccionar al menos una acción a realizar" SkinID="validationerror" data-i18n="configuration-queues-must_select_action-error" />
									</div>
								</div>
							</div>
						</div>
					</asp:Panel>
				</div>
				<div id="divQueueUsers" runat="server" clientidmode="static">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-queues-supervisors_selection-title">Selección de Supervisores</span>:</th>
							<td class="data">
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-select_supervisors" style="margin-top: 5px">
									Seleccione los supervisores que pertenecerán a la cola. Deberá seleccionar al menos 1 supervisor
								</yoizen:Message>
								<asp:ListBox ID="listboxSupervisors" runat="server" ClientIDMode="Static" SelectionMode="Multiple" DataTextField="Description" DataValueField="ID" />
								<div class="validationerror" style="display: none">
                                    <asp:CustomValidator runat="server" ClientValidationFunction="ValidateSelectedSupervisors" EnableClientScript="true" SkinID="validationerror" ErrorMessage="Seleccione al menos un supervisor" data-i18n="configuration-queues-select_supervisors-error" />
                                </div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-queues-user_selection-title">Selección de Usuarios</span>:</th>
							<td class="data">
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-queues-select_users" style="margin-top: 5px">
									Seleccione los usuarios (no supervisores) que podrán ver la información de la cola
								</yoizen:Message>
								<asp:ListBox ID="listboxUsers" runat="server" ClientIDMode="Static" SelectionMode="Multiple" DataTextField="Description" DataValueField="ID" />
							</td>
						</tr>
					</table>
				</div>
				<div id="divQueueTags" runat="server" clientidmode="static">
					<asp:Panel ID="panelQueueTags" runat="server" CssClass="seccion">
						<div class="title">
							<h2 data-i18n="configuration-queues-tag_selection-title">Selección de Etiquetas</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow">
									<th class="label withdescription"><span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs" style="width: 40%">
														<asp:TextBox ID="textboxTags" runat="server" ClientIDMode="Static" Width="100%" />
														<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;" LocalizationKey="configuration-queues-select_tags_to_queue">
																		Seleccione las etiquetas que pertenecerán a la cola y podrán utilizar los agente en los mensajes
														</yoizen:Message>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow">
									<th class="label withdescription"><span data-i18n="menu-configuration-system-taggroups">Grupos de etiquetas</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs" style="width: 40%">
														<asp:HiddenField ID="hiddenTagGroups" runat="server" ClientIDMode="Static" />
														<select id="listboxTagGroups" multiple="multiple"></select>
														<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;" LocalizationKey="configuration-queues-select_tag_groups_to_queue">
															Seleccione los grupos de etiquetas que pertenecerán a la cola y podrán utilizar los agente en los mensajes
														</yoizen:Message>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
						</div>
						<div class="validationerror" style="display: none">
                            <asp:CustomValidator runat="server" ClientValidationFunction="ValidateSelectedTags" EnableClientScript="true" SkinID="validationerror" ErrorMessage="Seleccione al menos una etiqueta o un grupo de etiquetas" data-i18n="configuration-queues-select_a_tag-error" />
                        </div>
					</asp:Panel>
				</div>
				<div id="divQueueSurveys" runat="server" clientidmode="Static">
					<asp:Panel ID="panelQueueSurveys" runat="server">
						<yoizen:Message ID="messageNoQueueSurveys" runat="server" Type="Warning" Visible="false" LocalizationKey="configuration-queues-no_surveys_created">No existen encuestas creadas y habilitadas</yoizen:Message>
						<asp:Panel ID="panelEnableSurveys" runat="server">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-enable_surveys">Habilitar Envío de Encuestas</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxEnableSurveys" ClientIDMode="Static" runat="server"/>
													</td>
													<td class="vMid pls" data-i18n="configuration-queues-enable_surveys-tip">
														Habilita el envío automático de encuestas al finalizar un caso
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
							<yoizen:Message ID="messageNoSurveysInTable" runat="server" Type="Warning" Style="display: none" ClientIDMode="Static" LocalizationKey="configuration-queues-no_surveys_in_queue">
								No existen encuestas en la cola.
							</yoizen:Message>
							<div id="divWithSurveys" style="display: none">
								<table class="reporte" cellspacing="0" rules="all" border="1" id="tableSurveys" style="width: 100%; border-collapse: collapse">
									<thead>
										<tr class="header">
											<th scope="col"><span data-i18n="globals-name">Nombre</span></th>
											<th scope="col">&nbsp;</th>
										</tr>
									</thead>
									<tbody id="bodySurveys"></tbody>
								</table>
								<div style="display: none">
									<div id="divQueueSurvey">
										<div class="scrollable-y" style="max-height: 350px">
											<div id="divSurveyConfiguration" class="subseccion" style="margin-top: 20px">
												<div class="title">
													<h2 data-i18n="configuration-queues-survey_configuration-title">Configuración de encuestas</h2>
												</div>
												<div class="contents">
													<yoizen:Message ID="messageSurveyDisabled" runat="server" Type="Warning" style="display: none;" ClientIDMode="Static" LocalizationKey="configuration-queues-survey_disabled">
														La cola tenía configurada una encuesta que está deshabilitada. Por favor seleccione otra
													</yoizen:Message>
													<table width="100%" border="0" class="uiInfoTable noBorder">
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-survey_to_send">Encuesta a enviar</span>:</th>
															<td class="data">
																<asp:DropDownList ID="dropdownQueueSurvey" runat="server" DataValueField="ID" DataTextField="Name" AppendDataBoundItems="true" ClientIDMode="Static">
																	<asp:ListItem Value="-1" Text="Seleccione una encuesta" Selected="True" data-i18n="configuration-queues-select_survey" />
																</asp:DropDownList>
																<span id="spanQueueSurvey"></span>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label"><span data-i18n="configuration-queues-survey_invitation">Invitación para Participar</span>:</th>
															<td class="data">
																<asp:TextBox ID="textboxQueueSurveyInvitation" ClientIDMode="Static" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="3" autotrim="true" />
																<yoizen:Message ID="messageFilterEmailSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																	<span data-i18n="configuration-queues-survey_invitation_fields">Debe utilizar el siguiente campo dentro del texto de la invitación</span>:<br />
																	<ul>
																		<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-queues-survey_invitation-field-link">Indica donde irá el link de la encuesta</span></li>
																		<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-queues-survey_invitation-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																		<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-queues-survey_invitation-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																	</ul>
																</yoizen:Message>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label"><span data-i18n="configuration-queues-survey_invitation_whatsapp">Invitación para canales de WhatsApp</span>:</th>
															<td class="data">
																<asp:TextBox ID="textboxSurveyInvitationInteractive" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
																<yoizen:Message ID="message1" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																	<span data-i18n="configuration-queues-survey_invitation_fields_whatsapp">Debe utilizar el siguiente campo dentro del texto de la invitación</span>:<br />
																	<ul>
																		<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																		<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																	</ul>
																</yoizen:Message>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label"><span data-i18n="configuration-queues-survey_button">Texto del boton para canales de WhatsApp</span>:</th>
															<td class="data">
																	<asp:TextBox ID="textboxSurveyInvitationButton" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
																	<yoizen:Message ID="message2" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																		<span data-i18n="configuration-queues-survey_invitation_fields_button">Debe utilizar el siguiente campo dentro del texto de la invitación</span>
																	</yoizen:Message>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" id="queueSurveyExpiration">
															<th class="label"><span data-i18n="configuration-queues-expiration_time">Tiempo de expiración de la invitación (minutos)</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:TextBox ID="textboxQueueSurveyExpiration" ClientIDMode="Static" runat="server" MaxLength="5" Width="150" />
																				<div id="timeInfoSurveyExpiration" class="timeinfo" related="textboxQueueSurveyExpiration"></div>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-expiration_time-tip">
																				Define la cantidad de minutos que deben pasar a partir de la fecha de envío de la encuesta para considerar la invitación como expirada.
																				Ingrese cero para indicar que la invitación nunca expira
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow" id="queueSurveySendMailIfFailed">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-send_mail_if_failed">Enviar por mail en caso de fallo</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs">
																				<asp:CheckBox ID="checkboxQueueSurveySendMailIfFailed" runat="server" ClientIDMode="Static" />
																			</td>
																			<td class="vMid pls" data-i18n="[html]configuration-queues-send_mail_if_failed-tip">
																				<div>
																					Si se selecciona esta opción se podrá realizar el envío de la invitación por mail
																					en caso de que no se pueda mandar la invitación por la red del último contacto del
																					cliente y siempre y cuando el cliente tenga cargado el mail en el sistema.
																				</div>
																				<div class="dependsOnTwitter">
																					En caso de <span class="fab fa-lg fa-twitter-square"></span> Twitter el envío de la invitación puede fallar si el cliente no sigue a la cuenta
																					o si no tiene habilitado la recepción de mensajes privados de cualquier usuario
																				</div>
																				<div class="dependsOnFacebook">
																					En caso de <span class="fab fa-lg fa-facebook"></span> Facebook el envío de la invitación puede fallar si el cliente no
																					tiene iniciado una conversación privada con la cuenta
																				</div>
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow" id="surveyEnabledForChat">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-survey_enabled_for_chat">Enviar encuesta en chat</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs">
																				<asp:CheckBox ID="checkboxSurveyEnabledForChat" runat="server" ClientIDMode="Static" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-survey_enabled_for_chat-tip">
																				Define si la encuesta se utilizará también en chat. Para el caso, se mostrará el link al usuario una vez finalizado el chat. 				
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
													</table>
													<div id="divQueueSurveyEmail" class="subseccion">
														<div class="title">
															<h2 data-i18n="configuration-queues-mail_configuration-title">Configuración para envío por mail</h2>
														</div>
														<div class="contents">
															<table width="100%" border="0" class="uiInfoTable noBorder">
																<tr class="dataRow dataRowSeparator">
																	<th class="label"><span data-i18n="configuration-queues-sender">Remitente</span>:</th>
																	<td class="data">
																		<asp:TextBox ID="textboxQueueSurveyEmailFrom" runat="server" MaxLength="200" Width="300" ClientIDMode="Static" />
																	</td>
																</tr>
																<tr class="dataRow dataRowSeparator">
																	<th class="label"><span data-i18n="configuration-queues-subject">Asunto del Email</span>:</th>
																	<td class="data">
																		<asp:TextBox ID="textboxQueueSurveyEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
																	</td>
																</tr>
																<tr class="dataRow dataRowSeparator">
																	<th class="label"><span data-i18n="configuration-queues-mail_template">Plantilla del Email</span>:</th>
																	<td class="data">
																		<asp:TextBox ID="textboxQueueSurveyEmailTemplate" runat="server" TextMode="MultiLine" ClientIDMode="Static" Rows="5" style="resize: vertical; max-height: 300px" />
																		<yoizen:Message ID="messageQueueSurveyEmailTemplate" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																			<span data-i18n="configuration-queues-survey_email_template">Debe utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
																			<ul>
																				<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-queues-survey_email-field-link">Indica donde irá el link de la encuesta</span></li>
																			</ul>
																		</yoizen:Message>
																	</td>
																</tr>
															</table>
														</div>
													</div>
												</div>
											</div>
											<div id="divSurveyBehaviour" class="subseccion">
												<div class="title">
													<h2 data-i18n="configuration-queues-survey_behaviour-title">Condiciones para el envío</h2>
												</div>
												<div class="contents">
													<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-sent_rate">Tasa de envío (%)</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs">
																				<asp:TextBox ID="textboxQueueSurveySentRate" ClientIDMode="Static" runat="server" Width="50px" MaxLength="3" style="text-align: right" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-sent_rate-tip">
																				Mide el porcentaje de casos sobre los que se va a generar el envío de la encuesta.
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-time_to_send">Tiempo de envío (minutos)</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:TextBox ID="textboxQueueSurveyTimeToSend" ClientIDMode="Static" runat="server" Width="50px" MaxLength="5" style="text-align: right" />
																				<div id="timeInfoSurveyTimeToSend" class="timeinfo" related="textboxQueueSurveyTimeToSend"></div>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-time_to_send-tip">
																				Define la cantidad de minutos que deben pasar a partir del cierre del caso para que se envíe la encuesta al usuario.
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-closed_case_conditions">Condiciones del caso cerrado</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs">
																				<asp:DropDownList ID="dropdownlistQueueSurveyCloseCondition" runat="server" ClientIDMode="Static">
																					<asp:ListItem Text="Cerrados por agentes" Value="1" data-i18n="configuration-queues-closed_by_agents" />
																					<asp:ListItem Text="Cerrados por el supervisor / Sistema" Value="2" data-i18n="configuration-queues-closed_by_supervisor" />
																					<asp:ListItem Text="Todos los casos" Value="3" data-i18n="configuration-queues-all_cases" />
																				</asp:DropDownList>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-closed_case_conditions-tip">
																				Define la condición del cierre del caso que se utilizará para evaluar a quién se enviará la encuesta
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;">
																<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-i18n-popover='{
																	"toggle": "popover",
																	"html": true,
																	"maxWidth": "400px",
																	"trigger": "hover",
																	"title": "configuration-queues-tags",
																	"content": "configuration-queues-select_survey_tags"
																	}' data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
																<span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
															<td class="data">
																<asp:TextBox ID="textboxSurveysTags" runat="server" ClientIDMode="Static" Width="40%" />
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;">
																<span data-i18n="configuration-queues-group-tags">Grupos de etiquetas</span>:</th>
															<td class="data">
																<asp:HiddenField ID="hiddenSurveyTagGroup" runat="server" ClientIDMode="Static" />
																<select id="listboxSurveyTagGroup" multiple="multiple"></select>
																<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Grupo de etiquetas" data-i18n-popover='{
																				"toggle": "popover",
																				"html": true,
																				"maxWidth": "400px",
																				"trigger": "hover",
																				"title": "configuration-queues-group-tags",
																				"content": "configuration-queues-select_survey_tags_group"
																				}' data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-messages_count">Cantidad de mensajes en la conversación</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs">
																				<asp:TextBox ID="textboxQueueSurveyMessagesCount" ClientIDMode="Static" runat="server" MaxLength="4" Width="50px" style="text-align: right" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-messages_count-tip">
																				Establece una mínima cantidad de mensajes dentro de la conversación del caso.
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-case_duration">Duración mínima del caso</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:TextBox ID="textboxQueueSurveyCaseDuration" ClientIDMode="Static" runat="server" Width="50px" MaxLength="4" style="text-align: right" />
																				<div id="timeInfoSurveyCaseDuration" class="timeinfo" related="textboxQueueSurveyCaseDuration"></div>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-case_duration-tip">
																				Establece una mínima duración en minutos del caso.
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator">
															<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-case_with_agent_reply">Caso con respuesta del agente</span>:</th>
															<td class="data">
																<asp:DropDownList ID="dropdownlistQueueSurveyWithAgentReply" runat="server" ClientIDMode="Static">
																	<asp:ListItem Text="Indistinto" Value="-1" Selected="True" data-i18n="globals-any" />
																	<asp:ListItem Text="Si" Value="1" data-i18n="globals-yes" />
																	<asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
																</asp:DropDownList>
															</td>
														</tr>
													</table>
												</div>
											</div>
											<div id="divSurveyIgnoreConditions" class="subseccion">
												<div class="title">
													<h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont-title">Condiciones para no realizar el envío</h2>
												</div>
													<div class="contents">
														<div class="subseccion">
															<div class="contents">
																<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-services-common-yflow-surveys-configuration-send_conditions-only_one_active">
																<b>"Encuestar si ya existe un caso nuevo"</b> y <b>"Encuestar si el nuevo caso está en yFlow"</b> está diseñado para que puedan activarse únicamente de forma individual. Estas opciones no pueden convivir de manera simultánea en el sistema.
																</yoizen:Message>
																<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
																	<tr class="dataRow dataRowSeparator">
																		<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case">Encuestar si ya existe un caso nuevo</span>:</th>
																			<td class="data">
																			<table class="uiGrid" cellspacing="0" cellpadding="0">
																			<tbody>
																				<tr>
																					<td class="vMid prs timeinfo">
																						<asp:CheckBox ID="checkboxSurveySendIfNewCaseExists" runat="server" ClientIDMode="Static"/>
																					</td>
																					<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case-tip">
																						Establece si un caso será encuestado dependiendo de si ya existe un caso nuevo del mismo perfil
																					</td>
																				</tr>
																			</tbody>
																			</table>
																			</td>
																		</tr>
																		<tr class="dataRow dataRowSeparator">
																			<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_has_tag">Encuestar si el caso anterior posee determinada etiqueta</span>:</th>
																				<td class="data">
																				<table class="uiGrid" cellspacing="0" cellpadding="0">
																				<tbody>
																					<tr>
																						<td class="vMid prs timeinfo">
																							<asp:CheckBox ID="checkboxSurveySendIfNewCaseHasTag" runat="server" ClientIDMode="Static"/>
																						</td>
																						<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_has_tag-tip">
																							 Si se habilita esta opción, se evaluará el envío de encuesta si el caso anterior posee determinada etiqueta.
																						</td>
																					</tr>
																				</tbody>
																				</table>
																				</td>
																		</tr>
																		<tr class="dataRow dataRowSeparator">
																		<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case_closed_by_yflow">Encuestar si el nuevo caso fue cerrado en yFlow</span>:</th>
																		<td class="data">
																		<table class="uiGrid" cellspacing="0" cellpadding="0">
																			<tbody>
																				<tr>
																					<td class="vMid prs timeinfo">
																						<asp:CheckBox ID="checkboxSurveySendIfNewCaseClosedByYflow" runat="server" ClientIDMode="Static" />
																					</td>
																					<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case_closed_by_yflow-tip">Si se habilita esta opción, un caso nuevo cerrado por yFlow no anula el envío de encuestas del caso anterior.
																					</td>
																				</tr>
																				</tbody>
																			</table>
																		</td>
																	</tr>
																</table>
															</div>
														</div>
														<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">															
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey">No encuestar si la última envíada fue dentro de los últimos (minutos)</span>:</th>
																<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																		<tbody>
																			<tr>
																				<td class="vMid prs timeinfo">
																					<asp:TextBox ID="textboxSurveyDontSendIfLastSurveyAfterMinutes" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
																					<div id="timeInfoDontSendIfLastSurveyAfterMinutes" class="timeinfo" related="textboxSurveyDontSendIfLastSurveyAfterMinutes"></div>
																				</td>
																				<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey-tip">
																					Especifica la cantidad de minutos a considerar hacia atrás para evaluar si no se envió ninguna encuesta al usuario del caso. En caso de que la última fecha de envío
																					de encuesta sea posterior a la fecha actual restando los minutos configurados, no se enviará la encuesta para dicho caso.
																					En caso de configurar cero, no se aplicará esta condición. El máximo valor son 172800 (4 meses)
																				</td>
																			</tr>
																		</tbody>
																	</table>
																</td>
															</tr>
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly">No encuestar si ya se enviaron encuestas en el mes</span>:</th>
																<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																		<tbody>
																			<tr>
																				<td class="vMid prs timeinfo">
																					<asp:TextBox ID="textboxSurveyDontSendTotalSendMonthly" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
																				</td>
																				<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly-tip">
																					Especifica la cantidad de encuestas que pueden enviarse en el més para cada cliente.
																					En caso de configurar cero, no se aplicará esta condición. El máximo valor son 30 encuestas al mes.
																				</td>
																			</tr>
																		</tbody>
																	</table>
																</td>
															</tr>
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;">
																	<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas que no debe incluir" data-i18n-popover='{
																		"toggle": "popover",
																		"html": true,
																		"maxWidth": "400px",
																		"trigger": "hover",
																		"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags",
																		"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags-tooltip"
																		}' data-content="Seleccione las etiquetas que deberán tener los casos cerrados para no ser considerados para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
																	<span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags">Etiquetas que no debe incluir</span>:
																</th>
																<td class="data">
																	<asp:TextBox ID="textboxSurveysIgnoreTags" runat="server" ClientIDMode="Static" Width="40%" />
																</td>
															</tr>
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-group-tags-ignore">Etiquetas</span>:</th>
																<td class="data">
																	<asp:HiddenField ID="hiddenSurveyTagGroupToIgnore" runat="server" ClientIDMode="Static" />
																	<select id="listboxSurveyTagGruopToIgnore" multiple="multiple"></select>
																	<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-i18n-popover='{
																		"toggle": "popover",
																		"html": true,
																		"maxWidth": "400px",
																		"trigger": "hover",
																		"title": "configuration-queues-group-tags",
																		"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags_group-tooltip"
																		}' data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
																</td>
															</tr>
														</table>
													</div>
												</div>
										</div>
										<div id="divQueueSurveyError" class="validationerror" style="display: none"><span></span></div>
										<div class="buttons">
											<label class="uiButton uiButtonLarge uiButtonConfirm">
												<button type="button" data-i18n="globals-accept" onclick="ValidateSurvey()">Aceptar</button>
											</label>
											<label class="uiButton uiButtonLarge">
												<button type="button" data-i18n="globals-cancel" onclick="ReturnTableSurvey()">Cancelar</button>
											</label>
										</div>
									</div>
								</div>
							</div>
							<div class="validationerror" style="display: none">
								<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSurveys" data-i18n="configuration-serviceswhatsapp-configuration-surveys-error" />
							</div>
							<div id="buttonNewSurvey" class="buttons">
								<label class="uiButton uiButtonLarge uiButtonConfirm">
									<button type="button" onclick="AddNewSurvey()" data-i18n="configuration-surveys-new_survey">Nueva Encuesta</button>
								</label>
							</div>
						</asp:Panel>
					</asp:Panel>
				</div>
				<div id="divQueueEwt" runat="server" clientidmode="static">
					<asp:Panel ID="panelQueueEwt" runat="server" CssClass="seccion">
						<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-ewt_subtitle">Configurar el EWT para esta cola</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxConfigEwt" ClientIDMode="Static" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-queues-ewt_tip">
													Permite configurar los tiempos del cálculo del ewt para esta cola en particular.
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
						<div id="divEwtConfiguration" class="seccion" style="margin-top: 10px">
							<div class="title">
								<h2 data-i18n="configuration-queues-ewt_title">Configuración EWT</h2>
							</div>
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-minutes_predicted_aht">Cantidad de minutos para calcular el AHT</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxMinutesPredictedAht" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="5" max="55" step="5" list="minutesPredictedAHT" EnableTheming="false" />
                                        <datalist id="minutesPredictedAHT">
                                            <option value="5">
                                            <option value="10">
                                            <option value="15">
                                            <option value="20">
                                            <option value="25">
                                            <option value="30">
                                            <option value="35">
                                            <option value="40">
                                            <option value="45">
                                            <option value="50">
                                            <option value="55">
                                        </datalist>
                                        <div id="spanMinutesPredictedAht" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxMinutesPredictedAht" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxMinutesPredictedAht" MinimumValue="5" MaximumValue="60" Type="Integer" />
										<yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px" LocalizationKey="configuration-systemsettings-queues-minutes_predicted_aht_info">
											Cantidad de minutos 
										</yoizen:Message>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-seconds_calculate_ewt">Cantidad de segundos para calcular el EWT</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxSecondsEwt" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="15" max="60" step="15" list="secondsEwt" EnableTheming="false" />
										<datalist id="secondsEwt">
											<option value="15">
											<option value="30">
											<option value="45">
											<option value="60">
										</datalist>
										<div id="spanSecondsEwt" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxSecondsEwt" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxSecondsEwt" MinimumValue="15" MaximumValue="60" Type="Integer" />
										<yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px" LocalizationKey="configuration-systemsettings-queues-seconds_calculate_ewt_info">
											Cantidad de segundos 
										</yoizen:Message>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-asa_base">Personalizar el EWT mediante un ASA base</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxASAPersonalized" runat="server" />
														<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px; display:none" rel="tableASABase" >
															<tbody>
																<tr>
																	<td class="dataRow dataRowSeparator">
																		<asp:TextBox ID="textboxAsaBase" runat="server" Width="100" TextMode="Number" min="30" max="3600" style="text-align: right" />
																		<div class="secondinfo" related="textboxAsaBase"></div>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-queues-asa_base_seconds">
														Permite calcular el EWT tomando el valor del ASA base expresado en segundos, siempre y cuando el sistema no cuente con los datos.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</table>
						</div>
						</div>
					</asp:Panel>
				</div>
                <div id="divQueueAutomaticActions" runat="server" clientidmode="static">
                    <yoizen:Message runat="server" Type="Warning" Text="Modificar parámetros del sistema afectará el comportamiento" LocalizationKey="configuration-queues-enable_automatic-warning" />
                    <asp:Panel ID="panelFirstAutomaticAction" runat="server" CssClass="seccion" ClientIDMode="Static">
                        <div class="title">
                            <h2 data-i18n="configuration-queues-first-expiration-title">Acciones ante el primer vencimiento</h2>
                        </div>
                        <table width="100%" border="0" class="uiInfoTable noBorder">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-enable_automatic-actions">Habilitar Acciones Automaticas</span>:</th>
                                <td class="data">
                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="vMid prs">
                                                    <asp:CheckBox ID="checkboxAllowFirstAutomaticActions" runat="server" ClientIDMode="Static" />
                                                </td>
                                                <td class="vMid pls" data-i18n="configuration-queues-enable_automatic-actions-tip">Realiza acciones automaticas al cumplir el tiempo establecido
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        <div id="divFirstAutomaticActionConfiguration" class="seccion" style="margin-top: 10px">
                            <div class="contents">
                                <table class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-actions_seconds">Temporizador (segundos)</span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs timeinfo">
                                                            <asp:TextBox ID="textboxFirstAutomaticActionSeconds" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                            <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionSeconds" />
                                                            <div class="timeinfo" related="textboxFirstAutomaticActionSeconds"></div>
                                                        </td>
                                                        <td class="vMid pls" data-i18n="configuration-queues-automatic_actions_seconds-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
														El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
														manera inmediata
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                <div id="panelAutomaticActions" runat="server" class="subseccion" clientidmode="Static">
                                    <div class="title">
                                        <h2 data-i18n="configuration-queues-automatic_actions-actions-title">Realizar acción al superar el umbral establecido</h2>
                                    </div>
                                    <div class="contents">
                                        <div class="subsubseccion collapseWithCheckbox">
                                            <div class="title">
                                                <h2>
                                                    <asp:CheckBox ID="checkboxFirstAutomaticActionsTransferQueue" runat="server" ClientIDMode="Static" />
                                                    <span data-i18n="configuration-queues-move_to_queue-title">Mover a cola</span>
                                                </h2>
                                            </div>
                                            <div class="contents">
                                                <yoizen:Message ID="messageFirstActionTransferQueueDisabled" runat="server" Visible="false" Type="Warning" LocalizationKey="configuration-queues-selected_queue_disabled">
												La cola seleccionada previamente se encuentra deshabilitada. Seleccione otra cola
                                                </yoizen:Message>
                                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                                    <tr class="dataRow">
                                                        <th class="label withdescription"><span data-i18n="configuration-queues-queue">Cola</span>:</th>
                                                        <td class="data">
                                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="vMid prs" style="width: 40%">
                                                                            <asp:ListBox ID="dropdownlistFirstAutomaticActionQueues" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" Width="300" />
                                                                        </td>
                                                                        <td class="vMid pls" data-i18n="configuration-queues-selected_queue_disabled-tip">Esta será la cola a la cual se asignará el mensaje cuando sea impuntual
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div class="validationerror" style="display: none">
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionAssignToQueue" ErrorMessage="Debe seleccionar al menos una cola" SkinID="validationerror" data-i18n="configuration-queues-must_select_queue-error" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="subsubseccion collapseWithCheckbox">
                                            <div class="title">
                                                <h2>
                                                    <asp:CheckBox ID="checkboxFirstAutomaticActionReply" runat="server" ClientIDMode="Static" />
                                                    <span data-i18n="configuration-queues-automatic_response-title">Responder Automáticamente</span>
                                                </h2>
                                            </div>
                                            <div class="contents">
                                                <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-automatic_response-no_chat">
												Las respuestas automáticas aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
                                                </yoizen:Message>
                                                <table width="100%" border="0" class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
                                                    <tr class="dataRow dataRowSeparator">
                                                        <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta</span>:</th>
                                                        <td class="data">
                                                            <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                <asp:TextBox ID="textboxFirstAutomaticActionReplyText" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                <span id="spanFirstAutomaticActionReplyTextCounter" clientidmode="Static" runat="server"></span>
                                                            </div>
                                                            <yoizen:Message ID="messageFirstAutomaticActionReply" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-tip">
															Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
															Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
															puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
															puede incluir <b>@@EWT@@</b> que se reemplazará por el EWT actual de la cola al momento de responder el mensaje. 
															puede incluir <b>@@POSMSGCOLA@@</b> que se reemplazará por la cantidad de mensajes al momento de responder el mensaje. 
                                                            </yoizen:Message>
                                                        </td>
                                                    </tr>
                                                    <tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionMinimumEWT">
                                                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_ewt">Minimo de mensajes encolados</span>:</th>
                                                        <td class="data">
                                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="vMid prs timeinfo">
                                                                            <asp:TextBox ID="textboxFirstAutomaticActionMinimumEWT" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                                            <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
                                                                        </td>
                                                                        <td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_ewt-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																			El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																			manera inmediata
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionMinimumEnqueueMessages">
                                                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_enqueued_messages">Minimo de mensajes encolados</span>:</th>
                                                        <td class="data">
                                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="vMid prs timeinfo">
                                                                            <asp:TextBox ID="textboxFirstAutomaticActionMinimumEnqueueMessages" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                                            <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
                                                                        </td>
                                                                        <td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_enqueued_messages-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																			El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																			manera inmediata
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionReplyEwtNoAgents">
                                                        <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_agents">Texto de respuesta ante agentes desconectados en la cola:</span>:</th>
                                                        <td class="data">
                                                            <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                <asp:TextBox ID="textboxFirstAutomaticActionReplyEwtNoAgents" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                <span id="spanFirstAutomaticActionReplyEwtNoAgentsCounter" clientidmode="Static" runat="server"></span>
                                                            </div>
                                                            <yoizen:Message ID="messageFirstActionEwtNoAgents" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-ewt-no-agents-tip">
																	Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																	Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																	puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
                                                            </yoizen:Message>
                                                        </td>
                                                    </tr>
                                                    <tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionReplyEwtNotCalculated">
                                                        <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_ewt">Texto de respuesta</span>:</th>
                                                        <td class="data">
                                                            <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                <asp:TextBox ID="textboxFirstAutomaticActionReplyEwtNotComputed" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                <span id="spanFirstAutomaticActionReplyEwtNotComputedCounter" clientidmode="Static" runat="server"></span>
                                                            </div>
                                                            <yoizen:Message ID="messageFirstActionEwtNotComputed" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-ewt-not-calculated-tip">
																		Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																		Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																		puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
                                                            </yoizen:Message>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div class="validationerror" style="display: none">
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionReplyText" SkinID="validationerror" data-i18n="configuration-queues-automatic_response-error_message">
													Debe ingresar el mensaje a notificar al chat
                                                    </asp:CustomValidator>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="subsubseccion collapseWithCheckbox">
                                            <div class="title">
                                                <h2>
                                                    <asp:CheckBox ID="checkboxFirstAutomaticActionNotifyChat" runat="server" ClientIDMode="Static" />
                                                    <span data-i18n="configuration-queues-chat_notify-title">Notificar al canal de chat</span> <span class="fa fa-lg fa-comments" style="margin-left: 3px"></span></h2>
                                            </div>
                                            <div class="contents">
                                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                                    <tr class="dataRow dataRowSeparator">
                                                        <th class="label"><span data-i18n="configuration-queues-chat_notify-text">Mensaje a notificar</span>:</th>
                                                        <td class="data">
                                                            <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                <asp:TextBox ID="textboxFirstAutomaticActionReplyChat" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
															</div>
															<yoizen:Message ID="messageFirstAutomaticActionReplyChat" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-tip">
																	Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																	Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																	puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
															</yoizen:Message>
                                                        </td>
                                                    </tr>
													<tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionMinimumEWTChat">
															<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_ewt">Minimo de mensajes encolados</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:TextBox ID="textboxFirstAutomaticActionMinimumEWTChat" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
																				<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_ewt-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																				El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																				manera inmediata
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
													</tr>
													<tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionMinimumEnqueueMessagesChat">
														<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_enqueued_messages">Minimo de mensajes encolados</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxFirstAutomaticActionMinimumEnqueueMessagesChat" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
																			<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
																		</td>
																		<td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_enqueued_messages-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																			El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																			manera inmediata
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionReplyEwtNoAgentsChat">
														<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_agents">Texto de respuesta ante agentes desconectados en la cola:</span>:</th>
														<td class="data">
															<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
																<asp:TextBox ID="textboxFirstAutomaticActionReplyEwtNoAgentsChat" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
																<span id="span1" clientidmode="Static" runat="server"></span>
															</div>
															<yoizen:Message ID="messageFirstAutomaticActionReplyEwtNoAgentsChat" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-ewt-no-agents-tip">
																	Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																	Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																	puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
															</yoizen:Message>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator" style="display: none" id="trFirstAutomaticActionReplyEwtNotCalculatedChat">
														<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_ewt">Texto de respuesta</span>:</th>
														<td class="data">
															<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
																<asp:TextBox ID="textboxFirstAutomaticActionReplyEwtNotCalculatedChat" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
																<span id="span2" clientidmode="Static" runat="server"></span>
															</div>
															<yoizen:Message ID="messageFirstAutomaticActionReplyEwtNotCalculatedChat" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-ewt-not-calculated-tip">
																	Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																	Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																	puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
															</yoizen:Message>
														</td>
													</tr>
                                                    <tr class="dataRow dataRowSeparator">
                                                        <th class="label"><span data-i18n="configuration-queues-chat_notify-finish">Finalizar el chat</span>:</th>
                                                        <td class="data">
                                                            <asp:DropDownList ID="dropdownlistFirstAutomaticActionMarkAsFinishChat" runat="server">
                                                                <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                                                <asp:ListItem Value="1" data-i18n="configuration-queues-chat_notify-yes_only_not_attended">Únicamente si no fue atendido</asp:ListItem>
                                                                <asp:ListItem Value="2" data-i18n="globals-yes">No</asp:ListItem>
                                                            </asp:DropDownList>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div class="validationerror" style="display: none">
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionChatNotify" SkinID="validationerror" data-i18n="configuration-queues-chat_notify-error">
													Debe ingresar el mensaje a notificar al chat
                                                    </asp:CustomValidator>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="subsubseccion collapseWithCheckbox">
                                            <div class="title">
                                                <h2>
                                                    <asp:CheckBox ID="checkboxFirstAutomaticActionApplyTags" runat="server" ClientIDMode="Static" />
                                                    <span data-i18n="configuration-queues-add_tags-title">Aplicar etiquetas</span>
                                                </h2>
                                            </div>
                                            <div class="contents">
												<table width="100%" border="0" class="uiInfoTable noBorder">
													<tr class="dataRow">
														<th class="label withdescription"><span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs" style="width: 40%">
																			<asp:TextBox ID="textboxFirstAutomaticActionsTags" runat="server" ClientIDMode="Static"
																				Width="100%" />
																			<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;"
																				LocalizationKey="configuration-queues-select_tags">
																				Seleccione las etiquetas que se asignarán a los mensajes. Deberá seleccionar al menos
																				una etiqueta
																			</yoizen:Message>
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
												</table>
                                                <div class="validationerror" style="display: none">
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionsTags" ErrorMessage="Debe seleccionar al menos una etiqueta" SkinID="validationerror" data-i18n="configuration-queues-must_select_tag-error" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="validationerror" style="display: none">
                                            <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticAction" ErrorMessage="Debe seleccionar al menos una acción a realizar" SkinID="validationerror" data-i18n="configuration-queues-must_select_action-error" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <asp:Panel ID="panelSecondAutomaticAction" runat="server" CssClass="seccion">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-enable_automatic-actions_second_level">Habilitar Acciones Automaticas</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxAllowSecondAutomaticActions" ClientIDMode="Static" runat="server" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-queues-enable_automatic-actions-tip">Realiza acciones automaticas al cumplir el tiempo establecido
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <div class="title">
                                <h2 data-i18n="configuration-queues-second-expiration-title">Acciones ante el segundo vencimiento</h2>
                            </div>
                            <div id="divSecondAutomaticActionConfiguration" class="seccion" style="margin-top: 10px">
                                <div class="contents">
                                    <table class="uiInfoTable noBorder" cellspacing="0" cellpadding="0">
                                        <tr class="dataRow dataRowSeparator">
                                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-actions_seconds">Temporizador (segundos)</span>:</th>
                                            <td class="data">
                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="vMid prs timeinfo">
                                                                <asp:TextBox ID="textboxSecondAutomaticActionsSeconds" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionsSeconds" />
                                                                <div class="timeinfo" related="textboxSecondAutomaticActionsSeconds"></div>
                                                            </td>
                                                            <td class="vMid pls" data-i18n="configuration-queues-automatic_actions_seconds-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos. 
																El valor tiene que estar entre 0 y 604800. En caso de specificar cero, el sistema aplicará la función manera inmediata
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
												<div class="validationerror" style="display: none">
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondActionTimeDiff" ErrorMessage="El valor debe ser mayor que el tiempo de la primera acción" SkinID="validationerror" data-i18n="configuration-queues-minutes_second_action-error" />
												</div>
                                            </td>
                                        </tr>
                                    </table>
                                    <div id="panelSecondAutomaticActions" runat="server" class="subseccion" clientidmode="Static">
                                        <div class="title">
                                            <h2 data-i18n="configuration-queues-automatic_actions-actions-title">Realizar acción al superar el umbral establecido</h2>
                                        </div>
                                        <div class="contents">
                                            <div class="subsubseccion collapseWithCheckbox">
                                                <div class="title">
                                                    <h2>
                                                        <asp:CheckBox ID="checkboxSecondAutomaticActionsTransferQueue" runat="server" ClientIDMode="Static" />
                                                        <span data-i18n="configuration-queues-move_to_queue-title">Mover a cola</span></h2>
                                                </div>
                                                <div class="contents">
                                                    <yoizen:Message ID="messageSecondAutomaticActionTransferQueueDisabled" runat="server" Visible="false" Type="Warning" LocalizationKey="configuration-queues-selected_queue_disabled">
														La cola seleccionada previamente se encuentra deshabilitada. Seleccione otra cola
                                                    </yoizen:Message>
                                                    <table width="100%" border="0" class="uiInfoTable noBorder">
                                                        <tr class="dataRow">
                                                            <th class="label withdescription"><span data-i18n="configuration-queues-queue">Cola</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs" style="width: 40%">
                                                                                <asp:ListBox ID="dropdownlistSecondAutomaticActionQueues" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" Width="300" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-queues-selected_queue_disabled-tip">Esta será la cola a la cual se asignará el mensaje cuando sea impuntual
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="validationerror" style="display: none">
                                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionAssignToQueue" ErrorMessage="Debe seleccionar al menos una cola" SkinID="validationerror" data-i18n="configuration-queues-must_select_queue-error" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="subsubseccion collapseWithCheckbox">
                                                <div class="title">
                                                    <h2>
                                                        <asp:CheckBox ID="checkboxSecondAutomaticActionReply" runat="server" ClientIDMode="Static" />
                                                        <span data-i18n="configuration-queues-automatic_response-title">Responder Automáticamente</span>
                                                    </h2>
                                                </div>
                                                <div class="contents">
                                                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-queues-automatic_response-no_chat">
											Las respuestas automáticas aplican exclusivamente a los canales que no son de tipo Chat o Chat Integrado
                                                    </yoizen:Message>
                                                    <table width="100%" border="0" class="uiInfoTable noBorder">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta</span>:</th>
                                                            <td class="data">
                                                                <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                    <asp:TextBox ID="textboxSecondAutomaticActionReplyText" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                    <span id="spanSecondAutomaticActionReplyTextCounter" clientidmode="Static" runat="server"></span>
                                                                </div>
                                                                <yoizen:Message ID="messageSecondAutomaticActionReply" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-tip">
																Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																puede incluir <b>@@EWT@@</b> que se reemplazará por el EWT actual de la cola al momento de responder el mensaje. 
																puede incluir <b>@@POSMSGCOLA@@</b> que se reemplazará por la cantidad de mensajes al momento de responder el mensaje. 
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionMinimumEWT">
                                                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_ewt">Minimo de mensajes encolados</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSecondAutomaticActionMinimumEWT" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionMinimumEnqueueMessages" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_ewt-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																	El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																	manera inmediata
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionMinimumEnqueueMessages">
                                                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_enqueued_messages">Minimo de mensajes encolados</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSecondAutomaticActionMinimumEnqueueMessages" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
                                                                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionMinimumEnqueueMessages" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_enqueued_messages-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																	El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																	manera inmediata
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionReplyEwtNoAgents">
                                                            <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta ante agentes desconectados en la cola:</span>:</th>
                                                            <td class="data">
                                                                <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                    <asp:TextBox ID="textboxSecondAutomaticActionReplyEwtNoAgents" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                    <span id="spanSecondAutomaticActionReplyEwtNoAgentsCounter" clientidmode="Static" runat="server"></span>
                                                                </div>
                                                                <yoizen:Message ID="messageSecondAutomaticReplyEwtNoAgents" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-ewt-no-agents-tip">
																Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																puede incluir <b>@@EWT@@</b> que se reemplazará por el EWT actual de la cola al momento de responder el mensaje. 
																puede incluir <b>@@POSMSGCOLA@@</b> que se reemplazará por la cantidad de mensajes al momento de responder el mensaje. 
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionReplyEwtNotCalculated">
                                                            <th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-text">Texto de respuesta</span>:</th>
                                                            <td class="data">
                                                                <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                    <asp:TextBox ID="textboxSecondAutomaticActionReplyEwtNotComputed" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
                                                                    <span id="spanSecondAutomaticActionReplyEwtNotComputedCounter" clientidmode="Static" runat="server"></span>
                                                                </div>
                                                                <yoizen:Message ID="messageSecondAutomaticReplyEwtNotCalculated" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-ewt-not-calculated-tip">
																Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																puede incluir <b>@@EWT@@</b> que se reemplazará por el EWT actual de la cola al momento de responder el mensaje. 
																puede incluir <b>@@POSMSGCOLA@@</b> que se reemplazará por la cantidad de mensajes al momento de responder el mensaje. 
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="validationerror" style="display: none">
                                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionReplyText" SkinID="validationerror" data-i18n="[html]configuration-queues-automatic_response-error_message">
												Debe incluir el parámetro @@USUARIO@@ en la respuesta<span class="dependsOnTwitter"> y la longitud no debe superar los 240 caracteres</span>
                                                        </asp:CustomValidator>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="subsubseccion collapseWithCheckbox">
                                                <div class="title">
                                                    <h2>
                                                        <asp:CheckBox ID="checkboxSecondAutomaticActionNotifyChat" runat="server" ClientIDMode="Static" />
                                                        <span data-i18n="configuration-queues-chat_notify-title">Notificar al canal de chat</span> <span class="fa fa-lg fa-comments" style="margin-left: 3px"></span></h2>
                                                </div>
                                                <div class="contents">
                                                    <table width="100%" border="0" class="uiInfoTable noBorder">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-queues-chat_notify-text">Mensaje a notificar</span>:</th>
                                                            <td class="data">
                                                                <div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
                                                                    <asp:TextBox ID="textboxSecondAutomaticActionChatReplyText" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
																</div>
																<yoizen:Message ID="messageSecondAutomaticActionChatReplyText" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-tip">
																	Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																	Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																	puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																</yoizen:Message>
                                                            </td>
                                                        </tr>
														<tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionMinimumEWTChat">
															<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_ewt">Minimo de mensajes encolados</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:TextBox ID="textboxSecondAutomaticActionMinimumEWTChat" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
																				<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_ewt-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																				El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																				manera inmediata
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionMinimumEnqueueMessagesChat">
														<th class="label" style="width: 200px !important"><span data-i18n="configuration-queues-automatic-minimum_enqueued_messages">Minimo de mensajes encolados</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSecondAutomaticActionMinimumEnqueueMessagesChat" runat="server" MaxLength="6" Width="50" autocomplete="off" ClientIDMode="Static" />
																			<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFirstAutomaticActionMinimumEnqueueMessages" />
																		</td>
																		<td class="vMid pls" data-i18n="configuration-queues-automatic-minimum_enqueued_messages-tip">Indica el objetivo de tiempo en que el cliente sera notificado y/o se ejecutaran acciones expresado en segundos.
																			El valor tiene que estar entre 0 y 604800. En caso de specificar cero, la cola no aplicará la función
																			manera inmediata
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
														</tr>
														<tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionReplyEwtNoAgentsChat">
															<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_agents">Texto de respuesta ante agentes desconectados en la cola:</span>:</th>
															<td class="data">
																<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
																	<asp:TextBox ID="textboxSecondAutomaticActionReplyEwtNoAgentsChat" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
																	<span id="span3" clientidmode="Static" runat="server"></span>
																</div>
																<yoizen:Message ID="message3" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-ewt-no-agents-tip">
																		Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																		Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																		puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																</yoizen:Message>
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" style="display: none" id="trSecondAutomaticActionReplyEwtNotCalculatedChat">
															<th class="label" style="width: 300px !important"><span data-i18n="configuration-queues-automatic_response-no_ewt">Texto de respuesta</span>:</th>
															<td class="data">
																<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
																	<asp:TextBox ID="textboxSecondAutomaticActionReplyEwtNotCalculatedChat" runat="server" ClientIDMode="Static" MaxLength="240" TextMode="MultiLine" Rows="2" Style="flex-grow: 1; flex-shrink: 1; margin-right: 5px;" />
																	<span id="span4" clientidmode="Static" runat="server"></span>
																</div>
																<yoizen:Message ID="message4" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="[html]configuration-queues-automatic-actions-automatic_response-chat-ewt-not-calculated-tip">
																		Esta será la respuesta que se le enviará el cliente. <span class="dependsOnTwitter">La longitud máxima del texto es de 240 caracteres. </span>
																		Debe incluír <b>@@USUARIO@@</b> al inicio del mensaje que se sustituirá por el nombre del usuario que envió el mensaje y 
																		puede incluir <b>@@FECHA@@</b> que se reemplazará por la fecha que se auto respondió el mensaje. 
																</yoizen:Message>
															</td>
														</tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-queues-chat_notify-finish">Finalizar el chat</span>:</th>
                                                            <td class="data">
                                                                <asp:DropDownList ID="dropdownlistSecondAutomaticActionMarkAsFinishChat" runat="server">
                                                                    <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                                                    <asp:ListItem Value="1" data-i18n="configuration-queues-chat_notify-yes_only_not_attended">Únicamente si no fue atendido</asp:ListItem>
                                                                    <asp:ListItem Value="2" data-i18n="globals-yes">No</asp:ListItem>
                                                                </asp:DropDownList>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="validationerror" style="display: none">
                                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionChatNotify" SkinID="validationerror" data-i18n="configuration-queues-chat_notify-error">
												Debe ingresar el mensaje a notificar al chat
                                                        </asp:CustomValidator>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="subsubseccion collapseWithCheckbox">
                                                <div class="title">
                                                    <h2>
                                                        <asp:CheckBox ID="checkboxSecondAutomaticActionApplyTags" runat="server" ClientIDMode="Static" />
                                                        <span data-i18n="configuration-queues-add_tags-title">Aplicar etiquetas</span></h2>
                                                </div>
                                                <div class="contents">
													<table width="100%" border="0" class="uiInfoTable noBorder">
														<tr class="dataRow">
															<th class="label withdescription"><span data-i18n="configuration-queues-tags">Etiquetas</span>:</th>
															<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs" style="width: 40%">
																				<asp:TextBox ID="textboxSecondAutomaticActionsTags" runat="server" ClientIDMode="Static" Width="100%" />
																				<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 5px;" LocalizationKey="configuration-queues-select_tags">
																				Seleccione las etiquetas que se asignarán a los mensajes. Deberá seleccionar al menos una etiqueta
																				</yoizen:Message>
																			</td>
																		</tr>
																	</tbody>
																</table>
															</td>
														</tr>
													</table>
                                                    <div class="validationerror" style="display: none">
                                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticActionsTags" ErrorMessage="Debe seleccionar al menos una etiqueta" SkinID="validationerror" data-i18n="configuration-queues-must_select_tag-error" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSecondAutomaticAction" ErrorMessage="Debe seleccionar al menos una acción a realizar" SkinID="validationerror" data-i18n="configuration-queues-must_select_action-error" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </asp:Panel>
                    </asp:Panel>
                </div>
				<div id="divQueueVideo">
					<asp:Panel ID="panelVideo" runat="server">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-video-enable_video">Habilitar uso de video</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxEnableVideo" ClientIDMode="Static" runat="server"/>
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id="trQueueVideoProject">
								<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-video-project">Proyecto</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:DropDownList ID="dropdownlistVideoProject" runat="server" DataTextField="name" DataValueField="id" Width="300px" />
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id="trQueueVideoDefaultText">
								<th class="label"><span data-i18n="configuration-queues-video-default_text">Texto predefinido</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxVideoDefaultText" runat="server" MaxLength="200" Width="90%" Enabled="false" />				
								</td>
							</tr>
						</table>
						<%--
							
							<div class="validationerror" style="display: none">
							<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSurveys" data-i18n="configuration-serviceswhatsapp-configuration-surveys-error" />
						</div>--%>
					</asp:Panel>
					<yoizen:Message ID="ErrorMessageVideo" Visible="false" runat="server" Type="Error" LocalizationKey="configuration-queues-video-configuration_errors_message" ClientIDMode="Static">
									Error de configuracion de video, revise los parametros del sistema
					</yoizen:Message>
				</div>
            </div>
			<div class="buttons">
				<label class="uiButton uiButtonLarge uiButtonConfirm">
					<asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" OnClientClick="buttonSave()" data-i18n="globals-accept"/>
				</label>
				<label class="uiButton uiButtonLarge">
					<asp:Button ID="buttonCancel" runat="server" Text="Cancelar" OnClick="buttonCancel_Click" CausesValidation="false" data-i18n="globals-cancel" />
				</label>
			</div>
		</asp:Panel>
		<asp:HiddenField ID="hiddenExportFormat" runat="server" ClientIDMode="Static" Value="1" />
		<div style="display: none">
			<div class="seccion" id="divExport">
				<div class="title">
					<h2 data-i18n="configuration-agents-export">Exportación de Agentes</h2>
				</div>
				<div class="contents">
					<div id="divExportStep1">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="[html]reports-globals-csv_instead_excel">
							Cuando el reporte a generar tiene mucha información es posible que en lugar
							de formato <b>Excel</b> se use <b>Separado por coma (CSV)</b>
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable">
							<tr class="dataRow">
								<th class="label">Formato:</th>
								<td class="data">
									<select id="selectExportFormat">
										<option value="1" data-i18n="reports-globals-export-format-excel">Excel</option>
										<option value="2" data-i18n="reports-globals-export-format-csv">Separados por coma (CSV)</option>
									</select>
								</td>
							</tr>
						</table>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-export" id="buttonExportCompleteVisible" value="Exportar" onclick="ExportVisible()">Exportar</button>
							</label>
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-cancel" value="Cancelar" onclick="$.colorbox.close()">Cancelar</button>
							</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</asp:Panel>
</asp:Content>
