﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Http;
using System.Web.Http.Filters;

namespace FacebookCallback
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Web API configuration and services
			config.IncludeErrorDetailPolicy = IncludeErrorDetailPolicy.Always;

            // Web API routes
            config.MapHttpAttributeRoutes();
			config.Filters.Add(new ExceptionHandlingAttribute());
			config.Routes.MapHttpRoute(
				name: "Twitter<PERSON><PERSON>",
				routeTemplate: "api/twitter/{action}/{id}",
				defaults: new
				{
					id = RouteParameter.Optional,
					action = "DefaultAction",
					controller = "twitter"
				}
			);
			config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "api/{controller}/{id}/{action}",
                defaults: new
                {
	                id = RouteParameter.Optional,
	                action = "DefaultAction"
                }
            );
			config.Routes.MapHttpRoute(
				name: "StatusA<PERSON>",
				routeTemplate: "api/status/{action}",
				defaults: new
				{
					action = "DefaultAction"
				}
			);
		}
    }

	public class ExceptionHandlingAttribute : ExceptionFilterAttribute
	{
		public override void OnException(HttpActionExecutedContext actionExecutedContext)
		{
			//Log Critical errors
			if (actionExecutedContext.Request != null)
			{
				try
				{
					var request = actionExecutedContext.Request;
					var requestInfo = $"{request.Method} {request.RequestUri}{Business.LogManager.ConvertHeadersToString(request.Headers)}";
					Yoizen.Common.Tracer.TraceError("Ocurrió un error no manejado en el request {0}: {1}", requestInfo, actionExecutedContext.Exception);
				}
				catch
				{
					Yoizen.Common.Tracer.TraceError("Ocurrió un error no manejado: {0}", actionExecutedContext.Exception);
				}
			}
			else
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error no manejado: {0}", actionExecutedContext.Exception);
			}

			throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
			{
				Content = new StringContent("An error occurred, please try again or contact the administrator."),
				ReasonPhrase = "Critical Exception"
			});
		}
	}
}
