﻿trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - 'Yoizen.Social.WebAgent/**'

pool:
  vmImage: 'windows-latest'

variables:
  - name: PROJECT_DIR
    value: 'Yoizen.Social.WebAgent'
  
  - name: DEPLOY_DIR
    value: '$(Build.SourcesDirectory)\Deploy\Release\WebAgent'

  - name: NPM_GLOBAL_PATH
    value: 'C:\npm-global'

  - group: StorageSecrets
  - group: global-variables

jobs:
  - job: Build_Web_Agent
    displayName: 'Build and publish WebAgent'
    steps:
    - checkout: self
      path: 'sources'  # Cambia la ruta de checkout

    - task: NodeTool@0
      inputs:
        versionSpec: '16.x'
      displayName: 'Instalar Node.js'

    - script: |
        echo "##[group]Configurar entorno npm"
        npm config set prefix "$(NPM_GLOBAL_PATH)"
        SET PATH=$(NPM_GLOBAL_PATH);%PATH%
        npm install -g grunt-cli
        echo "##[endgroup]"
      displayName: 'Configurar Grunt'

    - script: |
        echo "##[group]Instalar dependencias"
        cd $(PROJECT_DIR)
        npm install --legacy-peer-deps
        echo "##[endgroup]"
      displayName: 'Instalar dependencias'

    - script: |
        echo "##[group]Ejecutar Grunt"
        cd $(PROJECT_DIR)
        npx grunt --verbose prod_pipeline
        echo "##[endgroup]"
      displayName: 'Ejecutar construcción'
      env:
        DEPLOY_PATH: $(DEPLOY_DIR)

    - script: |
        echo "##[group]Verificar resultados"
        if not exist "$(DEPLOY_DIR)" (
          echo "##vso[task.logissue type=error]No se generaron archivos"
          exit 1
        )
        dir "$(DEPLOY_DIR)"
        echo "##[endgroup]"
      displayName: 'Verificar build'

    - task: CopyFiles@2
      displayName: 'Copiar artefactos'
      inputs:
        SourceFolder: '$(DEPLOY_DIR)'
        Contents: '**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'

    - task: ArchiveFiles@2
      displayName: 'Create ZIP file'
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/WebAgent.zip'
        replaceExistingArchive: true

    - task: PublishBuildArtifacts@1
      displayName: 'Publish ZIP artifact'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/WebAgent.zip'
        artifactName: 'WebAgent'

    - task: AzureCLI@2
      displayName: 'Upload ZIP to Blob Storage'
      inputs:
        azureSubscription: 'devops-pipeline-sp'
        scriptType: 'ps'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az storage blob upload --account-name "$(StorageAccountName)" --container-name "$(ContainerName)" --file "$(Build.ArtifactStagingDirectory)/WebAgent.zip" --name "dev/WebAgent.zip" --overwrite --auth-mode key --account-key "$(StorageAccountKey)"

  - job: Notify_GoogleChat
    displayName: 'Notificar Resultado del Pipeline a Google Chat'
    dependsOn:
      - Build_Web_Agent
    condition: always()
    steps:
    - checkout: none

    - task: PowerShell@2
      displayName: 'Notificar Éxito a Google Chat'
      condition: succeeded()
      inputs:
        targetType: 'inline'
        script: |
          $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
          $payload = @{
            text = "✅ *Pipeline ySocial-WebAgent-CI-DEV completado exitosamente*.`n🔗 Ver build: $buildUrl"
          } | ConvertTo-Json
          $headers = @{ "Content-Type" = "application/json" }
          Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers

    - task: PowerShell@2
      displayName: 'Notificar Falla a Google Chat'
      condition: failed()
      inputs:
        targetType: 'inline'
        script: |
          $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
          $payload = @{
            text = "❌ *Pipeline ySocial-WebAgent-CI-DEV falló*. Revisar errores.`n🔗 Ver build: $buildUrl"
          } | ConvertTo-Json
          $headers = @{ "Content-Type" = "application/json" }
          Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers  