﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Yoizen.Social.DomainModel;
using System.Threading.Tasks;
using Yoizen.Common;
using System.IO;
using System.Reflection;

namespace Yoizen.Social.DAL
{
	public partial class MessageDAO
	{
		#region Inner Classes

		public class RelatedEntitiesToRead
		{
			#region Properties

			public bool Service { get; set; }
			public bool RepliesTo { get; set; }
			public bool AssociatedMessage { get; set; }
			public bool PostedBy { get; set; }
			public bool AssignedTo { get; set; }
			public bool ShouldBeAssignedTo { get; set; }
			public bool RepliedBy { get; set; }
			public bool Queue { get; set; }
			public bool RepliesToSocialUser { get; set; }
			public bool Attachments { get; set; }
			public bool GroupedMessages { get; set; }
			public bool Chat { get; set; }
			public bool MessageSegments { get; set; }

			#endregion

			#region Constructors

			public RelatedEntitiesToRead()
				: this(true)
			{
			}

			public RelatedEntitiesToRead(bool readAllEntities)
			{
				this.AssociatedMessage = readAllEntities;
				this.PostedBy = readAllEntities;
				this.Queue = readAllEntities;
				this.RepliedBy = readAllEntities;
				this.RepliesTo = readAllEntities;
				this.Service = readAllEntities;
				this.ShouldBeAssignedTo = readAllEntities;
				this.RepliesToSocialUser = readAllEntities;
				this.Attachments = readAllEntities;
				this.GroupedMessages = readAllEntities;
				this.Chat = readAllEntities;
				this.MessageSegments = readAllEntities;
				this.AssignedTo = readAllEntities;
			}

			#endregion
		}

		#endregion

		#region Fields

		private static readonly Dictionary<DomainModel.SocialServiceTypes, DomainModel.ISocialServiceTypeCreator> creators;

		#endregion

		#region Properties

		public static Action<Message> PostProcessMessage { get; set; }

		#endregion

		#region Constructors

		static MessageDAO()
		{
			PostProcessMessage = null;

			creators = new Dictionary<SocialServiceTypes, ISocialServiceTypeCreator>();

			creators[SocialServiceTypes.AppleMessaging] = new Social.AppleMessaging.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Chat] = new Social.Chat.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Facebook] = new Social.Facebook.SocialServiceTypeCreator();
			creators[SocialServiceTypes.FacebookMessenger] = new Social.Facebook.SocialServiceTypeCreator();
			creators[SocialServiceTypes.GoogleMyBusiness] = new Social.GoogleBusiness.SocialServiceTypeCreator();
			creators[SocialServiceTypes.GooglePlay] = new Social.GooglePlay.SocialServiceTypeCreator();
			creators[SocialServiceTypes.GoogleRBM] = new Social.GoogleRBM.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Instagram] = new Social.Instagram.SocialServiceTypeCreator();
			creators[SocialServiceTypes.LinkedIn] = new Social.LinkedIn.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Mail] = new Social.Mail.SocialServiceTypeCreator();
			creators[SocialServiceTypes.MercadoLibre] = new Social.MercadoLibre.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Skype] = new Social.Skype.SocialServiceTypeCreator();
			creators[SocialServiceTypes.SMS] = new Social.SMS.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Telegram] = new Social.Telegram.SocialServiceTypeCreator();
			creators[SocialServiceTypes.Twitter] = new Social.Twitter.SocialServiceTypeCreator();
			creators[SocialServiceTypes.WhatsApp] = new Social.WhatsApp.SocialServiceTypeCreator();
			creators[SocialServiceTypes.YouTube] = new Social.YouTube.SocialServiceTypeCreator();
		}

		public MessageDAO()
		{
		}

		#endregion

		#region Private Methods

		private static void InsertAttachments(IEnumerable<DomainModel.Attachment> attachments, DomainModel.Message message, DbConnection connection, DbTransaction transaction)
		{
			InsertAttachments(attachments, message.ID, message.SocialServiceType, message.Service.ID, connection, transaction);
		}

		private static void InsertAttachments(IEnumerable<DomainModel.Attachment> attachments, long messageId, DomainModel.SocialServiceTypes socialServiceType, int serviceId, DbConnection connection, DbTransaction transaction)
		{
			if (attachments == null || !attachments.Any())
				return;

			using (var cmd = connection.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "Attachments_Insert";
				cmd.Transaction = transaction;

				cmd.AddParameter("MessageID", DbType.Int64, messageId, ParameterDirection.Input);
				var parameterIndex = cmd.AddParameter("Index", DbType.Byte, null, ParameterDirection.Input);
				var parameterSocialID = cmd.AddParameter("SocialID", DbType.String, null, ParameterDirection.Input);
				var parameterMimeType = cmd.AddParameter("MimeType", DbType.String, null, ParameterDirection.Input);
				var parameterFileName = cmd.AddParameter("FileName", DbType.String, null, ParameterDirection.Input);
				var parameterFileSize = cmd.AddParameter("FileSize", DbType.Int32, null, ParameterDirection.Input);
				var parameterParameters = cmd.AddParameter("Parameters", DbType.String, null, ParameterDirection.Input);
				var parameterData = cmd.AddParameter("Data", DbType.Binary, null, ParameterDirection.Input);
				var parameterSocialServiceTypeId = cmd.AddParameter("SocialServiceTypeID", DbType.Int16, null, ParameterDirection.Input);
				var parameterFileSystemRoute = cmd.AddParameter("FileSystemRoute", DbType.String, null, ParameterDirection.Input);
				var parameterEncrypted = cmd.AddParameter("Encrypted", DbType.Boolean, false, ParameterDirection.Input);
				cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

				foreach (var attach in attachments)
				{
					attach.MessageID = messageId;
					attach.SocialServiceType = socialServiceType;

					parameterIndex.Value = attach.Index;
					parameterSocialID.Value = attach.SocialID;
					parameterMimeType.Value = attach.MimeType;
					parameterFileName.Value = attach.FileName;
					parameterFileSize.Value = attach.FileSize;
					parameterSocialServiceTypeId.Value = (int) socialServiceType;
					parameterFileSystemRoute.Value = DomainModel.StorageManager.Instance.SaveAttachment(attach, serviceId);
					if (attach.Parameters == null || attach.Parameters.Count == 0)
						parameterParameters.Value = DBNull.Value;
					else
						parameterParameters.Value = Common.Conversions.ConvertDictionaryToString(attach.Parameters);
					parameterData.Value = DBNull.Value;

					if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
					{
						if (parameterData.Value != null && parameterData.Value != DBNull.Value && attach.Data != null && attach.Data.Length > 0)
							parameterData.Value = Common.Encryption.Encrypt(attach.Data, "Y01z3n");
						parameterEncrypted.Value = true;
						attach.Encrypted = true;
					}

					cmd.ExecuteNonQuery();
				}
			}
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Inserta el mensaje
		/// </summary>
		/// <remarks>
		/// Recordar que al modificar el método, se debe aplicar el cambio en el método InsertAsync
		/// </remarks>
		public static void Insert(Message message)
		{
			DbTransaction tran = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				try
				{
					tran = conn.BeginTransaction();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.CommandText = "Messages_Insert";
						cmd.Transaction = tran;

						cmd.AddParameter("ServiceID", DbType.Int32, message.Service.ID);
						cmd.AddParameter("ServiceTypeID", DbType.Int16, message.ServiceType);
						cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.SocialServiceType);

						if (message.PostedBy != null)
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, message.PostedBy.ID);
						else
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, null);

						cmd.AddParameter("SocialMessageID", DbType.String, message.SocialMessageID);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
						{
							cmd.AddParameter("Body", DbType.String, Common.Encryption.Encrypt(message.Body, "Y01z3n"));
							message.Encrypted = true;
						}
						else
						{
							cmd.AddParameter("Body", DbType.String, message.Body);
						}
						cmd.AddParameter("SentDate", DbType.DateTime, message.Date);
						cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);
						if (message.Parameters != null && message.Parameters.Count > 0)
							cmd.AddParameter("Parameters", DbType.String, Common.Conversions.ConvertDictionaryToString(message.Parameters));
						else
							cmd.AddParameter("Parameters", DbType.String, null);
						cmd.AddParameter("MessageStatusID", DbType.Int16, (short) message.Status);

						if (message.RepliesToSocialMessageID != null)
							cmd.AddParameter("RepliesSocialMessageID", DbType.String, message.RepliesToSocialMessageID);

						cmd.AddParameter("IsDirectMessage", DbType.Boolean, message.IsDirectMessage);
						cmd.AddParameter("Searched", DbType.Boolean, message.Searched);
						cmd.AddParameter("SocialConversationID", DbType.String, message.SocialConversationID);
						cmd.AddParameter("HasAttach", DbType.Boolean, message.HasAttach);
						cmd.AddParameter("Transferred", DbType.Boolean, message.Transferred);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
							cmd.AddParameter("Encrypted", DbType.Boolean, true);

						cmd.AddParameter("HasCoordinates", DbType.Boolean, message.HasCoordinates);
						if (message.HasCoordinates && message.Coordinates != null)
						{
							cmd.AddParameter("Latitude", DbType.Decimal, message.Coordinates.Latitude);
							cmd.AddParameter("Longitude", DbType.Decimal, message.Coordinates.Longitude);
						}
						else
						{
							cmd.AddParameter("Latitude", DbType.Decimal, null);
							cmd.AddParameter("Longitude", DbType.Decimal, null);
						}

						cmd.AddParameter("EmptyBody", DbType.Boolean, message.EmptyBody);
						cmd.AddParameter("ProcessingPush", DbType.Boolean, false);
						cmd.AddParameter("EventType", DbType.Byte, (short?) message.EventType);
						cmd.AddParameter("Important", DbType.Boolean, message.Important);

						var parameterId = cmd.AddParameter("MessageID", DbType.Int64, null, ParameterDirection.Output);

						cmd.ExecuteNonQuery();

						message.UpdateID(Convert.ToInt64(parameterId.Value));
					}

					if (message.HasAttach)
						InsertAttachments(message.Attachments, message, conn, tran);

					if (message.SocialServiceType == SocialServiceTypes.Chat && message.Chat != null)
					{
						message.Chat.MessageID = message.ID;

						ChatDAO.Insert(message.Chat, message, conn, tran);
					}

					if (message.SocialServiceType != SocialServiceTypes.Chat ||
						!message.PostedBy.Anonymous)
					{
						using (var cmd = conn.CreateCommand())
						{
							cmd.CommandType = CommandType.StoredProcedure;
							cmd.CommandText = "SocialUsers_UpdateInteractionDate";
							cmd.Transaction = tran;

							message.PostedBy.PreviousLastInteractionDate = message.PostedBy.LastInteractionDate;
							message.PostedBy.LastInteractionDate = DateTime.Now;

							cmd.AddParameter("SocialUserID", DbType.Int64, message.PostedBy.ID);
							cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.PostedBy.SocialServiceType);
							cmd.AddParameter("LastInteractionDate", DbType.DateTime, message.PostedBy.LastInteractionDate);
							cmd.AddParameter("PreviousLastInteractionDate", DbType.DateTime, message.PostedBy.PreviousLastInteractionDate);
							cmd.AddParameter("SocialUserProfileID", DbType.Int32, (short) message.PostedBy.Profile?.ID);

							cmd.ExecuteNonQuery();
						}

						using (var cmd = conn.CreateCommand())
						{
							cmd.CommandType = CommandType.StoredProcedure;
							cmd.CommandText = "SocialUsersServices_UpdateInteractionDate";
							cmd.Transaction = tran;

							cmd.AddParameter("SocialUserID", DbType.Int64, message.PostedBy.ID);
							cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.PostedBy.SocialServiceType);
							cmd.AddParameter("ServiceID", DbType.Int16, (short) message.Service.ID);
							cmd.AddParameter("LastInteraction", DbType.DateTime, message.PostedBy.ParametersByService[message.Service.ID].LastInteraction);
							cmd.AddParameter("PreviousLastInteraction", DbType.DateTime, message.PostedBy.ParametersByService[message.Service.ID].PreviousLastInteraction);

							cmd.ExecuteNonQuery();
						}
					}

					tran.Commit();
				}
				catch
				{
					if (tran != null)
						tran.Rollback();

					throw;
				}
				finally
				{
					if (tran != null)
						tran.Dispose();
				}
			}
		}

		/// <summary>
		/// Inserta el mensaje.
		/// </summary>
		/// <remarks>
		/// Recordar que al modificar el método, se debe debe aplicar el cambio en el método Insert
		/// </remarks>
		public static async Task InsertAsync(Message message)
		{
			DbTransaction tran = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				try
				{
					tran = conn.BeginTransaction();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.CommandText = "Messages_Insert";
						cmd.Transaction = tran;

						cmd.AddParameter("ServiceID", DbType.Int32, message.Service.ID);
						cmd.AddParameter("ServiceTypeID", DbType.Int16, message.ServiceType);
						cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.SocialServiceType);

						if (message.PostedBy != null)
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, message.PostedBy.ID);
						else
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, null);

						cmd.AddParameter("SocialMessageID", DbType.String, message.SocialMessageID);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
						{
							cmd.AddParameter("Body", DbType.String, Common.Encryption.Encrypt(message.Body, "Y01z3n"));
							message.Encrypted = true;
						}
						else
						{
							cmd.AddParameter("Body", DbType.String, message.Body);
						}
						cmd.AddParameter("SentDate", DbType.DateTime, message.Date);
						cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);
						if (message.Parameters != null && message.Parameters.Count > 0)
							cmd.AddParameter("Parameters", DbType.String, Common.Conversions.ConvertDictionaryToString(message.Parameters));
						else
							cmd.AddParameter("Parameters", DbType.String, null);
						cmd.AddParameter("MessageStatusID", DbType.Int16, (short) message.Status);

						if (message.RepliesToSocialMessageID != null)
							cmd.AddParameter("RepliesSocialMessageID", DbType.String, message.RepliesToSocialMessageID);

						cmd.AddParameter("IsDirectMessage", DbType.Boolean, message.IsDirectMessage);
						cmd.AddParameter("Searched", DbType.Boolean, message.Searched);
						cmd.AddParameter("SocialConversationID", DbType.String, message.SocialConversationID);
						cmd.AddParameter("HasAttach", DbType.Boolean, message.HasAttach);
						cmd.AddParameter("Transferred", DbType.Boolean, message.Transferred);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
							cmd.AddParameter("Encrypted", DbType.Boolean, true);

						cmd.AddParameter("HasCoordinates", DbType.Boolean, message.HasCoordinates);
						if (message.HasCoordinates && message.Coordinates != null)
						{
							cmd.AddParameter("Latitude", DbType.Decimal, message.Coordinates.Latitude);
							cmd.AddParameter("Longitude", DbType.Decimal, message.Coordinates.Longitude);
						}
						else
						{
							cmd.AddParameter("Latitude", DbType.Decimal, null);
							cmd.AddParameter("Longitude", DbType.Decimal, null);
						}

						cmd.AddParameter("EmptyBody", DbType.Boolean, message.EmptyBody);
						cmd.AddParameter("ProcessingPush", DbType.Boolean, false);
						cmd.AddParameter("EventType", DbType.Byte, (short?) message.EventType);
						cmd.AddParameter("Important", DbType.Boolean, message.Important);

						if (message.EventType != null && message.EventType == Message.MessageEventTypes.YFlowPendingMessage)
						{
							cmd.AddParameter("CaseID", DbType.Int64, message.Case?.ID);
						}

						var parameterId = cmd.AddParameter("MessageID", DbType.Int64, null, ParameterDirection.Output);

						await cmd.ExecuteNonQueryAsync();

						message.UpdateID(Convert.ToInt64(parameterId.Value));
					}

					if (message.HasAttach)
						InsertAttachments(message.Attachments, message, conn, tran);

					if (message.SocialServiceType == SocialServiceTypes.Chat && message.Chat != null)
					{
						message.Chat.MessageID = message.ID;

						ChatDAO.Insert(message.Chat, message, conn, tran);
					}

					if (message.PostedBy != null &&
						(
							message.SocialServiceType != SocialServiceTypes.Chat ||
							!message.PostedBy.Anonymous
						)
					)
					{
						using (var cmd = conn.CreateCommand())
						{
							cmd.CommandType = CommandType.StoredProcedure;
							cmd.CommandText = "SocialUsers_UpdateInteractionDate";
							cmd.Transaction = tran;

							message.PostedBy.PreviousLastInteractionDate = message.PostedBy.LastInteractionDate;
							message.PostedBy.LastInteractionDate = DateTime.Now;

							cmd.AddParameter("SocialUserID", DbType.Int64, message.PostedBy.ID);
							cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.PostedBy.SocialServiceType);
							cmd.AddParameter("LastInteractionDate", DbType.DateTime, message.PostedBy.LastInteractionDate);
							cmd.AddParameter("PreviousLastInteractionDate", DbType.DateTime, message.PostedBy.PreviousLastInteractionDate);
							cmd.AddParameter("SocialUserProfileID", DbType.Int32, (short) message.PostedBy.Profile?.ID);

							await cmd.ExecuteNonQueryAsync();
						}

						if (message.PostedBy.ParametersByService.ContainsKey(message.Service.ID))
						{
							using (var cmd = conn.CreateCommand())
							{
								cmd.CommandType = CommandType.StoredProcedure;
								cmd.CommandText = "SocialUsersServices_UpdateInteractionDate";
								cmd.Transaction = tran;

								cmd.AddParameter("SocialUserID", DbType.Int64, message.PostedBy.ID);
								cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.PostedBy.SocialServiceType);
								cmd.AddParameter("ServiceID", DbType.Int16, (short) message.Service.ID);
								cmd.AddParameter("LastInteraction", DbType.DateTime, message.PostedBy.ParametersByService[message.Service.ID].LastInteraction);
								cmd.AddParameter("PreviousLastInteraction", DbType.DateTime, message.PostedBy.ParametersByService[message.Service.ID].PreviousLastInteraction);

								await cmd.ExecuteNonQueryAsync();
							}
						}
					}

					tran.Commit();
				}
				catch
				{
					if (tran != null)
						tran.Rollback();

					throw;
				}
				finally
				{
					if (tran != null)
						tran.Dispose();
				}
			}
		}

		public static void UpdateMessagesOfTask(int taskId, IEnumerable<long> messageIds)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateMessagesOfTask";

					cmd.AddParameter("TaskID", DbType.Int32, taskId);
					cmd.AddEnumerationParameter("MessageIDs", messageIds);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdateAnswerInfoFromHsm(long messageId, DomainModel.Whatsapp.HSMAnswerTypes answerType, string body, out bool alreadyProcessed)
		{
			alreadyProcessed = false;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "MessagesPayment_UpdateAnswerInfoFromHsm";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("AnswerType", DbType.Byte, (byte) answerType);
					cmd.AddParameter("AnswerText", DbType.String, body);
					var exists = cmd.AddParameter("AlreadyUpdated", DbType.Boolean, DBNull.Value, ParameterDirection.Output);

					cmd.ExecuteNonQuery();

					alreadyProcessed = (bool) exists.Value;
				}
			}
		}

		/// <summary>
		/// Inserta el mensaje
		/// </summary>
		public static void InsertMissing(Message message)
		{
			DbTransaction tran = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				try
				{
					tran = conn.BeginTransaction();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.CommandText = "Messages_InsertMissing";
						cmd.Transaction = tran;

						cmd.AddParameter("ServiceID", DbType.Int32, message.Service.ID);
						cmd.AddParameter("ServiceTypeID", DbType.Int16, message.ServiceType);
						cmd.AddParameter("SocialServiceTypeID", DbType.Int16, (short) message.SocialServiceType);

						if (message.PostedBy != null)
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, message.PostedBy.ID);
						else
							cmd.AddParameter("PostedBySocialUserID", DbType.Int64, null);

						cmd.AddParameter("SocialMessageID", DbType.String, message.SocialMessageID);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
						{
							cmd.AddParameter("Body", DbType.String, Common.Encryption.Encrypt(message.Body, "Y01z3n"));
							message.Encrypted = true;
						}
						else
						{
							cmd.AddParameter("Body", DbType.String, message.Body);
						}
						cmd.AddParameter("SentDate", DbType.DateTime, message.Date);
						cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);
						if (message.Parameters != null && message.Parameters.Count > 0)
							cmd.AddParameter("Parameters", DbType.String, Common.Conversions.ConvertDictionaryToString(message.Parameters));
						else
							cmd.AddParameter("Parameters", DbType.String, null);
						cmd.AddParameter("MessageStatusID", DbType.Int16, (short) message.Status);
						cmd.AddParameter("IsReply", DbType.Boolean, message.IsReply);

						if (message.RepliesToSocialMessageID != null)
							cmd.AddParameter("RepliesSocialMessageID", DbType.String, message.RepliesToSocialMessageID);

						cmd.AddParameter("IsDirectMessage", DbType.Boolean, message.IsDirectMessage);
						cmd.AddParameter("Searched", DbType.Boolean, message.Searched);
						cmd.AddParameter("Read", DbType.Boolean, message.Read);
						cmd.AddParameter("SocialConversationID", DbType.String, message.SocialConversationID);
						cmd.AddParameter("HasAttach", DbType.Boolean, message.HasAttach);

						if (Licensing.LicenseManager.Instance.License.Configuration.EncryptMessages && DomainModel.SystemSettings.Instance.EncryptMessages)
							cmd.AddParameter("Encrypted", DbType.Boolean, true);

						cmd.AddParameter("HasCoordinates", DbType.Boolean, message.HasCoordinates);
						if (message.HasCoordinates && message.Coordinates != null)
						{
							cmd.AddParameter("Latitude", DbType.Decimal, message.Coordinates.Latitude);
							cmd.AddParameter("Longitude", DbType.Decimal, message.Coordinates.Longitude);
						}
						else
						{
							cmd.AddParameter("Latitude", DbType.Decimal, null);
							cmd.AddParameter("Longitude", DbType.Decimal, null);
						}

						cmd.AddParameter("EmptyBody", DbType.Boolean, message.EmptyBody);
						cmd.AddParameter("Incoming", DbType.Boolean, message.Incoming);

						var parameterId = cmd.AddParameter("MessageID", DbType.Int64, null, ParameterDirection.Output);

						cmd.ExecuteNonQuery();

						message.UpdateID(Convert.ToInt64(parameterId.Value));
					}

					if (message.HasAttach)
						InsertAttachments(message.Attachments, message, conn, tran);

					tran.Commit();
				}
				catch
				{
					if (tran != null)
						tran.Rollback();

					throw;
				}
				finally
				{
					if (tran != null)
						tran.Dispose();
				}
			}
		}

		#endregion

		#region Private Static Methods

		internal static Message Create(System.Data.Common.DbDataReader reader)
		{
			if (reader == null || reader.IsClosed)
				return new Message();

			return Create((IDataRecord) reader);
		}

		internal static Message Create(System.Data.IDataRecord record)
		{
			if (record == null)
				return new Message();

			Message message = null;
			try
			{
				var type = Message.GetSocialServiceTypeForRecord(record);
				if (creators.ContainsKey(type))
					message = creators[type].CreateMessage(record);
			}
			catch { }

			if (message == null)
				message = new Message(record);

			if (PostProcessMessage != null)
				PostProcessMessage(message);

			return message;
		}

		private static void ReadRelatedInfo(Message message, RelatedEntitiesToRead relatedEntitiesToRead, bool recurse)
		{
			if (relatedEntitiesToRead == null)
				relatedEntitiesToRead = new RelatedEntitiesToRead();

			if (relatedEntitiesToRead.Service && message.Service != null)
				message.Service = ServiceDAO.GetOneFromCache(message.Service.ID);

			if (recurse && relatedEntitiesToRead.RepliesTo && message.RepliesTo != null)
				message.RepliesTo = GetOne(message.RepliesTo.ID, relatedEntitiesToRead, false);

			if (relatedEntitiesToRead.AssociatedMessage && message.AssociatedMessage != null)
				message.AssociatedMessage = GetOne(message.AssociatedMessage.ID, relatedEntitiesToRead);

			if (relatedEntitiesToRead.PostedBy && message.PostedBy != null)
			{
				message.PostedBy = SocialUserDAO.GetOne(message.PostedBy.ID, message.SocialServiceType, false, true);
			}

			if (relatedEntitiesToRead.RepliesToSocialUser && message.RepliesToSocialUser != null)
			{
				message.RepliesToSocialUser = SocialUserDAO.GetOne(message.RepliesToSocialUser.ID, message.SocialServiceType, false);
			}

			if (relatedEntitiesToRead.ShouldBeAssignedTo && message.ShouldBeAssignedTo != null)
			{
				int id = message.ShouldBeAssignedTo.ID;
				if (DomainModel.Cache.Instance.Enabled)
				{
					if (Cache.Instance.ExistsItem<Agent>(id))
						message.RepliedBy = Cache.Instance.GetItem<Agent>(id);
					else if (Cache.Instance.ExistsItem<AgentDeleted>(id))
						message.RepliedBy = Cache.Instance.GetItem<AgentDeleted>(id);
				}
				else
				{
					message.ShouldBeAssignedTo = AgentDAO.GetOneFromCache(id);
					if (message.ShouldBeAssignedTo == null)
					{
						// Entra acá cuando el agente fue borrado
						message.ShouldBeAssignedTo = AgentDAO.GetOneDeleted(id);
					}
				}
			}

			if (relatedEntitiesToRead.AssignedTo && message.AssignedTo != null)
			{
				int id = message.AssignedTo.ID;
				message.AssignedTo = AgentDAO.GetOneFromCache(id);
				if (message.AssignedTo == null)
				{
					// Entra acá cuando el agente fue borrado
					message.AssignedTo = AgentDAO.GetOneDeleted(id);
				}
			}

			if (relatedEntitiesToRead.RepliedBy && message.RepliedBy != null && string.IsNullOrEmpty(message.RepliedBy.UserName))
			{
				int id = message.RepliedBy.ID;

				if (DomainModel.Cache.Instance.Enabled)
				{
					if (Cache.Instance.ExistsItem<Agent>(id))
						message.RepliedBy = Cache.Instance.GetItem<Agent>(id);
					else if (Cache.Instance.ExistsItem<User>(id))
						message.RepliedBy = Cache.Instance.GetItem<User>(id);
					else if (Cache.Instance.ExistsItem<AgentDeleted>(id))
						message.RepliedBy = Cache.Instance.GetItem<AgentDeleted>(id);
				}
				else
				{
					var agent = AgentDAO.GetOneFromCache(id);
					if (agent != null)
					{
						message.RepliedBy = agent;
					}
					else
					{
						var user = UserDAO.GetOneFromCache(id);
						if (user != null)
							message.RepliedBy = user;
					}
				}

				if (message.RepliedBy == null)
				{
					// Entra acá cuando la persona fue borrada
					message.RepliedBy = AgentDAO.GetOneDeleted(id);

					if (message.RepliedBy == null)
					{
						// Entra acá cuando la persona fue borrada
						message.RepliedBy = UserDAO.GetOneDeleted(id);
					}
				}
			}

			if (relatedEntitiesToRead.Queue && message.Queue != null)
				message.Queue = QueueDAO.GetOneFromCache(message.Queue.ID);

			if (relatedEntitiesToRead.Attachments && message.HasAttach)
			{
				message.Attachments = AttachmentDAO.GetAllByMessage(message.ID);

				if (message.SocialServiceType == SocialServiceTypes.Mail)
					message.ReprocessInlineAttachmentsInBody();
			}

			if (relatedEntitiesToRead.GroupedMessages && message.IsGrouping)
				message.Groups = GetAllGrouped(message).ToArray();

			if (relatedEntitiesToRead.Chat && message.SocialServiceType == SocialServiceTypes.Chat)
				message.Chat = ChatDAO.GetOneByMessageID(message.ID);

			if (relatedEntitiesToRead.MessageSegments && 
				message.SocialVersion > 1 &&
				message.NumberOfSegments > 0)
			{
				message.MessageSegments = MessageSegmentDAO.GetAllByMessage(message.ID);
				if (message.MessageSegments.Count > 0)
				{
					var firstSegment = message.MessageSegments[0];
					if (message.EnqueuedDate == null)
						message.EnqueuedDate = firstSegment.EnqueuedDate;
					if (message.AssignedDate == null)
						message.AssignedDate = firstSegment.AssignedDate;
					if (message.ReadDate == null)
						message.ReadDate = firstSegment.ReadDate;
					if (message.FinishedReadDate == null)
						message.FinishedReadDate = firstSegment.FinishedReadDate;
				}
			}
		}

		#endregion

		#region Public Static Methods

		public static List<Message> GetAllByTask(int taskId, long? fromMessageId, int recordCount, out bool moreMessagesAvailable)
		{
			moreMessagesAvailable = false;

			var messages = new List<Message>();
			using (var conn = DbManager.CreateConnection(true))
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllByTask";

					cmd.AddParameter("TaskID", DbType.Int32, taskId);
					cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
					cmd.AddParameter("RecordCount", DbType.Int32, recordCount + 1);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							if (messages.Count >= recordCount)
							{
								moreMessagesAvailable = true;
								break;
							}

							var message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			return messages;
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.MessagesExport"/> con los filtros</param>
		/// <param name="fromMessageId">Indica el código de mensaje desde el cual se deberán traer datos o null para todos los registros</param>
		/// <param name="recordCount">Indica la cantidad de registros que devolverá la consulta o null para traer todos los registros</param>
		/// <param name="moreMessagesAvailable">Cuando retorna, devuelva si existen más registros disponibles para la consulta paginada (siempre y cuando <paramref name="recordCount"/> no sea null; en caso de ser null devuelve null)</param>
		/// <param name="totalRecords">Cuando retorna, devuelve la cantidad de registros que devolvería la consulta (siempre y cuando <paramref name="recordCount"/> no sea null; en caso de ser null devuelve null)</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> Search(DomainModel.Reports.Export.MessagesExport filters, long? fromMessageId, int? recordCount, out bool? moreMessagesAvailable)
		{
			if (recordCount == null)
			{
				moreMessagesAvailable = null;
			}
			else
			{
				moreMessagesAvailable = false;
			}

			var messages = new Dictionary<long, Message>();
			using (var conn = DbManager.CreateConnection(true))
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_Search";
					cmd.CommandTimeout = TimeSpan.FromMinutes(10).Seconds;

					cmd.AddParameter("DateType", DbType.Byte, filters.DateType);
					cmd.AddParameter("FromDate", DbType.DateTime, filters.From);
					cmd.AddParameter("ToDate", DbType.DateTime, filters.To);
					cmd.AddEnumerationParameter("Services", filters.Services);
					cmd.AddEnumerationParameter("SocialServiceTypes", filters.SocialServiceTypes);
					cmd.AddEnumerationParameter("Queues", filters.Queues);
					cmd.AddParameter("OnlyVIPs", DbType.Boolean, filters.OnlyVIPs);
					cmd.AddParameter("SocialUserProfileId", DbType.Int32, filters.SocialUserProfileID);
					cmd.AddParameter(filters.SocialUser);
					cmd.AddEnumerationParameter("Agents", filters.Agents);
					cmd.AddEnumerationParameter("AgentGroups", filters.AgentGroups);
					cmd.AddParameter("IncomingMessage", DbType.Boolean, filters.IncomingMessage);
					if (filters.IncomingMessage == null || filters.IncomingMessage == false)
					{
						cmd.AddParameter("MessageStatusId", DbType.Int16, null);
						cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
					}
					else
					{
						if (filters.StatusId == null)
						{
							cmd.AddParameter("MessageStatusId", DbType.Int16, null);
							cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
						}
						else
						{
							cmd.AddParameter("MessageStatusId", DbType.Int16, (short) filters.StatusId.Value);
							if (filters.StatusId.Value == MessageStatuses.Discarded &&
								filters.DiscardSource != null)
								cmd.AddParameter("DiscardSourceId", DbType.Int16, (short) filters.DiscardSource.Value);
							else
								cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
						}
					}
					cmd.AddParameter("Body", DbType.String, filters.Body);
					cmd.AddParameter("PrivateMessage", DbType.Boolean, filters.PrivateMessage);
					cmd.AddParameter("Read", DbType.Boolean, filters.Read);
					cmd.AddParameter("HasAttach", DbType.Boolean, filters.HasAttach);
					cmd.AddParameter("HasCoordinates", DbType.Boolean, filters.WithCoordinates);

					if (recordCount == null)
						cmd.AddParameter("RecordCount", DbType.Int32, null);
					else
						cmd.AddParameter("RecordCount", DbType.Int32, recordCount.Value + 1);

					cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
					cmd.AddParameter("IncludeCase", DbType.Boolean, filters.IncludeCase);
					cmd.AddParameter("IncludeBody", DbType.Boolean, filters.IncludeBody);
					cmd.AddParameter("IncludeDiscardReason", DbType.Boolean, filters.IncludeDiscardReason);
					cmd.AddParameter("ExcludeSocialUserInfo", DbType.Boolean, filters.ExcludeSocialUserInfo);
					cmd.AddParameter("SystemTimeOperator", DbType.String, filters.SystemTimeOperator);

					if (filters.SystemTime == null)
						cmd.AddParameter("SystemTime", DbType.Int32, null);
					else
						cmd.AddParameter("SystemTime", DbType.Int32, filters.SystemTime.Value * 60);

					cmd.AddParameter("MessagesID", DbType.String, filters.MessagesID);
					cmd.AddParameter("CasesID", DbType.String, filters.CasesID);
					cmd.AddParameter("Classified", DbType.Boolean, filters.Classified);
					if (filters.Classified == true)
						cmd.AddEnumerationParameter("ContactReasons", filters.ContactReasons);

					using (var reader = cmd.ExecuteReader())
					{
						int repeatedMessages = 0;
						while (reader.Read())
						{
							if (recordCount != null && (messages.Count + repeatedMessages) >= recordCount.Value)
							{
								moreMessagesAvailable = true;
								break;
							}

							var message = Create(reader);
							if (messages.ContainsKey(message.ID))
								repeatedMessages++;
							else
								messages.Add(message.ID, message);
						}
					}
				}
			}

			foreach (var message in messages)
			{
				if (message.Value.IsGrouped && message.Value.GroupedBy != null && messages.ContainsKey(message.Value.GroupedBy.ID))
				{
					message.Value.GroupedBy = messages[message.Value.GroupedBy.ID];
				}

				//	ReadRelatedInfo(message);
			}

			return messages.Values.ToList();
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.DetailedWhatsappHSMExport"/> con los filtros</param>
		/// <param name="fromMessageId">Indica el código de mensaje desde el cual se deberán traer datos o null para todos los registros</param>
		/// <param name="recordCount">Indica la cantidad de registros que devolverá la consulta o null para traer todos los registros</param>
		/// <param name="moreMessagesAvailable">Cuando retorna, devuelva si existen más registros disponibles para la consulta paginada (siempre y cuando <paramref name="recordCount"/> no sea null; en caso de ser null devuelve null)</param>
		/// <param name="totalRecords">Cuando retorna, devuelve la cantidad de registros que devolvería la consulta (siempre y cuando <paramref name="recordCount"/> no sea null; en caso de ser null devuelve null)</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> SearchByPayment(DomainModel.Reports.Export.DetailedWhatsappHSMExport filters, long? fromMessageId, int? recordCount, out bool? moreMessagesAvailable, out int? totalRecords)
		{
			if (recordCount == null)
			{
				moreMessagesAvailable = null;
				totalRecords = null;
			}
			else
			{
				totalRecords = 0;
				moreMessagesAvailable = false;
			}

			var messages = new Dictionary<long, Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_SearchByPayment";
					cmd.CommandTimeout = TimeSpan.FromMinutes(10).Seconds;

					cmd.AddParameter("FromDate", DbType.DateTime, filters.From);
					cmd.AddParameter("ToDate", DbType.DateTime, filters.To);
					cmd.AddEnumerationParameter("Services", filters.Services);
					cmd.AddParameter("SocialUserProfileId", DbType.Int32, filters.SocialUserProfileID);
					cmd.AddParameter(filters.SocialUser);
					cmd.AddEnumerationParameter("Agents", filters.Agents);
					cmd.AddEnumerationParameter("Users", filters.Users);
					cmd.AddEnumerationParameter("Templates", filters.Templates);
					if (recordCount == null)
						cmd.AddParameter("RecordCount", DbType.Int32, null);
					else
						cmd.AddParameter("RecordCount", DbType.Int32, recordCount.Value);

					cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
					cmd.AddParameter("IncludeCase", DbType.Boolean, filters.IncludeCase);
					cmd.AddParameter("IncludeBody", DbType.Boolean, filters.IncludeBody);
					cmd.AddParameter("ExcludeSocialUserInfo", DbType.Boolean, filters.ExcludeSocialUserInfo);
					cmd.AddEnumerationParameter("Tasks", filters.Tasks);

					using (var reader = cmd.ExecuteReader())
					{
						int repeatedMessages = 0;
						while (reader.Read())
						{
							try
							{
								totalRecords = Convert.ToInt32(reader["TotalRecords"]);
							}
							catch { }

							if (recordCount != null && (messages.Count + repeatedMessages) >= recordCount.Value)
							{
								moreMessagesAvailable = true;
								break;
							}

							Message message = Create(reader);
							if (messages.ContainsKey(message.ID))
								repeatedMessages++;
							else
								messages.Add(message.ID, message);
						}
					}
				}
			}

			return messages.Values.ToList();
		}

		public static MessageDataReader SearchByPayment(DomainModel.Reports.Export.DetailedWhatsappHSMExport filters, long? fromMessageId, int? recordCount)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "Messages_SearchByPayment";
				cmd.CommandTimeout = TimeSpan.FromMinutes(10).Seconds;

				cmd.AddParameter("FromDate", DbType.DateTime, filters.From);
				cmd.AddParameter("ToDate", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Services", filters.Services);
				cmd.AddParameter("SocialUserProfileId", DbType.Int32, filters.SocialUserProfileID);
				cmd.AddParameter(filters.SocialUser);
				cmd.AddEnumerationParameter("Agents", filters.Agents);
				cmd.AddEnumerationParameter("Users", filters.Users);
				cmd.AddEnumerationParameter("Templates", filters.Templates);
				if (recordCount == null)
					cmd.AddParameter("RecordCount", DbType.Int32, null);
				else
					cmd.AddParameter("RecordCount", DbType.Int32, recordCount.Value);
				cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
				cmd.AddParameter("IncludeCase", DbType.Boolean, filters.IncludeCase);
				cmd.AddParameter("IncludeBody", DbType.Boolean, filters.IncludeBody);
				cmd.AddParameter("ExcludeSocialUserInfo", DbType.Boolean, filters.ExcludeSocialUserInfo);
				cmd.AddEnumerationParameter("Tasks", filters.Tasks);

				return new MessageDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static IEnumerable<Message> GetPreviousOfCase(long caseId, long fromMessageId, int recordCount)
		{
			List<Message> messages = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetPreviousOfCase";

					cmd.AddParameter("CaseID", DbType.Int64, caseId);
					cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
					cmd.AddParameter("RecordCount", DbType.Int32, recordCount);
					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							var message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			return messages.OrderBy(m => m.ID);
		}

		public static MessageDataReader Search(DateTime fromDate, DateTime toDate, int[] queues)
		{
			DomainModel.Reports.Export.MessagesExport filters = new DomainModel.Reports.Export.MessagesExport();
			filters.From = fromDate;
			filters.To = toDate;
			filters.Queues = queues;
			filters.IncludeBody = true;
			filters.ExcludeSocialUserInfo = false;

			return Search(filters, null, null);
		}

		public static MessageDataReader Search(DomainModel.Reports.Export.MessagesExport filters, long? fromMessageId, int? recordCount)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "Messages_Search";
				cmd.CommandTimeout = TimeSpan.FromMinutes(10).Seconds;

				cmd.AddParameter("DateType", DbType.Byte, filters.DateType);
				cmd.AddParameter("FromDate", DbType.DateTime, filters.From);
				cmd.AddParameter("ToDate", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Services", filters.Services);
				cmd.AddEnumerationParameter("SocialServiceTypes", filters.SocialServiceTypes);
				cmd.AddEnumerationParameter("Queues", filters.Queues);
				cmd.AddParameter("OnlyVIPs", DbType.Boolean, filters.OnlyVIPs);
				cmd.AddParameter("SocialUserProfileId", DbType.Int32, filters.SocialUserProfileID);
				cmd.AddParameter(filters.SocialUser);
				cmd.AddEnumerationParameter("Agents", filters.Agents);
				cmd.AddEnumerationParameter("AgentGroups", filters.AgentGroups);
				cmd.AddParameter("IncomingMessage", DbType.Boolean, filters.IncomingMessage);
				if (filters.IncomingMessage == null || filters.IncomingMessage == false)
				{
					cmd.AddParameter("MessageStatusId", DbType.Int16, null);
					cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
				}
				else
				{
					if (filters.StatusId == null)
					{
						cmd.AddParameter("MessageStatusId", DbType.Int16, null);
						cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
					}
					else
					{
						cmd.AddParameter("MessageStatusId", DbType.Int16, (short) filters.StatusId.Value);
						if (filters.StatusId.Value == MessageStatuses.Discarded &&
							filters.DiscardSource != null)
							cmd.AddParameter("DiscardSourceId", DbType.Int16, (short) filters.DiscardSource.Value);
						else
							cmd.AddParameter("DiscardSourceId", DbType.Int16, null);
					}
				}
				cmd.AddParameter("Body", DbType.String, filters.Body);
				cmd.AddParameter("PrivateMessage", DbType.Boolean, filters.PrivateMessage);
				cmd.AddParameter("Read", DbType.Boolean, filters.Read);
				cmd.AddParameter("HasAttach", DbType.Boolean, filters.HasAttach);
				cmd.AddParameter("HasCoordinates", DbType.Boolean, filters.WithCoordinates);
				if (recordCount == null)
					cmd.AddParameter("RecordCount", DbType.Int32, null);
				else
					cmd.AddParameter("RecordCount", DbType.Int32, recordCount.Value + 1);
				cmd.AddParameter("FromMessageId", DbType.Int64, fromMessageId);
				cmd.AddParameter("IncludeCase", DbType.Boolean, filters.IncludeCase);
				cmd.AddParameter("IncludeBody", DbType.Boolean, filters.IncludeBody);
				cmd.AddParameter("IncludeDiscardReason", DbType.Boolean, filters.IncludeDiscardReason);
				cmd.AddParameter("ExcludeSocialUserInfo", DbType.Boolean, filters.ExcludeSocialUserInfo);
				cmd.AddParameter("SystemTimeOperator", DbType.String, filters.SystemTimeOperator);
				if (filters.SystemTime == null)
					cmd.AddParameter("SystemTime", DbType.Int32, null);
				else
					cmd.AddParameter("SystemTime", DbType.Int32, filters.SystemTime.Value * 60);
				cmd.AddParameter("MessagesID", DbType.String, filters.MessagesID);
				cmd.AddParameter("CasesID", DbType.String, filters.CasesID);
				cmd.AddParameter("Classified", DbType.Boolean, filters.Classified);
				if (filters.Classified == true)
					cmd.AddEnumerationParameter("ContactReasons", filters.ContactReasons);

				return new MessageDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		/// <summary>
		/// Obtiene todos los mensajes de un caso
		/// </summary>
		/// <param name="case">El caso</param>
		/// <param name="maxIndex">El índice máximo del mensaje a traer</param>
		/// <param name="complete">Indica si se traerá toda la información de todas las entidades relacionadas</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes del caso</returns>
		public static List<Message> GetAllByCase(Case @case, bool complete)
		{
			List<Message> messages = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllByCase";

					cmd.AddParameter("CaseID", DbType.Int64, @case.ID);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							Message message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			if (complete)
			{
				RelatedEntitiesToRead entitiesToRead = new RelatedEntitiesToRead(false)
				{
					//Attachments = true,
					RepliedBy = true,
					//MessageSegments = true,
					//Chat = true,
					AssignedTo = true
				};
				foreach (var message in messages)
					ReadRelatedInfo(message, entitiesToRead, false);
			}

			return messages;
		}

		/// <summary>
		/// Obtiene un mensaje dado su código
		/// </summary>
		/// <param name="id">El código del mensaje</param>
		/// <returns>Un <see cref="Message"/> o null si no se encuentra el código</returns>
		public static Message GetOne(long id)
		{
			return GetOne(id, new RelatedEntitiesToRead(), true);
		}

		/// <summary>
		/// Obtiene un mensaje dado su código
		/// </summary>
		/// <param name="id">El código del mensaje</param>
		/// <param name="relatedEntitiesToRead">Indica las entidades relacionadas que serán obtenidas en forma completa</param>
		/// <returns>Un <see cref="Message"/> o null si no se encuentra el código</returns>
		public static Message GetOne(long id, RelatedEntitiesToRead relatedEntitiesToRead)
		{
			return GetOne(id, relatedEntitiesToRead, true);
		}

		/// <summary>
		/// Obtiene un mensaje dado su código
		/// </summary>
		/// <param name="id">El código del mensaje</param>
		/// <returns>Un <see cref="Message"/> o null si no se encuentra el código</returns>
		public static Message GetOne(long id, bool recurse)
		{
			return GetOne(id, new RelatedEntitiesToRead(), recurse);
		}

		/// <summary>
		/// Obtiene un mensaje dado su código
		/// </summary>
		/// <param name="id">El código del mensaje</param>
		/// <param name="relatedEntitiesToRead">Indica las entidades relacionadas que serán obtenidas en forma completa</param>
		/// <param name="recurse">Indica si se leerá los datos de los mensajes relacionados hasta llegar al primero</param>
		/// <returns>Un <see cref="Message"/> o null si no se encuentra el código</returns>
		public static Message GetOne(long id, RelatedEntitiesToRead relatedEntitiesToRead, bool recurse)
		{
			if (relatedEntitiesToRead == null)
				relatedEntitiesToRead = new RelatedEntitiesToRead();

			Message message = null;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetOne";

					cmd.AddParameter("MessageID", DbType.Int64, id);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							message = Create(reader);
						}
					}
				}
			}

			if (message != null)
				ReadRelatedInfo(message, relatedEntitiesToRead, recurse);

			return message;
		}

		/// <summary>
		/// Obtiene un mensaje dado su código de red social y el tipo de red social a la que pertenece
		/// </summary>
		/// <param name="socialMessageId">El código del mensaje de red social</param>
		/// <param name="type">Indica el tipo de red social</param>
		/// <returns>Un <see cref="Message"/> o null si no se encuentra el código</returns>
		public static Message GetOne(string socialMessageId, SocialServiceTypes type)
		{
			if (!DomainModel.StorageManager.Instance.ExistsMessage(socialMessageId, type, out long? messageId))
				return null;

			return GetOne(messageId.Value, new RelatedEntitiesToRead(false)
			{
				PostedBy = true
			}, false);
		}

		public static long? GetCaseId(long messageId)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetCaseID";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);

					var value = cmd.ExecuteScalar();

					if (value != DBNull.Value)
						return Convert.ToInt64(value);
				}
			}

			return null;
		}

		public static DomainModel.Message GetFirstOfCase(long caseId)
		{
			Message message = null;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetFirstOfCase";

					cmd.AddParameter("CaseID", DbType.Int64, caseId);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							message = Create(reader);
						}
					}
				}
			}

			return message;
		}

		public static DomainModel.Message GetLastIncomingOfCase(long caseId, RelatedEntitiesToRead relatedEntitiesToRead = null)
		{
			Message message = null;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetLastIncomingOfCase";

					cmd.AddParameter("CaseID", DbType.Int64, caseId);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							message = Create(reader);
						}
					}
				}
			}

			if (message != null)
			{
				if (relatedEntitiesToRead != null)
				{
					ReadRelatedInfo(message, relatedEntitiesToRead, false);
				}
			}

			return message;
		}

        public static DomainModel.Message GetLastMessage(int caseId, RelatedEntitiesToRead relatedEntitiesToRead = null, bool recurse = false)
        {
            Message message = null;
            using (var conn = DbManager.CreateConnection())
            {
                conn.Open();

                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "Messages_GetLastOfCase";

                    cmd.AddParameter("CaseID", DbType.Int32, caseId);

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            message = Create(reader);
                        }
                    }
                }
            }

            return message;
        }

        public static DomainModel.Message GetLastOutgoingOfCase(long caseId, RelatedEntitiesToRead relatedEntitiesToRead = null)
        {
            Message message = null;
            using (var conn = DbManager.CreateConnection())
            {
                conn.Open();

                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "Messages_GetLastOutgoingOfCase";

                    cmd.AddParameter("CaseID", DbType.Int64, caseId);

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            message = Create(reader);
                        }
                    }
                }
            }

            if (message != null)
            {
                if (relatedEntitiesToRead != null)
                {
                    ReadRelatedInfo(message, relatedEntitiesToRead, false);
                }
            }

            return message;
        }

        /// <summary>
        /// Obtiene todos los mensajes que aún no fueron asignados
        /// </summary>
        /// <param name="complete">Indica si se traerá toda la información de todas las entidades relacionadas</param>
        /// <returns>Una lista de <see cref="Message"/> con los mensajes</returns>
        public static IEnumerable<long> GetAllUnassigned(DateTime? since, long? lastMessageId, out bool moreAvailable)
		{
			moreAvailable = false;

			var messages = new List<long>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllUnassigned";
					cmd.CommandTimeout = TimeSpan.FromSeconds(60).Seconds;
					cmd.AddParameter("FromDate", DbType.DateTime, since);
					cmd.AddParameter("LastMessageID", DbType.Int64, lastMessageId);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							if (messages.Count == 50)
							{
								moreAvailable = true;
								break;
							}
							messages.Add(reader.GetValue<long>("MessageID"));
						}
					}
				}
			}

			return messages;
		}

		/// <summary>
		/// Obtiene todos los mensajes que fueron agrupados por el mensaje especificado
		/// </summary>
		/// <param name="messageThatGroups">El <see cref="DomainModel.Message"/> del cual se quiere obtener sus mensajes agrupados</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes agrupados</returns>
		public static IEnumerable<Message> GetAllGrouped(DomainModel.Message messageThatGroups)
		{
			List<Message> messages = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllGrouped";

					cmd.AddParameter("MessageID", DbType.Int64, messageThatGroups.ID);
					if (messageThatGroups.Case != null)
						cmd.AddParameter("CaseID", DbType.Int64, messageThatGroups.Case.ID);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							Message message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			RelatedEntitiesToRead entities = new RelatedEntitiesToRead(false)
			{
				Service = true,
				Queue = true
			};
			foreach (var message in messages)
				ReadRelatedInfo(message, entities, false);

			return messages;
		}

		/// <summary>
		/// Obtiene todos los mensajes que están asignados
		/// </summary>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes asignados</returns>
		public static IEnumerable<Message> GetAllAssigned()
		{
			List<Message> messages = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllAssigned";
					cmd.CommandTimeout = TimeSpan.FromMinutes(1).Seconds;

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							var message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			RelatedEntitiesToRead entities = new RelatedEntitiesToRead(false)
			{
				Service = true,
				Queue = true,
				AssignedTo = true
			};

			foreach (var message in messages)
				ReadRelatedInfo(message, entities, false);

			return messages;
		}

		public static IDictionary<int, int> GetAllAnsweredAndFailed(DateTime date, int[] services)
		{
			var result = new Dictionary<int, int>();

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllAnsweredAndFailed";
					cmd.CommandTimeout = TimeSpan.FromMinutes(1).Seconds;

					cmd.AddParameter("FromDate", DbType.DateTime, date);
					cmd.AddEnumerationParameter("Services", services);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							int queueId = reader.GetValue<int>("QueueID", 0);
							int count = reader.GetValue<int>("Count", 0);
							result[queueId] = count;
						}
					}
				}
			}

			return result;
		}

		/// <summary>
		/// Obtiene todos los mensajes respondidos y que no fueron publicados en las redes sociales
		/// </summary>
		/// <param name="serviceId">El código del servicio a los cuales pertenecen los mensajes respondidos</param>
		/// <param name="since">La fecha desde la cual se deben listar los mensajes</param>
		/// <param name="queueId">El código de cola a la que pertenece el mensaje</param>
		/// <param name="relatedEntitiesToRead">Las entidades a leer</param>
		/// <param name="moreRecordsAvailable">Cuando retorna devuelve si hay más registros disponibles para leer</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes pendientes</returns>
		public static List<Message> GetAllAnsweredByQueue(DateTime from, int queueId, int? serviceId, bool? requiresAuthorization, bool? authorized, byte deliveryStatus, long? fromMessageId, int? recordCount, out bool moreMessagesAvailable, out int totalRecords)
		{
			totalRecords = 0;
			moreMessagesAvailable = false;

			List<Message> messages = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllAnsweredByQueue";
					cmd.CommandTimeout = TimeSpan.FromMinutes(1).Seconds;

					cmd.AddParameter("FromDate", DbType.DateTime, from);
					cmd.AddParameter("QueueID", DbType.Int32, queueId);
					cmd.AddParameter("ServiceID", DbType.Int32, serviceId);
					cmd.AddParameter("RequiresAuthorization", DbType.Boolean, requiresAuthorization);
					cmd.AddParameter("Authorized", DbType.Boolean, authorized);
					cmd.AddParameter("DeliveryStatus", DbType.Byte, deliveryStatus);

					if (recordCount == null)
						cmd.AddParameter("RecordCount", DbType.Int32, null);
					else
						cmd.AddParameter("RecordCount", DbType.Int32, recordCount.Value + 1);

					cmd.AddParameter("FromMessageID", DbType.Int64, fromMessageId);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							try
							{
								totalRecords = Convert.ToInt32(reader["TotalRecords"]);
							}
							catch { }

							if (recordCount != null && messages.Count >= recordCount.Value)
							{
								moreMessagesAvailable = true;
								break;
							}

							Message message = Create(reader);
							messages.Add(message);
						}
					}
				}
			}

			return messages;
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.MessagesExport"/> con los filtros</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> Search(DomainModel.Reports.Export.MessagesExport filters)
		{
			bool? moreMessagesAvailableAux;
			return Search(filters, null, null, out moreMessagesAvailableAux);
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.MessagesExport"/> con los filtros</param>
		/// <param name="recordCount">Indica la cantidad de registros que devolverá la consulta o null para traer todos los registros</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> Search(DomainModel.Reports.Export.MessagesExport filters, int recordCount)
		{
			bool? moreMessagesAvailableAux;
			return Search(filters, null, recordCount, out moreMessagesAvailableAux);
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.MessagesExport"/> con los filtros</param>
		/// <param name="fromMessageId">Indica el código de mensaje desde el cual se deberán traer datos o null para todos los registros</param>
		/// <param name="recordCount">Indica la cantidad de registros que devolverá la consulta o null para traer todos los registros</param>
		/// <param name="moreMessagesAvailable">Cuando retorna, devuelva si existen más registros disponibles para la consulta paginada</param>
		/// <param name="totalRecords">Cuando retorna, devuelve la cantidad de registros que devolvería la consulta</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> Search(DomainModel.Reports.Export.MessagesExport filters, long? fromMessageId, int? recordCount, out bool moreMessagesAvailable)
		{
			bool? moreMessagesAvailableAux;
			List<Message> messages = Search(filters, fromMessageId, recordCount, out moreMessagesAvailableAux);
			moreMessagesAvailable = moreMessagesAvailableAux.Value;
			return messages;
		}

		/// <summary>
		/// Busca mensaje utilizando los filtros provistos
		/// </summary>
		/// <param name="filters">Un <see cref="DomainModel.Reports.Export.DetailedWhatsappHSMExport"/> con los filtros</param>
		/// <param name="fromMessageId">Indica el código de mensaje desde el cual se deberán traer datos o null para todos los registros</param>
		/// <param name="recordCount">Indica la cantidad de registros que devolverá la consulta o null para traer todos los registros</param>
		/// <param name="moreMessagesAvailable">Cuando retorna, devuelva si existen más registros disponibles para la consulta paginada</param>
		/// <param name="totalRecords">Cuando retorna, devuelve la cantidad de registros que devolvería la consulta</param>
		/// <returns>Una lista de <see cref="Message"/> con los mensajes que aplican a los filtros proporcionados</returns>
		public static List<Message> SearchByPayment(DomainModel.Reports.Export.DetailedWhatsappHSMExport filters, long? fromMessageId, int? recordCount, out bool moreMessagesAvailable, out int totalRecords)
		{
			bool? moreMessagesAvailableAux;
			int? totalRecordsAux;
			List<Message> messages = SearchByPayment(filters, fromMessageId, recordCount, out moreMessagesAvailableAux, out totalRecordsAux);
			moreMessagesAvailable = moreMessagesAvailableAux.Value;
			totalRecords = totalRecordsAux.Value;
			return messages;
		}

		/// <summary>
		/// Asigna un mensaje a una cola
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="socialMessageId">El código del mensaje en la red social</param>
		/// <param name="parameters">Un diccionario con los parámetros del mensaje que deberán ser actualizados o <code>null</code></param>
		public static void UpdateSocialMessage(long messageId, string socialMessageId, Dictionary<string, string> parameters)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateSocialMessage";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("SocialMessageID", DbType.String, socialMessageId);
					if (parameters != null && parameters.Count > 0)
						cmd.AddParameter("Parameters", DbType.String, Common.Conversions.ConvertDictionaryToString(parameters));
					else
						cmd.AddParameter("Parameters", DbType.String, null);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Verifica si un mensaje existe dado su código
		/// </summary>
		/// <param name="socialMessageID">El código del mensaje de la red social</param>
		/// <param name="type">Un valor de <see cref="SocialServiceTypes"/> indicando a qué tipo de servicio de red social pertenece el mensaje</param>
		/// <param name="serviceId">El código de servicio indicando a qué servicio de red social pertenece el mensaje</param>
		/// <returns>true si existe; en caso contrario, false</returns>
		public static bool ExistsBySocialMessage(string socialMessageID, SocialServiceTypes type, int serviceId)
		{
			return DomainModel.StorageManager.Instance.ExistsIncomingMessage(socialMessageID, type, serviceId);
		}

		/// <summary>
		/// Verifica si un mensaje existe dado su código
		/// </summary>
		/// <param name="socialMessageID">El código del mensaje de la red social</param>
		/// <param name="type">Un valor de <see cref="SocialServiceTypes"/> indicando a qué tipo de servicio de red social pertenece el mensaje</param>
		/// <returns>true si existe; en caso contrario, false</returns>
		public static bool ExistsBySocialMessage(string socialMessageID, SocialServiceTypes type)
		{
			return DomainModel.StorageManager.Instance.ExistsMessage(socialMessageID, type);
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje fue correctamente envíado
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="delivered">Indica si el mensaje fue enviado correctamente</param>
		/// <param name="error">El mensaje de error si es que falló el envío</param>
		/// <param name="shouldRetry">Indica si el envío debe ser reintentado</param>
		public static void UpdateDelivered(long messageId, bool delivered, string error, bool shouldRetry)
		{
			UpdateDelivered(messageId, delivered, error, null, shouldRetry);
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje fue correctamente envíado
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="delivered">Indica si el mensaje fue enviado correctamente</param>
		/// <param name="error">El mensaje de error si es que falló el envío</param>
		/// <param name="errorNumber">El código de error</param>
		/// <param name="shouldRetry">Indica si el envío debe ser reintentado</param>
		public static void UpdateDelivered(long messageId, bool delivered, string error, int? errorNumber, bool shouldRetry)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateDelivered";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("Delivered", DbType.Boolean, delivered);
					cmd.AddParameter("DeliveryError", DbType.String, error);
					cmd.AddParameter("DeliveryErrorNumber", DbType.Int32, errorNumber);
					cmd.AddParameter("DeliveryShouldRetry", DbType.Boolean, shouldRetry);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje indicando que no fue entregado aún recibiendo confirmación de que había sido recibido correctamente
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="error">El mensaje de error si es que falló el envío</param>
		/// <param name="errorNumber">El código de error</param>
		public static void UpdateNotDelivered(long messageId, string error, int? errorNumber, Dictionary<string, string> parameters)
		{
			UpdateNotDelivered(messageId, error, errorNumber, parameters, false);
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje indicando que no fue entregado aún recibiendo confirmación de que había sido recibido correctamente
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="error">El mensaje de error si es que falló el envío</param>
		/// <param name="errorNumber">El código de error</param>
		/// <param name="parameters">La colección de parámetros del mensaje</param>
		/// <param name="shouldRetry">Indica si se debe reintentar</param>
		public static void UpdateNotDelivered(long messageId, string error, int? errorNumber, Dictionary<string, string> parameters, bool shouldRetry)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateNotDelivered";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("DeliveryError", DbType.String, error);
					cmd.AddParameter("DeliveryErrorNumber", DbType.Int32, errorNumber);
					cmd.AddParameter("Parameters", parameters);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);
					cmd.AddParameter("DeliveryShouldRetry", DbType.Boolean, shouldRetry);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Marca un mensaje que falló al ser envíado como que nunca falló para ser reenviado
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		public static void ResetDeliveryError(long messageId)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_ResetDeliveryError";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje fue editado y autorizado
		/// </summary>
		/// <param name="messageId">El código de mensaje</param>
		/// <param name="editedBody">El texto del mensaje editado</param>
		/// <param name="originalBody">El texto del mensaje antes de ser editado</param>
		/// <param name="parameters">Los parámetros del mensaje</param>
		/// <param name="supervisorId">Indica quién autoriza el mensaje</param>
		/// <param name="authorize">Indica si el mensaje es autorizado o solo editado</param>
		public static void UpdateBodyAndAuthorized(long messageId, int supervisorId, string originalBody, string editedBody, Dictionary<string, string> parameters, bool authorize)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateBodyAndAuthorized";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("UserID", DbType.Int32, supervisorId);
					cmd.AddParameter("EditedBody", DbType.String, editedBody);
					cmd.AddParameter("Authorize", DbType.Boolean, authorize);

					if (parameters == null)
						parameters = new Dictionary<string, string>();
					parameters["OriginalBody"] = originalBody;

					cmd.AddParameter("Parameters", parameters);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje fue editado y autorizado
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está actualizando</param>
		/// <param name="supervisorId">Indica quién autoriza el mensaje</param>
		/// <param name="editedBody">El texto del mensaje editado</param>
		/// <param name="parameters">Los parámetros del mensaje editado</param>
		/// <param name="newAttachments">Una enumeración con <see cref="DomainModel.Attachment"/> con los archivos adjuntos nuevos</param>
		/// <param name="attachmentsToDelete">Los índices de los archivos adjuntos que deberán ser eliminados</param>
		/// <param name="oldValues">Los valores originales antes de la edición del mensaje</param>
		/// <param name="authorize">Indica si el mensaje es autorizado o solo editado</param>
		public static void UpdateMailAndAuthorized(DomainModel.Message message, int supervisorId, string editedBody, Dictionary<string, string> parameters, Dictionary<string, string> oldValues, IEnumerable<DomainModel.Attachment> newAttachments, IEnumerable<byte> attachmentsToDelete, bool authorize)
		{
			DbTransaction tran = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();
				try
				{
					tran = conn.BeginTransaction();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.CommandText = "Messages_UpdateMailAndAuthorized";
						cmd.Transaction = tran;

						cmd.AddParameter("MessageID", DbType.Int64, message.ID);
						cmd.AddParameter("UserID", DbType.Int32, supervisorId);
						cmd.AddParameter("EditedBody", DbType.String, editedBody);
						cmd.AddParameter("Authorize", DbType.Boolean, authorize);
						cmd.AddParameter("Parameters", parameters);
						cmd.AddParameter("OldValues", oldValues);
						cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

						cmd.ExecuteNonQuery();
					}

					if (newAttachments != null && newAttachments.Any())
						InsertAttachments(newAttachments, message, conn, tran);

					if (attachmentsToDelete != null && attachmentsToDelete.Any())
					{
						using (var cmd = conn.CreateCommand())
						{
							cmd.CommandType = CommandType.StoredProcedure;
							cmd.CommandText = "Attachments_Delete";
							cmd.Transaction = tran;

							cmd.AddParameter("MessageID", DbType.Int64, message.ID, ParameterDirection.Input);
							var parameterIndex = cmd.AddParameter("Index", DbType.Byte, null, ParameterDirection.Input);

							foreach (var index in attachmentsToDelete)
							{
								parameterIndex.Value = index;

								cmd.ExecuteNonQuery();
							}
						}
					}

					tran.Commit();
				}
				catch
				{
					if (tran != null)
						tran.Rollback();

					throw;
				}
				finally
				{
					if (tran != null)
						tran.Dispose();
				}
			}
		}

		/// <summary>
		/// Actualiza los datos de si un mensaje fue editado y autorizado
		/// </summary>
		/// <param name="message">El mensaje que se autoriza</param>
		/// <param name="supervisorId">Indica quién autoriza el mensaje</param>
		/// <param name="authorize">Indica si el mensaje es autorizado o solo editado</param>
		public static void UpdateAuthorized(DomainModel.Message message, int supervisorId)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateAuthorized";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("UserID", DbType.Int32, supervisorId);
					cmd.AddParameter("QueueID", DbType.Int32, supervisorId);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Actualiza el cuerpo de un mensaje
		/// </summary>
		/// <param name="message">El mensaje a actualizar</param>
		/// <param name="body">El cuerpo del mensaje</param>
		public static void UpdateBody(DomainModel.Message message, string body)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateBody";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("Body", DbType.String, body);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdatePostedBy(Message message)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdatePostedBy";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("PostedBySocialUserID", DbType.Int64, message.PostedBy.ID);
					cmd.AddParameter("Parameters", message.Parameters);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdateParameters(DomainModel.Message message)
		{
			UpdateParameters(message.ID, message.Parameters);
		}

		public static void UpdateParameters(long messageId, Dictionary<string, string> parameters)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateParameters";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("Parameters", parameters);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdateSocialConversation(DomainModel.Message message)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateSocialConversation";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("SocialConversationID", DbType.String, message.SocialConversationID);
					cmd.AddParameter("Parameters", message.Parameters);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdateClassification(DomainModel.Message message)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateClassification";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("CaseID", DbType.Int64, message.Case.ID);
					cmd.AddParameter("MessageStatusID", DbType.Int16, (short) message.Status);
					cmd.AddParameter("Parameters", message.Parameters);
					cmd.AddParameter("Classified", DbType.Boolean, message.Classified);
					cmd.AddParameter("TopClassification", DbType.String, message.TopClassification);
					if (message.ClassificationContactReason != null)
						cmd.AddParameter("ClassificationContactReasonID", DbType.Int16, message.ClassificationContactReason.ID);
					else
						cmd.AddParameter("ClassificationContactReasonID", DbType.Int16, null);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static void UpdateStatus(long messageId, DomainModel.MessageStatuses newStatus)
		{
			UpdateStatus(messageId, newStatus, null, null);
		}

		public static void UpdateStatus(long messageId, DomainModel.MessageStatuses newStatus, DomainModel.MessageLogTypes? messageLogType, IDictionary<string, string> parameters)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdateStatus";

					cmd.AddParameter("MessageID", DbType.Int64, messageId);
					cmd.AddParameter("ToMessageStatusID", DbType.Int16, (short) newStatus);
					if (messageLogType != null)
						cmd.AddParameter("MessageLogTypeID", DbType.Int16, (short) messageLogType.Value);
					else
						cmd.AddParameter("MessageLogTypeID", DbType.Int16, null);
					cmd.AddParameter("Parameters", parameters);
					cmd.AddParameter("Date", DbType.DateTime, DateTime.Now);

					cmd.ExecuteNonQuery();
				}
			}
		}

		public static DomainModel.MessageStatuses GetStatus(long messageId)
		{
			try
			{
				using (var conn = DbManager.CreateConnection())
				{
					conn.Open();

					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.CommandText = "Messages_GetStatus";

						cmd.AddParameter("MessageID", DbType.Int64, messageId);

						var status = cmd.ExecuteScalar();

						if (status != null && status != DBNull.Value)
							return (MessageStatuses) Convert.ToInt16(status);

						return MessageStatuses.MarkedForDeletion;
					}
				}
			}
			catch
			{
				return MessageStatuses.NotAssigned;
			}
		}

		/// <summary>
		/// Devuelve el pico de tipo (mimeType) y tamaño de adjuntos en una fecha especifica
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public static (long fileSize, string mimeType) GetAttachmentTotalSizeGroupedByType(DateTime date, bool? returnGroupedByType)
		{
			long fileSize = 0;
			string mimeType = string.Empty;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Attachments_GetTotalSizeByDate";

					cmd.AddParameter("Date", DbType.Date, date);

					if (returnGroupedByType.HasValue)
						cmd.AddParameter("ReturnGroupedByType", DbType.Boolean, returnGroupedByType.Value);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							var record = new YoizenDataRecord(reader);
							var fileSizeRecord = record.GetValue<long?>("FileSize");
							mimeType = record.GetValue<string>("MimeType");

							if (fileSizeRecord.HasValue)
							{
								fileSize = fileSizeRecord.Value;
							}
						}
					}
				}
			}

			return (fileSize, mimeType);
		}

		/// <summary>
		/// Devuelve el pico de tamaño de adjuntos en una fecha especifica
		/// </summary>
		/// <param name="date"></param>
		/// <returns></returns>
		public static long GetAttachmentsTotalSizeByDate(DateTime date)
		{
			long fileSize = 0;
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Attachments_GetTotalSizeByDate";
					cmd.CommandTimeout = TimeSpan.FromMinutes(5).Seconds;

					cmd.AddParameter("Date", DbType.Date, date);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.Read())
						{
							var record = new YoizenDataRecord(reader);
							var fileSizeRecord = record.GetValue<long?>("FileSize");
							if (fileSizeRecord.HasValue)
							{
								fileSize = fileSizeRecord.Value;
							}
						}
					}
				}
			}

			return fileSize;
		}

		/// <summary>
		/// Busca mensaje en la base de datos entre 2 fechas específicas y opcionalmente en estados
		/// </summary>
		/// <param name="from">La fecha desde la cual se buscan mensajes</param>
		/// <param name="to">La fecha hasta la cual se buscan mensajes</param>
		/// <param name="messageStatuses">Los estados de los mensajes a buscar</param>
		/// <returns>Una lista de códigos de mensajes con los mensajes que aplican a los filtros proporcionados</returns>
		public static IEnumerable<Message.MessageCase> GetAllByDates(DateTime from, DateTime to, params MessageStatuses[] messageStatuses)
		{
			var messages = new List<Message.MessageCase>();
			using (var conn = DbManager.CreateConnection(true))
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_GetAllByDates";

					cmd.AddParameter("From", DbType.DateTime, from);
					cmd.AddParameter("To", DbType.DateTime, to);
					if (messageStatuses != null)
						cmd.AddEnumerationParameter("Statuses", messageStatuses.Select(s => (short) s));

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							messages.Add(new Message.MessageCase(reader.GetValue<long>("MessageID"), reader.GetValue<long>("CaseID")));
						}
					}
				}
			}

			return messages;
		}

		public static void UpdateWorkingServiceLevel(DomainModel.Message message)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_UpdatWorkingeServiceLevel";

					cmd.AddParameter("MessageID", DbType.Int64, message.ID);
					cmd.AddParameter("AlreadyOutOfSLL", DbType.Boolean, message.OutOfSLL);

					cmd.ExecuteNonQuery();
				}
			}
		}

		/// <summary>
		/// Busca envios de HSM de whatsapp utilizando los filtros provistos
		/// </summary>
		public static List<Message> SearchWhatsappHSM(DateTime from, DateTime to, int[] services = null, string[] templates = null)
		{
			List<Message> msgs = new List<Message>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_SearchWhatsappHSM";

					cmd.AddParameter("FromDate", DbType.DateTime, from);
					cmd.AddParameter("ToDate", DbType.DateTime, to);
					cmd.AddEnumerationParameter("Services", services);
					cmd.AddEnumerationParameter("Templates", templates);

					using (DbDataReader reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							var msg = new Message();
							msg.ID = reader.GetValue<long>("MessageID");
							msg.HsmAnswerType = (DomainModel.Whatsapp.HSMAnswerTypes ?) reader.GetValue<short?>("HsmAnswerType");
							var parameters = reader.GetValue<string>("Parameters", null);
							if (!string.IsNullOrEmpty(parameters))
							{
								msg.Parameters.ParseString(parameters);
							}
							msgs.Add(msg);
						}
					}
				}
			}

			return msgs;
		}

		/// <summary>
		/// Descarta todos los mensajes de casos cerrados
		/// </summary>
		/// <returns>Una enumeración de códigos de mensajes descartados</returns>
		public static IEnumerable<long> DiscardOfClosedCases()
		{
			List<long> messages = null;
			using (var conn = DbManager.CreateConnection(true))
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "Messages_DiscardOfClosedCases";
					cmd.CommandTimeout = TimeSpan.FromMinutes(1).Seconds;

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							if (messages == null)
								messages = new List<long>();

							messages.Add(reader.GetValue<long>("MessageID"));
						}
					}
				}
			}

			return messages;
		}

		#endregion
	}

	#region Data Reader Clases

	public class MessageDataReader : DataReader<Message>
	{
		#region Constructors

		public MessageDataReader(System.Data.Common.DbDataReader innerDataReader)
			: base(innerDataReader)
		{
		}

		#endregion

		#region Properties

		protected override bool ShouldCreateInstanceOnNextRecord { get { return false; } }

		#endregion

		#region Protected Methods

		protected override Message CreateInstance(IDataRecord record)
		{
			return MessageDAO.Create(record);
		}

		protected override DataEnumerator<Message> CreateDataEnumerator(System.Data.Common.DbEnumerator enumerator)
		{
			return new MessageDataEnumerator(enumerator, this.ShouldCreateInstanceOnNextRecord, this.innerDataReader.ColumnNamesIndexes);
		}

		#endregion
	}

	public class MessageDataEnumerator : DataEnumerator<Message>
	{
		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de MessageDataEnumerator
		/// </summary>
		public MessageDataEnumerator(System.Data.Common.DbEnumerator innerDataEnumerator, bool shouldCreateInstanceOnNextRecord, IDictionary<string, int> columnNamesIndexes)
			: base(innerDataEnumerator, shouldCreateInstanceOnNextRecord, columnNamesIndexes)
		{
		}

		#endregion

		#region Protected Methods

		protected override DataRecord<Message> CreateDataRecord(YoizenDataRecord currentRecord)
		{
			return new MessageDataRecord(currentRecord, this.shouldCreateInstanceOnNextRecord);
		}

		#endregion
	}

	public class MessageDataRecord : DataRecord<Message>
	{
		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de MessageDataRecord
		/// </summary>
		public MessageDataRecord(YoizenDataRecord innerDataRecord, bool shouldCreateInstanceOnNextRecord)
			: base(innerDataRecord, shouldCreateInstanceOnNextRecord)
		{
		}

		#endregion

		#region Protected Methods

		/// <summary>
		/// Crea el <see cref="DomainObject"/> que le corresponde a la instancia del registro actual
		/// </summary>
		/// <param name="record">Un <see cref="System.Data.IDataRecord"/> con los datos del registro</param>
		/// <returns>Un <see cref="DomainObject"/></returns>
		protected override Message CreateInstance(System.Data.IDataRecord record)
		{
			return MessageDAO.Create(this.innerDataRecord);
		}

		#endregion
	}

	#endregion
}