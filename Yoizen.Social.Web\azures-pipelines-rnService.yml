trigger: none  # Deshabilita el desencadenador normal de CI

pr:
  branches:
    include:
      - main  # Se ejecutará en PRs que apunten a la rama main

variables:
  - name: RELEASE_NOTES_URL
    value: 'https://releaseanotes-yoizen-dev.ysocial.net/api/release-notes'

steps:
- task: PowerShell@2
  inputs:
    targetType: 'inline'
    script: |
      $body = @{
        prId = "$(System.PullRequest.PullRequestId)"  # Esta variable estará disponible automáticamente en el contexto de PR
        model = "gemini-2.0-flash"
        useAI = $false
        azureProject = "ySocial"
        azureRepoId = "ySocial"
        addCommentToPR = $false
        saveToFile = $true
      } | ConvertTo-Json

      $headers = @{
        'Content-Type' = 'application/json'
      }

      try {
        $response = Invoke-RestMethod -Uri "$(RELEASE_NOTES_URL)" -Method Post -Body $body -Headers $headers
        Write-Host "Successfully called release notes API"
      }
      catch {
        Write-Error "Failed to call release notes API: $_"
        exit 1
      }
  displayName: 'Generate Release Notes'