MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  L =]h        �            �9       @    @                       �          @�                           p9  K    @  �                   `                                                                       H           .text   �                           `.rsrc   �   @                    @  @.reloc      `      "              @  B                �9      H     D'  ,                                                       � r  p(  
 (  
 (   (   (  
 rI  p(  
 (  
&* 0 /     r�  p(  
 r�  p(  

s  
r� po  
r� po	  
,o
  
��+ :�    o  
8~  o  

 	r� po	  
,	r� po	  
o
  
��+ :D   	r� po	  
t  r po  
,r po  
o
  
��+ -r" pr po  
o
  
o  
 rF po  
,rF po  
o
  
��+ -rZ prF po  
o
  
o  
 r| po  
,r| po  
o
  
��+ -r� pr| po  
o
  
o  
 r� po  
,r� po  
o
  
��+ -r� pr� po  
o
  
o  
   o  
:r�����-o  
 �  r� p(  
 r pr" po  
-r& p+r" po  
 (  
(  
 r: prZ po  
-r& p+rZ po  
 (  
(  
 rV pr� po  
-r& p+r� po  
 (  
(  
 rv pr� po  
-r& p+r� po  
 (  
(  
 r� po  
,'r" po  
,rZ po  
,
r� po  
+ r� p-r� p+r� p (  
(  
 (  
 * A     Y   �  �         0 2     r* p(  
 rr p(  

s  
r� po  
r� po	  
,o
  
��+ :�    o  
8~  o  

 	r� po	  
,	r� po	  
o
  
��+ :D   	r� po	  
t  r po  
,r po  
o
  
��+ -r" pr po  
o
  
o  
 rF po  
,rF po  
o
  
��+ -rZ prF po  
o
  
o  
 r| po  
,r| po  
o
  
��+ -r� pr| po  
o
  
o  
 r� po  
,r� po  
o
  
��+ -r� pr� po  
o
  
o  
   o  
:r�����-o  
 �  r� p(  
 r pr" po  
-r& p+r" po  
 (  
(  
 r: prZ po  
-r& p+rZ po  
 (  
(  
 rV pr� po  
-r& p+r� po  
 (  
(  
 rv pr� po  
-r& p+r� po  
 (  
(  
 r� po  
-*r" po  
-rZ po  
-r� po  
�+ r� p-r' p+rm p (  
(  
 (  
 *  A     Y   �  �         (  
*BSJB         v4.0.30319     l     #~  �  �  #Strings    4  �  #US 
     #GUID    
    #Blob         W	    �%3                                            
       4 -  )	 I	 g-  y- 
 �� ��
 ��
 �� � � jW -  �-                     V�; 
 V�V 
 V�p 
 V�� 
 Q�� 
 Q�� 
 P     � � G �     � � M �#    � � M <'    �� Q     � U � Q! oZ! oM! �_1 �m � Q1 �zA ��A � +� 9�	 E� N�a v�i �Q �� ��q ��	 � Q  
   0   Q   v   �   �.  �.  ��s���                               $     	           d�       <Module> SimpleTest.exe SimpleTest mscorlib System Object ReferralSourceUrlParameter ReferralSourceIdParameter ReferralSourceTypeParameter ReferralCTWAClickIdParameter WebhookWithReferral WebhookWithoutReferral Main TestWithReferral TestWithoutReferral .ctor args System.Runtime.CompilerServices CompilationRelaxationsAttribute RuntimeCompatibilityAttribute Console WriteLine ConsoleKeyInfo ReadKey Newtonsoft.Json Newtonsoft.Json.Linq JObject Parse System.Collections.Generic Dictionary`2 JToken get_Item JTokenType get_Type IEnumerable`1 IEnumerator`1 GetEnumerator get_Current ToString set_Item System.Collections IEnumerator MoveNext IDisposable Dispose ContainsKey String Concat     G= = =   S i m p l e   c t w a _ c l i d   V a l i d a t i o n   = = =  [V a l i d a t i o n   c o m p l e t e .   P r e s s   a n y   k e y   t o   e x i t . . .  AT e s t :   W e b h o o k   W I T H   r e f e r r a l   d a t a  ��{ 
     " i n f o " :   { 
         " m e s s a g e s " :   [ 
             { 
                 " r e f e r r a l " :   { 
                     " s o u r c e _ u r l " :   " h t t p s : / / f b . m e / 4 4 4 I r c N b j " , 
                     " s o u r c e _ i d " :   " 1 2 0 2 2 8 5 3 2 0 9 1 8 0 0 1 6 4 " , 
                     " s o u r c e _ t y p e " :   " a d " , 
                     " c t w a _ c l i d " :   " A f f 3 k Z 5 j f W K b 5 w W e K u z k O u j I B I 0 m v H C 9 T S o D 4 W S z L L o - D 6 M - Q R 0 H N 3 X x C I z 6 X M P g T T C a k E R w T 5 q a X x m 8 m S h d Y h j l 0 M i Y L G Y G t D f N C g 6 6 j n P M b t q P Y j V q 1 A c c C M 0 J m n t D W K y s I w " 
                 } 
             } 
         ] 
     } 
 } 	i n f o  m e s s a g e s  r e f e r r a l  s o u r c e _ u r l  #R e f e r r a l S o u r c e U r l  s o u r c e _ i d  !R e f e r r a l S o u r c e I d  s o u r c e _ t y p e  %R e f e r r a l S o u r c e T y p e  c t w a _ c l i d  'R e f e r r a l C T W A C l i c k I d  R e s u l t s :      s o u r c e _ u r l :    N O T   F O U N D      s o u r c e _ i d :        s o u r c e _ t y p e :        c t w a _ c l i d :    S t a t u s :    3L'  F A I L E D   -   M i s s i n g   f i e l d s Q'  S U C C E S S   -   A l l   r e f e r r a l   f i e l d s   c a p t u r e d GT e s t :   W e b h o o k   W I T H O U T   r e f e r r a l   d a t a  ��{ 
     " i n f o " :   { 
         " m e s s a g e s " :   [ 
             { 
                 " t y p e " :   " i n t e r a c t i v e " 
             } 
         ] 
     } 
 }  EL'  F A I L E D   -   U n e x p e c t e d   f i e l d s   f o u n d k'  S U C C E S S   -   N o   r e f e r r a l   f i e l d s   c a p t u r e d   ( a s   e x p e c t e d )    _�%r�A���=F[�� �z\V4��"R e f e r r a l S o u r c e U r l  R e f e r r a l S o u r c e I d $R e f e r r a l S o u r c e T y p e &R e f e r r a l C T W A C l i c k I d ��{ 
     " i n f o " :   { 
         " m e s s a g e s " :   [ 
             { 
                 " r e f e r r a l " :   { 
                     " s o u r c e _ u r l " :   " h t t p s : / / f b . m e / 4 4 4 I r c N b j " , 
                     " s o u r c e _ i d " :   " 1 2 0 2 2 8 5 3 2 0 9 1 8 0 0 1 6 4 " , 
                     " s o u r c e _ t y p e " :   " a d " , 
                     " c t w a _ c l i d " :   " A f f 3 k Z 5 j f W K b 5 w W e K u z k O u j I B I 0 m v H C 9 T S o D 4 W S z L L o - D 6 M - Q R 0 H N 3 X x C I z 6 X M P g T T C a k E R w T 5 q a X x m 8 m S h d Y h j l 0 M i Y L G Y G t D f N C g 6 6 j n P M b t q P Y j V q 1 A c c C M 0 J m n t D W K y s I w " 
                 } 
             } 
         ] 
     } 
 } ��{ 
     " i n f o " :   { 
         " m e s s a g e s " :   [ 
             { 
                 " t y p e " :   " i n t e r a c t i v e " 
             } 
         ] 
     } 
 }          0�O沦��  ! !  %)!  - -!              !!-!        TWrapNonExceptionThrows �9          �9                          �9        _CorExeMain mscoree.dll     �%  @                                                                                  �   8  �                  P  �                  h  �                   �                      �   �@  T          �B  �          T4   V S _ V E R S I O N _ I N F O     ���                   ?                         D    V a r F i l e I n f o     $    T r a n s l a t i o n       ��   S t r i n g F i l e I n f o   �   0 0 0 0 0 4 b 0   ,   F i l e D e s c r i p t i o n         0   F i l e V e r s i o n     0 . 0 . 0 . 0   @   I n t e r n a l N a m e   S i m p l e T e s t . e x e     (   L e g a l C o p y r i g h t       H   O r i g i n a l F i l e n a m e   S i m p l e T e s t . e x e     4   P r o d u c t V e r s i o n   0 . 0 . 0 . 0   8   A s s e m b l y   V e r s i o n   0 . 0 . 0 . 0       ﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity version="1.0.0.0" name="MyApplication.app"/>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>
                                                                                                                                                                                                                                                                                               0     �9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      