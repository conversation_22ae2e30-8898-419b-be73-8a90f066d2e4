(function () {

    'use strict';

    angular
        .module('socialApp')
        .controller('EmailContactsModalController', EmailContactsModalController);

    EmailContactsModalController.$inject = ['$element', '$scope', 'close','utilsService', 'modalSocialService'];

    function EmailContactsModalController($element, $scope, close, utilsService, modalSocialService) {
        $scope.utilsService = utilsService;

        $scope.cancel = function () {
            close();
        };

        $scope.setEmailContact = function (emailContact) {
            close({
                textEmailContact: emailContact.email.toString()
            }, 500);
        };
    }
})();
