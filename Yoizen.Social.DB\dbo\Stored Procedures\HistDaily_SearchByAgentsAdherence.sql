﻿CREATE PROCEDURE [dbo].[HistDaily_SearchByAgentsAdherence]
	@From DATETIME
	, @To DATETIME
	, @Agents NVARCHAR(MAX)
	, @MinutesToConsider SMALLINT = 0
AS
BEGIN
	SET NOCOUNT ON;
	
	DECLARE @tAgents TABLE ([AgentID] INT);
	
	INSERT INTO @tAgents
		SELECT [Value] FROM [dbo].[Split](@Agents, N',');
		
	SELECT 
		[HistDaily].[Date]
		, [HistDaily].[Interval]
		, [HistDaily].[PersonID]
		, 0 AS [QueueID]
		, [HistDaily].[LoginTime]
		, [HistDaily].[AvailTime]
		, [HistDaily].[WorkingTime]
		, [HistDaily].[AuxTime]
		, [Persons].[FirstName] AS [PersonFirstName]
		, [Persons].[LastName] AS [PersonLastName]
		, [Persons].[Username] AS [PersonUserName]
		, [Persons].[PersonTypeID] AS [PersonPersonTypeID]
		, [Persons].[SiteID] AS [PersonSiteID]
		FROM [HistDaily] WITH (NOLOCK)
			INNER JOIN [Persons] WITH (NOLOCK)
				ON [HistDaily].[PersonID] = [Persons].[PersonID]
		WHERE
			[IntervalDateTime] >= @From AND [IntervalDateTime] < @To
			AND [HistDaily].[PersonID] IN (SELECT [AgentID] FROM @tAgents)
			AND [HistDaily].[QueueID] = 0
			AND (
				(@MinutesToConsider = 0 AND [HistDaily].[LoginTime] > 0)
				OR (@MinutesToConsider > 0 AND [HistDaily].[LoginTime] >= (@MinutesToConsider * 60))
			)
		ORDER BY [HistDaily].[IntervalDateTime], [Persons].[SiteID], [HistDaily].[PersonID];
END