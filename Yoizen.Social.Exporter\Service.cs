﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.ServiceProcess;
using System.Text;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.DAL;

namespace Yoizen.Social.Exporter
{
	public partial class Service : ServicePreshutdownBase
	{
		#region Constants

		private const int RefreshInterval = 35000;
		private DateTime lastLicenseInvalidMailSent = DateTime.MinValue;
		private DateTime lastOutOfFreeSpaceMailSent = DateTime.MinValue;

		#endregion
		
		#region Fields

		private System.Threading.Timer timer = null;
		private int refreshInterval;
		private string pathForFiles;
		private ExporterProcessor processor = null;

		#endregion

		#region Constructors

		public Service()
			: base()
		{
			InitializeComponent();
			this.CanShutdown = false;

			try
			{
				if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["RefreshInterval"], out this.refreshInterval))
				{
					if (this.refreshInterval < 5000)
						this.refreshInterval = 5000;
				}
				else
				{
					this.refreshInterval = RefreshInterval;
				}
			}
			catch
			{
				this.refreshInterval = RefreshInterval;
			}
		}

		#endregion
		
		#region ServiceBase Methods

		protected override void OnStart(string[] args)
		{
			CultureInfo culture = new CultureInfo("es-AR");
			System.Threading.Thread.CurrentThread.CurrentCulture = culture;
			System.Threading.Thread.CurrentThread.CurrentUICulture = culture;

			//System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;

			StartService();

			Tracer.TraceInfo("Iniciado correctamente");
			this.EventLog.WriteEntry("Servicio iniciado correctamente", System.Diagnostics.EventLogEntryType.Information);
		}

		protected override void OnStop()
		{
			if (Preshutdown)
				Tracer.TraceInfo("El servicio detectó que se apagará el equipo");
			else
				Tracer.TraceInfo("Solicitud de detener el servicio");

#if DEBUG
			Tracer.TraceInfo("Deteniendo timer");
#endif
			if (timer != null)
			{
				timer.Dispose();
				timer = null;
			}

#if DEBUG
			Tracer.TraceInfo("Liberando recursos");
#endif
			base.RequestAdditionalTime(2000);

#if DEBUG
			Tracer.TraceInfo("Guardando valores en la base de datos");
#endif
			try
			{
				DomainModel.SystemStatus.Instance.ExporterServiceStatus.Started = false;
				DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.ExporterServiceStatus.StatusPath));
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo actualizar los valores en la base de datos: {0}", ex);
			}

			if (Preshutdown)
				Tracer.TraceInfo("Finalizó el procesamiento previo a que se apague el equipo");
			else
				Tracer.TraceInfo("Servicio detenido");
		}

		#endregion

		#region Private Methods

#if RUNASPROGRAM
		public void StartService()
#else
		private void StartService()
#endif
		{
			AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);

			Tracer.TraceInfo("========================================================================");
			Tracer.TraceInfo("Identificación del equipo: {0}", Licensing.LicenseManager.Identification);
			if (!Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				Tracer.TraceError("No hay licencia para utilizar el producto: {0}", Licensing.LicenseManager.Instance.ValidationException);
				this.Stop();
				return;
			}

			DomainModel.Cache.Instance.Enabled = false;

			this.pathForFiles = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location), "Files");
			if (!Directory.Exists(this.pathForFiles))
			{
				Directory.CreateDirectory(this.pathForFiles);
				Tracer.TraceInfo("Se creó el directorio {0} para guardar archivos", pathForFiles);
			}

			this.processor = new ExporterProcessor(this.pathForFiles);

			try
			{
				SystemSettingsDAO.GetAll();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo cargar la configuración del sistema: {0}", ex);
				this.Stop();
				return;
			}

			DomainModel.SystemStatus.Instance.ExporterServiceStatus.Started = true;
			DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.ExporterServiceStatus.StatusPath));

#if RUNASPROGRAM
			DoWork();
#else
			Tracer.TraceInfo("Se utilizará un tiempo de refresco de {0} milisegundos ({1} segundos)", this.refreshInterval, this.refreshInterval / 1000);
			timer = new System.Threading.Timer((c) => { DoWork(); }, null, 5000, this.refreshInterval);
#endif
		}

		private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
		{
			Tracer.TraceError("Ocurrió un error no manejado en el servicio: {0}", e.ExceptionObject);
		}

		/// <summary>
		/// Realiza las tareas de consultas y respuestas a las redes sociales cada vez que expira el tiempo del timer
		/// </summary>
		private void DoWork()
		{
			Tracer.TraceInfo("Iniciando trabajo de exportación");

#if !RUNASPROGRAM
			timer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
#endif

			if (Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				try
				{
					SystemSettingsDAO.GetAll();
				}
				catch (OutOfMemoryException ex)
				{
					Tracer.TraceError("Falló la actualización de los parámetros del sistema por problemas de memoria: {0}", ex);
					DomainModel.SystemSettings.Instance.HandleException(ex);
					Environment.Exit(1);
					return;
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló la actualización de los parámetros del sistema: {0}", ex);
					DomainModel.SystemSettings.Instance.HandleException(ex);

#if !RUNASPROGRAM
					timer.Change(this.refreshInterval, this.refreshInterval);
#endif

					return;
				}

				try
				{
					this.processor.Process();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error haciendo el procesamiento de reportes: {0}", ex);
				}
			}

#if !RUNASPROGRAM
			timer.Change(this.refreshInterval, this.refreshInterval);
#endif
		}

		#endregion
	}
}