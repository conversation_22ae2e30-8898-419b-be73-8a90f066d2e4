﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.ServiceProcess;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;
using System.Xml.Serialization;
using Yoizen.Common;
using Yoizen.Social.DomainModel;
using Yoizen.Social.Licensing;

namespace Yoizen.Social.Web.Administration
{
	public partial class ChangeLicense : LoginRequiredBasePage
	{
		#region Properties

		protected override string RedirectUrl { get { return "~/Administration/ChangeLicense.aspx"; } }

		protected override string PageDescription { get { return "Cambiar parámetros de la licencia"; } }

		#endregion

		#region Inner Classes

		class LicenseProperty
		{
			public TypeCode Type { get; set; }

			public string Title { get; set; }

			public object Value { get; set; }

			public object FileValue { get; set; }

			public bool AllowsNull { get; set; } = false;

			public string DependsOn { get; set; }
		}

		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			if (!IsPostBack)
			{
				var license = Licensing.LicenseManager.Instance.License.Configuration;

				var fileContents = RetrieveCurrentLicenseValues();

				var properties = new Dictionary<string, LicenseProperty>();
				properties["MaxConcurrentAgents"] = new LicenseProperty()
				{
					Type = TypeCode.Int32,
					Title = "Agentes simultáneos",
					Value = license.MaxConcurrentAgents,
					FileValue = fileContents.MaxConcurrentAgents
				};
				properties["MaxNominalUsers"] = new LicenseProperty()
				{
					Type = TypeCode.Int32,
					Title = "Usuarios nominales",
					Value = license.MaxNominalUsers,
					FileValue = fileContents.MaxNominalUsers
				};
				properties["AllowAgentsToReturnMessagesToQueue"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes retornar mensajes a la cola",
					Value = license.AllowAgentsToReturnMessagesToQueue,
					FileValue = fileContents.AllowAgentsToReturnMessagesToQueue
				};
				properties["AllowToCreateServices"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir crear servicios",
					Value = license.AllowToCreateServices,
					FileValue = fileContents.AllowToCreateServices
				};
				properties["MaxServicesToCreate"] = new LicenseProperty()
				{
					Type = TypeCode.Int32,
					Title = "Máxima cantidad de servicios a crear",
					Value = license.MaxServicesToCreate,
					FileValue = fileContents.MaxServicesToCreate,
					AllowsNull = true,
					DependsOn = "AllowToCreateServices"
				};
				properties["AllowAgentsToReturnMessagesToSpecifiedQueue"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes transferir mensajes a otra cola",
					Value = license.AllowAgentsToReturnMessagesToSpecifiedQueue,
					FileValue = fileContents.AllowAgentsToReturnMessagesToSpecifiedQueue
				};
				properties["AllowSupervisorsToReplyMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los supervisores responder mensajes",
					Value = license.AllowSupervisorsToReplyMessages,
					FileValue = fileContents.AllowSupervisorsToReplyMessages
				};
				properties["AllowAgentsToHideFacebookMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes ocultar mensajes de facebook",
					Value = license.AllowAgentsToHideFacebookMessages,
					FileValue = fileContents.AllowAgentsToHideFacebookMessages
				};
				properties["AllowAgentsToLikeFacebookMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes hacer 'me gusta' de mensajes de facebook",
					Value = license.AllowAgentsToLikeFacebookMessages,
					FileValue = fileContents.AllowAgentsToLikeFacebookMessages
				};
				properties["AllowAgentsToRetweetMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes hacer retweet",
					Value = license.AllowAgentsToRetweetMessages,
					FileValue = fileContents.AllowAgentsToRetweetMessages
				};
				properties["AllowAgentsToFavouriteTwitterMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes marcar como favorito tweets",
					Value = license.AllowAgentsToFavouriteTwitterMessages,
					FileValue = fileContents.AllowAgentsToFavouriteTwitterMessages
				};
				properties["EncryptMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Encriptar mensajes en la base de datos",
					Value = license.EncryptMessages,
					FileValue = fileContents.EncryptMessages
				};
				properties["AllowCoaching"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir coaching en los agentes",
					Value = license.AllowCoaching,
					FileValue = fileContents.AllowCoaching
				};
				properties["AllowAgentsToFollowUnfollowTwitter"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los agentes hacer follow de usuarios de twitter",
					Value = license.AllowAgentsToFollowUnfollowTwitter,
					FileValue = fileContents.AllowAgentsToFollowUnfollowTwitter
				};
				properties["SurveysEnabled"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir utilizar encuestas",
					Value = license.SurveysEnabled,
					FileValue = fileContents.SurveysEnabled
				};
				properties["ExternalSurveysEnabled"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir utilizar encuestas de terceros",
					Value = license.ExternalSurveysEnabled,
					FileValue = fileContents.ExternalSurveysEnabled
				};
				properties["AllowToConfigureACDBalancing"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir configurar balanceo del ACD",
					Value = license.AllowToConfigureACDBalancing,
					FileValue = fileContents.AllowToConfigureACDBalancing
				};
				properties["AllowIntegrations"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir integraciones",
					Value = license.AllowIntegrations,
					FileValue = fileContents.AllowIntegrations
				};
				properties["AllowDisconnectAgents"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir a los supervisores desconectar agentes",
					Value = license.AllowDisconnectAgents,
					FileValue = fileContents.AllowDisconnectAgents
				};
				properties["AllowToExtendBusinessData"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir extender los datos de negocio",
					Value = license.AllowToExtendBusinessData,
					FileValue = fileContents.AllowToExtendBusinessData
				};
				properties["AllowCustomBusinessDataRegex"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir utilizar una expresión regular custom para datos de negocio",
					Value = license.AllowCustomBusinessDataRegex,
					FileValue = fileContents.AllowCustomBusinessDataRegex
				};
				properties["AllowToExtendProfile"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir extender los datos de perfiles",
					Value = license.AllowToExtendProfile,
					FileValue = fileContents.AllowToExtendProfile
				};
				properties["AllowToExtendCase"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir extender los datos de casos",
					Value = license.AllowToExtendCase,
					FileValue = fileContents.AllowToExtendCase
				};
				properties["AllowWhatsappOutbound"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir envío de HSM",
					Value = license.AllowWhatsappOutbound,
					FileValue = fileContents.AllowWhatsappOutbound
				};
				properties["AllowWhatsappOutboundService"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir envío de HSM a través de API",
					Value = license.AllowWhatsappOutboundService,
					FileValue = fileContents.AllowWhatsappOutboundService
				};
				properties["AllowToHidePoweredBy"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir ocultar el 'powered by yoizen' en servicios de chat",
					Value = license.AllowToHidePoweredBy,
					FileValue = fileContents.AllowToHidePoweredBy
				};
				properties["AllowToSetAuxReasonsByAgents"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir configurar motivos de auxiliar por agente",
					Value = license.AllowToSetAuxReasonsByAgents,
					FileValue = fileContents.AllowToSetAuxReasonsByAgents
				};
				properties["AllowToSendWhatsappHSMMassiveByExternalIntegrations"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir envío de HSM masivos a través de API",
					Value = license.AllowToSendWhatsappHSMMassiveByExternalIntegrations,
					FileValue = fileContents.AllowToSendWhatsappHSMMassiveByExternalIntegrations
				};
				properties["AllowAutomaticExport"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir exportación automática de archivos",
					Value = license.AllowAutomaticExport,
					FileValue = fileContents.AllowAutomaticExport
				};
				properties["AllowUrlLogin"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir login por URL",
					Value = license.AllowUrlLogin,
					FileValue = fileContents.AllowUrlLogin
				};
				properties["AllowChatWithYFlowHistory"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir guardar la conversación completa de chats con yFlow",
					Value = license.AllowChatWithYFlowHistory,
					FileValue = fileContents.AllowChatWithYFlowHistory
				};
				properties["AllowServicesAsChats"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir que los servicios que son de mensajería privada (whatsapp o telegram por ejemplo) se pueden comportar como chat",
					Value = license.AllowServicesAsChats,
					FileValue = fileContents.AllowServicesAsChats
				};
				properties["AllowToMarkAsReadWhatsappMessages"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir configurar si los mensajes de whatsapp recibidos pueden ser marcados como leído",
					Value = license.AllowToMarkAsReadWhatsappMessages,
					FileValue = fileContents.AllowToMarkAsReadWhatsappMessages
				};
				properties["AllowToGenerateWhatsappHSMJwt"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Permitir configurar el JWT para servicios de Whatsapp y el envío al callback de HSMs",
					Value = license.AllowToGenerateWhatsappHSMJwt,
					FileValue = fileContents.AllowToGenerateWhatsappHSMJwt
				};
				properties["AllowWhatsappOutboundWithoutCaseCreation"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se permite el envio masivo de mensajes salientes de Whatsapp sin creacion de casos",
					Value = license.AllowWhatsappOutboundWithoutCaseCreation,
					FileValue = fileContents.AllowWhatsappOutboundWithoutCaseCreation
				};
				properties["AllowWhatsappOutboundWithoutCaseWithContext"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se permite el envio masivo de mensajes salientes de Whatsapp sin creacion de casos con contexto",
					Value = license.AllowWhatsappOutboundWithoutCaseWithContext,
					FileValue = fileContents.AllowWhatsappOutboundWithoutCaseWithContext
                };
				properties["SendToServiceBusInsteadOfDirectSend"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se enviará los mensajes primero a una cola de service bus (true) para luego ser envíados al canal en lugar de hacer el envío directamente",
					Value = license.SendToServiceBusInsteadOfDirectSend,
					FileValue = fileContents.SendToServiceBusInsteadOfDirectSend
				};
				properties["AllowToSaveAttachmentsInAzureStorage"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se utilizará el Storage de Azure para guardar los archivos adjuntos",
					Value = license.AllowToSaveAttachmentsInAzureStorage,
					FileValue = fileContents.AllowToSaveAttachmentsInAzureStorage
				};
				properties["SaveReportsInStorage"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se grabarán los reportes exportados en Storage de Azure",
					Value = license.SaveReportsInStorage,
					FileValue = fileContents.SaveReportsInStorage
				};
				properties["DetailedReportsWithManyDaysByExporter"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si los reportes detallados cuando se elija más de un día, irán directamente a exportación",
					Value = license.DetailedReportsWithManyDaysByExporter,
					FileValue = fileContents.DetailedReportsWithManyDaysByExporter
				};
				properties["AllowToSearchMessagesByBody"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se permite buscar mensajes por cuerpo",
					Value = license.AllowToSearchMessagesByBody,
					FileValue = fileContents.AllowToSearchMessagesByBody
				};
				properties["AllowToSearchChatsByBody"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si se permite buscar chats por contenido",
					Value = license.AllowToSearchChatsByBody,
					FileValue = fileContents.AllowToSearchChatsByBody
				};
				properties["AllowAgentsToTransferMessagesToYFlow"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si permite que los agentes transfieran mensajes a yFlow",
					Value = license.AllowAgentsToTransferMessagesToYFlow,
					FileValue = fileContents.AllowAgentsToTransferMessagesToYFlow
				};
				properties["AllowAgentsToStartVideoCall"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si permite que los agentes pueden iniciar videollamadas",
					Value = license.AllowAgentsToStartVideoCall,
					FileValue = fileContents.AllowAgentsToStartVideoCall
				};
				properties["YoizenAdminCanDoSupervisorActions"] = new LicenseProperty()
				{
					Type = TypeCode.Boolean,
					Title = "Establece si yoizenadmin puede realizar acciones de supervisor (responder, descartar, mover de cola, etc)",
					Value = license.YoizenAdminCanDoSupervisorActions,
					FileValue = fileContents.YoizenAdminCanDoSupervisorActions
				};
				properties["YoizenAdminGroup"] = new LicenseProperty()
				{
					Type = TypeCode.String,
					Title = "Establece el nombre del grupo de AD al que debe pertenecer un usuario para loguearse como YoizenAdmin",
					Value = license.YoizenAdminGroup,
					FileValue = fileContents.YoizenAdminGroup,
					AllowsNull = true
				};

				this.RegisterJsonVariable("properties", properties);
			}
		}

		#region Private Methods

		private bool ValidateUser()
		{
			var isYoizenAdmin = false;
			try
			{
				isYoizenAdmin = (bool) Session["IsSuper"];
			}
			catch { }

			if (!isYoizenAdmin)
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private Licensing.ConfigurationType RetrieveCurrentLicenseValues()
		{
			string licenseFile = ConfigurationManager.AppSettings["LicenseFile"];
			if (string.IsNullOrEmpty(licenseFile))
			{
				string currentDirectory = GetCurrentDirectory();

				licenseFile = Path.Combine(currentDirectory, "Yoizen.Social.License.xml");
			}

			if (!Path.IsPathRooted(licenseFile))
			{
				string currentDirectory = GetCurrentDirectory();

				licenseFile = Path.Combine(currentDirectory, licenseFile);
			}

			if (!File.Exists(licenseFile))
			{
				Tracer.TraceError("El archivo de licencias {0} no existe", licenseFile);
				return null;
			}

			XmlDocument doc;
			try
			{
				doc = new XmlDocument();
				doc.PreserveWhitespace = true;
				doc.Load(licenseFile);
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Hubo un error procesando el archivo de licencia. El XML es inválido: {0}", ex);
				return null;
			}

			if (doc.DocumentElement == null)
			{
				Tracer.TraceError("No se encontró el elemento <license> del archivo de licencias");
				return null;
			}

			try
			{
				XmlSerializer serializer = new XmlSerializer(typeof(LicenseType));
				using (StreamReader reader = new StreamReader(licenseFile))
				{
					var license = (LicenseType) serializer.Deserialize(reader);
					return license.Configuration;
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceException("No se pudo deserializar la licencia: {0}", ex);
				return null;
			}
		}

		private static string GetCurrentDirectory()
		{
			string currentDirectory = null;
			try
			{
				if (HttpContext.Current != null)
				{
					try
					{
						currentDirectory = Path.Combine(HttpContext.Current.Request.PhysicalApplicationPath, "bin");
					}
					catch
					{
						currentDirectory = Path.Combine(HttpContext.Current.Server.MapPath("~/"), "bin");
					}
				}
				else
				{
					currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
				}
			}
			catch
			{
				currentDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
			}

			return currentDirectory;
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object Update(Licensing.ConfigurationType parameters)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				bool isYoizenAdmin = (bool) HttpContext.Current.Session["IsSuper"];
				if (!isYoizenAdmin)
					throw new Exception("El usuario logueado no tiene permisos");

				var license = Licensing.LicenseManager.Instance.License.Configuration;
				license.AllowAgentsToFavouriteTwitterMessages = parameters.AllowAgentsToFavouriteTwitterMessages;
				license.AllowAgentsToFollowUnfollowTwitter = parameters.AllowAgentsToFollowUnfollowTwitter;
				license.AllowAgentsToHideFacebookMessages = parameters.AllowAgentsToHideFacebookMessages;
				license.AllowAgentsToLikeFacebookMessages = parameters.AllowAgentsToLikeFacebookMessages;
				license.AllowAgentsToReturnMessagesToQueue = parameters.AllowAgentsToReturnMessagesToQueue;
				license.AllowAgentsToReturnMessagesToSpecifiedQueue = parameters.AllowAgentsToReturnMessagesToSpecifiedQueue;
				license.AllowAgentsToRetweetMessages = parameters.AllowAgentsToRetweetMessages;
				license.AllowAutomaticExport = parameters.AllowAutomaticExport;
				license.AllowChatWithYFlowHistory = parameters.AllowChatWithYFlowHistory;
				license.AllowCoaching = parameters.AllowCoaching;
				license.AllowCustomBusinessDataRegex = parameters.AllowCustomBusinessDataRegex;
				license.AllowDisconnectAgents = parameters.AllowDisconnectAgents;
				license.AllowIntegrations = parameters.AllowIntegrations;
				license.AllowSupervisorsToReplyMessages = parameters.AllowSupervisorsToReplyMessages;
				license.AllowToConfigureACDBalancing = parameters.AllowToConfigureACDBalancing;
				license.AllowToExtendBusinessData = parameters.AllowToExtendBusinessData;
				license.AllowToExtendCase = parameters.AllowToExtendCase;
				license.AllowToExtendProfile = parameters.AllowToExtendProfile;
				license.AllowToHidePoweredBy = parameters.AllowToHidePoweredBy;
				license.AllowToSendWhatsappHSMMassiveByExternalIntegrations = parameters.AllowToSendWhatsappHSMMassiveByExternalIntegrations;
				license.AllowToSetAuxReasonsByAgents = parameters.AllowToSetAuxReasonsByAgents;
				license.AllowUrlLogin = parameters.AllowUrlLogin;
				license.AllowWhatsappOutbound = parameters.AllowWhatsappOutbound;
				license.AllowWhatsappOutboundService = parameters.AllowWhatsappOutboundService;
				license.EncryptMessages = parameters.EncryptMessages;
				license.ExternalSurveysEnabled = parameters.ExternalSurveysEnabled;
				license.MaxConcurrentAgents = parameters.MaxConcurrentAgents;
				license.MaxNominalUsers = parameters.MaxNominalUsers;
				license.SurveysEnabled = parameters.SurveysEnabled;
				license.AllowToCreateServices = parameters.AllowToCreateServices;
				license.MaxServicesToCreate = parameters.MaxServicesToCreate;
				license.AllowServicesAsChats = parameters.AllowServicesAsChats;
				license.AllowToMarkAsReadWhatsappMessages = parameters.AllowToMarkAsReadWhatsappMessages;
				license.AllowToGenerateWhatsappHSMJwt = parameters.AllowToGenerateWhatsappHSMJwt;
				license.AllowWhatsappOutboundWithoutCaseCreation = parameters.AllowWhatsappOutboundWithoutCaseCreation;
				license.AllowWhatsappOutboundWithoutCaseWithContext = parameters.AllowWhatsappOutboundWithoutCaseWithContext;
				license.SendToServiceBusInsteadOfDirectSend = parameters.SendToServiceBusInsteadOfDirectSend;
				license.AllowToSaveAttachmentsInAzureStorage = parameters.AllowToSaveAttachmentsInAzureStorage;
				license.SaveReportsInStorage = parameters.SaveReportsInStorage;
				license.DetailedReportsWithManyDaysByExporter = parameters.DetailedReportsWithManyDaysByExporter;
				license.AllowToSearchMessagesByBody = parameters.AllowToSearchMessagesByBody;
				license.AllowToSearchChatsByBody = parameters.AllowToSearchChatsByBody;
				license.AllowAgentsToTransferMessagesToYFlow = parameters.AllowAgentsToTransferMessagesToYFlow;
				license.AllowAgentsToStartVideoCall = parameters.AllowAgentsToStartVideoCall;
				license.YoizenAdminCanDoSupervisorActions = parameters.YoizenAdminCanDoSupervisorActions;
				license.YoizenAdminGroup = parameters.YoizenAdminGroup;

				if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
					Core.System.Instance.EventsService != null)
				{
					var @event = new DomainModel.Events.DomainObjectSyncEvent();
					@event.Action = SystemActionTypes.Edit;
					@event.EntityType = SystemEntityTypes.License;
					Core.System.Instance.EventsService.PublishEvent(@event);
				}

				HttpContext.Current.Application.Lock();
				HttpContext.Current.Application["LicenseJson"] = null;
				HttpContext.Current.Application.UnLock();

				return new
				{
					Success = true
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		#endregion
	}
}