﻿using System;
using System.Net;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.Settings;

namespace Yoizen.Social.Web
{
    public partial class AccessYUsage : LoginRequiredBasePage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Licensing.LicenseManager.Instance.License.Configuration.AllowYUsage ||
                !DomainModel.SystemSettings.Instance.YUsage.Enabled)
                return;

            var yUsageSettings = DomainModel.SystemSettings.Instance.YUsage;
            var token = yUsageSettings.AccessToken;

            var apiUrl = yUsageSettings.UrlApi;
            if (!apiUrl.EndsWith("/"))
                apiUrl += "/";
            apiUrl += "api/auth/validate_token";

            var client = new System.Net.WebClient();
            client.Headers[HttpRequestHeader.ContentType] = "application/json";
            client.Headers[HttpRequestHeader.Authorization] = "Bearer " + token;

            try
            {
                var responseJson = client.UploadString(apiUrl, "POST", "");
                dynamic response = Newtonsoft.Json.JsonConvert.DeserializeObject(responseJson);

                if (response.token != null && response.role != null && response.client != null)
                {
                    var appUrl = yUsageSettings.Url;
                    if (!appUrl.EndsWith("/"))
                        appUrl += "/";

                    var redirectUrl = appUrl +
                        "?auth=" + System.Web.HttpUtility.UrlEncode((string)response.token);

                    Response.Redirect(redirectUrl, endResponse: true);
                }
                else
                {
                    messageRedirectFailed.Visible = true;
                }
            }
            catch (Exception ex)
            {
                messageRedirectFailed.Visible = true;

                System.Diagnostics.Debug.WriteLine("Error al validar token: " + ex.Message);
            }
        }



    }
}
