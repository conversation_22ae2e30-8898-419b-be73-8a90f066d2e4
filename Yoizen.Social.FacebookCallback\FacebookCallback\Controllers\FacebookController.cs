﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Web;
using System.Threading.Tasks;
using Microsoft.Azure.Amqp.Framing;
using Newtonsoft.Json;
using System.Collections.Specialized;

namespace FacebookCallback.Controllers
{
	public class FacebookController : ApiController
	{
		#region Constants

		private const string SecretKeyForHashing = "b29780205f1d02e838cf75ff19f64c76";

		#endregion

		#region Action Methods

		// GET: api/Facebook
		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(
			[FromUri(Name = "hub.mode")] string hubMode,
			[FromUri(Name = "hub.challenge")] string hubChallenge,
			[FromUri(Name = "hub.verify_token")] string hubVerifyToken)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			if (hubMode.Equals("subscribe") && hubVerifyToken.Equals(FacebookManager.VerifyToken))
			{
				string result = hubChallenge;
				var resp = new HttpResponseMessage(HttpStatusCode.OK)
				{
					Content = new StringContent(result, Encoding.UTF8, "text/plain")
				};
				return resp;
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare)
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			return Request.CreateResponse(HttpStatusCode.OK, new FacebookManager().GetPageLastNews(id));
		}

		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get(string id, bool messenger, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			string hashToCompare = this.Request.Headers.FirstOrDefault(x => x.Key == "hash").Value.FirstOrDefault();

			string queryString = this.Request.RequestUri.Query;
			string hashHmacHex = Utils.HashHMACHex(SecretKeyForHashing, queryString);

			if (hashHmacHex != hashToCompare)
				return new HttpResponseMessage(HttpStatusCode.Unauthorized);

			if (messenger)
				return Request.CreateResponse(HttpStatusCode.OK, new FacebookManager().GetPageLastNewsWithMessenger(id));
			else
				return Request.CreateResponse(HttpStatusCode.OK, new FacebookManager().GetPageLastNews(id));
		}

		// POST: api/Facebook
		[ActionName("DefaultAction")]
		[HttpPost]
		public async Task<HttpResponseMessage> Post()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			if (!FacebookManager.IgnoreXHubSignatureHeader)
			{
				if (Request.Headers.Contains("X-Hub-Signature-256"))
				{
					string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature-256").First();
					if (!xHubSignatureHeader.StartsWith("sha256="))
					{
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature-256 no empieza con sha256=, si no que fue {1}", body, xHubSignatureHeader);
						return new HttpResponseMessage(HttpStatusCode.OK);
					}
					xHubSignatureHeader = xHubSignatureHeader.Substring(7);

					string bodyHash = Encode256(body, FacebookManager.ApplicationSecret);

					if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
					{
						if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase) ||
							Request.RequestUri.AbsoluteUri.StartsWith("https://dev.ysocial.net", StringComparison.InvariantCultureIgnoreCase))
							Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature-256 contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
						else
							Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature-256 contiene una firma incorrecta", body);

						return new HttpResponseMessage(HttpStatusCode.OK);
					}
				}
				else
				{
					if (!Request.Headers.Contains("X-Hub-Signature"))
					{
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Hub-Signature", body);
						return new HttpResponseMessage(HttpStatusCode.OK);
					}

					string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
					if (!xHubSignatureHeader.StartsWith("sha1="))
					{
						Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
						return new HttpResponseMessage(HttpStatusCode.OK);
					}
					xHubSignatureHeader = xHubSignatureHeader.Substring(5);

					string bodyHash = Encode(body, FacebookManager.ApplicationSecret);

					if (!bodyHash.Equals(xHubSignatureHeader, StringComparison.InvariantCultureIgnoreCase))
					{
						if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase) ||
							Request.RequestUri.AbsoluteUri.StartsWith("https://dev.ysocial.net", StringComparison.InvariantCultureIgnoreCase))
							Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta. Debería contener: {1}", body, bodyHash);
						else
							Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);

						return new HttpResponseMessage(HttpStatusCode.OK);
					}
				}
			}

			try
			{
				FacebookManager facebookManager = new FacebookManager();
				await facebookManager.GenerateFiles(body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		// POST: api/Facebook
		[ActionName("Test")]
		[HttpPost]
		public async Task<HttpResponseMessage> Test(string id)
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			if (!Request.Headers.Contains("X-Hub-Signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header X-Hub-Signature", body);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			string xHubSignatureHeader = Request.Headers.GetValues("X-Hub-Signature").First();
			if (!xHubSignatureHeader.StartsWith("sha1="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature no empieza con sha1=, si no que fue {1}", body, xHubSignatureHeader);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}
			xHubSignatureHeader = xHubSignatureHeader.Substring(5);

			if (!xHubSignatureHeader.Equals("pepepe", StringComparison.InvariantCultureIgnoreCase))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header X-Hub-Signature contiene una firma incorrecta", body);

				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			try
			{
				FacebookManager facebookManager = new FacebookManager();
				await facebookManager.GenerateFiles(body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("NewsProcessed")]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			try
			{
				FacebookManager facebookManager = new FacebookManager();
				facebookManager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("Restart")]
		[HttpDelete]
		public HttpResponseMessage Restart(string id)
		{
			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se invocó la eliminación de los archivos de novedades de la página {0}", id);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			try
			{
				FacebookManager facebookManager = new FacebookManager();
				facebookManager.DeleteAllContents(id);

				Yoizen.Common.Tracer.TraceInfo("Se eliminó los archivos de novedades de la página {0}", id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de novedades de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("deleteuserdata")]
		[Route("api/facebook/delete")]
		[HttpPost]
		public async Task<HttpResponseMessage> PostDeleteUserData()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);
			
			try
			{
				var signedRequest = body.Substring(body.IndexOf("=") + 1);

				var parts = signedRequest.Split('.');
				if (parts.Length != 2)
					return Request.CreateResponse(HttpStatusCode.BadRequest, new { error = "Invalid signed_request format" });

				string encodedSig = parts[0];
				string encodedPayload = parts[1];

				// Decode data
				var signature = Base64UrlDecode(encodedSig);
				var payloadJson = Encoding.UTF8.GetString(Base64UrlDecode(encodedPayload));

				// Verify signature
				var expectedSig = GetHmacSha256Hash(encodedPayload, FacebookManager.ApplicationSecret);
				if (!signature.SequenceEqual(expectedSig))
					return Request.CreateResponse(HttpStatusCode.BadRequest, new { error = "Invalid signature" });

				var jBody = Newtonsoft.Json.Linq.JObject.Parse(payloadJson);

				var userId = jBody["user_id"].ToString();

				return Request.CreateResponse(HttpStatusCode.OK, new
				{
					url = $"https://callback.ysocial.net/FacebookUserDeletionStatus.html?id={userId}&ts={DateTimeOffset.UtcNow.ToUnixTimeSeconds()}",
					confirmation_code = userId.GetHashCode().ToString()
				});
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error tratando el pedido de borrado: {0}", ex);

				return Request.CreateResponse(HttpStatusCode.InternalServerError, new
				{
					Success = false,
					Error = new
					{
						Message = "couldn't process user deletion data request"
					}
				});
			}
		}

		#endregion

		#region Private Methods & Utils

		private static string Encode(string input, string key)
		{
			var keyBytes = Encoding.UTF8.GetBytes(key);
			using (HMACSHA1 myhmacsha1 = new HMACSHA1(keyBytes))
			{
				var bytes = Encoding.UTF8.GetBytes(input);
				using (var stream = new System.IO.MemoryStream(bytes))
				{
					return myhmacsha1.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
				}
			}
		}

		private static string Encode256(string input, string key)
		{
			var keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha256 = new HMACSHA256(keyBytes))
			{
				var bytes = Encoding.UTF8.GetBytes(input);
				using (var stream = new System.IO.MemoryStream(bytes))
				{
					return myhmacsha256.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
				}
			}
		}

		private static byte[] GetHmacSha256Hash(string data, string key)
		{
			var keyBytes = Encoding.UTF8.GetBytes(key);
			var dataBytes = Encoding.UTF8.GetBytes(data);

			using (var hmac = new HMACSHA256(keyBytes))
			{
				return hmac.ComputeHash(dataBytes);
			}
		}

		private static byte[] Base64UrlDecode(string input)
		{
			input = input.Replace('-', '+').Replace('_', '/');
			switch (input.Length % 4)
			{
				case 2: input += "=="; break;
				case 3: input += "="; break;
			}
			return Convert.FromBase64String(input);
		}

		#endregion
	}
}