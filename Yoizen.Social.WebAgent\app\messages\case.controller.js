﻿(function () {

    'use strict';

    angular.module('socialApp')
        .controller('CaseController', CaseController);

    CaseController.$inject = [
        'panels',
        '$rootScope',
        'messagesService',
        'agentService',
        'profileService',
        'modalSocialService',
        'settingsService',
        'CONFIG_INFO',
        'utilsService',
        '$sce',
        '$translate',
        '$scope',
        'outgoingService',
        'toastr',
        'traceService',
        '$timeout',
        '$log',
        'commonActionsService',
        'integrationsService',
        'statesService'
    ];

    function CaseController(panels,
                            $rootScope,
                            messagesService,
                            agentService,
                            profileService,
                            modalSocialService,
                            settingsService,
                            CONFIG_INFO,
                            utilsService,
                            $sce,
                            $translate,
                            $scope,
                            outgoingService,
                            toastr,
                            traceService,
                            $timeout,
                            $log,
                            commonActionsService,
                            integrationsService,
                            statesService) {

        var noTextForThisMessage, errorInAttachmentPreview, removePengindMessageSuccess, removePengindMessageError, modalConfirmRemovePendingMarkTitle, modalConfirmRemovePendingMarkDescription;

        var vm = this;

        if (typeof(vm.isMyOutgoingCases) !== 'boolean') {
            vm.isMyOutgoingCases = false;
        }

        if (typeof(vm.insideModal) !== 'boolean') {
            vm.insideModal = false;
        }

        vm.case = {};
        vm.showActionButtons = showActionButtons;
        vm.showMessageDetails = showMessageDetails;
        vm.getMessageUserName = getMessageUserName;
        vm.getUserAvatar = getUserAvatar;
        vm.getServiceIcon = getServiceIcon;
        vm.showAttachmentPopup = showAttachmentPopup;
        vm.attachmentIsAnImage = attachmentIsAnImage;
        vm.attachmentIsAudio = attachmentIsAudio;
        vm.attachmentIsVideo = attachmentIsVideo;
        vm.getImageUrl = getImageUrl;
        vm.getFileIcon = getFileIcon;
        vm.showReturnToQueue = showReturnToQueue;
        vm.showTransferToQueue = showTransferToQueue;
        vm.showReturnToYFlow = showReturnToYFlow;
        vm.discardCase = discardCase;
        vm.answerMessage = answerMessage;
        vm.answerMessageForMyCases = answerMessageForMyCases;
        vm.answerMessageWithHSMForMyCases = answerMessageWithHSMForMyCases;
        vm.answerMessageWithHSM = answerMessageWithHSM;
        vm.answerMessageWithMessengerTag = answerMessageWithMessengerTag;
        vm.messagesService = messagesService;
        vm.getServiceAvatar = getServiceAvatar;
        vm.socialServiceTypes = SocialServiceTypes;
        vm.loadMail = loadMail;
        vm.allowedToDiscard = !this.caseReadOnly && settingsService.settings.agent.allowedToDiscard;
        vm.allowedToReply = true;
        vm.allowedToSendHSM = false;
        vm.allowedToReplyWhatsappWithHSM = false;
        vm.allowedToReplyMessengerWithTag = false;
        vm.showActionButtonsForMyCases = false;
        vm.allowedToReturnToQueue = !this.caseReadOnly && agentService.agentIsAllowedToReturnToQueue();
        vm.allowedToTransferToQueue = !this.caseReadOnly && agentService.agentIsAllowedToTransferMessage();
        vm.allowedToSendAudio = !this.caseReadOnly && settingsService.settings.agent.allowedToSendAudio;
        vm.allowedToReturnToYFlow = false;
        vm.showEditCaseDetailsModal = showEditCaseDetailsModal;
        vm.generateMailOutgoing = generateMailOutgoing;
        vm.getProfileName = getProfileName;
        vm.showSocialUserProfile = showSocialUserProfile;
        vm.getBusinessData = getBusinessData;
        vm.getPreviousMessages = getPreviousMessages;
        vm.getNewEnqueuedMessages = getNewEnqueuedMessages;
        vm.isMail = isMail;
        vm.isMailAndCanGenerateOutgoing = isMailAndCanGenerateOutgoing;
        vm.isChat = isChat;
        vm.isCollapsed = true;
        vm.removeMarkAsPendingMessage = removeMarkAsPendingMessage;
        vm.showRemoveMarkAsPending = showRemoveMarkAsPending;
        vm.actAsChat = false;
        vm.showCasePanel = showCasePanel;
        vm.allowedToSpeak = false;
        vm.acceptToSpeak = true;
        vm.transcript = '';
        vm.noMobile = true;
        vm.generateOutgoingMail = false;

        if (typeof(vm.socialCase.actAsChatInfo) === 'object') {
            vm.actAsChatInfo = vm.socialCase.actAsChatInfo;
            vm.actAsChatInfo.typing = false;
            vm.actAsChatInfo.wasTyping = false;
        }
        else {
            vm.actAsChatInfo = createActAsChatInfo();
        }

        vm.actAsChatAddMessageToCase = actAsChatAddMessageToCase;
        vm.actAsChatOnKeydown = actAsChatOnKeydown;
        vm.handleSendClick = handleSendClick;
        vm.actAsChatOnChange = actAsChatOnChange;
        vm.actAsChatStopRecording = actAsChatStopRecording;
        vm.actAsChatDeleteRecording = actAsChatDeleteRecording;
        vm.actAsChatSendAudio = actAsChatSendAudio;
        vm.actAsChatDeleteAudio = actAsChatDeleteAudio;
        vm.actAsChatAttachFile = actAsChatAttachFile;
        vm.actAsChatShortUrls = actAsChatShortUrls;
        vm.actAsChatShowPredefinedAnswers = actAsChatShowPredefinedAnswers;
        vm.actAsChatShowForms = actAsChatShowForms;
        vm.actAsChatRecordAudio = actAsChatRecordAudio;
        vm.actAsChatSpeechToText = actAsChatSpeechToText;
        vm.actAsChatSendWhatsappVoiceCallInteractiveMessage = actAsChatSendWhatsappVoiceCallInteractiveMessage;
        vm.imagePastedUnsuscriber = null;
        vm.actAsChatInfo.attachments = [];

        let browserInfo = bowser.getParser(window.navigator.userAgent);
        if (browserInfo.parsedResult.platform.type.toLowerCase() === 'mobile') {
            vm.noMobile = false;
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (typeof (SpeechRecognition) !== 'undefined') {
            vm.allowedToSpeak = true;
        }

        vm.buttonLoadingOptions = {
            text: $translate.instant('GET_PREVIOUS_MESSAGES'),
            cssClass: 'btn-refresh',
            hideLoading: true,
            textLoading: $translate.instant('LOADING_PREVIOUS_MESSAGES'),
            cssClassLoading: 'btn-refresh'
        };

        vm.buttonLoadingNewMessagesOptions = {
            text: $translate.instant('GET_NEW_MESSAGES'),
            cssClass: 'btn-refresh',
            hideLoading: true,
            textLoading: $translate.instant('LOADING_NEW_MESSAGES'),
            cssClassLoading: 'btn-refresh'
        };

        vm.scrollToFixedOptions = {
            'marginTop': 81
        };

        if (!vm.caseReadOnly &&
            !vm.isMyOutgoingCases &&
            !agentService.isShowMyCasesNoChangeState
            ) {
            $scope.$watch(function () {
                if (typeof (messagesService.selectedSocialCase) !== 'undefined') {
                    return messagesService.selectedSocialCase.data.id.toString();
                }
            }, function (newVal, oldVal) {
                if (newVal !== oldVal) {
                    vm.socialCase = messagesService.selectedSocialCase;
                    vm.buttonLoadingOptions.hideLoading = true;

                    if (typeof(vm.socialCase.actAsChatInfo) === 'object') {
                        vm.actAsChatInfo = vm.socialCase.actAsChatInfo;
                        vm.actAsChatInfo.typing = false;
                        vm.actAsChatInfo.wasTyping = false;
                    }
                    else {
                        vm.actAsChatInfo = createActAsChatInfo();
                    }

                    console.log(`Se utilizará la información de actAsChat del mensaje ${vm.actAsChatInfo.messageId} y el mensaje actual es ${vm.socialCase.data.id}`);

                    activate();

                    if (vm.actAsChat) {
                        $timeout(function () {
                            $scope.$apply();
                        });
                    }

                    utilsService.scrollToBottom();
                }
            });
        }

        $scope.$on('messageSelectedToAnswer', function(e, message) {
            vm.actAsChatInfo.contextMessage = message;
        });

        $scope.$on('currentCaseEvent', function(e, event) {
            activate();
        });

        $scope.$on('voiceCall', function(e, event) {
            if (vm.socialCase.data.case.id === event.caseId) {
                console.log(`Se recibió evento de llamadas del caso actual: `, event);

                if (typeof(vm.socialCase.actAsChatInfo) === 'undefined') {
                    vm.socialCase.actAsChatInfo = vm.actAsChatInfo;
                }
                
                switch (event.event) {
                    case 'connected':
                        vm.socialCase.actAsChatInfo.isInCall = true;
                        break;
                    case 'received':
                        vm.socialCase.actAsChatInfo.isInCall = true;
                        break;
                    case 'recording_started':
                        break;
                    case 'terminated':
                        vm.socialCase.actAsChatInfo.isInCall = false;
                        break;
                    case 'rejected':
                        vm.socialCase.actAsChatInfo.isInCall = false;
                        break;
                }
            }
            else {
                console.log(`Se recibió evento de llamadas de otro caso ${event.caseId} que no es el actual: `, event);

                let message = messagesService.getMessageByCaseId(event.caseId);
                if (message !== null &&
                    typeof(message.socialCase) === 'object' &&
                    message.socialCase !== null &&
                    typeof(message.socialCase.actAsChatInfo) === 'object') {
                    switch (event.event) {
                        case 'connected':
                            message.socialCase.actAsChatInfo.isInCall = true;
                            break;
                        case 'received':
                            message.socialCase.actAsChatInfo.isInCall = true;
                            break;
                        case 'recording_started':
                            break;
                        case 'terminated':
                            message.socialCase.actAsChatInfo.isInCall = false;
                            break;
                        case 'rejected':
                            message.socialCase.actAsChatInfo.isInCall = false;
                            break;
                    }
                }
            }
        });

        let configurations = utilsService.toCamel(socialServiceTypesConfiguration);

        activate();

        function showRemoveMarkAsPending() {
            if (vm.caseReadOnly) {
                return false;
            }

            if (!vm.isMyOutgoingCases &&
                !agentService.isShowMyCasesNoChangeState) {
                return false;
            }

            if (!messagesService.caseHasPendingMark(vm.socialCase.data.case)) {
                return false;
            }

            return true;
        }

        function createActAsChatInfo() {
            const agentInfo = settingsService.getAgent();

            let info = {
                text: '',
                canAttach: false,
                canCloseCase: agentInfo.allowedToCloseCases,
                attachmentsQueue: [],
                attachments: [],
                showBackground: false,
                allowedToRepy: false,
                addMessageToCase: actAsChatAddMessageToCase,
                attachFile: actAsChatAttachFile,
                shortUrls: actAsChatShortUrls,
                showPredefinedAnswers: actAsChatShowPredefinedAnswers,
                speechToText: actAsChatSpeechToText,
				recordAudio : actAsChatRecordAudio,
				recorder: MediaRecorder,
				stop: true,
				blobAudio: Blob,
				sendAudio: actAsChatSendAudio,
				stopRecording : actAsChatStopRecording,
				deleteRecording: actAsChatDeleteRecording,
				deleteAudio: actAsChatDeleteAudio,
                onKeydown: actAsChatOnKeydown,
                onChange: actAsChatOnChange,
                handleOnChange: false,
                typing: false,
                wasTyping: false,
                onImagePasted: actAsChatOnImagePasted,
                finishMessage: actAsChatFinishMessage,
                onImagePastedUnsuscriber: null,
                contextMessage: null,
                removeContextMessage: actAsChatRemoveContextMessage,
                getContextMessageBody: actAsChatGetContextMessageBody,
                getContextMessageUser: actAsChatGetContextMessageUser,
                addedMessages: 0,
                loading: false,
				isRecording: false,
				noAudio: true,
                allowedToSendForms: false,
                allowedToSendWhatsappVoiceCallInteractiveMessage: false,
                showForms: actAsChatShowForms,
                canAnswer: canAnswer,
                evaluateFlag: evaluateFlag,
                isInCall: false
            };

            if (typeof (messagesService.selectedSocialCase) === 'object' &&
                typeof (messagesService.selectedSocialCase.data) === 'object') {
                info.handleOnChange = messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.AppleMessaging;
                info.messageId = messagesService.selectedSocialCase.data.id;
                info.caseId = messagesService.selectedSocialCase.data.case.id;

                if (messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.WhatsApp &&
                    typeof(agentInfo.whatsAppVoiceCallsEnabled) === 'boolean' &&
                    agentInfo.whatsAppVoiceCallsEnabled) {
                    let service = messagesService.selectedSocialCase.data.service;
                    info.allowedToSendWhatsappVoiceCallInteractiveMessage = typeof(service.settings.voiceCallsEnabled) === 'boolean' &&
                        service.settings.voiceCallsEnabled;
                }
            }

            return info;
        }

        function activate() {
            vm.hideLoadPreviousMessagesButton = false;
            vm.hideLoadNewMessagesButton = false;
            vm.actAsChat = false;

            if (vm.isMyOutgoingCases) {
                vm.allowedToReply = true;
                vm.allowedToReturnToQueue = false;
                vm.allowedToTransferToQueue = false;
                vm.allowedToDiscard = false;
                vm.allowedToReplyWhatsappWithHSM = false;
                vm.allowedToReplyMessengerWithTag = false;
                vm.allowedToReturnToYFlow = false;
                if (vm.socialCase !== null &&
                    typeof(vm.socialCase.data) !== 'undefined') {
                    let messageData = vm.socialCase.data;

                    if (vm.caseReadOnly) {
                        vm.showActionButtonsForMyCases = false;
                    }
                    else if (!vm.isMyOutgoingCases &&
                        !agentService.isShowMyCasesNoChangeState) {
                        vm.showActionButtonsForMyCases = false;
                    }
                    else if (typeof (vm.socialCase.data.actualData) !== 'undefined' &&
                        vm.socialCase.data.actualData !== null &&
                        typeof (vm.socialCase.data.actualData.answer) !== 'undefined' &&
                        vm.socialCase.data.actualData.answer !== null &&
                        vm.socialCase.data.actualData.answer.isAnswering) {
                        vm.showActionButtonsForMyCases = false;
                    }
                    else if (typeof (messageData.socialServiceType) === 'undefined') {
                        vm.showActionButtonsForMyCases = false;
                    }
                    else {
                        vm.showActionButtonsForMyCases = true;
                    }

                    if (messageData.socialServiceType === SocialServiceTypes.WhatsApp) {
                        checkWhatsappSettings();
                    }
                }


                if (messagesService.caseHasPendingMark(vm.socialCase.data.case)) {
                    toastr.info($translate.instant('CASE_HAS_PENDING_MARKS'));
                }

                console.log('No hay ninguna acción permitida con el mensaje y estamos en mensaje saliente');
            }
            else {
                if (!vm.caseReadOnly && typeof (messagesService.selectedSocialCase) !== 'undefined') {
                    vm.allowedToDiscard = settingsService.settings.agent.allowedToDiscard;
                    vm.allowedToReturnToQueue = agentService.agentIsAllowedToReturnToQueue();
                    vm.allowedToTransferToQueue = agentService.agentIsAllowedToTransferMessage();
                    vm.allowedToReply = true;
                    vm.allowedToReplyWhatsappWithHSM = false;
                    vm.allowedToReplyMessengerWithTag = false;
                    vm.allowedToReturnToYFlow = false;

                    if (messagesService.selectedSocialCase !== null &&
                        typeof (messagesService.selectedSocialCase.data) !== 'undefined' &&
                        messagesService.selectedSocialCase.data !== null) {
                        let messageData = messagesService.selectedSocialCase.data;
                        vm.serviceConfiguration = utilsService.getObjectByIdInArray(configurations, messageData.socialServiceType).configuration;
                        vm.supportsPublicPrivate = vm.serviceConfiguration.supportsPublicAndPrivate;
                        vm.actAsChat = typeof(messagesService.selectedSocialCase.data.service.settings.actAsChat) === 'boolean' &&
                            messagesService.selectedSocialCase.data.service.settings.actAsChat;

                        if (messageData.socialServiceType === SocialServiceTypes.WhatsApp) {
                            checkWhatsappSettings();
                        }
                        else if (messageData.socialServiceType === SocialServiceTypes.FacebookMessenger) {
                            checkMessengerSettings();
                        }
                        else if (messageData.socialServiceType === SocialServiceTypes.Facebook && messageData.isDirectMessage) {
                            checkFacebookSettings();
                        }
                        else if (messageData.socialServiceType === SocialServiceTypes.AppleMessaging) {
                            checkAppleMessagingSettings();
                        }
                        else if (messageData.socialServiceType === SocialServiceTypes.GoogleRBM) {
                            vm.allowedToSendAudio = false;
                        }
                        else if (messageData.socialServiceType === SocialServiceTypes.Instagram) {
                            checkInstagramSettings();
                        }

                        if (typeof (messageData.service.usesYFlow) === 'boolean' &&
                            messageData.service.usesYFlow) {
                            if (messageData.socialServiceType !== SocialServiceTypes.Chat &&
                                typeof (messageData.service.yFlowSettings) === 'object' &&
                                messageData.service.yFlowSettings !== null) {
                                vm.allowedToReturnToYFlow = vm.allowedToReply &&
                                    settingsService.settings.context.license.allowAgentsToTransferMessagesToYFlow &&
                                    messageData.service.yFlowSettings.allowAgentsToReturnMessagesToYFlow &&
                                    messageData.service.yFlowSettings.returnsFromAgents !== null &&
                                    messageData.service.yFlowSettings.returnsFromAgents.length > 0 &&
                                    messageData.case.parameters !== null &&
                                    typeof (messageData.case.parameters.yFlowDoNotInvoke) !== 'undefined' &&
                                    messageData.case.parameters.yFlowDoNotInvoke === 'True';

                                if (vm.allowedToReturnToYFlow) {
                                    if (messageData.socialServiceType === SocialServiceTypes.Facebook && !messageData.isDirectMessage) {
                                        vm.allowedToReturnToYFlow = false;
                                    }
                                    else if (messageData.socialServiceType === SocialServiceTypes.Twitter && !messageData.isDirectMessage) {
                                        vm.allowedToReturnToYFlow = false;
                                    }
                                }
                            }
                        }

                        if (!vm.allowedToReply &&
                            !vm.allowedToDiscard) {
                            if (messageData.socialServiceType === SocialServiceTypes.WhatsApp &&
                                !vm.allowedToReplyWhatsappWithHSM) {
                                console.log('No hay ninguna acción de respuesta permitida con el mensaje. Se habilita descartar');
                                vm.allowedToDiscard = true;
                            }
                            else if (messageData.socialServiceType === SocialServiceTypes.FacebookMessenger &&
                                !vm.allowedToReplyMessengerWithTag) {
                                console.log('No hay ninguna acción de respuesta permitida con el mensaje. Se habilita descartar');
                                vm.allowedToDiscard = true;
                            }
                        }
                    }

                    if (messagesService.caseHasPendingMark()) {
                        toastr.info($translate.instant('CASE_HAS_PENDING_MARKS'));
                    }

                    if (!vm.allowedToReply &&
                        !vm.allowedToReplyMessengerWithTag &&
                        !vm.allowedToDiscard &&
                        !vm.allowedToReplyWhatsappWithHSM &&
                        !vm.allowedToReturnToQueue &&
                        !vm.allowedToTransferToQueue) {
                        console.log('No hay ninguna acción permitida con el mensaje. Se habilita descartar');
                        vm.allowedToDiscard = true;
                    }
                }
            }

            $translate([
                'NO_TEXT_AVAILABLE_FOR_THIS_MESSAGE',
                'ERROR_IN_ATTACHMENT_PREVIEW',
                'REMOVE_PENDING_MESSAGE_SUCCESS',
                'REMOVE_PENDING_MESSAGE_ERROR',
                'MODAL_CONFIRM_REMOVE_PENDING_MARK_DESCRIPTION',
                'MODAL_CONFIRM_REMOVE_PENDING_MARK_TITLE'
            ])
                .then(function (translations) {
                    noTextForThisMessage = translations.NO_TEXT_AVAILABLE_FOR_THIS_MESSAGE;
                    errorInAttachmentPreview = translations.ERROR_IN_ATTACHMENT_PREVIEW;
                    removePengindMessageSuccess = translations.REMOVE_PENDING_MESSAGE_SUCCESS;
                    removePengindMessageError = translations.REMOVE_PENDING_MESSAGE_ERROR;
                    modalConfirmRemovePendingMarkDescription = translations.MODAL_CONFIRM_REMOVE_PENDING_MARK_DESCRIPTION;
                    modalConfirmRemovePendingMarkTitle = translations.MODAL_CONFIRM_REMOVE_PENDING_MARK_TITLE;
                });

            vm.actAsChatInfo.showBackground = false;
            if (vm.actAsChat) {
                if (vm.socialCase.data.socialServiceType === SocialServiceTypes.WhatsApp ||
                    vm.socialCase.data.socialServiceType === SocialServiceTypes.Telegram) {
                    vm.actAsChatInfo.showBackground = true;
                }
            }

            if (!vm.allowedToReply) {
                vm.actAsChatInfo.allowedToRepy = false;
            }
            else if (vm.actAsChat) {
                vm.actAsChatInfo.allowedToRepy = true;
                vm.allowedToReplyMessengerWithTag = false;
                vm.allowedToReplyWhatsappWithHSM = false;
            }

            if (vm.actAsChat && vm.actAsChatInfo.allowedToRepy) {
                switch (vm.socialCase.data.socialServiceType) {
                    case SocialServiceTypes.AppleMessaging:
                        vm.actAsChatInfo.canAttach = vm.socialCase.data.service.settings.allowToSendAttachments;
                        vm.actAsChatInfo.allowedToSendForms = typeof(vm.socialCase.data.service.settings.forms) === 'object' &&
                            Array.isArray(vm.socialCase.data.service.settings.forms) &&
                            vm.socialCase.data.service.settings.forms.length > 0;
                        break;

                    case SocialServiceTypes.WhatsApp:
                    case SocialServiceTypes.Telegram:
                    case SocialServiceTypes.FacebookMessenger:
                    case SocialServiceTypes.Twitter:
                    case SocialServiceTypes.GoogleRBM:
                        vm.actAsChatInfo.canAttach = vm.socialCase.data.service.settings.allowToSendAttachments;
                        break;
                }

                actAsChatScrollToBottom();

                if (vm.imagePastedUnsuscriber === null) {
                    vm.imagePastedUnsuscriber = $scope.$on("ImagePasted", actAsChatOnImagePasted);
                }

                if (typeof(vm.socialCase.actAsChatInfo) === 'undefined') {
                    vm.socialCase.actAsChatInfo = vm.actAsChatInfo;
                }
            }
            else {
                if (vm.imagePastedUnsuscriber !== null &&
                    typeof(vm.imagePastedUnsuscriber) === 'function') {
                    vm.imagePastedUnsuscriber();
                    vm.imagePastedUnsuscriber = null;
                }
            }
        }

        function checkMessengerSettings() {
            if (typeof(messagesService.selectedSocialCase.data.service.settings) !== 'undefined' &&
                messagesService.selectedSocialCase.data.service.settings !== null) {
                let settings = messagesService.selectedSocialCase.data.service.settings;
                let maxMinutesToAnswerMessages = 1440;
                if (typeof(settingsService.settings.context.systemSettings['facebookMessenger.MaxMinutesToAnswerMessages']) !== 'undefined') {
                    maxMinutesToAnswerMessages = settingsService.settings.context.systemSettings['facebookMessenger.MaxMinutesToAnswerMessages'];
                    if (typeof(maxMinutesToAnswerMessages) === 'string') {
                        maxMinutesToAnswerMessages = parseInt(maxMinutesToAnswerMessages, 10);
                    }
                }

                if (maxMinutesToAnswerMessages === 1440 &&
                    typeof(messagesService.selectedSocialCase.data.service.settings.allowHumanTag) === 'boolean' &&
                    messagesService.selectedSocialCase.data.service.settings.allowHumanTag) {
                    maxMinutesToAnswerMessages *= 7;
                }

                let sentDate = messagesService.selectedSocialCase.data.date;
                let sentDateMoment = moment(sentDate);
                sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                let nowMoment = moment();

                if (sentDateMoment < nowMoment) {
                    vm.allowedToReply = false;
                    let text = $translate.instant('MESSENGER_CANNOT_REPLY_OUT_OF_WINDOW');
                    messagesService.selectedSocialCase.data.parameters.alertMessage = text;
                    toastr.info(text);
                }

                if (settings.allowToSendTags &&
                    settings.allowAgentsToSendTags &&
                    settings.tagTemplates !== null &&
                    settings.tagTemplates.length > 0 &&
                    typeof(settingsService.settings.agent.allowedToReplyMessengerWithTemplate) === 'boolean' &&
                    settingsService.settings.agent.allowedToReplyMessengerWithTemplate) {
                    vm.allowedToReplyMessengerWithTag = true;
                }
            }
        }

        function checkFacebookSettings() {
            if (typeof(messagesService.selectedSocialCase.data.service.settings) !== 'undefined' &&
                messagesService.selectedSocialCase.data.service.settings !== null) {
                let maxMinutesToAnswerMessages = 1440;
                if (typeof(settingsService.settings.context.systemSettings['facebookMessenger.MaxMinutesToAnswerMessages']) !== 'undefined') {
                    maxMinutesToAnswerMessages = settingsService.settings.context.systemSettings['facebookMessenger.MaxMinutesToAnswerMessages'];
                    if (typeof(maxMinutesToAnswerMessages) === 'string') {
                        maxMinutesToAnswerMessages = parseInt(maxMinutesToAnswerMessages, 10);
                    }
                }

                if (maxMinutesToAnswerMessages === 1440 &&
                    typeof(messagesService.selectedSocialCase.data.service.settings.allowHumanTag) === 'boolean' &&
                    messagesService.selectedSocialCase.data.service.settings.allowHumanTag) {
                    maxMinutesToAnswerMessages *= 7;
                }

                let sentDate = messagesService.selectedSocialCase.data.date;
                let sentDateMoment = moment(sentDate);
                sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                let nowMoment = moment();

                if (sentDateMoment < nowMoment) {
                    vm.allowedToReply = false;
                    let text = $translate.instant('FACEBOOK_CANNOT_REPLY_OUT_OF_WINDOW');
                    if (typeof(messagesService.selectedSocialCase.data.parameters) === 'undefined') {
                        messagesService.selectedSocialCase.data.parameters = {};
                    }
                    messagesService.selectedSocialCase.data.parameters.alertMessage = text;
                    toastr.info(text);
                }
            }
        }

        function checkInstagramSettings() {
            if (typeof(messagesService.selectedSocialCase.data.service.settings) !== 'undefined' &&
                messagesService.selectedSocialCase.data.service.settings !== null) {
                let maxMinutesToAnswerMessages = 1440;
                if (typeof(settingsService.settings.context.systemSettings['instagram.MaxMinutesToAnswerMessages']) !== 'undefined') {
                    maxMinutesToAnswerMessages = settingsService.settings.context.systemSettings['instagram.MaxMinutesToAnswerMessages'];
                    if (typeof(maxMinutesToAnswerMessages) === 'string') {
                        maxMinutesToAnswerMessages = parseInt(maxMinutesToAnswerMessages, 10);
                    }
                }

                if (maxMinutesToAnswerMessages === 1440 &&
                    typeof(messagesService.selectedSocialCase.data.service.settings.allowHumanTag) === 'boolean' &&
                    messagesService.selectedSocialCase.data.service.settings.allowHumanTag) {
                    maxMinutesToAnswerMessages *= 7;
                }

                let sentDate = messagesService.selectedSocialCase.data.date;
                let sentDateMoment = moment(sentDate);
                sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                let nowMoment = moment();

                if (sentDateMoment < nowMoment) {
                    vm.allowedToReply = false;
                    let text = $translate.instant('INSTAGRAM_CANNOT_REPLY_OUT_OF_WINDOW');
                    messagesService.selectedSocialCase.data.parameters.alertMessage = text;
                    toastr.info(text);
                }
            }
        }

        function checkAppleMessagingSettings() {
            if (typeof(messagesService.selectedSocialCase.data) !== 'undefined' &&
                messagesService.selectedSocialCase.data !== null) {
                let postedBy = messagesService.selectedSocialCase.data.postedBy;
                if (typeof(postedBy.parametersByService) === 'object' &&
                    typeof(postedBy.parametersByService[messagesService.selectedSocialCase.data.service.id]) === 'object' &&
                    typeof(postedBy.parametersByService[messagesService.selectedSocialCase.data.service.id].conversationClosed) === 'string' &&
                    postedBy.parametersByService[messagesService.selectedSocialCase.data.service.id].conversationClosed.toLowerCase() === 'true') {
                    vm.allowedToReply = false;
                    let text = $translate.instant('APPLE_CANNOT_REPLY_CONVERSATIONCLOSED');
                    messagesService.selectedSocialCase.data.parameters.alertMessage = text;
                    toastr.info(text);
                }
            }
        }

        function checkWhatsappSettings() {
            let settings;
            let sentDate;
            if (vm.isMyOutgoingCases) {
                let lastIncomingMessage = vm.socialCase.data.case.getLastIncomingMessage();
                if (lastIncomingMessage === null) {
                    if (typeof (vm.socialCase.data.case.lastUpdatedOn) != 'undefined')
                        sentDate = vm.socialCase.data.case.lastUpdatedOn;

                    vm.allowedToReply = false;
                }
                else {
                    vm.allowedToReply = messagesService.messageCanBeReplied(lastIncomingMessage);
                    sentDate = lastIncomingMessage.date;
                }

                let service;
                try {
                    service = settingsService.getService(vm.socialCase.data.case.services[0] ? vm.socialCase.data.case.services[0] : vm.socialCase.data.service);
                }
                catch (ex) {
                    console.log('error: {0}', ex);
                }

                if (typeof (service) !== 'undefined') {
                    settings = service.settings;
                }
            }
            else if (typeof (messagesService.selectedSocialCase.data.service.settings) !== 'undefined' &&
                messagesService.selectedSocialCase.data.service.settings !== null) {
                settings = messagesService.selectedSocialCase.data.service.settings;
                sentDate = messagesService.selectedSocialCase.data.date;
            }

            if (typeof(settings) !== 'undefined' &&
                settings.integrationType !== 1) {
                let maxMinutesToAnswerMessages = 1440;
                if (typeof (settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages']) !== 'undefined') {
                    maxMinutesToAnswerMessages = settingsService.settings.context.systemSettings['whatsapp.MaxMinutesToAnswerMessages'];
                    if (typeof (maxMinutesToAnswerMessages) === 'string') {
                        maxMinutesToAnswerMessages = parseInt(maxMinutesToAnswerMessages, 10);
                    }
                }

                if (sentDate !== null) {
                    let sentDateMoment = moment(sentDate);
                    sentDateMoment.add(maxMinutesToAnswerMessages, 'm');
                    let nowMoment = moment();

                    if (sentDateMoment < nowMoment) {
                        vm.allowedToReply = false;
                        let text = $translate.instant('WHATSAPP_CANNOT_REPLY_OUT_OF_WINDOW');
                        if (typeof (messagesService.selectedSocialCase) !== 'undefined' &&
                            typeof (messagesService.selectedSocialCase.data) !== 'undefined')
                            messagesService.selectedSocialCase.data.parameters.alertMessage = text;

                        toastr.info(text);
                    }

                    if (typeof (settingsService.settings.context.license.allowWhatsappOutbound) !== 'boolean' ||
                        settingsService.settings.context.license.allowWhatsappOutbound) {
                        if (settings.allowToSendHSM &&
                            settings.allowAgentsToSendHSM &&
                            settings.hSMTemplates !== null &&
                            settings.hSMTemplates.length > 0) {
                            if (vm.isMyOutgoingCases) {
                                if (typeof (settingsService.settings.agent.outgoingMessagesForWhatsappUser) === 'boolean' &&
                                    settingsService.settings.agent.outgoingMessagesForWhatsappUser &&
                                    settingsService.settings.agent.useMyCasesSendHsm) {
                                    vm.allowedToSendHSM = true;
                                    vm.showActionButtonsForMyCases = true;
                                }
                            }
                            else {
                                if (typeof (settingsService.settings.agent.allowedToReplyWhatsappWithTemplate) === 'boolean' &&
                                    settingsService.settings.agent.allowedToReplyWhatsappWithTemplate)
                                    vm.allowedToReplyWhatsappWithHSM = true;
                            }
                        }
                    }
                }
            }
        }

        function showActionButtons() {
            if (vm.caseReadOnly) {
                return false;
            }

            if (typeof(vm.messagesService.selectedSocialCase) === 'undefined' ||
                typeof(vm.messagesService.selectedSocialCase.data) === 'undefined') {
                return false;
            }

            if (vm.messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.Chat) {
                return false;
            }

            var message = messagesService.getMessageById(vm.messagesService.selectedSocialCase.data.id);
            if (message !== null &&
                typeof(message.message) !== 'undefined' &&
                message.message !== null &&
                (
                    message.message.actualData.answer.isAnswering ||
                    message.message.actualData.answer.isAnsweringWithTemplate ||
                    message.message.actualData.discard.isDiscarding ||
                    message.message.actualData.returnToQueue.isReturningToQueue ||
                    message.message.actualData.transferToQueue.isTransferingToQueue ||
                    message.message.actualData.returnToYFlow.isReturningToYFlow ||
                    message.message.actualData.finishing.isFinishing
                )) {
                return false;
            }

            if (settingsService.connectionStatus.myOutgoingCases) {
                return false;
            }

            if (agentService.isShowMyCasesNoChangeState) {
                return false;
            }

            return true;
        }

        function getPreviousMessages() {
            if (!vm.buttonLoadingOptions.hideLoading) {
                return;
            }

            vm.buttonLoadingOptions.hideLoading = false;

            messagesService.getPreviousMessages(vm.socialCase.data.case.id, vm.socialCase.data.case.messages[0].id)
                .then(function (result) {
                    var res = utilsService.toCamel(result.data.Result);
                    var messages = res.messages;
                    if (messages.length > 0) {
                        for (var i = messages.length - 1; i >= 0; i--) {
                            /* jshint loopfunc: true */
                            messagesService.updateMessageRelatedData(messages[i], res.persons, res.socialUserProfiles, res.socialUsers);
                            if (messages[i].postedBy) {
                                var socialUsers = vm.socialCase.data.case.profile.socialUserAccounts.filter(function (account) {
                                    return account.id === messages[i].postedBy.id;
                                });
                                if (socialUsers.length > 0) {
                                    messages[i].postedBy = socialUsers[0];
                                }
                            }

                            var caseMessages = vm.socialCase.data.case.messages;
                            for (var k = 0; k < caseMessages.length; k++) {
                                /* jshint loopfunc: true */
                                if (caseMessages[k].groups && caseMessages[k].groups.length > 0) {
                                    for (var j = 0; j < caseMessages[k].groups.length; j++) {
                                        var groupedMessages = caseMessages.filter(function (message) {
                                            return message.id === caseMessages[k].groups[j].id;
                                        });
                                        if (groupedMessages.length > 0) {
                                            caseMessages[k].groups[j] = groupedMessages[0];
                                        }
                                    }
                                }

                                if (caseMessages[k].groupedBy) {
                                    var groupedByMessages = caseMessages.filter(function (message) {
                                        return message.id === caseMessages[k].groupedBy.id;
                                    });
                                    messages[i].groupedBy = groupedByMessages[0];
                                }

                            }

                            vm.socialCase.data.case.messages.unshift(messages[i]);
                        }

                        messagesService.reprocessCaseMessages(vm.socialCase.data);

                        utilsService.scrollToBottom();
                    }
                    else {
                        vm.hideLoadPreviousMessagesButton = true;
                    }

                    vm.buttonLoadingOptions.hideLoading = true;
                })
                .catch(function (error) {
                    vm.buttonLoadingOptions.hideLoading = true;
                    console.log(error);
                });
        }

        function getNewEnqueuedMessages() {
            if (!vm.buttonLoadingNewMessagesOptions.hideLoading) {
                return;
            }

            vm.buttonLoadingNewMessagesOptions.hideLoading = false;

            messagesService.getNewMessagesOfCase(vm.socialCase.data.case.id, vm.messagesService.selectedSocialCase.data.id)
                .then(function (result) {
                    var res = utilsService.toCamel(result.data.Result);
                    var messages = res.messages;
                    if (messages.length > 0) {
                        for (let i = 0; i < messages.length; i++) {
                            let messageIndex = -1;
                            for (let j = 0; j < vm.socialCase.data.case.messages.length; j++) {
                                if (vm.socialCase.data.case.messages[j].id === messages[i].id) {
                                    messageIndex = j;
                                    break;
                                }
                            }

                            if (messageIndex >= 0) {
                                continue;
                            }

                            /* jshint loopfunc: true */
                            messagesService.updateMessageRelatedData(messages[i], res.persons, res.socialUserProfiles, res.socialUsers);
                            if (messages[i].postedBy) {
                                var socialUsers = vm.socialCase.data.case.profile.socialUserAccounts.filter(function (account) {
                                    return account.id === messages[i].postedBy.id;
                                });
                                if (socialUsers.length > 0) {
                                    messages[i].postedBy = socialUsers[0];
                                }
                            }

                            var caseMessages = vm.socialCase.data.case.messages;
                            for (var k = 0; k < caseMessages.length; k++) {
                                /* jshint loopfunc: true */
                                if (caseMessages[k].groups && caseMessages[k].groups.length > 0) {
                                    for (var j = 0; j < caseMessages[k].groups.length; j++) {
                                        var groupedMessages = caseMessages.filter(function (message) {
                                            return message.id === caseMessages[k].groups[j].id;
                                        });
                                        if (groupedMessages.length > 0) {
                                            caseMessages[k].groups[j] = groupedMessages[0];
                                        }
                                    }
                                }

                                if (caseMessages[k].groupedBy) {
                                    var groupedByMessages = caseMessages.filter(function (message) {
                                        return message.id === caseMessages[k].groupedBy.id;
                                    });
                                    messages[i].groupedBy = groupedByMessages[0];
                                }

                            }
                            vm.socialCase.data.case.messages.push(messages[i]);
                        }
                        utilsService.scrollToBottom();
                    }
                    else {
                        vm.hideLoadNewMessagesButton = true;
                    }

                    vm.buttonLoadingNewMessagesOptions.hideLoading = true;
                    delete vm.socialCase.allowToReloadCase;
                })
                .catch(function (error) {
                    vm.buttonLoadingNewMessagesOptions.hideLoading = true;
                    delete vm.socialCase.allowToReloadCase;
                    console.log(error);
                });
        }

        function getProfileName() {
            return $sce.trustAsHtml(twemoji.parse(messagesService.selectedSocialCase.data.case.profile.name, twemoji.defaultOptions));
        }

        function getBusinessData() {
            return agentService.getProfileBusinessCode(vm.socialCase.data);
        }

        function showSocialUserProfile() {
            $rootScope.$broadcast('msgPanelSocialUserProfile');
            panels.open("panelSocialUserProfile");
        }

        function generateMailOutgoing() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);
            outgoingService.generateOutgoingForwardMail = true;

            if (messagesService.selectedSocialCase.data.attachments) {
                var dataModal = {
                    title: $translate.instant('WARNING'),
                    description: $translate.instant('ATTACH_FILES_ORIGINAL_MAIL_CONFIRM'),
                    acceptButton: $translate.instant('YES'),
                    cancelButton: $translate.instant('NO')
                };

                modalSocialService.showConfirmGeneral(dataModal)
                    .then(function (modal) {
                        modal.element.modal();
                        modal.close.then(function (result) {
                            outgoingService.generateOutgoingMailIsAttachingOriginalAttachment = result !== null;
                            showMailOutgoing();
                        });
                    });
            }
            else {
                outgoingService.generateOutgoingMailIsAttachingOriginalAttachment = false;
                showMailOutgoing();
            }
        }

        function showMailOutgoing() {
            outgoingService.disableOutgoingMessageTypes();
            outgoingService.selectOutgoingMessageType('mail', true, false);
            outgoingService.data.outgoingStep.selectedStep = outgoingService.data.outgoingMessageSteps.mail;
            outgoingService.data.closeButtonEnabled = false;
            outgoingService.data.showFirstStep = false;
            outgoingService.data.predefinedText = getLastMessageText();

            modalSocialService.showOutgoing()
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {

                    });
                });
        }

        function getLastMessageText() {
            var message = vm.messagesService.selectedSocialCase.data;
            var result = "<br /><br />";
            result += "<div>El " + moment(message.date).format('DD/MM/YYYY HH:mm') + ", ";
            $translate.instant('ARTICLE_PLUS_DATE', {date: moment(message.date).format('DD/MM/YYYY HH:mm')});
            result += message.postedBy.fullName;
            result += ":<br />";
            result += "<blockquote style=\"margin:0 0 0 .8ex;border-left:1px #ccc solid;padding-left:1ex;color:#500050\">";
            result += message.body;
            result += "</blockquote>";
            result += "</div>";

            return result;
        }

        function isMail() {
            if (typeof(vm.messagesService.selectedSocialCase) !== 'undefined' &&
                typeof(vm.messagesService.selectedSocialCase.data) !== 'undefined') {
                return vm.messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.Mail;
            }
            return false;
        }

        function isMailAndCanGenerateOutgoing() {
            if (!isMail()) {
                return false;
            }

            if (!settingsService.settings.agent.allowedToGenerateMultipleReplies) {
                return false;
            }

            if (vm.messagesService.selectedSocialCase.data.service.settings.generateMultipleRepliesBehaviour === 0) {
                return false;
            }

            return true;
        }

        function isChat() {
            if (typeof(vm.messagesService.selectedSocialCase) !== 'undefined' &&
                typeof(vm.messagesService.selectedSocialCase.data) !== 'undefined') {
                return vm.messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.Chat;
            }
            return false;
        }

        function loadMail() {
            if (messagesService.selectedSocialCase.data.socialServiceType === SocialServiceTypes.Mail) {
                var lastCase = vm.socialCase.dateCases[vm.socialCase.dateCases.length - 1];
                vm.uniqueMailMessage = {
                    socialCase: lastCase,
                    message: lastCase.messages[lastCase.messages.length - 1]
                };

                vm.email = {
                    from: vm.socialCase.data.postedBy ? vm.socialCase.data.postedBy.email : getUserAccount(vm.socialCase.data).email,
                    to: vm.socialCase.data.parameters ? getMailToAddresses(JSON.parse(vm.socialCase.data.parameters.to)) : "",
                    cc: vm.socialCase.data.parameters ? getMailCopyAddresses(JSON.parse(vm.socialCase.data.parameters.cc)) : "",
                    subject: vm.socialCase.data.parameters ? vm.socialCase.data.parameters.subject : ""
                };
            }
        }

        function getMailCopyAddresses(addresses) {
            var values = "";
            if (addresses.$values) {
                addresses.$values.forEach(function (item) {
                    if (values.length > 0) {
                        values += ", ";
                    }
                    values += item.Address;
                });
            }

            return values;
        }

        function getMailToAddresses(addresses) {
            var values = "";
            if (addresses.$values) {
                addresses.$values.forEach(function (item) {
                    if (values.length > 0) {
                        values += ", ";
                    }
                    values += item.Address;
                });
            }
            return values;
        }

        function showMessageDetails(message) {
            $rootScope.$broadcast('msgPanelMessage', {
                message: message,
                selectedSocialCase: messagesService.selectedSocialCase
            });
            panels.open("panelMessageDetails");
        }

        function getMessageUserName(msg) {
            var result = messagesService.getUserName(msg);
            return $sce.trustAsHtml(twemoji.parse(result, twemoji.defaultOptions));
        }

        function getUserAvatar(msg) {
            return messagesService.getUserAvatar(msg);
        }

        function getServiceIcon(msg) {
            return profileService.getIconBySocialServiceType(msg.socialServiceType);
        }

        function showAttachmentPopup(message, attachment) {
            modalSocialService.showAttachment(message, attachment)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {

                    });
                });
        }

        function getServiceAvatar() {
            return settingsService.getProductImage();
        }

        function attachmentIsAnImage(message) {
            if (!message.attachments || message.attachments.length > 1) {
                return false;
            }
            else {
                var isAnImage = messagesService.attachmentIsAnImage(message.attachments[0]);
                return isAnImage;
            }
        }

        function attachmentIsAudio(message) {
            if (!message.attachments || message.attachments.length > 1) {
                return false;
            }
            else {
                var isAnAudio = utilsService.stringStartsWith(message.attachments[0].mimeType, "audio");
                return isAnAudio;
            }
        }

        function attachmentIsVideo(message) {
            if (!message.attachments || message.attachments.length > 1) {
                return false;
            }
            else {
                var isVideo = utilsService.stringStartsWith(message.attachments[0].mimeType, "video");
                return isVideo;
            }
        }

        function getImageUrl(attachment) {
            return messagesService.getUrlAttachmentDownload(attachment);
        }

        function getFileIcon(attachment) {
            return messagesService.getFileIcon(attachment);
        }

        function discardCase() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.discard.isDiscarding = true;
            utilsService.scrollToBottom();
        }

        function answerMessageForMyCases() {
            vm.socialCase.outgoing = false;
            if (typeof(vm.socialCase.data.actualData) === 'undefined') {
                vm.socialCase.data.actualData = {
                    answer: {
                        isAnswering: true
                    }
                };
            }
            else {
                vm.socialCase.data.actualData.answer.isAnswering = true;
            }
        }

        function answerMessageWithHSMForMyCases() {
            vm.socialCase.outgoing = false;
            if (typeof(vm.socialCase.data.actualData) === 'undefined') {
                vm.socialCase.data.actualData = {
                    answer: {
                        isAnsweringWithTemplate: true
                    }
                };
            }
            else {
                vm.socialCase.data.actualData.answer.isAnsweringWithTemplate = true;
            }
        }

        function removeMarkAsPendingMessage() {
            let dataModal = {
                title: modalConfirmRemovePendingMarkTitle,
                description: modalConfirmRemovePendingMarkDescription,
                acceptButton: $translate.instant('YES'),
                cancelButton: $translate.instant('NO')
            };

            modalSocialService.showConfirmDiscard(dataModal)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (!result) {
                            return;
                        }

                        if (result.action === 'confirm') {
                            var message = vm.socialCase.data;
                            if (typeof(message.id) === 'undefined') {
                                message = vm.socialCase.data.case.messages[0];
                                message.case = vm.socialCase.data.case;
                            }

                            messagesService.removeMarkAsPendingMessage(message)
                                .then(function (result) {
                                    let messagesid = message.case.messages.map(function (e) {
                                        return e.id;
                                    });

                                    let agent = settingsService.settings.agent;
                                    agent.myPendingMessages = agent.myPendingMessages.filter(function (element) {
                                        return !messagesid.includes(element);
                                    });
                                    agent.pendingMessages = agent.pendingMessages.filter(function (element) {
                                        return !messagesid.includes(element);
                                    });
                                    toastr.success(removePengindMessageSuccess);
                                    $rootScope.$broadcast('RefreshDiaryCases');
                                    $rootScope.$broadcast('RefreshPendingCases');
                                })
                                .catch(function (error) {
                                    toastr.error(removePengindMessageError);
                                    traceService.warning(`El agente intentó quitar la marca de pendinete del mensaje ${message.id} pero hubo un error: ${JSON.stringify(error)}`);
                                });
                        }
                        else {
                            toastr.error(errorDiscardMessageValidation);
                        }
                    });
                });
        }

        function answerMessage() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.answer.isAnswering = true;
            utilsService.scrollToBottom();
        }

        function answerMessageWithHSM() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.answer.isAnsweringWithTemplate = true;
            utilsService.scrollToBottom();
        }

        function answerMessageWithMessengerTag() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.answer.isAnsweringWithTemplate = true;
            utilsService.scrollToBottom();
        }

        function showReturnToYFlow() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.returnToYFlow.isReturningToYFlow = true;
            utilsService.scrollToBottom();
        }

        function showReturnToQueue() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.returnToQueue.isReturningToQueue = true;
            utilsService.scrollToBottom();
        }

        function showTransferToQueue() {
            var message = messagesService.getMessageById(vm.socialCase.data.id).message;
            messagesService.markAsFinishedRead(message);

            vm.socialCase.outgoing = false;
            message.actualData.transferToQueue.isTransferingToQueue = true;
            utilsService.scrollToBottom();
        }

        function showEditCaseDetailsModal() {
            modalSocialService.showCaseDetails(vm.socialCase)
                .then(function (modal) {
                    modal.element.modal();

                    modal.close.then(function (result) {
                        //actualizar
                });
            });
        }

        function actAsChatAddMessageToCase(ignoreText, options) {
            if (typeof(ignoreText) !== 'boolean') {
                ignoreText = false;
            }

            if (!ignoreText) {
                if (typeof (vm.actAsChatInfo.text) !== 'string') {
                    vm.actAsChatInfo.text = '';
                }

                if (vm.actAsChatInfo.text.length === 0) {
                    toastr.error($translate.instant('TEXT_REQUIRED'));
                    return false;
                }

                switch (vm.socialCase.data.socialServiceType) {
                    case SocialServiceTypes.WhatsApp:
                        if (vm.actAsChatInfo.text.length > 4096) {
                            toastr.error($translate.instant('WHATSAPP_MAX_LENGTH', {length: vm.actAsChatInfo.text.length}));
                            return false;
                        }
                        break;
                    case SocialServiceTypes.FacebookMessenger:
                        if (vm.actAsChatInfo.text.length > 2000) {
                            toastr.error($translate.instant('MESSENGER_MAX_LENGTH', {length: vm.actAsChatInfo.text.length}));
                            return;
                        }
                        break;
                    case SocialServiceTypes.Twitter:
                        if (vm.actAsChatInfo.text.length > 10000) {
                            toastr.error($translate.instant('TWITTER_MAX_LENGTH', {length: vm.actAsChatInfo.text.length}));
                            return;
                        }
                        break;
                }

                if(settingsService.getAgent().allowedToDisableEmojis && messagesService.checkIfEmojis(vm.actAsChatInfo.text)) {
                    toastr.error($translate.instant('HAS_EMOJIS'));
                    return false;
                }
            }

            let contextMessage;
            if (vm.actAsChatInfo.contextMessage !== null) {
                if (typeof(vm.actAsChatInfo.contextMessage.parameters.wid) === 'string') {
                    contextMessage = vm.actAsChatInfo.contextMessage.parameters.wid;
                }
                else {
                    contextMessage = vm.actAsChatInfo.contextMessage.socialMessageId;
                }
            }

            let answerWithOptions = {
                data: {
                    principal: ignoreText ? null : vm.actAsChatInfo.text,
                    attachments: vm.actAsChatInfo.attachments
                },
                options: {
                    chkForwardMessage: false,
                    chkCloseCase: false,
                    contextMessage: contextMessage
                },
                messageId: vm.socialCase.data.id,
                case: vm.socialCase.data.case,
                socialServiceType: vm.socialCase.data.socialServiceType,
                service: vm.socialCase.data.service,
                socialUser: vm.socialCase.data.socialUser,
                showLoadingDialog: false,
                ignoreTagCasesOnStart: true,
                actAsChatInfo: vm.actAsChatInfo,
                success: function (messageId) {
                    let actAsChatInfo = this.actAsChatInfo;
                    let that = this;
                    if (vm.socialCase.data.case.id !== this.case.id) {
                        console.log(`Se recibió callback de mensaje ${messageId} agregado al case ${this.case.id} pero el caso actual es ${vm.socialCase.data.case.id}`);
                    }
                    else {
                        console.log(`Se recibió callback de mensaje ${messageId} agregado al case ${this.case.id}`);
                        document.getElementById('audio').innerHTML = '';
                        actAsChatOnInput();
                    }

                    actAsChatInfo.text = '';
                    actAsChatInfo.attachments = [];
                    actAsChatInfo.attachmentsQueue = [];
                    actAsChatInfo.contextMessage = null;
                    actAsChatInfo.addedMessages++;
					actAsChatInfo.isRecording  = false;
                    actAsChatInfo.wasTyping = false;
                    actAsChatInfo.typing = false;
					actAsChatInfo.noAudio = true;
                    actAsChatInfo.loading = false;

                    console.log(`Se envió exitosamente el mensaje ${messageId} en el caso ${this.case.id}`);

                    if (messagesService.timerService.timerExist(this.case.id)){
                        messagesService.timerService.stopAgentTimer(this.case.id);
                        messagesService.timerService.startFinalUserTimer(this.case.id);
                    }

                    messagesService.show(messageId)
                        .then(function (response) {
                            let result = utilsService.toCamel(response.data.Result);

                            result.message.isAgentMessage = true;
                            result.message.repliedBy = settingsService.settings.agent;
                            result.message.isReply = true;
                            messagesService.updateMessageRelatedData(result.message, result.persons, undefined, undefined);

                            let lastMessage = that.case.messages[that.case.messages.length - 1];
                            if (lastMessage.isReply &&
                                lastMessage.repliedBy !== null &&
                                lastMessage.repliedBy.id === result.message.repliedBy.id) {
                                result.message.sameAuthor = true;
                                lastMessage.continues = true;
                            }

                            if (!moment(lastMessage.date).isSame(moment(result.message.date), 'day')) {
                                result.message.beginsNewDay = true;
                            }

                            that.case.messages.push(result.message);
                            that.case.totalMessages++;

                            if (vm.socialCase.data.case.id === that.case.id) {
                                actAsChatOnInput();
                                actAsChatScrollToBottom();
                            }
                        })
                        .catch(function (error) {
                            actAsChatInfo.loading = false;
                        });
                },
                error: function(error) {
                    let actAsChatInfo = this.actAsChatInfo;
                    actAsChatInfo.loading = false;
                }
            };

            if (typeof(options) === 'object') {
                angular.extend(answerWithOptions.options, options);
            }

            vm.actAsChatInfo.loading = true;
            console.log(`Agregando mensaje al caso ${vm.socialCase.data.case.id}`);
            commonActionsService.addMessage(answerWithOptions);
        }

        function actAsChatSendWhatsappVoiceCallInteractiveMessage() {
            const service = vm.socialCase.data.service;
            const settings = service.settings;

            if (!settings.voiceCallsEnabled) {
                console.log(`El servicio ${service.id}-${service.name} no permite llamadas de voz`);
                return;
            }

            /*
            Dejamos lo siguiente comentado, dado que por el momento el agente estará habilitado para mandar la invitación para el caso
            if (!automatic &&
                !vm.actAsChatInfo?.allowedToSendWhatsappVoiceCallInteractiveMessage) {
                console.log(`El servicio ${service.id}-${service.name} no permite que los agentes envien en forma manual invitación a llamadas de voz`);
                return;
            }
            */

            let options = {
                type: 'interactive',
                interactive: {
                    type: 'voice_call',
                    header: undefined,
                    body: {
                        text: settings.voiceCallsInteractiveMessageInvite.body
                    },
                    footer: undefined,
                    action: {
                        name: 'voice_call',
                        parameters: {
                            display_text: settings.voiceCallsInteractiveMessageInvite.displayText,
                            ttl_minutes: settings.voiceCallsInteractiveMessageInvite.ttlMinutes
                        }
                    }
                },
                options: {
                    callInvite: true
                },
                messageId: vm.socialCase.data.id,
                case: vm.socialCase.data.case,
                socialServiceType: vm.socialCase.data.socialServiceType,
                service: vm.socialCase.data.service,
                socialUser: vm.socialCase.data.socialUser,
                showLoadingDialog: false,
                ignoreTagCasesOnStart: true,
                actAsChatInfo: vm.actAsChatInfo,
                success: function (messageId) {
                    let actAsChatInfo = this.actAsChatInfo;
                    let that = this;
                    if (vm.socialCase.data.case.id !== this.case.id) {
                        console.log(`Se recibió callback de mensaje ${messageId} agregado al case ${this.case.id} pero el caso actual es ${vm.socialCase.data.case.id}`);
                    }
                    else {
                        console.log(`Se recibió callback de mensaje ${messageId} agregado al case ${this.case.id}`);
                        document.getElementById('audio').innerHTML = '';
                        actAsChatOnInput();
                    }

                    actAsChatInfo.addedMessages++;
                    actAsChatInfo.loading = false;

                    console.log(`Se envió exitosamente el mensaje ${messageId} en el caso ${this.case.id}`);

                    if (messagesService.timerService.timerExist(this.case.id)) {
                        messagesService.timerService.updateTimerValidate(this.case.id, messagesService.timerInvalidationTypes.CallRequested);
                    }

                    messagesService.show(messageId)
                        .then(function (response) {
                            let result = utilsService.toCamel(response.data.Result);

                            result.message.isAgentMessage = true;
                            result.message.repliedBy = settingsService.settings.agent;
                            result.message.isReply = true;
                            messagesService.updateMessageRelatedData(result.message, result.persons, undefined, undefined);

                            let lastMessage = that.case.messages[that.case.messages.length - 1];
                            if (lastMessage.isReply &&
                                lastMessage.repliedBy !== null &&
                                lastMessage.repliedBy.id === result.message.repliedBy.id) {
                                result.message.sameAuthor = true;
                                lastMessage.continues = true;
                            }

                            if (!moment(lastMessage.date).isSame(moment(result.message.date), 'day')) {
                                result.message.beginsNewDay = true;
                            }

                            that.case.messages.push(result.message);
                            that.case.totalMessages++;

                            if (vm.socialCase.data.case.id === that.case.id) {
                                actAsChatOnInput();
                                actAsChatScrollToBottom();
                            }
                        })
                        .catch(function (error) {
                            actAsChatInfo.loading = false;
                        });
                },
                error: function(error) {
                    let actAsChatInfo = this.actAsChatInfo;
                    actAsChatInfo.loading = false;
                }
            };

            if (typeof (settings.voiceCallsInteractiveMessageInvite.headerType) === 'number' &&
                settings.voiceCallsInteractiveMessageInvite.headerType !== 0) {
                switch (settings.voiceCallsInteractiveMessageInvite.headerType) {
                    case InteractiveMessageHeaderTypes.Text:
                        options.interactive.header = {
                            type: 'text',
                            text: settings.voiceCallsInteractiveMessageInvite.headerText
                        };
                        break;
                    case InteractiveMessageHeaderTypes.Image:
                        options.interactive.header = {
                            type: 'image',
                            image: {
                                link: settings.voiceCallsInteractiveMessageInvite.headerMediaUrl
                            }
                        };
                        break;
                    case InteractiveMessageHeaderTypes.Video:
                        options.interactive.header = {
                            type: 'video',
                            video: {
                                link: settings.voiceCallsInteractiveMessageInvite.headerMediaUrl
                            }
                        };
                        break;
                    case InteractiveMessageHeaderTypes.Document:
                        options.interactive.header = {
                            type: 'document',
                            document: {
                                link: settings.voiceCallsInteractiveMessageInvite.headerMediaUrl
                            }
                        };
                        break;
                }
            }

            vm.actAsChatInfo.loading = true;

            messagesService.whatsappVoiceCallCanSendInvite(vm.socialCase.data.case.id)
                .then(function (response) {
                    response = utilsService.toCamel(response.data.Result);

                    if (typeof (response.canSendInvite) === 'boolean' &&
                        !response.canSendInvite) {
                        vm.actAsChatInfo.loading = false;
                        if (typeof (response.reason) === 'object' &&
                            typeof (response.reason.code) === 'number') {
                            switch (response.reason.code) {
                                case 1:
                                case 2:
                                case 3:
                                    modalSocialService.showAlert($translate.instant(`WHATSAPP_VOICECALL_CANNOT_SEND_INVITE_${response.reason.code}`), $translate.instant('WHATSAPP_VOICECALL_TITLE'));
                                    break;
                                default:
                                    modalSocialService.showAlert($translate.instant(`WHATSAPP_VOICECALL_CANNOT_SEND_INVITE_GENERIC`), $translate.instant('WHATSAPP_VOICECALL_TITLE'));
                                    break;
                            }
                        }
                        else {
                            modalSocialService.showAlert($translate.instant('WHATSAPP_VOICECALL_COULDNT_CHECK_CAN_SEND_INVITE'), $translate.instant('WHATSAPP_VOICECALL_TITLE'));
                        }
                    }
                    else {
                        console.log(`Agregando mensaje interactivo al caso ${vm.socialCase.data.case.id}`);
                        commonActionsService.addMessage(options);
                    }
                })
                .catch(function (error) {
                    vm.actAsChatInfo.loading = false;
                    toastr.error($translate.instant('WHATSAPP_VOICECALL_COULDNT_CHECK_CAN_SEND_INVITE'));
                    traceService.warning(`No se pudo verificar si se puede enviar una invitación de llamada para el caso ${vm.socialCase.data.case.id} porque hubo un error: ${JSON.stringify(error)}`);
                });
        }

        function actAsChatOnInput($event) {
            if (typeof ($event) === 'undefined' || typeof ($event.target) === 'undefined' || $event.target != null) {
                $event = {
                    target: document.getElementById('textareaActAsChat')
                };
            }
            if ($event.target !== null) {
                $event.target.dispatchEvent(new Event('change'));
            }
        }

        function handleSendClick($event) {
            if (typeof (settingsService.settings.agent.settings.sendMode) !== 'string' ||
                settingsService.settings.agent.settings.sendMode.toLowerCase() === 'click' ||
                settingsService.settings.agent.settings.sendMode.toLowerCase() === 'both') {
                actAsChatAddMessageToCase();
            }
            else {
                $event.preventDefault();
            }
        }

        function actAsChatOnKeydown($event) {
            if ($event.key === 'Enter' &&
                !$event.shiftKey) {
                $event.preventDefault();
                if (typeof (settingsService.settings.agent.settings.sendMode) !== 'string' ||
                    settingsService.settings.agent.settings.sendMode.toLowerCase() === 'enter' ||
                    settingsService.settings.agent.settings.sendMode.toLowerCase() === 'both') {
                    if (vm.actAsChatInfo.loading) {
                        console.log('Ya se está enviando');
                        return;
                    }

                    actAsChatAddMessageToCase();
                }
            }
         }

        function actAsChatOnChange($event) {
            if (!vm.actAsChatInfo.handleOnChange) {
                return;
            }

            if (vm.actAsChatInfo.text.length === 0) {
                vm.actAsChatInfo.typing = false;
                if (vm.actAsChatInfo.wasTyping) {
                    // Estaba escribiendo y ya no más. Se envía evento
                    messagesService.sendConversationEvent(vm.socialCase.data.case.id, 'typingend')
                        .then(function () {})
                        .catch(function (error) {
                            console.log(error);
                        });

                    vm.actAsChatInfo.wasTyping = false;
                }
            }
            else {
                vm.actAsChatInfo.typing = true;
                if (!vm.actAsChatInfo.wasTyping) {
                    // Está escribiendo. Se envía evento
                    messagesService.sendConversationEvent(vm.socialCase.data.case.id, 'typingstart')
                        .then(function () {})
                        .catch(function (error) {
                            console.log(error);
                        });

                    vm.actAsChatInfo.wasTyping = true;
                }
            }
        }

        function actAsChatAttachFile() {
            let files = vm.actAsChatInfo.attachmentsQueue;

            modalSocialService.showFileUploader(files, vm.selectedSocialCase)
                .then(function (modal) {
                    modal.element.modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    modal.element.on('hide.bs.modal', function (e) {
                        if (modalSocialService.blockModalHiding) {
                            e.preventDefault();
                        }
                    });

                    modal.close.then(function (result) {
                        if (typeof(result) !== 'undefined' &&
                            result != null) {
                            vm.actAsChatInfo.attachmentsQueue = result.queue;
                            actAsChatAddSelectedAttachments(result.queue);
                            if (result.queue.length === 0) {
                                vm.actAsChatInfo.attachments = [];
                            }
                        }
                    });
                });
        }

        function actAsChatAddSelectedAttachments(queue, isPredAnswerAttach, callback) {
            if (typeof(isPredAnswerAttach) !== 'boolean') {
                isPredAnswerAttach = false;
            }

            vm.actAsChatInfo.attachments = [];

            // Set up attachment files
            let attachment = {};
            if (isPredAnswerAttach) {
                attachment.MimeType = vm.actAsChatInfo.predAttachment.mimeType;
                attachment.FileName = vm.actAsChatInfo.predAttachment.name;
                attachment.OriginalFileName = vm.actAsChatInfo.predAttachment.originalName;
                attachment.FileSize = 0;
                attachment.isPredAnswerAttach = isPredAnswerAttach;
                // Push attachment
                vm.actAsChatInfo.attachments.push(attachment);
            }
            else {
                // Loop through uploads
                angular.forEach(queue, function (upload) {
                    if (typeof(upload.isPredAttach) === 'undefined') {
                        let attachment = {
                            MimeType: upload._file.type,
                            FileName: upload._file.name,
                            FileSize: upload._file.size
                        };

                        if (typeof(settingsService.settings.context.license.allowToSaveAttachmentsInAzureStorage) === 'boolean' &&
                            settingsService.settings.context.license.allowToSaveAttachmentsInAzureStorage &&
                            (
                                typeof(CONFIG_INFO.uploadAttachmentsToAzureStorage) !== 'boolean' ||
                                CONFIG_INFO.uploadAttachmentsToAzureStorage
                            )
                        ) {
                            attachment.File = upload._file;

                            vm.actAsChatInfo.attachments.push(attachment);

                            if (typeof (callback) === 'function') {
                                callback();
                            }
                        }
                        else {
                            // Base64 encode attachment
                            let reader = new FileReader();
                            reader.readAsDataURL(upload._file);
                            reader.onload = function (event) {
                                if (typeof (attachment.FileName) !== 'undefined') {
                                    if (vm.actAsChatInfo.attachments.find(item => item.FileName === attachment.FileName) === undefined) {
                                        // Build content
                                        attachment.Data = event.target.result.substring(event.target.result.indexOf(',') + 1, event.target.result.length);
                                        // Push attachment
                                        vm.actAsChatInfo.attachments.push(attachment);

                                        if (typeof (callback) === 'function') {
                                            callback();
                                        }
                                    }
                                }
                            };
                        }
                    }
                    else {
                        let attachment = {
                            MimeType: upload._file.type,
                            FileName: upload._file.fileName,
                            FileSize: 0,
                            isPredAnswerAttach: true
                        };

                        // Push attachment
                        vm.actAsChatInfo.attachments.push(attachment);
                    }
                });
            }
        }

        function actAsChatShortUrls() {
            let input = vm.actAsChatInfo.text;
            if (typeof(input) !== 'string' || input.length === 0) {
                return;
            }

            try {
                let promise = agentService.shortUrls(input);
                if (promise === null) {
                    return;
                }

                promise
                    .then(function (response) {
                        response.forEach(function (result) {
                            input = input.replace(result.data.Result.OriginalUrl, result.data.Result.ShortUrl);
                        });
                        vm.actAsChatInfo.text = input;
                        actAsChatOnInput();
                    })
                    .catch(function () {
                        $log.error($translate.instant('URLS_COULD_NOT_BE_CUT'));
                    });
            }
            catch (e) {
                $log.error($translate.instant('URLS_COULD_NOT_BE_CUT'));
            }
        }

        function actAsChatShowPredefinedAnswers() {
            let selectionStart, selectionEnd;
            let $editorPrimary = $('#textareaActAsChat');
            selectionStart = $editorPrimary[0].selectionStart;
            selectionEnd = $editorPrimary[0].selectionEnd;
            let data = messagesService.selectedSocialCase.data;

            modalSocialService.showPredefinedAnswers(data, settingsService.settings.agent)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (typeof(result) !== 'object' || typeof(result.textAnswer) !== 'string') {
                            return;
                        }

                        if (typeof(vm.actAsChatInfo.text) === 'undefined' || vm.actAsChatInfo.text === null || vm.actAsChatInfo.text.length === 0) {
                            vm.actAsChatInfo.text = result.textAnswer;
                        }
                        else {
                            if (selectionStart === selectionEnd && selectionEnd === vm.actAsChatInfo.text.length) {
                                if (!vm.actAsChatInfo.text.endsWith(' ') && !result.textAnswer.startsWith(' '))
                                    vm.actAsChatInfo.text = vm.actAsChatInfo.text + ' ' + result.textAnswer;
                                else
                                    vm.actAsChatInfo.text = vm.actAsChatInfo.text + result.textAnswer;
                            }
                            else {
                                vm.actAsChatInfo.text = vm.actAsChatInfo.text.substring(0, selectionStart) + result.textAnswer + vm.actAsChatInfo.text.substring(selectionEnd);
                            }
                        }

                        if (typeof(result.attach) === 'object' &&
                            result.attach !== null &&
                            typeof(result.attach.name) === 'string' &&
                            typeof(result.attach.mimeType) === 'string' &&
                            messagesService.selectedSocialCase.data.service.settings.allowToSendMultimedia) {
                            vm.actAsChatInfo.attachments = [];
                            vm.actAsChatInfo.attachmentsQueue = [];

                            vm.actAsChatInfo.predAttachment = result.attach;
                            actAsChatAddSelectedAttachments(vm.actAsChatInfo.predAttachment, true);
                            vm.actAsChatInfo.attachmentsQueue.push(vm.actAsChatInfo.predAttachment);
                        }

                        actAsChatOnInput();
                    });
                });
        }

        function actAsChatSpeechToText() {
            modalSocialService.showConfirmWebToSpeech({})
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (result && result.action === 'confirm') {
                            vm.actAsChatInfo.text = result.transcript;
                        }
                    });
                });
        }

		function actAsChatRecordAudio() {
            vm.actAsChatInfo.stop = true;
            vm.actAsChatInfo.isRecording = true;

            const workerOptions = {
                OggOpusEncoderWasmPath: 'https://cdn.jsdelivr.net/npm/opus-media-recorder@latest/OggOpusEncoder.wasm',
                WebMOpusEncoderWasmPath: 'https://cdn.jsdelivr.net/npm/opus-media-recorder@latest/WebMOpusEncoder.wasm'
            };

            let options = {mimeType: 'audio/ogg'};

            window.MediaRecorder = OpusMediaRecorder;

            var device = navigator.mediaDevices.getUserMedia({audio: true});
            var items = [];
            device.then((stream) => {
                vm.actAsChatInfo.recorder = new MediaRecorder(stream, options, workerOptions);
                vm.actAsChatInfo.recorder.ondataavailable = (e) => {
                    items = [];
                    items.push(e.data);
                    if (vm.actAsChatInfo.recorder.state === 'inactive') {
                        vm.actAsChatInfo.blobAudio = new Blob(items, {type: 'audio/ogg'});
                        vm.actAsChatInfo.blobAudio.name = "audio.ogg";
                        vm.actAsChatInfo.blobAudio.lastModifiedDate = new Date();
                        var audio = document.getElementById('audio');
                        var mainaudio = document.createElement('audio');
                        mainaudio.setAttribute('controls', 'controls');
                        audio.appendChild(mainaudio);
                        mainaudio.innerHTML = '<source src= "' + URL.createObjectURL(vm.actAsChatInfo.blobAudio) + '" type="audio/ogg"/>';
                        if (!vm.actAsChatInfo.stop) {
                            audio.innerHTML = '';
                        }
                    }
                };

                vm.actAsChatInfo.recorder.start();
                audio.innerHTML = '';
                $scope.$broadcast('timer-start');
            });
        }

		function actAsChatSendAudio() {
            if (messagesService.isAttachValid(vm.actAsChatInfo.blobAudio, vm.socialCase.data.service.settings.attachments)) {
                let attachments = [];
                attachments.push({
                    _file: vm.actAsChatInfo.blobAudio,
                    file: vm.actAsChatInfo.blobAudio
                });

                actAsChatAddSelectedAttachments(attachments, false, function () {
                    actAsChatAddMessageToCase(true);
                });
            }
            else {
                console.log(`No se puede adjuntar el archivo de tipo ${vm.actAsChatInfo.blobAudio.type} con tamaño ${vm.actAsChatInfo.blobAudio.size} y nombre ${vm.actAsChatInfo.blobAudio.name}`);
            }
        }

		function actAsChatStopRecording() {
            vm.actAsChatInfo.recorder.stop();
            vm.actAsChatInfo.isRecording = false;
            vm.actAsChatInfo.noAudio = false;
            $scope.$broadcast('timer-stop');
            vm.actAsChatInfo.recorder.stream.getAudioTracks().forEach(function (track) {
                track.stop();
            });
        }

		function actAsChatDeleteRecording() {
            vm.actAsChatInfo.recorder.stop();
            vm.actAsChatInfo.isRecording = false;
            vm.actAsChatInfo.stop = false;
            $scope.$broadcast('timer-stop');
            vm.actAsChatInfo.recorder.stream.getAudioTracks().forEach(function (track) {
                track.stop();
            });
        }

		function actAsChatDeleteAudio() {
            document.getElementById('audio').innerHTML = '';
            vm.actAsChatInfo.noAudio = true;
        }

        function actAsChatScrollToBottom() {
            var $container = $('.case-timeline');
            if ($container.length > 0) {
                $timeout(function () {
                    $container.animate({
                        scrollTop: $container.get(0).scrollHeight
                    }, 500);
                }, 100);
            }
        }

        function actAsChatOnImagePasted(event, data) {
            if (!vm.actAsChat || !vm.actAsChatInfo.canAttach) {
                return;
            }

            if (typeof(vm.actAsChatInfo.attachmentsQueue) === 'undefined' ||
                vm.actAsChatInfo.attachmentsQueue === null) {
                vm.actAsChatInfo.attachmentsQueue = [];
            }

            /*if (vm.actAsChatInfo.attachmentsQueue.length === 1) {
                toastr.error($translate.instant('MULTIPLE_ATTACHMENTS_FORBIDDEN'));
                return;
            }*/

            // Set up attachment files
            // Base64 encode attachment
            let blob = data.getAsFile();

            if (messagesService.selectedSocialCase.data.service.settings.allowToSendMultimedia &&
                messagesService.isAttachValid(blob, vm.socialCase.data.service.settings.attachments)) {
                if (vm.actAsChatInfo.attachmentsQueue.find(item => item.file.name === blob.name) !== undefined) {
                    toastr.error($translate.instant('FILE_ALREADY_ATTACHED'));
                }
                else {
                    let selectedSocialCase = messagesService.selectedSocialCase;
                    let attachmentConfig = vm.socialCase.data.service.settings.attachments;

                    if (selectedSocialCase.data.socialServiceType !== SocialServiceTypes.WhatsApp) {
                        if ((vm.actAsChatInfo.attachmentsQueue.length + 1) > attachmentConfig.maxAttachmentsAllowed) {
                            toastr.error($translate.instant('MULTIPLE_ATTACHMENTS_FORBIDDEN'));
                            return;
                        }
                    }

                    vm.actAsChatInfo.attachmentsQueue.push({
                        _file: blob,
                        file: blob
                    });

                    actAsChatAddSelectedAttachments(vm.actAsChatInfo.attachmentsQueue);

                    toastr.info($translate.instant('ADDED_NEW_ATTACH_FROM_CLIPBOARD', {type: blob.type, name: blob.name, size: blob.size}));
                }
            }
            else {
                console.log(`No se puede adjuntar el archivo de tipo ${blob.type} con tamaño ${blob.size} y nombre ${blob.name}`);
            }
        }

        function canAnswer(closeCase) {
            const {
                settings: {
                    context: {
                        systemSettings,
                        tags
                    }
                }
            } = settingsService;
        
            const { selectedSocialCase } = messagesService;
            const caseData = selectedSocialCase.data.case;
            const {
                replies,
                tags: caseTags,
                importantTagID,
                queue: { id: caseQueueId },
                taggedBy
            } = caseData;

            if (caseData.queue === null || typeof(caseData.queue) === 'undefined')
				return true;
        
            const tagCasesOnClose = systemSettings['cases.TagCasesOnClose'];
            const tagCasesOnStart = systemSettings['cases.TagCasesOnStart'];
            const tagCasesOnDiscard = systemSettings['cases.TagCasesOnDiscard'];
            const tagImportant = systemSettings['cases.ImportantTag'];
            const importantTagsQuantity = importantTagID ? importantTagID : -1;
            const tagsQuantity = caseTags.length;
        
            const queues = tags.filter(tag => tag.queues && tag.queues.some(queue => queue.id === caseQueueId));
            const taggedByAgent = Object.keys(taggedBy).filter(key => taggedBy[key] === "3");
        
            if (queues.length === 0 || (tagCasesOnStart === 0 && tagCasesOnClose === 0)) {
                return true;
            }
        
            let flagImportant = !(closeCase && tagImportant && importantTagsQuantity === -1);
            let flagOnStart = replies === 0 ? evaluateFlag(tagCasesOnStart, tagsQuantity, taggedByAgent) : true;
            let flagOnClose = closeCase ? evaluateFlag(tagCasesOnClose, tagsQuantity, taggedByAgent) : true;
        
            return flagOnStart && flagOnClose && flagImportant;
        }
        
        function evaluateFlag(tagCaseSetting, tagsQuantity, taggedByAgent) {
            switch(tagCaseSetting) {
                case 1:
                    return tagsQuantity > 0;
                case 2:
                    return tagsQuantity > 0 && taggedByAgent.length > 0;
                default:
                    return true;
            }
        }

        /**
         * Finaliza la atención del mensaje
         * @param {boolean} closeCase Indica si se cerrará el caso
         */
        function actAsChatFinishMessage(closeCase) {
            if (messagesService.isGrouping) {
                console.log(`No se puede realizar la finalización del mensaje ${messagesService.selectedSocialCase.data.id} porque en este momento se está realizando un agrupamiento`);
                return;
            }

            let input = vm.actAsChatInfo.text;
            let description;
            if (typeof(input) === 'string' &&
                input !== null &&
                input.length > 0) {
                description = $translate.instant('FINISH_MESSAGE_TEXT_WRITTEN');
            }
            else {
                description = $translate.instant('FINISH_MESSAGE_DIALOG_DESCRIPTION');
            }

            let dataModal = {
                title: $translate.instant('FINISH_MESSAGE_DIALOG_TITLE'),
                description: description,
                acceptButton: $translate.instant('YES'),
                cancelButton: $translate.instant('NO'),
                iconClass: 'fa-question-circle'
            };

            messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = true;

            modalSocialService.showConfirmGeneral(dataModal)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (!result) {
                            messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                            return;
                        }

                        if (result.action === 'confirm') {
                            let tagImportant = settingsService.settings.context.systemSettings['cases.ImportantTag'];

                            if (!messagesService.canAnswer(messagesService.selectedSocialCase.data.case,closeCase,false))  {
                                modalSocialService.showCaseDetails(messagesService.selectedSocialCase, undefined, closeCase && tagImportant)
                                    .then(function (modal) {
                                        modal.element.modal();
                                        modal.close.then(function (result) {
                                            if (result && result.updated) {
                                                actAsChatFinishMessageAction(closeCase);
                                            }
                                            else {
                                                messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                                            }
                                        });
                                    });
                            }
                            else {
                                actAsChatFinishMessageAction(closeCase);
                            }
                        }
                        else {
                            toastr.error($translate.instant('THERE_WAS_AN_ERROR_IN_DISCARD_MESSAGE_VALIDATION'));
                            messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                        }
                    });
                });
        }

        /**
         * Realiza la acción de Finalización de la atención del mensaje
         * @param {boolean} closeCase Indica si se cerrará el caso
         */
        function actAsChatFinishMessageAction(closeCase) {
            if (messagesService.isGrouping) {
                console.log(`No se puede realizar la finalización del mensaje ${messagesService.selectedSocialCase.data.id} porque en este momento se está realizando un agrupamiento`);
                messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                return;
            }

            let options = {
                closeCase: closeCase
            };

            utilsService.showLoadingMessage($translate.instant('FINISH_MESSAGE_LOADER'));
            messagesService.finishMessageAsChat(messagesService.selectedSocialCase.data, options)
                .then(function (res) {
                    
                    utilsService.hideLoading();
                    //Si el caso posee mensajes que aun NO se agruparon
                    if (typeof res.data.Result === "object" && res.data.Result != null) {
                        if (typeof res.data.Result.Success === "boolean" && !res.data.Result.Success) {
                            if (res.data.Result.ErrorCode === 253) {
                                messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                                utilsService.hideLoading();
                                messagesService.notifyMessagesToGroup(messagesService.selectedSocialCase.data.case.id);
                                return;
                            }
                        }
                    }

                    toastr.success($translate.instant('FINISH_MESSAGE_SUCCESS'));
                    if (messagesService.timerService.timerExist(messagesService.selectedSocialCase.data.case.id)) {
                        messagesService.timerService.stopAgentTimer(messagesService.selectedSocialCase.data.case.id);
                        messagesService.timerService.stopFinalUserTimer(messagesService.selectedSocialCase.data.case.id);
                        messagesService.timerService.deleteTimer(messagesService.selectedSocialCase.data.case.id);
                    }
                    traceService.info(`El agente finalizó la atención del mensaje ${messagesService.selectedSocialCase.data.id} correctamente`);
                    let messageToBeDiscarded = messagesService.selectedSocialCase.data;
                    messagesService.removeActualCase();
                    integrationsService.executeActions(window.IntegrationTypes.AgentMessageFinished, {
                        message: messageToBeDiscarded,
                        moreInfo: {
                            closedCase: closeCase
                        }
                    });
                    messagesService.selectedSocialCase = undefined;
                    statesService.changeConnectionStatus();

                    messagesService.openNextMessage();
                })
                .catch(function (error) {
                    messagesService.selectedSocialCase.data.actualData.finishing.isFinishing = false;
                    utilsService.hideLoading();
                    toastr.error($translate.instant('FINISH_MESSAGE_FAILED'));
                    traceService.warning(`El agente intentó finalizar la atención del mensaje ${messagesService.selectedSocialCase.data.id} pero hubo un error: ${JSON.stringify(error)}`);
                });
        }

        function actAsChatRemoveContextMessage() {
            vm.actAsChatInfo.contextMessage = null;
        }

        function actAsChatGetContextMessageBody() {
            return messagesService.getMessageBody(vm.actAsChatInfo.contextMessage, true);
        }

        function actAsChatGetContextMessageUser() {
            if (vm.actAsChatInfo.contextMessage.isReply) {
                return vm.actAsChatInfo.contextMessage.service.basicConfiguration.fullPhoneNumber;
            }
            return vm.actAsChatInfo.contextMessage.postedBy.name;
        }

        function actAsChatShowForms() {
            var data = messagesService.selectedSocialCase.data;

            modalSocialService.showForms(vm.socialCase.data.service.settings.forms)
                .then(function (modal) {
                    modal.element.modal();
                    modal.close.then(function (result) {
                        if (typeof(result) === 'object' &&
                            typeof(result.form) === 'string') {
                            let form = vm.socialCase.data.service.settings.forms.find(f => f.id === result.form);

                            actAsChatAddMessageToCase(true, {
                                form: form.id
                            });
                        }
                    });
                });
        }

        function showCasePanel() {
            $rootScope.$broadcast('msgPanelCase');
            panels.open("panelCaseDetails");
        }
    }
})();