<div class="modal fade" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-agentsettings">
        <div class="modal-content">
            <div class="modal-header modal-header-social">
                <p class="modal-title">
                    <span class="bold">{{'AGENT_SETTINGS' | translate}}</span>
                </p>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_ENABLESOUNDS' | translate}}</label>
                            <div style="width: 100%; padding: 6px 12px; margin-top: 1px">
                                <input type="checkbox" ng-model="configuration.useSounds" />
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6"
                         ng-if="otherServicesThanMailAndChat()">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_SHORT_SIGNATURE' | translate }}</label>
                            <input type="text"
                                   ng-model="configuration.signature"
                                   maxlength="4"
                                   class="form-control"
                                   uib-popover="{{ 'AGENT_SETTINGS_SHORT_SIGNATURE_DESC' | translate }}"
                                   popover-placement="bottom"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_FONTSIZE' | translate }}</label>
                            <select class="form-control" ng-model="configuration.fontSize">
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-6"
                        ng-if="otherServicesThanMailAndChat()">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_SENDMODE' | translate}}</label>
                            <select class="form-control" ng-model="configuration.sendMode">
                                <option value="enter">{{'SEND_MODE_ENTER' | translate}}</option>
                                <option value="click">{{'SEND_MODE_CLICK' | translate}}</option>
                                <option value="both">{{'SEND_MODE_BOTH' | translate}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-6" ng-if="isInTheCloud">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_FONTFAMILY' | translate }}</label>
                            <select class="form-control" ng-model="configuration.fontFamily">
                                <option value="">{{'AGENT_SETTINGS_FONTFAMILY_DEFAULT' | translate }}</option>
                                <option value="Fira Sans">Fira Sans</option>
                                <option value="Lato">Lato</option>
                                <option value="Montserrat">Montserrat</option>
                                <option value="Open Sans">Open Sans</option>
                                <option value="Roboto">Roboto</option>
                                <option value="Source Sans Pro">Source Sans Pro</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>{{'AGENT_SETTINGS_PLAYBACKRATE' | translate }}</label>
                            <select class="form-control" ng-model="configuration.playbackRate">
                                <option value="1">1</option>
                                <option value="1.5">1.5</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn btn-flat btn-action"
                        ng-click="saveAgentSettings()">
                    {{'SAVE' | translate}}
                </button>
                <button type="button"
                        class="btn btn-flat"
                        ng-click="cancel()">
                    {{'CANCEL' | translate}}
                </button>
            </div>
        </div>
    </div>
</div>
