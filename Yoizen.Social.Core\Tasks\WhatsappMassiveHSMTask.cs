﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json.Linq;
using SocketIOClient.Messages;
using SuperSocket.Common;
using Yoizen.Common;
using Yoizen.Social.Core.Enums;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.ServiceSettings;
using Yoizen.Social.DomainModel.Whatsapp;

namespace Yoizen.Social.Core.Tasks
{
	public sealed class WhatsappMassiveHSMTask : TaskBase
	{
		#region Fields

		private string queueName;
		private Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient administrationClient = null;
		private ServiceBusClient client = null;
		private ServiceBusProcessor processor = null;
		private ServiceBusSender sender = null;
		private int processingRecord = 0;
		private DomainModel.ServiceSettings.WhatsappSettings.HSMTemplate template;
		private List<long> alreadyProcessedNumbers;
		private DateTime lastDate = DateTime.MinValue;
		private object shared = new object();
		private bool finished = false;
		private bool resuming = false;
		private global::System.Collections.Concurrent.ConcurrentDictionary<byte, bool> notifiedPercents;
		private List<WhatsappSettings.IntegrationTypes> IntegrationTypesSendInBackground;

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de TaskBase
		/// </summary>
		/// <param name="task">La tarea que se ejecutará</param>
		/// <param name="cancellationToken">Un <see cref="global::System.Threading.CancellationToken"/> que se utilizará para
		/// cancelar la tarea</param>
		public WhatsappMassiveHSMTask(DomainModel.Tasks.Task task, global::System.Threading.CancellationToken cancellationToken)
			: base(task, cancellationToken)
		{
			this.CancellationToken.Register(async () =>
			{
				Tracer.TraceInfo("Se recibió solicitud de cancelar la tarea {0}", this.Task);
				if (this.Task.Status == DomainModel.Tasks.TaskStatuses.ProcessingQueue)
				{
					if (this.processor != null &&
						this.processor.IsProcessing)
					{
						await this.processor.StopProcessingAsync();
						Tracer.TraceInfo("Se detuvo el procesamiento de la cola de la tarea {0}", this.Task);
					}

					var taskParameters = this.Task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;
					taskParameters.TotalIgnoredByCancellation = taskParameters.TotalValid - this.processingRecord;

					this.cancelledAction?.Invoke(true);
				}
			});

			this.IntegrationTypesSendInBackground = new List<WhatsappSettings.IntegrationTypes>()
			{
				WhatsappSettings.IntegrationTypes.CloudApi,
				WhatsappSettings.IntegrationTypes.Yoizen
			};

			this.notifiedPercents = new global::System.Collections.Concurrent.ConcurrentDictionary<byte, bool>();
			for (byte i = 0; i < 100; i++)
			{
				this.notifiedPercents[i] = false;
			}
		}

		#endregion

		#region Private Methods

		private global::System.Threading.Tasks.Task ExceptionReceivedHandler(ProcessErrorEventArgs exceptionReceivedEventArgs)
		{
			StringBuilder sb = new StringBuilder();
			sb.AppendLine($"Message handler encountered an exception {exceptionReceivedEventArgs.Exception}.");
			var context = exceptionReceivedEventArgs.ErrorSource;
			sb.AppendLine("Exception context for troubleshooting:");
			sb.AppendLine($"- ErrorSource: {exceptionReceivedEventArgs.ErrorSource}");
			sb.AppendLine($"- FullyQualifiedNamespace: {exceptionReceivedEventArgs.FullyQualifiedNamespace}");
			sb.AppendLine($"- Executing EntityPath: {exceptionReceivedEventArgs.EntityPath}");
			Tracer.TraceError("Ocurrió un error obteniendo mensajes de la cola: {0}", sb.ToString());
			return global::System.Threading.Tasks.Task.CompletedTask;
		}

		private async global::System.Threading.Tasks.Task ProcessMessagesAsync(ProcessMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var message = args.Message;

			var body = message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la tarea {0} con número de secuencia {1} y contenido {2}", new Dictionary<string, object>()
			{
				{ DomainModel.TracingCommonProperties.ServiceBusSeq, message.SequenceNumber }
			}, this.Task, message.SequenceNumber, body);

			lock (shared)
			{
				this.lastDate = DateTime.Now;
			}

			if (args.CancellationToken.IsCancellationRequested)
			{
				await args.CompleteMessageAsync(message);
				return;
			}

			var taskParameters = this.Task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;

			if (taskParameters.CancelProcess)
			{
				if (taskParameters.CancelDateTime != null)
				{
					if (DateTime.Now > taskParameters.CancelDateTime.Value)
					{
						await args.CompleteMessageAsync(message);

						Tracer.TraceInfo("Se llegó al límite de la ejecución de la tarea {0} porque está indicado que se cancele a las {1}", this.Task, taskParameters.CancelDateTime);
						cancelledAction?.Invoke(true);
						return;
					}
				}
				else if (taskParameters.CancelAfterMinutes != null)
				{
					if (DateTime.Now > this.Task.DateStarted.Value.AddMinutes(taskParameters.CancelAfterMinutes.Value))
					{
						await args.CompleteMessageAsync(message);

						Tracer.TraceInfo("Se llegó al límite de la ejecución de la tarea {0} porque está indicado que se cancele luego de {1} minutos y la tarea empezó a las {2}", this.Task, taskParameters.CancelAfterMinutes.Value, this.Task.DateStarted);
						cancelledAction?.Invoke(true);
						return;
					}
				}
			}

			if (taskParameters.Service == null)
				taskParameters.Service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(taskParameters.ServiceID);

			var jMessage = Newtonsoft.Json.Linq.JObject.Parse(body);
			var currentLine = jMessage["index"].ToObject<int>();
			var options = jMessage["options"].ToObject<ActionOptions.SendWhatsappOptions>();

			var sendParameters = new Dictionary<string, string>();
			sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter] = options.SendDefinition.ElementName;
			sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter] = options.SendDefinition.Namespace;
			sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage] = options.SendDefinition.Language;
			sendParameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter] = Newtonsoft.Json.JsonConvert.SerializeObject(options.SendDefinition);
			sendParameters[Social.WhatsApp.WhatsAppMessage.HSMParameter] = true.ToString();
			sendParameters[Social.WhatsApp.WhatsAppMessage.CampaignParameter] = this.Task.Name;
			sendParameters[Social.WhatsApp.WhatsAppMessage.CampaignIdParameter] = this.Task.ID.ToString();

			options.Person = this.Task.User;
			options.Service = taskParameters.Service;
			options.Parameters = sendParameters;
			options.Tags = taskParameters.Tags;
			options.SendInBackground = false;
			options.Template = this.template;
			options.TaskID = this.Task.ID;

			var messageId = await Core.System.Instance.Logic.SendWhatsapp(options);


			bool informBrokerStats = true;

			var whatsappSettings = options.Service.Settings as WhatsappSettings;

			if (whatsappSettings != null)
			{
				var serviceIntegrationType = (WhatsappSettings.IntegrationTypes) whatsappSettings.IntegrationType;
				informBrokerStats = !this.IntegrationTypesSendInBackground.Contains(serviceIntegrationType);
			}

			if (informBrokerStats)
			{
				if (options.InsertedMessage != null &&
				options.InsertedMessage.Delivered == false)
				{
					Tracer.TraceInfo("Ocurrió un error enviando el mensaje {3} de la línea {2} en el procesamiento de la tarea {0}: {1}", this.Task, options.InsertedMessage.DeliveryErrorNumber, currentLine, options.InsertedMessage);
					taskParameters.InformRejectedMessage();
				}
				else
				{
					taskParameters.InformAcceptedMessage();
				}
			}
			
			global::System.Threading.Interlocked.Increment(ref this.processingRecord);

			if (this.processingRecord == taskParameters.TotalValid)
			{
				Tracer.TraceInfo("Finalizó el procesamiento de los {0} registros válidos del archivo de la tarea {1}", taskParameters.TotalValid, this.Task.ID);
				this.finished = true;
				
				global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async (ct) =>
				{
					await global::System.Threading.Tasks.Task.Delay(5000);
					finishedAction?.Invoke(taskParameters.TotalInvalid == 0 ? DomainModel.Tasks.TaskResults.Success : DomainModel.Tasks.TaskResults.Partial, null, null);
				});
			}
			else
			{
				float percent = this.processingRecord * 100 / taskParameters.TotalValid;
				byte fixedPercent = (byte) Math.Floor(percent);
				if (this.notifiedPercents.TryGetValue(fixedPercent, out bool notified) &&
					!notified)
				{
					if (this.notifiedPercents.TryUpdate(fixedPercent, true, false))
						this.progressAction?.Invoke(DomainModel.Tasks.TaskStatuses.ProcessingQueue, percent);
				}
			}

			await args.CompleteMessageAsync(message);
		}

		private async Task CreateQueue()
		{
			var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(this.queueName);
			options.MaxSizeInMegabytes = 1024;
			options.DefaultMessageTimeToLive = TimeSpan.FromHours(24);
			options.EnableBatchedOperations = false;
			options.LockDuration = TimeSpan.FromMinutes(1);

			await administrationClient.CreateQueueAsync(options, this.CancellationToken);

			Tracer.TraceInfo("Se creó la cola del service bus {0} para envío masivo de mensajes", this.queueName);
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Inicializa la tarea
		/// </summary>
		/// <param name="resume">Indica si la tarea está resumiéndose (<code>true</code>) o empezando (<code>false</code>)</param>
		/// <param name="progress">Establece el <see cref="Action"/> que se invocará para informar el progreso de la tarea</param>
		/// <param name="finished">Establece el <see cref="Action"/> que se invocará para informar la finalización de la tarea</param>
		/// <param name="cancelled">Establece el <see cref="Action"/> que se invocará para informar la cancelación de la tarea</param>
		public override async Task Initialize(bool resume, Action<DomainModel.Tasks.TaskStatuses, float?> progress, Action<DomainModel.Tasks.TaskResults, string, short?> finished, Action<bool> cancelled)
		{
			await base.Initialize(resume, progress, finished, cancelled);

			this.queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-massive-{this.Task.ID}";

			var connectionString = DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString;

			try
			{
				this.administrationClient = new Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient(connectionString);

				if (!await this.administrationClient.QueueExistsAsync(this.queueName, this.CancellationToken))
				{
					await CreateQueue();
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", this.queueName);

					if (resume &&
						this.Task.Status == DomainModel.Tasks.TaskStatuses.InProgress)
					{
						Tracer.TraceInfo("Al resumirse una tarea que no llegó a procesar el archivo en forma completa, se limpia la cola", this.queueName);

						Tracer.TraceInfo("Se borrará la cola {0} utilizada en la tarea {1}", this.queueName, this.Task.ID);
						await this.administrationClient.DeleteQueueAsync(this.queueName);

						await CreateQueue();

						var parameters = this.Task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;
						parameters.TotalAcceptedByBroker = 0;
						parameters.TotalDuplicated = 0;
						parameters.TotalFailed = 0;
						parameters.TotalIgnored = 0;
						parameters.TotalIgnoredByCancellation = 0;
						parameters.TotalInvalid = 0;
						parameters.TotalRecords = 0;
						parameters.TotalRejectedByBroker = 0;
						parameters.TotalSent = 0;
						parameters.TotalValid = 0;
						parameters.TotalDelivered = 0;
						parameters.TotalReaded = 0;
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para envío masivo de mensajes: {1}", this.queueName, ex);
				throw;
			}

			var clientOptions = new ServiceBusClientOptions();
			clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
			clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
				ServiceBusTransportType.AmqpWebSockets :
				ServiceBusTransportType.AmqpTcp;

			Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

			this.client = new ServiceBusClient(connectionString, clientOptions);

			var processorOptions = new ServiceBusProcessorOptions()
			{
				AutoCompleteMessages = false,
				MaxConcurrentCalls = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMassive,
				MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(2)
			};

			this.processor = client.CreateProcessor(queueName, processorOptions);
			this.processor.ProcessErrorAsync += ExceptionReceivedHandler;
			this.processor.ProcessMessageAsync += ProcessMessagesAsync;

			this.sender = client.CreateSender(queueName);

			this.alreadyProcessedNumbers = new List<long>();
			this.processingRecord = 0;
			this.resuming = resume;
		}

		/// <summary>
		/// Finaliza la tarea
		/// </summary>
		/// <param name="cancellationToken">Un <see cref="CancellationToken"/> a utilizar para abortar en caso de que pase el tiempo y no finalice</param>
		public override async Task Finalize(CancellationToken cancellationToken = default)
		{
			Tracer.TraceVerb("Ejecutando Finaliza de la tarea {0}", this.Task.ID);

			try
			{
				if (await this.administrationClient.QueueExistsAsync(this.queueName, cancellationToken))
				{
					Tracer.TraceInfo("Se borrará la cola {0} utilizada en la tarea {1}", this.queueName, this.Task.ID);
					await this.administrationClient.DeleteQueueAsync(this.queueName, cancellationToken);
					Tracer.TraceInfo("Se borró la cola {0} utilizada en la tarea {1}", this.queueName, this.Task.ID);
				}
				else
				{
					Tracer.TraceInfo("No se borrará la cola {0} utilizada en la tarea {1} porque no existe", this.queueName, this.Task.ID);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error borrando la cola {0} utilizada en la tarea {1}: {2}", this.queueName, this.Task.ID, ex);
			}

			try
			{
				if (this.sender != null)
				{
					if (!this.sender.IsClosed)
					{
						Tracer.TraceVerb("Finalizando el sender de la tarea {0}", this.Task.ID);
						await this.sender.CloseAsync(cancellationToken);
						Tracer.TraceVerb("Finalizó el sender de la tarea {0}", this.Task.ID);
					}
					else
					{
						Tracer.TraceVerb("El sender de la tarea {0} ya estaba cerrado", this.Task.ID);
					}
				}
				else
				{
					Tracer.TraceVerb("El sender de la tarea {0} era null", this.Task.ID);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error liberando el sender de la tarea de hsm masivos: {0}", ex);
			}

			try
			{
				if (this.processor != null)
				{
					if (!this.processor.IsClosed)
					{
						Tracer.TraceVerb("Finalizando el processor de la tarea {0}", this.Task.ID);
						await this.processor.CloseAsync(cancellationToken);
						Tracer.TraceVerb("Finalizó el processor de la tarea {0}", this.Task.ID);
					}
					else
					{
						Tracer.TraceVerb("El processor de la tarea {0} ya estaba cerrado", this.Task.ID);
					}
				}
				else
				{
					Tracer.TraceVerb("El processor de la tarea {0} era null", this.Task.ID);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error liberando el procesador de la tarea de hsm masivos: {0}", ex);
			}

			if (this.client != null)
			{
				if (!this.client.IsClosed)
				{
					Tracer.TraceVerb("Finalizando el cliente de la tarea {0}", this.Task.ID);
					await this.client.DisposeAsync();
					Tracer.TraceVerb("Finalizó el cliente de la tarea {0}", this.Task.ID);
				}
				else
				{
					Tracer.TraceVerb("El cliente de la tarea {0} ya estaba cerrado", this.Task.ID);
				}
			}
			else
			{
				Tracer.TraceVerb("El cliente de la tarea {0} era null", this.Task.ID);
			}

			try
			{
				if (this.Task.Parameters != null)
				{
					var taskParameters = this.Task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;
					if (File.Exists(taskParameters.File))
					{
						Tracer.TraceInfo("Se borrará el archivo csv utilizado en la tarea {0}", this.Task.ID);
						File.Delete(taskParameters.File);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error borrando el archivo utilizada en la tarea {0}: {1}", this.Task.ID, ex);
			}
		}

		/// <summary>
		/// Realiza el envío del mail para notificar algo de la tarea
		/// </summary>
		public override async Task SendMail()
		{
			var task = this.Task;
			var parameters = task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;

			if (parameters.Email == null || string.IsNullOrEmpty(parameters.Email.Emails))
				return;

			if (parameters.Service == null)
				parameters.Service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(parameters.ServiceID);
			Core.System.Instance.Logic.EnsureServiceInstance(parameters.Service);

			var configuration = parameters.Service.ServiceConfiguration as SocialServices.WhatsApp.WhatsAppServiceConfiguration;

			var subjectParameters = new Dictionary<string, object>();
			subjectParameters["@@CUENTA@@"] = configuration.FullPhoneNumber;
			subjectParameters["@@SERVICIO@@"] = parameters.Service.Name;
			subjectParameters["@@TAREA@@"] = task.Name;
			var templateParameters = new Dictionary<string, object>();
			templateParameters["@@FECHA@@"] = task.DateFinished?.ToString("dddd d 'de' MMMM 'del' yyyy", new global::System.Globalization.CultureInfo("es-AR"));
			templateParameters["@@CUENTA@@"] = configuration.FullPhoneNumber;
			templateParameters["@@SERVICIO@@"] = parameters.Service.Name;
			templateParameters["@@TAREA@@"] = task.Name;

			if (task.Status == DomainModel.Tasks.TaskStatuses.Finished &&
				task.Result != null)
				templateParameters["@@RESULTADO@@"] = DomainModel.Localizations.Instance.GetLocalizedString($"TaskResults.{task.Result.Value}", DomainModel.SystemSettings.Instance.DefaultLocale, task.Result.Value.ToString());
			else
				templateParameters["@@RESULTADO@@"] = "N/A";

			templateParameters["@@TOTAL_ACEPTADOS_BROKER@@"] = parameters.TotalAcceptedByBroker;
			templateParameters["@@TOTAL_DUPLICADOS@@"] = parameters.TotalDuplicated;
			templateParameters["@@TOTAL_FALLARON@@"] = parameters.TotalFailed;
			templateParameters["@@TOTAL_IGNORADOS@@"] = parameters.TotalIgnored;
			templateParameters["@@TOTAL_IGNORADOS_CANCELACION@@"] = parameters.TotalIgnoredByCancellation;
			templateParameters["@@TOTAL_INVALIDOS@@"] = parameters.TotalInvalid;
			templateParameters["@@TOTAL_REGISTROS@@"] = parameters.TotalRecords;
			templateParameters["@@TOTAL_RECHAZADOS_BROKER@@"] = parameters.TotalRejectedByBroker;
			templateParameters["@@TOTAL_ENVIADOS@@"] = parameters.TotalSent;
			templateParameters["@@TOTAL_VALIDOS@@"] = parameters.TotalValid;
			await DomainModel.SystemSettings.Instance.SendMailMessageAsync(parameters.Email, subjectParameters, templateParameters, null, false);
		}

		/// <summary>
		/// Lanza la tarea
		/// </summary>
		public override async Task Start()
		{
			var task = this.Task;

			Tracer.TraceInfo("Iniciando el procesamiento de la tarea {0}", task);

			var taskParameters = task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;

			if (task.Type == DomainModel.Tasks.TaskTypes.WhatsappMassiveHSMWithoutCaseCreation)
			{
				Tracer.TraceInfo("Se ignora la tarea {0} ya que la misma sera procesada por yWhatsApp", task);
				return;
			}

			if (taskParameters.Service == null)
				taskParameters.Service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(taskParameters.ServiceID);
					   
			if (this.resuming &&
				this.Task.Status == DomainModel.Tasks.TaskStatuses.ProcessingQueue)
			{
				Tracer.TraceInfo("La tarea {0} estaba en estado de procesamiento de cola. Se saltea el procesamiento del archivo y se inicia el trabajo del service bus", task);

				await this.processor.StartProcessingAsync();

				this.progressAction?.Invoke(DomainModel.Tasks.TaskStatuses.ProcessingQueue, 0);

				return;
			}

			try
			{
				var settings = taskParameters.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				var template = settings.HSMTemplates.FirstOrDefault(t =>
					t.ElementName.Equals(taskParameters.TemplateElementName) &&
					t.Namespace.Equals(taskParameters.TemplateNamespace));

				if (!string.IsNullOrEmpty(taskParameters.TemplateLanguage))
				{
					template = settings.HSMTemplates.SingleOrDefault(t =>
						t.ElementName.Equals(taskParameters.TemplateElementName) &&
						t.Namespace.Equals(taskParameters.TemplateNamespace) &&
						t.Language.Equals(taskParameters.TemplateLanguage));
				}

				if (template == null)
				{
					this.finishedAction?.Invoke(DomainModel.Tasks.TaskResults.Failed, "El template no existe en el servicio", 2);
					return;
				}

				this.template = template;

				if (!File.Exists(taskParameters.File))
				{
					this.finishedAction?.Invoke(DomainModel.Tasks.TaskResults.Failed, "El archivo no existe", 1);
					return;
				}

				var readerConfiguration = new CsvHelper.Configuration.Configuration()
				{
					Delimiter = taskParameters.Separator,
					HasHeaderRecord = false,
				};

				var failedFile = Path.Combine(Path.GetDirectoryName(taskParameters.File), $"{Path.GetFileNameWithoutExtension(taskParameters.File)}_failed.csv");
				var ignoredFile = Path.Combine(Path.GetDirectoryName(taskParameters.File), $"{Path.GetFileNameWithoutExtension(taskParameters.File)}_ignored.csv");

				var cancelled = false;

				using (var sr = File.OpenText(taskParameters.File))
				using (var swFailed = File.CreateText(failedFile))
				using (var swIgnored = File.CreateText(ignoredFile))
				using (var reader = new CsvHelper.CsvReader(sr, readerConfiguration))
				{
					var sendDefinition = new DomainModel.Whatsapp.HSMTemplateSendDefinition();
					sendDefinition.Namespace = taskParameters.TemplateNamespace;
					sendDefinition.ElementName = taskParameters.TemplateElementName;
					sendDefinition.Language = taskParameters.TemplateLanguage;
					bool flowDataChecked = false;
					var hsmFlowScreenData = new JObject();

					var jsonSerializer = new Newtonsoft.Json.JsonSerializer();
					jsonSerializer.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
					jsonSerializer.DefaultValueHandling = Newtonsoft.Json.DefaultValueHandling.Ignore;

					var serviceBusMessages = new List<ServiceBusMessage>(); 

					int currentLine = 1;
					while (reader.Read())
					{
						if (this.CancellationToken.IsCancellationRequested)
						{
							cancelled = true;
							break;
						}

						if (currentLine <= taskParameters.HeaderRecordsToIgnore)
						{
							currentLine++;
							continue;
						}

						var parts = reader.Context.Record;

						try
						{
							var phoneNumberText = parts[0];
							var phoneNumber = long.Parse(phoneNumberText);
							var ignored = false;
							if (this.alreadyProcessedNumbers.Contains(phoneNumber))
							{
								Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene el número de teléfono {2} que ya fue procesado", currentLine, task, phoneNumber);
								taskParameters.TotalDuplicated++;

								if (!taskParameters.SendDuplicatedRecordsAnyways)
								{
									ignored = true;
									swIgnored.Write(reader.Context.RawRecord);
								}
									
							}
							
							if (!ignored && !taskParameters.SendHSMIfCaseOpenAnyways)
							{
								if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway &&
									Core.System.Instance.QueueService.AreSocialUserMessagesEnqueuedOrAssigned(phoneNumberText, DomainModel.SocialServiceTypes.WhatsApp, taskParameters.Service.ID, out DomainModel.Message _))
								{
									Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene el número de teléfono {2} con un caso encolado o asignado", currentLine, task, phoneNumber);
									taskParameters.TotalIgnored++;

									swIgnored.Write(reader.Context.RawRecord);
									ignored = true;
								}
								else
								{
									if (DAL.CaseDAO.ExistsOpenForSocialUser(phoneNumber, DomainModel.SocialServiceTypes.WhatsApp, taskParameters.Service.ID))
									{
										Tracer.TraceInfo("El número {0} de la tarea {1} fue ignorado por tener un caso abierto en la base de datos", phoneNumber, task);
										taskParameters.TotalIgnored++;

										swIgnored.Write(reader.Context.RawRecord);
										ignored = true;
									}
								}
							}

							if (!ignored && taskParameters.ValidateDoNotCallList)
							{
								var user = SocialUserDAO.GetOne(phoneNumber, DomainModel.SocialServiceTypes.WhatsApp, false);
								if (user == null)
								{
									var socialUserReference = new DomainModel.Settings.WhatsappSocialUserReference(phoneNumber);
									if (DomainModel.SystemSettings.Instance.DoNotCallSocialUsers.Contains(socialUserReference))
									{
										Tracer.TraceInfo("El número {0} de la tarea {1} fue ignorado por estar incluido en la lista No Contactar", phoneNumber, task);
										taskParameters.TotalIgnored++;

										swIgnored.Write(reader.Context.RawRecord);
										ignored = true;
									}
								}
								else if (user.DoNotCall.HasValue && user.DoNotCall.Value)
								{
									Tracer.TraceInfo("El número {0} de la tarea {1} fue ignorado por estar incluido en la lista No Contactar", phoneNumber, task);
									taskParameters.TotalIgnored++;

									swIgnored.Write(reader.Context.RawRecord);
									ignored = true;
								}
							}

							if (!ignored)
							{
								sendDefinition.HeaderType = template.HeaderType;
								var parameterIndex = 1;
								string text = template.Template;

								switch (template.HeaderType)
								{
									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text:
										if (template.HeaderTextParameter != null)
										{
											sendDefinition.HeaderTextParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
											{
												Name = template.HeaderTextParameter.Name,
												Value = parts[parameterIndex++]
											};
										}
										break;
									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media:
										sendDefinition.HeaderMediaType = template.HeaderMediaType;
										sendDefinition.HeaderMediaUrl = parts[parameterIndex++];
										sendDefinition.HeaderMediaFileName = parts[parameterIndex++];
										sendDefinition.HeaderMediaUrlIsPublic = parts[parameterIndex++].Equals("1");

										if (Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
											sendDefinition.HeaderMediaUrlIsPublic = true;
										break;
									case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location:
										try
										{
											sendDefinition.HeaderLocationLatitude = double.Parse(parts[parameterIndex++], global::System.Globalization.CultureInfo.InvariantCulture);
											sendDefinition.HeaderLocationLongitude = double.Parse(parts[parameterIndex++], global::System.Globalization.CultureInfo.InvariantCulture);
											sendDefinition.HeaderLocationName = parts[parameterIndex++];
											sendDefinition.HeaderLocationAddress = parts[parameterIndex++];

											if (string.IsNullOrEmpty(sendDefinition.HeaderLocationAddress) ||
												string.IsNullOrEmpty(sendDefinition.HeaderLocationName))
											{
												Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene los parámetros de ubicación (nombre y dirección) inválidos {2}. Se lo ignora", currentLine, task);
												taskParameters.TotalIgnored++;

												swIgnored.Write(reader.Context.RawRecord);
												ignored = true;
											}

											if (!ignored)
											{
												if (sendDefinition.HeaderLocationLatitude < -90 ||
													sendDefinition.HeaderLocationLatitude > 90 ||
													sendDefinition.HeaderLocationLongitude < -180 ||
													sendDefinition.HeaderLocationLongitude > 180)
												{
													Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene los parámetros de ubicación (latitud y longitud) inválidos {2}. Se lo ignora", currentLine, task);
													taskParameters.TotalIgnored++;

													swIgnored.Write(reader.Context.RawRecord);
													ignored = true;
												}
											}
										}
										catch (Exception ex)
										{
											Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene los parámetros de ubicación inválidos {2}. Se lo ignora", currentLine, task, ex);
											taskParameters.TotalIgnored++;

											swIgnored.Write(reader.Context.RawRecord);
											ignored = true;
										}
										break;
									default:
										break;
								}

								if (!ignored)
								{
									if (template.TemplateParameters != null && template.TemplateParameters.Length > 0)
									{
										var parameters = new List<DomainModel.Whatsapp.HSMTemplateParameter>();

										foreach (var templateParameter in template.TemplateParameters)
										{
											var value = parts[parameterIndex++];

											if (string.IsNullOrEmpty(value) ||
												value.IndexOf("    ") >= 0 ||
												value.IndexOf("\t") >= 0 ||
												value.IndexOf("\n") >= 0)
											{
												Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene el parámetro {2} con 4 espacios, tab o enter. Se lo ignora", currentLine, task, parameterIndex - 1);
												taskParameters.TotalIgnored++;

												swIgnored.Write(reader.Context.RawRecord);
												ignored = true;
												break;
											}

											parameters.Add(new DomainModel.Whatsapp.HSMTemplateParameter()
											{
												Name = templateParameter.Name,
												Value = value
											});

											text = text.Replace($"{{{{{templateParameter.Name}}}}}", value);
										}

										sendDefinition.Parameters = parameters.ToArray();
									}
								}

								if (!ignored)
								{
									if (template.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None)
									{
										sendDefinition.Buttons = new DomainModel.Whatsapp.HSMTemplateButton[template.Buttons.Length];
										sendDefinition.ButtonsType = template.ButtonsType;

										int index = 0;
										foreach (var button in template.Buttons)
										{
											if ((template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply ||
												 template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
												 button.QuickReplyParameter != null)
											{
												sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
												{
													QuickReplyParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
													{
														Name = button.QuickReplyParameter.Name,
														Value = parts[parameterIndex++]
													}
												};
											}
											else if (template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode
													  &&
													  button.AuthCodeButtonType != null)
											{
												switch (button.AuthCodeButtonType.Value)
												{
													case DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode:
														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															AuthCodeButtonType = DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode,
															AuthCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
															{
																Name = button.AuthCodeParameter.Name,
																Value = parts[parameterIndex++]
															}
														};
														break;
												}
											}
											else if ((template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction ||
													  template.ButtonsType == DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed) &&
													  button.CallToActionButtonType != null)
											{
												switch (button.CallToActionButtonType.Value)
												{
													case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
														switch (button.UrlButtonType.Value)
														{
															case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed:
																sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
																{
																	CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
																	UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed
																};
																break;
															case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic:
																sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
																{
																	CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
																	UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic,
																	UrlParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
																	{
																		Name = button.UrlParameter.Name,
																		Value = parts[parameterIndex++]
																	}
																};
																break;
															default:
																break;
														}
														break;
													case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call:
														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call
														};
														break;
													case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode,
															OfferCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
															{
																Name = button.OfferCodeParameter.Name,
																Value = parts[parameterIndex++]
															}
														};
														break;
													case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow:

														var buttonData = button.FlowParameter;

														var flowParameter = new DomainModel.Whatsapp.HSMTemplateFlowParameters()
														{
															Name = button.FlowParameter.Name
														};

														if (!flowDataChecked)
														{
															var screenData = await settings.FindFlowScreensDataAsync(buttonData.FlowID, buttonData.NavigateScreen);

															if (screenData != null && screenData.Length > 0)
															{
																hsmFlowScreenData = screenData[0].Data;
															}

															flowDataChecked = true;
														}

														if (hsmFlowScreenData != null && hsmFlowScreenData.Count > 0)
														{
															var value = parts[parameterIndex++];
															if (ObjectExtensions.TryParse(value, out var data))
															{
																flowParameter.ActionData = new ActionPayload()
																{
																	Data = data
																};
															}
															else
															{
																Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} para el parametro {2} NO es un JSON valido. Se lo ignora", currentLine, task, parameterIndex - 1);
																taskParameters.TotalIgnored++;
																swIgnored.Write(reader.Context.RawRecord);
																ignored = true;
																break;
															}
														}

														sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
														{
															CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow,
															FlowParameter = flowParameter
														};
														break;
													default:
														break;
												}
											}

											index++;
										}
									}

									var source = DomainModel.ReplySources.Supervisor;
									int? sourceId = null;
									if (task.User == null)
									{
										source = DomainModel.ReplySources.ExternalIntegration;
										if (task.ExternalIntegration != null)
											sourceId = task.ExternalIntegration.ID;
									}

									OptionsBussinesData bussinesDataOption = OptionsBussinesData.NoneAction;
									string bussinesData = null;
									OptionsExtendedCaseData extendedCaseDataOption = OptionsExtendedCaseData.NoneAction;
									string extendedCaseData = null;
									var parametersCount = 0;
									if (template.Parameters != null && template.Parameters.Length > 0)
										parametersCount = template.Parameters.Length;

									switch (template.HeaderType)
									{
										case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text:
											if (template.HeaderTextParameter != null)
												parametersCount++;
											break;
										case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media:
											parametersCount += 3;
											break;
										case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location:
											parametersCount += 4;
											break;
										default:
											break;
									}

									switch (template.ButtonsType)
									{
										case DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply:
											parametersCount += template.Buttons.Length;
											break;
										case DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction:
											parametersCount += template.Buttons.Count(b => b.CallToActionButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url &&
												b.UrlButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic);
											break;
										case DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode:
											parametersCount += template.Buttons.Length;
											break;
										default:
											break;
									}

									if (!ignored && taskParameters.UpdateBussinesData)
									{
										parametersCount++;
										bussinesDataOption = (OptionsBussinesData) Enum.Parse(typeof(OptionsBussinesData), parts[parametersCount]);
										if (bussinesDataOption == OptionsBussinesData.Replace || bussinesDataOption == OptionsBussinesData.Concat || bussinesDataOption == OptionsBussinesData.DeleteOne)
										{
											parametersCount++;
											bussinesData = parts[parametersCount];

											if (!Core.System.Instance.Logic.IsBusinessDataValid(bussinesData))
											{
												Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene datos de negocio {2} que no son válidos", currentLine, task, bussinesData);
												taskParameters.TotalIgnored++;

												swIgnored.Write(reader.Context.RawRecord);
												ignored = true;
											}											
										}
									}
									
									if (!ignored && taskParameters.UpdateExtendedCaseData)
									{
										parametersCount++;
										extendedCaseDataOption = (OptionsExtendedCaseData) Enum.Parse(typeof(OptionsExtendedCaseData), parts[parametersCount]);
										if (extendedCaseDataOption == OptionsExtendedCaseData.Concat || extendedCaseDataOption == OptionsExtendedCaseData.Replace)
										{
											parametersCount++;
											extendedCaseData = parts[parametersCount];
											if (!Core.System.Instance.Logic.IsExtendedCaseValid(extendedCaseData))
											{
												Tracer.TraceInfo("La línea {0} en el procesamiento de la tarea {1} tiene datos extendidos del caso {2} que no son válidos", currentLine, task, extendedCaseData);
												taskParameters.TotalIgnored++;

												swIgnored.Write(reader.Context.RawRecord);
												ignored = true;
											}
										}
									}
								
									if (!ignored)
									{
										var options = new ActionOptions.SendWhatsappOptions()
										{
											PhoneNumber = phoneNumber,
											Source = source,
											SourceId = sourceId,
											Text = text,
											SendDefinition = sendDefinition,
											BusinessData = bussinesData,
											BusinessDataOption = bussinesDataOption,
											ExtendedCaseData = extendedCaseData,
											ExtendedCaseDataOption = extendedCaseDataOption,
										};

										var jMessage = new Newtonsoft.Json.Linq.JObject();
										jMessage["index"] = currentLine;
										jMessage["task_id"] = task.ID;
										jMessage["options"] = Newtonsoft.Json.Linq.JObject.FromObject(options, jsonSerializer);

										var body = jMessage.ToString();

										var messageToSend = new ServiceBusMessage();
										messageToSend.Body = new BinaryData(body);
										messageToSend.ContentType = "application/json";

										serviceBusMessages.Add(messageToSend);

										if (serviceBusMessages.Count == 100 || taskParameters.TestDelayMinutes > 0)
										{
											await this.sender.SendMessagesAsync(serviceBusMessages);

											serviceBusMessages.Clear();
										}

										taskParameters.TotalValid++;
									}
								}
							}

							this.alreadyProcessedNumbers.Add(phoneNumber);

							if (taskParameters.TestDelayMinutes > 0)
							{
								var delay = taskParameters.TestDelayMinutes;
								Tracer.TraceInfo("Se espera {0} minutos para enviar el siguiente registro ya que es una campaña de prueba", delay);

								delay = (delay * 60 * 1000);
								Thread.Sleep(delay);
							}
						}
						catch (Exception ex)
						{
							Tracer.TraceInfo("Ocurrió un error procesando la línea {2} en el procesamiento de la tarea {0}: {1}", task, ex, currentLine);
							taskParameters.TotalInvalid++;

							swFailed.Write(reader.Context.RawRecord);
						}

						taskParameters.TotalRecords++;
						currentLine++;
					}
									   
					if (serviceBusMessages.Count > 0)
					{
						await this.sender.SendMessagesAsync(serviceBusMessages);

						serviceBusMessages.Clear();
					}
				}

				if (!cancelled)
				{
					this.progressAction?.Invoke(DomainModel.Tasks.TaskStatuses.FileParsed, null);

					this.processingRecord = 0;

					if (taskParameters.TotalValid > 0)
					{
						Tracer.TraceInfo("Finalizó el procesamiento del archivo de la tarea {0} con {1} registros que se procesarán", task, taskParameters.TotalValid);

						try
						{
							var queueProperties = await administrationClient.GetQueueRuntimePropertiesAsync(this.queueName);
							if (queueProperties.Value.ActiveMessageCount == 0)
							{
								Tracer.TraceInfo("La tarea {0} tiene en la cola {1} {2} mensajes activos", this.Task, this.queueName, queueProperties.Value.ActiveMessageCount);
							}
						}
						catch { }

						Tracer.TraceInfo("Iniciando el procesamiento del service bus de la tarea {0}", this.Task);
						await this.processor.StartProcessingAsync();
						this.progressAction?.Invoke(DomainModel.Tasks.TaskStatuses.ProcessingQueue, 0);
					}
					else
					{
						Tracer.TraceInfo("Finalizó el procesamiento del archivo de lal tarea {0} pero no hay registros válidos a enviar", task);
						finishedAction?.Invoke(DomainModel.Tasks.TaskResults.Failed, "No hay registros válidos para enviar", 3);
					}
				}
				else
				{
					taskParameters.TotalRecords = 0;
					taskParameters.TotalInvalid = 0;
					taskParameters.TotalValid = 0;
					taskParameters.TotalDuplicated = 0;
					taskParameters.TotalIgnored = 0;
					this.cancelledAction?.Invoke(false);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("Ocurrió un error en el procesamiento de la tarea {0}: {1}", task, ex);
				this.finishedAction?.Invoke(DomainModel.Tasks.TaskResults.Failed, string.Format("Ocurrió un error procesando la tarea: {0}", ex), 1);
			}
		}

		/// <summary>
		/// Devuelve si la tarea finalizó
		/// </summary>
		/// <returns><code>true</code> si la tarea terminó; en caso contrario, <code>false</code>. Si finalizó bien, retorna además el resultado de la tarea</returns>
		public override async Task<(bool, DomainModel.Tasks.TaskResults?)> HasFinished()
		{
			DomainModel.Tasks.TaskResults? result = null;

			var taskParameters = this.Task.Parameters as DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters;

			if (this.finished)
			{
				Tracer.TraceInfo("La tarea {0} tiene establecido que terminó", this.Task);
				result = taskParameters.TotalInvalid == 0 ? DomainModel.Tasks.TaskResults.Success : DomainModel.Tasks.TaskResults.Partial;
				return (true, result);
			}

			int minutesToWait = 5;

			if (taskParameters.TestDelayMinutes > 0)
			{
				minutesToWait += taskParameters.TestDelayMinutes;
			}

			var minutes = DateTime.Now.Subtract(this.lastDate).TotalMinutes;
			if (DateTime.Now.Subtract(this.lastDate).TotalMinutes > minutesToWait)
			{
				Tracer.TraceInfo("La tarea {0} recibió por última vez un mensaje hace {1} minutos", this.Task, minutes);

				var queueProperties = await administrationClient.GetQueueRuntimePropertiesAsync(this.queueName);
				if (queueProperties.Value.ActiveMessageCount == 0)
				{
					Tracer.TraceInfo("La tarea {0} tiene en la cola {1} CERO mensajes activos. Se considera que finalizó", this.Task, this.queueName);
					result = taskParameters.TotalInvalid == 0 ? DomainModel.Tasks.TaskResults.Success : DomainModel.Tasks.TaskResults.Partial;
					return (true, result);
				}
			}

			return (false, null);
		}

		#endregion
	}
}
