(function () {
    'use strict';
    angular
        .module('socialApp')
        .controller('EmailContactsActionModalController', EmailContactsActionModalController);

    EmailContactsActionModalController.$inject = [
        '$scope',
        'close',
        'toastr',
        'emailContactsService',
        '$translate',
        'previousContact',
        'settingsService',
    ];

    function EmailContactsActionModalController(
                                     $scope,
                                     close,
                                     toastr,
                                     emailContactsService,
                                     $translate,
                                     previousContact,
                                     settingsService) {
        $scope.cancel = cancel;
        $scope.saveEmailContact = saveEmailContact;
        $scope.close = close;
        $scope.emailContactsService = emailContactsService;
        $scope.previousContact = previousContact;
        $scope.settingsService = settingsService;
        $scope.agentSettings = settingsService.settings.agent;
        $scope.name = '';
        $scope.lastName = '';
        $scope.email = '';

        if (previousContact !== undefined && previousContact !== null){
            $scope.name = previousContact.name;
            $scope.lastName = previousContact.lastName;
            $scope.email = previousContact.email;
        }
        
        function saveEmailContact() {
            
            if ($scope.previousContact === undefined || $scope.previousContact === null){
                let contact = {
                    name : $scope.name,
                    lastName : $scope.lastName,
                    email : $scope.email,
                    agentId : $scope.agentSettings.id
                };
                $scope.emailContactsService.insert(contact).then(function (response){
                    toastr.success($translate.instant('EMAIL_CONTACTS_INSERTED'));
                    contact.id = response.newId;
                    close({
                        action: 'insert',
                        contact: contact
                    }, 100);
                }).catch(function (error) {
                    if (error?.ErrorNumber !== null && error?.ErrorNumber === 100){
                        toastr.error($translate.instant('CANNOT_INSERT_EMAIL_CONTACTS'));
                    }else{
                        toastr.error($translate.instant('CANNOT_INSERT_EMAIL_CONTACTS'));
                    }
                });
            }
            else {
                let contact = {
                    id : $scope.previousContact.id,
                    name : $scope.name,
                    lastName : $scope.lastName,
                    email : $scope.email,
                    agentId : $scope.agentSettings.id
                };
                $scope.emailContactsService.update(contact).then(function (response){
                    toastr.success($translate.instant('EMAIL_CONTACTS_UPDATED'));
                    close({
                        action: 'update',
                        contact: contact
                    }, 100);
                }).catch(function (error) {
                    toastr.error($translate.instant('CANNOT_UPDATE_EMAIL_CONTACTS'));
                });
            }
        }

        function cancel() {
            close(null, 100);
        }
    }

})();