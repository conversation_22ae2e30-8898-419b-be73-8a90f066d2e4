﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master.Master" AutoEventWireup="true" CodeBehind="Adherence.aspx.cs" Inherits="Yoizen.Social.Web.Reports.Adherence" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/underscore-min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.floatThead.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.theme.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.data.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts-3d.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.drilldown.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.funnel.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.exporting.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/highcharts.no-data-to-display.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/numeral.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/ua-parser.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/Reports.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Reports/Adherence.js")%>'></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<span data-i18n="reports-adherence-title">Reporte de Adherencia</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<yoizen:Message ID="messageError" runat="server" Type="Error" Visible="false" />
	<yoizen:Message ID="messageNoAgents" runat="server" Type="Warning" Visible="false" LocalizationKey="reports-user_no_agents">
		No se dispone de acceso para ver información de agentes
	</yoizen:Message>
	<yoizen:Message ID="messageNoQueues" runat="server" Type="Warning" Visible="false" LocalizationKey="reports-user_no_queues">
		El usuario no tiene ninguna cola asignada.
	</yoizen:Message>
	<yoizen:Message ID="messageNoSites" runat="server" Type="Warning" Visible="false" LocalizationKey="reports-adherence-no_sites">
		No hay sitios dados de alta
	</yoizen:Message>
	<asp:Panel ID="panelContent" runat="server">
		<asp:Panel ID="panelFilters" runat="server" ClientIDMode="Static">
			<div id="divSeccion" class="seccion">
				<div class="title">
					<h2 data-i18n="reports-adherence-filters-title">Filtro</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" LocalizationKey="reports-globals-intervals_day_info">
						La información consolidada está disponible al cierre del intervalo o del día
					</yoizen:Message>
					<yoizen:Message ID="messageTimeZoneWarning" runat="server" ClientIDMode="Static" Type="Warning" style="display: none">
						<span data-i18n="reports-globals-other_utc">Usted utiliza otro huso horario al del servidor</span>.
						<div rel="server"><span data-i18n="reports-globals-server_timezone">El servidor está configurado con</span> <span rel="tz"></span></div>
						<div rel="local"><span data-i18n="reports-globals-user_timezone">Usted se encuentra en</span> <span rel="tz"></span></div>
						<div rel="configured-local" style="display: none"><span data-i18n="reports-globals-user_configured_timezone">Usted configuró utilizar</span> <span rel="tz"></span></div>
					</yoizen:Message>
					<table id="tableFilters" width="100%" border="0" class="uiInfoTable">
						<tbody>
							<tr class="dataRow dataRowSeparator" id ="trFromDateFilter" runat="server">
								<th class="label" style="width: 200px !important"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxFromDate" runat="server" Width="100" ClientIDMode="Static" SkinID="NoFocus" autocomplete="nope" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id ="trToDateFilter" runat="server">
								<th class="label"><span data-i18n="globals-to_date">Fecha hasta</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxToDate" runat="server" Width="100" ClientIDMode="Static" autocomplete="nope" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label noinput"><span data-i18n="reports-adherence-filters-sites">Sitios</span>:</th>
								<td class="data">
									<asp:ListBox ID="listboxSites" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" Width="450px" SelectionMode="Multiple" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="reports-adherence-filters-minutes">Minutos para considerar</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxMinutesToConsider" runat="server" MaxLength="2" TextMode="Number" min="0" max="30" Text="0" ClientIDMode="Static" />
												</td>
												<td class="vMid pls" data-i18n="reports-adherence-filters-minutes-desc">
													Especifica cuántos minutos tuvo que estar conectado cada agente en cada intervalo para considerarlo para el reporte de adhesión. Puede
													especificar CERO, para indicar que no importa la cantidad de minutos, solamente basta con que haya trabajado
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</tbody>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Debe ingresar fechas" EnableClientScript="true" ClientValidationFunction="ValidateFilters" /></div>
					<div id="divScheduleDuplicateerror" style="display: none" class="validationerror">
						<asp:CustomValidator runat="server" ClientIDMode="Static" Display="Dynamic" ID="validationScheduleDuplicate" SkinID="validationerror" ErrorMessage="Reporte ya solicitado" data-i18n="reports-messages-duplicate-error" />
					</div>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<asp:Button ID="buttonOKFilter" runat="server" Text="Buscar" OnClick="buttonOKFilter_Click" data-i18n="globals-search" />
						</label>
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<asp:Button ID="buttonScheduleReport" runat="server" Text="Buscar" OnClick="buttonScheduleReport_Click" data-i18n="globals-accept" ClientIDMode="Static"/>
						</label>
						<label class="uiButton uiButtonLarge">
							<button id="buttonCloseSchedule" type="button" data-i18n="globals-close" onclick="parent.jQuery.colorbox.close()" style="display: none">>Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</asp:Panel>
		<asp:Panel ID="panelResults" runat="server" Visible="false">
			<div id="divConsolidatedResults" class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="reports-adherence-consolidated-title">Consolidado por intervalo</h2>
				</div>
				<div class="contents">
					<div id="divConsolidatedLoading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageConsolidatedNoRecords" runat="server" Type="Information" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-no_records">
						No se encontraron registros para los filtros especificados
					</yoizen:Message>
					<yoizen:Message ID="messageConsolidatedError" runat="server" Type="Error" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-an_error_has_ocurred">
						Ocurrió un error trayendo los resultados
					</yoizen:Message>
					<yoizen:Message ID="messageConsolidatedTimeout" runat="server" Type="Error" style="display: none" ClientIDMode="Static" LocalizationKey="reports-globals-timeout">
						Ocurrió un error trayendo los resultados debido a que pasó el tiempo máximo de espera. Si lo desea puede exportar el reporte
					</yoizen:Message>
					<div id="divConsolidated" style="width: 100%; max-height: 400px; overflow: auto; display: none">
						<table id="tableConsolidated" class="reporte" cellspacing="0" rules="all" border="1">
							<thead>
								<tr class="header">
									<th class="nowrap" data-i18n="reports-adherence-consolidated-table_titles-date" style="width: 100px">Fecha</th>
									<th class="nowrap" data-i18n="reports-adherence-consolidated-table_titles-interval" style="width: 100px">Intervalo</th>
									<th class="nowrap" data-i18n="reports-adherence-consolidated-table_titles-site">Sitio</th>
									<th class="nowrap" data-i18n="reports-adherence-consolidated-table_titles-count" style="width: 100px">Cantidad</th>
								</tr>
							</thead>
							<tbody></tbody>
						</table>
					</div>
					<div id="divConsolidatedChartsContainer" class="subseccion collapsable" style="display: none">
						<div class="title">
							<h2 data-i18n="reports-adherence-consolidated-graph-title">Gráfico consolidado por intervalo</h2>
						</div>
						<div class="contents">
							<div id="divConsolidatedCharts" style="width: 100%;"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="buttons">
				<label class="uiButton uiButtonLarge uiButtonConfirm">
					<asp:Button ID="buttonNewSearch" runat="server" Text="Nueva búsqueda" OnClick="buttonClearFilters_Click" data-i18n="globals-new_search" />
				</label>
				<label class="uiButton uiButtonLarge">
					<asp:Button ID="buttonModifyFilters" runat="server" Text="Modificar filtros" OnClick="buttonModifyFilters_Click" data-i18n="globals-modify_filters" />
				</label>
				<label class="uiButton uiButtonLarge" style="display: none">
					<button type="button" id="buttonExport" onclick="ShowExportDialog()" data-i18n="globals-export">Exportar</button>
				</label>
			</div>
		</asp:Panel>

		<div style="display: none">
			<div class="seccion" id="divExport">
				<div class="title">
					<h2 data-i18n="reports-globals-export-title">Exportación</h2>
				</div>
				<div class="contents">
					<div id="divExportStep1">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="[html]reports-globals-csv_instead_excel">
							Cuando el reporte a generar tiene mucha información es posible que en lugar
							de formato <b>Excel</b> se use <b>Separado por coma (CSV)</b>
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="reports-globals-export-format">Formato</span>:</th>
								<td class="data">
									<select id="selectExportFormat">
										<option value="1" data-i18n="reports-globals-export-format-excel">Excel</option>
										<option value="2" data-i18n="reports-globals-export-format-csv">Separados por coma (CSV)</option>
									</select>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="reports-globals-export-email">Email destinatario</span>:</th>
								<td class="data">
									<input type="text" id="inputExportEmail" maxlength="200" style="width: 90%;" class="inputtext" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="reports-globals-export-sections">Secciones a exportar</span>:</th>
								<td class="data">
									<select id="selectSectionsToExport" multiple="multiple" style="width: 500px;">
										<option value="1" data-i18n="reports-adherence-export-sections-consolided">Consolidado por intervalo por sitio</option>
										<option value="2" data-i18n="reports-adherence-export-sections-detailed">Detallado por agente por sitio</option>
									</select>
									<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" LocalizationKey="reports-globals-export-one_file_per_section">
										Se generará un archivo por cada sección seleccionada
									</yoizen:Message>
								</td>
							</tr>
						</table>
						<div id="divExportStep1InvalidEmail" style="display: none" class="validationerror"><span class="validationerror" data-i18n="reports-globals-must_enter_email">Debe ingresar un email válido</span></div>
						<div id="divExportStep1InvalidSections" style="display: none" class="validationerror"><span class="validationerror" data-i18n="reports-globals-must_select_sections">Debe seleccionar alguna sección a exportar</span></div>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-export" onclick="ExportToMail()">Exportar</button>
							</label>
							<label class="uiButton uiButtonLarge">
								<button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
							</label>
						</div>
					</div>
					<div id="divExportStep2" style="display: none">
						<yoizen:Message ID="messageExportStep2" runat="server" Type="Information" LocalizationKey="reports-globals-export-mail_notification">
							Se enviará un mail a la dirección especificada apenas se encuentre listo el reporte
						</yoizen:Message>
						<div class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
							</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</asp:Panel>
</asp:Content>