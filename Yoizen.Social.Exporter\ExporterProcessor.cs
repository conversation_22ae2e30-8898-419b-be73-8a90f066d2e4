﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Mail;
using Yoizen.Common;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.DTO;
using Yoizen.Social.DomainModel.Reports;
using Yoizen.Social.DomainModel.Reports.Export;
using Yoizen.Social.DomainModel.Settings;
using static Yoizen.Social.DomainModel.MessageSegment;
using static Yoizen.Social.DomainModel.UserLog;

namespace Yoizen.Social.Exporter
{
	public class ExporterProcessor
	{
		#region Constants

		private const string StatusKey = "LastDailyDate";

		#endregion

		#region Fields

		private string pathForFiles;
		private string statusFileDaily;
		private string statusFileScheduled;
		private string socialURL;
		private string socialPathForGeneratedReports;
		private string socialPathForGeneratedDailyReports;
		private DateTime lastReloadCache = DateTime.MinValue;
		private DateTime lastReportsPurged = DateTime.MinValue;
		private bool generatingReport = false;
		private string socialPathForGeneratedScheduledReports;
		private string socialPathForExternalIntegrationsReports;

		#region Storage Fields

		private Azure.Storage.Blobs.BlobContainerClient blobContainerClientForMedia;

		#endregion

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="ExporterProcessor"/>
		/// </summary>
		public ExporterProcessor(string pathForFiles)
		{
			this.socialURL = System.Configuration.ConfigurationManager.AppSettings["SocialURL"];
			this.socialPathForGeneratedReports = System.Configuration.ConfigurationManager.AppSettings["SocialPathForGeneratedReports"];

			if (string.IsNullOrEmpty(this.socialPathForGeneratedReports) || !Directory.Exists(this.socialPathForGeneratedReports))
				throw new System.Configuration.ConfigurationErrorsException("El directorio para exportación no existe");

			this.socialPathForGeneratedDailyReports = Path.Combine(this.socialPathForGeneratedReports, "Daily");
			if (!Directory.Exists(this.socialPathForGeneratedDailyReports))
				Directory.CreateDirectory(this.socialPathForGeneratedDailyReports);

			this.socialPathForGeneratedScheduledReports = Path.Combine(this.socialPathForGeneratedReports, "Scheduled");
			if (!Directory.Exists(this.socialPathForGeneratedScheduledReports))
				Directory.CreateDirectory(this.socialPathForGeneratedScheduledReports);

			this.socialPathForExternalIntegrationsReports = Path.Combine(this.socialPathForGeneratedReports, "External");
			if (!Directory.Exists(this.socialPathForExternalIntegrationsReports))
				Directory.CreateDirectory(this.socialPathForExternalIntegrationsReports);

			this.pathForFiles = pathForFiles;
			this.statusFileDaily = Path.Combine(this.pathForFiles, "status.json");
			this.statusFileScheduled = Path.Combine(this.pathForFiles, "statusScheduled.json");

			DAL.Reports.Export.ReportExportDAO.ResetInProgressReports();
		}

		#endregion

		#region Private Methods

		private static string ConvertToZip(string sourceFileName)
		{
			var fileName = Path.GetFileName(sourceFileName);
			var zipFileName = Path.GetFileNameWithoutExtension(sourceFileName);
			zipFileName += ".zip";

			var destFileName = Path.Combine(Path.GetDirectoryName(sourceFileName), zipFileName);

			using (var ms = File.Create(destFileName))
			{
				using (var archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
				{
					using (var fs = new FileStream(sourceFileName, FileMode.Open, FileAccess.Read))
					{
						var entry = archive.CreateEntry(fileName, CompressionLevel.Fastest);
						using (var es = entry.Open())
						{
							fs.CopyTo(es);
						}
					}
				}

				ms.Flush();
			}

			return destFileName;
		}

		/// <summary>
		/// Guarda un <see cref="DomainModel.Attachment"/> en el Storage
		/// </summary>
		/// <param name="attachment">El <see cref="DomainModel.Attachment"/> a guardar</param>
		/// <param name="serviceId">El código de servicio asociado al mensaje del archivo adjunto</param>
		/// <returns>La ruta en donde se lo guardó</returns>
		private void DeleteReportInStorage(string storagePath)
		{
			if (this.blobContainerClientForMedia == null)
			{
				var accountSafeName = Licensing.LicenseManager.Instance.License.Configuration.ClientID.ToLower();
				this.blobContainerClientForMedia = new Azure.Storage.Blobs.BlobContainerClient(DomainModel.SystemSettings.Instance.Storage.ConnectionString, accountSafeName);
				this.blobContainerClientForMedia.CreateIfNotExists();
			}

			if (this.blobContainerClientForMedia.DeleteBlobIfExists(storagePath))
			{
				Tracer.TraceInfo("Se borró el blob {0} del storage de azure", storagePath);
			}
		}

		/// <summary>
		/// Guarda un <see cref="DomainModel.Attachment"/> en el Storage
		/// </summary>
		/// <param name="attachment">El <see cref="DomainModel.Attachment"/> a guardar</param>
		/// <param name="serviceId">El código de servicio asociado al mensaje del archivo adjunto</param>
		/// <returns>La ruta en donde se lo guardó</returns>
		private string SaveReportInStorage(ReportExport export, string fullname, out string storagePath)
		{
			if (this.blobContainerClientForMedia == null)
			{
				var accountSafeName = Licensing.LicenseManager.Instance.License.Configuration.ClientID.ToLower();
				this.blobContainerClientForMedia = new Azure.Storage.Blobs.BlobContainerClient(DomainModel.SystemSettings.Instance.Storage.ConnectionString, accountSafeName);
				this.blobContainerClientForMedia.CreateIfNotExists();
			}

			try
			{
				if (export.Format == DomainModel.Reports.Export.ExportFormats.CSV)
				{
					Tracer.TraceInfo("Se comprimirá el archivo {0}", fullname);
					fullname = ConvertToZip(fullname);
					Tracer.TraceInfo("Se comprimió el archivo {0}", fullname);
				}

				// Get a reference to a blob
				var blobName = $"ySocial\\Reports\\{export.ID}";
				var blob = this.blobContainerClientForMedia.GetBlobClient(blobName);

				// Upload file data
				var uploadOptions = new Azure.Storage.Blobs.Models.BlobUploadOptions();
				if (uploadOptions.HttpHeaders == null)
					uploadOptions.HttpHeaders = new Azure.Storage.Blobs.Models.BlobHttpHeaders();
				uploadOptions.HttpHeaders.ContentType = export.Format == DomainModel.Reports.Export.ExportFormats.CSV ? "application/zip" : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				if (uploadOptions.Metadata == null)
					uploadOptions.Metadata = new Dictionary<string, string>();

				Tracer.TraceInfo("Se subirá el archivo {0} a {1}", fullname, blobName);
				blob.Upload(fullname, uploadOptions);
				Tracer.TraceInfo("Se subió el archivo {0} a {1}", fullname, blobName);

				var builder = new Azure.Storage.Sas.BlobSasBuilder();
				builder.SetPermissions(Azure.Storage.Sas.BlobSasPermissions.Read);
				builder.ExpiresOn = DateTimeOffset.Now.AddYears(2);

				var reportName = DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", export.Language, export.Type.ToString());

				var contentDisposition = new System.Net.Mime.ContentDisposition();
				if (export.Format == DomainModel.Reports.Export.ExportFormats.CSV)
					contentDisposition.FileName = string.Format("{0}.zip", Uri.EscapeDataString(reportName));
				else
					contentDisposition.FileName = string.Format("{0}.xlsx", Uri.EscapeDataString(reportName));
				contentDisposition.Inline = false;
				builder.ContentDisposition = contentDisposition.ToString();				

				var url = blob.GenerateSasUri(builder).ToString();

				Tracer.TraceInfo("Se obtuvo la url del archivo {0} a {1}: {2}", fullname, blobName, url);

				if (export.Format == DomainModel.Reports.Export.ExportFormats.CSV)
				{
					try
					{
						File.Delete(fullname);
						Tracer.TraceInfo("Se borró el archivo temporal zip del reporte {0}", export.ID);
					}
					catch { }
				}

				storagePath = blobName;

				return url;
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Ocurrió un error subiendo el reporte {0} al storage de azure: {1}", export.ID, ex);
				throw;
			}
		}

		/// <summary>
		/// Guarda un <see cref="DomainModel.Attachment"/> en el Storage
		/// </summary>
		/// <param name="attachment">El <see cref="DomainModel.Attachment"/> a guardar</param>
		/// <param name="serviceId">El código de servicio asociado al mensaje del archivo adjunto</param>
		/// <returns>La ruta en donde se lo guardó</returns>
		private string SaveDailyReportInStorage(string fullname, DomainModel.Reports.Export.ExportFormats format, out string storagePath)
		{
			if (this.blobContainerClientForMedia == null)
			{
				var accountSafeName = Licensing.LicenseManager.Instance.License.Configuration.ClientID.ToLower();
				this.blobContainerClientForMedia = new Azure.Storage.Blobs.BlobContainerClient(DomainModel.SystemSettings.Instance.Storage.ConnectionString, accountSafeName);
				this.blobContainerClientForMedia.CreateIfNotExists();
			}

			try
			{
				if (format == DomainModel.Reports.Export.ExportFormats.CSV)
				{
					Tracer.TraceInfo("Se comprimirá el archivo {0}", fullname);
					fullname = ConvertToZip(fullname);
					Tracer.TraceInfo("Se comprimió el archivo {0}", fullname);
				}

				// Get a reference to a blob
				var blobName = $"ySocial\\Reports\\Daily\\{Uri.EscapeDataString(Path.GetFileName(fullname))}";
				var blob = this.blobContainerClientForMedia.GetBlobClient(blobName);

				// Upload file data
				var uploadOptions = new Azure.Storage.Blobs.Models.BlobUploadOptions();
				if (uploadOptions.HttpHeaders == null)
					uploadOptions.HttpHeaders = new Azure.Storage.Blobs.Models.BlobHttpHeaders();
				uploadOptions.HttpHeaders.ContentType = format == DomainModel.Reports.Export.ExportFormats.CSV ? "application/zip" : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				if (uploadOptions.Metadata == null)
					uploadOptions.Metadata = new Dictionary<string, string>();

				Tracer.TraceInfo("Se subirá el archivo {0} a {1}", fullname, blobName);
				blob.Upload(fullname, uploadOptions);
				Tracer.TraceInfo("Se subió el archivo {0} a {1}", fullname, blobName);

				var builder = new Azure.Storage.Sas.BlobSasBuilder();
				builder.SetPermissions(Azure.Storage.Sas.BlobSasPermissions.Read);
				builder.ExpiresOn = DateTimeOffset.Now.AddYears(2);
				var contentDisposition = new System.Net.Mime.ContentDisposition();
				contentDisposition.FileName = Path.GetFileName(fullname);
				contentDisposition.Inline = false;
				builder.ContentDisposition = contentDisposition.ToString();

				var url = blob.GenerateSasUri(builder).ToString();

				Tracer.TraceInfo("Se obtuvo la url del archivo {0} a {1}: {2}", fullname, blobName, url);

				if (format == DomainModel.Reports.Export.ExportFormats.CSV)
				{
					try
					{
						File.Delete(fullname);
						Tracer.TraceInfo("Se borró el archivo temporal zip del reporte diario {0}", fullname);
					}
					catch { }
				}

				storagePath = blobName;

				return url;
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Ocurrió un error subiendo el reporte {0} al storage de azure: {1}", fullname, ex);
				throw;
			}
		}

		private void ReloadCache()
		{
			if (DateTime.Now.Subtract(this.lastReloadCache).TotalMinutes < 30)
				return;

			Tracer.TraceInfo("Memory used before collection: {0:N0}", GC.GetTotalMemory(false));

			// Collect all generations of memory.
			GC.Collect();
			Tracer.TraceInfo("Memory used after full collection: {0:N0}", GC.GetTotalMemory(true));

			Tracer.TraceInfo("Inicializando Caché");
			DomainModel.Cache.Instance.Enabled = false;

			Tracer.TraceVerb("Obteniendo Etiquetas");
			List<DomainModel.Tag> tags = DAL.TagDAO.GetAll(true);
			foreach (var tag in tags)
			{
				if (tag.Parent != null)
				{
					tag.Parent = tags.Find(t => t.ID == tag.Parent.ID);
					tag.Parent.ChildTags.Add(tag);
				}
			}

			Tracer.TraceVerb("Obteniendo Servicios");
			var services = DAL.ServiceDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Sitios");
			var sites = DAL.SiteDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Colas");
			var queues = DAL.QueueDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Agentes");
			var agents = DAL.AgentDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Usuarios");
			var users = DAL.UserDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Encuestas");
			var surveys = DAL.SurveyDAO.GetAll();
			Tracer.TraceVerb("Obteniendo Motivos de contacto");
			var contactReasons = DAL.ContactReasonDAO.GetAll(true);
			Tracer.TraceVerb("Obteniendo Agentes borrados");
			var agentsDeleted = DAL.AgentDAO.GetDeleted(false);

			// Le agregamos a la cola aquellas etiquetas que son categorías y que tiene alguna etiqueta que esté asociada a la cola
			foreach (var queue in queues)
			{
				List<DomainModel.Tag> tagsToAdd = new List<DomainModel.Tag>();
				for (int i = 0; i < queue.Tags.Count; i++)
				{
					DomainModel.Tag tag = queue.Tags[i];
					tag = tags.Find(t => t.ID == tag.ID);
					queue.Tags[i] = tag;
					if (tag.Parent != null && !queue.Tags.Contains(tag.Parent) && !tagsToAdd.Contains(tag.Parent))
						tagsToAdd.Add(tag.Parent);
				}

				queue.Tags.AddRange(tagsToAdd);
			}

			// Le agregamos a las etiquetas las colas a las que pertenece
			foreach (var tag in tags)
			{
				tag.Queues.AddRange(queues.Where(q => q.Tags.Contains(tag)));
			}

			foreach (var agent in agents)
			{
				if (agent.Site != null)
				{
					agent.Site = sites.Find(s => s.ID == agent.Site.ID);
				}
			}

			DomainModel.Cache.Instance.Enabled = true;
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Tag>(tags);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Service>(services);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Site>(sites);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Queue>(queues);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Agent>(agents);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.User>(users);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Survey>(surveys);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.ContactReason>(contactReasons);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.AgentDeleted>(agentsDeleted);

			Tracer.TraceInfo("Se finalizó de inicializar el Caché");

			this.lastReloadCache = DateTime.Now;
		}

		/// <summary>
		/// Procesa el reporte diario
		/// </summary>
		/// <param name="interval">El intervalo actual</param>
		/// <returns>true si se pudo procesar el reporte; en caso contrario, false</returns>
		private void ProcessDailyReports(Interval interval)
		{
			bool retrieve = false;
			DateTime? lastDate = null;
			DateTime date = interval.IntervalDate;

			if (File.Exists(this.statusFileDaily))
			{
				try
				{
					lastDate = Newtonsoft.Json.JsonConvert.DeserializeObject<DateTime>(File.ReadAllText(this.statusFileDaily));
					//Tracer.TraceVerb("Se leyó el archivo {0} e indica que la última fecha de reportes diarios procesados fue {1}", this.statusFileDaily, lastDate.Value.Date);
				}
				catch { }
			}

			if (lastDate == null)
			{
				retrieve = true;
				lastDate = date.Date.AddDays(-1);
			}
			else
			{
				lastDate = lastDate.Value.AddDays(1);

				if (lastDate < date && interval.IntervalDateTime.Hour >= 1)
				{
					retrieve = true;
				}
			}

			if (!retrieve)
			{
				//Tracer.TraceVerb("No corresponde traer registros diarios");
				return;
			}

			Tracer.TraceInfo("[EXPORTER] Procesando reportes diarios");

			while (lastDate < date.Date)
			{
				DateTime from = SystemSettings.Instance.ConvertDateToServerDate(lastDate.Value);
				string fileName;
				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.Messages))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.Messages, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Messages.ToString(), from, fileName);
					GenerateMessagesReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.Messages);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.MessagesTransfers))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.MessagesTransfers, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.MessagesTransfers.ToString(), from, fileName);
					GenerateMessagesTransfersReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.MessagesTransfers);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.Cases))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.Cases, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Cases.ToString(), from, fileName);
					GenerateCasesReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.Cases);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.SocialUsers))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.SocialUsers, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.SocialUsers.ToString(), from, fileName);
					GenerateSocialUsersReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.SocialUsers);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.DetailedByQueue))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByQueue, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByQueue.ToString(), from, fileName);
					GenerateDetailedByQueueReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByQueue, false);

					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByQueue, Localizations.Instance.GetLocalizedString("reports-agents-filter-type-by_intervals", DomainModel.SystemSettings.Instance.DefaultLanguage, "Por intervalos"), from);
					Tracer.TraceInfo("Se generará el reporte diario por intervalos {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByQueue.ToString(), from, fileName);
					GenerateDetailedByQueuebyIntervalReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByQueue, true);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.TagsByQueue))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.TagsByQueue, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.TagsByQueue.ToString(), from, fileName);
					GenerateTagsByQueueReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.TagsByQueue);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.DetailedByAgent))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByAgent, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByAgent.ToString(), from, fileName);
					GenerateDetailedByAgentReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByAgent, false);

					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByAgent, Localizations.Instance.GetLocalizedString("reports-agents-filter-type-by_intervals", DomainModel.SystemSettings.Instance.DefaultLanguage, "Por intervalos"), from);
					Tracer.TraceInfo("Se generará el reporte diario por intervalos {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByAgent.ToString(), from, fileName);
					GenerateDetailedByAgentbyIntervalReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByAgent, true);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.TagsByAgent))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.TagsByAgent, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.TagsByAgent.ToString(), from, fileName);
					GenerateAgentsTagsReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.TagsByAgent);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.SessionsByAgent))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.SessionsByAgent, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.SessionsByAgent.ToString(), from, fileName);
					GenerateLoginByAgentReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.SessionsByAgent);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.Surveys))
				{
					var surveys = DomainModel.Cache.Instance.GetList<DomainModel.Survey>().Where(s => s.Enabled);
					foreach (var survey in surveys)
					{
						try
						{
							fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.Surveys, survey.Name, from);
							Tracer.TraceInfo("Se generará el reporte diario {0} de la encuesta {3} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Surveys.ToString(), from, fileName, survey.Name);
							GenerateSurveysReport(from, survey, fileName);
							SaveDailyReportInFtp(fileName);
							SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.Surveys, entityId: survey.ID.ToString(), entityName: survey.Name);
						}
						catch (Exception ex)
						{
							Tracer.TraceError("Ocurrió un error al generar el reporte diario de la encuesta {0}: {1}", survey, ex);
						}
					}
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.DetailedByService))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByService, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByService.ToString(), from, fileName);
					GenerateDetailedByServiceReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByService, false);

					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedByService, Localizations.Instance.GetLocalizedString("reports-agents-filter-type-by_intervals", DomainModel.SystemSettings.Instance.DefaultLanguage, "Por intervalos"), from);
					Tracer.TraceInfo("Se generará el reporte diario por intervalos {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByService.ToString(), from, fileName);
					GenerateDetailedByServicebyIntervalReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedByService, true);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.Chats))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.Chats, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Chats.ToString(), from, fileName);
					GenerateChatsReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.Chats);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.ChatsMessages))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.ChatsMessages, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.ChatsMessages.ToString(), from, fileName);
					GenerateChatsMessagesReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.ChatsMessages);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.DetailedWhatsappHSM))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.DetailedWhatsappHSM, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedWhatsappHSM.ToString(), from, fileName);
					GenerateDetailedWhatsappHSMReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.DetailedWhatsappHSM);
				}

				if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.MessagesSegments))
				{
					fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.MessagesSegments, from);
					Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.MessagesSegments.ToString(), from, fileName);
					GenerateMessagesSegmentReport(from, fileName);
					SaveDailyReportInFtp(fileName);
					SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.MessagesSegments);
				}

                if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.UsersLog))
                {
                    fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.UsersLog, from);
                    Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.UsersLog.ToString(), from, fileName);
                    GenerateUsersLogReport(from, fileName);
                    SaveDailyReportInFtp(fileName);
                    SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.UsersLog);
                }

                if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.UserSessions))
                {
                    fileName = GenerateDailyReportFileName(DomainModel.Reports.ReportTypes.UserSessions, from);
                    Tracer.TraceInfo("Se generará el reporte diario {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.UserSessions.ToString(), from, fileName);
                    GenerateUserSessionsReport(from, fileName);
                    SaveDailyReportInFtp(fileName);
                    SaveDailyReportInStorage(from, fileName, DomainModel.Reports.ReportTypes.UserSessions);
                }

                if (DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.WhatsappHSMWithoutCase))
				{
					var fileNames = GenerateDailyExternalReportFileNames(from);
					if (fileNames.Count > 0)
					{
						Tracer.TraceInfo("Se generarán los reportes diarios {0} con fecha {1}", DomainModel.Reports.ReportTypes.WhatsappHSMWithoutCase.ToString(), from);
						foreach (var file in fileNames)
						{
							SaveDailyReportInFtp(file, false);
							DeleteFile(file);
						}
					}
					else
					{
						Tracer.TraceInfo("No se encontraron archivos del tipo {0} para enviar por FTP", DomainModel.Reports.ReportTypes.WhatsappHSMWithoutCase.ToString());
					}
				}

				SendMail(from.Date);

				try
				{
					using (StreamWriter file = File.CreateText(this.statusFileDaily))
					{
						Newtonsoft.Json.JsonSerializer serializer = new Newtonsoft.Json.JsonSerializer();
						serializer.Formatting = Newtonsoft.Json.Formatting.Indented;
						serializer.Serialize(file, from.Date);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló grabar el archivo {0}: {1}", this.statusFileDaily, ex);
				}

				lastDate = lastDate.Value.AddDays(1);
			}

			Tracer.TraceInfo("Se generaron los reportes diarios hasta el día {0:dd/MM/yyyy}", lastDate);
		}

		private void SaveDailyReportInStorage(DateTime from, string fileName, DomainModel.Reports.ReportTypes reportType, bool? byIntervals = null, string entityId = null, string entityName = null)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.SaveReportsInStorage &&
				Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
			{
				var url = SaveDailyReportInStorage(fileName, DomainModel.SystemSettings.Instance.DailyReportsFormat, out string storagePath);

				var export = DomainModel.Reports.Export.ReportExport.Create(reportType);
				export.Filename = Path.GetFileNameWithoutExtension(fileName);
				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Owner = new User()
				{
					ID = 1
				};
				export.OwnerTimeZone = DomainModel.SystemSettings.Instance.DefaultTimeZone.Id;
				export.Daily = true;
				export.Format = DomainModel.SystemSettings.Instance.DailyReportsFormat;
				export.Result = ReportExportResults.Success;
				export.BlobPath = storagePath;
				export.BlobUrl = url;
				if (byIntervals != null)
					export.Parameters["ByIntervals"] = byIntervals.Value.ToString();
				if (entityId != null)
					export.Parameters["EntityId"] = entityId.ToString();
				if (entityName != null)
					export.Parameters["EntityName"] = entityName.ToString();
				var dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();

				if (DomainModel.SystemSettings.Instance.DailyReportsDeleteLocalFiles)
				{
					Tracer.TraceInfo("Se borrará el archivo {0} porque así lo indica Parámetros del sistema y ya se subió a Storage", fileName);
					try
					{
						File.Delete(fileName);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error borrando el archivo {0}: {1}", fileName, ex);
					}
				}
			}
		}

		/// <summary>
		/// Procesa el reporte programado
		/// </summary>
		/// <param name="interval">El intervalo actual</param>
		private void ProcessScheduledReports(Interval interval)
		{
			bool retrieve = false;
			DateTime? lastDate = null;
			DateTime date = interval.IntervalDate;

			if (File.Exists(this.statusFileScheduled))
			{
				try
				{
					lastDate = Newtonsoft.Json.JsonConvert.DeserializeObject<DateTime>(File.ReadAllText(this.statusFileScheduled));
					//Tracer.TraceVerb("Se leyó el archivo {0} e indica que la última fecha de reportes programados procesados fue {1}", this.statusFileScheduled, lastDate.Value.Date);
				}
				catch { }
			}

			if (lastDate == null)
			{
				retrieve = true;
				lastDate = date.Date.AddDays(-7);
			}
			else
			{
				lastDate = lastDate.Value.AddDays(1);

				if (lastDate < date && interval.IntervalDateTime.Hour >= 1)
				{
					retrieve = true;
				}
			}

			if (!retrieve)
			{
				Tracer.TraceVerb("No corresponde traer registros programados");
				return;
			}

			List<DomainModel.Reports.Scheduled.ReportScheduled> reportsScheduled = null;
			Tracer.TraceInfo("[EXPORTER] Procesando reportes programados");

			if (lastDate < date.Date)
			{
				reportsScheduled = DAL.Reports.Scheduled.ReportScheduledDAO.GetAllEnabled();
			}

			if (reportsScheduled == null || !reportsScheduled.Any())
			{
				Tracer.TraceVerb("No hay registros programados para ejecutar");
				return;
			}

			while (lastDate < date.Date)
			{
				DateTime from = SystemSettings.Instance.ConvertDateToServerDate(lastDate.Value);
				string fileName;

				IEnumerable<DomainModel.Reports.Scheduled.ReportScheduled> reportsToGenerate = reportsScheduled;
				var firstDayOfWeek = lastDate.Value.AddDays(((int) (lastDate.Value.DayOfWeek) * -1) + 1);
				var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
				if (lastDate != firstDayOfWeek)
				{
					reportsToGenerate = reportsToGenerate.Where(r => r.Periodicity != DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly).ToList();
				}
				if (lastDate != firstDayOfMonth)
				{
					reportsToGenerate = reportsToGenerate.Where(r => r.Periodicity != DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly).ToList();
				}

				foreach (var report in reportsToGenerate)
				{
					switch (report.Type)
					{
						case ReportTypes.Messages:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.Messages, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Messages.ToString(), from, fileName);
							GenerateMessagesReportScheduled(from, fileName, report);
							break;
						case ReportTypes.Cases:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.Cases, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Cases.ToString(), from, fileName);
							GenerateCasesReportScheduled(from, fileName, report);
							break;
						case ReportTypes.SocialUsers:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.SocialUsers, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.SocialUsers.ToString(), from, fileName);
							GenerateSocialUsersReportScheduled(from, fileName, report);
							break;
						case ReportTypes.DetailedByQueue:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.DetailedByQueue, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByQueue.ToString(), from, fileName);
							GenerateDetailedByQueueReportScheduled(from, fileName, report);
							break;
						case ReportTypes.TagsByQueue:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.TagsByQueue, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.TagsByQueue.ToString(), from, fileName);
							GenerateTagsByQueueReportScheduled(from, fileName, report);
							break;
						case ReportTypes.DetailedByAgent:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.DetailedByAgent, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByAgent.ToString(), from, fileName);
							GenerateDetailedByAgentReportScheduled(from, fileName, report);
							break;
						case ReportTypes.TagsByAgent:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.TagsByAgent, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.TagsByAgent.ToString(), from, fileName);
							GenerateAgentsTagsReportScheduled(from, fileName, report);
							break;
						case ReportTypes.SessionsByAgent:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.SessionsByAgent, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.SessionsByAgent.ToString(), from, fileName);
							GenerateLoginByAgentReportScheduled(from, fileName, report);
							break;
						case ReportTypes.Chats:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.Chats, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Chats.ToString(), from, fileName);
							GenerateChatsReportScheduled(from, fileName, report);
							break;
						case ReportTypes.Surveys:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.Surveys, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.Surveys.ToString(), from, fileName);
							GenerateSurveysReportScheduled(from, fileName, report);
							break;
						case ReportTypes.DailyByCase:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.DailyByCase, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DailyByCase.ToString(), from, fileName);
							GenerateDailyByCaseReportScheduled(from, fileName, report);
							break;
						case ReportTypes.UsersLog:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.UsersLog, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.UsersLog.ToString(), from, fileName);
							GenerateUsersLogReportScheduled(from, fileName, report);
							break;
						case ReportTypes.Users:
							break;
						case ReportTypes.Agents:
							break;
						case ReportTypes.AgentSession:
							break;
						case ReportTypes.DetailedByService:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.DetailedByService, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedByService.ToString(), from, fileName);
							GenerateDetailedByServiceReportScheduled(from, fileName, report);
							break;
						case ReportTypes.Tags:
							break;
						case ReportTypes.WhiteList:
							break;
						case ReportTypes.BlackList:
							break;
						case ReportTypes.DetailedWhatsappHSM:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.DetailedWhatsappHSM, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.DetailedWhatsappHSM.ToString(), from, fileName);
							GenerateDetailedWhatsappHSMReportScheduled(from, fileName, report);
							break;
						case ReportTypes.ConsolidatedWhatsappHSM:
							break;
						case ReportTypes.WhatsappHSMTasks:
							break;
						case ReportTypes.Tasks:
							break;
						case ReportTypes.ConsolidatedSurvey:
							break;
						case ReportTypes.ChatMessages:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.ChatsMessages, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.ChatsMessages.ToString(), from, fileName);
							GenerateChatsMessagesReportScheduled(from, fileName, report);
							break;
						case ReportTypes.AdherenceConsolidated:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.AdherenceConsolidated, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.AdherenceConsolidated.ToString(), from, fileName);
							GenerateAdherenceConsolidatedReportScheduled(from, fileName, report);
							break;
						case ReportTypes.AdherenceDetailed:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.AdherenceDetailed, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.AdherenceDetailed.ToString(), from, fileName);
							GenerateAdherenceDetailedReportScheduled(from, fileName, report);
							break;
						case ReportTypes.SocialUserProfiles:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.SocialUserProfiles, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.SocialUserProfiles.ToString(), from, fileName);
							GenerateSocialUserProfilesReportScheduled(from, fileName, report);
							break;
						case ReportTypes.UserSessions:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.UserSessions, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.UserSessions.ToString(), from, fileName);
							GenerateUserSessionsReportScheduled(from, fileName, report);
							break;
						case ReportTypes.WhatsappHSMRequestTasks:
							break;
						case ReportTypes.MessagesTransfers:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.MessagesTransfers, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.MessagesTransfers.ToString(), from, fileName);
							GenerateMessagesTransfersReportScheduled(from, fileName, report);
							break;
						case ReportTypes.CasesReopenings:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.CasesReopenings, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.CasesReopenings.ToString(), from, fileName);
							GenerateCasesReopeningsReportScheduled(from, fileName, report);
							break;
						case ReportTypes.ChatsMessages:
							break;
						case ReportTypes.MessagesSegments:
							fileName = GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes.MessagesSegments, from, report.Periodicity, report.Format, report.ID);
							Tracer.TraceInfo("Se generará el reporte programado {0} de la fecha {1} con archivo {2}", DomainModel.Reports.ReportTypes.MessagesSegments.ToString(), from, fileName);
							GenerateMessagesSegmentReportScheduled(from, fileName, report);
							break;
						case ReportTypes.TesterList:
							break;
						case ReportTypes.DoNotCallList:
							break;
						case ReportTypes.ProfilesMassiveUploadTasks:
							break;
						case ReportTypes.Queues:
							break;
						case ReportTypes.WhatsappHSMWithoutCase:
							break;
						case ReportTypes.AgentQueues:
							break;
						case ReportTypes.RTAgents:
							break;
						case ReportTypes.CasesTimes:
							break;
						case ReportTypes.VideoCalls:
							break;
						case ReportTypes.UsersLogWithoutParameters:
							break;
						case ReportTypes.Calls:
							break;
						default:
							break;
					}
				}

				try
				{
					using (StreamWriter file = File.CreateText(this.statusFileScheduled))
					{
						Newtonsoft.Json.JsonSerializer serializer = new Newtonsoft.Json.JsonSerializer();
						serializer.Formatting = Newtonsoft.Json.Formatting.Indented;
						serializer.Serialize(file, from.Date);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló grabar el archivo {0}: {1}", this.statusFileScheduled, ex);
				}

				lastDate = lastDate.Value.AddDays(1);
			}

			Tracer.TraceInfo("Se generaron los reportes programados hasta el día {0:dd/MM/yyyy}", lastDate);
		}

		private string GenerateDailyReportFileName(ReportTypes type, DateTime date)
		{
			var extension = "txt";
			if (DomainModel.SystemSettings.Instance.DailyReportsFormat == DomainModel.Reports.Export.ExportFormats.Excel)
				extension = "xlsx";
			else if (DomainModel.SystemSettings.Instance.DailyReportsFormat == DomainModel.Reports.Export.ExportFormats.CSV)
				extension = "csv";

			var filename = string.Format("{0} - {1:yyyy-MM-dd}.{2}",
				DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{type}", DomainModel.SystemSettings.Instance.DefaultLanguage, type.ToString()),
				date,
				extension);

			var fullname = Path.Combine(this.socialPathForGeneratedDailyReports, filename);

			return fullname;
		}

		private string GenerateScheduledReportFileName(DomainModel.Reports.ReportTypes type, DateTime date, DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity periodicity, DomainModel.Reports.Export.ExportFormats format, int reportId)
		{
			var extension = "txt";
			if (format == DomainModel.Reports.Export.ExportFormats.Excel)
				extension = "xlsx";
			else if (format == DomainModel.Reports.Export.ExportFormats.CSV)
				extension = "csv";

			var filename = string.Format("{0} - {1} - {4} - {2:yyyy-MM-dd}.{3}",
				DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{type}", DomainModel.SystemSettings.Instance.DefaultLanguage, type.ToString()),
				periodicity,
				date,
				extension,
				reportId);

			var fullname = Path.Combine(this.socialPathForGeneratedScheduledReports, filename);

			return fullname;
		}

		private static string GenerateSafePath(string path)
		{
			var regexSearch = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
			var r = new System.Text.RegularExpressions.Regex(string.Format("[{0}]", System.Text.RegularExpressions.Regex.Escape(regexSearch)));
			path = r.Replace(path, "_");
			return path;
		}

		private string GenerateDailyReportFileName(ReportTypes type, string entity, DateTime date)
		{
			var extension = "txt";
			if (DomainModel.SystemSettings.Instance.DailyReportsFormat == DomainModel.Reports.Export.ExportFormats.Excel)
				extension = "xlsx";
			else if (DomainModel.SystemSettings.Instance.DailyReportsFormat == DomainModel.Reports.Export.ExportFormats.CSV)
				extension = "csv";

			var filename = string.Format("{0} - {1} - {2:yyyy-MM-dd}.{3}",
				DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{type}", DomainModel.SystemSettings.Instance.DefaultLanguage, type.ToString()),
				entity,
				date,
				extension);

			filename = GenerateSafePath(filename);

			var fullname = Path.Combine(this.socialPathForGeneratedDailyReports, filename);

			return fullname;
		}

		private void ProcessNonGeneratedReports()
		{
			if (this.generatingReport)
			{
				Tracer.TraceInfo("Ya se está generando un reporte");
				return;
			}

			this.generatingReport = true;

			var reportToExport = DAL.Reports.Export.ReportExportDAO.GetNextPending();

			if (reportToExport == null)
			{
				this.generatingReport = false;
				return;
			}

			var tokenSource = new System.Threading.CancellationTokenSource();
			System.Threading.Tasks.Task task = null;

			try
			{
				Tracer.TraceInfo("Se generará el reporte {0} con código {1} solicitado por {2} con TimeZone={3}-{4} y lenguaje={4}", reportToExport.Type.ToString(), reportToExport.ID, reportToExport.Owner, reportToExport.OwnerTimeZone, reportToExport.Owner.TimeZoneId, reportToExport.Language);

				reportToExport.Status = DomainModel.Reports.Export.ReportExportStatus.InProgress;
				reportToExport.DateStarted = DateTime.Now;
				reportToExport.WriteToLog = true;
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(reportToExport);

				task = System.Threading.Tasks.Task.Factory.StartNew(state =>
				{
					var token = (System.Threading.CancellationToken) state;
					try
					{
						string extension = "txt";

						switch (reportToExport.Format)
						{
							case DomainModel.Reports.Export.ExportFormats.Excel:
								extension = "xlsx";
								break;
							case DomainModel.Reports.Export.ExportFormats.CSV:
								extension = "csv";
								break;
							case DomainModel.Reports.Export.ExportFormats.ZIP:
								extension = "zip";
								break;
						}

						string filename = string.Format("{0}.{1}", Guid.NewGuid(), extension);
						string fullname = Path.Combine(this.socialPathForGeneratedReports, filename);
						
						if (reportToExport.Type == ReportTypes.WhatsappHSMWithoutCase)
						{
							filename = reportToExport.Filename;
							fullname = Path.Combine(this.socialPathForExternalIntegrationsReports, filename);
						}

						Tracer.TraceInfo("Se generará el archivo {0} correspondiente al reporte {1}", fullname, reportToExport.ID);

						switch (reportToExport.Type)
						{
							case ReportTypes.Messages:
								{
									#region ReportsTypes.Messages

									var messagesExport = reportToExport as DomainModel.Reports.Export.MessagesExport;
									int? pageSize;
									DAL.MessageDataReader reader = null;

									if (messagesExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = messagesExport.TotalResults;
									}

									try
									{
										reader = DAL.MessageDAO.Search(messagesExport,
											0,
											pageSize);

										if (pageSize > 1048576 && messagesExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", messagesExport.ID);
											messagesExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										messagesExport.Generate<DomainModel.Message>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.Cases:
								{
									#region ReportsTypes.Cases

									var casesExport = reportToExport as DomainModel.Reports.Export.CasesExport;
									int pageSize;
									DomainModel.Case.CaseDataReader reader = null;

									if (casesExport.ExportComplete)
									{
										pageSize = casesExport.TotalRecords;
									}
									else
									{
										pageSize = casesExport.TotalResults;
									}

									try
									{
										reader = DAL.CaseDAO.Search(casesExport, pageSize);

										if (pageSize > 1048576 && casesExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", casesExport.ID);
											casesExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										casesExport.Generate<DomainModel.Case>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.SocialUsers:
								{
									#region ReportsTypes.SocialUsers

									var socialUsersExport = reportToExport as DomainModel.Reports.Export.SocialUsersExport;
									int pageSize;
									DomainModel.SocialUser.SocialUserDataReader reader = null;

									if (socialUsersExport.ExportComplete)
									{
										pageSize = socialUsersExport.TotalRecords;
									}
									else
									{
										pageSize = socialUsersExport.TotalResults;
									}

									try
									{
										reader = DAL.SocialUserDAO.Search(socialUsersExport);

										if (pageSize > 1048576 && socialUsersExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", socialUsersExport.ID);
											socialUsersExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										socialUsersExport.Generate<DomainModel.SocialUser>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.Chats:
								{
									#region ReportsTypes.Chats

									var chatsExport = reportToExport as DomainModel.Reports.Export.ChatsExport;
									int? pageSize;
									DomainModel.Chat.ChatDataReader reader = null;

									if (chatsExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = chatsExport.TotalResults;
									}

									try
									{
										reader = DAL.ChatDAO.Search(chatsExport, 0);

										if (pageSize > 1048576 && chatsExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", chatsExport.ID);
											chatsExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										chatsExport.Generate<DomainModel.Chat>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.Surveys:
								{
									#region ReportsTypes.Surveys

									var surveysExport = reportToExport as DomainModel.Reports.Export.SurveysExport;
									int? pageSize;
									DomainModel.SurveyAnswer.SurveyAnswerDataReader reader = null;

									if (surveysExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = surveysExport.TotalResults;
									}

									if (surveysExport.Surveys != null && surveysExport.Surveys.Length == 1)
									{
										var survey = DomainModel.Cache.Instance.GetItem<DomainModel.Survey>(surveysExport.Surveys[0].ToString());
										surveysExport.SurveyConfiguration = survey.Configuration;
									}

									try
									{
										reader = DAL.SurveyDAO.Search(surveysExport, pageSize);

										if (pageSize > 1048576 && surveysExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", surveysExport.ID);
											surveysExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										surveysExport.Generate<DomainModel.SurveyAnswer>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.DetailedByAgent:
								{
									#region ReportsTypes.DetailedByAgent

									var detailedByAgentExport = reportToExport as DomainModel.Reports.Export.DetailedByAgentExport;
									detailedByAgentExport.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);

									DomainModel.Historical.Daily.DailyDataReader reader = null;
									try
									{
										if (detailedByAgentExport.ByIntervals)
										{
											reader = DAL.Historical.DailyDAO.SearchByAgents(detailedByAgentExport);
										}
										else
										{
											reader = DAL.Historical.DailyByDayDAO.SearchByAgents(detailedByAgentExport);
										}

										var auxReasons = DAL.AuxReasonDAO.GetAll();
										detailedByAgentExport.AuxReasons = auxReasons.ToArray();
										detailedByAgentExport.Generate<DomainModel.Historical.Daily>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.TagsByAgent:
								{
									#region ReportsTypes.TagsByAgent

									var tagsByAgentExport = reportToExport as DomainModel.Reports.Export.TagsByAgentExport;
									DomainModel.Historical.DailyTag.DailyTagDataReader reader = null;

									try
									{
										if (tagsByAgentExport.ByIntervals)
										{
											reader = DAL.Historical.DailyTagDAO.SearchByAgents(tagsByAgentExport);
										}
										else
										{
											reader = DAL.Historical.DailyTagByDayDAO.SearchByAgents(tagsByAgentExport);
										}

										tagsByAgentExport.Generate<DomainModel.Historical.DailyTag>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.SessionsByAgent:
								{
									#region ReportsTypes.SessionsByAgent

									var sessionsByAgentExport = reportToExport as DomainModel.Reports.Export.SessionsByAgentExport;
									DomainModel.Historical.Session.SessionDataReader reader = null;

									try
									{
										reader = DAL.Historical.SessionDAO.Search(sessionsByAgentExport);

										sessionsByAgentExport.Generate<DomainModel.Historical.Session>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.DetailedByQueue:
								{
									#region ReportsTypes.DetailedByQueue

									var detailedByQueueExport = reportToExport as DomainModel.Reports.Export.DetailedByQueueExport;
									detailedByQueueExport.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
									detailedByQueueExport.IncludeSurveysColumns = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys;

									DomainModel.Historical.Daily.DailyDataReader reader = null;
									try
									{
										if (detailedByQueueExport.ByIntervals)
										{
											reader = DAL.Historical.DailyDAO.SearchByQueues(detailedByQueueExport);
										}
										else
										{
											reader = DAL.Historical.DailyByDayDAO.SearchByQueues(detailedByQueueExport);
										}

										var auxReasons = DAL.AuxReasonDAO.GetAll();
										detailedByQueueExport.AuxReasons = auxReasons.ToArray();
										detailedByQueueExport.Generate<DomainModel.Historical.Daily>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.TagsByQueue:
								{
									#region ReportsTypes.TagsByQueue

									var tagsByQueueExport = reportToExport as DomainModel.Reports.Export.TagsByQueueExport;
									DomainModel.Historical.DailyTag.DailyTagDataReader reader = null;

									try
									{
										if (tagsByQueueExport.ByIntervals)
										{
											reader = DAL.Historical.DailyTagDAO.SearchByQueues(tagsByQueueExport);
										}
										else
										{
											reader = DAL.Historical.DailyTagByDayDAO.SearchByQueues(tagsByQueueExport);
										}

										tagsByQueueExport.Generate<DomainModel.Historical.DailyTag>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.DailyByCase:
								{
									#region ReportsTypes.DailyByCase

									var dailyByCaseExport = reportToExport as DomainModel.Reports.Export.DailyByCaseExport;

									DomainModel.Historical.DailyCase.DailyCaseDataReader reader = null;

									try
									{
										if (dailyByCaseExport.ByIntervals)
											reader = DAL.Historical.DailyCaseDAO.Search(dailyByCaseExport);
										else
											reader = DAL.Historical.DailyCaseByDayDAO.Search(dailyByCaseExport);

										dailyByCaseExport.Generate<DomainModel.Historical.DailyCase>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.DetailedByService:
								{
									#region ReportsTypes.DetailedByService

									var detailedByServiceExport = reportToExport as DomainModel.Reports.Export.DetailedByServiceExport;
									detailedByServiceExport.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
									detailedByServiceExport.IncludePublicAndPrivateColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Facebook || s == DomainModel.SocialServiceTypes.Twitter);
									detailedByServiceExport.IncludeYFlowColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled;

									DomainModel.Historical.DailyService.DailyServiceDataReader reader = null;

									try
									{
										if (detailedByServiceExport.ByIntervals)
											reader = DAL.Historical.DailyServiceDAO.Search(detailedByServiceExport);
										else
											reader = DAL.Historical.DailyServiceByDayDAO.Search(detailedByServiceExport);

										detailedByServiceExport.Generate<DomainModel.Historical.DailyService>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.DetailedWhatsappHSM:
								{
									#region ReportsTypes.DetailedWhatsappHSM

									var messagesExport = reportToExport as DomainModel.Reports.Export.DetailedWhatsappHSMExport;
									int currentPage = 0;
									int? pageSize;
									DAL.MessageDataReader reader = null;

									if (messagesExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = messagesExport.TotalResults;
									}

									try
									{
										reader = DAL.MessageDAO.SearchByPayment(messagesExport,
											currentPage,
											pageSize);

										if (pageSize > 1048576 && messagesExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", messagesExport.ID);
											messagesExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										messagesExport.Generate<DomainModel.Message>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.ConsolidatedWhatsappHSM:
								{
									#region ReportsTypes.ConsolidatedWhatsappHSM

									var detailedByServiceExport = reportToExport as DomainModel.Reports.Export.ConsolidatedWhatsappHSMExport;
									DomainModel.Historical.DailyService.DailyServiceDataReader reader = null;

									try
									{
										if (detailedByServiceExport.ByIntervals)
											reader = DAL.Historical.DailyServiceDAO.Search(detailedByServiceExport);
										else
											reader = DAL.Historical.DailyServiceByDayDAO.Search(detailedByServiceExport);

										detailedByServiceExport.Generate<DomainModel.Historical.DailyService>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}
									#endregion
								}
								break;

							case ReportTypes.ConsolidatedSurvey:
								{
									#region ReportsTypes.DetailedBySurvey

									var detailedBySurveyExport = reportToExport as DomainModel.Reports.Export.DetailedBySurveyExport;
									DomainModel.Historical.DailySurvey.DailySurveyDataReader reader = null;

									try
									{
										if (detailedBySurveyExport.ByIntervals)
											reader = DAL.Historical.DailySurveyDAO.Search(detailedBySurveyExport);
										else
											reader = DAL.Historical.DailySurveyByDayDAO.Search(detailedBySurveyExport);

										detailedBySurveyExport.Generate<DomainModel.Historical.DailySurvey>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}
									#endregion
								}
								break;

							case ReportTypes.AdherenceConsolidated:
								{
									#region ReportsTypes.AdherenceConsolidated

									var adherenceConsolidatedExport = reportToExport as DomainModel.Reports.Export.AdherenceConsolidatedExport;

									DomainModel.Historical.Daily.DailyDataReader reader = null;
									try
									{
										reader = DAL.Historical.DailyDAO.SearchByAgentsAdherence(adherenceConsolidatedExport);
										var consolidatedRecords = DomainModel.Site.ConsolidateAdherence(adherenceConsolidatedExport.Sites, reader, new Common.Interval(adherenceConsolidatedExport.From, 2), new Common.Interval(adherenceConsolidatedExport.To, 2));
										adherenceConsolidatedExport.Generate<DomainModel.Historical.DailyAdherence>(consolidatedRecords, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.AdherenceDetailed:
								{
									#region ReportsTypes.AdherenceDetailed

									var adherenceDetailedExport = reportToExport as DomainModel.Reports.Export.AdherenceDetailedExport;

									DomainModel.Historical.Daily.DailyDataReader reader = null;
									try
									{
										reader = DAL.Historical.DailyDAO.SearchByAgentsAdherence(adherenceDetailedExport);
										adherenceDetailedExport.Generate<DomainModel.Historical.Daily>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.SocialUserProfiles:
								{
									#region ReportsTypes.SocialUserProfiles

									var socialUserProfilesExport = reportToExport as DomainModel.Reports.Export.SocialUserProfilesExport;

									DomainModel.SocialUserProfile.SocialUserProfileDataReader reader = null;
									try
									{
										reader = DAL.SocialUserProfileDAO.Search(socialUserProfilesExport, null);
										socialUserProfilesExport.Generate<DomainModel.SocialUserProfile>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.UserSessions:
								{
									#region ReportsTypes.SessionsByAgent

									var userSessionsExport = reportToExport as DomainModel.Reports.Export.UserSessionsExport;
									DomainModel.Historical.UserSession.UserSessionDataReader reader = null;

									try
									{
										reader = DAL.Historical.UserSessionDAO.Search(userSessionsExport);

										userSessionsExport.Generate<DomainModel.Historical.UserSession>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.MessagesTransfers:
								{
									#region ReportsTypes.MessagesTransfers

									var messagesExport = reportToExport as DomainModel.Reports.Export.MessagesTransfersExport;
									int? pageSize;
									DomainModel.MessageTransfer.MessageTransferDataReader reader = null;

									if (messagesExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = messagesExport.TotalResults;
									}

									try
									{
										reader = DAL.MessageTransferDAO.Search(messagesExport,
											0,
											pageSize);

										if (pageSize > 1048576 && messagesExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", messagesExport.ID);
											messagesExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										messagesExport.Generate(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.ChatsMessages:
								{
									#region ReportsTypes.ChatsMessages

									var chatsExport = reportToExport as DomainModel.Reports.Export.ChatsMessagesExport;
									int? pageSize;
									DomainModel.ChatMessage.ChatMessageDataReader reader = null;

									if (chatsExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = chatsExport.TotalResults;
									}

									try
									{
										reader = DAL.ChatMessageDAO.Search(chatsExport);

										if (pageSize > 1048576 && chatsExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", chatsExport.ID);
											chatsExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										chatsExport.Generate(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.CasesReopenings:
								{
									#region ReportsTypes.CasesReopenings

									var casesExport = reportToExport as DomainModel.Reports.Export.CasesReopeningsExport;
									int pageSize;
									DomainModel.CaseReopening.CaseReopeningDataReader reader = null;

									if (casesExport.ExportComplete)
									{
										pageSize = casesExport.TotalRecords;
									}
									else
									{
										pageSize = casesExport.TotalResults;
									}

									try
									{
										reader = DAL.CaseReopeningDAO.Search(casesExport, null, null);

										if (pageSize > 1048576 && casesExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", casesExport.ID);
											casesExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										casesExport.Generate(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.MessagesSegments:
								{
									#region ReportsTypes.MessagesSegments

									var messagesSegmentsExport = reportToExport as DomainModel.Reports.Export.MessagesSegmentsExport;
									int? pageSize;
									MessageSegmentDataReader reader = null;

									if (messagesSegmentsExport.ExportComplete)
									{
										pageSize = null;
									}
									else
									{
										pageSize = messagesSegmentsExport.TotalResults;
									}

									try
									{
										reader = DAL.MessageSegmentDAO.Search(messagesSegmentsExport);

										if (pageSize > 1048576 && messagesSegmentsExport.Format == DomainModel.Reports.Export.ExportFormats.Excel)
										{
											Tracer.TraceInfo("Se modificará el tipo de exportación del reporte {0} a CSV por superar la máxima cantidad de registros de un archivo excel", messagesSegmentsExport.ID);
											messagesSegmentsExport.Format = DomainModel.Reports.Export.ExportFormats.CSV;
										}

										messagesSegmentsExport.Generate<DomainModel.MessageSegment>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							case ReportTypes.UsersLog:
								{
									#region ReportsTypes.UsersLog
									var usersLogExport = reportToExport as DomainModel.Reports.Export.UsersLogExport;

									DomainModel.UserLog.UserLogDataReader reader = null;
									try
									{
										reader = DAL.UserLogDAO.Search(usersLogExport);
										usersLogExport.Generate<DomainModel.UserLog>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}
									#endregion
								}
								break;

							case ReportTypes.WhatsappHSMWithoutCase:
								{
									#region ReportsTypes.WhatsappHSMWithoutCase
									
									var hsmWithoutCaseReport = reportToExport as DomainModel.Reports.Export.WhatsappHSMWithoutCase;

									if (!string.IsNullOrEmpty(hsmWithoutCaseReport.FileUrl))
									{
										try
										{
											using (var wc = new WebClient())
											{
												wc.DownloadFile(hsmWithoutCaseReport.FileUrl, fullname);
											}
											
											Tracer.TraceInfo("Se descargó el archivo SIN CASO {0} correspondiente al reporte {1}", filename, reportToExport.ID);
										}

										catch (Exception ex)
										{
											Tracer.TraceError("Ocurrió un error al intentar descartgar el reporte HSM sin caso: {0}", ex);
										}
									}
									#endregion
								}
								break;

							case ReportTypes.Calls:
								{
									#region ReportsTypes.Calls
									var callsExport = reportToExport as DomainModel.Reports.Export.CallsExport;
									DomainModel.Call.CallDataReader reader = null;

									try
									{
										reader = DAL.CallDAO.Search(callsExport);
										callsExport.Generate<DomainModel.Call>(reader, fullname, token);
										
										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}
									#endregion
								}
								break;

							case ReportTypes.DoNotCallList:
								{
									#region ReportTypes.DoNotCallList

									var doNotCallListExport = reportToExport as DomainModel.Reports.Export.DoNotCallListExport;
									DomainModel.SocialUser.SocialUserDataReader reader = null;

									try
									{
										reader = DAL.SocialUserDAO.SearchByDoNotCallStatus(true);
										/*DomainModel.SystemSettings.Instance.DoNotCallSocialUsers.ForEach(u =>
										{
											socialUsers.Add(new DomainModel.SocialUser(0, u.SocialServiceType)
											{
												DisplayName = u.DisplayName,
												Name = u.Name,
												ID = u.ID,
												Profile = new DomainModel.SocialUserProfile()
												{
													ID = 0,
													Name = u.Name
												}
											});
										});*/

										doNotCallListExport.Generate<DomainModel.SocialUser>(reader, fullname, token);

										Tracer.TraceInfo("Se generó el archivo {0} correspondiente al reporte {1}", filename, reportToExport.ID);
									}
									finally
									{
										if (reader != null)
											reader.Dispose();
									}

									#endregion
								}
								break;

							default:
								Tracer.TraceInfo("El tipo de reporte {0} no existe", reportToExport.Type);
								throw new NotImplementedException();
						}

						if (!token.IsCancellationRequested)
						{
							Tracer.TraceInfo("Se generó el reporte {0} con código {1} solicitado por {2} en el archivo {3}", reportToExport.Type.ToString(), reportToExport.ID, reportToExport.Owner, filename);

							if (reportToExport.Type == ReportTypes.WhatsappHSMWithoutCase)
							{
								DAL.Reports.Export.ReportExportDAO.Delete(reportToExport.ID);
							}
							else
							{
								reportToExport.Status = DomainModel.Reports.Export.ReportExportStatus.Finished;
								reportToExport.Result = DomainModel.Reports.Export.ReportExportResults.Success;
								reportToExport.Filename = filename;
								reportToExport.Generated = true;
								reportToExport.DateGenerated = DateTime.Now;

								DAL.Reports.Export.ReportExportDAO.UpdateStatus(reportToExport);

								if (Licensing.LicenseManager.Instance.License.Configuration.SaveReportsInStorage &&
									Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
								{
									var url = SaveReportInStorage(reportToExport, fullname, out string storagePath);
									SendMail(reportToExport, reportToExport.Email, url);

									var jsonfileDescriptor = Path.Combine(this.socialPathForGeneratedReports, $"{Path.GetFileNameWithoutExtension(filename)}.json");
									File.WriteAllText(jsonfileDescriptor, Newtonsoft.Json.JsonConvert.SerializeObject(new
									{
										url = url,
										storagePath = storagePath,
										reportType = reportToExport.Type,
										reportId = reportToExport.ID
									}));
								}
								else
								{
									SendMail(reportToExport, reportToExport.Email);
								}
							}
						}
					}
					catch (Exception ex)
					{
						if (!token.IsCancellationRequested)
						{
							Tracer.TraceError("Hubo un error generando el reporte {0} con código {1} solicitado por {2}: {3}", reportToExport.Type.ToString(), reportToExport.ID, reportToExport.Owner, ex);

							if (reportToExport.Type == ReportTypes.WhatsappHSMWithoutCase)
							{
								DAL.Reports.Export.ReportExportDAO.Delete(reportToExport.ID);
							}
							else
							{
								reportToExport.Status = DomainModel.Reports.Export.ReportExportStatus.Finished;
								reportToExport.Result = DomainModel.Reports.Export.ReportExportResults.Failed;
								reportToExport.Filename = null;
								reportToExport.Generated = false;
								reportToExport.DateGenerated = null;
								DAL.Reports.Export.ReportExportDAO.UpdateStatus(reportToExport);
							}
						}
					}
				}, tokenSource.Token);

				var ts = TimeSpan.FromMinutes(DomainModel.SystemSettings.Instance.MinutesToAbortExporting);
				if (!task.Wait(ts))
				{
					Tracer.TraceInfo("Se solilcita cancelar la tarea");
					tokenSource.Cancel();

					int times = 0;
					while (!task.IsCanceled && !task.IsCompleted && times < 60)
					{
						System.Threading.Thread.Sleep(1000);
						Tracer.TraceInfo("Se espera a que termine la tarea 1 segundo más");
						times++;
					}

					Tracer.TraceInfo("Se canceló la generación del reporte {0} con código {1} solicitado por {2} luego de {3} minutos", reportToExport.Type.ToString(), reportToExport.ID, reportToExport.Owner, ts.TotalMinutes);

					if (reportToExport.Type == ReportTypes.WhatsappHSMWithoutCase)
					{
						DAL.Reports.Export.ReportExportDAO.Delete(reportToExport.ID);
					}
					else
					{
						reportToExport.Status = DomainModel.Reports.Export.ReportExportStatus.Finished;
						reportToExport.Result = DomainModel.Reports.Export.ReportExportResults.Aborted;
						reportToExport.Filename = null;
						reportToExport.Generated = false;
						reportToExport.DateGenerated = null;
						DAL.Reports.Export.ReportExportDAO.UpdateStatus(reportToExport);

						SendAbortedMail(reportToExport, reportToExport.Email);
					}
				}
			}
			finally
			{
				try
				{
					if (task != null)
						task.Dispose();
				}
				catch { }

				try
				{
					tokenSource.Dispose();
				}
				catch { }

				this.generatingReport = false;
			}
		}

		private void SendMail(DateTime dateTime, string url = null)
		{
			if (url == null)
				url = string.Format("{0}Reports/Exporter.aspx?date={1:yyyy-MM-dd}", this.socialURL, dateTime);

			var types = string.Join(", ", DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Select(type => DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{type}", DomainModel.SystemSettings.Instance.DefaultLanguage, type.ToString())));

			var templateParameters = new Dictionary<string, object>();
			templateParameters["@@LINK@@"] = string.Format("<a href='{0}'>aqu&#237;</a>", url);
			templateParameters["@@TIPOS@@"] = types;
			templateParameters["@@FECHA@@"] = dateTime.ToString("D");

			var subjectParameters = new Dictionary<string, object>();
			subjectParameters["@@FECHA@@"] = dateTime.ToString("D");
			subjectParameters["@@TIPOS@@"] = types;
			DomainModel.SystemSettings.Instance.SendMailMessage(DomainModel.SystemSettings.Instance.EmailDailyReports
				, subjectParameters
				, templateParameters
				, null
				, false);
		}

		private void SendMail(DomainModel.Reports.Export.ReportExport export, string email, string url = null)
		{
			if (url == null)
			{
				url = string.Format("{0}Services/Reports/DownloadReport?id={1}", this.socialURL, export.ID);
				var uri = Common.AuthorizationHelper.AppendSignatureToUrl(new Uri(url), "report", true, "sig").ToString();
				url = uri.ToString();
			}

			string link = string.Format("<a href='{0}'>aqu&#237;</a>", url);

			var templateParameters = new Dictionary<string, object>();
			templateParameters["@@LINK@@"] = link;
			templateParameters["@@ID@@"] = export.ID;
			templateParameters["@@TIPO@@"] = DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", DomainModel.SystemSettings.Instance.DefaultLanguage, export.Type.ToString());
			templateParameters["@@FECHA@@"] = export.DateSolicited.ToString("F");
			templateParameters["@@FECHA_GENERADO@@"] = DateTime.Now.ToString("F");

			var subjectParameters = new Dictionary<string, object>();
			subjectParameters["@@TIPO@@"] = DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", DomainModel.SystemSettings.Instance.DefaultLanguage, export.Type.ToString());

			var emailSettings = new DomainModel.Settings.EmailSettings("Export");
			emailSettings.Subject = DomainModel.SystemSettings.Instance.EmailExport.Subject;
			emailSettings.Template = DomainModel.SystemSettings.Instance.EmailExport.Template;
			emailSettings.Emails = email;

			DomainModel.SystemSettings.Instance.SendMailMessage(emailSettings
				, subjectParameters
				, templateParameters
				, null
				, false);
		}

		private void SendAbortedMail(DomainModel.Reports.Export.ReportExport export, string email)
		{
			var templateParameters = new Dictionary<string, object>();
			templateParameters["@@TIPO@@"] = DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", DomainModel.SystemSettings.Instance.DefaultLanguage, export.Type.ToString());
			templateParameters["@@FECHA@@"] = export.DateSolicited.ToString("F");

			var subjectParameters = new Dictionary<string, object>();
			subjectParameters["@@TIPO@@"] = DomainModel.Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", DomainModel.SystemSettings.Instance.DefaultLanguage, export.Type.ToString());

			var emailSettings = new DomainModel.Settings.EmailSettings("Export");
			emailSettings.Subject = DomainModel.SystemSettings.Instance.EmailExportAborted.Subject;
			emailSettings.Template = DomainModel.SystemSettings.Instance.EmailExportAborted.Template;
			emailSettings.Emails = email;

			DomainModel.SystemSettings.Instance.SendMailMessage(emailSettings
				, subjectParameters
				, templateParameters
				, null
				, false);
		}

        #endregion

        #region Public Methods

        /// <summary>
        /// Realiza el procesamiento cada vez que se cumple el intervalo del timer
        /// </summary>
        /// <returns>true si se pudo procesar; en caso contrario, false</returns>
        public bool Process()
		{
			ReloadCache();

			Interval interval = SystemSettings.Instance.GetCurrentIntervalInDefaultTimeZone();
			Tracer.TraceVerb("El intervalo actual es: {0}", interval);

			DateTime now = DateTime.Now.Date;

			if (now.Date > this.lastReportsPurged.Date)
			{
				DirectoryInfo directory = new DirectoryInfo(this.socialPathForGeneratedReports);
				List<FileInfo> files = new List<FileInfo>();
				files.AddRange(directory.GetFiles("*.xlsx", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.csv", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.txt", SearchOption.TopDirectoryOnly));
				foreach (var file in files)
				{
					TimeSpan diff = now.Subtract(file.CreationTime.Date);
					if (diff.TotalDays > DomainModel.SystemSettings.Instance.DailyReportsToMantain)
					{
						try
						{
							file.Delete();
							Tracer.TraceVerb("Se eliminó el reporte {0} de la fecha {1:dd/MM/yyyy}", file.Name, file.CreationTime);
						}
						catch { }

						var jsonfileDescriptor = Path.Combine(this.socialPathForGeneratedReports, $"{Path.GetFileNameWithoutExtension(file.Name)}.json");
						if (File.Exists(jsonfileDescriptor))
						{
							try
							{
								var jContents = Newtonsoft.Json.Linq.JObject.Parse(File.ReadAllText(jsonfileDescriptor));
								if (jContents["storagePath"] != null &&
									jContents["storagePath"].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									var storagePath = jContents["storagePath"].ToString();
									DeleteReportInStorage(storagePath);

									File.Delete(jsonfileDescriptor);
								}
							}
							catch { }
						}
					}
				}

				directory = new DirectoryInfo(this.socialPathForGeneratedDailyReports);
				files = new List<FileInfo>();
				files.AddRange(directory.GetFiles("*.xlsx", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.csv", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.txt", SearchOption.TopDirectoryOnly));
				foreach (var file in files)
				{
					try
					{
						var filename = Path.GetFileNameWithoutExtension(file.FullName);
						var dateText = filename.Substring(filename.Length - 10);
						var dailyDate = DateTime.Parse(dateText);
						TimeSpan diff = now.Subtract(dailyDate.Date);
						if (diff.TotalDays > DomainModel.SystemSettings.Instance.DailyReportsToMantain)
						{
							file.Delete();
							Tracer.TraceVerb("Se eliminó el reporte diario {0} de la fecha {1:dd/MM/yyyy}", file.Name, dailyDate);

							var jsonfileDescriptor = Path.Combine(this.socialPathForGeneratedDailyReports, $"{Path.GetFileNameWithoutExtension(file.Name)}.json");
							if (File.Exists(jsonfileDescriptor))
							{
								try
								{
									var jContents = Newtonsoft.Json.Linq.JObject.Parse(File.ReadAllText(jsonfileDescriptor));
									if (jContents["storagePath"] != null &&
										jContents["storagePath"].Type == Newtonsoft.Json.Linq.JTokenType.String)
									{
										var storagePath = jContents["storagePath"].ToString();
										DeleteReportInStorage(storagePath);

										File.Delete(jsonfileDescriptor);
									}
								}
								catch { }
							}
						}
					}
					catch { }
				}

				directory = new DirectoryInfo(this.socialPathForGeneratedScheduledReports);
				files = new List<FileInfo>();
				files.AddRange(directory.GetFiles("*.xlsx", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.csv", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.txt", SearchOption.TopDirectoryOnly));
				foreach (var file in files)
				{
					TimeSpan diff = now.Subtract(file.CreationTime.Date);
					if (diff.TotalDays > DomainModel.SystemSettings.Instance.ScheduledReportsToMantain)
					{
						try
						{
							file.Delete();
							Tracer.TraceVerb("[EXPORTER] Se eliminó el reporte {0} de la fecha {1:dd/MM/yyyy}", file.Name, file.CreationTime);
						}
						catch { }
					}
				}

				directory = new DirectoryInfo(this.socialPathForExternalIntegrationsReports);
				files = new List<FileInfo>();
				files.AddRange(directory.GetFiles("*.xlsx", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.csv", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.txt", SearchOption.TopDirectoryOnly));
				files.AddRange(directory.GetFiles("*.zip", SearchOption.TopDirectoryOnly));
				foreach (var file in files)
				{
					TimeSpan diff = now.Subtract(file.CreationTime.Date);
					if (diff.TotalDays > DomainModel.SystemSettings.Instance.DailyReportsToMantain)
					{
						try
						{
							file.Delete();
							Tracer.TraceVerb("[EXPORTER] Se eliminó el reporte externo {0} de la fecha {1:dd/MM/yyyy}", file.Name, file.CreationTime);
						}
						catch { }
					}
				}

				try
				{
					DAL.Reports.Export.ReportExportDAO.DeleteGenerated(DomainModel.SystemSettings.Instance.DailyReportsToMantain);
					Tracer.TraceVerb("[EXPORTER] Se eliminaron los reportes anteriores a {0} días de la base de datos", DomainModel.SystemSettings.Instance.DailyReportsToMantain);
				}
				catch { }

				try
				{
					DAL.Reports.Export.ReportExportDAO.DeleteGeneratedScheduled(DomainModel.SystemSettings.Instance.ScheduledReportsToMantain);
					Tracer.TraceVerb("[EXPORTER] Se eliminaron los reportes programados anteriores a {0} días de la base de datos", DomainModel.SystemSettings.Instance.ScheduledReportsToMantain);
				}
				catch { }

				this.lastReportsPurged = now;
			}

			try
			{
				ProcessNonGeneratedReports();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló mientras se generaban los reportes pendientes {0}", ex);
			}

			if (DomainModel.SystemSettings.Instance.GenerateDailyReport && DomainModel.SystemSettings.Instance.DailyReportsToGenerate != null && DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Length > 0)
			{
				try
				{
					ProcessDailyReports(interval);
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló mientras se generaba el reporte diario de llamadas {0}", ex);
				}
			}
			if (DAL.Reports.Scheduled.ReportScheduledDAO.GetTotalScheduledEnabled() > 0)
			{
				try
				{
					ProcessScheduledReports(interval);
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló mientras se generaba el reporte programado de llamadas {0}", ex);
				}
			}
			Tracer.TraceVerb("Finalizó el procesamiento del intervalo: {0}", interval);

			return true;
		}

		#endregion

		#region Export Methods

		private static void GenerateMessagesReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.MessagesExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
					services.Any(s =>
					s != null &&
					s.Enabled &&
					s.Settings != null &&
					s.Settings.ActAsChat))
				{
					export.IncludeAttendedTimeColumn = true;
				}

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.MessageDAO.Search(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte PROGRAMADO detallado de mensajes del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateMessagesReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.MessagesExport) DomainModel.Reports.Export.MessagesExport.Create(DomainModel.Reports.ReportTypes.Messages, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.DateType = 1;
				export.From = date;
				export.To = date.AddDays(1);
				export.IncludeID = true;
				export.IncludeCase = true;
				export.IncludeBody = true;
				export.IncludeTimes = true;
				export.IncludeDiscardReason = true;
				export.IncludeOutOfServiceLevel = true;
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.IncludeConversationID = true;
				export.IncludeSocialUserId = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
					services.Any(s =>
					s != null &&
					s.Enabled &&
					s.Settings != null &&
					s.Settings.ActAsChat))
				{
					export.IncludeAttendedTimeColumn = true;
				}

				using (var records = DAL.MessageDAO.Search(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de mensajes del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateMessagesSegmentReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.MessagesSegmentsExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.MessageSegmentDAO.Search(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte PROGRAMADO detallado de segmentos de tiempo de mensajes del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateMessagesSegmentReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.MessagesSegmentsExport) DomainModel.Reports.Export.MessagesSegmentsExport.Create(DomainModel.Reports.ReportTypes.MessagesSegments, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.MessageSegmentDAO.Search(export))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de segmentos de tiempo de mensajes del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateChatsReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.ChatsExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.ChatDAO.Search(export
					, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte programado de CHATS del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}
		
		private static void GenerateChatsReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.ChatsExport) DomainModel.Reports.Export.MessagesExport.Create(DomainModel.Reports.ReportTypes.Chats, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.IncludeID = true;
				export.IncludeTimes = true;
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.IncludeTimes = true;
				export.IncludeID = true;
				export.IncludeTags = true;
				export.IncludeConversationID = true;
				export.IncludeBrowser = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.ChatDAO.Search(export
					, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de CHATS del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateChatsMessagesReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.ChatsMessagesExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.ChatMessageDAO.Search(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de CHATS Messages del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateChatsMessagesReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.ChatsMessagesExport) DomainModel.Reports.Export.ChatsMessagesExport.Create(DomainModel.Reports.ReportTypes.ChatsMessages, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.IncludeID = true;
				export.IncludeTimes = true;
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.IncludeTimes = true;
				export.IncludeID = true;
				export.IncludeTags = true;
				export.IncludeConversationID = true;
				export.IncludeBrowser = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.ChatMessageDAO.Search(export))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de mensajes de chat del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateAdherenceDetailedReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.AdherenceDetailedExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyDAO.SearchByAgentsAdherence(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}

				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateAdherenceConsolidatedReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.AdherenceConsolidatedExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				var reader = DAL.Historical.DailyDAO.SearchByAgentsAdherence(export);
				var consolidatedRecords = DomainModel.Site.ConsolidateAdherence(export.Sites, reader, new Common.Interval(export.From, 2), new Common.Interval(export.To, 2));
				export.Generate<DomainModel.Historical.DailyAdherence>(consolidatedRecords, filename);


				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateCasesReopeningsReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.CasesReopeningsExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.CaseReopeningDAO.Search(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}

				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateUsersLogReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.UsersLogExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

                var userLogs = DAL.UserLogDAO.Search(export, null, null, out bool? moreLogInfoAvailable, out int? totalRecords);

                var records = UserLog.GenerateNewUserLogIndividualParameterList(userLogs);

                export.Generate(records, filename);

				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

        private static void GenerateUsersLogReport(DateTime date, string filename)
        {
			try
			{
                var export = (DomainModel.Reports.Export.UsersLogExport)DomainModel.Reports.Export.UsersLogExport.Create(DomainModel.Reports.ReportTypes.UsersLog, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

                export.From = date;
                export.To = date.AddDays(1);
                export.TotalResults = 0;
                export.ExportComplete = true;
                export.Email = null;
                export.WriteToLog = true;
                export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
                export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				var userLogs = DAL.UserLogDAO.Search(export, null, null, out bool? moreLogInfoAvailable, out int? totalRecords);

				var records = UserLog.GenerateNewUserLogIndividualParameterList(userLogs);

                export.Generate(records, filename);
            }
            catch (Exception ex)
            {
                Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
            }
        }

        private static void GenerateDailyByCaseReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DailyByCaseExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}


				using (var records = DAL.Historical.DailyCaseDAO.Search(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte consolidado de casos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateUserSessionsReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.UserSessionsExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.UserSessionDAO.Search(export))
				{
					export.Generate<DomainModel.Historical.UserSession>(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte consolidado de casos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

        private static void GenerateUserSessionsReport(DateTime date, string filename)
        {
            try
            {
                var export = (DomainModel.Reports.Export.UserSessionsExport)DomainModel.Reports.Export.UserSessionsExport.Create(DomainModel.Reports.ReportTypes.UserSessions, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

                export.From = date;
                export.To = date.AddDays(1);
                export.ExportComplete = true;
                export.Email = null;
                export.WriteToLog = true;
                export.Users = null;
                export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
                export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.Historical.UserSessionDAO.Search(export))
                {
                    export.Generate(records, filename);
                }
            }
            catch (Exception ex)
            {
                Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte consolidado de casos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
            }
        }

        private static void GenerateSocialUserProfilesReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.SocialUserProfilesExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.SocialUserProfileDAO.Search(export
					, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[EXPORTER] Ocurrió un error cuando se intentaba generar el reporte detallado de perfiles del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedWhatsappHSMReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedWhatsappHSMExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.MessageDAO.SearchByPayment(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de HSM del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedWhatsappHSMReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.DetailedWhatsappHSMExport) DomainModel.Reports.Export.MessagesExport.Create(DomainModel.Reports.ReportTypes.DetailedWhatsappHSM, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.IncludeID = true;
				export.IncludeCase = true;
				export.IncludeMoreInfo = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.MessageDAO.SearchByPayment(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de HSM del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateMessagesTransfersReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.MessagesTransfersExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.MessageTransferDAO.Search(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de transferencias del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateMessagesTransfersReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.MessagesTransfersExport) DomainModel.Reports.Export.MessagesTransfersExport.Create(DomainModel.Reports.ReportTypes.MessagesTransfers, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.MessageTransferDAO.Search(export
					, null
					, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de transferencias del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}
		private static void SaveDailyReportInFtp(string filename)
		{
			var zip = true;
			if (DomainModel.SystemSettings.Instance.DailyReportsFormat == DomainModel.Reports.Export.ExportFormats.Excel)
				zip = DomainModel.SystemSettings.Instance.DailyReportsZipExcel;
			else
				zip = DomainModel.SystemSettings.Instance.DailyReportsZipCSV;
			
			SaveDailyReportInFtp(filename, zip);
		}
		
		private static void SaveDailyReportInFtp(string filename, bool zip)
		{
			if (SystemSettings.Instance.EnableFtpDailyReports)
			{
				Guid ftpGuid;
				if (Guid.TryParse(SystemSettings.Instance.FtpIdDailyReports, out ftpGuid))
				{
					var connectionSettings = SystemSettings.Instance.GetOneFtp(ftpGuid);

					if (connectionSettings != null)
						connectionSettings.SaveFile(filename, SystemSettings.Instance.FtpDirectoryDailyReports, zip);
				}
			}
		}

		private static void GenerateCasesReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.CasesExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (DomainModel.Case.CaseDataReader records = DAL.CaseDAO.Search(export, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de casos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateCasesReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.CasesExport) DomainModel.Reports.Export.CasesExport.Create(DomainModel.Reports.ReportTypes.Cases, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.DateType = 1;
				export.From = date;
				export.To = date.AddDays(1);
				export.IncludeTotals = true;
				export.IncludeYFlowColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled;
				export.IncludeTimes = true;
				export.IncludeTags = true;
				export.IncludeCaseID = true;
				export.IncludeServicesAndSocialServiceTypes = true;
				export.IncludeAgents = true;
				export.IncludeDates = true;
				export.IncludeSurveys = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys;
				export.IncludeObservations = true;
				export.IncludeImportantTag = true;
				export.IncludeCaseExtended = Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendCase &&
					DomainModel.SystemSettings.Instance.ExtendedCasesFields != null &&
					DomainModel.SystemSettings.Instance.ExtendedCasesFields.Length > 0;
				export.IncludeProfileExtended = Licensing.LicenseManager.Instance.License.Configuration.AllowToExtendProfile &&
					DomainModel.SystemSettings.Instance.ExtendedProfilesFields != null &&
					DomainModel.SystemSettings.Instance.ExtendedProfilesFields.Length > 0;
				export.UseSurveyStatus = false;
				export.SurveyStatus = null;
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				using (DomainModel.Case.CaseDataReader records = DAL.CaseDAO.Search(export, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de casos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private void GenerateSocialUsersReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.SocialUsersExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.SocialUserDAO.Search(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de usuarios del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private void GenerateSocialUsersReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.SocialUsersExport) DomainModel.Reports.Export.SocialUsersExport.Create(DomainModel.Reports.ReportTypes.SocialUsers, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.From = date;
				export.To = date.AddDays(1);
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.Email = null;
				export.WriteToLog = true;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.SocialUserDAO.Search(export))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de usuarios del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private void GenerateSurveysReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.SurveysExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						if (export.UseCaseStartDate)
						{
							export.CaseStartFrom = date;
							export.CaseStartTo = date.AddDays(1).AddMinutes(-30);
						}
						if (export.UseCaseFinishDate)
						{
							export.CaseFinishFrom = date;
							export.CaseFinishTo = date.AddDays(1).AddMinutes(-30);
						}
						if (export.UseSurveyDate)
						{
							export.SurveyDateFrom = date;
							export.SurveyDateTo = date.AddDays(1).AddMinutes(-30);
						}

						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						if (export.UseCaseStartDate)
						{
							export.CaseStartFrom = firstDayOfWeek.AddDays(-7);
							export.CaseStartTo = firstDayOfWeek.AddMinutes(-30);
						}
						if (export.UseCaseFinishDate)
						{
							export.CaseFinishFrom = firstDayOfWeek.AddDays(-7);
							export.CaseFinishTo = firstDayOfWeek.AddMinutes(-30);
						}
						if (export.UseSurveyDate)
						{
							export.SurveyDateFrom = firstDayOfWeek.AddDays(-7);
							export.SurveyDateTo = firstDayOfWeek.AddMinutes(-30);
						}

						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						if (export.UseCaseStartDate)
						{
							days = DateTime.DaysInMonth(date.Year, date.Month);
							export.CaseStartFrom = firstDayOfMonth.AddDays(-days);
							export.CaseStartTo = date.AddMinutes(-30);
						}
						if (export.UseCaseFinishDate)
						{
							days = DateTime.DaysInMonth(date.Year, date.Month);
							export.CaseFinishFrom = firstDayOfMonth.AddDays(-days);
							export.CaseFinishTo = date.AddMinutes(-30);
						}
						if (export.UseSurveyDate)
						{
							days = DateTime.DaysInMonth(date.Year, date.Month);
							export.SurveyDateFrom = firstDayOfMonth.AddDays(-days);
							export.SurveyDateTo = date.AddMinutes(-30);
						}
						break;
				}

				if (export.Surveys != null && export.Surveys.Length == 1)
				{
					var survey = DomainModel.Cache.Instance.GetItem<Survey>(export.Surveys[0].ToString());
					export.SurveyConfiguration = survey.Configuration;
				}

				using (var records = DAL.SurveyDAO.Search(export, null))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de encuestas del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private void GenerateSurveysReport(DateTime date, DomainModel.Survey survey, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.SurveysExport) DomainModel.Reports.Export.SurveysExport.Create(DomainModel.Reports.ReportTypes.Surveys, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);

				export.UseCaseStartDate = true;
				export.CaseStartFrom = date.AddDays(-2);
				export.CaseStartTo = date.AddDays(1);
				export.UseSurveyDate = true;
				export.SurveyDateFrom = date;
				export.SurveyDateTo = date.AddDays(1);
				export.TotalResults = 0;
				export.ExportComplete = true;
				export.IncludeTags = true;
				export.IncludeImportantTag = true;
				export.IncludeServicesAndSocialServiceTypes = true;
				export.IncludeAgents = true;
				export.IncludeSummary = false;
				export.Email = null;
				export.WriteToLog = true;
				export.Surveys = new Guid[] { survey.ID };
				export.SurveyConfiguration = survey.Configuration;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.SurveyDAO.Search(export, null))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado de encuestas del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		#region Daily Queues Reports

		private static void GenerateDetailedByQueueReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();

			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByQueueExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.AuxReasons = auxReasons.ToArray();
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				if (export.ByIntervals)
				{
					using (var records = DAL.Historical.DailyDAO.SearchByQueues(export))
					{
						export.Generate<DomainModel.Historical.Daily>(records, filename);
					}
				}
				else
				{
					using (var records = DAL.Historical.DailyByDayDAO.SearchByQueues(export))
					{
						export.Generate<DomainModel.Historical.Daily>(records, filename);
					}
				}
				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por cola del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByQueueReport(DateTime date, string filename)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();

			try
			{
				var export = (DomainModel.Reports.Export.DetailedByQueueExport) DomainModel.Reports.Export.DetailedByQueueExport.Create(DomainModel.Reports.ReportTypes.DetailedByQueue, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.IncludeSurveysColumns = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys;
				export.ByIntervals = false;
				export.AuxReasons = auxReasons.ToArray();
				export.Queues = null;
				export.From = date;
				export.To = date.AddDays(1);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				using (var records = DAL.Historical.DailyByDayDAO.SearchByQueues(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por cola del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByQueuebyIntervalReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();

			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByQueueExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.AuxReasons = auxReasons.ToArray();
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyByDayDAO.SearchByQueues(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por cola del día {0}: {1}", date.ToString("dd/MM/yyyy HH:mm"), ex);
			}
		}

		private static void GenerateDetailedByQueuebyIntervalReport(DateTime date, string filename)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();

			try
			{
				var export = (DomainModel.Reports.Export.DetailedByQueueExport) DomainModel.Reports.Export.DetailedByQueueExport.Create(DomainModel.Reports.ReportTypes.DetailedByQueue, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.IncludeSurveysColumns = Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys;
				export.ByIntervals = true;
				export.AuxReasons = auxReasons.ToArray();
				export.Queues = null;
				export.From = date;
				export.To = date.AddDays(1);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				using (var records = DAL.Historical.DailyByDayDAO.SearchByQueues(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por cola del día {0}: {1}", date.ToString("dd/MM/yyyy HH:mm"), ex);
			}
		}

		private static void GenerateTagsByQueueReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.TagsByQueueExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyTagByDayDAO.SearchByQueues(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de actividad de etiquetas por cola del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateTagsByQueueReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.TagsByQueueExport) DomainModel.Reports.Export.TagsByQueueExport.Create(DomainModel.Reports.ReportTypes.TagsByQueue, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.From = date;
				export.To = date.AddDays(1);
				export.Queues = null;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.Historical.DailyTagByDayDAO.SearchByQueues(export))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de actividad de etiquetas por cola del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		#endregion

		#region Daily Services Reports

		private static void GenerateDetailedByServiceReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByServiceExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}
				if (export.ByIntervals)
				{
					using (var records = DAL.Historical.DailyServiceDAO.Search(export))
					{
						export.Generate<DomainModel.Historical.DailyService>(records, filename);
					}
				}
				else
				{
					using (var records = DAL.Historical.DailyServiceByDayDAO.Search(export))
					{
						export.Generate<DomainModel.Historical.DailyService>(records, filename);
					}
				}
				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por servicio del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByServiceReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.DetailedByServiceExport) DomainModel.Reports.Export.DetailedByServiceExport.Create(DomainModel.Reports.ReportTypes.DetailedByService, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.IncludePublicAndPrivateColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Facebook || s == DomainModel.SocialServiceTypes.Twitter);
				export.IncludeYFlowColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled;
				export.ByIntervals = false;
				export.Services = null;
				export.From = date;
				export.To = date.AddDays(1);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.Historical.DailyServiceByDayDAO.Search(export))
				{
					export.Generate<DomainModel.Historical.DailyService>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por servicio del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByServicebyIntervalReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByServiceExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyServiceByDayDAO.Search(export))
				{
					export.Generate<DomainModel.Historical.DailyService>(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por servicio del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByServicebyIntervalReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.DetailedByServiceExport) DomainModel.Reports.Export.DetailedByServiceExport.Create(DomainModel.Reports.ReportTypes.DetailedByService, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.IncludePublicAndPrivateColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Facebook || s == DomainModel.SocialServiceTypes.Twitter);
				export.IncludeYFlowColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled;
				export.ByIntervals = true;
				export.Services = null;
				export.From = date;
				export.To = date.AddDays(1);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.Historical.DailyServiceByDayDAO.Search(export))
				{
					export.Generate<DomainModel.Historical.DailyService>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por servicio del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		#endregion

		#region Daily Agents Reports

		private static void GenerateDetailedByAgentReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();
			var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByAgentExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.AuxReasons = auxReasons.ToArray();
				export.Periodicity = reportScheduled.Periodicity;
				export.ShowAttendedMessages = Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
					services.Any(s => s != null && s.Enabled && s.Settings != null && s.Settings.ActAsChat);

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				if (export.ByIntervals)
				{
					using (var records = DAL.Historical.DailyDAO.SearchByAgents(export))
					{
						export.Generate<DomainModel.Historical.Daily>(records, filename);
					}
				}
				else
				{
					using (var records = DAL.Historical.DailyByDayDAO.SearchByAgents(export))
					{
						export.Generate<DomainModel.Historical.Daily>(records, filename);
					}
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByAgentReport(DateTime date, string filename)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();
			var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			try
			{
				var export = (DomainModel.Reports.Export.DetailedByAgentExport) DomainModel.Reports.Export.DetailedByAgentExport.Create(DomainModel.Reports.ReportTypes.DetailedByAgent, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.ByIntervals = false;
				export.AuxReasons = auxReasons.ToArray();
				export.Queues = null;
				export.Agents = null;
				export.AgentGroup = null;
				export.ShowOnlyAllQueues = false;
				export.From = date;
				export.IncludeSite = true;
				export.ShowAttendedMessages = Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
					services.Any(s => s != null && s.Enabled && s.Settings != null && s.Settings.ActAsChat);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				export.To = date.AddDays(1);

				using (var records = DAL.Historical.DailyByDayDAO.SearchByAgents(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateDetailedByAgentbyIntervalReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();

			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.DetailedByAgentExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.AuxReasons = auxReasons.ToArray();
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyDAO.SearchByAgents(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por agente del día e intervalo {0}: {1}", date.ToString("dd/MM/yyyy HH:mm"), ex);
			}

		}
		private static void GenerateDetailedByAgentbyIntervalReport(DateTime date, string filename)
		{
			var auxReasons = DAL.AuxReasonDAO.GetAll();
			var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			try
			{
				var export = (DomainModel.Reports.Export.DetailedByAgentExport) DomainModel.Reports.Export.DetailedByAgentExport.Create(DomainModel.Reports.ReportTypes.DetailedByAgent, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.IncludeChatColumns = Licensing.LicenseManager.Instance.License.Configuration.AllowedSocialServiceTypes.Any(s => s == DomainModel.SocialServiceTypes.Chat);
				export.ByIntervals = true;
				export.AuxReasons = auxReasons.ToArray();
				export.Queues = null;
				export.Agents = null;
				export.AgentGroup = null;
				export.ShowOnlyAllQueues = false;
				export.From = date;
				export.IncludeSite = true;
				export.ShowAttendedMessages = Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
					services.Any(s => s != null && s.Enabled && s.Settings != null && s.Settings.ActAsChat);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				export.To = date.AddDays(1);

				using (var records = DAL.Historical.DailyDAO.SearchByAgents(export))
				{
					export.Generate<DomainModel.Historical.Daily>(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte detallado por agente del día e intervalo {0}: {1}", date.ToString("dd/MM/yyyy HH:mm"), ex);
			}
		}

		private static void GenerateAgentsTagsReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.TagsByAgentExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.DailyTagByDayDAO.SearchByAgents(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de actividad de etiquetas por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateAgentsTagsReport(DateTime date, string filename)
		{
			try
			{
				var export = (DomainModel.Reports.Export.TagsByAgentExport) DomainModel.Reports.Export.TagsByAgentExport.Create(DomainModel.Reports.ReportTypes.TagsByAgent, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.From = date;
				export.To = date.AddDays(1);
				export.Queues = null;
				export.Agents = null;
				export.AgentGroup = null;
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;

				using (var records = DAL.Historical.DailyTagByDayDAO.SearchByAgents(export))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de actividad de etiquetas por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateLoginByAgentReportScheduled(DateTime date, string filename, DomainModel.Reports.Scheduled.ReportScheduled reportScheduled)
		{
			try
			{
				var export = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Reports.Export.SessionsByAgentExport>(reportScheduled.Configuration);
				export.IdScheduled = reportScheduled.ID;
				export.Owner = reportScheduled.Owner;
				export.Owner.TimeZoneId = reportScheduled.Owner.TimeZoneId;
				export.OwnerTimeZone = reportScheduled.Owner.TimeZoneId;
				export.DateInterval = date;
				export.Format = reportScheduled.Format;
				export.Periodicity = reportScheduled.Periodicity;

				switch (reportScheduled.Periodicity)
				{
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Daily:
						export.From = date;
						export.To = date.AddDays(1).AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Weekly:
						var firstDayOfWeek = date.AddDays(((int) (date.DayOfWeek) * -1) + 1);
						export.From = firstDayOfWeek.AddDays(-7);
						export.To = firstDayOfWeek.AddMinutes(-30);
						break;
					case DomainModel.Reports.Scheduled.ReportScheduled.ExportPeriodicity.Monthly:
						var firstDayOfMonth = new DateTime(date.Year, date.AddDays(-1).Month, 1);
						int days = DateTime.DaysInMonth(date.Year, date.Month);
						export.From = firstDayOfMonth;
						export.To = date.AddMinutes(-30);
						break;
				}

				using (var records = DAL.Historical.SessionDAO.Search(export))
				{
					export.Generate(records, filename);
				}

				export.Status = ReportExportStatus.Finished;
				export.DateStarted = export.DateSolicited;
				export.DateGenerated = DateTime.Now;
				export.Generated = true;
				export.Result = ReportExportResults.Success;
				DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
				dao.Insert();
				DAL.Reports.Export.ReportExportDAO.UpdateStatus(export);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de Login Logout por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private static void GenerateLoginByAgentReport(DateTime date, string filename)
		{
			try
			{
				DomainModel.Reports.Export.SessionsByAgentExport export = (DomainModel.Reports.Export.SessionsByAgentExport) DomainModel.Reports.Export.SessionsByAgentExport.Create(DomainModel.Reports.ReportTypes.SessionsByAgent, 1, DomainModel.SystemSettings.Instance.DailyReportsFormat);
				export.Owner.TimeZoneId = SystemSettings.Instance.DefaultTimeZone.Id;
				export.OwnerTimeZone = SystemSettings.Instance.DefaultTimeZone.Id;
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				using (var records = DAL.Historical.SessionDAO.ExportByAgent(date))
				{
					export.Generate(records, filename);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el reporte de Login Logout por agente del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
			}
		}

		private List<string> GenerateDailyExternalReportFileNames(DateTime date)
		{
			List<string> paths = new List<string>();
			try
			{
				string pathForExternal = this.socialPathForExternalIntegrationsReports;
				if (!Directory.Exists(pathForExternal))
				{
					Tracer.TraceInfo("El directorio: {0} para reportes externos NO existe.", pathForExternal);
					return paths;
				}

				var files = Directory.GetFiles(pathForExternal);
				if (files.Length > 0)
				{
					foreach (var file in files)
						paths.Add(Path.Combine(pathForExternal, file));
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba generar el path reportes externos del día {0}: {1}", date.ToString("dd/MM/yyyy"), ex);
				
			}
			return paths;
		}

		private void DeleteFile(string filePath)
		{
			try
			{
				if (File.Exists(filePath))
				{
					File.Delete(filePath);
					Tracer.TraceInfo("Se eliminó del FileSystem el archivo: {0}", filePath);
				}
			}
			
			catch
			{
				Tracer.TraceError("Ocurrió un error cuando se intentaba eliminar el archivo: {0}", filePath);
			}
		}

		#endregion

		#endregion
	}
}