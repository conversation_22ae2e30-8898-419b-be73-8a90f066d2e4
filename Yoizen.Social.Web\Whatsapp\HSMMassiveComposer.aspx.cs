﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Common;
using Yoizen.Social.Core.Enums;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.Whatsapp;

namespace Yoizen.Social.Web.Whatsapp
{
	public partial class HSMMassiveComposer : LoginRequiredBasePage
	{
		#region Propiedades

		protected override string RedirectUrl { get { return "~/Whatsapp/HSMMassiveComposer.aspx"; } }

		protected override string PageDescription { get { return "Envío masivo de mensajes HSM de Whatsapp"; } }

		protected override string PageDescriptionLocalizationKey { get { return "whatsapp-hsmmassivecomposer-title"; } }

		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			if (!IsPostBack)
			{
				IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();

				services = services.Where(s => s != null && 
					s.Enabled && 
					s.Type == ServiceTypes.WhatsApp && 
					s.Settings != null && 
					!((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).IntegrationType.Equals("1") &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).AllowToSendHSM &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).HSMTemplates != null &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).HSMTemplates.Where(t => t.AvaiableForSupervisors).Any());
				this.RegisterJsonVariable("services", BuildServices(services));

				this.RegisterJsonVariable("loggedUserIsSuper", this.LoggedUserIsSuper);

				var emails = DomainModel.SystemSettings.Instance.GetEmailsConnections().OrderBy(item => item.AsDictionary()["Name"]);
				this.RegisterJsonVariable("emails", emails);

				this.RegisterEnums(new Type[] {
					typeof(DomainModel.Whatsapp.HSMTemplateButtonsTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateFooterTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes)
				});

				checkboxDoNotCall.Checked = true;
			}
		}

		#region Private Methods

		private bool ValidateUser()
		{
			if (!this.LoggedUser.HasPermission(DomainModel.Permissions.WhatsappSection))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound)
			{
				messageError.Visible = true;
				messageError.Text = "No hay licencia para acceder a esta sección";
				messageError.LocalizationKey = "globals-no_license";
				panelContent.Visible = false;
				return false;
			}

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutboundService)
			{
				messageError.Visible = true;
				messageError.Text = "No hay licencia para acceder a esta sección";
				messageError.LocalizationKey = "globals-no_license";
				panelContent.Visible = false;
				return false;
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutboundWithoutCaseCreation &&
				Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutboundWithoutCaseWithContext &&
				!this.LoggedUserIsSuper)
			{
				messageError.Visible = true;
				messageError.Text = "Sección deprecada";
				messageError.LocalizationKey = "whatsapp-hsmmassivecomposer-another-processor-to-load-tasks";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private object BuildServices(IEnumerable<Service> services)
		{
			if (services == null || !services.Any())
				return null;

			return services.Select(s => BuildService(s));
		}

		private object BuildService(Service service)
		{
			if (service == null)
				return null;

			Core.System.Instance.Logic.EnsureServiceInstance(service);

			var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			var configuration = service.ServiceConfiguration as SocialServices.WhatsApp.WhatsAppServiceConfiguration;

			return new
			{
				ID = service.ID,
				Name = service.Name,
				PhoneNumber = configuration.FullPhoneNumber,
				IntegrationType = settings.IntegrationType,
				HSMTemplates = settings.HSMTemplates?.Where(t => t.AvaiableForSupervisors).OrderBy(t => t.Description)
			};
		}

		#endregion

		#region Web Methods

		[System.Web.Services.WebMethod]
		public static object Schedule(string taskName
			, int serviceId
			, string templateNamespace
			, string templateElementName
			, string language
			, bool sendHSMIfCaseOpenAnyways
			, string file
			, string fileContentType
			, string separator
			, DateTimeOffset? scheduleFor
			, DomainModel.Settings.EmailSettings email
			, int[] tags
			, bool updateBussinesData
			, bool updateExtendedCaseData
			, bool cancelProcess
			, DateTimeOffset? cancelDateTime
			, int? cancelAfterMinutes
			, bool pauseProcess
			, short[] pauseIntervals
			, bool validateDoNotCallList
			, int flowParametersCount
			, bool sendDuplicateRecordsAnyways
			, int testDelayMinutes)
		{
			try
			{
				DomainModel.User supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutboundService)
					throw new Exception("La licencia no permite el envío masivo de mensajes de Whatsapp");

				var service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(serviceId);
				if (service == null || service.Type != ServiceTypes.WhatsApp)
					throw new ArgumentException("El servicio proporcionado no es un servicio de Whatsapp", nameof(serviceId));

				var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				if (settings == null || settings.IntegrationType == 1 || !settings.AllowToSendHSM)
					throw new ArgumentException("El servicio proporcionado no es un servicio de Whatsapp válido para envío masivo de HSM", nameof(serviceId));

				var template = settings.HSMTemplates.FirstOrDefault(t => 
					t.Namespace.Equals(templateNamespace) && 
					t.ElementName.Equals(templateElementName) &&
					t.Language.Equals(language));
				if (template == null)
					throw new ArgumentException("El namespace y element_name no forman parte del servicio proporcionado", nameof(templateNamespace));

				if (string.IsNullOrEmpty(file) || string.IsNullOrEmpty(fileContentType))
					throw new ArgumentException("El archivo especificado no existe", nameof(file));

				string csvFile = Path.Combine(HttpContext.Current.Server.MapPath("~/Configuration/Uploads"), file);
				if (!File.Exists(csvFile))
					throw new ArgumentException("El archivo especificado no existe", nameof(file));

				if (!Path.GetExtension(csvFile).Equals(".csv", StringComparison.InvariantCultureIgnoreCase))
					throw new ArgumentException("El archivo especificado no es un CSV", nameof(file));

				if (scheduleFor != null)
				{
					scheduleFor = supervisor.ConvertDateToServerDate(scheduleFor.Value);

					if (scheduleFor.Value < DateTime.Now)
						throw new ArgumentException("La fecha no puede ser anterior a ahora", nameof(scheduleFor));
				}

				if (email == null)
					throw new ArgumentNullException(nameof(email), "Los parámetros de email no pueden ser null");

				if (string.IsNullOrEmpty(separator))
					separator = ",";

				string hash;
				using (var md5 = System.Security.Cryptography.MD5.Create())
				{
					using (var stream = File.OpenRead(csvFile))
					{
						hash = Convert.ToBase64String(md5.ComputeHash(stream));
					}
				}

				if (DAL.TaskDAO.Exists(taskName, hash))
				{
					return new
					{
						Success = false,
						Error = new
						{
							Code = 3,
							Message = "The task already exists"
						}
					};
				}

				var parametersCount = 0;
				if (template.Parameters != null && template.Parameters.Length > 0)
					parametersCount = template.Parameters.Length;

				switch (template.HeaderType)
				{
					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text:
						if (template.HeaderTextParameter != null)
							parametersCount++;
						break;
					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media:
						parametersCount += 3;
						break;
					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location:
						parametersCount += 4;
						break;
					default:
						break;
				}

				switch (template.ButtonsType)
				{
					case DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply:
						parametersCount += template.Buttons.Length;
						break;
					case DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode:
						parametersCount += template.Buttons.Length;
						break;
					case DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction:
						int urlDynamicCount = template.Buttons.Count(b =>
							b.CallToActionButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url &&
							b.UrlButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic);

						int offerCodeCount = template.Buttons.Count(b =>
							b.CallToActionButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode);

						parametersCount += urlDynamicCount + offerCodeCount + flowParametersCount;
						break;

					case DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed:
						int quickReplyParameterCount = template.Buttons.Count(b => b.QuickReplyParameter != null);

						int urlDynamicCountMixed = template.Buttons.Count(b =>
							b.CallToActionButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url &&
							b.UrlButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic);

						int offerCodeCountMixed = template.Buttons.Count(b =>
							b.CallToActionButtonType == DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode);

						parametersCount += quickReplyParameterCount + urlDynamicCountMixed + offerCodeCountMixed + flowParametersCount;
						break;
					default:
						break;
				}

				var readerConfiguration = new CsvHelper.Configuration.Configuration()
				{
					Delimiter = separator,
					HasHeaderRecord = false,
				};
				using (var sr = File.OpenText(csvFile))
				using (var reader = new CsvHelper.CsvReader(sr, readerConfiguration))
				{
					int currentLine = 1;
					var regexPhoneNumber = new System.Text.RegularExpressions.Regex(@"^[0-9]{10,16}$");

					while (reader.Read())
					{
						var parts = reader.Context.Record;
						parts = parts.Where(s => !string.IsNullOrEmpty(s)).ToArray();
						var parametersCountForThisLine = parametersCount;

						if (updateBussinesData)
						{
							parametersCountForThisLine++;
							var action = parts[parametersCountForThisLine];
							if (!int.TryParse(action, out int actionInt))
							{
								return new
								{
									Success = false,
									Error = new
									{
										Code = 2,
										Message = "Invalid file contents",
										ErrorLine = currentLine
									}
								};
							}

							switch (actionInt)
							{
								case (int) OptionsBussinesData.DeleteOne:
								case (int) OptionsBussinesData.Concat:
								case (int) OptionsBussinesData.Replace:
									if (parts.Length <= parametersCountForThisLine + 1)
									{
										return new
										{
											Success = false,
											Error = new
											{
												Code = 2,
												Message = "Invalid file contents",
												ErrorLine = currentLine
											}
										};
									}
									parametersCountForThisLine++;
									if (!Core.System.Instance.Logic.IsBusinessDataValid(parts[parametersCountForThisLine]))
									{
										return new
										{
											Success = false,
											Error = new
											{
												Code = 2,
												Message = "Invalid file contents",
												ErrorLine = currentLine
											}
										};
									}
									break;
								case (int) OptionsBussinesData.NoneAction:
								case (int) OptionsBussinesData.DeleteAll:
									//no hago nada
									break;
								default:
									return new
									{
										Success = false,
										Error = new
										{
											Code = 2,
											Message = "Invalid file contents",
											ErrorLine = currentLine
										}
									};
							}
						}
						if (updateExtendedCaseData)
						{
							parametersCountForThisLine++;
							var action = parts[parametersCountForThisLine];
							if (!int.TryParse(action, out int actionInt))
							{
								return new
								{
									Success = false,
									Error = new
									{
										Code = 2,
										Message = "Invalid file contents",
										ErrorLine = currentLine
									}
								};
							}
							switch (actionInt)
							{
								case (int) OptionsExtendedCaseData.Concat:
								case (int) OptionsExtendedCaseData.Replace:
									if (parts.Length <= parametersCountForThisLine + 1)
									{
										return new
										{
											Success = false,
											Error = new
											{
												Code = 2,
												Message = "Invalid file contents",
												ErrorLine = currentLine
											}
										};
									}
									parametersCountForThisLine++;
									if (!Core.System.Instance.Logic.IsExtendedCaseValid(parts[parametersCountForThisLine]))
									{
										return new
										{
											Success = false,
											Error = new
											{
												Code = 2,
												Message = "Invalid file contents",
												ErrorLine = currentLine
											}
										};
									}
									break;
								case (int) OptionsExtendedCaseData.NoneAction:
									//no hago nada
									break;
								default:
									return new
									{
										Success = false,
										Error = new
										{
											Code = 2,
											Message = "Invalid file contents",
											ErrorLine = currentLine
										}
									};
							}
						}

						if (parts.Length != parametersCountForThisLine + 1)
						{
							return new
							{
								Success = false,
								Error = new
								{
									Code = 2,
									Message = "Invalid file contents",
									ErrorLine = currentLine
								}
							};
						}
						if (parts[0].Length == 0 || !regexPhoneNumber.IsMatch(parts[0]))
						{
							return new
							{
								Success = false,
								Error = new
								{
									Code = 2,
									Message = "Invalid file contents",
									ErrorLine = currentLine
								}
							};
						}

						if (parametersCount > 0)
						{
							for (var i = 0; i < parametersCount; i++)
							{
								if (parts[i + 1].Length == 0)
								{
									return new
									{
										Success = false,
										Error = new
										{
											Code = 2,
											Message = "Invalid file contents",
											ErrorLine = currentLine
										}
									};
								}

								/*if (parts[i + 1].IndexOf("    ") >= 0 ||
									parts[i + 1].IndexOf('\t') >= 0 ||
									parts[i + 1].IndexOf('\n') >= 0)
								{
									return new
									{
										Success = false,
										Error = new
										{
											Code = 2,
											Message = "Invalid file contents. Parameter cannot contain four consecutive spaces, new lines or tab",
											ErrorLine = currentLine,
											ParameterIndex = i
										}
									};
								}*/
							}
						}

						currentLine++;
					}
				}

				var tagsToApply = new List<DomainModel.Tag>();
				foreach (var tagId in tags)
				{
					DomainModel.Tag tag = null;
					try
					{
						tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(tagId);
					}
					catch { }

					if (tag != null)
					{
						tagsToApply.Add(tag);
					}
				}

				if (!template.AllowToConfigureSendHSMIfCaseOpen)
				{
					sendHSMIfCaseOpenAnyways = true;
				}

				string path = Path.Combine(SystemSettings.Instance.AttachmentsRoute, "__Tasks");
				if (!Directory.Exists(path))
					Directory.CreateDirectory(path);

				var destCsvFile = Path.Combine(path, Path.GetFileName(csvFile));
				File.Move(csvFile, destCsvFile);
				csvFile = destCsvFile;

				var task = new DomainModel.Tasks.WhatsappMassiveHSMTask();
				task.Name = taskName;
				task.User = supervisor;
				task.Hash = hash;
				if (scheduleFor == null)
#if DEBUG
					task.DateScheduled = DateTime.Now.AddSeconds(5);
#else
					task.DateScheduled = DateTime.Now.AddMinutes(1);
#endif
				else
					task.DateScheduled = scheduleFor.Value.LocalDateTime;

				var parameters = new DomainModel.Tasks.WhatsappMassiveHSMTask.WhatsappMassiveHSMTaskParameters();
				parameters.Email = email;
				parameters.File = csvFile;
				parameters.FileContentType = fileContentType;
				parameters.Separator = separator;
				parameters.ServiceID = serviceId;
				parameters.TemplateElementName = templateElementName;
				parameters.TemplateNamespace = templateNamespace;
				parameters.TemplateLanguage = language;
				parameters.SendHSMIfCaseOpenAnyways = sendHSMIfCaseOpenAnyways;
				parameters.ValidateDoNotCallList = validateDoNotCallList;
				parameters.Tags = tagsToApply;
				parameters.CancelProcess = cancelProcess;
				parameters.CancelAfterMinutes = cancelAfterMinutes;
				parameters.UpdateBussinesData = updateBussinesData;
				parameters.UpdateExtendedCaseData = updateExtendedCaseData;
				parameters.SendDuplicatedRecordsAnyways = sendDuplicateRecordsAnyways;
				parameters.TestDelayMinutes = testDelayMinutes;
				if (cancelDateTime != null)
					parameters.CancelDateTime = supervisor.ConvertDateToServerDate(cancelDateTime.Value);
				else
					parameters.CancelDateTime = null;
				parameters.PauseProcess = pauseProcess;
				parameters.PauseIntervals = pauseIntervals;
				task.Parameters = parameters;

				DAL.TaskDAO dao = new DAL.TaskDAO(task);
				dao.Insert();
				DomainModel.Cache.Instance.AddItem(task);

				return new
				{
					Success = true,
					Task = new
					{
						ID = task.ID,
						Name = task.Name
					}
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetFlowParametersData(HSMTemplateFlowParameters flowParameter, int serviceId)
		{
			try
			{
				var service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(serviceId);

				Core.System.Instance.Logic.EnsureServiceInstance(service);
				var serviceSettings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;

				var screenData = serviceSettings.FindFlowScreensData(flowParameter.FlowID, flowParameter.NavigateScreen);

				if (screenData != null && screenData.Length > 0)
				{
					return new
					{
						Success = true,
						Parameters = screenData[0].Data.ToString()
					};
				}

				return new
				{
					Success = true,
				};

			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrio un error al obtener los parametros del flow: {0}", ex);
				return new
				{
					Success = false,
				};
			}
		}

		#endregion
	}
}