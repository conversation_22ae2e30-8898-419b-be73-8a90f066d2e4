#if DEBUG
//#define LOCAL
#endif

using Azure;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Security.Permissions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.Core.FilterConditions;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.Reports.RealTime;
using Yoizen.Social.DomainModel.Whatsapp;
using Yoizen.Social.DomainModel.Whatsapp.Movistar;
using static System.Net.WebRequestMethods;
using static Yoizen.Social.DomainModel.Reports.Export.ReportExport;

namespace Yoizen.Social.SocialServices.Subscriber
{
	public static class Manager
	{
		#region Enum

		/// <summary>
		/// Enumeración con los tipos de procesadores del servicebus
		/// </summary>
		[Flags]
		public enum ServiceBusServices
		{
			/// <summary>
			/// Procesador de mensajes
			/// </summary>
			Messages = 1,

			/// <summary>
			/// Procesador de estados
			/// </summary>
			Statuses = 2,

			/// <summary>
			/// Procesador de respuestas
			/// </summary>
			Replies = 4,

			/// <summary>
			/// Procesador de eventos
			/// </summary>
			Events = 8,

			/// <summary>
			/// Procesador de mensajes de hsm de whatsapp
			/// </summary>
			WhatsappHSM = 16,

			/// <summary>
			/// Procesador de llamadas de whatsapp
			/// </summary>
			WhatsappWebRtc = 32,

			/// <summary>
			/// Todos los procesadores
			/// </summary>
			All = Messages + Statuses + Replies + Events + WhatsappHSM + WhatsappWebRtc
		}

		#endregion

		#region Constants

		private const string SecretKeyForHashing = "JqI6UtACBlpdBQB4cUALmPA/LkTowWqi72wXO+A/iKY=";

		#endregion
		
		#region Fields

		private static bool initialized = false;
		private static bool started = false;
		private static string connectionString;
		private static Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient administrationClient = null;
		private static ServiceBusClient client = null;
		private static ServiceBusSessionProcessor processorSession = null;
		private static ServiceBusProcessor processorStatuses = null;
		private static ServiceBusProcessor processorWhatsappHSM = null;
		private static ServiceBusProcessor processorEvents = null;
		private static ServiceBusProcessor processorReplies = null;
		private static ServiceBusSessionProcessor processorWhatsappWebRtc = null;
		private static Azure.Messaging.EventGrid.EventGridPublisherClient egClient = null;

		private static bool shouldCheckSubscriberMessagesInactivity = false;
		private static DateTime lastMessageReceivedDate = DateTime.MinValue;
		private static DateTime lastMessageCheckDate = DateTime.MinValue;

		#endregion

		#region Constructors

		static Manager()
		{
			if (!string.IsNullOrEmpty(global::System.Configuration.ConfigurationManager.AppSettings["CheckSubscriberMessagesInactivity"]) &&
				bool.TryParse(global::System.Configuration.ConfigurationManager.AppSettings["CheckSubscriberMessagesInactivity"], out bool checkSubscriberMessagesInactivity))
			{
				shouldCheckSubscriberMessagesInactivity = checkSubscriberMessagesInactivity;
			}
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve si fue inicializado
		/// </summary>
		public static bool Initialized { get { return initialized; } }

		/// <summary>
		/// Devuelve si el procesamiento de mensajes del service bus está iniciado
		/// </summary>
		public static bool IsStarted { get { return started; } }

		#endregion

		#region Internal Methods

		internal static string GenerateSHA256(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA256(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				return Convert.ToBase64String(myhmacsha1.ComputeHash(stream));
			}
		}

		#endregion

		#region Private Methods

		private static Task ExceptionReceivedHandler(ProcessErrorEventArgs exceptionReceivedEventArgs)
		{
			StringBuilder sb = new StringBuilder();
			sb.AppendLine($"Message handler encountered an exception {exceptionReceivedEventArgs.Exception.Message}.");
			var context = exceptionReceivedEventArgs.ErrorSource;
			sb.AppendLine("Exception context for troubleshooting:");
			sb.AppendLine($"- ErrorSource: {exceptionReceivedEventArgs.ErrorSource}");
			sb.AppendLine($"- FullyQualifiedNamespace: {exceptionReceivedEventArgs.FullyQualifiedNamespace}");
			sb.AppendLine($"- Executing EntityPath: {exceptionReceivedEventArgs.EntityPath}");
			Tracer.TraceError("Ocurrió un error obteniendo mensajes de la cola: {0}", sb.ToString());
			return Task.CompletedTask;
		}

		private static async Task ProcessMessagesAsync(ProcessMessageEventArgs args)
		{
			var message = args.Message;

			await ProcessMessagesAsync(message);
		}

		private static async Task ProcessMessagesAsync(ServiceBusReceivedMessage message)
		{
			var body = message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus con número de secuencia {0} y contenido {1}", message.SequenceNumber, body);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);

			if (jBody["type"] == null)
			{
				Tracer.TraceVerb($"Se ignorará el mensaje con número de secuencia {message.SequenceNumber} porque no trae type");
				return;
			}

			if (jBody["type"].Type == JTokenType.String)
			{
				var type = jBody["type"].ToString();
				if (type.StartsWith("gateway/"))
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
					{
						Tracer.TraceVerb($"El mensaje con número de secuencia {message.SequenceNumber} es una novedad de gateway");

						await Core.System.Instance.GatewayService.ProcessCallback(type, jBody);
					}
				}
				else
				{
					switch (type)
					{
						case "survey":
							Tracer.TraceVerb($"El mensaje con número de secuencia {message.SequenceNumber} es una novedad de encuesta");
							break;
						case "task":
							Tracer.TraceVerb($"El mensaje con número de secuencia {message.SequenceNumber} es una novedad de tareas");
							Core.System.Instance.TasksService.ProcessNews(jBody);
							break;
						case "report":
							Tracer.TraceVerb($"El mensaje con número de secuencia {message.SequenceNumber} es una novedad de reportes");
							GenerateExternalReport(jBody);
							break;
						default:
							break;
					}
				}

				return;
			}
			else if (jBody["type"].Type == JTokenType.Integer)
			{
				var type = (DomainModel.SocialServiceTypes) jBody["type"].ToObject<short>();
				var account = jBody["account"].ToString();
				var info = jBody["info"];

				DateTime? ts = null;
				if (jBody["ts"] != null && jBody["ts"].Type == JTokenType.Integer)
				{
					ts = Common.Conversions.UnixMillisecondsToDateTime(jBody["ts"].ToObject<long>()).ToLocalTime();
				}

				var service = DomainModel.Cache.Instance.FindItem<DomainModel.Service>(s =>
					s.Enabled &&
					s.SocialServiceType == type &&
					s.AccountID != null &&
					s.AccountID.Equals(account));

				if (service == null ||
					!service.Enabled)
				{
					if (type == SocialServiceTypes.FacebookMessenger)
					{
						service = DomainModel.Cache.Instance.FindItem<DomainModel.Service>(s =>
							s.Enabled &&
							s.SocialServiceType == SocialServiceTypes.Facebook &&
							s.AccountID != null &&
							s.AccountID.Equals(account));
					}
				}

				if (service == null ||
					!service.Enabled)
				{
					Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque la cuenta a la que corresponde no existe o no está habilitada");
					return;
				}

				Core.System.Instance.Logic.EnsureServiceInstance(service);

				if (service.ServiceConfiguration.FromDate > DateTime.Now.Date)
				{
					Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque la configuración del servicio especifica que ingresen mensajes posteriores a {service.ServiceConfiguration.FromDate}");
					return;
				}

				IEnumerable<DomainModel.Message> messages = null;
				var generatedNews = false;

				try
				{
					if (jBody["isPayment"] != null &&
						jBody["isPayment"].Type == JTokenType.Boolean &&
						jBody["isPayment"].ToObject<bool>())
					{
						var tempMessage = new PaymentMessage(service, info, type);
						messages = new List<DomainModel.Message>() { tempMessage };
					}
					else if (jBody["forwarded"] != null &&
						jBody["forwarded"].Type == JTokenType.Object)
					{
						var jForwarded = (JObject) jBody["forwarded"];
						var forwardedType = jForwarded["type"].ToString();
						if (forwardedType.Equals("message"))
						{
							var forwardedMessageId = jForwarded["messageId"].ToObject<long>();
							var entitiedToRead = new MessageDAO.RelatedEntitiesToRead(false)
							{
								PostedBy = true,
								GroupedMessages = false,
								Service = true,
								Queue = true,
								Attachments = true,
								MessageSegments = true,
								Chat = false,
							};

							var forwardedMessage = MessageDAO.GetOne(forwardedMessageId, entitiedToRead, false);
							forwardedMessage.Service = service;
							messages = new List<DomainModel.Message>() { forwardedMessage };
						}
						else if (forwardedType.Equals("whatsapp_status"))
						{
							var forwardedMessageId = jForwarded["messageId"].ToObject<long>();
							var agentId = jForwarded["agentId"].ToObject<int>();
							var caseId = jForwarded["caseId"].ToObject<long>();
							
							if (Core.System.Instance.QueueService.IsAnyAgentWorkingWithCase(caseId, out DomainModel.Case @case, out DomainModel.Agent agent))
							{
								var parameters = jForwarded["parameters"].ToObject<Dictionary<string, string>>();
								
								var notification = new DomainModel.AgentNotifications.WhatsappMessageStatusChange(agent, forwardedMessageId, caseId, parameters);
								await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);

								foreach (var caseMessage in @case.Messages)
								{
									if (caseMessage.ID == forwardedMessageId)
									{
										foreach (var parameter in parameters)
										{
											caseMessage.Parameters[parameter.Key] = parameter.Value;
										}

										break;
									}
								}
							}
						}
					}
                    else if (jBody["messageType"] != null &&
						jBody["messageType"].Type == JTokenType.String &&
						jBody["messageType"].ToObject<string>() == "paymentGateway")
                    {
                        var tempMessage = new PaymentMessage(service, info, type);

                        if (messages == null)
                            messages = new List<DomainModel.Message>();
                        ((List<DomainModel.Message>)messages).Add(tempMessage);
                    }
                    else if (jBody["messageType"] != null &&
						jBody["messageType"].Type == JTokenType.String &&
						jBody["messageType"].ToObject<string>() == "digitalSignature")
					{
						var tempMessage = new SignatureMessage(service, info, type);
						messages = new List<DomainModel.Message>() { tempMessage };
					}
					else if (jBody["messageType"] != null &&
						jBody["messageType"].Type == JTokenType.String &&
						(jBody["messageType"].ToObject<string>() == "accountLinking" || jBody["messageType"].ToObject<string>() == "accountUnlinking"))
					{
						var tempMessage = new AccountLinkingMessage(service, info, type);
						var parameters = new SocialUserServiceParameters();
						if (jBody["messageType"].ToObject<string>() == "accountLinking")
						{
							parameters[Social.Facebook.User.AccountLinkedParameter] = true.ToString();
							parameters[Social.Facebook.User.AccountLinkedTokenParameter] = tempMessage.Parameters[AccountLinkingMessage.AccountLinkingParameter];
						}
						else
						{
							parameters[Social.Facebook.User.AccountLinkedParameter] = false.ToString();
							parameters[Social.Facebook.User.AccountLinkedTokenParameter] = null;
						}
						DAL.SocialUserServiceDAO.Insert(service.ID, tempMessage.PostedBy.ID, type, parameters);

						messages = new List<DomainModel.Message>() { tempMessage };
					}
					else if (jBody["messageType"] != null &&
						jBody["messageType"].Type == JTokenType.String &&
						jBody["messageType"].ToObject<string>() == "biometric")
					{
						long caseId;
						if (!long.TryParse(info["biometric"]["caseId"].ToString(), out caseId))
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque el msj de biometría informado no tiene caso");
							return;
						}

						dynamic biometric = new System.Dynamic.ExpandoObject();
						biometric.status = info["biometric"]["status"];
						biometric.blocks = info["biometric"]["blocks"];
						biometric.flowId = info["biometric"]["flowId"];
						SocialUserServiceParameters parameters = new SocialUserServiceParameters();
						if (biometric.status == MetaEventVerificationStatuses.Completed)
						{
							biometric.verification = info["biometric"]["verification"];
							biometric.identification = info["biometric"]["identification"];
							biometric.identityStatus = info["biometric"]["identification"]["identityStatus"].ToString();
							if (info["biometric"]["verification"]["reFacematch"].Type == JTokenType.Null)
							{
								parameters[BiometricMessage.IdentityStatus] = info["biometric"]["identification"]["identityStatus"].ToString();
								parameters[BiometricMessage.VerificationId] = info["biometric"]["verification"]["id"].ToString();
								parameters[BiometricMessage.IdentificationId] = info["biometric"]["identification"]["id"].ToString();
							}
						}

						var lastStatus = info["biometric"]["lastStatus"].ToString();
						parameters[BiometricMessage.LastStatus] = lastStatus;

						var tempMessage = new BiometricMessage(service, info, type, biometric);
						if (parameters != null)
							DAL.SocialUserServiceDAO.Insert(service.ID, tempMessage.PostedBy.ID, type, parameters);

						if (caseId == 0)
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque el caso de biometría no tiene número de caso");
							return;
						}

						var @case = CaseDAO.GetOneWithoutMessages(caseId);
						if (@case.Status == CaseStatuses.Closed)
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque el caso de biometría ya está cerrado");
							return;
						}

						if (@case.Parameters.ContainsKey(DomainModel.Case.YFlowDoNotInvokeParameter) &&
							Convert.ToBoolean(@case.Parameters[DomainModel.Case.YFlowDoNotInvokeParameter]) == true)
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque el caso de biometría tiene asignado no invocar mas a yFlow");
							return;
						}

						if (messages == null)
							messages = new List<DomainModel.Message>();

						if (int.Parse(lastStatus) == (int) MetaEventType.VerificationStarted ||
							int.Parse(lastStatus) == (int) MetaEventType.VerificationCompleted)
						{
							((List<DomainModel.Message>) messages).Add(tempMessage);
						}
					}
					else
					{
						switch (type)
						{
							case DomainModel.SocialServiceTypes.Twitter:
								{
									if (info.Type == JTokenType.Array)
										messages = SocialServices.Twitter.Converter.Convert((JArray) info, service, (SocialServices.Twitter.TwitterServiceConfiguration) service.ServiceConfiguration);
								}
								break;
							case DomainModel.SocialServiceTypes.Facebook:
								messages = await SocialServices.Facebook.Converter.Convert(info, service);
								break;
							case DomainModel.SocialServiceTypes.Mail:
								break;
							case DomainModel.SocialServiceTypes.Chat:
								break;
							case DomainModel.SocialServiceTypes.WhatsApp:
								{
									var wpService = (WhatsApp.WhatsAppService) service.SocialService;
									var result = await SocialServices.WhatsApp.Converter.Convert(info, service, wpService);

									messages = result.Messages;
									var statusMessages = result.StatusMessages;

									var wpConfiguration = service.ServiceConfiguration as SocialServices.WhatsApp.WhatsAppServiceConfiguration;

									#region Mierda de movistar para derivación del bot a encuestas

									if (wpConfiguration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar &&
										messages != null)
									{
										var surveyMessages = messages.Where(m => m.Parameters != null &&
											m.Parameters.ContainsKey(Social.WhatsApp.WhatsAppMessage.IsSurveyHandoffParameter) &&
											bool.TryParse(m.Parameters[Social.WhatsApp.WhatsAppMessage.IsSurveyHandoffParameter], out bool isSurveyHandoff) &&
											isSurveyHandoff);

										messages = messages.Except(surveyMessages);

										if (surveyMessages != null && surveyMessages.Any())
										{
											if (wpConfiguration.IntegrationType6SurveyEnabled &&
												wpConfiguration.IntegrationType6SurveyID != Guid.Empty)
											{
												var survey = DomainModel.Cache.Instance.GetItem<DomainModel.Survey>(wpConfiguration.IntegrationType6SurveyID.ToString());
												if (survey != null && survey.Enabled)
												{
													foreach (var surveyMessage in surveyMessages)
													{
														var shouldSendSurvey = true;
														if (wpConfiguration.IntegrationType6SurveyDontSendIfLastSurveyAfterMinutes > 0)
														{
															var socialUserProfile = DAL.SocialUserProfileDAO.GetOneBySocialUser(surveyMessage.SocialUser);
															if (socialUserProfile == null)
															{
																Tracer.TraceVerb("El usuario {0} no existe, con lo cual se lo encuesta", surveyMessage.SocialUser.ID);
															}
															else if (socialUserProfile != null &&
																socialUserProfile.LastSurveySent != null)
															{
																Tracer.TraceVerb("El usuario {0} existe. Se verificará su última encuesta", surveyMessage.SocialUser.ID);

																var minutes = DateTime.Now.Subtract(socialUserProfile.LastSurveySent.Value).TotalMinutes;
																if (minutes < wpConfiguration.IntegrationType6SurveyDontSendIfLastSurveyAfterMinutes)
																{
																	Tracer.TraceVerb("Se ignora el mensaje {0} del usuario {1} porque la última encuesta al perfil {2} fue envíada hace {3} minutos y está configurado no enviar dentro de los {4} minutos",
																		surveyMessage.SocialMessageID,
																		surveyMessage.SocialUser.ID,
																		socialUserProfile,
																		minutes,
																		wpConfiguration.IntegrationType6SurveyDontSendIfLastSurveyAfterMinutes);

																	shouldSendSurvey = false;
																}
															}
														}

														var @case = surveyMessage.Case;

														if (shouldSendSurvey)
														{
															var user = surveyMessage.PostedBy;

															if (!user.ParametersByService.ContainsKey(surveyMessage.Service.ID))
																user.ParametersByService.Add(surveyMessage.Service.ID, new DomainModel.SocialUserServiceParameters());

															if (!user.RetrievedFromDatabase)
															{
																user.ParametersByService[surveyMessage.Service.ID].FirstInteraction = DateTime.Now;
																user.VIP = null;
															}
															user.ParametersByService[surveyMessage.Service.ID].PreviousLastInteraction = user.ParametersByService[surveyMessage.Service.ID].LastInteraction;
															user.ParametersByService[surveyMessage.Service.ID].LastInteraction = DateTime.Now;

															DomainModel.Settings.SocialUserReference socialUserReference = user.ToSocialUserReference();
															if (DomainModel.SystemSettings.Instance.VIPSocialUsers.Contains(socialUserReference))
															{
																user.VIP = true;
															}
															if (DomainModel.SystemSettings.Instance.TesterSocialUsers.Contains(socialUserReference))
															{
																user.Tester = true;
															}
															if (DomainModel.SystemSettings.Instance.DoNotCallSocialUsers.Contains(socialUserReference))
															{
																user.DoNotCall = true;
															}

															var socialUserDAO = new DAL.SocialUserDAO();
															socialUserDAO.SocialUser = surveyMessage.PostedBy;
															socialUserDAO.Insert();

															surveyMessage.Status = MessageStatuses.Historical;
															await DAL.MessageDAO.InsertAsync(surveyMessage);

															bool newCaseCreated = Core.System.Instance.Logic.CreateOrContinueCase(surveyMessage, null);

															try
															{
																if (wpConfiguration.IntegrationType6SurveySendEndOfConversation)
																{
																	try
																	{
																		Tracer.TraceVerb("Se notificará al servicio que se cerró el caso por HandOff desde el Manager: {0}", @case);

																		var handoffMotive = HandoffContext.GetHandoffMotive(shouldSendSurvey, @case);
																		var handoffContext = new HandoffContext(@case);

																		if (survey.Type == SurveyTypes.Movistar)
																		{
																			var surveyButtons = Newtonsoft.Json.JsonConvert.SerializeObject(new
																			{
																				Button1Payload = survey.Configuration.MovistarButton1Payload,
																				Button1Text = survey.Configuration.MovistarButton1Text,
																				Button2Payload = survey.Configuration.MovistarButton2Payload,
																				Button2Text = survey.Configuration.MovistarButton2Text,
																				Button3Payload = survey.Configuration.MovistarButton3Payload,
																				Button3Text = survey.Configuration.MovistarButton3Text,
																				NoAgent = survey.Configuration.MovistarNoAgent,
																				LastAgent = (int?) null,
																			});
																			var jSurveyConfiguration = Newtonsoft.Json.Linq.JObject.Parse(surveyButtons);
																			handoffContext.Keyword = HandoffContext.GetKeyword(jSurveyConfiguration);
																		}

																		await wpService.PostToMovistarHandOff(surveyMessage.SocialConversationID, surveyMessage.PostedBy.ID, handoffMotive, handoffContext);
																	}
																	catch (Exception ex)
																	{
																		Tracer.TraceError("No se pudo notificar al servicio que se cerró el caso: {0}", ex);
																	}
																}
															}
															catch (Exception ex)
															{
																Tracer.TraceVerb("Se produjo un error al obtener el link para el caso {0}: {1}", @case.ID, ex);
															}

															await Core.System.Instance.Logic.CloseCaseAsync(new Core.ActionOptions.CloseCaseOptions()
															{
																Case = @case,
																ClosedBy = CaseClosingResponsibles.System,
																Person = null,
																Interval = Common.Interval.Null,
																StoreInDatabase = true,
																Queue = null,
																Message = null,
																InvokeServiceOperations = false
															});
														}
													}
												}
											}
										}
									}

									#endregion

									if (statusMessages != null && statusMessages.Any())
									{
										generatedNews = true;

										if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
										{
											foreach (var statusMessage in statusMessages)
											{
												if (Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats &&
													statusMessage.Service.Settings.ActAsChat)
												{
													var caseId = statusMessage.Case?.ID;
													if (caseId != null)
													{
														if (Core.System.Instance.QueueService.IsAnyAgentWorkingWithCase(caseId.Value, out DomainModel.Case @case, out DomainModel.Agent agent))
														{
															var notification = new DomainModel.AgentNotifications.WhatsappMessageStatusChange(agent, statusMessage.ID, caseId.Value, statusMessage.Parameters);
															await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);

															foreach (var caseMessage in @case.Messages)
															{
																if (caseMessage.ID == statusMessage.ID)
																{
																	foreach (var parameter in statusMessage.Parameters)
																	{
																		caseMessage.Parameters[parameter.Key] = parameter.Value;
																	}

																	break;
																}
															}
														}
													}
												}
											}
										}
									}

									if (Licensing.LicenseManager.Instance.License.Configuration.AllowToMarkAsReadWhatsappMessages &&
										wpConfiguration.MarkAsReadBehaviour == SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.MarkOnReceived &&
										messages != null)
									{
										foreach (var wpMessage in messages)
										{
											await wpService.MarkMessageAsRead(wpMessage);
										}
									}
								}
								break;
							case DomainModel.SocialServiceTypes.SMS:
								break;
							case DomainModel.SocialServiceTypes.Telegram:
								messages = await SocialServices.Telegram.Converter.Convert(info, service, (Telegram.TelegramSocialService) service.SocialService);
								break;
							case DomainModel.SocialServiceTypes.LinkedIn:
								{
									var tempMessage = await SocialServices.LinkedIn.Converter.Convert((JObject) info, service);
									if (tempMessage != null)
									{
										if (messages == null)
											messages = new List<DomainModel.Message>();
										((List<DomainModel.Message>) messages).Add(tempMessage);
									}
								}
								break;
							case DomainModel.SocialServiceTypes.Skype:
								messages = SocialServices.Skype.Converter.Convert((Newtonsoft.Json.Linq.JObject) info, service);
								break;
							case DomainModel.SocialServiceTypes.FacebookMessenger:
								{
									var accountLinks = new List<DomainModel.AccountLink>();
									if (SocialServices.Facebook.MessengerConverter.ContainsAccountLinkingInfo(info, service, accountLinks))
									{
										foreach (var accountLinkInfo in accountLinks)
										{
											if (accountLinkInfo.Linked)
											{
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Facebook.User.AccountLinkedParameter] = true.ToString();
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Facebook.User.AccountLinkedTokenParameter] = accountLinkInfo.AuthorizationCode;
											}
											else
											{
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Facebook.User.AccountLinkedParameter] = false.ToString();
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Facebook.User.AccountLinkedTokenParameter] = string.Empty;
											}

											DAL.SocialUserServiceDAO.Insert(service.ID, accountLinkInfo.SocialUser.ID, accountLinkInfo.SocialUser.SocialServiceType, accountLinkInfo.SocialUser.ParametersByService[service.ID]);

											Tracer.TraceInfo("Se actualizó la información de vinculación de cuentas de Facebook Messenger del usuario {0}", accountLinkInfo.SocialUser);

											var tempMessage = new Social.Facebook.Messenger.Postback();
											tempMessage.Service = service;
											tempMessage.PostedBy = accountLinkInfo.SocialUser;
											tempMessage.Parameters[Social.Facebook.MessengerMessage.PayloadParameter] = "account_link";
											tempMessage.Date = DateTime.Now;
											tempMessage.InsertedDate = tempMessage.Date;
											tempMessage.SocialMessageID = Guid.NewGuid().ToString();
											tempMessage.Body = string.Empty;
											tempMessage.EmptyBody = true;
											tempMessage.RepliesToSocialMessageID = null;
											tempMessage.ServiceType = DomainModel.ServiceTypes.FacebookMessenger;
											tempMessage.Incoming = true;

											if (messages == null)
												messages = new List<DomainModel.Message>();
											((List<DomainModel.Message>) messages).Add(tempMessage);
										}
									}
									else
									{
										messages = await SocialServices.Facebook.MessengerConverter.Convert(info, service);
									}
								}
								break;
							case DomainModel.SocialServiceTypes.Instagram:
								{
									var accountLinks = new List<DomainModel.AccountLink>();
									if (SocialServices.Instagram.Converter.ContainsAccountLinkingInfo(info, service, accountLinks))
									{
										foreach (var accountLinkInfo in accountLinks)
										{
											if (accountLinkInfo.Linked)
											{
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Instagram.User.AccountLinkedParameter] = true.ToString();
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Instagram.User.AccountLinkedTokenParameter] = accountLinkInfo.AuthorizationCode;
											}
											else
											{
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Instagram.User.AccountLinkedParameter] = false.ToString();
												accountLinkInfo.SocialUser.ParametersByService[service.ID][Social.Instagram.User.AccountLinkedTokenParameter] = string.Empty;
											}

											DAL.SocialUserServiceDAO.Insert(service.ID, accountLinkInfo.SocialUser.ID, accountLinkInfo.SocialUser.SocialServiceType, accountLinkInfo.SocialUser.ParametersByService[service.ID]);

											Tracer.TraceInfo("Se actualizó la información de vinculación de cuentas de Instagram del usuario {0}", accountLinkInfo.SocialUser);

											var tempMessage = new Social.Instagram.Messenger.Postback();
											tempMessage.Service = service;
											tempMessage.PostedBy = accountLinkInfo.SocialUser;
											tempMessage.Parameters[Social.Instagram.PrivateMessage.PayloadParameter] = "account_link";
											tempMessage.Date = DateTime.Now;
											tempMessage.InsertedDate = tempMessage.Date;
											tempMessage.SocialMessageID = Guid.NewGuid().ToString();
											tempMessage.Body = string.Empty;
											tempMessage.EmptyBody = true;
											tempMessage.RepliesToSocialMessageID = null;
											tempMessage.ServiceType = DomainModel.ServiceTypes.Instagram;
											tempMessage.Incoming = true;

											if (messages == null)
												messages = new List<DomainModel.Message>();
											((List<DomainModel.Message>) messages).Add(tempMessage);
										}
									}
									else
									{
										messages = await SocialServices.Instagram.Converter.Convert(info, service);
									}
								}
								break;
							case DomainModel.SocialServiceTypes.MercadoLibre:
								messages = SocialServices.MercadoLibre.Converter.ConvertArray(info, service);
								break;
							case DomainModel.SocialServiceTypes.AppleMessaging:
								{
									messages = SocialServices.AppleMessaging.Converter.Convert(info, service, out IEnumerable<Social.AppleMessaging.AppleConversationEvent> events);

									if (events != null && events.Any())
									{
										generatedNews = true;

										foreach (var @event in events)
										{
											if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
											{
												DomainModel.SocialUser socialUser = null;
												DomainModel.Message assignedMessage = null;
												if (Core.System.Instance.QueueService.IsAnyAgentWorkingWithSocialUser(@event.SocialUserID, SocialServiceTypes.AppleMessaging, service.ID, out DomainModel.Case @case, out DomainModel.Agent agent, out assignedMessage))
												{
													var notification = new DomainModel.AgentNotifications.ConversationEvent(@event.Type.ToString(), agent, @case.ID, null);
													await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);

													socialUser = assignedMessage.PostedBy;
												}

												if (@event.Type == Social.AppleMessaging.AppleConversationEvent.AppleConversationEventTypes.ConversationClosed)
												{
													if (assignedMessage == null)
													{
														if (Core.System.Instance.QueueService.AreSocialUserMessagesEnqueued(@event.SocialUserID, SocialServiceTypes.AppleMessaging, service.ID, out assignedMessage))
														{
															socialUser = assignedMessage.PostedBy;
														}
													}

													if (socialUser == null)
														socialUser = DAL.SocialUserDAO.GetOneBySocialID(@event.SocialUserID, SocialServiceTypes.AppleMessaging, false);

													if (socialUser != null)
													{
														if (!socialUser.ParametersByService.ContainsKey(service.ID))
															socialUser.ParametersByService[service.ID] = new SocialUserServiceParameters();
														socialUser.ParametersByService[service.ID][Social.AppleMessaging.User.ConversationClosedParameter] = true.ToString();
														DAL.SocialUserServiceDAO.Insert(service.ID, socialUser.ID, SocialServiceTypes.AppleMessaging, socialUser.ParametersByService[service.ID]);
													}
												}
											}
											else
											{
												if (@event.Type == Social.AppleMessaging.AppleConversationEvent.AppleConversationEventTypes.ConversationClosed)
												{
													var socialUser = DAL.SocialUserDAO.GetOneBySocialID(@event.SocialUserID, SocialServiceTypes.AppleMessaging, false);
													if (socialUser != null)
													{
														if (!socialUser.ParametersByService.ContainsKey(service.ID))
															socialUser.ParametersByService[service.ID] = new SocialUserServiceParameters();
														socialUser.ParametersByService[service.ID][Social.AppleMessaging.User.ConversationClosedParameter] = true.ToString();
														DAL.SocialUserServiceDAO.Insert(service.ID, socialUser.ID, SocialServiceTypes.AppleMessaging, socialUser.ParametersByService[service.ID]);
													}
												}
											}
										}
									}
								}
								break;

							case DomainModel.SocialServiceTypes.GoogleRBM:
								{
									messages = SocialServices.GoogleRBM.Converter.Convert(info, service, out IEnumerable<Social.GoogleRBM.GoogleRBMConversationEvent> events);

									if (events != null && events.Any())
									{
										generatedNews = true;

										if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
										{
											foreach (var @event in events)
											{
												DomainModel.Message assignedMessage = null;
												if (Core.System.Instance.QueueService.IsAnyAgentWorkingWithMessage(@event.SocialMessageID, SocialServiceTypes.GoogleRBM, out DomainModel.Case @case, out DomainModel.Agent agent, out assignedMessage))
												{
													var notification = new DomainModel.AgentNotifications.ConversationEvent(@event.Type.ToString(), agent, @case.ID, null);
													await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);
												}
											}
										}
									}
								}
								break;
							default:
								break;
						}
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error al convertir el mensaje {0}: {1}", message.SequenceNumber, ex);
				}

				if (messages == null || 
					!messages.Any())
				{
					if (!generatedNews)
						Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{message.SequenceNumber}-Body:{body}] porque no se generaron mensajes");
					return;
				}

				Tracer.TraceVerb($"Se informaron {messages.Count()} mensajes");

				foreach (var tempMessage in messages)
				{
					if (ts != null)
					{
						tempMessage.Parameters[DomainModel.Message.ServiceBusDateParameter] = ts.Value.ToString("s");
					}

					tempMessage.Parameters[DomainModel.Message.ServiceBusSessionIDParameter] = message.SessionId;
					tempMessage.Parameters[DomainModel.Message.ServiceBusDownloadedDateParameter] = DateTime.Now.ToString("s");
				}

				if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
				{
					var enqueueSettings = new Core.EnqueueSettings();
					enqueueSettings.ServiceBusSequenceNumber = message.SequenceNumber;

					if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages)
					{
						enqueueSettings.CanInvokeYFlow = false;
						enqueueSettings.ForwardedMessage = true;
						await Core.System.Instance.QueueService.InsertAndEnqueueMessagesAsync(messages, service, enqueueSettings);
					}
					else
					{
						await Core.System.Instance.QueueService.InsertAndEnqueueMessagesAsync(messages, service, enqueueSettings);
					}
					Tracer.TraceVerb("Finalizó el procesamiento de los mensajes del service bus con número de secuencia {0}", message.SequenceNumber);
				}
				else
				{
					var processSettings = new Core.GatewayProcessSettings();
					processSettings.ServiceBusSequenceNumber = message.SequenceNumber;

					if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages)
					{
						processSettings.CanInvokeYFlow = false;
						await Core.System.Instance.GatewayService.ProcessMessages(messages, service, processSettings);
					}
					else
					{
						await Core.System.Instance.GatewayService.ProcessMessages(messages, service, processSettings);
					}
					
					Tracer.TraceVerb("Finalizó el procesamiento de los mensajes del service bus con número de secuencia {0}", message.SequenceNumber);
				}
			}
			else
			{
				Tracer.TraceVerb($"Se ignora el mensaje con número de secuencia {message.SequenceNumber} porque type es inválido");
			}
		}

		private static async Task ProcessMessagesWithSessionsAsync(ProcessSessionMessageEventArgs args)
		{
			var message = args.Message;

			lastMessageReceivedDate = DateTime.Now;

			await ProcessMessagesAsync(message);
		}

		private static async Task ProcessWhatsappWebRtcCallsWithSessionsAsync(ProcessSessionMessageEventArgs args)
		{
			var body = args.Message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de webrtc de whatsapp con número de secuencia {0} y contenido {1}", args.Message.SequenceNumber, body);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);

			var type = (DomainModel.SocialServiceTypes) jBody["type"].ToObject<short>();
			var account = jBody["account"].ToString();

			var service = DomainModel.Cache.Instance.FindItem<DomainModel.Service>(s =>
				s.Enabled &&
				s.SocialServiceType == type &&
				s.AccountID != null &&
				s.AccountID.Equals(account));

			if (service == null ||
				!service.Enabled)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque la cuenta a la que corresponde no existe o no está habilitada");
				return;
			}

			Core.System.Instance.Logic.EnsureServiceInstance(service);

			var whatsappService = service.SocialService as Social.SocialServices.WhatsApp.WhatsAppService;

			var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			if (!settings.VoiceCallsEnabled)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el servicio de la cuenta {service.ID} no soporta llamadas");
				return;
			}

			if (!settings.ActAsChat)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el servicio de la cuenta {service.ID} no está configurado para actuar como modo chat");
				return;
			}

			var jInfo = (Newtonsoft.Json.Linq.JObject) jBody["info"];

			var jCalls = (Newtonsoft.Json.Linq.JArray) jInfo["calls"];
			if (jCalls.Count == 0)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque no hay información de llamada");
				return;
			}

			var jCall = (Newtonsoft.Json.Linq.JObject) jCalls[0];

			var @event = jCall["event"].ToString();
			var from = jCall["from"].ToString();
			var callId = jCall["id"].ToString();
			var timestamp = jCall["timestamp"].ToObject<long>();

			// Buscamos el CASO (que puede estar en cola o asignado) que tiene el usuario y servicio que está haciendo la llamada
			if (!Core.System.Instance.QueueService.AreSocialUserMessagesEnqueuedOrAssigned(from, SocialServiceTypes.WhatsApp, service.ID, out DomainModel.Message message))
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque no se encontró un mensaje en cola o asignado del usuario de whatsapp {from} del servicio {service.ID}");

				if (!@event.Equals("terminate"))
				{
					// Se envía notificación al servicio que se rechaza la llamada
					await whatsappService.VoiceCallReject(callId);

					if (settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned != null)
					{
						await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned, message);
					}

					return;
				}
			}
			var @case = message.Case;

			if (message.Status != MessageStatuses.Assigned)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el caso {message.Case.ID} del usuario de whatsapp {from} del servicio {service.ID} no está asignado a ningún agente");

				if (!@event.Equals("terminate"))
				{
					// Se envía notificación al servicio que se rechaza la llamada
					await whatsappService.VoiceCallReject(callId);

					if (settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned != null)
					{
						await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned, message);
					}

					return;
				}
			}

			var info = Core.System.Instance.AgentsService.GetConnectionInfo(message.AssignedTo);
			if (info == null)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el agente {message.AssignedTo} no está conectado");

				if (!@event.Equals("terminate"))
				{
					// Se envía notificación al servicio que se rechaza la llamada
					await whatsappService.VoiceCallReject(callId);

					if (settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable != null)
					{
						await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable, message);
					}

					return;
				}
			}

			if (!message.AssignedTo.WhatsAppVoiceCallsEnabled)
			{
				if (!@event.Equals("terminate"))
				{
					Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el agente {message.AssignedTo.FullName} NO tiene habilitado llamadas por voz de Whatsapp");
					await whatsappService.VoiceCallReject(callId);
					return;
				}
			}

			if (info.CurrentCall != null)
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el agente {message.AssignedTo} ya se encuentra con una llamada");

				if (!@event.Equals("terminate"))
				{
					// Se envía notificación al servicio que se rechaza la llamada
					await whatsappService.VoiceCallReject(callId);

					if (settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall != null)
					{
						await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall, message);
					}

					return;
				}
			}

			var allowedStatusToAcceptCalls = new List<ConnectionStatuses>()
			{
				ConnectionStatuses.Available,
				ConnectionStatuses.Working
			};

			if (!allowedStatusToAcceptCalls.Contains(info.Status))
			{
				Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el agente {message.AssignedTo} No se encuentra en estado disponible");

				if (!@event.Equals("terminate"))
				{
					// Se envía notificación al servicio que se rechaza la llamada
					await whatsappService.VoiceCallReject(callId);

					if (settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable != null)
					{
						await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable, message);
					}

					return;
				}
			}

			switch (@event)
			{
				case "candidates":
					{
						var candidates = jCall["candidates"].ToString();
						Tracer.TraceVerb($"El agente {message.AssignedTo} tiene asignado un mensaje del usuario de whatsapp {from} del servicio {service.ID} y está llegando datos de candidato para una llamada. Se notifica al agente");
						var notification = new DomainModel.AgentNotifications.WhatsappVoiceCallCandidates(message.AssignedTo, message.ID, message.Case.ID, candidates);
						//info.CallsStats.RequestedCalls++;
						await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);
					}
					break;

				case "connect":
					{
						if (@case.CurrentCall != null)
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el caso {message.Case.ID} del usuario de whatsapp {from} del servicio {service.ID} ya tiene una llamada en curso");
							await whatsappService.VoiceCallReject(callId);

							if (settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall != null)
							{
								await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall, message);
							}

							return;
						}

						if (!@case.Parameters.ContainsKey(DomainModel.Case.WhatappVoiceCallLastInviteSentTimeStampParameter))
						{
							Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el caso {message.Case.ID} del usuario de whatsapp {from} del servicio {service.ID} todavía no tuvo una invitación");
							await whatsappService.VoiceCallReject(callId);

							if (settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite != null)
							{
								await SendInteractiveMessage(settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite, message);
							}

							return;
						}

						var jSession = (Newtonsoft.Json.Linq.JObject) jCall["session"];
						var sdpType = jSession["sdp_type"].ToString();
						var sdp = jSession["sdp"].ToString();
						var direction = jCall["direction"].ToString();

						var call = new DomainModel.Call();
						call.CaseID = @case.ID;
						call.SocialCallID = callId;
						call.Status = CallStatuses.New;
						call.GeneratedDate = DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime;
						call.SocialServiceType = SocialServiceTypes.WhatsApp;
						call.SocialUser = message.PostedBy;
						call.Service = message.Service;
						call.Incoming = direction.Equals("incoming", StringComparison.InvariantCultureIgnoreCase) || direction.Equals("user_initiated", StringComparison.InvariantCultureIgnoreCase);
						call.Agent = message.AssignedTo;

						DAL.CallDAO.Insert(call);

						@case.AddCall(call);
						DAL.CaseDAO.UpdateParameters(@case);

						info.AssignCall(call);
						info.CallsStats.RequestedCalls++;

						var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
						DomainModel.Historical.Daily dailyInfo;
						dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
						dailyInfo.TotalIncomingCalls = 1;
						dailyInfo.PersonID = message.AssignedTo.ID;
						Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);

						Tracer.TraceVerb($"El agente {message.AssignedTo} tiene asignado un mensaje del usuario de whatsapp {from} del servicio {service.ID} y está llegando datos para una llamada. Se notifica al agente");
						var notification = new DomainModel.AgentNotifications.WhatsappVoiceCallOffer(message.AssignedTo, message.ID, @case.ID, call.ID, callId, sdp, null);
						await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);
					}
					break;

				case "terminate":
					{
						Tracer.TraceVerb($"El agente {message.AssignedTo} tiene asignado un mensaje del usuario de whatsapp {from} del servicio {service.ID} y está llegando datos de terminación de una llamada. Se notifica al agente");

						if (@case.CurrentCall != null && 
							@case.CurrentCall.SocialCallID.Equals(callId))
						{
							if (jCall["status"] != null && jCall["status"].Type == JTokenType.String)
							{
								var status = jCall["status"].ToString();
								@case.CurrentCall.Parameters[DomainModel.Call.WhatsAppVoiceCallTerminateStatusParameter] = status;

								if (status.Equals("failed", StringComparison.InvariantCultureIgnoreCase))
								{
									var jErrors = (Newtonsoft.Json.Linq.JArray) jInfo["errors"];
									if (jErrors != null && jErrors.Count > 0)
										@case.CurrentCall.Parameters[DomainModel.Call.WhatsAppVoiceCallTerminateErrorsParameter] = jErrors.ToString();
								}
							}
							if (jCall["start_time"] != null && 
								(jCall["start_time"].Type == JTokenType.Integer || jCall["start_time"].Type == JTokenType.String))
								@case.CurrentCall.Parameters[DomainModel.Call.WhatsAppVoiceCallTerminateStartTimeParameter] = jCall["start_time"].ToString();
							if (jCall["end_time"] != null &&
								(jCall["end_time"].Type == JTokenType.Integer || jCall["end_time"].Type == JTokenType.String))
								@case.CurrentCall.Parameters[DomainModel.Call.WhatsAppVoiceCallTerminateEndTimeParameter] = jCall["end_time"].ToString();
							if (jCall["duration"] != null && 
								(jCall["duration"].Type == JTokenType.Integer || jCall["duration"].Type == JTokenType.String))
								@case.CurrentCall.Parameters[DomainModel.Call.WhatsAppVoiceCallTerminateDurationParameter] = jCall["duration"].ToString();

							if (@case.CurrentCall.Status != CallStatuses.Terminated)
							{
								var oldStatus = @case.CurrentCall.Status;
								var newStatus = CallStatuses.Terminated;
								
								@case.CurrentCall.EndDate = DateTime.Now;
								@case.CurrentCall.EndResponsible = CallEndResponsibles.User;

								//Meta no envia un estado distinto cuando es una llamada perdida por lo que podemos deducirlo con el estado viejo.
								if (oldStatus == CallStatuses.New)
								{
									@case.CurrentCall.EndResponsible = @case.CurrentCall.Agent != null ? CallEndResponsibles.Unanswered : CallEndResponsibles.Agent;

									newStatus = CallStatuses.ConnectionFailed;
									if (message.Queue != null)
									{
										var systemQueue = Core.System.Instance.QueueService.GetQueueById(message.Queue.ID);
										if (systemQueue != null)
										{
											systemQueue.RealTimeInfo.MissedCalls++;
										}
									}

									var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
									DomainModel.Historical.Daily dailyInfo;
									dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
									dailyInfo.PersonID = message.AssignedTo.ID;
									dailyInfo.TotalMissedCalls = 1;
									Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);
								}

								@case.CurrentCall.Status = newStatus;

								DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.UserTerminated);

								var notification = new DomainModel.AgentNotifications.WhatsappVoiceCallTerminate(message.AssignedTo, message.ID, message.Case.ID, @case.CurrentCall.ID, callId, null);
								await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);
							}
							else
							{
								DAL.CallDAO.UpdateParameters(@case.CurrentCall);
							}

							@case.FinishCurrentCall();

							info.CurrentCall = null;
						}
					}
					break;

				default:
					Tracer.TraceVerb($"Se ignora el mensaje [SequenceNumber:{args.Message.SequenceNumber}-Body:{body}] porque el evento {@event} no tiene tratamiento");
					break;
			}
		}

		private static async Task ProcessMessagesWhatsappHSMAsync(ProcessMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var message = args.Message;

			var body = message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de whatsapp hsm con número de secuencia {0} y contenido {1}", message.SequenceNumber, body);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
			string guid;

			Core.ActionOptions.SendWhatsappOptions options;

			if (jBody["fromCallback"] != null &&
				jBody["fromCallback"].Type == JTokenType.Boolean &&
				jBody["fromCallback"].ToObject<bool>())
			{
				var jInfo = (JObject) jBody["info"];
				options = Core.System.Instance.Logic.ParseSendWhatsapp(jInfo, ReplySources.ExternalIntegration, out string errorMessage, out int? errorCode);
				if (options == null)
				{
					Tracer.TraceInfo("Se ignora el update con número de secuencia {0} en el envío del HSM. Razón: {1}", message.SequenceNumber, errorMessage);

					await args.CompleteMessageAsync(message);

					return;
				}

				guid = jBody["guid"].ToString();

				options.SendInBackground = false;
			}
			else
			{
				options = jBody["options"].ToObject<Core.ActionOptions.SendWhatsappOptions>();
				guid = jBody["guid"].ToString();
				var serviceId = jBody["serviceId"].ToObject<int>();
				var service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(serviceId);
				options.Service = service;
			}

			if (options.Parameters == null)
				options.Parameters = new Dictionary<string, string>();
			options.Parameters["HSMGuid"] = guid;

			try
			{
				await Core.System.Instance.Logic.SendWhatsapp(options);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error envíando el mensaje de HSM: {0}", ex);
			}
			finally
			{
				await args.CompleteMessageAsync(message);
			}
		}

		private static async Task ProcessEventsAsync(ProcessMessageEventArgs args)
		{
			var message = args.Message;

			var body = message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de eventos con número de secuencia {0} y contenido {1}", message.SequenceNumber, body);

			try
			{
				var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
				var type = jBody["type"].ToString();

				switch (type)
				{
					case "signalr":
						await Core.System.Instance.RealTimeService.HandleEvent(jBody);
						break;
					case "mediaserver":
						{
							if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCallsRecording)
								return;

							var jInfo = (Newtonsoft.Json.Linq.JObject) jBody["info"];
							var action = jInfo["action"].ToString();

							if (action.Equals("vodReady", StringComparison.InvariantCultureIgnoreCase))
							{
								var vodId = jInfo["vodId"].ToString();
								var streamId = jInfo["id"].ToString();
								var streamName = jInfo["vodName"].ToString();
								try
								{
									var streamNameParts = streamName.Split("_".ToCharArray());
									var callId = long.Parse(streamNameParts[0]);
									var caseId = long.Parse(streamNameParts[1]);
									var serviceId = int.Parse(streamNameParts[2]);

									var audioContents = await DomainModel.SystemSettings.Instance.Whatsapp.DownloadVoiceCallRecording(Licensing.LicenseManager.Instance.License.Configuration.VoiceCallsMediaServerAppName, vodId);

									var @case = DAL.CaseDAO.GetOneWithoutMessages(caseId, false);
									
									var call = DAL.CallDAO.GetOne(callId);

									var voiceCallStoragePath = await StorageManager.Instance.SaveVoiceCallRecording(call, serviceId, $"{streamName}.mp4", audioContents.Content);

									var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
									DomainModel.Historical.Daily dailyInfo;
									dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
									
									if (call.Agent != null)
									{
										dailyInfo.PersonID = call.Agent.ID;
									}
									
									dailyInfo.TotalAvailableCallRecords = 1;
									Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);

									call.Recorded = true;
									call.RecordPath = voiceCallStoragePath;
									call.RecordSize = audioContents.Content.Length;
									call.RecordLength = Convert.ToInt64(audioContents.Length.TotalMilliseconds);
									DAL.CallDAO.UpdateRecording(call);

									Tracer.TraceInfo("Se grabó la grabación de la llamada del caso {0} en la ruta {1}", @case.ID, voiceCallStoragePath);
								}
								catch (Exception ex)
								{
									Tracer.TraceInfo("Ocurrió un error en la descarga y grabación en storage de la grabación {0}: {1}", vodId, ex);
								}
							}
						}
						break;
					default:
						break;
				}
			}
			catch { }
		}

		private static async Task ProcessRepliesAsync(ProcessMessageEventArgs args)
		{
			var body = args.Message.Body.ToString();

			await args.CompleteMessageAsync(args.Message);

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de respuestas con número de secuencia {0} y contenido {1}", new Dictionary<string, object>()
			{
				{ DomainModel.TracingCommonProperties.ServiceBusSeq, args.Message.SequenceNumber }
			}, args.Message.SequenceNumber, body);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
			var messageId = jBody["messageId"].ToObject<long>();

			var relatedEntitiesToRead = new MessageDAO.RelatedEntitiesToRead(false)
			{
				RepliesTo = true,
				AssociatedMessage = true,
				Service = true,
				Attachments = true,
				RepliedBy = true,
				RepliesToSocialUser = true,
				Queue = true
			};

			var message = DAL.MessageDAO.GetOne(messageId, relatedEntitiesToRead);
			if (message == null)
			{
				Tracer.TraceError("No se encontró el mensaje {0} para ser utilizado para responder", messageId);
				return;
			}

			if (message.Service == null)
			{
				Tracer.TraceError("No se encontró el servicio del mensaje {0} para ser utilizado para responder", message);
				return;
			}

			if (!message.Service.Enabled)
			{
				Tracer.TraceError("El servicio {0} del mensaje {1} para ser utilizado para responder está deshabilitado. Se ignora el mensaje", message.Service.Name, message);
				return;
			}

			Core.System.Instance.Logic.EnsureServiceInstance(message.Service);

			if (message.RequiresAuthorization == true && (message.Authorized == null || message.Authorized == false))
			{
				Tracer.TraceVerb("No se enviará el mensaje {0} porque requiere autorización", message);
				return;
			}

			if (message.DeliveryRetries != null)
			{
				if (message.DeliveryRetries.Value >= DomainModel.SystemSettings.Instance.MaxRetriesForOutgoingMessages)
				{
					Tracer.TraceVerb("No se enviará el mensaje {0} porque superó la cantidad de reintentos", message);
					return;
				}
			}

			var socialService = message.Service.SocialService;

			try
			{
				Tracer.TraceInfo("Enviando el mensaje {0} - {1}", message.ID, message);
				message.SocialMessageID = await socialService.Reply(message);

				Tracer.TraceInfo("Actualizando el mensaje {0} con el código de mensaje de la red social {1}", message.ID, message.SocialMessageID);
				DAL.MessageDAO.UpdateSocialMessage(message.ID, message.SocialMessageID, message.Parameters);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(message,
						Newtonsoft.Json.Linq.JObject.FromObject(new
						{
							ID = message.ID,
							SocialMessageID = message.SocialMessageID
						}));
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}", message.ID, socialService.Name, ex);

				if (ex.GetType() == typeof(Business.Exceptions.ReplyException))
				{
					var rex = ex as Business.Exceptions.ReplyException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, rex.ErrorMessage, rex.ErrorCode, rex.ShouldRetry);

					if (rex.ShouldRetry)
						await Core.System.Instance.Logic.PublishReply(message);
				}
				else if (ex.GetType() == typeof(Business.Exceptions.ServiceException))
				{
					Business.Exceptions.ServiceException sex = ex as Business.Exceptions.ServiceException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, sex.ErrorMessage, sex.ErrorCode, false);
				}
				else
				{
					DAL.MessageDAO.UpdateDelivered(message.ID, false, ex.Message, false);
				}
			}
		}

		/// <summary>
		/// Genera un reporte externo desde una novedad.
		/// </summary>
		/// <param name="jBody"></param>
		private static void GenerateExternalReport(JObject jBody)
		{
			try
			{
				if (!DomainModel.SystemSettings.Instance.DailyReportsToGenerate.Contains(DomainModel.Reports.ReportTypes.WhatsappHSMWithoutCase))
				{
					Tracer.TraceInfo("No esta configurado en parametros del sistema enviar reportes de tipo HSM sin caso. Se ignora la novedad");
					return;
				}

				if (jBody.TryGetValue("info", out var jInfo) && jInfo is JObject jInfoObject &&
					jInfoObject.TryGetValue("data", out var jData) && jData is JObject jDataObject)
				{
					bool success = (bool) jData["success"];
					if (success)
					{
						var export = new DomainModel.Reports.Export.WhatsappHSMWithoutCase();
						export.FileUrl = jData["uri"].ToString();
						export.Filename = jData["fileName"].ToString();
						export.Format = DomainModel.Reports.Export.ExportFormats.ZIP;
						export.Owner = null;

						DAL.Reports.Export.ReportExportDAO dao = new DAL.Reports.Export.ReportExportDAO(export);
						dao.Insert();
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"Ocurrió un error cuando se procesaba la novedad de reportes con error {ex}");
			}
		}

		/// <summary>
		/// Crea la definición para suscribir a servicios a notificaciones push
		/// </summary>
		/// <param name="services">La enumeración de servicios para suscribir</param>
		/// <returns>Un <see cref="JObject"/> con los datos de servicios por tipo de servicio</returns>
		private static JObject GenerateServicesByType(IEnumerable<DomainModel.Service> services, bool includeTokens = false)
		{
			var license = Licensing.LicenseManager.Instance.License.Configuration;

			var jServicesByType = new JObject();
			foreach (var service in services)
			{
				if (!service.Enabled)
					continue;

				if (service.Type == DomainModel.ServiceTypes.Chat ||
					service.Type == DomainModel.ServiceTypes.IntegrationChat)
				{
					continue;
				}

				if (!license.WorkAsGateway &&
					service.Queue == null)
				{
					continue;
				}

				try
				{
					Core.System.Instance.Logic.EnsureServiceInstance(service);

					var configuration = service.ServiceConfiguration;

					if (!license.AllowToCreateServices)
					{
						var licenseService = Licensing.LicenseManager.Instance.License.FindService(service.ID);
						if (!string.IsNullOrEmpty(licenseService.AccountId) &&
							!licenseService.AccountId.Equals(configuration.GetAccountID()))
						{
							Tracer.TraceVerb("No se puede suscribir el servicio {0} ({1}) a las notificaciones push porque su identificador {2} no es el licenciado {3}", service.ID, service.Name, configuration.GetAccountID(), licenseService.AccountId);
							continue;
						}
					}

					var basicConfiguration = configuration.GenerateSubscriptionConfiguration(service);
					if (!string.IsNullOrEmpty(basicConfiguration))
					{
						var jService = JObject.Parse(basicConfiguration);
						jService.Remove("$type");

						if (license.WorkAsGateway)
						{
							jService["id"] = service.ID;
							jService["name"] = service.Name;
							if (service.UsesYFlow)
								jService["flow"] = service.YFlow.Value;

							if (includeTokens)
							{
								if (service.Type == ServiceTypes.Telegram)
								{
									var telegramConfiguration = configuration as SocialServices.Telegram.TelegramServiceConfiguration;
									if (telegramConfiguration != null)
										jService["token"] = telegramConfiguration.Token;
								}
								else if (service.Type == ServiceTypes.FacebookMessenger)
								{
									var messengerConfiguration = configuration as SocialServices.Facebook.FacebookMessengerServiceConfiguration;
									if (messengerConfiguration != null)
										jService["token"] = messengerConfiguration.PageAccessToken;
								}
							}
						}

						var type = ((short) service.SocialServiceType).ToString();
						if (jServicesByType[type] == null)
							jServicesByType[type] = new JArray();

						var jServices = (JArray) jServicesByType[type];
						jServices.Add(jService);

#pragma warning disable CS0618 // Type or member is obsolete
						if (service.Type == ServiceTypes.Facebook || service.Type == ServiceTypes.FacebookRt)
						{
							// Se suscribe Facebook Messenger también cuando el servicio actual es Facebook
							type = ((short) SocialServiceTypes.FacebookMessenger).ToString();
							if (jServicesByType[type] == null)
								jServicesByType[type] = new JArray();

							jServices = (JArray) jServicesByType[type];
							jServices.Add(jService);
						}
#pragma warning restore CS0618 // Type or member is obsolete
					}
				}
				catch { }
			}

			return jServicesByType;
		}


		/// <summary>
		/// Envia un mensaje interactivo, agregando el mensaje al caso
		/// </summary>
		/// <param name="interactiveMessage"></param>
		/// <param name="message"></param>
		/// <returns></returns>
		private async static Task SendInteractiveMessage(InteractiveMessage interactiveMessage, DomainModel.Message message)
		{
			try
			{
				var parameters = new Dictionary<string, string>();
				parameters[DomainModel.Message.ReplyParameter] = interactiveMessage.GenerateJsonBody().ToString();
				var addMessageOptions = new Core.ActionOptions.AddMessageOptions()
				{
					ReplySource = ReplySources.System,
					Text = interactiveMessage.Body,
					Case = message.Case,
					Person = null,
					CaseId = message.Case.ID,
					CloseCase = false,
					Attachments = null,
					Parameters = parameters,
					SocialServiceType = message.SocialServiceType,
					Service = message.Service,
					SocialUserKey = message.SocialUser.Key
				};
				await Core.System.Instance.Logic.AddMessageToCaseAsync(addMessageOptions);
			} 
			catch (Exception ex)
			{
				Tracer.TraceError($"Fallo al enviar el mensaje interactivo con error: {ex}");
			}
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Inicializa el sistema de suscripción
		/// </summary>
		public static async Task Initialize()
		{
			if (initialized)
				return;

			started = false;

			var license = Licensing.LicenseManager.Instance.License.Configuration;

			if (license.ReadOnly)
			{
				initialized = true;
				return;
			}

			connectionString = DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString;

			try
			{
				await CreateServiceBusQueues();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error creando las colas para el service bus: {0}", ex);
			}

			var clientOptions = new ServiceBusClientOptions();
			clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
			clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
				ServiceBusTransportType.AmqpWebSockets :
				ServiceBusTransportType.AmqpTcp;

			Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

			client = new ServiceBusClient(connectionString, clientOptions);

			ServiceBusProcessorOptions processorOptions;

			if (!license.UseExternalServiceForIncomingMessages)
			{
				var processorSessionOptions = new ServiceBusSessionProcessorOptions()
				{
					AutoCompleteMessages = true,
					MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
					MaxConcurrentSessions = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages,
					MaxConcurrentCallsPerSession = 1,
					ReceiveMode = ServiceBusReceiveMode.PeekLock
				};

				processorSession = client.CreateSessionProcessor(license.ClientID, processorSessionOptions);
				processorSession.ProcessErrorAsync += ExceptionReceivedHandler;
				processorSession.ProcessMessageAsync += ProcessMessagesWithSessionsAsync;
			}
			else
			{
				var processorSessionOptions = new ServiceBusSessionProcessorOptions()
				{
					AutoCompleteMessages = true,
					MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
					MaxConcurrentSessions = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages,
					MaxConcurrentCallsPerSession = 1,
					ReceiveMode = ServiceBusReceiveMode.PeekLock
				};

				processorSession = client.CreateSessionProcessor($"{license.ClientID}-forwarded", processorSessionOptions);
				processorSession.ProcessErrorAsync += ExceptionReceivedHandler;
				processorSession.ProcessMessageAsync += ProcessMessagesWithSessionsAsync;
			}

			if (license.AllowedServiceTypes.Contains(ServiceTypes.WhatsApp))
			{
				if (!license.UseExternalServiceForIncomingMessages)
				{
					processorOptions = new ServiceBusProcessorOptions()
					{
						AutoCompleteMessages = true,
						MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
						MaxConcurrentCalls = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentStatuses
					};

					processorStatuses = client.CreateProcessor($"{license.ClientID}-statuses", processorOptions);
					processorStatuses.ProcessErrorAsync += ExceptionReceivedHandler;
					processorStatuses.ProcessMessageAsync += ProcessMessagesAsync;
				}

				processorOptions = new ServiceBusProcessorOptions()
				{
					AutoCompleteMessages = false,
					MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
					MaxConcurrentCalls = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMassive
				};

				processorWhatsappHSM = client.CreateProcessor($"{license.ClientID}-whatsapp-hsm", processorOptions);
				processorWhatsappHSM.ProcessErrorAsync += ExceptionReceivedHandler;
				processorWhatsappHSM.ProcessMessageAsync += ProcessMessagesWhatsappHSMAsync;

				if (license.AllowWhatsappVoiceCalls)
				{
					var processorSessionOptions = new ServiceBusSessionProcessorOptions()
					{
						AutoCompleteMessages = true,
						MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
						MaxConcurrentSessions = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentCalls,
						MaxConcurrentCallsPerSession = 1,
						ReceiveMode = ServiceBusReceiveMode.PeekLock
					};

					processorWhatsappWebRtc = client.CreateSessionProcessor($"{license.ClientID}-whatsapp-webrtc", processorSessionOptions);
					processorWhatsappWebRtc.ProcessErrorAsync += ExceptionReceivedHandler;
					processorWhatsappWebRtc.ProcessMessageAsync += ProcessWhatsappWebRtcCallsWithSessionsAsync;
				}
			}

			processorOptions = new ServiceBusProcessorOptions()
			{
				AutoCompleteMessages = true,
				MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
				MaxConcurrentCalls = 5
			};

			processorEvents = client.CreateProcessor($"{license.ClientID}-events", processorOptions);
			processorEvents.ProcessErrorAsync += ExceptionReceivedHandler;
			processorEvents.ProcessMessageAsync += ProcessEventsAsync;

			processorOptions = new Azure.Messaging.ServiceBus.ServiceBusProcessorOptions()
			{
				AutoCompleteMessages = false,
				MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
				MaxConcurrentCalls = 1
			};

			processorReplies = client.CreateProcessor($"{license.ClientID}-replies", processorOptions);
			processorReplies.ProcessErrorAsync += ExceptionReceivedHandler;
			processorReplies.ProcessMessageAsync += ProcessRepliesAsync;

			var azureEventGridEndpoint = new Uri(DomainModel.SystemSettings.Instance.Subscription.EventGridEndpoint);
			egClient = new Azure.Messaging.EventGrid.EventGridPublisherClient(azureEventGridEndpoint, new Azure.AzureKeyCredential(DomainModel.SystemSettings.Instance.Subscription.EventGridCredentials));

			Core.System.Instance.ScheduledWork += DoScheduledWork;

			initialized = true;
		}

		/// <summary>
		/// Retorna el estado de ejecución de cada procesador de service bus
		/// </summary>
		/// <returns>Un <see cref="Dictionary{ServiceBusServices, bool}"/> indiccando por cada procesador si está procesando o no</returns>
		public static Dictionary<ServiceBusServices, bool> RetrieveServiceBusProcessorStatus()
		{
			var result = new Dictionary<ServiceBusServices, bool>();
			result[ServiceBusServices.Messages] = processorSession != null && processorSession.IsProcessing;
			result[ServiceBusServices.Statuses] = processorStatuses != null && processorStatuses.IsProcessing;
			result[ServiceBusServices.WhatsappHSM] = processorWhatsappHSM != null && processorWhatsappHSM.IsProcessing;
			result[ServiceBusServices.Events] = processorEvents != null && processorEvents.IsProcessing;
			result[ServiceBusServices.Replies] = processorReplies != null && processorReplies.IsProcessing;
			result[ServiceBusServices.WhatsappWebRtc] = processorWhatsappWebRtc != null && processorWhatsappWebRtc.IsProcessing;
			return result;
		}

		/// <summary>
		/// Empieza el procesamiento del service bus de todos los procesadores
		/// </summary>
		public static async Task Start()
		{
			await Start(ServiceBusServices.All);
		}

		/// <summary>
		/// Empieza el procesamiento del service bus de los procesadores especificados
		/// </summary>
		/// <param name="services">Indica los servicios que se deberán detener</param>
		public static async Task Start(ServiceBusServices services)
		{
			if ((services & ServiceBusServices.Messages) == ServiceBusServices.Messages)
			{
				if (processorSession != null && !processorSession.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de mensajes del service bus");
					await processorSession.StartProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Statuses) == ServiceBusServices.Statuses)
			{
				if (processorStatuses != null && !processorStatuses.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de estados del service bus");
					await processorStatuses.StartProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.WhatsappHSM) == ServiceBusServices.WhatsappHSM)
			{
				if (processorWhatsappHSM != null && !processorWhatsappHSM.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de whatsapp hsm del service bus");
					await processorWhatsappHSM.StartProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Events) == ServiceBusServices.Events)
			{
				if (processorEvents != null && !processorEvents.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de eventos del service bus");
					await processorEvents.StartProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Replies) == ServiceBusServices.Replies)
			{
				if (processorReplies != null && !processorReplies.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de respuestas del service bus");
					await processorReplies.StartProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.WhatsappWebRtc) == ServiceBusServices.WhatsappWebRtc)
			{
				if (processorWhatsappWebRtc != null && !processorWhatsappWebRtc.IsProcessing)
				{
					Tracer.TraceInfo("Inicializando recepción de notificaciones de llamadas de whatsapp del service bus");
					await processorWhatsappWebRtc.StartProcessingAsync();
				}
			}

			started = true;
		}

		/// <summary>
		/// Detiene el procesamiento del service bus de todos los procesadores
		/// </summary>
		public static async Task Stop()
		{
			await Stop(ServiceBusServices.All);
		}

		/// <summary>
		/// Detiene el procesamiento del service bus
		/// </summary>
		/// <param name="services">Indica los servicios que se deberán detener</param>
		public static async Task Stop(ServiceBusServices services)
		{
			if ((services & ServiceBusServices.Messages) == ServiceBusServices.Messages)
			{
				if (processorSession != null && processorSession.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de mensajes del service bus");
					await processorSession.StopProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Statuses) == ServiceBusServices.Statuses)
			{
				if (processorStatuses != null && processorStatuses.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de estados del service bus");
					await processorStatuses.StopProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.WhatsappHSM) == ServiceBusServices.WhatsappHSM)
			{
				if (processorWhatsappHSM != null && processorWhatsappHSM.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de whatsapp hsm del service bus");
					await processorWhatsappHSM.StopProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Events) == ServiceBusServices.Events)
			{
				if (processorEvents != null && processorEvents.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de eventos del service bus");
					await processorEvents.StopProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.Replies) == ServiceBusServices.Replies)
			{
				if (processorReplies != null && processorReplies.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de respuestas del service bus");
					await processorReplies.StopProcessingAsync();
				}
			}

			if ((services & ServiceBusServices.WhatsappWebRtc) == ServiceBusServices.WhatsappWebRtc)
			{
				if (processorWhatsappWebRtc != null && processorWhatsappWebRtc.IsProcessing)
				{
					Tracer.TraceInfo("Deteniendo recepción de notificaciones de llamadas de whatsapp del service bus");
					await processorWhatsappWebRtc.StopProcessingAsync();
				}
			}

			started = false;
		}

		/// <summary>
		/// Finaliza el sistema de suscripción
		/// </summary>
		public static async Task Finish()
		{
			if (processorSession != null)
			{
				if (processorSession.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de mensajes del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorSession.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de mensajes del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de mensajes del service bus: {0}", ex);
					}
				}

				if (!processorSession.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de mensajes del service bus");
						await processorSession.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de mensajes del service bus: {0}", ex);
					}
				}

				await processorSession.DisposeAsync();
				processorSession = null;
			}

			if (processorStatuses != null)
			{
				if (processorStatuses.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de estados de whatsapp del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorStatuses.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de estados de whatsapp del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de estados de whatsapp del service bus: {0}", ex);
					}
				}

				if (!processorStatuses.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de estados de whatsapp del service bus");
						await processorStatuses.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de estados de whatsapp del service bus: {0}", ex);
					}
				}

				await processorStatuses.DisposeAsync();
				processorStatuses = null;
			}

			if (processorWhatsappHSM != null)
			{
				if (processorWhatsappHSM.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de hsm de whatsapp del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorWhatsappHSM.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de hsm de whatsapp del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de hsm de whatsapp del service bus: {0}", ex);
					}
				}

				if (!processorWhatsappHSM.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de hsm de whatsapp del service bus");
						await processorWhatsappHSM.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de hsm de whatsapp del service bus: {0}", ex);
					}
				}

				await processorWhatsappHSM.DisposeAsync();
				processorWhatsappHSM = null;
			}

			if (processorEvents != null)
			{
				if (processorEvents.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de eventos del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorEvents.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de eventos del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de eventos del service bus: {0}", ex);
					}
				}

				if (!processorEvents.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de eventos del service bus");
						await processorEvents.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de eventos del service bus: {0}", ex);
					}
				}

				await processorEvents.DisposeAsync();
				processorEvents = null;
			}

			if (processorReplies != null)
			{
				if (processorReplies.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de respuestas del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorReplies.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de respuestas del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de respuestas del service bus: {0}", ex);
					}
				}

				if (!processorReplies.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de respuestas del service bus");
						await processorReplies.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de respuestas del service bus: {0}", ex);
					}
				}

				await processorReplies.DisposeAsync();
				processorReplies = null;
			}

			if (processorWhatsappWebRtc != null)
			{
				if (processorWhatsappWebRtc.IsProcessing && client.TransportType == ServiceBusTransportType.AmqpWebSockets)
				{
					Tracer.TraceVerb("Deteniendo procesamiento de notificaciones de llamadas de whatsapp del service bus");
					try
					{
						using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(10)))
						{
							var start = DateTime.Now;
							await processorWhatsappWebRtc.StopProcessingAsync(cancellationTokenSource.Token);
							var end = DateTime.Now;
							Tracer.TraceVerb("Se detuvo el procesamiento de respuestas del service bus y demoró {0} segundos", end.Subtract(start).TotalSeconds);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error deteniendo el procesamiento de notificaciones de llamadas de whatsapp del service bus: {0}", ex);
					}
				}

				if (!processorWhatsappWebRtc.IsClosed)
				{
					try
					{
						Tracer.TraceVerb("Cerrando procesamiento de respuestas del service bus");
						await processorWhatsappWebRtc.CloseAsync();
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Ocurrió un error cerrando el procesamiento de notificaciones de llamadas de whatsapp del service bus: {0}", ex);
					}
				}

				await processorWhatsappWebRtc.DisposeAsync();
				processorWhatsappWebRtc = null;
			}

			if (client != null)
			{
				Tracer.TraceVerb("Cerrando cliente del service bus");
				await client.DisposeAsync();
				client = null;
			}

			if (administrationClient != null)
			{
				administrationClient = null;
			}

			started = false;
			initialized = false;
		}

		/// <summary>
		/// Devuelve si un usuario está suscripto a las notificaciones de la nube
		/// </summary>
		/// <param name="userId">El código del usuario</param>
		/// <returns><code>true</code> si el cliente está suscripto a las notificaciones; en caso contrario, <code>false</code></returns>
		public static async Task<bool> IsSubscribed()
		{
			var baseUrl = DomainModel.SystemSettings.Instance.Subscription.BaseUrl;
#if DEBUG && LOCAL
			baseUrl = "http://localhost/RTCallback/api/subscription";
#endif

			var account = Licensing.LicenseManager.Instance.License.Configuration.ClientID;

			using (var client = new HttpClient())
			{
				// Set up client timeout
				client.Timeout = TimeSpan.FromSeconds(10);

				// Build the request URL
				string requestUrl = $"{baseUrl}/{account}";

				try
				{
					// Send GET request
					using (var response = await client.GetAsync(requestUrl))
					{
						if (response.IsSuccessStatusCode)
						{
							// Read and parse the response content
							var content = await response.Content.ReadAsStringAsync();
							var jResponse = JObject.Parse(content);
							return jResponse["subscribed"].ToObject<bool>();
						}
						else
						{
							// Log unsuccessful response
							var content = await response.Content.ReadAsStringAsync();
							Common.Tracer.TraceInfo("No se pudo verificar si el usuario {0} está suscripto. Respuesta: {1}. Url: {4}. Error: {2}. Exception: {3}", account, content, response.ReasonPhrase, response.RequestMessage, requestUrl);
							return false;
						}
					}
				}
				catch (Exception ex)
				{
					Common.Tracer.TraceInfo("No se pudo verificar si el usuario {0} está suscripto. Exception: {1}. Url: {2}", account, ex.Message, requestUrl);
					return false;
				}
			}
		}

		/// <summary>
		/// Suscribe al cliente a las notificaciones de la nube informando los servicios de los cuales se quiere obtener las notificaciones
		/// </summary>
		/// <returns><code>true</code> si se pudo suscribir al cliente; en caso contrario, <code>false</code></returns>
		public static async Task<bool> Subscribe()
		{
			IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			services = services.Where(s => s != null && s.ID != 0 && s.Enabled);
			if (!await Subscribe(services))
				return false;

			return true;
		}

		/// <summary>
		/// Suscribe al cliente a las notificaciones de la nube informando los servicios de los cuales se quiere obtener las notificaciones
		/// </summary>
		/// <param name="baseUrl">La url donde se realizará la suscripción</param>
		/// <returns><code>true</code> si se pudo suscribir al cliente; en caso contrario, <code>false</code></returns>
		public static async Task<bool> Subscribe(string baseUrl)
		{
			IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			services = services.Where(s => s != null && s.ID != 0 && s.Enabled);
			if (!await Subscribe(services, baseUrl))
				return false;

			return true;
		}

		/// <summary>
		/// Suscribe al cliente a las notificaciones de la nube informando los servicios de los cuales se quiere obtener las notificaciones
		/// </summary>
		/// <param name="services">Una enumeración de <see cref="DomainModel.Service"/> con los servicios a suscribir</param>
		/// <returns><code>true</code> si se pudo suscribir al cliente; en caso contrario, <code>false</code></returns>
		public static async Task<bool> Subscribe(IEnumerable<DomainModel.Service> services)
		{
			var baseUrl = DomainModel.SystemSettings.Instance.Subscription.BaseUrl;
#if DEBUG && LOCAL
			baseUrl = "https://dev.ysocial.net/RTCallback/api/subscription";
#endif

			bool success = await Subscribe(services, baseUrl);

			if (!DomainModel.SystemSettings.Instance.Whatsapp.UrlRtNotifications.StartsWith("https://fbsocial.azurewebsites.net", StringComparison.InvariantCultureIgnoreCase) &&
				!DomainModel.SystemSettings.Instance.Whatsapp.UrlRtNotifications.StartsWith("https://callback.ysocial.net", StringComparison.InvariantCultureIgnoreCase))
			{
				baseUrl = DomainModel.SystemSettings.Instance.Whatsapp.UrlRtNotifications;
				baseUrl = baseUrl.Replace("api/whatsapp", "api/subscription");
				if (await Subscribe(services, baseUrl))
				{
					Tracer.TraceInfo("Se suscribió los servicios a la URL {0}", DomainModel.SystemSettings.Instance.Whatsapp.UrlRtNotifications);
				}
				else
				{
					Tracer.TraceInfo("No se pudo suscribir los servicios a la URL {0}", DomainModel.SystemSettings.Instance.Whatsapp.UrlRtNotifications);
				}
			}

			return success;
		}

		public static async Task<bool> Subscribe(IEnumerable<DomainModel.Service> services, string baseUrl)
		{
			var license = Licensing.LicenseManager.Instance.License.Configuration;

			// Check for read-only license
			if (license.ReadOnly)
				return true;

			// Validate input parameters
			if (services == null)
				throw new ArgumentNullException(nameof(services), "Los servicios no pueden ser null");
			if (string.IsNullOrEmpty(baseUrl))
				throw new ArgumentNullException(nameof(baseUrl), "La url base no puede ser null");

			// Build request URL
			string account = license.ClientID;
			string requestUrl = $"{baseUrl}/{account}";

			// Build the payload as a JObject
			var jSubscriptionData = new JObject
			{
				["token"] = GenerateSHA256(license.ClientID, SecretKeyForHashing),
				["services"] = GenerateServicesByType(services),
				["useServiceBus"] = true,
				["useDeveloperServiceBus"] = Licensing.LicenseManager.Instance.DevelopmentEnvironment,
				["useSeparateQueueForWhatsappStatusesInServiceBus"] = license.AllowedServiceTypes.Contains(ServiceTypes.WhatsApp),
				["useSeparateQueueForWhatsappCallsInServiceBus"] = license.AllowedServiceTypes.Contains(ServiceTypes.WhatsApp) &&
					license.AllowWhatsappVoiceCalls,
				["version"] = null
			};

			// Add version info
			try
			{
				var asm = System.Reflection.Assembly.GetExecutingAssembly();
				var fvi = System.Diagnostics.FileVersionInfo.GetVersionInfo(asm.Location);
				jSubscriptionData["version"] = fvi.FileVersion;
			}
			catch
			{
				// Handle exception silently
			}

			// Add alternative service bus connection if available
			if (!string.IsNullOrEmpty(license.AlternativeServiceBusConnectionString))
			{
				jSubscriptionData["useAlternativeServiceBus"] = true;
				jSubscriptionData["alternativeServiceBus"] = license.AlternativeServiceBusConnectionString;
			}

			// Serialize the payload to JSON
			string payloadText = jSubscriptionData.ToString();
			string hash = GenerateSHA256(payloadText, SecretKeyForHashing);

			using (var client = new HttpClient())
			{
				// Set timeout
				client.Timeout = TimeSpan.FromSeconds(10);

				// Create request content
				var content = new StringContent(payloadText, Encoding.UTF8, "application/json");

				// Add the custom header
				content.Headers.Add("x-ysocial-signature", $"sha256={hash}");

				try
				{
					// Send POST request
					using (HttpResponseMessage response = await client.PostAsync(requestUrl, content))
					{
						if (response.IsSuccessStatusCode)
						{
							Common.Tracer.TraceInfo("Se suscribió al cliente {0} a las notificaciones de la nube", license.ClientID);
						}
						else
						{
							// Log unsuccessful response
							var responseContent = await response.Content.ReadAsStringAsync();
							Common.Tracer.TraceInfo(
								"No se pudo suscribir el usuario {0}. Respuesta: {1}. Url: {4}. Error: {2}. Exception: {3}",
								license.ClientID, responseContent, response.ReasonPhrase, response.RequestMessage, requestUrl);
							return false;
						}
					}
				}
				catch (Exception ex)
				{
					// Log exception
					Common.Tracer.TraceInfo(
						"No se pudo suscribir el usuario {0}. Exception: {1}. Url: {2}",
						license.ClientID, ex.Message, requestUrl);
					return false;
				}
			}

			try
			{
				var json = jSubscriptionData.ToString(Newtonsoft.Json.Formatting.Indented);

				var egEvent = new Azure.Messaging.EventGrid.EventGridEvent("subscription_updated", license.ClientID, "1.0", json);
				await egClient.SendEventAsync(egEvent);

				Common.Tracer.TraceInfo("Se notificó a Event Grid el evento de cambios en suscripción del cliente {0}", license.ClientID);
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("No se pudo notificar a Event Grid el evento de cambios en suscripción del cliente {0}: {1}", license.ClientID, ex);
			}

			return true;
		}

		/// <summary>
		/// Devuelve la cantidad de mensajes encolados que hay en este momento en el service bus
		/// </summary>
		/// <returns>La cantidad de mensajes encolados</returns>
		public static async Task<long> GetEnqueuedMessages()
		{
			if (!initialized)
				throw new InvalidOperationException("Debe invocarse primero al Initialize");

			if (administrationClient == null)
			{
				administrationClient = new Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient(connectionString);
			}

			var result = await administrationClient.GetQueueRuntimePropertiesAsync(Licensing.LicenseManager.Instance.License.Configuration.ClientID);

			return result.Value.ActiveMessageCount;
		}

		/// <summary>
		/// Devuelve la información de la configuración de la cola en el service bus
		/// </summary>
		/// <returns>Un <see cref="QueueProperties"/> con la información de la cola</returns>
		public static async Task<QueueProperties> GetQueueProperties()
		{
			if (!initialized)
				throw new InvalidOperationException("Debe invocarse primero al Initialize");

			if (administrationClient == null)
			{
				administrationClient = new Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient(connectionString);
			}

			var result = await administrationClient.GetQueueAsync(Licensing.LicenseManager.Instance.License.Configuration.ClientID);

			return new QueueProperties(result.Value);
		}

		/// <summary>
		/// Actualiza la cantidad de mensajes concurrentes que puede procesar el service bus
		/// </summary>
		/// <param name="maxConcurrentCalls"></param>
		public static void ChangeConcurrentMessages(int maxConcurrentCalls)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages)
				return;

			processorSession.UpdateConcurrency(maxConcurrentCalls, 1);
		}

		/// <summary>
		/// Actualiza la cantidad de estados de mensajes concurrentes que puede procesar el service bus
		/// </summary>
		/// <param name="maxConcurrentCalls"></param>
		public static void ChangeConcurrentStatuses(int maxConcurrentCalls)
		{
			if (processorStatuses != null)
			{
				processorStatuses.UpdateConcurrency(maxConcurrentCalls);
			}
		}

		/// <summary>
		/// Actualiza la cantidad de mensajes de HSM concurrentes que puede procesar el service bus
		/// </summary>
		/// <param name="maxConcurrentCalls"></param>
		public static void ChangeConcurrentWhatsappHSM(int maxConcurrentCalls)
		{
			if (processorWhatsappHSM != null)
			{
				processorWhatsappHSM.UpdateConcurrency(maxConcurrentCalls);
			}
		}

		/// <summary>
		/// Actualiza la cantidad de mensajes de HSM concurrentes que puede procesar el service bus
		/// </summary>
		/// <param name="maxConcurrentCalls"></param>
		public static void ChangeConcurrentWhatsappWebRtc(int maxConcurrentCalls)
		{
			if (processorWhatsappWebRtc != null)
			{
				processorWhatsappWebRtc.UpdateConcurrency(maxConcurrentCalls, 1);
			}
		}

		/// <summary>
		/// Crea las colas del service bus
		/// </summary>
		/// <returns></returns>
		public static async Task CreateServiceBusQueues()
		{
			if (initialized)
				throw new InvalidOperationException("Ya se inicializó Initialize");

			if (administrationClient == null)
			{
				administrationClient = new Azure.Messaging.ServiceBus.Administration.ServiceBusAdministrationClient(connectionString);
			}

			var queueName = Licensing.LicenseManager.Instance.License.Configuration.ClientID;

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
					options.EnableBatchedOperations = false;
					options.RequiresSession = true;
					options.MaxDeliveryCount = 1;
					options.LockDuration = TimeSpan.FromMinutes(5);

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0}", queueName);
				}
				else
				{
					var queue = await administrationClient.GetQueueAsync(queueName);
					if (!queue.Value.RequiresSession)
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe pero no tenía habilitada sesiones. Se la borra y crea nuevamente", queueName);

						await administrationClient.DeleteQueueAsync(queueName);

						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
						options.EnableBatchedOperations = false;
						options.RequiresSession = true;
						options.MaxDeliveryCount = 1;
						options.LockDuration = TimeSpan.FromMinutes(5);

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se recreó la cola del service bus {0} con sesiones habilitadas", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo crear la cola del service bus {0} para estados de whatsapp: {1}", queueName, ex);
				throw;
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages)
			{
				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-forwarded";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
						options.EnableBatchedOperations = false;
						options.RequiresSession = true;
						options.MaxDeliveryCount = 1;
						options.LockDuration = TimeSpan.FromMinutes(5);

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para los mensajes que reenvia el procesador de mensajes externos", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para estados de whatsapp: {1}", queueName, ex);
				}
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-sync-events";

			try
			{
				if (!await administrationClient.TopicExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateTopicOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateTopicAsync(options);

					Tracer.TraceInfo("Se creó el tópico del service bus {0} para los eventos de sincronización", queueName);
				}
				else
				{
					Tracer.TraceInfo("El tópico del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para estados de whatsapp: {1}", queueName, ex);
			}


			if (Licensing.LicenseManager.Instance.License.Configuration.AllowedServiceTypes.Contains(ServiceTypes.WhatsApp))
			{
				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-statuses";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
						options.EnableBatchedOperations = false;

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para estados de whatsapp", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para estados de whatsapp: {1}", queueName, ex);
				}

				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-whatsapp-hsm";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
						options.EnableBatchedOperations = false;

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para envíos masivos de hsm de whatsapp", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para envíos masivos de hsm de whatsapp: {1}", queueName, ex);
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls)
				{
					queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-whatsapp-webrtc";

					try
					{
						if (!await administrationClient.QueueExistsAsync(queueName))
						{
							var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
							options.MaxSizeInMegabytes = 1024;
							options.DefaultMessageTimeToLive = TimeSpan.FromHours(14);
							options.EnableBatchedOperations = false;
							options.RequiresSession = true;
							options.MaxDeliveryCount = 1;
							options.LockDuration = TimeSpan.FromMinutes(5);

							await administrationClient.CreateQueueAsync(options);

							Tracer.TraceInfo("Se creó la cola del service bus {0} para recepción de datos de llamadas de whatsapp", queueName);
						}
						else
						{
							Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para recepción de datos de llamadas de whatsapp: {1}", queueName, ex);
					}
				}
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-events";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromMinutes(5);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para eventos", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para eventos de whatsapp: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-replies";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para respuestas", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para respuestas de whatsapp: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-sends";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;
					options.RequiresSession = true;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para envíos", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para envíos de whatsapp: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-template-sends";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para envíos de templates", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para envíos de templates de whatsapp: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-yflow-pendingreply-cases";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para casos pendientes de respuesta de yflow", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos pendientes de respuesta de yflow: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-surveys-cases";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para casos pendientes de respuesta de yflow", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos pendientes de respuesta de yflow: {1}", queueName, ex);
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-gateway-cases";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
					options.EnableBatchedOperations = false;

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para casos derivados al gateway", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos derivados al gateway: {1}", queueName, ex);
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow)
			{
				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-yflow-pending-cases";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
						options.EnableBatchedOperations = false;
						options.RequiresSession = true;

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para casos pendientes", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos pendientes al gateway: {1}", queueName, ex);
				}

				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-yflow-notify-closed-cases";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromDays(2);
						options.EnableBatchedOperations = false;
						options.RequiresSession = false;
						options.LockDuration = TimeSpan.FromMinutes(1);

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para notificacion de casos cerrados de yFlow", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos cerrados de yFlow: {1}", queueName, ex);
				}
			}

			if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway &&
				Licensing.LicenseManager.Instance.License.Configuration.AllowedServiceTypes != null &&
				Licensing.LicenseManager.Instance.License.Configuration.AllowedServiceTypes.Any(st => st == ServiceTypes.IntegrationChat))
			{
				queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-restchat";

				try
				{
					if (!await administrationClient.QueueExistsAsync(queueName))
					{
						var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
						options.MaxSizeInMegabytes = 1024;
						options.DefaultMessageTimeToLive = TimeSpan.FromDays(1);
						options.EnableBatchedOperations = false;
						options.RequiresSession = true;

						await administrationClient.CreateQueueAsync(options);

						Tracer.TraceInfo("Se creó la cola del service bus {0} para chat integrado", queueName);
					}
					else
					{
						Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para chat integrado: {1}", queueName, ex);
				}
			}

			queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-cases-to-close";

			try
			{
				if (!await administrationClient.QueueExistsAsync(queueName))
				{
					var options = new Azure.Messaging.ServiceBus.Administration.CreateQueueOptions(queueName);
					options.MaxSizeInMegabytes = 1024;
					options.DefaultMessageTimeToLive = TimeSpan.FromDays(2);
					options.EnableBatchedOperations = false;
					options.RequiresSession = false;
					options.LockDuration = TimeSpan.FromMinutes(1);

					await administrationClient.CreateQueueAsync(options);

					Tracer.TraceInfo("Se creó la cola del service bus {0} para notificacion de casos que deberán ser cerrados", queueName);
				}
				else
				{
					Tracer.TraceInfo("La cola del service bus {0} ya existe", queueName);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("No se pudo crear la cola del service bus {0} para casos que deberán ser cerrados: {1}", queueName, ex);
			}
		}

		public static void DoScheduledWork()
		{
			if (!Core.System.Instance.IsReady)
			{
				Tracer.TraceVerb("[Manager] El sistema todavía no está listo. No se realizan las tareas programadas");
				return;
			}

			if (!started)
			{
				Tracer.TraceVerb("[Manager] El manager todavía no fue iniciado. No se realizan las tareas programadas");
				return;
			}

			var now = DateTime.Now;
			if (lastMessageCheckDate == DateTime.MinValue ||
				DateTime.Now.Subtract(lastMessageCheckDate).TotalMinutes > 5)
			{
				if (processorSession.IsClosed)
				{
					Tracer.TraceVerb("El procesador de mensajes de la cola está cerrado");

					global::System.Threading.Tasks.Task.Run(async () =>
					{
						var processorSessionOptions = new ServiceBusSessionProcessorOptions()
						{
							AutoCompleteMessages = true,
							MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
							MaxConcurrentSessions = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages,
							MaxConcurrentCallsPerSession = 1,
							ReceiveMode = ServiceBusReceiveMode.PeekLock
						};

						processorSession = client.CreateSessionProcessor(Licensing.LicenseManager.Instance.License.Configuration.ClientID, processorSessionOptions);
						processorSession.ProcessErrorAsync += ExceptionReceivedHandler;
						processorSession.ProcessMessageAsync += ProcessMessagesWithSessionsAsync;

						await Start(ServiceBusServices.Messages);

						Tracer.TraceVerb("Se construyó e inició nuevamente el procesador de mensajes de la cola");

						lastMessageReceivedDate = DateTime.Now;
					});
				}
				else if (!processorSession.IsProcessing)
				{
					Tracer.TraceVerb("El procesador de mensajes de la cola no está procesando");

					global::System.Threading.Tasks.Task.Run(async () =>
					{
						await processorSession.StartProcessingAsync();
						Tracer.TraceVerb("Se inició nuevamente el procesador de mensajes de la cola");

						lastMessageReceivedDate = DateTime.Now;
					});
				}
				else
				{
					if (shouldCheckSubscriberMessagesInactivity)
					{
						var elapsedTime = now.Subtract(lastMessageReceivedDate);
						if (elapsedTime.TotalMinutes > 15)
						{
							Tracer.TraceVerb("Hace {0} minutos que no se reciben novedades de mensajes de la cola. Se procede a reiniciar el procesador", elapsedTime.TotalMinutes);
							global::System.Threading.Tasks.Task.Run(async () =>
							{
								var processorSessionOptions = new ServiceBusSessionProcessorOptions()
								{
									AutoCompleteMessages = true,
									MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
									MaxConcurrentSessions = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusConcurrentMessages,
									MaxConcurrentCallsPerSession = 1,
									ReceiveMode = ServiceBusReceiveMode.PeekLock
								};

								processorSession = client.CreateSessionProcessor(Licensing.LicenseManager.Instance.License.Configuration.ClientID, processorSessionOptions);
								processorSession.ProcessErrorAsync += ExceptionReceivedHandler;
								processorSession.ProcessMessageAsync += ProcessMessagesWithSessionsAsync;

								await Start(ServiceBusServices.Messages);
								Tracer.TraceVerb("Se inició nuevamente el procesador de mensajes de la cola");

								lastMessageReceivedDate = DateTime.MinValue;
							});
						}
						else if (elapsedTime.TotalMinutes > 5)
						{
							Tracer.TraceVerb("Hace {0} minutos que no se reciben novedades de mensajes de la cola", elapsedTime.TotalMinutes);
						}
					}
				}

				lastMessageCheckDate = DateTime.Now;
			}
		}

		#endregion
	}
}