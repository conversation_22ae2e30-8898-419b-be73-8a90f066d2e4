using System;
using System.Collections.Generic;

namespace Yoizen.Social.DomainModel.Settings
{
    public class YUsageSettings : BaseSettings
    {
        #region Properties

        /// <summary>
        /// Devuelve el prefijo que se utiliza para identificar las configuraciones de la clase
        /// </summary>
        protected override string SettingsPrefix { get { return "YUsageSettings"; } }

		/// <summary>
		/// Devuelve o establece si se utilizará yUsage
		/// </summary>
		[LocalizedDescription("YUsageSettings_Enabled")]
		public bool Enabled { get; set; }

		/// <summary>
		/// Devuelve o establece la URL donde se encuentra el servicio
		/// </summary>
		[LocalizedDescription("YUsageSettings_Url")]
		public string Url { get; set; }

		/// <summary>
		/// Devuelve o establece la URL donde se encuentra el servicio
		/// </summary>
		[LocalizedDescription("YUsageSettings_UrlApi")]
		public string UrlApi { get; set; }

		/// <summary>
		/// Devuelve o establece el token de acceso
		/// </summary>
		[LocalizedDescription("YUsageSettings_AccessToken")]
		public string AccessToken { get; set; }

		/// <summary>
		/// Devuelve o establece el timeout para invocar a yUsage (en segundos)
		/// </summary>
		[LocalizedDescription("YUsageSettings_Timeout")]
		public int Timeout { get; set; }

		/// <summary>
		/// Devuelve o establece el nombre de la cola para enviar los mensajes del centralizador
		/// </summary>
		[LocalizedDescription("YUsageSettings_QueueName")]
		public string QueueName { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Inicializa una nueva instancia de YUsageSettings
        /// </summary>
        public YUsageSettings()
        {
            this.Timeout = 20;
            this.Enabled = false;
            this.Url = "https://yusage-qa.ysocial.net/";
			this.QueueName = "yusage";
        }

        #endregion

        #region Internal Methods

        /// <summary>
        /// Inicializa los parámetros de la clase a partir de los datos del parámetro data
        /// </summary>
        internal override void Load(Dictionary<string, object> data)
        {
            base.Load(data);

            if (data.ContainsKey("YUsageSettings.Enabled"))
                this.Enabled = (bool)data["YUsageSettings.Enabled"];
            if (data.ContainsKey("YUsageSettings.Url"))
                this.Url = (string)data["YUsageSettings.Url"];
			if (data.ContainsKey("YUsageSettings.UrlApi"))
				this.UrlApi = (string)data["YUsageSettings.UrlApi"];
			if (data.ContainsKey("YUsageSettings.Timeout"))
                this.Timeout = (int)data["YUsageSettings.Timeout"];
            if (data.ContainsKey("YUsageSettings.AccessToken"))
                this.AccessToken = (string)data["YUsageSettings.AccessToken"];
			if (data.ContainsKey("YUsageSettings.QueueName"))
				this.QueueName = (string) data["YUsageSettings.QueueName"];
		}

        /// <summary>
        /// Persiste los datos de configuración dentro del diccionario especificado
        /// </summary>
        internal override void Save(Dictionary<string, object> data, bool omitIgnores)
        {
            if (data == null)
                throw new ArgumentNullException("data", "El diccionario no puede ser null");

            data.Add("YUsageSettings.Enabled", this.Enabled);
            data.Add("YUsageSettings.Url", this.Url);
            data.Add("YUsageSettings.UrlApi", this.UrlApi);
			data.Add("YUsageSettings.Timeout", this.Timeout);
            data.Add("YUsageSettings.AccessToken", this.AccessToken);
            data.Add("YUsageSettings.QueueName", this.QueueName);
        }

        #endregion
    }
}