﻿<div>
    <div class="row"
         ng-if="outgoingWhatsappCtrl.canValidatePhoneNumber">
        <div class="col-xs-12">
            <div class="form-group">
                <button-checkbox state-model="outgoingWhatsappCtrl.validatePhoneNumber"
                                 description="'OUTGOINGMESSAGE_WHATSAPP_USER_VALIDATE'"></button-checkbox>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'OUTGOINGMESSAGE_WHATSAPP_USER' | translate }}</label>
                <input type="tel"
                       class="form-control input-sm"
                       ng-model="outgoingWhatsappCtrl.message.phoneNumber"
                       ng-change="outgoingWhatsappCtrl.searchSocialUserPhoneNumber()"
                       ng-model-options="{ debounce: 500}"/>
            </div>
        </div>
    </div>
    <div class="row"
         ng-if="!outgoingWhatsappCtrl.validatePhoneNumber && outgoingWhatsappCtrl.invalidPhoneNumber">
        <div class="col-xs-12">
            <div class="alert alert-warning" role="alert" style="display: flex; flex-direction: row; align-items: center;">
                <span class="fa fa-exclamation-circle"
                      aria-hidden="true"></span>
                <span class="sr-only">{{'INFORMATION' | translate}}</span>
                <div style="margin-left: 2px;">
                    {{ 'OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE_NUMBER' | translate }}
                    <span ng-if="outgoingWhatsappCtrl.invalidPhoneNumberPossibleCountries.length > 0">
                        <br />{{ 'OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE_NUMBER_POSSIBLE_COUNTRIES' | translate }}: {{ outgoingWhatsappCtrl.invalidPhoneNumberPossibleCountries.join(', ') }}
                    </span>
                    <span ng-if="outgoingWhatsappCtrl.invalidPhoneNumberWrongType">
                        <br />{{ 'OUTGOINGMESSAGE_WHATSAPP_INVALID_PHONE_NUMBER_INVALID_TYPE' | translate }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="row"
         ng-if="outgoingWhatsappCtrl.message.socialUser !== undefined && outgoingWhatsappCtrl.message.socialUser !== null">
        <div class="col-xs-12">
            <social-user-profile social-case="outgoingWhatsappCtrl.fakeCase"
                                 view-shown-as="'div'"
                                 show-extended-info="false"
                                 show-link="false"
                                 dark-mode="false"
                                 allow-iframes="false"
                                 class="main-case-panel"
                                 is-outgoing="true">
            </social-user-profile>
        </div>
    </div>
    <div class="row"
         ng-if="outgoingWhatsappCtrl.message.userNotFound">
        <div class="col-xs-12">
            <div class="alert alert-info" role="alert">
                <span class="fa fa-exclamation-circle"
                      aria-hidden="true"></span>
                <span class="sr-only">{{'INFORMATION' | translate}}</span>
                {{ 'OUTGOINGMESSAGE_WHATSAPP_USER_NOT_FOUND' | translate }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'OUTGOINGMESSAGE_WHATSAPP_SERVICE' | translate }}</label>
                <select class="form-control input-sm"
                        ng-model="outgoingWhatsappCtrl.message.serviceId"
                        ng-change="outgoingWhatsappCtrl.serviceChanged()"
                        ng-options="service.id as service.displayText for service in outgoingWhatsappCtrl.services"></select>
            </div>
        </div>
    </div>

    <div class="row"
         ng-if="!outgoingWhatsappCtrl.serviceHasTemplates && !outgoingWhatsappCtrl.availableToAnswer">
        <div class="col-xs-12">
            <div class="alert alert-info"
                 role="alert">
                <span class="fa fa-exclamation-circle"
                      aria-hidden="true"></span>
                <span class="sr-only">{{'INFORMATION' | translate}}</span>
                {{ 'OUTGOINGMESSAGE_WHATSAPP_NO_TEMPLATE' | translate }}
            </div>
        </div>
    </div>
    <div class="row"
         ng-if="outgoingWhatsappCtrl.normalAnswerAndHsm && outgoingWhatsappCtrl.serviceHasTemplates">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'TYPE_OF_ANSWER' | translate }}:</label>
                <select class="form-control input-sm"
                        ng-model="outgoingWhatsappCtrl.message.answerType"
                        ng-change="outgoingWhatsappCtrl.typeOfAnswerChanged()"
                        ng-options="option.id as option.name for option in outgoingWhatsappCtrl.answerTypes">
                    <option value="">{{ 'TYPE_OF_ANSWER' | translate }}</option>
                </select>
            </div>
        </div>
    </div>
    <div class="row"
         ng-if="outgoingWhatsappCtrl.message.serviceId !== undefined && outgoingWhatsappCtrl.message.serviceId !== null && outgoingWhatsappCtrl.sendTemplates && outgoingWhatsappCtrl.serviceHasTemplates">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'WHATSAPP_TEMPLATE' | translate }}</label>
                <div class="form-group form-group-flex">
                    <ng-dropdown-multiselect id="dropdownTemplates"
                                             events="onItemSelect"
                                             options="outgoingWhatsappCtrl.templates"
                                             selected-model="outgoingWhatsappCtrl.templatesSelectionContainer"
                                             extra-settings="{
                         selectionLimit: 1,
                         showCheckAll: false,
                         showUncheckAll: false,
                         closeOnSelect: true,
                         closeOnDeselect: true,
                         smartButtonMaxItems: 1,
                         scrollable: true,
                         scrollableHeight: 180,
                         enableSearch: true,
                         clearSearchOnClose: true,
                         styleActive: true
                       }"
                                             translation-texts="{
                         buttonDefaultText: outgoingWhatsappCtrl.buttonDefaultText,
                         uncheckAll: outgoingWhatsappCtrl.uncheckAllText,
                         searchPlaceholder: outgoingWhatsappCtrl.searchPlaceholderText
                       }">
                    </ng-dropdown-multiselect>
                </div>
            </div>
        </div>
    </div>

    <!-- Template answer.-->
    <div ng-if="outgoingWhatsappCtrl.message.serviceId !== undefined &&
            outgoingWhatsappCtrl.message.serviceId !== null &&
            outgoingWhatsappCtrl.templatesSelectionContainer.id !== undefined &&
            outgoingWhatsappCtrl.sendTemplates">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'WHATSAPP_TEMPLATE_DEFINITION' | translate }}</label>
                <div class="row" style="display: flex; margin-bottom: 40px;">
                    <div class="col-sm-3"
                         style="display: flex;"
                         ng-if="outgoingWhatsappCtrl.message.template.headerType != 0">
                        <div class="whatsapp-hsm with-header header-text with-footer footer-text" style="width: 100%;">
                            <div class="hsm-contents">
                                <div class="hsm-header-text">
                                    <span>{{ 'WHATSAPP_TEMPLATE_HEADER' | translate }}</span>
                                </div>
                                <div class="hsm-body">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.template.headerText"></span>
                                </div>
                                <div class="hsm-footer">
                                    <span ng-if="outgoingWhatsappCtrl.message.template.headerType === 1">
                                        {{ 'WHATSAPP_TEMPLATE_HEADER-TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_HEADER-TEXT' | translate }}
                                    </span>
                                    <span ng-if="outgoingWhatsappCtrl.message.template.headerType === 2">
                                        {{ 'WHATSAPP_TEMPLATE_HEADER-TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_HEADER-MEDIA' | translate }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3"
                         style="display: flex;">
                        <div class="whatsapp-hsm with-header header-text" style="width: 100%;">
                            <div class="hsm-contents">
                                <div class="hsm-header-text">
                                    <span>{{ 'WHATSAPP_TEMPLATE_BODY' | translate }}</span>
                                </div>
                                <div class="hsm-body">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.template.template"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3"
                         style="display: flex;"
                         ng-if="outgoingWhatsappCtrl.message.template.buttons.length > 0">
                        <div class="whatsapp-hsm with-header header-text with-footer footer-text" style="width: 100%;">
                            <div class="hsm-contents">
                                <div class="hsm-header-text">
                                    <span>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }}</span>
                                </div>
                                <div class="hsm-body">
                                    <span ng-if="outgoingWhatsappCtrl.message.template.buttonsType === 1">
                                        {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_QUICK-REPLY' | translate }}
                                    </span>
                                    <span ng-if="outgoingWhatsappCtrl.message.template.buttonsType === 2">
                                        {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_CALL-TO-ACTION' | translate }}
                                    </span>
                                    <span ng-if="outgoingWhatsappCtrl.message.template.buttonsType === 3">
                                        {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_MIXED' | translate }}
                                    </span>
                                    <div ng-repeat="button in outgoingWhatsappCtrl.message.template.buttons track by $index">
                                        <span>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }} {{ $index + 1 }} {{(button.callToActionType)}}:</span>
                                        <span>{{ button.text }}</span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3"
                         style="display: flex;"
                         ng-if="outgoingWhatsappCtrl.message.template.footerType != 0">
                        <div class="whatsapp-hsm with-header header-text" style="width: 100%;">
                            <div class="hsm-contents">
                                <div class="hsm-header-text">
                                    <span>{{ 'WHATSAPP_TEMPLATE_FOOTER' | translate }}</span>
                                </div>
                                <div class="hsm-body">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.template.footerText"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'WHATSAPP_TEMPLATE_EXAMPLE' | translate }}</label>
                <div class="row">
                    <div class="col-sm-3">
                        <div class="whatsapp-hsm with-header header-text with-footer footer-text with-buttons with-buttons-2 with-buttons-3 with-buttons-all">
                            <div class="hsm-header-media">
                                <span class="fa fa-image" aria-hidden="true"></span>
                                <span class="fa fa-file-alt" aria-hidden="true"></span>
                                <span class="fa fa-play-circle" aria-hidden="true"></span>
                                <span class="fa fa-map-marker-alt" aria-hidden="true"></span>
                            </div>
                            <div class="hsm-contents">
                                <div class="hsm-header-text" ng-if="outgoingWhatsappCtrl.message.headerWithParameters.length > 0">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.headerWithParameters"></span>
                                </div>
                                <div class="hsm-body">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.templateWithParameters"></span>
                                </div>
                                <div class="hsm-footer" ng-if="typeof(outgoingWhatsappCtrl.message.template.footerText) === 'string' && outgoingWhatsappCtrl.message.template.footerText.length > 0">
                                    <span ng-bind-html="outgoingWhatsappCtrl.message.template.footerText"></span>
                                </div>
                            </div>
                            <div class="hsm-buttons">
                                <div class="hsm-button{{$index+1}}" ng-repeat="button in outgoingWhatsappCtrl.message.template.buttons | limitTo: 3 track by $index">
                                    <span class="fa fa-phone" ng-if="button.callToActionButtonType === 2" style="margin-right: 3px" aria-hidden="true"></span>
                                    <span class="fa fa-external-link" ng-if="button.callToActionButtonType === 1" style="margin-right: 3px" aria-hidden="true"></span>
                                    <span class="fa fa-copy" ng-if="button.callToActionButtonType === 3" style="margin-right: 3px" aria-hidden="true"></span>
                                    <span class="fa fa-copy" ng-if="button.authCodeButtonType === 4" style="margin-right: 3px" aria-hidden="true"></span>
                                    {{button.text}}
                                </div>
                                <div class="hsm-button-all">
                                    <span class="fa fa-list" ng-if="outgoingWhatsappCtrl.message.template.buttons.length > 3" style="margin-right: 3px" aria-hidden="true">
                                        {{ 'WHATSAPP_TEMPLATE_BUTTON_SEE-ALL-OPTIONS' | translate }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-9">
                        <div class="col-xs-4"
                             ng-if="outgoingWhatsappCtrl.message.template.headerType === 2 || (outgoingWhatsappCtrl.message.template.headerType === 1 && outgoingWhatsappCtrl.message.template.headerTextParameter !== null)">
                            <div class="form-group">
                                <label>
                                    {{ 'WHATSAPP_TEMPLATE_HEADER' | translate }}: {{ vm.message.headerWithParameters }}
                                </label>

                                <input ng-if="outgoingWhatsappCtrl.message.template.headerType === 1 && outgoingWhatsappCtrl.message.template.headerTextParameter !== null"
                                       type="text"
                                       class="form-control input-sm"
                                       ng-change="outgoingWhatsappCtrl.templateHeaderChanged()"
                                       ng-model-options="{ debounce: 500 }"
                                       ng-model="outgoingWhatsappCtrl.message.inputHeaderText"/>

                                <div ng-if="outgoingWhatsappCtrl.message.template.headerType === 2">
                                    <select class="btn btn-flat btn-default"
                                            id="mediaTypeSelct"
                                            ng-model="outgoingWhatsappCtrl.mediaInputSelect">
                                        <option value="url">URL</option>
                                        <option value="file">{{ 'FILE' | translate}}</option>
                                    </select>

                                    <div ng-if="outgoingWhatsappCtrl.mediaInputSelect === 'url'">
                                        <label>{{ 'ATTACH_NAME' | translate }}</label>
                                        <input type="text"
                                               class="form-control input-sm"
                                               ng-model-options="{ debounce: 500 }"
                                               ng-model="outgoingWhatsappCtrl.message.templateHeader.media[outgoingWhatsappCtrl.message.templateHeader.media.type].filename"/>
                                        <label>{{ 'ATTACH_LINK' | translate}}</label>
                                        <input type="text"
                                               class="form-control input-sm"
                                               ng-model-options="{ debounce: 500 }"
                                               ng-model="outgoingWhatsappCtrl.message.templateHeader.media[outgoingWhatsappCtrl.message.templateHeader.media.type].url"/>
                                        <br />
                                        <button-checkbox state-model="outgoingWhatsappCtrl.isPublicUrl"
                                                         description="'IS_PUBLIC_URL'">
                                        </button-checkbox>
                                    </div>

                                    <a class="btn btn-flat btn-default"
                                       ng-if="outgoingWhatsappCtrl.mediaInputSelect === 'file'"
                                       ng-click="outgoingWhatsappCtrl.attachFiles()">
                                        <i class="fa fa-paperclip"></i>
                                        {{'ATTACH' | translate}}
                                        <span class="bold"
                                              ng-if="outgoingWhatsappCtrl.principalQueue.length > 0">({{ outgoingWhatsappCtrl.principalQueue.length
                                            }})</span>
                                    </a>
                                </div>

                            </div>
                        </div>

                        <div class="col-xs-4"
                             ng-repeat="parameter in outgoingWhatsappCtrl.templateParameters track by $index">
                            <div class="form-group">
                                <label>{{ 'WHATSAPP_TEMPLATE_PARAMETER' | translate }} {{
                                    parameter.description }} (<span class="mono">{{ parameter.name }}</span>)</label>
                                <input type="text"
                                       class="form-control input-sm"
                                       ng-change="outgoingWhatsappCtrl.templateParameterChanged($index)"
                                       ng-model-options="{ debounce: 500 }"
                                       ng-model="outgoingWhatsappCtrl.message.templateParameters[$index]"/>
                            </div>
                        </div>

                        <div class="col-xs-4"
                             ng-repeat="button in outgoingWhatsappCtrl.message.templateButtons track by $index">
                            <div class="form-group"
                                 ng-if="button.sub_type == 'dynamic' || button.type == 'quick_reply' || button.type == 'offer' || button.type == 'url'">
                                <label>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }} {{$index + 1}}</label>
                                <input type="text"
                                       class="form-control input-sm"
                                       ng-change="outgoingWhatsappCtrl.addButtonsParameters($index)"
                                       ng-model-options="{ debounce: 500 }"
                                       ng-model="outgoingWhatsappCtrl.message.templateButtonsParameters[$index]"/>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="box-widget-body-footer">
            <div style="float: left">
                <button-checkbox state-model="outgoingWhatsappCtrl.chkMarkAsPending"
                                 description="'MARK_AS_PENDING'"
                                 ng-if="outgoingWhatsappCtrl.allowAgentsToMarkCasesAsPending"></button-checkbox>
                <button-checkbox state-model="outgoingWhatsappCtrl.sendHSMIfCaseOpenAnyways"
                                 description="'WHATSAPP_SENDHSM_IF-CASEO-OPEN-ANYWAYS'"
                                 ng-if="outgoingWhatsappCtrl.message.template.allowToConfigureSendHSMIfCaseOpen"></button-checkbox>
            </div>
            <div ng-if="main.isOutgoingMessageWhatsapp">
                <button type="button"
                        class="btn btn-flat"
                        ng-click="outgoingWhatsappCtrl.send(false, true)">
                    {{ 'OUTGOINGMESSAGE_SEND_AND_CLEAN' | translate }}
                </button>
                <button type="button"
                        class="btn btn-flat btn-action"
                        ng-click="outgoingWhatsappCtrl.send(true, true)"
                        tooltip-placement="top-right"
                        title=""
                        tooltip-popup-delay="500"
                        uib-tooltip="{{ 'OUTGOINGMESSAGE_SEND_AND_BACK_TO_PREVIOUS_STATUS_TIP' | translate }}">
                    {{ 'OUTGOINGMESSAGE_SEND_AND_BACK_TO_PREVIOUS_STATUS' | translate }}
                </button>
                <button type="button"
                        class="btn btn-flat"
                        ng-click="outgoingWhatsappCtrl.close()"
                        tooltip-placement="top-right"
                        title=""
                        tooltip-popup-delay="500"
                        uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS_TIP' | translate }}">
                    {{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS' | translate }}
                </button>
            </div>
            <div ng-if="main.agentService.isOutgoingWhatsappNoChangeState">
                <button type="button"
                        class="btn btn-flat"
                        ng-click="outgoingWhatsappCtrl.send(false, false)">
                    {{ 'OUTGOINGMESSAGE_SEND_AND_CLEAN' | translate }}
                </button>
                <button type="button"
                        class="btn btn-flat btn-action"
                        ng-click="outgoingWhatsappCtrl.send(true, false)"
                        tooltip-placement="top-right"
                        title=""
                        tooltip-popup-delay="500"
                        uib-tooltip="{{ 'OUTGOINGMESSAGE_SEND_TIP' | translate }}">
                    {{ 'OUTGOINGMESSAGE_SEND' | translate }}
                </button>
                <button type="button"
                        class="btn btn-flat"
                        ng-click="outgoingWhatsappCtrl.closeNoChangeState()"
                        tooltip-placement="top-right"
                        title=""
                        tooltip-popup-delay="500"
                        uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_TIP' | translate }}">
                    {{ 'OUTGOINGMESSAGE_CLOSE' | translate }}
                </button>
            </div>
        </div>
    </div>

    <!-- Normal answer.-->
    <div class="box box-widget box-widget-reply"
         ng-if="outgoingWhatsappCtrl.availableToAnswer">
        <div class="box-widget-header">
            <span ng-if="!outgoingWhatsappCtrl.socialCase.outgoing && !outgoingWhatsappCtrl.socialCase.isMyOutgoingCases">
                <i class="fa fa-reply icon-margin"></i>
                {{'MESSAGE_ANSWER' | translate }}
            </span>
            <span ng-if="outgoingWhatsappCtrl.socialCase.outgoing || outgoingWhatsappCtrl.socialCase.isMyOutgoingCases">
                <i class="fa fa-reply icon-margin"></i>
                {{'OUTBOUND_MESSAGE' | translate }}
            </span>
        </div>
        <div class="box-widget-body">
            <form ng-submit="outgoingWhatsappCtrl.answerMessage()"
                  role="form"
                  name="form"
                  novalidate>
                <div class="row row-eq-height margin-bottom-10">
                    <div class="col-xs-12">
                        <span class="answer-title">
                            <i class="fa fa-lock"></i> {{'ANSWER' | translate}}
                        </span>
                        <textarea class="editor-whatsapp"
                                  ng-model="outgoingWhatsappCtrl.answer.principalText"
                                  autoheight rows="1"
                                  ng-trim="false"
                                  placeholder="{{ outgoingWhatsappCtrl.replyTextPlaceholder }}"
                                  required>
                        </textarea>
                        <div class="buttons-footer margin-top-10">
                            <a class="btn btn-flat btn-default"
                               href=""
                               ng-click="outgoingWhatsappCtrl.shortUrls(true)">
                                <i class="fa fa-compress"></i>
                                {{'SHORT_URLS' | translate}}
                            </a>
                            <a class="btn btn-flat btn-default"
                               href=""
                               ng-click="outgoingWhatsappCtrl.showPredefinedAnswers(true)">
                                <i class="fa fa-book"></i>
                                {{'PREDEFINED_ANSWERS' | translate}}
                            </a>
                            <a class="btn btn-flat btn-default"
                               href=""
                               ng-click="outgoingWhatsappCtrl.attachFiles(true)"
                               ng-if="outgoingWhatsappCtrl.message.service.settings.allowToSendMultimedia">
                                <i class="fa fa-paperclip"></i>
                                {{'ATTACH' | translate}}
                                <span class="bold"
                                      ng-if="outgoingWhatsappCtrl.principalQueue.length > 0">({{
                                    outgoingWhatsappCtrl.principalQueue.length }})</span>
                            </a>
                            <div class="dropdown dropup"
                                  ng-if="::outgoingWhatsappCtrl.useOldEmojiPicker">
                                <button type="button"
                                        class="btn btn-flat btn-default dropdown-toggle"
                                        data-toggle="dropdown">
                                    <span class="fa fa-lg fa-smile"></span>
                                </button>
                                <div class="dropdown-menu emoji-popup-container">
                                    <emoji-picker on-select="outgoingWhatsappCtrl.emojiClicked(item, event)"></emoji-picker>
                                </div>
                            </div>
                            <emoji-button text="outgoingWhatsappCtrl.answer.principalText"
                                          selector=".editor-whatsapp"
                                          ng-if="::!outgoingWhatsappCtrl.useOldEmojiPicker"></emoji-button>
                        </div>
                    </div>
                </div>

                <div class="actions">
                    <message-actions actions="outgoingWhatsappCtrl.answer.actions"
                                     options="outgoingWhatsappCtrl.answer.options"></message-actions>

                    <hr class="margin-top-10 margin-bottom-10"/>
                    <div class="box-widget-body-footer">
                        <button-checkbox state-model="outgoingWhatsappCtrl.chkMarkAsPending"
                                         description="'MARK_AS_PENDING'"
                                         ng-if="outgoingWhatsappCtrl.allowAgentsToMarkCasesAsPending"></button-checkbox>
                        <div class="btn-group">
                            <button type="button"
                                    class="btn btn-flat btn-action dropdown-toggle"
                                    data-toggle="dropdown"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                <span>{{ 'SEND' | translate }}</span>
                                <span class="caret caret-up"></span>
                            </button>
                            <ul class="dropdown-menu drop-up"
                                ng-class="{ 'drop-left': true }">
                                <li>
                                    <a ng-click="outgoingWhatsappCtrl.answerMessage(false)">
                                        <span class="bold">{{ 'SEND' | translate }}</span>
                                    </a>
                                </li>
                                <li role="separator"
                                    class="divider"></li>
                                <li>
                                    <a ng-click="outgoingWhatsappCtrl.answerMessage(true)">
                                        <span>{{ 'SEND_AND_CLOSE_CASE' | translate}}</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="box-widget-body-footer"
         ng-if="outgoingWhatsappCtrl.templatesSelectionContainer.id == null || outgoingWhatsappCtrl.templatesSelectionContainer.id == undefined">
        <button type="button"
                class="btn btn-flat"
                ng-if="main.isOutgoingMessageWhatsapp"
                ng-click="outgoingWhatsappCtrl.close()"
                tooltip-placement="top-right"
                title=""
                tooltip-popup-delay="500"
                uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS_TIP' | translate }}">
            {{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS' | translate }}
        </button>
        <button type="button"
                class="btn btn-flat"
                ng-if="main.agentService.isOutgoingWhatsappNoChangeState"
                ng-click="outgoingWhatsappCtrl.closeNoChangeState()"
                tooltip-placement="top-right"
                title=""
                tooltip-popup-delay="500"
                uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_TIP' | translate }}">
            {{ 'OUTGOINGMESSAGE_CLOSE' | translate }}
        </button>
    </div>
</div>
