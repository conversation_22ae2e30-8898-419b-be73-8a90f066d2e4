﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="14.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">10.0</VisualStudioVersion>
    <SignAssembly>False</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Yoizen.Social.DB</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{4031901d-a235-4d47-a537-527532c0b065}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql110DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>Yoizen.Social.DB</RootNamespace>
    <AssemblyName>Yoizen.Social.DB</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>True</SqlServerVerification>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <GenerateCreateScript>True</GenerateCreateScript>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!-- VS10 without SP1 will not have VisualStudioVersion set, so do that here -->
  <PropertyGroup />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="dbo\Functions\" />
    <Folder Include="dbo\Stored Procedures\" />
    <Folder Include="dbo\User Defined Types\" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="Script.PostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\UsersQueues.sql" />
    <Build Include="dbo\Tables\UsersProfiles.sql" />
    <Build Include="dbo\Tables\UsersPermissions.sql" />
    <Build Include="dbo\Tables\Users.sql" />
    <Build Include="dbo\Tables\Templates.sql" />
    <Build Include="dbo\Tables\Tags.sql" />
    <Build Include="dbo\Tables\SystemSettings.sql" />
    <Build Include="dbo\Tables\SocialUsers.sql" />
    <Build Include="dbo\Tables\SocialServiceTypes.sql" />
    <Build Include="dbo\Tables\ServiceTypes.sql" />
    <Build Include="dbo\Tables\Services.sql" />
    <Build Include="dbo\Tables\QueuesTags.sql" />
    <Build Include="dbo\Tables\QueueSortTypes.sql" />
    <Build Include="dbo\Tables\Queues.sql" />
    <Build Include="dbo\Tables\ProfilesPermissions.sql" />
    <Build Include="dbo\Tables\Profiles.sql" />
    <Build Include="dbo\Tables\PersonTypes.sql" />
    <Build Include="dbo\Tables\Persons.sql" />
    <Build Include="dbo\Tables\Permissions.sql" />
    <Build Include="dbo\Tables\MessageStatuses.sql" />
    <Build Include="dbo\Tables\MessagesLog.sql" />
    <Build Include="dbo\Tables\Messages.sql" />
    <Build Include="dbo\Tables\MessageLogTypes.sql" />
    <Build Include="dbo\Tables\MessageDistributionTypes.sql" />
    <Build Include="dbo\Tables\HistSessions.sql" />
    <Build Include="dbo\Tables\HistDailyTags.sql" />
    <Build Include="dbo\Tables\HistDailyServices.sql" />
    <Build Include="dbo\Tables\HistDaily.sql" />
    <Build Include="dbo\Tables\Filters.sql" />
    <Build Include="dbo\Tables\Conversations.sql" />
    <Build Include="dbo\Tables\AuxReasons.sql" />
    <Build Include="dbo\Tables\AgentsQueues.sql" />
    <Build Include="dbo\Tables\Agents.sql" />
    <Build Include="dbo\Views\vUsers.sql" />
    <Build Include="dbo\Views\vAgents.sql" />
    <Build Include="dbo\Functions\fPersonIsAgent.sql" />
    <Build Include="dbo\Functions\Split.sql" />
    <Build Include="dbo\Stored Procedures\UsersQueues_Insert.sql" />
    <Build Include="dbo\Stored Procedures\UsersQueues_DeleteByQueue.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfiles_Insert.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfiles_DeleteByUser.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfiles_DeleteByProfile.sql" />
    <Build Include="dbo\Stored Procedures\UsersPermissions_Insert.sql" />
    <Build Include="dbo\Stored Procedures\UsersPermissions_DeleteByUser.sql" />
    <Build Include="dbo\Stored Procedures\Users_Update.sql" />
    <Build Include="dbo\Stored Procedures\Users_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetOneByUsername.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAllByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAllByProfile.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Users_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Templates_Update.sql" />
    <Build Include="dbo\Stored Procedures\Templates_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Templates_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Tags_Update.sql" />
    <Build Include="dbo\Stored Procedures\Tags_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetAllByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Tags_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Tags_CanBeDeleted.sql" />
    <Build Include="dbo\Stored Procedures\SystemSettings_Update.sql" />
    <Build Include="dbo\Stored Procedures\SystemSettings_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_Insert.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetOneByName.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByVIPStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByFilter.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByBlockedStatus.sql" />
    <Build Include="dbo\Stored Procedures\Services_UpdateStatus.sql" />
    <Build Include="dbo\Stored Procedures\Services_Update.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetOneByName.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetAllByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Services_Enable.sql" />
    <Build Include="dbo\Stored Procedures\QueuesTags_Insert.sql" />
    <Build Include="dbo\Stored Procedures\QueuesTags_DeleteByTag.sql" />
    <Build Include="dbo\Stored Procedures\QueuesTags_DeleteByQueue.sql" />
    <Build Include="dbo\Stored Procedures\QueueSortTypes_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Queues_Update.sql" />
    <Build Include="dbo\Stored Procedures\Queues_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByTag.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByAgentWithDistributionValue.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByAgent.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Queues_Exists.sql" />
    <Build Include="dbo\Stored Procedures\Queues_Enable.sql" />
    <Build Include="dbo\Stored Procedures\ProfilesPermissions_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ProfilesPermissions_DeleteByProfile.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_GetOneByName.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_Exists.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Persons_Login.sql" />
    <Build Include="dbo\Stored Procedures\Persons_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Persons_Exists.sql" />
    <Build Include="dbo\Stored Procedures\Persons_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Persons_ChangePassword.sql" />
    <Build Include="dbo\Stored Procedures\Permissions_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\Permissions_GetAllByProfile.sql" />
    <Build Include="dbo\Stored Procedures\Permissions_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\MessagesLog_GetAllByMessage.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateSocialMessage.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateDelivered.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Send.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SaveFilter.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ResetReservedAgent.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ResetDeliveryError.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ReserveForAgent.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Reply.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ReleaseFromAgent.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MoveToQueue.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsRead.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsImportant.sql" />
    <Build Include="dbo\Stored Procedures\Messages_InsertReply.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Group.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllUnassigned.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Enqueue.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Discard.sql" />
    <Build Include="dbo\Stored Procedures\Messages_AssignToQueue.sql" />
    <Build Include="dbo\Stored Procedures\Messages_AssignToAgent.sql" />
    <Build Include="dbo\Stored Procedures\HistSessions_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServices_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Filters_UpdateOrder.sql" />
    <Build Include="dbo\Stored Procedures\Filters_Update.sql" />
    <Build Include="dbo\Stored Procedures\Filters_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Filters_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Filters_GetAllByService.sql" />
    <Build Include="dbo\Stored Procedures\Filters_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Filters_Enable.sql" />
    <Build Include="dbo\Stored Procedures\Filters_Delete.sql" />
    <Build Include="dbo\Stored Procedures\AuxReasons_Update.sql" />
    <Build Include="dbo\Stored Procedures\AuxReasons_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\AgentsQueues_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentsQueues_DeleteByQueues.sql" />
    <Build Include="dbo\Stored Procedures\AgentsQueues_DeleteByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Agents_UpdateDistribution.sql" />
    <Build Include="dbo\Stored Procedures\Agents_Update.sql" />
    <Build Include="dbo\Stored Procedures\Agents_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetOneByUsername.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAllBySupervisor.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAllByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Agents_Disconnect.sql" />
    <Build Include="dbo\Stored Procedures\Agents_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Agents_Connect.sql" />
    <Build Include="dbo\Stored Procedures\Agents_ChangeConnectionStatus.sql" />
    <Build Include="dbo\Tables\SocialUserProfilesSocialUserBusinessProfiles.sql" />
    <Build Include="dbo\Tables\SocialUserProfiles.sql" />
    <Build Include="dbo\Tables\SocialUserBusinessProfiles.sql" />
    <Build Include="dbo\Functions\SplitText.sql" />
    <Build Include="dbo\Tables\CaseStatuses.sql" />
    <Build Include="dbo\Tables\CasesTags.sql" />
    <Build Include="dbo\Tables\CasesMessages.sql" />
    <Build Include="dbo\Tables\CasesLog.sql" />
    <Build Include="dbo\Tables\Cases.sql" />
    <Build Include="dbo\Tables\CaseLogTypes.sql" />
    <Build Include="dbo\Stored Procedures\Messages_InsertMissing.sql" />
    <Build Include="dbo\Tables\Attachments.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_Insert.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByProfile.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_VIP.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_UpdateBusinessData.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetOneBySocialUser.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByVIPStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByFilter.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByBlockedStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Block.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllByCase.sql" />
    <Build Include="dbo\Tables\CaseClosingResponsibles.sql" />
    <Build Include="dbo\Stored Procedures\Cases_CreateOrContinue.sql" />
    <Build Include="dbo\Stored Procedures\Cases_Close.sql" />
    <Build Include="dbo\Stored Procedures\Cases_ApplyTag.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetAllByCase.sql" />
    <Build Include="dbo\Stored Procedures\Cases_Update.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetOneByMessage.sql" />
    <Build Include="dbo\User Defined Types\SocialUsersTableType.sql" />
    <Build Include="dbo\User Defined Types\IntTableType.sql" />
    <Build Include="dbo\User Defined Types\BigIntTableType.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Search.sql" />
    <Build Include="dbo\Stored Procedures\Cases_Search.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_FindPossibleRelated.sql" />
    <Build Include="dbo\Stored Procedures\CasesLog_GetAllByCase.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_GetAllByMessage.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllGrouped.sql" />
    <Build Include="dbo\Tables\TemplatesTags.sql" />
    <Build Include="dbo\Tables\ServicesContactReasons.sql" />
    <Build Include="dbo\Tables\ContactReasons.sql" />
    <Build Include="dbo\Tables\ChatSenders.sql" />
    <Build Include="dbo\Tables\Chats.sql" />
    <Build Include="dbo\Tables\ChatMessages.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetAllByService.sql" />
    <Build Include="dbo\Stored Procedures\Templates_Enable.sql" />
    <Build Include="dbo\Stored Procedures\ServicesContactReasons_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ServicesContactReasons_DeleteByService.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_Update.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_GetAllByService.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_Delete.sql" />
    <Build Include="dbo\Stored Procedures\ContactReasons_CanBeDeleted.sql" />
    <Build Include="dbo\Stored Procedures\Chats_Update.sql" />
    <Build Include="dbo\Stored Procedures\Chats_Search.sql" />
    <Build Include="dbo\Stored Procedures\Chats_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Chats_GetOneByMessageID.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_GetAllByChatID.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Finish.sql" />
    <Build Include="dbo\Stored Procedures\Messages_Abandon.sql" />
    <Build Include="dbo\Tables\Countries.sql" />
    <Build Include="dbo\Stored Procedures\Countries_GetAll.sql" />
    <Build Include="dbo\Tables\AgentsTemplates.sql" />
    <Build Include="dbo\Stored Procedures\TemplatesTags_Insert.sql" />
    <Build Include="dbo\Stored Procedures\TemplatesTags_DeleteByTemplate.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetAllByAgent.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetAllByTemplate.sql" />
    <Build Include="dbo\Stored Procedures\AgentsTemplates_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentsTemplates_DeleteByAgent.sql" />
    <Build Include="dbo\Tables\ChatMessageTypes.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_Search.sql" />
    <Build Include="dbo\Tables\AgentsLog.sql" />
    <Build Include="dbo\Tables\AgentLogTypes.sql" />
    <Build Include="dbo\Stored Procedures\AgentsLog_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ReturnToQueue.sql" />
    <Build Include="dbo\Stored Procedures\AgentsLog_GetAllBySession.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsFinishedRead.sql" />
    <Build Include="dbo\Stored Procedures\MessagesLog_Insert.sql" />
    <Build Include="dbo\Tables\ChatsLog.sql" />
    <Build Include="dbo\Tables\ChatLogTypes.sql" />
    <Build Include="dbo\Stored Procedures\ChatsLog_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ChatsLog_GetAllByChatID_1.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetLastByProfile.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetOneByEmail.sql" />
    <Build Include="dbo\Tables\Notifications_1.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_Update_1.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_Insert_1.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_GetAllByUser_1.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_DeleteHistory_1.sql" />
    <Build Include="dbo\Functions\fGetSocialUserBusinessProfilesInfo.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Merge.sql" />
    <Build Include="dbo\Stored Procedures\Users_CanBeDeleted.sql" />
    <Build Include="dbo\Tables\UsersLog.sql" />
    <Build Include="dbo\Tables\SystemEntityTypes.sql" />
    <Build Include="dbo\Tables\SystemActionTypes.sql" />
    <Build Include="dbo\Stored Procedures\UsersLog_Insert.sql" />
    <Build Include="dbo\Tables\SystemStatus.sql" />
    <Build Include="dbo\Stored Procedures\SystemStatus_Update.sql" />
    <Build Include="dbo\Stored Procedures\SystemStatus_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateBody.sql" />
    <Build Include="dbo\Stored Procedures\SocialServiceTypes_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdatePostedBy.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\HistSessions_ExportByAgent.sql" />
    <Build Include="dbo\Tables\SocialUsersServices.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_Insert.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_GetAllBySocialUser.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_DeleteByService.sql" />
    <Build Include="dbo\Tables\SocialUserProfilesLists.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_Insert.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_Delete.sql" />
    <Build Include="dbo\Tables\SocialUserProfilesListsSocialUserProfiles.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesListsSocialUserProfiles_GetAllByList.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_Update.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_AddProfile.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesListsSocialUserProfiles_RemoveProfile.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesListsSocialUserProfiles_GetAllByProfileID.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsFiltersProcessed.sql" />
    <Build Include="dbo\Stored Procedures\Cases_CreateOrContinueMail.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetLastestsByProfile.sql" />
    <Build Include="dbo\Tables\MessagesSegments.sql" />
    <Build Include="dbo\Stored Procedures\MessagesSegments_UpdateServiceLevel.sql" />
    <Build Include="dbo\Stored Procedures\MessagesSegments_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\MessagesSegments_GetAllByMessage.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateTimes.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_Update.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_UpdateInteractionDate.sql" />
    <Build Include="dbo\Tables\ReportsTypes.sql" />
    <Build Include="dbo\Tables\ReportsExport.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllNotGenerated.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_DeleteGenerated.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateQuickAnswer.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_GetOne.sql" />
    <Build Include="dbo\Tables\SurveysAnswers.sql" />
    <Build Include="dbo\Tables\Surveys.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_Update.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateSurvey.sql" />
    <Build Include="dbo\Stored Procedures\SurveyAnswers_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllSurveys.sql" />
    <Build Include="dbo\Stored Procedures\SurveyAnswers_GetOneByCase.sql" />
    <Build Include="dbo\Tables\SurveyStatuses.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Update.sql" />
    <Build Include="dbo\Functions\SplitUniqueIdentifier.sql" />
    <Build Include="dbo\Stored Procedures\SurveyAnswers_Search.sql" />
    <Build Include="dbo\User Defined Types\UniqueIdentifierTableType.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsersServices_GetOneBySocialUserAndService.sql" />
    <Build Include="dbo\Stored Procedures\Surveys_UpdatePublished.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateSurveySentFailed.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateSurveyIgnore.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateBodyAndAuthorized.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateAuthorized.sql" />
    <Build Include="dbo\Tables\AgentsServices.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetAllByAgent.sql" />
    <Build Include="dbo\Stored Procedures\AgentsServices_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentsServices_DeleteByAgent.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SendMail.sql" />
    <Build Include="dbo\Tables\AgentGroupsSupervisors.sql" />
    <Build Include="dbo\Tables\AgentGroupsAgents.sql" />
    <Build Include="dbo\Tables\AgentGroups.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAllByGroup.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAllByGroup.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsSupervisors_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsSupervisors_DeleteByGroup.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsAgents_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsAgents_DeleteByGroup.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_Update.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_Exists.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_Delete.sql" />
    <Build Include="dbo\Views\vAgentGroupsAgents.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsAgents_GetAllByAgent.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsAgents_DeleteByAgent.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_GetOne.sql" />
    <Build Include="dbo\Views\vAgentGroupsSupervisors.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_GetAllByUser.sql" />
    <Build Include="dbo\Tables\AgentGroupsQueues.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByAgentGroupWithDistributionValue.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByAgentGroup.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsQueues_Insert.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsQueues_DeleteByQueues.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsQueues_DeleteByQueue.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_UpdateDistribution.sql" />
    <Build Include="dbo\Views\vQueues.sql" />
    <Build Include="dbo\Stored Procedures\Users_DeleteLogical.sql" />
    <Build Include="dbo\Stored Procedures\Agents_PhysicalDeletePossible.sql" />
    <Build Include="dbo\Stored Procedures\Agents_DeleteLogical.sql" />
    <Build Include="dbo\Stored Procedures\Queues_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateMailAndAuthorized.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_Delete.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_SearchByIDs.sql" />
    <Build Include="dbo\Functions\SplitBigInt.sql" />
    <Build Include="dbo\Tables\DiscardSources.sql" />
    <Build Include="dbo\Functions\fProfileHasMessagesInQueue.sql" />
    <Build Include="dbo\Functions\fProfileHasAssignedMessages.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Unmerge.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetPreviousOfCase.sql" />
    <Build Include="dbo\Tables\HistDailyFilters.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyFilters_Search.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateClassification.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllAnsweredByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Services_Insert.sql" />
    <Build Include="dbo\Tables\HistDailyCases.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCases_Insert.sql" />
    <Build Include="dbo\Functions\InlineMax.sql" />
    <Build Include="dbo\Stored Procedures\Agents_DisconnectAll.sql" />
    <Build Include="dbo\Tables\HistDailyByDay.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllAssigned.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServices_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_Update.sql" />
    <Build Include="dbo\Tables\HistDailyTagsByDay.sql" />
    <Build Include="dbo\Tables\HistDailyServicesByDay.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDay_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDay_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDay_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDay_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_SearchGroupedByQueue.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_SearchByQueues.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_SearchByAgents.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_SearchByQueues.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_SearchByAgents.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDay_SearchByQueues.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDay_SearchByAgents.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServices_GetAllByDay.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_GetAllByDay.sql" />
    <Build Include="dbo\Stored Procedures\UsersLog_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\UsersLog_Search.sql" />
    <Build Include="dbo\Tables\ExternalIntegrations.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_Update.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_Enable.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrations_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAllClosed.sql" />
    <Build Include="dbo\Stored Procedures\Cases_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Historical_DeleteOlder.sql" />
    <Build Include="dbo\Stored Procedures\Indexes_Rebuild.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetOneDeleted.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetOneDeleted.sql" />
    <Build Include="dbo\Functions\fArrayHasValue_1.sql" />
    <Build Include="dbo\Stored Procedures\Cases_CreateChat.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_Exists.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetStatus.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDay_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServices_Search.sql" />
    <Build Include="dbo\Stored Procedures\Services_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_ConsolidateByQueue.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_ConsolidateByPerson.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SendWhatsapp.sql" />
    <Build Include="dbo\Functions\fValuesAreInArray.sql" />
    <Build Include="dbo\Tables\MessagesPayment.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SearchByPayment.sql" />
    <Build Include="dbo\Tables\TaskTypes.sql" />
    <Build Include="dbo\Tables\TaskStatuses.sql" />
    <Build Include="dbo\Tables\Tasks.sql" />
    <Build Include="dbo\Tables\TaskResults.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_Update.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_Search.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_GetFirstPending.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_ExistsProcessing.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_Exists.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_CancelAllInProgress.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdatePending.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdatePendingReplyFromCustomer.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAllPending.sql" />
    <Build Include="dbo\Tables\HistDailySurveysByDay.sql" />
    <Build Include="dbo\Tables\HistDailySurveys.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDay_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDay_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDay_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveys_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveys_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveys_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetStatus.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateNotDelivered.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_UpdateStatus.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetNextPending.sql" />
    <Build Include="dbo\Stored Procedures\Agents_ConvertToUser.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetAllByType.sql" />
    <Build Include="dbo\Stored Procedures\Templates_GetAllByQueue.sql" />
    <Build Include="dbo\Tables\HistoryPassword.sql" />
    <Build Include="dbo\Stored Procedures\HistoryPassword_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistoryPassword_GetLast.sql" />
    <Build Include="dbo\Stored Procedures\HistoryPassword_GetAllByPerson.sql" />
    <Build Include="dbo\Stored Procedures\Users_UpdateSettings.sql" />
    <Build Include="dbo\Tables\HistSessionsAgentsCasesMessages.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_MarkAsPending.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_Insert.sql" />
    <Build Include="dbo\Stored Procedures\CasesLog_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_RemoveMarkAsPending.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_GetAllMyCases.sql" />
    <Build Include="dbo\Functions\fConvertDateAndIntervalToDateTime.sql" />
    <Build Include="dbo\Stored Procedures\Chats_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetFirstOfCase.sql" />
    <Build Include="dbo\Tables\HistDailyTagsByDayByTimeZone.sql" />
    <Build Include="dbo\Tables\HistDailySurveysByDayByTimeZone.sql" />
    <Build Include="dbo\Tables\HistDailyServicesByDayByTimeZone.sql" />
    <Build Include="dbo\Tables\HistDailyByDayByTimeZone.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_GetAllByDates.sql" />
    <Build Include="dbo\Tables\TimeZones.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_SearchByQueues.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_SearchByAgents.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDayByTimeZone_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDayByTimeZone_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_GetAllByDates.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDayByTimeZone_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDayByTimeZone_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveys_GetAllByDates.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDayByTimeZone_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDayByTimeZone_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServices_GetAllByDates.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDayByTimeZone_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDayByTimeZone_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDayByTimeZone_SearchByQueues.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDayByTimeZone_SearchByAgents.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_GetAllPendingAndOpen.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_SearchGroupedByQueue.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_GetAllPendingAndOpenByCase.sql" />
    <Build Include="dbo\Stored Procedures\Agents_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_SearchByQueuesByInterval.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateProfile.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllAnsweredAndFailed.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_SearchByQueues.sql" />
    <Build Include="dbo\Tables\Sites.sql" />
    <Build Include="dbo\Tables\QueuesQueueGroups.sql" />
    <Build Include="dbo\Tables\QueueGroups.sql" />
    <Build Include="dbo\Stored Procedures\Sites_Update.sql" />
    <Build Include="dbo\Stored Procedures\Sites_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Sites_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Sites_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Sites_Delete.sql" />
    <Build Include="dbo\Stored Procedures\QueuesQueueGroups_Insert.sql" />
    <Build Include="dbo\Stored Procedures\QueuesQueueGroups_DeleteByQueueGroup.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByQueueGroups.sql" />
    <Build Include="dbo\Stored Procedures\QueueGroups_Update.sql" />
    <Build Include="dbo\Stored Procedures\QueueGroups_Insert.sql" />
    <Build Include="dbo\Stored Procedures\QueueGroups_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\QueueGroups_Delete.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_RemoveMarkAsPendingById.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_reassign.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_GetOnePendingAndOpenById.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_SearchByAgentsAdherence.sql" />
    <Build Include="dbo\Tables\ExternalIntegrationsSecurity.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_Update.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_Enable.sql" />
    <Build Include="dbo\Stored Procedures\ExternalIntegrationsSecurity_Delete.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_SearchByAgents.sql" />
    <Build Include="dbo\Tables\HistSessionsUsers.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_InvalidateAll.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_DisconnectBySessionID.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_Disconnect.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_SearchByNone.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_SearchByNone.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SendTwitter.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_CancelByExpirationTime.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAllPendingReply.sql" />
    <Build Include="dbo\Tables\HistDailyCasesByTimeZone.sql" />
    <Build Include="dbo\Tables\HistDailyCasesByInterval.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByTimeZone_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByTimeZone_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByTimeZone_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByInterval_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByInterval_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByInterval_Insert.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByInterval_GetAllByDates.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCases_Update.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCases_Search.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetAllByType.sql" />
    <Build Include="dbo\Stored Procedures\Services_ExistsByAccountID.sql" />
    <Build Include="dbo\Stored Procedures\Profile_Update.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_MarkAsRead.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_MarkAllAsRead.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_GetTotalUnreadByUser.sql" />
    <Build Include="dbo\Stored Procedures\Notifications_GetLastUnreadByUser.sql" />
    <Build Include="dbo\Stored Procedures\Messages_AddToCase.sql" />
    <Build Include="dbo\Stored Procedures\AgentsServices_GetAllByAgent.sql" />
    <Build Include="dbo\Functions\fDateDiffMilliseconds.sql" />
    <Build Include="dbo\Stored Procedures\Users_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDayByTimeZone_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTagsByDay_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDayByTimeZone_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailySurveysByDay_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDayByTimeZone_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyServicesByDay_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCasesByTimeZone_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyCases_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDayByTimeZone_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyByDay_GetAllByDate.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllByDates.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetClosedByDates.sql" />
    <Build Include="dbo\Functions\fBuildTagPath.sql" />
    <Build Include="dbo\Stored Procedures\Cases_AreMessagesToEnqueueOrAssigned.sql" />
    <Build Include="dbo\Tables\QueueGroupsUsers.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_UpdateInteractionDate.sql" />
    <Build Include="dbo\Stored Procedures\Persons_UpdateLoginInfo.sql" />
    <Build Include="dbo\Tables\MessagesTransfers.sql" />
    <Build Include="dbo\Stored Procedures\MessagesTransfers_Search.sql" />
    <Build Include="dbo\Tables\CasesReopenings.sql" />
    <Build Include="dbo\Stored Procedures\CasesReopenings_Search.sql" />
    <Build Include="dbo\Stored Procedures\UsersQueues_DeleteByUser.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_Search.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_UpdateKey.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetOneForCache.sql" />
    <Build Include="dbo\Stored Procedures\Services_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetTotalSurveysSentBySocialUserProfile.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\QueueGroups_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\Profiles_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroups_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\HistDailyTags_SearchByQueue.sql" />
    <Build Include="dbo\Stored Procedures\HistDaily_SearchByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsRepliedByYFlow.sql" />
    <Build Include="dbo\Tables\CasesMessagesPending.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsPending.sql" />
    <Build Include="dbo\Stored Procedures\CasesMessagesPending_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdatePendingMessages.sql" />
    <Build Include="dbo\Stored Procedures\SystemSettings_Delete.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateSocialConversation.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsAttended.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetCaseID.sql" />
    <Build Include="dbo\Stored Procedures\Cases_IsPendingReply.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_ResetInProgress.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsAgentsCasesMessages_GetAllMyCasesByDate.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateSurveyShouldSend.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetLastIncomingOfCase.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetOneBySocialID.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateReplyByServiceParameters.sql" />
    <Build Include="dbo\Stored Procedures\Messages_MarkAsDerived.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAllOpenBySocialUserProfile.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetDeletedById.sql" />
    <Build Include="dbo\Stored Procedures\Agents_GetAllDeleted.sql" />
    <Build Include="dbo\Stored Procedures\Cases_ExistsOpenForSocialUser.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_GetFirstProcessing.sql" />
    <Build Include="dbo\Functions\fGetJsonStringValue.sql" />
    <Build Include="dbo\User Defined Types\StringTableType.sql" />
    <Build Include="dbo\User Defined Types\StringNTableType.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateMessagesOfTask.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetLastOutgoingOfCase.sql" />
    <Build Include="dbo\Stored Procedures\MessagesSegments_Search.sql" />
    <Build Include="dbo\Stored Procedures\Cases_UpdateMarkReplyOnCloseCase.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByTesterStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_Tester.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByTesterStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllByDoNotCallStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByDoNotCallStatus.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_DoNotCall.sql" />
    <Build Include="dbo\Stored Procedures\Services_UpdateConfiguration.sql" />
    <Build Include="dbo\Stored Procedures\Messages_ResetProcessingPush.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_DeleteAllUsersFromList.sql" />
    <Build Include="dbo\Tables\ReportsScheduled.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_Exists.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_Insert.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_Update.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_Delete.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_GetAllEnabled.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_Enable.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllScheduledByUser.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_DeleteGeneratedScheduled.sql" />
    <Build Include="dbo\Stored Procedures\MessagesPayment_UpdateAnswerInfoFromHsm_1.sql" />
    <Build Include="dbo\Tables\TagGroups.sql" />
    <Build Include="dbo\Tables\TagGroupsQueues.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_Delete.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_GetAllForCache.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_Insert.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_Update.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsQueues_DeleteByTagGroup.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsQueues_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Queues_GetAllByTagGroups.sql" />
    <Build Include="dbo\Stored Procedures\Tags_GetAllByTagGroup.sql" />
    <Build Include="dbo\Tables\TagGroupsTags.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsTags_Insert.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsTags_DeleteByTagGroup.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_GetAllByQueue.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_GetAllByTag.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsTags_DeleteByTag.sql" />
    <Build Include="dbo\Stored Procedures\TagGroupsQueues_DeleteByQueue.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetLastByProfileAndService.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_SearchProfilesTasks.sql" />
    <Build Include="dbo\Stored Procedures\Messages_GetAllByTask.sql" />
    <Build Include="dbo\Stored Procedures\ReportsScheduled_GetTotalScheduledEnabled.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_UpdateName.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllByService.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_GetAllBySocialUserProfileList.sql" />
    <None Include="Actualizar _Tags_ en la tabla _Chats_.sql" />
    <Build Include="dbo\Tables\CasesToDelete.sql" />
    <Build Include="dbo\Stored Procedures\CasesToDelete_DepurationOnly.sql" />
    <Build Include="dbo\Stored Procedures\CasesToDelete_DepurationAndHistory.sql" />
    <Build Include="dbo\Tables\TasksLog.sql" />
    <Build Include="dbo\Stored Procedures\TasksLog_GetAllByTask.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SendExternalWhatsapp_1.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_Update.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_HasEnqueuedMessages.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_HasAssignedMessages.sql" />
    <Build Include="dbo\Tables\MessagesLogOld.sql" />
    <Build Include="dbo\Tables\CasesLogOld.sql" />
    <Build Include="dbo\Tables\ChatsLogOld.sql" />
    <Build Include="dbo\Stored Procedures\TagGroups_GetOne.sql" />
    <Build Include="dbo\Tables\UsersProfilesLists.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfilesLists_Insert.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfilesLists_DeleteByUser.sql" />
    <Build Include="dbo\Stored Procedures\UsersProfilesLists_DeleteByProfilesList.sql" />
    <Build Include="dbo\Stored Procedures\Users_GetAllByProfilesList.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_GetAllByUser.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfiles_UpdateParameters.sql" />
    <Build Include="dbo\Tables\SurveysAnswersOld.sql" />
    <Build Include="dbo\Tables\MessagesSegmentsOld.sql" />
    <Build Include="dbo\Tables\MessagesPaymentOld.sql" />
    <Build Include="dbo\Tables\MessagesOld.sql" />
    <Build Include="dbo\Tables\HistSessionsAgentsCasesMessagesOld.sql" />
    <Build Include="dbo\Tables\ChatsOld.sql" />
    <Build Include="dbo\Tables\ChatMessagesOld.sql" />
    <Build Include="dbo\Tables\CasesTagsOld.sql" />
    <Build Include="dbo\Tables\CasesOld.sql" />
    <Build Include="dbo\Tables\AttachmentsOld.sql" />
    <Build Include="dbo\Tables\AgentsLogOld.sql" />
    <Build Include="dbo\Functions\fInitialValueForMessages.sql" />
    <Build Include="dbo\Functions\fInitialValueForHist.sql" />
    <Build Include="dbo\Functions\fInitialValueForChats.sql" />
    <Build Include="dbo\Functions\fInitialValueForCases.sql" />
    <Build Include="dbo\Stored Procedures\Print_Unlimited.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdateStatus.sql" />
    <Build Include="dbo\Stored Procedures\MessagesPayment_ConsolidateDay.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllByFilter.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetPaginatedByDoNotCallStatus.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAgentWorkingWithCase.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllThatCanBePurged.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllScheduledThatCanBePurged.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllExternal.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllDailyThatCanBePurged.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllDaily.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_GetAllByFilter.sql" />
    <Build Include="dbo\Stored Procedures\ReportsExport_DeleteGeneratedDaily.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_Update.sql" />
    <Build Include="dbo\Stored Procedures\ChatMessages_GetTotalSizeByDate.sql" />
    <Build Include="dbo\Stored Procedures\Attachments_GetTotalSizeByDate.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_SearchByStartedDate.sql" />
    <Build Include="dbo\Stored Procedures\Cases_GetAllOpenWithoutActiveMessages.sql" />
    <Build Include="dbo\Stored Procedures\AgentGroupsSupervisors_DeleteBySupervisor.sql" />
    <Build Include="dbo\Stored Procedures\MessagesSegments_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\Messages_UpdatWorkingeServiceLevel.sql" />
    <Build Include="dbo\Tables\VideoCallEvents.sql" />
    <Build Include="dbo\Stored Procedures\VideoCallEvents_Insert.sql" />
    <Build Include="dbo\Tables\VideoCalls.sql" />
    <Build Include="dbo\Stored Procedures\VideoCalls_Search.sql" />
    <Build Include="dbo\Stored Procedures\VideoCalls_Insert.sql" />
    <Build Include="dbo\Stored Procedures\VideoCallEvents_GetOne.sql" />
    <Build Include="dbo\Tables\CallsLog.sql" />
    <Build Include="dbo\Tables\Calls.sql" />
    <Build Include="dbo\Stored Procedures\Calls_UpdateStatus.sql" />
    <Build Include="dbo\Stored Procedures\Calls_UpdateParameters.sql" />
    <Build Include="dbo\Stored Procedures\Calls_Insert.sql" />
    <Build Include="dbo\Stored Procedures\Calls_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Calls_GetAllByCase.sql" />
    <Build Include="dbo\Stored Procedures\CallsLog_Insert.sql" />
    <Build Include="dbo\Stored Procedures\CallsLog_GetAllByCall.sql" />
    <Build Include="dbo\Stored Procedures\Calls_UpdateRecording.sql" />
    <Build Include="dbo\Stored Procedures\Tasks_SearchWhatsappHSM.sql" />
    <Build Include="dbo\Stored Procedures\Permissions_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Messages_SearchWhatsappHSM.sql" />
    <Build Include="dbo\Stored Procedures\Calls_Search.sql" />
    <Build Include="dbo\Stored Procedures\HistSessionsUsers_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\Cases_HasUserProfileOpenCasesInOtherServices.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserProfilesLists_GetOneByKey.sql" />
    <Build Include="dbo\Tables\SocialUserPreferenceTypes.sql" />
    <Build Include="dbo\Tables\SocialUserPreferences.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserPreferences_Insert.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserPreferences_GetOne.sql" />
    <Build Include="dbo\Stored Procedures\SocialUserPreferences_GetAll.sql" />
    <Build Include="dbo\Stored Procedures\SocialUsers_GetAllWhatsappNumbersByDoNotCallStatus.sql" />
    <Build Include="dbo\Stored Procedures\Messages_DiscardOfClosedCases.sql" />
    <Build Include="dbo\Tables\EmailContacts.sql" />
    <Build Include="dbo\Stored Procedures\EmailContacts_Update.sql" />
    <Build Include="dbo\Stored Procedures\EmailContacts_Insert.sql" />
    <Build Include="dbo\Stored Procedures\EmailContacts_GetAllByAgentID.sql" />
    <Build Include="dbo\Stored Procedures\EmailContacts_ExistsByEmailAndAgentID.sql" />
    <Build Include="dbo\Stored Procedures\EmailContacts_DeleteByID.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="local_Social.scmp" />
    <None Include="Cloud_Test.scmp" />
    <None Include="Cloud_Demo.scmp" />
    <None Include="Cloud_Partner.scmp" />
    <None Include="Yoizen.Social.DB.publish.xml" />
    <None Include="Cloud_Lab.scmp" />
    <None Include="Cloud_Oldversion.scmp" />
    <None Include="Actualizar _CaseID_ en tabla _Messages_.sql" />
    <None Include="Actualizar _Tags_ en la tabla _Cases_.sql" />
    <None Include="Cloud_PruebasEE.scmp" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <ArtifactReference Include="$(DacPacRootPath)\Extensions\Microsoft\SQLDB\Extensions\SqlServer\110\SqlSchemas\master.dacpac">
      <HintPath>$(DacPacRootPath)\Extensions\Microsoft\SQLDB\Extensions\SqlServer\110\SqlSchemas\master.dacpac</HintPath>
      <SuppressMissingDependenciesErrors>True</SuppressMissingDependenciesErrors>
      <DatabaseVariableLiteralValue>master</DatabaseVariableLiteralValue>
    </ArtifactReference>
  </ItemGroup>
</Project>