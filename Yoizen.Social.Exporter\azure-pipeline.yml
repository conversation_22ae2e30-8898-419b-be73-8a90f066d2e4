trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - 'Yoizen.Social.Exporter/**'

pool:
  vmImage: 'windows-latest'

variables:
  - name: buildConfiguration
    value: 'Release'
  - name: buildPlatform
    value: 'x86'
  - group: StorageSecrets
  - group: global-variables

jobs:
- job: Build_ySocial_Exporter
  displayName: 'Build ySocial Exporter Service'
  steps:
  # 1. Preparar estructura de carpetas
  - script: |
      mkdir "$(Build.SourcesDirectory)\Deploy"
      mkdir "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)"
      mkdir "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\ExporterService"
    displayName: 'Crear estructura Deploy'

  - task: NuGetCommand@2
    displayName: 'NuGet Restore'
    inputs:
      command: 'restore'
      restoreSolution: '**/Yoizen.Social.sln'

  # 2. Compilación con PowerShell (solución funcional)
  - task: PowerShell@2
    displayName: 'Compilar con MSBuild'
    inputs:
      targetType: 'inline'
      script: |
        $msbuild = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
        $project = "$(Build.SourcesDirectory)\Yoizen.Social.Exporter\Yoizen.Social.Exporter.csproj"
        
        & $msbuild $project `
          /p:Configuration=$(buildConfiguration) `
          /p:Platform=$(buildPlatform) `
          /p:OutputPath="$(Build.BinariesDirectory)\$(buildConfiguration)\" `
          /p:SolutionDir="$(Build.SourcesDirectory)\" `
          /p:DeployOnBuild=false `
          /nr:false
        
        if ($LASTEXITCODE -ne 0) { exit $LASTEXITCODE }
      pwsh: false

  # 3. Copiar archivos a Deploy (reemplazo PostBuildEvent)
  - task: PowerShell@2
    displayName: 'Preparar artefactos'
    inputs:
      targetType: 'inline'
      script: |
        $source = "$(Build.BinariesDirectory)\$(buildConfiguration)\*"
        $destination = "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\ExporterService"
        
        # Copiar todo excepto .pdb, .xml y vshost
        Get-ChildItem $source -Exclude "*.pdb","*.xml","*vshost*" | Copy-Item -Destination $destination -Force
        
        # Eliminar archivo específico si es Release
        if ("$(buildConfiguration)" -eq "Release") {
          Remove-Item "$destination\DocumentFormat.OpenXml.dll" -Force -ErrorAction SilentlyContinue
        }
      pwsh: true

  # 4. Crear paquete ZIP
  - task: ArchiveFiles@2
    displayName: 'Crear ZIP de despliegue'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\ExporterService'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)\ExporterService.zip'
      replaceExistingArchive: true

  # 5. Publicar artefacto
  - task: PublishBuildArtifacts@1
    displayName: 'Publicar artefacto'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)\ExporterService.zip'
      ArtifactName: 'ExporterService'

  # 6. Opcional: Subir a Azure Blob Storage
  - task: AzureCLI@2
    displayName: 'Subir a Blob Storage'
    inputs:
      azureSubscription: 'devops-pipeline-sp'
      scriptType: 'ps'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az storage blob upload `
          --account-name "$(StorageAccountName)" `
          --container-name "$(ContainerName)" `
          --file "$(Build.ArtifactStagingDirectory)\ExporterService.zip" `
          --name "dev/ExporterService.zip" `
          --overwrite `
          --auth-mode key `
          --account-key "$(StorageAccountKey)"

- job: Notify_GoogleChat
  displayName: 'Notificar Resultado del Pipeline a Google Chat'
  dependsOn:
    - Build_ySocial_Exporter
  condition: always()
  steps:
  - checkout: none

  - task: PowerShell@2
    displayName: 'Notificar Éxito a Google Chat'
    condition: succeeded()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "✅ *Pipeline ySocial-Exporter-CI-DEV completado exitosamente*.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers

  - task: PowerShell@2
    displayName: 'Notificar Falla a Google Chat'
    condition: failed()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "❌ *Pipeline ySocial-Exporter-CI-DEV falló*. Revisar errores.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers  