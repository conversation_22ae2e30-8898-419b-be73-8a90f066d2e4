﻿<div class="box box-widget box-widget-reply">
    <div class="box-widget-header">
        <span ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing">
            <i class="fa fa-reply icon-margin"></i>
            {{'MESSAGE_ANSWER' | translate }}
        </span>
        <span ng-if="whatsappAnswerTemplateCtrl.socialCase.outgoing">
            <i class="fa fa-reply icon-margin"></i>
            {{'OUTBOUND_MESSAGE' | translate }}
        </span>
    </div>
    <div class="box-widget-body">
        <form ng-submit="whatsappAnswerTemplateCtrl.answerMessage()"
              role="form"
              name="form"
              novalidate>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>{{ 'WHATSAPP_TEMPLATE' | translate }}</label>
                        <select class="form-control input-sm"
                                ng-model="whatsappAnswerTemplateCtrl.answer.templateIndex"
                                ng-change="whatsappAnswerTemplateCtrl.templateChanged()"
                                ng-options="template.id as template.label for template in whatsappAnswerTemplateCtrl.templates">
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12" ng-if="whatsappAnswerTemplateCtrl.answer.templateIndex !== undefined && whatsappAnswerTemplateCtrl.answer.templateIndex !== null">
                <div class="form-group">
                    <label>{{ 'WHATSAPP_TEMPLATE_DEFINITION' | translate }}</label>
                    <div class="row" style="display: flex; margin-bottom: 40px;">
                        <div class="col-sm-3"
                             style="display: flex;"
                             ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType != 0">
                            <div class="whatsapp-hsm with-header header-text with-footer footer-text" style="width: 100%;">
                                <div class="hsm-contents">
                                    <div class="hsm-header-text">
                                        <span>{{ 'WHATSAPP_TEMPLATE_HEADER' | translate }}</span>
                                    </div>
                                    <div class="hsm-body">
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.template.headerText"></span>
                                    </div>
                                    <div class="hsm-footer">
                                        <span ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType === 1">
                                            {{ 'WHATSAPP_TEMPLATE_HEADER-TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_HEADER-TEXT' | translate }}
                                        </span>
                                        <span ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType === 2">
                                            {{ 'WHATSAPP_TEMPLATE_HEADER-TYPE' | translate}}: {{ 'WHATSAPP_TEMPLATE_HEADER-MEDIA' | translate }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"
                             style="display: flex;">
                            <div class="whatsapp-hsm with-header header-text" style="width: 100%;">
                                <div class="hsm-contents">
                                    <div class="hsm-header-text">
                                        <span>{{ 'WHATSAPP_TEMPLATE_BODY' | translate }}</span>
                                    </div>
                                    <div class="hsm-body">
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.template.template"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"
                             style="display: flex;"
                             ng-if="whatsappAnswerTemplateCtrl.answer.template.buttons.length > 0">
                            <div class="whatsapp-hsm with-header header-text with-footer footer-text" style="width: 100%;">
                                <div class="hsm-contents">
                                    <div class="hsm-header-text"><span>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }}</span></div>
                                    <div class="hsm-body">
                                        <p>
                                            <span ng-if="whatsappAnswerTemplateCtrl.answer.template.buttonsType === 1"> {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate
                                                }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_QUICK-REPLY' | translate }} </span>
                                            <span ng-if="whatsappAnswerTemplateCtrl.answer.template.buttonsType === 2"> {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate
                                                }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_CALL-TO-ACTION' | translate }}</span>
                                            <span ng-if="outgoingWhatsappCtrl.answer.template.buttonsType === 3">
                                                {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_MIXED' | translate }} </span>
                                            <span ng-if="whatsappAnswerTemplateCtrl.answer.template.buttonsType === 4"> {{ 'WHATSAPP_TEMPLATE_BUTTON_TYPE' | translate
                                                }}: {{ 'WHATSAPP_TEMPLATE_BUTTON_AUTH-CODE' | translate }}</span>
                                        </p>
                                        <div ng-repeat="button in whatsappAnswerTemplateCtrl.answer.template.buttons track by $index">
                                            <span>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }} {{ $index + 1 }} {{(button.callToActionType)}}:</span>
                                            <span>{{ button.text }}</span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"
                             style="display: flex;"
                             ng-if="whatsappAnswerTemplateCtrl.answer.template.footerType != 0">
                            <div class="whatsapp-hsm with-header header-text" style="width: 100%;">
                                <div class="hsm-contents">
                                    <div class="hsm-header-text">
                                        <span>{{ 'WHATSAPP_TEMPLATE_FOOTER' | translate }}</span>
                                    </div>
                                    <div class="hsm-body">
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.template.footerText"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12"
                 ng-if="whatsappAnswerTemplateCtrl.answer.templateIndex !== undefined && whatsappAnswerTemplateCtrl.answer.templateIndex !== null">
                <div class="form-group">
                    <label>{{ 'WHATSAPP_TEMPLATE_EXAMPLE' | translate }}</label>
                    <div class="row">
                        <div class="col-sm-6" style="margin-right: 20px">
                            <div class="whatsapp-hsm with-header header-text with-footer footer-text with-buttons with-buttons-2 with-buttons-3 with-buttons-all">
                                <div class="hsm-header-media">
                                    <span class="fa fa-image" aria-hidden="true"></span>
                                    <span class="fa fa-file-alt" aria-hidden="true"></span>
                                    <span class="fa fa-play-circle" aria-hidden="true"></span>
                                    <span class="fa fa-map-marker-alt" aria-hidden="true"></span>
                                </div>
                                <div class="hsm-contents">
                                    <div class="hsm-header-text"><p>
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.headerWithParameters"></span>
                                    </p></div>
                                    <div class="hsm-body"><p>
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.templateWithParameters"></span>
                                    </p></div>
                                    <div class="hsm-footer">
                                        <span ng-bind-html="whatsappAnswerTemplateCtrl.answer.template.footerText"></span>
                                    </div>
                                </div>
                                <div class="hsm-buttons">
                                    <div class="hsm-button{{$index+1}}" ng-repeat="button in whatsappAnswerTemplateCtrl.answer.template.buttons | limitTo: 3 track by $index">
                                        <span class="fa fa-phone" ng-if="button.callToActionButtonType === 2" style="margin-right: 3px" aria-hidden="true"></span>
                                        <span class="fa fa-external-link" ng-if="button.callToActionButtonType === 1" style="margin-right: 3px" aria-hidden="true"></span>
                                        <span class="fa fa-copy" ng-if="button.callToActionButtonType === 3" style="margin-right: 3px" aria-hidden="true"></span>
                                        <span class="fa fa-copy" ng-if="button.authCodeButtonType === 4" style="margin-right: 3px" aria-hidden="true"></span>
                                        {{button.text}}
                                    </div>
                                    <div class="hsm-button-all">
                                        <span class="fa fa-list" ng-if="whatsappAnswerTemplateCtrl.answer.template.buttons.length > 3" style="margin-right: 3px" aria-hidden="true">
                                            {{ 'WHATSAPP_TEMPLATE_BUTTON_SEE-ALL-OPTIONS' | translate }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-5">
                            <div class="row"
                                 ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType === 2 || (whatsappAnswerTemplateCtrl.answer.template.headerType === 1 && whatsappAnswerTemplateCtrl.answer.template.headerTextParameter !== null)">
                                <div class="form-group">
                                    <label>
                                        {{ 'WHATSAPP_TEMPLATE_HEADER' | translate }}: {{ vm.message.headerWithParameters }}
                                    </label>

                                    <input ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType === 1 && whatsappAnswerTemplateCtrl.answer.template.headerTextParameter !== null"
                                           type="text"
                                           class="form-control input-sm"
                                           ng-change="whatsappAnswerTemplateCtrl.templateHeaderChanged()"
                                           ng-model-options="{ debounce: 500 }"
                                           ng-model="whatsappAnswerTemplateCtrl.headerInputText"/>

                                    <div ng-if="whatsappAnswerTemplateCtrl.answer.template.headerType === 2">
                                        <select class="form-control input-sm"
                                                id="mediaTypeSelct"
                                                ng-model="whatsappAnswerTemplateCtrl.mediaInputSelect">
                                            <option value="url">URL</option>
                                            <option value="file">{{ 'FILE' | translate}}</option>
                                        </select>

                                        <div ng-if="whatsappAnswerTemplateCtrl.mediaInputSelect === 'url'">
                                            <label>{{ 'ATTACH_NAME' | translate}}</label>
                                            <input type="text"
                                                   class="form-control input-sm"
                                                   ng-model-options="{ debounce: 500 }"
                                                   ng-model="whatsappAnswerTemplateCtrl.answer.templateHeader.media[whatsappAnswerTemplateCtrl.answer.templateHeader.media.type].filename"/>
                                            <label>{{ 'ATTACH_LINK' | translate}}</label>
                                            <input type="text"
                                                   class="form-control input-sm"
                                                   ng-model-options="{ debounce: 500 }"
                                                   ng-model="whatsappAnswerTemplateCtrl.answer.templateHeader.media[whatsappAnswerTemplateCtrl.answer.templateHeader.media.type].url"/>
                                            <br />
                                            <button-checkbox state-model="whatsappAnswerTemplateCtrl.isPublicUrl"
                                                             description="'IS_PUBLIC_URL'">
                                            </button-checkbox>
                                        </div>

                                        <a class="btn btn-flat btn-default"
                                           ng-if="whatsappAnswerTemplateCtrl.mediaInputSelect === 'file'"
                                           ng-click="whatsappAnswerTemplateCtrl.attachFiles()">
                                            <i class="fa fa-paperclip"></i>
                                            {{'ATTACH' | translate}}
                                            <span class="bold"
                                                  ng-if="whatsappAnswerTemplateCtrl.principalQueue.length > 0">
                                                ({{ whatsappAnswerTemplateCtrl.principalQueue.length }})
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="row"
                                 ng-repeat="parameter in whatsappAnswerTemplateCtrl.templateParameters track by $index">
                                <div class="form-group">
                                    <label>
                                        {{ 'WHATSAPP_TEMPLATE_PARAMETER' | translate }} {{ parameter.description }}
                                        (<span class="mono">{{ parameter.name }}</span>)
                                    </label>
                                    <input type="text"
                                           class="form-control input-sm"
                                           ng-change="whatsappAnswerTemplateCtrl.templateParameterChanged($index)"
                                           ng-model-options="{ debounce: 500 }"
                                           ng-model="whatsappAnswerTemplateCtrl.answer.templateParameters[$index]"/>
                                </div>
                            </div>

                            <div class="row"
                                 ng-repeat="button in whatsappAnswerTemplateCtrl.answer.templateButtons track by $index">
                                <div class="form-group"
                                     ng-if="button.sub_type == 'dynamic' || button.type == 'quick_reply'  || button.type == 'offer' || button.type == 'url'">
                                    <label>{{ 'WHATSAPP_TEMPLATE_BUTTON' | translate }} {{$index + 1}}</label>
                                    <input type="text"
                                           class="form-control input-sm"
                                           ng-change="whatsappAnswerTemplateCtrl.addButtonsParameters($index)"
                                           ng-model-options="{ debounce: 500 }"
                                           ng-model="whatsappAnswerTemplateCtrl.answer.templateButtonsParameters[$index]"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="actions">
                <message-actions actions="whatsappAnswerTemplateCtrl.answer.actions"
                                 options="whatsappAnswerTemplateCtrl.answer.options"></message-actions>

                <hr class="margin-top-10 margin-bottom-10"/>

                <div class="box-widget-body-footer">
                    <button class="btn btn-flat btn-action"
                            type="button"
                            ng-click="whatsappAnswerTemplateCtrl.answerMessage(false)"
                            ng-if="!whatsappAnswerTemplateCtrl.canCloseCase">
                        <span
                                ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing && !whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'ANSWER_VERB'
                        | translate }}</span>
                        <span
                                ng-if="whatsappAnswerTemplateCtrl.socialCase.outgoing || whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'SEND'
                        | translate }}</span>
                    </button>

                    <div class="btn-group"
                         ng-if="whatsappAnswerTemplateCtrl.canCloseCase">
                        <button type="button"
                                class="btn btn-flat btn-action dropdown-toggle"
                                data-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false">
                            <span
                                    ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing && !whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'ANSWER_VERB'
                            | translate }}</span>
                            <span
                                    ng-if="whatsappAnswerTemplateCtrl.socialCase.outgoing || whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'SEND'
                            | translate }}</span>
                            <span class="caret caret-up"></span>
                        </button>
                        <ul class="dropdown-menu drop-up"
                            ng-class="{ 'drop-left': whatsappAnswerTemplateCtrl.socialCase.outgoing }">
                            <li>
                                <a ng-click="whatsappAnswerTemplateCtrl.answerMessage(false)">
                                    <span class="bold"
                                          ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing && !whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'ANSWER_VERB'
                                    | translate }}</span>
                                    <span class="bold"
                                          ng-if="whatsappAnswerTemplateCtrl.socialCase.outgoing || whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'SEND'
                                    | translate }}</span>
                                </a>
                            </li>
                            <li role="separator"
                                class="divider"></li>
                            <li>
                                <a ng-click="whatsappAnswerTemplateCtrl.answerMessage(true)">
                                    <span
                                            ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing && !whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'ANSWER_AND_CLOSE_CASE'
                                    | translate }}</span>
                                    <span
                                            ng-if="whatsappAnswerTemplateCtrl.socialCase.outgoing || whatsappAnswerTemplateCtrl.socialCase.isMyOutgoingCases">{{'SEND_AND_CLOSE_CASE'
                                    | translate }}</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <a class="btn btn-flat btn-default"
                       href=""
                       ng-click="whatsappAnswerTemplateCtrl.cancelAnswer()"
                       ng-if="!whatsappAnswerTemplateCtrl.socialCase.outgoing">
                        {{'CANCEL' | translate}}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
