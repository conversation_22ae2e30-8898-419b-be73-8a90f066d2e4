﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6756B0CE-ADD7-4F52-A0D5-88ED8DAC79DA}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Yoizen.Social.Web</RootNamespace>
    <AssemblyName>Yoizen.Social.Web</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <TargetFrameworkProfile />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\Yoizen.Social.ruleset</CodeAnalysisRuleSet>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <ExcludeGeneratedDebugSymbol>true</ExcludeGeneratedDebugSymbol>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseEncrypted|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE;ENCRYPTED</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <ExcludeApp_Data>true</ExcludeApp_Data>
    <ExcludeFilesFromDeployment>**\.svn\**\*.*</ExcludeFilesFromDeployment>
    <ExcludeFoldersFromDeployment>Properties;Helpers;</ExcludeFoldersFromDeployment>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <ExcludeGeneratedDebugSymbol>true</ExcludeGeneratedDebugSymbol>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CsQuery, Version=1.3.5.200, Culture=neutral, PublicKeyToken=ab50af63106f043f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\CsQuery.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper">
      <HintPath>..\Dependencies\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EAGetMail40, Version=4.5.1.2, Culture=neutral, PublicKeyToken=e10a0812eb29cf94, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\EAGetMail40.dll</HintPath>
    </Reference>
    <Reference Include="FluentFTP">
      <HintPath>..\Dependencies\FluentFTP.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=1.69.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Admin.Directory.directory_v1, Version=1.69.0.3700, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Google.Apis.Admin.Directory.directory_v1.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.39.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.39.0.0, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Google.Authenticator">
      <HintPath>..\Dependencies\Google.Authenticator.dll</HintPath>
    </Reference>
    <Reference Include="JWT">
      <HintPath>..\Dependencies\JWT.dll</HintPath>
    </Reference>
    <Reference Include="LinkedInNET.ApiV2, Version=2.0.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\LinkedInNET.ApiV2.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\log4net.dll</HintPath>
    </Reference>
    <Reference Include="log4net.tools">
      <HintPath>..\Dependencies\log4net.tools.dll</HintPath>
    </Reference>
    <Reference Include="MailKit, Version=1.2.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights">
      <HintPath>..\Dependencies\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.Log4NetAppender">
      <HintPath>..\Dependencies\Microsoft.ApplicationInsights.Log4NetAppender.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Client, Version=1.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\SignalR\Microsoft.AspNet.SignalR.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\SignalR\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Microsoft.Exchange.WebServices.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices.Auth">
      <HintPath>..\Dependencies\Microsoft.Exchange.WebServices.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.44.0.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit, Version=1.2.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="MySqlConnector, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d33d3e53aa5f8c92, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\MySqlConnector.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Schema">
      <HintPath>..\Dependencies\Newtonsoft.Json.Schema.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder">
      <HintPath>..\Dependencies\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet">
      <HintPath>..\Dependencies\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=106.2.1.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Sparkle.LinkedInNET.ServiceDefinition, Version=1.0.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Sparkle.LinkedInNET.ServiceDefinition.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.DirectoryServices.Protocols" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=4.0.40306.1554, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Net.IPNetwork">
      <HintPath>..\Dependencies\System.Net.IPNetwork.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http">
      <HintPath>..\Yoizen.Social.WebAgent\dependencies\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TimeZoneConverter">
      <HintPath>..\Dependencies\TimeZoneConverter.dll</HintPath>
    </Reference>
    <ProjectReference Include="..\Yoizen.Common\Yoizen.Common.csproj">
      <Project>{e50d2ee2-4fcc-4e80-8c19-b9f1933dad74}</Project>
      <Name>Yoizen.Common</Name>
    </ProjectReference>
    <Reference Include="Yoizen.Web.UI">
      <HintPath>..\Dependencies\Yoizen.Web.UI.dll</HintPath>
    </Reference>
    <Reference Include="Zxcvbn, Version=1.0.0.0, Culture=neutral, PublicKeyToken=6754854bac2b6db3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Zxcvbn.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="AccessYFlow.aspx" />
    <Content Include="AccessYUsage.aspx" />
    <Content Include="AccessYFlowContingency.aspx" />
    <Content Include="Administration\AdminTasks.aspx" />
    <Content Include="Administration\AdminTasks.js" />
    <Content Include="Administration\ChangeLicense.aspx" />
    <Content Include="Administration\ChangeLicense.js" />
    <Content Include="Administration\SamlLogin.aspx" />
    <Content Include="Administration\SamlLogin.js" />
    <Content Include="Administration\YoizenLogin.js" />
    <Content Include="Administration\YoizenLogin.aspx" />
    <Content Include="Administration\UpdateSystem.aspx" />
    <Content Include="Administration\UpdateSystem.js" />
    <Content Include="Administration\UserInfo.aspx" />
    <Content Include="Administration\RTUsers.aspx" />
    <Content Include="Administration\RTUsers.js" />
    <Content Include="Administration\ServiceStatus.aspx" />
    <Content Include="Administration\ServiceStatus.js" />
    <Content Include="Administration\MyProfile.aspx" />
    <Content Include="Administration\MyProfile.js" />
    <Content Include="Administration\Profiles.js" />
    <Content Include="Administration\Users.js" />
    <Content Include="App_Themes\Avaya\fa-all.css" />
    <Content Include="App_Themes\Avaya\fa-all.min.css">
      <DependentUpon>fa-all.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Fonts.min.css">
      <DependentUpon>Fonts.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\popover.css">
      <DependentUpon>popover.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\popover.min.css">
      <DependentUpon>popover.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\tooltip.css">
      <DependentUpon>tooltip.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\tooltip.min.css">
      <DependentUpon>tooltip.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\favicon.ico" />
    <Content Include="App_Themes\Avaya\Fonts.css" />
    <Content Include="App_Themes\Avaya\jquery.ui.css">
      <DependentUpon>jquery.ui.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\jquery.ui.min.css">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Layout.css">
      <DependentUpon>Layout.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Layout.min.css">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Master.css">
      <DependentUpon>Master.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Master.min.css">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Nav.css">
      <DependentUpon>Nav.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Nav.min.css">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\fa-all.css" />
    <Content Include="App_Themes\Default\fa-all.min.css">
      <DependentUpon>fa-all.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\fa-all.css" />
    <Content Include="App_Themes\OmniCX\fa-all.min.css">
      <DependentUpon>fa-all.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Fonts.css" />
    <Content Include="App_Themes\OmniCX\Fonts.min.css">
      <DependentUpon>Fonts.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\jquery.ui.css">
      <DependentUpon>jquery.ui.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\jquery.ui.min.css">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Layout.css">
      <DependentUpon>Layout.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Layout.min.css">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Master.css">
      <DependentUpon>Master.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Master.min.css">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Nav.css">
      <DependentUpon>Nav.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Nav.min.css">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\favicon.ico" />
    <Content Include="App_Themes\OmniCX\popover.css">
      <DependentUpon>popover.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\popover.min.css">
      <DependentUpon>popover.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\tooltip.css">
      <DependentUpon>tooltip.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\tooltip.min.css">
      <DependentUpon>tooltip.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Fonts.css" />
    <Content Include="App_Themes\Default\Fonts.min.css">
      <DependentUpon>Fonts.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\jquery.ui.css">
      <DependentUpon>jquery.ui.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\jquery.ui.min.css">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Layout.css">
      <DependentUpon>Layout.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Layout.min.css">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Master.css">
      <DependentUpon>Master.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Master.min.css">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Nav.css">
      <DependentUpon>Nav.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Nav.min.css">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\popover.css">
      <DependentUpon>popover.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\popover.min.css">
      <DependentUpon>popover.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\tooltip.css">
      <DependentUpon>tooltip.less</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\tooltip.min.css">
      <DependentUpon>tooltip.css</DependentUpon>
    </Content>
    <Content Include="AgentInfo.aspx" />
    <Content Include="CacheViewer.aspx" />
    <Content Include="CognitiveServices\ConversationCounterExamples.aspx" />
    <Content Include="CognitiveServices\ConversationCounterExamples.js" />
    <Content Include="CognitiveServices\ConversationEntities.aspx" />
    <Content Include="CognitiveServices\ConversationEntities.js" />
    <Content Include="CognitiveServices\ConversationIntents.aspx" />
    <Content Include="CognitiveServices\ConversationIntents.js" />
    <Content Include="CognitiveServices\ConversationTest.aspx" />
    <Content Include="CognitiveServices\ConversationTest.js" />
    <Content Include="CognitiveServices\NLCTraining.js" />
    <Content Include="CognitiveServices\NLCTraining.aspx" />
    <Content Include="CognitiveServices\NLCTest.js" />
    <Content Include="CognitiveServices\NLCTest.aspx" />
    <Content Include="CognitiveServices\NLCCategories.aspx" />
    <Content Include="CognitiveServices\NotAuthorized.aspx" />
    <Content Include="CognitiveServices\MissingConfiguration.aspx" />
    <Content Include="CognitiveServices\NoPermissions.aspx" />
    <Content Include="CognitiveServices\NoLicense.aspx" />
    <Content Include="CognitiveServices\NLCCategories.js" />
    <Content Include="CognitiveServices\Usage.js" />
    <Content Include="CognitiveServices\Usage.aspx" />
    <Content Include="CognitiveServices\Plan.aspx" />
    <Content Include="CognitiveServices\Plan.js" />
    <Content Include="Configuration\AgentGroupInfo.aspx" />
    <Content Include="Configuration\AgentsGroups.aspx" />
    <Content Include="Configuration\AgentsGroups.js" />
    <Content Include="Configuration\AuxReasonsByAgents.aspx" />
    <Content Include="Configuration\AuxReasonsByAgents.js" />
    <Content Include="Configuration\AutomaticExport.aspx" />
    <Content Include="Configuration\AutomaticExport.js" />
    <Content Include="Configuration\EmailConnections.aspx" />
    <Content Include="Configuration\EmailConnections.js" />
    <Content Include="Configuration\Integrations.js" />
    <Content Include="Configuration\PredefinedAnswersByQueue.aspx" />
    <Content Include="Configuration\PredefinedAnswersByQueue.js" />
    <Content Include="Configuration\TagGroups.aspx" />
    <Content Include="Configuration\TagGroups.js" />
    <Content Include="Configuration\QueueInfo.aspx" />
    <Content Include="Configuration\QueueInfo.js" />
    <Content Include="Configuration\ServiceInfo.aspx" />
    <Content Include="Configuration\AgentInfo.aspx" />
    <Content Include="Configuration\Agents.aspx" />
    <Content Include="Configuration\Agents.js" />
    <Content Include="Configuration\AgentsQueues.aspx" />
    <Content Include="Configuration\AgentsQueues.js" />
    <Content Include="Configuration\AuxReasons.aspx" />
    <Content Include="Configuration\AuxReasons.js" />
    <Content Include="Configuration\BlackList.aspx" />
    <Content Include="Configuration\BlackList.js" />
    <Content Include="Configuration\PredefinedAnswers.aspx" />
    <Content Include="Configuration\PredefinedAnswers.js" />
    <Content Include="Configuration\Filters.aspx" />
    <Content Include="Configuration\ContactReasons.aspx" />
    <Content Include="Configuration\ContactReasons.js" />
    <Content Include="Configuration\ServicesGoogleBusiness.aspx" />
    <Content Include="Configuration\ServicesGoogleRBM.aspx" />
    <Content Include="Configuration\ServicesGoogleRBM.js" />
    <Content Include="Configuration\ServicesVideoCall.aspx" />
    <Content Include="Configuration\ServicesVideoCall.js" />
    <Content Include="Configuration\ServicesIntegrationChat.aspx" />
    <Content Include="Configuration\ServicesIntegrationChat.js" />
    <Content Include="Configuration\ServicesChat.js" />
    <Content Include="Configuration\ServicesCommon.js" />
    <Content Include="Configuration\ServicesGooglePlay.aspx" />
    <Content Include="Configuration\ServicesGooglePlay.js" />
    <Content Include="Configuration\ServicesAppleMessaging.aspx" />
    <Content Include="Configuration\ServicesAppleMessaging.js" />
    <Content Include="Configuration\ServicesGoogleBusiness.js" />
    <Content Include="Configuration\ServicesYouTube.aspx" />
    <Content Include="Configuration\ServicesYouTube.js" />
    <Content Include="Configuration\ServicesMercadoLibre.aspx" />
    <Content Include="Configuration\ServicesMercadoLibre.js" />
    <Content Include="Configuration\ServicesFacebookMessenger.js" />
    <Content Include="Configuration\ServicesFacebookMessenger.aspx" />
    <Content Include="Configuration\ServicesInstagram.js" />
    <Content Include="Configuration\ServicesFacebook.js" />
    <Content Include="Configuration\ServicesInstagram.aspx" />
    <Content Include="Configuration\ServicesLinkedin.aspx" />
    <Content Include="Configuration\ServicesLinkedin.js" />
    <Content Include="Configuration\ServicesMail.js" />
    <Content Include="Configuration\ServicesSkype.aspx" />
    <Content Include="Configuration\ServicesSkype.js" />
    <Content Include="Configuration\ServicesSMS.aspx" />
    <Content Include="Configuration\ServicesSMS.js" />
    <Content Include="Configuration\ServicesTelegram.js" />
    <Content Include="Configuration\ServicesTwitterSearches.js" />
    <Content Include="Configuration\ServicesWhatsapp.js" />
    <Content Include="Configuration\ServicesTwitter.js" />
    <Content Include="Configuration\ServicesTwitterSearches.aspx" />
    <Content Include="Configuration\ServicesFacebook.aspx" />
    <Content Include="Configuration\ServicesWhatsapp.aspx" />
    <Content Include="Configuration\ServicesTelegram.aspx" />
    <Content Include="Configuration\ServicesChat.aspx" />
    <Content Include="Configuration\ServicesMail.aspx" />
    <Content Include="Configuration\ServicesTwitter.aspx" />
    <Content Include="Configuration\QueueGroups.aspx" />
    <Content Include="Configuration\QueueGroups.js" />
    <Content Include="Configuration\FTPs.aspx" />
    <Content Include="Configuration\FTPs.js" />
    <Content Include="Configuration\Gateway.aspx" />
    <Content Include="Configuration\Gateway.js" />
    <Content Include="Configuration\SurveyItemTemplates.js" />
    <Content Include="Configuration\SurveyPreview.aspx" />
    <Content Include="Configuration\SurveyPreview.js" />
    <Content Include="Configuration\Surveys.aspx" />
    <Content Include="Configuration\Surveys.js" />
    <Content Include="Configuration\Integrations.aspx" />
    <Content Include="Configuration\Sites.aspx" />
    <Content Include="Configuration\Sites.js" />
    <Content Include="Configuration\WorkingTimes.aspx" />
    <Content Include="Configuration\WorkingTimes.js" />
    <Content Include="Configuration\Tags.js" />
    <Content Include="Configuration\SocialUserProfilesLists.aspx" />
    <Content Include="Configuration\SocialUserProfilesLists.js" />
    <Content Include="Configuration\WhiteList.aspx" />
    <Content Include="Configuration\Services.js" />
    <Content Include="Configuration\SystemSettings.js" />
    <Content Include="Configuration\Filters.js" />
    <Content Include="Configuration\Queues.aspx" />
    <Content Include="Administration\Profiles.aspx" />
    <Content Include="Configuration\Queues.js" />
    <Content Include="Configuration\Services.aspx" />
    <Content Include="Configuration\SystemSettings.aspx" />
    <Content Include="Administration\Users.aspx" />
    <Content Include="Configuration\Tags.aspx" />
    <Content Include="Configuration\WhiteList.js" />
    <Content Include="ErrorPages\400.aspx" />
    <Content Include="Images\Icons\yUsageLogo.png" />
    <Content Include="Reports\VideoInfo.aspx" />
    <Content Include="Reports\VideoInfo.js" />
    <Content Include="Reports\VideoCalls.aspx" />
    <Content Include="Whatsapp\Calls.aspx" />
    <Content Include="Reports\CallInfo.aspx" />
    <Content Include="Reports\CallInfo.js" />
    <Content Include="Reports\RTAgentsAuxReasons.js" />
    <Content Include="Reports\RTAgentsAuxReasons.aspx" />
    <Content Include="Reports\RTAssignedMessages.js" />
    <Content Include="Reports\RTAssignedMessages.aspx" />
    <Content Include="Whatsapp\Calls.js" />
    <Content Include="Reports\VideoCalls.js" />
    <Content Include="Wait.aspx" />
    <Content Include="InitError.aspx" />
    <Content Include="DownloadAgent.aspx" />
    <Content Include="Errors.aspx" />
    <Content Include="App_Themes\Default\favicon.ico" />
    <Content Include="ErrorPages\404.aspx" />
    <Content Include="Images\Acciones.png" />
    <Content Include="Images\Avaya\Avaya.png" />
    <Content Include="Images\Avaya\Product.png" />
    <Content Include="Images\Avaya\ProductFull.png" />
    <Content Include="Images\Avaya\ProductName.png" />
    <Content Include="Images\LogoYoizenWhite.png" />
    <Content Include="Images\TwitterWizardV2.png" />
    <Content Include="Images\YoizenWhatsappBroker.gif" />
    <Content Include="Images\Icons\MercadoLibre.png" />
    <Content Include="Images\MercadoLibre.png" />
    <Content Include="Images\OmniCX\Product.png" />
    <Content Include="Images\OmniCX\ProductFull.png" />
    <Content Include="Images\OmniCX\ProductName.png" />
    <Content Include="Images\OmniCX\Tecnovoz.png" />
    <Content Include="Images\Chat.png" />
    <Content Include="Images\checkbox-icon.png" />
    <Content Include="Images\dropdown-icon.png" />
    <Content Include="Images\FacebookDefaultProfileImage.png" />
    <Content Include="Images\FacebookMessenger.png" />
    <Content Include="Images\Icons\Add.png" />
    <Content Include="Images\Agents.png" />
    <Content Include="Images\AuxReasons.png" />
    <Content Include="Images\BlackList.png" />
    <Content Include="Images\CloseSmall.png" />
    <Content Include="Images\CloseSmallInverted.png" />
    <Content Include="Images\CompletionCodes.png" />
    <Content Include="Images\Facebook.png" />
    <Content Include="Images\FacebookWizard1.png" />
    <Content Include="Images\FacebookWizard2.png" />
    <Content Include="Images\Filters.png" />
    <Content Include="Images\Icons\AuxiliarLogin.png" />
    <Content Include="Images\Icons\Chat.png" />
    <Content Include="Images\Icons\Discarded.png" />
    <Content Include="Images\Google+.png" />
    <Content Include="Images\Icons\Assembly.png" />
    <Content Include="Images\Icons\Auxiliar.png" />
    <Content Include="Images\Icons\Available.png" />
    <Content Include="Images\Icons\Class.png" />
    <Content Include="Images\Icons\Delete.png" />
    <Content Include="Images\Icons\Down.png" />
    <Content Include="Images\Icons\Edit.png" />
    <Content Include="Images\Icons\Facebook.png" />
    <Content Include="Images\Icons\FacebookMessenger.png" />
    <Content Include="Images\Icons\Google+.png" />
    <Content Include="Images\Icons\Groups.png" />
    <Content Include="Images\Icons\HasAttach.png" />
    <Content Include="Images\Icons\Incoming.png" />
    <Content Include="Images\Icons\Instagram.png" />
    <Content Include="Images\Icons\Klout.png" />
    <Content Include="Images\Icons\Linkedin.png" />
    <Content Include="Images\Icons\Mail.png" />
    <Content Include="Images\Icons\Namespace.png" />
    <Content Include="Images\Icons\No.png" />
    <Content Include="Images\Icons\Outgoing.png" />
    <Content Include="Images\Icons\Password.png" />
    <Content Include="Images\Icons\Play.png" />
    <Content Include="Images\Icons\PrivateMessage.png" />
    <Content Include="Images\Icons\Queues.png" />
    <Content Include="Images\Icons\Services.png" />
    <Content Include="Images\Icons\Skype.png" />
    <Content Include="Images\Icons\SMS.png" />
    <Content Include="Images\Icons\SocialUserProfile.png" />
    <Content Include="Images\Icons\Stop.png" />
    <Content Include="Images\Icons\Stop1.png" />
    <Content Include="Images\Icons\Telegram.png" />
    <Content Include="Images\Icons\Twitter.png" />
    <Content Include="Images\Icons\TwitterSearches.png" />
    <Content Include="Images\Icons\Up.png" />
    <Content Include="Images\Icons\View.png" />
    <Content Include="Images\Icons\VIM.png" />
    <Content Include="Images\Icons\Whatsapp.png" />
    <Content Include="Images\Icons\Whitelist.png" />
    <Content Include="Images\Icons\Working.png" />
    <Content Include="Images\Icons\WorkingAvailable.png" />
    <Content Include="Images\Icons\WorkingPending.png" />
    <Content Include="Images\Icons\Yes.png" />
    <Content Include="Images\Icons\OutgoingAuto.png" />
    <Content Include="Images\Instagram.png" />
    <Content Include="Images\InstagramWizard1.png" />
    <Content Include="Images\ios9_1emoji.png" />
    <Content Include="Images\Linkedin.png" />
    <Content Include="Images\LinkedinWizard1.png" />
    <Content Include="Images\List.png" />
    <Content Include="Images\Mail.png" />
    <Content Include="Images\Polls.png" />
    <Content Include="Images\ProductFullInverse.png" />
    <Content Include="Images\Question.png" />
    <Content Include="Images\radiobutton-icon.png" />
    <Content Include="Images\Services.png" />
    <Content Include="Images\calendar_icons.png" />
    <Content Include="Images\Close.png" />
    <Content Include="Images\CloseInverted.png" />
    <Content Include="Images\Error.png" />
    <Content Include="Images\Expand.png" />
    <Content Include="Images\Groups.png" />
    <Content Include="Images\Icons\Error.png" />
    <Content Include="Images\Icons\Info.png" />
    <Content Include="Images\Icons\Warning.png" />
    <Content Include="Images\Info.png" />
    <Content Include="Images\Product.png" />
    <Content Include="Images\ProductFull.png" />
    <Content Include="Images\ProductName.png" />
    <Content Include="Images\Profiles.png" />
    <Content Include="Images\Queues.png" />
    <Content Include="Images\Skype.png" />
    <Content Include="Images\SMS.png" />
    <Content Include="Images\SocialUserProfile.png" />
    <Content Include="Images\SystemSettings.png" />
    <Content Include="Images\Tags.png" />
    <Content Include="Images\Telegram.png" />
    <Content Include="Images\Templates.png" />
    <Content Include="Images\TextFieldIcons.png" />
    <Content Include="Images\Tooltip.gif" />
    <Content Include="Images\TooltipBottom.png" />
    <Content Include="Images\TooltipMiddle.gif" />
    <Content Include="Images\TooltipRightMiddle.gif" />
    <Content Include="Images\Twitter.png" />
    <Content Include="Images\TwitterAllowDMFromEveryone.png" />
    <Content Include="Images\TwitterDefaultProfileImage.png" />
    <Content Include="Images\TwitterSearches.png" />
    <Content Include="Images\TwitterWizard1.png" />
    <Content Include="Images\TwitterWizard2.png" />
    <Content Include="Images\Users.png" />
    <Content Include="Images\Warning.png" />
    <Content Include="Images\Whatsapp.png" />
    <Content Include="Images\Whitelist.png" />
    <Content Include="Images\Yoizen.png" />
    <Content Include="License.aspx" />
    <Content Include="Login.aspx" />
    <Content Include="Logs\Web.config" />
    <Content Include="Reports\Adherence.aspx" />
    <Content Include="Reports\Adherence.js" />
    <Content Include="Reports\CasePopup.aspx" />
    <Content Include="Reports\CasesReopenings.aspx" />
    <Content Include="Reports\CasesReopenings.js" />
    <Content Include="Reports\Dashboards.css">
      <DependentUpon>Dashboards.less</DependentUpon>
    </Content>
    <Content Include="Reports\Dashboards.min.css">
      <DependentUpon>Dashboards.css</DependentUpon>
    </Content>
    <Content Include="Reports\MessagesSegments.js" />
    <Content Include="Reports\MessagesSegments.aspx" />
    <Content Include="Reports\MessagesTransfers.aspx" />
    <Content Include="Reports\MessagesTransfers.js" />
    <Content Include="Reports\Scheduled.aspx" />
    <Content Include="Reports\Scheduled.js" />
    <Content Include="Reports\UsersLoginLogout.aspx" />
    <Content Include="Reports\UsersLoginLogout.js" />
    <Content Include="Reports\LoginLogout.js" />
    <Content Include="Reports\LoginLogout.aspx" />
    <Content Include="Reports\SocialUserProfiles.aspx" />
    <Content Include="Reports\SocialUserProfiles.js" />
    <Content Include="Scripts\bowser.min.js" />
    <Content Include="Scripts\CLDRPluralRuleParser.js" />
    <Content Include="Scripts\crypto-js.min.js" />
    <Content Include="Scripts\dom-to-image.min.js" />
    <Content Include="Scripts\highcharts.sunburst.js" />
    <Content Include="Scripts\highcharts.wordcloud.js" />
    <Content Include="Scripts\dragula.min.js" />
    <Content Include="Scripts\jquery-3.5.1.min.js" />
    <Content Include="Scripts\jquery.i18n.emitter.bidi.js" />
    <Content Include="Scripts\jquery.i18n.emitter.js" />
    <Content Include="Scripts\jquery.i18n.fallbacks.js" />
    <Content Include="Scripts\jquery.i18n.js" />
    <Content Include="Scripts\jquery.i18n.language.js" />
    <Content Include="Scripts\jquery.i18n.messagestore.js" />
    <Content Include="Scripts\jquery.i18n.parser.js" />
    <Content Include="Scripts\jquery.tokeninput.js" />
    <Content Include="Scripts\leader-line.min.js" />
    <Content Include="Scripts\moment-timezone-with-data.js" />
    <Content Include="Reports\Controls\SocialUserFacebookMessengerAccount.ascx" />
    <Content Include="Reports\ConsolidatedSurveys.aspx" />
    <Content Include="Reports\ConsolidatedSurveys.js" />
    <Content Include="Reports\Services.js" />
    <Content Include="Reports\Services.aspx" />
    <Content Include="Scripts\jquery-ui.multidatespicker.js" />
    <Content Include="Scripts\jsonpath-0.8.0.js" />
    <Content Include="Scripts\Login.js" />
    <Content Include="Logout.aspx" />
    <Content Include="Logs.aspx" />
    <Content Include="Reports\AgentInfo.aspx" />
    <Content Include="Reports\Agents.aspx" />
    <Content Include="Reports\Agents.js" />
    <Content Include="Reports\AgentInfo.js" />
    <Content Include="Reports\ConsolidatedCases.aspx" />
    <Content Include="Reports\ConsolidatedCases.js" />
    <Content Include="Reports\Controls\SocialUserSkypeAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserSMSAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserChatAccount.ascx" />
    <Content Include="Reports\Case.aspx" />
    <Content Include="Reports\Controls\MailMessageControl.ascx" />
    <Content Include="Reports\Controls\SocialUserInstagramAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserWhatsAppAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserTelegramAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserMailAccount.ascx" />
    <Content Include="Reports\AttachInfo.aspx" />
    <Content Include="Reports\Chats.aspx" />
    <Content Include="Reports\Chats.js" />
    <Content Include="Reports\Exporter.aspx" />
    <Content Include="Reports\Exporter.js" />
    <Content Include="Reports\HeatMap.aspx" />
    <Content Include="Reports\HeatMap.js" />
    <Content Include="Reports\MessagesQuery.js" />
    <Content Include="Reports\RTServices.aspx" />
    <Content Include="Reports\RTServices.js" />
    <Content Include="Reports\SocialUserProfileInfo.js" />
    <Content Include="Reports\Controls\SocialUserFacebookAccount.ascx" />
    <Content Include="Reports\Controls\SocialUserTwitterAccount.ascx" />
    <Content Include="Reports\SocialUserProfileInfo.aspx" />
    <Content Include="Reports\Cases.aspx" />
    <Content Include="Reports\Cases.js" />
    <Content Include="Reports\CasesTimes.aspx" />
    <Content Include="Reports\CasesTimes.js" />
    <Content Include="Reports\CaseInfo.aspx" />
    <Content Include="Reports\CaseInfo.js" />
    <Content Include="Reports\MessageInfo.js" />
    <Content Include="Reports\Queues.js" />
    <Content Include="Reports\Queues.aspx" />
    <Content Include="Reports\Reports.js" />
    <Content Include="Reports\RTAgents.js" />
    <Content Include="Reports\RTMessages.aspx" />
    <Content Include="Reports\RTMessages.js" />
    <Content Include="Reports\RTQueues.js" />
    <Content Include="Reports\SocialUsers.aspx" />
    <Content Include="Reports\SocialUsers.js" />
    <Content Include="Reports\Surveys.aspx" />
    <Content Include="Reports\Surveys.js" />
    <Content Include="Reports\UsersLog.aspx" />
    <Content Include="Reports\UsersLog.js" />
    <Content Include="Reports\TwitterReport.aspx" />
    <Content Include="Reports\UsersLogInfo.aspx" />
    <Content Include="Reports\UsersLogInfo.js" />
    <Content Include="Reports\WordCloud.js" />
    <Content Include="Reports\MessagesQuery.aspx" />
    <Content Include="Reports\WordCloud.aspx" />
    <Content Include="Reports\UseGraph.aspx" />
    <Content Include="Reports\UseGraph.js" />
    <Content Include="Scripts\d3.layout.cloud.js" />
    <Content Include="Scripts\datatables.min.js" />
    <Content Include="Scripts\draggable.min.js" />
    <Content Include="Scripts\FileSaver.min.js" />
    <Content Include="Scripts\he.js" />
    <Content Include="Scripts\highcharts.exporting.js" />
    <Content Include="Scripts\gmaps-heatmap.js" />
    <Content Include="Scripts\heatmap.min.js" />
    <Content Include="Scripts\highcharts-3d.js" />
    <Content Include="Scripts\highcharts.data.js" />
    <Content Include="Scripts\highcharts.drilldown.js" />
    <Content Include="Scripts\highcharts.funnel.js" />
    <Content Include="Scripts\highcharts.no-data-to-display.js" />
    <Content Include="Scripts\popover.js" />
    <Content Include="Scripts\secure-random.js" />
    <Content Include="Scripts\signalr.min.js" />
    <Content Include="Scripts\socket.io.js" />
    <Content Include="Scripts\ua-parser.min.js" />
    <Content Include="Scripts\zxcvbn.js" />
    <Content Include="Styles\datatables.min.css" />
    <Content Include="Styles\Fonts\fa-brands-400.svg" />
    <Content Include="Styles\Fonts\fa-duotone-900.svg" />
    <Content Include="Styles\Fonts\fa-light-300.svg" />
    <Content Include="Styles\Fonts\fa-regular-400.svg" />
    <Content Include="Styles\Fonts\fa-solid-900.svg" />
    <Content Include="Styles\Fonts\yoizen.svg" />
    <Content Include="Styles\FullHeight.css">
      <DependentUpon>FullHeight.less</DependentUpon>
    </Content>
    <Content Include="Styles\FullHeight.min.css">
      <DependentUpon>FullHeight.css</DependentUpon>
    </Content>
    <Content Include="Styles\images\close.png" />
    <Content Include="Scripts\twemoji.min.js" />
    <Content Include="Scripts\jquery.color.js" />
    <Content Include="Scripts\jquery.fileupload-ui.js" />
    <Content Include="Scripts\jquery.fileupload.js" />
    <Content Include="Scripts\jquery.floatThead.min.js" />
    <Content Include="Scripts\jquery.ui.widget.js" />
    <Content Include="Scripts\jscolor.min.js" />
    <Content Include="Scripts\numeral.min.js" />
    <Content Include="Scripts\pnotify.custom.js" />
    <Content Include="Scripts\RTNotifications.js" />
    <Content Include="Styles\images\bar-alpha.png" />
    <Content Include="Styles\images\bar-opacity.png" />
    <Content Include="Styles\images\bar-pointer.png" />
    <Content Include="Styles\images\bar.png" />
    <Content Include="Styles\images\map-opacity.png" />
    <Content Include="Styles\images\map-pointer.png" />
    <Content Include="Styles\images\map.png" />
    <Content Include="Styles\images\preview-opacity.png" />
    <Content Include="Styles\images\ui-colorpicker.png" />
    <Content Include="Scripts\jquery.cleditor.min.js" />
    <Content Include="Styles\jquery-ui.multidatespicker.css" />
    <Content Include="Styles\jquery.colorpicker.css" />
    <Content Include="Scripts\jquery.colorpicker.js" />
    <Content Include="Scripts\jquery.dotdotdot.min.js" />
    <Content Include="Scripts\jquery.filedrop.js" />
    <Content Include="Scripts\jquery.getUrlParam.js" />
    <Content Include="Scripts\DatePicker.js" />
    <Content Include="Reports\MessageInfo.aspx" />
    <Content Include="Reports\Messages.aspx" />
    <Content Include="Reports\Messages.js" />
    <Content Include="Reports\RTAgents.aspx" />
    <Content Include="Reports\RTQueues.aspx" />
    <Content Include="Scripts\border.png" />
    <Content Include="Scripts\calendar.png" />
    <Content Include="Scripts\colorbox.css" />
    <Content Include="Scripts\controls.png" />
    <Content Include="Scripts\date.js" />
    <Content Include="Scripts\highcharts.js" />
    <Content Include="Scripts\highcharts.theme.js" />
    <Content Include="Scripts\jquery-ui-1.8.7.custom.min.js" />
    <Content Include="Scripts\jquery.colorbox-min.js" />
    <Content Include="Scripts\jquery.tablereorder.js" />
    <Content Include="Scripts\jquery.ui.selectmenu.js" />
    <Content Include="Scripts\moment-with-langs.js" />
    <Content Include="Scripts\query-builder.js" />
    <Content Include="Scripts\SocialUsers.js" />
    <Content Include="Scripts\title_notifier.js" />
    <Content Include="Scripts\tooltip.js" />
    <Content Include="Scripts\TweenMax.min.js" />
    <Content Include="Scripts\underscore-min.js" />
    <Content Include="Scripts\URI.js" />
    <Content Include="Sounds\announcement-begin.wav" />
    <Content Include="Sounds\announcement-end.wav" />
    <Content Include="Sounds\newchatmessage.mp3" />
    <Content Include="Sounds\newnotification.mp3" />
    <Content Include="Sounds\notification-squeak.wav" />
    <Content Include="Sounds\notification-up-i.wav" />
    <Content Include="Sounds\notification-up-ii.wav" />
    <Content Include="Startup.aspx" />
    <Content Include="Status.aspx" />
    <Content Include="Styles\jquery.cleditor.buttons.gif" />
    <Content Include="Styles\jquery.cleditor.css" />
    <Content Include="Styles\jquery.cleditor.toolbar.gif" />
    <Content Include="Styles\jquery.multiselect.css" />
    <Content Include="Scripts\jquery.multiselect.filter.min.js" />
    <Content Include="Scripts\jquery.multiselect.min.js" />
    <Content Include="Scripts\jquery.numeric.js" />
    <Content Include="Scripts\json2.js" />
    <Content Include="Scripts\loading.gif" />
    <Content Include="Scripts\loading_background.png" />
    <Content Include="Scripts\Master.js" />
    <Content Include="Scripts\UserSelection.js" />
    <Content Include="Default.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Styles\jquery.multiselect.filter.css">
      <DependentUpon>jquery.multiselect.filter.less</DependentUpon>
    </Content>
    <Content Include="Styles\jquery.multiselect.filter.min.css">
      <DependentUpon>jquery.multiselect.filter.css</DependentUpon>
    </Content>
    <Content Include="Styles\pnotify.custom.css" />
    <Content Include="Styles\query-builder.css" />
    <Content Include="Styles\SurveyPreview.css">
      <DependentUpon>SurveyPreview.less</DependentUpon>
    </Content>
    <Content Include="Styles\SurveyPreview.min.css">
      <DependentUpon>SurveyPreview.css</DependentUpon>
    </Content>
    <Content Include="Supervisor\InternalChat.aspx" />
    <Content Include="Supervisor\InternalChat.js" />
    <Content Include="Supervisor\Messages.aspx" />
    <Content Include="Supervisor\Messages.js" />
    <Content Include="Supervisor\Notifications.aspx" />
    <Content Include="Supervisor\Notifications.js" />
    <Content Include="Supervisor\ChatMessages.aspx" />
    <Content Include="Supervisor\ChatMessages.js" />
    <Content Include="Supervisor\PendingMessages.aspx" />
    <Content Include="Supervisor\PendingMessages.js" />
    <Content Include="Supervisor\OutgoingMessages.js" />
    <Content Include="Supervisor\OutgoingMessages.aspx" />
    <Content Include="Reports\Dashboards.aspx" />
    <Content Include="Reports\Dashboards.js" />
    <Content Include="Supervisor\ProfilesMassiveComposer.aspx" />
    <Content Include="Supervisor\Settings.aspx" />
    <Content Include="Supervisor\Settings.js" />
    <Content Include="Supervisor\ProfilesMassiveComposer.js" />
    <Content Include="Supervisor\SocialUserProfiles.aspx" />
    <Content Include="Supervisor\SocialUserProfiles.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="WebAgentUrlGenerator.aspx" />
    <Content Include="Whatsapp\Dashboards.aspx" />
    <Content Include="Whatsapp\Dashboards.css">
      <DependentUpon>Dashboards.less</DependentUpon>
    </Content>
    <Content Include="Whatsapp\Dashboards.js" />
    <Content Include="Whatsapp\Dashboards.min.css">
      <DependentUpon>Dashboards.css</DependentUpon>
    </Content>
    <Content Include="Whatsapp\EmbeddedSignup.aspx" />
    <Content Include="Whatsapp\EmbeddedSignup.js" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreationReports.aspx" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreationReports.js" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreationTask.aspx" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreationTask.js" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreation.aspx" />
    <Content Include="Whatsapp\HSMMassiveWithoutCaseCreation.js" />
    <Content Include="Whatsapp\HSMMassiveRequestTasksConfirmation.js" />
    <Content Include="Whatsapp\HSMMassiveRequestTasks.aspx" />
    <Content Include="Whatsapp\HSMMassiveRequestTasks.js" />
    <Content Include="Whatsapp\HSMMassiveTasks.aspx" />
    <Content Include="Whatsapp\HSMMassiveTasks.js" />
    <Content Include="Whatsapp\HSMMassiveComposer.aspx" />
    <Content Include="Whatsapp\HSMMassiveComposer.js" />
    <Content Include="Whatsapp\HSMComposer.aspx" />
    <Content Include="Whatsapp\HSMComposer.js" />
    <Content Include="Whatsapp\DetailedReport.aspx" />
    <Content Include="Whatsapp\DetailedReport.js" />
    <Content Include="Whatsapp\ConsolidatedReport.aspx" />
    <Content Include="Whatsapp\ConsolidatedReport.js" />
    <Content Include="Whatsapp\HSMMassiveRequestTasksConfirmation.aspx" />
    <Content Include="Whatsapp\DownloadCampaignAttachment.aspx" />
    <Content Include="Whatsapp\TaskInfoWithoutCase.js" />
    <Content Include="Whatsapp\TaskInfoWithoutCase.aspx" />
    <Content Include="Whatsapp\TaskInfo.aspx" />
    <Content Include="Whatsapp\TaskInfo.js" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\GlobalAssemblyInfo.cs">
      <Link>Properties\GlobalAssemblyInfo.cs</Link>
    </Compile>
    <Compile Include="AccessYFlow.aspx.cs">
      <DependentUpon>AccessYFlow.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AccessYFlow.aspx.designer.cs">
      <DependentUpon>AccessYFlow.aspx</DependentUpon>
    </Compile>
    <Compile Include="AccessYUsage.aspx.cs">
      <DependentUpon>AccessYUsage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AccessYUsage.aspx.designer.cs">
      <DependentUpon>AccessYUsage.aspx</DependentUpon>
    </Compile>
    <Compile Include="AccessYFlowContingency.aspx.cs">
      <DependentUpon>AccessYFlowContingency.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AccessYFlowContingency.aspx.designer.cs">
      <DependentUpon>AccessYFlowContingency.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\AdminTasks.aspx.cs">
      <DependentUpon>AdminTasks.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\AdminTasks.aspx.designer.cs">
      <DependentUpon>AdminTasks.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ChangeLicense.aspx.cs">
      <DependentUpon>ChangeLicense.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ChangeLicense.aspx.designer.cs">
      <DependentUpon>ChangeLicense.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\SamlLogin.aspx.cs">
      <DependentUpon>SamlLogin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\SamlLogin.aspx.designer.cs">
      <DependentUpon>SamlLogin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\YoizenLogin.aspx.cs">
      <DependentUpon>YoizenLogin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\YoizenLogin.aspx.designer.cs">
      <DependentUpon>YoizenLogin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\UpdateSystem.aspx.cs">
      <DependentUpon>UpdateSystem.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\UpdateSystem.aspx.designer.cs">
      <DependentUpon>UpdateSystem.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\UserInfo.aspx.cs">
      <DependentUpon>UserInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\UserInfo.aspx.designer.cs">
      <DependentUpon>UserInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\RTUsers.aspx.cs">
      <DependentUpon>RTUsers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\RTUsers.aspx.designer.cs">
      <DependentUpon>RTUsers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\ServiceStatus.aspx.cs">
      <DependentUpon>ServiceStatus.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\ServiceStatus.aspx.designer.cs">
      <DependentUpon>ServiceStatus.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\MyProfile.aspx.cs">
      <DependentUpon>MyProfile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\MyProfile.aspx.designer.cs">
      <DependentUpon>MyProfile.aspx</DependentUpon>
    </Compile>
    <Compile Include="CacheViewer.aspx.cs">
      <DependentUpon>CacheViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CacheViewer.aspx.designer.cs">
      <DependentUpon>CacheViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\CognitiveServicesBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\ConversationCounterExamples.aspx.cs">
      <DependentUpon>ConversationCounterExamples.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\ConversationCounterExamples.aspx.designer.cs">
      <DependentUpon>ConversationCounterExamples.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\ConversationEntities.aspx.cs">
      <DependentUpon>ConversationEntities.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\ConversationEntities.aspx.designer.cs">
      <DependentUpon>ConversationEntities.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\ConversationIntents.aspx.cs">
      <DependentUpon>ConversationIntents.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\ConversationIntents.aspx.designer.cs">
      <DependentUpon>ConversationIntents.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\ConversationTest.aspx.cs">
      <DependentUpon>ConversationTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\ConversationTest.aspx.designer.cs">
      <DependentUpon>ConversationTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NLCTraining.aspx.cs">
      <DependentUpon>NLCTraining.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NLCTraining.aspx.designer.cs">
      <DependentUpon>NLCTraining.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NLCTest.aspx.cs">
      <DependentUpon>NLCTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NLCTest.aspx.designer.cs">
      <DependentUpon>NLCTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NLCCategories.aspx.cs">
      <DependentUpon>NLCCategories.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NLCCategories.aspx.designer.cs">
      <DependentUpon>NLCCategories.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NotAuthorized.aspx.cs">
      <DependentUpon>NotAuthorized.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NotAuthorized.aspx.designer.cs">
      <DependentUpon>NotAuthorized.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\MissingConfiguration.aspx.cs">
      <DependentUpon>MissingConfiguration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\MissingConfiguration.aspx.designer.cs">
      <DependentUpon>MissingConfiguration.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NoPermissions.aspx.cs">
      <DependentUpon>NoPermissions.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NoPermissions.aspx.designer.cs">
      <DependentUpon>NoPermissions.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\NoLicense.aspx.cs">
      <DependentUpon>NoLicense.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\NoLicense.aspx.designer.cs">
      <DependentUpon>NoLicense.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\Usage.aspx.cs">
      <DependentUpon>Usage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\Usage.aspx.designer.cs">
      <DependentUpon>Usage.aspx</DependentUpon>
    </Compile>
    <Compile Include="CognitiveServices\Plan.aspx.cs">
      <DependentUpon>Plan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CognitiveServices\Plan.aspx.designer.cs">
      <DependentUpon>Plan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\VideoInfo.aspx.cs">
      <DependentUpon>VideoInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\VideoInfo.aspx.designer.cs">
      <DependentUpon>VideoInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\VideoCalls.aspx.cs">
      <DependentUpon>VideoCalls.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\VideoCalls.aspx.designer.cs">
      <DependentUpon>VideoCalls.aspx</DependentUpon>
    </Compile>
    <Compile Include="Services\EmailContactsHandler.cs" />
    <Compile Include="Whatsapp\Calls.aspx.cs">
      <DependentUpon>Calls.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\Calls.aspx.designer.cs">
      <DependentUpon>Calls.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CallInfo.aspx.cs">
      <DependentUpon>CallInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CallInfo.aspx.designer.cs">
      <DependentUpon>CallInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTAgentsAuxReasons.aspx.cs">
      <DependentUpon>RTAgentsAuxReasons.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTAgentsAuxReasons.aspx.designer.cs">
      <DependentUpon>RTAgentsAuxReasons.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTAgentsAuxReasonsModels\AgentInfoModel.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\ErrorInfo.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\ResponseData.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\RTAgentsAuxReasonsModelsJavaScriptConverter.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\StatsInfo.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\StatusInfo.cs" />
    <Compile Include="Reports\RTAgentsAuxReasonsModels\TotalInfo.cs" />
    <Compile Include="Reports\RTAgentsModels\RTAgentsModelsJavaScriptConverter.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\AssignedMessageInfoModel.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\AssignedMessagesInfoModel.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\ErrorInfo.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\ResponseData.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\RTAssignedMessagesModelsJavaScriptConverter.cs" />
    <Compile Include="Reports\RTAssignedMessagesModels\TotalInfo.cs" />
    <Compile Include="Reports\RTAssignedMessages.aspx.cs">
      <DependentUpon>RTAssignedMessages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTAssignedMessages.aspx.designer.cs">
      <DependentUpon>RTAssignedMessages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTAgentsModels\ErrorInfo.cs" />
    <Compile Include="Reports\RTAgentsModels\ResponseData.cs" />
    <Compile Include="Reports\RTAgentsModels\TotalInfo.cs" />
    <Compile Include="Reports\Utilities\RTAgentsAuxReasons\RTAgentAuxReasonsUtilities.cs" />
    <Compile Include="Reports\Utilities\RTAgents\RTAgentUtilities.cs" />
    <Compile Include="Reports\Utilities\RTAssignedMessages\RTAssignedMessagesUtilities.cs" />
    <Compile Include="Services\CallsHandler.cs" />
    <Compile Include="UnhandledExceptionModule.cs" />
    <Compile Include="CompressionModule.cs" />
    <Compile Include="Configuration\AgentGroupInfo.aspx.cs">
      <DependentUpon>AgentGroupInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AgentGroupInfo.aspx.designer.cs">
      <DependentUpon>AgentGroupInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AgentsGroups.aspx.cs">
      <DependentUpon>AgentsGroups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AgentsGroups.aspx.designer.cs">
      <DependentUpon>AgentsGroups.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AuxReasonsByAgents.aspx.cs">
      <DependentUpon>AuxReasonsByAgents.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AuxReasonsByAgents.aspx.designer.cs">
      <DependentUpon>AuxReasonsByAgents.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AutomaticExport.aspx.cs">
      <DependentUpon>AutomaticExport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AutomaticExport.aspx.designer.cs">
      <DependentUpon>AutomaticExport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\EmailConnections.aspx.cs">
      <DependentUpon>EmailConnections.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\EmailConnections.aspx.designer.cs">
      <DependentUpon>EmailConnections.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\PredefinedAnswersByQueue.aspx.cs">
      <DependentUpon>PredefinedAnswersByQueue.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\PredefinedAnswersByQueue.aspx.designer.cs">
      <DependentUpon>PredefinedAnswersByQueue.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\TagGroups.aspx.cs">
      <DependentUpon>TagGroups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\TagGroups.aspx.designer.cs">
      <DependentUpon>TagGroups.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\QueueInfo.aspx.cs">
      <DependentUpon>QueueInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\QueueInfo.aspx.designer.cs">
      <DependentUpon>QueueInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServiceInfo.aspx.cs">
      <DependentUpon>ServiceInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServiceInfo.aspx.designer.cs">
      <DependentUpon>ServiceInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AgentInfo.aspx.cs">
      <DependentUpon>AgentInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AgentInfo.aspx.designer.cs">
      <DependentUpon>AgentInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Agents.aspx.cs">
      <DependentUpon>Agents.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Agents.aspx.designer.cs">
      <DependentUpon>Agents.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AgentsQueues.aspx.cs">
      <DependentUpon>AgentsQueues.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AgentsQueues.aspx.designer.cs">
      <DependentUpon>AgentsQueues.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\AuxReasons.aspx.cs">
      <DependentUpon>AuxReasons.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\AuxReasons.aspx.designer.cs">
      <DependentUpon>AuxReasons.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\BlackList.aspx.cs">
      <DependentUpon>BlackList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\BlackList.aspx.designer.cs">
      <DependentUpon>BlackList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\FileHandler.ashx.cs">
      <DependentUpon>FileHandler.ashx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServiceAvatar.ashx.cs">
      <DependentUpon>ServiceAvatar.ashx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\PredefinedAnswers.aspx.cs">
      <DependentUpon>PredefinedAnswers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\PredefinedAnswers.aspx.designer.cs">
      <DependentUpon>PredefinedAnswers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Filters.aspx.cs">
      <DependentUpon>Filters.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Filters.aspx.designer.cs">
      <DependentUpon>Filters.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ContactReasons.aspx.cs">
      <DependentUpon>ContactReasons.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ContactReasons.aspx.designer.cs">
      <DependentUpon>ContactReasons.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesGoogleBusiness.aspx.cs">
      <DependentUpon>ServicesGoogleBusiness.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesGoogleBusiness.aspx.designer.cs">
      <DependentUpon>ServicesGoogleBusiness.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesGoogleRBM.aspx.cs">
      <DependentUpon>ServicesGoogleRBM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesGoogleRBM.aspx.designer.cs">
      <DependentUpon>ServicesGoogleRBM.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesVideoCall.aspx.cs">
      <DependentUpon>ServicesVideoCall.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesVideoCall.aspx.designer.cs">
      <DependentUpon>ServicesVideoCall.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesIntegrationChat.aspx.cs">
      <DependentUpon>ServicesIntegrationChat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesIntegrationChat.aspx.designer.cs">
      <DependentUpon>ServicesIntegrationChat.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesMercadoLibre.aspx.cs">
      <DependentUpon>ServicesMercadoLibre.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesMercadoLibre.aspx.designer.cs">
      <DependentUpon>ServicesMercadoLibre.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesFacebookMessenger.aspx.cs">
      <DependentUpon>ServicesFacebookMessenger.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesFacebookMessenger.aspx.designer.cs">
      <DependentUpon>ServicesFacebookMessenger.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesInstagram.aspx.cs">
      <DependentUpon>ServicesInstagram.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesInstagram.aspx.designer.cs">
      <DependentUpon>ServicesInstagram.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesLinkedin.aspx.cs">
      <DependentUpon>ServicesLinkedin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesLinkedin.aspx.designer.cs">
      <DependentUpon>ServicesLinkedin.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesSkype.aspx.cs">
      <DependentUpon>ServicesSkype.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesSkype.aspx.designer.cs">
      <DependentUpon>ServicesSkype.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesSMS.aspx.cs">
      <DependentUpon>ServicesSMS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesSMS.aspx.designer.cs">
      <DependentUpon>ServicesSMS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesTelegram.aspx.cs">
      <DependentUpon>ServicesTelegram.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesTelegram.aspx.designer.cs">
      <DependentUpon>ServicesTelegram.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesMail.aspx.cs">
      <DependentUpon>ServicesMail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesMail.aspx.designer.cs">
      <DependentUpon>ServicesMail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesChat.aspx.cs">
      <DependentUpon>ServicesChat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesChat.aspx.designer.cs">
      <DependentUpon>ServicesChat.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesTwitterSearches.aspx.cs">
      <DependentUpon>ServicesTwitterSearches.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesTwitterSearches.aspx.designer.cs">
      <DependentUpon>ServicesTwitterSearches.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesAppleMessaging.aspx.cs">
      <DependentUpon>ServicesAppleMessaging.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesAppleMessaging.aspx.designer.cs">
      <DependentUpon>ServicesAppleMessaging.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesWhatsapp.aspx.cs">
      <DependentUpon>ServicesWhatsapp.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesWhatsapp.aspx.designer.cs">
      <DependentUpon>ServicesWhatsapp.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesFacebook.aspx.cs">
      <DependentUpon>ServicesFacebook.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesFacebook.aspx.designer.cs">
      <DependentUpon>ServicesFacebook.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesTwitter.aspx.cs">
      <DependentUpon>ServicesTwitter.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesTwitter.aspx.designer.cs">
      <DependentUpon>ServicesTwitter.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesGooglePlay.aspx.cs">
      <DependentUpon>ServicesGooglePlay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesGooglePlay.aspx.designer.cs">
      <DependentUpon>ServicesGooglePlay.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\ServicesYouTube.aspx.cs">
      <DependentUpon>ServicesYouTube.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\ServicesYouTube.aspx.designer.cs">
      <DependentUpon>ServicesYouTube.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\QueueGroups.aspx.cs">
      <DependentUpon>QueueGroups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\QueueGroups.aspx.designer.cs">
      <DependentUpon>QueueGroups.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\FTPs.aspx.cs">
      <DependentUpon>FTPs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\FTPs.aspx.designer.cs">
      <DependentUpon>FTPs.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Gateway.aspx.cs">
      <DependentUpon>Gateway.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Gateway.aspx.designer.cs">
      <DependentUpon>Gateway.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\SurveyPreview.aspx.cs">
      <DependentUpon>SurveyPreview.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\SurveyPreview.aspx.designer.cs">
      <DependentUpon>SurveyPreview.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Surveys.aspx.cs">
      <DependentUpon>Surveys.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Surveys.aspx.designer.cs">
      <DependentUpon>Surveys.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\SocialUserProfilesLists.aspx.cs">
      <DependentUpon>SocialUserProfilesLists.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\SocialUserProfilesLists.aspx.designer.cs">
      <DependentUpon>SocialUserProfilesLists.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Integrations.aspx.cs">
      <DependentUpon>Integrations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Integrations.aspx.designer.cs">
      <DependentUpon>Integrations.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Sites.aspx.cs">
      <DependentUpon>Sites.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Sites.aspx.designer.cs">
      <DependentUpon>Sites.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\WorkingTimes.aspx.cs">
      <DependentUpon>WorkingTimes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\WorkingTimes.aspx.designer.cs">
      <DependentUpon>WorkingTimes.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\WhiteList.aspx.cs">
      <DependentUpon>WhiteList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\WhiteList.aspx.designer.cs">
      <DependentUpon>WhiteList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Queues.aspx.cs">
      <DependentUpon>Queues.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Queues.aspx.designer.cs">
      <DependentUpon>Queues.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Profiles.aspx.cs">
      <DependentUpon>Profiles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Profiles.aspx.designer.cs">
      <DependentUpon>Profiles.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Services.aspx.cs">
      <DependentUpon>Services.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Services.aspx.designer.cs">
      <DependentUpon>Services.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\SystemSettings.aspx.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\SystemSettings.aspx.designer.cs">
      <DependentUpon>SystemSettings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Administration\Users.aspx.cs">
      <DependentUpon>Users.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Administration\Users.aspx.designer.cs">
      <DependentUpon>Users.aspx</DependentUpon>
    </Compile>
    <Compile Include="Configuration\Tags.aspx.cs">
      <DependentUpon>Tags.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Configuration\Tags.aspx.designer.cs">
      <DependentUpon>Tags.aspx</DependentUpon>
    </Compile>
    <Compile Include="CORSModule.cs" />
    <Compile Include="ErrorPages\400.aspx.cs">
      <DependentUpon>400.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ErrorPages\400.aspx.designer.cs">
      <DependentUpon>400.aspx</DependentUpon>
    </Compile>
    <Compile Include="Wait.aspx.cs">
      <DependentUpon>Wait.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Wait.aspx.designer.cs">
      <DependentUpon>Wait.aspx</DependentUpon>
    </Compile>
    <Compile Include="InitError.aspx.cs">
      <DependentUpon>InitError.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InitError.aspx.designer.cs">
      <DependentUpon>InitError.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="DownloadAgent.aspx.cs">
      <DependentUpon>DownloadAgent.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DownloadAgent.aspx.designer.cs">
      <DependentUpon>DownloadAgent.aspx</DependentUpon>
    </Compile>
    <Compile Include="Errors.aspx.cs">
      <DependentUpon>Errors.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Errors.aspx.designer.cs">
      <DependentUpon>Errors.aspx</DependentUpon>
    </Compile>
    <Compile Include="ErrorPages\404.aspx.cs">
      <DependentUpon>404.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ErrorPages\404.aspx.designer.cs">
      <DependentUpon>404.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\Bitly.cs" />
    <Compile Include="Helpers\ControlsHelper.cs" />
    <Compile Include="Helpers\YourlsClient.cs" />
    <Compile Include="Helpers\YouTubeHelper.cs" />
    <Compile Include="Helpers\LinkedInHelper.cs" />
    <Compile Include="Helpers\FacebookHelper.cs" />
    <Compile Include="Helpers\FilesHelper.cs" />
    <Compile Include="Helpers\Fingerprint.cs" />
    <Compile Include="Helpers\InstagramHelper.cs" />
    <Compile Include="Helpers\MailHelper.cs" />
    <Compile Include="Helpers\MailParametersDTO.cs" />
    <Compile Include="Helpers\ReportsHelper.cs" />
    <Compile Include="Helpers\StartupHelper.cs" />
    <Compile Include="Helpers\TwitterHelper.cs" />
    <Compile Include="License.aspx.cs">
      <DependentUpon>License.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="License.aspx.designer.cs">
      <DependentUpon>License.aspx</DependentUpon>
    </Compile>
    <Compile Include="BasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LicenseValidatorBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Reports\CasePopup.aspx.cs">
      <DependentUpon>CasePopup.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CasePopup.aspx.designer.cs">
      <DependentUpon>CasePopup.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CasesReopenings.aspx.cs">
      <DependentUpon>CasesReopenings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CasesReopenings.aspx.designer.cs">
      <DependentUpon>CasesReopenings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MessagesSegments.aspx.cs">
      <DependentUpon>MessagesSegments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MessagesSegments.aspx.designer.cs">
      <DependentUpon>MessagesSegments.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MessagesTransfers.aspx.cs">
      <DependentUpon>MessagesTransfers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MessagesTransfers.aspx.designer.cs">
      <DependentUpon>MessagesTransfers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Scheduled.aspx.cs">
      <DependentUpon>Scheduled.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Scheduled.aspx.designer.cs">
      <DependentUpon>Scheduled.aspx</DependentUpon>
    </Compile>
    <Compile Include="Services\SystemHandler.cs" />
    <Compile Include="Services\SupervisorsHandler.cs" />
    <Compile Include="Services\SocialUsersHandler.cs" />
    <Compile Include="SignedQueryStringBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LoginRequiredBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Logout.aspx.cs">
      <DependentUpon>Logout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Logout.aspx.designer.cs">
      <DependentUpon>Logout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Logs.aspx.cs">
      <DependentUpon>Logs.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Logs.aspx.designer.cs">
      <DependentUpon>Logs.aspx</DependentUpon>
    </Compile>
    <Compile Include="Master.Master.cs">
      <DependentUpon>Master.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Master.Master.designer.cs">
      <DependentUpon>Master.Master</DependentUpon>
    </Compile>
    <Compile Include="PopupBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reports\AgentInfo.aspx.cs">
      <DependentUpon>AgentInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\AgentInfo.aspx.designer.cs">
      <DependentUpon>AgentInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Adherence.aspx.cs">
      <DependentUpon>Adherence.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Adherence.aspx.designer.cs">
      <DependentUpon>Adherence.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UsersLoginLogout.aspx.cs">
      <DependentUpon>UsersLoginLogout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UsersLoginLogout.aspx.designer.cs">
      <DependentUpon>UsersLoginLogout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\LoginLogout.aspx.cs">
      <DependentUpon>LoginLogout.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\LoginLogout.aspx.designer.cs">
      <DependentUpon>LoginLogout.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Agents.aspx.cs">
      <DependentUpon>Agents.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Agents.aspx.designer.cs">
      <DependentUpon>Agents.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ConsolidatedCases.aspx.cs">
      <DependentUpon>ConsolidatedCases.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ConsolidatedCases.aspx.designer.cs">
      <DependentUpon>ConsolidatedCases.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserControl.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserFacebookMessengerAccount.ascx.cs">
      <DependentUpon>SocialUserFacebookMessengerAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserFacebookMessengerAccount.ascx.designer.cs">
      <DependentUpon>SocialUserFacebookMessengerAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserSkypeAccount.ascx.cs">
      <DependentUpon>SocialUserSkypeAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserSkypeAccount.ascx.designer.cs">
      <DependentUpon>SocialUserSkypeAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserSMSAccount.ascx.cs">
      <DependentUpon>SocialUserSMSAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserSMSAccount.ascx.designer.cs">
      <DependentUpon>SocialUserSMSAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserChatAccount.ascx.cs">
      <DependentUpon>SocialUserChatAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserChatAccount.ascx.designer.cs">
      <DependentUpon>SocialUserChatAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Case.aspx.cs">
      <DependentUpon>Case.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Case.aspx.designer.cs">
      <DependentUpon>Case.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ControlsHelper.cs" />
    <Compile Include="Reports\Controls\Chat.cs" />
    <Compile Include="Reports\Controls\Case.cs" />
    <Compile Include="Reports\Controls\MailMessageControl.ascx.cs">
      <DependentUpon>MailMessageControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\MailMessageControl.ascx.designer.cs">
      <DependentUpon>MailMessageControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserInstagramAccount.ascx.cs">
      <DependentUpon>SocialUserInstagramAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserInstagramAccount.ascx.designer.cs">
      <DependentUpon>SocialUserInstagramAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserWhatsAppAccount.ascx.cs">
      <DependentUpon>SocialUserWhatsAppAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserWhatsAppAccount.ascx.designer.cs">
      <DependentUpon>SocialUserWhatsAppAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserTelegramAccount.ascx.cs">
      <DependentUpon>SocialUserTelegramAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserTelegramAccount.ascx.designer.cs">
      <DependentUpon>SocialUserTelegramAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserMailAccount.ascx.cs">
      <DependentUpon>SocialUserMailAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserMailAccount.ascx.designer.cs">
      <DependentUpon>SocialUserMailAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserFacebookAccount.ascx.cs">
      <DependentUpon>SocialUserFacebookAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserFacebookAccount.ascx.designer.cs">
      <DependentUpon>SocialUserFacebookAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserTwitterAccount.ascx.cs">
      <DependentUpon>SocialUserTwitterAccount.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Controls\SocialUserTwitterAccount.ascx.designer.cs">
      <DependentUpon>SocialUserTwitterAccount.ascx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Controls\WordCloud.cs" />
    <Compile Include="Reports\Cases.aspx.cs">
      <DependentUpon>Cases.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Cases.aspx.designer.cs">
      <DependentUpon>Cases.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CasesTimes.aspx.cs">
      <DependentUpon>CasesTimes.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CasesTimes.aspx.designer.cs">
      <DependentUpon>CasesTimes.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\AttachInfo.aspx.cs">
      <DependentUpon>AttachInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\AttachInfo.aspx.designer.cs">
      <DependentUpon>AttachInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Chats.aspx.cs">
      <DependentUpon>Chats.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Chats.aspx.designer.cs">
      <DependentUpon>Chats.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Exporter.aspx.cs">
      <DependentUpon>Exporter.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Exporter.aspx.designer.cs">
      <DependentUpon>Exporter.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\HeatMap.aspx.cs">
      <DependentUpon>HeatMap.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\HeatMap.aspx.designer.cs">
      <DependentUpon>HeatMap.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ConsolidatedSurveys.aspx.cs">
      <DependentUpon>ConsolidatedSurveys.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\ConsolidatedSurveys.aspx.designer.cs">
      <DependentUpon>ConsolidatedSurveys.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportsBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Services.aspx.cs">
      <DependentUpon>Services.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Services.aspx.designer.cs">
      <DependentUpon>Services.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTServices.aspx.cs">
      <DependentUpon>RTServices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTServices.aspx.designer.cs">
      <DependentUpon>RTServices.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SocialUserProfileInfo.aspx.cs">
      <DependentUpon>SocialUserProfileInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\SocialUserProfileInfo.aspx.designer.cs">
      <DependentUpon>SocialUserProfileInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\CaseInfo.aspx.cs">
      <DependentUpon>CaseInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\CaseInfo.aspx.designer.cs">
      <DependentUpon>CaseInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Queues.aspx.cs">
      <DependentUpon>Queues.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Queues.aspx.designer.cs">
      <DependentUpon>Queues.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MessagesQuery.aspx.cs">
      <DependentUpon>MessagesQuery.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MessagesQuery.aspx.designer.cs">
      <DependentUpon>MessagesQuery.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTMessages.aspx.cs">
      <DependentUpon>RTMessages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTMessages.aspx.designer.cs">
      <DependentUpon>RTMessages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SocialUserProfiles.aspx.cs">
      <DependentUpon>SocialUserProfiles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\SocialUserProfiles.aspx.designer.cs">
      <DependentUpon>SocialUserProfiles.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\SocialUsers.aspx.cs">
      <DependentUpon>SocialUsers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\SocialUsers.aspx.designer.cs">
      <DependentUpon>SocialUsers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Surveys.aspx.cs">
      <DependentUpon>Surveys.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Surveys.aspx.designer.cs">
      <DependentUpon>Surveys.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UsersLog.aspx.cs">
      <DependentUpon>UsersLog.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UsersLog.aspx.designer.cs">
      <DependentUpon>UsersLog.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\TwitterReport.aspx.cs">
      <DependentUpon>TwitterReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\TwitterReport.aspx.designer.cs">
      <DependentUpon>TwitterReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UsersLogInfo.aspx.cs">
      <DependentUpon>UsersLogInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UsersLogInfo.aspx.designer.cs">
      <DependentUpon>UsersLogInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\WordCloud.aspx.cs">
      <DependentUpon>WordCloud.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\WordCloud.aspx.designer.cs">
      <DependentUpon>WordCloud.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\MessageInfo.aspx.cs">
      <DependentUpon>MessageInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\MessageInfo.aspx.designer.cs">
      <DependentUpon>MessageInfo.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Messages.aspx.cs">
      <DependentUpon>Messages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Messages.aspx.designer.cs">
      <DependentUpon>Messages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTAgents.aspx.cs">
      <DependentUpon>RTAgents.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTAgents.aspx.designer.cs">
      <DependentUpon>RTAgents.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\RTQueues.aspx.cs">
      <DependentUpon>RTQueues.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\RTQueues.aspx.designer.cs">
      <DependentUpon>RTQueues.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\UseGraph.aspx.cs">
      <DependentUpon>UseGraph.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\UseGraph.aspx.designer.cs">
      <DependentUpon>UseGraph.aspx</DependentUpon>
    </Compile>
    <Compile Include="Scripts\Dynamic\ScriptsHandler.cs" />
    <Compile Include="Services\CasesHandler.cs" />
    <Compile Include="Services\TasksHandler.cs" />
    <Compile Include="Services\RestChatHandler.cs" />
    <Compile Include="Services\FlowHandler.cs" />
    <Compile Include="Services\MediaHandler.cs" />
    <Compile Include="Services\ChatHandler.cs" />
    <Compile Include="Services\ConfigurationHandler.cs" />
    <Compile Include="Services\AgentsHandler.cs" />
    <Compile Include="Services\BaseHandler.cs" />
    <Compile Include="Services\MessagingHandler.cs" />
    <Compile Include="Services\ReportsHandler.cs" />
    <Compile Include="Services\StatusHandler.cs" />
    <Compile Include="Startup.aspx.cs">
      <DependentUpon>Startup.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Startup.aspx.designer.cs">
      <DependentUpon>Startup.aspx</DependentUpon>
    </Compile>
    <Compile Include="Status.aspx.cs">
      <DependentUpon>Status.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Status.aspx.designer.cs">
      <DependentUpon>Status.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\InternalChat.aspx.cs">
      <DependentUpon>InternalChat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\InternalChat.aspx.designer.cs">
      <DependentUpon>InternalChat.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\Messages.aspx.cs">
      <DependentUpon>Messages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\Messages.aspx.designer.cs">
      <DependentUpon>Messages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\Notifications.aspx.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\Notifications.aspx.designer.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\ChatMessages.aspx.cs">
      <DependentUpon>ChatMessages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\ChatMessages.aspx.designer.cs">
      <DependentUpon>ChatMessages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\PendingMessages.aspx.cs">
      <DependentUpon>PendingMessages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\PendingMessages.aspx.designer.cs">
      <DependentUpon>PendingMessages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\OutgoingMessages.aspx.cs">
      <DependentUpon>OutgoingMessages.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\OutgoingMessages.aspx.designer.cs">
      <DependentUpon>OutgoingMessages.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reports\Dashboards.aspx.cs">
      <DependentUpon>Dashboards.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports\Dashboards.aspx.designer.cs">
      <DependentUpon>Dashboards.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\ProfilesMassiveComposer.aspx.cs">
      <DependentUpon>ProfilesMassiveComposer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\ProfilesMassiveComposer.aspx.designer.cs">
      <DependentUpon>ProfilesMassiveComposer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\Settings.aspx.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\Settings.aspx.designer.cs">
      <DependentUpon>Settings.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supervisor\SocialUserProfiles.aspx.cs">
      <DependentUpon>SocialUserProfiles.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supervisor\SocialUserProfiles.aspx.designer.cs">
      <DependentUpon>SocialUserProfiles.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserNotLoggedException.cs" />
    <Compile Include="WebAgentUrlGenerator.aspx.cs">
      <DependentUpon>WebAgentUrlGenerator.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebAgentUrlGenerator.aspx.designer.cs">
      <DependentUpon>WebAgentUrlGenerator.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\Dashboards.aspx.cs">
      <DependentUpon>Dashboards.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\Dashboards.aspx.designer.cs">
      <DependentUpon>Dashboards.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\EmbeddedSignup.aspx.cs">
      <DependentUpon>EmbeddedSignup.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\EmbeddedSignup.aspx.designer.cs">
      <DependentUpon>EmbeddedSignup.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreationReports.aspx.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreationReports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreationReports.aspx.designer.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreationReports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreationTask.aspx.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreationTask.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreationTask.aspx.designer.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreationTask.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreation.aspx.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveWithoutCaseCreation.aspx.designer.cs">
      <DependentUpon>HSMMassiveWithoutCaseCreation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveRequestTasks.aspx.cs">
      <DependentUpon>HSMMassiveRequestTasks.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveRequestTasks.aspx.designer.cs">
      <DependentUpon>HSMMassiveRequestTasks.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveTasks.aspx.cs">
      <DependentUpon>HSMMassiveTasks.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveTasks.aspx.designer.cs">
      <DependentUpon>HSMMassiveTasks.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveComposer.aspx.cs">
      <DependentUpon>HSMMassiveComposer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveComposer.aspx.designer.cs">
      <DependentUpon>HSMMassiveComposer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMComposer.aspx.cs">
      <DependentUpon>HSMComposer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMComposer.aspx.designer.cs">
      <DependentUpon>HSMComposer.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\DetailedReport.aspx.cs">
      <DependentUpon>DetailedReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\DetailedReport.aspx.designer.cs">
      <DependentUpon>DetailedReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\ConsolidatedReport.aspx.cs">
      <DependentUpon>ConsolidatedReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\ConsolidatedReport.aspx.designer.cs">
      <DependentUpon>ConsolidatedReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveRequestTasksConfirmation.aspx.cs">
      <DependentUpon>HSMMassiveRequestTasksConfirmation.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\HSMMassiveRequestTasksConfirmation.aspx.designer.cs">
      <DependentUpon>HSMMassiveRequestTasksConfirmation.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\DownloadCampaignAttachment.aspx.cs">
      <DependentUpon>DownloadCampaignAttachment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\DownloadCampaignAttachment.aspx.designer.cs">
      <DependentUpon>DownloadCampaignAttachment.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\TaskInfoWithoutCase.aspx.cs">
      <DependentUpon>TaskInfoWithoutCase.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\TaskInfoWithoutCase.aspx.designer.cs">
      <DependentUpon>TaskInfoWithoutCase.aspx</DependentUpon>
    </Compile>
    <Compile Include="Whatsapp\TaskInfo.aspx.cs">
      <DependentUpon>TaskInfo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Whatsapp\TaskInfo.aspx.designer.cs">
      <DependentUpon>TaskInfo.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Reports\Exported\Daily\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Dependencies\Facebook SDK\Facebook\Facebook.csproj">
      <Project>{857e540c-0092-4590-a279-f98a6b77ac0a}</Project>
      <Name>Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Dependencies\Twitterizer2\Twitterizer2.csproj">
      <Project>{2fdc3492-6b9e-4771-9755-7892c9cb1e96}</Project>
      <Name>Twitterizer2</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.AppleMessaging\Yoizen.Social.AppleMessaging.csproj">
      <Project>{cab6f453-4de7-4a04-a067-39c2e0e43cf5}</Project>
      <Name>Yoizen.Social.AppleMessaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Business\Yoizen.Social.Business.csproj">
      <Project>{13E5DDD0-A917-4714-9BD5-C10DEC38AC77}</Project>
      <Name>Yoizen.Social.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Chat\Yoizen.Social.Chat.csproj">
      <Project>{ce119bce-4147-4882-a0b2-1753b95b62da}</Project>
      <Name>Yoizen.Social.Chat</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.CognitiveServices\Yoizen.Social.CognitiveServices.csproj">
      <Project>{dadd884a-b6cb-4a2d-9867-20ec9153d004}</Project>
      <Name>Yoizen.Social.CognitiveServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Core\Yoizen.Social.Core.csproj">
      <Project>{36e13364-bacf-4405-8fb0-e22962c71ba1}</Project>
      <Name>Yoizen.Social.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.DAL\Yoizen.Social.DAL.csproj">
      <Project>{162DB3D5-4AB9-408D-B306-B43CE00375A4}</Project>
      <Name>Yoizen.Social.DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.DomainModel\Yoizen.Social.DomainModel.csproj">
      <Project>{5FC41DA8-0AFF-48ED-83AF-4C309BA0BF20}</Project>
      <Name>Yoizen.Social.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Facebook\Yoizen.Social.Facebook.csproj">
      <Project>{8504A80B-495F-4B35-9ADE-2FC9C6893EA9}</Project>
      <Name>Yoizen.Social.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleBusiness\Yoizen.Social.GoogleBusiness.csproj">
      <Project>{abe15d57-1544-40a9-966a-edf366ced7a5}</Project>
      <Name>Yoizen.Social.GoogleBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GooglePlay\Yoizen.Social.GooglePlay.csproj">
      <Project>{ce444c2e-935b-412b-823d-e46218633e3c}</Project>
      <Name>Yoizen.Social.GooglePlay</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.GoogleRBM\Yoizen.Social.GoogleRBM.csproj">
      <Project>{bc8ae046-1e86-40ff-bcd2-e3af4bcf89b2}</Project>
      <Name>Yoizen.Social.GoogleRBM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Instagram\Yoizen.Social.Instagram.csproj">
      <Project>{d3f34bf2-ddc7-497d-a163-9d42b20de75f}</Project>
      <Name>Yoizen.Social.Instagram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Licensing\Yoizen.Social.Licensing.csproj">
      <Project>{0E6DF3BC-8269-466B-9617-C7F84EB9BF0B}</Project>
      <Name>Yoizen.Social.Licensing</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.LinkedIn\Yoizen.Social.LinkedIn.csproj">
      <Project>{eca16318-99d5-4a3f-9abc-eb12ebdb9a59}</Project>
      <Name>Yoizen.Social.LinkedIn</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Mail\Yoizen.Social.Mail.csproj">
      <Project>{809eda6e-48e4-42a1-aad7-b3a6b5d51cf0}</Project>
      <Name>Yoizen.Social.Mail</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.MercadoLibre\Yoizen.Social.MercadoLibre.csproj">
      <Project>{fc98f3f1-85b5-4a6e-92b9-0695928bef79}</Project>
      <Name>Yoizen.Social.MercadoLibre</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Skype\Yoizen.Social.Skype.csproj">
      <Project>{93fa2d4d-6c73-42f3-bffc-10fdfffffeb9}</Project>
      <Name>Yoizen.Social.Skype</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SMS\Yoizen.Social.SMS.csproj">
      <Project>{2b7e8299-a4bf-4c7d-8ef2-ad06896d8e31}</Project>
      <Name>Yoizen.Social.SMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.AppleMessaging\Yoizen.Social.SocialServices.AppleMessaging.csproj">
      <Project>{861df304-42d0-454b-9dc9-904866e18316}</Project>
      <Name>Yoizen.Social.SocialServices.AppleMessaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Chat\Yoizen.Social.SocialServices.Chat.csproj">
      <Project>{907b4153-b3a8-46f1-9a96-5b02413c90e3}</Project>
      <Name>Yoizen.Social.SocialServices.Chat</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Facebook\Yoizen.Social.SocialServices.Facebook.csproj">
      <Project>{081E6A87-39CA-4678-830B-07998F3BDD6C}</Project>
      <Name>Yoizen.Social.SocialServices.Facebook</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GoogleBusiness\Yoizen.Social.SocialServices.GoogleBusiness.csproj">
      <Project>{c4aa24f5-691d-4b32-b3f6-fb19e65c19b8}</Project>
      <Name>Yoizen.Social.SocialServices.GoogleBusiness</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GooglePlay\Yoizen.Social.SocialServices.GooglePlay.csproj">
      <Project>{9de9cbdd-f748-406a-b3d8-6b2442126c0e}</Project>
      <Name>Yoizen.Social.SocialServices.GooglePlay</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.GoogleRBM\Yoizen.Social.SocialServices.GoogleRBM.csproj">
      <Project>{037a8c55-d414-4fe1-8245-464111e5c17d}</Project>
      <Name>Yoizen.Social.SocialServices.GoogleRBM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Instagram\Yoizen.Social.SocialServices.Instagram.csproj">
      <Project>{38a173ca-df7c-4209-ae45-e044482ca992}</Project>
      <Name>Yoizen.Social.SocialServices.Instagram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.LinkedIn\Yoizen.Social.SocialServices.LinkedIn.csproj">
      <Project>{931cc905-b81e-4d2d-8830-b6e0eb8ffa52}</Project>
      <Name>Yoizen.Social.SocialServices.LinkedIn</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Mail\Yoizen.Social.SocialServices.Mail.csproj">
      <Project>{c395915e-4f30-4410-ab07-749c5cae936a}</Project>
      <Name>Yoizen.Social.SocialServices.Mail</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.MercadoLibre\Yoizen.Social.SocialServices.MercadoLibre.csproj">
      <Project>{b986d66c-5a3c-45c8-a290-77fd12b3094d}</Project>
      <Name>Yoizen.Social.SocialServices.MercadoLibre</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Skype\Yoizen.Social.SocialServices.Skype.csproj">
      <Project>{15a25c7e-4c8d-4ef8-8b88-fde7af80296a}</Project>
      <Name>Yoizen.Social.SocialServices.Skype</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.SMS\Yoizen.Social.SocialServices.SMS.csproj">
      <Project>{d56efd3c-0fe3-4800-bee2-26c202573352}</Project>
      <Name>Yoizen.Social.SocialServices.SMS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Subscriber\Yoizen.Social.SocialServices.Subscriber.csproj">
      <Project>{804b9b56-8c6b-4d21-b6a0-6cdf8af1cc05}</Project>
      <Name>Yoizen.Social.SocialServices.Subscriber</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Telegram\Yoizen.Social.SocialServices.Telegram.csproj">
      <Project>{c74a16cb-8a20-4d65-8a6d-33f8ee85fa4a}</Project>
      <Name>Yoizen.Social.SocialServices.Telegram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.Twitter\Yoizen.Social.SocialServices.Twitter.csproj">
      <Project>{0392F986-CB8A-49FC-9322-94FB3077B164}</Project>
      <Name>Yoizen.Social.SocialServices.Twitter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.VideoCall\Yoizen.Social.SocialServices.VideoCall.csproj">
      <Project>{d7b18476-8184-45a1-a612-61dbb0b29df8}</Project>
      <Name>Yoizen.Social.SocialServices.VideoCall</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.WhatsApp\Yoizen.Social.SocialServices.WhatsApp.csproj">
      <Project>{34584168-0afb-4df2-9788-7128d7ef0841}</Project>
      <Name>Yoizen.Social.SocialServices.WhatsApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.SocialServices.YouTube\Yoizen.Social.SocialServices.YouTube.csproj">
      <Project>{0d2b40d5-a308-4c37-b7ad-93bcd1e2a239}</Project>
      <Name>Yoizen.Social.SocialServices.YouTube</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Telegram\Yoizen.Social.Telegram.csproj">
      <Project>{50366855-af3b-4cb5-b814-d2249d8375e3}</Project>
      <Name>Yoizen.Social.Telegram</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.Twitter\Yoizen.Social.Twitter.csproj">
      <Project>{bb066b75-ba4f-46d9-a057-990147d845b6}</Project>
      <Name>Yoizen.Social.Twitter</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.VideoCall\Yoizen.Social.VideoCall.csproj">
      <Project>{4ea5410f-a260-4c11-a799-6d1b299db775}</Project>
      <Name>Yoizen.Social.VideoCall</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.WhatsApp\Yoizen.Social.WhatsApp.csproj">
      <Project>{ca445328-e362-419c-8285-256228ed3bdf}</Project>
      <Name>Yoizen.Social.WhatsApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.YFlow\Yoizen.Social.YFlow.csproj">
      <Project>{136faf49-afed-463e-badb-de50a479f34c}</Project>
      <Name>Yoizen.Social.YFlow</Name>
    </ProjectReference>
    <ProjectReference Include="..\Yoizen.Social.YouTube\Yoizen.Social.YouTube.csproj">
      <Project>{98057068-7231-4784-A794-7C2DDFB34663}</Project>
      <Name>Yoizen.Social.YouTube</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Master.Master" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Themes\Default\Default.skin" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="log4net.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Styles\ui.selectmenu.css" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Configuration\ServiceAvatar.ashx" />
    <Content Include="Configuration\Uploads\Web.config" />
    <Content Include="Configuration\FileHandler.ashx" />
    <Content Include="App_Themes\Default\Definitions.less" />
    <Content Include="App_Themes\Default\Nav.less" />
    <Content Include="App_Themes\Default\Nav.css.map">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Master.less" />
    <Content Include="App_Themes\Default\Master.css.map">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\Layout.less" />
    <Content Include="App_Themes\Default\Layout.css.map">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Definitions.less" />
    <Content Include="App_Themes\OmniCX\Layout.less" />
    <Content Include="App_Themes\OmniCX\Layout.css.map">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Master.less" />
    <Content Include="App_Themes\OmniCX\Master.css.map">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Nav.less" />
    <Content Include="App_Themes\OmniCX\Nav.css.map">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\OmniCX\Default.skin" />
    <Content Include="App_Themes\OmniCX\jquery.ui.css.map">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Default.skin" />
    <Content Include="App_Themes\Avaya\Definitions.less" />
    <Content Include="App_Themes\Avaya\jquery.ui.css.map">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\jquery.ui.less" />
    <Content Include="App_Themes\Avaya\Layout.less" />
    <Content Include="App_Themes\Avaya\Layout.css.map">
      <DependentUpon>Layout.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Master.less" />
    <Content Include="App_Themes\Avaya\Master.css.map">
      <DependentUpon>Master.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\Nav.less" />
    <Content Include="App_Themes\Avaya\Nav.css.map">
      <DependentUpon>Nav.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Avaya\tooltip.less" />
    <Content Include="App_Themes\Avaya\popover.less" />
    <None Include="App_Themes\OmniCX\jquery.ui.less" />
    <Content Include="App_Themes\OmniCX\tooltip.less" />
    <Content Include="App_Themes\OmniCX\popover.less" />
    <Content Include="App_Themes\Default\jquery.ui.css.map">
      <DependentUpon>jquery.ui.css</DependentUpon>
    </Content>
    <Content Include="App_Themes\Default\jquery.ui.less" />
    <Content Include="App_Themes\Default\tooltip.less" />
    <Content Include="App_Themes\Default\popover.less" />
    <None Include="bundleconfig.json" />
    <None Include="compilerconfig.json" />
    <None Include="compilerconfig.json.defaults">
      <DependentUpon>compilerconfig.json</DependentUpon>
    </None>
    <Content Include="Images\svg\emojis.zip" />
    <Content Include="i18n\en.json" />
    <Content Include="i18n\es.json" />
    <Content Include="i18n\Web.config" />
    <Content Include="i18n\pt-br.json" />
    <Content Include="Images\Web.config" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\Social - Debug.pubxml">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\PublishProfiles\Social - Release.pubxml" />
    <None Include="Properties\PublishProfiles\Social - ReleaseEncrypted.pubxml" />
    <Content Include="Scripts\Dynamic\Web.config" />
    <Content Include="Services\Web.config" />
    <Content Include="Scripts\d3.min.js" />
    <Content Include="Reports\Exported\Web.config" />
    <Content Include="Styles\SurveyPreview.less" />
    <Content Include="Services\NewChat.json" />
    <Content Include="Storage\Web.config" />
    <Content Include="Styles\FullHeight.less" />
    <Content Include="Styles\Fonts\fa-brands-400.eot" />
    <Content Include="Styles\Fonts\fa-brands-400.ttf" />
    <Content Include="Styles\Fonts\fa-brands-400.woff" />
    <Content Include="Styles\Fonts\fa-brands-400.woff2" />
    <Content Include="Styles\Fonts\fa-light-300.eot" />
    <Content Include="Styles\Fonts\fa-light-300.ttf" />
    <Content Include="Styles\Fonts\fa-light-300.woff" />
    <Content Include="Styles\Fonts\fa-light-300.woff2" />
    <Content Include="Styles\Fonts\fa-regular-400.eot" />
    <Content Include="Styles\Fonts\fa-regular-400.ttf" />
    <Content Include="Styles\Fonts\fa-regular-400.woff" />
    <Content Include="Styles\Fonts\fa-regular-400.woff2" />
    <Content Include="Styles\Fonts\fa-solid-900.eot" />
    <Content Include="Styles\Fonts\fa-solid-900.ttf" />
    <Content Include="Styles\Fonts\fa-solid-900.woff" />
    <Content Include="Styles\Fonts\fa-solid-900.woff2" />
    <Content Include="Styles\Fonts\yoizen.eot" />
    <Content Include="Styles\Fonts\yoizen.ttf" />
    <Content Include="Styles\Fonts\yoizen.woff" />
    <Content Include="Styles\Fonts\material-icons.woff2" />
    <Content Include="Services\RestChatSchemas\NewRestChat.json" />
    <Content Include="Services\RestChatSchemas\UpdateRestChat.json" />
    <Content Include="Services\RestChatSchemas\ActionRestChat.json" />
    <Content Include="Services\RestChatSchemas\EndRestChat.json" />
    <Content Include="Services\CasesSchemas\UpdateCase.json" />
    <Content Include="Scripts\jquery.timetable.js" />
    <Content Include="Services\TasksSchemas\WhatsappHSM.json" />
    <Content Include="Services\FlowSchemas\UpdateProfile.json" />
    <Content Include="Services\TasksSchemas\WhatsappHSMRequest.json" />
    <Content Include="Services\ConfigurationSchemas\Agents_Create.json" />
    <Content Include="Reports\Dashboards.less" />
    <Content Include="Styles\Fonts\CeraRoundProBlack.woff" />
    <Content Include="Styles\Fonts\CeraRoundProBold.woff" />
    <Content Include="Styles\Fonts\CeraRoundProLight.woff" />
    <Content Include="Styles\Fonts\CeraRoundProMedium.woff" />
    <Content Include="Styles\Fonts\CeraRoundProRegular.woff" />
    <Content Include="Styles\Fonts\CeraRoundProThin.woff" />
    <Content Include="Styles\Fonts\fa-duotone-900.eot" />
    <Content Include="Styles\Fonts\fa-duotone-900.ttf" />
    <Content Include="Styles\Fonts\fa-duotone-900.woff" />
    <Content Include="Styles\Fonts\fa-duotone-900.woff2" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Scripts\cloud.json" />
    <Content Include="Services\EmailContactsSchemas\EmailContacts_Create.json" />
    <Content Include="Services\EmailContactsSchemas\EmailContacts_Update.json" />
    <None Include="Styles\jquery.multiselect.filter.less" />
    <Content Include="Whatsapp\Dashboards.less" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\Build\SmartAssembly.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>2879</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost/Social</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>