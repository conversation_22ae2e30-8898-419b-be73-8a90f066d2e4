﻿module.exports = function(grunt) {
    let version = grunt.file.read('../.build/version.txt');
    let webAgentVersion = parseInt(grunt.file.read('../.build/webagentversion.txt'));
 
    try {
        let task = grunt.cli.tasks[0];
        if (!task.startsWith('dev')) {
            console.log('Se está haciendo un deploy de un ambiente productivo');
            webAgentVersion++;
            webAgentVersion = webAgentVersion.toString();
            grunt.file.write('../.build/webagentversion.txt', webAgentVersion);
        }
    }
    catch (e) {
        console.log(e);
    }

    grunt.initConfig({
        pkg: grunt.file.readJSON('package.json'),
        msbuild: {
            prod: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'Release'
                    },
                    verbosity: 'minimal'
                }
            },
            prod_vs2022: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'Release'
                    },
                    verbosity: 'minimal'
                }
            },
            prod_cloud: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'ReleaseCloud'
                    },
                    verbosity: 'minimal'
                }
            },
            dev: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Debug',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: false
                    },
                    verbosity: 'minimal'
                }
            },
            prod_cloud_vs2022: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: process.env.DEV_VS2022_PATH || "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'ReleaseCloud'
                    },
                    verbosity: 'minimal'
                }
            },
            prod_cloud_pipeline: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: process.env.DEV_VS2022_PATH || "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'ReleaseCloudPipeline'
                    },
                    verbosity: 'minimal'
                }
            },
            prod_cloud_vs2019: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Release',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: true,
                        PublishProfile: 'ReleaseCloud'
                    },
                    verbosity: 'minimal'
                }
            },
            dev_vs2022: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Debug',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath:  process.env.DEV_VS2022_PATH || 'C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe',
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: false
                    },
                    verbosity: 'minimal'
                }
            },
            dev_vs2019: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Debug',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: false
                    },
                    verbosity: 'minimal'
                }
            },
            dev_vs2015: {
                src: ['Yoizen.Social.WebAgent.csproj'],
                options: {
                    projectConfiguration: 'Debug',
                    targets: ['Clean', 'Rebuild'],
                    version: 4.0,
                    maxCpuCount: 4,
                    msbuildPath: "C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319\\MSBuild.exe",
                    inferMsbuildPath: false,
                    buildParameters: {
                        WarningLevel: 2,
                        DeployOnBuild: false
                    },
                    verbosity: 'minimal'
                }
            }
        },
        copy: {
            prod: {
                files: [{
                    expand: true,
                    src: ['app/**', 'translations/**', 'index.html', '!app/css_components/*.css', '!app/content/*.css', '!app/content/*.less', '!app/content/*.map', 'app/content/app_production*.css', 'app/css_components/app_components*.css'],
                    dest: '../Deploy/ReleaseEncrypted/WebAgent/'
                }]
            },
            prod_cloud: {
                files: [{
                    expand: true,
                    src: ['app/**', 'translations/**', 'index.html', '!app/css_components/*.css', '!app/content/*.css', '!app/content/*.less', '!app/content/*.map', '!app/webfonts/fa-*.*', 'app/content/app_production*.css', 'app/css_components/app_components*.css'],
                    dest: process.env.DEPLOY_PATH || '../Deploy/Release/WebAgent/'
                }]
            },
            dev: {
                files: [
                    {
                        src: 'Web-iis.config',
                        dest: 'Web.config'
                    },
                    {
                        src: 'Global-iis.asax',
                        dest: 'Global.asax'
                    },
                    {
                        src: 'Global-iis.asax.cs',
                        dest: 'Global.asax.cs'
                    }
                ]
            },
            dev_node: {
                src: 'Web-node.config',
                dest: 'Web.config'
            }
        },
        clean: {
            prod: ['app/content/app_production*.css',
                'app/css_components/app_components*.css',
                'app/min/*.js',
                'app/min/*.map'],
            dev_node: ['bin/**', 'Global.asax*']
        },
        injector: {
            options: {
                addRootSlash: false,
                ignorePath: ['app/js_components/jquery.min.js', 'app/js_components/angular.min.js']
            },
            dev: {
                files: {
                    'index.html': [
                        [
                            './app/js_components/string.js',
                            './app/js_components/bowser.min.js',
                            './app/js_components/angular-locale_es-ar.js',
                            './app/js_components/tinymce.min.js',
                            './app/js_components/tinymce.js',
                            './app/js_components/bootstrap.min.js',
                            './app/js_components/hmac-sha1.js',
                            './app/js_components/hmac-sha256.js',
                            './app/js_components/aes.js',
                            './app/js_components/pbkdf2.js',
                            './app/js_components/sha1.js',
                            './app/js_components/md5.js',
                            './app/js_components/enc-base64-min.js',
                            './app/js_components/underscore-min.js',
                            './app/js_components/moment-with-locales.js',
                            './app/js_components/moment-duration-format.js',
                            './app/js_components/humanize-duration.js',
                            './app/js_components/pnotify.js',
                            './app/js_components/pnotify.desktop.js',
                            './app/js_components/pnotify.buttons.js',
                            './app/js_components/angular-underscore.min.js',
                            './app/js_components/angular-linkify.js',
                            './app/js_components/stacktrace.js',
                            './app/js_components/scrollglue.js',
                            './app/js_components/ivh-treeview.js',
                            './app/js_components/json-formatter.min.js',
                            './app/js_components/highcharts.src.js',
                            './app/js_components/highcharts-ng.js',
                            './app/js_components/angular-cookies.min.js',
                            './app/js_components/angular-messages.js',
                            './app/js_components/uploader.js',
                            './app/js_components/angular-file-upload.js',
                            './app/js_components/emoji-button.js',
                            './app/js_components/angular.panels.js',
                            './app/js_components/ui-bootstrap-tpls-2.5.0.js',
                            './app/js_components/angular-bootstrap-checkbox.js',
                            './app/js_components/ng-tags-input.js',
                            './app/js_components/angular-timer.min.js',
                            './app/js_components/angular-resource.min.js',
                            './app/js_components/angular-translate.min.js',
                            './app/js_components/angular-translate-loader-url.js',
                            './app/js_components/angular-translate-loader-static-files.min.js',
                            './app/js_components/angular-route.min.js',
                            './app/js_components/angular-sanitize.min.js',
                            './app/js_components/angular-block-ui.js',
                            './app/js_components/angular-toastr.tpls.min.js',
                            './app/js_components/angular-touch.min.js',
                            './app/js_components/angular-modal-service.js',
                            './app/js_components/angular-animate.min.js',
                            './app/js_components/angular-dragdrop.js',
                            './app/js_components/plugins/colorpicker/plugin.min.js',
                            './app/js_components/plugins/spellchecker/plugin.min.js',
                            './app/js_components/plugins/textcolor/plugin.min.js',
                            './app/js_components/plugins/paste/plugin.min.js',
                            './app/js_components/plugins/noneditable/plugin.min.js',
                            './app/js_components/plugins/autolink/plugin.min.js',
                            './app/js_components/plugins/code/plugin.min.js',
                            './app/js_components/plugins/autoresize/plugin.min.js',
                            './app/js_components/plugins/table/plugin.min.js',
                            './app/js_components/plugins/lists/plugin.min.js',
                            './app/js_components/themes/**/*.js',
                            './app/js_components/skins/**/*.js',
                            './app/js_components/langs/*.js',
                            './app/js_components/jquery-scrolltofixed.js',
                            './app/js_components/twitter-text-3.0.1.js',
                            './app/js_components/leader-line.min.js',
                            './app/app.module.js',
                            './app/app.config.js',
                            './app/app.route.js',
                            './app/js_components/onErrorSrc.js',
                            './app/js_components/exceptionOverwrite.js',
                            './app/answers/emoji-picker.directive.js',
                            './app/answers/**/*.js',
                            './app/authentication/*.js',
                            './app/config/*.js',
                            './app/core/*.js',
                            './app/filters/*.js',
                            './app/layout/*.js',
                            './app/cases/*.js',
                            './app/messages/*.js',
                            './app/modals/*.js',
                            './app/panels/*.js',
                            './app/profile/*.js',
                            './app/services/*.js',
                            './app/js_components/emojis-list.js',
                            './app/js_components/urlSearchParam.js',
                            './app/js_components/ng-map.min.js',
                            './app/js_components/showdown.min.js',
                            './app/js_components/signalr.min.js',
                            './app/js_components/html2canvas.min.js',
                            './app/js_components/webrtc_adaptor.js'
                        ],
                        [
                            './app/css_components/bootstrap.min.css',
                            './app/css_components/fontawesome.css',
                            './app/css_components/AdminLTE.css',
                            './app/content/main.css',
                            './app/content/fonts.css',
                            './app/css_components/angular-block-ui.css',
                            './app/css_components/angular-toastr.min.css',
                            './app/css_components/angular.panels.css',
                            './app/css_components/ng-tags-input.min.css',
                            './app/css_components/ng-tags-input.bootstrap.min.css',
                            './app/css_components/pnotify.css',
                            './app/css_components/ivh-treeview.css',
                            './app/css_components/ivh-treeview-theme-basic.css'
                        ]
                    ]
                }
            },
            prod: {
                files: {
                    'index.html': [
                        './app/min/angularbundle.js',
                        './app/core/config.js',
                        './app/min/appbundle.js',
                        './app/css_components/app_components_production.min.css',
                        './app/content/app_production.min.css',
                        './app/js_components/emojis-list.js'
                    ]
                }
            },
            prod_cloud: {
                files: {
                    'index.html': [
                        './app/min/angularbundle.js',
                        './app/core/config.js',
                        './app/min/appbundle.js',
                        './app/js_components/emojis-list.js',
                        './app/css_components/app_components_production.min.css',
                        './app/content/app_production.min.css'
                    ]
                }
            }
        },
        htmlbuild: {
            cloud: {
                src: 'index.html',
                options: {
                    keepTags: true,
                    replace: true,
                    relative: false,
                    styles: {
                        cloudcss: [
                            "https://fonts.googleapis.com/css2?family=Open+Sans+Condensed:ital,wght@0,300;1,300&family=Open+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap"
                        ]
                    },
                    scripts: {
                        basic: [
                            "https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js",
                            "https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js",
                            "https://cdnjs.cloudflare.com/ajax/libs/angular.js/1.4.7/angular.min.js",
                        ],
                        basic_async: [
                            "https://kit.fontawesome.com/b13d80c9ac.js",
                            "https://cdn.jsdelivr.net/npm/@twemoji/api@latest/dist/twemoji.min.js",
                            "https://unpkg.com/libphonenumber-js@latest/bundle/libphonenumber-max.js",
                            "./app/js_components/zxcvbn.js"
                        ]
                    }
                }
            },
            onpremise: {
                src: 'index.html',
                options: {
                    keepTags: true,
                    replace: true,
                    relative: false,
                    styles: {
                        cloudcss: []
                    },
                    scripts: {
                        basic: [
                            "./app/js_components/jquery.min.js",
                            "./app/js_components/angular.min.js"
                        ],
                        basic_async: [
                            "./app/js_components/twemoji.min.js",
                            "./app/js_components/zxcvbn.js",
                            "./app/js_components/libphonenumber-max.js"
                        ]
                    }
                }
            }
        },
        uglify: {
            options: {
                mangle: false,
                sourceMap: {
                    includeSources: true
                },
                beautify: false
            },
            my_target: {
                files: {
                    'app/min/appbundle.js': [
                        'app/app.module.js',
                        'app/app.config.js',
                        'app/app.route.js',
                        'app/services/*.js',
                        'app/answers/**/*.js',
                        'app/authentication/*.js',
                        'app/config/*.js',
                        'app/core/*.js',
                        'app/filters/*.js',
                        'app/layout/*.js',
                        'app/cases/*.js',
                        'app/messages/*.js',
                        'app/modals/*.js',
                        'app/panels/*.js',
                        'app/profile/*.js',
                        '!app/core/config.js'
                    ],
                    'app/min/angularbundle.js': [
                        'app/js_components/string.js',
                        'app/js_components/bowser.min.js',
                        'app/js_components/angular-locale_es-ar.js',
                        'app/js_components/tinymce.min.js',
                        'app/js_components/tinymce.js',
                        'app/js_components/bootstrap.min.js',
                        'app/js_components/hmac-sha1.js',
                        'app/js_components/hmac-sha256.js',
                        'app/js_components/aes.js',
                        'app/js_components/pbkdf2.js',
                        'app/js_components/sha1.js',
                        'app/js_components/md5.js',
                        'app/js_components/enc-base64-min.js',
                        'app/js_components/underscore-min.js',
                        'app/js_components/moment-with-locales.js',
                        'app/js_components/moment-duration-format.js',
                        'app/js_components/humanize-duration.js',
                        'app/js_components/pnotify.js',
                        'app/js_components/pnotify.desktop.js',
                        'app/js_components/pnotify.buttons.js',
                        'app/js_components/angular-underscore.min.js',
                        'app/js_components/angular-linkify.js',
                        'app/js_components/stacktrace.js',
                        'app/js_components/scrollglue.js',
                        'app/js_components/ivh-treeview.js',
                        'app/js_components/json-formatter.min.js',
                        'app/js_components/highcharts.src.js',
                        'app/js_components/highcharts-ng.js',
                        'app/js_components/angular-cookies.min.js',
                        'app/js_components/angular-messages.js',
                        'app/js_components/uploader.js',
                        'app/js_components/angular-file-upload.js',
                        'app/js_components/emoji-button.js',
                        'app/js_components/angular.panels.js',
                        'app/js_components/ui-bootstrap-tpls-2.5.0.js',
                        'app/js_components/angular-bootstrap-checkbox.js',
                        'app/js_components/ng-tags-input.js',
                        'app/js_components/angular-timer.min.js',
                        'app/js_components/angular-resource.min.js',
                        'app/js_components/angular-translate.min.js',
                        'app/js_components/angular-translate-loader-url.js',
                        'app/js_components/angular-translate-loader-static-files.min.js',
                        'app/js_components/angular-route.min.js',
                        'app/js_components/angular-sanitize.min.js',
                        'app/js_components/angular-block-ui.js',
                        'app/js_components/angular-toastr.tpls.min.js',
                        'app/js_components/angular-touch.min.js',
                        'app/js_components/angular-modal-service.js',
                        'app/js_components/angular-animate.min.js',
                        'app/js_components/angular-dragdrop.js',
                        'app/js_components/plugins/colorpicker/plugin.min.js',
                        'app/js_components/plugins/spellchecker/plugin.min.js',
                        'app/js_components/plugins/textcolor/plugin.min.js',
                        'app/js_components/plugins/paste/plugin.min.js',
                        'app/js_components/plugins/noneditable/plugin.min.js',
                        'app/js_components/plugins/autolink/plugin.min.js',
                        'app/js_components/plugins/code/plugin.min.js',
                        'app/js_components/plugins/autoresize/plugin.min.js',
                        'app/js_components/plugins/table/plugin.min.js',
                        'app/js_components/plugins/lists/plugin.min.js',
                        'app/js_components/themes/**/*.js',
                        'app/js_components/skins/**/*.js',
                        'app/js_components/langs/*.js',
                        'app/js_components/jquery-scrolltofixed.js',
                        'app/js_components/twitter-text-3.0.1.js',
                        'app/js_components/urlSearchParam.js',
                        'app/js_components/ng-map.min.js',
                        'app/js_components/leader-line.min.js',
                        'app/js_components/onErrorSrc.js',
                        'app/js_components/showdown.min.js',
                        'app/js_components/signalr.min.js',
                        'app/js_components/html2canvas.min.js',
                        'app/js_components/webrtc_adaptor.js'
                    ]
                }
            }
        },
        cssmin: {
            onpremise: {
                files: [{
                    'app/css_components/app_components_production.min.css': [
                        'app/css_components/bootstrap.min.css',
                        'app/css_components/fontawesome.css',
                        'app/css_components/AdminLTE.css',
                        'app/css_components/angular-block-ui.css',
                        'app/css_components/angular-toastr.min.css',
                        'app/css_components/angular.panels.css',
                        'app/css_components/ng-tags-input.min.css',
                        'app/css_components/ng-tags-input.bootstrap.min.css',
                        'app/css_components/pnotify.css',
                        'app/css_components/ivh-treeview.css',
                        'app/css_components/ivh-treeview-theme-basic.css'
                    ],
                    'app/content/app_production.min.css': [
                        'app/content/main.css',
                        'app/content/fonts.css'
                    ]
                }]
            },
            cloud: {
                files: [{
                    'app/css_components/app_components_production.min.css': [
                        'app/css_components/bootstrap.min.css',
                        'app/css_components/AdminLTE.css',
                        'app/css_components/angular-block-ui.css',
                        'app/css_components/angular-toastr.min.css',
                        'app/css_components/angular.panels.css',
                        'app/css_components/ng-tags-input.min.css',
                        'app/css_components/ng-tags-input.bootstrap.min.css',
                        'app/css_components/pnotify.css',
                        'app/css_components/ivh-treeview.css',
                        'app/css_components/ivh-treeview-theme-basic.css'
                    ],
                    'app/content/app_production.min.css': [
                        'app/content/main.css',
                        'app/content/fonts.css'
                    ]
                }]
            }
        },
        ngconstant: {
            // Options for all targets
            options: {
                space: '  ',
                wrap: '(function() {\n "use strict";\n\n {\%= __ngModule %} \n})();',
                name: 'config'
            },
            // Environment targets
            dev: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'http://localhost/Social/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: false,
                        doNotLogToServer: true
                    }
                }
            },
            // Environment targets
            dev_https: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'https://dev.ysocial.net/Social/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: false,
                        doNotLogToServer: true
                    }
                }
            },
            dev_https_log_grafana: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'https://dev.ysocial.net/Social/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: false,
                        doNotLogToServer: false,
                        logConfig: {
                            type: 'grafana',
                            url: 'http://10.5.0.4:3100/loki/api/v1/push',
                            labels: {
                                job: 'webagent',
                                clientId: 'malber-pc'
                            }
                        }
                    }
                }
            },
            dev_https_cloud: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'https://dev.ysocial.net/Social/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: true,
                        doNotLogToServer: true
                    }
                }
            },
            prod: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'https://s01.ysocial.net/demo/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: true,
                        doNotLogToServer: true,
                        doNotDisconnectOnRequestError: true
                    }
                }
            },
            test: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    CONFIG_INFO: {
                        baseUrl: 'https://qa.ysocial.net/test/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: true,
                        doNotLogToServer: true,
						doNotDisconnectOnRequestError: true
                    }
                }
            },
            cablevision_prod: {
                options: {
                    dest: 'app/core/config.js'
                },
                constants: {
                    'CONFIG_INFO': {
                        baseUrl: 'https://srv-socialt.corp.cablevision.com.ar/Social/services/',
                        version: version + '_' + webAgentVersion,
                        inTheCloud: false,
                        doNotLogToServer: true
                    }
                }
            }
        },
        jshint: {
            options: {
                esversion: 11
            },
            all: ['app/**/*.js', '!app/js_components/**/*.js', '!app/min/**/*.js']
        },
        cacheBust: {
            taskName: {
                options: {
                    assets: ['./app/min/*.js', './app/css_components/app_components_production.min.css', './app/content/app_production.min.css'],
                    deleteOriginals: true
                },
                src: ['index.html']
            }
        },
        less: {
            development: {
                files: {
                    'app/content/main.css': 'app/content/main.less'
                }
            },
            production: {
                options: {
                    compress: true
                },
                files: {
                    'app/content/main.css': 'app/content/main.less'
                }
            }
        }
    });
    grunt.file.defaultEncoding = 'utf8';

    grunt.loadNpmTasks('grunt-msbuild');
    grunt.loadNpmTasks('grunt-contrib-copy');
    grunt.loadNpmTasks('grunt-contrib-uglify');
    grunt.loadNpmTasks('grunt-contrib-clean');
    grunt.loadNpmTasks('grunt-ng-annotate');
    grunt.loadNpmTasks('grunt-contrib-cssmin');
    grunt.loadNpmTasks('grunt-injector');
    grunt.loadNpmTasks('grunt-contrib-jshint');
    grunt.loadNpmTasks('grunt-ng-constant');
    grunt.loadNpmTasks('grunt-cache-bust');
    grunt.loadNpmTasks('grunt-html-build');
    grunt.loadNpmTasks('grunt-contrib-less');

    grunt.registerTask('dev', ['clean:prod', 'less:development', 'ngconstant:dev', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev']);
    grunt.registerTask('dev_https', ['clean:prod', 'less:development', 'ngconstant:dev_https', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev']);
    grunt.registerTask('dev_https_log_grafana', ['clean:prod', 'less:development', 'ngconstant:dev_https_log_grafana', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev']);
    grunt.registerTask('dev_https_vs2022', ['clean:prod', 'less:development', 'ngconstant:dev_https', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev_vs2022']);
    grunt.registerTask('dev_https_vs2019', ['clean:prod', 'less:development', 'ngconstant:dev_https', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev_vs2019']);
    grunt.registerTask('dev_https_cloud', ['clean:prod', 'less:development', 'ngconstant:dev_https_cloud', 'injector:dev', 'htmlbuild:cloud', 'copy:dev', 'msbuild:dev']);
    grunt.registerTask('dev_https_cloud_prod', ['clean:prod', 'less:development', 'copy:dev', 'jshint', 'ngconstant:dev_https_cloud', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:dev']);
    grunt.registerTask('dev_https_prod', ['clean:prod', 'less:development', 'copy:dev', 'jshint', 'ngconstant:dev_https', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:dev']);
    grunt.registerTask('dev_vs2015', ['clean:prod', 'less:development', 'ngconstant:dev', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev', 'msbuild:dev_vs2015']);
    grunt.registerTask('dev_node', ['clean:prod', 'less:development', 'clean:dev_node', 'ngconstant:dev', 'injector:dev', 'htmlbuild:onpremise', 'copy:dev_node']);
    grunt.registerTask('dev_test', ['clean:prod', 'less:development', 'jshint', 'ngconstant:dev', 'uglify', 'cssmin:cloud', 'injector:prod', 'htmlbuild:onpremise', 'cacheBust', 'copy:dev', 'msbuild:dev']);
    grunt.registerTask('prod', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:prod', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('prod_2022', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('test_vs2022', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud_vs2022', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('prod_pipeline', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud_pipeline', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('test_vs2019', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud_vs2019', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('test', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('test_nocompile', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('cloud', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:test', 'uglify', 'cssmin:cloud', 'injector:prod_cloud', 'htmlbuild:cloud', 'cacheBust', 'msbuild:prod_cloud', 'copy:prod_cloud', 'clean:prod']);
    grunt.registerTask('cablevision_prod', ['clean:prod', 'less:production', 'copy:dev', 'jshint', 'ngconstant:cablevision_prod', 'uglify', 'cssmin:onpremise', 'injector:prod', 'htmlbuild:onpremise', 'cacheBust', 'msbuild:prod', 'copy:prod', 'clean:prod']);
};
