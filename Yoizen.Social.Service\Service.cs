﻿using Azure.Messaging.ServiceBus;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Mail;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.Service
{
	public partial class Service : ServicePreshutdownBase
	{
		#region Constants

		private const int RefreshInterval = 35000;
		private DateTime lastLicenseInvalidMailSent = DateTime.MinValue;
		private DateTime lastOutOfFreeSpaceMailSent = DateTime.MinValue;
		private DateTime lastSocialWebKeepAlive = DateTime.MinValue;

		#endregion

		#region Fields

		private System.Threading.Timer timer = null;
		private Dictionary<int, Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType>> services = null;
		private bool stopping = false;
		private int refreshInterval;
		private string pathForFiles;
		private List<DomainModel.SocialUser> blockedUsers;
		private DateTime lastReplyDate = DateTime.MinValue;
		private bool doingWork = false;

		private Azure.Messaging.ServiceBus.ServiceBusClient sbClient = null;
		private Azure.Messaging.ServiceBus.ServiceBusProcessor sbProcessorReplies = null;
		private Azure.Messaging.ServiceBus.ServiceBusSessionProcessor sbProcessorSends = null;
		private Azure.Messaging.ServiceBus.ServiceBusProcessor sbProcessorTemplateSends = null;
		private bool startServiceBusForReplies = false;
		private HttpClient client = null;
		private HttpClient clientTemplates = null;

		/// <summary>
		/// The set of characters that are unreserved in RFC 2396 but are NOT unreserved in RFC 3986.
		/// </summary>
		private static readonly string[] UriRfc3986CharsToEscape = new[] { "!", "*", "'", "(", ")" };

		private static string accessToken = "olasp/3qW5GzUq5qqXRiz3zNYVcaxcQhGb5nhrLdxXs=";
		private static string accessTokenSecret = "yGHKd/vHQ22uGS7ewTkmMw==";

		#endregion

		#region Constructors

		public Service()
			: base()
		{
			InitializeComponent();
			this.CanShutdown = false;

			services = new Dictionary<int, Tuple<DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType>>();

			try
			{
				if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["RefreshInterval"], out this.refreshInterval))
				{
					if (this.refreshInterval < 5000)
						this.refreshInterval = 5000;
				}
				else
				{
					this.refreshInterval = RefreshInterval;
				}
			}
			catch
			{
				this.refreshInterval = RefreshInterval;
			}

			try
			{
				bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["StartServiceBusForReplies"], out this.startServiceBusForReplies);
				if (this.startServiceBusForReplies)
					Tracer.TraceVerb("Se iniciará el procesador del service bus de respuestas");
			}
			catch { }
		}

		#endregion

		#region ServiceBase Methods

		protected override void OnStart(string[] args)
		{
			CultureInfo culture = new CultureInfo("es-AR");
			System.Threading.Thread.CurrentThread.CurrentCulture = culture;
			System.Threading.Thread.CurrentThread.CurrentUICulture = culture;

			System.Net.ServicePointManager.SecurityProtocol |= System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;

			StartService();

			Tracer.TraceInfo("Iniciado correctamente");
			this.EventLog.WriteEntry("Servicio iniciado correctamente", System.Diagnostics.EventLogEntryType.Information);
		}

		protected override void OnStop()
		{
			if (Preshutdown)
				Tracer.TraceInfo("El servicio detectó que se apagará el equipo");
			else
				Tracer.TraceInfo("Solicitud de detener el servicio");

			this.stopping = true;

#if DEBUG
			Tracer.TraceInfo("Deteniendo timer");
#endif
			if (timer != null)
			{
				timer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
				timer.Dispose();
				timer = null;
			}

			if (this.sbProcessorReplies != null)
			{
				if (this.sbProcessorReplies.IsProcessing)
					this.sbProcessorReplies.StopProcessingAsync().Wait();

				this.sbProcessorReplies.DisposeAsync().ConfigureAwait(false);
				this.sbProcessorReplies = null;
			}

			if (this.sbProcessorSends != null)
			{
				if (this.sbProcessorSends.IsProcessing)
					this.sbProcessorSends.StopProcessingAsync().Wait();

				this.sbProcessorSends.DisposeAsync().ConfigureAwait(false);
				this.sbProcessorSends = null;
			}

			if (this.sbProcessorTemplateSends != null)
			{
				if (this.sbProcessorTemplateSends.IsProcessing)
					this.sbProcessorTemplateSends.StopProcessingAsync().Wait();

				this.sbProcessorTemplateSends.DisposeAsync().ConfigureAwait(false);
				this.sbProcessorTemplateSends = null;
			}

			if (this.sbClient != null)
			{
				this.sbClient.DisposeAsync().ConfigureAwait(false);
				this.sbClient = null;
			}

			if (this.client != null)
			{
				this.client.Dispose();
				this.client = null;
			}

#if DEBUG
			Tracer.TraceInfo("Liberando recursos");
#endif
			if (this.services != null)
			{
				foreach (var service in this.services)
				{
					try
					{
						base.RequestAdditionalTime(2000);
						service.Value.Item2.Dispose();
					}
					catch { }
				}

				this.services.Clear();
				this.services = null;
			}

			base.RequestAdditionalTime(2000);
#if DEBUG
			Tracer.TraceInfo("Guardando valores en la base de datos");
#endif
			try
			{
				DomainModel.SystemStatus.Instance.SocialServiceStatus.Started = false;
				DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.SocialServiceStatus.StatusPath));
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo actualizar los valores en la base de datos: {0}", ex);
			}

			if (Preshutdown)
				Tracer.TraceInfo("Finalizó el procesamiento previo a que se apague el equipo");
			else
				Tracer.TraceInfo("Servicio detenido");
		}

		#endregion

		#region Private Methods

#if RUNASPROGRAM
		public void StartService()
#else
		private void StartService()
#endif
		{
			AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);

			Tracer.TraceInfo("========================================================================");
			Tracer.TraceInfo("Identificación del equipo: {0}", Licensing.LicenseManager.Identification);
			if (!Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				Tracer.TraceError("No hay licencia para utilizar el producto: {0}", Licensing.LicenseManager.Instance.ValidationException);
				this.Stop();
				return;
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Tracer.TraceError("La licencia indica que se trabaja en modo lectura. El servicio no se usa");
				this.Stop();
				return;
			}

			DomainModel.Cache.Instance.Enabled = false;

			this.pathForFiles = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location), "ServiceFiles");
			if (!Directory.Exists(this.pathForFiles))
			{
				Directory.CreateDirectory(this.pathForFiles);
				Tracer.TraceInfo("Se creó el directorio {0} para guardar archivos", pathForFiles);
			}

			try
			{
				SystemSettingsDAO.GetAll();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo cargar la configuración del sistema: {0}", ex);
				this.Stop();
				return;
			}

			if (!LoadServices())
			{
				Tracer.TraceError("No hay servicios configurados para utilizar el producto");
				this.Stop();
				return;
			}

			if (this.startServiceBusForReplies)
			{
				var clientOptions = new Azure.Messaging.ServiceBus.ServiceBusClientOptions();
				clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
				clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
					Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpWebSockets :
					Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpTcp;

				Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

				this.sbClient = new Azure.Messaging.ServiceBus.ServiceBusClient(DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString, clientOptions);

				var processorOptions = new Azure.Messaging.ServiceBus.ServiceBusProcessorOptions()
				{
					AutoCompleteMessages = false,
					MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
					MaxConcurrentCalls = 1
				};

				this.sbProcessorReplies = this.sbClient.CreateProcessor($"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-replies", processorOptions);
				this.sbProcessorReplies.ProcessErrorAsync += ExceptionReceivedHandler;
				this.sbProcessorReplies.ProcessMessageAsync += ProcessMessagesAsync;
				this.sbProcessorReplies.StartProcessingAsync().Wait();
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend)
			{
				if (this.sbClient == null)
				{
					var clientOptions = new Azure.Messaging.ServiceBus.ServiceBusClientOptions();
					clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
					clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
						Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpWebSockets :
						Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpTcp;

					Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

					this.sbClient = new Azure.Messaging.ServiceBus.ServiceBusClient(DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString, clientOptions);
				}

				{
					var processorOptions = new Azure.Messaging.ServiceBus.ServiceBusSessionProcessorOptions()
					{
						AutoCompleteMessages = true,
						MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
						MaxConcurrentSessions = 50,
						MaxConcurrentCallsPerSession = 1,
						SessionIdleTimeout = TimeSpan.FromSeconds(5),
					};

					if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ProcessorSends.MaxConcurrentSessions"]))
						processorOptions.MaxConcurrentSessions = int.Parse(System.Configuration.ConfigurationManager.AppSettings["ProcessorSends.MaxConcurrentSessions"]);
					if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ProcessorSends.MaxConcurrentCallsPerSession"]))
						processorOptions.MaxConcurrentCallsPerSession = int.Parse(System.Configuration.ConfigurationManager.AppSettings["ProcessorSends.MaxConcurrentCallsPerSession"]);

					Tracer.TraceInfo("Se procesarán {0} sesiones en simultáneo, descargando {1} por sesión", processorOptions.MaxConcurrentSessions, processorOptions.MaxConcurrentCallsPerSession);

					this.sbProcessorSends = this.sbClient.CreateSessionProcessor($"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-sends", processorOptions);
					this.sbProcessorSends.ProcessErrorAsync += ExceptionReceivedHandler;
					this.sbProcessorSends.ProcessMessageAsync += ProcessSendsAsync;
					this.sbProcessorSends.StartProcessingAsync().Wait();

					var httpClientHandler = new System.Net.Http.HttpClientHandler();
					httpClientHandler.ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true;
					Tracer.TraceVerb("MaxConnectionsPerServer actual: {0}", httpClientHandler.MaxConnectionsPerServer);
					if (httpClientHandler.MaxConnectionsPerServer < processorOptions.MaxConcurrentSessions)
						httpClientHandler.MaxConnectionsPerServer = processorOptions.MaxConcurrentSessions;

					this.client = new HttpClient(httpClientHandler);
					if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"]) &&
						int.TryParse(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"], out int timeout) &&
						timeout > 0)
					{
						Tracer.TraceInfo("Se utilizará un timeout de {0} segundos", timeout);
						this.client.Timeout = TimeSpan.FromSeconds(timeout);
					}
					else
					{
						this.client.Timeout = TimeSpan.FromSeconds(5);
					}
				}

				{
					var processorOptions = new Azure.Messaging.ServiceBus.ServiceBusProcessorOptions()
					{
						AutoCompleteMessages = true,
						MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
						MaxConcurrentCalls = 50
					};

					if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ProcessorTemplateSends.MaxConcurrentCalls"]))
						processorOptions.MaxConcurrentCalls = int.Parse(System.Configuration.ConfigurationManager.AppSettings["ProcessorTemplateSends.MaxConcurrentCalls"]);

					Tracer.TraceInfo("Se procesarán {0} sesiones en simultáneo para envío de templates", processorOptions.MaxConcurrentCalls);

					this.sbProcessorTemplateSends = this.sbClient.CreateProcessor($"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-template-sends", processorOptions);
					this.sbProcessorTemplateSends.ProcessErrorAsync += ExceptionReceivedHandler;
					this.sbProcessorTemplateSends.ProcessMessageAsync += ProcessTemplateSendsAsync;
					this.sbProcessorTemplateSends.StartProcessingAsync().Wait();

					var httpClientHandler = new System.Net.Http.HttpClientHandler();
					httpClientHandler.ServerCertificateCustomValidationCallback = (requestMessage, certificate, chain, sslErrors) => true;
					Tracer.TraceVerb("MaxConnectionsPerServer actual: {0}", httpClientHandler.MaxConnectionsPerServer);
					if (httpClientHandler.MaxConnectionsPerServer < processorOptions.MaxConcurrentCalls)
						httpClientHandler.MaxConnectionsPerServer = processorOptions.MaxConcurrentCalls;

					this.clientTemplates = new HttpClient(httpClientHandler);
					if (!string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"]) &&
						int.TryParse(System.Configuration.ConfigurationManager.AppSettings["HttpClient.Timeout"], out int timeout) &&
						timeout > 0)
					{
						Tracer.TraceInfo("Se utilizará un timeout de {0} segundos", timeout);
						this.clientTemplates.Timeout = TimeSpan.FromSeconds(timeout);
					}
					else
					{
						this.clientTemplates.Timeout = TimeSpan.FromSeconds(5);
					}
				}
			}

			DomainModel.SystemStatus.Instance.SocialServiceStatus.Started = true;
			DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.SocialServiceStatus.StatusPath));

			if (services.Count > 0)
			{
				Tracer.TraceInfo("Iniciando servicio con {0} servicios de redes sociales configurados", services.Count);
				this.doingWork = false;
#if RUNASPROGRAM
				DoWork().Wait();
#else
				Tracer.TraceInfo("Se utilizará un tiempo de refresco de {0} milisegundos ({1} segundos)", this.refreshInterval, this.refreshInterval / 1000);
				timer = new System.Threading.Timer(async (c) => { await DoWork(); }, null, 5000, this.refreshInterval);
#endif
			}
		}

		private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
		{
			Tracer.TraceError("Ocurrió un error no manejado en el servicio: {0}", e.ExceptionObject);
		}

		private System.Threading.Tasks.Task ExceptionReceivedHandler(Azure.Messaging.ServiceBus.ProcessErrorEventArgs exceptionReceivedEventArgs)
		{
			StringBuilder sb = new StringBuilder();
#if DEBUG
			sb.AppendLine($"Message handler encountered an exception {exceptionReceivedEventArgs.Exception}.");
#else
			sb.AppendLine($"Message handler encountered an exception {exceptionReceivedEventArgs.Exception.Message}.");
#endif
			var context = exceptionReceivedEventArgs.ErrorSource;
			sb.AppendLine("Exception context for troubleshooting:");
			sb.AppendLine($"- ErrorSource: {exceptionReceivedEventArgs.ErrorSource}");
			sb.AppendLine($"- FullyQualifiedNamespace: {exceptionReceivedEventArgs.FullyQualifiedNamespace}");
			sb.AppendLine($"- Executing EntityPath: {exceptionReceivedEventArgs.EntityPath}");
			Tracer.TraceError("Ocurrió un error obteniendo mensajes de la cola: {0}", sb.ToString());
			return System.Threading.Tasks.Task.CompletedTask;
		}

		private async System.Threading.Tasks.Task ProcessMessagesAsync(Azure.Messaging.ServiceBus.ProcessMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var body = args.Message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de respuestas con número de secuencia {0} y contenido {1}", new Dictionary<string, object>()
			{
				{ DomainModel.TracingCommonProperties.ServiceBusSeq, args.Message.SequenceNumber }
			}, args.Message.SequenceNumber, body);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);
			var messageId = jBody["messageId"].ToObject<long>();

			var relatedEntitiesToRead = new MessageDAO.RelatedEntitiesToRead(false)
			{
				RepliesTo = true,
				AssociatedMessage = true,
				Service = true,
				Attachments = true,
				RepliedBy = true,
				RepliesToSocialUser = true,
				Queue = true
			};

			var message = DAL.MessageDAO.GetOne(messageId, relatedEntitiesToRead);
			if (message == null)
			{
				Tracer.TraceError("No se encontró el mensaje {0} para ser utilizado para responder", messageId);
				return;
			}

			if (message.Service == null)
			{
				Tracer.TraceError("No se encontró el servicio del mensaje {0} para ser utilizado para responder", message);
				return;
			}

			if (!this.services.ContainsKey(message.Service.ID))
			{
				Tracer.TraceError("No se encontró el servicio {0} del mensaje {1} para ser utilizado para responder", message.Service.ID, message);
				return;
			}

			if (message.RequiresAuthorization == true && (message.Authorized == null || message.Authorized == false))
			{
				Tracer.TraceVerb("No se enviará el mensaje {0} porque requiere autorización", message);
				return;
			}

			if (message.DeliveryRetries != null)
			{
				if (message.DeliveryRetries.Value >= DomainModel.SystemSettings.Instance.MaxRetriesForOutgoingMessages)
				{
					Tracer.TraceVerb("No se enviará el mensaje {0} porque superó la cantidad de reintentos", message);
					return;
				}
			}

			var socialService = this.services[message.Service.ID].Item2;

			var messageHasBeenSent = false;

			try
			{
				//await args.RenewMessageLockAsync(args.Message);

				Tracer.TraceInfo("Enviando el mensaje {0} - {1}", message.ID, message);
				message.SocialMessageID = await socialService.Reply(message);

				messageHasBeenSent = true;

				if (socialService.ShouldPersistSocialMessageIDInStorage())
				{
					await DomainModel.StorageManager.Instance.SaveOutgoingMessageAsync(messageId, socialService.SocialServiceType, message.SocialMessageID, socialService.ID, socialService.ServiceType);
				}

				Tracer.TraceInfo("Actualizando el mensaje {0} con el código de mensaje de la red social {1}", message.ID, message.SocialMessageID);
				DAL.MessageDAO.UpdateSocialMessage(message.ID, message.SocialMessageID, message.Parameters);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(message,
						Newtonsoft.Json.Linq.JObject.FromObject(new
						{
							ID = message.ID,
							SocialMessageID = message.SocialMessageID
						}));
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}", message.ID, socialService.Name, ex);

				var type = ex.GetType();

				if (type == typeof(Business.Exceptions.ReplyException))
				{
					var rex = ex as Business.Exceptions.ReplyException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, rex.ErrorMessage, rex.ErrorCode, rex.ShouldRetry);

					if (rex.ShouldRetry)
						await args.AbandonMessageAsync(args.Message);
				}
				else if (type == typeof(Business.Exceptions.ServiceException))
				{
					var sex = ex as Business.Exceptions.ServiceException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, sex.ErrorMessage, sex.ErrorCode, false);
				}
				else if (type == typeof(ServiceBusException) ||
					type.IsSubclassOf(typeof(ServiceBusException)))
				{
					var sbex = ex as ServiceBusException;
					DAL.MessageDAO.UpdateDelivered(message.ID, false, "Unknown error", (int) sbex.Reason, !messageHasBeenSent);

					if (!messageHasBeenSent)
						await args.AbandonMessageAsync(args.Message);
				}
				else
				{
					DAL.MessageDAO.UpdateDelivered(message.ID, false, ex.Message, false);
				}
			}
		}

		private async System.Threading.Tasks.Task ProcessSendsAsync(Azure.Messaging.ServiceBus.ProcessSessionMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var body = args.Message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de envíos con número de secuencia {0} y contenido {1}", new Dictionary<string, object>()
			{
				{ DomainModel.TracingCommonProperties.ServiceBusSeq, args.Message.SequenceNumber }
			}, args.Message.SequenceNumber, body);

			var jInfo = Newtonsoft.Json.Linq.JObject.Parse(body);
			var messageId = jInfo["messageId"].ToObject<long>();
			var serviceId = jInfo["serviceId"].ToObject<int>();

			var socialService = this.services[serviceId].Item2;

			var messageHasBeenSent = false;

			try
			{
				var jRequest = (Newtonsoft.Json.Linq.JObject) jInfo["request"];

				var method = jRequest["method"].ToString();
				var url = jRequest["url"].ToString();
				using (var requestMessage = new HttpRequestMessage(new HttpMethod(method), url))
				{
					if (jRequest["headers"] != null && jRequest["headers"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jHeaders = (Newtonsoft.Json.Linq.JObject) jRequest["headers"];
						foreach (var jProperty in jHeaders.Properties())
						{
							var name = jProperty.Name;
							var jValues = (Newtonsoft.Json.Linq.JArray) jHeaders[name];

							foreach (Newtonsoft.Json.Linq.JValue jValue in jValues)
							{
								requestMessage.Headers.Add(name, jValue.ToString());
							}
						}
					}

					if (jRequest["content"] != null && jRequest["content"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jContent = jRequest["content"];

						var content = jContent["content"].ToString();

						Tracer.TraceVerb("Se realizará el {0} a la url {1} con body: {2}", method, url, content);

						var stringContent = new StringContent(content);

						var jHeaders = (Newtonsoft.Json.Linq.JObject) jContent["headers"];
						foreach (var jProperty in jHeaders.Properties())
						{
							var name = jProperty.Name;
							var jValues = (Newtonsoft.Json.Linq.JArray) jHeaders[name];

							if (name.Equals("Content-Type", StringComparison.InvariantCultureIgnoreCase))
							{
								var mediaType = jValues[0].ToString();
								string charset = null;
								int index;
								if ((index = mediaType.IndexOf(";")) >= 0)
								{
									charset = mediaType.Substring(mediaType.IndexOf("=")).TrimStart('=');
									mediaType = mediaType.Substring(0, index).TrimEnd(';');
								}

								var headerValue = new MediaTypeHeaderValue(mediaType);
								if (charset != null)
									headerValue.CharSet = charset;

								stringContent.Headers.ContentType = headerValue;
							}
							else if (name.Equals("Content-Length", StringComparison.InvariantCultureIgnoreCase))
							{
								continue;
							}
							else
							{
								foreach (Newtonsoft.Json.Linq.JValue jValue in jValues)
								{
									try
									{
										stringContent.Headers.Add(name, jValue.ToString());
									}
									catch { }
								}
							}
						}

						requestMessage.Content = stringContent;
					}

					Tracer.TraceInfo("Enviando el mensaje {0}", messageId);

					using (var response = await this.client.SendAsync(requestMessage))
					{
						Tracer.TraceInfo("Se terminó de invocar el request para el mensaje {0}", messageId);

						if (response.IsSuccessStatusCode)
						{
							string replyId = null;

							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var jsonResponse = await sr.ReadToEndAsync();
									if (string.IsNullOrEmpty(jsonResponse))
									{
										Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje {0} exitosamente pero no se obtuvo respuesta", messageId);
										replyId = string.Format("{0}_reply", Guid.NewGuid());
									}
									else
									{
										if (response.Content.Headers.ContentType == null ||
											string.IsNullOrEmpty(response.Content.Headers.ContentType.MediaType) ||
											(
												!response.Content.Headers.ContentType.MediaType.StartsWith("application/json", StringComparison.InvariantCultureIgnoreCase) &&
												!response.Content.Headers.ContentType.MediaType.StartsWith("text/javascript", StringComparison.InvariantCultureIgnoreCase)
											))
										{
											Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje {0} exitosamente pero se obtuvo una respuesta que no es un JSON: {1}", messageId, jsonResponse);
											replyId = string.Format("{0}_reply", Guid.NewGuid());
										}
										else
										{
											var jResponse = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);

											replyId = socialService.ExtractMessageIDFromResponse(jResponse);
											Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje {0} exitosamente con código {1} y respuesta {2}", messageId, replyId, jsonResponse);
										}
									}
								}
							}
							else
							{
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje {0} exitosamente pero no se obtuvo respuesta", messageId);
								replyId = string.Format("{0}_reply", Guid.NewGuid());
							}

							messageHasBeenSent = true;

							if (socialService.ShouldPersistSocialMessageIDInStorage())
							{
								await DomainModel.StorageManager.Instance.SaveOutgoingMessageAsync(messageId, socialService.SocialServiceType, replyId, socialService.ID, socialService.ServiceType);
							}

							if (jInfo["delay"] != null && jInfo["delay"].Type == Newtonsoft.Json.Linq.JTokenType.Integer)
							{
								var delay = jInfo["delay"].ToObject<int>();
								Tracer.TraceVerb("Se aguardarán {0} milisegundos porque se acaba de enviar un mensaje con multimedia", delay);
								await Task.Delay(delay);
							}

							Tracer.TraceInfo("Actualizando el mensaje {0} con el código de mensaje de la red social {1}", messageId, replyId);
							DAL.MessageDAO.UpdateSocialMessage(messageId, replyId, null);

							if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
								DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
							{
								DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(messageId,
									Newtonsoft.Json.Linq.JObject.FromObject(new
									{
										ID = messageId,
										SocialMessageID = replyId
									}));
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}-{3} - {4}", messageId, socialService.Name, response.ReasonPhrase, response.StatusCode, json);

									try
									{
										var jResponse = Newtonsoft.Json.Linq.JObject.Parse(json);
										socialService.ExtractErrorInfoFromResponse(response.StatusCode, jResponse, out string errorMessage, out int? errorCode, out bool shouldRetry);

										DAL.MessageDAO.UpdateDelivered(messageId, false, errorMessage, errorCode, shouldRetry);

										if (shouldRetry)
											await args.AbandonMessageAsync(args.Message);
									}
									catch
									{
										DAL.MessageDAO.UpdateDelivered(messageId, false, json, false);
									}
								}
							}
							else
							{
								Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}-{3}", messageId, socialService.Name, response.ReasonPhrase, response.StatusCode);

								DAL.MessageDAO.UpdateDelivered(messageId, false, response.ReasonPhrase, false);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				var type = ex.GetType();

				if (!messageHasBeenSent)
				{
					await args.AbandonMessageAsync(args.Message);

					Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}", messageId, socialService.Name, ex);

					if (type == typeof(ServiceBusException) ||
						type.IsSubclassOf(typeof(ServiceBusException)))
					{
						var sbex = ex as ServiceBusException;
						DAL.MessageDAO.UpdateDelivered(messageId, false, "Unknown error", (int) sbex.Reason, true);
					}
					else
					{
						DAL.MessageDAO.UpdateDelivered(messageId, false, ex.Message, false);
					}
				}
				else
				{
					Tracer.TraceInfo("Falló el procesamiento posterior a la respuesta del mensaje {0} del servicio {1}: {2}", messageId, socialService.Name, ex);
				}
			}
		}

		private async System.Threading.Tasks.Task ProcessTemplateSendsAsync(Azure.Messaging.ServiceBus.ProcessMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var body = args.Message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de envíos con número de secuencia {0} y contenido {1}", new Dictionary<string, object>()
			{
				{ DomainModel.TracingCommonProperties.ServiceBusSeq, args.Message.SequenceNumber }
			}, args.Message.SequenceNumber, body);

			var jInfo = Newtonsoft.Json.Linq.JObject.Parse(body);
			var messageId = jInfo["messageId"].ToObject<long>();
			var serviceId = jInfo["serviceId"].ToObject<int>();

			var socialService = this.services[serviceId].Item2;

			try
			{
				var jRequest = (Newtonsoft.Json.Linq.JObject) jInfo["request"];

				var method = jRequest["method"].ToString();
				var url = jRequest["url"].ToString();
				using (var requestMessage = new HttpRequestMessage(new HttpMethod(method), url))
				{
					if (jRequest["headers"] != null && jRequest["headers"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jHeaders = (Newtonsoft.Json.Linq.JObject) jRequest["headers"];
						foreach (var jProperty in jHeaders.Properties())
						{
							var name = jProperty.Name;
							var jValues = (Newtonsoft.Json.Linq.JArray) jHeaders[name];

							foreach (Newtonsoft.Json.Linq.JValue jValue in jValues)
							{
								requestMessage.Headers.Add(name, jValue.ToString());
							}
						}
					}

					if (jRequest["content"] != null && jRequest["content"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
					{
						var jContent = jRequest["content"];

						var content = jContent["content"].ToString();

						Tracer.TraceVerb("Se realizará el {0} a la url {1} con body: {2}", method, url, content);

						var stringContent = new StringContent(content);

						var jHeaders = (Newtonsoft.Json.Linq.JObject) jContent["headers"];
						foreach (var jProperty in jHeaders.Properties())
						{
							var name = jProperty.Name;
							var jValues = (Newtonsoft.Json.Linq.JArray) jHeaders[name];

							if (name.Equals("Content-Type", StringComparison.InvariantCultureIgnoreCase))
							{
								var mediaType = jValues[0].ToString();
								string charset = null;
								int index;
								if ((index = mediaType.IndexOf(";")) >= 0)
								{
									charset = mediaType.Substring(mediaType.IndexOf("=")).TrimStart('=');
									mediaType = mediaType.Substring(0, index).TrimEnd(';');
								}

								var headerValue = new MediaTypeHeaderValue(mediaType);
								if (charset != null)
									headerValue.CharSet = charset;

								stringContent.Headers.ContentType = headerValue;
							}
							else if (name.Equals("Content-Length", StringComparison.InvariantCultureIgnoreCase))
							{
								continue;
							}
							else
							{
								foreach (Newtonsoft.Json.Linq.JValue jValue in jValues)
								{
									try
									{
										stringContent.Headers.Add(name, jValue.ToString());
									}
									catch { }
								}
							}
						}

						requestMessage.Content = stringContent;
					}

					Tracer.TraceInfo("Enviando el mensaje {0}", messageId);

					using (var response = await this.clientTemplates.SendAsync(requestMessage))
					{
						Tracer.TraceInfo("Se terminó de invocar el request para el mensaje {0}", messageId);

						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var jsonResponse = await sr.ReadToEndAsync();
								var jResponse = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);

								var replyId = socialService.ExtractMessageIDFromResponse(jResponse);
								Yoizen.Common.Tracer.TraceInfo("Se envió el mensaje exitosamente con código {0} y respuesta {1}", replyId, jsonResponse);

								if (jInfo["delay"] != null && jInfo["delay"].Type == Newtonsoft.Json.Linq.JTokenType.Integer)
								{
									var delay = jInfo["delay"].ToObject<int>();
									Tracer.TraceVerb("Se aguardarán {0} milisegundos porque se acaba de enviar un mensaje con multimedia", delay);
									await Task.Delay(delay);
								}

								Tracer.TraceInfo("Actualizando el mensaje {0} con el código de mensaje de la red social {1}", messageId, replyId);
								DAL.MessageDAO.UpdateSocialMessage(messageId, replyId, null);

								if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
									DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
								{
									DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreMessageNews(messageId,
										Newtonsoft.Json.Linq.JObject.FromObject(new
										{
											ID = messageId,
											SocialMessageID = replyId
										}));
								}
							}
						}
						else
						{
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}-{3} - {4}", messageId, socialService.Name, response.ReasonPhrase, response.StatusCode, json);

									try
									{
										var jResponse = Newtonsoft.Json.Linq.JObject.Parse(json);
										socialService.ExtractErrorInfoFromResponse(response.StatusCode, jResponse, out string errorMessage, out int? errorCode, out bool shouldRetry);

										DAL.MessageDAO.UpdateDelivered(messageId, false, errorMessage, errorCode, shouldRetry);

										if (shouldRetry)
											await args.AbandonMessageAsync(args.Message);
										else
											await args.CompleteMessageAsync(args.Message);
									}
									catch
									{
										DAL.MessageDAO.UpdateDelivered(messageId, false, json, false);

										await args.CompleteMessageAsync(args.Message);
									}
								}
							}
							else
							{
								Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}-{3}", messageId, socialService.Name, response.ReasonPhrase, response.StatusCode);

								DAL.MessageDAO.UpdateDelivered(messageId, false, response.ReasonPhrase, false);

								await args.CompleteMessageAsync(args.Message);
							}
						}
					}
				}

				await args.CompleteMessageAsync(args.Message);
			}
			catch (Exception ex)
			{
				Tracer.TraceInfo("Falló la respuesta del mensaje {0} del servicio {1}: {2}", messageId, socialService.Name, ex);

				DAL.MessageDAO.UpdateDelivered(messageId, false, ex.Message, false);

				await args.CompleteMessageAsync(args.Message);
			}
		}

		/// <summary>
		/// Carga los servicios a partir de los datos de la licencia y de la base de datos
		/// </summary>
		/// <returns>true si se pudieron configurar; en caso contrario, false</returns>
		private bool LoadServices()
		{
			try
			{
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
				{
					var domainServices = DAL.ServiceDAO.GetAll(false);
					foreach (var domainService in domainServices)
					{
						if (domainService.Type == DomainModel.ServiceTypes.Chat ||
							domainService.Type == DomainModel.ServiceTypes.IntegrationChat ||
							domainService.Type == DomainModel.ServiceTypes.VideoCall)
							continue;

						if (this.services.ContainsKey(domainService.ID))
							continue;

						var socialService = LoadService(domainService, null, null);
						if (socialService != null)
						{
							Tracer.TraceInfo("El servicio {0} será configurado y agregado a la lista de servicios a procesar", domainService.Name);
							var tuple = new Tuple<DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType>(domainService, socialService, null);
							this.services.Add(domainService.ID, tuple);
						}
						else
						{
							Tracer.TraceInfo("El servicio {0} todavía no fue configurado", domainService.Name);
						}
					}
				}
				else
				{
					Licensing.ServiceType[] services = Licensing.LicenseManager.Instance.License.Services;
					if (services == null || services.Length == 0)
						return false;

					foreach (Licensing.ServiceType service in services)
					{
						if (service.Type != DomainModel.ServiceTypes.Chat &&
							service.Type != DomainModel.ServiceTypes.IntegrationChat &&
							service.Type != DomainModel.ServiceTypes.VideoCall)
						{
							if (service != null)
							{
								var tuple = LoadService(service);
								this.services.Add(service.ID, tuple);
							}
							else
							{
								Tracer.TraceInfo("El servicio {0} todavía no fue configurado", service.Name);
								this.services.Add(service.ID, null);
							}
						}
					}
				}

				return true;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló la inicialización de los servicios {0}", ex);
				return false;
			}
		}

		/// <summary>
		/// Intenta carga e inicializar un servicio
		/// </summary>
		/// <param name="service">El <see cref="Licensing.ServiceType"/> que representa un servicio de la licencia</param>
		/// <returns>Devuelve un <see cref="Tuple<DomainModel.Service, ISocialService>"/> con los datos del
		/// servicio de la base de datos y el <see cref="ISocialService"/> inicializado o null en caso de error o
		/// falta de configuración</returns>
		private Tuple<DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType> LoadService(Licensing.ServiceType service)
		{
			Tuple<DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType> tuple = null;
			try
			{
				Tracer.TraceInfo("Intentando inicializar y configurar el servicio {0}", service.Name);

				var domainService = ServiceDAO.GetOne(service.ID, false);
				if (domainService != null && domainService.Queue != null)
				{
					DomainModel.ISocialService socialService = LoadService(domainService, service.ValidUntil, service.AccountId);
					if (domainService.SocialServiceClassType != null)
					{
						tuple = new Tuple<DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType>(domainService, socialService, service);
					}
					else
					{
						Tracer.TraceInfo("El servicio {0} tiene un tipo {1} que no es válido", service.Name, domainService.ClassType);
					}
				}
				else
				{
					Tracer.TraceInfo("El servicio {0} todavía no fue configurado", service.Name);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló la inicialización del servicio {0} ({1})", service.Name, ex);
			}

			return tuple;
		}

		/// <summary>
		/// Intenta cargar e inicializar un servicio
		/// </summary>
		/// <param name="domainService">El <see cref="DomainModel.Service"/> a inicializar</param>
		/// <param name="validUntil">Indica hasta cuándo es válido un servicio o <code>null</code> si no expira</param>
		/// <param name="accountId">El identificador de la cuenta para validar que el servicio tenga dicha cuenta configurada o <code>null</code>
		/// para no validar</param>
		/// <returns>Devuelve la instancia de <see cref="ISocialService"/> creada para el servicio indicado</returns>
		private DomainModel.ISocialService LoadService(DomainModel.Service domainService, DateTime? validUntil, string accountId)
		{
			if (domainService == null)
				throw new ArgumentNullException(nameof(domainService));

			if (domainService.Type == DomainModel.ServiceTypes.Chat ||
				domainService.Type == DomainModel.ServiceTypes.IntegrationChat ||
				domainService.Type == DomainModel.ServiceTypes.VideoCall)
				throw new ArgumentOutOfRangeException(nameof(domainService), "El servicio no puede ser de tipo chat");

			DomainModel.ISocialService socialService = null;
			try
			{
				Tracer.TraceInfo("Intentando inicializar y configurar el servicio {0}", domainService.Name);

				if (Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway || domainService.Queue != null)
				{
					if (domainService.SocialServiceClassType != null)
					{
						socialService = (DomainModel.ISocialService) Activator.CreateInstance(domainService.SocialServiceClassType);
						var serviceConfiguration = (SocialServiceConfiguration) Activator.CreateInstance(socialService.ConfigurationType, domainService.Configuration);
						if (!Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
						{
							if (domainService.Type != ServiceTypes.TwitterSearches)
							{
								var configuredAccountId = serviceConfiguration.GetAccountID();
								if (!string.IsNullOrEmpty(accountId) &&
									!accountId.Equals(configuredAccountId))
								{
									Tracer.TraceInfo("El servicio {0} tiene un código de identificación {1} que no es permitido por la licencia {2}", domainService.Name, configuredAccountId, accountId);
								}
							}
						}

						var serviceStatus = (SocialServiceStatus) Activator.CreateInstance(socialService.StatusType, domainService.Status);

						string servicePathForFiles = Path.Combine(this.pathForFiles, domainService.ID.ToString());
						if (!Directory.Exists(servicePathForFiles))
						{
							Directory.CreateDirectory(servicePathForFiles);
							Tracer.TraceInfo("Se creó el directorio {0} para el servicio {1}", servicePathForFiles, domainService.Name);
						}

						socialService.Initialize(domainService, serviceConfiguration, serviceStatus, validUntil, servicePathForFiles);

						Tracer.TraceInfo("El servicio {0} fue inicializado", domainService.Name);

						if (socialService.Status.IsDirty)
						{
							DAL.ServiceDAO.UpdateStatus(domainService, socialService.Status);
							serviceStatus.ApplyChanges();
						}

						socialService.IsSocialUserBlocked = IsSocialUserBlocked;
					}
					else
					{
						Tracer.TraceInfo("El servicio {0} tiene un tipo {1} que no es válido", domainService.Name, domainService.ClassType);
					}
				}
				else
				{
					Tracer.TraceInfo("El servicio {0} todavía no fue configurado", domainService.Name);
				}

				return socialService;
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló la inicialización del servicio {0} ({1})", domainService.Name, ex);
				throw ex;
			}
		}

		/// <summary>
		/// Realiza las tareas de consultas y respuestas a las redes sociales cada vez que expira el tiempo del timer
		/// </summary>
		private async Task DoWork()
		{
			Tracer.TraceInfo("Iniciando consultas a los servicios de redes sociales");

#if !RUNASPROGRAM
			timer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
#endif

			if (this.doingWork)
				return;

			this.doingWork = true;

			try
			{
				var processStartTime = DateTime.Now;

				if (Licensing.LicenseManager.Instance.IsLicenseValid)
				{
					try
					{
						SystemSettingsDAO.GetAll();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló la actualización de los parámetros del sistema: {0}", ex);
						DomainModel.SystemSettings.Instance.HandleException(ex);

						if (ex.GetType() == typeof(OutOfMemoryException))
							Environment.Exit(1);

#if !RUNASPROGRAM
						timer.Change(this.refreshInterval, this.refreshInterval);
#endif

						return;
					}

					try
					{
						SystemStatusDAO.GetAll();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló la actualización de los parámetros de estado del sistema: {0}", ex);
						DomainModel.SystemSettings.Instance.HandleException(ex);

						if (ex.GetType() == typeof(OutOfMemoryException))
							Environment.Exit(1);

#if !RUNASPROGRAM
						timer.Change(this.refreshInterval, this.refreshInterval);
#endif

						return;
					}

					this.blockedUsers = SocialUserDAO.GetAllByBlockedStatus(true);

					var shouldProcessReplies = false;
#if DEBUG
					if (DateTime.Now.Subtract(this.lastReplyDate).TotalMinutes > 1)
#else
					if (DateTime.Now.Subtract(this.lastReplyDate).TotalMinutes > 5)
#endif
						shouldProcessReplies = true;

					try
					{
						if (Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
						{
							LoadServices();

							foreach (var keyvaluePair in this.services)
							{
								if (this.stopping)
									return;

								Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType> tuple = keyvaluePair.Value;
								await this.ProcessSocialService(tuple.Item1, tuple.Item2, tuple.Item3, shouldProcessReplies);
							}
						}
						else
						{
							foreach (var keyvaluePair in this.services)
							{
								var serviceId = keyvaluePair.Key;
								Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType> tuple = keyvaluePair.Value;
								if (tuple == null)
								{
									var service = Licensing.LicenseManager.Instance.License.FindService(serviceId);
									if (service != null)
										tuple = LoadService(service);
									else
										Tracer.TraceWarning("No se encontró el servicio licenciado con código {0}", serviceId);
								}

								if (tuple != null)
								{
									if (this.stopping)
										return;

									await this.ProcessSocialService(tuple.Item1, tuple.Item2, tuple.Item3, shouldProcessReplies);
								}
							}
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló el procesamiento de los servicios: {0}", ex);
						DomainModel.SystemSettings.Instance.HandleException(ex);
					}

					if (shouldProcessReplies)
						this.lastReplyDate = DateTime.Now;

					if (!string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.AttachmentsRoute) && DomainModel.SystemSettings.Instance.AttachmentsMinimumFreeSpace > 0)
					{
						if (DateTime.Now.Date > this.lastOutOfFreeSpaceMailSent.Date)
						{
							string pathRoot = System.IO.Path.GetPathRoot(DomainModel.SystemSettings.Instance.AttachmentsRoute);
							var driveInfo = new System.IO.DriveInfo(pathRoot);

							if (driveInfo != null)
							{
								var totalFreeSpaceFB = (double) driveInfo.TotalFreeSpace / (double) (1024 * 1024 * 1024);
								if (totalFreeSpaceFB < DomainModel.SystemSettings.Instance.AttachmentsMinimumFreeSpace)
								{
									Tracer.TraceWarning("El disco {0} se está quedando sin espacio ({1} bytes libres)", driveInfo.Name, driveInfo.TotalFreeSpace);

									var settings = DomainModel.SystemSettings.Instance.OutOfDiskSpaceForAttachments;
									string to = settings.Emails;
									Dictionary<string, object> templateParameters = new Dictionary<string, object>();
									templateParameters.Add("@@RUTA@@", driveInfo.Name);
									templateParameters.Add("@@ESPACIO_LIBRE_MINIMO@@", string.Format("{0} GB", DomainModel.SystemSettings.Instance.AttachmentsMinimumFreeSpace));
									templateParameters.Add("@@ESPACIO_LIBRE@@", string.Format("{0} bytes ({1} GB)", driveInfo.TotalFreeSpace, totalFreeSpaceFB));
#if !DEBUG
									DomainModel.SystemSettings.Instance.SendMailMessage(settings.Subject, to, settings.Template, templateParameters);
#endif
									this.lastOutOfFreeSpaceMailSent = DateTime.Now;
								}
							}
						}
					}
				}
				else
				{
					if (DateTime.Now.Date > this.lastLicenseInvalidMailSent.Date)
					{
						Tracer.TraceWarning("No hay licencia para seguir ejecutando el servicio");

						var settings = DomainModel.SystemSettings.Instance.Service.EmailLicenseExpired;
						string to = string.Format("{0},<EMAIL>", settings.Emails);
						Dictionary<string, object> templateParameters = new Dictionary<string, object>();
						templateParameters.Add("@@FECHA@@", Licensing.LicenseManager.Instance.License.ValidUntil.ToString("F"));
#if !DEBUG
						DomainModel.SystemSettings.Instance.SendMailMessage(settings.Subject, to, settings.Template, templateParameters);
#endif

						this.lastLicenseInvalidMailSent = DateTime.Now;
					}
				}

				try
				{
					if (DateTime.Now.Subtract(this.lastSocialWebKeepAlive).TotalMinutes > 5)
					{
						string url = System.Configuration.ConfigurationManager.AppSettings["SocialURL"];
						if (!url.EndsWith("/"))
							url = string.Concat(url, "/");
						url = string.Concat(url, "services/status");
						using (WebClient webClient = new WebClient())
						{
							webClient.DownloadString(url);

							this.lastSocialWebKeepAlive = DateTime.Now;

							Tracer.TraceInfo("Se consultó exitosamente el estado de Social+");
						}
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Se produjo un error consultando el estado de Social+: {0}", ex);
				}

#if !RUNASPROGRAM
				if (DateTime.Now.Subtract(processStartTime).TotalMilliseconds > this.refreshInterval)
				{
					Tracer.TraceInfo("Transcurrieron más segundos que el tiempo de refrezco, se continua sin esperar");
					timer.Change(500, this.refreshInterval);
				}
				else
				{
					timer.Change(this.refreshInterval, this.refreshInterval);
				}
#endif
			}
			finally
			{
				this.doingWork = false;
			}
		}

		/// <summary>
		/// Hace el procesamiento de un servicio
		/// </summary>
		/// <param name="domainService">El <see cref="DomainModel.Service"/> con los datos del servicio y configuración actualizada</param>
		/// <param name="socialService">El <see cref="ISocialService"/> a procesar</param>
		/// <param name="shouldProcessReplies">Indica si se debe procesar las respuestas</param>
		private async Task ProcessSocialService(DomainModel.Service domainService, DomainModel.ISocialService socialService, Licensing.ServiceType licenseService, bool shouldProcessReplies)
		{
			if (this.stopping)
				return;

			ReconfigureService(domainService, socialService, licenseService);
			if (this.stopping)
				return;

			if (domainService.Enabled &&
				domainService.Type != DomainModel.ServiceTypes.Chat &&
				domainService.Type != DomainModel.ServiceTypes.IntegrationChat &&
				domainService.Queue != null)
			{
				try
				{
					if (domainService.UsesYFlow ||
						domainService.Filters.Any(f =>
							f.Enabled &&
							f.Actions.InvokeAssembly) ||
						domainService.Filters.Any(f =>
							f.Enabled &&
							f.Actions.InvokeWebService &&
							f.Actions.WebServiceActions != null &&
							f.Actions.WebServiceActions.AutoReply))
					{
						var status = DAL.ServiceDAO.GetStatus<Business.SocialServiceStatus>(domainService.ID);
						if (status != null)
						{
							socialService.UpdateStatus(status);
						}
					}
				}
				catch { }
			}

			await QueryService(domainService, socialService);
			if (this.stopping)
				return;
		}

		/// <summary>
		/// Reconfigura un servicio ya inicializado
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con los datos del servicio y configuración actualizada</param>
		/// <param name="socialService">El <see cref="ISocialService"/> a reconfigurar</param>
		/// <param name="licenseService">El servicio de la licencia o <code>null</code> si se permite crear nuevos servicios</param>
		private void ReconfigureService(DomainModel.Service service, DomainModel.ISocialService socialService, Licensing.ServiceType licenseService)
		{
			Tracer.TraceInfo("Verificando por nuevas configuraciones del servicio {0}", socialService.Name);

			try
			{
				var serviceTemp = ServiceDAO.GetOne(service.ID, false);

				if (serviceTemp.Configuration != service.Configuration)
				{
					Tracer.TraceInfo("Se modificó la configuración del servicio {0}", socialService.Name);

					var serviceConfiguration = (SocialServiceConfiguration) Activator.CreateInstance(socialService.ConfigurationType, serviceTemp.Configuration);
					if (!Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
					{
						if (service.Type != ServiceTypes.TwitterSearches)
						{
							var configuredAccountId = serviceConfiguration.GetAccountID();
							if (!string.IsNullOrEmpty(licenseService.AccountId) &&
								!licenseService.AccountId.Equals(configuredAccountId))
							{
								Tracer.TraceInfo("El servicio {0} tiene un código de identificación {1} que no es permitido por la licencia {2}", socialService.Name, configuredAccountId, licenseService.AccountId);
								return;
							}
						}
					}

					service.Configuration = serviceTemp.Configuration;
					socialService.Reconfigure(service, serviceConfiguration);
				}

				if (serviceTemp.Enabled != service.Enabled)
				{
					if (serviceTemp.Enabled)
						Tracer.TraceInfo("El servicio {0} se habilitó", socialService.Name);
					else
						Tracer.TraceInfo("El servicio {0} se deshabilitó", socialService.Name);

					service.Enabled = serviceTemp.Enabled;
				}

				int relatedSocialServiceId;
				if (socialService.RequiresRelatedSocialService(out relatedSocialServiceId))
				{
					foreach (var key in this.services.Keys)
					{
						if (key == relatedSocialServiceId)
						{
							Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService, Licensing.ServiceType> tuple = this.services[key];
							if (tuple == null)
								break;

							SocialServiceConnector connector = new SocialServiceConnector(tuple.Item2);
							socialService.SetRelatedSocialService(connector);
							break;
						}
					}
				}

				service.Settings = serviceTemp.Settings;
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló la actualización de la configuración del servicio {0}: {{0}}", socialService.Name), ex);
			}
		}

		/// <summary>
		/// Devuelve si un usuario de red social está bloqueado por Social+
		/// </summary>
		/// <param name="userId">El código de usuario de red social</param>
		/// <param name="blockedUsers">La lista de usuarios bloqueados por Social+</param>
		/// <returns>true si el usuario se encuentra bloqueado; en caso contrario, false</returns>
		private bool IsSocialUserBlocked(DomainModel.SocialUser user)
		{
			if (this.blockedUsers != null)
			{
				foreach (var blockedUser in this.blockedUsers)
				{
					if (blockedUser.SocialServiceType != user.SocialServiceType)
						continue;

					if (user.SocialServiceType == DomainModel.SocialServiceTypes.Mail)
					{
						if (user.Email != null &&
							blockedUser.Email != null &&
							blockedUser.Email.Equals(user.Email, StringComparison.InvariantCultureIgnoreCase))
							return true;
					}
					else
					{
						if (blockedUser.ID == user.ID)
							return true;
					}
				}
			}

			try
			{
				var socialUserReference = user.ToSocialUserReference();
				if (DomainModel.SystemSettings.Instance.BlockedSocialUsers.Contains(socialUserReference))
				{
					return true;
				}
			}
			catch { }

			return false;
		}

		/// <summary>
		/// Realiza las consultas por nuevos mensajes a las redes sociales
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con los datos del servicio y configuración actualizada</param>
		/// <param name="socialService">El <see cref="ISocialService"/> a consultar</param>
		private async Task QueryService(Yoizen.Social.DomainModel.Service service, DomainModel.ISocialService socialService)
		{
			if (!service.Enabled)
				return;

			Tracer.TraceInfo("Consultando al servicio {0}", socialService.Name);

			if (socialService.ValidUntil != null && socialService.ValidUntil < DateTime.Now.Date)
			{
				Tracer.TraceInfo("No se realizarán las consultas del servicio {0} porque era válido hasta el {1:dd/MM/yyyy}", socialService.Name, socialService.ValidUntil.Value);
				return;
			}

			var insertedMessages = new List<long>();

			try
			{
				IEnumerable<DomainModel.Message> messages = await socialService.Query();
				if (messages != null)
				{
					var count = messages.Count();
					Tracer.TraceInfo("El servicio {0} devolvió {1} mensajes", socialService.Name, count);

					var index = 0;
					foreach (var message in messages)
					{
						index++;
						Tracer.TraceInfo("Procesando mensaje {0}/{1}", index, count);

						/*
						 * Si el mensaje ya existe, lo ignoramos
						 */
						if (DAL.MessageDAO.ExistsBySocialMessage(message.SocialMessageID, message.SocialServiceType, service.ID))
						{
							Tracer.TraceInfo("El mensaje {0} con código {1} ya existe en la base de datos", message, message.SocialMessageID);
							continue;
						}

						if (service.SocialServiceType == DomainModel.SocialServiceTypes.Twitter &&
							!message.IsDirectMessage &&
							!DomainModel.SystemSettings.Instance.Twitter.AllowMentionInMultipleServices)
						{
							/*
							 * Si el mensaje ya existe, lo ignoramos
							 */
							if (DAL.MessageDAO.ExistsBySocialMessage(message.SocialMessageID, DomainModel.SocialServiceTypes.Twitter))
							{
								Tracer.TraceInfo("El mensaje {0} con código {1} ya existe en la base de datos para otro servicio de Twitter", message, message.SocialMessageID);
								continue;
							}
						}

						if (message.Outgoing)
						{
							/*
							 * Únicamente pueden existir mensajes desde las cuentas de Social si estos fueron escritos fuera de la herramienta (por lo tanto
							 * no están en la base de datos)
							 * Puede ser que haya sigo un mensaje que no responda a otro, con lo cual se deberá crear la conversación que contenga
							 * a este mensaje (MissingOutgoing). En el caso de que sea una respuesta a otro y dicho mensaje exista en la base de datos entonces
							 * lo agregamos a esa conversación (MissingReply).
							 * Si el mensaje responde a otro y ese otro no se encuentra en la base de datos, entonces ignoramos el mensaje
							 */
							Tracer.TraceInfo("El mensage {0} fue envíado por el usuario de Social+ por fuera del sistema y no está en la base de datos", message);
							bool insert = true;
							if (string.IsNullOrEmpty(message.RepliesToSocialMessageID))
							{
								Tracer.TraceInfo("El mensage {0} es el primero de una conversación y se insertará", message);
							}
							else if (DAL.MessageDAO.ExistsBySocialMessage(message.RepliesToSocialMessageID, message.SocialServiceType, service.ID))
							{
								Tracer.TraceInfo("El mensage {0} corresponde a una conversación existente y se insertará", message);
							}
							else
							{
								Tracer.TraceInfo("El mensage {0} corresponde a una conversación inexistente y no se insertará", message);
								insert = false;
							}

							if (insert)
							{
								try
								{
									InsertMissingMessage(service, message);
								}
								catch (Exception ex)
								{
									Tracer.TraceError("Falló el servicio {0}: no se pudo insertar un mensaje que faltaba {1}", socialService.Name, ex);
									continue;
								}
							}
						}
						else
						{
							if (message.PostedBy == null)
							{
								Tracer.TraceInfo("Ignorando el mensage {0} porque no hay información del usuario que escribió el mensaje", message, message.PostedBy);
								continue;
							}

							if (IsSocialUserBlocked(message.PostedBy))
							{
								Tracer.TraceInfo("Ignorando el mensage {0} porque el usuario {1} está bloqueado", message, message.PostedBy);
								continue;
							}

							Tracer.TraceInfo("Insertando mensage {0}", message);

							if (!message.Searched)
							{
								/* Cuando estamos procesando mensajes de Twitter puede ocurrir que el mensaje sea respuesta de algún mensaje que no
								 * mencione a la cuenta. En ese caso rearmamos la conversación con los mensajes que están faltando
								 */
								Stack<DomainModel.Message> missingMessages = new Stack<DomainModel.Message>();
								string missingMessageId = message.RepliesToSocialMessageID;
								while (!string.IsNullOrEmpty(missingMessageId) && !DAL.MessageDAO.ExistsBySocialMessage(missingMessageId, message.SocialServiceType, service.ID))
								{
									try
									{
										DomainModel.Message missingMessage = await socialService.Query(missingMessageId);
										if (missingMessage != null)
										{
											missingMessages.Push(missingMessage);
											missingMessageId = missingMessage.RepliesToSocialMessageID;
										}
										else
										{
											missingMessageId = null;
										}
									}
									catch (Exception ex)
									{
										Tracer.TraceException(string.Format("No se pudo obtener el mensaje {0} del servicio {1}: {{0}}", missingMessageId, socialService.Name), ex);
										break;
									}
								}

								while (missingMessages.Count > 0)
								{
									try
									{
										InsertMissingMessage(service, missingMessages.Pop());
									}
									catch (Exception ex)
									{
										Tracer.TraceError("Falló el servicio {0}: no se pudo insertar un mensaje que faltaba {1}", socialService.Name, ex);
									}
								}
							}

							Tracer.TraceVerb("Se insertará el mensaje: {0}", message);
							InsertMessage(service, message);
							Tracer.TraceVerb("Se insertó el mensaje: {0}", message);
							insertedMessages.Add(message.ID);
						}
					}
				}
				else
				{
					Tracer.TraceInfo("El servicio {0} no devolvió mensajes", socialService.Name);
				}

				var now = DateTime.Now;
				socialService.Status[DomainModel.Service.LastQueryOperationStatus] = now.ToString("o");
				socialService.Status[DomainModel.Service.LastQueryOperationSucceededStatus] = true.ToString();
				if (messages.Any())
				{
					socialService.Status[DomainModel.Service.LastQueryOperationWithResultsStatus] = now.ToString("o");
				}

				ServiceDAO.UpdateStatus(service, socialService.Status);
#if !RUNASPROGRAM
				socialService.Status.ApplyChanges();
#endif
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló el servicio {0}: {{0}}", socialService.Name), ex);

				DomainModel.SystemSettings.Instance.HandleException(ex);

				if (ex.GetType() == typeof(OutOfMemoryException))
					Environment.Exit(1);

				socialService.Status.RevertChanges();

				socialService.Status[DomainModel.Service.LastQueryOperationStatus] = DateTime.Now.ToString("o");
				socialService.Status[DomainModel.Service.LastQueryOperationSucceededStatus] = false.ToString();
				ServiceDAO.UpdateStatus(service, socialService.Status);
#if !RUNASPROGRAM
				socialService.Status.ApplyChanges();
#endif
			}

			if (insertedMessages.Count > 0)
			{
				var error = false;
				try
				{
					NotifySocialForNewServiceMessages(service, insertedMessages);
				}
				catch (WebException ex)
				{
					if (ex.Response != null)
					{
						string textError = ExtractTextFromResponse(ex.Response as HttpWebResponse);
						Tracer.TraceError("Falló el servicio {0}: no se pudo notificar a ySocial de nuevos mensajes {1}: {2}", socialService.Name, ex, textError);
					}
					else
					{
						Tracer.TraceError("Falló el servicio {0}: no se pudo notificar a ySocial de nuevos mensajes {1}", socialService.Name, ex);
					}

					error = true;
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló el servicio {0}: no se pudo notificar a ySocial de nuevos mensajes {1}", socialService.Name, ex);
					error = true;
				}

				if (error)
				{
					foreach (var messageId in insertedMessages)
					{
						DAL.MessageDAO.UpdateStatus(messageId, MessageStatuses.NotAssigned, MessageLogTypes.MessageCreated, null);
					}

					Tracer.TraceInfo("Se marcaron los mensajes {0} como que no fueron notificados a ySocial del servicio {1}", string.Join(",", insertedMessages), service.ID);
				}
			}
		}

		private void NotifySocialForNewServiceMessages(DomainModel.Service service, IEnumerable<long> insertedMessages)
		{
			string url = System.Configuration.ConfigurationManager.AppSettings["SocialURL"];
			if (!url.EndsWith("/"))
				url = string.Concat(url, "/");
			url = string.Concat(url, "services/messaging/news");

			var news = new
			{
				serviceId = service.ID,
				messages = insertedMessages
			};

			var json = Newtonsoft.Json.JsonConvert.SerializeObject(news);

			using (var webClient = new DomainModel.YoizenWebClient())
			{
				webClient.Timeout = Convert.ToInt32(TimeSpan.FromSeconds(300).TotalMilliseconds);
				webClient.Headers[HttpRequestHeader.ContentType] = "application/json";
				AddAuthorizationHeaders(webClient.Headers, "POST", url);
				webClient.UploadString(url, json);

				Tracer.TraceInfo("Se notificó exitosamente a ySocial de los mensajes {0} del servicio {1}", string.Join(",", insertedMessages), service.ID);
			}
		}

		/// <summary>
		/// Inserta o actualiza un usuario de red social
		/// </summary>
		/// <param name="user">El <see cref="DomainModel.User"/> que se insertará</param>
		/// <param name="service">El <see cref="DomainModel.Service"/> que generó
		/// la inserción o actualización del usuario</param>
		private void InsertUser(DomainModel.SocialUser user, DomainModel.Service service)
		{
			DAL.SocialUserDAO socialUserDAO = new DAL.SocialUserDAO();
			user.VIP = null;

			if (!user.ParametersByService.ContainsKey(service.ID))
				user.ParametersByService[service.ID] = new DomainModel.SocialUserServiceParameters();

			if (!user.RetrievedFromDatabase)
				user.ParametersByService[service.ID].FirstInteraction = DateTime.Now;
			user.ParametersByService[service.ID].PreviousLastInteraction = user.ParametersByService[service.ID].LastInteraction;
			user.ParametersByService[service.ID].LastInteraction = DateTime.Now;

			DomainModel.Settings.SocialUserReference socialUserReference = user.ToSocialUserReference();
			if (DomainModel.SystemSettings.Instance.VIPSocialUsers.Contains(socialUserReference))
			{
				user.VIP = true;
			}
			if (DomainModel.SystemSettings.Instance.TesterSocialUsers.Contains(socialUserReference))
			{
				user.Tester = true;
			}
			if (DomainModel.SystemSettings.Instance.DoNotCallSocialUsers.Contains(socialUserReference))
			{
				user.DoNotCall = true;
			}

			socialUserDAO.SocialUser = user;
			socialUserDAO.Insert();
		}

		/// <summary>
		/// Crea un mensaje proveniente del servicio de captura de las redes sociales
		/// </summary>
		/// <param name="service">Un <see cref="Service"/> con el servicio al que pertenece el mensaje</param>
		/// <param name="message">Una instancia de <see cref="DomainModel.Message"/> con el mensaje</param>
		private void InsertMessage(DomainModel.Service service, DomainModel.Message message)
		{
			if (message.Incoming)
			{
				InsertUser(message.PostedBy, service);
			}
			else
			{
				message.PostedBy = null;
				message.Status = DomainModel.MessageStatuses.System;
			}

			if (DomainModel.SystemSettings.Instance.MarkWhiteListMessagesAsVIM &&
				message.PostedBy.VIP == true)
				message.Important = true;

			message.Service = service;
			message.ServiceType = service.Type;
			message.Status = MessageStatuses.Push;
			DAL.MessageDAO.Insert(message);

			DomainModel.StorageManager.Instance.SaveIncomingMessage(message);
		}

		/// <summary>
		/// Crea un mensaje proveniente del servicio de captura de las redes sociales que está ausente en la base de datos
		/// </summary>
		/// <remarks>
		/// Esto sucede únicamente cuando se respondió desde la página o cuando un mensaje está respondiendo a otro
		/// que no menciona a la cuenta (Twitter)
		/// </remarks>
		/// <param name="service">Un <see cref="Service"/> con el servicio al que pertenece el mensaje</param>
		/// <param name="message">Una instancia de <see cref="DomainModel.Message"/> con el mensaje</param>
		private void InsertMissingMessage(DomainModel.Service service, DomainModel.Message message)
		{
			if (message.Incoming)
			{
				InsertUser(message.PostedBy, service);
				message.Status = DomainModel.MessageStatuses.Historical;
			}
			else
			{
				message.PostedBy = null;
				message.Status = DomainModel.MessageStatuses.System;
			}

			message.Service = service;
			message.ServiceType = service.Type;
			DAL.MessageDAO.Insert(message);

			DomainModel.StorageManager.Instance.SaveIncomingMessage(message);
		}

		#region Private Methods

		/// <summary>
		/// Devuelve un <see cref="string"/> a partir de una respuesta HTTP
		/// </summary>
		/// <param name="response">Un <see cref="HttpWebResponse"/> con la respuesta de una ejecución de un servicio
		/// REST de Social</param>
		/// <returns>Un <see cref="string"/> con el contenido de la respuesta</returns>
		private static string ExtractTextFromResponse(HttpWebResponse response)
		{
			try
			{
				using (StreamReader sr = new StreamReader(response.GetResponseStream()))
				{
					string json = sr.ReadToEnd();
					return json;
				}
			}
			catch
			{
				return string.Empty;
			}
		}

		/// <summary>
		/// Generate the timestamp for the signature        
		/// </summary>
		/// <returns>A timestamp value in a string.</returns>
		private static string GenerateTimeStamp()
		{
			// Default implementation of UNIX time of the current UTC time
			TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
			return Convert.ToInt64(ts.TotalSeconds, CultureInfo.CurrentCulture).ToString(CultureInfo.CurrentCulture);
		}

		/// <summary>
		/// Generate a nonce
		/// </summary>
		/// <returns>A random number between 123400 and 9999999 in a string.</returns>
		private static string GenerateNonce()
		{
			// Just a simple implementation of a random number between 123400 and 9999999
			return new Random()
				.Next(123400, int.MaxValue)
				.ToString("X", CultureInfo.InvariantCulture);
		}

		/// <summary>
		/// Escapes a string according to the URI data string rules given in RFC 3986.
		/// </summary>
		/// <param name="value">The value to escape.</param>
		/// <returns>The escaped value.</returns>
		/// <remarks>
		/// The <see cref="Uri.EscapeDataString"/> method is <i>supposed</i> to take on
		/// RFC 3986 behavior if certain elements are present in a .config file.  Even if this
		/// actually worked (which in my experiments it <i>doesn't</i>), we can't rely on every
		/// host actually having this configuration element present.
		/// </remarks>
		private static string EscapeUriDataStringRfc3986(string value)
		{
			// Start with RFC 2396 escaping by calling the .NET method to do the work.
			// This MAY sometimes exhibit RFC 3986 behavior (according to the documentation).
			// If it does, the escaping we do that follows it will be a no-op since the
			// characters we search for to replace can't possibly exist in the string.
			StringBuilder escaped = new StringBuilder(Uri.EscapeDataString(value));

			// Upgrade the escaping to RFC 3986, if necessary.
			for (int i = 0; i < UriRfc3986CharsToEscape.Length; i++)
			{
				escaped.Replace(UriRfc3986CharsToEscape[i], Uri.HexEscape(UriRfc3986CharsToEscape[i][0]));
			}

			// Return the fully-RFC3986-escaped string.
			return escaped.ToString();
		}

		/// <summary>
		/// Encodes a value for inclusion in a URL querystring.
		/// </summary>
		/// <param name="value">The value to Url encode</param>
		/// <returns>Returns a Url encoded string</returns>
		private static string UrlEncode(string value)
		{
			if (string.IsNullOrEmpty(value))
				return string.Empty;

			value = EscapeUriDataStringRfc3986(value);

			// UrlEncode escapes with lowercase characters (e.g. %2f) but oAuth needs %2F
			value = System.Text.RegularExpressions.Regex.Replace(value, "(%[0-9a-f][0-9a-f])", c => c.Value.ToUpper());

			// these characters are not escaped by UrlEncode() but needed to be escaped
			value = value
				.Replace("!", "%21")
				.Replace("#", "%23")
				.Replace("$", "%24")
				.Replace("&", "%26")
				.Replace("'", "%27")
				.Replace("(", "%28")
				.Replace(")", "%29")
				.Replace("*", "%2A")
				.Replace("+", "%2B")
				.Replace(",", "%2C")
				.Replace("/", "%2F")
				.Replace(":", "%3A")
				.Replace(";", "%3B")
				.Replace("=", "%3D")
				.Replace("?", "%3F")
				.Replace("@", "%40")
				.Replace("[", "%5B")
				.Replace("]", "%5D");

			// these characters are escaped by UrlEncode() but will fail if unescaped!
			value = value.Replace("%7E", "~");

			return value;
		}

		/// <summary>
		/// Agrega autorización a las requests
		/// </summary>
		/// <param name="headers">La colección de headers</param>
		/// <param name="method">Método a ejecutar</param>
		/// <param name="url">Url completa</param>
		internal static void AddAuthorizationHeaders(WebHeaderCollection headers, string method, string url)
		{
			if (!string.IsNullOrEmpty(accessToken))
			{
				headers.Add("Authorization", string.Format("Bearer {0}", accessToken));
				var nonce = GenerateNonce();
				headers.Add("auth_nonce", nonce);
				var ts = GenerateTimeStamp();
				headers.Add("auth_timestamp", ts);
				headers.Add("auth_signature_method", "HMAC-SHA1");
				headers.Add("auth_token", accessToken);
				headers.Add("auth_url", url);
				headers.Add("auth_method", method);
				headers.Add("auth_client_type", "service");
				headers.Add("auth_person_id", "yoizen");

				var signatureBaseString = method + '&' + UrlEncode(url) + '&';

				var oauthParameters = "";
				oauthParameters += "auth_nonce=" + UrlEncode(nonce) + '&';
				oauthParameters += "auth_timestamp=" + UrlEncode(ts) + '&';
				oauthParameters += "auth_signature_method=" + UrlEncode("HMAC-SHA1") + '&';
				oauthParameters += "auth_token=" + UrlEncode(accessToken);

				signatureBaseString += UrlEncode(oauthParameters);

				// Generate the hash
				using (var hmacsha1 = new System.Security.Cryptography.HMACSHA1(Encoding.UTF8.GetBytes(accessTokenSecret)))
				{
					byte[] signatureBytes = hmacsha1.ComputeHash(Encoding.UTF8.GetBytes(signatureBaseString));
					var signature = Convert.ToBase64String(signatureBytes);

					headers.Add("auth_signature", signature);
				}
			}
		}

		#endregion

		#endregion
	}
}