﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Timers;
using Yoizen.Social.Core.Services;
using Yoizen.Common;
using System.Configuration;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DAL;
using System.Diagnostics;

namespace Yoizen.Social.Core
{
	/// <summary>
	/// Provee la funcionalidad del sistema de Social+ para interactuar con mensajes, colas y agentes
	/// </summary>
	public sealed partial class System : IDisposable
	{
		#region Fields

#if !NETCOREAPP
		/// <summary>
		/// El timer que se utiliza para realizar las tareas del sistema
		/// </summary>
		private Timer timerTasks;
#endif

		private bool starting = false;
		private bool started = false;
		private bool initializing = false;
		private bool readyFired = false;
		private static object startinglock = new object();

		#endregion

		#region Events

		/// <summary>
		/// Representa el <see cref="Delegate"/> asociado a cuando el sistema está listo
		/// </summary>
		public delegate void SystemReadyEventHandler();

		/// <summary>
		/// Ocurre cuando la el sistema está listo
		/// </summary>
		public event SystemReadyEventHandler Ready;

#if !NETCOREAPP
		/// <summary>
		/// Representa el <see cref="Delegate"/> asociado a cuando el sistema informa que se deben realizar tareas que dependen del paso del tiempo
		/// </summary>
		public delegate void ScheduledWorkEventHandler();

		/// <summary>
		/// Ocurre cuando el sistema notifica del paso del tiempo y alguna acción deba realizarse
		/// </summary>
		public event ScheduledWorkEventHandler ScheduledWork;
#endif

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve la única instancia del sistema
		/// </summary>
		public static System Instance { get; private set; }

		/// <summary>
		/// Devuelve si el sistema ha sido inicializado
		/// </summary>
		public bool Initialized { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de Agentes
		/// </summary>
		public AgentsService AgentsService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de Colas
		/// </summary>
		public QueueService QueueService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de Casos
		/// </summary>
		public CasesService CasesService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de Chats
		/// </summary>
		public ChatsService ChatsService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de intervalos
		/// </summary>
		public IntervalsService IntervalService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de tiempo real
		/// </summary>
		public RealTimeService RealTimeService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de integraciones
		/// </summary>
		public ServerIntegrationsService ServerIntegrationsService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de integraciones
		/// </summary>
		public SecurityPoliticsService SecurityPoliticsService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de exportación automática de reportes
		/// </summary>
		public AutomaticExportService AutomaticExportService { get; private set; }

		/// <summary>
		/// Devuelve la instancia de <see cref="Yoizen.Social.Core.SystemLogic"/>
		/// encargada de la lógica de los mensajes
		/// </summary>
		public SystemLogic Logic { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio
		/// </summary>
		public ServicesService ServicesServices { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de tareas
		/// </summary>
		public TasksService TasksService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de mensajes que no pudieron ser entregados
		/// </summary>
		public DeliveryFailedService DeliveryFailedService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de usuarios
		/// </summary>
		public UsersService UsersService
		{
			get;
#if !DEBUG
			private
#endif
			set;
		}

		/// <summary>
		/// Devuelve o establece el host donde está ySocial (el nombre o la IP)
		/// </summary>
		public string Host { get; set; }

		/// <summary>
		/// Devuelve o establece la URL base del sitio
		/// </summary>
		public string SiteRoot { get; set; }

		/// <summary>
		/// Devuelve o establece la URL base del sitio incluyendo el nombre de aplicación de IIS
		/// </summary>
		public string SiteRootWithApplication { get; set; }

		/// <summary>
		/// Devuelve o establece si ySocial funciona sobre https
		/// </summary>
		public bool Https { get; set; }

		/// <summary>
		/// Devuelve la configuracíon de autenticación
		/// </summary>
		public SystemAuthentication SystemAuthentication { get; private set; }

		/// <summary>
		/// Devuelve la instancia de asistencia para subir archivos
		/// </summary>
		public MediaService MediaService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de encuestas
		/// </summary>
		public SurveysService SurveysService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de gateway
		/// </summary>
		public GatewayService GatewayService { get; private set; }

		/// <summary>
		/// Devuelve la instancia del servicio de eventos
		/// </summary>
		public EventsService EventsService { get; private set; }

		public VideoService VideoService { get; private set; }

		/// <summary>
		/// Devuelve si el sistema ya está listo para empezar a trabajar
		/// </summary>
		public bool IsReady { get; private set; }

		/// <summary>
		/// Devuelve si el sistema falló durante su incializacion
		/// </summary>
		public bool InitializationFailed { get; private set; }

		#endregion

		#region Constructors

#if NETCOREAPP
		public System(ILogger<System> logger)
		{
			if (System.Instance != null)
				throw new InvalidOperationException();

			Yoizen.Common.Tracer.Logger = logger;

			Initialize();
		}
#else
		static System()
		{
			Instance = new System();
		}

		private System()
		{
			this.Initialized = false;
			this.Https = false;
			this.IsReady = false;
			this.InitializationFailed = false;
			this.Logic = new SystemLogic();
			this.SystemAuthentication = new SystemAuthentication();
		}
#endif

		#endregion

		#region Private Methods
		private void InicializeCache()
		{
			#region Cache

			Tracer.TraceInfo("Inicializando Caché");
			Cache.Instance.Enabled = false;

			Tracer.TraceVerb("Obteniendo Etiquetas");
			List<Tag> tags = TagDAO.GetAll(true);
			var tagsDictionary = new Dictionary<int, Tag>();
			foreach (var tag in tags)
			{
				tagsDictionary[tag.ID] = tag;

				if (tag.Parent != null)
				{
					tag.Parent = tagsDictionary[tag.Parent.ID];
					tag.Parent.ChildTags.Add(tag);
				}
			}

			Tracer.TraceVerb("Obteniendo Motivos de contacto");
			List<ContactReason> contactReasons = ContactReasonDAO.GetAll(true);

			Tracer.TraceVerb("Obteniendo Sitios");
			List<Site> sites = SiteDAO.GetAll();

			Tracer.TraceVerb("Obteniendo Servicios");
			var services = ServiceDAO.GetAllForCache();

			Tracer.TraceVerb("Obteniendo Colas");
			var queues = QueueDAO.GetAllForCache();
			var queuesDictionary = new Dictionary<int, Queue>();
			foreach (var queue in queues)
				queuesDictionary[queue.ID] = queue;

			Tracer.TraceVerb("Obteniendo Grupos de colas");
			var queueGroups = QueueGroupDAO.GetAllForCache(queuesDictionary);

			Tracer.TraceVerb("Obteniendo Agentes");
			var agents = AgentDAO.GetAllForCache(services, queuesDictionary);
			var agentsDictionary = new Dictionary<int, Agent>();
			foreach (var agent in agents)
				agentsDictionary[agent.ID] = agent;

			Tracer.TraceVerb("Obteniendo Agentes Borrados");
			var agentsDeleted = AgentDAO.GetDeleted();

			Tracer.TraceVerb("Obteniendo Usuarios");
			var users = UserDAO.GetAllForCache(queuesDictionary);
			var usersDictionary = new Dictionary<int, User>();
			foreach (var user in users)
				usersDictionary[user.ID] = user;

			Tracer.TraceVerb("Obteniendo Grupos de Agentes");
			var agentGroups = AgentGroupDAO.GetAllForCache(queuesDictionary, agentsDictionary, usersDictionary);

			Tracer.TraceVerb("Obteniendo Grupos de etiquetas");
			var tagGroups = TagGroupDAO.GetAllForCache(queuesDictionary);

			Tracer.TraceVerb("Obteniendo perfiles");
			var profiles = ProfileDAO.GetAllForCache(usersDictionary);

			Tracer.TraceVerb("Procesando referencias");
			foreach (var user in users)
			{
				if (user.TempProfileIDs != null)
				{
					foreach (var profileId in user.TempProfileIDs)
					{
						var profile = profiles.FirstOrDefault(p => p.ID == profileId);
						if (profile != null)
							user.Profiles.Add(profile);
					}
				}
			}

			foreach (var service in services)
			{
				if (service.SocialServiceType != SocialServiceTypes.Chat &&
					service.Type != ServiceTypes.TwitterSearches &&
					service.ID != 0 &&
					string.IsNullOrEmpty(service.AccountID))
				{
					try
					{
						Tracer.TraceVerb("Inicializando servicio {0}-{1}", service.ID, service.Name);
						var socialService = (ISocialService) Activator.CreateInstance(service.SocialServiceClassType);
						var serviceConfiguration = (Business.SocialServiceConfiguration) Activator.CreateInstance(socialService.ConfigurationType, service.Configuration);
						service.ServiceConfiguration = serviceConfiguration;
						service.AccountID = serviceConfiguration.GetAccountID();
					}
					catch { }
				}
			}

			// Le agregamos a la cola aquellas etiquetas que son categorías y que tiene alguna etiqueta que esté asociada a la cola
			var queueCount = queues.Count();
			var index = 0;
			Tracer.TraceVerb("Procesando referencias de {0} colas", queueCount);
			foreach (var queue in queues)
			{
				index++;

				Tracer.TraceVerb("Procesando referencias de la cola {0} ({1}/{2})", queue.Name, index, queueCount);

				if (queue.TempTagIDs != null)
				{
					var tagsToAdd = new Dictionary<int, Tag>();
					foreach (var tagId in queue.TempTagIDs)
					{
						if (tagsDictionary.TryGetValue(tagId, out var tag))
						{
							if (tag != null)
							{
								if (!tagsToAdd.ContainsKey(tag.ID))
									tagsToAdd.Add(tag.ID, tag);

								var parent = tag.Parent;
								while (parent != null)
								{
									if (!tagsToAdd.ContainsKey(parent.ID))
										tagsToAdd.Add(parent.ID, parent);

									parent = parent.Parent;
								}
							}
						}
					}

					queue.Tags.AddRange(tagsToAdd.Values);

					foreach (var tag in queue.Tags)
					{
						tag.Queues.Add(queue);
					}
				}

				// Le agregamos a las colas los grupos de etiquetas a los que pertenece
				queue.TagGroups.AddRange(tagGroups.Where(q => q.Queues.Contains(queue)));

				if (queue.RelatedQueuesToReturnMessages != null)
				{
					for (int i = 0; i < queue.RelatedQueuesToReturnMessages.Count; i++)
					{
						var relatedQueueToReturnMessages = queue.RelatedQueuesToReturnMessages[i];
						if (queuesDictionary.TryGetValue(relatedQueueToReturnMessages.ID, out var newRelatedQueue))
							queue.RelatedQueuesToReturnMessages[i] = newRelatedQueue;
					}
				}

				if (queue.TempUserIDs != null)
				{
					queue.Users.Clear();
					queue.Supervisors.Clear();

					foreach (var userId in queue.TempUserIDs)
					{
						if (usersDictionary.TryGetValue(userId, out var fullUser))
						{
							if (fullUser != null)
							{
								if (fullUser.IsSupervisor)
								{
									queue.Supervisors.Add(fullUser);
								}
								else
								{
									queue.Users.Add(fullUser);
								}
							}
						}
					}
				}

				if (queue.TempAgentIDs != null)
				{
					foreach (var agentId in queue.TempAgentIDs)
						if (agentsDictionary.TryGetValue(agentId, out var agent))
							queue.Agents.Add(agent);
				}

				if (queue.TempServiceIDs != null)
				{
					foreach (var serviceId in queue.TempServiceIDs)
					{
						var service = services.FirstOrDefault(a => a.ID == serviceId);
						if (service != null)
						{
							queue.Services.Add(service);
							service.Queue = queue;
						}
					}
				}
			}

			Tracer.TraceVerb("Procesando referencias de {0} usuarios", users.Count());
			foreach (var user in users)
			{
				if (user.TempAgentGroupIDs != null)
				{
					foreach (var agentGroupId in user.TempAgentGroupIDs)
					{
						var agentGroup = agentGroups.FirstOrDefault(ag => ag.ID == agentGroupId);
						if (agentGroup != null)
							user.AgentGroups.Add(agentGroup);
					}
				}
			}

			Tracer.TraceVerb("Procesando referencias de {0} grupos de etiquetas", tagGroups.Count());
			foreach (var tagGroup in tagGroups)
			{
				foreach (var tagGroupTagId in tagGroup.Tags)
				{
					var tagGroupTag = tagsDictionary[tagGroupTagId];
					tagGroupTag.TagGroups.Add(tagGroup.ID);
				}
			}

			Tracer.TraceVerb("Procesando referencias de {0} agentes", agents.Count());
			foreach (var agent in agents)
			{
				if (agent.Site != null)
				{
					agent.Site = sites.Find(s => s.ID == agent.Site.ID);
				}

				if (agent.TempAgentGroupIDs != null)
				{
					foreach (var agentGroupId in agent.TempAgentGroupIDs)
					{
						var agentGroup = agentGroups.FirstOrDefault(ag => ag.ID == agentGroupId);
						if (agentGroup != null)
							agent.AgentGroups.Add(agentGroup);
					}
				}
			}

			Tracer.TraceVerb("Obteniendo Encuestas");
			List<Survey> surveys = SurveyDAO.GetAll();

			Cache.Instance.Enabled = true;

			Tracer.TraceVerb("Obteniendo tipos de servicios de redes sociales");
			Cache.Instance.UpdateList(SocialServiceTypeDAO.GetAll());

			Tracer.TraceVerb("Obteniendo listas de perfiles de usuarios");
			Cache.Instance.UpdateList(SocialUserProfilesListDAO.GetAll());

			Tracer.TraceVerb("Obteniendo listas de integraciones con terceros");
			Cache.Instance.UpdateList(ExternalIntegrationDAO.GetAll());

			Tracer.TraceVerb("Obteniendo listas de configuraciones de seguridad para integraciones con terceros");
			Cache.Instance.UpdateList(ExternalIntegrationSecurityDAO.GetAll());

			#region Update Cache Lists
			Cache.Instance.UpdateList<ContactReason>(contactReasons);
			Cache.Instance.UpdateList<Tag>(tags);
			Cache.Instance.UpdateList<Service>(services);
			Cache.Instance.UpdateList<Site>(sites);
			Cache.Instance.UpdateList<QueueGroup>(queueGroups);
			Cache.Instance.UpdateList<Queue>(queues);
			Cache.Instance.UpdateList<TagGroup>(tagGroups);
			Cache.Instance.UpdateList<Agent>(agents);
			Cache.Instance.UpdateList<AgentDeleted>(agentsDeleted);
			Cache.Instance.UpdateList<AgentGroup>(agentGroups);
			Cache.Instance.UpdateList<User>(users);
			Cache.Instance.UpdateList<Survey>(surveys);
			Cache.Instance.UpdateList<Profile>(profiles);
			#endregion

			Tracer.TraceInfo("Se finalizó de inicializar el Caché");

			#endregion
		}
		private void OnReady()
		{
			lock (startinglock)
			{
				if (this.readyFired)
					return;

				this.readyFired = true;
			}

			if (this.Ready != null)
				this.Ready();
		}

#if !NETCOREAPP

		private void OnScheduledWork()
		{
			ScheduledWork?.Invoke();
		}

		private void timerTasks_Elapsed(object sender, ElapsedEventArgs e)
		{
			this.timerTasks.Stop();

			Tracer.TraceVerb("Se procesarán las tareas del sistema");

			try
			{
				try
				{
					this.IntervalService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de intervalos: {0}", ex);
				}

				if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
				{
					try
					{
						this.QueueService.DoScheduledWork();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de colas: {0}", ex);
					}
				}

				if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
				{
					try
					{
						this.AgentsService.DoScheduledWork();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de agentes: {0}", ex);
					}
				}

				try
				{
					this.CasesService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de casos: {0}", ex);
				}

				try
				{
					this.TasksService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de tareas: {0}", ex);
				}

				try
				{
					this.DeliveryFailedService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de mensajes enviados que fallaron: {0}", ex);
				}

				try
				{
					this.SecurityPoliticsService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de políticas de seguridad: {0}", ex);
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport)
				{
					try
					{
						this.AutomaticExportService.DoScheduledWork();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de exportación automática: {0}", ex);
					}
				}

				if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
				{
					try
					{
						if (this.ChatsService != null)
							this.ChatsService.DoScheduledWork();
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de chat: {0}", ex);
					}
				}

				try
				{
					this.MediaService.DoScheduledWork();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error realizando las tareas del servicio de archivos: {0}", ex);
				}

				OnScheduledWork();

				Tracer.TraceVerb("Finalizó el procesamiento de las tareas del sistema");
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error realizando las tareas de sistema: {0}", ex);
			}
			finally
			{
				this.timerTasks.Start();
			}
		}

		private string RetrieveWebAgentVersion()
		{
			string version = null;
			try
			{
				var path = global::System.Web.Hosting.HostingEnvironment.MapPath("~/");
				var directory = new global::System.IO.DirectoryInfo(path);
				directory = directory.Parent;
#if DEBUG
				string webAgentPath = global::System.IO.Path.Combine(directory.FullName, "Yoizen.Social.WebAgent", "app", "core", "config.js");
#else
				string webAgentPath = global::System.IO.Path.Combine(directory.FullName, "WebAgent", "app", "core", "config.js");
#endif
				if (global::System.IO.File.Exists(webAgentPath))
				{
					var contents = global::System.IO.File.ReadAllText(webAgentPath);
					var index = contents.IndexOf("version:");
					contents = contents.Substring(index + 8);
					index = contents.IndexOf("'");
					contents = contents.Substring(index + 1);
					index = contents.IndexOf("'");
					contents = contents.Substring(0, index);
					contents = contents.TrimStart('\'').TrimEnd('\'');

					version = contents;
					Tracer.TraceInfo("Se encontró la versión del agente web desde el archivo {0}: {1}", webAgentPath, version);
				}
			}
			catch { }

			if (version == null)
			{
				try
				{
					if (!string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.WebAgentURL))
					{
						var client = new RestSharp.RestClient(DomainModel.SystemSettings.Instance.WebAgentURL);

						var request = new RestSharp.RestRequest("api/status", RestSharp.Method.GET);

						var response = client.Execute(request);
						if (response.IsSuccessful)
						{
							if (response.ContentType.StartsWith("application/json"))
							{
								var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

								version = jContent["version"].ToString();
								Tracer.TraceInfo("Se encontró la versión del agente web: {0}", version);
							}
						}
					}
				}
				catch { }
			}

			return version;
		}

		/// <summary>
		/// Pasa todos los mensajes 'Asignados' a 'No Asignados' y, aquellos que fueron leídos, los reserva
		/// para el Agente que originalmente tomó el mensaje
		/// </summary>
		/// <remarks>
		/// Se ejecuta al iniciar el sistema.
		/// </remarks>
		private void ResetAssignedMessages()
		{
			/*
			 * Si se inicia el sistema, y había mensajes asignados (probablemente porque se cayó el servidor)
			 * entonces los modifico para que queden en estado de "Se asignará al usuario X"
			 */

			Tracer.TraceVerb("Se desasignarán los mensajes que quedaron asignados a agentes");

			var messages = MessageDAO.GetAllAssigned();
			Tracer.TraceVerb("Se obtuvieron {0} mensajes a desasignar", messages.Count());

			foreach (var message in messages)
			{
				if (message.AssignedTo == null)
					continue;

				Tracer.TraceVerb("Se desasignará el mensaje {0} asignado al agente {1}", message, message.AssignedTo);
				try
				{
					this.Logic.ReleaseMessageFromAgentOnStartup(message);
					Tracer.TraceVerb("Se desasignó el mensaje {0} asignado al agente {1}", message, message.AssignedTo);
				}
				catch { }
			}
		}

		/// <summary>
		/// Pasa todos los mensajes 'Asignados' a 'No Asignados' y, aquellos que fueron leídos, los reserva
		/// para el Agente que originalmente tomó el mensaje
		/// </summary>
		/// <remarks>
		/// Se ejecuta al iniciar el sistema.
		/// </remarks>
		private void DiscardMessagesOfClosedCases()
		{
			/*
			 * Si se inicia el sistema, y había mensajes en estado 1 pero de casos cerrados
			 * los descartamos
			 */

			Tracer.TraceVerb("Se descartarán los mensajes de casos cerrados");

			var messages = MessageDAO.DiscardOfClosedCases();
			if (messages != null && messages.Any())
			{
				Tracer.TraceVerb("Se descartaron {0} mensajes que pertenecían a casos ya cerrados ({1})", messages.Count(), string.Join(", ", messages));
			}
			else
			{
				Tracer.TraceVerb("No había ningún mensaje a descartar que pertenecían a casos ya cerrados");
			}
		}

		/// <summary>
		/// Resetea todos los mensajes que estaban en processingpush para que ingresen con normalidad
		/// </summary>
		private void ResetProcessingPushMessages()
		{
			MessageDAO.ResetProcessingPush();
		}

		private void Dispose(bool restarting)
		{
			if (this.timerTasks != null)
			{
				this.timerTasks.Stop();
				this.timerTasks.Dispose();
				this.timerTasks = null;
			}

			try
			{
				this.IntervalService.Dispose();
				this.IntervalService = null;
			}
			catch { }

			try
			{
				this.QueueService.Dispose();
			}
			catch { }

			try
			{
				this.AgentsService.Dispose();
			}
			catch { }

			try
			{
				this.CasesService.Dispose();
			}
			catch { }

			try
			{
				this.TasksService.Dispose();
			}
			catch { }

			try
			{
				this.SecurityPoliticsService.Dispose();
			}
			catch { }

			try
			{
				this.AutomaticExportService.Dispose();
			}
			catch { }

			if (!restarting)
			{
				try
				{
					this.ChatsService.Dispose();
					this.ChatsService = null;
				}
				catch { }

				try
				{
					if (this.RealTimeService != null)
					{
						this.RealTimeService.Dispose();
						this.RealTimeService = null;
					}
				}
				catch { }
			}
			
			try
			{
				this.ServicesServices.Dispose();
			}
			catch { }

			try
			{
				this.DeliveryFailedService.Dispose();
			}
			catch { }

			try
			{
				this.UsersService.Dispose();
			}
			catch { }

			try
			{
				this.MediaService.Dispose();
			}
			catch { }

			try
			{
				if (this.SurveysService != null)
					this.SurveysService.Dispose();
			}
			catch { }

			try
			{
				if (this.GatewayService != null)
					this.GatewayService.Dispose();
			}
			catch { }

			try
			{
				if (this.EventsService != null)
					this.EventsService.Dispose();
			}
			catch { }

			this.Initialized = false;
			this.started = false;
			this.starting = false;
			this.initializing = false;
		}
#endif

		#endregion

		#region IDisposable Members

		public void Dispose()
		{
			this.Dispose(false);
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Inicializa el sistema
		/// </summary>
		public void Initialize()
		{
			try
			{
				if (this.Initialized || this.initializing)
					return;

				this.initializing = true;
				this.IsReady = false;

				InitializeCache();

				bool startMessagingService;
				if (!bool.TryParse(ConfigurationManager.AppSettings["StartMessagingService"], out startMessagingService))
					startMessagingService = true; 

				if (startMessagingService)
				{
					// El timer de nuevos mensajes debe ser activo y permanente. El timer de mantenimiento alcanza con ejecutarlo a menudo pero no tan seguido.
					timerTasks = new Timer(5000);
					timerTasks.Elapsed += new ElapsedEventHandler(timerTasks_Elapsed);
				}

				try
				{
					if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
					{
						var version = RetrieveWebAgentVersion();
						if (!string.IsNullOrEmpty(version))
						{
							DomainModel.SystemSettings.Instance.WebAgentVersion = version;
						}
					}
				}
				catch { }

				ResetProcessingPushMessages();

				Tracer.TraceInfo("Se finalizó la inicialización");

				#region Set Properties
				this.Initialized = true;
				this.starting = false;
				this.started = false;
				this.readyFired = false;
				this.initializing = false;
				#endregion
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"Ocurrió un error iniciando el sistema: {ex.Message}");
				this.InitializationFailed = true;
			}
		}

		/// <summary>
		/// Inicializa el sistema de mensajería
		/// </summary>
		public void Start()
		{
			var license = Licensing.LicenseManager.Instance.License.Configuration;
			if (license.ReadOnly)
			{
				this.Initialized = true;
				return;
			}

			lock (startinglock)
			{
				if (!this.Initialized)
					this.Initialize();

				if (this.started)
				{
					Tracer.TraceInfo("Ya se inició del sistema");
					return;
				}

				if (this.starting)
				{
					Tracer.TraceInfo("Ya hay una ejecución de inicio del sistema");
					return;
				}

				this.starting = true;
			}

			Tracer.TraceInfo("Iniciando el servicio de ySocial");

			try
			{
				if (timerTasks != null)
				{
					bool startMessagingService;
					if (!bool.TryParse(ConfigurationManager.AppSettings["StartMessagingService"], out startMessagingService))
					{
						startMessagingService = true;
					}

					if (startMessagingService)
					{
						if (this.SecurityPoliticsService == null || !this.SecurityPoliticsService.Initialized)
						{
							this.SecurityPoliticsService = new SecurityPoliticsService();
							this.SecurityPoliticsService.Initialize();
						}

						if (license.AllowAutomaticExport &&
							(this.AutomaticExportService == null || !this.AutomaticExportService.Initialized))
						{
							this.AutomaticExportService = new AutomaticExportService();
							this.AutomaticExportService.Initialize();
						}

						if (this.ServerIntegrationsService == null || !this.ServerIntegrationsService.Initialized)
						{
							this.ServerIntegrationsService = new Services.ServerIntegrationsService();
							this.ServerIntegrationsService.Initialize();
						}

						if (this.IntervalService == null || !this.IntervalService.Initialized)
						{
							this.IntervalService = new IntervalsService();
							this.IntervalService.Initialize();
						}

						if (!license.WorkAsGateway)
						{
							ResetAssignedMessages();

							DiscardMessagesOfClosedCases();

							if (this.AgentsService == null || !this.AgentsService.Initialized)
							{
								this.AgentsService = new AgentsService();
								this.AgentsService.Initialize();
							}

							if (this.QueueService == null || !this.QueueService.Initialized)
							{
								this.QueueService = new Services.QueueService();
								this.QueueService.Initialize();
							}
						}

						if (this.CasesService == null || !this.CasesService.Initialized)
						{
							this.CasesService = new Services.CasesService();
							this.CasesService.OnInitializationFinished = () =>
							{
								Tracer.TraceInfo("Finalizó la inicialización del servicio de casos");
								this.IsReady = true;

								if (!this.starting)
								{
									// Si ya finalizó la inicialización, entonces disparamos el evento Ready
									this.OnReady();
								}
							};
							this.CasesService.Initialize();
						}

						if (!license.WorkAsGateway && 
							license.AllowedServiceTypes != null &&
							license.AllowedServiceTypes.Any(st => st == ServiceTypes.Chat ||
							st == ServiceTypes.IntegrationChat))
						{
							if (this.ChatsService == null)
							{
								try
								{
									this.ChatsService = new Services.ChatsService();
									this.ChatsService.Initialize();
								}
								catch (Exception ex)
								{
									Tracer.TraceError("Ocurrió un error iniciando el servicio de chat: {0}", ex);
								}
							}
							else
							{
								Tracer.TraceInfo("El servicio de chat ya se estaba ejecutando");
							}
						}

						if (this.ServicesServices == null || !this.ServicesServices.Initialized)
						{
							this.ServicesServices = new Services.ServicesService();
							this.ServicesServices.Initialize();
						}

						if (this.MediaService == null || !this.MediaService.Initialized)
						{
							this.MediaService = new Services.MediaService();
							this.MediaService.Initialize();
						}

						if (this.RealTimeService == null)
						{
							this.RealTimeService = new RealTimeService();
							this.RealTimeService.Initialize();
						}

						if (this.TasksService == null || !this.TasksService.Initialized)
						{
							this.TasksService = new TasksService();
							this.TasksService.Initialize();
						}

						if (this.DeliveryFailedService == null || !this.DeliveryFailedService.Initialized)
						{
							this.DeliveryFailedService = new DeliveryFailedService();
							this.DeliveryFailedService.Initialize();
						}

						if (this.UsersService == null || !this.UsersService.Initialized)
						{
							this.UsersService = new UsersService();
							this.UsersService.Initialize();
						}

						if (license.SurveysEnabled)
						{
							this.SurveysService = new SurveysService();
							this.SurveysService.Initialize();
						}

						if (!license.WorkAsGateway && license.AllowAgentsToStartVideoCall)
						{
							this.VideoService = new VideoService();
							this.VideoService.Initialize();
						}

						if (license.WorkAsGateway)
						{
							this.GatewayService = new GatewayService();
							this.GatewayService.Initialize();
						}

						this.EventsService = new EventsService();
						this.EventsService.Initialize();
						
					}

					Tracer.TraceInfo("Inicializando timer");
					timerTasks.Start();
				}

				lock (startinglock)
				{
					this.starting = false;
					this.started = true;

					if (this.IsReady && !this.readyFired)
					{
						Tracer.TraceInfo("Finalizó el inicio del sistema pero no se disparó el evento Ready, se lanza al finalizar el Initialize");
						this.OnReady();
					}
				}

				Tracer.TraceInfo("Finalizó el inicio del sistema");
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error iniciando el sistema: {0}", ex);
				
				this.starting = false;
				this.started = false;
				this.InitializationFailed = true;
			}
		}

		/// <summary>
		/// Reinicia el sistema completo
		/// </summary>
		public void Restart()
		{
			Tracer.TraceInfo("Se reiniciará el sistema");

			Tracer.TraceInfo("Deteniendo servicios y Liberando recursos");
			this.Dispose(true);

			Tracer.TraceInfo("Configurando servicios");
			this.Initialize();

			Tracer.TraceInfo("Iniciando servicios");
			this.Start();

			Tracer.TraceInfo("Se reinició el sistema");
		}

		#endregion
	}
}