﻿<%@ Page Async="true" Title="" Language="C#" MasterPageFile="~/Master.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="ServicesMail.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.ServicesMail" %>

<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.colorpicker.css")%>' rel="stylesheet" type="text/css" />

	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.colorpicker.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.numeric.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/URI.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.getUrlParam.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.filedrop.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesCommon.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesMail.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/dragula.min.js")%>'></script>

	<style type="text/css">
		.uiInfoTable .label { width: 150px !important; }
	</style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<asp:Image runat="server" ImageUrl="~/Images/Mail.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-servicesmail-title">Mail</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<div style="display: none">
		<div id="divServices" class="seccion">
            <div class="title">
                <h2 data-i18n="configuration-servicesmail-copy_attributes">Copiar atributos</h2>
            </div>
            <div class="contents">
                <yoizen:Message runat="server" Type="Information" Text="Seleccione el servicio de Mail del cual se desea copiar los atributos" Small="true" LocalizationKey="configuration-servicesmail-select_service" />
                <div id="divAllServices" style="max-height: 300px; overflow-y: auto; overflow-x: auto; max-width: 100%">
                    <table id="tableAllServices" class="reporte" cellspacing="0" rules="all" border="1" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr class="header">
                                <th style="width: 20px;" scope="col">&nbsp;</th>
                                <th scope="col"><span data-i18n="configuration-services-service">Servicio</span></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="buttons">
                    <label class="uiButton uiButtonLarge uiButtonConfirm">
                        <button type="button" data-i18n="globals-accept" id="buttonCopyAnswerDialogConfirm" onclick="CopyDialogConfirmCommon()">Aceptar</button>
                    </label>
                    <label class="uiButton uiButtonLarge">
                        <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                    </label>
                </div>
            </div>
        </div>
		<div id="divMailTestIMAP">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-imap_test-title">Prueba de conexión de servidor IMAP</h2>
				</div>
				<div class="contents">
					<div rel="loading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageMailTestIMAPResult" runat="server" Type="Information" ClientIDMode="Static"/>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="divMailTestPOP3">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-pop3_test-title">Prueba de conexión de servidor POP3</h2>
				</div>
				<div class="contents">
					<div rel="loading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageMailTestPOP3Result" runat="server" Type="Information" ClientIDMode="Static"/>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="divMailTestEWS">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-exchange_test-title">Prueba de conexión de servidor Exchange Web Services</h2>
				</div>
				<div class="contents">
					<div rel="loading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageMailTestEWSResult" runat="server" Type="Information" ClientIDMode="Static"/>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="divMailTestSMTP">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-smpt_test-title">Prueba de conexión de servidor SMTP</h2>
				</div>
				<div class="contents">
					<div rel="loading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageMailTestSMTPResult" runat="server" Type="Information" ClientIDMode="Static"/>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="divMailTestGmail">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-gmail-test-title">Prueba de conexión de Gmail</h2>
				</div>
				<div class="contents">
					<div rel="loading" class="loading">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
					<yoizen:Message ID="messageMailTestGmailResult" runat="server" Type="Information" ClientIDMode="Static"/>
					<div class="buttons">
						<label class="uiButton uiButtonLarge uiButtonConfirm">
							<button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
						</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="tabsMail">
		<ul>
			<li>
				<a href="#divMailConnection" data-i18n="configuration-servicesmail-connection">Conexión</a>
			</li>
			<li>
				<a href="#divMailTabInbound" data-i18n="configuration-servicesmail-reception">Recepción</a>
			</li>
			<li>
				<a href="#divMailTabOutbound" data-i18n="configuration-servicesmail-send">Envío</a>
			</li>
			<li>
				<a href="#divMailTabOther" data-i18n="configuration-servicesmail-other_settings">Otras configuraciones</a>
			</li>
			<li id="liTabCases">
				<a href="#divCases" data-i18n="configuration-systemsettings-cases">Casos</a>
			</li>
			<li>
				<a href="#divNotifications" data-i18n="configuration-servicesmail-notifications">Notificaciones por email</a>
			</li>
		</ul>
		<div id="divMailConnection">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-connection_type-title">Tipo de conexión</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Warning" LocalizationKey="configuration-servicesmail-connection_type-warning">
						El tipo de conexión indicará las conexiones al servidor de correo entrante y saliente.
						Modificar este parámetro una vez configurado puede generar inconsistencias
					</yoizen:Message>
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-connection_type">Tipo de conexión</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistMailConnectionType" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="1">IMAP y SMTP</asp:ListItem>
									<asp:ListItem Value="2">POP3 y SMTP</asp:ListItem>
									<asp:ListItem Value="3">Exchange Web Services</asp:ListItem>
									<asp:ListItem Value="4">Gmail</asp:ListItem>
								</asp:DropDownList>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div id="divIMAP" class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-imap_access_data-title">Datos de acceso al servidor IMAP</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-server">Servidor</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveServer" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-port">Puerto</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrievePort" runat="server" Width="40" ClientIDMode="Static" autocomplete="off" MaxLength="4" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-user">Usuario</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveUser" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
							</td>
						</tr>
						<asp:PlaceHolder ID="placeholderMailRetrievePassword" runat="server">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-servicesmail-configured_password">Contraseña configurada</span>:</th>
								<td class="data">
									<asp:Label ID="labelMailRetrievePassword" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
						</asp:PlaceHolder>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">
								<asp:Literal ID="literalMailRetrievePasswordChangePasswordTitle" runat="server"> <span data-i18n="configuration-servicesmail-new_password">Nueva contraseña</span>:</asp:Literal>
								<asp:Literal ID="literalMailRetrievePasswordNewPasswordTitle" runat="server"><span data-i18n="globals-password">Contraseña</span>:</asp:Literal>
							</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs" style="width: 200px;">
												<asp:CheckBox ID="checkboxMailRetrievePassword" runat="server" ClientIDMode="Static" ToolTip="Establecer una nueva contraseña" data-i18n-title="configuration-servicesmail-retrive_password"/>
												<asp:TextBox ID="textboxMailRetrievePassword" runat="server" Width="150" TextMode="Password" ClientIDMode="Static" placeholder="Contraseña" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-retrive_password-tip">
												Este parámetro indica la contraseña del usuario que se utilizará para realizar la autenticación 
												contra el servidor de correo.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-use_ssl">Utilizar SSL</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxEmailRetrieveUseSSL" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_ssl-tip">
												Este parámetro indica si para conectarse al servidor de email se utilizará SSL
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trEmailRetrieveSSLConnectionType" style="display: none">
							<th class="label"><span data-i18n="configuration-servicesmail-ssl_secure_options">Opciones de seguridad</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistEmailRetrieveSSLSecureOptions" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="2">SslOnConnect</asp:ListItem>
									<asp:ListItem Value="3">StartTls</asp:ListItem>
									<asp:ListItem Value="4">StartTlsWhenAvailable</asp:ListItem>
								</asp:DropDownList>
								<yoizen:Message runat="server" Type="Information" style="margin-top: 5px">
									<span data-i18n="configuration-servicesmail-ssl_secure_options-tip">Este parámetro especifica las opciones de seguridad que se tendrán al conectarse utilizando SSL</span>:
									<ul>
										<li><span class='templatefieldname'>SslOnConnect</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-2">La conexión debe usar encriptación SSL o TLS inmediatamente</span></li>
										<li><span class='templatefieldname'>StartTls</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-3">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor. Si el servidor no soporta la extensión STARTTLS, la conexión fallará</span></li>
										<li><span class='templatefieldname'>StartTlsWhenAvailable</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-4">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor pero únicamente si el el servidor soporta la extensión STARTTLS</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateIMAP" SkinID="validationerror" data-i18n="configuration-servicesmail-validation_error-imap">Debe completar todos los datos para conexión del servidor IMAP</asp:CustomValidator></div>
					<div class="buttons">
						<label class="uiButton">
							<button type="button" data-i18n="configuration-servicesmail-test_connection" onclick="MailIMAPTest()">Realizar prueba de conexión</button>
						</label>
					</div>
				</div>
			</div>
			<div id="divPOP3" class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-pop3_access_data-title">Datos de acceso al servidor POP3</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-server">Servidor</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrievePOP3Server" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-port">Puerto</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrievePOP3Port" runat="server" Width="40" ClientIDMode="Static" autocomplete="off" MaxLength="4" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-user">Usuario</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrievePOP3User" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
							</td>
						</tr>
						<asp:PlaceHolder ID="placeholderMailRetrievePOP3Password" runat="server">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-servicesmail-configured_password">Contraseña configurada</span>:</th>
								<td class="data">
									<asp:Label ID="labelMailRetrievePOP3Password" runat="server" ClientIDMode="Static" />
								</td>
							</tr>
						</asp:PlaceHolder>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">
								<asp:Literal ID="literalMailRetrievePOP3PasswordChangePasswordTitle" runat="server"> <span data-i18n="configuration-servicesmail-new_password">Nueva contraseña</span>:</asp:Literal>
								<asp:Literal ID="literalMailRetrievePOP3PasswordNewPasswordTitle" runat="server">Contraseña:</asp:Literal>
							</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs" style="width: 200px;">
												<asp:CheckBox ID="checkboxMailRetrievePOP3Password" runat="server" ClientIDMode="Static" ToolTip="Establecer una nueva contraseña" data-i18n-title="configuration-servicesmail-retrive_password"/>
												<asp:TextBox ID="textboxMailRetrievePOP3Password" runat="server" Width="150" TextMode="Password" ClientIDMode="Static" placeholder="Contraseña" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-retrive_password-tip">
												Este parámetro indica la contraseña del usuario que se utilizará para realizar la autenticación 
												contra el servidor de correo.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-use_ssl">Utilizar SSL</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxEMailRetrievePOP3UseSSL" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_ssl-tip">
												Este parámetro indica si para conectarse al servidor de email se utilizará SSL
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trEmailRetrievePOP3SSLConnectionType" style="display: none">
							<th class="label"><span data-i18n="configuration-servicesmail-ssl_secure_options">Opciones de seguridad</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistEmailRetrievePOP3SSLSecureOptions" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="2">SslOnConnect</asp:ListItem>
									<asp:ListItem Value="3">StartTls</asp:ListItem>
									<asp:ListItem Value="4">StartTlsWhenAvailable</asp:ListItem>
								</asp:DropDownList>
								<yoizen:Message runat="server" Type="Information" style="margin-top: 5px">
									<span data-i18n="configuration-servicesmail-ssl_secure_options-tip">Este parámetro especifica las opciones de seguridad que se tendrán al conectarse utilizando SSL</span>:
									<ul>
										<li><span class='templatefieldname'>SslOnConnect</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-2">La conexión debe usar encriptación SSL o TLS inmediatamente</span></li>
										<li><span class='templatefieldname'>StartTls</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-3">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor. Si el servidor no soporta la extensión STARTTLS, la conexión fallará</span></li>
										<li><span class='templatefieldname'>StartTlsWhenAvailable</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-4">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor pero únicamente si el el servidor soporta la extensión STARTTLS</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidatePOP3" SkinID="validationerror" data-i18n="configuration-servicesmail-validation_error-pop3">Debe completar todos los datos para conexión del servidor POP3</asp:CustomValidator></div>
					<div class="buttons">
						<label class="uiButton">
							<button type="button" data-i18n="configuration-servicesmail-test_connection" onclick="MailPOP3Test()">Realizar prueba de conexión</button>
						</label>
					</div>
				</div>
			</div>
			<div id="divSMTP" class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-smtp_access_data-title">Datos de acceso al servidor SMTP</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-server">Servidor</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailSendServer" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-port">Puerto</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailSendPort" runat="server" Width="40px" ClientIDMode="Static" autocomplete="off" MaxLength="4" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-use_authentication">Utilizar Autenticación</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0" style="width: 100%">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxMailSendUseCredentials" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_authentication-tip">
												Este parámetro indica si se utilizará algún usuario para conectarse al servidor SMTP
											</td>
										</tr>
										<tr id="trMailSendUseCredentials">
											<td colspan="2">
												<div class="subseccion">
													<div class="title">
														<h2 data-i18n="configuration-servicesmail-authentication_data-title">Datos de autenticación</h2>
													</div>
													<div class="contents">
														<table width="100%" border="0" class="uiInfoTable noBorder">
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 80px !important;"><span data-i18n="configuration-servicesmail-user">Usuario</span>:</th>
																<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																		<tbody>
																			<tr>
																				<td class="vMid prs">
																					<asp:TextBox ID="textboxMailSendUser" runat="server" Width="300" ClientIDMode="Static" />
																				</td>
																				<td class="vMid pls" data-i18n="configuration-servicesmail-user-tip">
																					Este parámetro indica el usuario que se utilizará para conectarse al servidor de email
																				</td>
																			</tr>
																		</tbody>
																	</table>
																</td>
															</tr>
															<asp:PlaceHolder ID="placeholderMailSendPassword" runat="server">
																<tr class="dataRow dataRowSeparator">
																	<th class="label withdescription"><span data-i18n="configuration-servicesmail-configured_password">Contraseña configurada</span>:</th>
																	<td class="data">
																		<asp:Label ID="labelMailSendPassword" runat="server" ClientIDMode="Static" />
																	</td>
																</tr>
															</asp:PlaceHolder>
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription">
																	<asp:Literal ID="literalMailSendPasswordChangePasswordTitle" runat="server"><span data-i18n="configuration-servicesmail-new_password">Nueva contraseña</span>:</asp:Literal>
																	<asp:Literal ID="literalMailSendPasswordNewPasswordTitle" runat="server"><span data-i18n="globals-password">Contraseña</span>:</asp:Literal>
																</th>
																<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																		<tbody>
																			<tr>
																				<td class="vMid prs" style="width: 200px;">
																					<asp:CheckBox ID="checkboxMailSendPassword" runat="server" ClientIDMode="Static" ToolTip="Establecer una nueva contraseña" data-i18n-title="configuration-servicesmail-retrive_password"/>
																					<asp:TextBox ID="textboxMailSendPassword" runat="server" Width="150" TextMode="Password" ClientIDMode="Static" placeholder="Contraseña" />
																				</td>
																				<td class="vMid pls" data-i18n="configuration-servicesmail-retrive_password-tip">
																					Este parámetro indica la contraseña del usuario que se utilizará para realizar la autenticación 
																					cuando se realicen búsquedas contra MailSend.
																				</td>
																			</tr>
																		</tbody>
																	</table>
																</td>
															</tr>
														</table>
													</div>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-use_ssl">Utilizar SSL</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxEmailSendUseSSL" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_ssl-tip">
												Este parámetro indica si para conectarse al servidor de email se utilizará SSL
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trEmailSendSSLConnectionType" style="display: none">
							<th class="label"><span data-i18n="configuration-servicesmail-ssl_secure_options">Opciones de seguridad</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistEmailSendSSLSecureOptions" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="2">SslOnConnect</asp:ListItem>
									<asp:ListItem Value="3">StartTls</asp:ListItem>
									<asp:ListItem Value="4">StartTlsWhenAvailable</asp:ListItem>
								</asp:DropDownList>
								<yoizen:Message runat="server" Type="Information" style="margin-top: 5px">
									<span data-i18n="configuration-servicesmail-ssl_secure_options-tip">Este parámetro especifica las opciones de seguridad que se tendrán al conectarse utilizando SSL</span>:
									<ul>
										<li><span class='templatefieldname'>SslOnConnect</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-2">La conexión debe usar encriptación SSL o TLS inmediatamente</span></li>
										<li><span class='templatefieldname'>StartTls</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-3">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor. Si el servidor no soporta la extensión STARTTLS, la conexión fallará</span></li>
										<li><span class='templatefieldname'>StartTlsWhenAvailable</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-ssl_secure_options-4">Eleva la conexión para usar encriptación TLS inmediatamente luego de leer el saludo de bienvenida y las capacidades del servidor pero únicamente si el el servidor soporta la extensión STARTTLS</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSMTP" SkinID="validationerror" data-i18n="configuration-servicesmail-validation_error-smtp">Debe completar todos los datos para conexión del servidor SMTP</asp:CustomValidator></div>
					<div class="buttons">
						<label class="uiButton">
							<button type="button" data-i18n="configuration-servicesmail-test_connection" onclick="MailSMTPTest()">Realizar prueba de conexión</button>
						</label>
					</div>
				</div>
			</div>
			<div id="divEWS" class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-exchange_access_data-title">Datos de acceso al servidor de correo a través de Exchange Web Services</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-server">Servidor</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveEWSServer" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
								<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px;" LocalizationKey="[html]configuration-servicesmail-server-tip">
									El servidor generalmente incluye en la parte final <span class="mono">/EWS/Exchange.asmx</span>
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-ews-authentication_type">Tipo de autenticación</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistMailRetrieveEWSAuthenticationType" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="1" Selected="True" data-i18n="configuration-servicesmail-ews-authentication_type-user_and_password">Usuario y contraseña</asp:ListItem>
									<asp:ListItem Value="2" data-i18n="configuration-servicesmail-ews-authentication_type-oauth">OAuth</asp:ListItem>
								</asp:DropDownList>
								<div id="divEWSAuthenticationTypeUserAndPassword" class="subseccion">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-ews-authentication_type-user_and_password">Usuario y contraseña</h2>
									</div>
									<div class="contents">
										<table width="95%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label" style="width: 200px !important;"><span data-i18n="configuration-servicesmail-user">Usuario</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxMailRetrieveEWSUser" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
												</td>
											</tr>
											<asp:PlaceHolder ID="placeholderMailRetrieveEWSPassword" runat="server">
												<tr class="dataRow dataRowSeparator">
													<th class="label withdescription"><span data-i18n="configuration-servicesmail-configured_password">Contraseña configurada</span>:</th>
													<td class="data">
														<asp:Label ID="labelMailRetrieveEWSPassword" runat="server" ClientIDMode="Static" />
													</td>
												</tr>
											</asp:PlaceHolder>
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription">
													<asp:Literal ID="literalMailRetrieveEWSPasswordChangePasswordTitle" runat="server"><span data-i18n="configuration-servicesmail-new_password">Nueva contraseña</span>:</asp:Literal>
													<asp:Literal ID="literalMailRetrieveEWSPasswordNewPasswordTitle" runat="server"><span data-i18n="globals-password">Contraseña</span>:</asp:Literal>
												</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs" style="width: 200px;">
																	<asp:CheckBox ID="checkboxMailRetrieveEWSPassword" runat="server" ClientIDMode="Static" ToolTip="Establecer una nueva contraseña" data-i18n-title="configuration-servicesmail-retrive_password"/>
																	<asp:TextBox ID="textboxMailRetrieveEWSPassword" runat="server" Width="150" TextMode="Password" ClientIDMode="Static" placeholder="Contraseña" />
																</td>
																<td class="vMid pls" data-i18n="configuration-servicesmail-retrive_password-tip">
																	Este parámetro indica la contraseña del usuario que se utilizará para realizar la autenticación 
																	contra el servidor de correo.
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div id="divEWSAuthenticationTypeOAuth" class="subseccion">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-ews-authentication_type-oauth">OAuth</h2>
									</div>
									<div class="contents">
										<table width="95%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label" style="width: 200px !important;"><span data-i18n="configuration-servicesmail-ews-oauth-email">Casilla de email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxMailRetrieveEWSOAuthEmailAddress" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-servicesmail-ews-oauth-appid">Identificador de Aplicación</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxMailRetrieveEWSOAuthAppID" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-servicesmail-ews-oauth-secret">Secreto de cliente</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxMailRetrieveEWSOAuthClientSecret" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-servicesmail-ews-oauth-tenantid">Identificador de directorio</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxMailRetrieveEWSOAuthTenantID" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
												</td>
											</tr>
										</table>
									</div>
								</div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-timeout">Tiempo de desconexión (timeout)</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveEWSTimeout" runat="server" Width="80" ClientIDMode="Static" autocomplete="off" MaxLength="5" TextMode="Number" />
								<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px;" LocalizationKey="[html]configuration-servicesmail-timeout-tip">
									El tiempo de desconexión (timeout) permite especificar, en segundos, el tiempo máximo que se esperará ante cada pedido 
									que se realice al servidor antes de abortar.<br />
									Si se espera que el servicio reciba correos de mucho tamaño, es recomendable subir el tiempo de desconexión para evitar
									que los mensajes no puedan ser descargados. El valor por defecto es 100 segundos.
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-exchange_version">Versión de Exchange</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistMailRetrieveEWSExchangeVersion" runat="server" ClientIDMode="Static">
									<asp:ListItem Value="" enum="Latest" data-i18n="configuration-servicesmail-latest">Última versión</asp:ListItem>
									<asp:ListItem Value="0" enum="Exchange2007_SP1">Microsoft Exchange 2007, Service Pack 1</asp:ListItem>
									<asp:ListItem Value="1" enum="Exchange2010">Microsoft Exchange 2010</asp:ListItem>
									<asp:ListItem Value="2" enum="Exchange2010_SP1">Microsoft Exchange 2010, Service Pack 1</asp:ListItem>
									<asp:ListItem Value="3" enum="Exchange2010_SP2">Microsoft Exchange 2010, Service Pack 2</asp:ListItem>
									<asp:ListItem Value="4" enum="Exchange2013">Microsoft Exchange 2013</asp:ListItem>
									<asp:ListItem Value="5" enum="Exchange2013_SP1">Microsoft Exchange 2013, Service Pack 1</asp:ListItem>
								</asp:DropDownList>
							</td>
						</tr>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateEWS" SkinID="validationerror" data-i18n="configuration-servicesmail-validation_error-exchange">Debe completar todos los datos para conexión del servidor EWS</asp:CustomValidator></div>
					<div class="buttons">
						<label class="uiButton">
							<button type="button" data-i18n="configuration-servicesmail-test_connection" onclick="MailEWSTest()">Realizar prueba de conexión</button>
						</label>
					</div>
				</div>
			</div>
			<div id="divGmail" class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-gmail-title">Gmail</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-user">Usuario</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveGmailUser" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-gmail-credentials">Credenciales</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailRetrieveGmailCredentials" runat="server" Width="80%" ClientIDMode="Static" autocomplete="off" TextMode="MultiLine" Rows="4" />
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="[html]configuration-servicesmail-gmail-credentials-tip">
									Las credenciales de la clave se obtienen de una cuenta de servicio dentro de la consola de Google. Se debe exportar la clave en formato JSON
									y utilizarla aquí. Adicionalmente, un administrador de dominio deberá dar acceso a la cuenta para obtener delegación de dominio y acceso al scope
									http://mail.google.com (para más información se puede consultar el siguiente <a href="https://developers.google.com/cloud-search/docs/guides/delegation">artículo</a>)
								</yoizen:Message>
							</td>
						</tr>
					</table>
					<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateGmail" SkinID="validationerror" data-i18n="configuration-servicesmail-gmail-validation_error">Debe completar todos los datos para conexión con Gmail</asp:CustomValidator></div>
					<div class="buttons">
						<label class="uiButton">
							<button type="button" data-i18n="configuration-servicesmail-test_connection" onclick="MailGmailTest()">Realizar prueba de conexión</button>
						</label>
					</div>
				</div>
			</div>
		</div>
		<div id="divMailTabInbound">
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-behaviour-title">Comportamiento</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator" data-protocol-dependency="imap">
							<th class="label"><span data-i18n="configuration-servicesmail-mark_as_read">Marcar como leídos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxEmailSetAsRead" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-mark_as_read-tip">
												Este parámetro indica si cuando llega un mail automáticamente se marca como leído en el servidor.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-delete_received">Eliminar recibidos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxEmailSetAsDeleted" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-delete_received-tip">
												Este parámetro indica si cuando llega un mail automáticamente se borra en el servidor.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" data-protocol-dependency="imap">
							<th class="label"><span data-i18n="configuration-servicesmail-days_until_deleted">Eliminar mails más antiguos que</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxDaysUntilMailsAreDeleted" runat="server" Width="50px" ClientIDMode="Static" autocomplete="off" />
												<asp:CompareValidator runat="server" ControlToValidate="textboxDaysUntilMailsAreDeleted" Type="Integer" Operator="GreaterThanEqual" ValueToCompare="1" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-days_until_deleted-tip">
												Especifica la cantidad de días que duran los mails en el servidor de mail. Si especifica un valor (mayor que cero) aquellos mails
												que estén en el servidor por más de los días especificados, serán borrados. Deje en blanco para no borrar mails antiguos
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailFromDate" runat="server" Width="120" ClientIDMode="Static" />
												<asp:CustomValidator runat="server" ControlToValidate="textboxMailFromDate" EnableClientScript="true" ClientValidationFunction="ValidateDateField" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-from_date-tip">
												Especifica la fecha a partir de la cual se obtendrán los emails
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-use_received_date">Utilizar fecha de recepción en lugar de envio</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxMailUseReceivedDateInsteadOfSentDate" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_received_date-tip">
												Especifica si se utilizará la fecha de recepción del mail en lugar de la fecha de envio
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trMailUseReceivedDateIfSentDateIsInTheFuture">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-use_received_date_if_future">Utilizar fecha de recepción si la fecha de envio es posterior</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxMailUseReceivedDateIfSentDateIsInTheFuture" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-use_received_date_if_future-tip">
												Especifica si se utilizará la fecha de recepción del mail en lugar de la fecha de envio cuando
												la fecha de envio sea posterior a la fecha actual del sistema. Esto puede ocurrir cuando algunos mails 
												son redactados en computadoras con una fecha y hora errónea
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-error_mail">Mail de error</span>:</th>
							<td class="data">
								<table cellpadding="0" cellspacing="0" class="uiGrid">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailDeliveryErrorFrom" runat="server" autocomplete="off" ClientIDMode="Static" TextMode="Email" Width="250" />
												<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailDeliveryErrorFrom" />
												<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMailDeliveryErrorFrom" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-error_mail-tip">
												Este parámetro indica la casilla de mail de donde provienen los errores cuando un mail no puede ser enviado
												por algún error proveniente del servidor de mail (generalmente estas casillas son llamadas mailer-daemon, postmaster, etc).
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">
								Ignorar mails con <span style="white-space: nowrap">X-Failed-Recipients</span>:
							</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEmailFilterIgnoreXFailedRecipients" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="[html]configuration-servicesmail-x_failed_recipients-tip">
												Este parámetro indica si los servicios de email ignorarán aquellos mails que incluyan información en la variable <span class="mono">X-Failed-Recipients</span> de la cabecera
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">
								Ignorar mails con <span style="white-space: nowrap">Content-Type: multipart/report</span>:
							</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEmailFilterIgnoreMultipartReport" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="[html]configuration-servicesmail-multipart_report-tip">
												Este parámetro indica si los servicios de email ignorarán aquellos mails cuyo tipo de contenido sea <span class="mono">Content-Type: multipart/report;</span>.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-grouping-title">Agrupamiento</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-grouping_type">Tipo de agrupamiento</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistGrouping" runat="server">
									<asp:ListItem Value="1" Text="Agrupar todos los mensajes" Selected="True" data-i18n="configuration-servicesmail-groupe_all" />
									<asp:ListItem Value="2" Text="Agrupar todos los mensajes excepto aquellos que vienen de ciertas casillas" data-i18n="configuration-servicesmail-groupe_all_from_box" />
									<asp:ListItem Value="3" Text="No agrupar (no recomendado)" data-i18n="configuration-servicesmail-not_groupe" />
								</asp:DropDownList>
								<div id="divGroupingExcludedSenders" class="subsubseccion" style="display: none">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-grouping_boxes-title">Casillas que no se tendrán en cuenta para el agrupamiento</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription" style="width: 100px !important;"><span data-i18n="configuration-servicesmail-mail_boxes">Casillas de mail</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs" style="width: 50%">
																	<asp:TextBox ID="textboxGroupingExcludedSenders" runat="server" ClientIDMode="Static" TextMode="MultiLine" Rows="5" Width="90%" />
																	<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateGroupingExcludedSenders" />
																</td>
																<td class="vMid pls" data-i18n="configuration-servicesmail-mail_boxes-tip">
																	Especifica las casillas de mail que no serán utilizadas a la hora de agrupar mensajes. Puede escribir más de una casilla de mail separando por coma
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-cases-title">Casos</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-case_handling">Manejo de Casos</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistCaseHandling" runat="server">
									<asp:ListItem Value="1" Text="Crear casos nuevos por hilo de conversación de mail por cada remitente" Selected="True" data-i18n="configuration-servicesmail-case1" />
									<asp:ListItem Value="2" Text="Crear casos únicos por cada remitente" data-i18n="configuration-servicesmail-case2" />
								</asp:DropDownList>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-web_forms-title">Formularios web</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-receive_web_forms">Recibe mails de Web Forms</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxReceivesWebFormsMails" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-receive_web_forms-tip">
												Este parámetro indica si el servicio recibe envios de mail provenientes de formularios web
											</td>
										</tr>
									</tbody>
								</table>
								<div id="divReceivesWebFormsMails" class="subseccion" style="display: none">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-web_forms-title">Formularios web</h2>
									</div>
									<div class="contents">
										<yoizen:Message runat="server" Type="Information">
											<span data-i18n="configuration-servicesmail-web_forms-template-1">
												Puede ingresar una o más direcciones de correo que se considerarán formularios web. A cada una se le asocia un
												nombre para poder ser utilizado luego para determinar de donde viene
											</span>.<br />
											<span data-i18n="configuration-servicesmail-web_forms-template-2">
												El nombre deberá ser un texto compuesto únicamente por caracteres y números con una longitud entre 3 y 50
											</span>.<br />
											<span data-i18n="[html]configuration-servicesmail-web_forms-template-3">
												Los mails que sean enviados por estas casillas y sean recibidos por el servicio configurado deberán contener los siguientes <i>headers</i>
											</span>:
											<ul>
												<li><span class='templatefieldname'>X-YoizenSocial-SenderMail</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-web_form_template-field-sender_mail">Indica el mail a donde se deberá realizar la respuesta</span></li>
												<li><span class='templatefieldname'>X-YoizenSocial-SenderName</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-web_form_template-field-sender_name">Indica el nombre de la persona a la que se realizará la respuesta</span></li>
												<li><span class='templatefieldname'>X-YoizenSocial-SenderBusinessData</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-web_form_template-field-sender_business_data">Indica los datos de negocio de la persona a la que se realizará la respuesta</span></li>
												<li><span class='templatefieldname'>X-YoizenSocial-WebFormName</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-web_form_template-field-web_form_name">Indica el nombre del formulario web</span></li>
												<li><span class='templatefieldname'>X-YoizenSocial-ExtraParameters</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-web_form_template-field-extra_paramenters">Indica otros parámetros adicionales pedidos en el formulario web. Se deberán escribir en formato: CLAVE=VALOR y un enter</span></li>
											</ul>
										</yoizen:Message>
										<asp:HiddenField ID="hiddenWebForms" runat="server" ClientIDMode="Static"></asp:HiddenField>
										<table id="tableWebForms" class="reporte" cellspacing="0" rules="all" border="1">
											<thead>
												<tr class="header">
													<th style="width: 20px"></th>
													<th style="width: 300px" scope="col"><span data-i18n="globals-name">Nombre</span></th>
													<th scope="col"><span data-i18n="configuration-servicesmail-address">Dirección</span></th>
												</tr>
											</thead>
											<tbody></tbody>
											<tfoot>
												<tr>
													<td style="text-align: center"><a id="aWebFormsAdd" title="Agregar nuevo formulario web"><span class="fa fa-lg fa-plus-square"></span></a></td>
													<td colspan="2"></td>
												</tr>
											</tfoot>
										</table>
										<div class="valbigerror"><asp:CustomValidator ID="customvalidatorWebForms" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWebForms" SkinID="bigerror" /></div>
									</div>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div id="divMailTabOutbound">
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-behaviour-title">Comportamiento</h2>
				</div>
				<div class="contents">
					<table width="95%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">Cuenta de mail para Remitente:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailAddress" runat="server" Width="250" ClientIDMode="Static" autocomplete="off" TextMode="Email" />
												<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailAddress" />
												<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMailAddress" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" />
											</td>
											<td class="vMid pls">
												Especifica la casilla de correo de la cuenta que se utilizará para completar el encabezado <span class="mono">From</span> del mail
											</td>
										</tr>
									</tbody>
								</table>
								<yoizen:Message ID="messageSMTPUserAndReplyUserNotTheSame" runat="server" ClientIDMode="Static" Type="Information" style="display: none" LocalizationKey="[html]configuration-servicesmail-smtp_user_and_reply_not_the_same">
									El usuario especificado en <u>Responder a</u> es distinto al usuario especificado en la cuenta de conexión al <u>Servidor de correo</u>
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-display_name">Nombre a mostrar</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailDisplayName" runat="server" Width="200px" ClientIDMode="Static" autocomplete="off" />
												<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailDisplayName" />
											</td>
											<td class="vMid pls" data-i18n="[html]configuration-servicesmail-display_name-tip">
												Especifica el nombre que se mostrará al recibir un email de <span class="productname"></span>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription">Responder a:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailAddressForReplyTo" runat="server" Width="250" ClientIDMode="Static" autocomplete="off" TextMode="Email" />
												<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMailAddressForReplyTo" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" />
											</td>
											<td class="vMid pls">
												Especifica la casilla de correo que tendrá los mails envíados que le sugerirá al destinatario para realizar las respuestas.
												Generalmente este valor es el mismo que el especificado en el usuario que se utiliza para el SMTP.
												Se utiliza para completar el encabezado de mails <span class="mono">Reply-To</span>.
												Deje la casilla en blanco para no utilizar otra cuenta.
											</td>
										</tr>
									</tbody>
								</table>
								<yoizen:Message ID="message1" runat="server" ClientIDMode="Static" Type="Information" style="display: none">
									El usuario especificado en <u>Responder a</u> es distinto al usuario especificado en la cuenta de conexión al <u>Servidor SMTP</u>
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-subject">Asunto</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxMailEditSubject" runat="server" Width="200" ClientIDMode="Static" autocomplete="off" />
												<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailEditSubject" />
												<yoizen:RequiredTextFieldValidator runat="server" ControlToValidate="textboxMailEditSubject" TextToSearch="@@ASUNTO@@"></yoizen:RequiredTextFieldValidator>
											</td>
											<td class="vMid pls" data-i18n="[html]configuration-servicesmail-subject-tip">
												Permite editar el asunto agregando información al responder un email desde <span class="productname"></span>
											</td>
										</tr>
									</tbody>
								</table>
								<yoizen:Message ID="messageMailEditSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" style="margin-top: 10px">
									<span data-i18n="configuration-servicesmail-edit_subject_fields">Debe utilizar los siguientes campos dentro del texto del asunto</span>:<br />
									<span class='templatefieldname'>@@ASUNTO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-subject-field-subject">Indica el asunto del mensaje entrante</span>
								</yoizen:Message>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-allow_to_edit_subject">Permitir editar el asunto</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxMailAllowToEditSubject" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-allow_to_edit_subject-tip">
												Este parámetro indica si los agentes pueden editar el asunto a la hora de responder los mails
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-queue_priority">Darle prioridad a la firma de la cola</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxQueuePriority" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-queue_priority-tip">
												Este parámetro indica si se le debe dar prioridad a la firma de la cola
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-signature">Firma</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:DropDownList ID="dropdownlistMailSignatureBehaviour" runat="server" ClientIDMode="Static">
													<asp:ListItem Value="0" Text="No utilizar firma" Selected="True" data-i18n="configuration-servicesmail-signature_behaviour-0" />
													<asp:ListItem Value="1" Text="Incluir firma en forma automática en el envío de mails" data-i18n="configuration-servicesmail-signature_behaviour-1" />
													<asp:ListItem Value="2" Text="Incluir firma al momento de redactar el mail permitiendo editarla" data-i18n="configuration-servicesmail-signature_behaviour-2" />
													<asp:ListItem Value="3" Text="Incluir firma al momento de redactar el mail sin posibilidad de editarla" data-i18n="configuration-servicesmail-signature_behaviour-3" />
												</asp:DropDownList>
											</td>
											<td class="vMid pls" data-i18n="[html]configuration-servicesmail-signature-tip">
												Este parámetro indica si se agregará una firma que se mostrará al responder un email desde <span class="productname"></span>
											</td>
										</tr>
									</tbody>
								</table>
								<div class="subseccion" id="divMailSignature">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-signature">Firma</h2>
									</div>
									<div class="contents">
										<asp:TextBox ID="textboxMailSignature" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
										<yoizen:Message ID="messageMailSignature" ClientIDMode="Static" runat="server" Type="Information" Small="true" style="margin-top: 10px;">
											<span data-i18n="configuration-servicesmail-mail_signature_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
											<span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-mail_signature-field-user_name">Indica el usuario que está respondiendo el mail</span>
										</yoizen:Message>
									</div>
								</div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-quote_type">Tipo de cita del mensaje entrante</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:DropDownList ID="dropdownlistMailQuoteType" runat="server">
													<asp:ListItem Value="0" Text="No citar el mensaje entrante" Selected="True" data-i18n="configuration-servicesmail-quote_type-0" />
													<asp:ListItem Value="1" Text="Citar en forma simple" data-i18n="configuration-servicesmail-quote_type-1" />
													<asp:ListItem Value="2" Text="Citar en forma extendida" data-i18n="configuration-servicesmail-quote_type-2" />
												</asp:DropDownList>
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-quote_type-tip">
												Este parámetro indica el tipo de cita del mensaje entrante que se incluirá en la respuesta del mail
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div id="divMailTabOther">
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-more_configuration-title">Otras configuraciones</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-service_name">Nombre de servicio</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxServiceName" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" MaxLength="50" autotrim="true" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxServiceName" />
								<asp:CustomValidator ID="customvalidatorServiceName" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceName" OnServerValidate="customvalidatorServiceName_ServerValidate" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-add_cc">Permitir enviar Con Copia (CC)</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEmailAddCC" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-add_cc-tip">
												Este parámetro indica si se permite enviar copias del mail al responder
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-add_bcc">Permitir enviar Con Copia Oculta (CCO)</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEmailAddBCC" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-add_bcc-tip">
												Este parámetro indica si se permite enviar copias del mail al responder
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-outside_domain_available">CC/CCO fuera de dominio</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxAnswerOutsideDomainAvailable" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-outside_domain_available-tip">
												Este parámetro indica si se pueden responder a mails fuera del dominio via cc/cco
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trAvailableDomains" style="display: none">
							<th class="label"><span data-i18n="configuration-servicesmail-available_domains">Dominios permitidos para CC/CCO</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:TextBox ID="textboxAvailableDomains" runat="server" Width="250" TextMode="MultiLine" Rows="3" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMailAvailableDomains" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-available_domains-tip">
												Ingresar los dominios habilitados para envío de mails via cc/cco. (Ej.: gmail.com,hotmail.com)
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-reply_options">Acciones de respuesta</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs checkbox">
												<asp:CheckBox ID="checkboxReplyOptionsAvailable" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-reply_options-tip">
												Este parámetro indica si se habilita o no las acciones de respuesta (Ej. Responder a todos)
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<asp:PlaceHolder ID="placeholderMailCheckSpelling" runat="server">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-servicesmail-spell_check">Revisión ortográfica obligatoria</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs checkbox">
													<asp:CheckBox ID="checkboxMailCheckSpelling" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-servicesmail-spell_check-tip">
													Este parámetro indica si antes de enviar el mensaje es obligatoria la revisión ortográfica
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</asp:PlaceHolder>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-change_font">Cambio de fuente</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxChangeFontAvailable" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-change_font-tip">
												Este parámetro indica si el agente puede cambiar el tipo y tamaño de fuente cuando responde el mensaje.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trFontType">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-font_type">Tipo de Fuente</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:DropDownList ID="dropdownlistFontType" runat="server" AppendDataBoundItems="true" DataTextField="Name" DataValueField="ID">
													<asp:ListItem Selected="True" Value="0" Text="Ninguna" data-i18n="configuration-servicesmail-none" />
												</asp:DropDownList>
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-font_type-tip">
												Este parámetro indica el tipo de fuente por defecto que se utilizará en los mails de respuesta
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trFontSize">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-font_size">Tamaño de Fuente</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxFontSize" runat="server" Width="40" MaxLength="2" TextMode="Number" max="18" min="8" />
												<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFontSize" />
												<asp:RangeValidator runat="server" ControlToValidate="textboxFontSize" Type="Integer" MaximumValue="18" MinimumValue="8" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-font_size-tip">
												Este parámetro indica el tamaño de fuente por defecto que se utilizará en los mails de respuesta (Valor mínimo: 8; Valor máximo: 18)
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-favorite_mails">Mails favoritos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxFavoriteMails" runat="server" Width="400" TextMode="MultiLine" Rows="3" spellcheck="false" />
												<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxFavoriteMails" ValidationExpression="^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(,[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-favorite_mails-tip">
												Este parámetro indica la lista de mails favoritos, separados por coma, para su utilización en el servicio de mails.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-default_queue">Cola por defecto</span>:</th>
							<td class="data">
								<asp:DropDownList ID="dropdownlistMailQueue" runat="server" DataTextField="Name" DataValueField="ID" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trMailDownloadOriginal">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-download_original">Permitir descargar original</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxMailDownloadOriginal" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-download_original-tip">
												Este parámetro indica si se permite que se descargue el contenido original del mensaje tal cual arriba a los servidores.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" id="trMailDownloadOriginalAgents">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-download_original_agents">Permitir descargar original a los agentes</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxMailDownloadOriginalAgents" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-download_original_agents-tip">
												Este parámetro indica si se permite que se descargue el contenido original del mensaje tal cual arriba a los servidores a los agentes.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-multiples_replies">Permitir generar múltiples respuestas</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:DropDownList ID="dropdownlistMailGenerateMultipleRepliesBehaviour" runat="server" ClientIDMode="Static">
													<asp:ListItem Value="0" Text="No permitir" Selected="True" data-i18n="configuration-servicesmail-multiples_replies_behaviour-0" />
													<asp:ListItem Value="1" Text="Permitir utilizando únicamente el servicio por el que entró el mail" data-i18n="configuration-servicesmail-multiples_replies_behaviour-1" />
													<asp:ListItem Value="2" Text="Permitir utilizando todos los servicios de mail disponibles" data-i18n="configuration-servicesmail-multiples_replies_behaviour-2" />
													<asp:ListItem Value="3" Text="Permitir utilizando únicamente los servicios de mail seleccionados" data-i18n="configuration-servicesmail-multiples_replies_behaviour-3" />
												</asp:DropDownList>
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-multiples_replies-tip">
												Este parámetro indica si se permite que el agente genere múltiples respuestas salientes a partir del mail entrante
											</td>
										</tr>
									</tbody>
								</table>
								<yoizen:Message ID="messageMailGenerateMultipleRepliesBehaviourNoOtherServices" runat="server" Type="Information" ClientIDMode="Static" style="display: none" LocalizationKey="configuration-servicesmail-no_other_service">
									No existen otros servicios de mail disponibles para utilizar
								</yoizen:Message>
								<asp:Panel ID="panelMailGenerateMultipleRepliesBehaviour" runat="server" CssClass="subseccion" style="display: none" ClientIDMode="Static">
									<div class="title">
										<h2 data-i18n="configuration-servicesmail-allowed_services-title">Servicios permitidos</h2>
									</div>
									<div class="contents">
										<asp:ListBox ID="listboxServicesForMultipleRepliesGeneration" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
									</div>
								</asp:Panel>
								<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServicesForMultipleRepliesGeneration" SkinID="validationerror" Text="Debe seleccionar al menos un servicio" /></div>
								<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateGenerateMultipleRepliesBehaviour" SkinID="validationerror" Text="El comportamiento seleccionado para generación de respuestas salientes es inválido dado que no existen otros servicios de mail" ControlToValidate="dropdownlistMailGenerateMultipleRepliesBehaviour" /></div>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-attached_files-title">Configuración de archivos adjuntos</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-send_attachments">Mandar archivos adjuntos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEmailAllowToSendAttachments" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-send_attachments-tip">
												Este parámetro indica si el agente puede enviar archivos adjuntos acompañando al mensaje
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-max_attachments">Máxima cantidad de adjuntos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxEmailMaxAttachments" runat="server" Width="80" TextMode="Number" max="99" min="1" Text="1" ClientIDMode="Static" />
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateEmailMaxAttachments" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-max_attachments-tip">
												Este parámetro indica la cantidad de archivos adjuntos que se permiten enviar en un mismo mail.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-max_size">Máximo tamaño para los adjuntos (en MB)</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:TextBox ID="textboxEmailMaxSizeAttachment" runat="server" Width="80" TextMode="Number" max="99" min="0" Text="1" />
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateEmailMaxSizeAttachment" />
											</td>
											<td class="vMid pls" data-i18n="configuration-servicesmail-max_size-tip">
												Este parámetro indica el tamaño máximo permitido en MB para el total de archivos adjuntos que se envían en el mail. Especifique cero para
												un valor ilimitado
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-files_types">Tipos de archivos adjuntos permitidos</span>:</th>
							<td class="data">
								<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateEmailMultimediaOptions" />
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeAllFiles" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeAllFiles" data-i18n="globals-file_types-all">Todos los archivos (*.*)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-image"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeImages" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeImages" data-i18n="globals-file_types-images">Archivos de imagen (JPG, JPEG, PNG)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-alt"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeText" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeText" data-i18n="globals-file_types-text">Archivos de texto</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-audio"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeAudio" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeAudio" data-i18n="globals-file_types-audio">Archivos de audio (MP3, WAV)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-pdf"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypePDF" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypePDF" data-i18n="globals-file_types-pdf">Archivos PDF</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-word"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeWord" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeWord" data-i18n="globals-file_types-doc">Archivos de Word (DOC, DOCX)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-excel"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeExcel" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeExcel" data-i18n="globals-file_types-excel">Archivos de Excel (XLS, XLSX)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-powerpoint"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypePPT" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypePPT" data-i18n="globals-file_types-ppt">Archivos de PowerPoint (PPT, PPTX)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow">
										<td colspan="2">
											<span class="fa fa-lg fa-file-archive"></span> 
											<asp:CheckBox ID="checkboxEmailAcceptedTypeZip" runat="server" />
											<asp:Label runat="server" AssociatedControlID="checkboxEmailAcceptedTypeZip" data-i18n="globals-file_types-zip">Archivos comprimidos (ZIP)</asp:Label>
										</td>
									</tr>
									<tr class="dataRow" id="trEmailZipOptions" style="display: none">
										<td class="data" colspan="2">
											<div class="subseccion">
												<div class="title">
													<h2 data-i18n="configuration-servicesmail-commpresed_files_options-title">Opciones de los archivos comprimidos</h2>
												</div>
												<div class="contents">
													<yoizen:Message runat="server" Small="true" Type="Information" LocalizationKey="configuration-servicesmail-commpresed_files_options-warning">
														Algunos servidores de correo no permiten archivos mayores a 25MB o que contengan archivos .exe
													</yoizen:Message>
													<table width="100%" border="0" class="uiInfoTable noBorder">
														<tr class="dataRow">
															<th class="label"><span data-i18n="configuration-servicesmail-max_zip_size">Máximo tamaño una vez descomprimido (en MB)</span>:</th>
															<td class="data">
																<asp:TextBox ID="textboxEmailZipOptionsMaxSize" runat="server" MaxLength="2" Width="30" TextMode="Number" />
																<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateEmailZipOptions" />
															</td>
														</tr>
														<tr class="dataRow">
															<th class="label" style="white-space: nowrap"><span data-i18n="configuration-servicesmail-include_exe">Incluir archivos .exe</span></th>
															<td class="data">
																<asp:CheckBox ID="checkboxEmailZipOptionsIncludeExe" runat="server" />
															</td>
														</tr>
													</table>
												</div>
											</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr class="dataRow" rel="AllowToSendAttachments">
							<th class="label withdescription"><span data-i18n="configuration-servicesmail-default_extension">Extensión por defecto</span>:</th>
							<td class="data">
								<asp:HiddenField ID="hiddenEmailDefaultExtension" runat="server" ClientIDMode="Static"></asp:HiddenField>
								<select id="dropdownlistEmailDefaultExtension"></select>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<asp:HiddenField ID="hiddenSurveys" runat="server" ClientIDMode="Static"></asp:HiddenField>
				<div class="title">
					<h2 data-i18n="configuration-services-common-yflow-surveys-title">Encuestas</h2>
				</div>
				<div class="contents">
					<yoizen:Message ID="messageNoSurveys" runat="server" Type="Warning" Visible="false" LocalizationKey="configuration-services-common-yflow-surveys-no_surveys">
						No existen encuestas creadas y habilitadas
					</yoizen:Message>
					<asp:Panel ID="panelEnableSurveys" runat="server">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-queues-enable_surveys">Habilitar Envío de Encuestas</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxEnableSurveys" ClientIDMode="Static" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-queues-enable_surveys-tip">
													Habilita el envío automático de encuestas al finalizar un caso
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
						<yoizen:Message ID="messageNoSurveysInTable" runat="server" Type="Warning" style="display: none; margin-top: 10px" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-no_surveys_in_service">
							No existen encuestas en el servicio.
						</yoizen:Message>
						<div id="divWithSurveys" style="display: none">
							<table class="reporte" cellspacing="0" rules="all" border="1" id="tableSurveys" style="width: 100%; border-collapse: collapse">
								<thead>
									<tr class="header">
										<th scope="col"><span data-i18n="globals-name">Nombre</span></th>
										<th scope="col">&nbsp;</th>
									</tr>
								</thead>
								<tbody id="bodySurveys"></tbody>
							</table>
							<div style="display: none">
								<div id="divSurvey">
									<div class="scrollable-y" style="max-height: 550px">
										<div id="divSurveyConfiguration" class="subseccion" style="margin-top: 20px">
											<div class="title">
												<h2 data-i18n="configuration-services-common-yflow-surveys-configuration-title">Configuración de encuestas</h2>
											</div>
											<div class="contents">
												<yoizen:Message ID="messageSurveyDisabled" runat="server" Type="Warning" style="display: none;" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-survey_disabled">
													El servicio tenía configurado una encuesta que está deshabilitada. Por favor seleccione otra
												</yoizen:Message>
												<table width="100%" border="0" class="uiInfoTable noBorder">
													<tr class="dataRow dataRowSeparator">
																				<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-survey">Encuesta a enviar</span>:</th>
														<td class="data">
															<asp:DropDownList ID="dropdownSurvey" runat="server" DataValueField="ID" DataTextField="Name" AppendDataBoundItems="true" ClientIDMode="Static">
																<asp:ListItem Value="-1" Text="Seleccione una encuesta" Selected="True" data-i18n="configuration-services-common-yflow-surveys-configuration-survey-select_one" />
															</asp:DropDownList>
																					<span id="spanSurvey"></span>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation">Invitación para Participar</span>:</th>
														<td class="data">
															<asp:TextBox ID="textboxSurveyInvitation" ClientIDMode="Static" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="3" />
															<yoizen:Message ID="messageSurveyInvitationFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																<span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields">Debe utilizar el campo @@LINK@@ dentro del texto y puede optar por utilizar los otros mencionados a continuación</span>:<br />
																<ul>
																	<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-link">Indica donde irá el link de la encuesta</span></li>
																	<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																	<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																</ul>
															</yoizen:Message>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator" style="display: none">
														<th class="label"><span data-i18n="configuration-queues-survey_invitation_whatsapp">Invitación para canales de WhatsApp</span>:</th>
														<td class="data">
															<asp:TextBox ID="textboxSurveyInvitationInteractive" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
															<yoizen:Message ID="messageFilterEmailSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																<span data-i18n="configuration-queues-survey_invitation_fields_whatsapp">Debe utilizar el siguiente campo dentro del texto de la invitación</span>:<br />
																<ul>
																	<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																	<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																</ul>
															</yoizen:Message>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator" style="display: none">
														<th class="label"><span data-i18n="configuration-queues-survey_button">Texto del boton para canales de WhatsApp</span>:</th>
														<td class="data">
															<asp:TextBox ID="textboxSurveyInvitationButton" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
															<yoizen:Message ID="message2" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																<span data-i18n="configuration-queues-survey_invitation_fields_button">Debe utilizar el siguiente campo dentro del texto de la invitación</span>
															</yoizen:Message>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator" id="queueSurveyExpiration">
														<th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration">Tiempo de expiración de la invitación (minutos)</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSurveyExpiration" ClientIDMode="Static" runat="server" MaxLength="5" Width="150" />
																			<div id="timeInfoSurveyExpiration" class="timeinfo" related="textboxSurveyExpiration"></div>
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration-tip">
																			Define la cantidad de minutos que deben pasar a partir de la fecha de envío de la encuesta para considerar la invitación como expirada.
																			Ingrese cero para indicar que la invitación nunca expira
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
												</table>
											</div>
										</div>
										<div id="divSurveyBehaviour" class="subseccion">
											<div class="title">
												<h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-title">Condiciones para el envío</h2>
											</div>
											<div class="contents">
												<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate">Tasa de envío (%)</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs">
																			<asp:TextBox ID="textboxSurveySentRate" ClientIDMode="Static" runat="server" Width="50px" MaxLength="3" style="text-align: right" />
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate-tip">
																			Mide el porcentaje de casos sobre los que se va a generar el envío de la encuesta.
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send">Tiempo de envío (minutos)</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSurveyTimeToSend" ClientIDMode="Static" runat="server" Width="50px" MaxLength="5" style="text-align: right" />
																			<div id="timeInfoSurveyTimeToSend" class="timeinfo" related="textboxSurveyTimeToSend"></div>
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send-tip">
																			Define la cantidad de minutos que deben pasar a partir del cierre del caso para que se envíe la encuesta al usuario.
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
                                                    <tr class="dataRow dataRowSeparator">
                                                        <th class="label">
                                                            <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
																	"toggle": "popover",
																	"html": true,
																	"maxWidth": "400px",
																	"trigger": "hover",
																	"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-title",
																	"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-content"
																}'
                                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas"
                                                                data-content="Opcionalmente se pueden asociar etiquetas a la respuesta predefinida, para que luego los agentes puedan encontrar esta respuesta predefinida cuando el caso del mensaje contenga las etiquetas seleccionadas"></span>
                                                            <span data-i18n="configuration-predefinedanswersbyqueue-related_tags">Etiquetas relacionadas</span>:
                                                        </th>
                                                        <td class="data">
                                                            <asp:TextBox ID="textboxSurveyTags" runat="server" ClientIDMode="Static" Width="100%" />
                                                        </td>
                                                    </tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count">Cantidad de mensajes en la conversación</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs">
																			<asp:TextBox ID="textboxSurveyMessagesCount" ClientIDMode="Static" runat="server" MaxLength="4" Width="50px" style="text-align: right" />
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count-tip">
																			Establece una mínima cantidad de mensajes dentro de la conversación del caso.
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration">Duración mínima del caso</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSurveyCaseDuration" ClientIDMode="Static" runat="server" Width="50px" MaxLength="4" style="text-align: right" />
																			<div id="timeInfoSurveyCaseDuration" class="timeinfo" related="textboxSurveyCaseDuration"></div>
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration-tip">
																			Establece una mínima duración en minutos del caso.
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
												</table>
											</div>
										</div>
										<div id="divSurveyIgnoreConditions" class="subseccion">
											<div class="title">
												<h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont-title">Condiciones para no realizar el envío</h2>
											</div>
											<div class="contents">
												<div class="subseccion">
													<div class="contents">
														<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-services-common-yflow-surveys-configuration-send_conditions-only_one_active">
														<b>"Encuestar si ya existe un caso nuevo"</b> y <b>"Encuestar si el nuevo caso está en yFlow"</b> está diseñado para que puedan activarse únicamente de forma individual. Estas opciones no pueden convivir de manera simultánea en el sistema.
														</yoizen:Message>
														<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
															<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case">Encuestar si ya existe un caso nuevo</span>:</th>
																	<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:CheckBox ID="checkboxSurveySendIfNewCaseExists" runat="server" ClientIDMode="Static"/>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case-tip">
																				Establece si un caso será encuestado dependiendo de si ya existe un caso nuevo del mismo perfil
																			</td>
																		</tr>
																	</tbody>
																	</table>
																	</td>
																</tr>
																<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_has_tag">Encuestar si el caso anterior posee determinada etiqueta</span>:</th>
																	<td class="data">
																	<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:CheckBox ID="checkboxSurveySendIfNewCaseHasTag" runat="server" ClientIDMode="Static"/>
																			</td>
																			<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_has_tag-tip">
																				Si se habilita esta opción, se evaluará el envío de encuesta si el caso anterior posee determinada etiqueta.
																			</td>
																		</tr>
																	</tbody>
																	</table>
																	</td>
																</tr>
																<tr class="dataRow dataRowSeparator">
																<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case_closed_by_yflow">Encuestar si el nuevo caso fue cerrado en yFlow</span>:</th>
																<td class="data">
																<table class="uiGrid" cellspacing="0" cellpadding="0">
																	<tbody>
																		<tr>
																			<td class="vMid prs timeinfo">
																				<asp:CheckBox ID="checkboxSurveySendIfNewCaseClosedByYflow" runat="server" ClientIDMode="Static" />
																			</td>
																			<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case_closed_by_yflow-tip">Si se habilita esta opción, un caso nuevo cerrado por yFlow no anula el envío de encuestas del caso anterior.
																			</td>
																		</tr>
																		</tbody>
																	</table>
																</td>
															</tr>
													</table>
												</div>
												</div>
												<table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey">No encuestar si la última envíada fue dentro de los últimos (minutos)</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSurveyDontSendIfLastSurveyAfterMinutes" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
																			<div id="timeInfoDontSendIfLastSurveyAfterMinutes" class="timeinfo" related="textboxSurveyDontSendIfLastSurveyAfterMinutes"></div>
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey-tip">
																			Especifica la cantidad de minutos a considerar hacia atrás para evaluar si no se envió ninguna encuesta al usuario del caso. En caso de que la última fecha de envío
																			de encuesta sea posterior a la fecha actual restando los minutos configurados, no se enviará la encuesta para dicho caso.
																			En caso de configurar cero, no se aplicará esta condición. El máximo valor son 172800 (4 meses)
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly">No encuestar si ya se enviaron encuestas en el mes</span>:</th>
														<td class="data">
															<table class="uiGrid" cellspacing="0" cellpadding="0">
																<tbody>
																	<tr>
																		<td class="vMid prs timeinfo">
																			<asp:TextBox ID="textboxSurveyDontSendTotalSendMonthly" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
																		</td>
																		<td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly-tip">
																			Especifica la cantidad de encuestas que pueden enviarse en el més para cada cliente.
																			En caso de configurar cero, no se aplicará esta condición. El máximo valor son 30 encuestas al mes.
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
													<tr class="dataRow dataRowSeparator">
														<th class="label withdescription" style="width: 250px !important;">
														<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas que no debe incluir" data-i18n-popover='{
																		"toggle": "popover",
																		"html": true,
																		"maxWidth": "400px",
																		"trigger": "hover",
																		"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags",
																		"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags-tooltip"
																		}' data-content="Seleccione las etiquetas que deberán tener los casos cerrados para no ser considerados para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
														<span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags">Etiquetas que no debe incluir</span>:
														</th>
														<td class="data">
															<asp:TextBox ID="textboxSurveysIgnoreTags" runat="server" ClientIDMode="Static" Width="40%" />
														</td>
													</tr>
												</table>
												</div>
												</div>
									</div>
									<div id="divSurveyError" class="validationerror" style="display: none"><span></span></div>
									<div class="buttons">
										<label class="uiButton uiButtonLarge uiButtonConfirm">
											<button type="button" data-i18n="globals-accept" onclick="ValidateSurvey()">Aceptar</button>
										</label>
										<label class="uiButton uiButtonLarge">
											<button type="button" data-i18n="globals-cancel" onclick="ReturnTableSurvey()">Cancelar</button>
										</label>
									</div>
								</div>
							</div>
						</div>
						<yoizen:Message ID="informativeMessage" runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="configuration-queues-enable_surveys-information">
							Está configuración solo se aplica para los casos que no tengan mensajes con colas.
						</yoizen:Message>
						<div class="validationerror" style="display: none">
							<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSurveys" data-i18n="configuration-serviceswhatsapp-configuration-surveys-error" />
						</div>
						<div id="divNewSurvey" class="buttons">
							<label class="uiButton uiButtonLarge uiButtonConfirm">
								<button type="button" onclick="AddNewSurvey()" data-i18n="configuration-surveys-new_survey">Nueva Encuesta</button>
							</label>
						</div>
					</asp:Panel>
				</div>
			</div>

		</div>
		<div id="divNotifications">
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-authentication_problems-title">Problemas de autenticación</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-servicesmail-authentication_problems-tip">
						Este mail se mandará una vez cada 30 minutos para avisar que ocurrió un error cuando se intentaba autenticar contra el servidor de correo.
					</yoizen:Message>
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
							<td class="data">
								<asp:HiddenField ID="hiddenAuthenticationErrorConnection" runat="server" ClientIDMode="Static" />
								<select id="listboxAuthenticationErrorConnection">
									<option value="" selected="selected" data-i18n="globals-email_default">Ninguna</option>
								</select>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-mail_subject">Asunto del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailAuthenticationErrorEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailAuthenticationErrorEmailSubject" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-recipients">Emails destinatarios</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailAuthenticationErrorEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailAuthenticationErrorEmails" />
								<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMailAuthenticationErrorEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-template">Plantilla del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailAuthenticationErrorEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailAuthenticationErrorEmailTemplate" />
								<yoizen:Message ID="messageMailAuthenticationErrorEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-servicesmail-auth_error_mail_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-auth_error_mail-field-date">Indica la fecha y hora cuando ocurrió el primer error</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-auth_error_mail-field-service_name">Indica el nombre del servicio que tiene el error</span></li>
										<li><span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-auth_error_mail-field-error_message">Indica el mensaje de error devuelto por el servidor de correo</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="seccion collapsable">
				<div class="title">
					<h2 data-i18n="configuration-servicesmail-inactive_time-title">Minutos con inactividad</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-servicesmail-inactive_time-tip">
						Este mail se mandará una vez cuando se detecte que luego de pasado cierta cantidad de minutos, no se hayan recibido mails nuevos.
					</yoizen:Message>
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-minutes">Minutos</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailMinutesForInactivity" runat="server" MaxLength="4" Width="80" TextMode="Number" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailMinutesForInactivity" />
								<asp:CompareValidator runat="server" ControlToValidate="textboxMailMinutesForInactivity" Type="Integer" Operator="GreaterThan" ValueToCompare="0" />
								<div class="timeinfo" related="textboxMailMinutesForInactivity"></div>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
							<td class="data">
								<asp:HiddenField ID="hiddenMailInactivityDetectedConnection" runat="server" ClientIDMode="Static" />
								<select id="listboxMailInactivityDetectedConnection">
									<option value="" selected="selected" data-i18n="globals-email_default">Ninguna</option>
								</select>
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-mail_subject">Asunto del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailInactivityDetectedEmailSubject" runat="server" MaxLength="200" Width="90%" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailInactivityDetectedEmailSubject" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-recipients">Emails destinatarios</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailInactivityDetectedEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailInactivityDetectedEmails" />
								<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMailInactivityDetectedEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
							</td>
						</tr>
						<tr class="dataRow dataRowSeparator">
							<th class="label"><span data-i18n="configuration-servicesmail-template">Plantilla del Email</span>:</th>
							<td class="data">
								<asp:TextBox ID="textboxMailInactivityDetectedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
								<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMailInactivityDetectedEmailTemplate" />
								<yoizen:Message ID="messageMailInactivityDetectedEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-servicesmail-inactivity_detected_mail_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-inactivity_detected_mail-field-date">Indica la fecha y hora cuando ocurrió el primer error</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-inactivity_detected_mail-field-service_name">Indica el nombre del servicio que tiene el error</span></li>
										<li><span class='templatefieldname'>@@MINUTOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesmail-edit-inactivity_detected_mail-field-minutes">Indica la cantidad de minutos que pasaron sin actividad en Mail</span></li>
									</ul>
								</yoizen:Message>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div id="divCases">
			<div class="seccion">
				<div class="title">
					<h2 data-i18n="configuration-systemsettings-case_configuration-title">Configuraciones de casos</h2>
				</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator hiddenAsGateway">
							<th class="label withdescription" style="width: 200px !important"><span data-i18n="configuration-services-common-cases_override">Sobreescribir configuración de casos</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxCasesOverrideSystemSettings" runat="server" ClientIDMode="Static" />
											</td>
											<td class="vMid pls" data-i18n="configuration-services-common-cases_override-tip">
												Este parámetro indica si el servicio puede sobreescribir la configuración de Párámetros del sistema con respecto a los casos 
												y así poder definir dintintos valores para el tratamiento de casos.
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</table>
					<div class="subseccion" id="divCasesOverride">
						<div class="title">
							<h2 data-i18n="configuration-services-common-cases_override-title">Configuraciones específicas de casos</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator hiddenAsGateway">
									<th class="label withdescription" style="width: 200px !important"><span data-i18n="configuration-systemsettings-verify_last_queue">Verificar última cola</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxCheckLastQueueOfOpenCase" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-verify_last_queue-tip">
														Este parámetro indica si el sistema verificará ante cada mensaje nuevo si ese usuario tiene
														un caso abierto y su último mensaje ingresó en otra cola (que no corresponde a la cola por defecto asignada al servicio). 
														De ser así se tomará como cola por defecto la cola del último mensaje
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator hiddenAsGateway">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-ignore_last_queue">Ignorar última cola si el último mensaje entrante fue movido por SL</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxIgnoreLastQueueForSLMovedMessage" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-ignore_last_queue-tip">
														Este parámetro indica si el sistema ignorará la última cola del caso en caso de que el último mensaje
														que haya sido escrito por el usuario fue movido por alguna acción del Service Level/Vencimiento.
														En caso de ignorar, se utilizará la cola por defecto del servicio por el cual ingresó el mensaje
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">
										<span data-i18n="configuration-systemsettings-time_to_close">Minutos para cierre automático</span>: <br />
									</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxMaxElapsedMinutesToCloseCases" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" />
														<div class="timeinfo" related="textboxMaxElapsedMinutesToCloseCases"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-tip">
														Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario y el sistema. Pasado
														esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
													</td>
												</tr>
										    
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">
										<span data-i18n="configuration-systemsettings-auto_reply_close_case">Responder en cierre automático</span>:
									</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:CheckBox ID="checkboxReplyInCloseCase" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-auto_reply_close_case-tip">
														Este parámetro indica si se evniará una respuesta al ultimo mensaje del caso antes de cerrarlo de forma automatica.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>						
								<tr class="dataRow dataRowSeparator" id="trAutoReplyInCloseCaseText">
									<th class="label">
										<span data-i18n="configuration-systemsettings-auto_reply_close_case">Respuesta en cierre automático</span>:
									</th>
									<td class="data">
										<asp:TextBox ID="textboxAutoReplyInCloseCaseText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">
										<span data-i18n="configuration-systemsettings-apply_tag_close_case">Aplicar etiqueta en cierre automático</span>:
									</th>
									<td class="data">
										<asp:TextBox ID="textboxTagCloseCase" runat="server" ClientIDMode="Static" Width="100%" />
									</td>
								</tr>
								<asp:PlaceHolder ID="placeholderYFlowCasesRelated" runat="server">
									<tr class="dataRow dataRowSeparator" id="trMaxElapsedMinutesToCloseYFlowCases">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case">Minutos para cierre automático de un caso atendido únicamente por yFlow </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs timeinfo">
															<asp:TextBox ID="textboxMaxElapsedMinutesToCloseYFlowCases" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" Text="0" />
															<div class="timeinfo" related="textboxMaxElapsedMinutesToCloseYFlowCases"></div>
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case-tip">
															Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario e yFlow. Pasado
															esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
															Un caso únicamete de yFlow es cuando nunca fue derivado a un agente
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trInvokeYFlowWhenClosedCases">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case">Invocar a yFlow al momento de cerrar el caso </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxInvokeYFlowWhenClosedCases" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case-tip">
															Este parámetro indica si se deberá invocar a yFlow cuando el cierre automático de un caso atendido únicamente por yFlow se ejecute.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</asp:PlaceHolder>
							</table>
							<div class="validationerror" style="display: none">
								<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateCasesSettings" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="buttons">
        <asp:HiddenField ID="hiddenServiceToCopy" runat="server" ClientIDMode="Static" />
		<label class="uiButton uiButtonLarge">
            <button type="button" data-i18n="configuration-services-copy_from_service" onclick="ShowCopyServiceCommon('Mail')">Copiar desde otro servicio</button>
        </label>
		<label class="uiButton uiButtonLarge uiButtonConfirm">
			<asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" data-i18n="globals-accept" />
		</label>
		<label class="uiButton uiButtonLarge">
			<asp:Button ID="buttonCancel" runat="server" Text="Cancelar" OnClick="buttonCancel_Click" CausesValidation="false" data-i18n="globals-cancel" OnClientClick="DisableFormValidation()" />
		</label>
        <asp:Button ID="buttonCopyService" runat="server" Text="Aceptar" OnClick="buttonCopyService_Click" CausesValidation="false" Style="display: none" ClientIDMode="Static" />
	</div>
</asp:Content>
