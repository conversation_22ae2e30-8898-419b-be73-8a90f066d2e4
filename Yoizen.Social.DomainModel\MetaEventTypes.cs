﻿namespace Yoizen.Social.DomainModel
{
	public enum MetaEventTypes
	{
		/// <summary>
		/// Cuando se realiza una compra o se completa el proceso de pago.
		/// </summary>
		Purchase,

		/// <summary>
		/// Cuando se completa un registro.
		/// </summary>
		LeadSubmitted,

		/// <summary>
		/// Cuando una persona ingresa al proceso de pago antes de completarlo.
		/// </summary>
		InitiateCheckout,

		/// <summary>
		/// Cuando se agrega un producto al carrito de compras.
		/// </summary>
		AddToCart,

		/// <summary>
		/// Visitar una página de contenido que te interesa, como una página de producto o de destino. 
		/// ViewContent indica si alguien visita la URL de una página web, pero no lo que mira o hace en esa página.
		/// </summary>
		ViewContent,

		/// <summary>
		/// Se ha creado un nuevo pedido.
		/// </summary>
		OrderCreated,

		/// <summary>
		/// Un pedido ha sido enviado.
		/// </summary>
		OrderShipped,

		/// <summary>
		/// Un pedido ha sido entregado.
		/// </summary>
		OrderDelivered,

		/// <summary>
		/// Un pedido ha sido cancelado.
		/// </summary>
		OrderCanceled,

		/// <summary>
		/// Un pedido ha sido devuelto por el cliente.
		/// </summary>
		OrderReturned,

		/// <summary>
		/// Un usuario abandonó el carrito de compras antes de completar la compra.
		/// </summary>
		CartAbandoned,

		/// <summary>
		/// Un cliente potencial ha sido calificado como de alta calidad.
		/// </summary>
		QualifiedLead,

		/// <summary>
		/// Un usuario ha proporcionado una calificación (ej. estrellas).
		/// </summary>
		RatingProvided,

		/// <summary>
		/// Un usuario ha enviado una reseña o comentario.
		/// </summary>
		ReviewProvided
	}
}
