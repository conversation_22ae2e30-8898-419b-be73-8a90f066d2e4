﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />

var users;
var datatable = null;

var $hiddenActionUserID;
var $hiddenActionName;
var $buttonAction;
var $divLoadingUsers;
var $tableUsers;
var $panelButtons;
var $panelList;
var $divFilters;
var $textboxUser;
var $selectEnabled;
var $selectQueues;
var $selectProfilesFilter;
var $checkboxFilterShowID;
var $divDeleteUser;
var $divCanBeDeletedLoading;
var $messageUserCannotBeDeleted;
var $messageUserCanBeDeleted;
var $messageUserCannotBeDeletedAndIsDisabled;
var $messageUserCanBeDeletedAndIsDisabled;
var $messageCouldntCheckUserCanBeDeleted;
var $buttonDeleteUserConfirm;
var $buttonDisableUserConfirm;
var $divSearchDomainUsers;
var $textboxDomainUserFilter;
var $buttonSearchDomainUsersByFilter;
var $buttonSearchDomainUsersConfirm;
var $divSearchDomainUsersLoading;
var $messageSearchDomainUsersNoResults;
var $divSearchDomainUsersResult;
var $tableSearchDomainUsersResults;
var $tableData;
var $dropdownlistUserType;
var $hiddenUserAvatarFile;
var $hiddenUserAvatarFileContentType;
var $fileUserAvatarFile;
var $divUserAvatarDropZone;
var $divUserAvatarFileInfo;
var $divLoading;

var $checkboxCopyProfile;
var $checkboxCopyAllowed;
var $checkboxCopyAsociatedQueues;
var $checkboxCopyAgentsGroups;
var $checkboxCopySettings;
var $copyValidationError;
var $buttonCopyUserConfirm;

var $textboxPassword;
var $meterPasswordStrength;
var $spanPasswordStrength;
var $textboxFirstName;
var $textboxLastName;
var $textboxUsername;
var $textboxEmail;
var $textboxLDAP;
var $textboxChangePassword;
var $meterChangePasswordStrength;
var $spanChangePasswordStrength;
var $gridviewProfiles;
var $gridviewPermissions;
var $hiddenPermissions;

var $divExport;
var $divExportStep1;
var $selectExportFormat;

var $selectUserQueues;
var $selectUserServicesConfig;
var $divEditServicesSpecific;
var $selectUserServices;

var strength;

$.fn.dataTable.ext.search.push(
    function (settings, data, dataIndex) {
    	var user = users[dataIndex];

    	var enabledOption = parseInt($selectEnabled.val(), 10);
    	var agentFilter = '';
    	if ($textboxUser.length > 0) {
    		agentFilter = $textboxUser.val().toLowerCase();
    	}
    	var queue = parseInt($selectQueues.val(), 10);
    	var profiles = $selectProfilesFilter.val();
    	
    	if (enabledOption != -1) {
    		if (user.Enabled != enabledOption)
    			return false;
    	}

    	if (agentFilter.length > 0) {
    		agentFilter = agentFilter.toLowerCase();
    		var nameIndex = user.FullName.toLowerCase().indexOf(agentFilter);
    		var usernameIndex = user.UserName.toLowerCase().indexOf(agentFilter);
    		if (nameIndex == -1 && usernameIndex == -1) {
    			return false;
    		}
    	}

    	if (queue !== -1) {
    		if (user.Queues === null ||
				user.Queues.length === 0 ||
				user.Queues.indexOf(queue) === -1) {
    			return false;
    		}
    	}

    	if (profiles !== null && profiles.length > 0) {
    		var userProfiles = user.Profiles;
    		if (userProfiles.length > 0) {
    			for (var i = 0; i < profiles.length; i++) {
    				if (userProfiles.indexOf(parseInt(profiles[i], 10)) === -1) {
    					return false;
    				}
    			}
    		}
    		else {
    			return false;
    		}
    	}

    	return true;
    }
);

var oldWebFormOnSubmit;

jQuery(document).ready(function () {
	$panelList = $('div[id$=panelList]');
	if ($panelList.length > 0) {
		$divFilters = $('#divFilters');
		$hiddenActionUserID = $('#hiddenActionUserID');
		$hiddenActionName = $('#hiddenActionName');
		$buttonAction = $('#buttonAction');
		$divLoadingUsers = $('#divLoadingUsers');
		$tableUsers = $('#tableUsers');
		$panelButtons = $('#panelButtons');
		RetrieveUsers();

		$textboxUser = $('#textboxUser', $divFilters);
		$selectEnabled = $('#selectEnabled', $divFilters);
		$selectQueues = $('#selectQueues', $divFilters);
		$selectUserQueues = $('#selectUserQueues');
		$selectUserServices = $('#selectUserServices');
		$selectUserServicesConfig = $('#selectUserServicesConfig');
		$divEditServicesSpecific = $('#divEditServicesSpecific');
		$selectProfilesFilter = $('#selectProfilesFilter');
		$checkboxFilterShowID = $('#checkboxFilterShowID');

		$checkboxCopyProfile = $('input[type=checkbox][id$=checkboxCopyProfile]');
		$checkboxCopyAllowed = $('input[type=checkbox][id$=checkboxCopyAllowed]');
		$checkboxCopyAsociatedQueues = $('input[type=checkbox][id$=checkboxCopyAsociatedQueues]');
		$checkboxCopyAgentsGroups = $('input[type=checkbox][id$=checkboxCopyAgentsGroups]');
		$checkboxCopySettings = $('input[type=checkbox][id$=checkboxCopySettings]');

		$divCopy = $('#divCopy', $panelList);
		$buttonCopyUserConfirm = $('#buttonCopyUserConfirm', $divCopy);
		$copyValidationError = $('[id$=CopyValidationError]');
		$MessageErrorCheckbox = $('#MessageErrorCheckbox', $divCopy);

		for (var i = 0; i < queues.length; i++) {
			var $option = $('<option></option>');
			$option.val(queues[i].ID);
			$option.text(queues[i].Name);
			$selectQueues.append($option);

			$option = $('<option></option>');
			$option.val(queues[i].ID);
			$option.text(queues[i].Name);
			$selectUserQueues.append($option);
		}

		for (var i = 0; i < services.length; i++) {
			$option = $('<option></option>');
			$option.val(services[i].ID);
			$option.text(services[i].Name);
			$selectUserServices.append($option);
		}

		for (var i = 0; i < profiles.length; i++) {
			var $option = $('<option></option>');
			$option.val(profiles[i].ID);
			$option.text(profiles[i].Name);
			$selectProfilesFilter.append($option);
		}

		$selectUserServicesConfig.change(function () {
			let option = parseInt($selectUserServicesConfig.val(), 10);
			$divEditServicesSpecific.toggle(option === 1);

			$.colorbox.resize();
		}).trigger('change');

		$selectEnabled.multiselect({ multiple: false, selectedList: 1 });
		$selectQueues.multiselect({ multiple: false, selectedList: 1 }).multiselectfilter();
		$selectUserQueues.multiselect({ multiple: true, noneSelectedText: 'Indistinto', selectedList: 4, menuWidth: '>500', buttonWidth: '>500' }).multiselectfilter();
		$selectUserServicesConfig.multiselect({ multiple: false, selectedList: 1 });
		$selectUserServices.multiselect({ multiple: true, noneSelectedText: 'Seleccione los servicios', selectedList: 4, menuWidth: '>500', buttonWidth: '>500' }).multiselectfilter();
		$selectProfilesFilter.multiselect({ multiple: true, noneSelectedText: 'Indistinto', selectedList: 4 }).multiselectfilter();

		$selectEnabled.change(FilterChanged);
		$selectQueues.change(FilterChanged);
		$selectProfilesFilter.change(FilterChanged);
		$textboxUser.keyup(FilterChanged).trigger('keyup');

		$divDeleteUser = $('#divDeleteUser', $panelList);
		$divCanBeDeletedLoading = $('#divCanBeDeletedLoading', $divDeleteUser);
		$messageUserCannotBeDeleted = $('#messageUserCannotBeDeleted', $divDeleteUser);
		$messageUserCanBeDeleted = $('#messageUserCanBeDeleted', $divDeleteUser);
		$messageUserCannotBeDeletedAndIsDisabled = $('#messageUserCannotBeDeletedAndIsDisabled', $divDeleteUser);
		$messageUserCanBeDeletedAndIsDisabled = $('#messageUserCanBeDeletedAndIsDisabled', $divDeleteUser);
		$messageCouldntCheckUserCanBeDeleted = $('#messageCouldntCheckUserCanBeDeleted', $divDeleteUser);
		$buttonDeleteUserConfirm = $('#buttonDeleteUserConfirm', $divDeleteUser);
		$buttonDisableUserConfirm = $('#buttonDisableUserConfirm', $divDeleteUser);

		var $gridview = $('table[id$=gridview]', $panelList);
		var $tr = $('tr[userid=1]', $gridview);
		var $td1 = $('td:nth-child(1)', $tr);
		var $td2 = $('td:nth-child(2)', $tr);
		var $td3 = $('td:nth-child(3)', $tr);

		$td1.text('ADMINISTRADOR');
		$td1.attr('colspan', 3);
		$td1.css('text-align', 'center');
		$td2.remove();
		$td3.remove();

		$checkboxFilterShowID.change(function () {
			datatable.column(0).visible($checkboxFilterShowID.is(':checked'));
		});
	}
	else {
		$textboxPassword = $('#textboxPassword');
		$meterPasswordStrength = $('#meterPasswordStrength');
		$spanPasswordStrength = $('#spanPasswordStrength');
		$textboxFirstName = $('input[type=text][id$=textboxFirstName]');
		$textboxLastName = $('input[type=text][id$=textboxLastName]');
		$textboxUsername = $('input[type=text][id$=textboxUsername]');
		$textboxEmail = $('input[type=text][id$=textboxEmail]');
		$textboxLDAP = $('#textboxLDAP');
		$textboxChangePassword = $('#textboxChangePassword');
		$meterChangePasswordStrength = $('#meterChangePasswordStrength');
		$spanChangePasswordStrength = $('#spanChangePasswordStrength');
		$divSearchDomainUsers = $('#divSearchDomainUsers');
		$buttonSearchDomainUsersConfirm = $('#buttonSearchDomainUsersConfirm', $divSearchDomainUsers);
		$buttonSearchDomainUsersByFilter = $('#buttonSearchDomainUsersByFilter', $divSearchDomainUsers);
		$textboxDomainUserFilter = $('#textboxDomainUserFilter', $divSearchDomainUsers);
		$divSearchDomainUsersLoading = $('#divSearchDomainUsersLoading', $divSearchDomainUsers);
		$messageSearchDomainUsersNoResults = $('div[id$=messageSearchDomainUsersNoResults]', $divSearchDomainUsers);
		$divSearchDomainUsersResult = $('#divSearchDomainUsersResult', $divSearchDomainUsers);
		$tableSearchDomainUsersResults = $('#tableSearchDomainUsersResults', $divSearchDomainUsers);
		$tableData = $('#tableData');
		$dropdownlistUserType = $('select[id$=dropdownlistUserType', $tableData);
		var $trLocalUser = $('tr[rel=localuser]', $tableData);
		var $trDomainUser = $('tr[rel=domainuser]', $tableData);
		var $buttonSearchDomainUsers = $('#buttonSearchDomainUsers', $tableData);
		var $inputs = $('input[type=text]', $tableData);

		strength = {
			0: "Inválida",
			1: "Mala",
			2: "Débil",
			3: "Buena",
			4: "Fuerte"
		};

		$('#textboxChangePasswordConf').bind("cut copy paste",function(e) {
			e.preventDefault();
		});

		$textboxPassword.on('input', function () {
			var val = $textboxPassword.val();
			var result = CalculatePasswordStrength(val, [$textboxFirstName, $textboxLastName, $textboxUsername, $textboxEmail]);

			// Update the password strength meter
			$meterPasswordStrength[0].value = result.score;

			// Update the text indicator
			if (val !== "") {
				$spanPasswordStrength.text($.i18n('globals-password_strength-security', strength[result.score]));
			}
			else {
				$spanPasswordStrength.text("");
			}
		});

		$textboxChangePassword.on('input', function () {
			var val = $textboxChangePassword.val();
			var result = CalculatePasswordStrength(val, [$('#spanChangePasswordFirstName'), $('#spanChangePasswordLastName'), $('#spanChangePasswordUsername')]);

			// Update the password strength meter
			$meterChangePasswordStrength[0].value = result.score;

			// Update the text indicator
			if (val !== "") {
				$spanChangePasswordStrength.text($.i18n('globals-password_strength-security', strength[result.score]));
			}
			else {
				$spanChangePasswordStrength.text("");
			}
		});

		$dropdownlistUserType.change(function () {
			if ($dropdownlistUserType.length > 0) {
				var value = parseInt($dropdownlistUserType.val());
				if (value == AuthenticationTypes.Domain) {
					$trLocalUser.hide();
					$trDomainUser.show();
					$buttonSearchDomainUsers.parent().show();
					$inputs.attr('readonly', 'readonly');
				}
				else if (value == AuthenticationTypes.Google) {
					$trLocalUser.hide();
					$trDomainUser.hide();
					$inputs.removeAttr('readonly');
				}
				else if (value == AuthenticationTypes.Saml) {
					$trLocalUser.hide();
				}
				else {
					$trLocalUser.show();
					$trDomainUser.hide();
					$buttonSearchDomainUsers.parent().hide();
					$inputs.removeAttr('readonly');
				}
			}
		}).trigger('change');

		if (typeof (allowCreateLocalUsers) !== 'undefined' && !allowCreateLocalUsers) {
			$trLocalUser.hide();
			if (typeof (authenticationType) === 'undefined' || authenticationType === AuthenticationTypes.Domain) {
				$trDomainUser.show();
				$buttonSearchDomainUsers.parent().show();
				$inputs.attr('readonly', 'readonly');
			}
		}

		$buttonSearchDomainUsersByFilter.click(SearchDomainUsers);
		$buttonSearchDomainUsers.click(ShowSearchDomainUsersDialog);

		$gridviewProfiles = $('#gridviewProfiles');
		$gridviewPermissions = $('#gridviewPermissions');
		$hiddenPermissions = $('#hiddenPermissions');

		$gridviewPermissions.removeAttr('style');
		$gridviewProfiles.removeAttr('style');

		$('input[type=checkbox][rel="profiles"]', $gridviewProfiles).change(function () {
			var selectedProfiles = GetGridViewSelection($gridviewProfiles);
			var selectedProfilesPermissions = [];
			const checkbox = this;
			const isChecked = checkbox.checked;

			const checkedProfileId = parseInt(checkbox.attributes.rowid.value);

			for (var i = 0; i < selectedProfiles.length; i++) {
				for (var j = 0; j < profileAndPermissions.length; j++) {
					var profileId = profileAndPermissions[j].ProfileID;

					if (selectedProfiles[i] == profileId) {
						for (var k = 0; k < profileAndPermissions[j].Permissions.length; k++) {
							selectedProfilesPermissions.push(profileAndPermissions[j].Permissions[k]);
						}
					}
				}
			}

			const PROFILE_ID_SUPERVISOR = 2;
			const PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES = 53;

			const hasProfileSupervisor = selectedProfiles.includes(PROFILE_ID_SUPERVISOR);
			const hasPermission = selectedProfilesPermissions.includes(PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES);

			if (event !== undefined) {
				if (!Array.isArray(userPermissions)) {
					userPermissions = [];
				}
				const hasUserPermision = userPermissions.includes(PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES);

				if (isChecked && checkedProfileId === PROFILE_ID_SUPERVISOR && !hasUserPermision) {
					userPermissions.push(PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES);
				}

				if (!isChecked && checkedProfileId === PROFILE_ID_SUPERVISOR && hasUserPermision) {

					let keepPermission = false;

					for (let i = 0; i < selectedProfiles.length; i++) {
						const currentProfileId = selectedProfiles[i];

						if (currentProfileId === PROFILE_ID_SUPERVISOR) continue;

						const profileData = profileAndPermissions.find(p => p.ProfileID === currentProfileId);
						if (profileData && profileData.Permissions.includes(PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES)) {
							keepPermission = true;
							break;
						}
					}

					if (!keepPermission) {
						const index = userPermissions.indexOf(PERMISSION_ID_ALLOW_MOVE_BETWEEN_QUEUES);
						if (index !== -1) {
							userPermissions.splice(index, 1);
						}
					}
				}
			}

			SetGridViewSelection($gridviewPermissions, userPermissions, selectedProfilesPermissions, true);
		}).trigger('change');


		if ($gridviewProfiles.length > 0) {
			if (typeof (WebForm_OnSubmit) == 'function') {
				oldWebFormOnSubmit = WebForm_OnSubmit;
				WebForm_OnSubmit = function () {
					var value = oldWebFormOnSubmit();
					if (value) {
						$('input[type=checkbox]', $gridviewPermissions).each(function () {
							var $this = $(this);
							$this.prop('disabled', false);
						});
					}

					return value;
				}
			}
		}

		$hiddenUserAvatarFile = $('#hiddenUserAvatarFile');
		$hiddenUserAvatarFileContentType = $('#hiddenUserAvatarFileContentType');
		$fileUserAvatarFile = $('#fileUserAvatarFile');
		$divUserAvatarDropZone = $('#divUserAvatarDropZone');
		$divUserAvatarFileInfo = $('#divUserAvatarFileInfo');

		$divUserAvatarDropZone.fileupload({
			url: '../Configuration/FileHandler.ashx',
			type: 'POST',
			dropZone: $divUserAvatarDropZone,
			pasteZone: $divUserAvatarDropZone,
			fileInput: $fileUserAvatarFile,
			singleFileUpload: true,
			autoUpload: true,
			add: function (e, data) {
				//if (console)
				//	console.log('fileuploadadd: ' + JSON.safestringify(data));
				if (data.files.length !== 1) {
					console.log('Sólo se permite adjuntar de a 1 archivo a la vez');
					return false;
				}

				if (data.files[0].type.indexOf('image/') == -1)
					return false;

				var type = data.files[0].type;
				if (type != 'image/png' &&
					type != 'image/jpeg' &&
					type != 'image/jpg' &&
					type != 'image/gif')
					return false;

				data.submit();

				$divUserAvatarDropZone.hide();
				$divUserAvatarFileInfo.show();
				$('div.filename', $divUserAvatarFileInfo).text(data.files[0].name);
				$divUserAvatarFileInfo.addClass('uploading');
			},
			drop: function (e, data) {
				$divUserAvatarDropZone.removeClass('dragging');

				$divUserAvatarDropZone.fileupload('add', {
					files: data.files
				});
			},
			dragover: function (e) {
				$divUserAvatarDropZone.addClass('dragging');
			},
			done: function (e, data) {
				$divUserAvatarFileInfo.removeClass('uploading uploadfail');
				$divUserAvatarFileInfo.addClass('uploadsuccess');

				$hiddenUserAvatarFile.val(data._response.result.FileName);
				$hiddenUserAvatarFileContentType.val(data._response.result.ContentType);
				$('div.filepreview > img', $divUserAvatarFileInfo).attr('src', data._response.result.Url);

				$fileUserAvatarFile.val('');
				$divUserAvatarFileInfo.show();
				$divUserAvatarDropZone.hide();
			},
			fail: function (e, data) {
				$divUserAvatarFileInfo.removeClass('uploading uploadsuccess');
				$divUserAvatarFileInfo.addClass('uploadfail');
				$fileUserAvatarFile.val('');
			}
		});

		$divUserAvatarDropZone.on('dragleave', function (e) {
			$divUserAvatarDropZone.removeClass('dragging');
		});
		$divUserAvatarDropZone.click(function () {
			var $inputfile = $divUserAvatarDropZone.fileupload('option', 'fileInput');
			$inputfile.click();
		});
		$('div.removefile', $divUserAvatarFileInfo).click(function () {
			$divUserAvatarFileInfo.hide();
			$divUserAvatarDropZone.show();
			$divUserAvatarFileInfo.removeClass('uploading uploadsuccess uploadfail');
			$hiddenUserAvatarFile.removeProp('hadLogo');
		});

		var currentFile = $hiddenUserAvatarFile.val();
		if (currentFile && currentFile.length > 0) {
			$('div.filepreview > img', $divUserAvatarFileInfo).attr('src', currentFile);
			$hiddenUserAvatarFile.val('');
			$hiddenUserAvatarFile.prop('hadLogo', true);
			$divUserAvatarFileInfo.show();
			$divUserAvatarDropZone.hide();
			$divUserAvatarFileInfo.addClass('uploadsuccess');
		}
	}

	$divLoading = $('#divLoading');
	$divExport = $('#divExport');
	$divExportStep1 = $('#divExportStep1');
	$selectExportFormat = $('#selectExportFormat', $divExportStep1);
});

function i18nLoaded() {
	if ($panelList.length === 0) {
		if (typeof (strength) !== 'undefined') {
			strength['0'] = $.i18n('globals-password_strength-0');
			strength['1'] = $.i18n('globals-password_strength-1');
			strength['2'] = $.i18n('globals-password_strength-2');
			strength['3'] = $.i18n('globals-password_strength-3');
			strength['4'] = $.i18n('globals-password_strength-4');
		}

		var noneSelectedText = $.i18n("configuration-agents-no_restriction_all_services");
		$listboxServicesForOutgoingMessagesForTwitterUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForFacebookUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForFacebookMessengerUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForMailUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForWhatsappUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForSMSUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForTelegramUser.multiselect('option', 'noneSelectedText', noneSelectedText);
		$listboxServicesForOutgoingMessagesForInstagramUser.multiselect('option', 'noneSelectedText', noneSelectedText);
	}
	else {
		if (typeof ($selectProfilesFilter) !== 'undefined') {
			$selectProfilesFilter.multiselect('option', 'noneSelectedText', $.i18n('globals-any'));
		}

		$selectUserQueues.multiselect('option', 'noneSelectedText', $.i18n('globals-none'));
		$selectUserServices.multiselect('option', 'noneSelectedText', $.i18n('administration-users-edit_user_services-select'));

		RetrieveUsers();
	}
}

function RetrieveUsers() {
	if (!translationsLoaded) {
		return;
	}

	$divLoadingUsers.show();
	$tableUsers.hide();
	$panelButtons.hide();

	$.ajax({
		type: "POST",
		url: "Users.aspx/RetrieveUsers",
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		data: null,
		success: function (result) {
			users = null;

			if (result.d.Success == true) {
				users = result.d.Users;

				if (result.d.Users.length > 0) {
					if (datatable !== null &&
						typeof(datatable.destroy) === 'function') {
						datatable.destroy();
						datatable = null;
					}

					datatable = $tableUsers.DataTable({
						ordering: false,
						searching: true,
						paging: true,
						pageLength: 20,
						lengthChange: false,
						deferRender: true,
						data: users,
						createdRow: function (row, data, dataIndex) {
							let $tr = $(row);
							$tr.prop('user', data);
						},
						columns: [
							{
								render: function (data, type, user) {
									return user.ID;
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.attr('rel', 'id');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.ID === 1) {
										return 'ADMINISTRADOR';
									}

									return user.FullName;
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let user = rowData;
									if (user.ID === 1) {
										let $td = $(td);
										$td.attr('colspan', '2');
										$td.css('text-align', 'center');
									}
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.ID === 1) {
										return 'ADMINISTRADOR';
									}

									return user.UserName;
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let user = rowData;
									if (user.ID === 1) {
										let $td = $(td);
										$td.css('display', 'none');
									}
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									return (user.Email || '');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									return BuildAutenthicationType(user);
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('text-align', 'center');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									let profilesText = '';
									for (let j = 0; j < user.Profiles.length; j++) {
										let profile = profiles.find(p => p.ID === user.Profiles[j]);
										if (typeof (profile) === 'object') {
											if (profilesText.length > 0) {
												profilesText += ', ';
											}
											profilesText += profile.Name;
										}
									}

									return profilesText;
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.LastLogin !== null) {
										return '<span class="display-date ellapsed-time">' + user.LastLogin + '</span>';
									}
									else {
										return $.i18n('globals-never');
									}
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.Enabled) {
										return '<span class="fa fa-lg fa-yes"></span>';
									}
									else {
										return '<span class="fa fa-lg fa-no"></span>';
									}
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('text-align', 'center');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.IsAdministrator) {
										return '<span class="fa fa-lg fa-yes"></span>';
									}
									else {
										return '<span class="fa fa-lg fa-no"></span>';
									}
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('text-align', 'center');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.IsSupervisor) {
										return '<span class="fa fa-lg fa-yes"></span>';
									}
									else {
										return '<span class="fa fa-lg fa-no"></span>';
									}
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('text-align', 'center');
								},
								defaultContent: ''
							},
							{
								render: function (data, type, user) {
									if (user.TwoFactorEnabled) {
										return '<span class="fa fa-lg fa-yes"></span>';
									}
									else {
										return '<span class="fa fa-lg fa-no"></span>';
									}
								},
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('text-align', 'center');
									$td.attr('rel', 'twofactor');
								},
								defaultContent: ''
							},
							{
								data: null,
								className: 'icons',
								width: '100px',
								createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
									let $td = $(td);
									$td.css('white-space', 'nowrap');

									let user = cellData;

									let $anchorEnable = $('a[rel=enable]', $td);
									$anchorEnable.click(user, function (e) {
										ConfirmDialog({
											title: $.i18n("administration-users-enable_user"),
											message: $.i18n("administration-users-enable_user-question", e.data.FullName),
											onAccept: function (agentId) {
												$hiddenActionUserID.val(agentId);
												$hiddenActionName.val('Enable');
												$buttonAction[0].click();
											},
											onCancel: $.colorbox.close,
											acceptArguments: e.data.ID,
											acceptText: $.i18n('globals-accept'),
											cancelText: $.i18n('globals-cancel'),
											width: '500px',
											height: '180px'
										});
									});

									let $anchorDisable = $('a[rel=disable]', $td);
									$anchorDisable.click(user, function (e) {
										ConfirmDialog({
											title: $.i18n("administration-users-disable_user"),
											message: $.i18n("administration-users-disable_user-confirm", e.data.FullName),
											onAccept: function (agentId) {
												$hiddenActionUserID.val(agentId);
												$hiddenActionName.val('Disable');
												$buttonAction[0].click();
											},
											onCancel: $.colorbox.close,
											acceptArguments: e.data.ID,
											acceptText: $.i18n('globals-accept'),
											cancelText: $.i18n('globals-cancel'),
											width: '500px',
											height: '180px'
										});
									});

									let $anchorEdit = $('a[rel=edit]', $td);
									$anchorEdit.click(user, function (e) {
										$hiddenActionUserID.val(e.data.ID);
										$hiddenActionName.val('Edit');
										$buttonAction[0].click();
									});

									let $anchorCopy = $('a[rel=copy]', $td);
									$anchorCopy.click(user, function (e) {
										ShowCopyDialog(e.data.ID);
									});

									let $anchorDelete = $('a[rel=delete]', $td);
									$anchorDelete.click(user, function (e) {
										ShowDeleteDialog(e.data);
									});

									let $anchorChangePassword = $('a[rel=password]', $td);
									$anchorChangePassword.click(user, function (e) {
										$hiddenActionUserID.val(e.data.ID);
										$hiddenActionName.val('ChangePassword');
										$buttonAction[0].click();
									});

									let $anchorShowMoreInfo = $('a[rel=more]', $td);
									$anchorShowMoreInfo.click(user, function (e) {
										ShowUserInfo(e.data.ID);
									});

									if (((user.ID === 1 && loggedUserID === 1) || (user.ID !== 1 && user.ID !== loggedUserID)) && userHasUserAdministrationPermission) {
										let $anchorEditQueues = $('a[rel=queues]', $td);
										$anchorEditQueues.click(user, function (e) {
											ShowEditQueues(e.data);
										});

										let $anchorEdiServices = $('a[rel=services]', $td);
										$anchorEdiServices.click(user, function (e) {
											ShowEditServices(e.data);
										});
									}

									if (user.TwoFactorEnabled) {
										let $anchorRemoveTwoFactor = $('a[rel=twofactor]', $td);
										$anchorRemoveTwoFactor.click({
											user: user,
											$tdTwoFactorEnabled: $('td[rel=twofactor]', $td.parent()),
											$anchorRemoveTwoFactor: $anchorRemoveTwoFactor
										}, function (e) {
											var user = e.data.user;
											var $tdTwoFactorEnabled = e.data.$tdTwoFactorEnabled;
											var $anchorRemoveTwoFactor = e.data.$anchorRemoveTwoFactor;

											RemoveTwoFactor(user, $tdTwoFactorEnabled, $anchorRemoveTwoFactor);
										});
									}
								},
								render: function (data, type, user) {
									let html = '';

									if (((user.ID === 1 && loggedUserID === 1) || (user.ID !== 1 && user.ID !== loggedUserID)) && userHasUserAdministrationPermission) {
										if (!user.Enabled) {
											html += '<a class="action" rel="enable"><span class="fa fa-lg fa-toggle-on" title="Habilitar" data-i18n-title="administration-users-enable"></span></a>';
										}
										else {
											html += '<a class="action" rel="disable"><span class="fa fa-lg fa-toggle-off" title="Deshabilitar" data-i18n-title="administration-users-disable"></span></a>';
										}

										html += '<a class="action" rel="edit"><span class="fa fa-lg fa-edit" title="Editar" data-i18n-title="globals-edit"></span></a>';
									}

									if (user.ID !== 1 && (typeof (cannotCreateMoreUsers) === 'undefined' || !cannotCreateMoreUsers)) {
										html += '<a class="action" rel="copy"><span class="fa fa-lg fa-clone" title="Copiar" data-i18n-title="globals-copy"></span></a>';
									}

									if (user.ID !== 1 && user.ID !== loggedUserID && userHasUserAdministrationPermission) {
										html += '<a class="action" rel="delete"><span class="fa fa-lg fa-trash" title="Eliminar" data-i18n-title="globals-delete"></span></a>';
									}

									if (user.AuthenticationType === AuthenticationTypes.Local) {
										html += '<a class="action" rel="password"><span class="fa fa-lg fa-lock" title="Cambiar Contraseña" data-i18n-title="configuration-agents-change_password"></span></a>';
									}

									html += '<a class="action" rel="more"><span class="fa fa-lg fa-search-plus" title="Ver más información" data-i18n-title="globals-viewmore"></span></a>';

									if (((user.ID === 1 && loggedUserID === 1) || (user.ID !== 1 && user.ID !== loggedUserID)) && userHasUserAdministrationPermission) {
										html += '<a class="action" rel="queues"><span class="fa fa-lg fa-inbox" title="Editar colas" data-i18n-title="administration-users-edit_queues"></span></a>';

										html += '<a class="action" rel="services"><span class="fa fa-lg fa-globe-americas" title="Editar servicios" data-i18n-title="administration-users-edit_services"></span></a>';
									}

									if (user.TwoFactorEnabled) {
										html += '<a class="action" rel="twofactor"><span class="fa fa-lg fa-user-unlock" title="Quitar doble factor" data-i18n-title="administration-users-remove_two_factor"></span></a>';
									}

									return html;
								}
							}
						],
						initComplete: function (settings, json) {
							var $tdTextFilterPlaceholder = $('#tdTextFilterPlaceholder');
							var $currentInput = $('input[type=search]', $tdTextFilterPlaceholder);
							$currentInput.remove();

							var $tableProfiles_filter = $('#tableUsers_filter');
							var $input = $('input[type=search]', $tableProfiles_filter);
							$input.detach();
							$input.addClass('inputtext normal-readonly');
							$tdTextFilterPlaceholder.append($input);
							$tableProfiles_filter.remove();
							$input.attr('autocomplete', 'new-password');
							$input.attr('name', 'agents-search');
							$input.attr('readonly', 'readonly');
							$input.val('');
							$input.focus(function () {
								this.removeAttribute('readonly');
							});
						}
					});

					datatable.column(0).visible($checkboxFilterShowID.is(':checked'));

					LoadCompositedElements();

					$tableUsers.show();
					$panelButtons.show();
				}
				else {
					$tableUsers.hide();
					$('#messageNoUsers').show();
					$('#anchorFilter').hide();
					$('#buttonExport').parent().hide();
					$panelButtons.show();
				}
			}
			else {
                AlertDialog($.i18n("configuration-agents-agents"), $.i18n("configuration-agents-cannot_recover_agent_list"))
			}

			$divLoadingUsers.hide();
		},
		error: function () {
			AlertDialog($.i18n("configuration-agents-agents"), $.i18n("configuration-agents-cannot_recover_agent_list"));
			$divLoadingUsers.hide();
		}
	});
}

function BuildAutenthicationType(user) {
	if (typeof user.AuthenticationType !== 'undefined' && user.AuthenticationType !== null) {

		switch (user.AuthenticationType) {
			case AuthenticationTypes.Local: return 'Local';
			case AuthenticationTypes.Google: return 'Google';
			case AuthenticationTypes.Saml: return 'SAML';
			case AuthenticationTypes.Domain: return $.i18n("configuration-agents-domain");
			default: return 'Local';
		}
	}

	return 'Local';
}

function CalculatePasswordStrength(password, $inputs) {
	var inputs = [];
	if (typeof ($inputs) !== 'undefined' &&
		$inputs !== null &&
		'length' in $inputs &&
		$inputs.length > 0) {

		for (var i = 0; i < $inputs.length; i++) {
			if ($inputs[i] === null)
				continue;

			var text;
			if (typeof ($inputs[i]) === 'string') {
				text = $inputs[i];
			}
			else if (typeof ($inputs[i]) === 'object') {
				if ($inputs[i] instanceof jQuery) {
					if ($inputs[i].prop('tagName') === 'SPAN')
						text = $inputs[i].text();
					else
						text = $inputs[i].val().trim();
				}
				else {
					text = $inputs[i].toString();
				}
			}
			else {
				text = $inputs[i].toString();
			}
				
			if (typeof (text) !== 'undefined' &&
				text !== null &&
				text.length > 0)
				inputs.push(text);
		}
	}

	var result = zxcvbn(password, inputs);

	return result;
}

var domainUsers;
function SearchDomainUsers() {
	var filter = $textboxDomainUserFilter.val();
	if (filter.length < 2)
		return;

	var dataToSend = JSON.stringify({ filter: filter });
	$messageSearchDomainUsersNoResults.hide();
	$divSearchDomainUsersResult.hide();

	$divSearchDomainUsersLoading.show();
	$.colorbox.resize();

	$.ajax({
		type: "POST",
		url: "Users.aspx/SearchDomainUsers",
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		data: dataToSend,
		success: function (result) {
			if (result.d.Success == true && result.d.Info.length > 0) {
				var $tBody = $('tbody', $tableSearchDomainUsersResults);
				$tBody.empty();

				domainUsers = result.d.Info;

				//Los parámetros fueron configurados para ser mapeados
				if (result.d.ConfigurationParams.Use) {
					for (var i = 0; i < result.d.Info.length; i++) {
						var user = result.d.Info[i];
						var configurationParams = result.d.ConfigurationParams;

						var html = '<tr class="' + ((i % 2 == 0) ? 'normal' : 'alternate') + '">' +
							  '<td style="white-space:normal">' + ((user.Properties.hasOwnProperty(configurationParams.FirstName) && user.Properties[configurationParams.FirstName] != null) ? user.Properties[configurationParams.FirstName] : user.FirstName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.Properties.hasOwnProperty(configurationParams.LastName) && user.Properties[configurationParams.LastName] != null) ? user.Properties[configurationParams.LastName] : user.LastName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.Properties.hasOwnProperty(configurationParams.UserName) && user.Properties[configurationParams.UserName] != null) ? user.Properties[configurationParams.UserName] : user.UserName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.Properties.hasOwnProperty(configurationParams.Email) && user.Properties[configurationParams.Email] != null) ? user.Properties[configurationParams.Email] : user.Email) + '</td>' +
							  '<td style="white-space:normal">' + ((user.Properties.hasOwnProperty(configurationParams.LDAP) && user.Properties[configurationParams.LDAP] != null) ? user.Properties[configurationParams.LDAP] : user.DistinguishedName) + '</td>' +
							  '<td style="white-space:normal;text-align:center"><a href="javascript:SearchDomainUserSelect(domainUsers[' + i + '])"><span class="fa fa-lg fa-check-square" title="Seleccionar" data-i18n-title="globals-select"></span></a></td></tr>';
						var $html = $(html);
						$tBody.append(html);
					}
				}
				else {
					for (var i = 0; i < result.d.Info.length; i++) {

						var user = result.d.Info[i];

						var html = '<tr class="' + ((i % 2 == 0) ? 'normal' : 'alternate') + '">' +
							  '<td style="white-space:normal">' + ((user.FirstName == null) ? '' : user.FirstName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.LastName == null) ? '' : user.LastName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.UserName == null) ? '' : user.UserName) + '</td>' +
							  '<td style="white-space:normal">' + ((user.Email == null) ? '' : user.Email) + '</td>' +
							  '<td style="white-space:normal">' + ((user.DistinguishedName == null) ? '' : user.DistinguishedName) + '</td>' +
							  '<td style="white-space:normal;text-align:center"><a href="javascript:SearchDomainUserSelect(domainUsers[' + i + '])"><span class="fa fa-lg fa-check-square" title="Seleccionar" data-i18n-title="globals-select"></span></a></td></tr>';
						var $html = $(html);
						$tBody.append(html);
					}
				}

				$divSearchDomainUsersResult.show();
				$divSearchDomainUsersLoading.hide();
				$.colorbox.resize();
			}
			else {
				$messageSearchDomainUsersNoResults.show();
				$divSearchDomainUsersLoading.hide();
				if (!result.d.Success && console)
					console.error('Ocurrió un error buscando usuarios: %o', result.d.Error);
				$.colorbox.resize();
			}
		},
		error: function () {
			$messageSearchDomainUsersNoResults.show();
			$divSearchDomainUsersLoading.hide();
		}
	});
}

function SearchDomainUserSelect(user) {
	$textboxFirstName.val(user.FirstName);
	$textboxLastName.val(user.LastName);
	$textboxUsername.val(user.UserName);
	$textboxEmail.val(user.Email);
	$textboxLDAP.val(user.DistinguishedName);

	$.colorbox.close();
}

function ShowSearchDomainUsersDialog() {
	$buttonSearchDomainUsersConfirm.parent().hide();
	$messageSearchDomainUsersNoResults.hide();
	$divSearchDomainUsersResult.hide();
	$textboxDomainUserFilter.val('');

	$.colorbox({
		inline: true,
		href: $divSearchDomainUsers,
		width: '65%',
		height: '500px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function ValidateCopyCriteria(sender, e) {	
	var $sender = $(sender);
	var checkedQ = 0;

	checkedQ += $checkboxCopyProfile[0].checked ? 1 : 0;
	checkedQ += $checkboxCopyAllowed[0].checked ? 1 : 0;
	checkedQ += $checkboxCopyAsociatedQueues[0].checked ? 1 : 0;
	checkedQ += $checkboxCopyAgentsGroups[0].checked ? 1 : 0;
	checkedQ += $checkboxCopySettings[0].checked ? 1 : 0;

	e.IsValid = (checkedQ != 0);

	if (!e.IsValid) {
		$sender.text(sender.errormessage).parent().show();
		$.colorbox.resize();
		return;
	}

	$sender.parent().hide();
	$.colorbox.resize();
}

function ValidateLocalUserPasswords(sender, e) {
	if ($dropdownlistUserType.length > 0) {
		var value = $dropdownlistUserType.val();
		if (value != AuthenticationTypes.Local) {
			e.IsValid = true;
			return;
		}
	}

	if (typeof (allowCreateLocalUsers) !== 'undefined' && !allowCreateLocalUsers) {
		e.IsValid = true;
		return;
	}

	var $sender = $(sender);

	var password = $textboxPassword.val();

	if (password.length === 0) {
		sender.errormessage = $.i18n('globals-password-error-invalid');
		$sender.text(sender.errormessage).parent().show();
		e.IsValid = false;
		return;
	}

	if (typeof (minimumPasswordStrength) === 'undefined' ||
		minimumPasswordStrength !== 0) {
		var result = CalculatePasswordStrength(password, [$textboxFirstName, $textboxLastName, $textboxUsername, $textboxEmail]);
		if (result.score < minimumPasswordStrength) {
			sender.errormessage = $.i18n('globals-password-error-insecure');
			$sender.text(sender.errormessage).parent().show();
			e.IsValid = false;
			return;
		}
	}

	var $textboxPasswordConf = $('#textboxPasswordConf');
	var conf = $textboxPasswordConf.val();

	if (password != conf) {
		sender.errormessage = $.i18n('globals-password-error-confirmation_not_equals');
		$sender.text(sender.errormessage).parent().show();
		e.IsValid = false;
		return;
	}

	$sender.parent().hide();
	e.IsValid = true;
}

function ValidateChangePasswords(sender, e) {
	var $sender = $(sender);

	var password = $textboxChangePassword.val();

	if (password.length === 0) {
		sender.errormessage = $.i18n('globals-password-error-invalid');
		$sender.text(sender.errormessage).parent().show();
		e.IsValid = false;
		return;
	}

	if (typeof (minimumPasswordStrength) === 'undefined' ||
		minimumPasswordStrength !== 0) {
		var result = CalculatePasswordStrength(password, [$('#spanChangePasswordFirstName'), $('#spanChangePasswordLastName'), $('#spanChangePasswordUsername')]);
		if (result.score < minimumPasswordStrength) {
			sender.errormessage = $.i18n('globals-password-error-insecure');
			$sender.text(sender.errormessage).parent().show();
			e.IsValid = false;
			return;
		}
	}

	var $textboxPasswordConf = $('#textboxChangePasswordConf');
	var conf = $textboxPasswordConf.val();

	if (password != conf) {
		sender.errormessage = $.i18n('globals-password-error-confirmation_not_equals');
		$sender.text(sender.errormessage).parent().show();
		e.IsValid = false;
		return;
	}

	$sender.parent().hide();
	e.IsValid = true;
}

function ValidateUserName(sender, e) {
	var $sender = $(sender);
	var $textboxUsername = $('input[type=text][id$=textboxUsername]');
	var username = $textboxUsername.val();

	if (username.length == 0) {
		sender.errormessage = $.i18n('administration-users-edit-invalid_username');
		$sender.text(sender.errormessage).parent().show();
		e.IsValid = false;
		return;
	}

	var dataToSend = JSON.stringify({ username: username });

	$.ajax({
		type: "POST",
		url: "Users.aspx/ValidateUserName",
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		data: dataToSend,
		async: false,
		success: function (result) {
			if (result.d.Success) {
				e.IsValid = result.d.IsValid;
			}
			else {
				e.IsValid = false;
			}
		},
		error: function () {
			e.IsValid = false;
		}
	});

	if (!e.IsValid) {
		sender.errormessage = $.i18n('administration-users-edit-already_exists');
		$sender.text(sender.errormessage).parent().show();
	}
}

function FilterChanged() {
	datatable.draw();
}

function ValidateAtLeastOneProfileOrPermission(sender, e) {
	var permissionsSelected = ValidateGridViewSelection($gridviewPermissions);
	var profilesSelected = ValidateGridViewSelection($gridviewProfiles);

	e.IsValid = permissionsSelected || profilesSelected;
	if (!e.IsValid) {
		$(sender).parent().show();
	}
}

function LoadCopyDefaultValues() {	
	$checkboxCopyProfile[0].checked = true;
	$checkboxCopyAllowed[0].checked = true;
	$checkboxCopyAsociatedQueues[0].checked = true;
	$checkboxCopyAgentsGroups[0].checked = true;
	$checkboxCopySettings[0].checked = true;
}

function ShowCopyDialog(user) {	
	LoadCopyDefaultValues();
	$('#hiddenUserID').val(user);

	$buttonCopyUserConfirm.bind('click', function () {		
		$hiddenActionUserID.val(user);
		$hiddenActionName.val('Copy');

		var $hiddenCopyProfile = $('#hiddenCopyProfile');
		var $hiddenCopyAllowed = $('#hiddenCopyAllowed');
		var $hiddenCopyAsociatedQueues = $('#hiddenCopyAsociatedQueues');
		var $hiddenCopyAgentsGroups = $('#hiddenCopyAgentsGroups');
		var $hiddenCopySettings = $('#hiddenCopySettings');

		$hiddenCopyProfile.val($checkboxCopyProfile.is(':checked') ? "true" : "false");
		$hiddenCopyAllowed.val($checkboxCopyAllowed.is(':checked') ? "true" : "false");
		$hiddenCopyAsociatedQueues.val($checkboxCopyAsociatedQueues.is(':checked') ? "true" : "false");
		$hiddenCopyAgentsGroups.val($checkboxCopyAgentsGroups.is(':checked') ? "true" : "false");
		$hiddenCopySettings.val($checkboxCopySettings.is(':checked') ? "true" : "false");

		if ($hiddenCopyProfile.val() != "true" && $hiddenCopyAllowed.val() != "true" && $hiddenCopyAsociatedQueues.val() != "true" && $hiddenCopyAgentsGroups.val() != "true" && $hiddenCopySettings.val() != "true") {
			$MessageErrorCheckbox.show();
			return;
		}

		$MessageErrorCheckbox.hide();
		$buttonAction[0].click();
	});

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $('#divCopy'),
		width: '400px',
		initialWidth: '400px',
		height: '400px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function ShowDeleteDialog(user) {
	$divCanBeDeletedLoading.show();
	$messageUserCannotBeDeleted.hide();
	$messageUserCanBeDeleted.hide();
	$messageUserCannotBeDeletedAndIsDisabled.hide();
	$messageUserCanBeDeletedAndIsDisabled.hide();
	$messageCouldntCheckUserCanBeDeleted.hide();
	$buttonDeleteUserConfirm.parent().hide();
	$buttonDisableUserConfirm.parent().hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divDeleteUser,
		width: '600px',
		initialWidth: '600px',
		height: '400px',
		preloading: false,
		closeButton: false,
		onLoad: function () {
			$.colorbox.resize();

			var dataToSend = JSON.stringify({ userId: user.ID });

			$.ajax({
				type: "POST",
				url: "Users.aspx/CanBeDeleted",
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				data: dataToSend,
				success: function (result) {
					$divCanBeDeletedLoading.hide();

					if (result.d.Success) {
						if (result.d.CanBeDeleted) {
							$buttonDeleteUserConfirm.unbind('click');
							$buttonDeleteUserConfirm.bind('click', function () {
								$hiddenActionUserID.val(user.ID);
								$hiddenActionName.val('Delete');
								$buttonAction[0].click();
							});
							$buttonDeleteUserConfirm.parent().show();

							if (user.Enabled) {
								$messageUserCanBeDeleted.show();

								$buttonDisableUserConfirm.unbind('click');
								$buttonDisableUserConfirm.bind('click', function () {
									$hiddenActionUserID.val(user.ID);
									$hiddenActionName.val('Disable');
									$buttonAction[0].click();
								});
								$buttonDisableUserConfirm.parent().show().removeClass('uiButtonConfirm');
							}
							else {
								$messageUserCanBeDeletedAndIsDisabled.show();
							}
						}
						else
							$messageUserCannotBeDeletedAndIsDisabled.show();
					}
					else {
						$messageCouldntCheckUserCanBeDeleted.show();
						$messageCouldntCheckUserCanBeDeleted.append('<br />' + result.d.Error.Message);
					}

					$.colorbox.resize();
				},
				error: function () {
					$divCanBeDeletedLoading.hide();
					$messageCouldntCheckUserCanBeDeleted.show();

					$.colorbox.resize();
				}
			});
		}
	});
}

/********** Export functions *************/

function Export() {
	var $buttonExportCompleteVisible = $('#buttonExportCompleteVisible', $divExport).parent();
	$divExportStep1.show();
	$buttonExportCompleteVisible.show();
	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divExport",
		width: '600px',
		initialWidth: '200px',
		preloading: false,
		closeButton: false
	});
}

function ExportVisible() {
	var $hiddenExportFormat;

	$hiddenExportFormat = $('#hiddenExportFormat');
	$hiddenExportFormat.val($selectExportFormat.val());
	var $buttonExport = $('input[id$="buttonExport"]');
	
	$buttonExport.click();
	$.colorbox.close();
}

function ValidateEmail(sender, e) {
	if ($dropdownlistUserType.val() === AuthenticationTypes.Google) {
		var $textboxEmail = $('#textboxEmail');
		var email = $textboxEmail.val();

		if (email.length === 0) {
			e.IsValid = false;
			return;
		}
	}

	e.IsValid = true;
}

function ValidateEmailDuplicated(sender, e) {
	var $textboxEmail = $('#textboxEmail');
	var email = $textboxEmail.val();

	if (email.length === 0) {
		e.IsValid = true;
		return;
	}

	var regex = /\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
	if (!regex.test(email)) {
		e.IsValid = false;
		return;
	}

	var dataToSend = JSON.stringify({ email: email });

	$.ajax({
		type: "POST",
		url: "Users.aspx/ValidateEmail",
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		data: dataToSend,
		async: false,
		success: function (result) {
			if (result.d.Success) {
				e.IsValid = result.d.IsValid;
			}
			else {
				e.IsValid = false;
			}
		},
		error: function () {
			e.IsValid = false;
		}
	});
}

function ShowUserInfo(id) {
	var url = "UserInfo.aspx?UserID=" + id;
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: url,
		width: '65%',
		height: '600px',
		initialHeight: '600px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			$('#tabsUser').tabs(
                        {
                        	activate: function (event, ui) {
                        		$.colorbox.resize();
                        	}
                        });

			LoadCompositedElements();

			$.colorbox.resize();
		}
	});
}

function ShowFilterDialog() {
	ShowRightPanel(true);
}

var currentUserEdition = null;

function ShowEditQueues(user) {
	var $divEditQueues = $('#divEditQueues');
	$('> .title > h2', $divEditQueues).text($.i18n('administration-users-edit_user_queues', user.FullName));

	$selectUserQueues.val(user.Queues);
	$selectUserQueues.multiselect('resync');

	currentUserEdition = user;

	$.colorbox({
		inline: true,
		href: $divEditQueues,
		width: '600px',
		height: '500px',
		preloading: false,
		closeButton: false,
		escKey: false,
		trapFocus: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function SaveUserQueues() {
	let selectedQueues = $selectUserQueues.val();
	if (selectedQueues !== null) {
		selectedQueues = selectedQueues.map(function (x) {
			return parseInt(x, 10);
		});

		selectedQueues.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0));
	}

	if (currentUserEdition.Queues) {
		currentUserEdition.Queues.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0));
	}

	if (selectedQueues === null &&
		(currentUserEdition.Queues === null || currentUserEdition.Queues.length === 0)) {
		AlertDialog($.i18n('administration-users-edit_user_queues', currentUserEdition.FullName), $.i18n('administration-users-edit_user_queues-no_changes'));
		return;
	}

	if (selectedQueues !== null) {
		if (selectedQueues.equals(currentUserEdition.Queues)) {
			AlertDialog($.i18n('administration-users-edit_user_queues', currentUserEdition.FullName), $.i18n('administration-users-edit_user_queues-no_changes'));
			return;
		}
	}

	LoadingDialog({
		title: $.i18n('globals-loading'),
		timeout: 300,
		autoClose: false,
		onTimeout: function () {
			$.ajax({
				type: "POST",
				url: "Users.aspx/SaveQueues",
				data: JSON.stringify({ userId: currentUserEdition.ID, queueIds: selectedQueues }),
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						currentUserEdition.Queues = selectedQueues;

						$.colorbox.close();
					}
					else {
						if (typeof (data.d.Error) !== 'undefined') {
							console.log(data.d.Error);
						}
						AlertDialog($.i18n('administration-users-edit_user_queues', currentUserEdition.FullName), $.i18n('globals-error'), undefined, undefined, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n('administration-users-edit_user_queues', currentUserEdition.FullName), $.i18n('globals-error'), undefined, undefined, 'Error');
				}
			});
		}
	});
}

function ShowEditServices(user) {
	var $divEditServices = $('#divEditServices');
	$('> .title > h2', $divEditServices).text($.i18n('administration-users-edit_user_services', user.FullName));

	let services = null;
	if (user.Services === null) {
		$selectUserServicesConfig.val(0);
	}
	else if (user.Services.length === 1 && user.Services[0] === -1) {
		$selectUserServicesConfig.val(-1);
	}
	else {
		$selectUserServicesConfig.val(1);
		services = user.Services;
	}
	$selectUserServicesConfig.trigger('change').multiselect('resync');

	$selectUserServices.val(services);
	$selectUserServices.multiselect('resync');

	currentUserEdition = user;

	$.colorbox({
		inline: true,
		href: $divEditServices,
		width: '600px',
		height: '500px',
		preloading: false,
		closeButton: false,
		escKey: false,
		trapFocus: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function SaveUserServices() {
	let services = null;
	let type = parseInt($selectUserServicesConfig.val(), 10);
	if (type === -1) {
		services = [];
		services.push(-1);
	}
	else if (type === 1) {
		services = $selectUserServices.val();
		if (services !== null) {
			services = services.map(function (x) {
				return parseInt(x, 10);
			});

			services.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0));
		}

		if (services === null || services.length === 0) {
			AlertDialog($.i18n('administration-users-edit_user_services', currentUserEdition.FullName), $.i18n('administration-users-edit_user_services-select_one'));
			return;
		}
	}

	LoadingDialog({
		title: $.i18n('globals-loading'),
		timeout: 300,
		autoClose: false,
		onTimeout: function () {
			$.ajax({
				type: "POST",
				url: "Users.aspx/SaveServices",
				data: JSON.stringify({ userId: currentUserEdition.ID, serviceIds: services }),
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						currentUserEdition.Services = services;

						$.colorbox.close();
					}
					else {
						if (typeof (data.d.Error) !== 'undefined') {
							console.log(data.d.Error);
						}
						AlertDialog($.i18n('administration-users-edit_user_services', currentUserEdition.FullName), $.i18n('globals-error'), undefined, undefined, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n('administration-users-edit_user_services', currentUserEdition.FullName), $.i18n('globals-error'), undefined, undefined, 'Error');
				}
			});
		}
	});
}

function RemoveTwoFactor(user, $tdTwoFactorEnabled, $anchorRemoveTwoFactor) {
	ConfirmDialog($.i18n('administration-users-remove_two_factor'), $.i18n('administration-users-remove_two_factor-question', user.FullName), function () {
		LoadingDialog({
			title: $.i18n('administration-users-remove_two_factor'),
			onClose: function () {
				var dataToSend = JSON.stringify({ userId: user.ID });

				$.ajax({
					type: "POST",
					url: "Users.aspx/RemoveTwoFactor",
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					data: dataToSend,
					async: false,
					success: function (result) {
						if (result.d.Success) {
							AlertDialog($.i18n('administration-users-remove_two_factor'), $.i18n('administration-users-remove_two_factor-success', user.FullName));

							$anchorRemoveTwoFactor.hide();
							var $span = $('span', $tdTwoFactorEnabled);
							$span.removeClass('fa-yes').addClass('fa-no');
						}
						else {
							AlertDialog($.i18n('administration-users-remove_two_factor'), $.i18n('administration-users-remove_two_factor-failed', user.FullName), null, null, 'Error');
						}
					},
					error: function () {
						AlertDialog($.i18n('administration-users-remove_two_factor'), $.i18n('administration-users-remove_two_factor-failed', user.FullName), null, null, 'Error');
					}
				});
			},
			timeout: 500
		});
	});
}