﻿/// <reference path="../Scripts/jquery-3.5.1.js" />


var oldWebFormOnSubmit;
var $textboxMailEditSubject;
var $messageMailEditSubjectFields;
var $dropdownlistMailSignatureBehaviour;
var $divMailSignature;
var $textboxMailSignature;
var $trMailQuoteTypes;
var $checkboxEmailIncludesPreviousQuotes;
var $checkboxAnswerOutsideDomainAvailable;
var $textboxAvailableDomains;
var $trAvailableDomains;
var $divEmailService;
var $checkboxEmailAllowToSendAttachments;
var $textboxEmailMaxAttachments;
var $trEmailZipOptions;
var $checkboxUseEmailZip;
var $textboxEmailZipOptionsMaxSize;
var $checkboxChangeFontAvailable;
var $trFontType;
var $trFontSize;
var $dropdownlistGrouping;
var $divGroupingExcludedSenders;
var $textboxGroupingExcludedSenders;
var $checkboxReceivesWebFormsMails;
var $divReceivesWebFormsMails;
var $hiddenWebForms;
var $tableWebForms;
var $tbodyWebForms;
var $aWebFormsAdd;
var $checkboxMailSendUseCredentials;
var $trMailSendUseCredentials;
var $textboxMailSendUser;
var $textboxMailSendPassword;
var $textboxMailAddress;
var $dropdownlistMailGenerateMultipleRepliesBehaviour;
var $messageMailGenerateMultipleRepliesBehaviourNoOtherServices;
var $panelMailGenerateMultipleRepliesBehaviour;
var $listboxServicesForMultipleRepliesGeneration;
var $checkboxMailRetrievePassword;
var $labelMailRetrievePassword;
var $textboxMailRetrievePassword;
var $checkboxMailRetrievePOP3Password;
var $labelMailRetrievePOP3Password;
var $textboxMailRetrievePOP3Password;
var $checkboxMailRetrieveEWSPassword;
var $labelMailRetrieveEWSPassword;
var $textboxMailRetrieveEWSPassword;
var $checkboxMailSendPassword;
var $labelMailSendPassword;
var $textboxMailSendPassword;
var $dropdownlistMailConnectionType;
var $divIMAP;
var $divSMTP;
var $divEWS;
var $dropdownlistMailRetrieveEWSAuthenticationType;
var $divEWSAuthenticationTypeUserAndPassword;
var $divEWSAuthenticationTypeOAuth;
var $divPOP3;
var $divGmail;

var $textboxMailAuthenticationErrorEmailTemplate;
var $textboxMailInactivityDetectedEmailTemplate;

var $listboxMailInactivityDetectedConnection;
var $hiddenMailInactivityDetectedConnection;
var $hiddenAuthenticationErrorConnection;
var $listboxAuthenticationErrorConnection;

$(function () {
	InitializeMail();

	LoadCompositedElements();
	$('#checkboxSurveySendIfNewCaseExists').click(function () {
		$('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', false);
		$('#checkboxSurveySendIfNewCaseHasTag').prop('checked', false);
	});

	$('#checkboxSurveySendIfNewCaseClosedByYflow').click(function () {
		$('#checkboxSurveySendIfNewCaseExists').prop('checked', false);
		$('#checkboxSurveySendIfNewCaseHasTag').prop('checked', false);
	});

	$('#checkboxSurveySendIfNewCaseHasTag').click(function () {
		$('#checkboxSurveySendIfNewCaseExists').prop('checked', false);
		$('#checkboxSurveySendIfNewCaseClosedByYflow').prop('checked', false);
	});
});

function InitializeMail() {
	$dropdownlistMailConnectionType = $('#dropdownlistMailConnectionType');
	$dropdownlistMailConnectionType.multiselect({ multiple: false, selectedList: 4 });
	$divIMAP = $('#divIMAP');
	$divSMTP = $('#divSMTP');
	$divPOP3 = $('#divPOP3');
	$divEWS = $('#divEWS');
	$divGmail = $('#divGmail');

	$dropdownlistMailRetrieveEWSAuthenticationType = $('#dropdownlistMailRetrieveEWSAuthenticationType');
	$divEWSAuthenticationTypeUserAndPassword = $('#divEWSAuthenticationTypeUserAndPassword');
	$divEWSAuthenticationTypeOAuth = $('#divEWSAuthenticationTypeOAuth');

	$dropdownlistMailRetrieveEWSAuthenticationType.change(function () {
		let authenticationType = parseInt($dropdownlistMailRetrieveEWSAuthenticationType.val(), 10);
		$divEWSAuthenticationTypeUserAndPassword.toggle(authenticationType === 1);
		$divEWSAuthenticationTypeOAuth.toggle(authenticationType === 2);
	}).trigger('change');

	var $trIMAPDependency = $('tr[data-protocol-dependency=imap]');
	var $trMailDownloadOriginal = $('#trMailDownloadOriginal');
	var $checkboxMailDownloadOriginal = $('#checkboxMailDownloadOriginal');
	var $trMailDownloadOriginalAgents = $('#trMailDownloadOriginalAgents');
	var $checkboxMailDownloadOriginalAgents = $('#checkboxMailDownloadOriginalAgents');
	$dropdownlistMailConnectionType.change(function () {
		var type = parseInt($dropdownlistMailConnectionType.val(), 10);
		switch (type) {
			case 1:
				$divIMAP.show();
				$divSMTP.show();
				$divPOP3.hide();
				$divEWS.hide();
				$divGmail.hide();
				$trIMAPDependency.show();
				break;
			case 2:
				$divPOP3.show();
				$divSMTP.show();
				$divIMAP.hide();
				$divEWS.hide();
				$divGmail.hide();
				$trIMAPDependency.hide();
				break;
			case 3:
				$divPOP3.hide();
				$divIMAP.hide();
				$divSMTP.hide();
				$divEWS.show();
				$divGmail.hide();
				$trIMAPDependency.hide();
				break;
			case 4:
				$divPOP3.hide();
				$divIMAP.hide();
				$divSMTP.hide();
				$divEWS.hide();
				$divGmail.show();
				$trIMAPDependency.hide();
				break;
		}

		$trMailDownloadOriginal.toggle(type !== 2);
		$trMailDownloadOriginalAgents.toggle(type !== 2 && $checkboxMailDownloadOriginal.is(':checked'));

		if (type === 2) {
			$checkboxMailDownloadOriginal.prop('checked', false).trigger('change');
		}
	}).trigger('change');

	$checkboxMailDownloadOriginal.change(function () {
		var type = parseInt($dropdownlistMailConnectionType.val(), 10);
		$trMailDownloadOriginalAgents.toggle(type !== 2 && $checkboxMailDownloadOriginal.is(':checked'));
	}).trigger('change');

	$("#textboxMailFromDate").datepicker({ changeMonth: true, changeYear: true, numberOfMonths: 1 });

	var cleditorOptions = {
		height: 200,
		width: 'auto',
		fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
		bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
		controls:     // controls to add to the toolbar
          "bold italic underline | font size " +
          "style | color highlight removeformat | bullets numbering | outdent " +
          "indent | alignleft center alignright justify | " +
          "image link | cut copy paste pastetext | source",
	};

	$textboxMailAuthenticationErrorEmailTemplate = $('#textboxMailAuthenticationErrorEmailTemplate');
	$textboxMailAuthenticationErrorEmailTemplate.cleditor(cleditorOptions);
	var $messageMailAuthenticationErrorEmailTemplateFields = $('#messageMailAuthenticationErrorEmailTemplateFields');
	CallValidationFields($textboxMailAuthenticationErrorEmailTemplate, $messageMailAuthenticationErrorEmailTemplateFields);

	$textboxMailInactivityDetectedEmailTemplate = $('#textboxMailInactivityDetectedEmailTemplate');
	$textboxMailInactivityDetectedEmailTemplate.cleditor(cleditorOptions);
	var $messageMailInactivityDetectedEmailTemplateFields = $('#messageMailInactivityDetectedEmailTemplateFields');
	CallValidationFields($textboxMailInactivityDetectedEmailTemplate, $messageMailInactivityDetectedEmailTemplateFields);

	var $listboxMailInactivityDetectedConnection = $('#listboxMailInactivityDetectedConnection');
	var $hiddenMailInactivityDetectedConnection = $('#hiddenMailInactivityDetectedConnection');

	var $hiddenAuthenticationErrorConnection = $('#hiddenAuthenticationErrorConnection');
	var $listboxAuthenticationErrorConnection = $('#listboxAuthenticationErrorConnection');

	if (typeof (emails) !== 'undefined' && emails !== null && emails.length > 0) {

		let addOption = function (email, $select) {
			let $option = $('<option></option>');
			$option.text(email.Name);
			$option.val(email.ID);
			$select.append($option);
		};

		let selectOptions = function ($hidden, $select) {
			let value = $hidden.val();
			if (typeof (value) === 'string' && value.length > 0) {
				$select.val(value);
			}
		};

		for (let i = 0; i < emails.length; i++) {
			//No listamos la casilla por defult ya que al enviar el valor en null se utilizara casilla por defecto
			if (!emails[i].UseAsDefault) {
				addOption(emails[i], $listboxAuthenticationErrorConnection);
				addOption(emails[i], $listboxMailInactivityDetectedConnection);
			}
		}

		selectOptions($hiddenAuthenticationErrorConnection, $listboxAuthenticationErrorConnection);
		selectOptions($hiddenMailInactivityDetectedConnection, $listboxMailInactivityDetectedConnection);
	}

	$listboxAuthenticationErrorConnection.multiselect({ multiple: false, noneSelectedText: "Por defecto (actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxAuthenticationErrorConnection.change(function () {
		var value = $listboxAuthenticationErrorConnection.val();
		$hiddenAuthenticationErrorConnection.val('');
		if (value != null) {
			$hiddenAuthenticationErrorConnection.val(value);
		}

	});

	$listboxMailInactivityDetectedConnection.multiselect({ multiple: false, noneSelectedText: "Por defecto(actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxMailInactivityDetectedConnection.change(function () {
		var value = $listboxMailInactivityDetectedConnection.val();
		$hiddenMailInactivityDetectedConnection.val('');
		if (value != null) {
			$hiddenMailInactivityDetectedConnection.val(value);
		}

	});

	$textboxMailEditSubject = $('input[type=text][id$=textboxMailEditSubject]');
	$messageMailEditSubjectFields = $('#messageMailEditSubjectFields');
	CallValidationFields($textboxMailEditSubject, $messageMailEditSubjectFields);

	$dropdownlistMailSignatureBehaviour = $('#dropdownlistMailSignatureBehaviour');
	$divMailSignature = $('#divMailSignature');
	$textboxMailSignature = $('#textboxMailSignature', $divMailSignature);
	$textboxMailSignature.cleditor(cleditorOptions);

	$dropdownlistMailSignatureBehaviour.change(function () {
		var type = parseInt($dropdownlistMailSignatureBehaviour.val(), 10);
		if (type != 0) {
			$divMailSignature.show();

			var editor = $textboxMailSignature.cleditor()[0];
			if (typeof (editor.refreshed) == 'undefined') {
				editor.refresh();
				editor.refreshed = true;
			}
		}
		else {
			$divMailSignature.hide();
		}
	}).trigger('change');

	// --> Esconder o mostrar dominios habilitados para cc/cco dependiendo si está habilitado
	$checkboxAnswerOutsideDomainAvailable = $('input[type=checkbox][id$=checkboxAnswerOutsideDomainAvailable]');
	$trAvailableDomains = $('#trAvailableDomains');
	$textboxAvailableDomains = $('textarea[id$=textboxAvailableDomains]', $trAvailableDomains);
	$checkboxAnswerOutsideDomainAvailable.change(function () {
		if ($checkboxAnswerOutsideDomainAvailable.is(':checked')) {
			$trAvailableDomains.hide();
		}
		else {
			$trAvailableDomains.show();
		}
	}).trigger('change');

	var $hiddenMailTab = $('input[type=hidden][id$=hiddenMailTab]');

	var $tabsMail = $('#tabsMail');
	$tabsMail.tabs({
		activate: function (tabs, page) {
			var $divTab;
			if ((page.newPanel instanceof jQuery)) {
				$divTab = page.newPanel;
			}
			else {
				$divTab = $(page.newPanel.selector);
			}
			var tabId = $divTab.get(0).id;

			$hiddenMailTab.val(tabId);

			if (history.pushState) {
				history.pushState(null, null, '#' + tabId);
			}
			else {
				location.hash = '#' + tabId;
			}

			if (tabId == 'divMailTabOutbound') {
				var editor = $textboxMailSignature.cleditor()[0];
				editor.refresh();
			}
			else if (tabId == 'divNotifications') {
				var editor = $textboxMailAuthenticationErrorEmailTemplate.cleditor()[0];
				editor.refresh();
				editor = $textboxMailInactivityDetectedEmailTemplate.cleditor()[0];
				editor.refresh();
			}
		}
	});

	if (typeof (WebForm_OnSubmit) == 'function') {
		oldWebFormOnSubmit = WebForm_OnSubmit;
		WebForm_OnSubmit = function () {
			var success = oldWebFormOnSubmit();
			if (!success && typeof (Page_Validators) != 'undefined') {
				for (var i = 0; i < Page_Validators.length; i++) {
					var validator = Page_Validators[i];
					if (!validator.isvalid) {
						var $validator = $(validator);
						var $parent = $validator.parents('.ui-tabs-panel');
						if ($parent.length > 0)
							$tabsMail.tabs('select', $parent.attr('id'));
						break;
					}
				}
			}

			return success;
		};
	}

	var $checkboxEmailAcceptedTypes = $('input[type=checkbox][id*=checkboxEmailAcceptedType]');
	var $dropdownlistEmailDefaultExtension = $('#dropdownlistEmailDefaultExtension');

	var $options = $('option', $dropdownlistEmailDefaultExtension);
	var $optionNoAcceptedTypeSelected = $('<option></option>');
	$optionNoAcceptedTypeSelected.attr('value', '');
	$optionNoAcceptedTypeSelected.text($.i18n("configuration-servicesmail-select_file"));

	if ($options.length == 0) {
		$dropdownlistEmailDefaultExtension.append($optionNoAcceptedTypeSelected);
		$dropdownlistEmailDefaultExtension.attr('disabled', 'disabled');
	}

	$checkboxEmailAcceptedTypes.change(function () {
		var $this = $(this);
		var id = $this.attr('id');
		var $parent = $this.parent();
		if ($parent.hasClass('switch')) {
			$parent = $parent.parent();
		}
		var attachmentType = $parent.attr('attachmentType');
		var $label = $('label[for=' + id + ']', $parent.parent());
		var currentValue = $dropdownlistEmailDefaultExtension.val();

		if ($this.is(':checked')) {
			var $option = $('<option></option>');
			$option.attr('value', attachmentType);
			$option.text($label.text());
			$option.attr('data-i18n', $label.attr('data-i18n'));
			$dropdownlistEmailDefaultExtension.append($option);

			if ($dropdownlistEmailDefaultExtension.attr('disabled') === 'disabled') {
				$dropdownlistEmailDefaultExtension.removeAttr('disabled');
				$optionNoAcceptedTypeSelected.detach();
			}
		}
		else {
			var $option = $('option[value=' + attachmentType + ']', $dropdownlistEmailDefaultExtension);
			if ($option.length > 0) {
				$option.detach();

				if (currentValue === attachmentType) {
					$dropdownlistEmailDefaultExtension.val($('option:first', $dropdownlistEmailDefaultExtension).val());
				}

				var $options = $('option', $dropdownlistEmailDefaultExtension);
				if ($options.length == 0) {
					$dropdownlistEmailDefaultExtension.append($optionNoAcceptedTypeSelected);
					$dropdownlistEmailDefaultExtension.attr('disabled', 'disabled');
				}
			}
		}
	}).trigger('change');

	var $hiddenEmailDefaultExtension = $('#hiddenEmailDefaultExtension');
	$dropdownlistEmailDefaultExtension.val($hiddenEmailDefaultExtension.val());

	$dropdownlistEmailDefaultExtension.change(function () {
		$hiddenEmailDefaultExtension.val($dropdownlistEmailDefaultExtension.val());
	});

	$checkboxEmailAllowToSendAttachments = $('input[type=checkbox][id$=checkboxEmailAllowToSendAttachments]');
	$checkboxUseEmailZip = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeZip]');
	$trEmailZipOptions = $('#trEmailZipOptions');
	$textboxEmailZipOptionsMaxSize = $('input[id$=textboxEmailZipOptionsMaxSize]');
	$textboxEmailMaxAttachments = $('input[id$=textboxEmailMaxAttachments]');

	$checkboxEmailAllowToSendAttachments.change(function () {
		var $table = $checkboxEmailAllowToSendAttachments.closest('table.uiInfoTable');
		var $trs = $('tr[rel=AllowToSendAttachments]', $table);
		if ($checkboxEmailAllowToSendAttachments.is(':checked')) {
			$trs.show();
		}
		else {
			$trs.hide();
		}
	}).trigger('change');

	$textboxEmailMaxAttachments.blur(function () {
		let value = $textboxEmailMaxAttachments.val();
		if (value.length === 0) {
			$textboxEmailMaxAttachments.val('1');
			return;
		}

		value = parseInt(value, 10);
		if (isNaN(value)) {
			$textboxEmailMaxAttachments.val('1');
			return;
		}

		if (value < 1) {
			$textboxEmailMaxAttachments.val('1');
			return;
		}

		if (value > 99) {
			$textboxEmailMaxAttachments.val('99');
			return;
		}
	}).trigger('blur');

	$checkboxUseEmailZip.change(function () {
		if ($checkboxUseEmailZip.is(':checked')) {
			$trEmailZipOptions.show();
		}
		else {
			$trEmailZipOptions.hide();
		}

	}).trigger('change');

	var $messageMailSignatureBody = $('#messageMailSignature');
	CallValidationFields($textboxMailSignature, $messageMailSignatureBody);

	$dropdownlistGrouping = $('select[id$=dropdownlistGrouping]');
	$divGroupingExcludedSenders = $('#divGroupingExcludedSenders');
	$textboxGroupingExcludedSenders = $('#textboxGroupingExcludedSenders', $divGroupingExcludedSenders);

	$dropdownlistGrouping.change(function () {
		var groupingStyle = parseInt($dropdownlistGrouping.val(), 10);
		if (groupingStyle == 2) {
			$divGroupingExcludedSenders.show();
		}
		else {
			$divGroupingExcludedSenders.hide();
		}
	}).trigger('change');

	$checkboxReceivesWebFormsMails = $('#checkboxReceivesWebFormsMails');
	$divReceivesWebFormsMails = $('#divReceivesWebFormsMails');
	$hiddenWebForms = $('#hiddenWebForms', $divReceivesWebFormsMails);
	$tableWebForms = $('#tableWebForms', $divReceivesWebFormsMails);
	$tbodyWebForms = $('tbody', $tableWebForms);
	$aWebFormsAdd = $('#aWebFormsAdd', $tableWebForms);

	$checkboxReceivesWebFormsMails.change(function () {
		if ($checkboxReceivesWebFormsMails.is(':checked'))
			$divReceivesWebFormsMails.show();
		else
			$divReceivesWebFormsMails.hide();
	}).trigger('change');

	$aWebFormsAdd.click(function () {
		var $lastTr = $('tr:last-child', $tbodyWebForms);
		var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
		var $newTr = $('<tr class="' + newTrClass + '"><td style="text-align: center"><a rel="removeparameter" title="Quitar este formulario web" data-i18n-title="configuration-servicesmail-delete_web_form"><span class="fa fa-lg fa-minus-square"></span></a></td><td><input type="text" rel="parametername" class="inputtext" maxlength="50" style="width: 90%" /></td><td><input type="text" rel="parametervalue" class="inputtext" maxlength="100" style="width: 90%" /></td></tr>');
		$tbodyWebForms.append($newTr);

		var $anchorRemove = $('a[rel=removeparameter]', $newTr);
		$anchorRemove.click(function () {
			var $tr = $(this).parent().parent();
			$tr.remove();

			var $trs = $('tr', $tbodyWebForms);
			if ($trs.length == 0) {
				$aWebFormsAdd.trigger('click');
			}
			else {
				for (var i = 0; i < $trs.length; i++) {
					var $tr = $($trs.get(i));
					$tr.removeClass('normal alternate');
					$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
				}
			}
		});
	});

	if (!$checkboxReceivesWebFormsMails.is(':checked')) {
		$aWebFormsAdd.trigger('click');
	}
	else {
		var value = $hiddenWebForms.val();
		if (value == null || value.length == 0) {
			$aWebFormsAdd.trigger('click');
		}
		else {
			var webForms = JSON.parse(value);
			for (var parameter in webForms) {
				$aWebFormsAdd.trigger('click');
				var $lastTr = $('tr:last-child', $tbodyWebForms);
				var $inputKey = $('input[rel=parametername]', $lastTr);
				var $inputValue = $('input[rel=parametervalue]', $lastTr);

				$inputKey.val(parameter);
				$inputValue.val(webForms[parameter]);
			}
		}
	}

	var $trs = $('tr', $tbodyWebForms);
	if ($trs.length == 0)
		$aWebFormsAdd.trigger('click');

	$checkboxMailSendUseCredentials = $('#checkboxMailSendUseCredentials');
	$trMailSendUseCredentials = $('#trMailSendUseCredentials');
	$textboxMailSendUser = $('#textboxMailSendUser');
	$textboxMailSendPassword = $('#textboxMailSendPassword');
	$textboxMailAddress = $('#textboxMailAddress');
	var $messageSMTPUserAndReplyUserNotTheSame = $('#messageSMTPUserAndReplyUserNotTheSame');

	$checkboxMailSendUseCredentials.change(function () {
		$trMailSendUseCredentials.toggle($checkboxMailSendUseCredentials.is(':checked'));
	}).trigger('change');

	let mailSendUserChange = function () {
		var user1;
		var type = parseInt($dropdownlistMailConnectionType.val(), 10);
		switch (type) {
			case MailServiceConfigurationConnectionTypes.ImapSmpt:
			case MailServiceConfigurationConnectionTypes.Pop3Smpt:
				user1 = $textboxMailSendUser.val().trim().toLowerCase();
				break;
			case MailServiceConfigurationConnectionTypes.EWS:
				let $textboxMailRetrieveEWSUser = $('#textboxMailRetrieveEWSUser');
				user1 = $textboxMailRetrieveEWSUser.val().trim().toLowerCase();
				break;
			case MailServiceConfigurationConnectionTypes.Gmail:
				let $textboxMailRetrieveGmailUser = $('#textboxMailRetrieveGmailUser');
				user1 = $textboxMailRetrieveGmailUser.val().trim().toLowerCase();
				break;
		}

		var user2 = $textboxMailAddress.val().trim().toLowerCase();

		if (user1.length === 0 || user2.length === 0) {
			$messageSMTPUserAndReplyUserNotTheSame.hide();
		}
		else if (user1 !== user2) {
			$messageSMTPUserAndReplyUserNotTheSame.show();
		}
		else {
			$messageSMTPUserAndReplyUserNotTheSame.hide();
		}
	};

	$textboxMailSendUser.change(mailSendUserChange).trigger('change');
	$textboxMailAddress.change(mailSendUserChange);
	
	if (typeof(editingService) !== 'undefined' && editingService) {
		$textboxMailAddress.trigger('change');
	}

	$dropdownlistMailGenerateMultipleRepliesBehaviour = $('#dropdownlistMailGenerateMultipleRepliesBehaviour');
	$messageMailGenerateMultipleRepliesBehaviourNoOtherServices = $('#messageMailGenerateMultipleRepliesBehaviourNoOtherServices');
	$panelMailGenerateMultipleRepliesBehaviour = $('#panelMailGenerateMultipleRepliesBehaviour');
	$listboxServicesForMultipleRepliesGeneration = $('#listboxServicesForMultipleRepliesGeneration');

	$dropdownlistMailGenerateMultipleRepliesBehaviour.change(function () {
		var val = parseInt($dropdownlistMailGenerateMultipleRepliesBehaviour.val(), 10);

		if (typeof (noOtherMailServices) != 'undefined' && noOtherMailServices) {
			$messageMailGenerateMultipleRepliesBehaviourNoOtherServices.toggle(val == 2 || val == 3);
		}

		if (val != 3) {
			$panelMailGenerateMultipleRepliesBehaviour.hide();
		}
		else {
			$panelMailGenerateMultipleRepliesBehaviour.show();
		}
	}).trigger('change');

	$listboxServicesForMultipleRepliesGeneration.multiselect({ multiple: true, noneSelectedText: "No ha seleccionado ningún servicio", selectedList: 4, buttonWidth: '>400' }).multiselectfilter();

	$checkboxMailRetrievePassword = $('#checkboxMailRetrievePassword');
	$labelMailRetrievePassword = $('#labelMailRetrievePassword');
	$textboxMailRetrievePassword = $('#textboxMailRetrievePassword');
	$checkboxMailRetrievePassword.change(function () {
		if (this.checked) {
			$textboxMailRetrievePassword.removeAttr('disabled');
		}
		else {
			$textboxMailRetrievePassword.attr('disabled', 'disabled');
			$textboxMailRetrievePassword.val('');
		}
	}).trigger('change');

	$checkboxMailRetrievePOP3Password = $('#checkboxMailRetrievePOP3Password');
	$labelMailRetrievePOP3Password = $('#labelMailRetrievePOP3Password');
	$textboxMailRetrievePOP3Password = $('#textboxMailRetrievePOP3Password');
	$checkboxMailRetrievePOP3Password.change(function () {
		if (this.checked) {
			$textboxMailRetrievePOP3Password.removeAttr('disabled');
		}
		else {
			$textboxMailRetrievePOP3Password.attr('disabled', 'disabled');
			$textboxMailRetrievePOP3Password.val('');
		}
	}).trigger('change');

	$checkboxMailRetrieveEWSPassword = $('#checkboxMailRetrieveEWSPassword');
	$labelMailRetrieveEWSPassword = $('#labelMailRetrieveEWSPassword');
	$textboxMailRetrieveEWSPassword = $('#textboxMailRetrieveEWSPassword');
	$checkboxMailRetrieveEWSPassword.change(function () {
		if (this.checked) {
			$textboxMailRetrieveEWSPassword.removeAttr('disabled');
		}
		else {
			$textboxMailRetrieveEWSPassword.attr('disabled', 'disabled');
			$textboxMailRetrieveEWSPassword.val('');
		}
	}).trigger('change');

	$checkboxMailSendPassword = $('#checkboxMailSendPassword');
	$labelMailSendPassword = $('#labelMailSendPassword');
	$textboxMailSendPassword = $('#textboxMailSendPassword');
	$checkboxMailSendPassword.change(function () {
		if (this.checked) {
			$textboxMailSendPassword.removeAttr('disabled');
		}
		else {
			$textboxMailSendPassword.attr('disabled', 'disabled');
			$textboxMailSendPassword.val('');
		}
	}).trigger('change');

	var $checkboxMailUseReceivedDateInsteadOfSentDate = $('#checkboxMailUseReceivedDateInsteadOfSentDate');
	var $trMailUseReceivedDateIfSentDateIsInTheFuture = $('#trMailUseReceivedDateIfSentDateIsInTheFuture');
	var $checkboxMailUseReceivedDateIfSentDateIsInTheFuture = $('#checkboxMailUseReceivedDateIfSentDateIsInTheFuture');
	$checkboxMailUseReceivedDateInsteadOfSentDate.change(function () {
		$trMailUseReceivedDateIfSentDateIsInTheFuture.toggle(!$checkboxMailUseReceivedDateInsteadOfSentDate.is(':checked'));
	}).trigger('change');

	var $checkboxEmailRetrieveUseSSL = $('#checkboxEmailRetrieveUseSSL');
	var $trEmailRetrieveSSLConnectionType = $('#trEmailRetrieveSSLConnectionType');
	$checkboxEmailRetrieveUseSSL.change(function () {
		$trEmailRetrieveSSLConnectionType.toggle(this.checked);
	}).trigger('change');

	var $checkboxEmailRetrievePOP3UseSSL = $('#checkboxEmailRetrievePOP3UseSSL');
	var $trEmailRetrievePOP3SSLConnectionType = $('#trEmailRetrievePOP3SSLConnectionType');
	$checkboxEmailRetrievePOP3UseSSL.change(function () {
		$trEmailRetrievePOP3SSLConnectionType.toggle(this.checked);
	}).trigger('change');

	var $checkboxEmailSendUseSSL = $('#checkboxEmailSendUseSSL');
	var $trEmailSendSSLConnectionType = $('#trEmailSendSSLConnectionType');
	$checkboxEmailSendUseSSL.change(function () {
		$trEmailSendSSLConnectionType.toggle(this.checked);
	}).trigger('change');

	InitializeCasesControls();

	var param = $(document).getUrlParam('tab');
	if (param && param != '') {
		$tabsMail.tabs('select', 'div' + param);
	}
	else {
		let tab = $hiddenMailTab.val();
		if (typeof(tab) === 'string' &&
			tab.length > 0) {
			tabsMail.tabs('select', tab);
		}
	}

	InitializeSurveysControls();
}

function i18nLoaded() {
	let connectionType = parseInt($dropdownlistMailConnectionType.val(), 10);
	if (connectionType === MailServiceConfigurationConnectionTypes.Pop3Smpt) {
		AlertDialog($.i18n('configuration-servicesmail-connection_type-title'), $.i18n('configuration-servicesmail-connection_type-pop3-deprecated'), undefined, undefined, 'Warning', false);
	}
}

function ValidateWebForms(sender, e) {
	e.IsValid = true;

	if (!$checkboxReceivesWebFormsMails.is(':checked'))
		return;

	var $inputKeys = $('input[rel=parametername]', $tbodyWebForms);
	var $inputValues = $('input[rel=parametervalue]', $tbodyWebForms);

	var $sender = $(sender);
	if ($inputKeys.length == 0) {
		$sender.text($.i18n("configuration-servicesmail-enter_web_form"));
		e.IsValid = false;
		return;
	}

	var regexName = /^[a-zA-Z0-9]{3,50}$/;
	var regexMail = /\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
	for (var i = 0; i < $inputKeys.length; i++) {
		var $inputKey = $($inputKeys.get(i));
		var name = $inputKey.val();
		if (name.trim().length == 0) {
			$sender.text($.i18n("configuration-servicesmail-enter_web_form_name"));
			$inputKey.focus();
			e.IsValid = false;
			return;
		}

		if (!regexName.test(name)) {
			$sender.text($.i18n("configuration-servicesmail-key_regex"));
			$inputKey.focus();
			e.IsValid = false;
			return;
		}
		
		var $inputValue = $($inputValues.get(i));
		var mail = $inputValue.val();
		if (mail.toLowerCase() !== 'root@localhost') {
			if (!regexMail.test(mail)) {
				$sender.text($.i18n("configuration-servicesmail-invalid_mail"));
				$inputValue.focus();
				e.IsValid = false;
				return;
			}
		}

		for (var j = i + 1; j < $inputKeys.length; j++) {
			var $inputNext = $($inputKeys.get(j));
			var valueNext = $inputNext.val();

			if (valueNext == value) {
				$sender.text($.i18n("configuration-servicesmail-no_same_name"));
				$inputNext.focus();
				e.IsValid = false;
				return;
			}
		}
	}

	var parameters = {};
	
	for (var i = 0; i < $inputKeys.length; i++) {
		var $inputKey = $($inputKeys.get(i));
		var $inputValue = $($inputValues.get(i));

		var key = $inputKey.val().trim();
		var value = $inputValue.val().trim();
		parameters[key] = value;
	}

	$hiddenWebForms.val(JSON.stringify(parameters));
}

function ValidateMailAvailableDomains(sender, e) {
	if ($checkboxAnswerOutsideDomainAvailable.is(':checked')) {
		e.IsValid = true;
		return;
	}

	var value = $textboxAvailableDomains.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	var regex = /^\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
	if (!regex.test(value)) {
		e.IsValid = false;
		return;
	}

	e.IsValid = true;
}

function MailIMAPTest(autoHide) {
	if (typeof (autoHide) == 'undefined')
		autoHide = false;

	var $divMailTestInbound = $('#divMailTestIMAP');
	var $divLoading = $('div[rel=loading]', $divMailTestInbound);
	var $messageMailTestInboundResult = $('#messageMailTestIMAPResult', $divMailTestInbound);
	var $tdText = $('td.text', $messageMailTestInboundResult);
	var $textboxMailRetrieveServer = $('#textboxMailRetrieveServer');
	var $textboxMailRetrievePort = $('#textboxMailRetrievePort');
	var $textboxMailRetrieveUser = $('#textboxMailRetrieveUser');
	var $textboxMailRetrievePassword = $('#textboxMailRetrievePassword');
	var $checkboxEmailRetrieveUseSSL = $('#checkboxEmailRetrieveUseSSL');
	var $dropdownlistEmailRetrieveSSLSecureOptions = $('#dropdownlistEmailRetrieveSSLSecureOptions');

	var server = $textboxMailRetrieveServer.val();
	var port = $textboxMailRetrievePort.val();
	var user = $textboxMailRetrieveUser.val();
	var password = $textboxMailRetrievePassword.val();
	
	if (server.length == 0 || port.length == 0 || user.length == 0) {
		return;
	}

	if (!$.isNumeric(port)) {
		return;
	}

	port = parseInt(port, 10);
	if (port < 0 || port > 99999) {
		return;
	}

	var useCurrentPassword = false;
	if ($checkboxMailRetrievePassword.length > 0 && !$checkboxMailRetrievePassword.is(':checked')) {
		password = null;
		useCurrentPassword = true;
	}
	else if (password.length == 0) {
		return;
	}

	var useSSL = $checkboxEmailRetrieveUseSSL.is(':checked');
	var secureOptions = 0;
	if (useSSL) {
		secureOptions = parseInt($dropdownlistEmailRetrieveSSLSecureOptions.val(), 10);
	}

	var dataToSend = JSON.stringify({
		serviceId: (editingService ? editingServiceId : null),
		server: server,
		port: port,
		user: user,
		password: password,
		useSSL: useSSL,
		secureOptions: secureOptions,
		useCurrentPassword: useCurrentPassword
	});

	$divLoading.show();
	$messageMailTestInboundResult.hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divMailTestInbound,
		width: '400px',
		initialWidth: '400px',
		height: '240px',
		preloading: false,
		closeButton: false,
		escKey: false,
		onLoad: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: "ServicesMail.aspx/TestIMAPConnection",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					if (result.d.Success) {
						if (console)
							console.log(result.d);

						if (result.d.IsValid) {
							$tdText.text($.i18n("configuration-servicesmail-succesfull_connection"));
						}
						else {
							$tdText.text($.i18n("configuration-servicesmail-connection_error", result.d.Reason));
						}
					}
					else {
						if (console)
							console.error('Hubo un error verificando la conexión al servidor IMAP: %o', result.d.Error)
						$tdText.text($.i18n("configuration-servicesmail-verification_error"));
					}

					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				},
				error: function (jqXHR, textStatus, errorThrown) {
					if (console)
						console.error('Hubo un error verificando la conexión al servidor IMAP: %o', JSON.parse(jqXHR.responseText));

					$tdText.text($.i18n("configuration-servicesmail-verification_error"));
					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				}
			});
		}
	});
}

function MailPOP3Test(autoHide) {
	if (typeof (autoHide) == 'undefined')
		autoHide = false;

	var $divMailTestInbound = $('#divMailTestPOP3');
	var $divLoading = $('div[rel=loading]', $divMailTestInbound);
	var $messageMailTestInboundResult = $('#messageMailTestPOP3Result', $divMailTestInbound);
	var $tdText = $('td.text', $messageMailTestInboundResult);
	var $textboxMailRetrievePOP3Server = $('#textboxMailRetrievePOP3Server');
	var $textboxMailRetrievePOP3Port = $('#textboxMailRetrievePOP3Port');
	var $textboxMailRetrievePOP3User = $('#textboxMailRetrievePOP3User');
	var $textboxMailRetrievePOP3Password = $('#textboxMailRetrievePOP3Password');
	var $checkboxEMailRetrievePOP3UseSSL = $('#checkboxEMailRetrievePOP3UseSSL');
	var $dropdownlistEmailRetrievePOP3SSLSecureOptions = $('#dropdownlistEmailRetrievePOP3SSLSecureOptions')

	var server = $textboxMailRetrievePOP3Server.val();
	var port = $textboxMailRetrievePOP3Port.val();
	var user = $textboxMailRetrievePOP3User.val();
	var password = $textboxMailRetrievePOP3Password.val();

	if (server.length == 0 || port.length == 0 || user.length == 0)
		return;

	if (!$.isNumeric(port))
		return;

	port = parseInt(port, 10);
	if (port < 0 || port > 99999)
		return;

	var useCurrentPassword = false;
	if ($checkboxMailRetrievePOP3Password.length > 0 && !$checkboxMailRetrievePOP3Password.is(':checked')) {
		password = null;
		useCurrentPassword = true;
	}
	else if (password.length == 0)
		return;

	var useSSL = $checkboxEMailRetrievePOP3UseSSL.is(':checked');
	var secureOptions = 0;
	if (useSSL) {
		secureOptions = parseInt($dropdownlistEmailRetrievePOP3SSLSecureOptions.val(), 10);
	}

	var dataToSend = JSON.stringify({
		serviceId: (editingService ? editingServiceId : null),
		server: server,
		port: port,
		user: user,
		password: password,
		useSSL: useSSL,
		secureOptions: secureOptions,
		useCurrentPassword: useCurrentPassword
	});

	$divLoading.show();
	$messageMailTestInboundResult.hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divMailTestInbound,
		width: '400px',
		initialWidth: '400px',
		height: '240px',
		preloading: false,
		closeButton: false,
		escKey: false,
		onLoad: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: "ServicesMail.aspx/TestPOP3Connection",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					if (result.d.Success) {
						if (console)
							console.log(result.d);

						if (result.d.IsValid) {
							$tdText.text($.i18n("configuration-servicesmail-succesfull_connection_pop3"));
						}
						else {
							$tdText.text($.i18n("configuration-servicesmail-connection_error_pop3", result.d.Reason));
						}
					}
					else {
						if (console)
							console.error('Hubo un error verificando la conexión al servidor POP3: %o', result.d.Error)
						$tdText.text($.i18n("configuration-servicesmail-verification_error_pop3"));
					}

					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				},
				error: function (jqXHR, textStatus, errorThrown) {
					if (console)
						console.error('Hubo un error verificando la conexión al servidor POP3: %o', JSON.parse(jqXHR.responseText));

					$tdText.text($.i18n("configuration-servicesmail-verification_error_pop3"));
					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				}
			});
		}
	});
}

function MailEWSTest(autoHide) {
	if (typeof (autoHide) !== 'boolean') {
		autoHide = false;
	}

	var $divMailTestInbound = $('#divMailTestEWS');
	var $divLoading = $('div[rel=loading]', $divMailTestInbound);
	var $messageMailTestInboundResult = $('#messageMailTestEWSResult', $divMailTestInbound);
	var $tdText = $('td.text', $messageMailTestInboundResult);
	var $textboxMailRetrieveEWSServer = $('#textboxMailRetrieveEWSServer');
	var $dropdownlistMailRetrieveEWSExchangeVersion = $('#dropdownlistMailRetrieveEWSExchangeVersion');

	var server = $textboxMailRetrieveEWSServer.val();
	var version = $dropdownlistMailRetrieveEWSExchangeVersion.val();
	if (version.length === 0) {
		version = null;
	}
	else {
		version = parseInt(version, 10);
	}

	let authenticationType = parseInt($dropdownlistMailRetrieveEWSAuthenticationType.val(), 10);

	let dataToSend;
	let method;
	if (authenticationType === 1) {
		var $textboxMailRetrieveEWSUser = $('#textboxMailRetrieveEWSUser');
		var $textboxMailRetrieveEWSPassword = $('#textboxMailRetrieveEWSPassword');
		var user = $textboxMailRetrieveEWSUser.val();
		var password = $textboxMailRetrieveEWSPassword.val();

		if (server.length == 0 || user.length == 0)
			return;

		var useCurrentPassword = false;
		if ($checkboxMailRetrieveEWSPassword.length > 0 && !$checkboxMailRetrieveEWSPassword.is(':checked')) {
			password = null;
			useCurrentPassword = true;
		}
		else if (password.length == 0) {
			return;
		}

		dataToSend = JSON.stringify({ serviceId: (editingService ? editingServiceId : null), server: server, user: user, password: password, useCurrentPassword: useCurrentPassword, version: version });
		method = 'ServicesMail.aspx/TestEWSConnection';
	}
	else if (authenticationType === 2) {
		var $textboxMailRetrieveEWSOAuthEmailAddress = $('#textboxMailRetrieveEWSOAuthEmailAddress');
		var $textboxMailRetrieveEWSOAuthAppID = $('#textboxMailRetrieveEWSOAuthAppID');
		var $textboxMailRetrieveEWSOAuthClientSecret = $('#textboxMailRetrieveEWSOAuthClientSecret');
		var $textboxMailRetrieveEWSOAuthTenantID = $('#textboxMailRetrieveEWSOAuthTenantID');

		var user = $textboxMailRetrieveEWSOAuthEmailAddress.val();
		if (user.length === 0 || user.trim().length === 0) {
			return;
		}

		var appId = $textboxMailRetrieveEWSOAuthAppID.val();
		if (appId.trim().length === 0) {
			return;
		}

		var clientSecret = $textboxMailRetrieveEWSOAuthClientSecret.val();
		if (clientSecret.trim().length === 0) {
			return;
		}

		var tenantId = $textboxMailRetrieveEWSOAuthTenantID.val();
		if (tenantId.trim().length === 0) {
			return;
		}

		dataToSend = JSON.stringify({
			serviceId: (editingService ? editingServiceId : null),
			server: server,
			emailAddress: user,
			appId: appId,
			clientSecret: clientSecret,
			tenantId: tenantId,
			version: version
		});
		method = 'ServicesMail.aspx/TestEWSWithOAuthConnection';
	}
	else {
		return;
	}

	$divLoading.show();
	$messageMailTestInboundResult.hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divMailTestInbound,
		width: '600px',
		initialWidth: '600px',
		height: '300px',
		preloading: false,
		closeButton: false,
		escKey: false,
		onComplete: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: method,
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					if (result.d.Success) {
						if (console)
							console.log(result.d);

						if (result.d.IsValid) {
							$tdText.text($.i18n("configuration-servicesmail-succesfull_connection_exchange"));
						}
						else {
							$tdText.text($.i18n("configuration-servicesmail-connection_error_exchange", result.d.Reason));
						}
					}
					else {
						if (console)
							console.error('Hubo un error verificando la conexión al servidor Exchange Web Services: %o', result.d.Error)
						$tdText.text($.i18n("configuration-servicesmail-verification_error_exchange"));
					}

					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				},
				error: function (jqXHR, textStatus, errorThrown) {
					if (console)
						console.error('Hubo un error verificando la conexión al servidor Exchange Web Services: %o', JSON.parse(jqXHR.responseText));

					$tdText.text($.i18n("configuration-servicesmail-verification_error_exchange"));
					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide)
						$.colorbox.close();
					else
						$.colorbox.resize();
				}
			});
		}
	});
}

function MailSMTPTest() {
	var $divMailTestOutbound = $('#divMailTestSMTP');
	var $divLoading = $('div[rel=loading]', $divMailTestOutbound);
	var $messageMailTestOutboundResult = $('#messageMailTestSMTPResult', $divMailTestOutbound);
	var $tdText = $('td.text', $messageMailTestOutboundResult);
	var $textboxMailSendServer = $('#textboxMailSendServer');
	var $textboxMailSendPort = $('#textboxMailSendPort');
	var $textboxMailSendUser = $('#textboxMailSendUser');
	var $textboxMailSendPassword = $('#textboxMailSendPassword');
	var $checkboxEmailSendUseSSL = $('#checkboxEmailSendUseSSL');
	var $dropdownlistEmailSendSSLSecureOptions = $('#dropdownlistEmailSendSSLSecureOptions');

	var server = $textboxMailSendServer.val();
	var port = $textboxMailSendPort.val();
	var useCredentials = $checkboxMailSendUseCredentials.is(':checked');
	var user = $textboxMailSendUser.val();
	var password = $textboxMailSendPassword.val();

	if (server.length == 0 || port.length == 0 || (useCredentials && user.length == 0))
		return;

	if (!$.isNumeric(port))
		return;

	var useCurrentPassword = false;
	if (!useCredentials) {
		user = null;
		password = null;
	}
	else if ($checkboxMailSendPassword.length > 0 && !$checkboxMailSendPassword.is(':checked')) {
		password = null;
		useCurrentPassword = true;
	}
	else {
		if (password.length == 0)
			return;
	}

	port = parseInt(port, 10);
	if (port < 0 || port > 99999)
		return;

	var useSSL = $checkboxEmailSendUseSSL.is(':checked');
	var secureOptions = 0;
	if (useSSL) {
		secureOptions = parseInt($dropdownlistEmailSendSSLSecureOptions.val(), 10);
	}

	var dataToSend = JSON.stringify({
		serviceId: (editingService ? editingServiceId : null),
		server: server,
		port: port,
		useCredentials: useCredentials,
		user: user,
		password: password,
		useSSL: useSSL,
		secureOptions: secureOptions,
		useCurrentPassword: useCurrentPassword
	});

	$divLoading.show();
	$messageMailTestOutboundResult.hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divMailTestOutbound,
		width: '400px',
		initialWidth: '400px',
		height: '240px',
		preloading: false,
		closeButton: false,
		escKey: false,
		onLoad: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: "ServicesMail.aspx/TestSMTPConnection",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					if (result.d.Success) {
						if (result.d.IsValid)
							$tdText.text($.i18n("configuration-servicesmail-succesfull_connection_smtp"));
						else
							$tdText.text($.i18n("configuration-servicesmail-connection_error_smtp", result.d.Reason));
					}
					else {
						$tdText.text($.i18n("configuration-servicesmail-verification_error_smtp"));
					}

					$messageMailTestOutboundResult.show();
					$divLoading.hide();

					$.colorbox.resize();
				},
				error: function () {
					$tdText.text($.i18n("configuration-servicesmail-verification_error_smtp"));
					$messageMailTestOutboundResult.show();
					$divLoading.hide();

					$.colorbox.resize();
				}
			});
		}
	});
}

function ValidateEmailMaxAttachments(sender, e) {
	e.IsValid = true;

	if (!$checkboxEmailAllowToSendAttachments.is(':checked'))
		return;

	var value = $textboxEmailMaxAttachments.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(value)) {
		e.IsValid = false;
		return;
	}

	value = parseInt(value, 10);

	if (value < 1) {
		e.IsValid = false;
		return;
	}

	if (value > 99) {
		e.IsValid = false;
		return;
	}
}

function ValidateEmailMaxSizeAttachment(sender, e) {
	e.IsValid = true;

	if (!$checkboxEmailAllowToSendAttachments.is(':checked'))
		return;

	var $textbox = $('input[id$=textboxEmailMaxSizeAttachment]');
	var value = $textbox.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(value)) {
		e.IsValid = false;
		return;
	}

	value = parseInt(value, 10);

	if (value < 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateEmailMultimediaOptions(sender, e) {
	e.IsValid = true;

	if (!$checkboxEmailAllowToSendAttachments.is(':checked'))
		return;

	var $checkboxEmailAcceptedTypeImages = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeImages]');
	var $checkboxEmailAcceptedTypeText = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeText]');
	var $checkboxEmailAcceptedTypeAudio = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeAudio]');
	var $checkboxEmailAcceptedTypePDF = $('input[type=checkbox][id$=checkboxEmailAcceptedTypePDF]');
	var $checkboxEmailAcceptedTypeWord = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeWord]');
	var $checkboxEmailAcceptedTypeExcel = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeExcel]');
	var $checkboxEmailAcceptedTypePPT = $('input[type=checkbox][id$=checkboxEmailAcceptedTypePPT]');
	var $checkboxEmailAcceptedTypeZip = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeZip]');
	var $checkboxEmailAcceptedTypeAllFiles = $('input[type=checkbox][id$=checkboxEmailAcceptedTypeAllFiles]');

	if (!$checkboxEmailAcceptedTypeImages.is(':checked') &&
		 !$checkboxEmailAcceptedTypeText.is(':checked') &&
		 !$checkboxEmailAcceptedTypeAudio.is(':checked') &&
		 !$checkboxEmailAcceptedTypePDF.is(':checked') &&
		 !$checkboxEmailAcceptedTypeWord.is(':checked') &&
		 !$checkboxEmailAcceptedTypeExcel.is(':checked') &&
		 !$checkboxEmailAcceptedTypePPT.is(':checked') &&
		 !$checkboxEmailAcceptedTypeZip.is(':checked') &&
		 !$checkboxEmailAcceptedTypeAllFiles.is(':checked')) {
		e.IsValid = false;
		return;
	}
}

function ValidateEmailZipOptions(sender, e) {
	e.IsValid = true;

	if (!$checkboxEmailAllowToSendAttachments.is(':checked'))
		return;

	if (!$checkboxUseEmailZip.is(':checked'))
		return;

	var size = $textboxEmailZipOptionsMaxSize.val();
	if (size.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(size)) {
		e.IsValid = false;
		return;
	}

	if (parseInt(size, 10) <= 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateGroupingExcludedSenders(sender, e) {
	e.IsValid = true;

	var groupingStyle = parseInt($dropdownlistGrouping.val(), 10);
	if (groupingStyle != 2)
		return;

	var value = $textboxGroupingExcludedSenders.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	var regex = /^((\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*([,])*)*$/;
	if (!regex.test(value)) {
		e.IsValid = false;
		return;
	}

	e.IsValid = true;
}

function ValidateServicesForMultipleRepliesGeneration(sender, e) {
	e.IsValid = true;
	
	var behaviour = parseInt($dropdownlistMailGenerateMultipleRepliesBehaviour.val(), 10);
	if (behaviour != 3)
		return;

	if (typeof (noOtherMailServices) != 'undefined' && noOtherMailServices)
		return;

	var selection = $listboxServicesForMultipleRepliesGeneration.multiselect('getChecked');
	if (selection.length == 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateGenerateMultipleRepliesBehaviour(sender, e) {
	e.IsValid = true;

	var behaviour = parseInt($dropdownlistMailGenerateMultipleRepliesBehaviour.val(), 10);
	if (behaviour == 0 || behaviour == 1)
		return;

	if (typeof (noOtherMailServices) != 'undefined' && noOtherMailServices) {
		e.IsValid = false;
		return;
	}
}

function ValidateMailSendPassword(sender, e) {
	e.IsValid = true;

	if (!$checkboxMailSendUseCredentials.is(':checked'))
		return;

	if ($checkboxMailSendPassword.length > 0 && !$checkboxMailSendPassword.is(':checked'))
		return;

	var value = $textboxMailSendPassword.val().trim();

	if (value.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateIMAP(sender, e) {
	var type = parseInt($dropdownlistMailConnectionType.val());
	if (type !== MailServiceConfigurationConnectionTypes.ImapSmpt) {
		e.IsValid = true;
		return;
	}

	var $textboxMailRetrieveServer = $('#textboxMailRetrieveServer');
	var server = $textboxMailRetrieveServer.val();
	var serverRegex = /(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$)|(^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$)/;
	if (server.length === 0 || !serverRegex.test(server)) {
		e.IsValid = false;
		return;
	}

	var $textboxMailRetrievePort = $('#textboxMailRetrievePort');
	var port = $textboxMailRetrievePort.val();
	if (port.length === 0 || !$.isNumeric(port) || parseInt(port) <= 0) {
		e.IsValid = false;
		return;
	}

	var $textboxMailRetrieveUser = $('#textboxMailRetrieveUser');
	var user = $textboxMailRetrieveUser.val();
	if (user.length === 0 || user.trim().length === 0) {
		e.IsValid = false;
		return;
	}

	if ($checkboxMailRetrievePassword.length === 0 || $checkboxMailRetrievePassword.is(':checked')) {
		var password = $textboxMailRetrievePassword.val();

		if (password.length === 0 || password.trim().length === 0) {
			e.IsValid = false;
			return;
		}
	}
}

function ValidatePOP3(sender, e) {
	var type = parseInt($dropdownlistMailConnectionType.val());
	if (type !== MailServiceConfigurationConnectionTypes.Pop3Smpt) {
		e.IsValid = true;
		return;
	}

	var $textboxMailRetrievePOP3Server = $('#textboxMailRetrievePOP3Server');
	var server = $textboxMailRetrievePOP3Server.val();
	var serverRegex = /(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$)|(^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$)/;
	if (server.length === 0 || !serverRegex.test(server)) {
		e.IsValid = false;
		return;
	}

	var $textboxMailRetrievePOP3Port = $('#textboxMailRetrievePOP3Port');
	var port = $textboxMailRetrievePOP3Port.val();
	if (port.length === 0 || !$.isNumeric(port) || parseInt(port) <= 0) {
		e.IsValid = false;
		return;
	}

	var $textboxMailRetrievePOP3User = $('#textboxMailRetrievePOP3User');
	var user = $textboxMailRetrievePOP3User.val();
	if (user.length === 0 || user.trim().length === 0) {
		e.IsValid = false;
		return;
	}

	if ($checkboxMailRetrievePOP3Password.length === 0 || $checkboxMailRetrievePOP3Password.is(':checked')) {
		var password = $textboxMailRetrievePOP3Password.val();

		if (password.length === 0 || password.trim().length === 0) {
			e.IsValid = false;
			return;
		}
	}
}

function ValidateEWS(sender, e) {
	var type = parseInt($dropdownlistMailConnectionType.val());
	if (type !== MailServiceConfigurationConnectionTypes.EWS) {
		e.IsValid = true;
		return;
	}

	var $textboxMailRetrieveEWSServer = $('#textboxMailRetrieveEWSServer');
	var server = $textboxMailRetrieveEWSServer.val();
	if (server.length === 0 || !server.trim().length === 0) {
		e.IsValid = false;
		return;
	}

	let authenticationType = parseInt($dropdownlistMailRetrieveEWSAuthenticationType.val(), 10);
	if (authenticationType === 1) {
		var $textboxMailRetrieveEWSUser = $('#textboxMailRetrieveEWSUser');
		var user = $textboxMailRetrieveEWSUser.val();
		if (user.length === 0 || user.trim().length === 0) {
			e.IsValid = false;
			return;
		}

		if ($checkboxMailRetrieveEWSPassword.length === 0 || $checkboxMailRetrieveEWSPassword.is(':checked')) {
			var password = $textboxMailRetrieveEWSPassword.val();

			if (password.length === 0 || password.trim().length === 0) {
				e.IsValid = false;
				return;
			}
		}
	}
	else if (authenticationType === 2) {
		var $textboxMailRetrieveEWSOAuthEmailAddress = $('#textboxMailRetrieveEWSOAuthEmailAddress');
		var $textboxMailRetrieveEWSOAuthAppID = $('#textboxMailRetrieveEWSOAuthAppID');
		var $textboxMailRetrieveEWSOAuthClientSecret = $('#textboxMailRetrieveEWSOAuthClientSecret');
		var $textboxMailRetrieveEWSOAuthTenantID = $('#textboxMailRetrieveEWSOAuthTenantID');

		var user = $textboxMailRetrieveEWSOAuthEmailAddress.val();
		if (user.length === 0 || user.trim().length === 0) {
			e.IsValid = false;
			return;
		}

		var appId = $textboxMailRetrieveEWSOAuthAppID.val();
		if (appId.trim().length === 0) {
			e.IsValid = false;
			return;
		}

		var clientSecret = $textboxMailRetrieveEWSOAuthClientSecret.val();
		if (clientSecret.trim().length === 0) {
			e.IsValid = false;
			return;
		}

		var tenantId = $textboxMailRetrieveEWSOAuthTenantID.val();
		if (tenantId.trim().length === 0) {
			e.IsValid = false;
			return;
		}
	}

	var $textboxMailRetrieveEWSTimeout = $('#textboxMailRetrieveEWSTimeout');
	var timeout = $textboxMailRetrieveEWSTimeout.val();
	if (timeout.length === 0 || !$.isNumeric(timeout) || parseInt(timeout) <= 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateSMTP(sender, e) {
	var type = parseInt($dropdownlistMailConnectionType.val());
	if (type !== MailServiceConfigurationConnectionTypes.Pop3Smpt &&
		type !== MailServiceConfigurationConnectionTypes.ImapSmpt) {
		e.IsValid = true;
		return;
	}


	var $textboxMailSendServer = $('#textboxMailSendServer');
	var server = $textboxMailSendServer.val();
	var serverRegex = /(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$)|(^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$)/;
	if (server.length === 0 || !serverRegex.test(server)) {
		e.IsValid = false;
		return;
	}

	var $textboxMailSendPort = $('#textboxMailSendPort');
	var port = $textboxMailSendPort.val();
	if (port.length === 0 || !$.isNumeric(port) || parseInt(port) <= 0) {
		e.IsValid = false;
		return;
	}

	var $checkboxMailSendUseCredentials = $('#checkboxMailSendUseCredentials');
	if ($checkboxMailSendUseCredentials.is(':checked')) {
		var $textboxMailSendUser = $('#textboxMailSendUser');
		var user = $textboxMailSendUser.val();
		if (user.length === 0 || user.trim().length === 0) {
			e.IsValid = false;
			return;
		}

		if ($checkboxMailSendPassword.length === 0 || $checkboxMailSendPassword.is(':checked')) {
			var password = $textboxMailSendPassword.val();

			if (password.length === 0 || password.trim().length === 0) {
				e.IsValid = false;
				return;
			}
		}
	}
}

function ValidateGmail(sender, e) {
	var type = parseInt($dropdownlistMailConnectionType.val());
	if (type !== MailServiceConfigurationConnectionTypes.Gmail) {
		e.IsValid = true;
		return;
	}

	let $textboxMailRetrieveGmailUser = $('#textboxMailRetrieveGmailUser');
	let $textboxMailRetrieveGmailCredentials = $('#textboxMailRetrieveGmailCredentials');

	let user = $textboxMailRetrieveGmailUser.val();
	if (user.length === 0 || user.trim().length === 0) {
		e.IsValid = false;
		return;
	}

	let json = $textboxMailRetrieveGmailCredentials.val();
	if (json.length > 0) {
		try {
			let parsedJson = JSON.parse(json);
			if (typeof(parsedJson.type) !== 'string' ||
				parsedJson.type !== 'service_account') {
				e.IsValid = false;
				return;
			}

			if (typeof (parsedJson.private_key) !== 'string' ||
				parsedJson.private_key.length === 0) {
				e.IsValid = false;
				return;
			}

			if (typeof (parsedJson.client_email) !== 'string' ||
				parsedJson.client_email.length === 0) {
				e.IsValid = false;
				return;
			}
		}
		catch (error) {
			e.IsValid = false;
			return;
		}
	}

	if (!editingService) {
		if (json.length === 0) {
			e.IsValid = false;
			return;
		}
	}
	else if (currentConnectionType !== MailServiceConfigurationConnectionTypes.Gmail) {
		if (json.length === 0) {
			e.IsValid = false;
			return;
		}
	}

	e.IsValid = true;
}

function MailGmailTest(autoHide) {
	if (typeof (autoHide) !== 'boolean') {
		autoHide = false;
	}

	let $divMailTestInbound = $('#divMailTestGmail');
	var $divLoading = $('div[rel=loading]', $divMailTestInbound);
	var $messageMailTestInboundResult = $('#messageMailTestGmailResult', $divMailTestInbound);
	var $tdText = $('td.text', $messageMailTestInboundResult);

	let $textboxMailRetrieveGmailUser = $('#textboxMailRetrieveGmailUser');
	let $textboxMailRetrieveGmailCredentials = $('#textboxMailRetrieveGmailCredentials');

	let user = $textboxMailRetrieveGmailUser.val();
	if (user.length === 0 || user.trim().length === 0) {
		return;
	}

	let json = $textboxMailRetrieveGmailCredentials.val();
	if (json.length > 0) {
		try {
			let parsedJson = JSON.parse(json);
			if (typeof (parsedJson.type) !== 'string' ||
				parsedJson.type !== 'service_account') {
				return;
			}

			if (typeof (parsedJson.private_key) !== 'string' ||
				parsedJson.private_key.length === 0) {
				return;
			}

			if (typeof (parsedJson.client_email) !== 'string' ||
				parsedJson.client_email.length === 0) {
				return;
			}
		}
		catch (e) {
			return;
		}
	}

	if (!editingService) {
		if (json.length === 0) {
			return;
		}
	}
	else if (currentConnectionType !== MailServiceConfigurationConnectionTypes.Gmail) {
		if (json.length === 0) {
			return;
		}
	}

	var dataToSend = JSON.stringify({
		serviceId: (editingService ? editingServiceId : null),
		user: user,
		json: json
	});

	$divLoading.show();
	$messageMailTestInboundResult.hide();

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divMailTestInbound,
		width: '600px',
		initialWidth: '600px',
		height: '240px',
		preloading: false,
		closeButton: false,
		escKey: false,
		onLoad: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: "ServicesMail.aspx/TestGmailConnection",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (result) {
					if (result.d.Success) {
						if (console)
							console.log(result.d);

						if (result.d.IsValid) {
							$tdText.text($.i18n("configuration-servicesmail-gmail-test-success"));
						}
						else {
							$tdText.text($.i18n("configuration-servicesmail-gmail-test-failed_with_reason", result.d.Reason));
						}
					}
					else {
						$tdText.text($.i18n("configuration-servicesmail-gmail-test-failed"));
					}

					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide) {
						$.colorbox.close();
					}
					else {
						$.colorbox.resize();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.error('Hubo un error verificando la conexión al servidor Gmail: %o', JSON.parse(jqXHR.responseText));

					$tdText.text($.i18n("configuration-servicesmail-gmail-test-failed"));
					$messageMailTestInboundResult.show();
					$divLoading.hide();

					if (autoHide) {
						$.colorbox.close();
					}
					else {
						$.colorbox.resize();
					}
				}
			});
		}
	});
}