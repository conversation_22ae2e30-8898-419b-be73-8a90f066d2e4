﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Yoizen.Social.DomainModel
{
	/// <summary>
	/// Define la configuración de la cola que contiene envío de encuestas
	/// </summary>
	public class QueueSurveyConfiguration
	{
		#region Enums

		/// <summary>
		/// Enumeración con los posibles condiciones de cierre de casos para envío de encuestas
		/// </summary>
		public enum CloseCaseConditions
		{
			/// <summary>
			/// Casos cerrados por los agentes
			/// </summary>
			Agents = 1,

			/// <summary>
			/// Casos cerrados por el sistema
			/// </summary>
			System = 2,

			/// <summary>
			/// Todos
			/// </summary>
			All = 3
		}

		#endregion

		#region Constructoors

		/// <summary>
		/// Inicializa una nueva instancia de QueueSurveyConfiguration
		/// </summary>
		public QueueSurveyConfiguration()
		{
			this.SendIfNewCaseExists = true;
			this.SendIfNewCaseHasTag = false;
			this.SendIfNewCaseClosedByYflow = false;
			this.DontSendIfLastSurveyAfterMinutes = 0;
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve o establece el texto de la invitación a la encuesta
		/// </summary>
		public string Invitation { get; set; }

		/// <summary>
		/// Devuelve o establece el texto de la invitación interactiva a la encuesta
		/// </summary>
		public string InteractiveInvitation { get; set; }	
		
		/// <summary>
		/// Devuelve o establece el texto del boton a la encuesta
		/// </summary>
		public string InteractiveInvitationButtonText { get; set; }

		/// <summary>
		/// Devuelve o establece el tiempo de expiración de la invitación a la encuesta
		/// </summary>
		/// <remarks>
		/// Cero indica que nunca expira
		/// </remarks>
		public int Expiration { get; set; }

		/// <summary>
		/// Devuelve o establece el ratio de envío de encuestas (cuando es 100, se enviará a todos)
		/// </summary>
		public int SentRate { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos que deben pasar a partir del cierre del caso para que se envie la encuesta al usuario
		/// </summary>
		public int TimeToSend { get; set; }

		/// <summary>
		/// Devuelve o establece las condiciones de cierre del caso para considerar el envío de la encuesta
		/// </summary>
		public CloseCaseConditions CloseCaseCondition { get; set; }

		/// <summary>
		/// Devuelve o establece las etiquetas que deberá tener el caso para considerar el envío de la encuesta
		/// </summary>
		public int[] Tags { get; set; }

		/// <summary>
		/// Devuelve o establece los grupos de etiquetas que deberá tener el caso para considerar el envío de la encuesta
		/// </summary>
		public int[] TagGroups { get; set; }

		/// <summary>
		/// Devuelve o establece la mínima cantidad de mensajes que deberá tener el caso para considerar el envío de la encuesta
		/// </summary>
		/// <remarks>
		/// <code>null</code> indica que la condición no se aplica a ningún caso
		/// </remarks>
		public int? MessagesCount { get; set; }

		/// <summary>
		/// Devuelve o establece la máxima de casos que deberá tener un perfil para considerar el envío de la encuesta
		/// </summary>
		/// <remarks>
		/// <code>0</code> indica que la condición no se aplica a ningún caso
		/// </remarks>
		public int? MaxSurveySend { get; set; }

		/// <summary>
		/// Devuelve o establece la duración mínima del caso en minutos
		/// </summary>
		/// <remarks>
		/// <code>null</code> indica que la condición no se aplica a ningún caso
		/// </remarks>
		public int? CaseDuration { get; set; }

		/// <summary>
		/// Devuelve o establece si se filtrará por casos que hayan sido o no respondidos por el agente o todos
		/// </summary>
		/// <remarks>
		/// <code>null</code> indica que la condición no se aplica a ningún caso
		/// </remarks>
		public bool? CaseWithAgentReply { get; set; }

		/// <summary>
		/// Devuelve o establece si en caso de fallar el envío por la red social que le corresponde, se enviará por correo
		/// </summary>
		public bool SendMailIfFailed { get; set; }

		/// <summary>
		/// Devuelve o establece el remitente del correo
		/// </summary>
		public string EmailFrom { get; set; }

		/// <summary>
		/// Devuelve o establece el asunto del correo
		/// </summary>
		public string EmailSubject { get; set; }

		/// <summary>
		/// Devuelve o establece la plantilla del correo
		/// </summary>
		public string EmailTemplate { get; set; }

		/// <summary>
		/// Devuelve o establece si el envío de encuestas está habilitado para mensajes de chat
		/// </summary>
		public bool EnabledForChat { get; set; }

		/// <summary>
		/// Devuelve o establece si se realizará el envío evaluando si ya hay un caso nuevo para el perfil
		/// </summary>
		public bool SendIfNewCaseExists { get; set; }

		/// <summary>
		/// Devuelve o establece si se realizará el envío evaluando si el caso tiene asignada una etiqueta
		/// </summary>
		public bool SendIfNewCaseHasTag { get; set; }

		/// <summary>
		/// Devuelve o establece si se realizará el envío evaluando si el cierre del caso nuevo fue realizado por yFlow
		/// </summary>
		public bool SendIfNewCaseClosedByYflow { get; set; }

		/// <summary>
		/// Devuelve o establece las etiquetas que deberá tener el caso para no considerar el envío de la encuesta
		/// </summary>
		public int[] TagsToIgnore { get; set; }

		/// <summary>
		/// Devuelve o establece los grupos de etiquetas que deberá tener el caso para considerar el envío de la encuesta
		/// </summary>
		public int[] TagGroupsToIgnore { get; set; }


		/// <summary>
		/// Devuelve o establece la cantidad de minutos para considerar no enviar una encuesta desde la última envíada para un perfil
		/// </summary>
		public int DontSendIfLastSurveyAfterMinutes { get; set; }

		/// <summary>
		/// Devuelve o establece la ID de la encuesta
		/// </summary>
		public Guid SurveyID { get; set; }

		/// <summary>
		/// Devuelve o establece la encuesta que se enviará
		/// </summary>
		public Survey Survey { get; set; }

		#endregion
	}
}
