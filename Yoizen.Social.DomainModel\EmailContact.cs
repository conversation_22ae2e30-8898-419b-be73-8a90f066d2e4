﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yoizen.Social.DomainModel
{
	/// <summary>
	/// Representa un contacto de email
	/// </summary>
	public class EmailContact : DomainObjectWithName<int>
	{
		#region Properties

		/// <summary>
		/// Devuelve el apellido del contacto
		/// </summary>
		public string LastName { get; set; }

		/// <summary>
		/// Devuelve el email del contacto
		/// </summary>
		public string Email { get; set; }

		/// <summary>
		/// Devuelve el id del agente que agrego el contacto
		/// </summary>
		public int AgentID { get; set; }

		#endregion

		#region Constructors
		public EmailContact(IDataRecord record)
			: base(record)
		{
			this.LastName = (string)record["LastName"];
			this.Email = (string)record["Email"];
			this.AgentID = (int)record["AgentID"];
		}

		public EmailContact()
		{
		}
		#endregion
	}
}
