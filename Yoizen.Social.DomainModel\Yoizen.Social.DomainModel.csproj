﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5FC41DA8-0AFF-48ED-83AF-4C309BA0BF20}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Yoizen.Social.DomainModel</RootNamespace>
    <AssemblyName>Yoizen.Social.DomainModel</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\Yoizen.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Yoizen.Social.DomainModel.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Yoizen.Social.DomainModel.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseEncrypted|AnyCPU'">
    <OutputPath>bin\ReleaseEncrypted\</OutputPath>
    <DefineConstants>TRACE;RELEASEENCRYPTED</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>Yoizen.Social.DomainModel.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Azure.Core">
      <HintPath>..\Dependencies\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Storage.Blobs">
      <HintPath>..\Dependencies\Azure.Storage.Blobs.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Storage.Common">
      <HintPath>..\Dependencies\Azure.Storage.Common.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper">
      <HintPath>..\Dependencies\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="FluentFTP">
      <HintPath>..\Dependencies\FluentFTP.dll</HintPath>
    </Reference>
    <Reference Include="JWT">
      <HintPath>..\Dependencies\JWT.dll</HintPath>
    </Reference>
    <Reference Include="MailKit">
      <HintPath>..\Dependencies\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices, Version=15.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Microsoft.Exchange.WebServices.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices.Auth">
      <HintPath>..\Dependencies\Microsoft.Exchange.WebServices.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client">
      <HintPath>..\Dependencies\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit">
      <HintPath>..\Dependencies\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet">
      <HintPath>..\Dependencies\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Memory.Data">
      <HintPath>..\Dependencies\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.IPNetwork">
      <HintPath>..\Dependencies\System.Net.IPNetwork.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="UAParser">
      <HintPath>..\Dependencies\UAParser.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <ProjectReference Include="..\Yoizen.Common\Yoizen.Common.csproj">
      <Project>{e50d2ee2-4fcc-4e80-8c19-b9f1933dad74}</Project>
      <Name>Yoizen.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\GlobalAssemblyInfo.cs">
      <Link>Properties\GlobalAssemblyInfo.cs</Link>
    </Compile>
    <Compile Include="AccountLink.cs" />
    <Compile Include="AgentNotifications\ChatSummary.cs" />
    <Compile Include="AgentNotifications\ShareScreenshot.cs" />
    <Compile Include="AgentNotifications\ChatSupervisedStart.cs" />
    <Compile Include="AgentNotifications\ChatSupervisedEnd.cs" />
    <Compile Include="AgentNotifications\ChatActionAttachment.cs" />
    <Compile Include="AgentNotifications\ChatActionStopTyping.cs" />
    <Compile Include="AgentNotifications\ChatActionMessage.cs" />
    <Compile Include="AgentNotifications\ChatActionTyping.cs" />
    <Compile Include="AgentNotifications\ChatNotification.cs" />
    <Compile Include="AgentNotifications\InternalChatMessage.cs" />
    <Compile Include="AgentNotifications\InternalChatSupervisorDisconnected.cs" />
    <Compile Include="AgentNotifications\WhatsappVoiceCallCandidates.cs" />
    <Compile Include="AgentNotifications\WhatsappVoiceCallTerminate.cs" />
    <Compile Include="AgentNotifications\WhatsappVoiceCallOffer.cs" />
    <Compile Include="AgentTagRequiredOptions.cs" />
    <Compile Include="BiometricMessage.cs" />
    <Compile Include="AgentNotifications\DisconnectedBySupervisor.cs" />
    <Compile Include="AgentNotifications\ChatAbandoned.cs" />
    <Compile Include="AgentNotifications\ChangeStatus.cs" />
    <Compile Include="AgentNotifications\ChatUnasignedAfterDisconnection.cs" />
    <Compile Include="AgentNotifications\MessageUnasigned.cs" />
    <Compile Include="AgentNotifications\AlertMessage.cs" />
    <Compile Include="AgentNotifications\ConversationEvent.cs" />
    <Compile Include="AgentNotifications\WhatsappMessageStatusChange.cs" />
    <Compile Include="AgentNotifications\ServiceChanged.cs" />
    <Compile Include="AgentNotifications\QueueChanged.cs" />
    <Compile Include="AgentNotifications\ReservedMessage.cs" />
    <Compile Include="AgentNotifications\NewMessageFromAssignedCase.cs" />
    <Compile Include="AgentNotifications\WhenPendingCaseAssigned.cs" />
    <Compile Include="AgentNotifications\WhenPendingCaseRemoved.cs" />
    <Compile Include="AgentNotifications\WhenPendingCaseClosed.cs" />
    <Compile Include="AgentNotificationTypes.cs" />
    <Compile Include="AgentNotification.cs" />
    <Compile Include="Agent.cs" />
    <Compile Include="AgentConnectionResult.cs" />
    <Compile Include="AgentGroup.cs" />
    <Compile Include="AgentLog.cs" />
    <Compile Include="AuthenticationTypes.cs" />
    <Compile Include="CaseClosingSources.cs" />
    <Compile Include="Call.cs" />
    <Compile Include="CallStatuses.cs" />
    <Compile Include="CallEndResponsibles.cs" />
    <Compile Include="CallLogTypes.cs" />
    <Compile Include="CallLog.cs" />
    <Compile Include="CaseMessagePending.cs" />
    <Compile Include="CaseOpenStatuses.cs" />
    <Compile Include="CaseReopening.cs" />
    <Compile Include="ChatFinishReasons.cs" />
    <Compile Include="CaseStartedBySouces.cs" />
    <Compile Include="ChatStructuredMessageTypes.cs" />
    <Compile Include="DashboardSettings\WhatsAppDashboard.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetWhatsApp.cs" />
    <Compile Include="DTO\CallDTO.cs" />
    <Compile Include="EmailContact.cs" />
    <Compile Include="JSON\SocialUserPreferenceConverter.cs" />
    <Compile Include="MetaEventAssociation.cs" />
    <Compile Include="SocialUserPreference.cs" />
    <Compile Include="SocialUserPreferenceTypes.cs" />
    <Compile Include="MetaEventTypes.cs" />
    <Compile Include="Stats\AgentCallsStats.cs" />
    <Compile Include="Status\ContingencyBot.cs" />
    <Compile Include="Whatsapp\HSMTemplateAuthCodeButtonTypes.cs" />
    <Compile Include="YFlowInstances.cs" />
    <Compile Include="ConsumptionNotification.cs" />
    <Compile Include="CubiqSettings\BearerSettings.cs" />
    <Compile Include="CubiqSettings\Customer.cs" />
    <Compile Include="CubiqSettings\CustomerRequest.cs" />
    <Compile Include="CubiqSettings\CustomerRoot.cs" />
    <Compile Include="CubiqSettings\IssueCustomer.cs" />
    <Compile Include="CubiqSettings\Issue.cs" />
    <Compile Include="CubiqSettings\IssuesRequest.cs" />
    <Compile Include="CubiqSettings\IssuesRoot.cs" />
    <Compile Include="CubiqSettings\Links.cs" />
    <Compile Include="CubiqSettings\LoginRequest.cs" />
    <Compile Include="CubiqSettings\LoginResponse.cs" />
    <Compile Include="CubiqSettings\MediaServer.cs" />
    <Compile Include="CubiqSettings\Operator.cs" />
    <Compile Include="CubiqSettings\OperatorRequest.cs" />
    <Compile Include="CubiqSettings\OperatorRoot.cs" />
    <Compile Include="CubiqSettings\Projects.cs" />
    <Compile Include="CubiqSettings\ProjectsRoot.cs" />
    <Compile Include="CubiqSettings\Records.cs" />
    <Compile Include="CubiqSettings\Timeline.cs" />
    <Compile Include="DashboardSettings\Dashboard.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetCases.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetQueues.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetAgents.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetBase.cs" />
    <Compile Include="DashboardSettings\DashboardWidgetTypes.cs" />
    <Compile Include="DownloadedAudioVideoContents.cs" />
    <Compile Include="DownloadedFileContents.cs" />
    <Compile Include="DTO\CaseTimeInfoDTO.cs" />
    <Compile Include="DTO\YSmart\DataWrapperList.cs" />
    <Compile Include="DTO\YSmart\Enums\TypeChatSummary.cs" />
    <Compile Include="DTO\YSmart\ProjectsDTO.cs" />
    <Compile Include="DTO\YSmart\ProjectService.cs" />
    <Compile Include="DTO\YSmart\Requests\ProfileRequest.cs" />
    <Compile Include="DTO\YSmart\Requests\RequestCaseInfo.cs" />
    <Compile Include="DTO\YSmart\Requests\RequestChatSummary.cs" />
    <Compile Include="DTO\YSmart\Requests\RequestMessage.cs" />
    <Compile Include="Events\DomainObjectSyncEvent.cs" />
    <Compile Include="Events\EventBase.cs" />
    <Compile Include="Events\EventJsonConverter.cs" />
    <Compile Include="Events\EventTypes.cs" />
    <Compile Include="Filters\FilterActionKeyItemConfig.cs" />
    <Compile Include="Filters\FilterActionConfigField.cs" />
    <Compile Include="Filters\FilterActionInvokeResults.cs" />
    <Compile Include="Filters\FilterActionAssemblyException.cs" />
    <Compile Include="Filters\IFilterActions.cs" />
    <Compile Include="GeoCoordinate.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="DTO\AgentQueuesExportHelper.cs" />
    <Compile Include="Historical\DailyTask.cs" />
    <Compile Include="HttpRequestSettings.cs" />
    <Compile Include="ISocialServiceTypeCreator.cs" />
    <Compile Include="JSON\CaseInsensitiveDictionaryConverter.cs" />
    <Compile Include="JSON\ChatMessageConverter.cs" />
    <Compile Include="JSON\ChatConverter.cs" />
    <Compile Include="JSON\AttachmentConverter.cs" />
    <Compile Include="JSON\BotConversationMessageConverter.cs" />
    <Compile Include="JSON\DomainObjectNameInfoConverter.cs" />
    <Compile Include="JSON\GeoCoordinateConverter.cs" />
    <Compile Include="JSON\ServiceSettingsContractResolver.cs" />
    <Compile Include="JSON\SocialUserProfileConverter.cs" />
    <Compile Include="JSON\SocialUserConverter.cs" />
    <Compile Include="JSON\TagAgentConverter.cs" />
    <Compile Include="JSON\AuxReasonConverter.cs" />
    <Compile Include="JSON\TagJsonWriter.cs" />
    <Compile Include="Localizations.cs" />
    <Compile Include="MetricsManager.cs" />
    <Compile Include="Metrics\MetricsAdapter.cs" />
    <Compile Include="Notifications\ScreenshotShared.cs" />
    <Compile Include="Reports\DailyReportView.cs" />
    <Compile Include="Reports\Export\CasesTimesExport.cs" />
    <Compile Include="Reports\Export\CallsExport.cs" />
    <Compile Include="Reports\Export\ExportFormats.cs" />
    <Compile Include="Reports\Export\ExportPeriodicity.cs" />
    <Compile Include="AutomaticActions.cs" />
    <Compile Include="AutomaticActionsTypes.cs" />
    <Compile Include="AutomaticActionsMethods.cs" />
    <Compile Include="Reports\Export\AgentQueuesExport.cs" />
    <Compile Include="Reports\Export\RTAgentsExport.cs" />
    <Compile Include="Reports\Export\VideoCallsExport.cs" />
    <Compile Include="Reports\Export\UsersLogWithoutParametersExport.cs" />
    <Compile Include="Reports\RTAgentsModels\AgentInfoModel.cs" />
    <Compile Include="Reports\RTAgentsModels\StatsInfo.cs" />
    <Compile Include="Reports\RTAgentsModels\StatusInfo.cs" />
    <Compile Include="RequiredTagging.cs" />
    <Compile Include="Responses\GenericResponse.cs" />
    <Compile Include="Responses\SummaryResponse.cs" />
    <Compile Include="Settings\KeycloakAuthSettings.cs" />
    <Compile Include="Settings\MetaApp\MetaAppSettings.cs" />
    <Compile Include="Settings\RestChatSettings.cs" />
    <Compile Include="Settings\RealTimeSettings.cs" />
    <Compile Include="Settings\YUsageSettings.cs" />
    <Compile Include="Settings\YSmartSettings.cs" />
    <Compile Include="Stats\AgentCasesStats.cs" />
    <Compile Include="TaskLog.cs" />
    <Compile Include="NameFormatter.cs" />
    <Compile Include="Notifications\InternalChatAgentClosed.cs" />
    <Compile Include="Notifications\InternalChatMessage.cs" />
    <Compile Include="Notifications\InternalChatAgentDisconnected.cs" />
    <Compile Include="Notifications\TaskProgress.cs" />
    <Compile Include="PublicMessageServiceBusOptions.cs" />
    <Compile Include="Reports\Export\QueueExport.cs" />
    <Compile Include="Reports\Export\WhatsappHSMWithoutCase.cs" />
    <Compile Include="ServerTimingDurationComponent.cs" />
    <Compile Include="TagGroup.cs" />
    <Compile Include="Reports\Export\MessagesSegmentsExport.cs" />
    <Compile Include="Reports\Export\DoNotCallListExport.cs" />
    <Compile Include="Reports\Export\TesterListExport.cs" />
    <Compile Include="Reports\Export\ReportScheduled.cs" />
    <Compile Include="RequestOriginators.cs" />
    <Compile Include="ServiceSettings\GoogleBusinessSettings.cs" />
    <Compile Include="ServiceSettings\GoogleRBMAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\GoogleRBMSettings.cs" />
    <Compile Include="Settings\GatewayIntegration\GatewayIntegrationActionsToExecute.cs" />
    <Compile Include="Settings\GatewayIntegration\GatewayIntegrationCCasSConfiguration.cs" />
    <Compile Include="Settings\GatewayIntegration\GatewayIntegrationConfiguration.cs" />
    <Compile Include="Settings\GatewayIntegration\GatewayIntegrationTypes.cs" />
    <Compile Include="Settings\GatewayIntegration\GatewayIntegrationConfigurationJavaScriptConverter.cs" />
    <Compile Include="Settings\GoogleRBMSettings.cs" />
    <Compile Include="ServiceSettings\AppleMessagingSettings.cs" />
    <Compile Include="Settings\GatewaySettings.cs" />
    <Compile Include="Settings\Integration.cs" />
    <Compile Include="Settings\IntegrationConditions\IntegrationCondition.cs" />
    <Compile Include="Settings\StorageSettings.cs" />
    <Compile Include="Settings\WFMSettings.cs" />
    <Compile Include="Settings\AppleMessagingSettings.cs" />
    <Compile Include="ShortGuid.cs" />
    <Compile Include="SignatureMessage.cs" />
    <Compile Include="AccountLinkingMessage.cs" />
    <Compile Include="PaymentMessage.cs" />
    <Compile Include="ReplySources.cs" />
    <Compile Include="Reports\Export\CasesReopeningsExport.cs" />
    <Compile Include="Reports\Export\ChatsMessagesExport.cs" />
    <Compile Include="Reports\Export\MessagesTransfersExport.cs" />
    <Compile Include="Reports\Export\WhatsappHSMRequestTasksExport.cs" />
    <Compile Include="ServiceSettings\GooglePlaySettings.cs" />
    <Compile Include="ServiceSettings\VideoCallSettings.cs" />
    <Compile Include="StorageManager.cs" />
    <Compile Include="SurveyTypes.cs" />
    <Compile Include="Settings\AutomaticExportSettings.cs" />
    <Compile Include="Settings\AutomaticExportByReportSettings.cs" />
    <Compile Include="Settings\SFTPConnectionSettings.cs" />
    <Compile Include="Settings\UploadFileConnectionSettings.cs" />
    <Compile Include="Settings\FTPConnectionSettings.cs" />
    <Compile Include="MessageTransfer.cs" />
    <Compile Include="SocialUserParametersByService.cs" />
    <Compile Include="SocialUserServiceParameters.cs" />
    <Compile Include="Status\ACDStatus.cs" />
    <Compile Include="Status\ServiceStatus.cs" />
    <Compile Include="TaggedBySources.cs" />
    <Compile Include="ExtendedProfileBusinessCodeField.cs" />
    <Compile Include="ExternalIntegrationSecurity.cs" />
    <Compile Include="Historical\BaseJavaScriptConverter.cs" />
    <Compile Include="Historical\DailyAdherence.cs" />
    <Compile Include="Historical\DailySurvey.cs" />
    <Compile Include="Historical\UserSession.cs" />
    <Compile Include="HistSessionsAgentsCasesMessages.cs" />
    <Compile Include="IntegrationChatActions.cs" />
    <Compile Include="JSON\PendingBasicInfoConverter.cs" />
    <Compile Include="JSON\TemplateConverter.cs" />
    <Compile Include="Notifications\AgentRequiresHelp.cs" />
    <Compile Include="Notifications\AnnoyingUser.cs" />
    <Compile Include="Notifications\MessageRequiresAuthorization.cs" />
    <Compile Include="Notifications\ServiceCrashed.cs" />
    <Compile Include="Notifications\DeliveryFailed.cs" />
    <Compile Include="Notifications\ServiceLevel.cs" />
    <Compile Include="Notifications\MessageFiltered.cs" />
    <Compile Include="PendingResponseFromCustomerByOrigins.cs" />
    <Compile Include="Reports\Export\AdherenceDetailedExport.cs" />
    <Compile Include="Reports\Export\ChatMessagesExport.cs" />
    <Compile Include="Reports\Export\AdherenceConsolidatedExport.cs" />
    <Compile Include="Reports\Export\DetailedBySurveyExport.cs" />
    <Compile Include="Reports\Export\ReportExportResults.cs" />
    <Compile Include="Reports\Export\ReportExportStatus.cs" />
    <Compile Include="Reports\Export\UserSessionsExport.cs" />
    <Compile Include="Reports\Export\SocialUserProfilesExport.cs" />
    <Compile Include="Reports\Export\TasksExport.cs" />
    <Compile Include="Reports\Export\WhatsappHSMTasksExport.cs" />
    <Compile Include="Reports\Export\ConsolidatedWhatsappHSMExport.cs" />
    <Compile Include="Reports\Export\DetailedWhatsappHSMExport.cs" />
    <Compile Include="ServiceSettings\IntegrationChatAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\IntegrationChatSettings.cs" />
    <Compile Include="ServiceSettings\MercadoLibreAttachmentSettingsy.cs" />
    <Compile Include="ServiceSettings\LinkedInAttachmentSettings.cs" />
    <Compile Include="ServiceSettings\YouTubeAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\YouTubeSettings.cs" />
    <Compile Include="Settings\DeliveryFailedNotificationSettings.cs" />
    <Compile Include="Settings\InternalChatSettings.cs" />
    <Compile Include="Settings\WorkingTimesSettings.cs" />
    <Compile Include="Settings\WebAgentIFrameIntegrationSettings.cs" />
    <Compile Include="QueueGroup.cs" />
    <Compile Include="StringExtensions.cs" />
    <Compile Include="SystemUpdateSettings.cs" />
    <Compile Include="Tasks\TaskParameters.cs" />
    <Compile Include="Tasks\Task.cs" />
    <Compile Include="Tasks\TaskResults.cs" />
    <Compile Include="Tasks\TaskStatuses.cs" />
    <Compile Include="Tasks\TaskTypes.cs" />
    <Compile Include="Tasks\WhatsappMassiveHSMRequest.cs" />
    <Compile Include="Tasks\ProfilesMassiveUploadTask.cs" />
    <Compile Include="Tasks\WhatsappMassiveHSMTaskWithoutCaseCreation.cs" />
    <Compile Include="Tasks\WhatsappMassiveHSMTask.cs" />
    <Compile Include="Site.cs" />
    <Compile Include="Helpers\YourlsClient.cs" />
    <Compile Include="TracingCommonProperties.cs" />
    <Compile Include="VideoCall.cs" />
    <Compile Include="VideoCallEvent.cs" />
    <Compile Include="VideoCallStatuses.cs" />
    <Compile Include="WebAgentIFrameIntegrationTypes.cs" />
    <Compile Include="ExtraHeader.cs" />
    <Compile Include="ExtendedField.cs" />
    <Compile Include="ExternalIntegration.cs" />
    <Compile Include="DiscardSources.cs" />
    <Compile Include="Filters\FilterActionWebServiceException.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorBase.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorVIM.cs" />
    <Compile Include="Filters\WebServiceActions.cs" />
    <Compile Include="Filters\WebServiceInvokeResults.cs" />
    <Compile Include="Filters\WebServiceInvokeSettings.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorMoveToQueue.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorAddParameterToCase.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorDiscard.cs" />
    <Compile Include="Filters\WebServiceActionEvaluatorAutoReply.cs" />
    <Compile Include="Historical\DailyCase.cs" />
    <Compile Include="Historical\DailyFilter.cs" />
    <Compile Include="IAgentGrupable.cs" />
    <Compile Include="IAgentSettingsManager.cs" />
    <Compile Include="IMessageDistribution.cs" />
    <Compile Include="ISocialService.cs" />
    <Compile Include="ISocialServiceOperations.cs" />
    <Compile Include="Reports\Export\BlackListExport.cs" />
    <Compile Include="Reports\Export\WhiteListExport.cs" />
    <Compile Include="Reports\Export\TagsExport.cs" />
    <Compile Include="Reports\Export\DetailedByServiceExport.cs" />
    <Compile Include="ServerIntegrationActionTypes.cs" />
    <Compile Include="IntegrationActionTypes.cs" />
    <Compile Include="ServerIntegrationTypes.cs" />
    <Compile Include="IntegrationTypes.cs" />
    <Compile Include="JSON\IdToStringConverter.cs" />
    <Compile Include="JSON\JsonTextWriterEx.cs" />
    <Compile Include="JSON\QueueConverter.cs" />
    <Compile Include="JSON\SecureStringJsonConverter.cs" />
    <Compile Include="JSON\AgentJavaScriptConverter.cs" />
    <Compile Include="JSON\PersonJavaScriptConverter.cs" />
    <Compile Include="JSON\ServiceConverter.cs" />
    <Compile Include="JSON\UserJavaScriptConverter.cs" />
    <Compile Include="JSON\FilterConditionTypesJavaScriptConverter.cs" />
    <Compile Include="JSON\DomainObjectBasicInfoConverter.cs" />
    <Compile Include="JSON\PrivatePropertiesContractResolver.cs" />
    <Compile Include="JSON\SurveyAnswerConverter.cs" />
    <Compile Include="JSON\SurveyConverter.cs" />
    <Compile Include="ChatLog.cs" />
    <Compile Include="LoginResponseInformation.cs" />
    <Compile Include="MimeTypeMap.cs" />
    <Compile Include="ObjectExtensions.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="QueuesWithDistributionValues.cs" />
    <Compile Include="Reports\Export\AgentsExport.cs" />
    <Compile Include="Reports\Export\DailyByCaseExport.cs" />
    <Compile Include="Reports\Export\SessionByAgentExport.cs" />
    <Compile Include="Reports\Export\SurveysExport.cs" />
    <Compile Include="Reports\Export\UsersExport.cs" />
    <Compile Include="Reports\Export\UsersLogExport.cs" />
    <Compile Include="ServiceConfiguration.cs" />
    <Compile Include="ServiceSettings\ChatSettings.cs" />
    <Compile Include="ServiceSettings\InstagramAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\FacebookMessengerAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\FacebookMessengerSettings.cs" />
    <Compile Include="ServiceSettings\MercadoLibreSettings.cs" />
    <Compile Include="ServiceSettings\YFlowSettings.cs" />
    <Compile Include="ServiceSettings\SkypeAttachmentsSettings.cs" />
    <Compile Include="Settings\AnnoyingEmailSettings.cs" />
    <Compile Include="Settings\BaseServiceSettings.cs" />
    <Compile Include="Settings\GoogleAuthSettings.cs" />
    <Compile Include="Settings\MercadoLibreSettings.cs" />
    <Compile Include="Settings\YFlowSettings.cs" />
    <Compile Include="Settings\FacebookMessengerSettings.cs" />
    <Compile Include="Settings\ServerIntegrationActions\ServerIntegrationActionBaseSettings.cs" />
    <Compile Include="Settings\ServerIntegrationActions\ServerIntegrationActionSendEmailSettings.cs" />
    <Compile Include="Settings\ServerIntegrationActions\ServerIntegrationActionNotifySupervisorsSettings.cs" />
    <Compile Include="Settings\ServerIntegrationActions\ServerIntegrationActionHttpRequestSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionPostMessageSettings.cs" />
    <Compile Include="Settings\SubscriptionSettings.cs" />
    <Compile Include="Settings\MaintenanceSettings.cs" />
    <Compile Include="Settings\WebAgentStateManagementSettings.cs" />
    <Compile Include="Settings\WebAgentUrlLoginSettings.cs" />
    <Compile Include="Settings\WordCloudSettings.cs" />
    <Compile Include="Settings\BitLySettings.cs" />
    <Compile Include="Settings\FilterEmailSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionAssemblySettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionMessageBoxSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionHttpRequestSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionOpenBrowserSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionExecuteSettings.cs" />
    <Compile Include="Settings\IntegrationsSettings.cs" />
    <Compile Include="Settings\IntegrationActions\IntegrationActionBaseSettings.cs" />
    <Compile Include="Settings\SurveysServiceSettings.cs" />
    <Compile Include="ISocialServiceStatus.cs" />
    <Compile Include="SR.cs" />
    <Compile Include="SurveyStatuses.cs" />
    <Compile Include="ChatLogTypes.cs" />
    <Compile Include="AgentLogTypes.cs" />
    <Compile Include="Attachment.cs" />
    <Compile Include="ChatMessageTypes.cs" />
    <Compile Include="QueueSurveyConfiguration.cs" />
    <Compile Include="ServiceSettings\SkypeSettings.cs" />
    <Compile Include="Settings\SkypeSettings.cs" />
    <Compile Include="SurveyAnswer.cs" />
    <Compile Include="Survey.cs" />
    <Compile Include="Settings\ExporterServiceSettings.cs" />
    <Compile Include="Stats\UserMessageStats.cs" />
    <Compile Include="Surveys\SurveyConfiguration.cs" />
    <Compile Include="UserConnectionInfo.cs" />
    <Compile Include="MessageSegment.cs" />
    <Compile Include="Reports\Export\ChatsExport.cs" />
    <Compile Include="ServiceLevelTypes.cs" />
    <Compile Include="ServiceLevelActions.cs" />
    <Compile Include="FontTypes.cs" />
    <Compile Include="JSON\AgentBasicInfoConverter.cs" />
    <Compile Include="JSON\GeoCoordinateJavaScriptConverter.cs" />
    <Compile Include="MailQuoteTypes.cs" />
    <Compile Include="Notification.cs" />
    <Compile Include="NotificationTypes.cs" />
    <Compile Include="Reports\Export\DetailedByAgentExport.cs" />
    <Compile Include="Reports\Export\DetailedByQueueExport.cs" />
    <Compile Include="Reports\Export\SessionsByAgentExport.cs" />
    <Compile Include="Reports\Export\SocialUsersExport.cs" />
    <Compile Include="Reports\Export\TagsByAgentExport.cs" />
    <Compile Include="Reports\Export\TagsByQueueExport.cs" />
    <Compile Include="ServiceSettings\FacebookAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\FacebookSettings.cs" />
    <Compile Include="ServiceSettings\InstagramAttachmentSettings.cs" />
    <Compile Include="ServiceSettings\LinkedInSettings.cs" />
    <Compile Include="ServiceSettings\InstagramSettings.cs" />
    <Compile Include="ServiceSettings\MailAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\MailSettings.cs" />
    <Compile Include="ServiceSettings\ServiceAttachmentTypes.cs" />
    <Compile Include="ServiceSettings\TelegramAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\SMSSettings.cs" />
    <Compile Include="ServiceSettings\TelegramSettings.cs" />
    <Compile Include="ServiceSettings\TwitterAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\TwitterSettings.cs" />
    <Compile Include="ServiceSettings\WhatsappAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\WhatsappSettings.cs" />
    <Compile Include="ServiceSettings\BaseAttachmentsSettings.cs" />
    <Compile Include="ServiceSettings\ServiceSettings.cs" />
    <Compile Include="ServiceSettings\GroupingSettings.cs" />
    <Compile Include="Settings\LinkedInSettings.cs" />
    <Compile Include="Settings\InstagramSettings.cs" />
    <Compile Include="Settings\FacebookSettings.cs" />
    <Compile Include="Settings\MailSettings.cs" />
    <Compile Include="Settings\SMSSettings.cs" />
    <Compile Include="Settings\TwitterSettings.cs" />
    <Compile Include="Settings\WhatsappSettings.cs" />
    <Compile Include="Settings\TelegramSettings.cs" />
    <Compile Include="Cache.cs" />
    <Compile Include="Case.cs" />
    <Compile Include="CaseClosingResponsibles.cs" />
    <Compile Include="ConnectionInfo.cs" />
    <Compile Include="ChatSenders.cs" />
    <Compile Include="ConnectionStatuses.cs" />
    <Compile Include="Chat.cs" />
    <Compile Include="ChatMessage.cs" />
    <Compile Include="Conversation.cs" />
    <Compile Include="AuxReason.cs" />
    <Compile Include="Country.cs" />
    <Compile Include="DataReader.cs" />
    <Compile Include="ContactReason.cs" />
    <Compile Include="Settings\BaseSettings.cs" />
    <Compile Include="Settings\ChatSettings.cs" />
    <Compile Include="Settings\CasesSettings.cs" />
    <Compile Include="Settings\EmailConnectionSettings.cs" />
    <Compile Include="Settings\EmailSettings.cs" />
    <Compile Include="Settings\CognitiveServicesSettings.cs" />
    <Compile Include="Settings\ForwardSettings.cs" />
    <Compile Include="Settings\LDAPSettings.cs" />
    <Compile Include="Settings\SocialServiceSettings.cs" />
    <Compile Include="FilterCondition.cs" />
    <Compile Include="FilterConditionCollection.cs" />
    <Compile Include="JSON\CaseConverter.cs" />
    <Compile Include="JSON\PersonBasicInfoConverter.cs" />
    <Compile Include="JSON\ConversationConverter.cs" />
    <Compile Include="JSON\DictionaryWithDomainObjectConverter.cs" />
    <Compile Include="JSON\JsonCreationConverter.cs" />
    <Compile Include="JSON\MessageArrayBasicInfoConverter.cs" />
    <Compile Include="JSON\MessageBasicInfoConverter.cs" />
    <Compile Include="JSON\FilterBasicInfoConverter.cs" />
    <Compile Include="JSON\MessageConverter.cs" />
    <Compile Include="JSON\QueueBasicInfoConverter.cs" />
    <Compile Include="JSON\QueuesWithDistributionValuesConverter.cs" />
    <Compile Include="JSON\ServiceBasicInfoConverter.cs" />
    <Compile Include="JSON\TagBasicInfoConverter.cs" />
    <Compile Include="JSON\UserBasicInfoConverter.cs" />
    <Compile Include="CaseStatuses.cs" />
    <Compile Include="CaseLog.cs" />
    <Compile Include="CaseLogTypes.cs" />
    <Compile Include="Reports\Export\ExcelHelper.cs" />
    <Compile Include="Reports\Export\MessagesExport.cs" />
    <Compile Include="Reports\Export\CasesExport.cs" />
    <Compile Include="Reports\Export\ReportExport.cs" />
    <Compile Include="Reports\ReportTypes.cs" />
    <Compile Include="ServiceTypes.cs" />
    <Compile Include="Settings\SocialUserReference.cs" />
    <Compile Include="SocialServiceTypes.cs" />
    <Compile Include="SocialUserProfile.cs" />
    <Compile Include="Stats\AgentComputedStats.cs" />
    <Compile Include="SystemActionTypes.cs" />
    <Compile Include="ChatMappingTypes.cs" />
    <Compile Include="SystemEntityTypes.cs" />
    <Compile Include="SystemStatus.cs" />
    <Compile Include="SystemSetttings.cs" />
    <Compile Include="SocialUserProfilesList.cs" />
    <Compile Include="ServiceLevel.cs" />
    <Compile Include="Template.cs" />
    <Compile Include="DomainObject.cs" />
    <Compile Include="Entities.cs" />
    <Compile Include="FilterActions.cs" />
    <Compile Include="Historical\DailyService.cs" />
    <Compile Include="Historical\DailyBase.cs" />
    <Compile Include="Historical\DailyTag.cs" />
    <Compile Include="Historical\Daily.cs" />
    <Compile Include="MessageDistributionTypes.cs" />
    <Compile Include="MessageLog.cs" />
    <Compile Include="MessageLogTypes.cs" />
    <Compile Include="Person.cs" />
    <Compile Include="Filter.cs" />
    <Compile Include="FilterConditionTypes.cs" />
    <Compile Include="Permission.cs" />
    <Compile Include="PersonTypes.cs" />
    <Compile Include="QueueSortTypes.cs" />
    <Compile Include="Reports\RealTime\Agents.cs" />
    <Compile Include="Reports\RealTime\Queues.cs" />
    <Compile Include="Reports\RealTime\Services.cs" />
    <Compile Include="Historical\Session.cs" />
    <Compile Include="Stats\AgentTimeStats.cs" />
    <Compile Include="Stats\IDataRecordExtensionMethods.cs" />
    <Compile Include="Stats\MessageLogStat.cs" />
    <Compile Include="Stats\QueuePerformanceStats.cs" />
    <Compile Include="Stats\StatBase.cs" />
    <Compile Include="Tag.cs" />
    <Compile Include="Profile.cs" />
    <Compile Include="Queue.cs" />
    <Compile Include="SocialUser.cs" />
    <Compile Include="MessageStatuses.cs" />
    <Compile Include="Message.cs" />
    <Compile Include="Service.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="User.cs" />
    <Compile Include="Stats\AgentMessageStats.cs" />
    <Compile Include="UserLog.cs" />
    <Compile Include="Whatsapp\HSMButtonPayload.cs" />
    <Compile Include="Whatsapp\InteractiveMessageFlow.cs" />
    <Compile Include="Whatsapp\InteractiveMessageProductList.cs" />
    <Compile Include="Whatsapp\InteractiveMessageProduct.cs" />
    <Compile Include="Whatsapp\InteractiveMessageList.cs" />
    <Compile Include="Whatsapp\InteractiveMessageCatalog.cs" />
    <Compile Include="Whatsapp\InteractiveMessageButton.cs" />
    <Compile Include="Whatsapp\InteractiveMessageCallPermissionRequest.cs" />
    <Compile Include="Whatsapp\InteractiveMessageText.cs" />
    <Compile Include="Whatsapp\InteractiveMessageCtaUrl.cs" />
    <Compile Include="Whatsapp\InteractiveMessageVoiceCall.cs" />
    <Compile Include="Whatsapp\InteractiveMessageFooterTypes.cs" />
    <Compile Include="Whatsapp\InteractiveMessageTypes.cs" />
    <Compile Include="Whatsapp\InteractiveMessageHeaderTypes.cs" />
    <Compile Include="Whatsapp\InteractiveMessage.cs" />
    <Compile Include="Whatsapp\Flows\Flow.cs" />
    <Compile Include="Whatsapp\Flows\FlowCategories.cs" />
    <Compile Include="Whatsapp\Flows\FlowScreen.cs" />
    <Compile Include="Whatsapp\Flows\FlowStatuses.cs" />
    <Compile Include="Whatsapp\HSMTemplateFlowParameters.cs" />
    <Compile Include="Whatsapp\HSMTypesOfAnswers.cs" />
    <Compile Include="Whatsapp\HSMTemplateSendDefinition.cs" />
    <Compile Include="Whatsapp\HSMTemplateButton.cs" />
    <Compile Include="Whatsapp\HSMTemplateButtonsTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateCallToActionUrlButtonTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateCallToActionButtonTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateFooterTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateHeaderMediaTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateHeaderTypes.cs" />
    <Compile Include="Whatsapp\HSMTemplateParameter.cs" />
    <Compile Include="Whatsapp\Movistar\HandoffContext.cs" />
    <Compile Include="Whatsapp\Movistar\HandoffMotives.cs" />
    <Compile Include="YoizenWebClient.cs">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ClassesDefinitions.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Yoizen.Social.DomainModel.ruleset" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\Build\SmartAssembly.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>