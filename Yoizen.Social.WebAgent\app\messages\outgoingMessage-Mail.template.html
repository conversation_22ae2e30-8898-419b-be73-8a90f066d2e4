<div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>{{ 'OUTGOINGMESSAGE_MAIL' | translate }}</label>
                <select class="form-control input-sm"
                        ng-model="outgoingMailCtrl.message.serviceId"
                        ng-change="outgoingMailCtrl.serviceChanged()"
                        ng-options="service.id as service.displayText for service in outgoingMailCtrl.services"
                        ng-disabled="outgoingMailCtrl.services.length == 1 && outgoingMailCtrl.isFromNormalCase">
                </select>
            </div>
        </div>
    </div>
    <div ng-if="outgoingMailCtrl.message.serviceId">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <div>
                        <label>{{ 'OUTGOINGMESSAGE_EMAIL_USER' | translate }} / </label>
                        <a href=""
                           class="btn-link"
                           ng-click="outgoingMailCtrl.showEmailContacts()">
                            <i class="fa fa-book"></i>
                            <span class="btn-text">{{'EMAIL_CONTACTS_LIST' | translate }}</span>
                        </a>
                    </div>
                    <tags-input type="email"
                           class="bootstrap"
                           use-strings="true"
                           ng-model="outgoingMailCtrl.inputMailTo"
                           spellcheck="false"
                           max-tags="1"
                           ng-blur="outgoingMailCtrl.searchMailUsers()" 
                            />
                           <auto-complete source="outgoingMailCtrl.loadUsefulEmails($query)"
                           max-results-to-show="32"
                           template="autocomplete-template-email"
                           select-first-match="false"
                           aria-multiselectable="false"></auto-complete>
                        </tags-input>
                </div>
            </div>
        </div>

        <div class="row"
             ng-if="outgoingMailCtrl.showResultsSearch">
            <div class="col-xs-12">
                <social-user-profile social-case="outgoingMailCtrl.fakeCase"
                                     view-shown-as="'div'"
                                     show-extended-info="false"
                                     show-link="false"
                                     dark-mode="false"
                                     allow-iframes="false"
                                     class="main-case-panel"
                                     is-outgoing="true">
                </social-user-profile>
            </div>
        </div>
        <div class="row"
             ng-if="!outgoingMailCtrl.profileExist && outgoingMailCtrl.showResultsSearch">
            <div class="col-xs-12">
                <div class="alert alert-info"
                     role="alert">
                    <span class="fa fa-exclamation-circle"
                          aria-hidden="true"></span>
                    <span class="sr-only">{{'INFORMATION' | translate}}</span>
                    {{ 'OUTGOINGMESSAGE_WHATSAPP_USER_NOT_FOUND' | translate }}
                </div>
            </div>
        </div>
    </div>
    <div class="box-widget-body"
         ng-if="outgoingMailCtrl.message.serviceId && outgoingMailCtrl.showResultsSearch">
        <form ng-submit="outgoingMailCtrl.answerMessage()">
            <div class="row row-eq-height margin-bottom-10">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-11 margin-top-6">
                            <span>{{'FROM' | translate}}:</span>
                        </div>
                        <div class="col-xs-12">
                            <input class="form-control input-border-radius"
                                   ng-model="outgoingMailCtrl.message.service.settings.fromEmailAddress"
                                   disabled />
                        </div>
                    </div>
                    <div class="row margin-top-5">
                        <div class="col-xs-11 margin-top-6">
                            <span>{{'TO' | translate}}:</span>
                        </div>
                        <div class="col-xs-12">
                            <input class="form-control input-border-radius"
                                   ng-model="outgoingMailCtrl.message.mailTo"
                                   disabled />
                        </div>
                    </div>
                    <div ng-if="outgoingMailCtrl.showEditCC && outgoingMailCtrl.message.useCC"
                         class="row margin-top-5">
                        <div class="col-xs-11 margin-top-6">
                            <span>{{'CC' | translate}}:</span>
                        </div>
                        <div class="col-xs-12">
                            <tags-input class="bootstrap"
                                        ng-model="outgoingMailCtrl.message.mailCC"
                                        typeahead="mail.id as mail.mailName for mail in outgoingMailCtrl.favoriteMails"
                                        typeahead-on-select="outgoingMailCtrl.selectFavMail($item, 'mailCC)"
                                        allowed-tags-pattern="^[A-Za-z0-9._%+-]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z]{2,}$"
                                        replace-spaces-with-dashes="false">
                                <auto-complete source="outgoingMailCtrl.loadUsefulEmails($query)"
                                               max-results-to-show="32"
                                               template="autocomplete-template-email"
                                               select-first-match="false"></auto-complete>
                            </tags-input>
                        </div>
                    </div>
                    <div ng-if="outgoingMailCtrl.showEditBCC && outgoingMailCtrl.message.useCCO"
                         class="row margin-top-5">
                        <div class="col-xs-11 margin-top-6">
                            <span>{{'BCC' | translate}}:</span>
                        </div>
                        <div class="col-xs-11">
                            <tags-input class="bootstrap"
                                        ng-model="outgoingMailCtrl.message.mailCCO"
                                        allowed-tags-pattern="^[A-Za-z0-9._%+-]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z]{2,}$"
                                        replace-spaces-with-dashes="false">
                                <auto-complete source="outgoingMailCtrl.loadUsefulEmails($query)"
                                               max-results-to-show="32"
                                               template="autocomplete-template-email"
                                               select-first-match="false"></auto-complete>
                            </tags-input>
                        </div>
                    </div>
                    <div class="row margin-top-5">
                        <div class="col-xs-11 margin-top-6">
                            <span>{{'SUBJECT' | translate}}:</span>
                        </div>
                        <div class="col-xs-12">
                            <input class="form-control input-border-radius"
                                   ng-model="outgoingMailCtrl.message.subject" />
                        </div>
                    </div>

                    <div class="row margin-top-5">
                        <div class="col-xs-12">
                            <button-checkbox state-model="outgoingMailCtrl.message.useCC"
                                             description="'CC'"
                                             ng-if="outgoingMailCtrl.showEditCC"></button-checkbox>
                            <button-checkbox state-model="outgoingMailCtrl.message.useCCO"
                                             description="'BCC'"
                                             ng-if="outgoingMailCtrl.showEditBCC"></button-checkbox>
                        </div>
                    </div>

                    <div class="row margin-top-5">
                        <div class="col-xs-12">
                            <textarea class="editor-mail"
                                      autoheight rows="1"
                                      autofocus
                                      ui-tinymce="outgoingMailCtrl.tinymceOptions"
                                      ng-model="outgoingMailCtrl.message.principal"
                                      required>
								</textarea>
                        </div>
                    </div>

                    <div class="buttons-footer margin-top-5">
                        <a class="btn btn-flat btn-default"
                           href=""
                           ng-click="outgoingMailCtrl.showPredefinedAnswers(true)">
                            <i class="fa fa-book"></i>
                            {{'PREDEFINED_ANSWERS' | translate}}
                        </a>
                        <a class="btn btn-flat btn-default"
                           href=""
                           ng-click="outgoingMailCtrl.attachFiles(true)"
                           ng-if="outgoingMailCtrl.allowAttachment">
                            <i class="fa fa-paperclip"></i>
                            {{'ATTACH' | translate}}
                            <span class="bold"
                                  ng-if="outgoingMailCtrl.message.principalQueue.length > 0">({{
                                outgoingMailCtrl.message.principalQueue.length }})</span>
                        </a>
                        <span class="dropdown dropup"
                              ng-if="::outgoingMailCtrl.useOldEmojiPicker">
                            <button type="button"
                                    class="btn btn-flat btn-default dropdown-toggle"
                                    data-toggle="dropdown">
                                <span class="fa fa-lg fa-smile"></span>
                            </button>
                            <ul class="dropdown-menu emoji-popup-container">
                                <emoji-picker on-select="outgoingMailCtrl.emojiClicked(item, event)"></emoji-picker>
                            </ul>
                        </span>
                        <emoji-button text="outgoingMailCtrl.message.principal"
                                      selector=".editor-mail"
                                      ng-if="::!outgoingMailCtrl.useOldEmojiPicker"></emoji-button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    
    <div ng-if="outgoingMailCtrl.showResultsSearch"
         class="box-widget-body-footer">

         <!--Si enviamos un saliente cambiando de estado-->
         <div ng-if="main.isOutgoingMessageMail">
            <button-checkbox state-model="outgoingMailCtrl.chkMarkAsPending"
                             description="'MARK_AS_PENDING'"
                             ng-if="outgoingMailCtrl.allowAgentsToMarkCasesAsPending">
            </button-checkbox>
            <div class="btn-group">
                <button type="button"
                        class="btn btn-flat btn-action dropdown-toggle"
                        data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <span>{{ 'OUTGOINGMESSAGE_SEND_AND_CLEAN' | translate }}</span>
                    <span class="caret caret-up"></span>
                </button>
                <ul class="dropdown-menu drop-up"
                    ng-class="{ 'drop-left': emailAnswerCtrl.socialCase.outgoing }">
                    <li>
                        <a ng-click="outgoingMailCtrl.send(false, true, false)">
                            <span class="bold">{{ 'SEND' | translate }}</span>
                        </a>
                    </li>
                    <li role="separator" class="divider"></li>
                    <li>
                        <a ng-click="outgoingMailCtrl.send(false, true, true)">
                            <span>{{ 'SEND_AND_CLOSE_CASE' | translate }}</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="btn-group">
                <button type="button"
                        class="btn btn-flat dropdown-toggle"
                        data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <span>{{ 'OUTGOINGMESSAGE_SEND_AND_BACK_TO_PREVIOUS_STATUS' | translate }}</span>
                    <span class="caret caret-up"></span>
                </button>
                <ul class="dropdown-menu drop-up"
                    ng-class="{ 'drop-left': emailAnswerCtrl.socialCase.outgoing }">
                    <li>
                        <a ng-click="outgoingMailCtrl.send(true, true, false)">
                            <span class="bold">{{ 'SEND' | translate }}</span>
                        </a>
                    </li>
                    <li role="separator" class="divider"></li>
                    <li>
                        <a ng-click="outgoingMailCtrl.send(true, true, true)">
                            <span>{{ 'SEND_AND_CLOSE_CASE' | translate }}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <button type="button"
                    class="btn btn-flat"
                    ng-click="outgoingMailCtrl.close()"
                    tooltip-placement="top-right"
                    title=""
                    tooltip-popup-delay="500"
                    uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS_TIP' | translate }}">
                {{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS' | translate }}
            </button>
        </div>

        <!--Si enviamos un saliente sin cambiar de estado o es desde generar saliente-->
        <div ng-if="main.agentService.isOutgoingMailNoChangeState || outgoingMailCtrl.isFromNormalCase">
            <button-checkbox state-model="outgoingMailCtrl.chkMarkAsPending"
                             description="'MARK_AS_PENDING'"
                             ng-if="outgoingMailCtrl.allowAgentsToMarkCasesAsPending">
            </button-checkbox>
            
            <!--Solo mostramos la opcion de enviar y limpiar form si es saliente NO en generar saliente-->
            <div class="btn-group" ng-if="!outgoingMailCtrl.isFromNormalCase">
                <button type="button"
                        class="btn btn-flat dropdown-toggle"
                        data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <span>{{ 'OUTGOINGMESSAGE_SEND_AND_CLEAN' | translate }}</span>
                    <span class="caret caret-up"></span>
                </button>
                <ul class="dropdown-menu drop-up"
                    ng-class="{ 'drop-left': emailAnswerCtrl.socialCase.outgoing }">
                    <li>
                        <a ng-click="outgoingMailCtrl.send(false, false, false)">
                            <span class="bold">{{ 'SEND' | translate }}</span>
                        </a>
                    </li>
                    <li role="separator" class="divider"></li>
                    <li>
                        <a ng-click="outgoingMailCtrl.send(false, false, true)">
                            <span>{{ 'SEND_AND_CLOSE_CASE' | translate }}</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="btn-group">
                <button type="button"
                        class="btn btn-flat btn-action dropdown-toggle"
                        data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    <span>{{ 'OUTGOINGMESSAGE_SEND' | translate }}</span>
                    <span class="caret caret-up"></span>
                </button>
                <ul class="dropdown-menu drop-up"
                    ng-class="{ 'drop-left': emailAnswerCtrl.socialCase.outgoing }">
                    <li>
                        <a ng-click="outgoingMailCtrl.send(true, false, false)">
                            <span class="bold">{{ 'SEND' | translate }}</span>
                        </a>
                    </li>
                    <li role="separator" class="divider"></li>
                    <li>
                        <a ng-click="outgoingMailCtrl.send(true, false, true)">
                            <span>{{ 'SEND_AND_CLOSE_CASE' | translate }}</span>
                        </a>
                    </li>
                </ul>
            </div>
            <button ng-if="!outgoingMailCtrl.isFromNormalCase"
                    type="button"
                    class="btn btn-flat"
                    ng-click="outgoingMailCtrl.closeNoChangeState()"
                    tooltip-placement="top-right"
                    title=""
                    tooltip-popup-delay="500"
                    uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_TIP' | translate }}">
                {{ 'OUTGOINGMESSAGE_CLOSE' | translate }}
            </button>
        </div>
    </div>
   
    <div ng-if="!outgoingMailCtrl.showResultsSearch"
         class="box-widget-body-footer">
        <!-- Cerrar modal y volver a estado previo-->
        <button type="button"
                class="btn btn-flat"
                ng-if="main.isOutgoingMessageMail"
                ng-click="outgoingMailCtrl.close()"
                tooltip-placement="top-right"
                title=""
                tooltip-popup-delay="500"
                uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS_TIP' | translate }}">
            {{ 'OUTGOINGMESSAGE_CLOSE_AND_BACK_TO_PREVIOUS_STATUS' | translate }}
        </button>
        <!-- Cerrar modal sin cambiar de estado-->
        <button type="button"
                class="btn btn-flat"
                ng-if="main.agentService.isOutgoingMailNoChangeState"
                ng-click="outgoingMailCtrl.closeNoChangeState()"
                tooltip-placement="top-right"
                title=""
                tooltip-popup-delay="500"
                uib-tooltip="{{ 'OUTGOINGMESSAGE_CLOSE_TIP' | translate }}">
            {{ 'OUTGOINGMESSAGE_CLOSE' | translate }}
        </button>
    </div>
</div>