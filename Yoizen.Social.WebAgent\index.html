<!doctype html>
<html dir="ltr" ng-app="socialApp" ng-cloak ng-controller="ContentWrapperController as contentWrapperCtrl" ng-class="{
    'inLogin': !contentWrapperCtrl.data.isAgentLogged,
	'has-messages-to-answer': contentWrapperCtrl.data.hasMessagesToAnswer,
	'no-messages-to-answer': !contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.agentMessages.length == 0,
	'with-selected-message': !contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isSelectedAnyMessage,
    'outgoing-message-whatsapp' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageWhatsapp,
    'outgoing-message-twitter' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageTwitter,
    'outgoing-message-facebook' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageFacebookMessenger,
    'outgoing-message-telegram' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageTelegram,
    'outgoing-message-instagram' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageInstagram,
	'outgoing-message' : contentWrapperCtrl.data.isOutgoingMessageSelected,
	'with-chat-message' : !contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isSelectedAnyMessage && contentWrapperCtrl.data.isChatMessage,
	'outgoing-message-writing' : contentWrapperCtrl.data.isOutgoingMessageSelected && contentWrapperCtrl.data.isOutgoingMessageMail && contentWrapperCtrl.data.isWritingOutgoingMessage
}">
<head>
    <meta charset="utf-8">
    <title>ySocial Web Agent</title>
    <meta http-equiv="cache-control" content="max-age=0"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT"/>
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

    <link rel="icon" href="./app/favicon.ico" type="image/x-icon"/>

    <!-- injector:css -->
    <link rel="stylesheet" href="./app/css_components/bootstrap.min.css">
    <link rel="stylesheet" href="./app/css_components/fontawesome.css">
    <link rel="stylesheet" href="./app/css_components/AdminLTE.css">
    <link rel="stylesheet" href="./app/content/main.css">
    <link rel="stylesheet" href="./app/content/fonts.css">
    <link rel="stylesheet" href="./app/css_components/angular-block-ui.css">
    <link rel="stylesheet" href="./app/css_components/angular-toastr.min.css">
    <link rel="stylesheet" href="./app/css_components/angular.panels.css">
    <link rel="stylesheet" href="./app/css_components/ng-tags-input.min.css">
    <link rel="stylesheet" href="./app/css_components/ng-tags-input.bootstrap.min.css">
    <link rel="stylesheet" href="./app/css_components/pnotify.css">
    <link rel="stylesheet" href="./app/css_components/ivh-treeview.css">
    <link rel="stylesheet" href="./app/css_components/ivh-treeview-theme-basic.css">
    <!-- endinjector -->

<!-- build:style cloudcss -->
    <!-- /build -->

<!-- build:script basic -->
    <script type="text/javascript" src="./app/js_components/jquery.min.js"></script>
<script type="text/javascript" src="./app/js_components/angular.min.js"></script><!-- /build -->
<!-- build:script basic_async [async] -->
    <script type="text/javascript" async src="./app/js_components/twemoji.min.js"></script>
<script type="text/javascript" async src="./app/js_components/zxcvbn.js"></script>
<script type="text/javascript" async src="./app/js_components/libphonenumber-max.js"></script><!-- /build -->

<script src="https://cdn.jsdelivr.net/npm/opus-media-recorder@latest/OpusMediaRecorder.umd.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/opus-media-recorder@latest/encoderWorker.umd.js" defer></script>

</head>

<body class="hold-transition layout-top-nav background-body-color"
      ng-class="contentWrapperCtrl.getExtendedClasses()">
    <header class="main-header" ng-controller="NavigationController as navigation">
        <nav class="navbar" ng-show="navigation.isAgentLogged()">
            <div class="navbar-header">
                <div class="product-logo hidden-xs">
                    <img ng-src="{{ navigation.getProductImage() }}" />
                </div>
                <div class="social">
                    <ul>
                        <li class="assigned-messages"
                            ng-class="{
                                'new-case': msg.message.case.repliesByAgents === 0 && !msg.message.read
                                }"
                            ng-repeat="msg in navigation.messagesForAgent()"
                            popover-class="navigation-messages-popover"
                            popover-enable="!msg.message.read"
                            uib-popover-template="'navbarMessagesPopover.html'" popover-placement="bottom-left"
                            popover-trigger="'mouseenter'">
                            <script type="text/ng-template" id="navbarMessagesPopover.html">
                                <div class="popover-message">
                                    <div class="row">
                                        <span class="color-gray-borders"> {{navigation.getPopoverFormattedDate(msg.message)}} </span>
                                        <span class="color-mail bold" ng-bind-html="navigation.formatDisplayName(msg.message)"></span>
                                        <span class="color-mail bold"> a {{msg.message.service ? msg.message.service.name : '' }}</span>
                                    </div>
                                    <div class="row">
                                        <span class="font-italic break-word">{{ navigation.getPopover(msg.message) }}</span>
                                    </div>
                                    <hr/>
                                    <div class="row bold" ng-if="msg.message.queue">
                                        <i class="fa fa-database fa-fw"></i>
                                        <span class="font-medium"> {{ 'QUEUE' | translate }}: </span>
                                        <span> {{msg.message.queue.name}} </span>
                                    </div>
                                    <div class="row bold" ng-if="msg.message.important">
                                        <i class="fa fa-star fa-fw"></i>
                                        <span class="font-medium"> {{ 'MESSAGE_VIM' | translate }} </span>
                                    </div>
                                </div>
                            </script>
                            <button class="{{msg.navigation.class}} navigation-bar-button"
                                    ng-click="navigation.markAsRead(msg.message)"
                                    ng-class="{
                                        'selected-message' : msg.message.id === navigation.messagesService.selectedSocialCase.data.id,
                                        'unread' : !msg.message.read,
                                        'vim' : msg.message.important,
                                        'with-activity' : msg.withActivity,
                                        'verified': msg.message.postedBy.parameters.verified === 'True',
                                        'new-case': msg.message.case.repliesByAgents === 0 && !msg.message.read,
                                        'timer-expired': msg.timerExpired
                                        }">
                                <div>
                                    <i class="{{msg.navigation.image.icon}} navigation-bar-icons"> </i>
                                    <i class="fa fa-asterisk unread"></i>
                                    <i class="fa fa-star vim"></i>
                                    <i class="fa fa-badge-check verified"></i>
                                    <i class="fad fa-exclamation-circle with-activity"></i>
                                    <i class="fad fa-clock timer-expired"></i>
                                    <span ng-if="msg.message.chat != null && msg.message.chat.userUnreadMessages > 0 && !msg.message.chat.chatInactivityValues.isAbandoned"
                                          class="label-chat-unread badge bg-green">{{msg.message.chat.userUnreadMessages}}</span>
                                    <i ng-if="msg.message.chat != null && msg.message.chat.chatInactivityValues.isAbandoned"
                                       class="label-chat-disconnected badge bg-light-blue"><span class="fa fa-exclamation"></span></i>
                                </div>
                            </button>
                            <div class="navigation-bar-button-more-info"
                                 ng-if="msg.message.id !== navigation.messagesService.selectedSocialCase.data.id && msg.socialCase.whatsappVoiceCall && msg.socialCase.whatsappVoiceCall.active">
                                <div class="voice-call">
                                    <div class="voice-call-title">{{'WHATSAPP_VOICECALL_TITLE' | translate}}</div>
                                    <div class="voice-call-icon"><span class="fa fa-phone"></span></div>
                                    <div class="voice-call-callout"></div>

                                    <div class="voice-call-buttons" ng-if="msg.socialCase.whatsappVoiceCall.status === navigation.whatsAppVoiceCallStatus.Ringing">
                                        <button type="button"
                                                class="btn btn-flat btn-circle btn-voice-call-reject"
                                                ng-click="navigation.messagesService.whatsappVoiceCallOfferReject(msg.socialCase, true)">
                                            <span class="fa fa-times"></span>
                                        </button>
                                    </div>

                                    <div class="voice-call-buttons" ng-if="msg.socialCase.whatsappVoiceCall.status === navigation.whatsAppVoiceCallStatus.Connected || msg.socialCase.whatsappVoiceCall.status === navigation.whatsAppVoiceCallStatus.AcceptedOffer">
                                        <button type="button"
                                                class="btn btn-flat btn-circle"
                                                ng-class="{ 'btn-voice-call-reject': navigation.messagesService.inputDeviceMuted }"
                                                ng-click="navigation.messagesService.whatsappVoiceCallMuteUnmute(msg.socialCase)">
                                            <span class="fas fa-microphone" ng-if="!navigation.messagesService.inputDeviceMuted"></span>
                                            <span class="fas fa-microphone-slash" ng-if="navigation.messagesService.inputDeviceMuted"></span>
                                        </button>
                                        <button type="button"
                                                class="btn btn-flat btn-circle btn-voice-call-reject"
                                                ng-click="navigation.messagesService.whatsappVoiceCallTerminate(msg.socialCase, true)">
                                            <span class="fa fa-phone-slash"></span>
                                        </button>
                                    </div>

                                    <div class="voice-call-totaltime">
                                        <span>{{ msg.socialCase.whatsappVoiceCall.totalTime }}</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="slots"
                            ng-if="!navigation.isLoginOrAuxiliar"
                            ng-repeat="n in [] | range : navigation.slots">
                            <div class="slots">
                                <i class="fa fa-circle-notch fa-spin fa-3x fa-fw"></i>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="navbar-custom-menu" ng-if="navigation.isAgentLogged()">
                <div class="nav navbar-nav">
                    <div ng-if="navigation.allowAgentToChangeState() && navigation.agentCanSendOutbound && navigation.messagesForAgent().length === 0 && !navigation.processingMessages() && navigation.outboundAsButton && !contentWrapperCtrl.data.isOutgoingMessageSelected">
                        <div class="button" ng-click="navigation.sendOutboundMessage()">
                            <i class="fa fa-lg fa-sign-out-alt fa-flip-horizontal"></i>
                            <span class="title hidden-xs">{{ 'OUTBOUND_MESSAGE' | translate }}</span>
                        </div>
                    </div>
                    <div class="relative" ng-if="(navigation.allowAgentToChangeState() && navigation.isOutgoingNoChangeState && navigation.agentCanSendOutbound) || ((navigation.allowAgentToChangeState() && navigation.agentCanSendOutbound && navigation.messagesForAgent().length === 0 && !navigation.processingMessages() && !navigation.outboundAsButton && !contentWrapperCtrl.data.isOutgoingMessageSelected))">
                        <div class="dropdown-toggle" uib-dropdown>
                            <div class="button" uib-dropdown-toggle>
                                <i class="fa fa-lg fa-sign-out-alt fa-flip-horizontal"></i>
                                <span class="title hidden-xs">{{ 'OUTBOUND_MESSAGE' | translate }}</span>
                            </div>
                            <div class="dropdown-menu" uib-dropdown-menu>
                                <ul class="menu menu-outgoing">
                                    <!-- <li class="menu-outgoing-item">
                                        <a ng-click="navigation.sendOutboundMessage()">
                                            <div>
                                                <i class="fa fa-share-alt icon"></i>
                                                <span class="text">{{ 'OUTGOING_CASES' | translate }}</span>
                                            </div>
                                        </a>
                                    </li> -->
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundTwitter">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.Twitter)">
                                            <div>
                                                <i class="fab fa-twitter-square color-twitter icon"></i>
                                                <span class="text">{{ 'OUTGOING_TWITTER' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundFacebookMessenger">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.FacebookMessenger)">
                                            <div>
                                                <i class="fab fa-facebook color-facebook icon"></i>
                                                <span class="text">{{ 'OUTGOING_FACEBOOK/MESSENGER' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundMail">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.Mail)">
                                            <div>
                                                <i class="fa fa-envelope color-mail icon"></i>
                                                <span class="text">{{ 'OUTGOING_MAIL' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundWhatsapp">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.WhatsApp)">
                                            <div>
                                                <i class="fab fa-whatsapp-square color-whatsapp icon"></i>
                                                <span class="text">{{ 'OUTGOING_WHATSAPP' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundTelegram">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.Telegram)">
                                            <div>
                                                <i class="fab fa-telegram color-telegram icon"></i>
                                                <span class="text">{{ 'OUTGOING_TELEGRAM' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="menu-outgoing-item" ng-if="navigation.agentCanSendOutboundInstagram">
                                        <a ng-click="navigation.sendOutboundMessage(navigation.socialServiceTypes.Instagram)">
                                            <div>
                                                <i class="fab fa-instagram color-instagram icon"></i>
                                                <span class="text">{{ 'OUTGOING_INSTAGRAM' | translate }}</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div ng-if="(!contentWrapperCtrl.data.isOutgoingMessageSelected || navigation.isOutgoingNoChangeState) && navigation.allowAgentToChangeState() && navigation.agentCanShowMyCases && (navigation.agentService.useMyCases || navigation.agentService.allowedToMarkAsPending) && navigation.messagesForAgent().length === 0 && !navigation.processingMessages()">
                        <div class="button" ng-click="navigation.showMyCases()">
                            <i class="fa fa-lg fa-clipboard-list"></i>
                            <span class="title hidden-xs">{{ 'SEARCH_CASES' | translate }}</span>
                        </div>
                    </div>
                    <div ng-if="(!contentWrapperCtrl.data.isOutgoingMessageSelected || navigation.isOutgoingNoChangeState) && !navigation.agentCanShowMyCases && (navigation.agentService.useMyCases || navigation.agentService.allowedToMarkAsPending)">
                        <div class="button" ng-click="navigation.showMyCasesNoChangeState()">
                            <i class="fa fa-lg fa-clipboard-list"></i>
                            <span class="title hidden-xs">{{ 'SEARCH_CASES' | translate }}</span>
                        </div>
                    </div>
                    <div class="relative" ng-if="navigation.allowAgentToChangeState()" uib-dropdown is-open="navigation.isStatusMenuOpen">
                        <div class="dropdown-toggle" uib-dropdown-toggle>
                            <div class="button">
                                <div class="divided-circle">
                                    <div style="background-image: url('./app/content/images/login.png')" ng-if="navigation.connectionStatus.class === 'login'" loading="lazy"></div>
                                    <div style="background-image: url('./app/content/images/available.png')" ng-if="navigation.connectionStatus.class === 'available'" loading="lazy"></div>
                                    <div style="background-image: url('./app/content/images/auxiliar.png')" ng-if="navigation.connectionStatus.class === 'auxiliar'" loading="lazy"></div>
                                    <div style="background-image: url('./app/content/images/working.png')" ng-if="navigation.connectionStatus.class === 'working'" loading="lazy"></div>
                                    <div style="background-image: url('./app/content/images/workingAvailable.png')" ng-if="navigation.connectionStatus.class === 'workingAvailable'" loading="lazy"></div>
                                    <div style="background-image: url('./app/content/images/workingPendingAux.png')" ng-if="navigation.connectionStatus.class === 'workingPending'" loading="lazy"></div>
                                </div>
                                <span class="title hidden-xs">{{ navigation.connectionStatus.name }}</span>
                                <timer id="stateTimer" interval="1000" autostart="false">
                                    <span class="title hidden-md hidden-lg hidden-sm visible-xs-inline-block"><span ng-if="hours > 60">{{hhours}}:</span>{{mminutes}}:{{sseconds}}</span>
                                    <span class="title visible-md-inline-block visible-lg-inline-block visible-sm-inline-block hidden-xs">{{hhours}}:{{mminutes}}:{{sseconds}}</span>
                                    <input type="hidden" id="hiddenTimer" value="{{seconds + (minutes * 60) + (hours * 3600)}}" />
                                </timer>
                                <input type="hidden" id="hiddenCurrentStatus" value="{{navigation.connectionStatus.class}}" />
                                <input type="hidden" id="hiddenCurrentStatusAux" value="{{navigation.connectionStatus.id}} "/>
                                <input type="hidden" id="hiddenCurrentStatusIsOutgoing" value="{{contentWrapperCtrl.data.isOutgoingMessageSelected}} "/>
                            </div>
                        </div>
                        <div class="dropdown-menu" uib-dropdown-menu role="menu">
                            <ul class="menu menu-states">
                                <li ng-repeat="state in navigation.settings().states track by $index"
                                    role="menuitem"
                                    class="agentState">
                                    <a ng-click="navigation.switchStates(state)"
                                       ng-show="state.canBeShown && !state.outgoing && !state.myOutgoingCases">
                                        <div>
                                            <div class="state-icon">
                                                <div class="divided-circle">
                                                    <div style="background-image: url('./app/content/images/login.png')" ng-if="state.class === 'login'" loading="lazy"></div>
                                                    <div style="background-image: url('./app/content/images/available.png')" ng-if="state.class === 'available'" loading="lazy"></div>
                                                    <div style="background-image: url('./app/content/images/auxiliar.png')" ng-if="state.class === 'auxiliar'" loading="lazy"></div>
                                                </div>
                                            </div>
                                            <div class="state-name">
                                                {{ state.name }}
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div ng-if="!navigation.allowAgentToChangeState()">
                        <div class="button button-none">
                            <div class="divided-circle">
                                <div style="background-image: url('./app/content/images/login.png')" ng-if="navigation.connectionStatus.class === 'login'" loading="lazy"></div>
                                <div style="background-image: url('./app/content/images/available.png')" ng-if="navigation.connectionStatus.class === 'available'" loading="lazy"></div>
                                <div style="background-image: url('./app/content/images/auxiliar.png')" ng-if="navigation.connectionStatus.class === 'auxiliar'" loading="lazy"></div>
                                <div style="background-image: url('./app/content/images/working.png')" ng-if="navigation.connectionStatus.class === 'working'" loading="lazy"></div>
                                <div style="background-image: url('./app/content/images/workingAvailable.png')" ng-if="navigation.connectionStatus.class === 'workingAvailable'" loading="lazy"></div>
                                <div style="background-image: url('./app/content/images/workingPendingAux.png')" ng-if="navigation.connectionStatus.class === 'workingPending'" loading="lazy"></div>
                            </div>
                            <span class="title hidden-xs">{{ navigation.connectionStatus.name }}</span>
                            <timer id="stateTimer" interval="1000" autostart="false">
                                <span class="title hidden-md hidden-lg hidden-sm visible-xs-inline-block"><span ng-if="hours > 60">{{hhours}}:</span>{{mminutes}}:{{sseconds}}</span>
                                <span class="title visible-md-inline-block visible-lg-inline-block visible-sm-inline-block hidden-xs">{{hhours}}:{{mminutes}}:{{sseconds}}</span>
                                <input type="hidden" id="hiddenTimerNoChange" value="{{seconds + (minutes * 60) + (hours * 3600) }}" />
                            </timer>
                            <input type="hidden" id="hiddenCurrentStatusNoChange" value="{{navigation.connectionStatus.class}}" />
                            <input type="hidden" id="hiddenCurrentStatusAuxNoChange" value="{{navigation.connectionStatus.id}} "/>
                            <input type="hidden" id="hiddenCurrentStatusIsOutgoingNoChange" value="{{contentWrapperCtrl.data.isOutgoingMessageSelected}} "/>
                        </div>
                    </div>
                    <div class="relative">
                        <div class="dropdown-toggle" data-toggle="dropdown">
                            <div class="button">
                                <img ng-src="{{ navigation.getAgentAvatar() }}" class="user-image"/>
                                <span style="margin-left: 5px"
                                      ng-if="navigation.asistanceSolicited && !navigation.activeSupervisorChat"
                                      tooltip-placement="bottom" title="" uib-tooltip="{{ 'WAITING_ASSISTANCE' | translate }}">
                                    <i class="fa fa-bell"></i>
                                </span>
                            </div>
                        </div>
                        <div class="dropdown-menu user-info">
                            <div class="user-header">
                                <img ng-src="{{ navigation.getAgentAvatar() }}"
                                     class="user-image-profile" />
                                <p class="name">{{ navigation.settings().settings.agent.fullName }}</p>
                                <p class="username">{{ navigation.settings().settings.agent.userName }}</p>
                                <p class="version">{{ ::navigation.getVersion() }}</p>
                            </div>
                            <div class="user-body">
                                <div class="row">
                                    <a role="button" type="button"
                                       ng-if="navigation.internalChat && !navigation.asistanceSolicited"
                                       ng-click="navigation.chatSupervisorAlert()"
                                       ng-disabled="navigation.activeSupervisorChat"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'GET_ASSISTANCE' | translate }}">
                                        <span class="fa fa-lg fa-bell"></span>
                                    </a>
                                    <a role="button" type="button"
                                       ng-if="navigation.internalChat && navigation.asistanceSolicited"
                                       ng-click="navigation.chatSupervisorCancel()"
                                       ng-disabled="navigation.activeSupervisorChat"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'GET_ASSISTANCE' | translate }}">
                                        <span class="fa fa-lg fa-bell-slash"></span>
                                    </a>
                                    <!-- </div> -->
                                    <a role="button" type="button"
                                       ng-click="navigation.showAgentStatistics()"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'STATISTICS' | translate }}">
                                        <span class="fa fa-lg fa-chart-bar"></span>
                                    </a>
                                    <a role="button" type="button"
                                       ng-click="navigation.showAgentSettings()"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'ADJUSTMENTS' | translate }}">
                                        <span class="fa fa-lg fa-cog"></span>
                                    </a>
                                    <a role="button" type="button" ng-if="navigation.canChangePassword()"
                                       ng-click="navigation.showAgentChangePassword()"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'PROFILE' | translate }}">
                                        <span class="fa fa-lg fa-id-card"></span>
                                    </a>
                                    <a role="button" type="button"
                                       ng-click="navigation.showTwoFactorDialog()"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'TWO_FACTOR_CONFIGURATION' | translate }}">
                                        <span class="fa fa-lg fa-user-shield"></span>
                                    </a>
                                    <a role="button" type="button"
                                       ng-hide="navigation.removeLogoutButton()"
                                       ng-click="navigation.signOut()"
                                       class="button-profile col-xs-3"
                                       ng-style="navigation.agentButtonStyles"
                                       tooltip-placement="bottom" title="" uib-tooltip="{{ 'LOGOUT' | translate }}">
                                        <span class="fa fa-lg fa-power-off"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="outdated-browser">
        <div class="alert alert-warning alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <span class="fa fa-exclamation-triangle" aria-hidden="true"></span>
            <span class="sr-only">Información:</span>
            {{ 'BROWSER_OUTDATED' | translate}}
        </div>
    </div>

    <div class="wrapper">
        <div class="content-wrapper" block-ui="main" class="block-ui-main">
            <section class="content">
                <div ng-view class="view"></div>
            </section>
        </div>
    </div>

    <div class="connection-problems">
        <span class="fa fa-lg fa-wifi-slash"></span>
        <span class="fa fa-lg fa-wifi"></span>
        <span class="connecting">{{ 'CONNECTION_CONNECTING' | translate }}</span>
        <span class="error">{{ 'CONNECTION_ERROR' | translate }}</span>
    </div>

    <!-- injector:js -->
    <script src="./app/js_components/bowser.min.js"></script>
    <script src="./app/js_components/angular-locale_es-ar.js"></script>
    <script src="./app/js_components/tinymce.min.js"></script>
    <script src="./app/js_components/tinymce.js"></script>
    <script src="./app/js_components/bootstrap.min.js"></script>
    <script src="./app/js_components/hmac-sha1.js"></script>
    <script src="./app/js_components/hmac-sha256.js"></script>
    <script src="./app/js_components/aes.js"></script>
    <script src="./app/js_components/pbkdf2.js"></script>
    <script src="./app/js_components/sha1.js"></script>
    <script src="./app/js_components/md5.js"></script>
    <script src="./app/js_components/enc-base64-min.js"></script>
    <script src="./app/js_components/underscore-min.js"></script>
    <script src="./app/js_components/moment-with-locales.js"></script>
    <script src="./app/js_components/moment-duration-format.js"></script>
    <script src="./app/js_components/humanize-duration.js"></script>
    <script src="./app/js_components/pnotify.js"></script>
    <script src="./app/js_components/pnotify.desktop.js"></script>
    <script src="./app/js_components/pnotify.buttons.js"></script>
    <script src="./app/js_components/angular-underscore.min.js"></script>
    <script src="./app/js_components/angular-linkify.js"></script>
    <script src="./app/js_components/stacktrace.js"></script>
    <script src="./app/js_components/scrollglue.js"></script>
    <script src="./app/js_components/ivh-treeview.js"></script>
    <script src="./app/js_components/highcharts.src.js"></script>
    <script src="./app/js_components/highcharts-ng.js"></script>
    <script src="./app/js_components/angular-cookies.min.js"></script>
    <script src="./app/js_components/angular-messages.js"></script>
    <script src="./app/js_components/uploader.js"></script>
    <script src="./app/js_components/angular-file-upload.js"></script>
    <script src="./app/js_components/emoji-button.js"></script>
    <script src="./app/js_components/angular.panels.js"></script>
    <script src="./app/js_components/ui-bootstrap-tpls-2.5.0.js"></script>
    <script src="./app/js_components/angular-bootstrap-checkbox.js"></script>
    <script src="./app/js_components/ng-tags-input.js"></script>
    <script src="./app/js_components/angular-timer.min.js"></script>
    <script src="./app/js_components/angular-resource.min.js"></script>
    <script src="./app/js_components/angular-translate.min.js"></script>
    <script src="./app/js_components/angular-translate-loader-url.js"></script>
    <script src="./app/js_components/angular-translate-loader-static-files.min.js"></script>
    <script src="./app/js_components/angular-route.min.js"></script>
    <script src="./app/js_components/angular-sanitize.min.js"></script>
    <script src="./app/js_components/angular-block-ui.js"></script>
    <script src="./app/js_components/angular-toastr.tpls.min.js"></script>
    <script src="./app/js_components/angular-touch.min.js"></script>
    <script src="./app/js_components/angular-modal-service.js"></script>
    <script src="./app/js_components/angular-animate.min.js"></script>
    <script src="./app/js_components/angular-dragdrop.js"></script>
    <script src="./app/js_components/plugins/colorpicker/plugin.min.js"></script>
    <script src="./app/js_components/plugins/spellchecker/plugin.min.js"></script>
    <script src="./app/js_components/plugins/textcolor/plugin.min.js"></script>
    <script src="./app/js_components/plugins/paste/plugin.min.js"></script>
    <script src="./app/js_components/plugins/noneditable/plugin.min.js"></script>
    <script src="./app/js_components/plugins/autolink/plugin.min.js"></script>
    <script src="./app/js_components/plugins/code/plugin.min.js"></script>
    <script src="./app/js_components/plugins/autoresize/plugin.min.js"></script>
    <script src="./app/js_components/plugins/table/plugin.min.js"></script>
    <script src="./app/js_components/plugins/lists/plugin.min.js"></script>
    <script src="./app/js_components/themes/modern/theme.min.js"></script>
    <script src="./app/js_components/langs/es_MX.js"></script>
    <script src="./app/js_components/langs/pt_BR.js"></script>
    <script src="./app/js_components/jquery-scrolltofixed.js"></script>
    <script src="./app/js_components/twitter-text-3.0.1.js"></script>
    <script src="./app/js_components/leader-line.min.js"></script>
    <script src="./app/app.module.js"></script>
    <script src="./app/app.config.js"></script>
    <script src="./app/app.route.js"></script>
    <script src="./app/js_components/onErrorSrc.js"></script>
    <script src="./app/js_components/exceptionOverwrite.js"></script>
    <script src="./app/answers/emoji-picker.directive.js"></script>
    <script src="./app/answers/apple/apple-answer.controller.js"></script>
    <script src="./app/answers/apple/apple-answer.directive.js"></script>
    <script src="./app/answers/auto-height-textarea.directive.js"></script>
    <script src="./app/answers/chat/chat-answer.controller.js"></script>
    <script src="./app/answers/chat/chat-answer.directive.js"></script>
    <script src="./app/answers/email/email-answer.controller.js"></script>
    <script src="./app/answers/email/email-answer.directive.js"></script>
    <script src="./app/answers/emoji-button.controller.js"></script>
    <script src="./app/answers/emoji-button.directive.js"></script>
    <script src="./app/answers/facebook/facebook-answer.controller.js"></script>
    <script src="./app/answers/facebook/facebook-answer.directive.js"></script>
    <script src="./app/answers/facebookmessenger/facebookmessenger-answer-template.controller.js"></script>
    <script src="./app/answers/facebookmessenger/facebookmessenger-answer-template.directive.js"></script>
    <script src="./app/answers/facebookmessenger/facebookmessenger-answer.controller.js"></script>
    <script src="./app/answers/facebookmessenger/facebookmessenger-answer.directive.js"></script>
    <script src="./app/answers/generic/answer.controller.js"></script>
    <script src="./app/answers/generic/answer.directive.js"></script>
    <script src="./app/answers/googlemybusiness/googlemybusiness-answer.controller.js"></script>
    <script src="./app/answers/googlemybusiness/googlemybusiness-answer.directive.js"></script>
    <script src="./app/answers/googleplay/googleplay-answer.controller.js"></script>
    <script src="./app/answers/googleplay/googleplay-answer.directive.js"></script>
    <script src="./app/answers/googlerbm/googlerbm-answer.controller.js"></script>
    <script src="./app/answers/googlerbm/googlerbm-answer.directive.js"></script>
    <script src="./app/answers/instagram/instagram-answer.controller.js"></script>
    <script src="./app/answers/instagram/instagram-answer.directive.js"></script>
    <script src="./app/answers/linkedin/linkedin-answer.controller.js"></script>
    <script src="./app/answers/linkedin/linkedin-answer.directive.js"></script>
    <script src="./app/answers/mercadolibre/mercadolibre-answer.controller.js"></script>
    <script src="./app/answers/mercadolibre/mercadolibre-answer.directive.js"></script>
    <script src="./app/answers/skype/skype-answer.controller.js"></script>
    <script src="./app/answers/skype/skype-answer.directive.js"></script>
    <script src="./app/answers/telegram/telegram-answer.controller.js"></script>
    <script src="./app/answers/telegram/telegram-answer.directive.js"></script>
    <script src="./app/answers/twitter/twitter-answer.controller.js"></script>
    <script src="./app/answers/twitter/twitter-answer.directive.js"></script>
    <script src="./app/answers/whatsapp/whatsapp-answer-template.controller.js"></script>
    <script src="./app/answers/whatsapp/whatsapp-answer-template.directive.js"></script>
    <script src="./app/answers/whatsapp/whatsapp-answer.controller.js"></script>
    <script src="./app/answers/whatsapp/whatsapp-answer.directive.js"></script>
    <script src="./app/answers/youtube/youtube-answer.controller.js"></script>
    <script src="./app/answers/youtube/youtube-answer.directive.js"></script>
    <script src="./app/authentication/login.controller.js"></script>
    <script src="./app/config/angularjs-dropdown-multiselect.directive.js"></script>
    <script src="./app/config/httpInjectors.service.js"></script>
    <script src="./app/core/config.js"></script>
    <script src="./app/core/connectionStatuses.js"></script>
    <script src="./app/core/contentWrapper.controller.js"></script>
    <script src="./app/core/main.controller.js"></script>
    <script src="./app/filters/businessDataFieldsDependsFilter.js"></script>
    <script src="./app/filters/cut.filter.js"></script>
    <script src="./app/filters/dateParse.filter.js"></script>
    <script src="./app/filters/general.filter.js"></script>
    <script src="./app/filters/range.filter.js"></script>
    <script src="./app/filters/todayOrYesterday.filter.js"></script>
    <script src="./app/filters/truncateHtml.js"></script>
    <script src="./app/layout/navigation.controller.js"></script>
    <script src="./app/cases/my-cases.controller.js"></script>
    <script src="./app/cases/my-cases.directive.js"></script>
    <script src="./app/cases/my-pending-cases.controller.js"></script>
    <script src="./app/cases/my-pending-cases.directive.js"></script>
    <script src="./app/cases/pending-cases.controller.js"></script>
    <script src="./app/cases/pending-cases.directive.js"></script>
    <script src="./app/cases/pending-reply-cases.controller.js"></script>
    <script src="./app/cases/pending-reply-cases.directive.js"></script>
    <script src="./app/messages/active-button.directive.js"></script>
    <script src="./app/messages/button-checkbox.directive.js"></script>
    <script src="./app/messages/case-conversation-chat-item.controller.js"></script>
    <script src="./app/messages/case-conversation-chat-item.directive.js"></script>
    <script src="./app/messages/case-conversation-item-bot.controller.js"></script>
    <script src="./app/messages/case-conversation-item-bot.directive.js"></script>
    <script src="./app/messages/case-conversation-item-summary.controller.js"></script>
    <script src="./app/messages/case-conversation-item-summary.directive.js"></script>
    <script src="./app/messages/case-conversation-item.controller.js"></script>
    <script src="./app/messages/case-conversation-item.directive.js"></script>
    <script src="./app/messages/case-conversation-mail-item.controller.js"></script>
    <script src="./app/messages/case-conversation-mail-item.directive.js"></script>
    <script src="./app/messages/case-conversation.controller.js"></script>
    <script src="./app/messages/case-conversation.directive.js"></script>
    <script src="./app/messages/case-info.controller.js"></script>
    <script src="./app/messages/case-info.directive.js"></script>
    <script src="./app/messages/case.controller.js"></script>
    <script src="./app/messages/case.directive.js"></script>
    <script src="./app/messages/caseDetails.controller.js"></script>
    <script src="./app/messages/caseDetails.directive.js"></script>
    <script src="./app/messages/cases-and-results.controller.js"></script>
    <script src="./app/messages/cases-and-results.directive.js"></script>
    <script src="./app/messages/chat-message.controller.js"></script>
    <script src="./app/messages/chat-message.directive.js"></script>
    <script src="./app/messages/chat.controller.js"></script>
    <script src="./app/messages/chat.directive.js"></script>
    <script src="./app/messages/discard.controller.js"></script>
    <script src="./app/messages/discard.directive.js"></script>
    <script src="./app/messages/emailContacts.controller.js"></script>
    <script src="./app/messages/emailContacts.directive.js"></script>
    <script src="./app/messages/message-actions.controller.js"></script>
    <script src="./app/messages/message-actions.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Cases.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Cases.directive.js"></script>
    <script src="./app/messages/outgoingMessage-FacebookMessenger.controller.js"></script>
    <script src="./app/messages/outgoingMessage-FacebookMessenger.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Instagram.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Instagram.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Mail.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Mail.directive.js"></script>
    <script src="./app/messages/outgoingMessage-SMS.controller.js"></script>
    <script src="./app/messages/outgoingMessage-SMS.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Telegram.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Telegram.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Twitter.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Twitter.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Twitter2.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Twitter2.directive.js"></script>
    <script src="./app/messages/outgoingMessage-Whatsapp.controller.js"></script>
    <script src="./app/messages/outgoingMessage-Whatsapp.directive.js"></script>
    <script src="./app/messages/outgoingMessage.controller.js"></script>
    <script src="./app/messages/outgoingMessage.directive.js"></script>
    <script src="./app/messages/predefined-answers.controller.js"></script>
    <script src="./app/messages/predefined-answers.directive.js"></script>
    <script src="./app/messages/profiles-list.controller.js"></script>
    <script src="./app/messages/profiles-list.directive.js"></script>
    <script src="./app/messages/returnToQueue.controller.js"></script>
    <script src="./app/messages/returnToQueue.directive.js"></script>
    <script src="./app/messages/returnToYFlow.controller.js"></script>
    <script src="./app/messages/returnToYFlow.directive.js"></script>
    <script src="./app/messages/scrollToFixed.directive.js"></script>
    <script src="./app/messages/span-binding.directive.js"></script>
    <script src="./app/messages/structured-message-apple.controller.js"></script>
    <script src="./app/messages/structured-message-apple.directive.js"></script>
    <script src="./app/messages/structured-message-chat.controller.js"></script>
    <script src="./app/messages/structured-message-chat.directive.js"></script>
    <script src="./app/messages/structured-message-googlerbm.controller.js"></script>
    <script src="./app/messages/structured-message-googlerbm.directive.js"></script>
    <script src="./app/messages/structured-message-instagram.controller.js"></script>
    <script src="./app/messages/structured-message-instagram.directive.js"></script>
    <script src="./app/messages/structured-message-mercadolibre.controller.js"></script>
    <script src="./app/messages/structured-message-mercadolibre.directive.js"></script>
    <script src="./app/messages/structured-message-messenger.controller.js"></script>
    <script src="./app/messages/structured-message-messenger.directive.js"></script>
    <script src="./app/messages/structured-message-skype.controller.js"></script>
    <script src="./app/messages/structured-message-skype.directive.js"></script>
    <script src="./app/messages/structured-message-telegram.controller.js"></script>
    <script src="./app/messages/structured-message-telegram.directive.js"></script>
    <script src="./app/messages/structured-message-twitter.controller.js"></script>
    <script src="./app/messages/structured-message-twitter.directive.js"></script>
    <script src="./app/messages/structured-message-whatsapp.controller.js"></script>
    <script src="./app/messages/structured-message-whatsapp.directive.js"></script>
    <script src="./app/messages/supervisor-chat-message.controller.js"></script>
    <script src="./app/messages/supervisor-chat-message.directive.js"></script>
    <script src="./app/messages/supervisorChat.controller.js"></script>
    <script src="./app/messages/supervisorChat.directive.js"></script>
    <script src="./app/messages/toogle-parent-class.directive.js"></script>
    <script src="./app/messages/transferToQueue.controller.js"></script>
    <script src="./app/messages/transferToQueue.directive.js"></script>
    <script src="./app/messages/user-profile-chat.controller.js"></script>
    <script src="./app/messages/user-profile-chat.directive.js"></script>
    <script src="./app/messages/valid-business-code.directive.js"></script>
    <script src="./app/messages/videocall.controller.js"></script>
    <script src="./app/messages/videocall.directive.js"></script>
    <script src="./app/modals/agentChangePassword-modal.controller.js"></script>
    <script src="./app/modals/agentSettings-modal.controller.js"></script>
    <script src="./app/modals/agentStatistics-modal.controller.js"></script>
    <script src="./app/modals/answerGroupedMessage-modal.controller.js"></script>
    <script src="./app/modals/appleForms-modal.controller.js"></script>
    <script src="./app/modals/attachment-modal.controller.js"></script>
    <script src="./app/modals/case-modal.controller.js"></script>
    <script src="./app/modals/caseDetails-modal.controller.js"></script>
    <script src="./app/modals/confirm-general.controller.js"></script>
    <script src="./app/modals/confirm-webtospeech.controller.js"></script>
    <script src="./app/modals/custom-modal.controller.js"></script>
    <script src="./app/modals/emailContacts-action-modal.controller.js"></script>
    <script src="./app/modals/emailContacts-modal.controller.js"></script>
    <script src="./app/modals/groupingMessage-modal.controller.js"></script>
    <script src="./app/modals/myPredefinedAnswers-modal.controller.js"></script>
    <script src="./app/modals/notQuitable-modal.controller.js"></script>
    <script src="./app/modals/outgoing-modal.controller.js"></script>
    <script src="./app/modals/outgoing-type-modal.controller.js"></script>
    <script src="./app/modals/predefinedAnswers-modal.controller.js"></script>
    <script src="./app/modals/profileEdit-modal.controller.js"></script>
    <script src="./app/modals/profiles-merging-confirm-modal.controller.js"></script>
    <script src="./app/modals/searchUserProfiles-modal.controller.js"></script>
    <script src="./app/modals/twoFactorSettings-modal.controller.js"></script>
    <script src="./app/modals/uploadFiles-modal.controller.js"></script>
    <script src="./app/panels/case.panel.controller.js"></script>
    <script src="./app/panels/message-details.panel.controller.js"></script>
    <script src="./app/panels/social-user-profile.panel.controller.js"></script>
    <script src="./app/profile/social-network-account.controller.js"></script>
    <script src="./app/profile/social-network-account.directive.js"></script>
    <script src="./app/profile/userprofile-cases.controller.js"></script>
    <script src="./app/profile/userprofile-cases.directive.js"></script>
    <script src="./app/profile/userprofile-merge.controller.js"></script>
    <script src="./app/profile/userprofile-merge.directive.js"></script>
    <script src="./app/profile/userprofile.controller.js"></script>
    <script src="./app/profile/userprofile.directive.js"></script>
    <script src="./app/services/agent.service.js"></script>
    <script src="./app/services/authentication.service.js"></script>
    <script src="./app/services/charts.service.js"></script>
    <script src="./app/services/chatHandler.service.js"></script>
    <script src="./app/services/chatSupervisor.service.js"></script>
    <script src="./app/services/common-actions.service.js"></script>
    <script src="./app/services/crypto.service.js"></script>
    <script src="./app/services/emailContacts.service.js"></script>
    <script src="./app/services/enums.service.js"></script>
    <script src="./app/services/exceptionLogger.service.js"></script>
    <script src="./app/services/integrations.service.js"></script>
    <script src="./app/services/logger.service.js"></script>
    <script src="./app/services/messages.service.js"></script>
    <script src="./app/services/modalSocial.service.js"></script>
    <script src="./app/services/outgoing.service.js"></script>
    <script src="./app/services/profile.service.js"></script>
    <script src="./app/services/sessionInjector.service.js"></script>
    <script src="./app/services/settings.service.js"></script>
    <script src="./app/services/socialService.service.js"></script>
    <script src="./app/services/states.service.js"></script>
    <script src="./app/services/utils.service.js"></script>
    <script src="./app/js_components/emojis-list.js"></script>
    <script src="./app/js_components/urlSearchParam.js"></script>
    <script src="./app/js_components/ng-map.min.js"></script>
    <script src="./app/js_components/showdown.min.js"></script>
    <script src="./app/js_components/signalr.min.js"></script>
    <script src="./app/js_components/html2canvas.min.js"></script>
    <script src="./app/js_components/webrtc_adaptor.js"></script>
    <!-- endinjector -->

</body>

</html>
