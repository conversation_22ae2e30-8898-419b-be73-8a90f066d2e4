﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.Licensing;
using System.Net;
using System.IO;
using System.Text;
using System.Web.Script.Serialization;
using System.Web.Script.Services;
using Yoizen.Common;
using RestSharp.Extensions;

namespace Yoizen.Social.Web.Configuration
{
	public partial class ServicesFacebookMessenger : ServicesBasePage
	{
		protected override ServiceTypes ServiceType { get { return DomainModel.ServiceTypes.FacebookMessenger; } }

		protected override string PageDescription { get { return "Servicio de Facebook Messenger"; } }

		protected override string PageDescriptionLocalizationKey { get { return "configuration-servicesfacebookmessenger-title"; } }

		protected override TextBox TextboxServiceName { get { return textboxServiceName; } }

		protected void Page_Load(object sender, EventArgs e)
		{
			var isCopying = false;

			if (!string.IsNullOrEmpty(hiddenServiceToCopy.Value))
			{
				isCopying = true;
			}

			if (!IsPostBack || isCopying)
			{
				var license = Licensing.LicenseManager.Instance.License.Configuration;

				var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>()
					.OrderBy(q => q.Name)
					.Select(q => new
					{
						ID = q.ID,
						Name = q.Name
					});
				dropdownlistFacebookQueue.DataSource = queues;
				dropdownlistFacebookQueue.DataBind();
				listboxFlowShareEnqueuedMessagesFromQueues.DataSource = queues;
				listboxFlowShareEnqueuedMessagesFromQueues.DataBind();
				listboxFlowShareConnectedAgentsFromQueues.DataSource = queues;
				listboxFlowShareConnectedAgentsFromQueues.DataBind();

				this.RegisterJsonVariable("facebookUrlToken", DomainModel.SystemSettings.Instance.FacebookMessenger.UrlToken);
				this.RegisterJsonVariable("MinutesToCloseCases", DomainModel.SystemSettings.Instance.Cases.MaxElapsedMinutesToCloseCases);

				placeholderFacebookCheckSpelling.Visible = !license.OnlyWebAgent;
				liTabVideo.Visible = license.AllowAgentsToStartVideoCall;
				panelVideo.Visible = license.AllowAgentsToStartVideoCall;

				if (license.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled)
				{
					placeholderAllowYFlow.Visible = true;
					liTabAdvancedConfigurationYFlow.Visible = true;
					placeholderAllowAgentsToReturnMessagesToYFlow.Visible = license.AllowAgentsToTransferMessagesToYFlow;
					placeholderYFlowCasesRelated.Visible = true;

					if (license.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						panelYFlowSurveys.Visible = true;

						var surveys = DomainModel.Cache.Instance.GetList<Survey>()
							.Where(s => s.Enabled)
							.OrderBy(s => s.Name)
							.Select(s => new
							{
								s.ID,
								s.Name,
								s.Type
							});
						if (surveys.Any())
						{
							panelEnableSurveys.Visible = true;
							messageNoSurveys.Visible = false;
							dropdownSurvey.DataSource = surveys;
							dropdownSurvey.DataBind();

							this.RegisterJsonVariable("availableSurveys", surveys);
						}
						else
						{
							panelEnableSurveys.Visible = false;
							messageNoSurveys.Visible = true;
						}

						var tagGroups = DomainModel.Cache.Instance.GetList<TagGroup>()
							.OrderBy(tg => tg.Name)
							.Select(tg => new
							{
								tg.ID,
								tg.Name
							});

						listboxSurveyTagGroup.DataSource = tagGroups;
						listboxSurveyTagGroup.DataBind();
						listboxSurveyTagGroupToIgnore.DataSource = tagGroups;
						listboxSurveyTagGroupToIgnore.DataBind();
					}
					else
					{
						panelYFlowSurveys.Visible = false;
					}

					divYFlowContingency.Visible = Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled &&
						DomainModel.SystemStatus.Instance.ContingencyBot.AllowContingencyBot &&
						this.LoggedUser.AccessTokenYFlowContingency != null;
				}
				else
				{
					placeholderYFlowCasesRelated.Visible = false;
					placeholderAllowYFlow.Visible = false;
					liTabAdvancedConfigurationYFlow.Visible = false;
				}

				this.RegisterJsonVariable("maxMinutesToAnswerMessages", DomainModel.SystemSettings.Instance.FacebookMessenger.MaxMinutesToAnswerMessages);
				this.RegisterJsonVariable("enabledCapi", DomainModel.SystemSettings.Instance.EnableCapi);
				placeholderActAsChat.Visible = license.AllowServicesAsChats;

			}
		}

		#region Protected Methods

		/// <summary>
		/// Inicializa un nuevo servicio
		/// </summary>
		protected override void InitializeNewService()
		{
			//textboxFacebookUserId.Text = string.Empty;
			textboxFacebookPageId.Text = string.Empty;
			textboxFacebookPageAccessToken.Text = string.Empty;
			textboxFacebookPageName.Text = string.Empty;
			textboxFacebookFromDate.Text = this.LoggedUser.FormatDate(DateTime.Now);
			dropdownlistFacebookQueue.Items.Insert(0, Helpers.ControlsHelper.CreateListItem("Sin definir", "0", "globals-undefined"));
			dropdownlistFacebookQueue.SelectedIndex = 0;
			checkboxFacebookCheckSpelling.Checked = true;
			textboxFacebookMinutesForInactivity.Text = "1440";
			checkboxVerifyAccessToken.Checked = false;
			
			var settings = new DomainModel.ServiceSettings.FacebookMessengerSettings();
			checkboxFacebookCheckSpelling.Checked = settings.MustCheckSpellingBeforeSend;
			textboxFacebookOAuthErrorEmailSubject.Text = settings.OAuthErrorOcurred.Subject;
			textboxFacebookOAuthErrorEmails.Text = settings.OAuthErrorOcurred.Emails;
			textboxFacebookOAuthErrorEmailTemplate.Text = settings.OAuthErrorOcurred.Template;
			checkboxFacebookAllowToSendMedia.Checked = settings.AllowToSendMultimedia;
			textboxFacebookMaxSizeAttachment.Text = settings.Attachments.MaxSizeAllowed.ToString();
			checkboxFacebookAcceptedTypeImages.Checked = settings.Attachments.AllowAttachImages;
			checkboxFacebookAcceptedTypeAudio.Checked = settings.Attachments.AllowAttachAudio;
			checkboxFacebookAcceptedTypeVideo.Checked = settings.Attachments.AllowAttachVideo;
			checkboxFacebookAcceptedTypeAllFiles.Checked = settings.Attachments.AllowAttachAllFiles;
			textboxFacebookMinutesForInactivity.Text = settings.MinutesForInactivity.ToString();
			textboxFacebookInactivityDetectedEmailSubject.Text = settings.InactivityDetected.Subject;
			textboxFacebookInactivityDetectedEmails.Text = settings.InactivityDetected.Emails;
			textboxFacebookInactivityDetectedEmailTemplate.Text = settings.InactivityDetected.Template;

			checkboxUseGetStartedButton.Checked = settings.UseGetStartedButton;
			textboxWelcomeMessage.Text = settings.WelcomeMessage;
			
			hiddenServiceIDToDisable.Value = string.Empty;
			textboxFlowMinutesAfterAgentClosedCase.Text = "0";

			checkboxAutoReplyBeforeMaxTimeToAnswer.Checked = false;
			textboxAutoReplyBeforeMaxTimeToAnswerText.Text = string.Empty;
			textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text = "60";
			checkboxDiscardAfterMaxTimeToAnswer.Checked = false;
			checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked = false;
			dropdownlistAllowToSendHSM.SelectedValue = "0";
			dropdownlistAllowAgentsToSendHSM.SelectedValue = "0";
			hiddenHSMTemplates.Value = string.Empty;
			dropdownlistAllowSurveys.SelectedValue = "0";

			checkboxEnableSurveys.Checked = false;
			textboxSurveyExpiration.Text = string.Empty;
			textboxSurveyInvitation.Text = string.Empty;
			textboxSurveySentRate.Text = "100";
			textboxSurveyTimeToSend.Text = "24";
			textboxSurveyMessagesCount.Text = "0";
			textboxSurveyCaseDuration.Text = "0";
			checkboxSurveySendIfNewCaseExists.Checked = true;
			textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
			textboxSurveyDontSendTotalSendMonthly.Text = "0";

			checkboxActAsChat.Checked = false;
			checkboxEnableCapi.Checked = false;
			
			checkboxCasesOverrideSystemSettings.Checked = false;
			this.RegisterJsonVariable("casesSettings", DomainModel.SystemSettings.Instance.Cases);
		}

		/// <summary>
		/// Carga los datos relacionados con el servicio
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> del cual se cargarán los datos</param>
		protected override void LoadServiceData(Service service)
		{
			var configuration = new SocialServices.Facebook.FacebookMessengerServiceConfiguration(service.Configuration);
			//textboxFacebookUserId.Text = configuration.UserId.ToString();
			if (!IsCopyingService)
			{
				textboxFacebookPageId.Text = configuration.PageId.ToString();
				textboxFacebookPageAccessToken.Text = configuration.PageAccessToken;
				textboxFacebookPageName.Text = configuration.PageName;
			}
						
			if (configuration.FromDate == DateTime.MinValue)
				textboxFacebookFromDate.Text = string.Empty;
			else
				textboxFacebookFromDate.Text = this.LoggedUser.FormatDate(configuration.FromDate);

			if (service.Queue != null)
			{
				dropdownlistFacebookQueue.SelectedValue = service.Queue.ID.ToString();
			}
			else
			{
				dropdownlistFacebookQueue.Items.Insert(0, Helpers.ControlsHelper.CreateListItem("Sin definir", "0", "globals-undefined"));
				dropdownlistFacebookQueue.SelectedIndex = 0;
			}

			checkboxVerifyAccessToken.Checked = configuration.VerifyAccessToken;
			checkboxAutoReplyToChatPluingGetStarted.Checked = configuration.AutoReplyToChatPluingGetStarted;
			textboxAutoReplyToChatPluingGetStartedText.Text = configuration.AutoReplyToChatPluingGetStartedText;

			var settings = service.Settings as DomainModel.ServiceSettings.FacebookMessengerSettings;

			checkboxActAsChat.Checked = settings.ActAsChat;
			checkboxFacebookCheckSpelling.Checked = settings.MustCheckSpellingBeforeSend;
			hiddenConnectionOAuth.Value = settings.OAuthErrorOcurred.EmailConnection;
			textboxFacebookOAuthErrorEmailSubject.Text = settings.OAuthErrorOcurred.Subject;
			textboxFacebookOAuthErrorEmails.Text = settings.OAuthErrorOcurred.Emails; 
			textboxFacebookOAuthErrorEmailTemplate.Text = settings.OAuthErrorOcurred.Template;
			checkboxFacebookAllowToSendMedia.Checked = settings.AllowToSendMultimedia;
			textboxFacebookMaxSizeAttachment.Text = settings.Attachments.MaxSizeAllowed.ToString();
			checkboxFacebookAcceptedTypeImages.Checked = settings.Attachments.AllowAttachImages;
			checkboxFacebookAcceptedTypeAudio.Checked = settings.Attachments.AllowAttachAudio;
			checkboxFacebookAcceptedTypeVideo.Checked = settings.Attachments.AllowAttachVideo;
			checkboxFacebookAcceptedTypeAllFiles.Checked = settings.Attachments.AllowAttachAllFiles;
			textboxFacebookMinutesForInactivity.Text = settings.MinutesForInactivity.ToString();
			hiddenConnectionInactivity.Value = settings.InactivityDetected.EmailConnection;
			textboxFacebookInactivityDetectedEmailSubject.Text = settings.InactivityDetected.Subject;
			textboxFacebookInactivityDetectedEmails.Text = settings.InactivityDetected.Emails;
			textboxFacebookInactivityDetectedEmailTemplate.Text = settings.InactivityDetected.Template;

			checkboxAutoReplyBeforeCloseCase.Checked = settings.UseAutoReplyBeforeCloseCase;
			textboxAutoReplyBeforeCloseCaseText.Text = settings.AutoReplyBeforeCloseCaseText;
			textboxAutoReplyBeforeCloseCaseMinutes.Text = settings.AutoReplyBeforeCloseCaseMinutes.ToString();
			textboxFlowMinutesAfterAgentClosedCase.Text = "0";

			checkboxUseGetStartedButton.Checked = settings.UseGetStartedButton;
			textboxWelcomeMessage.Text = settings.WelcomeMessage;

			hiddenServiceIDToDisable.Value = string.Empty;

			checkboxEnableVideo.Checked = settings.EnableVideo;
			checkboxEnableCapi.Checked = settings.EnableCapi;

			dropdownlistUseYFlow.SelectedValue = service.UsesYFlow.ToString().ToLower();
			if (service.YFlowSettings != null)
			{
				hiddenFlow.Value = Newtonsoft.Json.JsonConvert.SerializeObject(new
				{
					ID = service.YFlowSettings.ID,
					Version = service.YFlowSettings.Version.ToString(),
					Name = service.YFlowSettings.Name
				});
				textboxFlowMinutesAfterAgentClosedCase.Text = service.YFlowSettings.MinutesAfterAgentClosedCase.ToString();
				checkboxAllowAgentsToReturnMessagesToYFlow.Checked = service.YFlowSettings.AllowAgentsToReturnMessagesToYFlow;

				if (service.YFlowSettings.QueueTransfersByKey != null)
				{
					hiddenFlowQueueTransfersByKey.Value = Newtonsoft.Json.JsonConvert.SerializeObject(service.YFlowSettings.QueueTransfersByKey);
				}
				else
				{
					hiddenFlowQueueTransfersByKey.Value = string.Empty;
				}

				if (service.YFlowSettings.QueuesToShareEnqueuedMessages == null ||
					service.YFlowSettings.QueuesToShareEnqueuedMessages.Length == 0)
				{
					listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowSettings.QueuesToShareEnqueuedMessages)
					{
						try
						{
							var item = listboxFlowShareEnqueuedMessagesFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}

				if (service.YFlowSettings.QueuesToShareConnectedAgents == null ||
					service.YFlowSettings.QueuesToShareConnectedAgents.Length == 0)
				{
					listboxFlowShareConnectedAgentsFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowSettings.QueuesToShareConnectedAgents)
					{
						try
						{
							var item = listboxFlowShareConnectedAgentsFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}
				if (service.YFlowContingencySettings != null)
				{
					hiddenFlowContingency.Value = Newtonsoft.Json.JsonConvert.SerializeObject(new
					{
						ID = service.YFlowContingencySettings.ID,
						Version = service.YFlowContingencySettings.Version.ToString(),
						Name = service.YFlowContingencySettings.Name
					});
					textboxFlowMinutesAfterAgentClosedCase.Text = service.YFlowContingencySettings.MinutesAfterAgentClosedCase.ToString();
					checkboxAllowAgentsToReturnMessagesToYFlow.Checked = service.YFlowContingencySettings.AllowAgentsToReturnMessagesToYFlow;

					if (service.YFlowContingencySettings.QueueTransfersByKey != null)
					{
						hiddenFlowQueueTransfersByKey.Value = Newtonsoft.Json.JsonConvert.SerializeObject(service.YFlowContingencySettings.QueueTransfersByKey);
					}
					else
					{
						hiddenFlowQueueTransfersByKey.Value = string.Empty;
					}

					if (service.YFlowContingencySettings.QueuesToShareEnqueuedMessages == null ||
						service.YFlowContingencySettings.QueuesToShareEnqueuedMessages.Length == 0)
					{
						listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex = -1;
					}
					else
					{
						foreach (var queueId in service.YFlowContingencySettings.QueuesToShareEnqueuedMessages)
						{
							try
							{
								var item = listboxFlowShareEnqueuedMessagesFromQueues.Items.FindByValue(queueId.ToString());
								if (item != null)
									item.Selected = true;
							}
							catch { }
						}
					}

					if (service.YFlowContingencySettings.QueuesToShareConnectedAgents == null ||
						service.YFlowContingencySettings.QueuesToShareConnectedAgents.Length == 0)
					{
						listboxFlowShareConnectedAgentsFromQueues.SelectedIndex = -1;
					}
					else
					{
						foreach (var queueId in service.YFlowContingencySettings.QueuesToShareConnectedAgents)
						{
							try
							{
								var item = listboxFlowShareConnectedAgentsFromQueues.Items.FindByValue(queueId.ToString());
								if (item != null)
									item.Selected = true;
							}
							catch { }
						}
					}
				}
				else
				{
					hiddenFlowContingency.Value = string.Empty;
				}
			}

			checkboxAutoReplyBeforeMaxTimeToAnswer.Checked = settings.AutoReplyBeforeMaxTimeToAnswer;
			textboxAutoReplyBeforeMaxTimeToAnswerText.Text = settings.AutoReplyBeforeMaxTimeToAnswerText;
			textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text = settings.AutoReplyBeforeMaxTimeToAnswerMinutes.ToString();
			checkboxDiscardAfterMaxTimeToAnswer.Checked = settings.DiscardAfterMaxTimeToAnswer;
			checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked = settings.DiscardAndCloseCaseAfterMaxTimeToAnswer;
			dropdownlistAllowToSendHSM.SelectedValue = settings.AllowToSendTags ? "1" : "0";
			dropdownlistAllowAgentsToSendHSM.SelectedValue = (settings.AllowToSendTags && settings.AllowAgentsToSendTags) ? "1" : "0";
			hiddenHSMTemplates.Value = string.Empty;
			if (settings.TagTemplates != null && settings.TagTemplates.Length > 0)
				hiddenHSMTemplates.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.TagTemplates);
			dropdownlistAllowSurveys.SelectedValue = settings.AllowSurveys ? "1" : "0";

			checkboxEnableSurveys.Checked = false;
			textboxSurveyExpiration.Text = string.Empty;
			textboxSurveyInvitation.Text = string.Empty;
			textboxSurveySentRate.Text = "100";
			textboxSurveyTimeToSend.Text = "24";
			textboxSurveyMessagesCount.Text = "0";
			textboxSurveyCaseDuration.Text = "0";
			checkboxSurveySendIfNewCaseExists.Checked = true;
			textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
			textboxSurveyDontSendTotalSendMonthly.Text = "0";

			if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
				DomainModel.SystemSettings.Instance.EnableSurveys &&
				service.YFlowSettings != null &&
				service.YFlowSettings.SurveyEnabled &&
				service.YFlowSettings.SurveyList != null &&
				service.YFlowSettings.SurveyList.Count > 0)
			{
				var surveys = (from serviceSurvey 
								   in service.YFlowSettings.SurveyList
								   where serviceSurvey != null
						  join s in DomainModel.Cache.Instance.GetList<Survey>()
						  on serviceSurvey.SurveyID equals s.ID
						  select new
						  {
							  ID = s.ID,
							  Name = s.Name,
							  SurveyConfiguration = serviceSurvey
						  });
				
				this.RegisterJsonVariable("surveys", surveys);
				checkboxEnableSurveys.Checked = true;
			}


			this.RegisterJsonVariable("serviceID", service.ID);

			checkboxCasesOverrideSystemSettings.Checked = service.OverrideCasesSystemSettings;
			this.RegisterJsonVariable("casesSettings", service.CasesSettings);
			if (service.CasesSettings.TagOnAutoCloseCase != -1)
			{
				var tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(service.CasesSettings.TagOnAutoCloseCase);
				if (tag != null)
				{
					this.RegisterJsonVariable("casesSettingsTag", new
					{
						ID = tag.ID,
						FullName = tag.FullName
					});
				}
			}
		}

		/// <summary>
		/// Devuelve el identificador del servicio configurado a partir de los datos de cada tipo de servicio
		/// </summary>
		/// <returns>El identificador del servicio</returns>
		protected override string GetServiceAccountId()
		{
			return this.textboxFacebookPageId.Text;
		}

		#endregion

		#region Private Methods

		private static object BuildTagsByIds(List<int> tagsIds)
		{
			if (tagsIds == null || tagsIds.Count == 0)
				return null;

			IEnumerable<DomainModel.Tag> tags = DomainModel.Cache.Instance.GetList<DomainModel.Tag>().Where(tag => tagsIds.Contains(tag.ID));

			return BuildTags(tags);
		}

		private static object BuildTags(IEnumerable<Tag> tags)
		{
			if (tags == null || !tags.Any())
				return null;

			return tags.Select(t => BuildTag(t));
		}

		private static object BuildTag(Tag tag)
		{
			return new
			{
				tag.ID,
				tag.FullName
			};
		}

		#endregion

		#region Button Events

		protected async void buttonSave_Click(object sender, EventArgs e)
		{
			if (Page.IsValid)
			{
				Service service = GetService();

				string oldPageAccessToken = null;
				if (service.Configuration != null)
				{
					SocialServices.Facebook.FacebookServiceConfiguration oldConfiguration = new SocialServices.Facebook.FacebookServiceConfiguration(service.Configuration);
					oldPageAccessToken = oldConfiguration.PageAccessToken;
				}

				DateTime fromDate = DateTime.MinValue;
				if (!string.IsNullOrEmpty(textboxFacebookFromDate.Text))
					fromDate = this.LoggedUser.ParseDate(textboxFacebookFromDate.Text);

				var configuration = new SocialServices.Facebook.FacebookMessengerServiceConfiguration(
					Convert.ToInt64(textboxFacebookPageId.Text),
					textboxFacebookPageName.Text,
					textboxFacebookPageAccessToken.Text,
					fromDate,
					checkboxAutoReplyToChatPluingGetStarted.Checked,
					textboxAutoReplyToChatPluingGetStartedText.Text);

				service.AccessTokenExpiresAt = null;
				string contentType;
				service.Avatar = Helpers.FacebookHelper.GetProfilePicture(long.Parse(textboxFacebookPageId.Text), textboxFacebookPageAccessToken.Text, out contentType);
				if (service.Avatar != null && !string.IsNullOrEmpty(contentType))
					service.Parameters[DomainModel.Service.AvatarContentTypeParameter] = contentType;

				if (dropdownlistFacebookQueue.SelectedValue != "0")
				{
					service.Queue = QueueDAO.GetOneFromCache(int.Parse(dropdownlistFacebookQueue.SelectedValue));
				}

				service.Configuration = configuration.Serialize();
				service.ServiceConfiguration = configuration;
				service.AccountID = configuration.GetAccountID();

				var settings = service.Settings as DomainModel.ServiceSettings.FacebookMessengerSettings;
				settings.AllowToSendMultimedia = checkboxFacebookAllowToSendMedia.Checked;
				settings.MustCheckSpellingBeforeSend = checkboxFacebookCheckSpelling.Checked;
				if (settings.AllowToSendMultimedia)
				{
					settings.Attachments.MaxSizeAllowed = int.Parse(textboxFacebookMaxSizeAttachment.Text);
					settings.Attachments.AllowAttachImages = checkboxFacebookAcceptedTypeImages.Checked;
					settings.Attachments.AllowAttachAudio = checkboxFacebookAcceptedTypeAudio.Checked;
					settings.Attachments.AllowAttachVideo = checkboxFacebookAcceptedTypeVideo.Checked;
					settings.Attachments.AllowAttachAllFiles = checkboxFacebookAcceptedTypeAllFiles.Checked;
				}
				settings.OAuthErrorOcurred.Subject = textboxFacebookOAuthErrorEmailSubject.Text;
				settings.OAuthErrorOcurred.Emails = textboxFacebookOAuthErrorEmails.Text;
				settings.OAuthErrorOcurred.EmailConnection = hiddenConnectionOAuth.Value;
				settings.OAuthErrorOcurred.Template = textboxFacebookOAuthErrorEmailTemplate.Text;
				settings.MinutesForInactivity = int.Parse(textboxFacebookMinutesForInactivity.Text);
				settings.InactivityDetected.Subject = textboxFacebookInactivityDetectedEmailSubject.Text;
				settings.InactivityDetected.EmailConnection = hiddenConnectionInactivity.Value;
				settings.InactivityDetected.Emails = textboxFacebookInactivityDetectedEmails.Text;
				settings.InactivityDetected.Template = textboxFacebookInactivityDetectedEmailTemplate.Text;

				if (checkboxAutoReplyBeforeCloseCase.Checked
					&& !string.IsNullOrEmpty(textboxAutoReplyBeforeCloseCaseText.Text)
					&& !string.IsNullOrEmpty(textboxAutoReplyBeforeCloseCaseMinutes.Text))
				{
					settings.UseAutoReplyBeforeCloseCase = true;
					settings.AutoReplyBeforeCloseCaseText = textboxAutoReplyBeforeCloseCaseText.Text;
					settings.AutoReplyBeforeCloseCaseMinutes = int.Parse(textboxAutoReplyBeforeCloseCaseMinutes.Text);
				}
				else
				{
					settings.UseAutoReplyBeforeCloseCase = false;
					settings.AutoReplyBeforeCloseCaseText = null;
					settings.AutoReplyBeforeCloseCaseMinutes = 0;
				}

				settings.ActAsChat = checkboxActAsChat.Checked;
				settings.EnableVideo = checkboxEnableVideo.Checked;
				settings.EnableCapi = checkboxEnableCapi.Checked;

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow &&
					DomainModel.SystemSettings.Instance.YFlow.Enabled &&
					(dropdownlistUseYFlow.SelectedValue.Equals("true") || Convert.ToBoolean(dropdownlistUseYFlow.SelectedValue)))
				{
					var jYFlowSettings = Newtonsoft.Json.Linq.JObject.Parse(hiddenFlow.Value);
					if (jYFlowSettings["id"] != null || jYFlowSettings["ID"] != null)
					{
						service.YFlow = (jYFlowSettings["id"] != null) ? jYFlowSettings["id"].ToObject<int>() : jYFlowSettings["ID"].ToObject<int>();
						service.YFlowSettings = new DomainModel.ServiceSettings.YFlowSettings();
						service.YFlowSettings.ID = service.YFlow.Value;
						service.YFlowSettings.Name = (jYFlowSettings["name"] != null) ? jYFlowSettings["name"].ToString() : jYFlowSettings["Name"].ToString();
						service.YFlowSettings.Version = (jYFlowSettings["Version"] != null) ? jYFlowSettings["Version"].ToObject<int>() : Convert.ToInt32(jYFlowSettings["ActiveProductionVersion"]["number"].ToString());

						if (!string.IsNullOrEmpty(hiddenFlowQueueTransfersByKey.Value))
						{
							service.YFlowSettings.QueueTransfersByKey = new Dictionary<string, int>();
							var jFlowQueueTransfersByKey = (Newtonsoft.Json.Linq.JArray) Newtonsoft.Json.JsonConvert.DeserializeObject(hiddenFlowQueueTransfersByKey.Value);
							foreach (Newtonsoft.Json.Linq.JObject jTransfer in jFlowQueueTransfersByKey)
							{
								try
								{
									service.YFlowSettings.QueueTransfersByKey[jTransfer["Key"].ToString()] = jTransfer["QueueID"].ToObject<int>();
								}
								catch { }
							}
						}

						if (listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex == -1)
						{
							service.YFlowSettings.QueuesToShareEnqueuedMessages = null;
						}
						else
						{
							service.YFlowSettings.QueuesToShareEnqueuedMessages = listboxFlowShareEnqueuedMessagesFromQueues.GetSelectedIndices()
								.Select(index => int.Parse(listboxFlowShareEnqueuedMessagesFromQueues.Items[index].Value)).ToArray();
						}

						if (listboxFlowShareConnectedAgentsFromQueues.SelectedIndex == -1)
						{
							service.YFlowSettings.QueuesToShareConnectedAgents = null;
						}
						else
						{
							service.YFlowSettings.QueuesToShareConnectedAgents = listboxFlowShareConnectedAgentsFromQueues.GetSelectedIndices()
								.Select(index => int.Parse(listboxFlowShareConnectedAgentsFromQueues.Items[index].Value)).ToArray();
						}

						short.TryParse(textboxFlowMinutesAfterAgentClosedCase.Text, out short minutesAfterAgentClosedCase);
						service.YFlowSettings.MinutesAfterAgentClosedCase = minutesAfterAgentClosedCase;

						service.YFlowSettings.AllowAgentsToReturnMessagesToYFlow = checkboxAllowAgentsToReturnMessagesToYFlow.Checked;
					}

					if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						service.YFlowSettings.SurveyEnabled = checkboxEnableSurveys.Checked;
						if (service.YFlowSettings.SurveyEnabled)
						{
							service.YFlowSettings.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(hiddenSurveys.Value);
						}
						else
						{
							service.YFlowSettings.SurveyList = new List<QueueSurveyConfiguration>();
						}
					}

					#region YFlow Contingencia

					if (Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled &&
						DomainModel.SystemStatus.Instance.ContingencyBot.AllowContingencyBot &&
						this.LoggedUser.AccessTokenYFlowContingency != null)
					{
						var jYFlowContingencySettings = Newtonsoft.Json.Linq.JObject.Parse(hiddenFlowContingency.Value);
						if (jYFlowContingencySettings["id"] != null || jYFlowContingencySettings["ID"] != null)
						{
							service.YFlowContingency = (jYFlowContingencySettings["id"] != null) ? jYFlowContingencySettings["id"].ToObject<int>() : jYFlowContingencySettings["ID"].ToObject<int>();
							service.YFlowContingencySettings = new DomainModel.ServiceSettings.YFlowSettings();
							service.YFlowContingencySettings.ID = service.YFlowContingency.Value;
							service.YFlowContingencySettings.Name = (jYFlowContingencySettings["name"] != null) ? jYFlowContingencySettings["name"].ToString() : jYFlowContingencySettings["Name"].ToString();
							service.YFlowContingencySettings.Version = (jYFlowContingencySettings["Version"] != null) ? jYFlowContingencySettings["Version"].ToObject<int>() : Convert.ToInt32(jYFlowContingencySettings["ActiveProductionVersion"]["number"].ToString());

							if (!string.IsNullOrEmpty(hiddenFlowQueueTransfersByKey.Value))
							{
								service.YFlowContingencySettings.QueueTransfersByKey = new Dictionary<string, int>();
								var jFlowQueueTransfersByKey = (Newtonsoft.Json.Linq.JArray) Newtonsoft.Json.JsonConvert.DeserializeObject(hiddenFlowQueueTransfersByKey.Value);
								foreach (Newtonsoft.Json.Linq.JObject jTransfer in jFlowQueueTransfersByKey)
								{
									try
									{
										service.YFlowContingencySettings.QueueTransfersByKey[jTransfer["Key"].ToString()] = jTransfer["QueueID"].ToObject<int>();
									}
									catch { }
								}
							}

							if (listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex == -1)
							{
								service.YFlowContingencySettings.QueuesToShareEnqueuedMessages = null;
							}
							else
							{
								service.YFlowContingencySettings.QueuesToShareEnqueuedMessages = listboxFlowShareEnqueuedMessagesFromQueues.GetSelectedIndices()
									.Select(index => int.Parse(listboxFlowShareEnqueuedMessagesFromQueues.Items[index].Value)).ToArray();
							}

							if (listboxFlowShareConnectedAgentsFromQueues.SelectedIndex == -1)
							{
								service.YFlowContingencySettings.QueuesToShareConnectedAgents = null;
							}
							else
							{
								service.YFlowContingencySettings.QueuesToShareConnectedAgents = listboxFlowShareConnectedAgentsFromQueues.GetSelectedIndices()
									.Select(index => int.Parse(listboxFlowShareConnectedAgentsFromQueues.Items[index].Value)).ToArray();
							}

							short.TryParse(textboxFlowMinutesAfterAgentClosedCase.Text, out short minutesAfterAgentClosedCase);
							service.YFlowContingencySettings.MinutesAfterAgentClosedCase = minutesAfterAgentClosedCase;

							service.YFlowContingencySettings.AllowAgentsToReturnMessagesToYFlow = checkboxAllowAgentsToReturnMessagesToYFlow.Checked;
						}

						if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
						{
							service.YFlowContingencySettings.SurveyEnabled = checkboxEnableSurveys.Checked;
							if (service.YFlowContingencySettings.SurveyEnabled)
							{
								service.YFlowContingencySettings.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(hiddenSurveys.Value);
							}
							else
							{
								service.YFlowContingencySettings.SurveyList = new List<QueueSurveyConfiguration>();
							}
						}
					}
					else
					{
						service.YFlowContingency = null;
					}

					#endregion					
				}
				else
				{
					service.YFlow = null;
					service.YFlowContingency = null;
					//service.YFlowSettings = null;
					/*service.YFlowSettings.SurveyEnabled = false;
					service.YFlowSettings.Survey = null;
					service.YFlowSettings.SurveyConfiguration = null;*/

					settings.UseGetStartedButton = checkboxUseGetStartedButton.Checked;
					settings.WelcomeMessage = textboxWelcomeMessage.Text;
				}

				settings.AutoReplyBeforeMaxTimeToAnswer = checkboxAutoReplyBeforeMaxTimeToAnswer.Checked;
				if (settings.AutoReplyBeforeMaxTimeToAnswer)
				{
					settings.AutoReplyBeforeMaxTimeToAnswerText = textboxAutoReplyBeforeMaxTimeToAnswerText.Text;
					settings.AutoReplyBeforeMaxTimeToAnswerMinutes = short.Parse(textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text);
				}
				settings.DiscardAfterMaxTimeToAnswer = checkboxDiscardAfterMaxTimeToAnswer.Checked;
				settings.DiscardAndCloseCaseAfterMaxTimeToAnswer = checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked;
				settings.AllowToSendTags = dropdownlistAllowToSendHSM.SelectedValue.Equals("1");
				if (settings.AllowToSendTags)
				{
					settings.AllowAgentsToSendTags = dropdownlistAllowAgentsToSendHSM.SelectedValue.Equals("1");
					settings.TagTemplates = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ServiceSettings.FacebookMessengerSettings.TagTemplate[]>(hiddenHSMTemplates.Value);
				}
				else
				{
					settings.AllowAgentsToSendTags = false;
					settings.TagTemplates = null;
				}
				settings.AllowSurveys = dropdownlistAllowSurveys.SelectedValue.Equals("1");

				service.OverrideCasesSystemSettings = checkboxCasesOverrideSystemSettings.Checked;
				if (service.OverrideCasesSystemSettings)
				{
					service.CasesSettings = new DomainModel.Settings.CasesSettings();
					service.CasesSettings.CheckLastQueueOfOpenCase = checkboxCheckLastQueueOfOpenCase.Checked;
					service.CasesSettings.IgnoreLastQueueForSLMovedMessage = checkboxIgnoreLastQueueForSLMovedMessage.Checked;
					service.CasesSettings.MaxElapsedMinutesToCloseCases = ushort.Parse(textboxMaxElapsedMinutesToCloseCases.Text);
					service.CasesSettings.AllowToAutoReplyCloseCases = checkboxReplyInCloseCase.Checked;
					service.CasesSettings.AutoReplyInCloseCaseText = textboxAutoReplyInCloseCaseText.Text;
					if (!string.IsNullOrEmpty(textboxTagCloseCase.Text))
						service.CasesSettings.TagOnAutoCloseCase = int.Parse(textboxTagCloseCase.Text);
					else
						service.CasesSettings.TagOnAutoCloseCase = -1;

					service.CasesSettings.MaxElapsedMinutesToCloseYFlowCases = ushort.Parse(textboxMaxElapsedMinutesToCloseYFlowCases.Text);
					service.CasesSettings.InvokeYFlowWhenClosedCases = checkboxInvokeYFlowWhenClosedCases.Checked;
				}

				if (this.CreatingNewService)
					ServiceDAO.Insert(service);
				else
					ServiceDAO.Update(service);
				await this.AfterSaveAsync(service);

				this.LoggedUser.Parameters[DomainModel.User.FacebookMessengerUserAccessTokenParameter] = hiddenUserAccessToken.Value;

				if ((oldPageAccessToken != null && !oldPageAccessToken.Equals(configuration.PageAccessToken)) || // Si cambió el page access token, volvemos a suscribirnos
					!SocialServices.Facebook.FacebookMessengerTokens.IsSubscribedToRealTimeUpdates(configuration)
				)
				{
					IEnumerable<DomainModel.Service> existingServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
					DomainModel.Service anotherFacebookService = null;
					if (existingServices != null)
					{
						var facebookServices = existingServices.Where(s => s != null && s.Type == ServiceTypes.Facebook || s.Type == ServiceTypes.FacebookRt);
						if (facebookServices != null)
						{
							foreach (var facebookService in facebookServices)
							{
								var configurationFb = facebookService.ServiceConfiguration as SocialServices.Facebook.FacebookServiceConfiguration;
								if (configurationFb == null)
								{
									configurationFb = new SocialServices.Facebook.FacebookServiceConfiguration(facebookService.Configuration);
									facebookService.ServiceConfiguration = configurationFb;
								}


								if (configurationFb.PageId == configuration.PageId)
								{
									anotherFacebookService = facebookService;
									break;
								}
							}
						}
					}

					if (anotherFacebookService == null)
					{
						Tracer.TraceInfo("Suscribiendo a la página de facebook {0} a las notificaciones de tiempo real de messenger", configuration.PageId);
						if (SocialServices.Facebook.FacebookMessengerTokens.SubscribeToRealTimeUpdates(configuration))
						{
							Tracer.TraceInfo("Página de facebook {0} subscripta a las notificaciones de tiempo real de messenger", configuration.PageId);
						}
						else
						{
							Tracer.TraceInfo("No se pudo suscribir a la página de facebook {0} a las notificaciones de tiempo real de messenger", configuration.PageId);
						}
					}
					else
					{
						Tracer.TraceInfo("Suscribiendo a la página de facebook {0} a las notificaciones de tiempo real de messenger y facebook", configuration.PageId);
						if (SocialServices.Facebook.FacebookTokens.SubscribeToRealTimeUpdates((SocialServices.Facebook.FacebookServiceConfiguration) anotherFacebookService.ServiceConfiguration, true, out string[] subscribedFields))
						{
							Tracer.TraceInfo("Página de facebook {0} subscripta a las notificaciones de tiempo real de messenger y facebook", configuration.PageId);
							anotherFacebookService.Parameters["SubscribedFields"] = string.Join(",", subscribedFields);
							DAL.ServiceDAO.UpdateParameters(anotherFacebookService);
						}
						else
						{
							Tracer.TraceInfo("No se pudo suscribir a la página de facebook {0} a las notificaciones de tiempo real de messenger y facebook", configuration.PageId);
						}
					}
				}

				if (!service.UsesYFlow)
				{
					SocialServices.Facebook.FacebookMessengerTokens.ConfigureMessenger(configuration.PageAccessToken, configuration.PageId, textboxWelcomeMessage.Text, checkboxUseGetStartedButton.Checked);
				}
				else
				{
					if (service.YFlow != null)
					{
						try
						{
							var response = await YFlow.Manager.Instance.GetConfiguration(service.YFlow.Value, SocialServiceTypes.FacebookMessenger);
							if (response.Succeed)
							{
								if (response.Response["settings"] != null &&
									response.Response["settings"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
								{
									var jSettings = (Newtonsoft.Json.Linq.JObject) response.Response["settings"];
									if (jSettings["returnsFromAgent"] != null &&
										jSettings["returnsFromAgent"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
									{
										var jReturnsFromAgent = (Newtonsoft.Json.Linq.JArray) jSettings["returnsFromAgent"];
										service.YFlowSettings.ReturnsFromAgents = jReturnsFromAgent.ToObject<DomainModel.ServiceSettings.YFlowSettings.ReturnFromAgent[]>();
										DAL.ServiceDAO.Update(service);
									}
								}

								var jConfiguration = (Newtonsoft.Json.Linq.JObject) response.Response["configuration"];
								if (jConfiguration != null)
								{
									var configurationChanged = true;
									string newConfigurationHash;
									using (var md5 = System.Security.Cryptography.MD5.Create())
									{
										byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(jConfiguration.ToString());
										byte[] hash = md5.ComputeHash(inputBytes);
										newConfigurationHash = Common.Conversions.ConvertByteArrayToHexString(hash);
									}

									if (service.Parameters.ContainsKey("OldConfigurationHash"))
									{
										var oldConfigurationHash = service.Parameters["OldConfigurationHash"];
										if (newConfigurationHash.Equals(oldConfigurationHash))
										{
											configurationChanged = false;
										}
									}

									if (configurationChanged)
									{
										SocialServices.Facebook.FacebookMessengerTokens.ConfigureMessenger(configuration.PageAccessToken, configuration.PageId, jConfiguration);
										Tracer.TraceInfo("Publicando la configuración del servicio de Facebook Messenger");

										service.Parameters["OldConfigurationHash"] = newConfigurationHash;
										ServiceDAO.UpdateParameters(service);
										Tracer.TraceInfo("Hash de la configuración del servicio de Facebook Messenger: {0}", newConfigurationHash);
									}
									else
									{
										Tracer.TraceInfo("La configuración del servicio de Facebook Messenger no cambió. No se realizará ningún cambio de publicación");
									}
								}
							}
							else
							{
								Tracer.TraceError("No se pudo obtener la configuración del servicio desde yFlow: {0}", response.Exception);
							}
						}
						catch (Exception ex)
						{
							Tracer.TraceError("Ocurrió un error al intentar obtener la configuración del servicio desde yFlow: {0}", ex);
						}
					}
				}

				try
				{
					if (!string.IsNullOrEmpty(hiddenServiceIDToDisable.Value))
					{
						int existingServiceID = int.Parse(hiddenServiceIDToDisable.Value);
						var existingService = DAL.ServiceDAO.GetOneFromCache(existingServiceID);

						if (existingService != null)
						{
							if (existingService.Type == ServiceTypes.Facebook || existingService.Type == ServiceTypes.FacebookRt)
							{
								var existingServiceConfiguration = new Social.SocialServices.Facebook.FacebookServiceConfiguration(existingService.Configuration);
								if (existingServiceConfiguration.PageId == configuration.PageId && existingServiceConfiguration.UseDirectMessages)
								{
									var oldParameters = existingService.AsDictionary(true, "Configuration", "Settings");

									Tracer.TraceInfo("Al servicio de facebook {0} - {1} se le deshabilitará los mensajes privados", existingService.ID, existingService.Name);

									existingServiceConfiguration = new Social.SocialServices.Facebook.FacebookServiceConfiguration(
										existingServiceConfiguration.PageId,
										existingServiceConfiguration.PageName,
										existingServiceConfiguration.PageAccessToken,
										existingServiceConfiguration.UsePostsAndComments,
										false,
										existingServiceConfiguration.FromDate,
										existingServiceConfiguration.AllowCommentReplies,
										existingServiceConfiguration.AllowDiscardDeletedPostsAndComments,
										existingServiceConfiguration.IgnorePrivateMessagesFromFacebook,
										existingServiceConfiguration.MentionUsersOnReply,
										false,
										false,
										existingServiceConfiguration.AutoReplyToChatPluingGetStartedText);
									existingService.Configuration = existingServiceConfiguration.Serialize();

									ServiceDAO.Update(existingService);

									var newParameters = existingService.AsDictionary(true, "Configuration", "Settings");

									var entityName = oldParameters.ContainsKey("Name") ? oldParameters["Name"] : existingService.Name;
									DAL.UserLogDAO.Insert(this.LoggedUser
										, SystemEntityTypes.Services
										, SystemActionTypes.Edit
										, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
										, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
										, existingService.ID
										, entityName);
								}
							}
						}
					}
				}
				catch { }

				Response.Redirect(this.RedirectUrl, false);
			}
		}

		protected void buttonCancel_Click(object sender, EventArgs e)
		{
			Response.Redirect(this.RedirectUrl);
		}

		protected override string GetServiceToCopyId()
		{
			return hiddenServiceToCopy.Value;
		}

		protected override void CleanHiddenServiceToCopyId()
		{
			hiddenServiceToCopy.Value = "";
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object BuildAuthorizationUriForFacebook(int? serviceId, DomainModel.ServiceTypes serviceType)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new InvalidOperationException("El usuario está deslogueado");

				DomainModel.Service service = null;
				if (serviceId != null)
					service = DAL.ServiceDAO.GetOneFromCache(serviceId.Value);

				string oem = null;

				var pages = (System.Web.Configuration.PagesSection) System.Web.Configuration.WebConfigurationManager.GetSection("system.web/pages");
				if (!pages.Theme.Equals("Default"))
					oem = pages.Theme;

				IDictionary<string, object> parameters;
				Uri uri = Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.BuildAuthorizationUri(service, oem, out parameters);

				Yoizen.Social.SocialServices.Facebook.Wrappers.User fbUser = null;
				Yoizen.Social.SocialServices.Facebook.Wrappers.Accounts accounts = null;

				if (user.Parameters.ContainsKey(DomainModel.User.FacebookMessengerUserAccessTokenParameter) &&
					!string.IsNullOrEmpty(user.Parameters[DomainModel.User.FacebookMessengerUserAccessTokenParameter]))
				{
					try
					{
						Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetUserAccountInfo(user.Parameters[DomainModel.User.FacebookMessengerUserAccessTokenParameter], out fbUser, out accounts);
					}
					catch
					{
						fbUser = null;
						accounts = null;
					}
				}

				return new
				{
					Success = true,
					Uri = uri.AbsoluteUri,
					User = fbUser,
					Accounts = accounts
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetAccountInfoForFacebook(string accessToken, int expires)
		{
			try
			{
				string longLivedAccessToken = null;
				int? longLivedExpires = 0;

				if (expires > 0)
				{
					TimeSpan expiresTimeSpan = TimeSpan.FromSeconds(expires);
					if (expiresTimeSpan.TotalHours < 24)
						Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetLongLivedAccessToken(accessToken, out longLivedAccessToken, out longLivedExpires);
				}

				Yoizen.Social.SocialServices.Facebook.Wrappers.User user;
				Yoizen.Social.SocialServices.Facebook.Wrappers.Accounts accounts;

				if (longLivedAccessToken != null)
				{
					Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetUserAccountInfo(longLivedAccessToken, out user, out accounts);
				}
				else
				{
					Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetUserAccountInfo(accessToken, out user, out accounts);
				}

				return new
				{
					Success = true,
					User = user,
					Accounts = accounts,
					LongLivedAccessToken = longLivedAccessToken
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetAccountsAfter(string accessToken, string after)
		{
			try
			{
				var accounts = Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetAccountsAfter(accessToken, after);
			
				return new
				{
					Success = true,
					Accounts = accounts
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetAccessTokenInfoForFacebook(string accessToken)
		{
			try
			{
				Yoizen.Social.SocialServices.Facebook.Wrappers.AccessToken accessTokenInfo;
				accessTokenInfo = Yoizen.Social.SocialServices.Facebook.FacebookMessengerTokens.GetAccessTokenInfo(accessToken);

				return new
				{
					Success = true,
					Info = accessTokenInfo
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsPageUsedInAnotherService(int? serviceId, string pageIdStr)
		{
			try
			{
				if (string.IsNullOrEmpty(pageIdStr))
					throw new ArgumentNullException(nameof(pageIdStr));

				long pageId = long.Parse(pageIdStr);

				var existingServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				if (existingServices != null && existingServices.Any())
				{
					foreach (var existingService in existingServices)
					{
						if (serviceId != null && existingService.ID == serviceId.Value)
							continue;

						if ((existingService.Type == ServiceTypes.FacebookMessenger || existingService.Type == ServiceTypes.Facebook || existingService.Type == ServiceTypes.FacebookRt) &&
							existingService.Enabled)
						{
							long existingPageId;
							bool usesPrivateMessaging;
							if (existingService.Type == ServiceTypes.FacebookMessenger)
							{
								var configuration = new Social.SocialServices.Facebook.FacebookMessengerServiceConfiguration(existingService.Configuration);
								existingPageId = configuration.PageId;
								usesPrivateMessaging = true;
							}
							else
							{
								var configuration = new Social.SocialServices.Facebook.FacebookServiceConfiguration(existingService.Configuration);
								existingPageId = configuration.PageId;
								usesPrivateMessaging = configuration.UseDirectMessages;
							}

							if (existingPageId == pageId)
							{
								Tracer.TraceInfo("El servicio ({0} - {1}) que monitorea la misma cuenta. Todo mensaje privado será ignorado", existingService.ID, existingService.Name);

								return new
								{
									Success = true,
									ExistsAnotherService = true,
									Service = new
									{
										ID = existingService.ID,
										Name = existingService.Name,
										Type = existingService.Type
									},
									UsesPrivateMessaging = usesPrivateMessaging
								};
							}
						}
					}
				}

				return new
				{
					Success = true,
					ExistsAnotherService = false
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsPageOwnedByBusiness(string accessToken)
		{
			try
			{
				if (string.IsNullOrEmpty(accessToken))
					throw new ArgumentNullException(nameof(accessToken));

				bool isPageOwnedByBusiness = SocialServices.Facebook.FacebookMessengerTokens.IsPageOwnedByBusiness(accessToken);

				return new
				{
					Success = true,
					IsPageOwnedByBusiness = isPageOwnedByBusiness
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveServicesByServiceType(int serviceId)
		{
			return RetrieveServicesByServiceType(serviceId, ServiceTypes.FacebookMessenger);
		}
		#endregion
	}
}