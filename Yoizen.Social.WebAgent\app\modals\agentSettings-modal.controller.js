(function () {
    'use strict';
    angular
        .module('socialApp')
        .controller('AgentSettingsController', AgentSettingsController);

    AgentSettingsController.$inject = [
        'authenticationService',
        'agentService',
        'settingsService',
        '$scope',
        '$element',
        'CONFIG_INFO',
        '$http',
        '$log',
        'toastr',
        'close',
        'utilsService',
        '$translate'
    ];

    function AgentSettingsController(authenticationService,
                                     agentService,
                                     settingsService,
                                     $scope,
                                     $element,
                                     CONFIG_INFO,
                                     $http,
                                     $log,
                                     toastr,
                                     close,
                                     utilsService,
                                     $translate) {
        $scope.saveAgentSettings = saveAgentSettings;
        $scope.cancel = cancel;
        $scope.close = close;
        $scope.otherServicesThanMailAndChat = otherServicesThanMailAndChat;
        $scope.agentService = agentService;
        $scope.authenticationService = authenticationService;
        $scope.configuration = {
            agent: settingsService.settings.agent,
            settings: settingsService.settings.agent.settings,
            useSounds: settingsService.settings.agent.settings.useSounds,
            signature: settingsService.settings.agent.settings.signature,
            fontSize: settingsService.settings.agent.settings.fontSize,
            fontFamily: settingsService.settings.agent.settings.fontFamily,
            playbackRate: settingsService.settings.agent.settings.playbackRate,
            sendMode: settingsService.settings.agent.settings.sendMode
        };
        $scope.systemSettings = settingsService.settings.context.systemSettings;
        $scope.isInTheCloud = typeof(CONFIG_INFO.inTheCloud) === 'boolean' && CONFIG_INFO.inTheCloud;

        if (typeof($scope.configuration.fontFamily) === 'undefined' || $scope.configuration.fontFamily === null) {
            $scope.configuration.fontFamily = '';
        }

        if (typeof($scope.configuration.playbackRate) !== 'string') {
            $scope.configuration.playbackRate = '1';
        }

        if (typeof($scope.configuration.sendMode) !== 'string')  {
            $scope.configuration.sendMode = 'both';
        }

        if (typeof($scope.configuration.signature) === 'undefined') {
            if (typeof(settingsService.settings.agent.lastName) === 'undefined' ||
                settingsService.settings.agent.lastName === null ||
                settingsService.settings.agent.lastName.length === 0) {
                $scope.configuration.signature = '^' + settingsService.settings.agent.firstName.substr(0, 2);
            }
            else {
                $scope.configuration.signature = '^' + settingsService.settings.agent.firstName.substr(0, 1) + settingsService.settings.agent.lastName.substr(0, 1);
            }
        }

        if (typeof($scope.configuration.fontSize) === 'undefined') {
            $scope.configuration.fontSize = '8';
        }

        function saveAgentSettings() {
            settingsService.settings.agent.settings.useSounds = $scope.configuration.useSounds;
            settingsService.settings.agent.settings.signature = $scope.configuration.signature;
            settingsService.settings.agent.settings.fontSize = $scope.configuration.fontSize;
            settingsService.settings.agent.settings.playbackRate = $scope.configuration.playbackRate;
            settingsService.settings.agent.settings.sendMode = $scope.configuration.sendMode;
            if ($scope.isInTheCloud) {
                settingsService.settings.agent.settings.fontFamily = $scope.configuration.fontFamily;
            }

            agentSavingSettings(settingsService.settings.agent)
                .then(function () {
                    toastr.success($translate.instant('DATA_WAS_SAVED'));

                    utilsService.loadCustomFonts(settingsService.settings.agent.settings.fontFamily);

                    utilsService.updatePlaybackRate(settingsService.settings.agent.settings.playbackRate);

                    utilsService.updateSendMode(settingsService.settings.agent.settings.sendMode);
                    
                    close();
                })
                .catch(function (error) {
                    toastr.error($translate.instant('ERROR_SAVING_DATA'));
                    $log.info(error);
                });
        }

        function agentSavingSettings(agent) {
            let url = `${CONFIG_INFO.baseUrl}agents/updatesettings?id=${agent.id}`;
            return $http.post(url, agent.settings);
        }

        function otherServicesThanMailAndChat() {
            for (var i = 0; i < settingsService.settings.context.services.length; i++) {
                var service = settingsService.settings.context.services[i];

                if (service.socialServiceType !== SocialServiceTypes.Mail &&
                    service.socialServiceType !== SocialServiceTypes.Chat) {
                    return true;
                }
            }
        }

        function cancel() {
            close(null, 100);
        }
    }

})();
