﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />

var onlyVisibles = true;
var $divLoading;
var $divMustSelectAtLeastOneMessage;
var $divReplyAllWithChatMessagesFailed;
var $divCannotReserveToAnyAgent;
var $messageNoResults;
var $messageTotalResults;
var $tdTotalResults;
var $spanShowingResults;
var $divWithResults;
var $divLoadMoreResults;
var $messageCouldNotLoadMoreResults;
var $ulQueues;
var $divQueueLoadError;
var $divQueueInfo;
var $textboxFilter;
var $checkboxFilterInGroupedMessages;
var $textboxMessageIDs;
var $textboxCasesIDs;
var $selectSocialServiceType;
var $selectFilterService;
var $selectMessageType;
var $selectTagInCase;
var $selectSystemTimeComparison;
var $textboxSystemTime;
var $selectQueueTimeComparison;
var $textboxQueueTime;
var $selectOrderBy;
var $selectFilterServiceFilters;
var $selectReserveAgent;
var $selectMoveMessageToQueue;
var $messageNoAgentsQueues;
var $selectTags;
var $divFilter;
var $tableMessages;
var $buttonFilter;
var $buttonApplyTags;
var $buttonDiscard;
var $buttonReleaseMessage;
var $buttonReserveMessage;
var $buttonVIM;
var $buttonMoveMessageToQueue;
var $buttonClearShouldNotBeAssigned;
var $buttonReplyAll;
var $textareaAnswerBodyEmail;
var $textboxPublicTwitterReply;
var $textboxGenericReply;
var $spanGenericReplyCounter;
var $spanPublicReplyCounter;
var $divReplyAllForms;
var $divColumns;
var $checkboxColumnID;
var $checkboxColumnCaseID;
var $checkboxColumnService;
var $checkboxColumnVIM;
var $checkboxColumnPrivate;
var $checkboxColumnBody;
var $checkboxColumnReservedTo;
var $checkboxColumnHasAttach;
var $checkboxColumnReturnedToQueue;
var $checkboxColumnTags;

var queue;
var currentMessages;
var currentFilter;
var moreRecordsAvailable;
var selectedServices;
var selectedSocialServiceTypes;
var selectedMessagesContainsPublic;
var selectedMessagesContainsPrivate;
var $allMessageCheckbox;
var $selectAllMessagesButton;

jQuery(document).ready(function () {
	$.colorbox.settings.trapFocus = false;

	$divLoading = $('#divLoading');
	$divMustSelectAtLeastOneMessage = $('#divMustSelectAtLeastOneMessage');
	$divReplyAllWithChatMessagesFailed = $('#divReplyAllWithChatMessagesFailed');
	$divCannotReserveToAnyAgent = $('#divCannotReserveToAnyAgent');
	$messageNoResults = $('#messageNoResults');
	$messageTotalResults = $('#messageTotalResults');
	$tdTotalResults = $('td:last', $messageTotalResults);
	$spanShowingResults = $('span[rel=showing]', $tdTotalResults);
	$divWithResults = $('#divWithResults');
	$divLoadMoreResults = $('#divLoadMoreResults');
	$messageCouldNotLoadMoreResults = $('#messageCouldNotLoadMoreResults');
	$divQueueLoadError = $('#divQueueLoadError');
	$divQueueInfo = $('#divQueueInfo');
	$textboxFilter = $('#textboxFilter');
	$checkboxFilterInGroupedMessages = $('#checkboxFilterInGroupedMessages');
	$textboxMessageIDs = $('#textboxMessageIDs');
	$textboxCasesIDs = $('#textboxCasesIDs');
	$selectSocialServiceType = $('#selectSocialServiceType');
	$selectFilterService = $('#selectFilterService');
	$selectFilterServiceFilters = $('#selectFilterServiceFilters');
	$selectMessageType = $('#selectMessageType');
	$selectTagInCase = $('#selectTagInCase');
	$selectSystemTimeComparison = $('#selectSystemTimeComparison');
	$textboxSystemTime = $('#textboxSystemTime');
	$selectQueueTimeComparison = $('#selectQueueTimeComparison');
	$textboxQueueTime = $('#textboxQueueTime');
	$selectOrderBy = $('#selectOrderBy');
	$ulQueues = $('#ulQueues');
	$selectReserveAgent = $('#selectReserveAgent');
	$selectMoveMessageToQueue = $('#selectMoveMessageToQueue');
	$selectMoveMessageToQueue.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$messageNoAgentsQueues = $('div[id$=messageNoAgentsQueues]');
	$selectTags = $('#selectTags');
	$divFilter = $('#divFilter');
	$tableMessages = $('#tableMessages');
	$buttonFilter = $('#buttonFilter');
	$buttonApplyTags = $('#buttonApplyTags');
	$buttonDiscard = $('#buttonDiscard');
	$buttonReleaseMessage = $('#buttonReleaseMessage');
	$buttonReserveMessage = $('#buttonReserveMessage');
	$buttonVIM = $('#buttonVIM');
	$buttonMoveMessageToQueue = $('#buttonMoveMessageToQueue');
	$buttonClearShouldNotBeAssigned = $('#buttonClearShouldNotBeAssigned');
	$buttonReplyAll = $('#buttonReplyAll');
	$textareaAnswerBodyEmail = $('#textareaAnswerBodyEmail');
	$textboxGenericReply = $('#textboxGenericReply');
	$textboxPublicTwitterReply = $('#textboxPublicTwitterReply');
	$spanGenericReplyCounter = $('#spanGenericReplyCounter');
	$spanPublicReplyCounter = $('#spanPublicReplyCounter');
	$divReplyAllForms = $('#divReplyAllForms');
	$divColumns = $('#divColumns');
	$checkboxColumnID = $('#checkboxColumnID', $divColumns);
	$checkboxColumnCaseID = $('#checkboxColumnCaseID', $divColumns);
	$checkboxColumnService = $('#checkboxColumnService', $divColumns);
	$checkboxColumnVIM = $('#checkboxColumnVIM', $divColumns);
	$checkboxColumnPrivate = $('#checkboxColumnPrivate', $divColumns);
	$checkboxColumnBody = $('#checkboxColumnBody', $divColumns);
	$checkboxColumnReservedTo = $('#checkboxColumnReservedTo', $divColumns);
	$checkboxColumnHasAttach = $('#checkboxColumnHasAttach', $divColumns);
	$checkboxColumnReturnedToQueue = $('#checkboxColumnReturnedToQueue', $divColumns);
	$checkboxColumnTags = $('#checkboxColumnTags', $divColumns);
	$allMessageCheckbox = $('#allMessageCheckbox');
	$selectAllMessagesButton = $('#selectAllMessagesButton');

	$selectTags.multiselect({ multiple: true, noneSelectedText: "Seleccione alguna etiqueta", selectedList: 20, buttonWidth: '>500' }).multiselectfilter();
	$selectTags.change(function () {
		setTimeout($.colorbox.resize, 100);
	});

	$textboxGenericReply.keyup(function () {
		$spanGenericReplyCounter.text($textboxGenericReply.val().length);
	}).trigger('keyup');

	$textboxPublicTwitterReply.keyup(function () {
		$spanPublicReplyCounter.text($textboxPublicTwitterReply.val().length);
	}).trigger('keyup');

	if (typeof (twitterStatusTextCharacterLimit) !== 'undefined')
		$textboxPublicTwitterReply.attr('maxlength', twitterStatusTextCharacterLimit);

	$('input[type=checkbox]', $divColumns).change(function () {
		var $this = $(this);
		var checked = $this.is(':checked');
		var colToShow = $this.attr('colToShow');

		var $tds = $('td.' + colToShow + ',th.' + colToShow, $tableMessages);
		if (checked)
			$tds.show();
		else
			$tds.hide();
	});

	UpdateMessageCountForQueues();

	$textboxFilter.val('');
	$textboxFilter.keyup(function () {
		var filter = $textboxFilter.val();
		if (filter.length > 0) {
			$checkboxFilterInGroupedMessages.removeAttr('disabled');
		}
		else {
			$checkboxFilterInGroupedMessages.attr('disabled', 'disabled');
			$checkboxFilterInGroupedMessages.removeAttr('checked');
		}
	}).trigger('keyup');

	var $spanExpandCollapseBody = $('#spanExpandCollapseBody');
	$spanExpandCollapseBody.click(function () {
		var $div = $('tr.normal div,tr.alternate div.ellipsis', $tableMessages);
		if ($spanExpandCollapseBody.hasClass('fa-angle-double-right')) {
			//$div.toggleClass('ellipsis', false);
			$div.css('width', '400px');
			$div.css('max-width', '400px');
			$spanExpandCollapseBody.tooltip('hide').attr('data-original-title', 'Mostrar contenido recortado').tooltip('show');
			$spanExpandCollapseBody.removeClass('fa-angle-double-right');
			$spanExpandCollapseBody.addClass('fa-angle-double-left');
		}
		else {
			//$div.toggleClass('ellipsis', true);
			$div.css('width', '200px');
			$div.css('max-width', '200px');
			$spanExpandCollapseBody.addClass('fa-angle-double-right');
			$spanExpandCollapseBody.removeClass('fa-angle-double-left');
			$spanExpandCollapseBody.tooltip('hide').attr('data-original-title', 'Mostrar contenido completo').tooltip('show');
		}
	});

	var $option = $('<option></option>');
	$selectFilterService.append($option);
	$option.attr('value', '');
	$option.text('Todos');
	$option.attr('data-i18n', 'supervisor-messages-service_type-all');

	for (var i = 0; i < servicesAndFilters.length; i++) {
		var service = servicesAndFilters[i];

		$option = $('<option></option>');
		$selectFilterService.append($option);
		$option.attr('value', service.ID);
		$option.text(service.Name);
	}

	$option = $('<option></option>');
	$selectSocialServiceType.append($option);
	$option.attr('value', '');
	$option.text('Todos');
	$option.attr('data-i18n', 'supervisor-messages-service_type-all');
	for (var i = 0; i < socialServiceTypes.length; i++) {
		var socialServiceType = socialServiceTypes[i];
		$option = $('<option></option>');
		$selectSocialServiceType.append($option);
		$option.attr('value', socialServiceType.ID);
		$option.text(socialServiceType.Name);
	}

	$selectSystemTimeComparison.multiselect({ multiple: false, selectedList: 1, noneSelectedText: 'Seleccione el operador' });
	$selectSystemTimeComparison.change(function () {
		var val = $selectSystemTimeComparison.val();
		$textboxSystemTime.toggle(val.length > 0);
		$.colorbox.resize();
	});
	$selectQueueTimeComparison.multiselect({ multiple: false, selectedList: 1, noneSelectedText: 'Seleccione el operador' });
	$selectQueueTimeComparison.change(function () {
		var val = $selectQueueTimeComparison.val();
		$textboxQueueTime.toggle(val.length > 0);
		$.colorbox.resize();
	});
	$selectMessageType.multiselect({ multiple: false, selectedList: 1 });
	$selectTagInCase.multiselect({ multiple: false, selectedList: 1, menuWidth: 'auto', buttonWidth: '>500' });
	$selectSocialServiceType.multiselect({ multiple: false, selectedList: 1 });
	$selectFilterService.multiselect({ multiple: false, selectedList: 1 });
	$selectFilterServiceFilters.multiselect({ multiple: false, selectedList: 1 });
	$selectOrderBy.multiselect({ multiple: false, selectedList: 1});

	$selectFilterService.change(function () {
		$selectFilterServiceFilters.empty();

		$option = $('<option></option>');
		$selectFilterServiceFilters.append($option);
		$option.attr('value', '');
		$option.text('Ninguno');
		$option.attr('data-i18n', 'supervisor-settings-none');

		var service = GetServiceById($selectFilterService.val());
		if (service != null) {
			for (var i = 0; i < service.Filters.length; i++) {
				$option = $('<option></option>');
				$selectFilterServiceFilters.append($option);
				$option.attr('value', service.Filters[i].ID);
				$option.text(service.Filters[i].Name);
			}
		}

		$selectFilterServiceFilters.multiselect("refresh");
	}).trigger('change');

	if (typeof (queues) != 'undefined') {
		for (var i = 0; i < queues.length; i++) {
			var queue = queues[i];
			var $li = $('<li class="sideNavItem"></li>');
			$li.attr('rel', queue.ID);

			var $anchor = $('<a></a>');
			$li.append($anchor);
			$anchor.addClass('item');
			$anchor.attr('href', 'javascript:LoadQueue(' + queue.ID + ', null)');

			$anchor.append('<div class="rfloat"><span class="count uiSideNavCount"><span class="countValue fss">0</span></span><span class="grip"></span></div>');

			var $div = $('<div></div>');
			$anchor.append($div);

			var $span = $('<span></span>');
			$div.append($span);
			$span.addClass('imgWrap fa fa-lg fa-inbox');
			$span.css('top', '');

			$span = $('<span></span>');
			$div.append($span);
			$span.addClass('linkWrap');
			$span.addClass('hasCount');
			$span.text(queue.Name);

			$ulQueues.append($li);
		}
	}

	if (location.hash) {
	    var hash = location.hash;
	    if (hash.startsWith('#'))
	        hash = hash.substring(1);

	    try {
	        LoadQueue(parseInt(hash));
	    }
	    catch (e) {
	        LoadQueue(queues[0].ID);
	    }
	}
	else {
	    LoadQueue(queues[0].ID);
	}

	$allMessageCheckbox.click(function () {
		if (!this.checked) {
			onlyVisibles = !this.checked;
		}
	});
	
	$selectAllMessagesButton.click(function () {
		onlyVisibles = false;
	});
});

function i18nLoaded() {
	$selectSystemTimeComparison.multiselect('option', 'noneSelectedText', $.i18n('supervisor-messages-select_operator'));
	$selectQueueTimeComparison.multiselect('option', 'noneSelectedText', $.i18n('supervisor-messages-select_operator'));
	$selectTags.multiselect('option', 'noneSelectedText', $.i18n('configuration-queues-select_tag'));
}

function GetServiceById(id) {
	for (var i = 0; i < servicesAndFilters.length; i++) {
		var service = servicesAndFilters[i];
		if (service.ID == id)
			return service;
	}

	return null;
}

/* Cargar Servicio */

function LoadQueue(queueId, filter, callback) {
	if (queue != null) {
		if (queue.ID == queueId) {
			return;
		}
		else {
			$selectOrderBy.val(1);
			$selectOrderBy.multiselect("refresh");
		}
	}

	if (typeof (callback) != 'function')
		callback = null;

	if (typeof (filter) != 'object')
		filter = null;

	if (filter == null)
	    ClearFilter(false);

    if (history.pushState) {
        history.pushState(null, null, '#' + queueId);
    }
    else {
        location.hash = '#' + queueId;
    }

	$buttonClearShouldNotBeAssigned.parent().hide();

	$('li.sideNavItem', $ulQueues).removeClass('selectedItem');
	$('li.sideNavItem[rel=' + queueId + ']', $ulQueues).addClass('selectedItem');

	$selectMoveMessageToQueue.empty();
	for (var i = 0; i < queues.length; i++) {
		if (queues[i].ID != queueId) {
			var $option = $('<option></option>');
			$option.attr('value', queues[i].ID);
			$option.text(queues[i].Name);
			$selectMoveMessageToQueue.append($option);
		}
	}

	$selectMoveMessageToQueue.multiselect('refresh');

	var data = { queueId: queueId, filter: filter, orderBy: parseInt($selectOrderBy.val(), 10) };

	if (console)
		console.info("Se buscará datos de la cola: %o", data);
	var dataToSend = JSON.stringify(data);

	queue = null;
	moreRecordsAvailable = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onLoad: function () {
			$.colorbox.resize();

			$.ajax({
				type: "POST",
				url: "Messages.aspx/RetrieveQueue",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						queue = data.d.Queue;
						$divQueueInfo.show();
						$divQueueLoadError.hide();
					}
					else {
						if (console)
							console.error('No se pudo cargar información de mensajes: %s', data.d.Error.Message);
						$divQueueLoadError.show();
						$divQueueInfo.hide();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					var error = JSON.parse(jqXHR.responseText);
					if (console)
						console.error('No se pudo cargar información de mensajes: %s', error.Message);
					$divQueueLoadError.show();
					$divQueueInfo.hide();
				}
			});

			setTimeout(LoadResults, 500, callback);
		}
	});
}

function Refresh() {
	if (queue != null) {
		var queueId = queue.ID;
		queue = null;

		var filter;
		if (currentMessages !== null && currentFilter !== null)
			filter = $.extend({}, currentFilter, { pageSize: currentMessages.length });
		else
			filter = currentFilter;

		var $buttonFilterParent = $buttonFilter.parent();
		if (filter != null) {
			if (!$messageTotalResults.hasClass('activefilter'))
				$messageTotalResults.addClass('activefilter');
		}
		else {
			if ($messageTotalResults.hasClass('activefilter'))
				$messageTotalResults.removeClass('activefilter');
		}

		onlyVisibles = true;

		LoadQueue(queueId, filter);
	}
}

function LoadResults(callback) {
	if (typeof (callback) != 'function')
		callback = null;

	if (queue == null) {
		$.colorbox.close();
		return;
	}

	if (queue.Count > 0) {
		$tableMessages.parent().show();
		$tableMessages.show();
		$messageNoResults.hide();
		$messageTotalResults.show();
		$divWithResults.show();
		$messageCouldNotLoadMoreResults.hide();
		
		currentMessages = [];
		LoadMessages(queue.Messages, true);

		moreRecordsAvailable = queue.MoreRecordsAvailable;
		if (queue.MoreRecordsAvailable)
			$divLoadMoreResults.show();
		else
			$divLoadMoreResults.hide();

		var percentage = queue.Messages.length * 100 / queue.Count;
		
		$spanShowingResults.text($.i18n('reports-globals-showing_records', queue.Messages.length, queue.Count, percentage.toFixed(2)));

		$tableMessages.parent().scrollLeft(0);

		$buttonApplyTags.parent().show();
		$buttonFilter.parent().show();
		$buttonDiscard.parent().show();

		if (queue.SupportsReservation) {
			$buttonReleaseMessage.parent().show();
			$buttonReserveMessage.parent().show();
		}
		else {
			$buttonReleaseMessage.parent().hide();
			$buttonReserveMessage.parent().hide();
		}
		$buttonMoveMessageToQueue.parent().show();

		$buttonVIM.parent().show();
		$buttonReplyAll.parent().show();
	}
	else {
		$tableMessages.parent().hide();
		$messageNoResults.show();
		$messageTotalResults.hide();
		$divWithResults.hide();
		$messageCouldNotLoadMoreResults.hide();

		$buttonApplyTags.parent().hide();
		$buttonDiscard.parent().hide();
		$buttonVIM.parent().hide();
		$buttonReleaseMessage.parent().hide();
		$buttonReserveMessage.parent().hide();
		$buttonMoveMessageToQueue.parent().hide();
		$buttonClearShouldNotBeAssigned.parent().hide();
		$buttonReplyAll.parent().hide();

		if (currentFilter == null) {
			$buttonFilter.parent().hide();
		} 
		else {
			/*en caso que el filtro este cargado y la consulta no traiga resultados muestro
			el filtro activo*/
			if (!$messageNoResults.hasClass('activefilter'))
				$messageNoResults.addClass('activefilter');
		}
	}

	UpdateMessageCountForQueues();

	if (queue.Agents == null || queue.Agents.length == 0) {
		$messageNoAgentsQueues.show();
		$buttonReserveMessage.parent().hide();
	}
	else {
		$messageNoAgentsQueues.hide();

		if (queue.Messages != null &&
			queue.Messages.length > 0 &&
			queue.SupportsReservation) {
			$buttonReserveMessage.parent().show();
		}
		else {
			$buttonReserveMessage.parent().hide();
		}
	}

	LoadTags(queue.Tags);

	$.colorbox.close();

	if (callback != null)
		callback();
}

function LoadMoreResults() {
	var dataToSend = JSON.stringify({ queueId: queue.ID, skip: currentMessages.length, filter: currentFilter, orderBy: parseInt($selectOrderBy.val(), 10) });

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onLoad: function () {
			$.ajax({
				type: "POST",
				url: "Messages.aspx/LoadMoreResults",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						$messageCouldNotLoadMoreResults.hide();

						LoadMessages(data.d.Messages);

						if (typeof (data.d.AreAllAgentsLogged) !== 'undefined')
							queue.AreAllAgentsLogged = data.d.AreAllAgentsLogged;

						moreRecordsAvailable = data.d.MoreRecordsAvailable;
						$divLoadMoreResults.toggle(data.d.MoreRecordsAvailable);

						var percentage = currentMessages.length * 100 / data.d.Total;

						$spanShowingResults.text($.i18n('reports-globals-showing_records', currentMessages.length, data.d.Total, percentage.toFixed(2)));
					}
					else {
						if (console)
							console.error('No se pudo cargar información de mensajes: %s', data.d.Error.MessageMessage);
						$messageCouldNotLoadMoreResults.show();
					}

					$.colorbox.close();
				},
				error: function (jqXHR, textStatus, errorThrown) {
					var error = JSON.parse(jqXHR.responseText);
					if (console)
						console.error('No se pudo cargar información de mensajes: %s', error.Message);
					$messageCouldNotLoadMoreResults.show();
					$.colorbox.close();
				}
			});
		}
	});
}

function LoadTags(queueTags) {
	var queueTagsFull;
	if (queueTags === null || queueTags.length === 0) {
		queueTagsFull = [];
	}
	else {
		queueTagsFull = tags.filter(function (tag) {
			return queueTags.indexOf(tag.ID) >= 0 ||
				tag.TagGroups.some(tg => tg !== null && tg.Queues !== null && tg.Queues.some(q => queueTags.indexOf(q) >= 0));
		});
	}

	var $option;

	$selectTags.empty();
	$selectTagInCase.empty();

	$option = $('<option></option>');
	$option.val('');
	$option.text($.i18n('globals-any'));
	$selectTagInCase.append($option);

	for (var i = 0; i < queueTagsFull.length; i++) {
		var tag = queueTagsFull[i];

		$option = $('<option></option>');
		$option.val(tag.ID);
		$option.text(tag.FullName);
		$selectTags.append($option);

		$option = $('<option></option>');
		$option.val(tag.ID);
		$option.text(tag.FullName);
		$selectTagInCase.append($option);
	}

	$selectTags.multiselect('refresh');
	$selectTagInCase.multiselect('refresh');
}

function LoadMessages(messages, clear) {
	var $thead = $('thead', $tableMessages);
	$('input[type=checkbox]', $thead).attr('checked', false);
	var $tbody = $('tbody', $tableMessages);

	if (clear) {
		$tbody.empty();
	}

	var showID = $checkboxColumnID.is(':checked');
	var showCaseID = $checkboxColumnCaseID.is(':checked');
	var showService = $checkboxColumnService.is(':checked');
	var showVIM = $checkboxColumnVIM.is(':checked');
	var showPrivate = $checkboxColumnPrivate.is(':checked');
	var showBody = $checkboxColumnBody.is(':checked');
	var showReservedTo = $checkboxColumnReservedTo.is(':checked');
	var showHasAttach = $checkboxColumnHasAttach.is(':checked');
	var showReturnedToQueue = $checkboxColumnReturnedToQueue.is(':checked');
	var showTags = $checkboxColumnTags.is(':checked');

	var socialServiceTypes = [];
	var services = [];

	var $spanExpandCollapseBody = $('#spanExpandCollapseBody');
	var isExpanded = $spanExpandCollapseBody.hasClass('fa-angle-double-left')
	for (var i = 0; i < messages.length; i++) {
		var message = messages[i];

		var found = false;
		for (var j = 0; j < currentMessages.length; j++) {
			if (currentMessages[j].ID == message.ID) {
				found = true;
				break;
			}
		}

		if (!found) {
			if (socialServiceTypes.indexOf(message.SocialServiceType) == -1)
				socialServiceTypes.push(message.SocialServiceType);

			if (services.indexOf(message.Service.ID) == -1)
				services.push(message.Service.ID);

			if (message.SocialServiceType == SocialServiceTypes.Twitter) {
				$selectMessageType.multiselect('enable');
			}
			else if (message.SocialServiceType == SocialServiceTypes.Facebook) {
				$selectMessageType.multiselect('enable');
			}

			var returnedToQueueTooltip = '';
			if (message.ReturnedToQueue) {
				returnedToQueueTooltip += message.TimesReturnedToQueue + ' veces';
				if (message.ReturnedToQueueDate != null)
					returnedToQueueTooltip += '<br />Fecha retorno: ' + DisplayDateTime(message.ReturnedToQueueDate);

				if (message.ShouldNotBeAssignedTo != null) {
					returnedToQueueTooltip += '<br />No puede ser asignado a: ' + message.ShouldNotBeAssignedTo;

					$buttonClearShouldNotBeAssigned.parent().show();
				}

				returnedToQueueTooltip = HtmlEncode(returnedToQueueTooltip);
			}

			var html = '<tr class="' + (currentMessages.length % 2 == 0 ? 'normal' : 'alternate') + '">' +
				'<td align="center" style="white-space:nowrap; width:30px;" class="inspect">' +
					'<a class="TooltipRight" href="javascript:ShowExtendedMessageInfo(' + message.ID + ')" title="Ver más información del mensaje" data-toggle="tooltip" data-placement="right" data-i18n-title="globals-viewmore-message">' +
						'<span class="fa fa-lg fa-search-plus"></span>' +
					'</a>' +
				'</td>' +
				'<td align="center" style="width:30px;"><input id="row_' + i + '_checkboxSelected" type="checkbox" rowid="' + message.ID + '"></td>' +
				'<td style="white-space:nowrap;' + (showID ? '' : 'display: none') + '" class="id">' + message.ID + '</td>' +
				'<td style="white-space:nowrap;' + (showCaseID ? '' : 'display: none') + '" class="case"><a href="javascript:ShowExtendedCaseInfo(' + message.Case.ID + ')" title="Ver más información del caso" data-i18n-title="globals-viewmore-case">' + message.Case.ID + '</a></td>' +
				'<td style="white-space:nowrap;">' + BuildDate(message.Date, message.DateText) + '</td>' +
				'<td style="white-space:nowrap;">' + BuildDate(message.InsertedDate, message.InsertedDateText) + '</td>' +
				'<td style="white-space:nowrap;' + (showService ? '' : 'display: none') + '" class="service">' +
					BuildService(message.Service) +
				'</td>' +
				'<td align="center" style="white-space:nowrap;' + (showVIM ? '' : 'display: none') + '" class="vim">' +
					(message.VIM ? '<span class="fa fa-lg fa-star starvim"></span>' : '') +
				'</td>' +
				'<td align="center" style="white-space:nowrap;' + (showPrivate ? '' : 'display: none') + '" class="private">' +
					(message.PrivateMessage ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
				'</td>' +
				'<td style="white-space:nowrap;' + (showBody ? '' : 'display: none') + '" class="body">' +
					'<div class="ellipsis"></div>' +
				'</td>' +
				'<td style="white-space:nowrap;" class="socialuser">' + BuildSocialUser(message.SocialUser) + '</td>' +
				'<td style="white-space:nowrap;' + (showReservedTo ? '' : 'display: none') + '" class="reserved">' + ((message.Agent == null) ? 'N/A' : message.Agent) + '</td>' +
				'<td align="center" style="white-space:nowrap;' + (showHasAttach ? '' : 'display: none') + '" class="hasattach">' +
					(message.HasAttach ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
				'</td>' +
				'<td align="center" style="white-space:nowrap;' + (showReturnedToQueue ? '' : 'display: none') + '" class="returned">' +
					(message.ReturnedToQueue ? '<span class="fa fa-lg fa-check-circle" title="' + returnedToQueueTooltip + '"></span>' : '') +
				'</td>' +
				'<td style="white-space:nowrap;' + ((message.AgentWorkingWithCase == null) ? 'text-align: center' : '') + '" class="caseassigned">' + ((message.AgentWorkingWithCase == null) ? '<span class="fa fa-lg fa-no"></span>' : message.AgentWorkingWithCase) + '</td>' +
				'<td style="white-space:nowrap;' + (showTags ? '' : 'display: none') + '" class="casetags">' +
					BuildTags(message.Tags) + 
				'</td>' +
			"</tr>";

			var $html = $(html);
			var $body = $('td.body div.ellipsis', $html);
			if (typeof (message.EmptyBody) === 'boolean' && message.EmptyBody) {
				$body.html($.i18n('globals-empty-body'));
			}
			else if (typeof (message.Parameters) !== 'undefined' && typeof (message.Parameters.IsHidden) !== 'undefined' && message.Parameters.IsHidden == "True") {
				$body.html('<span class="fa fa-fw fa-eye-slash" title="Mensaje oculto" data-i18n-title="reports-messageinfo-hidden_message"></span> <span data-i18n="reports-messageinfo-hidden_message">Mensaje oculto</span>');
			}
			else {
				$body.text(message.Body);
			}

			if (typeof (message.IsEvent) === 'boolean' && message.IsEvent) {
				$body.prepend('<span class="fas fa-bolt" style="margin-right: 3px" title="Mensaje encolado luego de retomar un caso con marca de pendiente de respuesta del cliente" data-i18n-title="globals-message-event_from_pending_reply"></span>');
			}

			if (isExpanded) {
				$body.css('width', '400px');
			}

			$html.prop('message', message);

			$tbody.append($html);

			$html.find('[data-toggle="tooltip"]').tooltip();

			currentMessages.push(message);
		}
	}
	
	var supportsPublicAndPrivate = false;
	for (var i = 0; i < socialServiceTypes.length; i++) {
		var configuration = socialServiceTypesConfiguration.getByID(socialServiceTypes[i]);
		if (configuration != null)
			supportsPublicAndPrivate |= configuration.SupportsPublicAndPrivate;
	}

	if (!supportsPublicAndPrivate) {
		var colToShow = $checkboxColumnPrivate.attr('colToShow');

		var $tds = $('td.' + colToShow + ',th.' + colToShow, $tableMessages);

		$tds.hide();

		$checkboxColumnPrivate.removeAttr('checked');
		$checkboxColumnPrivate.attr('disabled', 'disabled');
	}
	else {
		$checkboxColumnPrivate.removeAttr('disabled');
	}

	LoadCompositedElements();

	if (clear) {
		BuildTableKeyboardSelection($tableMessages);
	}
}

function BuildTags(tags) {
    if (tags === null || tags.length === 0) {
        return $.i18n('globals-no_data');
    }

    var tagsText = '<div class="tags">';

	if (tags && tags.length > 0) {
		let style = '';
		if (typeof (tags[0].Color) === 'string' &&
			tags[0].Color.length > 0) {
			style = 'background-color: ' + tags[0].Color + '; color: ' + getContrast(tags[0].Color);
		}

		if (tags.length === 1) {
			tagsText += '<span class="tag" style="' + style + '">' + tags[0].FullName + '</span>';
		}
		else {
			tagsText += '<span class="tag" style="' + style + '" title="' + BuildTitleTags(tags) + '">' + tags[0].FullName + '...</span>';
		}
	}

    tagsText += '</div>';

    return tagsText;
}

function BuildTitleTags(tags) {
	var tagText = "";

	for (var i = 1; i < tags.length; i++) {

		tagText += i === 1
			? tags[i].FullName
			: ', ' + tags[i].FullName;
	}

	return tagText;
}

function BuildDate(date, dateText) {
	if (typeof (moment) != 'undefined' && typeof (loggedUserSettings) != 'undefined' && loggedUserSettings != null && typeof (loggedUserSettings.ReportsRealTimeQueuesDateFormat) != 'undefined') {
		return ConvertDateTimeToElapsedTime(date, loggedUserSettings.ReportsRealTimeQueuesDateFormat);
	}
	else if (typeof (moment) !== 'undefined') {
		return ConvertDateTimeToElapsedTime(date, 1);
	}
	else {
		return dateText;
	}
}

function BuildService(service) {
	var html = '<span class="fa-lg ';

	if (service.Type != ServiceTypes.TwitterSearches) {
		html += GetServiceTypeClass(service.Type) + '"></span> ';
	}
	else {
		html += 'fa-stack-05"><i class="fab fa-twitter-square fa-stack-1x"></i><i class="fa fa-search fa-stack-05x"></i></span> ';
	}

	html += service.Name;

	return html;
}

function BuildSocialUser(socialUser) {
	if (socialUser == null)
		return 'N/A';

	if (socialUser.Anonymous) {
		var displayText = $.i18n('reports-chats-anonymous');
		if (typeof (socialUser.Parameters) !== 'undefined' &&
			socialUser.Parameters !== null) {
			if (typeof (socialUser.Parameters.AnonymousDisplayName) !== 'undefined' &&
				socialUser.Parameters.AnonymousDisplayName !== null &&
				socialUser.Parameters.AnonymousDisplayName.length > 0) {
				displayText = socialUser.Parameters.AnonymousDisplayName;
			}

			if (typeof (socialUser.Parameters.AnonymousMail) !== 'undefined' &&
				socialUser.Parameters.AnonymousMail !== null &&
				socialUser.Parameters.AnonymousMail.length > 0) {
				displayText += ' <' + socialUser.Parameters.AnonymousMail + '>';
			}

			if (typeof (socialUser.Parameters.AnonymousPhone) !== 'undefined' &&
				socialUser.Parameters.AnonymousPhone !== null &&
				socialUser.Parameters.AnonymousPhone.length > 0) {
				displayText += ' - ' + socialUser.Parameters.AnonymousPhone;
			}
		}

		return '<span class="fa fa-lg fa-user-secret" title="Anónimo" data-i18n-title="reports-messagesquery-anonymous"></span> ' + he.encode(displayText);
	}

	var classes = '';
	var attributesToAdd = '';

	if (socialUser.VIP)
		classes += 'vip ';

	if (socialUser.Blocked)
		classes += 'blocked ';

	if (socialUser.Parameters != null &&
		typeof (socialUser.Parameters.Verified) === 'string' &&
		socialUser.Parameters.Verified.toLowerCase() === 'true') {
		classes += 'verified ';
	}

	if (socialUser.SocialServiceType == SocialServiceTypes.Twitter) {
		if (socialUser.Parameters != null) {
			if (socialUser.Parameters.IsFollowing != null)
				classes += (socialUser.Parameters.IsFollowing ? " following" : " notfollowing");

			if (socialUser.Parameters.NumberOfFollowers != null && socialUser.Parameters.NumberOfFollowers > 0)
				attributesToAdd += 'followers="' + socialUser.Parameters.NumberOfFollowers + '" ';
		}
	}

	return '<a class="' + classes + '" ' + attributesToAdd + ' href="javascript:ShowExtendedSocialUserProfileInfo(null, \'' + socialUser.ID + '\', ' + socialUser.SocialServiceType + ')">' + he.encode(socialUser.DisplayText) + '</a>';
}

function UpdateMessageCountForQueues() {
	var queueIds = new Array();
	for (var i = 0; i < queues.length; i++) {
		queueIds.push(queues[i].ID);
	}

	var dataToSend = JSON.stringify({ ids: queueIds });

	$.ajax({
		type: "POST",
		url: "Messages.aspx/RetrieveMessageCountForQueues",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			for (var i = 0; i < data.d.length; i++) {
				var $anchor = $('li[rel=' + data.d[i].ID + ']', $ulQueues);
				var $span = $('span.uiSideNavCount', $anchor);
				var $count = $('span.countValue', $span);
				$count.text(data.d[i].Count);

				if (data.d[i].Count > 0) {
					$span.addClass('countValueMoreThanZero');
				}
				else {
					$span.removeClass('countValueMoreThanZero');
				}
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			if (console)
				console.log('Ocurrió un error consultando: ' + jqXHR.responseText);
		}
	});
}

function ShowExtendedMessageInfo(messageId) {
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: "../Reports/MessageInfo.aspx?MessageID=" + messageId,
		width: '65%',
		height: '600px',
		initialHeight: '600px',
		maxHeight: '700px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			LoadMessageInfoContents();
		},
		onClosed: function () {
			var $facebookJssdk = $('#facebook-jssdk');
			$facebookJssdk.remove();
		}
	});
}

function ShowExtendedCaseInfo(caseId) {
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: "../Reports/CaseInfo.aspx?CaseID=" + caseId,
		width: '65%',
		height: '600px',
		initialHeight: '600px',
		maxHeight: '700px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			LoadCaseInfoContents();

			$.colorbox.resize();
		}
	});
}

function ShowExtendedSocialUserProfileInfo(socialUserProfile, socialUser, socialServiceType) {
	var url = "../Reports/SocialUserProfileInfo.aspx?SocialUserProfileID=" + (socialUserProfile || '') + '&SocialUserID=' + (socialUser || '') + '&SocialServiceTypeID=' + (socialServiceType || '');
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: url,
		width: '65%',
		height: '570px',
		initialHeight: '570px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			LoadSocialUserProfileInfoContents();

			$.colorbox.resize();
		}
	});
}

function ShowAttachInfo(messageId, index) {
	$.colorbox({
		transition: 'elastic',
		speed: 100,
		href: '../Reports/AttachInfo.aspx?MessageID=' + messageId + '&Index=' + index,
		width: '65%',
		height: '570px',
		initialHeight: '570px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function ShowFilterDialog() {
	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: $divFilter,
		width: '60%',
		initialWidth: '60%',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			LoadCurrentFilterOptions();
			$.colorbox.resize();
		}
	});
}

function ShowDiscardDialog() {
	if (onlyVisibles)
	{
		var messagesSelected = ValidateGridViewSelection($tableMessages);
		if (!messagesSelected) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}

		var messages = GetGridViewDataPropSelection($tableMessages, 'message');
		for (var i = 0; i < messages.length; i++) {
			if (messages[i].AgentWorkingWithCase !== null) {
				AlertDialog($.i18n('supervisor-messages-discard-title'), $.i18n('supervisor-messages-cannot_perform_action_case_assigned'), undefined, undefined, 'Error');
				return;
			}
		}

		var $textboxDiscardReason = $('#textboxDiscardReason');
		$textboxDiscardReason.val('');
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divDiscard",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function DiscardConfirm(closeCase) {
	var messageIds = GetGridViewSelection($tableMessages);
	var $textboxDiscardReason = $('#textboxDiscardReason');
	var discardReason = $textboxDiscardReason.val();

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, discardReason: discardReason, closeCase: closeCase, onlyVisibles: onlyVisibles });

			if (console) {
				if (console.group)
					console.group("Descarte");

				console.log('Se hará descarte con la siguiente información: ' + dataToSend);
			}

			$.ajax({
				type: "POST",
				url: "Messages.aspx/Discard",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (console) {
							if (failedIds != null && failedIds.length > 0)
								console.log('Fallaron descartar los siguientes mensajes: ' + JSON.stringify(failedIds));
							else
								console.log('Se descartaron todos los mensajes');
						}
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					error = true;
				}
			});

			if (console) {
				if (error)
					console.log('Ocurrió un error descartando los mensajes');

				if (console.groupEnd)
					console.groupEnd();
			}

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-discard"), $.i18n("supervisor-messages-discard_error"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-discard"), $.i18n("supervisor-messages-discarded_messages_all"), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-discard"), $.i18n("supervisor-messages-discarded_messages", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length) {
						AlertDialog($.i18n("supervisor-messages-discard"), $.i18n("supervisor-messages-cannot_discard"), '$.colorbox.close()');
					}
					else {
						AlertDialog($.i18n("supervisor-messages-discard"), $.i18n("supervisor-messages-discarded_and_not",messageIds.length - failedIds.length, failedIds.length), '$.colorbox.close()', Refresh);
					}
				}
			}, 500);
		}
	});
}

function ShowApplyTagsDialog() {
	if (onlyVisibles) {
		var messagesSelected = ValidateGridViewSelection($tableMessages);
		if (!messagesSelected) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}
	}

	$selectTags.val(null);
	$selectTags.multiselect('refresh');
	
	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divApplyTags",
		width: '70%x',
		initialWidth: '70%',
		preloading: false,
		closeButton: false
	});
}

function ApplyTagsConfirm() {
	var tagsSelected = $selectTags.val();
	var $divApplyTagsError = $('#divApplyTagsError');
	if (tagsSelected === null || tagsSelected.length === 0) {
		$divApplyTagsError.show();
		return;
	}

	$divApplyTagsError.hide();

	var messageIds = GetGridViewSelection($tableMessages);
	var tagIds = tagsSelected;

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, tagIds: tagIds, onlyVisibles: onlyVisibles });

			if (console) {
				if (console.group)
					console.group("Etiquetas");

				console.log('Se aplicarán etiquetas con la siguiente información: ' + dataToSend);
			}

			$.ajax({
				type: "POST",
				url: "Messages.aspx/ApplyTags",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (console) {
							if (failedIds != null && failedIds.length > 0)
								console.log('Fallaron agregar etiquetas a los siguientes mensajes: ' + JSON.stringify(failedIds));
							else
								console.log('Se agregaron etiquetas a todos los mensajes');
						}
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					error = true;
				}
			});

			if (console) {
				if (error)
					console.log('Ocurrió un error aplicando etiquetas a los mensajes');

				if (console.groupEnd)
					console.groupEnd();
			}

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-apply_tags"), $.i18n("supervisor-messages-error_applying_tags"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-apply_tags"), $.i18n("supervisor-messages-tag-applied_messages_all"), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-apply_tags"), $.i18n("supervisor-messages-tags_applied_to", messageIds.length), '$.colorbox.close()');
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-apply_tags"), $.i18n("supervisor-messages-cannot_apply_tags"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-apply_tags"), $.i18n("supervisor-messages-tags_applied_to_while", messageIds.length - failedIds.length, failedIds.length), '$.colorbox.close()');
				}
			}, 500);
		}
	});
}

function ShowReleaseMessageDialog() {
	if (onlyVisibles) {
		var messagesSelected = ValidateGridViewSelection($tableMessages);
		if (!messagesSelected) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divReleaseMessage",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function ReleaseMessageConfirm() {
	var messageIds = GetGridViewSelection($tableMessages);

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, onlyVisibles: onlyVisibles });

			if (console) {
				if (console.group)
					console.group("Liberar mensajes");

				console.log('Se liberarán los mensajes con la siguiente información: ' + dataToSend);
			}

			$.ajax({
				type: "POST",
				url: "Messages.aspx/Release",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (console) {
							if (failedIds != null && failedIds.length > 0)
								console.log('Fallaron liberar los siguientes mensajes: ' + JSON.stringify(failedIds));
							else
								console.log('Se liberaron todos los mensajes');
						}
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					error = true;
				}
			});

			if (console) {
				if (error)
					console.log('Ocurrió un error liberando los mensajes');

				if (console.groupEnd)
					console.groupEnd();
			}

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-release_message"), $.i18n("supervisor-messages-error_releasing_messages"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-release_message"), $.i18n("supervisor-messages-release_messages_all"), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-release_message"), $.i18n("supervisor-messages-messages_released", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-release_message"), $.i18n("supervisor-messages-cannot_release_messages"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-release_message"), $.i18n("supervisor-messages-messages_released_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
				}
			}, 500);
		}
	});
}

function ShowReserveMessageDialog() {

	var selectedSocialServiceTypes = [];
	if (onlyVisibles) {
		var messagesSelected = ValidateGridViewSelection($tableMessages);
		if (!messagesSelected) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}

		var messagesSelectedIndexes = GetGridViewSelectionIndexes($tableMessages);
		for (var i = 0; i < messagesSelectedIndexes.length; i++) {
			var index = messagesSelectedIndexes[i];
			var message = currentMessages[index];
			if (selectedSocialServiceTypes.indexOf(message.SocialServiceType) === -1)
				selectedSocialServiceTypes.push(message.SocialServiceType);
		}
	}

	var atLeastOneAgent = false;
	$selectReserveAgent.empty();
	for (var i = 0; i < queue.Agents.length; i++) {
		var agent = queue.Agents[i];
		if (agent.AllowedSocialServiceTypes !== null && agent.AllowedSocialServiceTypes.length > 0) {
			for (var j = 0; j < agent.AllowedSocialServiceTypes.length; j++) {
				if (selectedSocialServiceTypes.indexOf(agent.AllowedSocialServiceTypes[j]) >= 0 || !onlyVisibles) {
					var $option = $('<option></option>');
					$selectReserveAgent.append($option);
					$option.attr('value', agent.ID);
					$option.text(agent.FullName);
					atLeastOneAgent = true;
					break;
				}
			}
		}
	}

	if (!atLeastOneAgent) {
		ShowMessageDialog($divCannotReserveToAnyAgent);
		return;
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divReserveMessage",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function ReserveMessageConfirm() {
	var messageIds = GetGridViewSelection($tableMessages);
	var agentId = parseInt($selectReserveAgent.val());

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, agentId: agentId, onlyVisibles: onlyVisibles });

			if (console) {
				if (console.group)
					console.group("Reservar");

				console.log('Se reservarán los mensajes con la siguiente información: ' + dataToSend);
			}

			$.ajax({
				type: "POST",
				url: "Messages.aspx/Reserve",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (console) {
							if (failedIds != null && failedIds.length > 0)
								console.log('Fallaron reservar los siguientes mensajes: ' + JSON.stringify(failedIds));
							else
								console.log('Se reservaron todos los mensajes');
						}
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					error = true;
				}
			});

			if (console) {
				if (error)
					console.log('Ocurrió un error reservando los mensajes');

				if (console.groupEnd)
					console.groupEnd();
			}

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-reserve_message"), $.i18n("supervisor-messages-error_reserving_message"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-reserve_message"), $.i18n("supervisor-messages-messages_reserved_all"), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-reserve_message"), $.i18n("supervisor-messages-messages_reserved", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-reserve_message"), $.i18n("supervisor-messages-cannot_reserve_messages"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-reserve_message"), $.i18n("supervisor-messages-messages_reserved_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
				}
			}, 500);
		}
	});
}

function ShowVIMDialog() {
	if (onlyVisibles) {
		var messagesSelected = ValidateGridViewSelection($tableMessages);
		if (!messagesSelected) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divVIM",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function VIMConfirm() {
	var messageIds = GetGridViewSelection($tableMessages);

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, onlyVisibles: onlyVisibles });

			if (console) {
				if (console.group)
					console.group("VIM");

				console.log('Se hará VIM con la siguiente información: ' + dataToSend);
			}

			$.ajax({
				type: "POST",
				url: "Messages.aspx/VIM",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (console) {
							if (failedIds != null && failedIds.length > 0)
								console.log('Fallaron hacer VIM los siguientes mensajes: ' + JSON.stringify(failedIds));
							else
								console.log('Se marcaron todos los mensajes como VIM');
						}
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					error = true;
				}
			});

			if (console) {
				if (error)
					console.log('Ocurrió un error marcando los mensajes como VIM');

				if (console.groupEnd)
					console.groupEnd();
			}

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-mark_as_important"), $.i18n("supervisor-messages-error_marking"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-mark_as_important"), $.i18n("supervisor-messages-message_marked_all"), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-mark_as_important"), $.i18n("supervisor-messages-message_marked", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-mark_as_important"), $.i18n("supervisor-messages-cannot_mark"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-mark_as_important"), $.i18n("supervisor-messages-messages_marked_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
				}
			}, 500);
		}
	});
}

function ShowClearShouldNotBeAssignedDialog() {
	var messagesSelected = ValidateGridViewSelection($tableMessages);
	if (!messagesSelected) {
		ShowMessageDialog($divMustSelectAtLeastOneMessage);
		return;
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divClearShouldNotBeAssigned",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function ClearShouldNotBeAssignedConfirm() {
	var messageIds = GetGridViewSelection($tableMessages);

	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds });

			$.ajax({
				type: "POST",
				url: "Messages.aspx/ClearShouldNotBeAssigned",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					var errorResponse = JSON.parse(jqXHR.responseText);
					alert(errorResponse.Message);
				}
			});

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-allow_assign_to_agent"), $.i18n("supervisor-messages-error_marking_messages"), '$.colorbox.close()');
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-allow_assign_to_agent"), $.i18n("supervisor-messages-marked_to_assign", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-allow_assign_to_agent"), $.i18n("supervisor-messages-cannot_mark_to_assign"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-allow_assign_to_agent"), $.i18n("supervisor-messages-marked_to_assign_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
				}
			}, 500);
		}
	});
}

function ShowMoveMessageToQueueDialog() {
	var messagesSelected = ValidateGridViewSelection($tableMessages);
	if (!messagesSelected) {
		ShowMessageDialog($divMustSelectAtLeastOneMessage);
		return;
	}

	var messages = GetGridViewDataPropSelection($tableMessages, 'message');
	for (var i = 0; i < messages.length; i++) {
		if (messages[i].AgentWorkingWithCase !== null) {
			AlertDialog($.i18n('supervisor-messages-move_message_to_queue-title'), $.i18n('supervisor-messages-cannot_perform_action_case_assigned'), undefined, undefined, 'Error');
			return;
		}
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divMoveMessageToQueue",
		width: '600px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false
	});
}

function MoveMessageToQueueConfirm() {
	let messageIds = GetGridViewSelection($tableMessages);
	let destQueueId = $selectMoveMessageToQueue.val();

	LoadingDialog({
		title: $.i18n('globals-loading'),
		timeout: 300,
		autoClose: false,
		onTimeout: function () {
			let dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, destQueueId: destQueueId, onlyVisibles: onlyVisibles });

			$.ajax({
				type: "POST",
				url: "Messages.aspx/MoveToQueue",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					let failedIds = null;
					let error = false;

					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;

						if (typeof (data.d.NonWorkingTime) === 'boolean' &&
							data.d.NonWorkingTime) {
							AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-error_moving_messages-out_working_time"), '$.colorbox.close()');
							return;
						}

						if (typeof (data.d.NonConnectedAgents) === 'boolean' &&
							data.d.NonConnectedAgents) {
							AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-error_moving_messages-no_connected_agents"), '$.colorbox.close()');
							return;
						}
					}
					else {
						error = true;
					}

					if (error) {
						AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-error_moving_messages"), '$.colorbox.close()');
					}
					else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
						AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-messages_moved_all",), '$.colorbox.close()', Refresh);
					}
					else if (failedIds == null || failedIds.length == 0) {
						AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-messages_moved", messageIds.length), '$.colorbox.close()', Refresh);
					}
					else {
						if (failedIds.length == messageIds.length)
							AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-cannot_move_messages"), '$.colorbox.close()');
						else
							AlertDialog($.i18n("supervisor-messages-move_message_from_queue"), $.i18n("supervisor-messages-messages_moved_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Ocurrió un error consultando: ' + jqXHR.responseText);
				}
			});
		}
	});
}

/* Filtrado de mensajes */

function FilterMessages() {
	var messageIDs = $textboxMessageIDs.val();
	if (messageIDs.length > 0) {
		if (!/^[0-9]+(,[0-9]+)*$/.test(messageIDs)) {
			$textboxMessageIDs.parent().addClass('error');
			return;
		}
	}
	else {
		messageIDs = null;
	}
	$textboxMessageIDs.parent().removeClass('error');

	var casesIDs = $textboxCasesIDs.val();
	if (casesIDs.length > 0) {
		if (!/^[0-9]+(,[0-9]+)*$/.test(casesIDs)) {
			$textboxCasesIDs.parent().addClass('error');
			return;
		}
	}
	else {
		casesIDs = null;
	}
	$textboxCasesIDs.parent().removeClass('error');

	currentFilter = {
		text: $textboxFilter.val(),
		checkTextInGroupedMessages: $checkboxFilterInGroupedMessages.is(':checked'),
		socialServiceTypeId: $selectSocialServiceType.val(),
		messageTypeOnlyPrivate: $selectMessageType.val(),
		tagId: $selectTagInCase.val(),
		systemTimeComparison: $selectSystemTimeComparison.val(),
		systemTime: $textboxSystemTime.val(),
		queueTimeComparison: $selectQueueTimeComparison.val(),
		queueTime: $textboxQueueTime.val(),
		serviceId: $selectFilterService.val(),
		filterId: $selectFilterServiceFilters.val(),
		messageIDs: messageIDs,
		caseIDs: casesIDs
	};

	if (currentFilter.socialServiceTypeId.length > 0)
		currentFilter.socialServiceTypeId = parseInt(currentFilter.socialServiceTypeId);

	if (currentFilter.serviceId.length > 0)
		currentFilter.serviceId = parseInt(currentFilter.serviceId);

	currentMessages = null;
	Refresh();
}

function ClearFilter(callRefresh) {
	if (typeof (callRefresh) == 'undefined')
		callRefresh = true;

	$textboxFilter.val('');
	$checkboxFilterInGroupedMessages.removeAttr('checked');
	$textboxMessageIDs.val('');
	$textboxCasesIDs.val('');
	$selectSocialServiceType.val('');
	$selectFilterService.val('');
	$selectSystemTimeComparison.val('').trigger('change');
	$textboxSystemTime.val(0);
	$textboxQueueTime.val(0);
	$selectQueueTimeComparison.val('').trigger('change');
	$selectMessageType.val('');
	$selectTagInCase.val('');
	$selectOrderBy.val(1);

	$selectMessageType.multiselect('disable');

	$selectSocialServiceType.multiselect('refresh');
	$selectFilterService.multiselect('refresh');
	$selectSystemTimeComparison.multiselect('refresh');
	$selectQueueTimeComparison.multiselect('refresh');
	$selectMessageType.multiselect('refresh');
	$selectTagInCase.multiselect('refresh');
	$selectOrderBy.multiselect("refresh");

	$messageTotalResults.removeClass('activefilter');
	$messageNoResults.removeClass('activefilter');

	$selectFilterService.trigger('change');
	$selectFilterService.multiselect("widget").find("label").each(function (index) {
		if (index > 0) {
			var $label = $(this);
			var $input = $('input', $label);
			var $textSpan = $('span', $label);

			var service = servicesAndFilters[index - 1];
			var $typeSpan = $('<span class="fa-lg" style="margin-left: 5px;"></span>');

			if (service.Type != ServiceTypes.TwitterSearches) {
				$typeSpan.addClass(GetSocialServiceTypeClass(service.SocialServiceType));
			}
			else {
				$typeSpan.addClass('fa-stack-05');
				$typeSpan.append('<i class="fab fa-twitter-square fa-stack-1x"></i><i class="fa fa-search fa-stack-05x"></i>');
			}

			$typeSpan.insertAfter($textSpan);
		}
	});
	$selectSocialServiceType.multiselect("widget").find("label").each(function (index) {
		if (index > 0) {
			var $label = $(this);
			var $input = $('input', $label);
			var $textSpan = $('span', $label);

			var type = socialServiceTypes[index - 1].Type;
			var $typeSpan = $('<span class="fa-lg" style="margin-left: 5px;"></span>');
			$typeSpan.addClass(GetSocialServiceTypeClass(type));
			$typeSpan.insertAfter($textSpan);
		}
	});

	currentFilter = null;
	currentMessages = null;

	if (callRefresh)
		Refresh();
}

function FilterAppliesToMessage(serviceId, filterId, messageId) {
	var dataToSend = JSON.stringify({ queueId: queue.ID, serviceId: serviceId, filterId: filterId, messageId: messageId });

	var returnValue;

	$.ajax({
		type: "POST",
		url: "Messages.aspx/FilterAppliesToMessage",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		async: false,
		success: function (data) {
			if (data.d.Success) {
				returnValue = data.d.Result;
			}
			else {
				returnValue = false;
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			returnValue = false;
		}
	});

	return returnValue;
}

function ShowReplyAllForms() {
	if (typeof (queue.AreAllAgentsLogged) !== 'undefined' && queue.AreAllAgentsLogged) {
		AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-cannot_answer"), '$.colorbox.close()');
		return;
	}

	var $divReplyTwitterPublic = $('#divReplyTwitterPublic');
	var $h2ReplyTwitterPublic = $('#h2ReplyTwitterPublic');
	var $divReplyMail = $('#divReplyMail');
	var $h2ReplyMail = $('#h2ReplyMail');
	var $divReplyGeneric = $('#divReplyGeneric');
	var $h2ReplyGeneric = $('#h2ReplyGeneric');
	var $buttonReplyPredefinedAnswers = $('#buttonReplyPredefinedAnswers');
	var $buttonReplyPredefinedAnswersParent = $buttonReplyPredefinedAnswers.parent();
	var $messageCannotShowPredefinedAnswers = $('#messageCannotShowPredefinedAnswers');

	var $divPredefinedAnswers = $('#divPredefinedAnswers');
	$divPredefinedAnswers.hide();
	var $divReplyComposing = $('#divReplyComposing');
	$divReplyComposing.show();

	$spanPublicReplyCounter.text('0');
	$spanGenericReplyCounter.text('0');
	$textareaAnswerBodyEmail.val("").blur();
	$textboxPublicTwitterReply.val("");
	$textboxGenericReply.val("");

	$divReplyTwitterPublic.hide();
	$h2ReplyTwitterPublic.hide();
	$divReplyMail.hide();
	$h2ReplyMail.hide();
	$divReplyGeneric.hide();
	$h2ReplyGeneric.hide();

	if (onlyVisibles) {
		var messageIds = GetGridViewSelection($tableMessages);

		if (messageIds.length == 0) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}

		var messages = GetGridViewDataPropSelection($tableMessages, 'message');
		for (var i = 0; i < messages.length; i++) {
			if (messages[i].AgentWorkingWithCase !== null) {
				AlertDialog($.i18n('supervisor-messages-reply_all-title'), $.i18n('supervisor-messages-cannot_perform_action_case_assigned'), undefined, undefined, 'Error');
				return;
			}
		}

		selectedServices = [];
		selectedSocialServiceTypes = [];
		selectedMessagesContainsPublic = false;
		selectedMessagesContainsPrivate = false;

		for (var i = 0; i < messageIds.length; i++) {
			for (var c = 0; c < currentMessages.length; c++) {
				var message = currentMessages[c];
				if (message.ID == messageIds[i]) {
					if (message.SocialServiceType == SocialServiceTypes.Chat) {
						$.colorbox({
							transition: 'none',
							speed: 100,
							inline: true,
							href: $divReplyAllWithChatMessagesFailed,
							width: '600px',
							initialWidth: '600px',
							preloading: false,
							closeButton: false,
							onComplete: function () {
								$.colorbox.resize();
							}
						});
						return;
					}

					if (selectedServices.indexOf(message.Service.ID) == -1)
						selectedServices.push(message.Service.ID);

					if (selectedSocialServiceTypes.indexOf(message.SocialServiceType) == -1)
						selectedSocialServiceTypes.push(message.SocialServiceType);

					if (message.PrivateMessage)
						selectedMessagesContainsPrivate = true;
					else
						selectedMessagesContainsPublic = true;
				}
			}
		}

		if (selectedServices.length === 1 || queue.Templates.length > 0) {
			$buttonReplyPredefinedAnswersParent.removeClass('uiButtonDisabled');
			$buttonReplyPredefinedAnswers.removeAttr('disabled');
			$messageCannotShowPredefinedAnswers.hide();
		}
		else if (selectedServices.length > 1 && queue.Templates.length === 0) {
			$buttonReplyPredefinedAnswersParent.addClass('uiButtonDisabled');
			$buttonReplyPredefinedAnswers.attr('disabled', 'disabled');
			$messageCannotShowPredefinedAnswers.show();
		}

		var showGeneric = false;
		if (selectedSocialServiceTypes.length == 1) {
			if (selectedSocialServiceTypes[0] == SocialServiceTypes.Twitter && selectedMessagesContainsPublic && !selectedMessagesContainsPrivate) {
				$divReplyTwitterPublic.show();
				$h2ReplyTwitterPublic.show();
			}
			else if (selectedSocialServiceTypes[0] == SocialServiceTypes.Mail) {
				$divReplyMail.show();
				$h2ReplyMail.show();
			}
			else {
				showGeneric = true;
			}
		}
		else {
			showGeneric = true;
		}

		if (showGeneric) {
			$divReplyGeneric.show();
			$h2ReplyGeneric.show();

			if (selectedSocialServiceTypes.indexOf(SocialServiceTypes.Twitter) >= 0) {
				$('#messageGenericReply').hide();
				if (selectedMessagesContainsPublic) {
					if (typeof (twitterStatusTextCharacterLimit) !== 'undefined')
						$textboxGenericReply.attr('maxlength', twitterStatusTextCharacterLimit);
					else
						$textboxGenericReply.attr('maxlength', 280);
					$('#messageMaxLength120').show();
					$('#messageMaxLength10000').hide();
				}
				else {
					if (typeof (twitterDmTextCharacterLimit) !== 'undefined')
						$textboxGenericReply.attr('maxlength', twitterDmTextCharacterLimit);
					else
						$textboxGenericReply.attr('maxlength', 10000);

					$('#messageMaxLength120').hide();
					$('#messageMaxLength10000').show();
				}
			}
			else {
				$textboxGenericReply.removeAttr('maxlength');
				$('#messageGenericReply').show();
				$('#messageMaxLength120').hide();
				$('#messageMaxLength10000').hide();
			}
		}
	}
	else {
		$divReplyGeneric.show();
		$h2ReplyGeneric.show();
		$textboxGenericReply.removeAttr('maxlength');
		$('#messageGenericReply').show();
		$('#messageMaxLength120').hide();
		$('#messageMaxLength10000').hide();
	}


	var $divErrorEmptyText = $('#divErrorEmptyText');
	$divErrorEmptyText.hide();
	var $divErrorOverMaxLength = $('#divErrorOverMaxLength');
	$divErrorOverMaxLength.hide();

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: $divReplyAllForms,
		width: '800px',
		initialWidth: '400px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			if (typeof ($textareaAnswerBodyEmail.initialized) == 'undefined') {
				$textareaAnswerBodyEmail.cleditor({
					height: 200,
					width: '100%',
					fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
					bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
					controls:     // controls to add to the toolbar
					  "bold italic underline | font size " +
					  "style | color highlight removeformat | bullets numbering | outdent " +
					  "indent | alignleft center alignright justify | " +
					  "image link | source",
				});

				$textareaAnswerBodyEmail.initialized = true;
				$textareaAnswerBodyEmail.cleditor()[0].focus();
			}
			else {
				$textareaAnswerBodyEmail.cleditor()[0].refresh();
				$textareaAnswerBodyEmail.cleditor()[0].focus();
			}

			$.colorbox.resize();
		}
	});
}

function CancelReplyMessages() {
	$.colorbox.close();
}

function ReplyAllMessages(closeCase) {

	var messageIds = []; 

	if (onlyVisibles) {
		messageIds = GetGridViewSelection($tableMessages);

		if (messageIds.length == 0) {
			ShowMessageDialog($divMustSelectAtLeastOneMessage);
			return;
		}

		var maxLength = 0;
		var replyText = null;
		var isGeneric = false;
		if (selectedSocialServiceTypes.length == 1) {
			if (selectedSocialServiceTypes[0] == SocialServiceTypes.Twitter && selectedMessagesContainsPublic && !selectedMessagesContainsPrivate) {
				maxLength = twitterStatusTextCharacterLimit;
				replyText = $textboxPublicTwitterReply.val();
			}
			else if (selectedSocialServiceTypes[0] == SocialServiceTypes.Mail) {
				replyText = $textareaAnswerBodyEmail.val();
			}
			else {
				isGeneric = true;
			}
		}
		else {
			isGeneric = true;
		}

		if (isGeneric) {
			replyText = $textboxGenericReply.val();

			if (selectedSocialServiceTypes.indexOf(SocialServiceTypes.Twitter) >= 0) {
				if (selectedMessagesContainsPublic) {
					maxLength = twitterStatusTextCharacterLimit;
				}
				else {
					maxLength = 10000;
				}
			}
		}
	}
	else {
		maxLength = 10000;
		replyText = $textboxGenericReply.val();
	}

	replyText = replyText.trim();

	var $divErrorEmptyText = $('#divErrorEmptyText');
	if (replyText.length == 0) {
		ToggleValidator($divErrorEmptyText, false);
		return;
	}

	ToggleValidator($divErrorEmptyText, true);

	var $divErrorOverMaxLength = $('#divErrorOverMaxLength');
	var $spanErrorMaxLength = $('#spanErrorMaxLength', $divErrorOverMaxLength);
	if (maxLength > 0 && replyText.length > maxLength) {
		$spanErrorMaxLength.text(maxLength);
		ToggleValidator($divErrorOverMaxLength, false);
		return;
	}

	ToggleValidator($divErrorOverMaxLength, true);
	var failedIds = null;
	var error = false;

	$.colorbox({
		transition: 'elastic',
		speed: 200,
		inline: true,
		href: $divLoading,
		width: '400px',
		initialWidth: '400px',
		height: '160px',
		preloading: false,
		closeButton: false,
		overlayClose: false,
		escKey: false,
		onComplete: function () {
			var dataToSend = JSON.stringify({ queueId: queue.ID, messageIds: messageIds, replyText: replyText, closeCase: closeCase, onlyVisibles: onlyVisibles });

			$.ajax({
				type: "POST",
				url: "Messages.aspx/ReplyAllMessages",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				async: false,
				success: function (data) {
					if (data.d.Success) {
						failedIds = data.d.FailedIds;
						appliedToAllMessages = data.d.AppliedToAllMessages;
					}
					else {
						error = true;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					if (console)
						console.log('Ocurrió un error consultando: ' + jqXHR.responseText);
				}
			});

			setTimeout(function () {
				if (error) {
					AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-error_answering_messages"), '$.colorbox.close()');
				}
				else if (typeof (appliedToAllMessages) === 'boolean' && appliedToAllMessages) {
					AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-message_replied_all", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else if (failedIds == null || failedIds.length == 0) {
					AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-answered_messages", messageIds.length), '$.colorbox.close()', Refresh);
				}
				else {
					if (failedIds.length == messageIds.length)
						AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-cannot_answer_messages"), '$.colorbox.close()');
					else
						AlertDialog($.i18n("supervisor-messages-answer_message"), $.i18n("supervisor-messages-messages_answered_while", (messageIds.length - failedIds.length), failedIds.length), '$.colorbox.close()', Refresh);
				}
			}, 500);
		}
	});
}

function ShowPredefinedAnswers() {
	var serviceId = selectedServices[0];
	var selectedSocialServiceType = selectedSocialServiceTypes[0];

	var predefinedAnswers = [];

	var templates;

	if (selectedServices.length === 1) {
		templates = FindServiceAnswers(serviceId);
		if (templates != null) {
			for (var i = 0; i < templates.length; i++) {
				var template = templates[i];
				if (selectedSocialServiceType === SocialServiceTypes.Twitter || selectedSocialServiceType === SocialServiceTypes.Facebook) {
					if (selectedMessagesContainsPublic && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Public))
						continue;
					if (selectedMessagesContainsPrivate && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Private))
						continue;
				}

				if (template.AnswerUseType.indexOf(AnswerUseTypes.Supervisor) !== -1) {
					predefinedAnswers.push(template);
				}
			}
		}
	}

	templates = FindServiceAnswers(0);
	if (templates != null) {
		for (var i = 0; i < templates.length; i++) {
			var template = templates[i];
			if (template.SelectedOptionsIDs.indexOf(selectedSocialServiceType) !== -1) {
				if (selectedSocialServiceType === SocialServiceTypes.Twitter || selectedSocialServiceType === SocialServiceTypes.Facebook) {
					if (selectedMessagesContainsPublic && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Public))
						continue;
					if (selectedMessagesContainsPrivate && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Private))
						continue;
				}

				if (template.AnswerUseType.indexOf(AnswerUseTypes.Supervisor) !== -1) {
					predefinedAnswers.push(template);
				}
			}
		}
	}

	var $divPredefinedAnswers = $('#divPredefinedAnswers');
	var $divPredefinedAnswersZero = $('#divPredefinedAnswersZero');
	var $divPredefinedAnswersOneOrMore = $('#divPredefinedAnswersOneOrMore');

	if (predefinedAnswers.length > 0) {
		predefinedAnswers.sort(function (a, b) {
			if (a.Name < b.Name)
				return -1;

			if (a.Name > b.Name)
				return 1;

			return 0;
		});

		var $tablePredefinedAnswers = $('#tablePredefinedAnswers');
		var $tbodyAnswers = $('tbody', $tablePredefinedAnswers);
		$tbodyAnswers.empty();

		for (var i = 0; i < predefinedAnswers.length; i++) {
			var predefinedAnswer = predefinedAnswers[i];
			var body = predefinedAnswer.Body;

			var initials = '';
			if (loggedUserLastName !== null && loggedUserLastName.length > 0 && loggedUserLastName !== loggedUserFirstName) {
				initials = loggedUserFirstName.substr(0, 1).toUpperCase() + loggedUserLastName.substr(0, 1).toUpperCase();
			}
			else {
				initials = loggedUserFirstName.substr(0, 2).toUpperCase();
			}

			body = body.replaceAll('@@AGENTE@@', loggedUserName)
				.replaceAll('@@SUPERVISOR@@', loggedUserName)
				.replaceAll('@@SUPERVISOR[NOMBRE]@@', loggedUserFirstName)
				.replaceAll('@@SUPERVISOR[APELLIDO]@@', loggedUserLastName)
				.replaceAll('@@SUPERVISOR[USERNAME]@@', loggedUserUserName)
				.replaceAll('@@SUPERVISOR[INICIALES]@@', initials)
				.replaceAll('@@PERSONA@@', loggedUserName)
				.replaceAll('@@PERSONA[NOMBRE]@@', loggedUserFirstName)
				.replaceAll('@@PERSONA[APELLIDO]@@', loggedUserLastName)
				.replaceAll('@@PERSONA[USERNAME]@@', loggedUserUserName)
				.replaceAll('@@PERSONA[INICIALES]@@', initials);

			var $tr = $('<tr></tr>');
			$tr.addClass(i % 2 == 0 ? 'normal' : 'alternate');

			var $td = $('<td></td>');
			if (predefinedAnswer.IsGlobal)
				$td.append('<span class="fa fa-lg fa-globe" title="Respuesta predefinida global" style="margin-right: 5px"></span>');
			$td.append(predefinedAnswer.Name);
			$tr.append($td);

			$tr.append('<td><div style="overflow: hidden; text-overflow: ellipsis; max-width: 480px; white-space:nowrap;">' + body + '</div></td>');
			
			$td = $('<td align="center" style="width: 80px; white-space: nowrap"></td>');

			var $anchor = $('<a><span class="fa fa-lg fa-check-square" title="Seleccionar"></span></a>');
			$td.append($anchor);
			$anchor.click(body, function ($event) {
				HidePredefinedAnswers();

				var body = $event.data;
				if (selectedSocialServiceType === SocialServiceTypes.Mail) {
					var clEditor = $textareaAnswerBodyEmail.cleditor();
					var a = clEditor[0].execCommand('insertHTML', body, false, null);
				}
				else {
					var $input;
					if (selectedSocialServiceType === SocialServiceTypes.Twitter && selectedMessagesContainsPublic && !selectedMessagesContainsPrivate) {
						$input = $textboxPublicTwitterReply;
					}
					else {
						$input = $textboxGenericReply;
					}

					InsertAtCaret($input, body);
				}
			});

			$tr.append($td);

			$tbodyAnswers.append($tr);
		}

		$divPredefinedAnswersZero.hide();
		$divPredefinedAnswersOneOrMore.show();

		LoadCompositedElements();
	}
	else {
		$divPredefinedAnswersZero.show();
		$divPredefinedAnswersOneOrMore.hide();
	}

	predefinedAnswers = [];
	templates = FindQueueAnswers();
	if (templates != null) {
		for (var i = 0; i < templates.length; i++) {
			var template = templates[i];
			if (template.SelectedOptionsIDs.indexOf(selectedSocialServiceType) !== -1) {
				if (selectedSocialServiceType === SocialServiceTypes.Twitter || selectedSocialServiceType === SocialServiceTypes.Facebook) {
					if (selectedMessagesContainsPublic && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Public))
						continue;
					if (selectedMessagesContainsPrivate && !(template.Ambit === PredefinedAnswersAmbitEnum.BothOfThem || template.Ambit === PredefinedAnswersAmbitEnum.Private))
						continue;
				}

				if (template.AnswerUseType.indexOf(AnswerUseTypes.Supervisor) !== -1) {
					predefinedAnswers.push(template);
				}
			}
		}
	}

	var $divPredefinedAnswersByQueueZero = $('#divPredefinedAnswersByQueueZero');
	var $divPredefinedAnswersByQueueOneOrMore = $('#divPredefinedAnswersByQueueOneOrMore');

	if (predefinedAnswers.length > 0) {
		predefinedAnswers.sort(function (a, b) {
			if (a.Name < b.Name)
				return -1;

			if (a.Name > b.Name)
				return 1;

			return 0;
		});

		var $tablePredefinedAnswersByQueue = $('#tablePredefinedAnswersByQueue');
		var $tbodyAnswers = $('tbody', $tablePredefinedAnswersByQueue);
		$tbodyAnswers.empty();

		for (var i = 0; i < predefinedAnswers.length; i++) {
			var predefinedAnswer = predefinedAnswers[i];
			var body = predefinedAnswer.Body;
			body = body.replaceAll('@@AGENTE@@', loggedUserName);

			var $tr = $('<tr></tr>');
			$tr.addClass(i % 2 == 0 ? 'normal' : 'alternate');

			var $td = $('<td></td>');
			$td.append(predefinedAnswer.Name);
			$tr.append($td);

			var $td = $('<td align="center"></td>');
			var html = '<span class="fa-lg ';
			html += GetSocialServiceTypeClass(predefinedAnswer.SocialServiceType);
			html += '"></span> ';
			$td.append(html);
			$tr.append($td);

			$tr.append('<td><div style="overflow: hidden; text-overflow: ellipsis; max-width: 480px; white-space:nowrap;">' + body + '</div></td>');

			$td = $('<td align="center" style="width: 80px; white-space: nowrap"></td>');

			var $anchor = $('<a><span class="fa fa-lg fa-check-square" title="Seleccionar"></span></a>');
			$td.append($anchor);
			$anchor.click(body, function ($event) {
				HidePredefinedAnswers();

				var body = $event.data;
				if (selectedSocialServiceType === SocialServiceTypes.Mail) {
					var clEditor = $textareaAnswerBodyEmail.cleditor();
					var a = clEditor[0].execCommand('insertHTML', body, false, null);
				}
				else {
					var $input;
					if (selectedSocialServiceType === SocialServiceTypes.Twitter && selectedMessagesContainsPublic && !selectedMessagesContainsPrivate) {
						$input = $textboxPublicTwitterReply;
					}
					else {
						$input = $textboxGenericReply;
					}

					InsertAtCaret($input, body);
				}
			});

			$tr.append($td);

			$tbodyAnswers.append($tr);
		}

		$divPredefinedAnswersByQueueZero.hide();
		$divPredefinedAnswersByQueueOneOrMore.show();

		LoadCompositedElements();
	}
	else {
		$divPredefinedAnswersByQueueZero.show();
		$divPredefinedAnswersByQueueOneOrMore.hide();
	}

	var $divReplyComposing = $('#divReplyComposing');
	$divReplyComposing.hide();
	$divPredefinedAnswers.show();

	$.colorbox.resize({ width: '800px' });
}

function FindServiceAnswers(serviceId) {
	for (var i = 0; i < servicesAndFilters.length; i++) {
		if (serviceId === servicesAndFilters[i].ID) {
			return servicesAndFilters[i].Templates;
		}
	}

	return null;
}

function FindQueueAnswers() {
	for (var i = 0; i < queues.length; i++) {
		if (queue.ID === queues[i].ID) {
			return queues[i].Templates;
		}
	}

	return null;
}

function HidePredefinedAnswers() {
	var $divReplyComposing = $('#divReplyComposing');
	var $divPredefinedAnswers = $('#divPredefinedAnswers');
	$divReplyComposing.show();
	$divPredefinedAnswers.hide();
	$.colorbox.resize({ width: '800px' });
}

function ShowMessageDialog($message) {
	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: $message,
		width: '500px',
		initialWidth: '500px',
		preloading: false,
		closeButton: false,
		onComplete: function () {
			$.colorbox.resize();
		}
	});
}

function LoadCurrentFilterOptions() {
	if (currentFilter === null)
		return;
	
	$textboxFilter.val(currentFilter.text);
	$checkboxFilterInGroupedMessages.attr('disabled', !currentFilter.text.length > 0);
	$checkboxFilterInGroupedMessages.attr('checked', currentFilter.checkTextInGroupedMessages);
	$selectSocialServiceType.val(currentFilter.socialServiceTypeId);
	$selectMessageType.val(currentFilter.messageTypeOnlyPrivate);
	$selectTagInCase.val(currentFilter.tagId);
	$selectSystemTimeComparison.val(currentFilter.systemTimeComparison)
	$textboxSystemTime.val(currentFilter.systemTime),
	$selectQueueTimeComparison.val(currentFilter.queueTimeComparison);
	$textboxQueueTime.val(currentFilter.queueTime);
	$selectFilterService.val(currentFilter.serviceId);
	$selectFilterServiceFilters.val(currentFilter.filterId);

	$selectSocialServiceType.multiselect("refresh");
	$selectMessageType.multiselect("refresh");
	$selectTagInCase.multiselect("refresh");
	$selectSystemTimeComparison.multiselect("refresh");
	$selectQueueTimeComparison.multiselect("refresh");
	$selectFilterService.multiselect("refresh");
	$selectFilterServiceFilters.multiselect("refresh");
}