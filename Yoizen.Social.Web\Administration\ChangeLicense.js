﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />

var $tableParameters;

jQuery(document).ready(function () {
	$tableParameters = $('#tableParameters');

	for (property in properties) {
		if (properties.hasOwnProperty(property)) {
			let $tr = $('<tr class="dataRow dataRowSeparator"><th class="label" style="width: 400px !important;"></th><td class="data"></td></tr>');
			$tr.attr('property', property);
			let $thLabel = $('th.label', $tr);
			let $thData = $('td.data', $tr);

			$thLabel.text(properties[property].Title + ':');

			switch (properties[property].Type) {
				case TypeCode.Boolean:
					let $checkbox = $('<input type="checkbox" />');
					if (properties[property].Value) {
						$checkbox.prop('checked', true);
					}
					$thData.append($checkbox);
					break;
				case TypeCode.Int16:
				case TypeCode.Int32:
				case TypeCode.Int64:
					let $inputNumber = $('<input type="number" min="0" max="250" step="1" class="inputtext" />');
					if (typeof (properties[property].Value) === 'number') {
						$inputNumber.val(properties[property].Value);
					}
					$thData.append($inputNumber);
					break;
				case TypeCode.String:
					let $inputText = $('<input type="text" class="inputtext" />');
					if (typeof (properties[property].Value) === 'string') {
						$inputText.val(properties[property].Value);
					}
					$thData.append($inputText);
				default:
					break;
			}

			let $span = $('<span style="margin-left: 20px; font-style: italic;"></span>');
			$span.text('Valor en el archivo de licencias: ' + properties[property].FileValue);
			$thData.append($span);

			if (properties[property].Value !== properties[property].FileValue) {
				$thData.append('<span class="fa fa-lg fa-exclamation-circle" style="margin-left: 20px; margin-right: 3px; font-weight: bold;"></span>');
				$span = $('<span style="font-weight: bold;"></span>');
				$span.text('El valor en el archivo es distinto al valor actual');
				$thData.append($span);
			}

			if (typeof (properties[property].DependsOn) === 'string' &&
				properties[property].DependsOn.length > 0) {
				let $trThatDependsOn = $('tr[property=' + properties[property].DependsOn + ']', $tableParameters)
				let $checkboxThatDependsOn = $('input[type=checkbox]', $trThatDependsOn);
				$checkboxThatDependsOn.change($tr, function (e) {
					e.data.toggle(this.checked);
				});
			}

			$tableParameters.append($tr);
		}
	}
});

function Confirm() {
	var parameters = {};

	for (property in properties) {
		if (properties.hasOwnProperty(property)) {
			let $tr = $('tr[property=' + property + ']', $tableParameters);

			switch (properties[property].Type) {
				case TypeCode.Boolean:
					let $checkbox = $('input', $tr);
					parameters[property] = $checkbox.is(':checked');
					break;
				case TypeCode.Int16:
				case TypeCode.Int32:
				case TypeCode.Int64:
					let $inputNumber = $('input', $tr);

					if (typeof (properties[property].AllowsNull) === 'boolean' &&
						properties[property].AllowsNull &&
						$inputNumber.val().length === 0) {
						parameters[property] = null;
					}
					else {
						parameters[property] = parseInt($inputNumber.val(), 10);

						if (parameters[property] < 0) {
							AlertDialog('Cambiar parámetros licencia', `El valor de <b>${properties[property].Title}</b> es inválido`, null, null, 'Error');
							$inputNumber.focus();
							return;
						}
					}

					break;
				case TypeCode.String:
					let $inputText = $('input', $tr);

					if (typeof (properties[property].AllowsNull) === 'boolean' &&
						properties[property].AllowsNull &&
						$inputText.val().length === 0) {
						parameters[property] = null;
					}
					else {
						parameters[property] = $inputText.val();
					}

					break;
				default:
					break;
			}

			$tableParameters.append($tr);
		}
	}

	ConfirmDialog({
		title: 'Cambiar parámetros licencia',
		message: '¿Está seguro que desea actualizar los parámetros de la licencia?',
		onAccept: function () {
			LoadingDialog({
				title: 'Cambiar parámetros licencia',
				onClose: function () {
					var dataToSend = JSON.stringify({
						parameters: parameters
					});

					$.ajax({
						type: "POST",
						url: "ChangeLicense.aspx/Update",
						contentType: "application/json; charset=utf-8",
						dataType: "json",
						data: dataToSend,
						async: false,
						success: function (result) {
							if (result.d.Success) {
								AlertDialog('Cambiar parámetros licencia', 'Se cambiaron los parámetros de la licencia');
							}
							else {
								AlertDialog('Cambiar parámetros licencia', 'No se pudo realizar la operación porque el archivo es inválido', null, null, 'Error');
							}
						},
						error: function () {
							AlertDialog('Cambiar parámetros licencia', 'Ocurrió un error realizando la operación', null, null, 'Error');
						}
					});
				},
				timeout: 500
			});
		},
		onCancel: $.colorbox.close,
		acceptText: $.i18n('globals-accept'),
		cancelText: $.i18n('globals-cancel'),
		width: '500px',
		height: '180px'
	});
}