﻿CREATE PROCEDURE [dbo].[EmailContacts_Insert]
	@Name NVARCHAR(100)
	, @LastName NVARCHAR(100)
	, @Email NVARCHAR(100)
	, @AgentID INT
	, @NewID INT OUTPUT
AS
BEGIN
	SET NOCOUNT ON;

	BEGIN TRY
		INSERT INTO [dbo].[EmailContacts] ([Name], [LastName], [Email], [AgentID])
		VALUES (@Name, @LastName, @Email, @AgentID);

		SET @NewID = SCOPE_IDENTITY();
		RETURN 0;
	END TRY
	BEGIN CATCH
		IF ERROR_NUMBER() IN (2601, 2627)
		BEGIN
			RETURN 1;
		END

		RETURN -1;
	END CATCH
END