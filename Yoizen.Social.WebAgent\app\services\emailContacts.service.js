(function () {
    'use strict';

    angular.module('socialApp')
        .factory('emailContactsService', emailContactsService);

    emailContactsService.$inject = ['$http', 'CONFIG_INFO'];

    function emailContactsService($http, CONFIG_INFO) {
        var baseUrl = CONFIG_INFO.baseUrl + 'emailcontacts/';

        var service = {
            getEmailContacts: getEmailContacts,
            insert: insert,
            update: update,
            deleteContact: deleteContact
        };

        return service;

        function getEmailContacts(agentId, text, lastId) {
            let url = `${baseUrl}getemailcontacts?agentId=${agentId}&text=${text}`;
    
            if (lastId !== undefined && lastId !== null) {
                url += `&lastEmailContactID=${lastId}`;
            }

            return $http({
                method: 'GET',
                url: url
            });
        }


        function insert(data) {
            return $http({
                method: 'POST',
                url: baseUrl + 'insert',
                data: data,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        function update(data) {
            return $http({
                method: 'POST',
                url: baseUrl + 'update',
                data: data,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        function deleteContact(id) {
            return $http({
                method: 'DELETE',
                url: baseUrl + 'delete?id=' + id,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
    }
})();
