﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


	public partial class ServicesInstagram
	{

		/// <summary>
		/// messageInstagramWizardLoadingError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramWizardLoadingError;

		/// <summary>
		/// messageInstagramNoPages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramNoPages;

		/// <summary>
		/// messageInstagramUrlInvalid control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramUrlInvalid;

		/// <summary>
		/// messageInstagramAccountError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramAccountError;

		/// <summary>
		/// messageInstagramCouldntValidateAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramCouldntValidateAccessToken;

		/// <summary>
		/// messageAnotherInstagramServiceExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAnotherInstagramServiceExists;

		/// <summary>
		/// liTabAdvancedConfigurationYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabAdvancedConfigurationYFlow;

		/// <summary>
		/// hiddenUserAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenUserAccessToken;

		/// <summary>
		/// textboxServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxServiceName;

		/// <summary>
		/// customvalidatorServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorServiceName;

		/// <summary>
		/// textboxInstagramUserAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramUserAccessToken;

		/// <summary>
		/// textboxInstagramPageId control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramPageId;

		/// <summary>
		/// textboxInstagramPageName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramPageName;

		/// <summary>
		/// textboxInstagramPageAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramPageAccessToken;

		/// <summary>
		/// textboxInstagramAccountId control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramAccountId;

		/// <summary>
		/// textboxInstagramAccountName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramAccountName;

		/// <summary>
		/// textboxInstagramAccountUsername control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramAccountUsername;

		/// <summary>
		/// textboxInstagramFromDate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramFromDate;

		/// <summary>
		/// dropdownlistInstagramQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistInstagramQueue;

		/// <summary>
		/// placeholderInstagramCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderInstagramCheckSpelling;

		/// <summary>
		/// checkboxInstagramCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInstagramCheckSpelling;

		/// <summary>
		/// placeholderAllowYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYFlow;

		/// <summary>
		/// dropdownlistUseYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistUseYFlow;

		/// <summary>
		/// messageInstagramTokenExpiry control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramTokenExpiry;

		/// <summary>
		/// divAdvancedConfigurationYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divAdvancedConfigurationYFlow;

		/// <summary>
		/// hiddenFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlow;

		/// <summary>
		/// hiddenSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveys;

		/// <summary>
		/// divYFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divYFlowContingency;

		/// <summary>
		/// hiddenFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowContingency;

		/// <summary>
		/// hiddenSurveysContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveysContingency;

		/// <summary>
		/// hiddenFlowQueueTransfersByKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowQueueTransfersByKey;

		/// <summary>
		/// listboxFlowShareEnqueuedMessagesFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareEnqueuedMessagesFromQueues;

		/// <summary>
		/// listboxFlowShareConnectedAgentsFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareConnectedAgentsFromQueues;

		/// <summary>
		/// textboxFlowMinutesAfterAgentClosedCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFlowMinutesAfterAgentClosedCase;

		/// <summary>
		/// placeholderAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// checkboxAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// panelYFlowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelYFlowSurveys;

		/// <summary>
		/// messageNoSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveys;

		/// <summary>
		/// panelEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEnableSurveys;

		/// <summary>
		/// checkboxEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

		/// <summary>
		/// messageNoSurveysInTable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveysInTable;

		/// <summary>
		/// messageSurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyDisabled;

		/// <summary>
		/// dropdownSurvey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownSurvey;

		/// <summary>
		/// textboxSurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitation;

		/// <summary>
		/// messageSurveyInvitationFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyInvitationFields;

		/// <summary>
		/// textboxSurveyInvitationInteractive control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationInteractive;

		/// <summary>
		/// messageFilterEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

		/// <summary>
		/// textboxSurveyInvitationButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationButton;

		/// <summary>
		/// message1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message1;

		/// <summary>
		/// textboxSurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyExpiration;

		/// <summary>
		/// textboxSurveySentRate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveySentRate;

		/// <summary>
		/// textboxSurveyTimeToSend control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTimeToSend;

		/// <summary>
		/// textboxSurveyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTags;

		/// <summary>
		/// listboxSurveyTagGroup control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroup;

		/// <summary>
		/// textboxSurveyMessagesCount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyMessagesCount;

		/// <summary>
		/// textboxSurveyCaseDuration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyCaseDuration;

		/// <summary>
		/// checkboxSurveySendIfNewCaseExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseExists;

		/// <summary>
		/// textboxSurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// textboxSurveyDontSendTotalSendMonthly control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendTotalSendMonthly;

		/// <summary>
		/// textboxSurveysIgnoreTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysIgnoreTags;

		/// <summary>
		/// listboxSurveyTagGroupToIgnore control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroupToIgnore;

		/// <summary>
		/// checkboxInstagramAllowToSendMedia control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInstagramAllowToSendMedia;

		/// <summary>
		/// textboxInstagramMaxSizeAttachment control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramMaxSizeAttachment;

		/// <summary>
		/// checkboxInstagramAcceptedTypeImages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInstagramAcceptedTypeImages;

		/// <summary>
		/// hiddenInstagramOAuthErrorConnection control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenInstagramOAuthErrorConnection;

		/// <summary>
		/// textboxInstagramOAuthErrorEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramOAuthErrorEmailSubject;

		/// <summary>
		/// textboxInstagramOAuthErrorEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramOAuthErrorEmails;

		/// <summary>
		/// textboxInstagramOAuthErrorEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramOAuthErrorEmailTemplate;

		/// <summary>
		/// messageInstagramOAuthErrorEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramOAuthErrorEmailTemplateFields;

		/// <summary>
		/// textboxInstagramMinutesForInactivity control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramMinutesForInactivity;

		/// <summary>
		/// hiddenInstagramInactivityDetectedConnection control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenInstagramInactivityDetectedConnection;

		/// <summary>
		/// textboxInstagramInactivityDetectedEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramInactivityDetectedEmailSubject;

		/// <summary>
		/// textboxInstagramInactivityDetectedEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramInactivityDetectedEmails;

		/// <summary>
		/// textboxInstagramInactivityDetectedEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxInstagramInactivityDetectedEmailTemplate;

		/// <summary>
		/// messageInstagramInactivityDetectedEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageInstagramInactivityDetectedEmailTemplateFields;

		/// <summary>
		/// checkboxAutoReplyBeforeMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeMaxTimeToAnswer;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// messageAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerMinutes;

		/// <summary>
		/// checkboxDiscardAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxAutoReplyBeforeCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeCloseCase;

		/// <summary>
		/// textboxAutoReplyBeforeCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCloseCaseText;

		/// <summary>
		/// textboxAutoReplyBeforeCloseCaseMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCloseCaseMinutes;

		/// <summary>
		/// dropdownlistAllowToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowToSendHSM;

		/// <summary>
		/// dropdownlistAllowAgentsToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowAgentsToSendHSM;

		/// <summary>
		/// hiddenHSMTemplates control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenHSMTemplates;

		/// <summary>
		/// panelSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelSurveys;

		/// <summary>
		/// dropdownlistAllowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowSurveys;

		/// <summary>
		/// divCapiService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCapiService;

		/// <summary>
		/// checkboxEnableCapi control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableCapi;

		/// <summary>
		/// checkboxCasesOverrideSystemSettings control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCasesOverrideSystemSettings;

		/// <summary>
		/// checkboxCheckLastQueueOfOpenCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueOfOpenCase;

		/// <summary>
		/// checkboxIgnoreLastQueueForSLMovedMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIgnoreLastQueueForSLMovedMessage;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseCases;

		/// <summary>
		/// checkboxReplyInCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReplyInCloseCase;

		/// <summary>
		/// textboxAutoReplyInCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyInCloseCaseText;

		/// <summary>
		/// textboxTagCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTagCloseCase;

		/// <summary>
		/// placeholderYFlowCasesRelated control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowCasesRelated;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseYFlowCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseYFlowCases;

		/// <summary>
		/// checkboxInvokeYFlowWhenClosedCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInvokeYFlowWhenClosedCases;

		/// <summary>
		/// hiddenServiceToCopy control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenServiceToCopy;

		/// <summary>
		/// buttonCopyService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCopyService;

		/// <summary>
		/// buttonSave control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonSave;

		/// <summary>
		/// buttonCancel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCancel;
	}
}
