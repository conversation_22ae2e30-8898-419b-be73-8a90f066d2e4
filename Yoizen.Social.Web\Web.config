﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<appSettings>
		<add key="log4netConfig" value="log4net.config" />
		<add key="StartMes**gingService" value="true" />
		<add key="Host" value="dev.ysocial.net" />
		<add key="Https" value="true" />
		<add key="FacebookApiVersion" value="v10.0" />
		<add key="LicenseFile" value="C:\Source\Yoizen\Social\Yoizen.Social.License.xml" />
		<add key="StoragePath" value="C:\Source\Yoizen\Social\Storage\" />
		<add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
		<!--add key="CORSOrigins" value="na-01.workspaces.avayacloud.com,******2.ysocial.net,******.ysocial.net,dev.ysocial.net" /-->
		<add key="YoizMeToken" value="6c95c2a1c8" />
		<add key="Di**bleCacheAppThemes" value="false" />
		<add key="CheckYFlowPendingCasesInactivity" value="true" />
		<add key="TwitterAppsFile" value="C:\Source\Yoizen\Social\Yoizen.Social.SocialServices.Twitter.Apps.json" />
		<!-- Los siguientes valores de GoogleLogin es para validar el login de un usuario contra AD de google -->
		<!--add key="GoogleLoginCertificateFile" value="C:\Source\Yoizen\Social\******.com_******-social-60a1b74d2d52.p12" />
		<add key="GoogleLoginCertificateKey" value="" />
		<add key="GoogleLoginServiceAccountEmail" value="" />
		<add key="GoogleLoginUserAdministratorEmail" value="<EMAIL>" /-->
	</appSettings>
	<connectionStrings>
		<add name="Default" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="IntervalsDb" connectionString="server=127.0.0.1;Uid=root;Password=******;Database=ysocial_intervals" providerName="MySql.Data.MySqlClient" />
		<add name="YN-LRECCHINI" connectionString="data source=localhost,1433;User id=**;Password=************$$$$;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="LAPTOP-AUKCTJ83" connectionString="data source=localhost,1433;User id=**;Password=*********;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="LAPTOP-ULG84BQH" connectionString="data source=(local);User id=**;Password=root;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-0794CLP" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="MALBERBOOK" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="MATIAS-PC" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="MALBER-PC" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="YN-MMALBERGIER" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="YN-MMALBERGIER-IntervalsDb" connectionString="Data Source=(local);Database=SocialIntervals;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<!--add name="MALBER-PC-IntervalsDb" connectionString="server=127.0.0.1;Uid=root;Password=******;Database=ysocial_intervals" providerName="MySql.Data.MySqlClient" /-->
		<add name="MALBER-PC-IntervalsDb" connectionString="Data Source=(local);Database=SocialIntervals;User ID=**;Password=******" providerName="System.Data.SqlClient" />
		<add name="YN-MMALBERGIER" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="YN-MMALBERGIER-IntervalsDb" connectionString="Data Source=(local);Database=SocialIntervals;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-DVGU4H7" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="YN-IRAO" connectionString="data source=(local);User id=**;Password=YourNewStrong@Passw0rd;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-62L2MKH" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=********;" providerName="System.Data.SqlClient" />
		<add name="LAPTOP-Q67KLMF9" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="YN-ALANARI" connectionString="Data Source=(local)\SQLEXPRESS01;Database=Social.Chat;User ID=social;Password=social;" providerName="System.Data.SqlClient" />
		<add name="YN-ALANARI-IntervalsDb" connectionString="Data Source=(local)\SQLEXPRESS01;Database=SocialIntervals;User ID=social;Password=social;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-UUADIV0" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=**;" providerName="System.Data.SqlClient" />
		<add name="LAPTOP-Q67KLMF9" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="Agustin-PC" connectionString="Data Source=(local)\YOIZEN2022;Database=Social.Chat;User ID=**;Password=**;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-CD448AH" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=*********;" providerName="System.Data.SqlClient" />
		<add name="YN-MZIFFER" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=**;" providerName="System.Data.SqlClient" />	
		<add name="DESKTOP-ID04HIK" connectionString="Data Source=(local);Database=YSocial;User ID=**;Password=*********;" providerName="System.Data.SqlClient" />
		<add name="MIEZZI" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=*********;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-ID04HIK" connectionString="Data Source=(local);Database=YSocial;User ID=**;Password=*********;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-FDK8HG3" connectionString="data source=(local);User id=**;Password=**;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="YN-TVALLEJOS" connectionString="data source=(local);User id=**;Password=**;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="NY-EMOLINA" connectionString="data source=(local)\SQLEXPRESS;User id=**;Password=**;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="LAPTOP-Q67KLMF9-IntervalsDb" connectionString="Data Source=(local);Database=SocialIntervals;User ID=**;Password=******" providerName="System.Data.SqlClient" />
		<add name="YN-NCIOFFI" connectionString="data source=(local)\SQLEXPRESS;User id=**;Password=******2025;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="YN-TPOCHAT" connectionString="data source=(local)\SQLEXPRESS;User id=**;Password=**********;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="ASUS000178364" connectionString="Data Source=(local)\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******1234;" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-3KA820C" connectionString="Data Source=DESKTOP-3KA820C\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******" providerName="System.Data.SqlClient" />
		<add name="MATIAS-Q" connectionString="data source=MATIAS-Q\SQLEXPRESS;User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="YN-MFERNANDEZ" connectionString="Data Source=YN-MFERNANDEZ\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******" providerName="System.Data.SqlClient" />
		<add name="YN-MRUSSO" connectionString="Data Source=(local);Database=Social.Chat;User ID=ysocialdbuser;Password=***********" providerName="System.Data.SqlClient" />
		<add name="NOTEBOOK-25" connectionString="Data Source=NOTEBOOK-25;Database=Social.Chat;User ID=Social;Password=******" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-LT0PVTE" connectionString="data source=(local);User id=ysocialdbuser;Password=**********;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="DESKTOP-LT0PVTE-IntervalsDb" connectionString="server=(local);Uid=ysocialdbuser;Password=**********;Database=socialIntervalsDb" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<!--system.data>
		<DbProviderFactories>
			<add name="MySql.Data.MySqlClient"
			 invariant="MySql.Data.MySqlClient"
			 description="Async MySQL ADO.NET Connector"
			 type="MySqlConnector.MySqlConnectorFactory, MySqlConnector, Culture=neutral, PublicKeyToken=d33d3e53aa5f8c92" />
		</DbProviderFactories>
	</system.data-->
	<location inheritInChildApplications="false" path=".">
		<system.web>
			<compilation debug="true" targetFramework="4.7.2" numRecompilesBeforeAppRestart="50" />
			<sessionState cookieName="******social" cookieSameSite="Lax" />
			<authentication mode="Windows" />
			<pages theme="Default" clientIDMode="AutoID" controlRenderingCompatibilityVersion="4.0">
				<controls>
					<add tagPrefix="******" namespace="Yoizen.Web.UI" assembly="Yoizen.Web.UI, Version=*******, Culture=neutral, PublicKeyToken=8c5134be849e992c" />
					<add tagPrefix="ysocial" namespace="Yoizen.Social.Web.Reports.Controls" assembly="Yoizen.Social.Web" />
				</controls>
			</pages>
			<customErrors mode="RemoteOnly" defaultRedirect="~/Errors.aspx" redirectMode="ResponseRedirect" />
			<globalization uiCulture="es-AR" culture="es-AR" />
			<!--globalization uiCulture="en-US" culture="es-AR"/-->
			<httpRuntime requestValidationMode="2.0" maxRequestLength="1048576" enableVersionHeader="false" targetFramework="4.7.2" />
			<httpCookies httpOnlyCookies="true" requireSSL="true" />
			<!--1GB-->
			<!--sessionState mode="StateServer" stateConnectionString="tcpip=localhost:42424" cookieless="false" timeout="20" /-->
		</system.web>
		<system.webServer>
			<modules runAllManagedModulesForAllRequests="true">
				<remove name="CompressionModule" />  
				<add name="CompressionModule" type="Yoizen.Social.Web.CompressionModule" />
				<add name="UnhandledExceptionModule" type="Yoizen.Social.Web.UnhandledExceptionModule" />
				<!--add name="CORSModule" type="Yoizen.Social.Web.CORSModule" /-->
			</modules>
		</system.webServer>
	</location>
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="2147483647">
					<converters>
						<add name="GeoCoordinateJavaScriptConverter" type="Yoizen.Social.DomainModel.JSON.GeoCoordinateJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="DailyJavaScriptConverter" type="Yoizen.Social.DomainModel.Historical.Daily+DailyJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="DailyServiceJavaScriptConverter" type="Yoizen.Social.DomainModel.Historical.DailyService+DailyServiceJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="DailySurveyJavaScriptConverter" type="Yoizen.Social.DomainModel.Historical.DailySurvey+DailySurveyJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="DailyCaseJavaScriptConverter" type="Yoizen.Social.DomainModel.Historical.DailyCase+DailyCaseJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="DateTimeJavaScriptConverter" type="Yoizen.Common.Conversions+DateTimeJavaScriptConverter, Yoizen.Common" />
						<add name="UserJavaScriptConverter" type="Yoizen.Social.DomainModel.JSON.UserJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="AgentJavaScriptConverter" type="Yoizen.Social.DomainModel.JSON.AgentJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="PersonJavaScriptConverter" type="Yoizen.Social.DomainModel.JSON.PersonJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="QueuesJavaScriptConverter" type="Yoizen.Social.DomainModel.Reports.RealTime.Queues+QueuesJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="IntegrationActionBaseSettingsJavaScriptConverter" type="Yoizen.Social.DomainModel.Settings.IntegrationActions.IntegrationActionBaseSettings+IntegrationActionBaseSettingsJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="GatewayIntegrationConfigurationJavaScriptConverter" type="Yoizen.Social.DomainModel.Settings.GatewayIntegration.GatewayIntegrationConfigurationJavaScriptConverter, Yoizen.Social.DomainModel" />
						<add name="RealTimeAgents" type="Yoizen.Social.Web.Reports.RTAgentsModels.RTAgentsModelsJavaScriptConverter, Yoizen.Social.Web" />
						<add name="RealTimeAgentsAuxReasons" type="Yoizen.Social.Web.Reports.RTAgentsAuxReasonsModels.RTAgentsAuxReasonsModelsJavaScriptConverter, Yoizen.Social.Web" />
						<add name="RealTimeAssignedMes**ges" type="Yoizen.Social.Web.Reports.RTAssignedMes**gesModels.RTAssignedMes**gesModelsJavaScriptConverter, Yoizen.Social.Web" />
					</converters>
				</jsonSerialization>
			</webServices>
		</scripting>
	</system.web.extensions>
	<system.webServer>
		<validation validateIntegratedModeConfiguration="false" />
		<staticContent>
			<remove fileExtension=".woff" />
			<remove fileExtension=".woff2" />
			<remove fileExtension=".json" />
			<!-- In case IIS already has this mime type -->
			<mimeMap fileExtension=".woff" mimeType="application/x-font-woff" />
			<mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
			<mimeMap fileExtension=".json" mimeType="application/json" />
		</staticContent>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="1073741824" maxQueryString="9000" />
				<verbs>
					<add verb="TRACE" allowed="false" />
					<add verb="TRACK" allowed="false" />
				</verbs>
				<denyUrlSequences>
					<add sequence="scripts/cloud.json" />
				</denyUrlSequences>
			</requestFiltering>
		</security>
		<httpProtocol>
			<customHeaders>
				<remove name="X-Powered-By" />
				<add name="X-Frame-Options" value="DENY" />
				<add name="X-XSS-Protection" value="1; mode=block" />
				<add name="X-Content-Type-Options" value="nosniff" />
				<add name="Content-Security-Policy" value="frame-ancestors 'self'" />
				<add name="X-Robots-Tag" value="noindex" />
				<add name="Referrer-Policy" value="**me-origin" />
			</customHeaders>
		</httpProtocol>
		<rewrite>
			<rules>
				<rule name="Invalid Host" stopProcessing="true" enabled="true">
					<match url="(.*)" />
					<conditions>
						<add input="{HTTP_HOST}" matchType="Pattern" pattern="^localhost(:\d+)?$" negate="true" />
						<add input="{HTTP_HOST}" matchType="Pattern" pattern="^127\.0\.0\.1(:\d+)?$" negate="true" />
						<add input="{HTTP_HOST}" matchType="Pattern" pattern="^dev\.ysocial\.net(:\d+)?$" negate="true" />
					</conditions>
					<action type="AbortRequest" statusCode="403" />
				</rule>
				<rule name="HTTP to HTTPS redirect" stopProcessing="true" enabled="true">
					<match url="(.*)" />
					<conditions>
						<add input="{HTTPS}" pattern="off" ignoreCase="true" />
						<!--add input="{HTTP_HOST}" matchType="Pattern" pattern="^localhost(:\d+)?$" negate="true" />
						<add input="{HTTP_HOST}" matchType="Pattern" pattern="^127\.0\.0\.1(:\d+)?$" negate="true" /-->
					</conditions>
					<!-- Para de**rrollo dejar la siguiente -->
					<action type="Redirect" url="https://dev.ysocial.net/Social/{R:1}" redirectType="Permanent" />
					<!-- Para producción dejar la siguiente -->
					<!--action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" /-->
				</rule>
				<rule name="fingerprint" enabled="true">
					<match url="([\S]+)(/v-[0-9]+/)([\S]+)" />
					<action type="Rewrite" url="{R:1}/{R:3}" />
				</rule>
			</rules>
			<outboundRules rewriteBeforeCache="true">
				<rule name="Remove Server header" stopProcessing="false">
					<match serverVariable="RESPONSE_Server" pattern=".+" />
					<action type="Rewrite" value="ySocial Server" />
				</rule>
				<rule name="Add Strict-Transport-Security when HTTPS" enabled="true" stopProcessing="false">
					<match serverVariable="RESPONSE_Strict_Transport_Security" pattern=".*" />
					<conditions>
						<add input="{HTTPS}" pattern="on" ignoreCase="true" />
					</conditions>
					<action type="Rewrite" value="max-age=0" />
				</rule>
				<!-- Para NO permitir embeber en otro sitio, dejar lo de abajo con enabled false y esta con true -->
				<rule name="Add SameSite" preCondition="No SameSite" enabled="true">
					<match serverVariable="RESPONSE_Set_Cookie" pattern=".*" negate="false" />
					<action type="Rewrite" value="{R:0}; SameSite=lax" />
				</rule>
				<!-- Para permitir embeber en otro sitio, dejar lo de abajo con enabled true y la de arriba con enabled false -->
				<rule name="Add SameSite-None" preCondition="No SameSite-None" enabled="false">
					<match serverVariable="RESPONSE_Set_Cookie" pattern=".*" negate="false" />
					<action type="Rewrite" value="{R:0}; SameSite=None; Secure" />
				</rule>
				<preConditions>
					<preCondition name="No SameSite">
						<add input="{RESPONSE_Set_Cookie}" pattern="." />
						<add input="{RESPONSE_Set_Cookie}" pattern="; SameSite=lax" negate="true" />
					</preCondition>
					<preCondition name="No SameSite-None">
						<add input="{RESPONSE_Set_Cookie}" pattern="." />
						<add input="{RESPONSE_Set_Cookie}" pattern="; SameSite=none" negate="true" />
					</preCondition>
				</preConditions>
			</outboundRules>
		</rewrite>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30AD4FE6B2A6AEED" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-14.0.0.0" newVersion="9.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="LinkedInNET.ApiV2" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.2.10.0" newVersion="1.2.10.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Business" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Chat" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.DAL" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.DomainModel" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Facebook" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Instagram" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Licensing" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.LinkedIn" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Mail" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Skype" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.SMS" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Telegram" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Twitter" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.WhatsApp" publicKeyToken="8c5134be849e992c" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="DevExpress.Data.v15.2" publicKeyToken="b88d1754d700e49a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-15.2.15.0" newVersion="15.2.15.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.Serialization.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.69.0.0" newVersion="1.69.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Google.Apis.Core" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.69.0.0" newVersion="1.69.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="DocumentFormat.OpenXml" publicKeyToken="8fb06cb64d019a17" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.10.0.0" newVersion="2.10.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.44.1.0" newVersion="1.44.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Un**fe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory.Data" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" culture="neutral" publicKeyToken="cc7b13ffcd2ddd51" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.3" newVersion="7.0.0.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" culture="neutral" publicKeyToken="669e0ddf0bb1aa2a" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.12.0" newVersion="2.0.12.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="SuperSocket.SocketBase" culture="neutral" publicKeyToken="6c80000676988ebb" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="1.6.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="SuperSocket.SocketEngine" culture="neutral" publicKeyToken="6c80000676988ebb" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="1.6.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="SuperSocket.Common" culture="neutral" publicKeyToken="6c80000676988ebb" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="1.6.4.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="4.0.2.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Azure.Amqp" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.4.0.0" newVersion="2.4.0.0" />
			</dependentAssembly>
		</assemblyBinding>
		<AppContextSwitchOverrides value="Switch.System.Net.DontEnableSchUseStrongCrypto=false" />
	</runtime>
</configuration>