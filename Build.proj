﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
	
	<PropertyGroup>
		<MSBuildCommunityTasksPath>$(MSBuildProjectDirectory)\.build</MSBuildCommunityTasksPath>
	</PropertyGroup>

	<PropertyGroup>
		<VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
	<VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
	</PropertyGroup>

	<Import Project="$(MSBuildCommunityTasksPath)\MSBuild.Community.Tasks.targets"/>
	<Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
	<Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
	<Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />

	<PropertyGroup Condition=" '$(BuildConfiguration)' == '' ">
		<BuildConfiguration>Release</BuildConfiguration>
	</PropertyGroup>

	<PropertyGroup Condition=" '$(BuildPlatform)' == '' ">
		<BuildPlatform>Mixed Platforms</BuildPlatform>
	</PropertyGroup>

	<Target Name="Clean">
		<DeleteTree Directories="**\obj\**;**\bin\**" />
	</Target>

	<Target Name="Version">
		<Version VersionFile="$(MSBuildCommunityTasksPath)\version.txt" Major="6" Minor="0" BuildType="Automatic" StartDate="04/10/2018" RevisionType="BuildIncrement">
			<Output TaskParameter="Major" PropertyName="Major" />
			<Output TaskParameter="Minor" PropertyName="Minor" />
			<Output TaskParameter="Build" PropertyName="Build" />
			<Output TaskParameter="Revision" PropertyName="Revision" />
		</Version>

		<Message Text="Revision: $(Revision)"/>

		<CreateProperty Value="$(Major).$(Minor)">
			<Output TaskParameter="Value" PropertyName="Version"/>
		</CreateProperty>

		<Math.Subtract Numbers="$(Minor);1">
			<Output TaskParameter="Result" PropertyName="PreviousVersionMinor" />
		</Math.Subtract>

		<CreateProperty Value="$(Major)\.$(PreviousVersionMinor)\.0\.0">
			<Output TaskParameter="Value" PropertyName="PreviousVersion"/>
		</CreateProperty>

		<CreateProperty Value="$(Version).$(Build).$(Revision)">
			<Output TaskParameter="Value" PropertyName="FileVersion"/>
			<Output TaskParameter="Value" PropertyName="InformationalVersion"/>
		</CreateProperty>
		
		<Attrib Files="$(MSBuildProjectDirectory)\GlobalAssemblyInfo.cs" ReadOnly="False" />
		
		<Time>
			<Output TaskParameter="Year" PropertyName="Year" />
		</Time>

		<Message Text="Version: $(Version)"/>
		<Message Text="FileVersion: $(FileVersion)"/>
		
		<AssemblyInfo CodeLanguage="CS"
									OutputFile="$(MSBuildProjectDirectory)\GlobalAssemblyInfo.cs"
									GenerateClass="false"
									AssemblyCopyright="Copyright Yoizen © 2011-$(Year). All rights reserved."
									AssemblyConfiguration="$(BuildConfiguration)"
									AssemblyVersion="$(Version)"
									AssemblyFileVersion="$(FileVersion)"
									AssemblyInformationalVersion="$(InformationalVersion)"
									AssemblyCulture=""
									AssemblyTrademark=""
									AssemblyProduct="ySocial"
									AssemblyCompany="Yoizen S.A."/>

		<Message Text="Versión anterior: $(PreviousVersion)" />

		<ItemGroup>
			<FilesToUpdate Include="Yoizen.Social.Web\web.config;Yoizen.Social.Service\app.config;Yoizen.Social.Surveys.Service\app.config;Yoizen.Social.Exporter\app.config;Yoizen.Social.DB\Script.PostDeployment.sql" />
		</ItemGroup>

		<FileUpdate Files="@(FilesToUpdate)"
            Regex="$(PreviousVersion)"
            ReplacementText="$(Version).0.0" />
	</Target>

	<!-- Projects to Build -->
	<ItemGroup>
		<ProjectFiles Include="$(MSBuildProjectDirectory)\Yoizen.Social.sln">
			<Properties>Configuration=$(BuildConfiguration);Platform=$(BuildPlatform)</Properties>
		</ProjectFiles>
	</ItemGroup>

	<Target Name="Compile" DependsOnTargets="Version">
		<Message Text="Configuration: $(BuildConfiguration)"/>
		<MSBuild Projects="@(ProjectFiles)" />
	</Target>

	<Target Name="Build">
		<CallTarget Targets="Compile" />

		<!--MSBuild Projects="$(MSBuildProjectDirectory)\Yoizen.Social.Web\Yoizen.Social.Web.csproj" 
						 Properties="Configuration=$(BuildConfiguration);Platform=AnyCPU;SolutionDir=$(MSBuildProjectDirectory)\;PublishProfile=Social - Release;DeployOnBuild=true"/-->

		<ReadLinesFromFile File="$(MSBuildCommunityTasksPath)\version.txt" Condition=" '$(FileVersion)' == '' ">
			<Output PropertyName="FileVersion" TaskParameter="Lines" />
		</ReadLinesFromFile>
		
		<!--Message Text="Creando tag en GIT"/>
		<Exec Command="git tag -a v$(FileVersion) -m &quot;Versión v$(FileVersion)&quot;" /-->
	</Target>

</Project>