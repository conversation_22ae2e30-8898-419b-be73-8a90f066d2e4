﻿CREATE PROCEDURE [dbo].[EmailContacts_Update]
    @ID INT
	, @Name NVARCHAR(50)
	, @LastName NVARCHAR(50)
	, @Email NVARCHAR(50)
	, @AgentID INT
	, @ResultCode INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        UPDATE [EmailContacts]
        SET Name = @Name,
            LastName = @LastName,
            Email = @Email,
            AgentID = @AgentID
        WHERE ID = @ID;

        IF @@ROWCOUNT = 0
        BEGIN
            SET @ResultCode = -1;
        END
        ELSE
        BEGIN
            SET @ResultCode = 1;
        END
    END TRY
    BEGIN CATCH
        IF ERROR_NUMBER() = 2627 OR ERROR_NUMBER() = 2601
        BEGIN
            SET @ResultCode = -2;
        END
        ELSE
        BEGIN
            SET @ResultCode = -99;
        END
    END CATCH
END