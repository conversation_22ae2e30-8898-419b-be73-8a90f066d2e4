﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Master.Master.cs" Inherits="Yoizen.Social.Web.Master" %>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" runat="server" id="html" dir="ltr" translate="no">
<head runat="server" id="header">
	<title>Yoizen ySocial</title>
	<link rel="Shortcut Icon" href="~/favicon.ico" runat="server" id="linkFavIcon" />
	<meta property="og:url" content="https://social.social.com/" />
	<meta property="og:type" content="website" />
	<meta property="og:title" content="Yoizen ySocial" />
	<meta property="og:description" content="Sistema de administración y supervisión de Yoizen ySocial" />
	<meta property="og:image" content="https://yoizen.com/wp-content/uploads/2021/04/yoizen-preview.jpg" />
	<meta property="og:image:secure_url" content="https://yoizen.com/wp-content/uploads/2021/04/yoizen-preview.jpg" />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:type" content="image/jpeg" />
	<meta property="fb:app_id" content="197887303586344" />
	<meta property="theme-color" content="#ffc900" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />

	<meta name="twitter:card" content="summary" />
	<meta name="twitter:site" content="@yoizen" />
	<meta name="twitter:creator" content="@yoizen" />

	<asp:PlaceHolder ID="placeholderCloudCss" runat="server" Visible="false">
		<script src="https://kit.fontawesome.com/b13d80c9ac.js" integrity="sha512-hAxve1QWFBARobhRz8cOZHEYDZgXgQK89wgV/kbQW7VDOfGMo4LB12G4LisXX92LncBYfQY4sUASvOMUWjxX6A==" crossorigin="anonymous"></script>
	</asp:PlaceHolder>
	<asp:Literal ID="literalStyleColorbox" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalStylePopup" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptTwemoji" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptJQuery" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptColorbox" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptCryptoJs" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptMaster" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptMenu" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptEnums" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptTimeZones" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptSocialServiceTypes" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptJSON" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptTooltip" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptPopover" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptSignalR" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptMomentJs" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptMomentTimeZoneJs" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptNumeralJs" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptPopup" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptRTNotifications" runat="server" EnableViewState="false" />
	<asp:Literal ID="literalScriptTitleNotifier" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptHE" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptDomToImage" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptPluralRuleParser" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n_messagestore" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n_fallbacks" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n_parser" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n_emitter" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptJQueryi18n_language" runat="server" EnableViewState="false"/>
	<asp:Literal ID="literalScriptOthersCloud" runat="server" EnableViewState="false"/>
	<asp:ContentPlaceHolder ID="contentplaceholderHead" runat="server" EnableViewState="false"/>
	<style type="text/css" runat="server" id="styleDefault" visible="false" EnableViewState="false"></style>
</head>
<body runat="server" id="body">
	<div id="divSideNav" class="sidenav collapsed" style="display: none">
		<div class="header">
			<a class="expand">
				<span class="far fa-lg fa-bars"></span>
			</a>
			<a class="pin" href="javascript:ToggleSideNavAlwaysVisible(true)" title="Dejar fijo" data-i18n-title="menu-fixed-position" tooltipdirection="bottom" data-container="body">
				<span class="far fa-thumbtack"></span>
			</a>
			<a class="collapse" title="Cerrar" tooltipdirection="bottom" data-container="body" data-i18n-title="menu-close">
				<span class="far fa-times"></span>
			</a>
		</div>
		<div class="contents">
			<div class="account">
				<div class="avatar">
					<img id="imgAvatar" />
				</div>
				<div class="name"></div>
			</div>
			<div class="menu"></div>
		</div>
		<div class="footer">
			<div class="internalchat">
				<asp:HyperLink NavigateUrl="~/Supervisor/InternalChat.aspx" runat="server" Target="_blank">
					<span class="far fa-fw fa-comments"></span>
					<span class="footer-item-text" data-i18n="menu-internalchat">Chat con agentes</span>
				</asp:HyperLink>
			</div>
			<div class="notifications">
				<a href="javascript:ShowLatestNotifications()">
					<div>
						<span class="far fa-fw fa-globe"></span>
						<div class="unread" id="divNotificationsCount"></div>
					</div>
					<span class="footer-item-text" data-i18n="menu-notification">Ver notificaciones</span>
				</a>
			</div>
			<div class="myprofile">
				<asp:HyperLink runat="server" NavigateUrl="~/Administration/MyProfile.aspx">
					<span class="far fa-fw fa-id-card"></span>
					<span class="footer-item-text" data-i18n="menu-myprofile">Mi perfil</span>
				</asp:HyperLink>
			</div>
			<div class="status">
				<asp:HyperLink runat="server" NavigateUrl="~/Administration/ServiceStatus.aspx">
					<span class="far fa-fw fa-monitor-heart-rate"></span>
					<span class="footer-item-text" data-i18n="menu-servicestatus">Estado del sistema</span>
				</asp:HyperLink>
			</div>
			<div class="settings">
				<asp:HyperLink runat="server" NavigateUrl="~/Supervisor/Settings.aspx">
					<span class="far fa-fw fa-cogs"></span>
					<span class="footer-item-text" data-i18n="menu-settings">Ajustes</span>
				</asp:HyperLink>
			</div>
			<div class="yflow">
				<asp:HyperLink ID="hyperlinkAccessYFlow" runat="server" Target="_blank" NavigateUrl="~/AccessYFlow.aspx">
					<span class="yzn-yFlowISO fa-fw"></span>
					<span class="footer-item-text"><span data-i18n="menu-yflow">Acceder a yFlow</span> <span class="fas fa-external-link-square" title="Se abre en una nueva pestaña" data-i18n-title="menu-open_in_new_tab"></span></span>
				</asp:HyperLink>
			</div>
           <asp:Panel ID="panelYUsage" runat="server" class="yusage">
				<asp:HyperLink ID="hyperlinkAccessYUsage" runat="server" Target="_blank" NavigateUrl="~/AccessYUsage.aspx">
					<img src="<%= ResolveUrl("~/Images/Icons/yUsageLogo.png") %>" alt="yUsage" style="height: 16px; vertical-align: middle;" />
					<span class="footer-item-text">
						<span data-i18n="menu-yusage">Acceder a yUsage</span>
						<span class="fas fa-external-link-square" title="Se abre en una nueva pestaña" data-i18n-title="menu-open_in_new_tab"></span>
					</span>
				</asp:HyperLink>
			</asp:Panel>
			<asp:Panel ID="panelYFlowContingencyLink" runat="server" CssClass="yflow">
				<asp:HyperLink ID="hyperlinkAccessYFlowContingency" runat="server" Target="_blank" NavigateUrl="~/AccessYFlowContingency.aspx">
					<span class="yzn-yFlowISO fa-fw"></span>
					<span class="footer-item-text"><span data-i18n="menu-yflow-contingency">Acceder a yFlow Contingencia</span> <span class="fas fa-external-link-square" title="Se abre en una nueva pestaña" data-i18n-title="menu-open_in_new_tab"></span></span>
				</asp:HyperLink>
			</asp:Panel>
			<div class="logout">
				<asp:HyperLink runat="server" NavigateUrl="~/Logout.aspx">
					<span class="far fa-fw fa-sign-out-alt"></span>
					<span class="footer-item-text" data-i18n="menu-logout">Cerrar sesión</span>
				</asp:HyperLink>
			</div>
		</div>
	</div>
	<form id="form1" runat="server">
		<input type="hidden" id="hiddenClientTzOffset" name="hiddenClientTzOffset" />
		<input type="hidden" id="hiddenClientTzId" name="hiddenClientTzId" />
		<div id="topBar">
			<div class="logo">
				<asp:HyperLink runat="server" NavigateUrl="~/Default.aspx">
					<div></div>
				</asp:HyperLink>
			</div>
			<div class="separator"></div>
			<div class="productname">
				<asp:HyperLink runat="server" NavigateUrl="https://yoizen.com/ysocial/" Target="_blank" rel="noopener noreferrer">
					<div></div>
				</asp:HyperLink>
			</div>
		</div>
		<div id="globalContainer">
			<div id="mainContainer">
				<div id="leftColContainer" runat="server" clientidmode="Static">
					<div id="leftCol">
						<asp:ContentPlaceHolder ID="contentplaceholderIzquierda" runat="server" />
					</div>
				</div>
				<div id="contentCol">
					<div id="contentArea">
						<div class="contentAreaHeaderPage">
							<h2 class="contentAreaHeaderPageTitle"><asp:ContentPlaceHolder ID="contentplaceholderTitulo" runat="server" /></h2>
						</div>
						<div class="contentAreaContents">
							<asp:ContentPlaceHolder ID="contentplaceholderContenido" runat="server">
							</asp:ContentPlaceHolder>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="pageFooter">
			<div class="copyright">
				<span class="copyrighttext"> Yoizen © 2011-<asp:Literal ID="literalYear" runat="server" /></span>
				<span class="copyrightoem"></span>
				<span class="copyrightversion"></span>
			</div>
			<div class="currenttask">
				<div class="currenttask-name"><span data-i18n="currenttask-executing">Ejecutando tarea</span>: <span id="spanCurrentTaskName"></span></div>
				<div class="currenttask-progress">
		            <div class="progress">
						<div id="divCurrentTaskProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
					</div>
				</div>
			</div>
			<div class="navigation">
				<span class="poweredby"></span><asp:HyperLink runat="server" NavigateUrl="http://www.yoizen.com" CssClass="about"><asp:Image runat="server" ImageUrl="~/Images/Yoizen.png" ImageAlign="AbsMiddle" AlternateText="Yoizen" /></asp:HyperLink>
			</div>
		</div>
		<div id="divRightPanel" class="rightpanel collapsed">
			<div class="buttons">
				<div class="button" onclick="ExpandRightPanel()" data-i18n-title="globals-expand" title="Expandir" tooltipdirection="bottom">
					<span class="fa fa-lg fa-arrow-to-left expand"></span>
					<span class="fa fa-lg fa-arrow-to-right collapse"></span>
				</div>
				<div class="button" onclick="ShowRightPanel(false)" data-i18n-title="globals-close" title="Cerrar" tooltipdirection="bottom">
					<span class="fa fa-lg fa-times"></span>
				</div>
			</div>
			<div class="contents" id="divRightPanelContainer">
				<asp:ContentPlaceHolder ID="contentplaceholderRightPanel" runat="server" />
			</div>
		</div>
	</form>
	<div style="display:none">
		<div id="divNotificationsManager" class="seccion">
			<div class="title">
				<h2 id="h2Title" data-i18n="supervisor-notifications-latest_notifications-title">Últimas Notificaciones</h2>
			</div>
			<div class="contents">
				<div id="divNotificationsContent" class="notificationsbox"></div>
				<div class="buttons">
					<label class="uiButton uiButtonLarge uiButtonConfirm">
						<button type="button" id="buttonSeeAll" data-i18n="supervisor-notifications-latest_notifications-view_all" onclick="window.open(notificationsPageURL, '_self')">Ver todas</button>
					</label>
					<label class="uiButton uiButtonLarge">
						<button type="button" id="buttonCancel" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
					</label>
				</div>
			</div>
		</div>
	</div>
</body>
</html>