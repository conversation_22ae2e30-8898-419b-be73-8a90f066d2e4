﻿<?xml version="1.0" encoding="UTF-8"?>
<License Debug="true" ValidUntil="2016-09-12" MachineIdentification="MtaDorRZAhclW0qkGnsHPepLw3A=&#xD;&#xA;" Expires="false" Version="2" IgnoreMachineIdentification="true">
	<Services>
		<Service ID="1" Name="Twitter" Type="Twitter" />
		<Service ID="2" Name="Facebook" Type="Facebook" />
		<Service ID="3" Name="Búsquedas de Twitter" Type="TwitterSearches" />
		<Service ID="4" Name="Búsquedas de Twitter de YznTest" Type="TwitterSearches" />
		<Service ID="5" Name="Mail" Type="Mail" />
		<Service ID="6" Name="Chat" Type="Chat" />
		<Service ID="7" Name="WhatsApp" Type="WhatsApp" />
		<Service ID="8" Name="Telegram" Type="Telegram" />
		<Service ID="9" Name="Facebook RT" Type="FacebookRt" />
		<Service ID="10" Name="Instagram" Type="Instagram" />
		<Service ID="11" Name="SMS" Type="SMS" />
		<Service ID="12" Name="LinkedIn" Type="LinkedIn" />
		<Service ID="13" Name="Skype" Type="Skype" />
		<Service ID="14" Name="Mail 2" Type="Mail" />
		<Service ID="15" Name="Facebook Messenger" Type="FacebookMessenger" />
		<Service ID="17" Name="YouTube" Type="YouTube" />
		<Service ID="19" Name="GooglePlay" Type="GooglePlay" />
	</Services>
	<Configuration>
		<MaxConcurrentAgents>0</MaxConcurrentAgents>
		<MaxNominalUsers>0</MaxNominalUsers>
		<AllowSimultaneousLogins>false</AllowSimultaneousLogins>
		<ReadOnly>false</ReadOnly>
		<AllowToSendWhatsappHSMMassiveByExternalIntegrations>true</AllowToSendWhatsappHSMMassiveByExternalIntegrations>
		<AllowAgentsToReturnMessagesToQueue>true</AllowAgentsToReturnMessagesToQueue>
		<AllowAgentsToReturnMessagesToSpecifiedQueue>true</AllowAgentsToReturnMessagesToSpecifiedQueue>
		<AllowAgentsToHideFacebookMessages>true</AllowAgentsToHideFacebookMessages>
		<AllowAgentsToRetweetMessages>true</AllowAgentsToRetweetMessages>
		<AllowAgentsToFavouriteTwitterMessages>true</AllowAgentsToFavouriteTwitterMessages>
		<AllowAgentsToLikeFacebookMessages>true</AllowAgentsToLikeFacebookMessages>
		<AllowCustomShortener>true</AllowCustomShortener>
		<AllowCustomBusinessDataRegex>true</AllowCustomBusinessDataRegex>
		<AllowMonitorAgents>true</AllowMonitorAgents>
		<AllowCoaching>true</AllowCoaching>
		<AllowInternalChat>true</AllowInternalChat>
		<AllowAgentsToFollowUnfollowTwitter>false</AllowAgentsToFollowUnfollowTwitter>
		<AllowSupervisorsToReplyMessages>true</AllowSupervisorsToReplyMessages>
		<SurveysEnabled>true</SurveysEnabled>
		<ExternalSurveysEnabled>true</ExternalSurveysEnabled>
		<MaxSurveys>0</MaxSurveys>
		<SurveysClientID>Yoizen</SurveysClientID>
		<UseAuxReasons>true</UseAuxReasons>
		<AllowToEditAuxReasons>true</AllowToEditAuxReasons>
		<MaxAuxReasons>25</MaxAuxReasons>
		<AllowIntegrations>true</AllowIntegrations>
		<AllowCognitiveServices>false</AllowCognitiveServices>
		<CognitiveServicesClientID>Dev</CognitiveServicesClientID>
		<AllowWebAgent>true</AllowWebAgent>
		<OnlyWebAgent>true</OnlyWebAgent>
		<AllowToCreateServices>true</AllowToCreateServices>
		<AllowedServiceTypesToCreate>Twitter FacebookRt TwitterSearches WhatsApp Telegram Mail Chat Skype FacebookMessenger TwitterRt Instagram MercadoLibre YouTube IntegrationChat LinkedIn GooglePlay VideoCall AppleMessaging GoogleRBM GoogleMyBusiness</AllowedServiceTypesToCreate>
		<AllowDisconnectAgents>true</AllowDisconnectAgents>
		<ClientID>@@MACHINENAME@@</ClientID>
		<AllowToExtendBusinessData>true</AllowToExtendBusinessData>
		<AllowToExtendProfile>true</AllowToExtendProfile>
		<AllowYFlow>true</AllowYFlow>
		<AllowWebServiceCallsInFilters>false</AllowWebServiceCallsInFilters>
		<AllowWhatsappOutbound>true</AllowWhatsappOutbound>
		<AllowWhatsappOutboundService>true</AllowWhatsappOutboundService>
		<MaxWhatsappServicesToCreate>5</MaxWhatsappServicesToCreate>
		<AllowWhatsappIntegrationTypeWhatsappWeb>false</AllowWhatsappIntegrationTypeWhatsappWeb>
		<AllowWhatsappIntegrationTypePostback>true</AllowWhatsappIntegrationTypePostback>
		<AllowPbxIntegration>true</AllowPbxIntegration>
		<AllowToConfigureACDBalancing>true</AllowToConfigureACDBalancing>
		<InTheCloud>true</InTheCloud>
		<AllowToExtendCase>true</AllowToExtendCase>
		<AllowToConfigureTimeZones>true</AllowToConfigureTimeZones>
		<AllowToHidePoweredBy>true</AllowToHidePoweredBy>
		<AllowToSetAuxReasonsByAgents>true</AllowToSetAuxReasonsByAgents>
		<UseSingleChatServiceOnPremise>true</UseSingleChatServiceOnPremise>
		<AllowAgentsToTransferMessagesToYFlow>true</AllowAgentsToTransferMessagesToYFlow>
		<AllowAssemblyCallsInFilters>false</AllowAssemblyCallsInFilters>
		<AllowAutomaticExport>true</AllowAutomaticExport>
		<AllowWhatsappIntegrationTypeMovistar>true</AllowWhatsappIntegrationTypeMovistar>
		<AllowWhatsappIntegrationTypeGupshup>false</AllowWhatsappIntegrationTypeGupshup>
		<AllowWhatsappIntegrationTypeTwilio>false</AllowWhatsappIntegrationTypeTwilio>
		<AllowWhatsappIntegrationTypeInteraxa>false</AllowWhatsappIntegrationTypeInteraxa>
		<AllowChatWithYFlowHistory>true</AllowChatWithYFlowHistory>
		<AllowServicesAsChats>true</AllowServicesAsChats>
		<MaxNominalAgents>0</MaxNominalAgents>
		<AllowWFMIntegration>false</AllowWFMIntegration>
		<WorkAsGateway>false</WorkAsGateway>
		<GatewayIntegrationType>CCasS</GatewayIntegrationType>
		<AllowToMarkAsReadWhatsappMessages>true</AllowToMarkAsReadWhatsappMessages>
		<AllowWhatsappOutboundWithoutCaseCreation>true</AllowWhatsappOutboundWithoutCaseCreation>
		<AllowWhatsappOutboundWithoutCaseWithContext>false</AllowWhatsappOutboundWithoutCaseWithContext>
		<AllowToGenerateWhatsappHSMJwt>true</AllowToGenerateWhatsappHSMJwt>
		<AllowToSaveAttachmentsInAzureStorage>true</AllowToSaveAttachmentsInAzureStorage>
		<SendToServiceBusInsteadOfDirectSend>false</SendToServiceBusInsteadOfDirectSend>
		<SaveReportsInStorage>true</SaveReportsInStorage>
		<UseDatabaseForIntervals>false</UseDatabaseForIntervals>
		<AllowedTwitterAppIds>892157,27671925,28280516</AllowedTwitterAppIds>
		<UseExternalServiceForIncomingMessages>false</UseExternalServiceForIncomingMessages>
		<AllowAgentsToStartVideoCall>true</AllowAgentsToStartVideoCall>
		<VideoProviderCubic>2a7c2979-4e7f-4f4e-80b2-acefde7e9bee</VideoProviderCubic>
		<MaxNominalVideoAgents>1</MaxNominalVideoAgents>
		<AllowWhatsappVoiceCalls>true</AllowWhatsappVoiceCalls>
		<AllowWhatsappVoiceCallsCloudApi>true</AllowWhatsappVoiceCallsCloudApi>
		<AllowWhatsappVoiceCallsRecording>true</AllowWhatsappVoiceCallsRecording>
		<CalculateSLLIndependently>false</CalculateSLLIndependently>
		<ContingencyBotEnabled>false</ContingencyBotEnabled>
		<AllowChatSummary>true</AllowChatSummary>
		<AllowYUsage>true</AllowYUsage>
	</Configuration>
	
<Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1" /><DigestValue>IGicOi1hFPZKoAc8ejczp3SxqSo=</DigestValue></Reference></SignedInfo><SignatureValue>NCBjfXEeDDxUmN/9ndzjYHtQ+HflG3Fy07Rn5smMYTBrJKDhOHRve8MNs7ws8dlvpT+fYkJ9AZ0Cq7prtFvU/DoODenbU+mrix1ABnmV6UAZXmc6lWZl7EtvAOgicBHda25QjxlHQLnhRG1uryaGq4LfWYTEWZOatC2UTGFIkwE=</SignatureValue></Signature></License>