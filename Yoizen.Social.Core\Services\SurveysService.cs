﻿using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Yoizen.Common;
using Azure.Messaging.ServiceBus;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DAL;

namespace Yoizen.Social.Core.Services
{
	public class SurveysService
#if !NETCOREAPP
		: IService
#endif
	{
		#region Fields

		private static Random random = new Random();
		private static Queue<int> randomValues = new Queue<int>();

		private ServiceBusClient client = null;
		private ServiceBusSender sender = null;

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="SurveysService"/>
		/// </summary>
		public SurveysService()
		{
		}

		#endregion

		#region Private Methods

		private List<int> GenerateRandom(int count, int min, int max)
		{
			if (max <= min || count < 0 ||
					// max - min > 0 required to avoid overflow
					(count > max - min && max - min > 0))
			{
				// need to use 64-bit to support big ranges (negative min, positive max)
				throw new ArgumentOutOfRangeException("Range " + min + " to " + max +
						" (" + ((Int64) max - (Int64) min) + " values), or count " + count + " is illegal");
			}

			// generate count random values.
			HashSet<int> candidates = new HashSet<int>();

			// start count values before max, and end at max
			for (int top = max - count; top < max; top++)
			{
				// May strike a duplicate.
				// Need to add +1 to make inclusive generator
				// +1 is safe even for MaxVal max value because top < max
				if (!candidates.Add(random.Next(min, top + 1)))
				{
					// collision, add inclusive max.
					// which could not possibly have been added before.
					candidates.Add(top);
				}
			}

			// load them in to a list, to sort
			List<int> result = candidates.ToList();

			// shuffle the results because HashSet has messed
			// with the order, and the algorithm does not produce
			// random-ordered results (e.g. max-1 will never be the first value)
			for (int i = result.Count - 1; i > 0; i--)
			{
				int k = random.Next(i + 1);
				int tmp = result[k];
				result[k] = result[i];
				result[i] = tmp;
			}

			return result;
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Procesa un caso para verificar si se debe enviar encuesta
		/// </summary>
		/// <param name="case">El caso que se evaluará</param>
		/// <param name="autoClosed">Indica si el procesamiento proviene de un cierre automático por paso del tiempo o un cierre a partir
		/// de una acción de agente, supervisor, yflow o algo configurado en el sistema</param>
		public async Task ProcessClosedCase(DomainModel.Case @case, bool autoClosed = false)
		{
			if (!this.Initialized)
				throw new InvalidOperationException("El servicio de encuestas no ha sido inicializado");

			if (!Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled)
			{
				@case.SurveyShouldSend = false;
				DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
				return;
			}

			if (!DomainModel.SystemSettings.Instance.EnableSurveys)
			{
				@case.SurveyShouldSend = false;
				DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
				return;
			}

			if (@case.SurveySentDate != null)
			{
				Tracer.TraceVerb("[SURVEYS] La encuesta para el caso {0} se envió el {1}", @case, @case.SurveySentDate);
				return;
			}

			if (@case.IsChatCase)
			{
				@case.SurveyShouldSend = false;
				DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
				return;
			}

			try
			{
				DomainModel.Message lastIncomingMessage = null;
				DomainModel.ISocialService socialService = null;
				DomainModel.QueueSurveyConfiguration configuration = null;
				DomainModel.Survey configurationSurvey = null;
				List<DomainModel.QueueSurveyConfiguration> configurations;
				var now = DateTime.Now;
				bool byQueue;

				if (@case.Messages != null &&
					@case.Messages.Any())
				{
					lastIncomingMessage = @case.Messages.LastOrDefault(m => !m.IsReply);
				}

				if (lastIncomingMessage == null)
				{
					if (autoClosed)
					{
						if (@case.LastIncomingMessageID != null)
						{
							lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new DAL.MessageDAO.RelatedEntitiesToRead(false)
							{
								PostedBy = true,
								Service = true,
								Queue = true
							}, false);
						}
					}
					else
					{
						if (@case.LastIncomingMessageID != null)
						{
							lastIncomingMessage = @case.Messages.FirstOrDefault(m => m.ID == @case.LastIncomingMessageID.Value);
							if (lastIncomingMessage == null)
							{
								lastIncomingMessage = DAL.MessageDAO.GetOne(@case.LastIncomingMessageID.Value, new DAL.MessageDAO.RelatedEntitiesToRead(false)
								{
									PostedBy = true,
									Service = true,
									Queue = true
								}, false);
							}
						}
					}

					if (lastIncomingMessage == null)
					{
						if (@case.IsMailCase)
						{
							Tracer.TraceVerb("[SURVEYS] Se irá a buscar los datos del último mensaje saliente del caso {0}", @case.ID);

							lastIncomingMessage = DAL.MessageDAO.GetLastOutgoingOfCase(@case.ID, new DAL.MessageDAO.RelatedEntitiesToRead(false)
							{
								RepliesToSocialUser = true,
								Service = true,
								Queue = true
							});
						}
					}
				}

				if (lastIncomingMessage == null)
				{
					Tracer.TraceVerb("[SURVEYS] No se encontró datos del último mensaje entrante o saliente del caso {0}, se ignora", @case.ID);
					return;
				}

				socialService = lastIncomingMessage.Service.SocialService;

				if (socialService == null)
				{
					Tracer.TraceVerb("[SURVEYS] Se produjo un error al obtener el ISocialService para el caso {0}, se ignora", @case.ID);
					@case.SurveyShouldSend = false;
					DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
					return;
				}

				if (@case.Queue == null)
				{
					if (lastIncomingMessage.Service.Type == DomainModel.ServiceTypes.Mail)
					{
						var settings = lastIncomingMessage.Service.Settings as DomainModel.ServiceSettings.MailSettings;
						if (settings == null ||
							!settings.SurveyEnabled ||
							settings.SurveyList == null ||
							settings.SurveyList.Count == 0)
						{
							return;
						}
						else
						{
							configurations = settings.SurveyList;
						}
					}
					else if (!Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow ||
						!lastIncomingMessage.Service.UsesYFlow ||
						!lastIncomingMessage.Service.YFlowSettings.SurveyEnabled)
					{
						return;
					}
					else
					{
						configurations = lastIncomingMessage.Service.YFlowSettings.SurveyList;
					}

					byQueue = false;
				}
				else
				{
					if (!@case.Queue.SurveyEnabled)
						return;

					configurations = lastIncomingMessage.Queue.SurveyList;

					byQueue = true;
				}

				bool withTagsOrTagsGroup = configurations.Any(config =>
																		(config.Tags != null && config.Tags.Length > 0) ||
																		(config.TagsToIgnore != null && config.TagsToIgnore.Length > 0) ||
																		(config.TagGroups != null && config.TagGroups.Length > 0) ||
																		(config.TagGroupsToIgnore != null && config.TagGroupsToIgnore.Length > 0));

				if (withTagsOrTagsGroup && !@case.HasTags)
				{
					// Leemos las etiquetas de los casos cerrados automáticamente porque no se tienen
					@case.Tags = DAL.TagDAO.GetAllByCase(@case.ID);
				}

				if (!@case.ClosedDate.HasValue)
				{
					Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} por no tener fecha de cierre", @case.ID);
					@case.SurveyShouldSend = false;
					DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
					return;
				}

				foreach (DomainModel.QueueSurveyConfiguration config in configurations)
				{
					var survey = DomainModel.Cache.Instance.GetItem<DomainModel.Survey>(config.SurveyID.ToString());
					if (!survey.Enabled)
					{
						Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} porque la encuesta {1} no está habilitada", @case.ID, config.SurveyID);
						continue;
					}

					if (config.MessagesCount.HasValue && @case.TotalMessages < config.MessagesCount.Value)
					{
						Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por tener menos mensajes {2} que los configurados {3}", @case.ID, config.SurveyID, @case.TotalMessages, config.MessagesCount.Value);
						continue;
					}

					if (config.CaseDuration.HasValue && @case.TotalTime < (config.CaseDuration.Value * 60))
					{
						Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por tener menos duración {2} que los configurados {3}", @case.ID, config.SurveyID, @case.TotalTime, config.CaseDuration.Value * 60);
						continue;
					}

					if (config.MaxSurveySend.HasValue && config.MaxSurveySend.Value != 0)
					{
						var from = new DateTime(@case.ClosedDate.Value.Year, @case.ClosedDate.Value.Month, 1);
						var to = from.AddMonths(1).AddDays(-1);
						var total = DAL.CaseDAO.GetTotalSurveysSentBySocialUserProfile(@case.Profile.ID, from, to, byQueue);

						if (total >= config.MaxSurveySend.Value)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por haber sido encuestado mas de {2} veces en el mes que los configurados {3}", @case.ID, config.SurveyID, total, config.MaxSurveySend.Value);
							continue;
						}
					}

					if (config.CaseWithAgentReply.HasValue &&
						config.CaseWithAgentReply.Value &&
						@case.RepliesByAgents == 0)
					{
						Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por no tener respuestas del agente", @case.ID, config.SurveyID);
						continue;
					}

					HashSet<int> allTags = new HashSet<int>();

					if (config.Tags != null && config.Tags.Length > 0)
					{
						allTags.UnionWith(config.Tags);
					}

					if (config.TagGroups != null && config.TagGroups.Length > 0)
					{
						foreach (var tg in config.TagGroups)
						{
							var tagGroup = TagGroupDAO.GetOneFromCache((short) tg);

							if (tagGroup != null)
							{
								if (tagGroup.Tags != null && tagGroup.Tags.Count > 0)
								{
									var arrayGroupTags = tagGroup.Tags;
									allTags.UnionWith(arrayGroupTags);
								}
							}
						}
					}

					HashSet<int> allTagsToIgnore = new HashSet<int>();

					if (config.TagsToIgnore != null && config.TagsToIgnore.Length > 0)
					{
						allTagsToIgnore.UnionWith(config.TagsToIgnore);
					}

					if (config.TagGroupsToIgnore != null && config.TagGroupsToIgnore.Length > 0)
					{
						foreach (var tg in config.TagGroupsToIgnore)
						{
							var tagGroup = TagGroupDAO.GetOneFromCache((short) tg);

							if (tagGroup != null)
							{
								if (tagGroup.Tags != null && tagGroup.Tags.Count > 0)
								{
									var arrayGroupTags = tagGroup.Tags;
									allTagsToIgnore.UnionWith(arrayGroupTags);
								}
							}
						}
					}

					if (allTags.Count > 0)
					{
						if (!config.SendIfNewCaseHasTag)
						{
							bool hasTags = allTags.Any(tag => @case.Tags.Contains(tag));
							if (!hasTags)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por no tener las etiquetas configuradas", @case.ID, config.SurveyID);
								continue;
							}
						}
					}

					if (allTagsToIgnore.Count > 0)
					{
						bool hasTagsToIgnore = allTagsToIgnore.Any(tag => @case.Tags.Contains(tag));
						if (hasTagsToIgnore)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por tener las etiquetas configuradas para ignorar", @case.ID, config.SurveyID);
							continue;
						}
					}

					if (config.CloseCaseCondition != DomainModel.QueueSurveyConfiguration.CloseCaseConditions.All)
					{
						if (config.CloseCaseCondition == DomainModel.QueueSurveyConfiguration.CloseCaseConditions.Agents &&
						  @case.ClosedBy != DomainModel.CaseClosingResponsibles.Agent)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por no haber sido cerrado por agente", @case.ID, config.SurveyID);
							continue;
						}

						if (config.CloseCaseCondition == DomainModel.QueueSurveyConfiguration.CloseCaseConditions.System &&
							@case.ClosedBy == DomainModel.CaseClosingResponsibles.Agent)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} por no haber sido cerrado por el sistema", @case.ID, config.SurveyID);
							continue;
						}
					}

					if (@case.SocialServiceTypes != null &&
						@case.SocialServiceTypes.Contains(DomainModel.SocialServiceTypes.WhatsApp))
					{
						if (lastIncomingMessage.SocialServiceType == DomainModel.SocialServiceTypes.WhatsApp)
						{
							if (lastIncomingMessage.Service != null &&
								lastIncomingMessage.Service.Type == DomainModel.ServiceTypes.WhatsApp)
							{
								var settings = lastIncomingMessage.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
								if (settings != null)
								{
									if (settings.IntegrationType == 1 || (byQueue && !settings.AllowSurveys))
									{
										Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el último servicio es de Whatsapp que no permite encuestas", @case.ID, config.SurveyID);
										continue;
									}
								}
							}

							if (DateTime.Now.Subtract(lastIncomingMessage.Date).TotalMinutes >= 1440)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el último mensaje {2} es de Whatsapp e ingresó hace más de 1 día", @case.ID, config.SurveyID, lastIncomingMessage);
								continue;
							}
						}
					}

					if (@case.SocialServiceTypes != null &&
						@case.SocialServiceTypes.Contains(DomainModel.SocialServiceTypes.FacebookMessenger))
					{
						if (lastIncomingMessage.SocialServiceType == DomainModel.SocialServiceTypes.FacebookMessenger)
						{
							if (lastIncomingMessage.Service != null &&
								lastIncomingMessage.Service.Type == DomainModel.ServiceTypes.FacebookMessenger)
							{
								var settings = lastIncomingMessage.Service.Settings as DomainModel.ServiceSettings.FacebookMessengerSettings;
								if (settings != null)
								{
									if (byQueue && !settings.AllowSurveys)
									{
										Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el último servicio es de messenger que no permite encuestas", @case.ID, config.SurveyID);
										continue;
									}
								}
							}

							if (DateTime.Now.Subtract(lastIncomingMessage.Date).TotalMinutes >= 1440)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el último mensaje {2} es de messenger e ingresó hace más de 1 día", @case.ID, config.SurveyID, lastIncomingMessage);
								continue;
							}
						}
					}

					if (!config.SendIfNewCaseExists && @case.Next != null)
					{
						if (config.SendIfNewCaseClosedByYflow)
						{
							@case.Next = CaseDAO.GetOneWithoutMessages(@case.Next.ID);
							//Corroboro que este abierto o (en caso que exista un caso nuevo cerrado) que este haya sido cerrado por yFlow, y en caso que haya sido cerrado por sistema, que no tenga Cola ni Agentes asociados (lo que significa que fue cerrado por yFlow por inactividad)
							if (@case.Next.Status == CaseStatuses.Open)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} esta abierto",
									@case.ID, config.SurveyID, @case.Next.ID);
								continue;
							}
							else if (@case.Next.ClosedBy != null && @case.Next.ClosedBy != CaseClosingResponsibles.YFlow && !(@case.Next.ClosedBy == CaseClosingResponsibles.System && @case.Next.Queue == null && @case.Next.Agents == 0))
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} no fue cerrado por yFlow",
									@case.ID, config.SurveyID, @case.Next.ID);
								continue;
							}
						}
						else
						{
							if (!config.SendIfNewCaseHasTag)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque ya existe un caso nuevo {2}",
									@case.ID, config.SurveyID, @case.Next.ID);
								continue;
							}								
						}
					}

					if (config.DontSendIfLastSurveyAfterMinutes > 0)
					{
						if (@case.Profile != null && !@case.Profile.RetrievedFromDatabase)
							@case.Profile = DAL.SocialUserProfileDAO.GetOne(@case.Profile.ID, false);

						if (@case.Profile != null &&
							@case.Profile.LastSurveySent != null)
						{
							var minutes = now.Subtract(@case.Profile.LastSurveySent.Value).TotalMinutes;
							if (minutes < config.DontSendIfLastSurveyAfterMinutes)
							{
								Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque la última encuesta al perfil {2} fue envíada hace {3} minutos y está configurado no enviar dentro de los {4} minutos", @case.ID, config.SurveyID, @case.Profile, minutes, config.DontSendIfLastSurveyAfterMinutes);
								continue;
							}
						}
					}


					if (config.SentRate < 100)
					{
						if (randomValues.Count == 0)
						{
							var sb = new StringBuilder();
							var list = GenerateRandom(100, 0, 100);
							foreach (int randomNumber in list)
							{
								sb.AppendFormat("{0}-", randomNumber);
								randomValues.Enqueue(randomNumber);
							}

							Tracer.TraceInfo("[SURVEYS] Nuevos números aleatorios generados: {0}", sb.ToString());
						}

						int number = randomValues.Dequeue();

						if (number > config.SentRate)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} porque no corresponde por porcentaje {2}", @case.ID, config.SurveyID, number);
							continue;
						}
					}
					//me guardo la primera encuesta que cumpla con todo
					configuration = config;
					configurationSurvey = survey;
					break;
				}

				if (configuration == null)
				{
					@case.SurveyShouldSend = false;
					DAL.CaseDAO.UpdateSurveyIgnore(@case.ID);
				}
				else
				{
					Tracer.TraceVerb("[SURVEYS] El caso {0} será agendado para enviar la encuesta {1}", @case.ID, configurationSurvey);
					@case.SurveyShouldSend = true;
					DAL.CaseDAO.UpdateSurveyShouldSend(@case);

					var jMessage = new Newtonsoft.Json.Linq.JObject();
					jMessage["type"] = "survey";
					jMessage["subtype"] = "send";
					jMessage["caseId"] = @case.ID;
					jMessage["lastIncomingMessageId"] = lastIncomingMessage.ID;
					jMessage["serviceId"] = socialService.ID;
					jMessage["surveyId"] = configuration.SurveyID;
					jMessage["byQueue"] = byQueue;
					jMessage["entityId"] = byQueue ? lastIncomingMessage.Queue.ID : lastIncomingMessage.Service.ID;
					jMessage["closedDate"] = Common.Conversions.DateTimeToUnixTime(@case.ClosedDate.Value);

					var oldSurvey = configuration.Survey;
					configuration.Survey = null;
					jMessage["configuration"] = JObject.FromObject(configuration);
					configuration.Survey = oldSurvey;

					var body = jMessage.ToString();

					var messageToSend = new ServiceBusMessage();
					messageToSend.Body = new BinaryData(body);
					messageToSend.ContentType = "application/json";

					var scheduleEnqueueTime = new DateTimeOffset(DateTime.UtcNow).AddMinutes(configuration.TimeToSend);

					try
					{
						var sequenceNumber = await this.sender.ScheduleMessageAsync(messageToSend, scheduleEnqueueTime);

						Tracer.TraceVerb("[SURVEYS] Se agendó el caso {0} para enviar la encuesta {1} dentro de {2} minutos", @case.ID, configurationSurvey, configuration.TimeToSend);
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("[SURVEYS] Se produjo un error al querer agendar el caso {0} para enviar la encuesta {1} dentro de {2} minutos: {3}", @case.ID, configurationSurvey, configuration.TimeToSend, ex);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[SURVEYS] Se produjo un error al evaluar si la encuesta para el caso {0} debe ser enviada: {1}", @case.ID, ex);
			}			
		}

		#endregion

		#region IService Implementation

		/// <summary>
		/// Devuelve si el servicio fue inicializado
		/// </summary>
		public bool Initialized { get; private set; }

		/// <summary>
		/// Libera los recursos que fueron utilizados por esta instancia
		/// </summary>
		public void Dispose()
		{
			var result = global::System.Threading.Tasks.Task.Run(async () =>
			{
				if (this.sender != null)
				{
					await this.sender.DisposeAsync();
					this.sender = null;
				}

				if (this.client != null)
				{
					if (!this.client.IsClosed)
						await this.client.DisposeAsync();
				}

				return true;
			}).Result;
		}

		/// <summary>
		/// Inicializa el servicio de casos
		/// </summary>
		public void Initialize()
		{
			Tracer.TraceInfo("[SURVEYS] Iniciando el servicio de encuestas");

			if (!Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled)
			{
				this.Initialized = true;
				return;
			}

			var clientOptions = new ServiceBusClientOptions();
			clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
			clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
				ServiceBusTransportType.AmqpWebSockets :
				ServiceBusTransportType.AmqpTcp;

			Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

			this.client = new ServiceBusClient(DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString, clientOptions);

			var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-surveys-cases";

			this.sender = this.client.CreateSender(queueName);

#if !NETCOREAPP
			if (DomainModel.SystemSettings.Instance.EnableSurveys)
			{
				if (!DomainModel.SystemStatus.Instance.SurveysSubscribedToPush)
				{
					var surveys = DomainModel.Cache.Instance.GetList<DomainModel.Survey>();

					global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(async (ct) =>
					{
						foreach (var survey in surveys)
						{
							if (survey.Published && survey.LastVersionPublished)
							{

								var (published, publishError) = await DomainModel.Survey.PublishSurvey(survey, json =>
								{
									var jSurvey = Newtonsoft.Json.Linq.JObject.Parse(json);
									jSurvey["push"] = true;
									jSurvey["clientIdForPush"] = Licensing.LicenseManager.Instance.License.Configuration.ClientID;
									return jSurvey.ToString();
								});

								if (published)
								{
									Tracer.TraceInfo("[SURVEYS] Se actualizó la encuesta {0} para indicar que debe ser con push", survey);
								}
								else
								{
									Tracer.TraceInfo("[SURVEYS] No se actualizó la encuesta {0} para indicar que debe ser con push: {1}", survey, publishError);
									survey.LastVersionPublished = false;
									DAL.SurveyDAO.UpdatePublished(survey);
								}
							}
						}

						DomainModel.SystemStatus.Instance.SurveysSubscribedToPush = true;
						DAL.SystemStatusDAO.Update("SurveysSubscribedToPush");
					});
				}
			}
#endif

			this.Initialized = true;
		}

		#endregion
	}
}
