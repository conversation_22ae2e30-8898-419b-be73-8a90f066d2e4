﻿CREATE TABLE [dbo].[EmailContacts] (
    [ID]       INT           IDENTITY (1, 1) NOT NULL,
    [Name]     NVARCHAR (50) NOT NULL,
    [LastName] NVARCHAR (50) NOT NULL,
    [Email]    NVARCHAR (50) NOT NULL,
    [AgentID]  INT           NOT NULL,
    CONSTRAINT [PK_EmailContact] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_EmailContacts_Agents] FOREIGN KEY ([AgentID]) REFERENCES [dbo].[Agents] ([AgentID]),
    CONSTRAINT [UQ_EmailContacts_Email_AgentID] UNIQUE NONCLUSTERED ([Email] ASC, [AgentID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_EmailContacts_AgentID_LastName]
    ON [dbo].[EmailContacts]([AgentID] ASC, [LastName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EmailContacts_AgentID_Name]
    ON [dbo].[EmailContacts]([AgentID] ASC, [Name] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EmailContacts_AgentID_Email]
    ON [dbo].[EmailContacts]([AgentID] ASC, [Email] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EmailContacts_AgentID]
    ON [dbo].[EmailContacts]([AgentID] ASC);

