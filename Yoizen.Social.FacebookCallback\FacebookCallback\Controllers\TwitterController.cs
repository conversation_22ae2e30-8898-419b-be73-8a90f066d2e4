﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Web.Http;
using FacebookCallback.Business;
using System.Web;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace FacebookCallback.Controllers
{
	public class TwitterController : ApiController
	{
		#region Constants

		private const string SecretKeyForHashing = "3N3TqAeRNMfwBfYie5IlSPCudWXs9qx4s1AB6IIno";

		#endregion

		#region Inner Classes

		class TwitterAppDetails
		{
			public int AppId { get; set; }
			public string Name { get; set; }
			public string ConsumerKey { get; set; }
			public string ConsumerSecret { get; set; }
			public string AccessToken { get; set; }
			public string AccessTokenSecret { get; set; }
			public string AppAccessToken { get; set; }
		}

		#endregion

		#region Fields

		static readonly Dictionary<int, TwitterAppDetails> twitterApps;

		#endregion

		#region Constructors

		static TwitterController()
		{
			twitterApps = new Dictionary<int, TwitterAppDetails>();
			twitterApps[892157] = new TwitterAppDetails()
			{
				AppId = 892157,
				Name = "Yoizen Social",
				ConsumerKey = "fM9RDHBV5BMt6U26okr3dg",
				ConsumerSecret = "3N3TqAeRNMfwBfYie5IlSPCudWXs9qx4s1AB6IIno",
				AccessToken = "188994529-3RkPVBPEnCyxKIzm5UTEWFO5YqwjPxHNszfKouAl",
				AccessTokenSecret = "K6Xwg38WbjHXrc61VgJT4YsZx5wjm8lBrgFtcEmiTIM",
				AppAccessToken = "AAAAAAAAAAAAAAAAAAAAAP2cDQAAAAAAMZ%2BivYlaD55cGES%2F3NAtlAbc%2Fn0%3DaW2qHL3ISZ3JJnraUDygs9bHY8gMF1y2fKsrhjerBXouM6LxLK"
			};
			twitterApps[17899969] = new TwitterAppDetails()
			{
				AppId = 17899969,
				Name = "Yoizen ySocial",
				ConsumerKey = "*************************",
				ConsumerSecret = "rtFe6Ox1x0M79DgHSRRpBfivBZRsmSvVJ5tVqpQM4osOoYZ7Jb",
				AccessToken = "188994529-6UO9zWNSSuBj8DZKVAppx6yIgn11wlgbXxIhDdSS",
				AccessTokenSecret = "6OZwFqfCdBFccB6Wnw3tXJkAkYJNiRriSssY5hkBjl2EI",
				AppAccessToken = "AAAAAAAAAAAAAAAAAAAAAMEhEQEAAAAAK04pSzybEf3ckSxtu40t%2BpfW%2BC4%3DKu0edUucENGMjL8bUNv8duRrG5tccSVbMnaOE7YyEvNl5Z0joG"
			};
		}

		#endregion

		#region Action Methods

		// GET: api/Facebook
		[ActionName("DefaultAction")]
		[HttpGet]
		public HttpResponseMessage Get([FromUri(Name = "crc_token")] string crcToken)
		{
			Yoizen.Common.Tracer.TraceInfo("Twitter invocó al endpoing para valid con crc_token={0} [{1}]", crcToken, Request.RequestUri.Query);

			var hash = new HMACSHA256(Encoding.ASCII.GetBytes(SecretKeyForHashing));
			var responseToken = Convert.ToBase64String(hash.ComputeHash(Encoding.ASCII.GetBytes(crcToken)));

			var response = new
			{
				response_token = string.Format("sha256={0}", responseToken)
			};

			var responseMessage = Request.CreateResponse(HttpStatusCode.OK, response);
			return responseMessage;
		}

		[ActionName("news")]
		[HttpGet]
		public HttpResponseMessage Get(string id, string ts)
		{
			Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));

			TwitterManager.CheckSubscriptionStatus();

			string queryString = this.Request.RequestUri.Query;

			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque no tiene header hash");
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "missing hash header"
				});
			}

			string hash = Request.Headers.GetValues("hash").First();
			if (!hash.StartsWith("sha256="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque el header hash no empieza con sha256=, si no que fue {0}", hash);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header (expecting sha256)"
				});
			}
			hash = hash.Substring(7);

			string queryHash = Encode(queryString, SecretKeyForHashing);

			if (!queryHash.Equals(hash, StringComparison.InvariantCultureIgnoreCase))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque el header hash contiene una firma incorrecta");
				if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header",
						expecting = queryHash
					});
				}
				else
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
			}

			if (HttpContext.Current.Application["LastTwitterFoldersProcess"] != null)
			{
				HttpContext.Current.Application.Lock();

				var date = (DateTime) HttpContext.Current.Application["LastTwitterFoldersProcess"];
				if (DateTime.Now.Date > date.Date)
				{
					try
					{
						TwitterManager.ProcessFolders();
						HttpContext.Current.Application["LastTwitterFoldersProcess"] = DateTime.Now.Date;
					}
					catch (Exception ex)
					{
						Yoizen.Common.Tracer.TraceError("Ocurrió un error manteniendo las carpetas: {0}", ex);
					}
				}

				HttpContext.Current.Application.UnLock();
			}


			return Request.CreateResponse(HttpStatusCode.OK, new TwitterManager().GetPageLastNews(id));
		}

		// POST: api/Facebook
		[ActionName("DefaultAction")]
		[HttpPost]
		public async Task<HttpResponseMessage> Post()
		{
			HttpContent content = Request.Content;

			string body = await content.ReadAsStringAsync();

			if (string.IsNullOrEmpty(body))
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers));
			else
				Yoizen.Common.Tracer.TraceInfo("{0} {1}{2}\r\n{3}", Request.Method, Request.RequestUri, LogManager.ConvertHeadersToString(Request.Headers), body);

			if (content.Headers.ContentType == null || !content.Headers.ContentType.MediaType.Equals("application/json"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene ContentType=application/json", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid content-type"
				});
			}

			if (!Request.Headers.Contains("x-twitter-webhooks-signature"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque no tiene header x-twitter-webhooks-signature", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "missing signature header"
				});
			}

			string xTwitterWebhooksSignature = Request.Headers.GetValues("x-twitter-webhooks-signature").First();
			if (!xTwitterWebhooksSignature.StartsWith("sha256="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header x-twitter-webhooks-signature no empieza con sha256=, si no que fue {1}", body, xTwitterWebhooksSignature);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header (expecting sha256)"
				});
			}
			xTwitterWebhooksSignature = xTwitterWebhooksSignature.Substring(7);

			string bodyHash = EncodeBase64(body, SecretKeyForHashing);

			if (!bodyHash.Equals(xTwitterWebhooksSignature, StringComparison.InvariantCultureIgnoreCase))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el update {0} porque el header x-twitter-webhooks-signature contiene una firma incorrecta", body);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header"
				});
			}

			try
			{
				var manager = new TwitterManager();
				await manager.GenerateFiles(body);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error procesando el update {0}: {1}", body, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("newsprocessed")]
		[HttpPost]
		public HttpResponseMessage PostNewsProcessed(string id)
		{
			string queryString = this.Request.RequestUri.Query;

			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque no tiene header hash");
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "missing hash header"
				});
			}

			string hash = Request.Headers.GetValues("hash").First();
			if (!hash.StartsWith("sha256="))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque el header hash no empieza con sha256=, si no que fue {0}", hash);
				return Request.CreateResponse(HttpStatusCode.BadRequest, new
				{
					error = "invalid signature header (expecting sha256)"
				});
			}
			hash = hash.Substring(7);

			string queryHash = Encode(queryString, SecretKeyForHashing);

			if (!queryHash.Equals(hash, StringComparison.InvariantCultureIgnoreCase))
			{
				Yoizen.Common.Tracer.TraceInfo("Se ignora el pedido porque el header hash contiene una firma incorrecta");
				if (Request.RequestUri.AbsoluteUri.StartsWith("http://localhost", StringComparison.InvariantCultureIgnoreCase))
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header",
						expecting = queryHash
					});
				}
				else
				{
					return Request.CreateResponse(HttpStatusCode.BadRequest, new
					{
						error = "invalid signature header"
					});
				}
			}

			try
			{
				TwitterManager manager = new TwitterManager();
				manager.DeleteTemporalFiles(id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de temporales de la página {0}: {1}", id, ex);
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		[ActionName("Restart")]
		[HttpDelete]
		public HttpResponseMessage Restart(string id)
		{
			if (!Request.Headers.Contains("hash"))
			{
				Yoizen.Common.Tracer.TraceInfo("Se invocó la eliminación de los archivos de novedades de la página {0}", id);
				return new HttpResponseMessage(HttpStatusCode.OK);
			}

			try
			{
				TwitterManager manager = new TwitterManager();
				manager.DeleteAllContents(id);

				Yoizen.Common.Tracer.TraceInfo("Se eliminó los archivos de novedades de la página {0}", id);
			}
			catch (Exception ex)
			{
				Yoizen.Common.Tracer.TraceError("Ocurrió un error eliminando los archivos de novedades de la página {0}: {1}", id, ex);
				return Request.CreateResponse(HttpStatusCode.InternalServerError, new
				{
					error = "couldn't suscribe",
					ex = ex
				});
			}

			return new HttpResponseMessage(HttpStatusCode.OK);
		}

		#endregion

		#region Private Methods & Utils

		private static string Encode(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA256(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				return myhmacsha1.ComputeHash(stream).Aggregate("", (s, e) => s + String.Format("{0:x2}", e), s => s);
			}
		}

		private static string EncodeBase64(string input, string key)
		{
			byte[] keyBytes = Encoding.UTF8.GetBytes(key);
			using (var myhmacsha1 = new HMACSHA256(keyBytes))
			{
				byte[] bytes = Encoding.UTF8.GetBytes(input);
				System.IO.MemoryStream stream = new System.IO.MemoryStream(bytes);
				var hash = myhmacsha1.ComputeHash(stream);
				return Convert.ToBase64String(hash);
			}
		}

		#endregion
	}
}