<div>
    <div class="box box-widget social-case-conversation">
        <div class="box-widget-header social-case-conversation-header">
            <span ng-if="!caseCtrl.actAsChat || caseCtrl.caseReadOnly">{{ 'CASE_MESSAGES' | translate }}</span>
            <span ng-if="caseCtrl.actAsChat && !caseCtrl.caseReadOnly" class="hidden-xs visible-sm visible-md visible-lg">{{ 'CASE' | translate }} #{{ caseCtrl.socialCase.data.case.id }}</span>
            <a ng-click="caseCtrl.showCasePanel()" ng-if="caseCtrl.actAsChat && !caseCtrl.caseReadOnly" class="visible-xs hidden-sm hidden-md hidden-lg">{{'CASE' | translate }} #{{ caseCtrl.socialCase.data.case.id }}</a>
        </div>
        <div class="box-widget-body">
            <div class="case-conversation-background whatsapp" ng-if="caseCtrl.actAsChatInfo.showBackground && caseCtrl.socialCase.data.socialServiceType === caseCtrl.socialServiceTypes.WhatsApp"></div>
            <div class="case-conversation-background telegram" ng-if="caseCtrl.actAsChatInfo.showBackground && caseCtrl.socialCase.data.socialServiceType === caseCtrl.socialServiceTypes.Telegram"></div>
            <case-conversation social-case="caseCtrl.socialCase"
                               allowed-to-reply-grouped-messages="!caseCtrl.socialCase.isMyOutgoingCases"
                               case-read-only="caseCtrl.caseReadOnly"
                               act-as-chat="caseCtrl.actAsChat"></case-conversation>

            <div ng-if="caseCtrl.actAsChat && !caseCtrl.caseReadOnly && caseCtrl.allowedToReply && caseCtrl.actAsChatInfo.allowedToRepy"
                 class="social-case-reply-as-chat"
                 ng-class="{ 'loading': caseCtrl.actAsChatInfo.loading }">
                <div class="social-case-reply-as-chat-context"
                     ng-if="caseCtrl.actAsChatInfo.contextMessage">
                    <div class="context-message">
                        <span class="from">{{ caseCtrl.actAsChatInfo.getContextMessageUser() }}</span>
                        <span ng-bind-html="caseCtrl.actAsChatInfo.getContextMessageBody()" class="body-text"></span>
                    </div>
                    <div class="remove">
                        <a ng-click="caseCtrl.actAsChatInfo.removeContextMessage()"><span class="fa fa-lg fa-times"></span></a>
                    </div>
                </div>

                <div class="social-case-reply-as-chat-compose" ng-if="caseCtrl.actAsChatInfo.isRecording" style="text-align: center; align-items: center; justify-content: center;">
                    <button type="button"
                            style="margin-left: 15px;"
                            ng-click="caseCtrl.actAsChatStopRecording()"
                            class="btn btn-flat btn-default"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'STOP' | translate }}">
                        <i class="fa fa-square"></i>
                        <span class="hidden-xs">{{'STOP' | translate}}</span>
                    </button>
                    <button type="button"
                            style="margin-left: 15px;"
                            ng-click="caseCtrl.actAsChatDeleteRecording()"
                            class="btn btn-flat btn-default"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'DELETE' | translate }}">
                        <i class="fa fa-trash"></i>
                        <span class="hidden-xs">{{'DELETE' | translate}}</span>
                    </button>
                    <timer style="padding-left: 10px;" interval="1000" autostart="false" max-time-unit="'hour'">
                        {{hhours}}:{{mminutes}}:{{sseconds}}
                    </timer>
                </div>
                <div class="social-case-reply-as-chat-compose" style="text-align: center; align-items: center; justify-content: center;">
                    <div id="audio">

                    </div>
                    <button type="button"
                            style="margin-left: 15px;"
                            ng-if="!caseCtrl.actAsChatInfo.noAudio"
                            ng-click="caseCtrl.actAsChatSendAudio()"
                            class="btn btn-flat btn-default"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'SEND' | translate }}">
                        <i class="fa fa-play"></i>
                        <span class="hidden-xs">{{'SEND' | translate}}</span>
                    </button>
                    <button type="button"
                            style="margin-left: 15px;"
                            ng-if="!caseCtrl.actAsChatInfo.noAudio"
                            ng-click="caseCtrl.actAsChatDeleteAudio()"
                            class="btn btn-flat btn-default"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'DELETE' | translate }}">
                        <i class="fa fa-trash"></i>
                        <span class="hidden-xs">{{'DELETE' | translate}}</span>
                    </button>
                </div>

                <div class="social-case-reply-as-chat-compose" ng-if="!caseCtrl.actAsChatInfo.isRecording && caseCtrl.actAsChatInfo.noAudio">
                    <textarea class="form-control" rows="1" id="textareaActAsChat"
                              autoheight
                              ng-model="caseCtrl.actAsChatInfo.text"
                              ng-keydown="caseCtrl.actAsChatOnKeydown($event)"
                              ng-change="caseCtrl.actAsChatOnChange($event)"
                              placeholder="{{ 'WRITE_CHATMESSAGE' | translate }}">
                    </textarea>
                    <a class="social-case-reply-as-chat-send"
                       ng-click="caseCtrl.handleSendClick($event)">
                        <svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="#263238" fill-opacity=".45" d="M1.101 21.757L23.8 12.028 1.101 2.3l.011 7.912 13.623 1.816-13.623 1.817-.011 7.912z"></path></svg>
                    </a>
                    <div class="social-case-reply-as-chat-send-loading">
                        <span class="fa fa fa-spinner fa-spin"></span>
                    </div>
                </div>

                <div class="social-case-reply-as-chat-buttons" ng-if="!caseCtrl.actAsChatInfo.isRecording && caseCtrl.actAsChatInfo.noAudio">
                    <button type="button"
                            ng-if="caseCtrl.actAsChatInfo.canAttach"
                            class="btn btn-flat btn-default"
                            ng-click="caseCtrl.actAsChatAttachFile()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'ATTACH' | translate }}">
                        <i class="fa fa-paperclip"></i>
                        <span class="hidden-xs">{{'ATTACH' | translate}}</span>
                        <span class="bold" ng-if="caseCtrl.actAsChatInfo.attachmentsQueue.length > 0">({{ caseCtrl.actAsChatInfo.attachmentsQueue.length }})</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-click="caseCtrl.actAsChatShortUrls()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'SHORT_URLS' | translate }}">
                        <i class="fa fa-compress"></i>
                        <span class="hidden-xs">{{'SHORT_URLS' | translate}}</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-click="caseCtrl.actAsChatShowPredefinedAnswers()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'PREDEFINED_ANSWERS' | translate }}">
                        <i class="fa fa-book"></i>
                        <span class="hidden-xs">{{'PREDEFINED_ANSWERS' | translate}}</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-if="caseCtrl.actAsChatInfo.allowedToSendForms"
                            ng-click="caseCtrl.actAsChatShowForms()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'APPLE_FORMS' | translate }}">
                        <i class="fa fa-tasks"></i>
                        <span class="hidden-xs">{{'APPLE_FORMS' | translate}}</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-if="caseCtrl.allowedToSendAudio && !caseCtrl.actAsChatInfo.isInCall"
                            ng-click="caseCtrl.actAsChatRecordAudio()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'RECORD' | translate }}">
                        <i class="fa fa-microphone"></i>
                        <span class="hidden-xs">{{'RECORD' | translate}}</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-if="caseCtrl.allowedToSpeak && caseCtrl.noMobile && !caseCtrl.actAsChatInfo.isInCall"
                            ng-click="caseCtrl.actAsChatSpeechToText()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'SPEAK' | translate }}">
                        <i class="fa fa-microphone-stand" id="mic"></i>
                        <span class="hidden-xs">{{'SPEAK' | translate}}</span>
                    </button>

                    <button type="button"
                            class="btn btn-flat btn-default"
                            ng-if="caseCtrl.actAsChatInfo.allowedToSendWhatsappVoiceCallInteractiveMessage && !caseCtrl.actAsChatInfo.isInCall"
                            ng-click="caseCtrl.actAsChatSendWhatsappVoiceCallInteractiveMessage()"
                            tooltip-placement="top" title="" uib-tooltip="{{ 'SEND_WHATSAPP_VOICECALL_INTERACTIVE_MESSAGE' | translate }}">
                        <i class="fa fa-phone-alt"></i>
                        <span class="hidden-xs">{{'SEND_WHATSAPP_VOICECALL_INTERACTIVE_MESSAGE' | translate}}</span>
                    </button>

                    <emoji-button text="caseCtrl.actAsChatInfo.text"
                                  selector="#textareaActAsChat">
                    </emoji-button>
                </div>
            </div>

            <div class="box-widget-body-footer social-case-conversation-buttons">
                <div class="social-case-conversation-buttons-left">
                    <active-button ng-click="caseCtrl.getPreviousMessages()"
                                   button="caseCtrl.buttonLoadingOptions"
                                   ng-if="(caseCtrl.socialCase.data.case.messages.length < caseCtrl.socialCase.data.case.totalMessages) && !caseCtrl.hideLoadPreviousMessagesButton && !caseCtrl.actAsChatInfo.isRecording && caseCtrl.actAsChatInfo.noAudio"
                                   spinner-icon="fa-spinner"></active-button>
                    <active-button ng-click="caseCtrl.getNewEnqueuedMessages()"
                                   button="caseCtrl.buttonLoadingNewMessagesOptions"
                                   ng-if="caseCtrl.socialCase.allowToReloadCase && !caseCtrl.hideLoadNewMessagesButton"
                                   spinner-icon="fa-spinner"></active-button>
                </div>
                <div ng-if="caseCtrl.showActionButtonsForMyCases"
                     class="social-case-conversation-buttons-right">
                    <button type="button"
                            ng-if="caseCtrl.allowedToReply"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.answerMessageForMyCases()">
                        {{'SEND' | translate}}
                    </button>
                    <button type="button"
                            ng-if="caseCtrl.allowedToSendHSM"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.answerMessageWithHSMForMyCases()">
                            {{'SEND_TEMPLATE' | translate}}
                    </button>
                    <button type="button"
                            class="btn btn-flat btn-action"
                            ng-if="caseCtrl.showRemoveMarkAsPending()"
                            ng-click="caseCtrl.removeMarkAsPendingMessage()">
                        {{'REMOVE_PENDING_MESSAGE' | translate}}
                    </button>
                </div>
                <div ng-if="caseCtrl.showActionButtons() && !caseCtrl.actAsChatInfo.isRecording && caseCtrl.actAsChatInfo.noAudio"
                     class="social-case-conversation-buttons-right">

                    <button type="button"
                            ng-if="caseCtrl.allowedToReply && !caseCtrl.actAsChat"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.answerMessage()">
                        {{'ANSWER_VERB' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.actAsChat && caseCtrl.actAsChatInfo.allowedToRepy && caseCtrl.actAsChatInfo.addedMessages > 0 && !caseCtrl.actAsChatInfo.canCloseCase && !caseCtrl.actAsChatInfo.isInCall"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.actAsChatInfo.finishMessage(false)">
                        <span>{{'FINISH' | translate}}</span>
                    </button>

                    <div class="btn-group" ng-if="caseCtrl.actAsChat && caseCtrl.actAsChatInfo.allowedToRepy && caseCtrl.actAsChatInfo.addedMessages > 0 && caseCtrl.actAsChatInfo.canCloseCase && !caseCtrl.actAsChatInfo.isInCall">
                        <button type="button"
                                class="btn btn-flat btn-action dropdown-toggle"
                                data-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                            <span>{{'FINISH' | translate}}</span>
                            <span class="caret caret-up"></span>
                        </button>
                        <ul class="dropdown-menu drop-up">
                            <li>
                                <a ng-click="caseCtrl.actAsChatInfo.finishMessage(false)">
                                    <span class="bold">{{'FINISH' | translate}}</span>
                                </a>
                            </li>
                            <li role="separator" class="divider"></li>
                            <li>
                                <a ng-click="caseCtrl.actAsChatInfo.finishMessage(true)">
                                    <span>{{ 'FINISH_AND_CLOSE' | translate }}</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <button type="button"
                            ng-if="caseCtrl.allowedToReplyWhatsappWithHSM"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.answerMessageWithHSM()">
                        {{'REPLY_WITH_TEMPLATE' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.allowedToReplyMessengerWithTag"
                            class="btn btn-flat btn-action"
                            ng-click="caseCtrl.answerMessageWithMessengerTag()">
                        {{'REPLY_WITH_TEMPLATE' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.allowedToDiscard && (!caseCtrl.actAsChatInfo || !caseCtrl.actAsChatInfo.isInCall)"
                            class="btn btn-flat"
                            ng-click="caseCtrl.discardCase()">
                        {{'DISCARD' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.allowedToReturnToQueue && !caseCtrl.socialCase.data.hideReturnToQueue && (!caseCtrl.actAsChatInfo || !caseCtrl.actAsChatInfo.isInCall)"
                            ng-click="caseCtrl.showReturnToQueue()"
                            class="btn btn-flat">
                        {{'RETURN_TO_QUEUE' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.allowedToTransferToQueue && !caseCtrl.socialCase.data.hideReturnToQueue && (!caseCtrl.actAsChatInfo || !caseCtrl.actAsChatInfo.isInCall)"
                            ng-click="caseCtrl.showTransferToQueue()"
                            class="btn btn-flat">
                        {{'TRANSFER' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.allowedToReturnToYFlow && (!caseCtrl.actAsChatInfo || !caseCtrl.actAsChatInfo.isInCall)"
                            ng-click="caseCtrl.showReturnToYFlow()"
                            class="btn btn-flat">
                        {{'RETURN_TO_YFLOW' | translate}}
                    </button>

                    <button type="button"
                            ng-if="caseCtrl.isMailAndCanGenerateOutgoing()"
                            ng-click="caseCtrl.generateMailOutgoing()"
                            class="btn btn-flat">
                        {{'GENERATE_OUTGOING' | translate}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="tagsTooltipTemplate.html">
    <div ng-repeat="tag in caseCtrl.socialCase.data.case.tags"> {{ tag.fullName }}</div>
</script>
