﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.ServiceProcess;
using System.Text;
using Yoizen.Common;

namespace Yoizen.Social.Surveys.Service
{
	public class ServicePreshutdownBase : ServiceBase
	{
		#region Properties

		/// <summary>
		/// Devuelve si el servicio está en estado previo a Shutdown
		/// </summary>
		public bool Preshutdown { get; private set; }

		#endregion
		
		#region Constructors

		public ServicePreshutdownBase()
			: base()
		{
			Version versionWinVistaSp1 = new Version(6, 0, 6001);
			if (Environment.OSVersion.Platform == PlatformID.Win32NT && Environment.OSVersion.Version >= versionWinVistaSp1)
			{
				var acceptedCommandsField = typeof(ServiceBase).GetField("acceptedCommands", BindingFlags.Instance | BindingFlags.NonPublic);
				if (acceptedCommandsField == null)
					throw new InvalidOperationException("Private field acceptedCommands not found on ServiceBase");

				int acceptedCommands = (int) acceptedCommandsField.GetValue(this);
				acceptedCommands |= 0x00000100; //SERVICE_ACCEPT_PRESHUTDOWN;
				acceptedCommandsField.SetValue(this, acceptedCommands);

				Yoizen.Common.Tracer.TraceInfo("Subscripto al PRESHUTDOWN");
			}
		}

		#endregion

		#region Overrides

		protected override void OnCustomCommand(int command)
		{
			// command is SERVICE_CONTROL_PRESHUTDOWN
			if (command == 0x0000000F)
			{
				Yoizen.Common.Tracer.TraceInfo("Se recibió SERVICE_CONTROL_PRESHUTDOWN");

				var baseCallback = typeof(ServiceBase).GetMethod("ServiceCommandCallback", BindingFlags.Instance | BindingFlags.NonPublic);
				if (baseCallback == null)
					throw new InvalidOperationException("Private method ServiceCommandCallback not found on ServiceBase");

				try
				{
					this.Preshutdown = true;

					Yoizen.Common.Tracer.TraceInfo("Se invoca al Close");

					//now pretend stop was called 0x00000001
					baseCallback.Invoke(this, new object[] { 0x00000001 });
				}
				finally
				{
					this.Preshutdown = false;
				}
			}
			else
			{
				base.OnCustomCommand(command);
			}
		}

		#endregion
	}
}