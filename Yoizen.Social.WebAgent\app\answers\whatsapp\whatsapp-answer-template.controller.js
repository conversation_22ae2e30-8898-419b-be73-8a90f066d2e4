(function () {

    'use strict';

    angular.module('socialApp')
        .controller('WhatsappAnswerTemplateMessageController', WhatsappAnswerTemplateMessageController);

    WhatsappAnswerTemplateMessageController.$inject = [
        '$scope',
        'agentService',
        '$log',
        'utilsService',
        '$translate',
        'messagesService',
        'commonActionsService',
        'toastr',
        'modalSocialService',
        'settingsService',
        '$timeout',
        '$rootScope',
        '$sce'
    ];

    function WhatsappAnswerTemplateMessageController($scope,
                                                     agentService,
                                                     $log,
                                                     utilsService,
                                                     $translate,
                                                     messagesService,
                                                     commonActionsService,
                                                     toastr,
                                                     modalSocialService,
                                                     settingsService,
                                                     $timeout,
                                                     $rootScope,
                                                     $sce) {
        var vm = this;

        // --> Variables globales para uso de ortografía
        var configurations = utilsService.toCamel(socialServiceTypesConfiguration);
        var messageOriginal = !vm.socialCase.createCase ? messagesService.getMessageById(vm.socialCase.data.id) : null;
        var isOutgoing = vm.socialCase.outgoing;
        var isMyOutgoingCases = vm.socialCase.isMyOutgoingCases;
        var isNewProfileOutgoing = vm.socialCase.outgoing && !vm.socialCase.data.socialUserProfile;
        var answerWasSentSuccessfully, answerCouldNotBeSent;

        // --> Bindeos del view model

        if (messageOriginal) {
            vm.answer = messageOriginal.message.actualData.answer;
        }
        else {
            vm.socialCase.data.socialUserProfile = vm.socialCase.data.case.profile;
            var service = settingsService.getService(vm.socialCase.data.case.services[0] ? vm.socialCase.data.case.services[0] : vm.socialCase.data.service);
            vm.socialCase.data.service = service;
            vm.answer = {
                displayName: getUserAccount(vm.socialCase.data).displayName,
                profile: vm.socialCase.data.profile ? vm.socialCase.data.profile : vm.socialCase.data.socialUserProfile,
                socialParsed: vm.socialCase.data.socialParsed,
                service: service,
                case: vm.socialCase.data.case,
                options: {
                    isOutgoing: true,
                    isMyOutgoingCases: vm.socialCase.data.isMyOutgoingCases
                },
                actions: {
                    messageOptionsAreValid: null
                },
                isAnswering: null
            };
        }

        vm.cancelAnswer = cancelAnswer;
        vm.isPrivateAndCanAttach = isPrivateAndCanAttach;
        vm.answerMessage = answerMessage;
        vm.templateChanged = templateChanged;
        vm.templateParameterChanged = templateParameterChanged;
        vm.addButtonsParameters = addButtonsParameters;
        vm.answer.messageOriginal = messageOriginal;
        vm.selectedSocialCase = vm.socialCase;
        vm.whatsappConfiguration = utilsService.getObjectByIdInArray(configurations, SocialServiceTypes.WhatsApp).configuration;
        vm.canCloseCase = settingsService.getAgent().allowedToCloseCases;
        vm.templates = null;
        vm.templateParameters = null;
        vm.templateHeaderChanged = templateHeaderChanged;
        vm.mediaInputSelect = undefined;
        vm.principalQueue = [];
        vm.attachFiles = attachFiles;
        vm.attachFromFileUrl = undefined;
        vm.headerInputText = undefined;

        if (typeof(vm.answer.templateIndex) === 'undefined') {
            vm.answer.templateIndex = null;
        }
        if (typeof(vm.answer.templateParameters) === 'undefined') {
            vm.answer.templateParameters = null;
        }
        if (typeof (vm.isPublicUrl) === 'undefined')
            vm.isPublicUrl = true;

        activate();

        if (vm.answer.templateIndex !== null && vm.answer.templateParameters !== null) {
            templateChanged();
        }

        function getUserAccount(selectedCase) {
            if (isNewProfileOutgoing) {
                var lastIndexMessage = selectedCase.case.messages.length - 1;
                var socialUser = selectedCase.case.messages[lastIndexMessage].socialUser;
                return socialUser ? socialUser : {
                    displayName: 'N/A'
                };
            }
            else {
                var accounts = selectedCase.socialUserProfile.socialUserAccounts;
                for (var a = 0; a < accounts.length; a++) {
                    if (accounts[a].socialServiceType === SocialServiceTypes.WhatsApp) {
                        return accounts[a];
                    }
                }
            }
        }

        function activate() {
            $translate([
                'TEXT_ANSWER_PLACEHOLDER',
                'ANSWER_WAS_SENT_SUCCESSFULLY',
                'ANSWER_COULD_NOT_BE_SENT_TRY_AGAIN'
            ])
                .then(function (translations) {
                    vm.replyTextPlaceholder = translations.TEXT_ANSWER_PLACEHOLDER;
                    answerWasSentSuccessfully = translations.ANSWER_WAS_SENT_SUCCESSFULLY;
                    answerCouldNotBeSent = translations.ANSWER_COULD_NOT_BE_SENT_TRY_AGAIN;
                });

            if (typeof (isMyOutgoingCases) === 'undefined') {
                isMyOutgoingCases = false;
            }

            if (typeof (vm.socialCase.data.case.messagesInQueue) === 'undefined') {
                vm.socialCase.data.case.messagesInQueue = false;
            }

            if (typeof (vm.socialCase.data.case.messagesAssigned) === 'undefined') {
                vm.socialCase.data.case.messagesAssigned = false;
            }

            if (vm.canCloseCase) {
                if (isMyOutgoingCases &&
                    (vm.socialCase.data.case.messagesInQueue || vm.socialCase.data.case.messagesAssigned)) {
                    // Si hay mensajes en cola o asignados del mismo caso, no se puede cerrar el caso
                    vm.canCloseCase = false;
                    console.log(`No se puede cerrar el caso ${vm.socialCase.data.case.id} porque hay mensajes encolados o asignados`);
                }
            }

            vm.templates = vm.selectedSocialCase.data.service.settings.hSMTemplates.map(function (template, index) {
                return {
                    id: index,
                    label: template.description
                };
            });
        }

        const HSMTemplateHeaderTypes = {
            none: 0,
            text: 1,
            media: 2
        };
        const HSMTemplateHeaderMediaTypes = {
            none: 0,
            document: 1,
            image: 2,
            video: 3
        };
        const HSMTemplateButtonsTypes = {
            none: 0,
            quickReply: 1,
            callToAction: 2,
            mixed: 3,
            authCode: 4
        };
        const HSMTemplateCallToActionButtonTypes = {
            url: 1,
            call: 2,
            offer: 3
        };
         const HSMTemplateAuthCodeButtonTypes = {
            authCode: 1
        };
        const HSMTemplateCallToActionUrlButtonTypes = {
            fixed: 1,
            dynamic: 2
        };

        if (typeof (vm.mediaInputSelect) === 'undefined' || vm.mediaInputSelect === null) {
            vm.mediaInputSelect = 'url';
        }

        function templateChanged() {
            vm.headerInputText = undefined;
            let template = vm.selectedSocialCase.data.service.settings.hSMTemplates[vm.answer.templateIndex];
            vm.answer.template = template;
            vm.answer.templateWithParameters = $sce.trustAsHtml(template.template.replaceAll('\n', '<br />'));
            if (typeof (template.headerText) === 'string') {
                vm.answer.headerWithParameters = $sce.trustAsHtml(template.headerText.replaceAll('\n', '<br />'));
            }
            else {
                vm.answer.headerWithParameters = '';
            }

            //Header build
            if (typeof (template.headerType) === 'number') {
                let header = {};
                vm.answer.templateHeader = {};
                switch (template.headerType) {
                    case HSMTemplateHeaderTypes.text:
                        header = {
                            type: 'text',
                            text: null
                        };
                        if (template.headerTextParameter !== null) {
                            header.text = {
                                parameter: {
                                    name: template.headerTextParameter.name,
                                    value: ''
                                }
                            };
                        }
                        break;

                    case HSMTemplateHeaderTypes.media:
                        header = {
                            type: 'media',
                            media: {
                                type: 'none'
                            }
                        };

                        switch (template.headerMediaType) {
                            case HSMTemplateHeaderMediaTypes.document:
                                header.media.type = 'document';
                                break;
                            case HSMTemplateHeaderMediaTypes.image:
                                header.media.type = 'image';
                                break;
                            case HSMTemplateHeaderMediaTypes.video:
                                header.media.type = 'video';
                                break;
                            default:
                                break;
                        }
                        if (HSMTemplateHeaderTypes.media) {
                            header.media[header.media.type] = {
                                filename: null,
                                url: template.headerMediaUrl,
                                isPublic: null,
                            };
                        }
                        break;
                }
                vm.answer.templateHeader = header;
            }
            else {
                vm.answer.templateHeader = null;
            }

            //parametersbuild
            if (template.parameters !== null && template.parameters.length > 0) {
                vm.answer.templateParameters = [];
                vm.templateParameters = [];
                for (let i = 0; i < template.parameters.length; i++) {
                    vm.answer.templateParameters.push(null);
                    var parameterName = template.parameters[i].substr(0, template.parameters[i].indexOf('='));
                    var parameterDescription = template.parameters[i].substr(template.parameters[i].indexOf('=') + 1);
                    vm.templateParameters.push({
                        name: '{{' + parameterName + '}}',
                        description: parameterDescription
                    });
                }
            }
            else {
                vm.answer.templateParameters = null;
                vm.templateParameters = null;
            }

            //Buttons Build
            if (typeof (template.buttonsType) === 'number') {
                let buttons = [];
                vm.answer.templateButtons = null;
                vm.templateButtons = null;
                vm.answer.templateButtonsParameters = null;
                if (template.buttonsType != HSMTemplateButtonsTypes.none && 
                    template.buttons.length > 0) {
                    vm.answer.templateButtonsParameters = [];
                    for (var j = 0; j < template.buttons.length; j++) {
                        let button;
                        let templateButton = template.buttons[j];
                        switch (template.buttonsType) {
                            case HSMTemplateButtonsTypes.quickReply:
                                button = buildQuickReply(templateButton);
                            break;

                            case HSMTemplateButtonsTypes.callToAction:
                                button = buildCallToAction(templateButton);
                            break;
                            
                            case HSMTemplateButtonsTypes.mixed:
                                button = templateButton.quickReplyParameter != null ? buildQuickReply(templateButton) : buildCallToAction(templateButton);
                            break;

                            case HSMTemplateButtonsTypes.authCode:
                                button = buildAuth(templateButton);
                            break;
                        }
                        button.index = j;
                        buttons.push(button);
                        vm.answer.templateButtons = buttons;
                    }
                }
            }
        }

        function buildCallToAction(templateButton){
            const button = {};
            switch (templateButton.callToActionButtonType) {
                case HSMTemplateCallToActionButtonTypes.url:
                    button.type = 'url';
                    if (templateButton.urlButtonType === HSMTemplateCallToActionUrlButtonTypes.fixed) {
                        button.sub_type = 'fixed';
                    }
                    else {
                        button.sub_type = 'dynamic';
                        button.parameter = {};
                        button.parameter[templateButton.urlParameter.name] = null;  
                    }
                break;
                case HSMTemplateCallToActionButtonTypes.call:
                    button.type = 'call';
                break;
                case HSMTemplateCallToActionButtonTypes.offer:
                    button.type = 'offer';
                    button.parameter = {};
                    button.parameter[templateButton.offerCodeParameter.name] = null;
                break;
            }
            return button;
        }

         function buildAuth(templateButton){
            const button = {};
            button.type = 'url';
            button.parameter = {};
            button.parameter[templateButton.authCodeParameter.name] = null;
            return button;
        }

        function buildQuickReply(templateButton) {
            const button = {};
            button.type = 'quick_reply';
            button.payload = null;
            button.parameter = {};
            button.parameter[templateButton.quickReplyParameter.name] = null;
            return button;
        }

        function templateParameterChanged() {
            let template = vm.selectedSocialCase.data.service.settings.hSMTemplates[vm.answer.templateIndex];
            let text = template.template;
            for (let i = 0; i < template.parameters.length; i++) {
                let parameter = template.parameters[i];
                let parameterName = parameter.substr(0, parameter.indexOf('='));

                let value = vm.answer.templateParameters[i];
                if (value !== null && value.length > 0) {
                    text = text.replace('{{' + parameterName + '}}', value);
                }
            }

            vm.answer.templateWithParameters = $sce.trustAsHtml(text.replaceAll('\n', '<br />'));
        }

        function templateHeaderChanged() {
            if (vm.answer.template.headerType === HSMTemplateHeaderTypes.text) {
                let text = vm.answer.template.headerText;
                let value = vm.headerInputText;
                let parameterName = vm.answer.template.headerTextParameter.name;
                if (value !== null && value.length > 0) {
                    text = text.replace('{{' + parameterName + '}}', value);
                }
                vm.answer.headerWithParameters = $sce.trustAsHtml(text.replaceAll('\n', '<br />'));
                vm.answer.templateHeader.text.parameter.value = value;
            }
        }

        function addButtonsParameters(buttonIndex) {
            const templateButton = vm.answer.templateButtons[buttonIndex];
            let value = vm.answer.templateButtonsParameters[buttonIndex];
            const tempButton = vm.answer.template.buttons[templateButton.index];

            switch (templateButton.type) {
                case 'quick_reply':
                    templateButton.parameter[tempButton.quickReplyParameter.name] = value;
                    break;
                case 'url':
                    templateButton.parameter[tempButton.urlParameter.name] = value;
                    break;
                case 'offer':
                    templateButton.parameter[tempButton.offerCodeParameter.name] = value;
                    break;
                case 'authCode':
                    templateButton.parameter[tempButton.authCodeParameter.name] = value;
                    break;
            }
        }

        function attachFiles() {
            var files = vm.principalQueue;
            modalSocialService.showFileUploader(files, vm.selectedSocialCase)
                .then(function (modal) {
                    modal.element.modal({
                        backdrop: 'static',
                        keyboard: false
                    });

                    modal.element.on('hide.bs.modal', function (e) {
                        if (modalSocialService.blockModalHiding) {
                            e.preventDefault();
                        }
                    });

                    modal.close.then(function (result) {
                        if (typeof (result) !== 'undefined' &&
                            result != null) {
                            vm.principalQueue = result.queue;
                            addSelectedAttachments(result.queue);
                            if (result.queue.length === 0) {
                                vm.attachments = [];
                            }
                        }
                    });
                });
        }

        function addSelectedAttachments(queue) {
            vm.attachments = [];
            //Loop through uploads
            angular.forEach(queue, function (upload) {
                var attachment = {};

                attachment.MimeType = upload._file.type;
                attachment.FileName = upload._file.name;
                attachment.FileSize = upload._file.size;

                //Base64 encode attachment
                var reader = new FileReader();
                reader.readAsDataURL(upload._file);
                reader.onload = function (event) {
                    //Build content
                    attachment.Data = event.target.result.substring(event.target.result.indexOf(',') + 1, event.target.result.length);
                    // Push attachment
                    vm.attachments.push(attachment);
                };

            });
        }

        // --> Chequeo el mensaje y lo respondo
        function answerMessage(closeCase) {
            if (vm.answer.templateIndex === null) {
                toastr.error($translate.instant('TEMPLATE_NOT_SELECTED'));
                return false;
            }

            var template = vm.selectedSocialCase.data.service.settings.hSMTemplates[vm.answer.templateIndex];


            if (template.parameters !== null && template.parameters.length > 0) {
                for (let i = 0; i < template.parameters.length; i++) {
                    let parameterName = template.parameters[i].substr(0, template.parameters[i].indexOf('='));
                    parameterName = '{{' + parameterName + '}}';
                    let parameterDescription = template.parameters[i].substr(template.parameters[i].indexOf('=') + 1);

                    if (typeof (vm.answer.templateParameters[i]) !== 'string' ||
                        vm.answer.templateParameters[i] === null ||
                        vm.answer.templateParameters[i].length === 0) {
                        toastr.error($translate.instant('MUST_FILL_PARAMETER', {
                            param_desc: parameterDescription,
                            param_name: parameterName
                        }));
                        return false;
                    }

                    if (vm.answer.templateParameters[i].indexOf('    ') >= 0 ||
                        vm.answer.templateParameters[i].indexOf('\t') >= 0 ||
                        vm.answer.templateParameters[i].indexOf('\n') >= 0) {
                        toastr.error($translate.instant('MUST_FILL_PARAMETER_VALID', {
                            param_desc: parameterDescription,
                            param_name: parameterName
                        }));
                        return false;
                    }
                }
            }
            else {
                vm.answer.templateParameters = null;
            }

            //Valido Header
            if (typeof (vm.answer.templateHeader) === 'object') {
                var header = vm.answer.templateHeader;
                switch (header.type) {
                    case 'text':
                        if (typeof (vm.answer.template.headerTextParameter) === 'string') {
                            let value = vm.answer.templateHeader.text.parameter.value;
                            if (typeof (header.text.parameter.value) !== 'string' ||
                                header.text.parameter.value.length === 0) {
                            }
                            if (header.text.parameter.value.indexOf('    ') >= 0 ||
                                header.text.parameter.value.indexOf('\t') >= 0 ||
                                header.text.parameter.value.indexOf('\n') >= 0) {
                            }
                            if (value === null || value.trim().length === 0) {
                                toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_HEADER', {parameter: header.text.parameter.name}));
                                return false;
                            }
                        }
                        break;
                    case 'media':
                        switch (vm.mediaInputSelect) {
                            case 'url':
                                if (header.media[header.media.type].filename === null || header.media[header.media.type].filename.length === 0) {
                                    toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_FILENAME-HEADER'));
                                    return false;
                                }
                                if (header.media[header.media.type].url === null || header.media[header.media.type].url.length === 0) {
                                    toastr.error($translate.instant('OUTGOINGMESSAGE_WHATSAPP_INVALID_URL-HEADER'));
                                    return false;
                                }
                                vm.answer.templateHeader.media[header.media.type].isPublic = vm.isPublicUrl;
                                break;
                            case 'file':
                                if (!Array.isArray(vm.attachments)) {
                                    toastr.error($translate.instant('MUST_SELECT_FILE'));
                                    return false;
                                }

                                switch (header.media.type) {
                                    case 'document':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'application/pdf') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_DOCUMENT'));
                                            return false;
                                        }
                                        break;
                                    case 'image':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'image/jpeg' &&
                                            vm.attachments[0].MimeType.toLowerCase() !== 'image/jpg' &&
                                            vm.attachments[0].MimeType.toLowerCase() !== 'image/png') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_IMAGE'));
                                            return false;
                                        }
                                        break;
                                    case 'video':
                                        if (vm.attachments[0].MimeType.toLowerCase() !== 'video/mp4') {
                                            toastr.error($translate.instant('WHATSAPP_INVALID_FORMAT_VIDEO'));
                                            return false;
                                        }
                                        break;
                                    default:
                                        break;
                                }

                                header.media[header.media.type] = {
                                    filename: vm.attachments[0].FileName,
                                    file: {
                                        mimeType: vm.attachments[0].MimeType,
                                        data: vm.attachments[0].Data
                                    }
                                };

                                break;
                        }
                        break;
                }
            }

            if (Array.isArray(vm.answer.templateButtons)) {
                for (let i = 0; i < vm.answer.templateButtons.length; i++) {
                    const templateButton = vm.answer.templateButtons[i];
                    const tempButton = vm.answer.template.buttons[templateButton.index];
                    let mustToValidate = false;
                    let value;

                    switch (templateButton.type) {
                        case 'quick_reply':
                            value = templateButton.parameter[tempButton.quickReplyParameter.name];
                            mustToValidate = true;
                            break;
                        case 'url':
                            if (templateButton.sub_type === 'dynamic') {
                                value = templateButton.parameter[tempButton.urlParameter.name];
                                mustToValidate = true;
                            }
                            break;
                        case 'offer':
                            value = templateButton.parameter[tempButton.offerCodeParameter.name];
                            mustToValidate = true;
                            break;
                    }

                    if (mustToValidate) {

                        if (value === null || value.trim().length === 0) {
                            toastr.error($translate.instant('WHATSAPP_INSERT_VALUE_FOR_BUTTON') + tempButton.text);
                            return false;
                        }

                        if (templateButton.type == 'offer' &&
                            (value.trim().length > 15 || value.indexOf(" ") !== -1)) {
                            toastr.error($translate.instant('WHATSAPP_INSERT_VALUE_FOR_BUTTON_TYPE_OFFER', { buttonName: value }));
                            return false;
                        }
                    } 
                }
            }

            vm.answer.options = {
                chkCloseCase: closeCase,
                templateData: vm.answer.templateParameters,
                templateParameters: {
                    header: vm.answer.templateHeader,
                    buttons: vm.answer.templateButtons
                },
                temaplateButtons: vm.answer.templateButtons,
                templateName: template.elementName,
                templateNamespace: template.namespace,
                templateLanguage: template.language,
            };

            answerAction();
        }

        // --> Lógica de respuesta del mensaje de whatsapp
        function answerAction() {
            vm.answer.actions.messageOptionsAreValid();
            if (!vm.answer.options.validationMessages || vm.answer.options.validationMessages.length === 0) {
                vm.answer.principal = null;

                var answerWithOptions = {
                    data: vm.answer,
                    options: vm.answer.options
                };

                //vm.answer.options.serviceId = answerWithOptions.data.service.id;

                if (isOutgoing) {
                    sendWhatsappOutgoing(answerWithOptions);
                }
                else {
                    if (isMyOutgoingCases) {
                        if(typeof(vm.socialCase.data.socialUser) !== 'undefined'){
                            answerWithOptions.socialUser = vm.socialCase.data.socialUser;
                            answerWithOptions.options.phoneNumber = vm.socialCase.data.socialUser.id.toString();
                        }
                        else{
                            var lastIndexMessage = vm.socialCase.data.case.messages.length - 1;
                            var socialUser = vm.socialCase.data.case.messages[lastIndexMessage].socialUser;
                            answerWithOptions.socialUser = socialUser;
                            answerWithOptions.options.phoneNumber = socialUser.id.toString();
                        }


                        answerWithOptions.case = vm.socialCase.data.case;
                        answerWithOptions.socialServiceType = vm.socialCase.data.socialServiceType;
                        answerWithOptions.service = vm.socialCase.data.service;
                        commonActionsService.addMessage(answerWithOptions);
                    }
                    else {
                        replyMessage(answerWithOptions);
                    }
                }
            }
            else {
                toastr.error(vm.answer.options.validationMessages[0]);
            }
        }

        function sendWhatsappOutgoing(answerWithOptions) {
            commonActionsService.sendSocialNetworkMessage(answerWithOptions, SocialServiceTypes.WhatsApp);
        }

        function replyMessage(answerWithOptions) {
            commonActionsService.answerMessage(answerWithOptions);
        }

        // --> Cancelo el panel de respuesta de mensaje de whatsapp
        function cancelAnswer() {
            if (typeof(vm.socialCase.isMyOutgoingCases) !== 'undefined' && vm.socialCase.isMyOutgoingCases){
                vm.socialCase.data.actualData.answer.isAnsweringWithTemplate = false;
                vm.socialCase.data.actualData.answer.wasAnsweringWithTemplate = false;
            }
            else {
                messageOriginal.message.actualData.answer.isAnsweringWithTemplate = false;
                messageOriginal.message.actualData.answer.wasAnsweringWithTemplate = false;
            }
        }

        function removeBlackSpaces(text) {
            if (typeof(text) !== 'string' || text === null)
                return '';

            text = text.trim();
            return text;
        }

        function isPrivateAndCanAttach() {
            return vm.whatsappConfiguration.supportsAttachments;
        }
    }
})();
