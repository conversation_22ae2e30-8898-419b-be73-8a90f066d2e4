﻿<%@ Page Async="true" Title="" Language="C#" MasterPageFile="~/Master.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="ServicesFacebookMessenger.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.ServicesFacebookMessenger" %>

<%@ Register TagPrefix="cc1" Namespace="Yoizen.Web.UI" Assembly="Yoizen.Web.UI" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.colorpicker.css")%>' rel="stylesheet" type="text/css" />

    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.numeric.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/URI.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.getUrlParam.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.filedrop.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesCommon.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesFacebookMessenger.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/dragula.min.js")%>'></script>
    <style type="text/css">
		.uiInfoTable .label { width: 150px !important; }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<asp:Image runat="server" ImageUrl="~/Images/FacebookMessenger.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-servicesfacebookmessenger-title">Facebook Messenger</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
    <div style="display: none">
        <div id="divServices" class="seccion">
            <div class="title">
                <h2 data-i18n="configuration-servicesfacebookmessenger-copy_attributes">Copiar atributos</h2>
            </div>
            <div class="contents">
                <yoizen:Message runat="server" Type="Information" Text="Seleccione el servicio de Facebook Messenger del cual se desea copiar los atributos" Small="true" LocalizationKey="configuration-servicesfacebookmessenger-select_service" />
                <div id="divAllServices" style="max-height: 300px; overflow-y: auto; overflow-x: auto; max-width: 100%">
                    <table id="tableAllServices" class="reporte" cellspacing="0" rules="all" border="1" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr class="header">
                                <th style="width: 20px;" scope="col">&nbsp;</th>
                                <th scope="col"><span data-i18n="configuration-services-service">Servicio</span></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="buttons">
                    <label class="uiButton uiButtonLarge uiButtonConfirm">
                        <button type="button" data-i18n="globals-accept" id="buttonCopyAnswerDialogConfirm" onclick="CopyDialogConfirmCommon()">Aceptar</button>
                    </label>
                    <label class="uiButton uiButtonLarge">
                        <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                    </label>
                </div>
            </div>
        </div>
        <div id="divFacebookWizardLoading">
            <div class="seccion">
                <div class="title">
					<h2><asp:Image runat="server" ImageUrl="~/Images/Icons/FacebookMessenger.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-servicesfacebookmessenger-subtitle">Asistente de configuración de Facebook Messenger</span></h2>
                </div>
                <div class="contents">
                    <table id="tableFacebookWizardLoading" width="100%" border="0">
                        <tr>
                            <td align="center"><span data-i18n="globals-loading">Cargando...</span></td>
                        </tr>
                        <tr>
                            <td align="center">
                                <i class="fa fa-3x fa-spinner fa-pulse"></i>
                            </td>
                        </tr>
                    </table>
					<yoizen:Message ID="messageFacebookWizardLoadingError" runat="server" ClientIDMode="Static" Type="Error"/>
                </div>
            </div>
        </div>
        <div id="divFacebookWizard">
            <div class="seccion">
                <div class="title">
					<h2><asp:Image runat="server" ImageUrl="~/Images/Icons/FacebookMessenger.png" ImageAlign="AbsMiddle" /> <span data-i18n="configuration-servicesfacebookmessenger-subtitle">Asistente de configuración de Facebook Messenger</span></h2>
                </div>
                <div class="contents">
                    <div class="facebook-login-button">
                        <div>
                            <div>
                                <div style="display: inline-block">
                                    <div class="facebook-login-button-container" role="button">
                                        <table class="" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 216 216" color="#FFFFFF">
                                                                    <path fill="#FFFFFF" d="M204.1 0H11.9C5.3 0 0 5.3 0 11.9v192.2c0 6.6 5.3 11.9 11.9
																		11.9h103.5v-83.6H87.2V99.8h28.1v-24c0-27.9 17-43.1 41.9-43.1
																		11.9 0 22.2.9 25.2 1.3v29.2h-17.3c-13.5 0-16.2 6.4-16.2
																		15.9v20.8h32.3l-4.2 32.6h-28V216h55c6.6 0 11.9-5.3
																		11.9-11.9V11.9C216 5.3 210.7 0 204.1 0z"></path>
                                                                </svg>
                                                                <img src="https://www.facebook.com/rsrc.php/v3/yy/r/uPlIYLfynqH.png" alt="app-facebook" width="16" height="16" />
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text"><span data-i18n="configuration-servicesfacebookmessenger-login">Iniciar sesión con Facebook</span></div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="divFacebookUser" class="subseccion" style="display: none; margin-top: 10px;">
                        <div class="title">
                            <h2 data-i18n="configuration-servicesfacebookmessenger-facebook_user-title">Usuario de Facebook logueado</h2>
                        </div>
                        <div class="contents">
                            <div class="facebook-user">
								<div class="facebook-user-avatar"><img /></div>
                                <div class="facebook-user-name"></div>
                            </div>
                            <div id="divFacebookPage" class="subsubseccion" style="display: none; margin-top: 10px;">
                                <div class="title">
                                    <h2 data-i18n="configuration-servicesfacebookmessenger-facebook_page-title">Seleccione la página a utilizar</h2>
                                </div>
                                <div class="contents">
                                    <div id="divFacebookPages" class="facebook-pages">
                                    </div>
                                    <div class="buttons" id="divButtonsLoadMore">
                                        <label class="uiButton">
                                            <button type="button" data-i18n="configuration-servicesfacebookmessenger-load_more_accounts" id="buttonLoadMoreAccounts">Obtener más cuentas</button>
                                        </label>
                                    </div>
                                </div>
                            </div>
							<yoizen:Message ID="messageFacebookNoPages" runat="server" ClientIDMode="Static" Type="Error" style="display: none; margin-top: 10px" LocalizationKey="configuration-servicesfacebookmessenger-no_facebook_pages">
								El usuario no es dueño de ninguna página
                            </yoizen:Message>
                        </div>
                    </div>
                    <div id="divFacebookRedirectUri" class="subseccion" style="display: none; margin-top: 10px;">
                        <div class="title">
                            <h2 data-i18n="configuration-servicesfacebookmessenger-auth_url">URL de autorización de Facebook</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-auth_url">URL del autorización de Facebook</span>:</th>
                                    <td class="data">
                                        <input id="inputFacebookUrl" type="text" style="width: 400px;" autocomplete="off" class="inputtext" />
                                    </td>
                                </tr>
                            </table>
                            <div class="buttons">
                                <label class="uiButton">
                                    <button type="button" data-i18n="configuration-servicesfacebookmessenger-revalidate_url" onclick="RevalidateFacebookUrl()">Revalidar URL</button>
                                </label>
                            </div>
                        </div>
                    </div>
					<yoizen:Message ID="messageFacebookUrlInvalid" runat="server" ClientIDMode="Static" Type="Error" style="display: none"/>
					<yoizen:Message ID="messageFacebookAccountError" runat="server" ClientIDMode="Static" Type="Error" style="display: none; margin-top: 10px;" LocalizationKey="configuration-servicesfacebookmessenger-account_error">
						No se pudo obtener información de la cuenta de facebook
                    </yoizen:Message>
					<yoizen:Message ID="messageFacebookCouldntValidateAccessToken" runat="server" ClientIDMode="Static" Type="Error" style="display: none; margin-top: 10px;" LocalizationKey="configuration-servicesfacebookmessenger-couldnt_validate_access_token">
						No se pudo validar el access token
                    </yoizen:Message>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge" style="float: left" id="buttonEnterFacebookUrl">
                            <button type="button" data-i18n="configuration-servicesfacebookmessenger-enter_url" onclick="ShowFacebookUrlInput()">Ingresar URL de Facebook a mano</button>
                        </label>
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divAnotherFacebookMessengerServiceExists">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-subtitle">Asistente de configuración de Facebook</h2>
                </div>
                <div class="contents">
                    <yoizen:Message ID="messageAlreadyExistsAnotherFacebookMessengerService" runat="server" Type="Error" ClientIDMode="Static" LocalizationKey="configuration-servicesfacebookmessenger-already_exists_another_service">
						Ya existe otro servicio de Facebook Messenger habilitado para la página especificada. Por favor seleccione otra página o
						deshabilite el otro servicio
                    </yoizen:Message>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" data-i18n="globals-close" onclick="$.colorbox.close()">Cerrar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divAnotherFacebookServiceExists">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-subtitle">Asistente de configuración de Facebook</h2>
                </div>
                <div class="contents">
                    <yoizen:Message ID="messageAnotherFacebookServiceExists" runat="server" Type="Error" ClientIDMode="Static" LocalizationKey="[html]configuration-servicesfacebookmessenger-another_service_exist-message">
						Ya existe otro servicio de Facebook (<span rel="servicename"></span>) habilitado para la página especificada que procesa mensajes privados.<br /> 
						Seleccione si desea deshabilitar la mensajería privada del otro servicio o si cancela la actual edición y modifica los datos de la página.
                    </yoizen:Message>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge">
                            <button type="button" data-i18n="configuration-servicesfacebookmessenger-save_and_disable" id="buttonSaveAndDisable" onclick="SaveAndDisable()">Grabar y deshabilitar mensajería privada en el otro servicio</button>
                        </label>
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="tabsFacebookMessenger">
        <ul>
            <li>
                <a href="#divBasicConfiguration" data-i18n="configuration-servicesfacebookmessenger-settings">Configuración básica</a>
            </li>
            <li id="liTabAdvancedConfiguration" style="display: none">
                <a href="#divAdvancedConfiguration" data-i18n="configuration-servicesfacebookmessenger-advanced_settings">Configuración avanzada</a>
            </li>
            <li id="liTabAdvancedConfigurationYFlow" runat="server" style="display: none" clientidmode="static">
				<a href="#divAdvancedConfigurationYFlow"><span  data-i18n="configuration-servicesfacebookmessenger-advanced_settings">Configuración avanzada</span> <span class="yzn-yFlowISO"></span></a>
            </li>
            <li id="liTabBehaviour">
                <a href="#divBehaviour" data-i18n="configuration-servicesfacebookmessenger-behaviour">Comportamiento</a>
            </li>
            <li class="hiddenAsGateway">
                <a href="#divFacebookServiceMedia" data-i18n="configuration-servicesfacebookmessenger-atached_files">Archivos adjuntos</a>
            </li>
            <li id="liTabCases">
                <a href="#divCases" data-i18n="configuration-systemsettings-cases">Casos</a>
            </li>
            <li>
                <a href="#divNotifications" data-i18n="configuration-servicesfacebookmessenger-mail_notifications">Notificaciones por email</a>
            </li>
			<li id="liTabVideo" runat="server" visible="false">
				<a href="#divVideo" data-i18n="configuration-serviceswhatsapp-video">Video</a>
			</li>
        </ul>
        <div id="divBasicConfiguration">
            <yoizen:Message runat="server" Type="Information" Small="true">
				<span data-i18n="configuration-servicesfacebookmessenger-configuration_assistant">Usted puede optar por utilizar el asistente de configuración. Para ello haga clic</span> <a href="javascript:ConfigureFacebook()" data-i18n="globals-here">aquí</a>.
            </yoizen:Message>
            <asp:HiddenField ID="hiddenUserAccessToken" runat="server" ClientIDMode="Static"></asp:HiddenField>
            <asp:HiddenField ID="hiddenServiceIDToDisable" runat="server" ClientIDMode="Static"></asp:HiddenField>
            <table width="100%" border="0" class="uiInfoTable noBorder">
                <tr class="dataRow dataRowSeparator">
                    <th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesfacebookmessenger-service_name">Nombre de servicio</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxServiceName" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" MaxLength="50" autotrim="true" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxServiceName" />
                        <asp:CustomValidator ID="customvalidatorServiceName" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceName" OnServerValidate="customvalidatorServiceName_ServerValidate" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-page_code">Código de Página</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxFacebookPageId" runat="server" Width="200" ClientIDMode="Static" autocomplete="off" spellcheck="false" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookPageId" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-page_name">Nombre de Página</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxFacebookPageName" runat="server" Width="200" autocomplete="off" spellcheck="false" ClientIDMode="Static" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookPageName" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-page_access_token">Page Access Token</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxFacebookPageAccessToken" runat="server" Width="90%" autocomplete="off" spellcheck="false" ClientIDMode="Static" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookPageAccessToken" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator" style="display: none">
                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-check_acces_token">Verificar Access Token antes de cada acción</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs checkbox">
                                        <asp:CheckBox ID="checkboxVerifyAccessToken" runat="server" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-check_acces_token-tip">
										Este parámetro indica si antes de realizar cualquier invocación a Facebook se validará el Access Token de la página
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:TextBox ID="textboxFacebookFromDate" runat="server" Width="120" ClientIDMode="Static" />
                                        <asp:CustomValidator runat="server" ControlToValidate="textboxFacebookFromDate" EnableClientScript="true" ClientValidationFunction="ValidateDateField" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-from_date-tip">
										Especifica la fecha a partir de la cual se obtendrán los mensajes
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-default_queue">Cola por defecto</span>:</th>
                    <td class="data">
                        <asp:DropDownList ID="dropdownlistFacebookQueue" runat="server" DataTextField="Name" DataValueField="ID" />
                    </td>
                </tr>
                <asp:PlaceHolder ID="placeholderFacebookCheckSpelling" runat="server">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-spell_check">Revisión ortográfica obligatoria</span>:</th>
                        <td class="data">
                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <td class="vMid prs checkbox">
                                            <asp:CheckBox ID="checkboxFacebookCheckSpelling" runat="server" />
                                        </td>
										<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-spell_check-tip">
											Este parámetro indica si antes de enviar el mensaje es obligatoria la revisión ortográfica
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </asp:PlaceHolder>
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription" style="width: 310px !important"><span data-i18n="configuration-servicesfacebook-chat_plugin-auto_reply">Responder automáticamente al inicio de una conversación del Plugin de chat</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:CheckBox ID="checkboxAutoReplyToChatPluingGetStarted" runat="server" ClientIDMode="Static" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebook-chat_plugin-auto_reply-tip">
										Este parámetro indica si el servicio responderá en forma automática a los inicios de conversación que se dan cuando se utiliza el Plugin de chat de Messenger
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="subseccion" id="divAutoReplyToChatPluingGetStarted">
                            <div class="title">
                                <h2 data-i18n="configuration-servicesfacebook-chat_plugin-auto_reply-title">Respuesta automática</h2>
                            </div>
                            <div class="contents">
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label withdescription" style="width: 80px !important"><span data-i18n="configuration-servicesfacebook-chat_plugin-auto_reply-text">Texto</span>:</th>
                                        <td class="data">
                                            <asp:TextBox ID="textboxAutoReplyToChatPluingGetStartedText" runat="server" ClientIDMode="Static" TextMode="MultiLine" Rows="3" Width="100%" />
                                        </td>
                                    </tr>
                                </table>
                                <div class="validationerror" style="display: none">
                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyToChatPluingGetStartedText" SkinID="validationerror" data-i18n="configuration-servicesfacebook-chat_plugin-auto_reply-text-error" />
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <asp:PlaceHolder ID="placeholderAllowYFlow" runat="server">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesfacebookmessenger-associate">Asociar a</span> <span class="yzn-yFlowISO"></span>:</th>
                        <td class="data">
                            <asp:DropDownList ID="dropdownlistUseYFlow" runat="server" ClientIDMode="Static">
                                <asp:ListItem Value="false" Text="No asociar a yFlow" Selected="True" data-i18n="configuration-servicesfacebookmessenger-donot_associate_yFlow" />
                                <asp:ListItem Value="true" Text="Asociar a yFlow" data-i18n="configuration-servicesfacebookmessenger-associate_yFlow" />
                            </asp:DropDownList>
                            <yoizen:Message runat="server" Type="Information" Small="true" Style="display: none; margin-top: 10px" LocalizationKey="configuration-servicesfacebookmessenger-associate_yFlow-tip">
								Si el servicio se asocia a yFlow algunas configuraciones y herramientas del sistema no estarán disponibles, como por ejemplo
								la utilización de Filtros.
                            </yoizen:Message>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Debe completar los datos de yFlow en la solapa de configuración avanzada"
									EnableClientScript="true" ClientValidationFunction="ValidateYFlow" data-i18n="configuration-servicesfacebookmessenger-associate_yFlow-error"/>
                            </div>
                        </td>
                    </tr>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderActAsChat" runat="server">
                    <tr class="dataRow dataRowSeparator">
						<th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesapplemessaging-act_as_chat">Utilizar el canal como un chat</span>:</th>
                        <td class="data">
                            <asp:CheckBox ID="checkboxActAsChat" runat="server" ClientIDMode="Static" />
                        </td>
                    </tr>
                </asp:PlaceHolder>
            </table>
			<yoizen:Message ID="messageFacebookTokenExpiry" runat="server" Type="Warning" style="display: none; margin-top: 5px">
				<span data-i18n="configuration-servicesfacebookmessenger-token_expiry">El access token expira el</span> <span rel="expires"></span>
            </yoizen:Message>
        </div>
        <div id="divAdvancedConfiguration" style="display: none">
            <table width="95%" border="0" class="uiInfoTable noBorder">
                <tr class="dataRow dataRowSeparator">
                    <th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesfacebookmessenger-welcome">Mensaje de bienvenida</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxWelcomeMessage" runat="server" MaxLength="160" Width="80%" autocomplete="off" ClientIDMode="Static" />
                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWelcomeMessage" />
                        <yoizen:Message ID="messageWelcomeMessage" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
							<span data-i18n="configuration-servicesfacebookmessenger-welcome_message">Indica el mensaje de bienvenida que mostrará la conversación privada la primera vez que el usuario inicie la conversación.</span>
							<span data-i18n="configuration-servicesfacebookmessenger-welcome_message-fields">Puede utilizar variables para personalizar el mensaje</span>:
							<ul>
								<li><span class='templatefieldname'>@@user_first_name@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-welcome_message-field-user_name">Indica el primer nombre del usuario de facebook</span></li>
								<li><span class='templatefieldname'>@@user_last_name@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-welcome_message-field-surname">Indica el apellido del usuario de facebook</span></li>
								<li><span class='templatefieldname'>@@user_full_name@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-welcome_message-field-name">Indica el nombre completo del usuario de facebook</span></li>
							</ul>
                        </yoizen:Message>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label" style="width: 200px !important"><span data-i18n="[html]configuration-servicesfacebookmessenger-start_button">Utilizar botón de <span class="bold">Empezar</span>:</span></th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs timeinfo">
                                        <asp:CheckBox ID="checkboxUseGetStartedButton" runat="server" ClientIDMode="Static" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-start_button-tip">
										Indica si se le presentará al usuario al iniciar la conversación un botón de "Empezar"
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divAdvancedConfigurationYFlow" runat="server" style="display: none" clientidmode="static">
            <div class="seccion hiddenAsGateway">
                <div class="title">
                    <h2 data-i18n="configuration-services-yFlow">yFlow</h2>
                </div>
                <div class="contents">
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-services-common-yflow-flow">Flujo</span>:</th>
                            <td class="data">
                                <asp:HiddenField ID="hiddenFlow" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <asp:HiddenField ID="hiddenSurveys" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <select id="selectFlowToUse"></select>
                                <a id="anchorFlowsReload">
                                    <span class="fa fa-lg fa-sync" title="Refrescar" data-i18n-title="configuration-services-common-yflow-refresh"></span>
                                </a>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-code">Código</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowID"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-name">Nombre</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowName"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-version">Versión</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowVersion"></span>
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
				        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSelectFlowToUse" data-i18n="configuration-serviceschat-configuration-error" />
                    </div>
                </div>
                <div class="seccion hiddenAsGateway" id="divYFlowContingency" runat="server">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-Contingency-title">yFlow contingencia</h2>
                    </div>
                    <div class="contents">
                        <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-services-common-yflow-flow">Flujo</span>:</th>
                            <td class="data">
                                <asp:HiddenField ID="hiddenFlowContingency" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <asp:HiddenField ID="hiddenSurveysContingency" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <select id="selectFlowContingencyToUse"></select>
                                <a id="anchorFlowsContingencyReload">
				                    <span class="fa fa-lg fa-sync" title="Refrescar" data-i18n-title="configuration-services-common-yflow-refresh"></span>
                                </a>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-code">Código</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowContingencyID"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-name">Nombre</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowContingencyName"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-version">Versión</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowContingencyVersion"></span>
                            </td>
                        </tr>
                        </table>
                    </div>
                </div>
                <div class="seccion hiddenAsGateway">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-flow_queue_transfers-title">Mapeo para transferencia a distintas colas</h2>
                    </div>
                    <div class="contents">
                        <asp:HiddenField ID="hiddenFlowQueueTransfersByKey" runat="server" ClientIDMode="Static"></asp:HiddenField>
                        <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-services-common-yflow-flow_queue_transfers">
						    Este mapeo permite configurar distintas claves que se configuran en la pieza de yFlow que permite derivar el chat a agente
						    a distintas colas. En caso de que no se encuentre ningún mapeo se utilizará la cola por defecto del servicio
                        </yoizen:Message>
                        <table id="tableFlowQueueTransfersByKey" class="reporte" cellspacing="0" rules="all" border="1">
                            <thead>
                                <tr class="header">
                                    <th style="width: 20px"></th>
                                    <th scope="col"><span data-i18n="configuration-services-common-yflow-flow_queue_transfers-key">Clave</span></th>
                                    <th scope="col"><span data-i18n="configuration-services-common-yflow-flow_queue_transfers-queue">Cola</span></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                            <tfoot>
                                <tr>
                                    <td style="text-align: center"><a id="anchorFlowQueueTransfersByKeyAdd"><span class="fa fa-lg fa-plus-square"></span></a></td>
                                    <td colspan="5"></td>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="validationerror" style="display: none">
                            <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateFlowQueueTransfersByKey" data-i18n="configuration-services-common-yflow-configuration-error" />
                        </div>
                    </div>
                </div>
                <div class="seccion hiddenAsGateway">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-more_configuration-title">Otras configuraciones</h2>
                    </div>
                    <div class="contents">
                        <table width="95%" border="0" class="uiInfoTable noBorder">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 250px !important"><span data-i18n="configuration-services-common-yflow-share_enqueued_messages">Compartir mensajes encolados de las siguientes colas</span>:</th>
                                <td class="data">
                                    <asp:ListBox ID="listboxFlowShareEnqueuedMessagesFromQueues" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-share_connected_agents">Compartir agentes conectados de las siguientes colas</span>:</th>
                                <td class="data">
                                    <asp:ListBox ID="listboxFlowShareConnectedAgentsFromQueues" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label withdescription"><span data-i18n="configuration-services-common-yflow-minutes_to_call_yflow">Minutos para no invocar a yFlow luego de caso cerrado por agente</span>:</th>
                                <td class="data">
                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="vMid prs">
												    <asp:TextBox ID="textboxFlowMinutesAfterAgentClosedCase" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" />
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFlowMinutesAfterAgentClosedCase" />
                                                    <div class="timeinfo" related="textboxFlowMinutesAfterAgentClosedCase"></div>
                                                </td>
											    <td class="vMid pls" data-i18n="configuration-services-common-yflow-minutes_to_call_yflow-tip">
												    Este parámetro indica cuantos minutos se considerarán, luego de un caso cerrado por Agente, para encolar el
												    mensaje proveniente del mismo usuario y evitar invocar primero a yFlow.
												    Especifique cero para indicar siempre invocar a yFlow independientemente del tiempo transcurrido desde que se
												    cerraron los casos.
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <asp:PlaceHolder ID="placeholderAllowAgentsToReturnMessagesToYFlow" runat="server">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-services-common-yflow-allow_agents_to_return_messages">Permitir a agente devolver un mensaje a yFlow</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxAllowAgentsToReturnMessagesToYFlow" runat="server" />
                                    </td>
                                </tr>
                            </asp:PlaceHolder>
                        </table>
                    </div>
                </div>
                <asp:Panel ID="panelYFlowSurveys" runat="server" CssClass="seccion" Visible="false">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-surveys-title">Encuestas</h2>
                    </div>
                    <div class="contents">
                        <yoizen:Message ID="messageNoSurveys" runat="server" Type="Warning" Visible="false" LocalizationKey="configuration-services-common-yflow-surveys-no_surveys">
						    No existen encuestas creadas y habilitadas
                        </yoizen:Message>
                        <asp:Panel ID="panelEnableSurveys" runat="server">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-enable">Habilitar Envío de Encuestas</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxEnableSurveys" ClientIDMode="Static" runat="server" />
                                                    </td>
												    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-enable-tip">
													    Habilita el envío automático de encuestas al finalizar un caso atendido exclusivamente por yFlow
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </table>
						    <yoizen:Message ID="messageNoSurveysInTable" runat="server" Type="Warning" style="display: none; margin-top: 10px" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-no_surveys_in_service">
							    No existen encuestas en el servicio.
                            </yoizen:Message>
                            <div id="divWithSurveys" style="display: none">
                                <table class="reporte" cellspacing="0" rules="all" border="1" id="tableSurveys" style="width: 100%; border-collapse: collapse">
                                    <thead>
                                        <tr class="header">
                                            <th scope="col"><span data-i18n="globals-name">Nombre</span></th>
                                            <th scope="col">&nbsp;</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bodySurveys"></tbody>
                                </table>
                                <div style="display: none">
                                    <div id="divSurvey">
                                        <div class="scrollable-y" style="max-height: 550px">
                                            <div id="divSurveyConfiguration" class="subseccion" style="margin-top: 20px">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-title">Configuración de encuestas</h2>
                                                </div>
                                                <div class="contents">
												    <yoizen:Message ID="messageSurveyDisabled" runat="server" Type="Warning" style="display: none;" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-survey_disabled">
													    El servicio tenía configurado una encuesta que está deshabilitada. Por favor seleccione otra
                                                    </yoizen:Message>
                                                    <table width="100%" border="0" class="uiInfoTable noBorder">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-survey">Encuesta a enviar</span>:</th>
                                                            <td class="data">
                                                                <asp:DropDownList ID="dropdownSurvey" runat="server" DataValueField="ID" DataTextField="Name" AppendDataBoundItems="true" ClientIDMode="Static">
                                                                    <asp:ListItem Value="-1" Text="Seleccione una encuesta" Selected="True" data-i18n="configuration-services-common-yflow-surveys-configuration-survey-select_one" />
                                                                </asp:DropDownList>
                                                                <span id="spanSurvey"></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation">Invitación para Participar</span>:</th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveyInvitation" ClientIDMode="Static" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="3" />
                                                                <yoizen:Message ID="messageSurveyInvitationFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																    <span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields">Debe utilizar el campo @@LINK@@ dentro del texto y puede optar por utilizar los otros mencionados a continuación</span>:<br />
																    <ul>
																	    <li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-link">Indica donde irá el link de la encuesta</span></li>
																	    <li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																	    <li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																    </ul>
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
													    <tr class="dataRow dataRowSeparator" style="display: none">
														    <th class="label"><span data-i18n="configuration-queues-survey_invitation_whatsapp">Invitación para canales de WhatsApp</span>:</th>
														    <td class="data">
															    <asp:TextBox ID="textboxSurveyInvitationInteractive" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
															    <yoizen:Message ID="messageFilterEmailSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																    <span data-i18n="configuration-queues-survey_invitation_fields_whatsapp">Debe utilizar el siguiente campo dentro del texto de la invitación</span>:<br />
																    <ul>
																	    <li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																	    <li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																    </ul>
															    </yoizen:Message>
														    </td>
													    </tr>
													    <tr class="dataRow dataRowSeparator" style="display: none">
														    <th class="label"><span data-i18n="configuration-queues-survey_button">Texto del boton para canales de WhatsApp</span>:</th>
														    <td class="data">
															    <asp:TextBox ID="textboxSurveyInvitationButton" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
															    <yoizen:Message ID="message1" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																    <span data-i18n="configuration-queues-survey_invitation_fields_button">Debe utilizar el siguiente campo dentro del texto de la invitación</span>
															    </yoizen:Message>
														    </td>
													    </tr>
                                                        <tr class="dataRow dataRowSeparator" id="queueSurveyExpiration">
                                                            <th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration">Tiempo de expiración de la invitación (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyExpiration" ClientIDMode="Static" runat="server" MaxLength="5" Width="150" />
                                                                                <div id="timeInfoSurveyExpiration" class="timeinfo" related="textboxSurveyExpiration"></div>
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration-tip">
																			    Define la cantidad de minutos que deben pasar a partir de la fecha de envío de la encuesta para considerar la invitación como expirada.
																			    Ingrese cero para indicar que la invitación nunca expira
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            <div id="divSurveyBehaviour" class="subseccion">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-title">Condiciones para el envío</h2>
                                                </div>
                                                <div class="contents">
                                                    <table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate">Tasa de envío (%)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs">
																			    <asp:TextBox ID="textboxSurveySentRate" ClientIDMode="Static" runat="server" Width="50px" MaxLength="3" style="text-align: right" />
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate-tip">
																			    Mide el porcentaje de casos sobre los que se va a generar el envío de la encuesta.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send">Tiempo de envío (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
																			    <asp:TextBox ID="textboxSurveyTimeToSend" ClientIDMode="Static" runat="server" Width="50px" MaxLength="5" style="text-align: right" />
                                                                                <div id="timeInfoSurveyTimeToSend" class="timeinfo" related="textboxSurveyTimeToSend"></div>
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send-tip">
																			    Define la cantidad de minutos que deben pasar a partir del cierre del caso para que se envíe la encuesta al usuario.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
	                                                        <th class="label">
		                                                        <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
				                                                        "toggle": "popover",
				                                                        "html": true,
				                                                        "maxWidth": "400px",
				                                                        "trigger": "hover",
				                                                        "title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-title",
                                                                        "content": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-content"
																     }' data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas"
			                                                        data-content="Opcionalmente se pueden asociar etiquetas a la respuesta predefinida, para que luego los agentes puedan encontrar esta respuesta predefinida cuando el caso del mensaje contenga las etiquetas seleccionadas"></span>
		                                                        <span data-i18n="configuration-predefinedanswersbyqueue-related_tags">Etiquetas relacionadas</span>:
	                                                        </th>
	                                                        <td class="data">
		                                                        <asp:TextBox ID="textboxSurveyTags" runat="server" ClientIDMode="Static" Width="100%" />
	                                                        </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group">Etiquetas</span>:</th>
                                                            <td class="data">
                                                                    <asp:ListBox ID="listboxSurveyTagGroup" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
                                                                    <span class="fa fa-lg fa-info-circle" style="margin-left: 5px"  data-i18n-popover='{
	                                                                    "toggle": "popover",
	                                                                    "html": true,
	                                                                    "maxWidth": "400px",
	                                                                    "trigger": "hover",
	                                                                    "title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group-popover-title",
	                                                                    "content": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group-popover-content"
	                                                                    }' data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count">Cantidad de mensajes en la conversación</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs">
																			    <asp:TextBox ID="textboxSurveyMessagesCount" ClientIDMode="Static" runat="server" MaxLength="4" Width="50px" style="text-align: right" />
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count-tip">
																			    Establece una mínima cantidad de mensajes dentro de la conversación del caso.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration">Duración mínima del caso</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
																			    <asp:TextBox ID="textboxSurveyCaseDuration" ClientIDMode="Static" runat="server" Width="50px" MaxLength="4" style="text-align: right" />
                                                                                <div id="timeInfoSurveyCaseDuration" class="timeinfo" related="textboxSurveyCaseDuration"></div>
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration-tip">
																			    Establece una mínima duración en minutos del caso.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            <div id="divSurveyIgnoreConditions" class="subseccion">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont-title">Condiciones para no realizar el envío</h2>
                                                </div>
                                                <div class="contents">
                                                    <table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case">Encuestar si ya existe un caso nuevo</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
																			    <asp:CheckBox ID="checkboxSurveySendIfNewCaseExists" runat="server" ClientIDMode="Static"/>
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case-tip">
																			    Establece si un caso será encuestado dependiendo de si ya existe un caso nuevo del mismo perfil
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey">No encuestar si la última envíada fue dentro de los últimos (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
																			    <asp:TextBox ID="textboxSurveyDontSendIfLastSurveyAfterMinutes" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
                                                                                <div id="timeInfoDontSendIfLastSurveyAfterMinutes" class="timeinfo" related="textboxSurveyDontSendIfLastSurveyAfterMinutes"></div>
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey-tip">
																			    Especifica la cantidad de minutos a considerar hacia atrás para evaluar si no se envió ninguna encuesta al usuario del caso. En caso de que la última fecha de envío
																			    de encuesta sea posterior a la fecha actual restando los minutos configurados, no se enviará la encuesta para dicho caso.
																			    En caso de configurar cero, no se aplicará esta condición. El máximo valor son 172800 (4 meses)
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly">No encuestar si ya se enviaron encuestas en el mes</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
																			    <asp:TextBox ID="textboxSurveyDontSendTotalSendMonthly" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" style="text-align: right" />
                                                                            </td>
																		    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly-tip">
																			    Especifica la cantidad de encuestas que pueden enviarse en el més para cada cliente.
																			    En caso de configurar cero, no se aplicará esta condición. El máximo valor son 30 encuestas al mes.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
	                                                        <th class="label">
		                                                        <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
				                                                        "toggle": "popover",
				                                                        "html": true,
				                                                        "maxWidth": "400px",
				                                                        "trigger": "hover",
				                                                        "title": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags",
                                                                        "content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags-tooltip"
																    }' data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas"
			                                                        data-content="Opcionalmente se pueden asociar etiquetas a la respuesta predefinida, para que luego los agentes puedan encontrar esta respuesta predefinida cuando el caso del mensaje contenga las etiquetas seleccionadas"></span>
		                                                        <span data-i18n="configuration-predefinedanswersbyqueue-related_tags">Etiquetas relacionadas</span>:
	                                                        </th>
	                                                        <td class="data">
		                                                        <asp:TextBox ID="textboxSurveysIgnoreTags" runat="server" ClientIDMode="Static" Width="100%" />
	                                                        </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
	                                                        <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags_group">Etiquetas</span>:</th>
	                                                        <td class="data">
		                                                         <asp:ListBox ID="listboxSurveyTagGroupToIgnore" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
		                                                         <span class="fa fa-lg fa-info-circle" style="margin-left: 5px"  data-i18n-popover='{
			                                                         "toggle": "popover",
			                                                         "html": true,
			                                                         "maxWidth": "400px",
			                                                         "trigger": "hover",
			                                                         "title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags_group-popover-title",
			                                                         "content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags_group-popover-content"
			                                                         }' data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
	                                                        </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="divSurveyError" class="validationerror" style="display: none"><span></span></div>
                                        <div class="buttons">
                                            <label class="uiButton uiButtonLarge uiButtonConfirm">
                                                <button type="button" data-i18n="globals-accept" onclick="ValidateSurvey()">Aceptar</button>
                                            </label>
                                            <label class="uiButton uiButtonLarge">
                                                <button type="button" data-i18n="globals-cancel" onclick="ReturnTableSurvey()">Cancelar</button>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSurveys" data-i18n="configuration-serviceswhatsapp-configuration-surveys-error" />
                            </div>
                            <div id="divNewSurvey" class="buttons">
                                <label class="uiButton uiButtonLarge uiButtonConfirm">
                                    <button type="button" onclick="AddNewSurvey()" data-i18n="configuration-surveys-new_survey">Nueva Encuesta</button>
                                </label>
                            </div>
                        </asp:Panel>
                    </div>
                </asp:Panel>
            </div>
        </div>
        <div id="divFacebookServiceMedia" class="hiddenAsGateway">
            <table width="100%" border="0" class="uiInfoTable noBorder">
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="configuration-servicesfacebookmessenger-allow_to_send">Permitir envío</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:CheckBox ID="checkboxFacebookAllowToSendMedia" runat="server" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-allow_to_send-tip">
										Este parámetro indica si el agente puede enviar archivos multimedia acompañando al mensaje
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
                    <th class="label withdescription"><span data-i18n="configuration-servicesfacebookmessenger-max_size">Máximo tamaño (en MB)</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:TextBox ID="textboxFacebookMaxSizeAttachment" runat="server" MaxLength="1" Width="80" TextMode="Number" />
                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFacebookMaxSizeAttachment" />
                                    </td>
									<td class="vMid pls" data-i18n="configuration-servicesfacebookmessenger-max_size-tip">
										Este parámetro indica el tamaño máximo permitido en MB para el total de archivos multimedia que se envían por Facebook.
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
                    <th class="label withdescription"><span data-i18n="configuration-servicesfacebookmessenger-accepted_file_type">Tipos de archivos permitidos</span>:</th>
                    <td class="data">
                        <div class="subseccion collapsable">
                            <div class="title">
                                <h2 data-i18n="configuration-servicesfacebookmessenger-accepted_file_type_options">Opciones para los archivos multimedia</h2>
                            </div>
                            <div class="contents">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFacebookMultimediaOptions" />
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-image"></span>
                                            <asp:CheckBox ID="checkboxFacebookAcceptedTypeImages" runat="server" ClientIDMode="Static" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxFacebookAcceptedTypeImages" data-i18n="globals-file_types-images">Archivos de imagen (JPG, JPEG, PNG)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-audio"></span>
                                            <asp:CheckBox ID="checkboxFacebookAcceptedTypeAudio" runat="server" ClientIDMode="Static" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxFacebookAcceptedTypeAudio" data-i18n="globals-file_types-audio">Archivos de audio (MP3)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-video"></span>
                                            <asp:CheckBox ID="checkboxFacebookAcceptedTypeVideo" runat="server" ClientIDMode="Static" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxFacebookAcceptedTypeVideo" data-i18n="globals-file_types-video">Archivos de video (MP4)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file"></span>
                                            <asp:CheckBox ID="checkboxFacebookAcceptedTypeAllFiles" runat="server" ClientIDMode="Static" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxFacebookAcceptedTypeAllFiles" data-i18n="globals-file_types-all">Archivos los archivos (*.*)</asp:Label>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divNotifications">
            <div class="seccion collapsable">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-auth_problem-title">Problemas de autenticación</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-servicesfacebookmessenger-auth_problem_mail">
						Este mail se mandará una vez por día para avisar que ocurrió un error cuando se intentaba autenticar algún servicio de
						Facebook.
                    </yoizen:Message>
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
						    <th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
						    <td class="data">
							    <asp:HiddenField ID="hiddenConnectionOAuth" runat="server" ClientIDMode="Static" />
							    <select id="listboxConnectionOAuth">
								    <option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
							    </select>
						    </td>
					    </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_subject">Asunto del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookOAuthErrorEmailSubject" runat="server" MaxLength="200" Width="90%" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookOAuthErrorEmailSubject" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_recipients">Emails destinatarios</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookOAuthErrorEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookOAuthErrorEmails" />
                                <asp:RegularExpressionValidator runat="server" ControlToValidate="textboxFacebookOAuthErrorEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_template">Plantilla del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookOAuthErrorEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookOAuthErrorEmailTemplate" />
                                <yoizen:Message ID="messageFacebookOAuthErrorEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-servicesfacebookmessenger-auth_problem_mail_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-auth_problem_mail-field-date_and_time">Indica la fecha y hora cuando ocurrió el primer error</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-auth_problem_mail-field-service_name">Indica el nombre del servicio que tiene el error</span></li>
										<li><span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-auth_problem_mail-field-error_message">Indica el mensaje de error devuelto por Facebook</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="seccion collapsable">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-inactive_time-title">Minutos con inactividad</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-servicesfacebookmessenger-inactive_time">
						Este mail se mandará una vez cuando se detecte que luego de pasado cierta cantidad de minutos, no se hayan recibido novedades de Facebook.
                    </yoizen:Message>
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
						    <th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
						    <td class="data">
							    <asp:HiddenField ID="hiddenConnectionInactivity" runat="server" ClientIDMode="Static" />
							    <select id="listboxConnectionInactivity">
								    <option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
							    </select>
						    </td>
					    </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-minutes">Minutos</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookMinutesForInactivity" runat="server" MaxLength="4" Width="80" TextMode="Number" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookMinutesForInactivity" />
                                <asp:CompareValidator runat="server" ControlToValidate="textboxFacebookMinutesForInactivity" Type="Integer" Operator="GreaterThan" ValueToCompare="0" />
                                <div class="timeinfo" related="textboxFacebookMinutesForInactivity"></div>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_subject">Asunto del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookInactivityDetectedEmailSubject" runat="server" MaxLength="200" Width="90%" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookInactivityDetectedEmailSubject" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_recipients">Emails destinatarios</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookInactivityDetectedEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookInactivityDetectedEmails" />
                                <asp:RegularExpressionValidator runat="server" ControlToValidate="textboxFacebookInactivityDetectedEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-mail_template">Plantilla del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookInactivityDetectedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFacebookInactivityDetectedEmailTemplate" />
                                <yoizen:Message ID="messageFacebookInactivityDetectedEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-servicesfacebookmessenger-inactivity_deleted_mail_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-inactivity_deleted_mail-field-date_and_hour">Indica la fecha y hora cuando ocurrió el primer error</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-inactivity_deleted_mail-field-service_name">Indica el nombre del servicio que tiene el error</span></li>
										<li><span class='templatefieldname'>@@MINUTOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-inactivity_deleted_mail-field-inactive_time">Indica la cantidad de minutos que pasaron sin actividad en Facebook</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div id="divBehaviour">
            <div class="seccion hiddenAsGateway">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-auto_reply_before_max_time-title">Acciones sobre mensajes próximos a superar la cantidad máxima de tiempo para responder</h2>
                </div>
                <div class="contents">
                    <div class="subseccion collapseWithCheckbox">
                        <div class="title">
                            <h2>
                                <asp:CheckBox ID="checkboxAutoReplyBeforeMaxTimeToAnswer" runat="server" ClientIDMode="Static" />
                                <span data-i18n="configuration-servicesfacebookmessenger-answer">Responder</span></h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-answer_message">Mensaje de respuesta</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeMaxTimeToAnswerText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
										<yoizen:Message ID="messageAutoReplyBeforeMaxTimeToAnswerText" runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px">
											<span data-i18n="configuration-servicesfacebookmessenger-answer_message_fields">Puede utilizar los siguientes campos dentro del mensaje</span>:<br />
											<ul>
												<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-answer_message-field-user_name">Indica el nombre del usuario</span></li>
												<li><span class='templatefieldname'>@@HORA@@</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-answer_message-field-current_time">Indica la hora actual</span></li>
											</ul>
                                        </yoizen:Message>
                                    </td>
                                </tr>
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-auto_reply_max_time">Cantidad minutos antes del límite</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeMaxTimeToAnswerMinutes" runat="server" MaxLength="4" ClientIDMode="Static" />
                                        <div class="timeinfo" related="textboxAutoReplyBeforeMaxTimeToAnswerMinutes"></div>
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyBeforeMaxTimeToAnswer" SkinID="validationerror" />
                            </div>
                        </div>
                    </div>
                    <div class="subseccion collapseWithCheckbox">
                        <div class="title">
                            <h2>
                                <asp:CheckBox ID="checkboxDiscardAfterMaxTimeToAnswer" runat="server" ClientIDMode="Static" />
                                <span data-i18n="configuration-servicesfacebookmessenger-discard-title">Descartar</span></h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-close_case">Cerrar el caso</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer" runat="server" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div id="divReplyBeforeClose">
                <div class="seccion">
                    <div class="title">
                        <h2 data-i18n="configuration-servicesfacebookmessenger-div-auto-reply-title">Acciones sobre mensajes próximos a superar la cantidad máxima de tiempo para responder</h2>
                    </div>
                    <div class="contents">
                        <div class="subseccion collapseWithCheckbox">
                            <div class="title">
                                <h2>
                                    <asp:CheckBox ID="checkboxAutoReplyBeforeCloseCase" runat="server" ClientIDMode="Static" />
                                    <span data-i18n="configuration-servicesfacebookmessenger-auto_reply_title">Responder</span></h2>
                            </div>
                            <div class="contents">
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow">
                                        <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-auto_reply_message">Mensaje de respuesta</span>:</th>
                                        <td class="data">
                                            <asp:TextBox ID="textboxAutoReplyBeforeCloseCaseText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-minutes_before_limit">Cantidad minutos antes del límite</span>:</th>
                                        <td class="data">
                                            <asp:TextBox ID="textboxAutoReplyBeforeCloseCaseMinutes" runat="server" MaxLength="4" ClientIDMode="Static" />
                                            <div class="timeinfo" related="textboxAutoReplyBeforeCloseCaseMinutes"></div>
                                        </td>
                                    </tr>
                                </table>
                                <div class="validationerror" style="display: none">
                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyBeforeCloseCase" SkinID="validationerror" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-message_tags-title">Etiquetas de mensaje</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-servicesfacebookmessenger-message_tags_tip">
						Las etiquetas de mensajes te ofrecen la posibilidad de enviar mensajes a una persona fuera del plazo de 24 horas que se suele conceder para una serie de objetivos 
						que requieren notificación o actualizaciones continuas. De este modo, la flexibilidad relativa a la interacción del bot con las personas y a los tipos de experiencias 
						que puedes crear en la plataforma de Messenger es mucho mayor.<br />
						El envío de este tipo de mensaje puede incluir costos
                    </yoizen:Message>
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesfacebookmessenger-allow_to_send_hsm">Permitir uso de Etiquetas de Mensaje</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowToSendHSM" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator hiddenAsGateway" id="trAllowAgentsToSendHSM">
                            <th class="label"><span data-i18n="configuration-servicesfacebookmessenger-allow_agents_to_send_hsm">Permitir a los agentes enviar Etiquetas de Mensaje</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowAgentsToSendHSM" runat="server">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                    <div id="divHSMTemplates" class="subseccion">
                        <div class="title">
                            <h2 data-i18n="configuration-servicesfacebookmessenger-admited_tags-title">Etiquetas admitidas</h2>
                        </div>
                        <div class="contents">
                            <asp:HiddenField ID="hiddenHSMTemplates" runat="server" ClientIDMode="Static"></asp:HiddenField>
                            <yoizen:Message runat="server" Type="Information" Small="true">
								<span data-i18n="configuration-servicesfacebookmessenger-tags_content">Cada etiqueta contiene</span>:
								<ul>
									<li><span class='templatefieldname'>Código</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-tags_content-field-code">Indica el código interno para identificar cada pantalla</span></li>
									<li><span class='templatefieldname'>Descripción</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-tags_content-field-safe_name">Indica un nombre amigable para presentar en las pantallas</span></li>
									<li><span class='templatefieldname'>Etiqueta</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-tags_content-field-tag_type">Indica el tipo de etiqueta que se enviará</span></li>
									<li><span class='templatefieldname'>Plantilla</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-tags_content-field-tag_text">Indica el texto de la etiqueta</span></li>
									<li><span class='templatefieldname'>Parámetros</span>: <span class='templatefielddescription' data-i18n="configuration-servicesfacebookmessenger-edit-tags_content-field-safe_parameters_name">Indica un nombre amigable para cada parámetro que se definió dentro de la estructura del mensaje. Se debe ingresar un parámetro por línea</span></li>
								</ul>
                            </yoizen:Message>
                            <table id="tableHSMTemplates" class="reporte" cellspacing="0" rules="all" border="1">
                                <thead>
                                    <tr class="header">
                                        <th style="width: 20px"></th>
                                        <th scope="col" style="width: 20%"><span data-i18n="configuration-servicesfacebookmessenger-tag_template-code">Código</span></th>
                                        <th scope="col" style="width: 20%"><span data-i18n="configuration-servicesfacebookmessenger-tag_template-description">Descripción</span></th>
                                        <th scope="col" style="width: 20%"><span data-i18n="configuration-servicesfacebookmessenger-tag_template-tag">Etiqueta</span></th>
                                        <th scope="col" style="width: 20%"><span data-i18n="configuration-servicesfacebookmessenger-tag_template-template">Plantilla</span></th>
                                        <th scope="col" style="width: 20%"><span data-i18n="configuration-servicesfacebookmessenger-tag_template-parameters">Parámetros</span></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                                <tfoot>
                                    <tr>
                                        <td style="text-align: center"><a id="anchorHSMTemplatesAdd"><span class="fa fa-lg fa-plus-square"></span></a></td>
                                        <td colspan="7"></td>
                                    </tr>
                                </tfoot>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateHSMTemplates" data-i18n="configuration-servicesfacebookmessenger-configuration-error" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <asp:Panel ID="panelSurveys" runat="server" CssClass="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-servicesfacebookmessenger-polls-title">Encuestas</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-servicesfacebookmessenger-polls_info">
						La definición de las encuestas se definen en las colas de atención. Desde aquí puede deshabilitar el uso de encuestas para mensajes
						de Messenger
                    </yoizen:Message>
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-servicesfacebookmessenger-allow_polls">Permitir encuestas</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowSurveys" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                </div>
            </asp:Panel>
            <div id="divCapiService" runat="server" clientidmode="static" class="seccion" style="display:none;">
	            <div class="title">
		            <h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;Capi</h2>
	            </div>
	            <div class="contents">
	            <table width="100%" border="0" class="uiInfoTable noBorder">
		            <tr class="dataRow dataRowSeparator">
			            <th class="label withdescription"><span data-i18n="configuration-systemsettings-capi_enable">Habilitar CAPI</span>:</th>
			            <td class="data">
				            <table class="uiGrid" cellspacing="0" cellpadding="0">
					            <tbody>
						            <tr>
							            <td class="vMid prs">
								            <asp:CheckBox ID="checkboxEnableCapi" runat="server" />
							            </td>
						            </tr>
					            </tbody>
				            </table>
			            </td>
		            </tr>
	            </table>
	            </div>
            </div>
        </div>
        <div id="divCases">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-systemsettings-case_configuration-title">Configuraciones de casos</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator hiddenAsGateway">
                            <th class="label withdescription" style="width: 200px !important"><span data-i18n="configuration-services-common-cases_override">Sobreescribir configuración de casos</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:CheckBox ID="checkboxCasesOverrideSystemSettings" runat="server" ClientIDMode="Static" />
                                            </td>
											<td class="vMid pls" data-i18n="configuration-services-common-cases_override-tip">
												Este parámetro indica si el servicio puede sobreescribir la configuración de Párámetros del sistema con respecto a los casos 
												y así poder definir dintintos valores para el tratamiento de casos.
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <div class="subseccion" id="divCasesOverride">
                        <div class="title">
                            <h2 data-i18n="configuration-services-common-cases_override-title">Configuraciones específicas de casos</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                                    <th class="label withdescription" style="width: 200px !important"><span data-i18n="configuration-systemsettings-verify_last_queue">Verificar última cola</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxCheckLastQueueOfOpenCase" runat="server" ClientIDMode="Static" />
                                                    </td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-verify_last_queue-tip">
														Este parámetro indica si el sistema verificará ante cada mensaje nuevo si ese usuario tiene
														un caso abierto y su último mensaje ingresó en otra cola (que no corresponde a la cola por defecto asignada al servicio). 
														De ser así se tomará como cola por defecto la cola del último mensaje
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                                    <th class="label withdescription"><span data-i18n="configuration-systemsettings-ignore_last_queue">Ignorar última cola si el último mensaje entrante fue movido por SL</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxIgnoreLastQueueForSLMovedMessage" runat="server" ClientIDMode="Static" />
                                                    </td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-ignore_last_queue-tip">
														Este parámetro indica si el sistema ignorará la última cola del caso en caso de que el último mensaje
														que haya sido escrito por el usuario fue movido por alguna acción del Service Level/Vencimiento.
														En caso de ignorar, se utilizará la cola por defecto del servicio por el cual ingresó el mensaje
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
										<span data-i18n="configuration-systemsettings-time_to_close">Minutos para cierre automático</span>: <br />
                                    </th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxMaxElapsedMinutesToCloseCases" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" />
                                                        <div class="timeinfo" related="textboxMaxElapsedMinutesToCloseCases"></div>
                                                    </td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-tip">
														Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario y el sistema. Pasado
														esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-auto_reply_close_case">Responder en cierre automático</span>:
                                    </th>
                                    <td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs timeinfo">
                                                        <asp:CheckBox ID="checkboxReplyInCloseCase" runat="server" ClientIDMode="Static" />
                                                    </td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-auto_reply_close_case-tip">
														Este parámetro indica si se evniará una respuesta al ultimo mensaje del caso antes de cerrarlo de forma automatica.
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trAutoReplyInCloseCaseText">
                                    <th class="label">
                                        <span data-i18n="configuration-systemsettings-auto_reply_close_case">Respuesta en cierre automático</span>:
                                    </th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyInCloseCaseText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-apply_tag_close_case">Aplicar etiqueta en cierre automático</span>:
                                    </th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxTagCloseCase" runat="server" ClientIDMode="Static" Width="100%" />
                                    </td>
                                </tr>
                                <asp:PlaceHolder ID="placeholderYFlowCasesRelated" runat="server">
                                    <tr class="dataRow dataRowSeparator" id="trMaxElapsedMinutesToCloseYFlowCases">
                                        <th class="label withdescription"><span data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case">Minutos para cierre automático de un caso atendido únicamente por yFlow </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs timeinfo">
															<asp:TextBox ID="textboxMaxElapsedMinutesToCloseYFlowCases" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" Text="0" />
                                                            <div class="timeinfo" related="textboxMaxElapsedMinutesToCloseYFlowCases"></div>
                                                        </td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case-tip">
															Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario e yFlow. Pasado
															esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
															Un caso únicamete de yFlow es cuando nunca fue derivado a un agente
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr class="dataRow dataRowSeparator" id="trInvokeYFlowWhenClosedCases">
                                        <th class="label withdescription"><span data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case">Invocar a yFlow al momento de cerrar el caso </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs">
                                                            <asp:CheckBox ID="checkboxInvokeYFlowWhenClosedCases" runat="server" ClientIDMode="Static" />
                                                        </td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case-tip">
															Este parámetro indica si se deberá invocar a yFlow cuando el cierre automático de un caso atendido únicamente por yFlow se ejecute.
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </asp:PlaceHolder>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateCasesSettings" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
		<div id="divVideo">
			<asp:Panel ID="panelVideo" runat="server">
				<table width="100%" border="0" class="uiInfoTable noBorder">
					<tr class="dataRow dataRowSeparator">
						<th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-serviceswhatsapp-video-enable_video">Habilitar uso de video</span>:</th>
						<td class="data">
							<asp:CheckBox ID="checkboxEnableVideo" ClientIDMode="Static" runat="server"/>
						</td>
					</tr>
				</table>
			</asp:Panel>
		</div>
    </div>
    <div class="buttons">
        <asp:HiddenField ID="hiddenServiceToCopy" runat="server" ClientIDMode="Static" />
        <label class="uiButton uiButtonLarge">
            <button type="button" data-i18n="configuration-services-copy_from_service" onclick="ShowCopyServiceCommon('FacebookMessenger')">Copiar desde otro servicio</button>
        </label>
        <asp:Button ID="buttonCopyService" runat="server" Text="Aceptar" OnClick="buttonCopyService_Click" CausesValidation="false" Style="display: none" ClientIDMode="Static" />
        <label class="uiButton uiButtonLarge uiButtonConfirm">
            <button type="button" data-i18n="globals-accept" onclick="VerifyOtherServices()">Aceptar</button>
			<asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" style="display: none" ClientIDMode="Static" data-i18n="globals-accept" />
        </label>
        <label class="uiButton uiButtonLarge">
            <asp:Button ID="buttonCancel" runat="server" Text="Cancelar" OnClick="buttonCancel_Click" CausesValidation="false" data-i18n="globals-cancel" />
        </label>
    </div>
</asp:Content>
