﻿CREATE PROCEDURE [dbo].[Messages_DiscardOfClosedCases]
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @tMessages BigIntTableType;
	DECLARE @Date DATETIME = DATEADD(day, -7, GETDATE());

	INSERT INTO @tMessages
		SELECT [MessageID] 
			FROM [Messages] WITH (NOLOCK)
				INNER JOIN [Cases] WITH (NOLOCK)
					ON [Messages].[CaseID] = [Cases].[CaseID]
			WHERE 
				[Messages].[MessageStatusID] IN (1,2,13)  --No asignado, Asignado y Push
				AND [Messages].[CaseID] IS NOT NULL
				AND [Date] > @Date
				AND [Cases].[CaseStatusID] = 2; --Cerrado

	UPDATE [Messages] SET
		[MessageStatusID] = 4 --Descartado
		WHERE [MessageID] IN (SELECT VALUE FROM @tMessages);

	SELECT [VALUE] AS [MessageID]
		FROM @tMessages;
END