﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />

$.ajaxSetup({ cache: false });

var $divLoading;
var $divCompose;
var $selectService;
var $divServiceInfo;
var $spanServiceInfoTotalSent;
var $divServiceInfoLicensed;
var $spanServiceInfoLicensed;
var $trSendHsmAnyways;
var $selectTemplate;
var $selectSendHsmAnyways;
var $divHSMParametersContainer;
var $tableParameters;
var $tbodyParameters;
var $divError;
var $messageTemplateEmptyParameters;
var $messageNoWhatsappServices;
var $selectDestinationCountry;
var $inputDestinationNumber;
var $textboxTags;
var $divHSMHeader;
var $messageHSMHeaderNone;
var $divHSMHeaderText;
var $spanHSMHeaderText;
var $divHSMHeaderMedia;
var $spanHSMHeaderMediaType;
var $selectHSMHeaderSendFrom;
var $trHSMHeaderSendFromUrl;
var $inputHSMHeaderSendFromUrl;
var $trHSMHeaderSendFromUrlPublic;
var $inputHSMHeaderSendFromUrlIsPublic;
var $trHSMHeaderSendFromFile;
var $divHSMHeaderSendFromFileContainer;
var $inputHSMHeaderMediaName;
var $divHSMHeaderLocation;
var $inputHSMHeaderLocationLatitude;
var $inputHSMHeaderLocationLongitude;
var $inputHSMHeaderLocationName;
var $inputHSMHeaderLocationAddress;
var $divHSMContents;
var $divHSMFooter;
var $messageHSMFooterNone;
var $divHSMFooterText;
var $spanHSMFooterText;
var $divHSMButtons;
var $messageHSMButtonsNone;
var $divHSMButtonsContainer;
var $spanHSMButtonsType;
var $divHSMButtonsMoreButtons;
var $spanHSMButtonsMoreButtonsText;
var $divHSMExample;
var markdownConverter = null;
var $checkboxDoNotCall;
var $tButtons;
var parameters = [];
var template = null;
var flowScreenData;

$(function () {
    $divCompose = $('#divCompose');
    $selectService = $('#selectService');
    $divServiceInfo = $('#divServiceInfo');
    $spanServiceInfoTotalSent = $('.totalsent', $divServiceInfo);
    $divServiceInfoLicensed = $('div.item.totallicensed', $divServiceInfo);
    $spanServiceInfoLicensed = $('span.totallicensed', $divServiceInfoLicensed);
    $selectTemplate = $('#selectTemplate');
    $selectSendHsmAnyways = $('#selectSendHsmAnyways');
    $selectSendHsmAnyways.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>500' });
    $trSendHsmAnyways = $('#trSendHsmAnyways');
    $divHSMParametersContainer = $('#divHSMParametersContainer');
    $tableParameters = $('#tableParameters');
    $tbodyParameters = $('tbody', $tableParameters);
    $divError = $('#divError');
    $messageTemplateEmptyParameters = $('#messageTemplateEmptyParameters');
    $messageNoWhatsappServices = $('#messageNoWhatsappServices');
    $selectDestinationCountry = $('#selectDestinationCountry');
    $inputDestinationNumber = $('#inputDestinationNumber');

    $divHSMHeader = $('#divHSMHeader');
    $messageHSMHeaderNone = $('#messageHSMHeaderNone');
    $divHSMHeaderText = $('#divHSMHeaderText');
    $spanHSMHeaderText = $('#spanHSMHeaderText');
    $divHSMHeaderMedia = $('#divHSMHeaderMedia');
    $spanHSMHeaderMediaType = $('#spanHSMHeaderMediaType');
    $selectHSMHeaderSendFrom = $('#selectHSMHeaderSendFrom');
    $trHSMHeaderSendFromUrl = $('#trHSMHeaderSendFromUrl');
    $inputHSMHeaderSendFromUrl = $('#inputHSMHeaderSendFromUrl');
    $trHSMHeaderSendFromUrlPublic = $('#trHSMHeaderSendFromUrlPublic');
    $inputHSMHeaderSendFromUrlIsPublic = $('#inputHSMHeaderSendFromUrlIsPublic');
    $trHSMHeaderSendFromFile = $('#trHSMHeaderSendFromFile');
    $divHSMHeaderSendFromFileContainer = $('#divHSMHeaderSendFromFileContainer');
    $inputHSMHeaderMediaName = $('#inputHSMHeaderMediaName');
    $divHSMHeaderLocation = $('#divHSMHeaderLocation');
    $inputHSMHeaderLocationLatitude = $('#inputHSMHeaderLocationLatitude');
    $inputHSMHeaderLocationLongitude = $('#inputHSMHeaderLocationLongitude');
    $inputHSMHeaderLocationName = $('#inputHSMHeaderLocationName');
    $inputHSMHeaderLocationAddress = $('#inputHSMHeaderLocationAddress');
    $divHSMContents = $('#divHSMContents');
    $divHSMFooter = $('#divHSMFooter');
    $messageHSMFooterNone = $('#messageHSMFooterNone');
    $divHSMFooterText = $('#divHSMFooterText');
    $spanHSMFooterText = $('#spanHSMFooterText');
    $divHSMButtons = $('#divHSMButtons');
    $messageHSMButtonsNone = $('#messageHSMButtonsNone');
    $divHSMButtonsContainer = $('#divHSMButtonsContainer');
    $spanHSMButtonsType = $('#spanHSMButtonsType');
    $divHSMButtonsMoreButtons = $('#divHSMButtonsMoreButtons');
    $spanHSMButtonsMoreButtonsText = $('#spanHSMButtonsMoreButtonsText');
    $tButtons = $('#tButtons');

    $textboxTags = $('#textboxTags');

    $textboxTags.tokenInput("../Reports/Cases.aspx/SearchTags", {
        method: 'POST',
        queryParam: 'q',
        searchDelay: 300,
        minChars: 3,
        //tokenLimit: 1,
        propertyToSearch: 'FullName',
        //jsonContainer: 'd',
        preventDuplicates: true,
        tokenValue: 'ID',
        contentType: 'json',
        resultsLimit: 20,
        excludeCurrent: true,
        styles: {
            dropdown: {
                'max-height': '200px',
                'overflow-y': 'auto'
            },
            tokenList: {
                'width': '100%'
            }
        },
        prePopulate: null,
        onSend: function (params) {
            params.data = JSON.stringify(params.data);
            params.contentType = "application/json; charset=utf-8";
        },
        onResult: function (results, query, cache_key) {
            var settingsTags = $textboxTags.data('settings');
            if (typeof (settingsTags.local_data) === 'undefined') {
                delete settingsTags.url;
                settingsTags.local_data = results.d;

                return $.grep(settingsTags.local_data, function (row) {
                    return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
                });
            }

            return results;
        }
    });

    $checkboxDoNotCall = $('#checkboxDoNotCall');

    $divHSMExample = $('#divHSMExample');

    $selectHSMHeaderSendFrom.change(function () {
        var value = parseInt($selectHSMHeaderSendFrom.val(), 10);
        $trHSMHeaderSendFromUrl.toggle(value === 1);
        $trHSMHeaderSendFromUrlPublic.toggle(value === 1);
        $trHSMHeaderSendFromFile.toggle(value === 2);
    });

    BuildFileUploader({
        container: $divHSMHeaderSendFromFileContainer,
        accept: '.pdf',
        acceptMimeTypes: ['application/pdf'],
        onAddedMultipleFiles: function () {
            AlertDialog($.i18n('globals-upload_file-title'), $.i18n('globals-upload_file-only_one_file'), undefined, undefined, 'Error');
        },
        onFileUploaded: function (data) {
            $inputHSMHeaderMediaName.val(data.originalFileName);
        },
        onInvalidFile: function (mimeType) {
            AlertDialog($.i18n('globals-upload_file-title'), $.i18n('globals-upload_file-invalid_type', mimeType), undefined, undefined, 'Error');
        }
    });

    if (typeof (services) === 'undefined' ||
        services === null ||
        services.length === 0) {
        $divCompose.hide();
        $messageNoWhatsappServices.show();
    }
    else {
        $divCompose.show();
        $messageNoWhatsappServices.hide();

        for (var i = 0; i < countries.length; i++) {
            var $option = $('<option></option>');
            $option.text(countries[i].Description + ' (+' + countries[i].InternationalCode + ')');
            $option.val(countries[i].InternationalCode);
            $option.prop('country', countries[i]);
            $selectDestinationCountry.append($option);
        }

        $selectDestinationCountry.val(defaultCountry);
        $selectDestinationCountry.multiselect({ multiple: false, selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

        for (var i = 0; i < services.length; i++) {
            var $option = $('<option></option>');
            $option.text(services[i].Name + ' (' + services[i].PhoneNumber + ')');
            $option.val(services[i].ID);
            $option.prop('service', services[i]);
            $selectService.append($option);
        }

        $selectService.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
        $selectTemplate.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>500' }).multiselectfilter();

        $selectTemplate.change(function () {
            var $option = $('option:selected', $selectTemplate);
            template = $option.prop('template');
            parameters = [];

            if (typeof (template.AllowToConfigureSendHSMIfCaseOpen) != 'undefined' &&
                template.AllowToConfigureSendHSMIfCaseOpen) {
                $trSendHsmAnyways.show();
            }
            else {
                $trSendHsmAnyways.hide();
            }

            $divHSMHeaderText.hide();
            $messageHSMHeaderNone.hide();
            $divHSMHeaderMedia.hide();
            $divHSMHeaderLocation.hide();
            $selectHSMHeaderSendFrom.val(1).trigger('change');
            $inputHSMHeaderSendFromUrl.val('');
            $inputHSMHeaderSendFromUrlIsPublic.prop('checked', true);
            $inputHSMHeaderMediaName.val('');
            $inputHSMHeaderLocationLatitude.val('');
            $inputHSMHeaderLocationLongitude.val('');
            $inputHSMHeaderLocationName.val('');
            $inputHSMHeaderLocationAddress.val('');
            $divHSMFooterText.hide();
            $messageHSMFooterNone.hide();

            $divHSMExample.removeClass('with-header header-text header-media header-location with-footer footer-text with-buttons with-buttons-2 with-buttons-3 with-buttons-all');

            if (typeof (template.HeaderType) === 'number') {
                switch (template.HeaderType) {
                    case HSMTemplateHeaderTypes.None:
                        $messageHSMHeaderNone.show();
                        break;
                    case HSMTemplateHeaderTypes.Text:
                        $divHSMExample.addClass('with-header header-text');
                        $divHSMHeaderText.show();
                        $spanHSMHeaderText.text(template.HeaderText);

                        if (template.HeaderTextParameter !== null) {
                            template.HeaderTextParameter.Type = 'header';
                            parameters.push(template.HeaderTextParameter);
                        }
                        break;
                    case HSMTemplateHeaderTypes.Media:
                        $divHSMExample.addClass('with-header header-media');
                        $divHSMHeaderMedia.show();
                        switch (template.HeaderMediaType) {
                            case HSMTemplateHeaderMediaTypes.Document:
                                if (translationsLoaded) {
                                    $spanHSMHeaderMediaType.text($.i18n('configuration-serviceswhatsapp-template-header-media-type-document'));
                                }
                                else {
                                    $spanHSMHeaderMediaType.attr('data-i18n', 'configuration-serviceswhatsapp-template-header-media-type-document');
                                }
                                $divHSMExample.addClass('header-media-document');

                                $divHSMHeaderSendFromFileContainer.setOption('accept', '.pdf');
                                $divHSMHeaderSendFromFileContainer.setOption('acceptMimeTypes', ['application/pdf']);
                                break;
                            case HSMTemplateHeaderMediaTypes.Image:
                                if (translationsLoaded) {
                                    $spanHSMHeaderMediaType.text($.i18n('configuration-serviceswhatsapp-template-header-media-type-image'));
                                }
                                else {
                                    $spanHSMHeaderMediaType.attr('data-i18n', 'configuration-serviceswhatsapp-template-header-media-type-image');
                                }
                                $divHSMExample.addClass('header-media-image');

                                $divHSMHeaderSendFromFileContainer.setOption('accept', '.jpg,.png,jpeg');
                                $divHSMHeaderSendFromFileContainer.setOption('acceptMimeTypes', ['image/jpg', 'image/jpeg', 'image/png']);
                                break;
                            case HSMTemplateHeaderMediaTypes.Video:
                                if (translationsLoaded) {
                                    $spanHSMHeaderMediaType.text($.i18n('configuration-serviceswhatsapp-template-header-media-type-video'));
                                }
                                else {
                                    $spanHSMHeaderMediaType.attr('data-i18n', 'configuration-serviceswhatsapp-template-header-media-type-video');
                                }
                                $divHSMExample.addClass('header-media-video');

                                $divHSMHeaderSendFromFileContainer.setOption('accept', '.mp4');
                                $divHSMHeaderSendFromFileContainer.setOption('acceptMimeTypes', ['video/mp4']);
                                break;
                            default:
                                break;
                        }

                        if (template.HeaderMediaUrl !== null) {
                            $inputHSMHeaderSendFromUrl.val(template.HeaderMediaUrl);
                        }

                        if (typeof (template.HeaderMediaFileName) === 'string' &&
                            template.HeaderMediaFileName !== null) {
                            $inputHSMHeaderMediaName.val(template.HeaderMediaFileName);
                        }

                        break;
                    case HSMTemplateHeaderTypes.Location:
                        $divHSMExample.addClass('with-header header-location');
                        $divHSMHeaderLocation.show();

                        $inputHSMHeaderLocationLatitude.val(template.HeaderLocationLatitude);
                        $inputHSMHeaderLocationLongitude.val(template.HeaderLocationLongitude);
                        break;
                }
            }
            else {
                $divHSMHeader.hide();
            }

            if (typeof (template.FooterType) === 'number') {
                switch (template.FooterType) {
                    case HSMTemplateFooterTypes.None:
                        $messageHSMFooterNone.show();
                        break;
                    case HSMTemplateFooterTypes.Text:
                        $divHSMExample.addClass('with-footer footer-text');
                        $divHSMFooterText.show();
                        $spanHSMFooterText.text(template.FooterText);
                        break;
                }
            }
            else {
                $divHSMFooter.hide();
            }

            $divHSMContents.html(FormatMarkdown(template.Template));

            if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
                Array.prototype.push.apply(parameters, template.TemplateParameters);
            }

            if (typeof (template.ButtonsType) === 'number') {
                $messageHSMButtonsNone.hide();
                $divHSMButtonsContainer.hide();
                $tButtons.empty();

                let locale;
                switch (template.ButtonsType) {
                    case HSMTemplateButtonsTypes.None:
                        $messageHSMButtonsNone.show();
                        break;
                    case HSMTemplateButtonsTypes.QuickReply:
                        locale = 'configuration-serviceswhatsapp-template-buttons-type-quick_reply';
                        break;
                    case HSMTemplateButtonsTypes.CallToAction:
                        locale = 'configuration-serviceswhatsapp-template-buttons-type-call_to_action';
                        break;
                    case HSMTemplateButtonsTypes.AuthCode:
                        locale = 'configuration-serviceswhatsapp-template-buttons-type-authcode';
                        break;
                    case HSMTemplateButtonsTypes.Mixed:
                        locale = 'configuration-serviceswhatsapp-template-buttons-type-mixed';
                        break;
                    default:
                        break;
                }

                if (template.ButtonsType != HSMTemplateButtonsTypes.None) {

                    $divHSMExample.addClass('with-buttons');
                    if (translationsLoaded) {
                        $spanHSMButtonsType.text($.i18n(locale));
                    }
                    else {
                        $spanHSMButtonsType.attr('data-i18n', locale);
                    }

                    $divHSMButtonsContainer.show();

                    if (template.Buttons.length >= 2) {
                        $divHSMExample.addClass('with-buttons-2');
                        if (template.Buttons.length >= 3) {
                            $divHSMExample.addClass('with-buttons-3');
                            if (template.Buttons.length >= 4) {
                                $divHSMExample.addClass('with-buttons-all');
                            }
                        }
                    }

                    for (var i = 0; i < template.Buttons.length; i++) {
                        var newRow = $("<tr>").addClass("dataRow dataRowSeparator");

                        if (translationsLoaded) {
                            newRow.append("<th class='label' style='width: 200px !important'>" +
                                $.i18n('whatsapp-hsmcomposer-buttons-button', (i + 1)));
                        }
                        else {
                            newRow.append("<th class='label' style='width: 200px !important'>" +
                                `Botón ${i + 1}:`)
                        }

                        newRow.append(BuildHSMButton(template.Buttons[i], parameters));

                        $tButtons.append(newRow);
                    }
                }
            }
            else {
                $divHSMButtons.hide();
            }

            BuildParameters();
            RenderExample();
        });

        $selectService.change(function () {
            var $option = $('option:selected', $selectService);
            var service = $option.prop('service');

            $selectTemplate.empty();
            for (var i = 0; i < service.HSMTemplates.length; i++) {
                var template = service.HSMTemplates[i];
                var $option = $('<option></option>');
                $option.text(template.Description + ' (' + template.Namespace + ' - ' + template.ElementName + ')');
                $option.val(template.ElementName);
                $option.prop('template', template);
                $selectTemplate.append($option);
            }

            $selectTemplate.multiselect('refresh');
            $selectTemplate.trigger('change');
        }).trigger('change');
    }
});

function i18nLoaded() {
    RenderExample();
}


function BuildParameters() {
    if (parameters === null || parameters.length === 0) {
        $messageTemplateEmptyParameters.show();
        $divHSMParametersContainer.hide();
    }
    else {
        $messageTemplateEmptyParameters.hide();
        $divHSMParametersContainer.show();
        $tbodyParameters.empty();

        for (var i = 0; i < parameters.length; i++) {
            var $lastTr = $("tr:last-child", $tbodyParameters);
            var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
            var $newTr = $('<tr></tr>');
            $newTr.addClass(newTrClass);

            var $td = $('<td></td>');
            if (typeof (parameters[i]) === 'string') {
                $td.text(parameters[i]);
            }
            else {
                if (typeof (parameters[i].Type) === 'string') {
                    switch (parameters[i].Type) {
                        case 'button': {
                            let $span = $('<span class="fa fa-rectangle-wide" style="margin-right: 3px"></span>');
                            $td.append($span);
                            $span.tooltip({ title: $.i18n('whatsapp-hsmcomposer-parameters-from_button'), trigger: 'hover' });
                        } break;
                        case 'header': {
                            let $span = $('<span class="fa fa-heading" style="margin-right: 3px"></span>');
                            $td.append($span);
                            $span.tooltip({ title: $.i18n('whatsapp-hsmcomposer-parameters-from_header'), trigger: 'hover' });
                        } break;
                        case 'flow': {
                            let $span = $('<span class="fa fa-layer-group" style="margin-right: 3px"></span>');
                            $td.append($span);
                            $span.tooltip({ title: $.i18n('whatsapp-hsmcomposer-parameters-from_flow'), trigger: 'hover' });
                        } break;
                    }
                }
                $td.append(parameters[i].Name + '=' + parameters[i].Description);
            }
            $newTr.append($td);

            var $td = $('<td></td>');
            $newTr.append($td);

            let $inputValue;
            if (typeof (parameters[i].SubType) == 'string') {
                switch (parameters[i].SubType) {
                    case 'object':
                        $inputValue = $('<textarea class="inputtext" spellcheck="false" style="width:95%"/>');
                        $inputValue.prop('parameter', parameters[i]);
                        $inputValue.val("{\n}")
                        $td.append($inputValue);
                        break;
                    case 'string':
                        $inputValue = $('<input type="text" class="inputtext" spellcheck="false" style="width:95%"/>');
                        $inputValue.prop('parameter', parameters[i]);
                        $td.append($inputValue);
                        break
                    case 'array':
                        $inputValue = $('<textarea class="inputtext" spellcheck="false" style="width:95%"/>');
                        $inputValue.prop('parameter', parameters[i]);
                        $inputValue.val("[\n]")
                        $td.append($inputValue);
                        break;
                }
            }
            else {
                $inputValue = $('<input type="text" class="inputtext" spellcheck="false" style="width:95%"/>');
                $inputValue.change(template, function (e) {
                    RenderExample();
                });
                $inputValue.prop('parameter', parameters[i]);
                $td.append($inputValue);
            }

            $tbodyParameters.append($newTr);
        }

        $tableParameters.show();
    }
}

function RenderExample() {
    var replaceInText = function (text) {
        var $inputs = $('input', $tbodyParameters);
        for (var j = 0; j < $inputs.length; j++) {
            var $input = $($inputs.get(j));
            var value = $input.val();
            var parameter = $input.prop('parameter');
            var parameterName = parameter.Name;
            if (value.length > 0) {
                text = text.replace('{{' + parameterName + '}}', value);
            }
        }

        return FormatMarkdown(text);
    };

    var $option = $('option:selected', $selectTemplate);
    var template = $option.prop('template');

    if (typeof (template.HeaderType) === 'number') {
        switch (template.HeaderType) {
            case HSMTemplateHeaderTypes.Text:
                $('.hsm-header-text', $divHSMExample).html(replaceInText(template.HeaderText));
                break;
            case HSMTemplateHeaderTypes.Media:
                break;
            case HSMTemplateHeaderTypes.Location:
                break;
        }
    }

    if (typeof (template.FooterType) === 'number') {
        switch (template.FooterType) {
            case HSMTemplateFooterTypes.Text:
                $('.hsm-footer', $divHSMExample).text(template.FooterText);
                break;
        }
    }

    $('.hsm-body', $divHSMExample).html(replaceInText(template.Template));

    if (typeof (template.ButtonsType) === 'number' && template.ButtonsType != HSMTemplateButtonsTypes.None) {
        $('.hsm-button1', $divHSMExample).html(BuildHSMButton(template.Buttons[0], null, true));
        if (template.Buttons.length >= 2) {
            $('.hsm-button2', $divHSMExample).html(BuildHSMButton(template.Buttons[1], null, true));
            if (template.Buttons.length >= 3) {
                $('.hsm-button3', $divHSMExample).html(BuildHSMButton(template.Buttons[2], null, true));
                if (template.Buttons.length >= 5) {
                    $('.hsm-more-buttons', $divHSMExample).html('<span class="fa fa-list" style="margin-right: 3px"></span>' + $.i18n('whatsapp-hsmcomposer-buttons-all_buttons'));
                }
            }
        }
    }
}

function BuildHSMButtonCallToAction(button, parameters, onlyText) {
    let text = button.Text;

    switch (button.CallToActionButtonType) {
        case HSMTemplateCallToActionButtonTypes.Url:

            text = '<span class="fa fa-external-link" style="margin-right: 3px"></span>' + text;

            if (!onlyText && translationsLoaded) {
                if (button.UrlButtonType === HSMTemplateCallToActionUrlButtonTypes.Fixed) {
                    text += ' (' + $.i18n('configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-fixed') + ')';
                }
                else {
                    text += ' (' + $.i18n('configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-dynamic') + ')';

                    button.UrlParameter.Type = 'button';
                    parameters.push(button.UrlParameter);

                    text += ' - ' + $.i18n('whatsapp-hsmcomposer-buttons-button_parameter') + '<span class="mono" style="margin-left: 3px">' + button.UrlParameter.Name + '=' + button.UrlParameter.Description + '</span>';
                }
            }
            break;
        case HSMTemplateCallToActionButtonTypes.Call:
            text = '<span class="fa fa-phone" style="margin-right: 3px"></span>' + text;
            break;
        case HSMTemplateCallToActionButtonTypes.OfferCode:
            text = '<span class="fa fa-copy" style="margin-right: 3px"></span>' + text;

            button.OfferCodeParameter.Type = 'button';
            parameters.push(button.OfferCodeParameter);
            break;

        case HSMTemplateCallToActionButtonTypes.Flow:
            text = '<span style="margin-right: 3px"></span>' + text;
            button.FlowParameter.Type = 'button';

            if (!onlyText) {
                ConvertFlowDataToParameters(button);
            }
            break;
    }

    return text;
}

function BuildHSMButtonAuthCode(button, parameters) {
    let text = button.Text;

    switch (button.AuthCodeButtonType) {

        case HSMTemplateAuthCodeButtonTypes.AuthCode:
            text = '<span class="fa fa-copy" style="margin-right: 3px"></span>' + text;

            button.AuthCodeParameter.Type = 'button';
            parameters.push(button.AuthCodeParameter);
            break;
    }

    return text;
}

function BuildHSMButtonQuickReply(button, parameters) {
    let text = button.Text;

    button.QuickReplyParameter.Type = 'button';
    parameters.push(button.QuickReplyParameter);

    return text;
}

function BuildHSMButton(button, parameters, onlyText) {

    if (typeof (parameters) === 'undefined' || parameters === null) {
        parameters = [];
    }

    if (typeof (onlyText) !== 'boolean') {
        onlyText = false;
    }

    if (typeof (button.CallToActionButtonType) != 'undefined' &&
        button.CallToActionButtonType != null) {

        return BuildHSMButtonCallToAction(button, parameters, onlyText);
    }
    if (typeof (button.AuthCodeButtonType) != 'undefined' &&
        button.AuthCodeButtonType != null) {

        return BuildHSMButtonAuthCode(button, parameters);
    }
    return BuildHSMButtonQuickReply(button, parameters);
}

function ConvertFlowDataToParameters(button) {
    if (button == null || typeof button == 'undefined')
        return;

    if (button.FlowParameter == null || typeof button.FlowParameter == 'undefined')
        return;

    var $option = $('option:selected', $selectService);
    var service = $option.prop('service');

    var dataToSend = JSON.stringify({
        flowParameter: button.FlowParameter,
        serviceId: service.ID
    });

    $.ajax({
        type: "POST",
        url: "HSMComposer.aspx/GetFlowParametersData",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        data: dataToSend,
        success: function (result) {
            if (result.d.Success) {

                if (typeof (result.d.Parameters) === 'string') {
                    flowScreenData = JSON.parse(result.d.Parameters);

                    if (flowScreenData && Object.keys(flowScreenData).length !== 0) {
                        for (const key in flowScreenData) {

                            let parameter = {}
                            parameter.Name = key;
                            parameter.Type = 'flow';
                            const property = flowScreenData[key];

                            parameter.SubType = property['type'].toLowerCase();

                            let itemExample = '';
                            if (typeof (property['items']) !== 'undefined' && property['items'] != null) {
                                itemExample = JSON.stringify(property['items']);
                            }

                            switch (parameter.SubType) {
                                case 'array':
                                    parameter.Description = property['type'] + " []" + itemExample;
                                    break;
                                case 'object':
                                    parameter.Description = property['type'] + " {}" + itemExample;
                                    break;
                                default:
                                    parameter.Description = property['type'];
                                    break;
                            }

                            parameters.push(parameter);
                            BuildParameters();
                        }
                    }
                }
            }
            else {
                if (typeof (result.d.Error) === 'number') {
                    if (typeof (result.d.Error) === 'number') {
                        AlertDialog($.i18n("whatsapp-hsmmassivecomposer-hsm_template_send"), $.i18n("whatsapp-hsmmassivecomposer-template_sent-error-line_parameter", result.d.Error.ErrorLine, result.d.Error.ParameterIndex + 1), null, null, 'Error');
                    }
                    else {
                        AlertDialog($.i18n("whatsapp-hsmmassivecomposer-hsm_template_send"), $.i18n("whatsapp-hsmmassivecomposer-template_sent-error-line", result.d.Error.ErrorLine), null, null, 'Error');
                    }
                }
                else {
                    AlertDialog($.i18n("whatsapp-hsmmassivecomposer-hsm_template_send"), $.i18n("whatsapp-hsmmassivecomposer-template_sent-error") + result.d.Error.Message, null, null, 'Error');
                }
            }
        },
        error: function (err) {
            AlertDialog($.i18n("whatsapp-hsmmassivecomposer-hsm_template_send"), $.i18n("whatsapp-hsmmassivecomposer-template_sent-error"), null, null, 'Error');
        }
    });
}
function ValidateAndSend() {
    var $option = $('option:selected', $selectService);
    var service = $option.prop('service');

    if (service.IntegrationType == 2) {
        if (maxWhatsappMonthlyHSMToSend !== null && service.Count >= maxWhatsappMonthlyHSMToSend) {
            AlertDialog($.i18n("whatsapp-hsmcomposer-hsm_template_send"), $.i18n("whatsapp-hsmcomposer-max_amount_template_sent"), null, null, 'Warning');
            return;
        }
    }

    var regexNumber = /^[0-9]{8,15}$/;
    var number = $inputDestinationNumber.val();
    if (number.length === 0 || !regexNumber.test(number)) {
        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_phone"));
        ToggleValidator($divError, false);
        return;
    }

    var fullPhoneNumber = $selectDestinationCountry.val() + number;

    var tags = [];
    var selectedTags = $textboxTags.val().trim();
    if (selectedTags.length > 0)
        tags = selectedTags.split(',').map(str => parseInt(str, 10));

    $option = $('option:selected', $selectTemplate);
    var template = $option.prop('template');

    let parameters = {};
    var $inputs = $('input', $tbodyParameters);
    for (var j = 0; j < $inputs.length; j++) {
        var $input = $($inputs.get(j));
        var value = $input.val();
        var parameter = $input.prop('parameter');
        parameters[parameter.Name] = value;
    }

    var $textareas = $('textarea', $tbodyParameters);
    for (var j = 0; j < $textareas.length; j++) {
        var $textarea = $($textareas.get(j));
        var value = $textarea.val();
        var parameter = $textarea.prop('parameter');
        parameters[parameter.Name] = value;
    }

    var hsm = {
        elementName: template.ElementName,
        namespace: template.Namespace,
        language: template.Language,
        header: null,
        body: null,
        buttons: null
    };

    if (typeof (template.HeaderType) === 'number') {
        switch (template.HeaderType) {
            case HSMTemplateHeaderTypes.Text:
                hsm.header = {
                    type: 'text',
                    text: null
                };

                if (template.HeaderTextParameter !== null) {
                    hsm.header.text = {
                        parameter: {
                            name: template.HeaderTextParameter.Name,
                            value: parameters[template.HeaderTextParameter.Name]
                        }
                    };

                    if (typeof (hsm.header.text.parameter.value) !== 'string' ||
                        hsm.header.text.parameter.value.length === 0) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_header_parameter", template.HeaderTextParameter.Name));
                        ToggleValidator($divError, false);
                        return;
                    }

                    if (hsm.header.text.parameter.value.indexOf('    ') >= 0 ||
                        hsm.header.text.parameter.value.indexOf('\t') >= 0 ||
                        hsm.header.text.parameter.value.indexOf('\n') >= 0) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-invalid_characters", template.HeaderTextParameter.Name));
                        ToggleValidator($divError, false);
                        return;
                    }
                }
                break;
            case HSMTemplateHeaderTypes.Media:
                hsm.header = {
                    type: 'media',
                    media: {
                        type: 'none'
                    }
                };

                let sendFrom = parseInt($selectHSMHeaderSendFrom.val(), 10);

                switch (template.HeaderMediaType) {
                    case HSMTemplateHeaderMediaTypes.Document:
                        hsm.header.media.type = 'document';
                        break;
                    case HSMTemplateHeaderMediaTypes.Image:
                        hsm.header.media.type = 'image';
                        break;
                    case HSMTemplateHeaderMediaTypes.Video:
                        hsm.header.media.type = 'video';
                        break;
                    default:
                        break;
                }

                if (sendFrom === 1) {
                    hsm.header.media[hsm.header.media.type] = {
                        filename: $inputHSMHeaderMediaName.val(),
                        url: $inputHSMHeaderSendFromUrl.val(),
                        isPublic: $inputHSMHeaderSendFromUrlIsPublic.is(':checked')
                    };

                    if (hsm.header.media[hsm.header.media.type].url === null ||
                        hsm.header.media[hsm.header.media.type].url.length === 0) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_header_media_url"));
                        ToggleValidator($divError, false);
                        return;
                    }

                    if (hsm.header.media[hsm.header.media.type].filename === null ||
                        hsm.header.media[hsm.header.media.type].filename.length === 0) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_header_media_filename"));
                        ToggleValidator($divError, false);
                        return;
                    }
                }
                else {
                    let selectedFile = $divHSMHeaderSendFromFileContainer.getFile();
                    if (selectedFile === null) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_upload_file"));
                        ToggleValidator($divError, false);
                        return;
                    }

                    switch (template.HeaderMediaType) {
                        case HSMTemplateHeaderMediaTypes.Document:
                            if (selectedFile.mimeType.toLowerCase() !== 'application/pdf') {
                                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_upload_file-pdf"));
                                ToggleValidator($divError, false);
                                return;
                            }
                            break;
                        case HSMTemplateHeaderMediaTypes.Image:
                            if (selectedFile.mimeType.toLowerCase() !== 'image/jpeg' &&
                                selectedFile.mimeType.toLowerCase() !== 'image/jpg' &&
                                selectedFile.mimeType.toLowerCase() !== 'image/png') {
                                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_upload_file-image"));
                                ToggleValidator($divError, false);
                                return;
                            }
                            break;
                        case HSMTemplateHeaderMediaTypes.Video:
                            if (selectedFile.mimeType.toLowerCase() !== 'video/mp4') {
                                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_upload_file-video"));
                                ToggleValidator($divError, false);
                                return;
                            }
                            break;
                        default:
                            break;
                    }

                    hsm.header.media[hsm.header.media.type] = {
                        file: {
                            mimeType: selectedFile.mimeType,
                            name: selectedFile.fileName,
                        },
                        filename: $inputHSMHeaderMediaName.val(),
                    };

                    if (hsm.header.media[hsm.header.media.type].filename === null ||
                        hsm.header.media[hsm.header.media.type].filename.length === 0) {
                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_header_media_filename"));
                        ToggleValidator($divError, false);
                        return;
                    }
                }

                break;
            case HSMTemplateHeaderTypes.Location: {
                let latitude = parseFloat($inputHSMHeaderLocationLatitude.val());
                let longitude = parseFloat($inputHSMHeaderLocationLongitude.val());

                if (isNaN(latitude) || latitude < -90 || latitude > 90) {
                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-header-location-latitude-invalid"));
                    ToggleValidator($divError, false);
                    return;
                }

                if (isNaN(longitude) || longitude < -180 || longitude > 180) {
                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-header-location-longitude-invalid"));
                    ToggleValidator($divError, false);
                    return;
                }

                let name = $inputHSMHeaderLocationName.val();
                if (name.length === 0) {
                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-header-location-name-invalid"));
                    ToggleValidator($divError, false);
                    return;
                }

                let address = $inputHSMHeaderLocationAddress.val();
                if (address.length === 0) {
                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-header-location-address-invalid"));
                    ToggleValidator($divError, false);
                    return;
                }

                hsm.header = {
                    type: 'location',
                    location: {
                        latitude: latitude,
                        longitude: longitude,
                        name: name,
                        address: address
                    }
                };
            } break;
        }
    }

    if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
        hsm.body = {};

        for (var i = 0; i < template.TemplateParameters.length; i++) {
            let parameter = parameters[template.TemplateParameters[i].Name];
            if (typeof (parameter) !== 'string' ||
                parameter.length === 0 ||
                parameter.trim().length === 0) {
                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_parameters"));
                ToggleValidator($divError, false);
                return;
            }

            if (parameter.indexOf('    ') >= 0 ||
                parameter.indexOf('\t') >= 0 ||
                parameter.indexOf('\n') >= 0) {
                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-invalid_characters", template.TemplateParameters[i].Name));
                ToggleValidator($divError, false);
                return;
            }

            hsm.body[template.TemplateParameters[i].Name] = parameter;
        }
    }

    if (typeof (template.ButtonsType) === 'number') {

        if (template.ButtonsType != HSMTemplateButtonsTypes.None) {

            hsm.buttons = [];
            for (var i = 0; i < template.Buttons.length; i++) {

                let button;

                switch (template.ButtonsType) {
                    case HSMTemplateButtonsTypes.Mixed:
                    case HSMTemplateButtonsTypes.QuickReply:
                        if (template.Buttons[i].QuickReplyParameter != null) {
                            button = {
                                type: 'quick_reply',
                                index: i,
                                payload: null,
                                parameter: {}
                            };

                            button.parameter[template.Buttons[i].QuickReplyParameter.Name] = parameters[template.Buttons[i].QuickReplyParameter.Name];

                            if (typeof (button.parameter[template.Buttons[i].QuickReplyParameter.Name]) !== 'string' ||
                                button.parameter[template.Buttons[i].QuickReplyParameter.Name].length === 0) {
                                $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_button_parameter", template.Buttons[i].QuickReplyParameter.Name, template.Buttons[i].Text));
                                ToggleValidator($divError, false);
                                return;
                            }
                            break;
                        }
                    case HSMTemplateButtonsTypes.AuthCode:

                        if (template.Buttons[i].AuthCodeButtonType != null) {
                            switch (template.Buttons[i].AuthCodeButtonType) {
                                case HSMTemplateAuthCodeButtonTypes.AuthCode:
                                    button = {
                                        type: 'url',
                                        index: i,
                                        parameter: {}
                                    };

                                    button.parameter[template.Buttons[i].AuthCodeParameter.Name] = parameters[template.Buttons[i].AuthCodeParameter.Name];

                                    if (typeof (button.parameter[template.Buttons[i].AuthCodeParameter.Name]) !== 'string' ||
                                        button.parameter[template.Buttons[i].AuthCodeParameter.Name].length === 0) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_button_parameter", template.Buttons[i].AuthCodeParameter.Name, template.Buttons[i].Text));
                                        ToggleValidator($divError, false);
                                        return;
                                    }

                                    if (button.parameter[template.Buttons[i].AuthCodeParameter.Name].length > 15) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-max_length_button", template.Buttons[i].AuthCodeParameter.Name));
                                        ToggleValidator($divError, false);
                                        return;
                                    }

                                    if (button.parameter[template.Buttons[i].AuthCodeParameter.Name].match(/\s/g)) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-button_spaces", template.Buttons[i].AuthCodeParameter.Name));
                                        ToggleValidator($divError, false);
                                        return;
                                    }
                                    break;
                            }
                            break;
                        }

                    case HSMTemplateButtonsTypes.Mixed:
                    case HSMTemplateButtonsTypes.CallToAction:

                        if (template.Buttons[i].CallToActionButtonType != null) {
                            switch (template.Buttons[i].CallToActionButtonType) {
                                case HSMTemplateCallToActionButtonTypes.Url:
                                    if (template.Buttons[i].UrlButtonType === HSMTemplateCallToActionUrlButtonTypes.Fixed) {
                                        button = {
                                            type: 'url',
                                            sub_type: 'fixed',
                                            index: i
                                        };
                                    }
                                    else {
                                        button = {
                                            type: 'url',
                                            sub_type: 'dynamic',
                                            index: i,
                                            parameter: {}
                                        };

                                        button.parameter[template.Buttons[i].UrlParameter.Name] = parameters[template.Buttons[i].UrlParameter.Name];

                                        if (typeof (button.parameter[template.Buttons[i].UrlParameter.Name]) !== 'string' ||
                                            button.parameter[template.Buttons[i].UrlParameter.Name].length === 0) {
                                            $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_button_parameter", template.Buttons[i].UrlParameter.Name, template.Buttons[i].Text));
                                            ToggleValidator($divError, false);
                                            return;
                                        }
                                    }
                                    break;
                                case HSMTemplateCallToActionButtonTypes.Call:
                                    button = {
                                        type: 'call',
                                        index: i
                                    };
                                    break;
                                case HSMTemplateCallToActionButtonTypes.OfferCode:
                                    button = {
                                        type: 'offer',
                                        index: i,
                                        parameter: {}
                                    };

                                    button.parameter[template.Buttons[i].OfferCodeParameter.Name] = parameters[template.Buttons[i].OfferCodeParameter.Name];

                                    if (typeof (button.parameter[template.Buttons[i].OfferCodeParameter.Name]) !== 'string' ||
                                        button.parameter[template.Buttons[i].OfferCodeParameter.Name].length === 0) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-must_enter_button_parameter", template.Buttons[i].OfferCodeParameter.Name, template.Buttons[i].Text));
                                        ToggleValidator($divError, false);
                                        return;
                                    }

                                    if (button.parameter[template.Buttons[i].OfferCodeParameter.Name].length > 15) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-max_length_button", template.Buttons[i].OfferCodeParameter.Name));
                                        ToggleValidator($divError, false);
                                        return;
                                    }

                                    if (button.parameter[template.Buttons[i].OfferCodeParameter.Name].match(/\s/g)) {
                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-button_spaces", template.Buttons[i].OfferCodeParameter.Name));
                                        ToggleValidator($divError, false);
                                        return;
                                    }
                                    break;

                                case HSMTemplateCallToActionButtonTypes.Flow:
                                    button = {
                                        type: 'flow',
                                        index: i,
                                        parameter: {}
                                    };

                                    var flowParameter = {};
                                    if (flowScreenData && Object.keys(flowScreenData).length !== 0) {
                                        for (const key in flowScreenData) {

                                            let screenParameter = {}
                                            screenParameter.Name = key;
                                            const property = flowScreenData[key];
                                            screenParameter.SubType = property['type'].toLowerCase();

                                            if (typeof (screenParameter) == 'object' && screenParameter != null) {

                                                if (typeof (screenParameter.SubType) == 'string') {
                                                    try {
                                                        switch (screenParameter.SubType) {
                                                            case 'object':
                                                                if (!IsValidObject(parameters[key])) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-not-object", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                let parsedValue = JSON.parse(parameters[key]);
                                                                if ((typeof parsedValue !== 'object' && parsedValue !== null)) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-not-object", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                if (Object.keys(parsedValue).length == 0) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-empty-object", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                parameters[key] = parsedValue;

                                                                break;
                                                            case 'string':
                                                                if (typeof (parameters[key]) !== 'string' || parameters[key].length == 0) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-empty-string", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }
                                                                break;
                                                            case 'array':
                                                                if (!IsValidObject(parameters[key])) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-not-array", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                let parsedArrayValue = JSON.parse(parameters[key]);
                                                                if (!Array.isArray(parsedArrayValue)) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-not-array", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                if (parsedArrayValue.length == 0) {
                                                                    $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-is-empty-array", key));
                                                                    ToggleValidator($divError, false);
                                                                    return;
                                                                }

                                                                parameters[key] = parsedArrayValue;
                                                                break;
                                                        }

                                                    } catch (error) {
                                                        $('div', $divError).text($.i18n("whatsapp-hsmcomposer-parameter-not-valid-flows-paramsy", key));
                                                        ToggleValidator($divError, false);
                                                        return;
                                                    }

                                                }

                                                flowParameter[key] = parameters[key];
                                            }
                                        }
                                        button.parameter[template.Buttons[i].FlowParameter.Name] = flowParameter;
                                    }
                                    break;
                            }
                        }
                        break;
                    default:
                        break;
                }

                hsm.buttons.push(button);
            }
        }
    }

    ToggleValidator($divError, true);

    let sendHSMIfCaseOpenAnyways = false;
    if ($selectSendHsmAnyways.length > 0) {
        sendHSMIfCaseOpenAnyways = $selectSendHsmAnyways.val() !== '0';
    }
    let validateDoNotCallList = $checkboxDoNotCall.is(":checked");


    let data = {
        serviceId: service.ID,
        userId: loggedUserID,
        text: null,
        hsm: hsm,
        phoneNumber: fullPhoneNumber,
        tags: tags,
        sendHSMIfCaseOpenAnyways: sendHSMIfCaseOpenAnyways,
        validateDoNotCallList: validateDoNotCallList
    };

    /*if (data.hsm.header === null &&
        data.hsm.buttons === null) {
        let hsm = data.hsm;
        delete data.hsm;

        data.parameters = {
            templateData: null,
            templateName: hsm.elementName,
            templateNamespace: hsm.namespace,
            language: hsm.language
        };

        if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
            data.parameters.templateData = [];
            for (var i = 0; i < template.TemplateParameters.length; i++) {
                let parameter = parameters[template.TemplateParameters[i].Name];
                data.parameters.templateData.push(parameter);
            }
        }
    }*/

    LoadingDialog({
        title: $.i18n("whatsapp-hsmcomposer-title"),
        onClose: function () {
            var dataToSend = JSON.stringify(data);

            $.ajax({
                type: "POST",
                url: "../Services/Messaging/SendWhatsapp",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: dataToSend,
                async: false,
                success: function (result) {
                    if (result.Success) {
                        service.Count++;
                        $spanServiceInfoTotalSent.text(service.Count);
                        AlertDialog($.i18n("whatsapp-hsmcomposer-title"), $.i18n("whatsapp-hsmcomposer-template_shipping_scheduled"));
                    }
                    else {
                        AlertDialog($.i18n("whatsapp-hsmcomposer-title"), $.i18n("whatsapp-hsmcomposer-error_sending_template"), null, null, 'Error');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    var error = JSON.parse(jqXHR.responseText);
                    if (error.ErrorNumber == 272)
                        AlertDialog($.i18n("whatsapp-hsmcomposer-title"), $.i18n("whatsapp-hsmcomposer-ignored_template"), null, null, 'Warning');
                    else if (error.ErrorNumber == 273)
                        AlertDialog($.i18n("whatsapp-hsmcomposer-title"), $.i18n("whatsapp-hsmcomposer-ignored_template_donotcall"), null, null, 'Warning');
                    else
                        AlertDialog($.i18n("whatsapp-hsmcomposer-title"), $.i18n("whatsapp-hsmcomposer-error_sending_template"), null, null, 'Error');
                }
            });
        },
        timeout: 500
    });
}

function IsValidObject(value) {
    try {
        return JSON.parse(value);
    }
    catch {
        return false;
    }
}

function FormatMarkdown(text) {
    function parseEmoji(text) {
        if (typeof (twemoji) !== 'undefined' && twemoji !== null) {
            if (typeof (twemoji.parse) === 'function') {
                text = twemoji.parse(text);
            }
        }

        return text;
    }

    if (typeof (showdown) !== 'undefined') {
        if (markdownConverter === null) {
            markdownConverter = new showdown.Converter({
                simplifiedAutoLink: true,
                simpleLineBreaks: true,
                literalMidWordUnderscores: true
            });
        }

        text = parseEmoji(text);
        text = markdownConverter.makeHtml(text);
    }
    else {
        text = text.replaceAll('\n', '<br />')
    }

    return text;
}