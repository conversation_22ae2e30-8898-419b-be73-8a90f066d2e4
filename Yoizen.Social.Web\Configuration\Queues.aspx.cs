﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Common;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using static Yoizen.Social.DomainModel.ServiceSettings.MailSettings;

namespace Yoizen.Social.Web.Configuration
{
	public partial class Queues : LoginRequiredBasePage
	{
		#region Constants

		private const string OptionNotConfigured = @"El sistema no permite configurar esta opción";

		private const string VideoMessageDefaultText = @"Hola [Perfil], únete a la videollamada aquí: [Link]. ¡Estoy disponible para ayudarte!";

        #endregion

        #region Propiedades

        protected override string RedirectUrl
		{
			get
			{
				if (Request.QueryString.ToString().Equals("Startup", StringComparison.InvariantCultureIgnoreCase))
					return "~/Configuration/Queues.aspx?Startup";
				return "~/Configuration/Queues.aspx";
			}
		}

		protected override string PageDescription { get { return "Colas"; } }

		protected override string PageDescriptionLocalizationKey { get { return "configuration-queues-title"; } }

		public bool EnListado
		{
			get { return (bool) ViewState["EnListado"]; }
			set { ViewState["EnListado"] = value; }
		}

		public QueueSurveyConfiguration SurveySelected
		{
			get { return (QueueSurveyConfiguration) ViewState["SurveySelected"]; }
			set { ViewState["SurveySelected"] = value; }
		}
		public bool Nuevo
		{
			get { return (bool) ViewState["Nuevo"]; }
			set { ViewState["Nuevo"] = value; }
		}

		public int EntityID
		{
			get { return (int) ViewState["ID"]; }
			set { ViewState["ID"] = value; }
		}

		public string SurveyList
		{
			get { return (string) ViewState["SurveyList"]; }
			set { ViewState["SurveyList"] = value; }
		}

		public Entities Entities
		{
			get { return (Entities) ViewState["Entities"]; }
			set { ViewState["Entities"] = value; }
		}

		public Entities EditEntities
		{
			get { return (Entities) ViewState["EditEntities"]; }
			set { ViewState["EditEntities"] = value; }
		}

		#endregion
		
		protected async void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			bool canConfigureQueues = true;

			if (Helpers.StartupHelper.ShouldConfigureAgents)
			{
				panelContent.Visible = false;
				messageNoAgents.Visible = true;
				canConfigureQueues = false;
			}

			if (Helpers.StartupHelper.ShouldConfigureSupervisors)
			{
				panelContent.Visible = false;
				messageNoSupervisors.Visible = true;
				canConfigureQueues = false;
			}

			if (Helpers.StartupHelper.ShouldConfigureTags)
			{
				panelContent.Visible = false;
				messageNoTags.Visible = true;
				canConfigureQueues = false;
			}

			if (!canConfigureQueues)
				return;

			var license = Licensing.LicenseManager.Instance.License.Configuration;
			var settings = DomainModel.SystemSettings.Instance;

			if (!IsPostBack)
			{
				this.Entities = DomainModel.Entities.None;
				this.EnListado = true;

				ValidateSettings();

				buttonNew.Parent.Visible = this.LoggedUser.HasPermission(Permissions.QueuesEdition);

				bool hasPermissionForQueuesEdition = this.LoggedUser.HasPermission(Permissions.QueuesEdition);
				bool hasPermissionForQueuesSupervisors = this.LoggedUser.HasAnyPermissions(Permissions.QueueSupervisors);
				bool hasPermissionForQueuesTags = this.LoggedUser.HasAnyPermissions(Permissions.QueueTags);
				bool hasPermissionForQueuesTagGroups = this.LoggedUser.HasAnyPermissions(Permissions.QueueTags);
				bool hasPermissionForServiceLevel = this.LoggedUser.HasPermission(Permissions.QueueServiceLevel);
				bool hasPermissionForSurveys = this.LoggedUser.HasPermission(Permissions.QueueSurveys);

				liTabBehaviour.Visible = hasPermissionForQueuesEdition;
				divQueueBehaviour.Visible = hasPermissionForQueuesEdition;
				liTabServiceLevel.Visible = hasPermissionForQueuesEdition || hasPermissionForServiceLevel;
				divQueueServiceLevel.Visible = hasPermissionForQueuesEdition || hasPermissionForServiceLevel;
				liTabUsers.Visible = hasPermissionForQueuesEdition || hasPermissionForQueuesSupervisors;
				divQueueUsers.Visible = hasPermissionForQueuesEdition || hasPermissionForQueuesSupervisors;
				liTabTags.Visible = hasPermissionForQueuesEdition || hasPermissionForQueuesTags;
				divQueueTags.Visible = hasPermissionForQueuesEdition || hasPermissionForQueuesTags;
				liTabSurveys.Visible = hasPermissionForSurveys || hasPermissionForQueuesEdition;
				panelQueueSurveys.Visible = hasPermissionForQueuesEdition || hasPermissionForSurveys;
				liTabEwt.Visible = true;
				liTabAutomaticActions.Visible = true;
				liTabVideo.Visible = license.AllowAgentsToStartVideoCall;
				panelVideo.Visible = license.AllowAgentsToStartVideoCall;

				if (license.AllowAgentsToStartVideoCall && ValidateVideoCubiqSystemSettings())
				{
					var projects = await Core.System.Instance.VideoService.GetAllProjects();
					if(projects != null)
					{
						dropdownlistVideoProject.DataSource = projects;
						dropdownlistVideoProject.DataBind();
						ErrorMessageVideo.Visible = false;
					}
					else 
					{
						panelVideo.Visible = false;
						ErrorMessageVideo.Visible = true;
					}
					
					textboxVideoDefaultText.Text = VideoMessageDefaultText;
                }

                if (license.SurveysEnabled && settings.EnableSurveys)
				{
					IEnumerable<Survey> surveys = DomainModel.Cache.Instance.GetList<DomainModel.Survey>().Where(s => s.Enabled);
					if (surveys.Any())
					{
						panelEnableSurveys.Visible = true;
						messageNoQueueSurveys.Visible = false;

						var sorteredSurveys = surveys.OrderBy(s => s.Name).Select(s => new
						{
							s.ID,
							s.Name,
							s.Type
						});

						dropdownQueueSurvey.DataSource = sorteredSurveys;
						dropdownQueueSurvey.DataBind();
						
						this.RegisterJsonVariable("availableSurveys", sorteredSurveys);
					}
					else
					{
						panelEnableSurveys.Visible = false;
						placeholderCopySurveys.Visible = false;
						messageNoQueueSurveys.Visible = true;
					}
				}
				else
				{
					liTabSurveys.Visible = false;
					divQueueSurveys.Visible = false;
					placeholderCopySurveys.Visible = false;
				}

				panelReturnMessagesToQueue.Visible = license.AllowAgentsToReturnMessagesToQueue;
				checkboxAllowAgentsToReturnMessagesToQueue.Enabled = settings.AllowAgentsToReturnMessagesToQueue;
				panelAllowAgentsToReturnMessagesToSpecifiedQueue.Visible = license.AllowAgentsToReturnMessagesToSpecifiedQueue;
				checkboxAllowAgentsToSelectQueueOnReturnToQueue.Enabled = settings.AllowAgentsToSelectQueueOnReturnToQueue;
				placeholderQueueWorkingHoursForReceivingMessages.Visible = license.AllowAgentsToReturnMessagesToQueue ||
					license.AllowAgentsToReturnMessagesToSpecifiedQueue;

				LoadAutoReplyPanels();

				if (hasPermissionForQueuesEdition || hasPermissionForQueuesSupervisors)
					this.Entities |= DomainModel.Entities.Users;
				if (hasPermissionForQueuesEdition || hasPermissionForQueuesTags)
					this.Entities |= DomainModel.Entities.Tags;
				if (hasPermissionForQueuesEdition && hasPermissionForQueuesTagGroups)
					this.Entities |= DomainModel.Entities.TagGroups;

				this.RegisterJsonVariable("surveysEnabled", license.SurveysEnabled && settings.EnableSurveys);
			}
			else
			{
				if (license.SurveysEnabled && settings.EnableSurveys)
				{
					this.RegisterJsonVariable("surveysEnabled", true);
					IEnumerable<Survey> surveys = DomainModel.Cache.Instance.GetList<DomainModel.Survey>().Where(s => s.Enabled);
					if (surveys.Any())
					{
						var sorteredSurveys = surveys.OrderBy(s => s.Name).Select(s => new
						{
							s.ID,
							s.Name,
							s.Type
						});

						this.RegisterJsonVariable("availableSurveys", sorteredSurveys);
					}
				}
				else
				{
					this.RegisterJsonVariable("surveysEnabled", false);
				}
			}

			IEnumerable<Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			this.RegisterJsonVariable("services", BuildServices(services));
		}

		#region Private Members

		private static dynamic BuildServices(IEnumerable<Service> services)
		{
			if (services == null)
				return null;

			return (from service
						in services
					select BuildService(service));
		}

		private static dynamic BuildService(Service service)
		{
			if (service == null)
				return null;

			return new
			{
				ID = service.ID,
				Name = service.Name,
				Type = service.Type,
				SocialServiceType = service.SocialServiceType
			};
		}

		private static object BuildQueues(IEnumerable<Queue> queues, IEnumerable<Queue> allQueues, IEnumerable<Service> allServices, IEnumerable<DomainModel.QueueGroup> allQueueGroups, List<string> fields)
		{
			if (queues == null || !queues.Any())
				return null;

			return queues.Where(q => q != null).Select(q => BuildQueue(q, allQueues, allServices, allQueueGroups, fields));
		}

		private static object BuildQueue(Queue queue, IEnumerable<Queue> allQueues, IEnumerable<Service> allServices, IEnumerable<DomainModel.QueueGroup> allQueueGroups, List<string> fields, bool returnDetailed = false)
		{
			var withSl = queue.ServiceLevelParameters.ContainsKey(ServiceLevelTypes.ServiceLevel) &&
				queue.ServiceLevelParameters[ServiceLevelTypes.ServiceLevel].Seconds > 0;
			var withSurveys = queue.SurveyEnabled;
			var surveysCount = queue.SurveyList?.Count ?? 0;

			var withAutomaticActions = queue.HasAutomaticActionsConfigured;

            IDictionary<DomainModel.Queue.QueueConnectionFromServicesTypes, IEnumerable<int>> relatedServicesByType = null;
			IEnumerable<int> relatedServices = null;
			if (returnDetailed)
				relatedServicesByType = queue.GetServicesConnectionsByType(allServices);
			else
				relatedServices = queue.GetServicesConnections(allServices);

			IDictionary<DomainModel.Queue.QueueConnectionFromQueuesTypes, IEnumerable<int>> relatedQueuesByType = null;
			IEnumerable<int> relatedQueues = null;
			if (returnDetailed)
				relatedQueuesByType = queue.GetQueuesConnectionsByType(allQueues);
			else
				relatedQueues = queue.GetQueuesConnections(allQueues);

			var relatedQueuesThatTransferToThisQueue = new List<int>();
			if (allQueues != null)
			{
				try
				{
					var filteredQueues = allQueues.Where(q => q.RelatedQueuesToReturnMessages != null && q.RelatedQueuesToReturnMessages.Contains(queue));
					if (filteredQueues != null && filteredQueues.Any())
					{
						var queueIDs = filteredQueues.Select(s => s.ID);
						if (queueIDs != null && queueIDs.Any())
						{
							relatedQueuesThatTransferToThisQueue.AddRange(queueIDs);
						}
					}
				}
				catch { }
			}

			var socialQueue = Core.System.Instance.QueueService.GetQueueById(queue.ID);

			var messages = socialQueue.Count;
			var subscribedAgents = socialQueue.TotalSubscribedAgents;

			var relatedQueueGroups = allQueueGroups.Where(qg => qg.Queues != null && qg.Queues.Contains(queue)).Select(q => new { q.ID, q.Name });

			var cannotBeEnabled = false;
			var cannotBeDisabled = false;
			if (!queue.Enabled)
			{
				if (!string.IsNullOrEmpty(queue.Key))
				{
					if (allQueues.Any(q => q.ID != queue.ID &&
						q.Enabled &&
						(
							(!string.IsNullOrEmpty(q.Key) && q.Key.Equals(queue.Key, StringComparison.InvariantCultureIgnoreCase)) ||
							q.Name.Equals(queue.Name, StringComparison.InvariantCultureIgnoreCase)
						)))
					{
						cannotBeEnabled = true;
					}
				}
			}
			else
			{
				if (subscribedAgents > 0 || messages > 0 || 
					(relatedQueues != null && relatedQueues.Any()) || 
					(relatedQueuesByType != null && relatedQueuesByType.Count > 0) ||
					(relatedServices != null && relatedServices.Any()) || 
					(relatedServicesByType != null && relatedServicesByType.Count > 0) || 
					relatedQueueGroups.Any())
					cannotBeDisabled = true;
			}

			var canBeDeleted = Core.System.Instance.QueueService.CanDeleteQueue(queue, allQueues, allServices, allQueueGroups);

			if (fields != null && fields.Count == 0)
			{
				fields.Add("ID");
				fields.Add("Name");
				fields.Add("Description");
				fields.Add("Key");
				fields.Add("Enabled");
				fields.Add("Agents");
				fields.Add("RelatedServices");
				fields.Add("RelatedQueues");
				fields.Add("RelatedQueuesThatTransferToThisQueue");
				fields.Add("WithSL");
				fields.Add("WithSurveys");
				fields.Add("SurveysCount");
				fields.Add("Messages");
				fields.Add("SubscribedAgents");
				fields.Add("CannotBeEnabled");
				fields.Add("CannotBeDisabled");
				fields.Add("CanBeDeleted");
				fields.Add("RelatedQueueGroups");
				fields.Add("Transfer");
				fields.Add("Return");
				fields.Add("WithAutomaticActions");
			}

			return new object[]
			{
				queue.ID,
				queue.Name,
				queue.Description,
				queue.Key,
				queue.Enabled,
				returnDetailed ? (object) BuildAgents(queue.Agents) : (object) queue.Agents.Count(),
				returnDetailed ? (object) BuildRelatedServices(relatedServicesByType) : (object) relatedServices.Count(),
				returnDetailed ? (object) BuildRelatedQueues(relatedQueuesByType) : (object) relatedQueues.Count(),
				returnDetailed ? (object) relatedQueuesThatTransferToThisQueue.Distinct() : (object) relatedQueuesThatTransferToThisQueue.Distinct().Count(),
				withSl,
				withSurveys,
				surveysCount,
				messages,
				subscribedAgents,
				cannotBeEnabled,
				cannotBeDisabled,
				canBeDeleted,
				returnDetailed ? (object) relatedQueueGroups : (object) relatedQueueGroups.Count(),
				queue.AllowAgentsToSelectQueueOnReturnToQueue,
				queue.AllowAgentsToReturnMessagesToQueue,
                withAutomaticActions
            };
		}

		private static object BuildRelatedQueues(IDictionary<Queue.QueueConnectionFromQueuesTypes, IEnumerable<int>> relatedQueuesByType)
		{
			if (relatedQueuesByType == null || relatedQueuesByType.Count == 0)
				return null;

			return relatedQueuesByType.Select(kvp => new
			{
				Key = kvp.Key,
				Value = kvp.Value
			});
		}

		private static object BuildRelatedServices(IDictionary<Queue.QueueConnectionFromServicesTypes, IEnumerable<int>> relatedServicesByType)
		{
			if (relatedServicesByType == null || relatedServicesByType.Count == 0)
				return null;

			return relatedServicesByType.Select(kvp => new
			{
				Key = kvp.Key,
				Value = kvp.Value
			});
		}

		private static object BuildAgents(IEnumerable<Agent> agents)
		{
			if (agents == null)
				return null;

			return agents.Select(a => BuildAgent(a));
		}

		private static object BuildAgent(Agent agent)
		{
			return new
			{
				ID = agent.ID,
				FullName = agent.FullName,
				Username = agent.UserName
			};
		}

		private static object BuildTags(IEnumerable<Tag> tags)
		{
			if (tags == null || !tags.Any())
				return null;

			return tags.Select(t => BuildTag(t));
		}

		private static object BuildTag(Tag tag)
		{
			return new
			{
				tag.ID,
				tag.FullName
			};
		}

		private static object BuildTagsByIds(List<int> tagsIds)
		{
			if (tagsIds == null || tagsIds.Count == 0)
				return null;

            IEnumerable<DomainModel.Tag> tags = DomainModel.Cache.Instance.GetList<DomainModel.Tag>().Where(tag => tagsIds.Contains(tag.ID));

			return BuildTags(tags);
        }

		private static object BuildTagGroups(IEnumerable<TagGroup> tagGroups)
		{
			return tagGroups.Select(tagGroup => BuildTagGroup(tagGroup));
		}

		private static object BuildTagGroup(TagGroup tagGroup)
		{
			if (tagGroup == null)
				return null;

			return new
			{
				ID = tagGroup.ID,
				Name = tagGroup.Name
			};
		}

		private bool ValidateUser()
		{
			if (!this.LoggedUser.HasAnyPermissions(Permissions.QueuesEdition, Permissions.QueueAgents, Permissions.QueueSupervisors, Permissions.QueueTags))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private void ValidateSettings()
		{

			Licensing.ConfigurationType configuration = Licensing.LicenseManager.Instance.License.Configuration;
			DomainModel.SystemSettings settings = DomainModel.SystemSettings.Instance;

			// --> AllowAgentsToReturnMessagesToQueue
			ValidateOption(configuration.AllowAgentsToReturnMessagesToQueue,
				settings.AllowAgentsToReturnMessagesToQueue,
				panelReturnMessagesToQueue,
				checkboxAllowAgentsToReturnMessagesToQueue);
			ValidateOption(configuration.AllowAgentsToReturnMessagesToSpecifiedQueue,
				settings.AllowAgentsToSelectQueueOnReturnToQueue,
				panelAllowAgentsToReturnMessagesToSpecifiedQueue,
				checkboxAllowAgentsToSelectQueueOnReturnToQueue);
		}

		private void ValidateOption(bool licensePermission, bool domainPermission, Control container, CheckBox checkbox)
		{
			if (!licensePermission)
			{
				container.Visible = false;
			}
			else if (!domainPermission)
			{
				checkbox.Enabled = false;
				checkbox.ToolTip = OptionNotConfigured;
			}
		}

		/// <summary>
		/// Devuelve si la cola especificada tiene agentes conectados
		/// </summary>
		/// <param name="queue">La <see cref="DomainModel.Queue"/> que se desea verificar</param>
		/// <returns>true si la cola tiene algún agente logueado; en caso contrario, false</returns>
		private bool QueueHasConnectedAgents(DomainModel.Queue queue)
		{
			if (Core.System.Instance.AgentsService != null && Core.System.Instance.AgentsService.ConnectedAgents != null && Core.System.Instance.AgentsService.ConnectedAgents.Length > 0)
			{
				foreach (var agent in Core.System.Instance.AgentsService.ConnectedAgents)
				{
					if (agent.Queues.Contains(queue))
						return true;
				}
			}

			return false;
		}

		private void CambiarModo()
		{
			if (this.EnListado)
			{
				panelEdition.Visible = false;
				panelListado.Visible = true;
			}
			else
			{
				panelEdition.Visible = true;
				panelListado.Visible = false;

				if ((this.EditEntities & DomainModel.Entities.Users) == DomainModel.Entities.Users)
				{
					IEnumerable<DomainModel.User> supervisors = DomainModel.Cache.Instance.GetList<DomainModel.User>();
					supervisors = supervisors.Where(u => u.IsSupervisor && !u.Deleted && u.ID != 1);
					listboxSupervisors.DataSource = supervisors.OrderBy(a => a.FullName).Select(q => new
					{
						q.ID,
						q.Description
					});
					listboxSupervisors.DataBind();

					IEnumerable<DomainModel.User> otherUsers = DomainModel.Cache.Instance.GetList<DomainModel.User>();
					otherUsers = otherUsers.Except(supervisors).Where(u => !u.Deleted && u.ID != 1);
					listboxUsers.DataSource = otherUsers.OrderBy(a => a.FullName).Select(q => new
					{
						q.ID,
						q.Description
					});
					listboxUsers.DataBind();

					var auxReasons = DAL.AuxReasonDAO.GetAll();
					listboxDontReserveWithStatus.DataSource = auxReasons.OrderBy(s => s.ID).Select(s => new
					{
						s.ID,
						s.Name
					});
					listboxDontReserveWithStatus.DataBind();
				}

				if (this.Nuevo && !hiddenActionName.Value.Equals("CopyQueue"))
				{
					textboxName.Text = string.Empty;
					textboxDescription.Text = string.Empty;
					textboxKey.Text = string.Empty;
					textboxMinutesToWaitForAgent.Text = "15";
					textboxMaxReservedMessagesPerAgent.Text = "0";
					dropdownlistMailSignatureBehaviour.SelectedValue = "0";
					textboxMailSignature.Text = string.Empty;
					dropdownlistReserveConditions.SelectedValue = "1";
					checkboxDeleteReservedWithStatus.Checked = false;
					checkboxRenewReserveTimeWhenGrouping.Checked = true;
					textboxQueueServiceLevelSeconds.Text = "0";
					textboxAgentIdleMinutes.Text = "0";
					textboxFinalUserIdleMinutes.Text = "0";
					checkboxAgentIdleReturnToQueue.Checked = true;
					checkboxSLMinutesActionsAssignToQueue.Checked = false;
					checkboxSLMinutesActionsNotify.Checked = false;
					checkboxSLMinutesActionsDiscard.Checked = false;
					checkboxSLMinutesActionsAutoReply.Checked = false;
					textboxSLMinutesActionsAutoReply.Text = string.Empty;
					checkboxSLMinutesActionsAutoReplyIfAlreadyForCase.Checked = true;
					checkboxSLMinutesActionsAddTags.Checked = false;
					checkboxSLChatNotify.Checked = false;
					dropdownlistSLChatNotifyAndFinish.SelectedValue = "0";
					textboxSLChatNotifyText.Text = string.Empty;
					textboxQueueServiceLevelExpired.Text = "0";
					checkboxSLMinutesExpiredActionsAssignToQueue.Checked = false;
					checkboxSLMinutesExpiredActionsNotify.Checked = false;
					checkboxSLMinutesExpiredActionsDiscard.Checked = false;
					checkboxSLMinutesExpiredActionsAutoReply.Checked = false;
					textboxSLMinutesExpiredActionsAutoReply.Text = string.Empty;
					checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase.Checked = true;
					checkboxSLMinutesExpiredActionsAddTags.Checked = false;
					checkboxSLExpiredChatNotify.Checked = false;
					dropdownlistSLExpiredChatNotifyAndFinish.SelectedValue = "0";
					textboxSLExpiredChatNotifyText.Text = string.Empty;
					checkboxChatAvailableDaysAndTimes.Checked = false;
					checkboxEnableSurveys.Checked = false;
					textboxQueueSurveyExpiration.Text = string.Empty;
					textboxQueueSurveyInvitation.Text = string.Empty;
					textboxQueueSurveySentRate.Text = "100";
					textboxQueueSurveyTimeToSend.Text = "24";
					textboxQueueSurveyMessagesCount.Text = "0";
					textboxQueueSurveyCaseDuration.Text = "0";
					dropdownlistQueueSurveyWithAgentReply.SelectedValue = "-1";
					checkboxQueueSurveySendMailIfFailed.Checked = false;
					checkboxSurveyEnabledForChat.Checked = false;
					textboxQueueSurveyEmailFrom.Text = string.Empty;
					textboxQueueSurveyEmailSubject.Text = string.Empty;
					textboxQueueSurveyEmailTemplate.Text = string.Empty;
					checkboxSurveySendIfNewCaseExists.Checked = true;
					checkboxSurveySendIfNewCaseHasTag.Checked = false;
					checkboxSurveySendIfNewCaseClosedByYflow.Checked = false;
					textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
					textboxSurveyDontSendTotalSendMonthly.Text = "0";

					var queues = this.LoggedUser.Queues.Where(q => q.Enabled).Select(q => new
					{
						q.ID,
						q.Name
					}).OrderBy(q => q.Name);

					listboxQueues.DataSource = queues;
					listboxQueues.DataBind();
					dropdownlistSLMinutesActionsAssignToQueue.DataSource = queues;
					dropdownlistSLMinutesExpiredActionsAssignToQueue.DataSource = queues;
                    dropdownlistFirstAutomaticActionQueues.DataSource = queues;
                    dropdownlistSecondAutomaticActionQueues.DataSource = queues;

                    dropdownlistSLMinutesActionsAssignToQueue.DataBind();
					dropdownlistSLMinutesExpiredActionsAssignToQueue.DataBind();
                    dropdownlistFirstAutomaticActionQueues.DataBind();
                    dropdownlistSecondAutomaticActionQueues.DataBind();

					textboxSLMinutesActionsTags.Text = string.Empty;
					
					hiddenSurveyTagGroup.Value = string.Empty;
					hiddenSurveyTagGroupToIgnore.Value = string.Empty;
					

					hiddenQueueWorkingHoursForReceivingMessages.Value = Newtonsoft.Json.JsonConvert.SerializeObject(new DomainModel.Settings.WorkingTimesSettings.WorkingDatesSettings(true));
					checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors.Checked = true;
					checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL.Checked = true;
					checkboxQueueConnectedAgentsForReceivingMessages.Checked = false;
					checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors.Checked = true;
					checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL.Checked = true;
				}
				else
				{
					var queues = this.LoggedUser.Queues.Where(q => q.Enabled && q.ID != this.EntityID).Select(q => new
					{
						q.ID,
						q.Name
					}).OrderBy(q => q.Name);

					listboxQueues.DataSource = queues;
					listboxQueues.DataBind();
					dropdownlistSLMinutesActionsAssignToQueue.DataSource = queues;
					dropdownlistSLMinutesExpiredActionsAssignToQueue.DataSource = queues;
                    dropdownlistFirstAutomaticActionQueues.DataSource = queues;
                    dropdownlistSecondAutomaticActionQueues.DataSource = queues;
                    dropdownlistSLMinutesActionsAssignToQueue.DataBind();
					dropdownlistSLMinutesExpiredActionsAssignToQueue.DataBind();
                    dropdownlistFirstAutomaticActionQueues.DataBind();
                    dropdownlistSecondAutomaticActionQueues.DataBind();
                }

				textboxMinutesNotAssignToPreviousAgent.Text = "0";
				checkboxAllowAgentsToReturnMessagesToQueue.Checked = false;
				checkboxAllowAgentsToSelectQueueOnReturnToQueue.Checked = false;

				panelQueueTags.Visible = ((this.Entities & DomainModel.Entities.Tags) == DomainModel.Entities.Tags);
				liTabTags.Visible = ((this.Entities & DomainModel.Entities.Tags) == DomainModel.Entities.Tags);

				//panelQueueTagGroups.Visible = ((this.Entities & DomainModel.Entities.TagGroups) == DomainModel.Entities.TagGroups);

				this.RegisterJsonVariable("AllowAgentsToSelectQueueOnReturnToQueue", DomainModel.SystemSettings.Instance.AllowAgentsToSelectQueueOnReturnToQueue);


				IEnumerable<TagGroup> tagGroups = DomainModel.Cache.Instance.GetList<TagGroup>();
				tagGroups = tagGroups.OrderBy(tg => tg.Name);
				this.RegisterJsonVariable("tagGroups", BuildTagGroups(tagGroups));
			}
		}

		private void LoadAutoReplyPanels()
		{
			textboxSLMinutesActionsAutoReply.MaxLength = 240;
			textboxSLMinutesExpiredActionsAutoReply.MaxLength = 240;
		}

		private bool ValidateVideoCubiqSystemSettings()
		{
			return !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.VideoCubiqUrl) && 
				   !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.VideoCubiqSecret) && 
				   !string.IsNullOrEmpty(DomainModel.SystemSettings.Instance.VideoCubiqApiKey);

        }

		#endregion

		#region Overrides

		protected override void OnPreRender(EventArgs e)
		{
			base.OnPreRender(e);

			try
			{
				if (!this.EnListado)
				{
					this.RegisterJsonVariable("editingQueueId", this.Nuevo ? (int?) null : (int?) this.EntityID);
					this.RegisterJsonVariable("editingQueue", !this.Nuevo);
					this.RegisterJsonVariable("creatingQueue", this.Nuevo);
				}
			}
			catch { }
		}

		#endregion

		#region CustomValidator Events

		protected void customvalidatorName_OnServerValidate(object sender, ServerValidateEventArgs e)
		{
			if (Page.IsValid)
			{
				var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
				if (this.Nuevo)
				{
					if (queues.Any(s => s != null && s.Name.Equals(this.textboxName.Text, StringComparison.InvariantCultureIgnoreCase)))
					{
						e.IsValid = false;
						return;
					}
				}
				else
				{
					if (queues.Any(s => s != null && s.ID != this.EntityID && s.Name.Equals(this.textboxName.Text, StringComparison.InvariantCultureIgnoreCase)))
					{
						e.IsValid = false;
						return;
					}
				}
			}
		}

		protected void customvalidatorKey_OnServerValidate(object sender, ServerValidateEventArgs e)
		{
			if (Page.IsValid)
			{
				var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
				if (this.Nuevo)
				{
					if (queues.Any(s => s != null &&
						s.Enabled &&
						!string.IsNullOrEmpty(s.Key) &&
						s.Key.Equals(this.textboxKey.Text, StringComparison.InvariantCultureIgnoreCase)))
					{
						e.IsValid = false;
						return;
					}
				}
				else
				{
					if (queues.Any(s => s != null &&
						s.Enabled &&
						s.ID != this.EntityID &&
						!string.IsNullOrEmpty(s.Key) &&
						s.Key.Equals(this.textboxKey.Text, StringComparison.InvariantCultureIgnoreCase)))
					{
						e.IsValid = false;
						return;
					}
				}
			}
		}

		#endregion

		#region Button Events

		protected void buttonAction_Click(object sender, EventArgs e)
		{
			this.EnListado = false;
			this.Nuevo = false;

			int id = int.Parse(hiddenActionQueueID.Value);
			var queue = QueueDAO.GetOneFromCache(id);

			this.EntityID = id;

			switch (hiddenActionName.Value)
			{
				case "Enable":
					queue.Enabled = true;
					QueueDAO.Enable(id, true);
					DAL.UserLogDAO.Insert(this.LoggedUser, SystemEntityTypes.Queues, SystemActionTypes.Enable, id, queue.Name);
					
					Application.Lock();
					Application["QueuesJson"] = null;
					Application.UnLock();

					Response.Redirect(this.RedirectUrl);
					break;

				case "Disable":
					queue.Enabled = false;
					QueueDAO.Enable(id, false);
					DAL.UserLogDAO.Insert(this.LoggedUser, SystemEntityTypes.Queues, SystemActionTypes.Disable, id, queue.Name);

					Application.Lock();
					Application["QueuesJson"] = null;
					Application.UnLock();

					Response.Redirect(this.RedirectUrl);
					break;

				case "EditQueue":
				case "CopyQueue":
					{
						this.EditEntities = this.Entities;

						if (hiddenActionName.Value.Equals("EditQueue"))
						{
							textboxName.Text = queue.Name;
							textboxDescription.Text = queue.Description;
							textboxKey.Text = queue.Key;
						}
						else
						{
							textboxName.Text = string.Empty;
							textboxDescription.Text = string.Empty;
							textboxKey.Text = string.Empty;
							this.Nuevo = true;
						}

						textboxMinutesToWaitForAgent.Text = queue.MinutesToWaitForAgent.ToString();
						textboxMaxReservedMessagesPerAgent.Text = queue.MaxReservedMessagesPerAgent.ToString();
						dropdownlistReserveConditions.SelectedValue = ((byte) queue.ReserveCondition).ToString();
						textboxMinutesNotAssignToPreviousAgent.Text = queue.MinutesNotAssignToPreviousAgent.ToString();
						dropdownlistMailSignatureBehaviour.SelectedValue = ((int) queue.SignatureBehaviour).ToString();
						textboxMailSignature.Text = queue.Signature;
						checkboxDeleteReservedWithStatus.Checked = queue.DeleteReservedWithStatus;
						checkboxRenewReserveTimeWhenGrouping.Checked = queue.RenewReserveTimeWhenGrouping;

						CambiarModo();

						foreach (ListItem item in listboxDontReserveWithStatus.Items)
						{
							int statusId = int.Parse(item.Value);
							if (queue.DontReserveWithStatusID.Contains(statusId))
							{
								item.Selected = true;
							}
						}

						checkboxAllowAgentsToReturnMessagesToQueue.Checked = queue.AllowAgentsToReturnMessagesToQueue;
						if (queue.AllowAgentsToReturnMessagesToQueue)
						{
							if (queue.MinutesNotAssignToPreviousAgent != null)
								textboxMinutesNotAssignToPreviousAgent.Text = queue.MinutesNotAssignToPreviousAgent.Value.ToString();
						}


						checkboxAllowAgentsToSelectQueueOnReturnToQueue.Checked = queue.AllowAgentsToSelectQueueOnReturnToQueue;
						if (queue.AllowAgentsToSelectQueueOnReturnToQueue)
						{
							foreach (ListItem item in listboxQueues.Items)
							{
								int queueId = int.Parse(item.Value);
								if (queue.RelatedQueuesToReturnMessages.Contains(queueId))
								{
									item.Selected = true;
								}
							}

							for (int i = 0; i < queue.RelatedQueuesToReturnMessages.Count; i++)
							{
								var relatedQueue = queue.RelatedQueuesToReturnMessages[i];
								if (relatedQueue != null)
								{
									if (relatedQueue.Name == null)
									{
										relatedQueue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(relatedQueue.ID);
										queue.RelatedQueuesToReturnMessages[i] = relatedQueue;
									}

									if (!this.LoggedUser.Queues.Contains(relatedQueue))
									{
										ListItem item = new ListItem();
										item.Value = relatedQueue.ID.ToString();
										item.Text = relatedQueue.Name;
										item.Selected = true;
										item.Attributes.Add("disabled", "disabled");
										//item.Enabled = false;
										listboxQueues.Items.Add(item);
									}
								}
							}
						}

						hiddenQueueWorkingHoursForReceivingMessages.Value = Newtonsoft.Json.JsonConvert.SerializeObject(queue.WorkingHoursForReceivingMessages);
						checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors.Checked = queue.WorkingHoursForReceivingMessagesExceptionSupervisors;
						checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL.Checked = queue.WorkingHoursForReceivingMessagesExceptionSL;

						checkboxQueueConnectedAgentsForReceivingMessages.Checked = queue.ConnectedAgentsForReceivingMessages;
						checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSupervisors.Checked = queue.ConnectedAgentsForReceivingMessagesExceptionsSupervisors;
						checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSL.Checked = queue.ConnectedAgentsForReceivingMessagesExceptionsSL;
						textboxAgentIdleMinutes.Text = queue.AgentIdleMinutesOnChatMode.ToString();
						checkboxAgentIdleReturnToQueue.Checked = queue.AgentIdleReturnToQueueOnChatMode;
						textboxFinalUserIdleMinutes.Text = queue.FinalUserIdleMinutesOnChatMode.ToString();

						if (Licensing.LicenseManager.Instance.License.Configuration.AllowAgentsToStartVideoCall)
						{
							checkboxEnableVideo.Checked = queue.EnableVideo;
							textboxVideoDefaultText.Text = queue.VideoDefaultText == string.Empty ? VideoMessageDefaultText : queue.VideoDefaultText;
							if (!string.IsNullOrEmpty(queue.VideoProject))
							{
								dropdownlistVideoProject.SelectedValue = queue.VideoProject;
							}
						}

						#region Load Users

						if ((this.EditEntities & DomainModel.Entities.Users) == DomainModel.Entities.Users)
						{
							if (hiddenActionName.Value.Equals("EditQueue") ||
								(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyUsers.Checked))
							{
								foreach (var user in queue.Supervisors)
								{
									var listitem = listboxSupervisors.Items.FindByValue(user.ID.ToString());
									if (listitem != null)
										listitem.Selected = true;
								}

								foreach (var user in queue.Users)
								{
									var listitem = listboxUsers.Items.FindByValue(user.ID.ToString());
									if (listitem != null)
										listitem.Selected = true;
								}
							}
						}

						#endregion

						#region Load Tags
						
						if ((this.EditEntities & DomainModel.Entities.Tags) == DomainModel.Entities.Tags)
						{
							if (hiddenActionName.Value.Equals("EditQueue") ||
								(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyTags.Checked))
							{
								if (queue.Tags != null && queue.Tags.Count > 0)
								{
                                    this.RegisterJsonVariable("queueTags", BuildTags(queue.Tags));
                                }
							}
						}

						#endregion

						#region Load TagGroups

						hiddenTagGroups.Value = string.Empty;
						if ((this.EditEntities & DomainModel.Entities.TagGroups) == DomainModel.Entities.TagGroups)
						{
							if (hiddenActionName.Value.Equals("EditQueue") ||
								(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyTags.Checked))
							{
								if (queue.TagGroups != null && queue.TagGroups.Count > 0)
									hiddenTagGroups.Value = string.Join(",", queue.TagGroups.Select(t => t.ID));
							}
						}

						#endregion

						#region Load ServiceLevel

						textboxQueueServiceLevelSeconds.Text = "0";
						textboxQueueServiceLevelExpired.Text = "0";

						if (hiddenActionName.Value.Equals("EditQueue") ||
							(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyServiceLevel.Checked))
						{
							if (queue.ServiceLevelParameters != null && queue.ServiceLevelParameters.Count > 0)
							{
								foreach (var slp in queue.ServiceLevelParameters)
								{
									var sl = slp.Value;
									switch (slp.Key)
									{
										case ServiceLevelTypes.ServiceLevel:
											if (sl.Seconds > 0)
											{
												textboxQueueServiceLevelSeconds.Text = sl.Seconds.ToString();
												checkboxSLMinutesActionsAssignToQueue.Checked = sl.Actions.AssignToQueue;
												if (sl.Actions.AssignToQueue)
												{
													try
													{
														dropdownlistSLMinutesActionsAssignToQueue.SelectedValue = sl.Actions.Queue.ToString();
													}
													catch
													{
														messageSLMinutesActionsAssignToQueueDisabled.Visible = true;
													}
												}
												checkboxSLMinutesActionsAutoReply.Checked = sl.Actions.AutoReply;
												if (sl.Actions.AutoReply)
												{
													textboxSLMinutesActionsAutoReply.Text = sl.Actions.AutoReplyText;
													checkboxSLMinutesActionsAutoReplyIfAlreadyForCase.Checked = sl.Actions.AutoReplyIfAlreadyForCase;
												}
												checkboxSLMinutesActionsDiscard.Checked = sl.Actions.Discard;
												checkboxSLMinutesActionsDiscardAndCloseCase.Checked = sl.Actions.CloseCaseOnDiscard;
												checkboxSLMinutesActionsNotify.Checked = sl.Actions.Notify;
												checkboxSLMinutesActionsAddTags.Checked = sl.Actions.AddTags;
                                                textboxSLMinutesActionsTags.Text = string.Empty;
                                                if (sl.Actions.AddTags)
												{
													if (sl.Actions.Tags != null && sl.Actions.Tags.Count > 0)
                                                        this.RegisterJsonVariable("slMinutesActionTags", BuildTagsByIds(sl.Actions.Tags));
                                                   
												}
												checkboxChatAvailableDaysAndTimes.Checked = sl.Actions.AvailableDaysAndTimes;
												hiddenChatAvailableDaysAndTimes.Value = string.Empty;
												if (sl.Actions.AvailableDaysAndTimes)
												{
													hiddenChatAvailableDaysAndTimes.Value = Newtonsoft.Json.JsonConvert.SerializeObject(sl.Actions.DaysAndTimes);
												}
												checkboxSLMinutesActionsVIM.Checked = sl.Actions.VIM;

												checkboxSLChatNotify.Checked = sl.Actions.NotifyToChat;
												if (sl.Actions.NotifyToChat)
												{
													dropdownlistSLChatNotifyAndFinish.SelectedValue = ((short) sl.Actions.NotifyToChatAndFinish).ToString();
													textboxSLChatNotifyText.Text = sl.Actions.NotifyToChatText;
												}
											}
											break;
										case ServiceLevelTypes.MessageExpired:
											if (sl.Minutes > 0)
											{
												textboxQueueServiceLevelExpired.Text = sl.Minutes.ToString();
												checkboxSLMinutesExpiredActionsAssignToQueue.Checked = sl.Actions.AssignToQueue;
												if (sl.Actions.AssignToQueue)
												{
													try
													{
														dropdownlistSLMinutesExpiredActionsAssignToQueue.SelectedValue = sl.Actions.Queue.ToString();
													}
													catch
													{
														messageSLMinutesExpiredActionsAssignToQueueDisabled.Visible = true;
													}
												}
												checkboxSLMinutesExpiredActionsAutoReply.Checked = sl.Actions.AutoReply;
												if (sl.Actions.AutoReply)
												{
													textboxSLMinutesExpiredActionsAutoReply.Text = sl.Actions.AutoReplyText;
													checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase.Checked = sl.Actions.AutoReplyIfAlreadyForCase;
												}
												checkboxSLMinutesExpiredActionsDiscard.Checked = sl.Actions.Discard;
												checkboxSLMinutesExpiredActionsDiscardAndCloseCase.Checked = sl.Actions.CloseCaseOnDiscard;
												checkboxSLMinutesExpiredActionsNotify.Checked = sl.Actions.Notify;
												checkboxSLMinutesExpiredActionsAddTags.Checked = sl.Actions.AddTags;
                                                textboxSLMinutesExpiredActionsTags.Text = string.Empty;
                                                if (sl.Actions.AddTags)
												{
													if (sl.Actions.Tags != null && sl.Actions.Tags.Count > 0)
                                                        this.RegisterJsonVariable("slMinutesExpiredActionsTags", BuildTagsByIds(sl.Actions.Tags));
                                                }

												checkboxSLExpiredChatNotify.Checked = sl.Actions.NotifyToChat;
												if (sl.Actions.NotifyToChat)
												{
													dropdownlistSLExpiredChatNotifyAndFinish.SelectedValue = ((short) sl.Actions.NotifyToChatAndFinish).ToString();
													textboxSLExpiredChatNotifyText.Text = sl.Actions.NotifyToChatText;
												}
												checkboxSLMinutesExpiredActionsVIM.Checked = sl.Actions.VIM;
											}
											break;
									}
								}
							}
						}

                        #endregion

                        #region Load AutomaticActions

                        if (hiddenActionName.Value.Equals("EditQueue") ||
							(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyAutomaticActions.Checked))
						{
							if (queue.AutomaticActions != null && queue.AutomaticActions.Count > 0)
							{
								foreach (var acm in queue.AutomaticActions)
								{
									var ac = acm.Value;
									switch (acm.Key)
									{
										case AutomaticActionsTypes.FirstInteractionLevel:
											if (ac.Enabled)
											{
												checkboxAllowFirstAutomaticActions.Checked = ac.Enabled;
												textboxFirstAutomaticActionSeconds.Text = ac.Minutes.ToString();
												checkboxFirstAutomaticActionsTransferQueue.Checked = ac.Actions.AssignToQueue;
												if (ac.Actions.AssignToQueue)
												{
													try
													{
														dropdownlistFirstAutomaticActionQueues.SelectedValue = ac.Actions.Queue.ToString();
													}
													catch
													{
                                                        messageFirstActionTransferQueueDisabled.Visible = true;
													}
												}
												checkboxFirstAutomaticActionReply.Checked = ac.Actions.AutoReply;
												if (ac.Actions.AutoReply)
												{
													textboxFirstAutomaticActionReplyText.Text = ac.Actions.AutoReplySettings.ReplyText;

													if (ac.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@"))
													{
                                                        textboxFirstAutomaticActionReplyEwtNoAgents.Text = ac.Actions.AutoReplySettings.ReplyTextNoEwtAgents;
                                                        textboxFirstAutomaticActionReplyEwtNotComputed.Text = ac.Actions.AutoReplySettings.ReplyTextNoEwtComputed;
                                                    }

                                                    if (ac.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@") || ac.Actions.AutoReplySettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                                                    {
                                                        textboxFirstAutomaticActionMinimumEWT.Text = ac.Actions.AutoReplySettings.MinimumEWT.ToString();
                                                        textboxFirstAutomaticActionMinimumEnqueueMessages.Text = ac.Actions.AutoReplySettings.MinimumPositionOfMessageInQueue.ToString();
                                                    }
                                                }

												checkboxFirstAutomaticActionApplyTags.Checked = ac.Actions.AddTags;
                                                textboxFirstAutomaticActionsTags.Text = string.Empty;
												if (ac.Actions.AddTags)
												{
													if (ac.Actions.Tags != null && ac.Actions.Tags.Count > 0)
                                                        this.RegisterJsonVariable("firstAutomaticActionsTags", BuildTagsByIds(ac.Actions.Tags));
                                                }

												checkboxFirstAutomaticActionNotifyChat.Checked = ac.Actions.NotifyToChat;
												if (ac.Actions.NotifyToChat)
												{
													dropdownlistFirstAutomaticActionMarkAsFinishChat.SelectedValue = ((short) ac.Actions.NotifyToChatAndFinish).ToString();
													textboxFirstAutomaticActionReplyChat.Text = ac.Actions.NotifyToChatSettings.ReplyText;

                                                    if (ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@"))
                                                    {
                                                        textboxFirstAutomaticActionReplyEwtNoAgentsChat.Text = ac.Actions.NotifyToChatSettings.ReplyTextNoEwtAgents;
                                                        textboxFirstAutomaticActionReplyEwtNotCalculatedChat.Text = ac.Actions.NotifyToChatSettings.ReplyTextNoEwtComputed;
                                                    }

                                                    if (ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@") || ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                                                    {
                                                        textboxFirstAutomaticActionMinimumEWTChat.Text = ac.Actions.NotifyToChatSettings.MinimumEWT.ToString();
                                                        textboxFirstAutomaticActionMinimumEnqueueMessagesChat.Text = ac.Actions.NotifyToChatSettings.MinimumPositionOfMessageInQueue.ToString();
                                                    }
                                                }
											}
											break;
										case AutomaticActionsTypes.SecondInteractionLevel:
											if (ac.Enabled)
											{
												checkboxAllowSecondAutomaticActions.Checked = ac.Enabled;
												textboxSecondAutomaticActionsSeconds.Text = ac.Minutes.ToString();
												checkboxSecondAutomaticActionsTransferQueue.Checked = ac.Actions.AssignToQueue;
												if (ac.Actions.AssignToQueue)
												{
													try
													{
														dropdownlistSecondAutomaticActionQueues.SelectedValue = ac.Actions.Queue.ToString();
													}
													catch
													{
                                                        messageSecondAutomaticActionTransferQueueDisabled.Visible = true;
													}
												}
                                                checkboxSecondAutomaticActionReply.Checked = ac.Actions.AutoReply;
												if (ac.Actions.AutoReply)
												{
													textboxSecondAutomaticActionReplyText.Text = ac.Actions.AutoReplySettings.ReplyText;
                                                    if (ac.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@"))
                                                    {
                                                        textboxSecondAutomaticActionReplyEwtNoAgents.Text = ac.Actions.AutoReplySettings.ReplyTextNoEwtAgents;
                                                        textboxSecondAutomaticActionReplyEwtNotComputed.Text = ac.Actions.AutoReplySettings.ReplyTextNoEwtComputed;
                                                    }

													if (ac.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@") || ac.Actions.AutoReplySettings.ReplyText.Contains("@@POSMSGCOLA@@"))
													{
														textboxSecondAutomaticActionMinimumEWT.Text = ac.Actions.AutoReplySettings.MinimumEWT.ToString();
														textboxSecondAutomaticActionMinimumEnqueueMessages.Text = ac.Actions.AutoReplySettings.MinimumPositionOfMessageInQueue.ToString();
                                                    }
                                                }
												

												checkboxSecondAutomaticActionApplyTags.Checked = ac.Actions.AddTags;
                                                textboxSecondAutomaticActionsTags.Text = string.Empty;
												if (ac.Actions.AddTags)
												{
													if (ac.Actions.Tags != null && ac.Actions.Tags.Count > 0)
                                                        this.RegisterJsonVariable("secondAutomaticActionsTags", BuildTagsByIds(ac.Actions.Tags));
                                                }

												checkboxSecondAutomaticActionNotifyChat.Checked = ac.Actions.NotifyToChat;
												if (ac.Actions.NotifyToChat)
												{
                                                    dropdownlistSecondAutomaticActionMarkAsFinishChat.SelectedValue = ((short) ac.Actions.NotifyToChatAndFinish).ToString();
                                                    textboxSecondAutomaticActionChatReplyText.Text = ac.Actions.NotifyToChatSettings.ReplyText;

                                                    if (ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@"))
                                                    {
                                                        textboxSecondAutomaticActionReplyEwtNoAgentsChat.Text = ac.Actions.NotifyToChatSettings.ReplyTextNoEwtAgents;
                                                        textboxSecondAutomaticActionReplyEwtNotCalculatedChat.Text = ac.Actions.NotifyToChatSettings.ReplyTextNoEwtComputed;
                                                    }

                                                    if (ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@") || ac.Actions.NotifyToChatSettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                                                    {
                                                        textboxSecondAutomaticActionMinimumEWTChat.Text = ac.Actions.NotifyToChatSettings.MinimumEWT.ToString();
                                                        textboxSecondAutomaticActionMinimumEnqueueMessagesChat.Text = ac.Actions.NotifyToChatSettings.MinimumPositionOfMessageInQueue.ToString();
                                                    }
                                                }
											}
											break;
									}
								}
							}
						}
					

                        #endregion

                        #region Load Surveys
                        checkboxEnableSurveys.Checked = false;

						if (hiddenActionName.Value.Equals("EditQueue") ||
							(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopySurveys.Checked))
						{
							checkboxEnableSurveys.Checked = queue.SurveyEnabled;
							if (queue.SurveyList != null)
							{
								var su = (from queueSurvey in queue.SurveyList
										  join s in DomainModel.Cache.Instance.GetList<Survey>()
										  on queueSurvey.SurveyID equals s.ID
										  select new
										  {
											  ID = s.ID,
											  Name = s.Name,
											  QueueSurveyConfiguration = queueSurvey
                                          });
								this.RegisterJsonVariable("surveys", su);
							}
							else
							{
								this.RegisterJsonVariable("surveys", new List<QueueSurveyConfiguration>());
							}
						}

						this.RegisterJsonVariable("queueID", this.EntityID);
						textboxQueueSurveyExpiration.Text = string.Empty;
						checkboxQueueSurveySendMailIfFailed.Checked = false;
						checkboxSurveyEnabledForChat.Checked = false;
						textboxQueueSurveyEmailFrom.Text = string.Empty;
						textboxQueueSurveyEmailSubject.Text = string.Empty;
						textboxQueueSurveyEmailTemplate.Text = string.Empty;
						textboxQueueSurveyInvitation.Text = string.Empty;
						textboxQueueSurveySentRate.Text = "100";
						textboxQueueSurveyTimeToSend.Text = "24";
						dropdownlistQueueSurveyCloseCondition.SelectedIndex = 0;
						textboxQueueSurveyMessagesCount.Text = "0";
						textboxQueueSurveyCaseDuration.Text = "0";
						dropdownlistQueueSurveyWithAgentReply.SelectedValue = "-1";
						checkboxSurveySendIfNewCaseExists.Checked = true;
						checkboxSurveySendIfNewCaseHasTag.Checked = false;
						checkboxSurveySendIfNewCaseClosedByYflow.Checked = false;
						textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
						textboxSurveyDontSendTotalSendMonthly.Text = "0";
						dropdownQueueSurvey.SelectedValue = "-1";
						SurveySelected = null;
						#endregion

						#region Load EWT

						if (hiddenActionName.Value.Equals("EditQueue") ||
							(hiddenActionName.Value.Equals("CopyQueue") && checkboxCopyEWT.Checked))
						{
							checkboxConfigEwt.Checked = queue.UseQueueEwtConfig;
							textboxMinutesPredictedAht.Text = queue.UseQueueEwtConfig ? queue.MinutesPredictedAht.ToString() : DomainModel.SystemSettings.Instance.MinutesPredictedAht.ToString(); ;
							textboxSecondsEwt.Text = queue.UseQueueEwtConfig ? queue.SecondsEwt.ToString() : DomainModel.SystemSettings.Instance.SecondsEwt.ToString();
							checkboxASAPersonalized.Checked = queue.AllowToSetASAValueByDefault;
							textboxAsaBase.Text = queue.UseQueueEwtConfig ? queue.AllowToSetASAValueByDefault ? queue.ASADefaultValue.ToString() : DomainModel.SystemSettings.Instance.ASADefaultValue > 3600 ? "3600" : DomainModel.SystemSettings.Instance.ASADefaultValue.ToString() : "30";
                        }
						
						#endregion Load EWT

						if (hiddenActionName.Value.Equals("EditQueue") && this.QueueHasConnectedAgents(queue))
						{
							this.EditEntities = this.EditEntities.Remove<DomainModel.Entities>(DomainModel.Entities.Tags);

							panelQueueTags.Visible = false;
							liTabTags.Visible = false;

							this.EditEntities = this.EditEntities.Remove<DomainModel.Entities>(DomainModel.Entities.TagGroups);

							//panelQueueTagGroups.Visible = false;
						}
					}
					break;
				case "DeleteQueue":
					{
						var canBeDeleted = Core.System.Instance.QueueService.CanDeleteQueue(queue);

						if (canBeDeleted)
						{
							QueueDAO.Delete(queue.ID);
							DAL.UserLogDAO.Insert(this.LoggedUser
								, SystemEntityTypes.Queues
								, SystemActionTypes.Delete
								, new Dictionary<string, string>() { { "Name", queue.Name } }
								, null
								, id
								, queue.Name);

							Application.Lock();
							Application["QueuesJson"] = null;
							Application.UnLock();
						}

						Response.Redirect(this.RedirectUrl);
					}
					break;
			}
		}

		protected void buttonNew_Click(object sender, EventArgs e)
		{
			this.EnListado = false;
			this.Nuevo = true;
			this.EditEntities = this.Entities;

			this.RegisterJsonVariable("surveys", new List<QueueSurveyConfiguration>());
			CambiarModo();
		}

		protected void buttonCancel_Click(object sender, EventArgs e)
		{
			/*if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
			{
				var queue = QueueDAO.GetOneFromCache(this.EntityID);
				if (queue.ActualSurvey != null)
					queue.SurveyList.Add(queue.ActualSurvey);

				queue.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(this.SurveyList);
			}*/

			this.EnListado = true;
			CambiarModo();
		}

		protected void buttonCopyQueueConfirm_Click(object sender, EventArgs e)
		{

		}

		protected void buttonSave_Click(object sender, EventArgs e)
		{
			if (Page.IsValid)
			{
				Queue queue = null;
				Dictionary<string, string> oldParameters = null;
				if (this.Nuevo)
				{
					queue = new Queue();
				}
				else
				{
					queue = QueueDAO.GetOneFromCache(this.EntityID);
					oldParameters = queue.AsDictionary(true);
				}

				bool nameChanged = (!this.Nuevo && queue.Name != textboxName.Text);
				queue.Name = textboxName.Text;
				queue.Description = textboxDescription.Text;
				queue.Key = textboxKey.Text;
				if (string.IsNullOrEmpty(queue.Key))
					queue.Key = null;

				if (nameChanged)
				{
					Core.SystemQueue coreQueue = Core.System.Instance.QueueService.GetQueueById(this.EntityID);
					coreQueue.Rename(queue.Name);
				}

				queue.MinutesToWaitForAgent = int.Parse(textboxMinutesToWaitForAgent.Text);
				queue.MaxReservedMessagesPerAgent = int.Parse(textboxMaxReservedMessagesPerAgent.Text);
				queue.ReserveCondition = (Queue.QueueReserveConditions)byte.Parse(dropdownlistReserveConditions.SelectedValue);
				queue.SignatureBehaviour = (SignatureBehaviours)int.Parse(dropdownlistMailSignatureBehaviour.SelectedValue);
				queue.Signature = textboxMailSignature.Text;
				var statusId = new List<int>();
				foreach (ListItem item in listboxDontReserveWithStatus.Items)
				{
					if (item.Selected)
						statusId.Add(int.Parse(item.Value));
				}
				queue.DontReserveWithStatusID = statusId;
				queue.DeleteReservedWithStatus = checkboxDeleteReservedWithStatus.Checked;
				queue.RenewReserveTimeWhenGrouping = checkboxRenewReserveTimeWhenGrouping.Checked;

				queue.AgentIdleMinutesOnChatMode = int.Parse(textboxAgentIdleMinutes.Text);
				queue.AgentIdleReturnToQueueOnChatMode = checkboxAgentIdleReturnToQueue.Checked;
				queue.FinalUserIdleMinutesOnChatMode = int.Parse(textboxFinalUserIdleMinutes.Text);

				if (checkboxAllowAgentsToReturnMessagesToQueue.Checked)
				{
					queue.AllowAgentsToReturnMessagesToQueue = true;
					queue.MinutesNotAssignToPreviousAgent = int.Parse(textboxMinutesNotAssignToPreviousAgent.Text);
				}
				else
				{
					queue.AllowAgentsToReturnMessagesToQueue = false;
					queue.MinutesNotAssignToPreviousAgent = null;
				}

				if (checkboxAllowAgentsToSelectQueueOnReturnToQueue.Checked)
				{
					queue.AllowAgentsToSelectQueueOnReturnToQueue = true;

					if (this.Nuevo)
					{
						queue.RelatedQueuesToReturnMessages.Clear();
					}
					else
					{
						IEnumerable<DomainModel.Queue> currentRelatedQueuesToReturnMessages = queue.RelatedQueuesToReturnMessages.ToArray();
						currentRelatedQueuesToReturnMessages = currentRelatedQueuesToReturnMessages.Except(this.LoggedUser.Queues);
						queue.RelatedQueuesToReturnMessages.Clear();
						queue.RelatedQueuesToReturnMessages.AddRange(currentRelatedQueuesToReturnMessages);
					}

					foreach (ListItem item in listboxQueues.Items)
					{
						if (item.Selected)
							queue.RelatedQueuesToReturnMessages.Add(QueueDAO.GetOneFromCache(int.Parse(item.Value)));
					}
				}
				else
				{
					queue.AllowAgentsToSelectQueueOnReturnToQueue = false;
					queue.RelatedQueuesToReturnMessages.Clear();
				}

				if (!string.IsNullOrEmpty(hiddenQueueWorkingHoursForReceivingMessages.Value))
					queue.WorkingHoursForReceivingMessages = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Settings.WorkingTimesSettings.WorkingDatesSettings>(hiddenQueueWorkingHoursForReceivingMessages.Value);
				queue.WorkingHoursForReceivingMessagesExceptionSupervisors = checkboxQueueWorkingHoursForReceivingMessagesExceptionsSupervisors.Checked;
				queue.WorkingHoursForReceivingMessagesExceptionSL = checkboxQueueWorkingHoursForReceivingMessagesExceptionsSL.Checked;
				queue.ConnectedAgentsForReceivingMessages = checkboxQueueConnectedAgentsForReceivingMessages.Checked;
				queue.ConnectedAgentsForReceivingMessagesExceptionsSupervisors = checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSupervisors.Checked;
				queue.ConnectedAgentsForReceivingMessagesExceptionsSL = checkboxQueueConnectedAgentsForReceivingMessagesExceptionsSL.Checked;

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAgentsToStartVideoCall)
				{
					queue.EnableVideo = checkboxEnableVideo.Checked;
					queue.VideoProject = (checkboxEnableVideo.Checked) ? dropdownlistVideoProject.SelectedValue : "";
					queue.VideoDefaultText = textboxVideoDefaultText.Text;
				}


                #region Sorting

                queue.SortBy = QueueSortTypes.MessageDate;

				#endregion

				#region Save Users

				if ((this.EditEntities & DomainModel.Entities.Users) == DomainModel.Entities.Users)
				{
					foreach (ListItem item in listboxSupervisors.Items)
					{
						int userId = int.Parse(item.Value);
						User user = UserDAO.GetOneFromCache(userId);

						if (item.Selected)
						{
							if (!queue.Supervisors.Contains(user))
								queue.Supervisors.Add(user);

							if (!user.Queues.Contains(queue))
								user.Queues.Add(queue);
						}
						else
						{
							if (queue.Supervisors.Contains(user))
								queue.Supervisors.Remove(user);

							if (user.Queues.Contains(queue))
								user.Queues.Remove(queue);
						}
					}

					foreach (ListItem item in listboxUsers.Items)
					{
						int userId = int.Parse(item.Value);
						User user = UserDAO.GetOneFromCache(userId);

						if (item.Selected)
						{
							if (!queue.Users.Contains(user))
								queue.Users.Add(user);

							if (!user.Queues.Contains(queue))
								user.Queues.Add(queue);
						}
						else
						{
							if (queue.Users.Contains(user))
								queue.Users.Remove(user);

							if (user.Queues.Contains(queue))
								user.Queues.Remove(queue);
						}
					}
				}

				#endregion

				#region Save Tags

				if ((this.EditEntities & DomainModel.Entities.Tags) == DomainModel.Entities.Tags)
				{
					IEnumerable<int> tags = null;
					if (!string.IsNullOrEmpty(textboxTags.Text))
						tags = textboxTags.Text.Split(",".ToCharArray()).Select(tag => int.Parse(tag));

					IEnumerable<DomainModel.Tag> removedTags = null;
					if (!this.Nuevo)
					{
						removedTags = queue.Tags.Where(tag => tags == null || !tags.Contains(tag.ID)).ToArray();
					}

					if (tags != null)
					{
						foreach (var tagId in tags)
						{
							Tag tag = TagDAO.GetOneFromCache(tagId);

							if (tag.Parent == null || tag.HasChildTags)
								continue;

							if (!queue.Tags.Contains(tag))
								queue.Tags.Add(tag);

							if (!tag.Queues.Contains(queue))
								tag.Queues.Add(queue);

							if (!queue.Tags.Contains(tag.Parent))
								queue.Tags.Add(tag.Parent);

							if (!tag.Parent.Queues.Contains(queue))
								tag.Parent.Queues.Add(queue);
						}
					}

					if (removedTags != null)
					{
						foreach (var tag in removedTags)
						{
							if (queue.Tags.Contains(tag))
								queue.Tags.Remove(tag);

							if (tag.Queues.Contains(queue))
								tag.Queues.Remove(queue);

							if (queue.Tags.Contains(tag.Parent) && queue.Tags.Intersect(tag.Parent.ChildTags).Count() == 0)
							{
								queue.Tags.Remove(tag.Parent);

								if (tag.Parent.Queues.Contains(queue))
									tag.Parent.Queues.Remove(queue);
							}
						}
					}
				}

				#endregion

				#region Save TagGroups

				if ((this.EditEntities & DomainModel.Entities.TagGroups) == DomainModel.Entities.TagGroups)
				{
					IEnumerable<short> tagGroups = null;
					if (!string.IsNullOrEmpty(hiddenTagGroups.Value))
						tagGroups = hiddenTagGroups.Value.Split(",".ToCharArray()).Select(tagGroup => short.Parse(tagGroup));

					IEnumerable<DomainModel.TagGroup> removedTagGroups = null;
					if (!this.Nuevo)
					{
						removedTagGroups = queue.TagGroups.Where(tg => tagGroups == null || !tagGroups.Contains(tg.ID)).ToArray();
					}

					if (tagGroups != null)
					{
						foreach (var tagGroupId in tagGroups)
						{
							TagGroup tg = TagGroupDAO.GetOneFromCache(tagGroupId);

							if (!queue.TagGroups.Contains(tg))
								queue.TagGroups.Add(tg);

							if (!tg.Queues.Contains(queue))
								tg.Queues.Add(queue);
						}
					}

					if (removedTagGroups != null)
					{
						foreach (var tg in removedTagGroups)
						{
							if (queue.TagGroups.Contains(tg))
								queue.TagGroups.Remove(tg);

							if (tg.Queues.Contains(queue))
								tg.Queues.Remove(queue);
						}
					}
				}

				#endregion

				#region Save ServiceLevel

				List<ServiceLevel> serviceLevelParameters = new List<ServiceLevel>();
				ServiceLevel sl;

				sl = new ServiceLevel();
				sl.Type = ServiceLevelTypes.ServiceLevel;
				sl.Seconds = int.Parse(textboxQueueServiceLevelSeconds.Text);
				sl.Minutes = 0;

				if (sl.Seconds > 0)
				{
					if (checkboxSLMinutesActionsAssignToQueue.Checked)
					{
						sl.Actions.AssignToQueue = true;
						sl.Actions.Queue = int.Parse(dropdownlistSLMinutesActionsAssignToQueue.SelectedValue);
					}

					if (checkboxSLMinutesActionsAutoReply.Checked)
					{
						sl.Actions.AutoReply = true;
						sl.Actions.AutoReplyText = textboxSLMinutesActionsAutoReply.Text;
						sl.Actions.AutoReplyIfAlreadyForCase = checkboxSLMinutesActionsAutoReplyIfAlreadyForCase.Checked;
					}

					sl.Actions.Discard = checkboxSLMinutesActionsDiscard.Checked;
					if (sl.Actions.Discard)
						sl.Actions.CloseCaseOnDiscard = checkboxSLMinutesActionsDiscardAndCloseCase.Checked;
					else
						sl.Actions.CloseCaseOnDiscard = false;
					sl.Actions.Notify = checkboxSLMinutesActionsNotify.Checked;
					sl.Actions.AddTags = checkboxSLMinutesActionsAddTags.Checked;

					if (checkboxSLMinutesActionsAddTags.Checked)
					{
						List<int> actionTags = new List<int>();
						if (!string.IsNullOrEmpty(textboxSLMinutesActionsTags.Text))
							actionTags.AddRange(textboxSLMinutesActionsTags.Text.Split(",".ToCharArray()).Select(tag => int.Parse(tag)));
						sl.Actions.Tags = actionTags;
					}

					sl.Actions.AvailableDaysAndTimes = checkboxChatAvailableDaysAndTimes.Checked;
                    if (checkboxChatAvailableDaysAndTimes.Checked)
                    {
						var workingDates = Newtonsoft.Json.JsonConvert.DeserializeObject<ServiceLevelActions.WorkingDatesSettings>(hiddenChatAvailableDaysAndTimes.Value);
						sl.Actions.DaysAndTimes = workingDates;
					}

					sl.Actions.NotifyToChat = checkboxSLChatNotify.Checked;
					if (sl.Actions.NotifyToChat)
					{
						sl.Actions.NotifyToChatAndFinish = (ServiceLevelActions.ChatFinishBehaviours) short.Parse(dropdownlistSLChatNotifyAndFinish.SelectedValue);
						sl.Actions.NotifyToChatText = textboxSLChatNotifyText.Text;
					}

					sl.Actions.VIM = checkboxSLMinutesActionsVIM.Checked;
				}

				serviceLevelParameters.Add(sl);

				#endregion

				#region Save MinutesExpired

				sl = new ServiceLevel();
				sl.Type = ServiceLevelTypes.MessageExpired;
				sl.Minutes = int.Parse(textboxQueueServiceLevelExpired.Text);

				if (sl.Minutes > 0)
				{
					if (checkboxSLMinutesExpiredActionsAssignToQueue.Checked)
					{
						sl.Actions.AssignToQueue = true;
						sl.Actions.Queue = int.Parse(dropdownlistSLMinutesExpiredActionsAssignToQueue.SelectedValue);
					}

					if (checkboxSLMinutesExpiredActionsAutoReply.Checked)
					{
						sl.Actions.AutoReply = true;
						sl.Actions.AutoReplyText = textboxSLMinutesExpiredActionsAutoReply.Text;
						sl.Actions.AutoReplyIfAlreadyForCase = checkboxSLMinutesExpiredActionsAutoReplyIfAlreadyForCase.Checked;
					}

					sl.Actions.Discard = checkboxSLMinutesExpiredActionsDiscard.Checked;
					if (sl.Actions.Discard)
						sl.Actions.CloseCaseOnDiscard = checkboxSLMinutesExpiredActionsDiscardAndCloseCase.Checked;
					else
						sl.Actions.CloseCaseOnDiscard = false;
					sl.Actions.Notify = checkboxSLMinutesExpiredActionsNotify.Checked;
					sl.Actions.AddTags = checkboxSLMinutesExpiredActionsAddTags.Checked;

					if (checkboxSLMinutesExpiredActionsAddTags.Checked)
					{
						List<int> actionTags = new List<int>();
						if (!string.IsNullOrEmpty(textboxSLMinutesExpiredActionsTags.Text))
							actionTags.AddRange(textboxSLMinutesExpiredActionsTags.Text.Split(",".ToCharArray()).Select(tag => int.Parse(tag)));
						sl.Actions.Tags = actionTags;
					}

					sl.Actions.NotifyToChat = checkboxSLExpiredChatNotify.Checked;
					if (sl.Actions.NotifyToChat)
					{
						sl.Actions.NotifyToChatAndFinish = (ServiceLevelActions.ChatFinishBehaviours) short.Parse(dropdownlistSLExpiredChatNotifyAndFinish.SelectedValue);
						sl.Actions.NotifyToChatText = textboxSLExpiredChatNotifyText.Text;
					}
					sl.Actions.VIM = checkboxSLMinutesExpiredActionsVIM.Checked;
				}

				serviceLevelParameters.Add(sl);

				#endregion

				queue.ServiceLevelParameters.Refresh(serviceLevelParameters);

                #region Save AutomaticActions
                List<AutomaticActions> automaticActionsParameters = new List<AutomaticActions>();
                AutomaticActions at;

                at = new AutomaticActions();
                at.Type = AutomaticActionsTypes.FirstInteractionLevel;

				at.Enabled = checkboxAllowFirstAutomaticActions.Checked;

                if (at.Enabled)
                {
                    at.Minutes = int.Parse(textboxFirstAutomaticActionSeconds.Text);

                    if (checkboxFirstAutomaticActionsTransferQueue.Checked)
                    {
                        at.Actions.AssignToQueue = true;
                        at.Actions.Queue = int.Parse(dropdownlistFirstAutomaticActionQueues.SelectedValue);
                    }

                    if (checkboxFirstAutomaticActionReply.Checked)
                    {
                        at.Actions.AutoReply = true;
                        at.Actions.AutoReplySettings.ReplyText = textboxFirstAutomaticActionReplyText.Text;
                        
						if (at.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@"))
						{
                            at.Actions.AutoReplySettings.ReplyTextNoEwtAgents = textboxFirstAutomaticActionReplyEwtNoAgents.Text;
                            at.Actions.AutoReplySettings.ReplyTextNoEwtComputed = textboxFirstAutomaticActionReplyEwtNotComputed.Text;
                        }

                        if (at.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@") || at.Actions.AutoReplySettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                        {
							at.Actions.AutoReplySettings.MinimumEWT = int.Parse(textboxFirstAutomaticActionMinimumEWT.Text);
							at.Actions.AutoReplySettings.MinimumPositionOfMessageInQueue = int.Parse(textboxFirstAutomaticActionMinimumEnqueueMessages.Text);
                        }
                    }
					
                    at.Actions.AddTags = checkboxFirstAutomaticActionApplyTags.Checked;

                    if (at.Actions.AddTags)
                    {
                        List<int> actionTags = new List<int>();
                        if (!string.IsNullOrEmpty(textboxFirstAutomaticActionsTags.Text))
                            actionTags.AddRange(textboxFirstAutomaticActionsTags.Text.Split(",".ToCharArray()).Select(tag => int.Parse(tag)));
                        at.Actions.Tags = actionTags;
                    }

                    at.Actions.NotifyToChat = checkboxFirstAutomaticActionNotifyChat.Checked;
                    if (at.Actions.NotifyToChat)
                    {
                        at.Actions.NotifyToChatAndFinish = (AutomaticActionsMethods.ChatFinishBehaviours) short.Parse(dropdownlistFirstAutomaticActionMarkAsFinishChat.SelectedValue);
                        at.Actions.NotifyToChatSettings.ReplyText = textboxFirstAutomaticActionReplyChat.Text;

                        if (at.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@"))
                        {
                            at.Actions.NotifyToChatSettings.ReplyTextNoEwtAgents = textboxFirstAutomaticActionReplyEwtNoAgentsChat.Text;
                            at.Actions.NotifyToChatSettings.ReplyTextNoEwtComputed = textboxFirstAutomaticActionReplyEwtNotCalculatedChat.Text;
                        }

                        if (at.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@") || at.Actions.NotifyToChatSettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                        {
                            at.Actions.NotifyToChatSettings.MinimumEWT = int.Parse(textboxFirstAutomaticActionMinimumEWTChat.Text);
                            at.Actions.NotifyToChatSettings.MinimumPositionOfMessageInQueue = int.Parse(textboxFirstAutomaticActionMinimumEnqueueMessagesChat.Text);
                        }
                    }

                }

                automaticActionsParameters.Add(at);

                #endregion

                #region Save SecondInteractionLevel

                at = new AutomaticActions();
                at.Type = AutomaticActionsTypes.SecondInteractionLevel;
              
                at.Enabled = checkboxAllowSecondAutomaticActions.Checked;

                if (at.Enabled)
                {
                    at.Minutes = int.Parse(textboxSecondAutomaticActionsSeconds.Text);

                    if (checkboxSecondAutomaticActionsTransferQueue.Checked)
                    {
                        at.Actions.AssignToQueue = true;
                        at.Actions.Queue = int.Parse(dropdownlistSecondAutomaticActionQueues.SelectedValue);
                    }

                    if (checkboxSecondAutomaticActionReply.Checked)
                    {
                        at.Actions.AutoReply = true;
                        at.Actions.AutoReplySettings.ReplyText = textboxSecondAutomaticActionReplyText.Text;
                        
						if (at.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@"))
                        {
                            at.Actions.AutoReplySettings.ReplyTextNoEwtAgents = textboxSecondAutomaticActionReplyEwtNoAgents.Text;
                            at.Actions.AutoReplySettings.ReplyTextNoEwtComputed = textboxSecondAutomaticActionReplyEwtNotComputed.Text;
                        }

                        if (at.Actions.AutoReplySettings.ReplyText.Contains("@@EWT@@") || at.Actions.AutoReplySettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                        {
                            at.Actions.AutoReplySettings.MinimumEWT = int.Parse(textboxSecondAutomaticActionMinimumEWT.Text);
                            at.Actions.AutoReplySettings.MinimumPositionOfMessageInQueue = int.Parse(textboxSecondAutomaticActionMinimumEnqueueMessages.Text);
                        }
                    }

                    at.Actions.AddTags = checkboxSecondAutomaticActionApplyTags.Checked;

                    if (at.Actions.AddTags)
                    {
                        List<int> actionTags = new List<int>();
                        if (!string.IsNullOrEmpty(textboxSecondAutomaticActionsTags.Text))
                            actionTags.AddRange(textboxSecondAutomaticActionsTags.Text.Split(",".ToCharArray()).Select(tag => int.Parse(tag)));
                        at.Actions.Tags = actionTags;
                    }

                    at.Actions.NotifyToChat = checkboxSecondAutomaticActionNotifyChat.Checked;
                    if (at.Actions.NotifyToChat)
                    {
                        at.Actions.NotifyToChatAndFinish = (AutomaticActionsMethods.ChatFinishBehaviours) short.Parse(dropdownlistSecondAutomaticActionMarkAsFinishChat.SelectedValue);
                        at.Actions.NotifyToChatSettings.ReplyText = textboxSecondAutomaticActionChatReplyText.Text;

                        if (at.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@"))
                        {
                            at.Actions.NotifyToChatSettings.ReplyTextNoEwtAgents = textboxSecondAutomaticActionReplyEwtNoAgentsChat.Text;
                            at.Actions.NotifyToChatSettings.ReplyTextNoEwtComputed = textboxSecondAutomaticActionReplyEwtNotCalculatedChat.Text;
                        }

                        if (at.Actions.NotifyToChatSettings.ReplyText.Contains("@@EWT@@") || at.Actions.NotifyToChatSettings.ReplyText.Contains("@@POSMSGCOLA@@"))
                        {
                            at.Actions.NotifyToChatSettings.MinimumEWT = int.Parse(textboxSecondAutomaticActionMinimumEWTChat.Text);
                            at.Actions.NotifyToChatSettings.MinimumPositionOfMessageInQueue = int.Parse(textboxSecondAutomaticActionMinimumEnqueueMessagesChat.Text);
                        }
                    }
                }

                automaticActionsParameters.Add(at);

                #endregion

                queue.AutomaticActions.Refresh(automaticActionsParameters);


                #region Save Surveys

                if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
				{
					queue.SurveyEnabled = checkboxEnableSurveys.Checked;
					if (queue.SurveyEnabled)
					{
						queue.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(hiddenSurveyList.Value);
					}
				}

				#endregion

				#region Ewt
				if (checkboxConfigEwt.Checked)
				{
					queue.MinutesPredictedAht = int.Parse(textboxMinutesPredictedAht.Text);
					queue.SecondsEwt = int.Parse(textboxSecondsEwt.Text);
					queue.AllowToSetASAValueByDefault = checkboxASAPersonalized.Checked;
					if (queue.AllowToSetASAValueByDefault)
					{
                        queue.ASADefaultValue = int.Parse(textboxAsaBase.Text);
                    }
					else
					{
						queue.ASADefaultValue = 0;

                    }
					
                }
				queue.UseQueueEwtConfig = checkboxConfigEwt.Checked;
				#endregion

				QueueDAO dao = new QueueDAO(queue);
				if (this.Nuevo)
				{
					dao.Insert(this.EditEntities);
					var newParameters = queue.AsDictionary(true);
					UserLogDAO.Insert(this.LoggedUser, SystemEntityTypes.Queues, SystemActionTypes.Add, null, newParameters, queue.ID, queue.Name);
				}
				else
				{
					dao.Update(this.EditEntities);
					var newParameters = queue.AsDictionary(true);

					var entityName = oldParameters.ContainsKey("Name") ? oldParameters["Name"] : queue.Name;
					DAL.UserLogDAO.Insert(this.LoggedUser
						, SystemEntityTypes.Queues
						, SystemActionTypes.Edit
						, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, queue.ID
						, entityName);
				}

				Application.Lock();
				Application["QueuesJson"] = null;
				Application.UnLock();

				Response.Redirect(this.RedirectUrl);
			}
		}

		protected void buttonExport_Click(object sender, EventArgs e)
		{
			Response.Clear();
			Response.Buffer = true;
			Response.Charset = string.Empty;
			Response.BufferOutput = false;

			using (MemoryStream ms = new MemoryStream())
			{
				IEnumerable<Queue> queues = null;
				if (this.LoggedUser.HasPermission(Permissions.QueuesEdition))
					queues = DomainModel.Cache.Instance.GetList<Queue>();
				else
					queues = this.LoggedUser.Queues;

				queues = queues.Where(q => !q.Deleted).OrderBy(q => q.Name);
				DomainModel.Reports.Export.QueueExport export;
				export = (DomainModel.Reports.Export.QueueExport) DomainModel.Reports.Export.ReportExport.Create(DomainModel.Reports.ReportTypes.Queues);
				export.Format = (DomainModel.Reports.Export.ExportFormats) byte.Parse(hiddenExportFormat.Value);
				export.Email = this.LoggedUser.Email;
				export.Owner = this.LoggedUser;
				export.OwnerTimeZone = this.LoggedUser.GetTimeZoneToUse();
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				if (this.LoggedUser.Settings != null && this.LoggedUser.Settings.ContainsKey(DomainModel.User.Locale))
					export.Language = this.LoggedUser.Settings[DomainModel.User.Locale];
				export.Generate(queues, ms);

				if (Response.IsClientConnected)
				{
					switch (export.Format)
					{
						case DomainModel.Reports.Export.ExportFormats.Excel:
							Response.Headers.Add("content-disposition", $"attachment;filename={Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", export.Language, "Listado de colas")}.xlsx");
							Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
							break;
						case DomainModel.Reports.Export.ExportFormats.CSV:
							Response.Headers.Add("content-disposition", $"attachment;filename={Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", export.Language, "Listado de colas")}.csv");
							Response.ContentType = "text/csv";
							break;
						default:
							Response.Headers.Add("content-disposition", $"attachment;filename={Localizations.Instance.GetLocalizedString($"ReportTypes.{export.Type}", export.Language, "Listado de colas")}.txt");
							Response.ContentType = "text/plain";
							break;
					}

					ms.Position = 0;
					Response.AddHeader("content-length", ms.Length.ToString());

					using (BinaryReader br = new BinaryReader(ms))
					{
						byte[] buffer = new byte[1024];
						int read = br.Read(buffer, 0, buffer.Length);
						while (read > 0 && Response.IsClientConnected)
						{
							Response.OutputStream.Write(buffer, 0, read);
							Response.Flush();

							read = br.Read(buffer, 0, buffer.Length);
						}
					}
				}

				Response.Flush();
				Response.End();
			}
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object AddSurvey(QueueSurveyConfiguration surveyConfiguration, string dropdownlistQueueSurveyCloseConditionValue)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var survey = DomainModel.Cache.Instance.GetItem<Survey>(surveyConfiguration.SurveyID.ToString());
				surveyConfiguration.CloseCaseCondition = (QueueSurveyConfiguration.CloseCaseConditions) short.Parse(dropdownlistQueueSurveyCloseConditionValue);
				return new
				{
					Success = true,
					Survey = new
					{
						ID = survey.ID,
						Name = survey.Name,
						QueueSurveyConfiguration = surveyConfiguration
					}
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveQueues()
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				IEnumerable<DomainModel.Queue> allQueues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();

				IEnumerable<Queue> queues = null;
				if (user.HasPermission(Permissions.QueuesEdition))
					queues = allQueues;
				else
					queues = user.Queues;

				queues = queues.Where(q => !q.Deleted).OrderBy(q => q.Name);

				IEnumerable<DomainModel.Service> allServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				IEnumerable<DomainModel.QueueGroup> allQueueGroups = DomainModel.Cache.Instance.GetList<DomainModel.QueueGroup>();
				var fields = new List<string>();

				return new
				{
					Success = true,
					Queues = BuildQueues(queues, allQueues, allServices, allQueueGroups, fields),
					Fields = fields
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object QueueCanBeDeleted(int queueId)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var queue = DomainModel.Cache.Instance.GetItem<Queue>(queueId);
				bool canBeDeleted = Core.System.Instance.QueueService.CanDeleteQueue(queue);

				return new
				{
					Success = true,
					CanBeDeleted = canBeDeleted
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsQueueNameValid(string name, int? queueId)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var isValid = true;
				if (string.IsNullOrEmpty(name))
				{
					isValid = false;
				}
				else
				{
					var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
					if (queueId == null)
					{
						if (queues.Any(s => s != null && s.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase)))
							isValid = false;
					}
					else
					{
						if (queues.Any(s => s != null && s.ID != queueId.Value && s.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase)))
							isValid = false;
					}
				}

				return new
				{
					Success = true,
					IsValid = isValid
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object IsQueueKeyValid(string key, int? queueId)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var isValid = true;
				if (!string.IsNullOrEmpty(key))
				{
					var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
					if (queueId == null)
					{
						if (queues.Any(s => s != null &&
							s.Enabled &&
							!string.IsNullOrEmpty(s.Key) &&
							s.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase)))
							isValid = false;
					}
					else
					{
						if (queues.Any(s => s != null && 
							s.Enabled &&
							s.ID != queueId.Value &&
							!string.IsNullOrEmpty(s.Key) &&
							s.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase)))
							isValid = false;
					}
				}

				return new
				{
					Success = true,
					IsValid = isValid
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveQueue(int queueId)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				IEnumerable<DomainModel.Queue> allQueues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
				IEnumerable<DomainModel.Service> allServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				IEnumerable<DomainModel.QueueGroup> allQueueGroups = DomainModel.Cache.Instance.GetList<DomainModel.QueueGroup>();
				var queue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueId);

				if (queue == null)
					throw new ArgumentException("El código de cola es incorrecto", nameof(queueId));

				var fields = new List<string>();

				return new
				{
					Success = true,
					Queue = BuildQueue(queue, allQueues, allServices, allQueueGroups, fields, true)
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object SaveQueuesThatCanTransferToQueue(int queueId, int[] queueIdsThatCanTransfer)
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				IEnumerable<DomainModel.Queue> allQueues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
				IEnumerable<DomainModel.Service> allServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				var queue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueId);

				if (queue == null)
					throw new ArgumentException("El código de cola es incorrecto", nameof(queueId));

				var currentQueuesThatCanTransfer = allQueues.Where(q => 
					q.RelatedQueuesToReturnMessages != null && 
					q.RelatedQueuesToReturnMessages.Contains(queue)).Select(q => q.ID);

				if (queueIdsThatCanTransfer != null)
				{
					var newQueueIdsThatCanTransfer = queueIdsThatCanTransfer.Except(currentQueuesThatCanTransfer);
					foreach (var queueIdThatCanTransfer in newQueueIdsThatCanTransfer)
					{
						var queueThatCanTransfer = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueIdThatCanTransfer);
						if (queueThatCanTransfer != null)
						{
							var update = false;
							var oldParameters = queueThatCanTransfer.AsDictionary(true);

							if (!queueThatCanTransfer.AllowAgentsToSelectQueueOnReturnToQueue)
							{
								queueThatCanTransfer.AllowAgentsToSelectQueueOnReturnToQueue = true;
								update = true;
							}

							if (!queueThatCanTransfer.RelatedQueuesToReturnMessages.Contains(queue))
							{
								queueThatCanTransfer.RelatedQueuesToReturnMessages.Add(queue);
								update = true;
							}

							if (update)
							{
								new DAL.QueueDAO(queueThatCanTransfer).Update(Entities.None);
								var newParameters = queueThatCanTransfer.AsDictionary(true);

								var entityName = oldParameters.ContainsKey("Name") ? oldParameters["Name"] : queueThatCanTransfer.Name;
								DAL.UserLogDAO.Insert(user
									, SystemEntityTypes.Queues
									, SystemActionTypes.Edit
									, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
									, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
									, queueThatCanTransfer.ID
									, entityName);
							}
						}
					}
				}

				IEnumerable<int> removedQueueIdsThatCanTransfer = currentQueuesThatCanTransfer.Except(user.Queues.Select(q => q.ID));
				if (queueIdsThatCanTransfer != null)
					removedQueueIdsThatCanTransfer = currentQueuesThatCanTransfer.Except(queueIdsThatCanTransfer);
				foreach (var queueIdThatCanTransfer in removedQueueIdsThatCanTransfer)
				{
					var queueThatCanTransfer = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueIdThatCanTransfer);
					if (queueThatCanTransfer != null)
					{
						var update = false;
						var oldParameters = queueThatCanTransfer.AsDictionary(true);

						if (queueThatCanTransfer.RelatedQueuesToReturnMessages.Contains(queue))
						{
							queueThatCanTransfer.RelatedQueuesToReturnMessages.Remove(queue);
							update = true;
						}

						if (queueThatCanTransfer.RelatedQueuesToReturnMessages.Count == 0 &&
							queueThatCanTransfer.AllowAgentsToSelectQueueOnReturnToQueue)
						{
							queueThatCanTransfer.AllowAgentsToSelectQueueOnReturnToQueue = false;
							update = true;
						}
						
						if (update)
						{
							new DAL.QueueDAO(queueThatCanTransfer).Update(Entities.None);
							var newParameters = queueThatCanTransfer.AsDictionary(true);

							var entityName = oldParameters.ContainsKey("Name") ? oldParameters["Name"] : queueThatCanTransfer.Name;
							DAL.UserLogDAO.Insert(user
								, SystemEntityTypes.Queues
								, SystemActionTypes.Edit
								, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
								, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
								, queueThatCanTransfer.ID
								, entityName);

							HttpContext.Current.Application.Lock();
							HttpContext.Current.Application["Queues"] = null;
							HttpContext.Current.Application.UnLock();
						}
					}
				}

				IEnumerable<Queue> queues = null;
				if (user.HasPermission(Permissions.QueuesEdition))
					queues = allQueues;
				else
					queues = user.Queues;

				queues = queues.Where(q => !q.Deleted).OrderBy(q => q.Name);

				IEnumerable<DomainModel.QueueGroup> allQueueGroups = DomainModel.Cache.Instance.GetList<DomainModel.QueueGroup>();

				var fields = new List<string>();

				return new
				{
					Success = true,
					Queues = BuildQueues(queues, allQueues, allServices, allQueueGroups, fields),
					Fields = fields
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

        [System.Web.Services.WebMethod]
        public static object RetrieveTags(int[] tagsIds, int[] tagsToIgnoreIds)
        {
            try
            {
                DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
                if (user == null)
                    throw new UserNotLoggedException("No hay ningún usuario logueado");

				var tagsIdsList = new List<int>();
				var tagsToIgnoreIdsList = new List<int>();
                
				if (tagsIds != null && tagsIds.Length > 0)
                    tagsIdsList = tagsIds.ToList();
               
				if (tagsToIgnoreIds != null && tagsToIgnoreIds.Length > 0)
                    tagsToIgnoreIdsList = tagsToIgnoreIds.ToList();

                return new
                {
                    Success = true,
                    Tags = BuildTagsByIds(tagsIdsList),
                    TagsToIgnore = BuildTagsByIds(tagsToIgnoreIdsList)
                };
            }
            catch (UserNotLoggedException ex)
            {
                return new
                {
                    Success = false,
                    Error = new
                    {
                        Message = ex.Message,
                        NotLogged = true
                    }
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    Success = false,
                    Error = new
                    {
                        Message = ex.Message,
                        StackTrace = ex.StackTrace
                    }
                };
            }
        }

        #endregion
    }
}