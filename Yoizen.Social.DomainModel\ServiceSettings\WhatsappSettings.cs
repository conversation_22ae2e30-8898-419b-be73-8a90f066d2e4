﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.DomainModel.Whatsapp;
using Yoizen.Social.DomainModel.Whatsapp.Flows;

namespace Yoizen.Social.DomainModel.ServiceSettings
{
	public class WhatsappSettings : ServiceSettings
	{
		#region Inner Classes

		public class HSMTemplateParameter
		{
			#region Properties

			/// <summary>
			/// Devuelve o establece el nombre del parámetro
			/// </summary>
			public string Name { get; set; }

			/// <summary>
			/// Devuelve o establece la descripción del parámetro
			/// </summary>
			public string Description { get; set; }

			#endregion

			#region Constructors

			/// <summary>
			/// Inicializa una nueva instancia de HSMTemplateParameter
			/// </summary>
			public HSMTemplateParameter()
			{
			}

			/// <summary>
			/// Inicializa una nueva instancia de HSMTemplateParameter
			/// </summary>
			/// <param name="parameter">Los datos de un parámetro ingresados como [nombre]=[descripción]</param>
			public HSMTemplateParameter(string parameter)
			{
				var index = parameter.IndexOf("=");
				if (index >= 0)
				{
					this.Name = parameter.Substring(0, index);
					this.Description = parameter.Substring(index + 1);
				}
				else
				{
					this.Name = parameter;
					this.Description = parameter;
				}
			}

			#endregion
		}

		/// <summary>
		/// Representa un botón dentro de los HSM
		/// </summary>
		public class HSMTemplateButton
		{
			#region Properties

			/// <summary>
			/// Devuelve o establece el texto del botón
			/// </summary>
			public string Text { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de botón para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>
			/// </summary>
			public HSMTemplateCallToActionButtonTypes? CallToActionButtonType { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de botón para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.AuthCode"/>
			/// </summary>
			public HSMTemplateAuthCodeButtonTypes? AuthCodeButtonType { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de botón de url para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/> y para cuando <see cref="CallToActionButtonType"/> sea
			/// <see cref="HSMTemplateCallToActionButtonTypes.Url"/>
			/// </summary>
			public HSMTemplateCallToActionUrlButtonTypes? UrlButtonType { get; set; }

			/// <summary>
			/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
			/// <see cref="HSMTemplateCallToActionButtonTypes.Url"/> y para cuando <see cref="UrlButtonType"/> sea
			/// <see cref="HSMTemplateCallToActionUrlButtonTypes.Dynamic"/>
			/// </summary>
			public HSMTemplateParameter UrlParameter { get; set; }

			/// <summary>
			/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.QuickReply"/>
			/// </summary>
			public HSMTemplateParameter QuickReplyParameter { get; set; }

			/// <summary>
			/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
			/// <see cref="HSMTemplateCallToActionButtonTypes.OfferCode"/> 
			/// </summary>
			public HSMTemplateParameter OfferCodeParameter { get; set; }

			/// <summary>
			/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.AuthCode"/>, para cuando <see cref="AuthCodeButtonType"/> sea
			/// <see cref="HSMTemplateAuthCodeButtonTypes.AuthCode"/> 
			/// </summary>
			public HSMTemplateParameter AuthCodeParameter { get; set; }

			/// <summary>
			/// /// Devuelve o esstablece los parámetros que se utiliza para cuando <see cref="HSMTemplate.ButtonsType"/> del template
			/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
			/// <see cref="HSMTemplateCallToActionButtonTypes.Flow"/> 
			/// </summary>
			public HSMTemplateFlowParameters FlowParameter { get; set; }

			#endregion
		}

		/// <summary>
		/// Definición de un mensaje de plantilla (HSM) de Whatsapp
		/// </summary>
		public class HSMTemplate
		{
			#region Properties

			/// <summary>
			/// Devuelve o establece la descripción de la plantilla
			/// </summary>
			public string Description { get; set; }

			/// <summary>
			/// Devuelve o establece el <code>namespace</code> de la plantilla
			/// </summary>
			public string Namespace { get; set; }

			/// <summary>
			/// Devuelve o establece el <code>element_name</code> de la plantilla
			/// </summary>
			public string ElementName { get; set; }

			/// <summary>
			/// Devuelve o establece el <code>category</code> de la plantilla
			/// </summary>
			public string Category { get; set; }

			/// <summary>
			/// Devuelve o establece el lenguage del template
			/// </summary>
			public string Language { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de encabezado
			/// </summary>
			public HSMTemplateHeaderTypes HeaderType { get; set; }

			/// <summary>
			/// Devuelve o esstablece el texto del encabezado de la plantilla para cuando <see cref="HeaderType"/>
			/// sea <see cref="HSMTemplateHeaderTypes.Text"/>
			/// </summary>
			public string HeaderText { get; set; }

			/// <summary>
			/// Devuelve o esstablece el parámetro que se utiliza en el encabezado para cuando <see cref="HeaderType"/>
			/// sea <see cref="HSMTemplateHeaderTypes.Text"/> y el texto contenga algún parámetro
			/// </summary>
			public HSMTemplateParameter HeaderTextParameter { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de multimedia del encabezado de la plantilla para cuando <see cref="HeaderType"/>
			/// sea <see cref="HSMTemplateHeaderTypes.Media"/>
			/// </summary>
			public HSMTemplateHeaderMediaTypes HeaderMediaType { get; set; }

			/// <summary>
			/// Devuelve o establece la URL de la imagen del encabezado, para cuando <see cref="HeaderType"/>
			/// sea <see cref="HSMTemplateHeaderTypes.Media"/>
			/// </summary>
			public string HeaderMediaUrl { get; set; }

			/// <summary>
			/// Devuelve o establece el texto de la plantilla
			/// </summary>
			public string Template { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de pie
			/// </summary>
			public HSMTemplateFooterTypes FooterType { get; set; }

			/// <summary>
			/// Devuelve o establece el pie de la plantilla
			/// </summary>
			public string FooterText { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de botones que incluirá
			/// </summary>
			public HSMTemplateButtonsTypes ButtonsType { get; set; }

			/// <summary>
			/// Devuelve o establece los botones de la plantilla
			/// </summary>
			public HSMTemplateButton[] Buttons { get; set; }

			/// <summary>
			/// Devuelve o establece los parámetros de la plantilla
			/// </summary>
			public string[] Parameters { get; set; }

			/// <summary>
			/// Devuelve o establece los parámetros de la plantilla
			/// </summary>
			public HSMTemplateParameter[] TemplateParameters
			{
				get
				{
					if (this.Parameters == null || this.Parameters.Length == 0)
						return null;

					return this.Parameters.Select(p => new HSMTemplateParameter(p)).ToArray();
				}
			}

			/// <summary>
			/// Devuelve o establece si el template puede ser usado por agentes
			/// </summary>
			public bool AvaiableForAgents { get; set; }

			/// <summary>
			/// Devuelve o establece si el template puede ser usado por supervisores
			/// </summary>
			public bool AvaiableForSupervisors { get; set; }

			/// <summary>
			/// Devuelve o establece si el template puede ser usado por integración
			/// </summary>
			public bool AvaiableForIntegrations { get; set; }

			/// <summary>
			/// Devuelve o establece si el template puede ser enviado si el usuario tiene caso abierto
			/// </summary>
			public bool AllowToConfigureSendHSMIfCaseOpen { get; set; }

			/// <summary>
			/// Devuelve o establece la latitud para cuando <see cref="HeaderType"/> sea <see cref="HSMTemplateHeaderTypes.Location"/>
			/// </summary>
			public double? HeaderLocationLatitude { get; set; }

			/// <summary>
			/// Devuelve o establece la longitud para cuando <see cref="HeaderType"/> sea <see cref="HSMTemplateHeaderTypes.Location"/>
			/// </summary>
			public double? HeaderLocationLongitude { get; set; }

			#endregion

			#region Constructors

			public HSMTemplate()
			{
				this.HeaderType = HSMTemplateHeaderTypes.None;
				this.HeaderText = null;
				this.HeaderMediaType = HSMTemplateHeaderMediaTypes.None;
				this.HeaderMediaUrl = null;
				this.Buttons = null;
				this.AvaiableForAgents = true;
				this.AvaiableForIntegrations = true;
				this.AvaiableForSupervisors = true;
				this.AllowToConfigureSendHSMIfCaseOpen = true;
				this.FooterType = HSMTemplateFooterTypes.None;
				this.ButtonsType = HSMTemplateButtonsTypes.None;
				this.HeaderLocationLatitude = null;
				this.HeaderLocationLongitude = null;
			}

			#endregion
		}

		public class InteractiveMessage
		{

		}

		/// <summary>
		/// Definición de una propiedad del payload
		/// </summary>
		public class PayloadProperty
		{
			/// <summary>
			/// Devuelve o establece el nombre de la propiedad
			/// </summary>
			[Newtonsoft.Json.JsonProperty("name")]
			public string Name { get; set; }

			/// <summary>
			/// Devuelve o establece la descripción de la propiedad
			/// </summary>
			[Newtonsoft.Json.JsonProperty("description")]
			public string Description { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de la propiedad
			/// </summary>
			[Newtonsoft.Json.JsonProperty("type")]
			[Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
			public PayloadPropertyTypes Type { get; set; }

			/// <summary>
			/// Devuelve o establece si se utilizará para derivar
			/// </summary>
			[Newtonsoft.Json.JsonProperty("useForDerivation")]
			public bool UseForDerivation { get; set; }

			/// <summary>
			/// Devuelve o establece si se mostrará el valor de la propiedad a los agentes
			/// </summary>
			[Newtonsoft.Json.JsonProperty("showToAgents")]
			public bool ShowToAgents { get; set; }
		}

		public class HSMTemplatesForAgentConverter : Newtonsoft.Json.JsonConverter
		{
			public override bool CanRead => false;

			public override bool CanConvert(Type objectType)
			{
				if (objectType == typeof(HSMTemplate[]))
					return true;
				return false;
			}

			public override object ReadJson(Newtonsoft.Json.JsonReader reader, Type objectType, object existingValue, Newtonsoft.Json.JsonSerializer serializer)
			{
				throw new NotImplementedException();
			}

			public override void WriteJson(Newtonsoft.Json.JsonWriter writer, object value, Newtonsoft.Json.JsonSerializer serializer)
			{
				if (value == null)
				{
					writer.WriteNull();
					return;
				}

				IEnumerable<HSMTemplate> templates = (HSMTemplate[]) value;
				templates = templates.Where(t => t.AvaiableForAgents);

				serializer.Serialize(writer, templates);
			}
		}

		#endregion

		#region Enums

		/// <summary>
		/// Enumeración con los posibles tipos de integraciones que soporta el servicio de whatsapp
		/// </summary>
		public enum IntegrationTypes : byte
		{
			/// <summary>
			/// La integración con Whatsapp es a través de botmaker
			/// </summary>
			BotMaker = 2,

			/// <summary>
			/// La integración con Whatsapp es a través endpoints configurables
			/// </summary>
			Postback = 3,

			/// <summary>
			/// La integración con Whatsapp es a través de infobip
			/// </summary>
			Infobip = 4,

			/// <summary>
			/// La integración con Whatsapp es a través de wavy
			/// </summary>
			Wavy = 5,

			/// <summary>
			/// La integración con Whatsapp es a través de movistar
			/// </summary>
			Movistar = 6,

			/// <summary>
			/// La integración con Whatsapp es a través de interaxa
			/// </summary>
			Interaxa = 7,

			/// <summary>
			/// La integración con Whatsapp es a través de gupshup
			/// </summary>
			Gupshup = 8,

			/// <summary>
			/// La integración con Whatsapp es a través de twilio
			/// </summary>
			Twilio = 9,

			/// <summary>
			/// La integración con Whatsapp es a través de Yoizen
			/// </summary>
			Yoizen = 10,

			/// <summary>
			/// La integración con Whatsapp es a través de Cloud API
			/// </summary>
			CloudApi = 11
		}

		/// <summary>
		/// Enumeración con los posibles tipos de integraciones que soporta el servicio de whatsapp
		/// </summary>
		public enum IntegrationWithPostbackPullTypes : byte
		{
			/// <summary>
			/// La integración con Postback no va a traer novedades a través de servicio
			/// </summary>
			NoPull = 1,

			/// <summary>
			/// La integración con Postback va a traer novedades desde URLs específicas
			/// </summary>
			SpecificUrls = 2,

			/// <summary>
			/// La integración con Postback va a traer novedades desde las URLs por defecto
			/// </summary>
			DefaultUrls = 3
		}

		/// <summary>
		/// Enumeración con los posibles tipos de datos de las propiedades del Payload
		/// </summary>
		public enum PayloadPropertyTypes
		{
			/// <summary>
			/// Tipo de dato texto
			/// </summary>
			[System.Runtime.Serialization.EnumMember(Value = "string")]
			String = 1,

			/// <summary>
			/// Tipo de dato fecha
			/// </summary>
			[System.Runtime.Serialization.EnumMember(Value = "date")]
			Date = 2,

			/// <summary>
			/// Tipo de dato numérico
			/// </summary>
			[System.Runtime.Serialization.EnumMember(Value = "integer")]
			Number = 3,

			/// <summary>
			/// Tipo de dato booleano
			/// </summary>
			[System.Runtime.Serialization.EnumMember(Value = "boolean")]
			Boolean = 4,

			/// <summary>
			/// Tipo de dato unix timestamp
			/// </summary>
			[System.Runtime.Serialization.EnumMember(Value = "timestamp")]
			UnixTimestamp = 5
		}

		/// <summary>
		/// Enumeración con los posibles comportamientos de asignación de llamadas a los agentes
		/// </summary>
		public enum VoiceCallsAgentsBehaviours : byte
		{
			/// <summary>
			/// Indica que la configuración debe ser la misma que el sistema
			/// </summary>
			/// <remarks>
			/// Para ser usado por los agentes
			/// </remarks>
			SameAsSystem = 0,

			/// <summary>
			/// El agente recibirá la llamada pero pasará a pendiente de auxiliar
			/// </summary>
			SwitchToPending = 1,

			/// <summary>
			/// El agente no podrá recibir llamadas si se encuentra con más de un mensaje asignado
			/// </summary>
			DoNotReceiveWithMessages = 2,

			/// <summary>
			/// El agente recibirá la llamada (siempre y cuando no esté con otra llamada)
			/// </summary>
			Receive = 3
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve el tipo de servicio asociado a los parámetros definidos en la instancia
		/// </summary>
		public override ServiceTypes ServiceType { get { return ServiceTypes.WhatsApp; } }

		/// <summary>
		/// Devuelve o establece el tipo de integración
		/// </summary>
		public byte IntegrationType { get; set; }

		/// <summary>
		/// Devuelve o establece si el servicio de Whatsapp permite mandar archivos multimedia
		/// </summary>
		public bool AllowToSendMultimedia { get; set; }

		/// <summary>
		/// Devuelve si el servicio soporta enviar archivos adjuntos
		/// </summary>
		public override bool AllowToSendAttachments { get { return this.AllowToSendMultimedia; } }

		/// <summary>
		/// Devuelve o establece la configuración de archivos adjuntos
		/// </summary>
		public WhatsappAttachmentsSettings Attachments { get; set; }

		/// <summary>
		/// Devuelve o establece el código de país por defecto
		/// </summary>
		public int DefaultInternationCode { get; set; }

		/// <summary>
		/// Devuelve o establece los minutos que deben pasar para considerar inactivo el servicio indicando que puede haber un problema
		/// </summary>
		[LocalizedDescription("WhatsappSettings_MinutesForInactivity")]
		public int MinutesForInactivity { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de mail para cuando se detecta inactividad en Whatsapp
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("WhatsappSettings_InactivityDetected")]
		public Settings.EmailSettings InactivityDetected { get; set; }

		/// <summary>
		/// Devuelve o establece si se responderán los mensajes dentro de la ventana de 24 horas
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeMaxTimeToAnswer")]
		public bool AutoReplyBeforeMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece el texto de la respuesta de los mensajes dentro de la ventana de 24 horas cuando <see cref="AutoReplyBeforeMaxTimeToAnswer"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeMaxTimeToAnswerText")]
		public string AutoReplyBeforeMaxTimeToAnswerText { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos previos a las 24 horas para enviar la respuesta siempre y cuando
		/// <see cref="AutoReplyBeforeMaxTimeToAnswer"/> sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeMaxTimeToAnswerMinutes")]
		public short AutoReplyBeforeMaxTimeToAnswerMinutes { get; set; }

		/// <summary>
		/// Devuelve o establece si se descartarán los mensajes cuando se cumplan 24 horas de encolado
		/// </summary>
		[LocalizedDescription("WhatsappSettings_DiscardAfterMaxTimeToAnswer")]
		public bool DiscardAfterMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece si se cerrará el caso al descartar los mensajes cuando se cumplan 24 horas de encolado
		/// </summary>
		/// <remarks>
		/// Esta propiedad depende que <see cref=" DiscardAfterMaxTimeToAnswer"/> sea <code>true</code>
		/// </remarks>
		[LocalizedDescription("WhatsappSettings_DiscardAndCloseCaseAfterMaxTimeToAnswer")]
		public bool DiscardAndCloseCaseAfterMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece si se enviara algun mensaje antes de que el caso se cierre por inactividad
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeCaseIsClosed")]
		public bool UseAutoReplyBeforeCloseCase { get; set; }

		/// <summary>
		/// Devuelve o establece el texto de la respuesta de los mensajes dentro de la ventana de  <see cref="AutoReplyBeforeCaseIsClosed"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeCaseIsClosedText")]
		public string AutoReplyBeforeCloseCaseText { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos previos a que se cierre el caso por inactividad siempre y cuando
		/// <see cref="AutoReplyBeforeCaseIsClosed"/> sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AutoReplyBeforeMaxTimeToAnswerMinutes")]
		public short AutoReplyBeforeCloseCaseMinutes { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite enviar mensajes de plantilla (HSM)
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AllowToSendHSM")]
		public bool AllowToSendHSM { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite enviar flows de Whatsapp
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AllowToSendFlows")]
		public bool AllowToSendFlows { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite a los agentes enviar mensajes de plantilla (HSM) simpre y cuando <see cref="AllowToSendHSM"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AllowAgentsToSendHSM")]
		public bool AllowAgentsToSendHSM { get; set; }
		/// <summary>
		/// Devuelve o establece si se permite enviar mensajes de plantilla (HSM) cuando el usuario tiene un caso abierto, simpre y cuando <see cref="AllowToSendHSM"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AllowToConfigureSendHSMIfCaseOpen")]
		public bool AllowToConfigureSendHSMIfCaseOpen { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de las plantillas de mensaje (HSM)
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("WhatsappSettings_HSMTemplates")]
		public HSMTemplate[] HSMTemplates { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite enviar encuestas a mensajes de Whatsapp
		/// </summary>
		[LocalizedDescription("WhatsappSettings_AllowSurveys")]
		public bool AllowSurveys { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de los flows de meta
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("WhatsappSettings_Flows")]
		public Flow[] MetaFlows { get; set; }

		/// <summary>
		/// Devuelve si el servicio de WhatsApp tiene habilitado la creacion de Videollamadas desde el agente
		/// </summary>
		[LocalizedDescription("WhatsappSettings_EnableVideo")]
		public bool EnableVideo { get; set; }

		/// <summary>
		/// Devuelve o establece si el servicio de WhatsApp tiene habilitado las llamadas de voz
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsEnabled")]
		public bool VoiceCallsEnabled { get; set; }

		/// <summary>
		/// Devuelve o establece si el servicio de WhatsApp tiene habilitado la grabación de llamadas de voz
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsRecordingEnabled")]
		public bool VoiceCallsRecordingEnabled { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para invitar al usuario a iniciar la llamada de voz
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageInvite")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessageVoiceCall VoiceCallsInteractiveMessageInvite { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para enviar cuando se rechaza una llamada y el agente no está disponible
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageRejectAgentNotAvailable")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessage VoiceCallsInteractiveMessageRejectAgentNotAvailable { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para enviar cuando se rechaza una llamada y el agente está en otra llamada
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageRejectAgentWithAnotherCall")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessage VoiceCallsInteractiveMessageRejectAgentWithAnotherCall { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para enviar cuando se rechaza una llamada porque el caso del usuario no está asignado
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessage VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para enviar cuando se rechaza una llamada porque el caso todavía no tuvo una invitación para la llamada
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageRejectCaseWithoutInvite")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessage VoiceCallsInteractiveMessageRejectCaseWithoutInvite { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje interactivo para enviar cuando se rechaza una llamada porque el caso ya tiene una llamada en curso
		/// </summary>
		[LocalizedDescription("WhatsappSettings_VoiceCallsInteractiveMessageRejectCaseWithCurrentCall")]
		[Newtonsoft.Json.JsonConverter(typeof(DomainModel.Whatsapp.InteractiveMessage.InteractiveMessageConverter))]
		public DomainModel.Whatsapp.InteractiveMessage VoiceCallsInteractiveMessageRejectCaseWithCurrentCall { get; set; }

		/// <summary>
		/// Devuelve o establece si los agentes pueden enviar la invitación a la llamada
		/// </summary>
		[Obsolete("La propiedad no se utiliza ya que se evalua el permiso del agente para enviar invite", true)]
		[LocalizedDescription("WhatsappSettings_VoiceCallsAllowAgentsToSendInteractiveMessage")]
		public bool VoiceCallsAllowAgentsToSendInteractiveMessage { get; set; }

		/// <summary>
		/// Devuelve o establece si se permitirán configurar Conversations API
		/// </summary>
		[LocalizedDescription("WhatsappSettings_EnableCapi")]
		public bool EnableCapi { get; set; }

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de CasesSettings
		/// </summary>
		public WhatsappSettings()
		{
			this.AllowToSendMultimedia = false;
			this.DefaultInternationCode = 54;
			this.Attachments = new WhatsappAttachmentsSettings();
			this.InactivityDetected = new Settings.EmailSettings("Whatsapp.InactivityDetected");
			this.MinutesForInactivity = 1440;

#if !DEBUG
			this.InactivityDetected.Emails = "<EMAIL>";
#endif
			this.InactivityDetected.Subject = "Se detectó inactividad en el servicio de Whatsapp";
			this.InactivityDetected.Template = "<div style=\"font-family: 'Trebuchet MS';\">El @@FECHA@@ se detectó que no se reciben novedades del servicio de Whatsapp @@SERVICIO@@ desde hace @@MINUTOS@@ minutos.</div>";
#if DEBUG
			this.MinutesForInactivity = 999999;
#endif

			this.AutoReplyBeforeMaxTimeToAnswer = false;
			this.AutoReplyBeforeMaxTimeToAnswerText = string.Empty;
			this.AutoReplyBeforeMaxTimeToAnswerMinutes = 60;
			this.DiscardAfterMaxTimeToAnswer = false;
			this.DiscardAndCloseCaseAfterMaxTimeToAnswer = false;
			this.AllowToSendHSM = false;
			this.AllowToSendFlows = false;
			this.AllowAgentsToSendHSM = false;
			this.HSMTemplates = null;
			this.AllowSurveys = false;
			this.IntegrationType = (byte) IntegrationTypes.Yoizen;
			this.UseAutoReplyBeforeCloseCase = false;
			this.MetaFlows = null;
			this.EnableVideo = false;
			this.VoiceCallsEnabled = false;
			this.VoiceCallsRecordingEnabled = false;
			this.EnableCapi = false;
		}

		#endregion

		#region Internal Methods

		[OnDeserialized]
		internal void OnDeserializedMethod(StreamingContext context)
		{
			if (this.Attachments == null)
				this.Attachments = new WhatsappAttachmentsSettings();
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Devuelve el <see cref="HSMTemplate"/> a partir de un espacio de nombres, nombre de elemento y lenguaje
		/// </summary>
		/// <param name="namespace">El espacio de nombres</param>
		/// <param name="elementName">El nombre de elemento</param>
		/// <param name="language">El lenguaje</param>
		/// <returns>Un <see cref="HSMTemplate"/> que corresponde a los filtros o <code>null</code> si no se encontró</returns>
		public HSMTemplate FindTemplate(string @namespace, string elementName, string language)
		{
			if (this.HSMTemplates == null || this.HSMTemplates.Length == 0)
				return null;

			try
			{
				return this.HSMTemplates.First(t =>
					t.Namespace.Equals(@namespace) &&
					t.ElementName.Equals(elementName) &&
					t.Language.Equals(language));
			}
			catch
			{
				return null;
			}
		}

		/// <summary>
		/// Devuelve el <see cref="HSMTemplate"/> a partir de un espacio de nombres, nombre de elemento y lenguaje
		/// </summary>
		/// <param name="namespace">El espacio de nombres</param>
		/// <param name="elementName">El nombre de elemento</param>
		/// <param name="language">El lenguaje</param>
		/// <returns>Un <see cref="HSMTemplate"/> que corresponde a los filtros o <code>null</code> si no se encontró</returns>
		public HSMTemplate FindTemplate(string elementName, string language)
		{
			if (this.HSMTemplates == null || this.HSMTemplates.Length == 0)
				return null;

			try
			{
				return this.HSMTemplates.First(t =>
					t.ElementName.Equals(elementName) &&
					t.Language.Equals(language));
			}
			catch
			{
				return null;
			}
		}

		public override bool HasEmailConnnectionInUse(Guid emailId)
		{
			if (!string.IsNullOrEmpty(this.InactivityDetected.EmailConnection) &&
				Guid.TryParse(this.InactivityDetected.EmailConnection, out Guid inactivityGuid))
			{
				return emailId == inactivityGuid;
			}

			return false;
		}

		public string GenerateBodyFromParameters(string parameters, HSMTemplate template)
		{
			string body = string.Empty;

			if (template != null)
			{
				body = template.Template;
				if (template.Parameters != null &&
					template.Parameters.Length > 0 &&
					!string.IsNullOrEmpty(parameters))
				{
					var parametersValues = JArray.Parse(parameters);

					for (var i = 0; i < template.Parameters.Length; i++)
					{
						var parameterName = template.Parameters[i].Substring(0, template.Parameters[i].IndexOf('='));
						body = body.Replace("{{" + parameterName + "}}", parametersValues[i].ToString());
					}
				}
			}

			return body;
		}

		public Flow FindFlow(string id)
		{
			if (this.MetaFlows == null || this.MetaFlows.Length == 0)
				return null;

			try
			{
				return this.MetaFlows.First(f =>
					f.ID.Equals(id));
			}
			catch
			{
				return null;
			}
		}

		/// <summary>
		/// Retorna un JArray con las screens del Flow.
		/// </summary>
		/// <param name="flowId"></param>
		/// <param name="screenName">para retornar unicamente la pantalla indicada en caso de ser null devuelve todas</param>
		/// <returns></returns>
		public async Task<FlowScreen[]> FindFlowScreensDataAsync(string flowId, string screenName)
		{
			if (this.MetaFlows == null || this.MetaFlows.Length == 0)
				return null;

			try
			{
				var flow = this.MetaFlows.FirstOrDefault(f => f.ID.Equals(flowId));

				if (flow == null)
					return null;

				if (flow.Data == null)
					return null;

				var url = flow.Data.DownloadUrl;

				if (string.IsNullOrEmpty(url))
					return null;

				using (var httpClient = new System.Net.Http.HttpClient())
				{
					using (var response = await httpClient.GetAsync(url))
					{
						if (response.IsSuccessStatusCode)
						{
							var contents = await response.Content.ReadAsStringAsync();
							var jFlowJson = Newtonsoft.Json.Linq.JObject.Parse(contents);

							JArray flowDataToFill = new JArray();
							if (jFlowJson["screens"] != null && jFlowJson["screens"].Type == JTokenType.Array)
							{
								JArray screens = jFlowJson["screens"].ToObject<JArray>();

								if (string.IsNullOrEmpty(screenName))
								{
									return screens.Select(s => new FlowScreen
									{
										ID = (string) s["id"],
										Title = (string) s["title"],
										Data = (JObject) s["data"]
									}).ToArray();
								}
								else
								{
									JToken screen = screens.FirstOrDefault(s => (string) s["id"] == screenName);
									if (screen != null && screen.HasValues)
									{
										return new FlowScreen[] { new FlowScreen
										{
											ID = (string)screen["id"],
											Title = (string)screen["title"],
											Data =  (JObject) screen["data"]
										}};
									}
								}
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error al obtener la data del Flow con ID: {0}. Detalles: {1}", flowId, ex.Message);
			}

			return null;
		}

		/// <summary>
		/// Retorna un JArray con las screens del Flow.
		/// </summary>
		/// <param name="flowId"></param>
		/// <param name="screenName">para retornar unicamente la pantalla indicada en caso de ser null devuelve todas</param>
		/// <returns></returns>
		public FlowScreen[] FindFlowScreensData(string flowId, string screenName)
		{
			if (this.MetaFlows == null || this.MetaFlows.Length == 0)
				return null;

			try
			{
				var flow = this.MetaFlows.FirstOrDefault(f => f.ID.Equals(flowId));

				if (flow == null)
				{
					Tracer.TraceVerb("No se encontró el flujo con id={0}", flowId);
					return null;
				}

				if (flow.Data == null)
				{
					Tracer.TraceVerb("El flujo con id={0} no tiene definido data", flowId);
					return null;
				}

				var url = flow.Data.DownloadUrl;

				if (string.IsNullOrEmpty(url))
				{
					Tracer.TraceVerb("El flujo con id={0} no tiene definido la url para descargar la definición", flowId);
					return null;
				}

				using (var httpClient = new System.Net.Http.HttpClient())
				{
					using (var response = httpClient.GetAsync(url).Result)
					{
						if (response.IsSuccessStatusCode)
						{
							var contents = response.Content.ReadAsStringAsync().Result;
							var jFlowJson = Newtonsoft.Json.Linq.JObject.Parse(contents);

							JArray flowDataToFill = new JArray();
							if (jFlowJson["screens"] != null && jFlowJson["screens"].Type == JTokenType.Array)
							{
								JArray screens = jFlowJson["screens"].ToObject<JArray>();

								if (string.IsNullOrEmpty(screenName))
								{
									return screens.Select(s => new FlowScreen
									{
										ID = (string) s["id"],
										Title = (string) s["title"],
										Data = (JObject) s["data"]
									}).ToArray();
								}
								else
								{
									JToken screen = screens.FirstOrDefault(s => s["id"].ToString().Equals(screenName));
									if (screen != null && screen.HasValues)
									{
										return new FlowScreen[] { new FlowScreen
										{
											ID = (string) screen["id"],
											Title = (string) screen["title"],
											Data =  (JObject) screen["data"]
										}};
									}
								}
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error al obtener la data del Flow con ID: {0}. Detalles: {1}", flowId, ex.Message);
			}

			return null;
		}

		public bool TemplateHasFlowWithParams(HSMTemplate template)
		{
			if (template == null)
				return false;

			if (template.Buttons.Length == 0)
				return false;

			var flowButton = template.Buttons.FirstOrDefault(b => b.FlowParameter != null);

			if (flowButton != null) 
			{
				var flowParam = flowButton.FlowParameter;
				
				var screenData = FindFlowScreensData(flowParam.FlowID, flowParam.NavigateScreen);

				return screenData != null && screenData.Length > 0;
			}

			return false;
		}

		/// <summary>
		/// Retorna los parámetros relacionados a enviar mensaje previo a cerrar el caso 
		/// </summary>
		/// <param name="text">Indica el texto del mensaje a agregar, cuando se utilice la funcionalidad</param>
		/// <param name="minutes">Indica cuántos minutos antes del cierre del caso se enviará el mensaje, cuando se utilice la funcionalidad</param>
		/// <returns><code>true</code> si se utilizará la funcionalidad de agregar mensaje al caso minutos antes del cierre; en caso contrario, <code>false</code></returns>
		public override bool UseAutoReplyBeforeCaseClose(out string text, out int minutes)
		{
			text = this.AutoReplyBeforeCloseCaseText;
			minutes = this.AutoReplyBeforeCloseCaseMinutes;

			return this.UseAutoReplyBeforeCloseCase;
		}

		#endregion
	}
}
