﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Configuration
{


	public partial class ServicesWhatsapp
	{

		/// <summary>
		/// messageFacebookNoBusinesses control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookNoBusinesses;

		/// <summary>
		/// messageFacebookBusinessCatalogNoProducts control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookBusinessCatalogNoProducts;

		/// <summary>
		/// messageFacebookNoBusinessCatalogs control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookNoBusinessCatalogs;

		/// <summary>
		/// messageFacebookUrlInvalid control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookUrlInvalid;

		/// <summary>
		/// messageFacebookAccountError control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookAccountError;

		/// <summary>
		/// messageFacebookCouldntValidateAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookCouldntValidateAccessToken;

		/// <summary>
		/// messageMustSelectBusiness control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMustSelectBusiness;

		/// <summary>
		/// messageMustSelectCatalog control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageMustSelectCatalog;

		/// <summary>
		/// hiddenTab control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenTab;

		/// <summary>
		/// liTabVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabVideo;

		/// <summary>
		/// liTabVoiceCalls control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl liTabVoiceCalls;

		/// <summary>
		/// textboxServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxServiceName;

		/// <summary>
		/// customvalidatorServiceName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorServiceName;

		/// <summary>
		/// dropdownlistWhatsAppCountries control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsAppCountries;

		/// <summary>
		/// textboxWhatsAppSelectedPhoneCode control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppSelectedPhoneCode;

		/// <summary>
		/// textboxWhatsAppPhoneNumber control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppPhoneNumber;

		/// <summary>
		/// customvalidatorPhoneNumber control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator customvalidatorPhoneNumber;

		/// <summary>
		/// textboxWhatsAppFromDate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsAppFromDate;

		/// <summary>
		/// dropdownlistWhatsAppQueue control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistWhatsAppQueue;

		/// <summary>
		/// placeholderWhatsAppCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderWhatsAppCheckSpelling;

		/// <summary>
		/// checkboxWhatsAppCheckSpelling control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsAppCheckSpelling;

		/// <summary>
		/// placeholderAllowYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowYFlow;

		/// <summary>
		/// dropdownlistUseYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistUseYFlow;

		/// <summary>
		/// placeholderActAsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderActAsChat;

		/// <summary>
		/// checkboxActAsChat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxActAsChat;

		/// <summary>
		/// checkboxAllowToReplyToSpecificMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowToReplyToSpecificMessage;

		/// <summary>
		/// dropdownlistIntegrationType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType;

		/// <summary>
		/// textboxIntegrationType10AccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType10AccessToken;

		/// <summary>
		/// textboxIntegrationType10BaseUrl control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType10BaseUrl;

		/// <summary>
		/// placeholderIntegrationType10SendToServiceBus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderIntegrationType10SendToServiceBus;

		/// <summary>
		/// checkboxIntegrationType10SendToServiceBus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType10SendToServiceBus;

		/// <summary>
		/// textboxIntegrationType10AccountID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType10AccountID;

		/// <summary>
		/// textboxIntegrationType10LineID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType10LineID;

		/// <summary>
		/// dropdownlistIntegrationType10UseSessionInServiceBus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType10UseSessionInServiceBus;

		/// <summary>
		/// dropdownlistIntegrationType10UseSessionForHsmInServiceBus control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType10UseSessionForHsmInServiceBus;

		/// <summary>
		/// checkboxIntegrationType10UseSeparateQueueForSingle control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType10UseSeparateQueueForSingle;

		/// <summary>
		/// placeholderIntegrationType10MarkAsRead control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderIntegrationType10MarkAsRead;

		/// <summary>
		/// dropdownlistIntegrationType10MarkAsRead control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType10MarkAsRead;

		/// <summary>
		/// textboxIntegrationType11GraphApiVersion control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType11GraphApiVersion;

		/// <summary>
		/// textboxIntegrationType11WabaId control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType11WabaId;

		/// <summary>
		/// textboxIntegrationType11AccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType11AccessToken;

		/// <summary>
		/// textboxIntegrationType11PhoneNumberId control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType11PhoneNumberId;

		/// <summary>
		/// placeholderIntegrationType11MarkAsRead control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderIntegrationType11MarkAsRead;

		/// <summary>
		/// dropdownlistIntegrationType11MarkAsRead control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType11MarkAsRead;

		/// <summary>
		/// placeholderIntegrationType11Testing control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderIntegrationType11Testing;

		/// <summary>
		/// checkboxIntegrationType11TestingAccount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType11TestingAccount;

		/// <summary>
		/// textboxIntegrationType11TestingMapping control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType11TestingMapping;

		/// <summary>
		/// textboxIntegrationType2AccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType2AccessToken;

		/// <summary>
		/// textboxIntegrationType2Secret control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType2Secret;

		/// <summary>
		/// textboxIntegrationType2RefreshToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType2RefreshToken;

		/// <summary>
		/// checkboxIntegrationType3UseWhatsappFormat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType3UseWhatsappFormat;

		/// <summary>
		/// checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat;

		/// <summary>
		/// hiddenIntegrationType3ReplyEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3ReplyEndpoint;

		/// <summary>
		/// panelIntegrationType3VoiceCallsEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelIntegrationType3VoiceCallsEndpoint;

		/// <summary>
		/// hiddenIntegrationType3VoiceCallsEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3VoiceCallsEndpoint;

		/// <summary>
		/// dropdownlistIntegrationType3PullType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType3PullType;

		/// <summary>
		/// hiddenIntegrationType3GetNewsEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3GetNewsEndpoint;

		/// <summary>
		/// hiddenIntegrationType3PostNewsProcessedEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3PostNewsProcessedEndpoint;

		/// <summary>
		/// dropdownlistIntegrationType3NotifyClosedCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType3NotifyClosedCases;

		/// <summary>
		/// hiddenIntegrationType3CloseCaseEndpoint control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3CloseCaseEndpoint;

		/// <summary>
		/// dropdownlistIntegrationType3PayloadType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType3PayloadType;

		/// <summary>
		/// hiddenIntegrationType3PayloadTypeObject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3PayloadTypeObject;

		/// <summary>
		/// hiddenIntegrationType3Derivations control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType3Derivations;

		/// <summary>
		/// dropdownlistIntegrationType3IgnorePreviousQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType3IgnorePreviousQueues;

		/// <summary>
		/// dropdownlistIntegrationType4AuthorizationType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType4AuthorizationType;

		/// <summary>
		/// textboxIntegrationType4User control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType4User;

		/// <summary>
		/// textboxIntegrationType4Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType4Password;

		/// <summary>
		/// textboxIntegrationType4ApiKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType4ApiKey;

		/// <summary>
		/// textboxIntegrationType4BaseUrl control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType4BaseUrl;

		/// <summary>
		/// textboxIntegrationType5Username control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType5Username;

		/// <summary>
		/// textboxIntegrationType5Token control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType5Token;

		/// <summary>
		/// textboxIntegrationType6ClientID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6ClientID;

		/// <summary>
		/// textboxIntegrationType6ClientSecret control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6ClientSecret;

		/// <summary>
		/// textboxIntegrationType6UrlBase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6UrlBase;

		/// <summary>
		/// textboxIntegrationType6Login control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6Login;

		/// <summary>
		/// messageIntegrationType6Login control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6Login;

		/// <summary>
		/// textboxIntegrationType6SendMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6SendMessage;

		/// <summary>
		/// messageIntegrationType6SendMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6SendMessage;

		/// <summary>
		/// textboxIntegrationType6ConversationActivities control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6ConversationActivities;

		/// <summary>
		/// messageIntegrationType6ConversationActivities control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6ConversationActivities;

		/// <summary>
		/// textboxIntegrationType6ChannelID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6ChannelID;

		/// <summary>
		/// textboxIntegrationType6FromID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6FromID;

		/// <summary>
		/// textboxIntegrationType6FromName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6FromName;

		/// <summary>
		/// checkboxIntegrationType6SendEndOfConversation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType6SendEndOfConversation;

		/// <summary>
		/// textboxIntegrationType6EndOfConversation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6EndOfConversation;

		/// <summary>
		/// messageIntegrationType6EndOfConversation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6EndOfConversation;

		/// <summary>
		/// dropdownlistIntegrationType6PayloadType control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType6PayloadType;

		/// <summary>
		/// hiddenIntegrationType6PayloadTypeObject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenIntegrationType6PayloadTypeObject;

		/// <summary>
		/// panelIntegrationType6Surveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelIntegrationType6Surveys;

		/// <summary>
		/// messageIntegrationType6NoSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6NoSurveys;

		/// <summary>
		/// panelIntegrationType6EnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelIntegrationType6EnableSurveys;

		/// <summary>
		/// checkboxIntegrationType6EnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType6EnableSurveys;

		/// <summary>
		/// messageIntegrationType6SurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6SurveyDisabled;

		/// <summary>
		/// dropdownlistIntegrationType6Survey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistIntegrationType6Survey;

		/// <summary>
		/// textboxIntegrationType6SurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6SurveyInvitation;

		/// <summary>
		/// messageIntegrationType6SurveyInvitationFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageIntegrationType6SurveyInvitationFields;

		/// <summary>
		/// textboxIntegrationType6SurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6SurveyExpiration;

		/// <summary>
		/// textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// checkboxIntegrationType6SurveySendEndOfConversation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIntegrationType6SurveySendEndOfConversation;

		/// <summary>
		/// textboxIntegrationType7User control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType7User;

		/// <summary>
		/// textboxIntegrationType7Password control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType7Password;

		/// <summary>
		/// textboxIntegrationType7BaseUrl control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType7BaseUrl;

		/// <summary>
		/// textboxIntegrationType7MediaID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType7MediaID;

		/// <summary>
		/// textboxIntegrationType8AppName control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType8AppName;

		/// <summary>
		/// textboxIntegrationType8ApiKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType8ApiKey;

		/// <summary>
		/// textboxIntegrationType9AccountSid control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType9AccountSid;

		/// <summary>
		/// textboxIntegrationType9AuthToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxIntegrationType9AuthToken;

		/// <summary>
		/// checkboxAutoReplyBeforeMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeMaxTimeToAnswer;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// messageAutoReplyBeforeMaxTimeToAnswerText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageAutoReplyBeforeMaxTimeToAnswerText;

		/// <summary>
		/// textboxAutoReplyBeforeMaxTimeToAnswerMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeMaxTimeToAnswerMinutes;

		/// <summary>
		/// checkboxDiscardAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer;

		/// <summary>
		/// checkboxAutoReplyBeforeCaseIsClosed control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAutoReplyBeforeCaseIsClosed;

		/// <summary>
		/// textboxAutoReplyBeforeCaseIsClosedText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCaseIsClosedText;

		/// <summary>
		/// textboxAutoReplyBeforeCaseIsClosedMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyBeforeCaseIsClosedMinutes;

		/// <summary>
		/// panelHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelHSM;

		/// <summary>
		/// dropdownlistAllowToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowToSendHSM;

		/// <summary>
		/// dropdownlistAllowAgentsToSendHSM control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowAgentsToSendHSM;

		/// <summary>
		/// hiddenHSMTemplates control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenHSMTemplates;

		/// <summary>
		/// messageHSMTemplatesEmpty control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageHSMTemplatesEmpty;

		/// <summary>
		/// panelSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelSurveys;

		/// <summary>
		/// dropdownlistAllowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowSurveys;

		/// <summary>
		/// textboxDelayBetweenReplies control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxDelayBetweenReplies;

		/// <summary>
		/// textboxDelayAfterMultimedia control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxDelayAfterMultimedia;

		/// <summary>
		/// checkboxPreviewUrlForTextMessages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxPreviewUrlForTextMessages;

		/// <summary>
		/// hiddenFacebookCatalogAccessToken control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFacebookCatalogAccessToken;

		/// <summary>
		/// hiddenFacebookCatalogCatalog control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFacebookCatalogCatalog;

		/// <summary>
		/// checkboxUseFacebookCatalog control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxUseFacebookCatalog;

		/// <summary>
		/// textboxFacebookCatalogBusiness control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookCatalogBusiness;

		/// <summary>
		/// textboxFacebookCatalogID control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFacebookCatalogID;

		/// <summary>
		/// messageFacebookBusinessCatalogSelectedNoProducts control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFacebookBusinessCatalogSelectedNoProducts;

		/// <summary>
		/// dropdownlistAllowToSendFlows control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistAllowToSendFlows;

		/// <summary>
		/// hiddenFlows control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlows;

		/// <summary>
		/// messageFlowsEmpty control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFlowsEmpty;

		/// <summary>
		/// divCapiService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divCapiService;

		/// <summary>
		/// checkboxEnableCapi control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableCapi;

		/// <summary>
		/// hiddenFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlow;

		/// <summary>
		/// hiddenSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveys;

		/// <summary>
		/// divYFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlGenericControl divYFlowContingency;

		/// <summary>
		/// hiddenFlowContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowContingency;

		/// <summary>
		/// hiddenSurveysContingency control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenSurveysContingency;

		/// <summary>
		/// hiddenFlowQueueTransfersByKey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenFlowQueueTransfersByKey;

		/// <summary>
		/// listboxFlowShareEnqueuedMessagesFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareEnqueuedMessagesFromQueues;

		/// <summary>
		/// listboxFlowShareConnectedAgentsFromQueues control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareConnectedAgentsFromQueues;

		/// <summary>
		/// listboxFlowShareWithServices control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxFlowShareWithServices;

		/// <summary>
		/// textboxFlowMinutesAfterAgentClosedCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFlowMinutesAfterAgentClosedCase;

		/// <summary>
		/// placeholderAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// checkboxAllowAgentsToReturnMessagesToYFlow control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxAllowAgentsToReturnMessagesToYFlow;

		/// <summary>
		/// panelYFlowSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelYFlowSurveys;

		/// <summary>
		/// messageNoSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveys;

		/// <summary>
		/// panelEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelEnableSurveys;

		/// <summary>
		/// checkboxEnableSurveys control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableSurveys;

		/// <summary>
		/// messageNoSurveysInTable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoSurveysInTable;

		/// <summary>
		/// messageSurveyDisabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyDisabled;

		/// <summary>
		/// dropdownSurvey control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownSurvey;

		/// <summary>
		/// textboxSurveyInvitation control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitation;

		/// <summary>
		/// messageSurveyInvitationFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageSurveyInvitationFields;

		/// <summary>
		/// textboxSurveyInvitationInteractive control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationInteractive;

		/// <summary>
		/// messageFilterEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageFilterEmailSubjectFields;

		/// <summary>
		/// textboxSurveyInvitationButton control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyInvitationButton;

		/// <summary>
		/// message1 control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message message1;

		/// <summary>
		/// textboxSurveyExpiration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyExpiration;

		/// <summary>
		/// textboxSurveySentRate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveySentRate;

		/// <summary>
		/// textboxSurveyTimeToSend control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTimeToSend;

		/// <summary>
		/// textboxSurveyTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyTags;

		/// <summary>
		/// listboxSurveyTagGroup control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroup;

		/// <summary>
		/// textboxSurveyMessagesCount control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyMessagesCount;

		/// <summary>
		/// textboxSurveyCaseDuration control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyCaseDuration;

		/// <summary>
		/// checkboxSurveySendIfNewCaseExists control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxSurveySendIfNewCaseExists;

		/// <summary>
		/// textboxSurveyDontSendIfLastSurveyAfterMinutes control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendIfLastSurveyAfterMinutes;

		/// <summary>
		/// textboxSurveyDontSendTotalSendMonthly control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveyDontSendTotalSendMonthly;

		/// <summary>
		/// textboxSurveysIgnoreTags control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxSurveysIgnoreTags;

		/// <summary>
		/// listboxSurveyTagGroupToIgnore control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSurveyTagGroupToIgnore;

		/// <summary>
		/// checkboxWhatsappAllowToSendMedia control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAllowToSendMedia;

		/// <summary>
		/// textboxWhatsappMaxSizeAttachment control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsappMaxSizeAttachment;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeImages control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeImages;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeAudio control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeAudio;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeVideo;

		/// <summary>
		/// checkboxWhatsappAcceptedTypePDF control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypePDF;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeWord control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeWord;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeText;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeAllFiles control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeAllFiles;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeExcel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeExcel;

		/// <summary>
		/// checkboxWhatsappAcceptedTypePPT control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypePPT;

		/// <summary>
		/// checkboxWhatsappAcceptedTypeZip control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxWhatsappAcceptedTypeZip;

		/// <summary>
		/// hiddenWhatsappDefaultExtension control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenWhatsappDefaultExtension;

		/// <summary>
		/// textboxWhatsappMinutesForInactivity control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsappMinutesForInactivity;

		/// <summary>
		/// hiddenInactivityDetectedConnection control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenInactivityDetectedConnection;

		/// <summary>
		/// textboxWhatsappInactivityDetectedEmailSubject control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsappInactivityDetectedEmailSubject;

		/// <summary>
		/// messageWhatsappInactivityDetectedEmailSubjectFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageWhatsappInactivityDetectedEmailSubjectFields;

		/// <summary>
		/// textboxWhatsappInactivityDetectedEmails control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsappInactivityDetectedEmails;

		/// <summary>
		/// textboxWhatsappInactivityDetectedEmailTemplate control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxWhatsappInactivityDetectedEmailTemplate;

		/// <summary>
		/// messageWhatsappInactivityDetectedEmailTemplateFields control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageWhatsappInactivityDetectedEmailTemplateFields;

		/// <summary>
		/// checkboxCasesOverrideSystemSettings control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCasesOverrideSystemSettings;

		/// <summary>
		/// checkboxCheckLastQueueOfOpenCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxCheckLastQueueOfOpenCase;

		/// <summary>
		/// checkboxIgnoreLastQueueForSLMovedMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxIgnoreLastQueueForSLMovedMessage;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseCases;

		/// <summary>
		/// checkboxReplyInCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxReplyInCloseCase;

		/// <summary>
		/// textboxAutoReplyInCloseCaseText control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxAutoReplyInCloseCaseText;

		/// <summary>
		/// textboxTagCloseCase control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTagCloseCase;

		/// <summary>
		/// placeholderYFlowCasesRelated control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderYFlowCasesRelated;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseYFlowCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseYFlowCases;

		/// <summary>
		/// checkboxInvokeYFlowWhenClosedCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxInvokeYFlowWhenClosedCases;

		/// <summary>
		/// textboxMaxElapsedMinutesToCloseHsmCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxMaxElapsedMinutesToCloseHsmCases;

		/// <summary>
		/// textboxTagOnHsmCases control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxTagOnHsmCases;

		/// <summary>
		/// panelVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelVideo;

		/// <summary>
		/// checkboxEnableVideo control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxEnableVideo;

		/// <summary>
		/// panelVoiceCalls control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelVoiceCalls;

		/// <summary>
		/// messageVoiceCallsNotAvailable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageVoiceCallsNotAvailable;

		/// <summary>
		/// checkboxVoiceCallsEnabled control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxVoiceCallsEnabled;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageInvite control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageInvite;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall;

		/// <summary>
		/// hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite;

		/// <summary>
		/// checkboxVoiceCallsAllowAgentsToSendInteractiveMessage control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxVoiceCallsAllowAgentsToSendInteractiveMessage;

		/// <summary>
		/// placeholderVoiceCallAllowRecording control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderVoiceCallAllowRecording;

		/// <summary>
		/// checkboxVoiceCallsAllowRecording control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CheckBox checkboxVoiceCallsAllowRecording;

		/// <summary>
		/// hiddenServiceToCopy control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenServiceToCopy;

		/// <summary>
		/// buttonCopyService control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCopyService;

		/// <summary>
		/// buttonSave control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonSave;

		/// <summary>
		/// buttonCancel control.
		/// </summary>
		/// <remarks>
		/// Auto-generated field.
		/// To modify move field declaration from designer file to code-behind file.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonCancel;
	}
}
