(function() {
    'use strict';

    angular.module('socialApp')
        .factory('chartService', chartService);

    chartService.$inject = ['settingsService', 'utilsService', '$timeout', '$translate'];

    function chartService(settingsService, utilsService, $timeout, $translate) {

        var settings = settingsService.getSettings();

        var service = {
            getChartAgentTimesDescriptions: getChartAgentTimesDescriptions,
            getChartAgentTimesConfig: getChartAgentTimesConfig,
            getChartAgentMessagesConfig: getChartAgentMessagesConfig
        };

        return service;

        function getChartAgentTimesDescriptions(work) {

            var messages = work.messageStats;
            var computedStats = work.computedStats;
            var auxMessages = [];

            auxMessages.push({
                description: 'MESSAGES_ASSIGNED',
                value: messages.assignedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_DISCARDED',
                value: messages.discardedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_REPLIED',
                value: messages.repliedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_ATTENDED',
                value: messages.attendedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_FINISHED',
                value: messages.finishedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_UNASSIGNED',
                value: messages.unassignedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_RETURNED',
                value: messages.returnedToQueueMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_GROUPED',
                value: messages.agentGroupedMessages,
                icon: 'fa fa-envelope'
            });
            auxMessages.push({
                description: 'MESSAGES_TMO',
                value: computedStats.tMOText,
                icon: 'fa fa-tachometer-average'
            });
            auxMessages.push({
                description: 'MESSAGES_TXH',
                value: computedStats.txHText,
                icon: 'fa fa-tachometer-average'
            });
            auxMessages.push({
                description: 'MESSAGES_RXH',
                value: computedStats.repliedxHText,
                icon: 'fa fa-tachometer-average'
            });
            auxMessages.push({
                description: 'MESSAGES_TML',
                value: computedStats.tMLText,
                icon: 'fa fa-tachometer-average'
            });

            return auxMessages;
        }

        function getChartAgentTimesConfig(work, title) {
            var stats = work.timeStats;
            var computedStats = work.computedStats;
            var data = [];
            data.push({
                name: $translate.instant('STATUS_AVAILABLE'),
                y: stats.availTime,
                time: utilsService.secondsToTime(stats.availTime)
            });
            data.push({
                name: $translate.instant('STATUS_WORKING'),
                y: stats.workingTime,
                time: utilsService.secondsToTime(stats.workingTime)
            });
            /*      data.push({name: 'TMO', y: computedStats.tMO, time: utilsService.secondsToTime(computedStats.tMO)});
            data.push({name: 'TxH', y: computedStats.txH, time: utilsService.secondsToTime(computedStats.txH)});*/

            settingsService.settings.context.auxReasons.forEach(function(auxReason) {
                var value = stats.auxTime[auxReason.id];
                if (value > 0) {
                    data.push({
                        name: auxReason.name,
                        y: value,
                        time: utilsService.secondsToTime(value)
                    });
                }

            });

            var chartConfig = {
                options: {
                    chart: {
                        type: 'areaspline',
                        width: null,
                        height: null
                    },
                    colors: ['#7cb5ec', '#f7a35c', '#90ee7e', '#7798BF', '#aaeeee', '#ff0066',
                        '#eeaaee', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee'],
                    plotOptions: {
                        pie: {
                            dataLabels: {
                                enabled: false
                            },
                            allowPointSelect: true,
                            cursor: 'pointer',
                            showInLegend: true
                        }
                    },
                    legend: {
                        layout: 'horizontal',
                        align: 'center',
                        verticalAlign: 'bottom',
                        floating: false,
                        backgroundColor: '#FFFFFF',
                        labelFormatter: function() {
                            return this.name + ' (' + this.time + ')';
                        }
                    },
                    tooltip: {
                        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
                        pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.time}</b><br/>'
                    }
                },
                title: {
                    text: undefined,
                    align: 'left',
                    style: {
                        fontWeight: 'bold',
                        fontFamily: 'Open Sans'
                    }
                },
                credits: {
                    enabled: false
                },
                loading: false,
                series: [{
                    name: 'Status',
                    colorByPoint: true,
                    data: data,
                    type: 'pie'
                }]
            };

            return chartConfig;
        }

        function getChartAgentMessagesConfig(work) {
            var activity = work.activity;

            if (typeof(activity) === 'undefined' ||
                activity === null)
                return;

            var intervals = [];
            var assignedMessages = [];
            var repliedMessages = [];
            var discardedMessages = [];
            var finishedMessages = [];

            activity.forEach(function(data) {
                var interval;
                if (typeof(data.intervalDateTime) === 'undefined') {
                    var intervalText = data.interval.toString().padStart(4, '0');
                    interval = moment();
                    interval.minutes(parseInt(intervalText.substr(2, 2), 10));
                    interval.hours(parseInt(intervalText.substr(0, 2), 10));
                }
                else {
                    interval = moment(data.intervalDateTime);
                }

                interval = interval.format("LT");
                var _assignedMessages = data.assignedMessages;
                var _repliedMessages = data.repliedMessages;
                var _discardedMessages = data.discardedMessages;
                var _finishedMessages = data.finishedMessages;
                intervals.push(interval);
                assignedMessages.push(_assignedMessages);
                repliedMessages.push(_repliedMessages);
                discardedMessages.push(_discardedMessages);
                finishedMessages.push(_finishedMessages);
            });

            var chartConfig = {
                options: {
                    chart: {
                        type: 'column',
                        width: null,
                        height: null
                    },
                    colors: ['#7cb5ec', '#f7a35c', '#90ee7e', '#7798BF', '#aaeeee', '#ff0066',
                        '#eeaaee', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee'],
                    style: {
                        width: '100%'
                    },
                    legend: {
                        layout: 'horizontal',
                        align: 'center',
                        verticalAlign: 'bottom',
                        floating: false,
                        backgroundColor: '#FFFFFF'
                    },
                    tooltip: {
                        headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                        pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                            '<td style="padding:0"><b>{point.y}</b></td></tr>',
                        footerFormat: '</table>',
                        shared: true,
                        useHTML: true
                    },
                    plotOptions: {
                        column: {
                            pointPadding: 0.2,
                            borderWidth: 0
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: intervals,
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    tickAmount: 10,
                    tickInterval: 1,
                    title: {
                        text: $translate.instant('MSG_AMOUNT_PER_INTERVAL'),
                        margin: 40
                    }
                },
                title: {
                    text: null,
                },
                series: [{
                    name: $translate.instant('MESSAGES_ASSIGNED'),
                    data: assignedMessages
                }, {
                    name: $translate.instant('MESSAGES_REPLIED'),
                    data: repliedMessages
                }, {
                    name: $translate.instant('MESSAGES_DISCARDED'),
                    data: discardedMessages
                }, {
                    name: $translate.instant('MESSAGES_FINISHED'),
                    data: finishedMessages
                }]
            };

            return chartConfig;
        }
    }
})();