﻿/// <reference path="../Scripts/jquery-3.5.1.js" />

function LoadMessages(parameters, firstTime) {
	if (typeof (firstTime) == 'undefined')
		firstTime = true;

	var $divContents = $('#divContents');
	var $divLoading = $('#divLoading');

	var $divLoadMoreResults = $('#divLoadMoreResults', $divContents);
	$divLoadMoreResults.toggleClass('working');

	if (firstTime)
		$divLoading.show();

	$.getJSON("../services/messaging/query", parameters, function (data) {
		if (data.Success) {
			var $tableMessages = $('#tableMessages', $divContents);
			var $thead = $('thead', $tableMessages);
			var $tbody = $('tbody', $tableMessages);

			var $messageNoResults = $('#messageNoResults', $divContents);
			var $messageTotalResults = $('#messageTotalResults', $divContents);
			var $divWithResults = $('#divWithResults', $divContents);

			if (firstTime) {
				$tbody.empty();

				if (data.Result.Messages.length == 0) {
					$messageNoResults.show();
					$messageTotalResults.hide();
					$divWithResults.hide();
				}

				var $spanExpandBody = $('#spanExpandBody', $thead);
				$spanExpandBody.click(function () {
					$tableMessages.addClass('withBodyExpanded');
				});
				var $spanCollapseBody = $('#spanCollapseBody', $thead);
				$spanCollapseBody.click(function () {
					$tableMessages.removeClass('withBodyExpanded');
				});
			}

			var table = $tableMessages[0];
			var lastRow = table.rows[table.rows.length - 1];

			for (var i = 0; i < data.Result.Messages.length; i++) {
				var message = data.Result.Messages[i];

				var messageQueue = data.Queue === null ? "N/A" : data.Queue.Name;

				var html = '<tr class="' + (i % 2 == 0 ? 'normal' : 'alternate') + '">' +
					'<td style="white-space:nowrap;">' + message.ID + '</td>' +
					'<td style="text-align: right">' + BuildCase(message) + '</td>' +
					'<td style="white-space:nowrap;">' + BuildDate(message.Date, message.DateText) + '</td>' +
					'<td style="white-space:nowrap;">' + BuildDate(message.InsertedDate, message.InsertedDateText) + '</td>' +
					(data.Result.ShowServiceColumn ? ('<td style="white-space:nowrap;">' + BuildService(message.Service) + '</td>') : '') +
					(data.Result.ShowQueueColumn ? ('<td style="white-space:nowrap;">' + messageQueue + '</td>') : '') +
					(data.Result.ShowVIMColumn ? ('<td align="center" style="white-space:nowrap;">' +
						(message.VIM ? '<span class="fa fa-lg fa-star starvim"></span>' : '') +
					'</td>') : '') +
					(data.Result.ShowPrivateMessageColumn ? ('<td align="center" style="white-space:nowrap;">' +
						(message.PrivateMessage ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
					'</td>') : '') +
					'<td align="center" style="white-space:nowrap;">' +
						BuildIsGrouping(message) +
					'</td>' +
					'<td style="white-space:nowrap;" class="socialuser">' + BuildSocialUser(message.SocialUser) + '</td>' +
					(data.Result.ShowShouldBeAssigntedToColumn ? ('<td style="white-space:nowrap;">' + BuildAgent(message.Person) + '</td>') : '') +
					(data.Result.ShowStatusColumn ? ('<td style="width:120px;white-space:nowrap;">' + BuildMessageStatus(message) + '</td>') : '') +
					(data.Result.ShowReadColumn ? ('<td align="center" style="white-space:nowrap;">' +
						(message.Read ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
					'</td>') : '') +
					'<td class="body"><div class="ellipsis"></div></td>' +
					'<td align="center" style="white-space:nowrap;">' +
						(message.HasAttach ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
					'</td>' +
					(data.Result.ShowReturnedToQueueColumn ? ('<td align="center" style="white-space:nowrap;">' +
						(message.ReturnedToQueue ? '<span class="fa fa-lg fa-check-circle"></span>' : '') +
					'</td>') : '') +
					'<td align="center" style="white-space:nowrap;">'
						+ (message.IsReply || message.OutOfServiceLevel == null
							? '<span>N/A</span>'
							: (message.OutOfServiceLevel ? '<span class="fa fa-lg fa-times-circle"></span>' : '<span class="fa fa-lg fa-check-circle"></span>')
						+ '</td>') +
					'<td align="center" style="white-space:nowrap;">'
						+ (message.IsReply || message.QuickAnswer == null
							? '<span>N/A</span>'
							: (message.QuickAnswer ? '<span class="fa fa-lg fa-check-circle"></span>' : '')
						+ '</td>') +
						'<td align="center" style="white-space:nowrap;" class="inspect">' +
							BuildActions(message, parameters) +
						'</td>' +
				"</tr>";

				var $html = $(html);
				var $body = $('td.body div.ellipsis', $html);
				if (typeof (message.EmptyBody) === 'boolean' && message.EmptyBody) {
					$body.html($.i18n('globals-empty-body'));
				}
				else {
					$body.text(message.Body);
				}

				if (typeof (message.IsEvent) === 'boolean' && message.IsEvent) {
					$body.prepend('<span class="fas fa-bolt" style="margin-right: 3px" title="Mensaje encolado luego de retomar un caso con marca de pendiente de respuesta del cliente" data-i18n-title="globals-message-event_from_pending_reply"></span>');
				}

				if (parameters.showBy === 2 &&
					typeof (allowedToUnnasignMessagesFromAgents) === 'boolean' &&
					allowedToUnnasignMessagesFromAgents) {
					var $anchorReleaseMessage = $('a[rel=release-message]', $html);
					$anchorReleaseMessage.click(message, function (args) {
						let message = args.data;
						ReleaseMessageFromAgent(message, message.Person)
					});
				}

				if (parameters.showBy === 8) {
					var title = $('div[id=modal-title]');
					title.html('<h2 data-i18n="reports-messagesquery-messages-title-only-VIM">Mensajes VI</h2>');
				}

				$('tbody:last', $tableMessages).append($html);

				var $thService = $('#thService', $thead);
				var $thQueue = $('#thQueue', $thead);
				var $thVIM = $('#thVIM', $thead);
				var $thPrivateMessage = $('#thPrivateMessage', $thead);
				var $thGroups = $('#thGroups', $thead);
				var $thSocialUser = $('#thSocialUser', $thead);
				var $thShouldBeAssignedTo = $('#thShouldBeAssignedTo', $thead);
				var $thStatus = $('#thStatus', $thead);
				var $thRead = $('#thRead', $thead);
				var $thBody = $('#thBody', $thead);
				var $thHasAttach = $('#thHasAttach', $thead);
				var $thReturnedToQueue = $('#thReturnedToQueue', $thead);

				$thService.toggle(data.Result.ShowServiceColumn);
				$thQueue.toggle(data.Result.ShowQueueColumn);
				$thVIM.toggle(data.Result.ShowVIMColumn);
				$thPrivateMessage.toggle(data.Result.ShowPrivateMessageColumn);
				$thShouldBeAssignedTo.toggle(data.Result.ShowShouldBeAssigntedToColumn);
				$thStatus.toggle(data.Result.ShowStatusColumn);
				$thRead.toggle(data.Result.ShowReadColumn);
				$thReturnedToQueue.toggle(data.Result.ShowReturnedToQueueColumn);
			}

			if (firstTime) {
				if (data.Result.Messages.length > 0) {
					$messageNoResults.hide();
					$messageTotalResults.show();
					$divWithResults.show();
				}
			}

			LoadCompositedElements();

			if (firstTime) {
				parameters.skip = data.Result.Messages.length;
			}
			else {
				parameters.skip += data.Result.Messages.length;
			}

			if (data.Result.Paginated && data.Result.MoreRecordsAvailable) {
				var $anchorLoadMoreResults = $('#anchorLoadMoreResults', $divLoadMoreResults);

				$divLoadMoreResults.show();

				$anchorLoadMoreResults.attr('href', 'javascript:LoadMessages(' + JSON.stringify(parameters) + ', false)');
			}
			else {
				$divLoadMoreResults.hide();
			}

			$divLoadMoreResults.toggleClass('working');

			if (data.Result.Total > 0) {
				var totalResults = parameters.skip;
				var totalRecords;
				if (firstTime) {
					totalRecords = data.Result.Total;
					$messageTotalResults.prop('totalRecords', totalRecords);
				}
				else {
					totalRecords = $messageTotalResults.prop('totalRecords');
					
				}

				var percentage = totalResults * 100 / totalRecords;
				$('td.text', $messageTotalResults).text($.i18n('reports-globals-showing_records', totalResults, totalRecords, percentage.toFixed(2)));
			}

			$divLoading.hide();

			$.colorbox.resize();
		}
		else {
			var $messageError = $('#messageError');
			messageError.show();
			divContents.hide();
			$divLoading.hide();
		}
	});

	function BuildCase(message) {
		if (message.Case === null) {
			return '';
		}

		return '<a href="javascript:ShowExtendedCaseInfo(' + message.Case.ID + ')" title="Ver más información del caso" data-i18n-title="globals-viewmore-case">' + message.Case.ID + '</a>';
	}

	function BuildMessageStatus(message) {
		return DisplayLocalizedEnumValue('MessageStatuses', message.StatusID);
	}

	function BuildIsGrouping(message) {
		if (!message.IsGrouping) {
			return '';
		}
		
		var html = '<span class="fa fa-lg fa-check-circle"></span>';
		if (typeof (message.GroupsLength) === 'number' && message.GroupsLength > 0) {
			html += 'x' + message.GroupsLength.toString();
		}

		return html;
	}

	function BuildActions(message, parameters) {
		var tooltipdirection = 'left';
		if (typeof (loggedUserSettings) != 'undefined' &&
			loggedUserSettings != null &&
			typeof (loggedUserSettings.ShowMoreInformationInTheLeft) != 'undefined' &&
			loggedUserSettings.ShowMoreInformationInTheLeft === 'True') {
			tooltipdirection = 'right';
		}

		var html = '<a href="javascript:ShowExtendedMessageInfo(' + message.ID + ')" title="Ver más información del mensaje" data-i18n-title="reports-messagesquery-more_message_information" tooltipdirection="' + tooltipdirection + '"><span class="fa fa-lg fa-search-plus"></span></a>';

		if (parameters.showBy === 2 &&
			typeof (allowedToUnnasignMessagesFromAgents) === 'boolean' &&
			allowedToUnnasignMessagesFromAgents) {
			html += ' <a rel="release-message" title="Desasignar mensaje del agente" data-i18n-title="reports-messagesquery-desassing_agent_message" tooltipdirection="' + tooltipdirection + '"><span class="fa fa-lg fa-layer-minus"></span></a>';
		}

		return html;
	}

	function BuildDate(date, dateText) {
		if (typeof (moment) != 'undefined' && typeof (loggedUserSettings) != 'undefined' && loggedUserSettings != null && typeof (loggedUserSettings.ReportsRealTimeQueuesDateFormat) != 'undefined') {
			return ConvertDateTimeToElapsedTime(date, loggedUserSettings.ReportsRealTimeQueuesDateFormat);
		}
		else {
			return dateText;
		}
	}

	function BuildService(service) {
		var html = '<span class="fa-lg ';

		if (service.Type != ServiceTypes.TwitterSearches) {
			html += GetServiceTypeClass(service.Type) + '"></span>';
		}
		else {
			html += 'fa-stack-05"><i class="fab fa-twitter-square fa-stack-1x"></i><i class="fa fa-search fa-stack-05x"></i></span>';
		}

		html += ' ' + service.Name;

		return html;
	}

	function BuildAgent(person) {
		if (person == null)
			return 'N/A';

		if (person.ID == 0)
			return '<i>' + person.FirstName + '</i>';

		return person.FullName;
	}

	function BuildSocialUser(socialUser) {
		if (socialUser != null) {
			var classes = '';
			var attributesToAdd = '';

			if (socialUser.VIP)
				classes += 'vip ';

			if (socialUser.Blocked)
				classes += 'blocked ';

			if (socialUser.Parameters != null &&
				typeof (socialUser.Parameters.Verified) === 'string' &&
				socialUser.Parameters.Verified.toLowerCase() === 'true') {
				classes += 'verified ';
			}

			if (socialUser.SocialServiceType == SocialServiceTypes.Twitter) {
				if (socialUser.Parameters != null) {
					if (socialUser.Parameters.IsFollowing != null)
						classes += (socialUser.Parameters.IsFollowing ? " following" : " notfollowing");

					if (socialUser.Parameters.NumberOfFollowers != null && socialUser.Parameters.NumberOfFollowers > 0)
						attributesToAdd += 'followers="' + socialUser.Parameters.NumberOfFollowers + '" ';
				}
			}

			if (socialUser.SocialServiceType == SocialServiceTypes.Chat && socialUser.Anonymous) {
				var displayText = $.i18n('reports-chats-anonymous');
				if (typeof (socialUser.Parameters) !== 'undefined' &&
					socialUser.Parameters !== null) {
					if (typeof (socialUser.Parameters.AnonymousDisplayName) !== 'undefined' &&
						socialUser.Parameters.AnonymousDisplayName !== null &&
						socialUser.Parameters.AnonymousDisplayName.length > 0) {
						displayText = socialUser.Parameters.AnonymousDisplayName;
					}

					if (typeof (socialUser.Parameters.AnonymousMail) !== 'undefined' &&
						socialUser.Parameters.AnonymousMail !== null &&
						socialUser.Parameters.AnonymousMail.length > 0) {
						displayText += ' <' + socialUser.Parameters.AnonymousMail + '>';
					}

					if (typeof (socialUser.Parameters.AnonymousPhone) !== 'undefined' &&
						socialUser.Parameters.AnonymousPhone !== null &&
						socialUser.Parameters.AnonymousPhone.length > 0) {
						displayText += ' - ' + socialUser.Parameters.AnonymousPhone;
					}
				}

				return '<span class="fa fa-lg fa-user-secret" title="Anónimo" data-i18n-title="reports-messagesquery-anonymous"></span> ' + he.encode(displayText);
			}
			else {
				return '<a class="' + classes + '" ' + attributesToAdd + ' href="javascript:ShowExtendedSocialUserProfileInfo(null, \'' + socialUser.ID + '\', ' + socialUser.SocialServiceType + ')">' + he.encode(socialUser.DisplayText) + '</a>';
			}
		}

		return 'N/A';
	}
}