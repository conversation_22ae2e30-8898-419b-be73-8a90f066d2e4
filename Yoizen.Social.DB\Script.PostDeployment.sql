﻿/*
Post-Deployment Script Template							
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.		
 Use SQLCMD syntax to include a file in the post-deployment script.			
 Example:      :r .\myfile.sql								
 Use SQLCMD syntax to reference a variable in the post-deployment script.		
 Example:      :setvar TableName MyTable							
               SELECT * FROM [$(TableName)]					
--------------------------------------------------------------------------------------
*/

IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 9)
	INSERT INTO [TaskStatuses]([TaskStatusID], [Description]) VALUES (9, 'FileParsed');

IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 10)
	INSERT INTO [dbo].[TaskStatuses]([TaskStatusID], [Description]) VALUES (10, 'ProcessingQueue');

IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 11)
	INSERT INTO [dbo].[TaskStatuses]([TaskStatusID], [Description]) VALUES (11, 'Cancelling');

IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 12)
	INSERT INTO [dbo].[TaskStatuses]([TaskStatusID], [Description]) VALUES (12, 'CancelledWithProcess');

IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 1)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (1, N'Twitter');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 2)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (2, N'Facebook');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 4)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (4, N'Mail');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 8)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (8, N'Chat');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16, N'WhatsApp');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 32)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (32, N'SMS');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 64)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (64, N'Global');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 128)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (128, N'Telegram');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 256)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (256, N'Instagram');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 512)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (512, N'LinkedIn');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 1024)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (1024, N'Skype');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 2048)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (2048, N'Facebook Messenger');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 4096)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (4096, N'Mercado Libre');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 8192)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (8192, N'YouTube');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16384)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16384, N'Google Play');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16385)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16385, N'VideoCall');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16386)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16386, N'Google RBM');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16400)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16400, N'Apple Messaging Business');
GO
IF NOT EXISTS(SELECT 1 FROM [SocialServiceTypes] WHERE [SocialServiceTypeID] = 16401)
	INSERT INTO [dbo].[SocialServiceTypes] ([SocialServiceTypeID], [Name]) VALUES (16401, N'Google My Business');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'AgentsInMultipleQueues')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'AgentsInMultipleQueues', N'False', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'MaxAssignableMessagesPerUser')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'MaxAssignableMessagesPerUser', N'3', N'System.Int16');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'UseQueues')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'UseQueues', N'True', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'QueueServiceType')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'QueueServiceType', N'Yoizen.Social.Core.Services.QueueService, Yoizen.Social.Core, Version=*******, Culture=neutral, PublicKeyToken=8c5134be849e992c', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'SessionTimeOut')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'SessionTimeOut', N'5', N'System.Int16');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'IntervalsPerHour')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'IntervalsPerHour', N'2', N'System.Byte');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'WordsToExcludeFromCloud')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'WordsToExcludeFromCloud', N'a,acá,ah,ahi,ahí,al,amo,año,asi,así,aun,aún,ay,con,da,dan,dar,das,de,del,di,día,dio,don,dos,doy,dr,e,eh,el,él,en,era,es,esa,ésa,ese,ése,eso,fin,fue,fui,ha,han,has,hay,haz,he,hey,hoy,iba,ido,ir,irá,iré,la,las,le,les,ley,lo,los,luz,mal,mas,más,me,mi,mí,mil,mío,mis,muy,ni,no,nos,o,oh,oí,oír,ok,os,oye,par,paz,pie,pon,por,prueba,que,qué,rey,sal,san,se,sé,sea,ser,si,sí,sin,son,soy,sr,sra,su,sus,tal,tan,te,ten,ti,tío,tu,tú,tus,ud,uds,un,una,uno,usa,va,van,vas,ve,ven,veo,ver,ves,vez,vi,vio,voy,voz,y,ya,yo', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 1)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (1, N'Twitter', N'Yoizen.Social.SocialServices.Twitter.TwitterSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 1, 1);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 2)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (2, N'Facebook', N'Yoizen.Social.SocialServices.Facebook.FacebookSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 2);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 3)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (3, N'Búsquedas de Twitter', N'Yoizen.Social.SocialServices.Twitter.TwitterSearchSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 1, 1);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 4)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (4, N'Mail', N'Yoizen.Social.SocialServices.Mail.MailService, Yoizen.Social.SocialServices.Mail, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 4);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 5)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (5, N'Chat', N'Yoizen.Social.SocialServices.Chat.ChatService, Yoizen.Social.SocialServices.Chat, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 8);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 6)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (6, N'WhatsApp', N'Yoizen.Social.SocialServices.WhatsApp.WhatsAppService, Yoizen.Social.SocialServices.WhatsApp, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 7)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (7, N'SMS', N'Yoizen.Social.SocialServices.SMS.SMSSocialService, Yoizen.Social.SocialServices.SMS, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 32);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 8)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (8, N'Global', N'Yoizen.Social.SocialServices.Global.GlobalService, Yoizen.Social.SocialServices.Global, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 64);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 9)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (9, N'Telegram', N'Yoizen.Social.SocialServices.Telegram.TelegramSocialService, Yoizen.Social.SocialServices.Telegram, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 128);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 10)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (10, N'Facebook RT', N'Yoizen.Social.SocialServices.Facebook.FacebookRTSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 2);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 11)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (11, N'Instagram', N'Yoizen.Social.SocialServices.Instagram.InstagramSocialService, Yoizen.Social.SocialServices.Instagram, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c',0, 256);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 12)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (12, N'LinkedIn', N'Yoizen.Social.SocialServices.LinkedIn.LinkedInSocialService, Yoizen.Social.SocialServices.LinkedIn, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c',0, 512);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 13)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (13, N'Skype', N'Yoizen.Social.SocialServices.Skype.SkypeSocialService, Yoizen.Social.SocialServices.Skype, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c',0, 1024);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 14)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (14, N'Facebook Messenger', N'Yoizen.Social.SocialServices.Facebook.FacebookMessengerSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 2048);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 15)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (15, N'Twitter RT', N'Yoizen.Social.SocialServices.Twitter.TwitterRTSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 1, 1);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 16)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (16, N'Mercado Libre', N'Yoizen.Social.SocialServices.MercadoLibre.MercadoLibreSocialService, Yoizen.Social.SocialServices.MercadoLibre, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 1, 4096);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 17)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (17, N'YouTube', N'Yoizen.Social.SocialServices.YouTube.YouTubeSocialService, Yoizen.Social.SocialServices.YouTube, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 8192);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 18)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (18, N'Chat con integración', N'Yoizen.Social.SocialServices.Chat.IntegrationChatService, Yoizen.Social.SocialServices.Chat, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 8);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 19)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (19, N'Google Play', N'Yoizen.Social.SocialServices.GooglePlay.GooglePlaySocialService, Yoizen.Social.SocialServices.GooglePlay, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16384);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 20)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (20, N'Videoconferencias', N'Yoizen.Social.SocialServices.VideoCalls.VideoCallsSocialService, Yoizen.Social.SocialServices.VideoCalls, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16385);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 21)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (21, N'Google RBM', N'Yoizen.Social.SocialServices.GoogleRBM.GoogleRBMSocialService, Yoizen.Social.SocialServices.GoogleRBM, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16386);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 22)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (22, N'Apple Messaging', N'Yoizen.Social.SocialServices.AppleMessaging.AppleMessagingSocialService, Yoizen.Social.SocialServices.AppleMessaging, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16400);
GO
IF NOT EXISTS(SELECT 1 FROM [ServiceTypes] WHERE [ServiceTypeID] = 23)
	INSERT INTO [dbo].[ServiceTypes] ([ServiceTypeID], [Name], [ClassType], [CanMessageBeInSeveralConversations], [SocialServiceTypeID]) VALUES (23, N'Google My Business', N'Yoizen.Social.SocialServices.GoogleBusiness.GoogleBusinessSocialService, Yoizen.Social.SocialServices.GoogleBusiness, Version=*******, Culture=neutral, PublicKeyToken=8c5134be849e992c', 0, 16401);
GO
IF NOT EXISTS(SELECT 1 FROM [SurveyStatuses] WHERE [SurveysStatusID] = 1)
	INSERT INTO [dbo].[SurveyStatuses] ([SurveysStatusID], [Description]) VALUES (1, N'Enviada');
GO
IF NOT EXISTS(SELECT 1 FROM [SurveyStatuses] WHERE [SurveysStatusID] = 2)
	INSERT INTO [dbo].[SurveyStatuses] ([SurveysStatusID], [Description]) VALUES (2, N'Completa');
GO
IF NOT EXISTS(SELECT 1 FROM [SurveyStatuses] WHERE [SurveysStatusID] = 3)
	INSERT INTO [dbo].[SurveyStatuses] ([SurveysStatusID], [Description]) VALUES (3, N'Inompleta')
SET IDENTITY_INSERT [dbo].[Profiles] ON
IF NOT EXISTS(SELECT 1 FROM [Profiles] WHERE [ProfileID] = 1)
	INSERT INTO [dbo].[Profiles] ([ProfileID], [Name]) VALUES (1, N'Administrador');
GO
IF NOT EXISTS(SELECT 1 FROM [Profiles] WHERE [ProfileID] = 2)
	INSERT INTO [dbo].[Profiles] ([ProfileID], [Name]) VALUES (2, N'Supervisor')
SET IDENTITY_INSERT [dbo].[Profiles] OFF
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 1)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (1, N'No Asignado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 2)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (2, N'Asignado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 3)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (3, N'Respondido');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 4)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (4, N'Descartado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 5)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (5, N'Marcado para eliminar');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 6)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (6, N'Tiempo de espera agotado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 7)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (7, N'Ignorado por superar cantidad de mensajes en la cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 8)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (8, N'Histórico');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 9)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (9, N'Agrupado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 10)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (10, N'Abandonado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 11)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (11, N'Finalizado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 12)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (12, N'Respondido por yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 13)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (13, N'Push');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 14)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (14, N'Pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 15)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (15, N'Atendido');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 16)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (16, N'Derivado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageStatuses] WHERE [MessageStatusID] = 999)
	INSERT INTO [dbo].[MessageStatuses] ([MessageStatusID], [Name]) VALUES (999, N'Sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 1)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (1, N'Crear Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 2)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (2, N'Asignar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 3)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (3, N'Responder Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 4)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (4, N'Descartar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 5)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (5, N'Devolver Mensaje a la cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 6)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (6, N'Leer Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 7)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (7, N'Encolar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 8)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (8, N'Desasignar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 9)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (9, N'Reservar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 10)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (10, N'Aplicar Filtro');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 11)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (11, N'Mover de Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 12)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (12, N'Asignar a Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 13)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (13, N'Agrupar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 14)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (14, N'Marcar como Importante');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 15)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (15, N'Liberar Reserva');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 16)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (16, N'Abandonar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 17)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (17, N'Finalizar Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 18)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (18, N'Finalizar Lectura Mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 19)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (19, N'Mensaje fue forwardeado');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 20)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (20, N'Mensaje Editado por Supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 21)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (21, N'Mensaje Autorizado por Supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 22)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (22, N'Mensaje Incumplido en Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 23)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (23, N'Mensaje Incumplido en Agente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 24)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (24, N'Mensaje Vencido en Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 25)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (25, N'Mensaje Movido por Incumplimiento en Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 26)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (26, N'Mensaje Movido por Vencimiento en Cola');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 27)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (27, N'Mensaje Movido por Usuario En Linea');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 28)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (28, N'Mensaje marcado con el usuario en línea');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 29)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (29, N'Marcar como no leído');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 30)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (30, N'Asignar Mensaje por reconexión');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 31)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (31, N'Mensaje desmarcado con el usuario en línea');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 32)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (32, N'Mensaje clasificado con el servicio cognitivo de conversación');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 33)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (33, N'Mensaje respondido automáticamente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 34)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (34, N'Mensaje marcado como pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 35)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (35, N'Mensaje desmarcado como pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 36)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (36, N'Mensaje respondido por yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 37)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (37, N'Mensaje marcado como pendiente por suporvisor');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 38)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (38, N'Mensaje desmarcado como pendiente por supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 39)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (39, N'Mensaje transferido a yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 40)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (40, N'Mensaje falló transferencia a yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 41)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (41, N'Mensaje marcado como pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 42)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (42, N'Se quitó la marca de pendiente del mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 43)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (43, N'Mensaje marcado como finalizado de atender');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 44)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (44, N'Mensaje derivado al gateway');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 45)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (45, N'Inactividad del agente en modo chat');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageLogTypes] WHERE [MessageLogTypeID] = 46)
	INSERT INTO [dbo].[MessageLogTypes] ([MessageLogTypeID], [Name]) VALUES (46, N'inactividad del usuario final en modo chat');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 1)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (1, N'Gestión de usuarios', N'Gestión de usuarios del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 3)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (3, N'Gestión de perfiles', N'Gestión de perfiles del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 4)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (4, N'Parámetros del sistema', N'Gestión de los parámetros generales del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 5)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (5, N'Servicios', N'Administración de los servicios de redes sociales');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 6)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (6, N'Blacklist', N'Administración de la lista de usuarios bloqueados');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 7)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (7, N'Colas crear', N'Acceso total a la creación y edición de colas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 8)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (8, N'Etiquetas', N'Administración de etiquetas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 11)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (11, N'Agentes en colas', N'Asignación de agentes a colas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 12)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (12, N'Etiquetas en colas', N'Asignación de etiquetas a colas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 13)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (13, N'Supervisores en colas', N'Asignación de supervisores a colas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 14)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (14, N'Gestión de Agentes', N'Gestión de agentes del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 15)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (15, N'Reportes tiempo real', N'Acceso a los reportes de tiempo real');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 16)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (16, N'Reportes Histórico', N'Acceso a los reportes de históricos');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 17)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (17, N'Motivos de Auxiliar', N'Gestión de los motivos de auxiliar de los agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 18)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (18, N'Plantillas', N'Gestión de plantillas de respuesta');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 19)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (19, N'Acciones', N'Gestión de acciones por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 20)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (20, N'Whitelist', N'Administración de la lista de usuarios VIP');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 21)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (21, N'Configuración Avanzada', N'Configuración Avanzada de Servicios');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 22)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (22, N'Filtros', N'Gestión de Filtros');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 23)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (23, N'Respuestas Predefinidas', N'Gestión de respuestas predefinidas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 24)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (24, N'Motivos de Contacto', N'Gestión de motivos de contacto');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 25)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (25, N'Mensajes de Chat', N'Acceso a los chats activos para incorporarse');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 26)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (26, N'Listas de Perfiles de Usuarios', N'Administración de las listas de usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 27)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (27, N'Service Level', N'Administración de los Niveles de Servicio');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 28)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (28, N'Reportes Generados', N'Administración de Reportes Generados');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 29)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (29, N'Reportes Diarios Generados', N'Administración de Reportes Diarios Generados');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 30)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (30, N'Envío de Encuestas', N'Administración de Envíos de Encuestas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 31)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (31, N'Gestión de Grupos de Agentes', N'Gestión de grupos de agentes del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 32)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (32, N'Integraciones', N'Gestión de integraciones del sistema de acuerdo a eventos');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 33)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (33, N'Servicios Cognitivos', N'Gestión de las configuraciones de los servicios cognitivos');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 34)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (34, N'Integraciones con terceros', N'Gestión de las configuraciones de integraciones con terceros');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 35)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (35, N'Reportes Administrativos', N'Acceso a los reportes administrativos');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 36)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (36, N'Whatsapp', N'Acceso a las funciones específicas del canal de Whatsapp');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 37)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (37, N'Días y horarios laborables', N'Acceso a la configuración de días y horarios laborables');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 38)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (38, N'Monitorear agentes', N'Acceso a monitorear la pantalla de los agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 39)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (39, N'Responder mensajes', N'Responder mensajes en cola');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 40)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (40, N'Desconectar agentes', N'Acceso a desconectar agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 41)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (41, N'Supervisor', N'Permiso de supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 42)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (42, N'Cambiar contraseña usuarios', N'Permiso para cambiar contraseña de otros usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 43)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (43, N'Cambiar contraseña agentes', N'Permiso para cambiar contraseña de agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 44)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (44, N'Desasignar mensajes a agentes', N'Permiso para desasignar mensajes de los agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 45)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (45, N'Sitios', N'Permiso para administrar los sitios');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 46)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (46, N'Grupos de colas', N'Permiso para administrar los grupos de colas');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 47)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (47, N'Estado del sistema', N'Permiso para acceder al estado del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 48)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (48, N'Whatsapp HSM - Reportes', N'Acceso a los reportes de WhatsApp');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 49)
	INSERT into [dbo].[Permissions] ([PermissionID],[Name],[Description]) VALUES (49, N'Envio HSM masivo sin caso', N'Permite enviar un HSM masvio sin necesidad de crear un caso por cada registro');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 50)
	INSERT into [dbo].[Permissions] ([PermissionID],[Name],[Description]) VALUES (50, N'Reportes HSM masivo sin caso', N'Permite acceder a las tareas de HSM masvio sin caso');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 51)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (51, N'Reportes Programados', N'Administración de Reportes Programados');
GO
IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 52)
	INSERT INTO [dbo].[Permissions] ([PermissionID], [Name], [Description]) VALUES (52, N'Grupos de etiquetas', N'Permiso para administrar los grupos de etiquetas');
GO

IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 1)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 1);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 3)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 3);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 4)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 4);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 5)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 5);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 6)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 6);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 7)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 7);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 17)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 17);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 20)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 20);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 21)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 21);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 26)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 26);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 27)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 27);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 28)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 28);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 29)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 29);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 30)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 30);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 32)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 32);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 33)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 33);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 34)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 34);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 35)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 35);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 37)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 37);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 45)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 45);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 46)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 46);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 1 AND [PermissionID] = 47)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (1, 47);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 8)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 8);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 11)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 11);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 12)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 12);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 13)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 13);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 14)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 14);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 15)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 15);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 16)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 16);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 18)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 18);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 19)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 19);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 22)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 22);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 23)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 23);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 24)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 24);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 25)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 25);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 31)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 31);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 38)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 38);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 39)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 39);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 40)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 40);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 44)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 44);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 45)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 45);
GO
IF NOT EXISTS(SELECT 1 FROM [ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 46)
	INSERT INTO [dbo].[ProfilesPermissions] ([ProfileID], [PermissionID]) VALUES (2, 46);
GO

DELETE FROM [UsersPermissions] WHERE [PermissionID] IN (9,10);
GO
DELETE FROM [ProfilesPermissions] WHERE [PermissionID] IN (9,10);
GO
DELETE FROM [Permissions] WHERE [PermissionID] IN (9,10);
GO

INSERT INTO [dbo].[UsersPermissions] ([UserID], [PermissionID])
	SELECT [UserID], 38 --Monitorear agentes
		FROM [vUsers]
		WHERE
			[AllowedToMonitorAgents] = 1
			AND [UserID] NOT IN (SELECT [UserID] FROM [UsersPermissions] WHERE [PermissionID] = 38);
GO
INSERT INTO [dbo].[UsersPermissions] ([UserID], [PermissionID])
	SELECT [UserID], 39 --Responder mensajes
		FROM [vUsers]
		WHERE
			[AllowedToReplyMessages] = 1
			AND [UserID] NOT IN (SELECT [UserID] FROM [UsersPermissions] WHERE [PermissionID] = 39);
GO
INSERT INTO [dbo].[UsersPermissions] ([UserID], [PermissionID])
	SELECT [UserID], 40 --Desconectar agentes
		FROM [vUsers]
		WHERE
			[AllowedToDisconnectAgents] = 1
			AND [UserID] NOT IN (SELECT [UserID] FROM [UsersPermissions] WHERE [PermissionID] = 40);
GO


IF NOT EXISTS(SELECT 1 FROM [PersonTypes] WHERE [PersonTypeID] = 1)
	INSERT INTO [dbo].[PersonTypes] ([PersonTypeID], [Name]) VALUES (1, N'Usuario');
GO
IF NOT EXISTS(SELECT 1 FROM [PersonTypes] WHERE [PersonTypeID] = 2)
	INSERT INTO [dbo].[PersonTypes] ([PersonTypeID], [Name]) VALUES (2, N'Agente');

SET IDENTITY_INSERT [dbo].[Persons] ON
IF NOT EXISTS(SELECT 1 FROM [Persons] WHERE [PersonID] = 1)
	INSERT INTO [dbo].[Persons] ([PersonID], [FirstName], [LastName], [Username], [Password], [Enabled], [UserLDAP], [Email], [PersonTypeID]) VALUES (1, N'Administrador', NULL, N'Administrador', N'ef1lZwei+ySInLyhu6/ZM8P44Rg=', 1, NULL, N'<EMAIL>', 1)
SET IDENTITY_INSERT [dbo].[Persons] OFF

IF NOT EXISTS(SELECT 1 FROM [Users] WHERE [UserID] = 1)
	INSERT INTO [dbo].[Users] ([UserID]) VALUES (1);
GO
IF NOT EXISTS(SELECT 1 FROM [UsersProfiles] WHERE [UserID] = 1 AND [ProfileID] = 1)
	INSERT INTO [dbo].[UsersProfiles] ([UserID], [ProfileID]) VALUES (1, 1);
GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 1)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (1, N'Fecha del mensaje', N'Los mensajes se ordenarán de acuerdo a la fecha de creación de los mismos. Esta opción es la opción por defecto');

GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 2)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (2, N'Personalizado', N'Los mensajes se ordenarán de acuerdo a un componente que especifica el usuario');

GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 3)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (3, N'Seguidores', N'Los mensajes se ordenarán de acuerdo a la cantidad de seguidores que tiene el usuario que tweetió el mensaje');

GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 4)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (4, N'Seguidores / Siguiendo', N'Los mensajes se ordenarán de acuerdo al coeficiente que se obtiene de dividir a la cantidad de seguidores<br />por la cantidad de usuario que sigue del usuario que tweetió el mensaje');

GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 5)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (5, N'Tweets', N'Los mensajes se ordenarán de acuerdo a la cantidad de mensajes que tweetió el usuario');
GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 6)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (6, N'Mensajes Públicos por sobre Privados', N'Los mensajes se ordenarán de acuerdo a si son públicos (más prioridad) o privados (menos prioridad)');
GO
IF NOT EXISTS(SELECT 1 FROM [QueueSortTypes] WHERE [QueueSortTypeID] = 7)
	INSERT INTO [dbo].[QueueSortTypes] ([QueueSortTypeID], [Name], [Description]) VALUES (7, N'Mensajes Privados por sobre Públicos', N'Los mensajes se ordenarán de acuerdo a si son privados (más prioridad) o públicos (menos prioridad)');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageDistributionTypes] WHERE [MessageDistributionTypeID] = 1)
	INSERT INTO [dbo].[MessageDistributionTypes] ([MessageDistributionTypeID], [Name]) VALUES (1, N'Por Prioridad');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageDistributionTypes] WHERE [MessageDistributionTypeID] = 2)
	INSERT INTO [dbo].[MessageDistributionTypes] ([MessageDistributionTypeID], [Name]) VALUES (2, N'Por Porcentaje');
GO
IF NOT EXISTS(SELECT 1 FROM [MessageDistributionTypes] WHERE [MessageDistributionTypeID] = 3)
	INSERT INTO [dbo].[MessageDistributionTypes] ([MessageDistributionTypeID], [Name]) VALUES (3, N'Por Niveles');
GO
IF NOT EXISTS(SELECT 1 FROM [AuxReasons] WHERE [AuxReasonID] = 0)
	INSERT INTO [dbo].[AuxReasons] ([AuxReasonID], [Name], [Enabled]) VALUES (0, N'Login', 1);
GO
IF NOT EXISTS(SELECT 1 FROM [CaseStatuses] WHERE [CaseStatusID] = 1)
	INSERT INTO [dbo].[CaseStatuses] ([CaseStatusID], [Name]) VALUES (1, N'Abierto');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseStatuses] WHERE [CaseStatusID] = 2)
	INSERT INTO [dbo].[CaseStatuses] ([CaseStatusID], [Name]) VALUES (2, N'Cerrado');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 1)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (1, N'Creación');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 2)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (2, N'Cierre por sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 3)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (3, N'Cierre por filtro');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 4)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (4, N'Cierre por agente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 5)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (5, N'Cierre por supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 6)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (6, N'Reapertura');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 7)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (7, N'Etiquetado automático');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 8)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (8, N'Cierre por transferencia');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 9)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (9, N'Asignación de nueva cola');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 10)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (10, N'Envío de encuesta');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 11)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (11, N'Procesamiento de encuesta');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 12)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (12, N'Nueva etiqueta');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 13)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (13, N'Marcar como pendiente por persona');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 14)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (14, N'Quitar marca de pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 15)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (15, N'Marcar como pendiente de respuesta del cliente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 16)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (16, N'Quitar marca de pendiente de respuesta del cliente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 17)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (17, N'Cierre por Service Level');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 18)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (18, N'Cierre por YFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 19)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (19, N'Caso removido porque se reabrió el caso previo');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 20)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (20, N'Procesamiento de encuesta finalizada');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 21)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (21, N'Agente agregó mensaje al caso');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 22)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (22, N'Se agregó un tag importante al caso');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 23)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (23, N'Se eliminó un tag importante del caso');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 24)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (24, N'El agente finalizó la atención del mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 25)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (25, N'Cierre por Integración con terceros');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 26)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (26, N'Recepción de llamada');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 27)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (27, N'Realización de llamada saliente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 28)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (28, N'La llamada se conectó');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 29)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (29, N'La llamada terminó');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseLogTypes] WHERE [CaseLogTypeID] = 30)
	INSERT INTO [dbo].[CaseLogTypes] ([CaseLogTypeID], [Name]) VALUES (30, N'La llamada se rechazó');
GO

IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 1)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (1, N'Sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 2)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (2, N'Filtro');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 3)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (3, N'Agente');
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 4)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (4, N'Supervisor')
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 5)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (5, N'Service Level')
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 6)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (6, N'YFlow')
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 7)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (7, N'Gateway')
GO
IF NOT EXISTS(SELECT 1 FROM [CaseClosingResponsibles] WHERE [CaseClosingResponsibleID] = 8)
	INSERT INTO [dbo].[CaseClosingResponsibles] ([CaseClosingResponsibleID], [Name]) VALUES (8, N'Integraciones con terceros')
GO

SET IDENTITY_INSERT [dbo].[Countries] ON
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 1)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (1, N'Afghanistan', 93, N'412', 1, N'AF', N'ps');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 2)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (2, N'Albania', 355, N'276', 1, N'AL', N'sq');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 3)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (3, N'Alberta', 1403, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 4)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (4, N'Alberta', 1780, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 5)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (5, N'Algeria', 213, N'603', 1, N'DZ', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 6)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (6, N'Andorra', 376, N'213', 3, N'AD', N'ca');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 7)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (7, N'Angola', 244, N'631', 2, N'AO', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 8)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (8, N'Anguilla', 1264, N'365', 10, N'AI', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 9)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (9, N'Antarctica (Australian bases)', 6721, N'232', 1, N'AQ', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 10)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (10, N'Antigua and Barbuda', 1268, N'344', 50, N'AG', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 11)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (11, N'Argentina', 54, N'722', 10, N'AR', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 12)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (12, N'Armenia', 374, N'283', 10, N'AM', N'hy');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 13)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (13, N'Aruba', 297, N'363', 1, N'AW', N'nl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 14)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (14, N'Ascension', 247, N'658', 1, N'AC', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 15)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (15, N'Australia', 61, N'505', 1, N'AU', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 16)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (16, N'Austria', 43, N'232', 3, N'AT', N'de');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 17)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (17, N'Azerbaijan', 994, N'400', 1, N'AZ', N'az');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 18)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (18, N'Bahamas', 1242, N'364', 39, N'BS', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 19)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (19, N'Bahrain', 973, N'426', 1, N'BH', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 20)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (20, N'Bangladesh', 880, N'470', 1, N'BD', N'bn');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 21)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (21, N'Barbados', 1246, N'342', 750, N'BB', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 22)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (22, N'Belarus', 375, N'257', 1, N'BY', N'be');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 23)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (23, N'Belgium', 32, N'206', 1, N'BE', N'nl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 24)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (24, N'Belize', 501, N'702', 67, N'BZ', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 25)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (25, N'Benin', 229, N'616', 1, N'BJ', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 26)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (26, N'Bermuda', 1441, N'350', 1, N'BM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 27)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (27, N'Bhutan', 975, N'402', 11, N'BT', N'dz');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 28)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (28, N'Bolivia', 591, N'736', 1, N'BO', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 29)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (29, N'Bosnia and Herzegovina', 387, N'218', 3, N'BA', N'bs');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 30)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (30, N'Botswana', 267, N'652', 4, N'BW', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 31)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (31, N'Brazil', 55, N'724', 2, N'BR', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 32)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (32, N'British Columbia', 1250, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 33)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (33, N'British Columbia', 1604, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 34)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (34, N'British Columbia', 1778, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 35)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (35, N'British Indian Ocean Territory', 246, N'348', 1, N'IO', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 36)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (36, N'British Virgin Islands', 1284, N'348', 170, N'GB', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 37)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (37, N'Brunei', 673, N'528', 11, N'BN', N'ms');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 38)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (38, N'Bulgaria', 359, N'284', 3, N'BG', N'bg');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 39)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (39, N'Burkina Faso', 226, N'613', 1, N'BF', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 40)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (40, N'Burundi', 257, N'642', 82, N'BI', N'rn');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 41)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (41, N'Cambodia', 855, N'456', 2, N'KH', N'km');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 42)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (42, N'Cameroon', 237, N'624', 1, N'CM', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 43)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (43, N'Cape Verde', 238, N'625', 1, N'CV', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 44)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (44, N'Cayman Islands', 1345, N'346', 50, N'GB', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 45)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (45, N'Central African Republic', 236, N'623', 3, N'CF', N'sg');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 46)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (46, N'Chad', 235, N'622', 4, N'TD', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 47)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (47, N'Chile', 56, N'730', 2, N'CL', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 48)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (48, N'China', 86, N'460|461', 3, N'CN', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 49)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (49, N'Colombia', 57, N'732', 102, N'CO', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 50)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (50, N'Comoros', 269, N'654', 1, N'KM', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 51)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (51, N'Democratic Republic of the Congo', 243, N'630', 1, N'CD', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 52)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (52, N'Republic of the Congo', 242, N'629', 1, N'CG', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 53)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (53, N'Cook Islands', 682, N'548', 1, N'CK', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 54)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (54, N'Costa Rica', 506, N'658', 4, N'CR', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 55)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (55, N'Cote dIvoire', 712, N'612', 1, N'CI', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 56)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (56, N'Croatia', 385, N'219', 1, N'HR', N'hr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 57)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (57, N'Cuba', 53, N'368', 1, N'CU', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 58)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (58, N'Cyprus', 357, N'280', 1, N'CY', N'el');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 59)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (59, N'Czech Republic', 420, N'230', 2, N'CZ', N'cs');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 60)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (60, N'Denmark', 45, N'238', 1, N'DK', N'da');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 61)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (61, N'Djibouti', 253, N'638', 1, N'DJ', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 62)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (62, N'Dominica', 1767, N'366', 20, N'DM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 63)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (63, N'Dominican Republic', 1809, N'370', 1, N'DO', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 64)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (64, N'Dominican Republic', 1829, N'370', 1, N'DO', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 65)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (65, N'East Timor', 670, N'514', 1, N'TL', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 66)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (66, N'Ecuador', 593, N'740', 0, N'EC', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 67)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (67, N'Egypt', 20, N'602', 2, N'EG', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 68)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (68, N'El Salvador', 503, N'706', 1, N'SV', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 69)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (69, N'Equatorial Guinea', 240, N'627', 3, N'GQ', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 70)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (70, N'Eritrea', 291, N'657', 1, N'ER', N'ti');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 71)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (71, N'Estonia', 372, N'248', 3, N'EE', N'et');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 72)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (72, N'Ethiopia', 251, N'636', 11, N'ET', N'am');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 73)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (73, N'Falkland Islands', 500, N'750', 1, N'FK', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 74)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (74, N'Faroe Islands', 298, N'288', 2, N'FO', N'fo');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 75)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (75, N'Fiji', 679, N'542', 1, N'FJ', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 76)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (76, N'Finland', 358, N'244', 5, N'FI', N'fi');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 77)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (77, N'France', 33, N'208', 9, N'FR', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 78)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (78, N'French Guiana', 594, N'742', 1, N'GF', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 79)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (79, N'French Polynesia', 689, N'547', 15, N'PF', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 80)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (80, N'Gabon', 241, N'628', 1, N'GA', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 81)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (81, N'Gambia', 220, N'607', 1, N'GM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 82)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (82, N'Gaza Strip', 970, N'0', 0, N'PS', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 83)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (83, N'Georgia', 995, N'282', 1, N'GE', N'ka');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 84)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (84, N'Germany', 49, N'262', 1, N'DE', N'de');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 85)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (85, N'Ghana', 233, N'620', 2, N'GH', N'ak');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 86)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (86, N'Gibraltar', 350, N'266', 9, N'GI', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 87)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (87, N'Greece', 30, N'202', 5, N'GR', N'el');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 88)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (88, N'Greenland', 299, N'290', 1, N'GL', N'kl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 89)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (89, N'Grenada', 1473, N'352', 30, N'GD', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 90)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (90, N'Guadeloupe', 590, N'340', 1, N'GP', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 91)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (91, N'Guam', 1671, N'535', 32, N'GU', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 92)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (92, N'Guatemala', 502, N'704', 1, N'GT', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 93)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (93, N'Guinea', 224, N'611', 1, N'GN', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 94)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (94, N'Guinea-Bissau', 245, N'632', 3, N'GW', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 95)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (95, N'Guyana', 592, N'738', 1, N'GY', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 96)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (96, N'Haiti', 509, N'372', 2, N'HT', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 97)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (97, N'Honduras', 504, N'708', 2, N'HN', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 98)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (98, N'Hong Kong', 852, N'454', 0, N'HK', N'zh');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 99)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (99, N'Hungary', 36, N'216', 70, N'HU', N'hu');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 100)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (100, N'Iceland', 354, N'274', 2, N'IS', N'is');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 101)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (101, N'India', 91, N'404|405|406', 30, N'IN', N'hi');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 102)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (102, N'Indonesia', 62, N'510', 10, N'ID', N'id');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 103)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (103, N'Iraq', 964, N'418', 20, N'IQ', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 104)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (104, N'Iran', 98, N'432', 35, N'IR', N'fa');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 105)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (105, N'Ireland (Eire)', 353, N'272', 1, N'IE', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 106)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (106, N'Israel', 972, N'425', 1, N'IL', N'he');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 107)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (107, N'Italy', 39, N'222', 10, N'IT', N'it');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 108)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (108, N'Jamaica', 1876, N'338', 50, N'JM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 109)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (109, N'Japan', 81, N'440|441', 1, N'JP', N'ja');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 110)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (110, N'Jordan', 962, N'416', 77, N'JO', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 111)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (111, N'Kazakhstan', 7, N'401', 77, N'KZ', N'kk');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 112)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (112, N'Kenya', 254, N'639', 7, N'KE', N'sw');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 113)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (113, N'Kiribati', 686, N'545', 1, N'KI', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 114)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (114, N'Kuwait', 965, N'419', 4, N'KW', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 115)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (115, N'Kyrgyzstan', 996, N'437', 1, N'KG', N'ky');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 116)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (116, N'Laos', 856, N'457', 1, N'LA', N'lo');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 117)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (117, N'Latvia', 371, N'247', 2, N'LV', N'lv');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 118)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (118, N'Lebanon', 961, N'415', 1, N'LB', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 119)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (119, N'Lesotho', 266, N'651', 1, N'LS', N'st');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 120)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (120, N'Liberia', 231, N'618', 7, N'LR', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 121)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (121, N'Libya', 218, N'606', 0, N'LY', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 122)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (122, N'Liechtenstein', 423, N'295', 2, N'LI', N'de');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 123)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (123, N'Lithuania', 370, N'246', 3, N'LT', N'lt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 124)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (124, N'Luxembourg', 352, N'270', 99, N'LU', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 125)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (125, N'Macau', 853, N'455', 2, N'MO', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 126)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (126, N'Republic of Macedonia', 389, N'294', 1, N'MK', N'mk');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 127)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (127, N'Madagascar', 261, N'646', 2, N'MG', N'mg');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 128)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (128, N'Malawi', 265, N'650', 1, N'MW', N'ny');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 129)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (129, N'Malaysia', 60, N'502', 16, N'MY', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 130)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (130, N'Maldives', 960, N'472', 1, N'MV', N'dv');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 131)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (131, N'Mali', 223, N'610', 2, N'ML', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 132)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (132, N'Malta', 356, N'278', 1, N'MT', N'mt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 133)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (133, N'Manitoba', 1204, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 134)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (134, N'Marshall Islands', 692, N'551', 1, N'MH', N'mh');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 135)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (135, N'Martinique', 596, N'340', 1, N'MQ', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 136)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (136, N'Mauritania', 222, N'609', 2, N'MR', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 137)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (137, N'Mauritius', 230, N'617', 1, N'MU', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 138)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (138, N'Mayotte', 262, N'654', 1, N'YT', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 139)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (139, N'Mexico', 52, N'334', 3, N'MX', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 140)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (140, N'Federated States of Micronesia', 691, N'550', 1, N'FM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 141)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (141, N'Moldova', 373, N'259', 1, N'MD', N'ru');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 142)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (142, N'Monaco', 377, N'212', 1, N'MC', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 143)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (143, N'Mongolia', 976, N'428', 91, N'MN', N'mn');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 144)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (144, N'Montenegro', 382, N'297', 2, N'ME', N'sr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 145)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (145, N'Montserrat', 1664, N'354', 860, N'MS', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 146)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (146, N'Morocco', 212, N'604', 0, N'MA', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 147)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (147, N'Mozambique', 258, N'643', 4, N'MZ', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 148)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (148, N'Myanmar', 95, N'414', 1, N'MM', N'my');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 149)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (149, N'Namibia', 264, N'649', 3, N'NA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 150)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (150, N'Nauru', 674, N'536', 2, N'NR', N'na');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 151)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (151, N'Netherlands', 31, N'204', 4, N'NL', N'nl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 152)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (152, N'Netherlands Antilles', 599, N'362', 51, N'AN', N'nl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 153)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (153, N'Nepal', 977, N'429', 1, N'NP', N'ne');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 154)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (154, N'New Brunswick', 1506, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 155)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (155, N'New Caledonia', 687, N'546', 1, N'NC', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 156)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (156, N'New Zealand', 64, N'530', 1, N'NZ', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 157)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (157, N'Newfoundland', 1709, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 158)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (158, N'Nicaragua', 505, N'710', 30, N'NI', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 159)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (159, N'Niger', 227, N'614', 4, N'NE', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 160)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (160, N'Nigeria', 234, N'621', 20, N'NG', N'ha');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 161)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (161, N'Niue', 683, N'555', 1, N'NU', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 162)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (162, N'Norfolk Island', 6723, N'505', 10, N'NF', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 163)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (163, N'North Korea', 850, N'467', 193, N'KP', N'ko');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 164)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (164, N'Northern Mariana Islands', 1670, N'534', 1, N'MP', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 165)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (165, N'Northwest Territories', 1867, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 166)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (166, N'Norway', 47, N'242', 4, N'NO', N'nb');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 167)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (167, N'Nova Scotia', 1902, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 168)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (168, N'Oman', 968, N'422', 2, N'OM', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 169)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (169, N'Ontario', 1416, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 170)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (170, N'Ontario', 1519, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 171)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (171, N'Ontario', 1613, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 172)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (172, N'Ontario', 1647, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 173)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (173, N'Ontario', 1705, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 174)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (174, N'Ontario', 1807, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 175)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (175, N'Ontario', 1905, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 176)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (176, N'Pakistan', 92, N'410', 1, N'PK', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 177)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (177, N'Palau', 680, N'552', 80, N'PW', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 178)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (178, N'Palestine', 970, N'425', 6, N'PS', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 179)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (179, N'Panama', 507, N'714', 2, N'PA', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 180)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (180, N'Papua New Guinea', 675, N'537', 3, N'PG', N'ho');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 181)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (181, N'Paraguay', 595, N'744', 6, N'PY', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 182)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (182, N'Peru', 51, N'716', 6, N'PE', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 183)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (183, N'Philippines', 63, N'515', 2, N'PH', N'fil');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 184)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (184, N'Poland', 48, N'260', 3, N'PL', N'pl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 185)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (185, N'Portugal', 351, N'268', 1, N'PT', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 186)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (186, N'Qatar', 974, N'427', 2, N'QA', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 187)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (187, N'Quebec', 1418, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 188)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (188, N'Quebec', 1450, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 189)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (189, N'Quebec', 1514, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 190)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (190, N'Quebec', 1819, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 191)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (191, N'Reunion', 262, N'647', 0, N'RE', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 192)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (192, N'Romania', 40, N'226', 1, N'RO', N'ro');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 193)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (193, N'Russia', 7, N'250', 20, N'RU', N'ru');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 194)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (194, N'Rwanda', 250, N'635', 10, N'RW', N'rw');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 195)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (195, N'Saint-Barthelemy', 590, N'340', 1, N'BL', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 196)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (196, N'Saint Helena', 290, N'658', 1, N'SH', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 197)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (197, N'Saint Kitts and Nevis', 1869, N'356', 50, N'KN', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 198)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (198, N'Saint Lucia', 1758, N'358', 50, N'LC', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 199)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (199, N'Saint Martin (French side)', 590, N'340', 1, N'MF', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 200)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (200, N'Saint Pierre and Miquelon', 508, N'308', 2, N'PM', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 201)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (201, N'Saint Vincent and the Grenadines', 1670, N'360', 70, N'VC', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 202)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (202, N'Samoa', 685, N'549', 1, N'WS', N'sm');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 203)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (203, N'Sao Tome and Principe', 239, N'626', 1, N'ST', N'pt');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 204)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (204, N'Saskatchewan', 1306, N'302', 720, N'CA', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 205)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (205, N'Saudi Arabia', 966, N'420', 4, N'SA', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 206)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (206, N'Senegal', 221, N'608', 1, N'SN', N'wo');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 207)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (207, N'Serbia', 381, N'220', 1, N'RS', N'sr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 208)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (208, N'Seychelles', 248, N'633', 10, N'SC', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 209)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (209, N'Sierra Leone', 232, N'619', 4, N'SL', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 210)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (210, N'Singapore', 65, N'525', 1, N'SG', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 211)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (211, N'Slovakia', 421, N'231', 4, N'SK', N'sk');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 212)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (212, N'Slovenia', 386, N'293', 31, N'SI', N'sl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 213)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (213, N'Solomon Islands', 677, N'540', 2, N'SB', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 214)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (214, N'Somalia', 252, N'637', 82, N'SO', N'so');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 215)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (215, N'South Africa', 27, N'655', 1, N'ZA', N'xh');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 216)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (216, N'South Korea', 82, N'450', 5, N'KR', N'ko');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 217)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (217, N'South Sudan', 211, N'659', 2, N'SS', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 218)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (218, N'Spain', 34, N'214', 1, N'ES', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 219)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (219, N'Sri Lanka', 94, N'413', 1, N'LK', N'si');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 220)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (220, N'Sudan', 249, N'634', 7, N'SD', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 221)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (221, N'Suriname', 597, N'746', 3, N'SR', N'nl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 222)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (222, N'Swaziland', 268, N'653', 10, N'SZ', N'ss');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 223)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (223, N'Sweden', 46, N'240', 7, N'SE', N'sv');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 224)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (224, N'Switzerland', 41, N'228', 3, N'CH', N'de');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 225)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (225, N'Syria', 963, N'417', 1, N'SY', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 226)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (226, N'Taiwan', 886, N'466', 1, N'TW', N'cmn');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 227)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (227, N'Tajikistan', 992, N'436', 1, N'TJ', N'tg');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 228)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (228, N'Tanzania', 255, N'640', 4, N'TZ', N'sw');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 229)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (229, N'Thailand', 66, N'520', 0, N'TH', N'th');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 230)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (230, N'Togo', 228, N'615', 1, N'TG', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 231)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (231, N'Tokelau', 690, N'690', 1, N'TK', N'tkl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 232)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (232, N'Tonga', 676, N'539', 1, N'TO', N'to');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 233)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (233, N'Trinidad and Tobago', 1868, N'374', 12, N'TT', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 234)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (234, N'Tunisia', 216, N'605', 1, N'TN', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 235)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (235, N'Turkey', 90, N'286', 2, N'TR', N'tr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 236)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (236, N'Turkmenistan', 993, N'438', 1, N'TM', N'tk');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 237)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (237, N'Turks and Caicos Islands', 1649, N'376', 50, N'TC', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 238)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (238, N'Tuvalu', 688, N'553', 1, N'TV', N'tvl');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 239)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (239, N'Uganda', 256, N'641', 14, N'UG', N'sw');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 240)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (240, N'Ukraine', 380, N'255', 1, N'UA', N'uk');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 241)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (241, N'United Arab Emirates', 971, N'424|430|431', 2, N'AE', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 242)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (242, N'United Kingdom', 44, N'234|235', 10, N'GB', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 243)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (243, N'United States of America', 1, N'310|311|312|313|314|315|316', 4, N'US', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 244)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (244, N'Uruguay', 598, N'748', 7, N'UY', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 245)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (245, N'Uzbekistan', 998, N'434', 7, N'UZ', N'uz');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 246)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (246, N'Vanuatu', 678, N'541', 5, N'VU', N'bi');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 247)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (247, N'Venezuela', 58, N'734', 4, N'VE', N'es');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 248)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (248, N'Vietnam', 84, N'452', 1, N'VN', N'vi');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 249)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (249, N'U.S. Virgin Islands', 1340, N'332', 4, N'VI', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 250)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (250, N'Wallis and Futuna', 681, N'543', 1, N'WF', N'fr');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 251)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (251, N'West Bank', 970, N'0', 1, N'PS', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 252)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (252, N'Yemen', 967, N'421', 2, N'YE', N'ar');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 253)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (253, N'Zambia', 260, N'645', 2, N'ZM', N'en');
GO
IF NOT EXISTS(SELECT 1 FROM [Countries] WHERE [CountryID] = 254)
	INSERT INTO [dbo].[Countries] ([CountryID], [CountryDescription], [CountryInternationalCode], [CountryAuxCode1], [CountryAuxCode2], [CountryAbbreviation], [CountryLanguage]) VALUES (254, N'Zimbabwe', 263, N'648', 2, N'ZW', N'en');
GO
SET IDENTITY_INSERT [dbo].[Countries] OFF
GO

IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 1)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (1, N'Agente');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 2)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (2, N'Usuario');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 3)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (3, N'Supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 4)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (4, N'Sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 5)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (5, N'Bot')
GO
IF NOT EXISTS(SELECT 1 FROM [ChatSenders] WHERE [ChatSenderID] = 6)
	INSERT INTO [dbo].[ChatSenders] ([ChatSenderID], [ChatSenderDescription]) VALUES (6, N'Service Level')
GO

IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 1)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (1,N'Loguearse');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 2)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (2,N'Desloguearse');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 3)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (3,N'Cambiar de estado');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 4)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (4,N'Responder mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 5)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (5,N'Descartar mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 6)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (6,N'Cerrar caso');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 7)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (7,N'Etiquetar');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 8)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (8,N'Reabrir caso');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 9)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (9,N'Asignar mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 10)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (10,N'Agrupar mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 11)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (11,N'Leer mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 12)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (12,N'Modificar nro de cliente');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 13)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (13,N'Like a mensaje de facebook');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 14)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (14,N'Follow a usuario de twitter');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 15)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (15,N'Unfollow a usuario de twitter');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 16)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (16,N'Desconexión forzosa');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 17)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (17,N'Reconexión');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 18)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (18,N'Devolver mensaje a la cola');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 19)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (19,N'Finalizar de leer el mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 20)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (20,N'Agente hizo favorito de un tweet');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 21)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (21,N'Agente hizo retweet');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 22)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (22,N'Agente hizo forward de un mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 23)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (23,N'Mover mensaje a otra cola');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 24)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (24,N'Unificar perfiles de usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 25)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (25,N'Hide a mensaje de facebook');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 26)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (26,N'Desasociar una cuenta de usuario de un perfil');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 27)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (27,N'Finalizar un mensaje de chat');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 28)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (28,N'Agregar usuario a la Black List');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 29)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (29,N'Ponerse en Pendiente Auxiliar');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 30)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (30,N'Cambiar el motivo de auxiliar en el estado Pendiente de Auxiliar');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 31)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (31,N'Cancelar cambio a Pendiente de Auxiliar');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 32)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (32,N'El agente transfirió el mensaje a yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 33)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (33,N'El agente transfirió el mensaje a yFlow pero falló');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 34)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (34,N'El agente agregó un mensaje a un caso');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 35)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (35,N'Desasignar mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 36)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (36,N'Inactividad del agente en modo chat');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 37)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (37,N'Inactividad del usuario final en modo chat');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 38)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (38,N'Realizó una llamada saliente');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 39)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (39,N'Recepción de llamada');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 40)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (40,N'La llamada se conectó');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 41)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (41,N'La llamada terminó');
GO
IF NOT EXISTS(SELECT 1 FROM [AgentLogTypes] WHERE [AgentLogTypeID] = 42)
	INSERT INTO [dbo].[AgentLogTypes] ([AgentLogTypeID],[Name]) VALUES (42,N'La llamada se rechazó');
GO

IF NOT EXISTS(SELECT 1 FROM [Services] WHERE [ServiceID] = 0)
	INSERT INTO [dbo].[Services] ([ServiceID], [Name], [ServiceTypeID], [Configuration], [Status], [Enabled], [Avatar], [AccessTokenExpiresAt], [QueueID], [SocialServiceTypeID], [GroupingOptions], [Settings]) VALUES (0, N'Global', 8, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL);
GO

IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 1)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (1, N'Chat Iniciado con Agente y Usuario');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 2)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (2, N'Usuario abandonó la conversación');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 3)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (3, N'Agente dio por finalizado el chat');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 4)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (4, N'Supervisor comenzó a observar el chat');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 5)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (5, N'Supervisor ingresa en la conversación');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 6)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (6, N'Supervisor abandona la conversación sin cerrar el chat');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 7)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (7, N'Supervisor abandona la conversación cerrando el chat');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 8)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (8, N'Usuario envía mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 9)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (9, N'Usuario envía archivo adjunto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 10)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (10, N'Agente envía mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 11)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (11, N'Agente envía archivo adjunto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 12)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (12, N'Supervisor envía mensaje');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 13)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (13, N'Supervisor envía archivo adjunto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 14)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (14, N'Usuario califica agente');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 15)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (15, N'Agente envía el primer mensaje de inactividad');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 16)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (16, N'Agente envía la advertencia de que se cerrará el chat por inactividad');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 17)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (17, N'Agente pone en pausa la conversación');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 18)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (18, N'Agente devuelve un chat a la cola');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 19)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (19, N'Agente transfiere un chat a otra cola');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 20)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (20, N'Usuario solicita transcripción del chat al finalizar');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 21)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (21, N'Supervisor en modo privado con el agente');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatLogTypes] WHERE [ChatLogTypeID] = 22)
	INSERT INTO [dbo].[ChatLogTypes] ([ChatLogTypeID], [Name]) VALUES (22, N'Se retorna el chat a la cola luego de desconexión del agente');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 1)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (1, N'Usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 2)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (2, N'Perfiles');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 3)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (3, N'Colas');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 4)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (4, N'Agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 5)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (5, N'Agentes por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 6)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (6, N'Motivos de Auxiliar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 7)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (7, N'White List');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 8)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (8, N'Black List');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 9)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (9, N'Etiquetas');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 10)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (10, N'Parámetros del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 11)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (11, N'Servicios');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 12)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (12, N'Filtros');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 13)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (13, N'Respuestas predefinidas');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 14)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (14, N'Perfiles de usuario');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 15)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (15, N'Mensajes del supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 16)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (16, N'Mensajes salientes');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 17)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (17, N'Grupos de agente por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 18)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (18, N'Grupos de agente');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 19)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (19, N'Motivos de contacto');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 20)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (20, N'Encuestas');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 21)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (21, N'Integraciones con terceros');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 22)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (22, N'Respuestas predefinidas por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 23)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (23, N'Sitios');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 24)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (24, N'Grupos de cola');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 25)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (25, N'Configuración de seguridad para integraciones con terceros');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 26)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (26, N'Licencia');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 27)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (27, N'Estado del sistema');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 28)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (28, N'Listas de perfiles de usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 29)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (29, N'Tester List');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 30)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (30, N'DoNotCall List');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 31)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (31, N'Grupos de etiquetas');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemEntityTypes] WHERE [SystemEntityTypeID] = 32)
	INSERT INTO [dbo].[SystemEntityTypes] ([SystemEntityTypeID], [Name]) VALUES (32, N'Casillas de correo');
GO

IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 1)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (1, N'Agregar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 2)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (2, N'Eliminar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 3)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (3, N'Editar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 4)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (4, N'Habilitar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 5)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (5, N'Deshabilitar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 6)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (6, N'Cambiar contraseña');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 7)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (7, N'Reintentar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 8)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (8, N'Reservar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 9)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (9, N'Descartar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 10)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (10, N'Verificar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 11)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (11, N'Reordenar');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemActionTypes] WHERE [SystemActionTypeID] = 12)
	INSERT INTO [dbo].[SystemActionTypes] ([SystemActionTypeID], [Name]) VALUES (12, N'Convertir agente en usuario');
GO

UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:true, PublicMaxLength:280, PublicSupportsAttachments:true, PublicMaxAttachments:4, PrivateMaxLength:10000, PrivateSupportsAttachments:true, FontAwesomeIcon:"fab fa-twitter-square" }' WHERE [SocialServiceTypeID] = 1;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:true, PublicMaxLength:0, PublicSupportsAttachments:true, PublicMaxAttachments:1, PrivateMaxLength:0, PrivateSupportsAttachments:true, FontAwesomeIcon:"fab fa-facebook" }' WHERE [SocialServiceTypeID] = 2;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:0, FontAwesomeIcon:"fa fa-envelope" }' WHERE [SocialServiceTypeID] = 4;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, FontAwesomeIcon:"fa fa-comments" }' WHERE [SocialServiceTypeID] = 8;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:1, FontAwesomeIcon:"fab fa-whatsapp-square" }' WHERE [SocialServiceTypeID] = 16;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:160, SupportsAttachments:false, FontAwesomeIcon:"fa fa-comment" }' WHERE [SocialServiceTypeID] = 32;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:1, FontAwesomeIcon:"fab fa-telegram" }' WHERE [SocialServiceTypeID] = 128;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:true, MaxLength:0, PublicSupportsAttachments:false, PublicMaxAttachments:0, PrivateMaxLength:10000, PrivateSupportsAttachments:true, FontAwesomeIcon:"fab fa-instagram" }' WHERE [SocialServiceTypeID] = 256;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:true, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fab fa-linkedin" }' WHERE [SocialServiceTypeID] = 512;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:1, FontAwesomeIcon:"fab fa-skype" }' WHERE [SocialServiceTypeID] = 1024;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:1, FontAwesomeIcon:"fab fa-facebook-messenger" }' WHERE [SocialServiceTypeID] = 2048;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fab fa-mercadolibre" }' WHERE [SocialServiceTypeID] = 4096;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fab fa-youtube" }' WHERE [SocialServiceTypeID] = 8192;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fab fa-google-play" }' WHERE [SocialServiceTypeID] = 16384;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fas fa-video" }' WHERE [SocialServiceTypeID] = 16385;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:1, FontAwesomeIcon:"fab fa-android" }' WHERE [SocialServiceTypeID] = 16386;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:true, MaxAttachments:0, FontAwesomeIcon:"fab fa-apple" }' WHERE [SocialServiceTypeID] = 16400;
GO
UPDATE [SocialServiceTypes] SET [Configuration] = '{ SupportsPublicAndPrivate:false, MaxLength:0, SupportsAttachments:false, MaxAttachments:0, FontAwesomeIcon:"fab fa-google" }' WHERE [SocialServiceTypeID] = 16401;
GO

IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 1)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (1, N'Detalle de Mensajes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 3)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (3, 'Detallado de casos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 4)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (4, 'Detallado de usuarios de redes sociales');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 5)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (5, 'Detallado de colas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 6)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (6, 'Actividad de mensajes por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 7)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (7, 'Actividad de etiquetas por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 8)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (8, 'Actividad de servicios por cola');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 9)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (9, 'Detallado de agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 10)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (10, 'Actividad de mensajes por agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 11)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (11, 'Actividad de etiquetas por agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 12)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (12, 'Actividad de servicios por agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 13)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (13, 'Login Logout de Agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 14)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (14, 'Detallado de Chats');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 15)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (15, 'Detallado de Encuestas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 16)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (16, 'Consolidado de Casos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 17)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (17, 'Detallado de Eventos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 18)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (18, 'Listado de Usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 19)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (19, 'Listado de agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 20)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (20, 'Información de trabajo de una sesión de Agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 21)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (21, 'Detallado de Servicios');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 22)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (22, 'Etiquetas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 23)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (23, 'WhiteList');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 24)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (24, 'BlackList');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 25)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (25, 'Detallado de Mensajes de Whatsapp HSM');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 26)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (26, 'Consolidado de Mensajes de Whatsapp HSM');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 27)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (27, 'Detallado de Tareas de envío masivo de Mensajes de Whatsapp HSM');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 28)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (28, 'Detallado de Tareas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 29)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (29, 'Consolidado de Encuestas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 30)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (30, 'Detallado de mensajes de un chat');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 31)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (31, 'Reporte de adherencia consolidado por sitio');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 32)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (32, 'Reporte de adherencia detallado por agente por sitio');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 33)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (33, 'Reporte detallado de perfiles de usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 34)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (34, 'Reporte detallado de login logout de usuarios');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 35)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (35, 'Reporte detallado de Tareas de envío masivo de Mensajes de Whatsapp HSM');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 36)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (36, 'Reporte detallado de transferencias');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 37)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (37, 'Reporte detallado de Reapertura de casos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 38)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (38, 'Detallado de mensajes de chats');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 39)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (39, 'Detallado de segmentos de tiempo de mensajes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 40)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (40, 'Lista de perfiles Testers');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 41)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (41, 'Lista de perfiles No Contactar');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 42)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (42, 'Reporte de carga de perfiles masiva');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 43)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (43, 'Listado de colas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 44)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (44, 'HSM sin caso');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 45)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (45, 'Asignación por colas de agentes o grupos de agentes');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 46)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (46, 'Agentes en tiempo real');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 47)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (47, 'Detallado de tiempos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 48)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (48, 'VideoLlamadas');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 49)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (49, 'Detallado de eventos');
GO
IF NOT EXISTS(SELECT 1 FROM [ReportsTypes] WHERE [IdReportType] = 50)
	INSERT INTO [dbo].[ReportsTypes] ([IdReportType], [Name]) VALUES (50, 'Llamadas');
GO

IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 1)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (1, N'Descarte por filtro');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 2)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (2, N'Descarte por cumplimiento de condición de SL');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 3)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (3, N'Descarte por el agente');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 4)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (4, N'Descarte del supervisor');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 5)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (5, N'Descarte por agrupamiento');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 6)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (6, N'Descarte porque se eliminó el mensaje en la red social');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 7)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (7, N'Descarte del sistema porque se superó la cantidad configurada de mensajes recibidos');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 8)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (8, N'Descarte del Sistema por máximo tiempo permitido por el servicio de Whatsapp');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 9)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (9, N'Descarte del Sistema por máximo tiempo permitido por el servicio de Messenger');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 10)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (10, N'Descarte del Sistema por YFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 11)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (11, N'Descarte del sistema porque ingresó un nuevo mensaje y canceló la invocación a yFlow');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 12)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (12, N'Descarte del sistema porque ingresó un nuevo mensaje y el mensaje pendiente previo del caso ya no aplica');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 13)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (13, N'Descarte del sistema porque falló la derivación al gateway');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 14)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (14, N'Descarte del Sistema por máximo tiempo permitido por el servicio de Instagram');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 15)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (15, N'Descarte por servicio deshabilitado');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 16)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (16, N'Descarte por falta de novedades del gateway');
GO
IF NOT EXISTS(SELECT 1 FROM [DiscardSources] WHERE [DiscardSourceID] = 17)
	INSERT INTO [dbo].[DiscardSources] ([DiscardSourceID], [Name]) VALUES (17, N'Descarte por inactividad del usuario final en servicios con modo CHAT');
GO

UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Twitter.TwitterSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 1;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Facebook.FacebookSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 2;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Twitter.TwitterSearchSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 3;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Mail.MailService, Yoizen.Social.SocialServices.Mail, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 4;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Chat.ChatService, Yoizen.Social.SocialServices.Chat, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 5;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.WhatsApp.WhatsAppService, Yoizen.Social.SocialServices.WhatsApp, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 6;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.SMS.SMSSocialService, Yoizen.Social.SocialServices.SMS, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 7;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Global.GlobalService, Yoizen.Social.SocialServices.Global, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 8;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Telegram.TelegramSocialService, Yoizen.Social.SocialServices.Telegram, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 9;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Facebook.FacebookRTSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 10;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Instagram.InstagramSocialService, Yoizen.Social.SocialServices.Instagram, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 11;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.LinkedIn.LinkedInSocialService, Yoizen.Social.SocialServices.LinkedIn, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 12;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Skype.SkypeSocialService, Yoizen.Social.SocialServices.Skype, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 13;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Facebook.FacebookMessengerSocialService, Yoizen.Social.SocialServices.Facebook, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c', [SocialServiceTypeID] = 2048 WHERE [ServiceTypeID] = 14;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Twitter.TwitterRTSocialService, Yoizen.Social.SocialServices.Twitter, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 15;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.MercadoLibre.MercadoLibreSocialService, Yoizen.Social.SocialServices.MercadoLibre, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 16;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.YouTube.YouTubeSocialService, Yoizen.Social.SocialServices.YouTube, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 17;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.Chat.IntegrationChatService, Yoizen.Social.SocialServices.Chat, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 18;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.GooglePlay.GooglePlaySocialService, Yoizen.Social.SocialServices.GooglePlay, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 19;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.VideoCalls.VideoCallsSocialService, Yoizen.Social.SocialServices.VideoCalls, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 20;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.GoogleRBM.GoogleRBMSocialService, Yoizen.Social.SocialServices.GoogleRBM, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 21;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.AppleMessaging.AppleMessagingSocialService, Yoizen.Social.SocialServices.AppleMessaging, Version=9.4.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 22;
GO
UPDATE [dbo].[ServiceTypes] SET [ClassType] = N'Yoizen.Social.SocialServices.GoogleBusiness.GoogleBusinessSocialService, Yoizen.Social.SocialServices.GoogleBusiness, Version=*******, Culture=neutral, PublicKeyToken=8c5134be849e992c' WHERE [ServiceTypeID] = 23;
GO

UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Usuarios' WHERE [SystemEntityTypeID] = 1;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Perfiles' WHERE [SystemEntityTypeID] = 2;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Colas' WHERE [SystemEntityTypeID] = 3;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Agentes' WHERE [SystemEntityTypeID] = 4;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Agentes por cola' WHERE [SystemEntityTypeID] = 5;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Motivos de Auxiliar' WHERE [SystemEntityTypeID] = 6;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'White List' WHERE [SystemEntityTypeID] = 7;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Black List' WHERE [SystemEntityTypeID] = 8;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Etiquetas' WHERE [SystemEntityTypeID] = 9;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Parámetros del sistema' WHERE [SystemEntityTypeID] = 10;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Servicios' WHERE [SystemEntityTypeID] = 11;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Filtros' WHERE [SystemEntityTypeID] = 12;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Respuestas predefinidas' WHERE [SystemEntityTypeID] = 13;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Perfiles de usuario' WHERE [SystemEntityTypeID] = 14;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Mensajes del supervisor' WHERE [SystemEntityTypeID] = 15;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Mensajes salientes' WHERE [SystemEntityTypeID] = 16;
GO
UPDATE [dbo].[SystemEntityTypes] SET [Name] = N'Grupos de agente por cola' WHERE [SystemEntityTypeID] = 17;
GO

UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Agregar' WHERE [SystemActionTypeID] = 1;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Eliminar' WHERE [SystemActionTypeID] = 2;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Editar' WHERE [SystemActionTypeID] = 3;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Habilitar' WHERE [SystemActionTypeID] = 4;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Deshabilitar' WHERE [SystemActionTypeID] = 5;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Cambiar contraseña' WHERE [SystemActionTypeID] = 6;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Reintentar' WHERE [SystemActionTypeID] = 7;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Reservar' WHERE [SystemActionTypeID] = 8;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Descartar' WHERE [SystemActionTypeID] = 9;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Verificar' WHERE [SystemActionTypeID] = 10;
GO
UPDATE [dbo].[SystemActionTypes] SET [Name] = N'Reordenar' WHERE [SystemActionTypeID] = 11;
GO

IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 1)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (1, N'Texto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 2)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (2, N'Adjunto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 3)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (3, N'Postback');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 4)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (4, N'Estructurado');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 5)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (5, N'Contexto');
GO
IF NOT EXISTS(SELECT 1 FROM [ChatMessageTypes] WHERE [ChatMessageTypeID] = 6)
	INSERT INTO [dbo].[ChatMessageTypes] ([ChatMessageTypeID], [Type]) VALUES (6, N'ContextoConAdjunto');
GO

IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Credentials.EncryptedPassword')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Credentials.EncryptedPassword', N'i2Doa+3jy3FNX9YtUNaCfA==', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Credentials.Password')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Credentials.Password', N'', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Credentials.Username')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Credentials.Username', N'<EMAIL>', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.From')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.From', N'<EMAIL>', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Port')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Port', N'465', N'System.Int16');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Protocol')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Protocol', N'1', N'System.Byte');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.Server')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.Server', N'smtp.domain.com', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.SMTPServer')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.SMTPServer', N'smtp.gmail.com', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.UseCredentials')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.UseCredentials', N'True', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.UseDefaultPort')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.UseDefaultPort', N'False', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Email.UseSSL')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Email.UseSSL', N'True', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Cases.AllowToAutoReplyCloseCases')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Cases.AllowToAutoReplyCloseCases', N'False', N'System.Boolean');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Cases.AutoReplyInCloseCaseText')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Cases.AutoReplyInCloseCaseText', N'', N'System.String');
GO
IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'Cases.TagOnAutoCloseCase')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'Cases.TagOnAutoCloseCase', N'0', N'System.Int32');
GO

IF NOT EXISTS(SELECT 1 FROM [SystemSettings] WHERE [Key] = N'ScheduledReportsToMantain')
	INSERT INTO [dbo].[SystemSettings] ([Key], [Value], [Type]) VALUES (N'ScheduledReportsToMantain', N'20', N'System.Int32');
GO

--UPDATE [dbo].[SystemSettings] SET [Value] = N'i2Doa+3jy3FNX9YtUNaCfA==' WHERE [Key] = N'Email.Credentials.EncryptedPassword';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'' WHERE [Key] = N'Email.Credentials.Password';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'<EMAIL>' WHERE [Key] = N'Email.Credentials.Username';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'<EMAIL>' WHERE [Key] = N'Email.From';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'465' WHERE [Key] = N'Email.Port';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'1' WHERE [Key] = N'Email.Protocol';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'smtp.domain.com' WHERE [Key] = N'Email.Server';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'smtp.gmail.com' WHERE [Key] = N'Email.SMTPServer';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'True' WHERE [Key] = N'Email.UseCredentials';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'False' WHERE [Key] = N'Email.UseDefaultPort';
--UPDATE [dbo].[SystemSettings] SET [Value] = N'True' WHERE [Key] = N'Email.UseSSL';
UPDATE [dbo].[SystemSettings] SET [Value] = N'https://survey.ysocial.net/' WHERE [Key] = N'SurveysURL';
GO

DELETE FROM [dbo].[ProfilesPermissions] WHERE [ProfileID] = 2 AND [PermissionID] = 21;
GO

IF NOT EXISTS(SELECT 1 FROM [TaskResults] WHERE [TaskResultID] = 1)
	INSERT INTO [dbo].[TaskResults] ([TaskResultID], [Description]) VALUES (1, N'Exitoso');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskResults] WHERE [TaskResultID] = 2)
	INSERT INTO [dbo].[TaskResults] ([TaskResultID], [Description]) VALUES (2, N'Parcial');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskResults] WHERE [TaskResultID] = 3)
	INSERT INTO [dbo].[TaskResults] ([TaskResultID], [Description]) VALUES (3, N'Fallido');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 1)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (1, N'Pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 2)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (2, N'En proceso');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 3)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (3, N'Finalizado');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 4)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (4, N'Cancelada');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 5)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (5, N'Autorización Pendiente');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 6)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (6, N'Expiró por falta de Autorización');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 7)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (7, N'Rechazada');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 8)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (8, N'Autorizada');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 9)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (9, N'Archivo interpretado');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 10)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (10, N'Procesamiento de cola');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 11)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (11, N'Con pedido de cancelación');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskStatuses] WHERE [TaskStatusID] = 12)
	INSERT INTO [dbo].[TaskStatuses] ([TaskStatusID], [Description]) VALUES (12, N'Cancelada pero con procesamiento previo');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskTypes] WHERE [TaskTypeID] = 1)
	INSERT INTO [dbo].[TaskTypes] ([TaskTypeID], [Description]) VALUES (1, N'Envío masivo de Mensajes de Plantilla de whatsapp (HSM)');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskTypes] WHERE [TaskTypeID] = 2)
	INSERT INTO [dbo].[TaskTypes] ([TaskTypeID], [Description]) VALUES (2, N'Solicitud de Envío masivo de Mensajes de Plantilla de whatsapp (HSM)');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskTypes] WHERE [TaskTypeID] = 3)
	INSERT INTO [dbo].[TaskTypes] ([TaskTypeID], [Description]) VALUES (3, N'Solicitud de Envío masivo de Mensajes de Plantilla de whatsapp (HSM) Sin creacion de caso');
GO
IF NOT EXISTS(SELECT 1 FROM [TaskTypes] WHERE [TaskTypeID] = 4)
	INSERT INTO [dbo].[TaskTypes] ([TaskTypeID], [Description]) VALUES (4, N'Carga Masiva de Perfiles');
GO
UPDATE [ReportsExport] SET [ReportExportStatusID] = 1 WHERE [Generated] = 0;
GO
UPDATE [ReportsExport] SET [ReportExportStatusID] = 3, [ReportExportResultID] = 1 WHERE [Generated] = 1;
GO
UPDATE [Templates] SET [Type] = 1 WHERE [ServiceID] IS NOT NULL AND [QueueID] IS NULL;
GO

UPDATE [Tags] SET
	[Level] = 
		CASE
			WHEN [ParentTagID] IS NULL THEN 1
			ELSE 2
		END
	WHERE [Level] IS NULL;
GO
UPDATE [Tags] SET
	[Used] = 
		CASE
			WHEN EXISTS(SELECT 1 FROM [CasesTags] ct WHERE ct.[TagID] = [Tags].[TagID]) THEN 1
			ELSE 0
		END
	WHERE [Used] IS NULL;
GO
UPDATE [Permissions] SET
	
	[Name] = 'WhatsApp HSM - Envios', [Description] = 'Permiso de tareas HSM'

	WHERE [PermissionID] = 36;
GO

IF ((SELECT [Value] FROM SystemSettings WHERE [Key] = 'Cases.AllowAgentsToMarkCasesAsPending') = 'True')
BEGIN
	UPDATE dbo.Agents
	SET AllowedToMarkAsPending = 1,  UseMyCases = 1
	UPDATE dbo.AgentGroups
	SET AllowedToMarkAsPending = 1,  UseMyCases = 1, OtherPermissionsConfigured = 1
	UPDATE SystemSettings
	SET Value = 'False'
	WHERE [Key] = 'Cases.AllowAgentsToMarkCasesAsPending'
END
GO

IF (ISNULL((SELECT [Value] FROM [SystemStatus] WHERE [Key] = 'UpdateSecondSLQueues'), 'False') != 'True')
BEGIN
    update Queues 
    SET Parameters = LEFT(parameters, CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) + 35) + '0,"Seconds":' + (CONVERT(nvarchar(10),SUBSTRING(parameters, CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) + 36, CHARINDEX(',', parameters, 27) - CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) - 36) * 60)) + ','+
                    RIGHT(parameters, LEN(parameters) - CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) - 35 - LEN(CONVERT(nvarchar(10),SUBSTRING(parameters, CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) + 36, CHARINDEX(',', parameters, 27) - CHARINDEX('{"ServiceLevel":{"Type":1,"Minutes":', parameters) - 36))))                
    WHERE Parameters LIKE '%{"ServiceLevel":{"Type":1,"Minutes":%'
    AND Parameters NOT LIKE '%"Seconds":%'

    INSERT INTO [SystemStatus]
    VALUES ('UpdateSecondSLQueues','True','System.Boolean')
END

IF (ISNULL((SELECT [Value] FROM [SystemStatus] WHERE [Key] = 'UpdateWhatsAppReportPermission'), 'False') != 'True')
BEGIN
	INSERT INTO [UsersPermissions] (UserID, PermissionID)
		SELECT [UserID], 48
			FROM [UsersPermissions]
			WHERE [PermissionID] = 36;

	INSERT INTO [SystemStatus]
		VALUES ('UpdateWhatsAppReportPermission','True','System.Boolean');
END
GO

IF NOT EXISTS (SELECT 1 FROM [SystemStatus] WHERE [Key] = 'AllowContingencyBot')
BEGIN
	INSERT INTO [SystemStatus] ([Key], [Value], [Type])
		VALUES ('AllowContingencyBot', 'False', 'System.Boolean');
END
GO

UPDATE [Services] SET
	[Settings] = REPLACE([Settings], '"Permissions":["manage_pages","pages_messaging","pages_manage_metadata","pages_show_list","publish_pages"]', '"Permissions":["pages_messaging","pages_manage_metadata","pages_show_list"]')
	WHERE [ServiceTypeID] = 14;
GO

DECLARE @OEM NVARCHAR(100);
SET @OEM = 'Default';
--SET @OEM = 'OmniCX';

-- Dejar descomentado lo siguiente para cuando NO es CCaaS
DECLARE @WorkAsGateway BIT = 0;
DECLARE @GatewayType NVARCHAR(100) = NULL;
-- Dejar descomentado lo siguiente para cuando es CCaaS
--DECLARE @WorkAsGateway BIT = 1;
--DECLARE @GatewayType NVARCHAR(100) = N'CCaaS'; 

IF @OEM = 'OmniCX'
BEGIN
	INSERT INTO [dbo].[AuxReasons] ([AuxReasonID], [Name], [Enabled]) VALUES (1, N'DND', 1);
	INSERT INTO [dbo].[AuxReasons] ([AuxReasonID], [Name], [Enabled]) VALUES (2, N'Saliente', 1);

	SET IDENTITY_INSERT [dbo].[ExternalIntegrations] ON;
	INSERT INTO [dbo].[ExternalIntegrations] ([ExternalIntegrationID], [Name], [AccessToken], [AccessTokenSecret], [Enabled]) VALUES (1, N'tmAdmin', N'xdzb7GcvMgI3nsRfT9QcNRvVXDhF58BF59OChbOBWw8=', N'Cj0JLqa3zxBVH8zOo0UzKQ==', 1);
	SET IDENTITY_INSERT [dbo].[ExternalIntegrations] OFF;

	INSERT INTO [dbo].[SystemSettings] ([Key], [Type], [Value]) VALUES (N'AuxReasonForOutgoingMessages', N'System.Int16', N'2');
END;

IF @WorkAsGateway = 1
BEGIN
	IF @GatewayType = 'CCaaS'
	BEGIN
		IF NOT EXISTS(SELECT 1 FROM [ExternalIntegrations] WHERE [Name] = N'CCaaS Agent')
		BEGIN
			INSERT [dbo].[ExternalIntegrations] ([Name], [AccessToken], [AccessTokenSecret], [Enabled], [JWTEnabled], [JWTIssuedAt], [JWT]) VALUES (N'CCaaS Agent', N'LKBMGxpWNMCAeUSwKKiD9xbi7OlPhzMdJD6ZFjRtAEY=', N'xern6PLjdk1vdVAMY7oiyA==', 1, 1, GETDATE(), N'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3UiOjE2NTI3MjYyMzksImVpIjoxfQ.B1HibGm7H8YXUVlPb8jHYew7wPaoCK1wOCbF829uO0o')
		END;

		IF NOT EXISTS(SELECT 1 FROM [ExternalIntegrationsSecurity] WHERE [Origin] = N'https://*.workspaces.avayacloud.com')
		BEGIN
			INSERT [dbo].[ExternalIntegrationsSecurity] ([Origin], [Methods], [Headers], [Enabled]) VALUES (N'https://*.workspaces.avayacloud.com', NULL, NULL, 1);
		END;
	END;
END;
GO

UPDATE [SystemSettings] SET
	[Value] = 'https://callback.ysocial.net' + SUBSTRING([Value], 35, LEN([Value]) - 34)
	WHERE [Value] like 'https://fbsocial.azurewebsites.net%';

UPDATE [SystemSettings] SET 
	[Value] = 'https://callback.ysocial.net/api/apple' 
	WHERE 
		[Key] = 'AppleMessaging.UrlRtNotifications' 
		AND [Value] = 'https://callback.ysocial.net/api/applemessaging';

/*
Actualizamos los mensajes de la tabla MessagesPayment para agregarle a cada mensaje qué template
fue el envíado. Para poder hacer búsquedas
*/
IF (ISNULL((SELECT [Value] FROM [SystemStatus] WHERE [Key] = 'UpdatedWhatsAppPaymentMessages'), 'False') <> 'True')
BEGIN
	DECLARE @MessageID BIGINT, @Parameters NVARCHAR(MAX);

	DECLARE curMessages CURSOR FOR 
	SELECT [MessagesPayment].[MessageID], [Messages].[Parameters]
		FROM [MessagesPayment] WITH (NOLOCK)
			INNER JOIN [Messages] WITH (NOLOCK)
				ON [MessagesPayment].[MessageID] = [Messages].[MessageID]
		WHERE [MessagesPayment].[Template] IS NULL;

	OPEN curMessages;

	FETCH NEXT FROM curMessages
		INTO @MessageID, @Parameters;

	WHILE @@FETCH_STATUS = 0
	BEGIN
		DECLARE @Namespace VARCHAR(100);
		DECLARE @ElementName VARCHAR(100);
		DECLARE @Language VARCHAR(100);
		DECLARE @Template VARCHAR(300);

		SET @Namespace = [dbo].[fGetJsonStringValue]('TemplateNamespace', @Parameters);
		SET @ElementName = [dbo].[fGetJsonStringValue]('TemplateName', @Parameters);
		SET @Language = [dbo].[fGetJsonStringValue]('Language', @Parameters);

		SET @Template = LOWER(@Namespace) + '##' + LOWER(@ElementName) + '##' + LOWER(@Language);

		UPDATE [MessagesPayment] SET
			[Template] = @Template
			WHERE [MessageID] = @MessageID;

		FETCH NEXT FROM curMessages
			INTO @MessageID, @Parameters;
	END;
			
	CLOSE curMessages;
	DEALLOCATE curMessages;

	INSERT INTO [SystemStatus]
		VALUES ('UpdatedWhatsAppPaymentMessages','True','System.Boolean');
END;
/*
Actualizamos los casos abiertos para convertirlos en casos por servicios
*/
IF (ISNULL((SELECT [Value] FROM [SystemStatus] WHERE [Key] = 'UpdatedCasesToCasesByService'), 'False') <> 'True')
BEGIN
	DECLARE @CaseID BIGINT
		, @ServiceID INT;

	DECLARE curCases CURSOR FOR 
	SELECT [CaseID]
		FROM [Cases] WITH (NOLOCK)
		WHERE 
			[CaseStatusID] = 1
			AND [Version] = 2;

	OPEN curCases;

	FETCH NEXT FROM curCases
		INTO @CaseID;

	WHILE @@FETCH_STATUS = 0
	BEGIN
		PRINT 'Procesando caso ' + LTRIM(STR(@CaseID)) + ' que está abierto para convertirlo a caso por servicio';
		
		SELECT TOP 1
			@MessageID = [MessageID]
			FROM [CasesMessages] WITH (NOLOCK)
			WHERE [CaseID] = @CaseID
			ORDER BY [Index] DESC;

		SELECT 
			@ServiceID = [ServiceID]
			FROM [Messages] WITH (NOLOCK)
			WHERE [MessageID] = @MessageID;

		UPDATE [Cases] SET
			[ServiceID] = @ServiceID
			, [Version] = 3
			WHERE [CaseID] = @CaseID;

		FETCH NEXT FROM curCases
			INTO @CaseID;
	END;
			
	CLOSE curCases;
	DEALLOCATE curCases;

	INSERT INTO [SystemStatus]
		VALUES ('UpdatedCasesToCasesByService','True','System.Boolean');
END;

IF NOT EXISTS(SELECT 1 FROM [SystemStatus] WHERE [Key] = 'ChatSearchJoinWithCaseTable')
BEGIN
	insert into systemStatus values ('ChatSearchJoinWithCaseTable', 'False', 'System.Boolean');
END;
GO

IF NOT EXISTS(SELECT 1 FROM [SystemStatus] WHERE [Key] = 'ChatMessagesSearchJoinWithCaseTable')
BEGIN
	insert into systemStatus values ('ChatMessagesSearchJoinWithCaseTable', 'False', 'System.Boolean');
END;
GO

UPDATE [SystemSettings] SET [Type] = N'System.Byte' WHERE [Key] IN ('Cases.TagCasesOnStart', 'Cases.TagCasesOnClose', 'Cases.TagCasesOnDiscard');
GO

UPDATE [SystemSettings] SET [Value] = N'0' WHERE [Key] IN ('Cases.TagCasesOnStart', 'Cases.TagCasesOnClose', 'Cases.TagCasesOnDiscard') AND [Value] = 'False';
GO

UPDATE [SystemSettings] SET [Value] = N'1' WHERE [Key] IN ('Cases.TagCasesOnStart', 'Cases.TagCasesOnClose', 'Cases.TagCasesOnDiscard') AND [Value] = 'True';
GO

UPDATE [Services] SET
	[CasesSettings] = REPLACE(
		REPLACE(
			REPLACE(
				REPLACE(
					REPLACE(
						REPLACE([CasesSettings], 
							'"TagCasesOnStart":true', '"TagCasesOnStart":1'),
						'"TagCasesOnStart":false', '"TagCasesOnStart":0'),
					'"TagCasesOnDiscard":false', '"TagCasesOnDiscard":0'),
				'"TagCasesOnDiscard":true', '"TagCasesOnDiscard":1'),
			'"TagCasesOnClose":false', '"TagCasesOnClose":0'),
		'"TagCasesOnClose":true', '"TagCasesOnClose":1')
	WHERE [CasesSettings] IS NOT NULL;
GO

UPDATE [Tasks] SET
	[Parameters] = REPLACE([Parameters], N'Languaje', N'TemplateLanguage');
GO

UPDATE [Services] SET
	[Configuration] = REPLACE(
						REPLACE([Configuration], 
							'"TagOnClose":true', '"TagOnClose":1'),
						'"TagOnClose":false', '"TagOnClose":0')
	WHERE [Configuration] IS NOT NULL;
GO

IF NOT EXISTS(SELECT 1 FROM [SocialUserPreferenceTypes] WHERE [SocialUserPreferenceTypeID] = 1)
BEGIN
	INSERT INTO [SocialUserPreferenceTypes] ([SocialUserPreferenceTypeID], [Name]) VALUES (1 , 'MarketingMessages')
END;
GO

IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 53)
BEGIN
	INSERT INTO [Permissions] ([PermissionID], [Name], [Description]) VALUES (53 , 'Mover mensajes entre colas' ,'Permite mover mensajes entre las direfentes colas')
END;
GO

INSERT INTO [UsersPermissions] (UserID, PermissionID)
	SELECT [UsersProfiles].UserID, 53
		FROM [UsersProfiles]
			WHERE [UsersProfiles].ProfileID = 2
			AND NOT EXISTS (
				SELECT 1 
					FROM [UsersPermissions]
						WHERE [UsersPermissions].UserID = [UsersProfiles].UserID AND [UsersPermissions].PermissionID = 53
);
GO

IF NOT EXISTS(SELECT 1 FROM [Permissions] WHERE [PermissionID] = 54)
BEGIN
	INSERT INTO [Permissions] ([PermissionID], [Name], [Description]) VALUES (54 , 'Acceso a yUsage' ,'Permite ver el link de acceso a yUsage')
END;
GO