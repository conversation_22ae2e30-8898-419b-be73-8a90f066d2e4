﻿<%@ Page Async="true" Title="" Language="C#" MasterPageFile="~/Master.Master" ValidateRequest="false" AutoEventWireup="true" CodeBehind="ServicesWhatsapp.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.ServicesWhatsapp" %>

<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.colorpicker.css")%>' rel="stylesheet" type="text/css" />
    <link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/datatables.min.css")%>' rel="stylesheet" type="text/css" />

    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/DatePicker.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/datatables.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.numeric.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/URI.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.getUrlParam.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.filedrop.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesCommon.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/ServicesWhatsapp.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
    <script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/dragula.min.js")%>'></script>
    <style type="text/css">
        .uiInfoTable .label {
            width: 150px !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
    <asp:Image runat="server" ImageUrl="~/Images/Whatsapp.png" ImageAlign="AbsMiddle" />
    <span data-i18n="configuration-serviceswhatsapp-title">WhatsApp</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
    <div style="display: none">
        <div id="divServices" class="seccion">
            <div class="title">
                <h2 data-i18n="configuration-serviceswhatsapp-copy_attributes">Copiar atributos</h2>
            </div>
            <div class="contents">
                <yoizen:Message runat="server" Type="Information" Text="Seleccione el servicio de Whatsapp del cual se desea copiar los atributos" Small="true" LocalizationKey="configuration-serviceswhatsapp-select_service" />
                <div id="divAllServices" style="max-height: 300px; overflow-y: auto; overflow-x: auto; max-width: 100%">
                    <table id="tableAllServices" class="reporte" cellspacing="0" rules="all" border="1" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr class="header">
                                <th style="width: 20px;" scope="col">&nbsp;</th>
                                <th scope="col"><span data-i18n="configuration-services-service">Servicio</span></th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="buttons">
                    <label class="uiButton uiButtonLarge uiButtonConfirm">
                        <button type="button" data-i18n="globals-accept" id="buttonCopyAnswerDialogConfirm" onclick="CopyDialogConfirmCommon()">Aceptar</button>
                    </label>
                    <label class="uiButton uiButtonLarge">
                        <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                    </label>
                </div>
            </div>
        </div>
        <div id="divHSM">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-message_template-title">Plantillas de Mensajes (HSM)</h2>
                </div>
                <div class="contents">
                    <div class="scrollable-y" style="max-height: 350px">
                        <div class="filter-table">
                            <div class="row">
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-description">Descripción</span>:
											<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
												"toggle": "popover",
												"html": true,
												"maxWidth": "400px",
												"trigger": "hover",
												"title": "configuration-serviceswhatsapp-template-description",
												"content": "configuration-serviceswhatsapp-edit_message_template-field-clean_name"
												}'
                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Descripción" data-content="Indica un nombre amigable para presentar en las pantallas"></span>
                                        </div>
                                        <input id="inputHSMDescription" type="text" class="inputtext" />
                                    </div>
                                </div>
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-lenguage">Lenguaje</span>:
											<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
												"toggle": "popover",
												"html": true,
												"maxWidth": "400px",
												"trigger": "hover",
												"title": "configuration-serviceswhatsapp-template-lenguage",
												"content": "configuration-serviceswhatsapp-edit_message_template-field-language"
												}'
                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Lenguaje" data-content="Indica el lenguaje utilizado para la plantilla que se definió en el administrador comercial de whatsapp"></span>
                                        </div>
                                        <select id="selectHSMLanguage">
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-es" value="es">Español</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-es_AR" value="es_AR">Español (Argentina)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-es_MX" value="es_MX">Español (México)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-es_ES" value="es_ES">Español (España)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-en" value="en">Inglés</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-en_GB" value="en_GB">Inglés (UK)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-en_US" value="en_US">Inglés (Estados Unidos)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-pt_BR" value="pt_BR">Portugués (Brasil)</option>
                                            <option data-i18n="configuration-serviceswhatsapp-template-lenguage-pt_PT" value="pt_PT">Portugués (Portugal)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-namespace">Espacio de nombres</span> (<span class="mono">namespace</span>):
											<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
												"toggle": "popover",
												"html": true,
												"maxWidth": "400px",
												"trigger": "hover",
												"title": "configuration-serviceswhatsapp-template-namespace",
												"content": "configuration-serviceswhatsapp-edit_message_template-field-namespace"
												}'
                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Espacio de nombres" data-content="Indica el espacio de nombres (<span class='mono'>namespace</span>) que se definió en el administrador comercial de whatsapp"></span>
                                        </div>
                                        <input id="inputHSMNamespace" type="text" class="inputtext" />
                                    </div>
                                </div>
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-element_name">Nombre de elemento</span> (<span class="mono">element_name</span>):
											<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
												"toggle": "popover",
												"html": true,
												"maxWidth": "400px",
												"trigger": "hover",
												"title": "configuration-serviceswhatsapp-template-element_name",
												"content": "configuration-serviceswhatsapp-edit_message_template-field-element_name"
												}'
                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Nombre de elemento" data-content="Indica el nombre de elemento (<span class='mono'>element_name</span>) que se definió en el administrador comercial de whatsapp"></span>
                                        </div>
                                        <input id="inputHSMElementName" type="text" class="inputtext" />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-allowed_uses">Disponible para usar en</span>:
                                        </div>
                                        <select id="selectHSMAllowedUses" multiple="multiple">
                                            <option value="1" data-i18n="configuration-serviceswhatsapp-template-avail_for_agents">Agentes</option>
                                            <option value="2" data-i18n="configuration-serviceswhatsapp-template-avail_for_supervisors">Supervisores</option>
                                            <option value="3" data-i18n="configuration-serviceswhatsapp-template-avail_for_integrations">Integraciones</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="cell cell-6">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-send_hsm_with_case_open">Permitir envío de plantilla con caso Abierto</span>:
                                        </div>
                                        <select id="selectAllowToConfigureSendHSMIfCaseOpen">
                                            <option value="1" data-i18n="globals-yes">Sí</option>
                                            <option value="0" data-i18n="globals-no">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="subseccion">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-template-header">Encabezado</h2>
                            </div>
                            <div class="contents">
                                <div class="filter-table">
                                    <div class="row">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-type">Tipo de encabezado</span>:
                                                </div>
                                                <select id="selectHSMHeaderType">
                                                    <option value="0" data-i18n="configuration-serviceswhatsapp-template-header-type-none">Ninguno</option>
                                                    <option value="1" data-i18n="configuration-serviceswhatsapp-template-header-type-text">Texto</option>
                                                    <option value="2" data-i18n="configuration-serviceswhatsapp-template-header-type-media">Multimedia</option>
                                                    <option value="3" data-i18n="configuration-serviceswhatsapp-template-header-type-location">Ubicación</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="divHSMHeaderTypeText" style="display: none">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-text">Texto del encabezado</span>:
													<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
														"toggle": "popover",
														"html": true,
														"maxWidth": "400px",
														"trigger": "hover",
														"title": "configuration-serviceswhatsapp-template-header-text",
														"content": "configuration-serviceswhatsapp-template-header-text-tip"
														}'
                                                        data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Texto del encabezado" data-content="Indica el texto del mensaje tal cual se definió en el administrador comercial de whatsapp"></span>
                                                </div>
                                                <input id="inputHSMHeaderTypeText" type="text" class="inputtext" maxlength="60" />
                                            </div>
                                        </div>
                                        <div class="cell cell-3" id="divHSMHeaderTypeTextParameterName" style="display: none">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-text-parameter-name">Nombre del parámetro</span>:
                                                </div>
                                                <input id="inputHSMHeaderTypeTextParameterName" type="text" class="inputtext" maxlength="60" readonly="readonly" />
                                            </div>
                                        </div>
                                        <div class="cell cell-3" id="divHSMHeaderTypeTextParameterDescription" style="display: none">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-text-parameter-desc">Descripción del parámetro</span>:
                                                </div>
                                                <input id="inputHSMHeaderTypeTextParameterDescription" type="text" class="inputtext" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="divHSMHeaderTypeLocation" style="display: none">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-location-latitude">Latitud</span>:
                                                </div>
                                                <input type="number" id="inputHSMHeaderTypeLatitude" class="inputtext" min="-90" max="90" step="1" />
                                            </div>
                                        </div>
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-location-longitude">Longitud</span>:
                                                </div>
                                                <input type="number" id="inputHSMHeaderTypeLongitude" class="inputtext" min="-180" max="180" step="1" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="divHSMHeaderTypeMedia" style="display: none">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-media-type">Tipo de multimedia</span>:
                                                </div>
                                                <select id="selectHSMHeaderTypeMedia">
                                                    <option value="1" data-i18n="configuration-serviceswhatsapp-template-header-media-type-document">Documento</option>
                                                    <option value="2" data-i18n="configuration-serviceswhatsapp-template-header-media-type-image">Imágen</option>
                                                    <option value="3" data-i18n="configuration-serviceswhatsapp-template-header-media-type-video">Video</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-header-media-url">Url por defecto (opcional)</span>:
                                                </div>
                                                <input id="inputHSMHeaderTypeMediaUrl" type="text" class="inputtext" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="subseccion">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-template-body">Contenido</h2>
                            </div>
                            <div class="contents">
                                <div class="filter-table">
                                    <div class="row">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-template">Plantilla</span>:
													<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
														"toggle": "popover",
														"html": true,
														"maxWidth": "400px",
														"trigger": "hover",
														"title": "configuration-serviceswhatsapp-template-template",
														"content": "configuration-serviceswhatsapp-edit_message_template-field-text-v2"
														}'
                                                        data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Plantilla" data-content="Indica el texto del mensaje tal cual se definió en el administrador comercial de whatsapp"></span>
                                                </div>
                                                <textarea id="textareaHSMTemplate" class="inputtext" spellcheck="false" style="width: 100%; min-height: 80px; max-height: 200px; box-sizing: border-box"></textarea>
                                            </div>
                                        </div>
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-parameters">Parámetros</span>:
													<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
														"toggle": "popover",
														"html": true,
														"maxWidth": "400px",
														"trigger": "hover",
														"title": "configuration-serviceswhatsapp-template-parameters",
														"content": "configuration-serviceswhatsapp-edit_message_template-field-parameters_clean_name"
														}'
                                                        data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Parámetros" data-content="Indica un nombre amigable para cada parámetro que se definió dentro de la estructura del mensaje. Se debe ingresar un parámetro por línea"></span>
                                                </div>
                                                <div id="divHSMParametersContainer"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="subseccion">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-template-footer">Pie</h2>
                            </div>
                            <div class="contents">
                                <div class="filter-table">
                                    <div class="row">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-footer-type">Tipo de pie</span>:
                                                </div>
                                                <select id="selectHSMFooterType">
                                                    <option value="0" data-i18n="configuration-serviceswhatsapp-template-footer-type-none">Ninguno</option>
                                                    <option value="1" data-i18n="configuration-serviceswhatsapp-template-footer-type-text">Texto</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="cell cell-6" id="divHSMFooterTypeText" style="display: none">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-footer-text">Texto del pie</span>:
                                                </div>
                                                <input id="inputHSMFooterTypeText" type="text" class="inputtext" maxlength="60" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="subseccion">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-template-buttons">Botones</h2>
                            </div>
                            <div class="contents">
                                <div class="filter-table">
                                    <div class="row">
                                        <div class="cell cell-6">
                                            <div class="group">
                                                <div class="group-title">
                                                    <span data-i18n="configuration-serviceswhatsapp-template-buttons-type">Tipo de botones</span>:
                                                </div>
                                                <select id="selectHSMButtonsType">
                                                    <option value="0" data-i18n="configuration-serviceswhatsapp-template-buttons-type-none">Ninguno</option>
                                                    <option value="1" data-i18n="configuration-serviceswhatsapp-template-buttons-type-quick_reply">Respuesta rápida</option>
                                                    <option value="2" data-i18n="configuration-serviceswhatsapp-template-buttons-type-call_to_action">Llamada a una acción</option>
                                                    <option value="3" class="mixed" data-i18n="configuration-serviceswhatsapp-template-buttons-type-mixed">Mixto</option>
                                                    <option value="4" data-i18n="configuration-serviceswhatsapp-template-buttons-type-authcode">Autenticacion</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="divHSMButtonsTypeCallToAction" class="subsubseccion" style="display: none">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-template-buttons-type-call_to_action-title">Definición de Botones de llamada a una acción</h2>
                                    </div>
                                    <div class="contents">
                                        <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-template-buttons-type-call_to_action-info">
											Puede definir hasta 2 botones de llamada a una acción
                                        </yoizen:Message>
                                        <div id="divHSMButtonsTypeCallToActionContainer"></div>
                                    </div>
                                </div>
                                <div id="divHSMButtonsTypeQuickReply" class="subsubseccion" style="display: none">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-template-buttons-type-quick_reply-title">Definición de Botones de Respuesta rápida</h2>
                                    </div>
                                    <div class="contents">
                                        <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-template-buttons-type-quick_reply-info">
											Puede definir hasta 10 botones de respuesta rápida
                                        </yoizen:Message>
                                        <div id="divHSMButtonsTypeQuickReplyContainer"></div>
                                    </div>
                                </div>
                                <div id="divHSMButtonsTypeMixed" class="subsubseccion" style="display: none">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-template-buttons-type-mixed-title">Definición de Botones de Respuesta rápida</h2>
                                    </div>
                                    <div class="contents">
                                        <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-template-buttons-type-mixed-info">
											Puede definir un máximo de 10 botones, con un límite de 4 llamados a la acción, distribuidos en 2 de tipo URL, 1 de código de oferta y 1 de tipo llamada.
                                        </yoizen:Message>
                                        <div id="divHSMButtonsTypeMixedContainer"></div>
                                    </div>
                                </div>
                                <div id="divHSMButtonsTypeAuthCode" class="subsubseccion" style="display: none">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-template-buttons-type-auth_code-title">Definición de Botones de Autenticacion</h2>
                                    </div>
                                    <div class="contents">
                                        <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-template-buttons-type-auth_code-info">
									Puede definir hasta 10 botones de autenticacion
                                        </yoizen:Message>
                                        <div id="divHSMButtonsTypeAuthCodeContainer"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="divHSMError" class="validationerror" style="display: none"><span></span></div>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" onclick="SaveHSM()" data-i18n="globals-accept">Aceptar</button>
                        </label>
                        <label class="uiButton uiButtonLarge">
                            <button type="button" onclick="$.colorbox.close()" data-i18n="globals-cancel">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divImportHSMsWithoutNamespace">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-message_template-title">Plantillas de Mensajes (HSM)</h2>
                </div>
                <div class="contents">
                    <div class="scrollable-y" style="max-height: 350px">
                        <div class="filter-table">
                            <div class="row">
                                <div class="cell">
                                    <div class="group">
                                        <div class="group-title">
                                            <span data-i18n="configuration-serviceswhatsapp-template-namespace">Espacio de nombres</span> (<span class="mono">namespace</span>):
											<span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
												"toggle": "popover",
												"html": true,
												"maxWidth": "400px",
												"trigger": "hover",
												"title": "configuration-serviceswhatsapp-template-namespace",
												"content": "configuration-serviceswhatsapp-edit_message_template-field-namespace"
												}'
                                                data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Espacio de nombres" data-content="Indica el espacio de nombres (<span class='mono'>namespace</span>) que se definió en el administrador comercial de whatsapp"></span>
                                        </div>
                                        <input id="inputImportHSMWihtoutNamespaceNamespace" type="text" class="inputtext" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="divImportHSMWihtoutNamespaceError" class="validationerror" style="display: none"><span data-i18n="configuration-serviceswhatsapp-whatsapp11-error"></span></div>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" id="buttonImportHSMWihtoutNamespaceAccept" data-i18n="globals-accept">Aceptar</button>
                        </label>
                        <label class="uiButton uiButtonLarge">
                            <button type="button" onclick="$.colorbox.close()" data-i18n="globals-cancel">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divFacebookCatalogs">
            <div class="seccion">
                <div class="title">
                    <h2><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-title">Asistente de configuración de Catálogos de Facebook</span></h2>
                </div>
                <div class="contents">
                    <div class="facebook-login-button">
                        <div>
                            <div>
                                <div style="display: inline-block">
                                    <div class="facebook-login-button-container" role="button">
                                        <table class="" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <div>
                                                            <span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 216 216" color="#FFFFFF">
                                                                    <path fill="#FFFFFF" d="M204.1 0H11.9C5.3 0 0 5.3 0 11.9v192.2c0 6.6 5.3 11.9 11.9
																		11.9h103.5v-83.6H87.2V99.8h28.1v-24c0-27.9 17-43.1 41.9-43.1
																		11.9 0 22.2.9 25.2 1.3v29.2h-17.3c-13.5 0-16.2 6.4-16.2
																		15.9v20.8h32.3l-4.2 32.6h-28V216h55c6.6 0 11.9-5.3
																		11.9-11.9V11.9C216 5.3 210.7 0 204.1 0z">
                                                                    </path>
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text"><span data-i18n="configuration-servicesinstagram-log_in_facebook">Iniciar sesión con Facebook</span></div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="divFacebookUser" class="subseccion" style="display: none; margin-top: 20px;">
                        <div class="title">
                            <h2 data-i18n="configuration-servicesfacebook-user_loged">Usuario de Facebook logueado</h2>
                        </div>
                        <div class="contents">
                            <div class="facebook-user" id="divFacebookUserInfo">
                                <div class="facebook-user-avatar">
                                    <img />
                                </div>
                                <div class="facebook-user-name"></div>
                            </div>
                            <div id="divFacebookCatalogsSelectBusiness">
                                <div id="divFacebookBusiness" class="subsubseccion" style="display: none; margin-top: 10px;">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-select_business">Seleccione el negocio</h2>
                                    </div>
                                    <div class="contents">
                                        <div id="divFacebookBusinesses" class="facebook-pages">
                                        </div>
                                        <div class="buttons" id="divButtonsLoadMoreBusinesses">
                                            <label class="uiButton">
                                                <button type="button" data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-load_more_businesses" id="buttonLoadMoreBusinesses">Obtener más negocios</button>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <yoizen:Message ID="messageFacebookNoBusinesses" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-no_businesses-error">
									El usuario no pertenece a ningún negocio
                                </yoizen:Message>
                            </div>
                            <div id="divFacebookCatalogsSelectCatalog" style="display: none">
                                <div id="divFacebookBusinessCatalogs" class="subsubseccion" style="display: none; margin-top: 10px;">
                                    <div class="title">
                                        <h2 data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-business_info">Información de negocio</h2>
                                    </div>
                                    <div class="contents">
                                        <table width="100%" border="0" class="uiInfoTable noBorder">
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-business">Negocio</span>:</th>
                                                <td class="data">
                                                    <div class="facebook-user" id="divFacebookBusinessInfo">
                                                        <div class="facebook-user-avatar">
                                                            <img />
                                                        </div>
                                                        <div class="facebook-user-name"></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog">Catálogo</span>:</th>
                                                <td class="data">
                                                    <select id="selectFacebookBusinessCatalog"></select>
                                                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog-info" Style="margin-top: 5px">
														Se debe seleccionar el mismo catálogo que se seleccionó en el administrador comercial de Whatsapp
                                                    </yoizen:Message>
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog-products">Productos</span>:</th>
                                                <td class="data">
                                                    <div class="facebook-catalog-products-container">
                                                        <div id="divFacebookBusinessCatalogProducts" class="facebook-catalog-products"></div>
                                                    </div>
                                                    <yoizen:Message ID="messageFacebookBusinessCatalogNoProducts" runat="server" ClientIDMode="Static" Type="Warning" Style="display: none; margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog_no_products-error">
														El catálogo seleccionado no tiene ningún producto
                                                    </yoizen:Message>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <yoizen:Message ID="messageFacebookNoBusinessCatalogs" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-no_business_catalogs-error">
									El negocio seleccinado no tiene ningún catálogo
                                </yoizen:Message>
                            </div>
                        </div>
                    </div>
                    <div id="divFacebookRedirectUri" class="subseccion" style="display: none; margin-top: 20px;">
                        <div class="title">
                            <h2 data-i18n="configuration-servicesfacebook-facebook_auth_url">URL de autorización de Facebook</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-servicesfacebook-facebook_auth_url">URL del autorización de Facebook</span>:</th>
                                    <td class="data">
                                        <input id="inputFacebookUrl" type="text" style="width: 100%" autocomplete="off" class="inputtext" />
                                    </td>
                                </tr>
                            </table>
                            <div class="buttons">
                                <label class="uiButton">
                                    <button type="button" data-i18n="configuration-servicesfacebook-revalidate_facebook_url" onclick="RevalidateFacebookUrl()">Revalidar URL</button>
                                </label>
                            </div>
                        </div>
                    </div>
                    <yoizen:Message ID="messageFacebookUrlInvalid" runat="server" ClientIDMode="Static" Type="Error" Style="display: none" />
                    <yoizen:Message ID="messageFacebookAccountError" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px;" LocalizationKey="configuration-servicesfacebook-account_error">
						No se pudo obtener información de la cuenta de facebook
                    </yoizen:Message>
                    <yoizen:Message ID="messageFacebookCouldntValidateAccessToken" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px;" LocalizationKey="configuration-servicesfacebook-acces_token_error">
						No se pudo validar el access token
                    </yoizen:Message>
                    <yoizen:Message ID="messageMustSelectBusiness" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px;" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-must_select_business-error">
						Debe seleccionar un negocio
                    </yoizen:Message>
                    <yoizen:Message ID="messageMustSelectCatalog" runat="server" ClientIDMode="Static" Type="Error" Style="display: none; margin-top: 10px;" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-must_select_catalog-error">
						Debe seleccionar un catálogo que tengan productos
                    </yoizen:Message>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge" style="float: left" id="buttonEnterFacebookUrl">
                            <button type="button" data-i18n="configuration-servicesfacebook-enter_facebook_url" onclick="ShowFacebookUrlInput()">Ingresar URL de Facebook a mano</button>
                        </label>
                        <label class="uiButton uiButtonLarge" id="buttonChangeFacebookBusiness" style="display: none">
                            <button type="button" data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-change_business" onclick="ChangeFacebookBusiness()">Cambiar de negocio</button>
                        </label>
                        <label class="uiButton uiButtonLarge uiButtonConfirm" id="buttonFacebookBusinessCatalogAccept" style="display: none">
                            <button type="button" data-i18n="globals-accept" onclick="AcceptFacebookBusinessDialog()">Aceptar</button>
                        </label>
                        <label class="uiButton uiButtonLarge">
                            <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divCloudApiTokenAndPhoneNumbers">
            <div class="seccion">
                <div class="title">
                    <h2><span data-i18n="configuration-serviceswhatsapp-whatsapp11-wizard-title">Líneas de Whatsapp</span></h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceswhatsapp-whatsapp11-wizard-access_token_valid"></yoizen:Message>
                    <div id="divIntegrationType11PhoneNumbers" class="facebook-pages"></div>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge">
                            <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="divInteractiveMessage">
            <div class="seccion">
                <div class="title">
                    <h2><span data-i18n="configuration-serviceswhatsapp-interactive-message-title">Mensaje interactivo</span></h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 250px !important"><span data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-interactive_message-header">Encabezado</span>:</th>
                            <td class="data">
                                <input id="inputInteractiveMessageHeader" style="width: 50%" autotrim="true" maxlength="60" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 250px !important"><span data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-interactive_message-body">Cuerpo</span>:</th>
                            <td class="data">
                                <textarea id="textareaInteractiveMessageBody" rows="3" style="resize: none; width: 50%" autotrim="true" maxlength="1024"></textarea>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 250px !important"><span data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-interactive_message-footer">Pie de página</span>:</th>
                            <td class="data">
                                <input id="inputInteractiveMessageFooter" style="width: 50%" autotrim="true" maxlength="60" />
                            </td>
                        </tr>
                    </table>
                    <div class="buttons">
                        <label class="uiButton uiButtonLarge uiButtonConfirm">
                            <button type="button" data-i18n="globals-accept" onclick="AcceptInteractiveMessageDialog()">Aceptar</button>
                        </label>
                        <label class="uiButton uiButtonLarge">
                            <button type="button" data-i18n="globals-cancel" onclick="$.colorbox.close()">Cancelar</button>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <asp:HiddenField ID="hiddenTab" runat="server" ClientIDMode="Static"></asp:HiddenField>
    <div id="tabsWhatsapp">
        <ul>
            <li>
                <a href="#divBasicConfiguration" data-i18n="configuration-serviceswhatsapp-settings">Configuración básica</a>
            </li>
            <li id="liTabAdvancedConfigurationYFlow">
                <a href="#divAdvancedConfigurationYFlow">Configuración avanzada <span class="yzn-yFlowISO"></span></a>
            </li>
            <li class="hiddenAsGateway">
                <a href="#divWhatsappServiceMedia" data-i18n="configuration-serviceswhatsapp-attached_files">Archivos adjuntos</a>
            </li>
            <li id="liTabBehaviour">
                <a href="#divWhatsappBehaviour" data-i18n="configuration-serviceswhatsapp-behavior">Comportamiento</a>
            </li>
            <li id="liTabCases">
                <a href="#divCases" data-i18n="configuration-systemsettings-cases">Casos</a>
            </li>
            <li>
                <a href="#divNotifications" data-i18n="configuration-serviceswhatsapp-email_notifications">Notificaciones por email</a>
            </li>
            <li id="liTabVideo" runat="server" visible="false">
                <a href="#divVideo" data-i18n="configuration-serviceswhatsapp-video">Video</a>
            </li>
            <li id="liTabVoiceCalls" runat="server" visible="false">
                <a href="#divVoiceCalls" data-i18n="configuration-serviceswhatsapp-voice_calls">Llamadas</a>
            </li>
        </ul>
        <div id="divBasicConfiguration">
            <table width="100%" border="0" class="uiInfoTable noBorder">
                <tr class="dataRow dataRowSeparator">
                    <th class="label" style="width: 250px !important"><span data-i18n="configuration-serviceswhatsapp-service_name">Nombre de servicio</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxServiceName" runat="server" Width="400" ClientIDMode="Static" autocomplete="off" MaxLength="50" autotrim="true" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxServiceName" />
                        <asp:CustomValidator ID="customvalidatorServiceName" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateServiceName" OnServerValidate="customvalidatorServiceName_ServerValidate" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-phone_country">País del teléfono</span>:</th>
                    <td class="data">
                        <asp:DropDownList ID="dropdownlistWhatsAppCountries" runat="server" DataTextField="FullDescription" DataValueField="InternationalCode" AppendDataBoundItems="true" ClientIDMode="Static">
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-phone">Teléfono</span>:</th>
                    <td class="data">
                        <asp:TextBox ID="textboxWhatsAppSelectedPhoneCode" runat="server" ReadOnly="true" Width="30" ClientIDMode="Static" />
                        <span>-</span>
                        <asp:TextBox ID="textboxWhatsAppPhoneNumber" runat="server" Width="120" ClientIDMode="Static" />
                        <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsAppPhoneNumber" />
                        <asp:RegularExpressionValidator runat="server" ControlToValidate="textboxWhatsAppPhoneNumber" ValidationExpression="\d+" />
                        <asp:CustomValidator ID="customvalidatorPhoneNumber" runat="server" EnableClientScript="true" ClientValidationFunction="ValidatePhoneNumber" />
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="globals-from_date">Fecha desde</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:TextBox ID="textboxWhatsAppFromDate" runat="server" Width="120" ClientIDMode="Static" />
                                        <asp:CustomValidator runat="server" ControlToValidate="textboxWhatsAppFromDate" EnableClientScript="true" ClientValidationFunction="ValidateDateField" />
                                    </td>
                                    <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-from_date-tip">Especifica la fecha a partir de la cual se obtendrán los mensajes de WhatsApp
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-default_queue">Cola por defecto</span>:</th>
                    <td class="data">
                        <asp:DropDownList ID="dropdownlistWhatsAppQueue" runat="server" DataTextField="Name" DataValueField="ID" />
                    </td>
                </tr>
                <asp:PlaceHolder ID="placeholderWhatsAppCheckSpelling" runat="server">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label"><span data-i18n="configuration-serviceswhatsapp-spell_check">Revisión ortográfica obligatoria</span>:</th>
                        <td class="data">
                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <td class="vMid prs checkbox">
                                            <asp:CheckBox ID="checkboxWhatsAppCheckSpelling" runat="server" />
                                        </td>
                                        <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-spell_check-tip">Este parámetro indica si antes de enviar el mensaje es obligatoria la revisión ortográfica
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderAllowYFlow" runat="server">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-associate">Asociar a</span> <span class="yzn-yFlowISO"></span>:</th>
                        <td class="data">
                            <asp:DropDownList ID="dropdownlistUseYFlow" runat="server" ClientIDMode="Static">
                                <asp:ListItem Value="false" Text="No asociar a yFlow" Selected="True" data-i18n="configuration-serviceswhatsapp-not_associate-yFlow" />
                                <asp:ListItem Value="true" Text="Asociar a yFlow" data-i18n="configuration-serviceswhatsapp-associate-yFlow" />
                            </asp:DropDownList>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Debe completar los datos de yFlow en la solapa de configuración avanzada" EnableClientScript="true" ClientValidationFunction="ValidateYFlow" data-i18n="configuration-serviceswhatsapp-yFlow_validation-error" />
                            </div>
                        </td>
                    </tr>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderActAsChat" runat="server">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-act_as_chat">Utilizar el canal como un chat</span>:</th>
                        <td class="data">
                            <asp:CheckBox ID="checkboxActAsChat" runat="server" ClientIDMode="Static" />
                        </td>
                    </tr>
                    <asp:PlaceHolder runat="server" Visible="false">
                        <tr class="dataRow dataRowSeparator" id="trAllowToReplyToSpecificMessage">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-act_as_chat_allow_to_reply">Permitir responder sobre un mensaje</span>:</th>
                            <td class="data">
                                <asp:CheckBox ID="checkboxAllowToReplyToSpecificMessage" runat="server" />
                            </td>
                        </tr>
                    </asp:PlaceHolder>
                </asp:PlaceHolder>
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-integration_type">Tipo de integración</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs timeinfo">
                                        <asp:DropDownList ID="dropdownlistIntegrationType" runat="server" ClientIDMode="Static">
                                            <asp:ListItem Value="10" Selected="True" data-i18n="configuration-serviceswhatsapp-whatsapp_yoizen">Whatsapp Business (Yoizen)</asp:ListItem>
                                            <asp:ListItem Value="4">Whatsapp Business (Infobip)</asp:ListItem>
                                            <asp:ListItem Value="5">Whatsapp Business (Wavy)</asp:ListItem>
                                            <asp:ListItem Value="3" data-i18n="configuration-serviceswhatsapp-whatsapp_postback">Whatsapp Business con Postback</asp:ListItem>
                                            <asp:ListItem Value="6">Whatsapp Movistar</asp:ListItem>
                                            <asp:ListItem Value="7">Whatsapp Business (Interaxa)</asp:ListItem>
                                            <asp:ListItem Value="8">Whatsapp Business (Gupshup)</asp:ListItem>
                                            <asp:ListItem Value="9">Whatsapp Business (Twilio)</asp:ListItem>
                                            <asp:ListItem Value="11">Whatsapp Cloud API</asp:ListItem>
                                            <asp:ListItem Value="2">Whatsapp Business (BotMaker)</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                    <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-integration_type-tip">Indica el tipo de integración a realizar
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
            <div id="divIntegrationType10" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp10-title">Configuración para Whatsapp Business (Yoizen)</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-access_token">Access Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType10AccessToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-url">URL base</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType10BaseUrl" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <asp:PlaceHolder ID="placeholderIntegrationType10SendToServiceBus" runat="server" Visible="false">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus">Enviar directamente al Service Bus</span>:</th>
                                <td class="data">
                                    <asp:CheckBox ID="checkboxIntegrationType10SendToServiceBus" runat="server" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType10AccountID">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-account_id">Código de cuenta</span>:</th>
                                <td class="data">
                                    <asp:TextBox ID="textboxIntegrationType10AccountID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType10LineID">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-line_id">Código de línea</span>:</th>
                                <td class="data">
                                    <asp:TextBox ID="textboxIntegrationType10LineID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType10UseSessionInServiceBus">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus_use_sessions">Utilizar sesión</span>:</th>
                                <td class="data">
                                    <asp:DropDownList ID="dropdownlistIntegrationType10UseSessionInServiceBus" runat="server" ClientIDMode="Static">
                                        <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                        <asp:ListItem Value="1" data-i18n="globals-yes">Si</asp:ListItem>
                                        <asp:ListItem Value="2" data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus_sessions_with_line">Utilizar línea destino para la sesión</asp:ListItem>
                                    </asp:DropDownList>
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType10UseSessionForHsmInServiceBus">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus_use_sessions_hsms">Utilizar sesión</span>:</th>
                                <td class="data">
                                    <asp:DropDownList ID="dropdownlistIntegrationType10UseSessionForHsmInServiceBus" runat="server" ClientIDMode="Static">
                                        <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                        <asp:ListItem Value="1" data-i18n="globals-yes">Si</asp:ListItem>
                                        <asp:ListItem Value="2" data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus_sessions_with_line">Utilizar línea destino para la sesión</asp:ListItem>
                                    </asp:DropDownList>
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType10UseSeparateQueueForSingle">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp10-send_to_servicebus_use_alternate_queue_for_single">Utilizar cola diferente para envíos HSM individuales</span>:</th>
                                <td class="data">
                                    <asp:CheckBox ID="checkboxIntegrationType10UseSeparateQueueForSingle" runat="server" ClientIDMode="Static" />
                                </td>
                            </tr>
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="placeholderIntegrationType10MarkAsRead" runat="server" Visible="false">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-mark_as_read">Marcar mensajes como leído</span>:</th>
                                <td class="data">
                                    <asp:DropDownList ID="dropdownlistIntegrationType10MarkAsRead" runat="server">
                                        <asp:ListItem Value="0" data-i18n="globals-no" Selected="True">No</asp:ListItem>
                                        <asp:ListItem Value="1" data-i18n="configuration-serviceswhatsapp-mark_as_read-yes">Si, al recibirlos</asp:ListItem>
                                    </asp:DropDownList>
                                </td>
                            </tr>
                        </asp:PlaceHolder>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType10" data-i18n="configuration-serviceswhatsapp-whatsapp10-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType11" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp11-title">Configuración para Whatsapp Cloud API</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp11-graph_api_version">Versión Graph API</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:TextBox ID="textboxIntegrationType11GraphApiVersion" runat="server" Width="100px" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                            </td>
                                            <td class="vMid pls" data-i18n="[html]configuration-serviceswhatsapp-whatsapp11-graph_api_version-tip">La versión de la Graph Api deberá ser en formato <span class="bold">v##.#</span> (la cual se puede obtener desde https://developers.facebook.com/docs/graph-api/changelog/)
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Waba Id</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType11WabaId" runat="server" Width="200px" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp11-access_token">Access Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType11AccessToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                <label class="uiButton">
                                    <button type="button" data-i18n="globals-validate" onclick="TestIntegrationType11()">Validar</button>
                                </label>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp11-phone_number_id">Id de teléfono</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType11PhoneNumberId" runat="server" Width="200px" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <asp:PlaceHolder ID="placeholderIntegrationType11MarkAsRead" runat="server" Visible="false">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-mark_as_read">Marcar mensajes como leído</span>:</th>
                                <td class="data">
                                    <asp:DropDownList ID="dropdownlistIntegrationType11MarkAsRead" runat="server">
                                        <asp:ListItem Value="0" data-i18n="globals-no" Selected="True">No</asp:ListItem>
                                        <asp:ListItem Value="1" data-i18n="configuration-serviceswhatsapp-mark_as_read-yes">Si, al recibirlos</asp:ListItem>
                                    </asp:DropDownList>
                                </td>
                            </tr>
                        </asp:PlaceHolder>
                        <asp:PlaceHolder ID="placeholderIntegrationType11Testing" runat="server" Visible="false">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 200px !important">Cuenta de prueba:</th>
                                <td class="data">
                                    <asp:CheckBox ID="checkboxIntegrationType11TestingAccount" runat="server" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" id="trIntegrationType11TestingMapping" style="display: none">
                                <th class="label" style="width: 200px !important">Mapeo de cambios de teléfono de prueba:</th>
                                <td class="data">
                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="vMid prs">
                                                    <asp:TextBox ID="textboxIntegrationType11TestingMapping" runat="server" ClientIDMode="Static" TextMode="MultiLine" Rows="5" Style="width: 200px" autotrim="true" />
                                                </td>
                                                <td class="vMid pls">Escribir en cada renglón el inicio del número de teléfono a reemplazar, el caracter igual, y el inicio del número de teléfono que se reemplazará.<br />
                                                    Escriba la cantidad de mapeos que desee, escribiendo.<br />
                                                    El formato, por ejemplo es: 54911=541115
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </asp:PlaceHolder>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType11" data-i18n="configuration-serviceswhatsapp-whatsapp11-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType2" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp2-title">Configuración para Whatsapp Business (Yoizen)</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp2-access_token">Access Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType2AccessToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" style="display: none">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp2-secret">Secret</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType2Secret" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp2-refresh_token">Refresh Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType2RefreshToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType2" data-i18n="configuration-serviceswhatsapp-whatsapp2-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType3" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp3-title">Configuración para Whatsapp Business (Postback)</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-serviceswhatsapp-whatsapp3-info">
						Todas las URLs configuradas deben ser accesibles desde <span class="productname"></span>
                    </yoizen:Message>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-postback-format-title">Configuración de formato</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-postback-format">Utilizar formato de mensajes propio de WhatsApp</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxIntegrationType3UseWhatsappFormat" runat="server" ClientIDMode="Static" />
                                        <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceswhatsapp-postback-format-tip" Style="margin-top: 5px">
											En caso de utilizar esta opción, se utilizará el formato nativo de los mensajes de whatsapp sin necesidad de convertir los mensajes a otro formato.
											Los mensajes entrantes deberán tener el contenido que Whatsapp envió y las respuestas deberán respetar el formato que Whatsapp define para
											las respuestas
                                        </yoizen:Message>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trIntegrationType3UseWhatsappFormatIncludeFrom">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-postback-include_from">Incluir el número de teléfono del servicio en la propiedad</span> <span class="mono">from</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat" runat="server" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-answers_configuration-title">Configuración para respuestas</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important; padding-top: 35px;"><span data-i18n="configuration-serviceswhatsapp-answers_url">URL donde se publicarán las respuestas</span>:</th>
                                    <td class="data">
                                        <div id="divIntegrationType3ReplyEndpoint" class="http-request">
                                            <asp:HiddenField ID="hiddenIntegrationType3ReplyEndpoint" runat="server" ClientIDMode="Static" />
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div id="divPanelIntegrationType3VoiceCallsEndpoint" style="display: none">
                        <asp:Panel ID="panelIntegrationType3VoiceCallsEndpoint" runat="server" CssClass="subseccion collapsable">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-voicecalls_configuration-title">Configuración para llamadas</h2>
                            </div>
                            <div class="contents">
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label" style="width: 200px !important; padding-top: 35px;"><span data-i18n="configuration-serviceswhatsapp-voicecalls_url">URL donde se publicarán las acciones de llamadas</span>:</th>
                                        <td class="data">
                                            <div id="divIntegrationType3VoiceCallsEndpoint" class="http-request">
                                                <asp:HiddenField ID="hiddenIntegrationType3VoiceCallsEndpoint" runat="server" ClientIDMode="Static" />
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </asp:Panel>
                    </div>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-service_configuration-title">Configuración para servicio</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-pull_type">Mecanismo para obtener novedades</span>:</th>
                                    <td class="data">
                                        <asp:DropDownList ID="dropdownlistIntegrationType3PullType" runat="server" ClientIDMode="Static">
                                            <asp:ListItem Value="1" data-i18n="configuration-serviceswhatsapp-pull_type-no">No obtener novedades con servicio</asp:ListItem>
                                            <asp:ListItem Value="2" data-i18n="configuration-serviceswhatsapp-pull_type-specific_url">Obtener novedades invocando URLs específicas</asp:ListItem>
                                            <asp:ListItem Value="3" data-i18n="configuration-serviceswhatsapp-pull_type-default">Obtener novedades invocando a la ubicación por defecto</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trIntegrationType3PullTypeSpecificGet">
                                    <th class="label" style="width: 200px !important; padding-top: 35px;"><span data-i18n="configuration-serviceswhatsapp-get_news">HTTP GET para obtener novedades</span>:</th>
                                    <td class="data">
                                        <div id="divIntegrationType3GetNewsEndpoint" class="http-request">
                                            <asp:HiddenField ID="hiddenIntegrationType3GetNewsEndpoint" runat="server" ClientIDMode="Static" />
                                        </div>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trIntegrationType3PullTypeSpecificPost">
                                    <th class="label" style="width: 200px !important; padding-top: 35px;"><span data-i18n="configuration-serviceswhatsapp-post_news_processed">HTTP POST para informar novedades procesadas</span>:</th>
                                    <td class="data">
                                        <div id="divIntegrationType3PostNewsProcessedEndpoint" class="http-request">
                                            <asp:HiddenField ID="hiddenIntegrationType3PostNewsProcessedEndpoint" runat="server" ClientIDMode="Static" />
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-postback-notify_closed_case-title">Configuración para Cierre de casos</h2>
                        </div>
                        <div class="contents">
                            <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-serviceswhatsapp-postback-notify_closed_case-info">
								Algunas integraciones necesitan que <span class="productname"></span> informe cuando finaliza un caso para poder tomar decisiones
                            </yoizen:Message>
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-postback-notify_closed_case-should_notify">Informar casos cerrados</span>:</th>
                                    <td class="data">
                                        <asp:DropDownList ID="dropdownlistIntegrationType3NotifyClosedCases" runat="server" ClientIDMode="Static">
                                            <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                            <asp:ListItem Value="1" data-i18n="globals-yes">Si</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trIntegrationType3NotifyClosedCasesTrue">
                                    <th class="label" style="width: 200px !important; padding-top: 35px;"><span data-i18n="configuration-serviceswhatsapp-postback-notify_closed_case-endpoint">HTTP POST para informar</span>:</th>
                                    <td class="data">
                                        <div id="divIntegrationType3CloseCaseEndpoint" class="http-request">
                                            <asp:HiddenField ID="hiddenIntegrationType3CloseCaseEndpoint" runat="server" ClientIDMode="Static" />
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-title">Más configuraciones</h2>
                        </div>
                        <div class="contents">
                            <yoizen:Message runat="server" Type="Information">
								<span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-info-1">La utilización de la propiedad <span class="mono">payload</span> dentro del mensaje de Whatsapp permite obtener información adicional al texto que se envía en el mensaje.</span><br />
								<span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-info-2">Es requisito de la propiedad es que sea un <span class="mono">objeto</span> y sus propiedades podrán ser de los siguientes tipos</span>:
								<ul>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string">Texto</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string-desc">Contiene un texto. Este es el único tipo de dato que soporta la configuración de las derivaciones a otras colas</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer">Numérico</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer-desc">Contiene un valor numérico</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date">Fecha</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date-desc">Contiene una fecha en formato texto</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean">Booleano</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean-desc">Contiene un valor booleano (si/no)</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp">Timestamp</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp-desc">Contiene una fecha en formato Unix Timestamp (valor numérico con la cantidad de segundos desde el 01/01/1970)</span></li>
								</ul>
								<span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-info-3">Adicional y opcionalmente, se podrá seleccionar solamente una propiedad de tipo texto para configurar las derivaciones a colas</span>
                            </yoizen:Message>
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-allow_payload">Permitir <span class="mono">payload</span></span>:</th>
                                    <td class="data">
                                        <asp:DropDownList ID="dropdownlistIntegrationType3PayloadType" runat="server" ClientIDMode="Static">
                                            <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                            <asp:ListItem Value="1" data-i18n="configuration-serviceswhatsapp-postback-more_settings-allow_payload-yes-object">Si, en formato objeto</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                            </table>
                            <div class="subsubseccion collapsable" id="divIntegrationType3PayloadTypeObject" style="display: none">
                                <div class="title">
                                    <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-title">Propiedades del objeto</h2>
                                </div>
                                <div class="contents">
                                    <asp:HiddenField ID="hiddenIntegrationType3PayloadTypeObject" runat="server" ClientIDMode="Static" />
                                    <yoizen:Message runat="server" Type="Information">
										<span data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-info-1">Por cada propiedad deberá especificar</span>:
										<ul>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-name">Nombre</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-name-desc">El nombre de la propiedad. Puede contener letras, números, los símbolos $ y _. No puede empezar con números y no puede repetirse</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-desc">Descripción</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-desc-desc">La descripción de la propiedad</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type">Tipo</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-desc">El tipo de la propiedad</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-use_for_queue">Usar para segmentar</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-use_for_queue-desc">Indica si la propiedad será utilizada para segmentar por colas. Solamente compatible cuando el tipo de la propiedad es Texto y no puede haber más de una propiedad que se use para segmentar</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-show_to_agents">Mostrar a los agentes</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-show_to_agents-desc">Indica si la propiedad mostrará su valor a los agentes</span></li>
										</ul>
                                    </yoizen:Message>
                                    <div id="divIntegrationType3PayloadTypeObjectContainer">
                                    </div>
                                    <div class="validationerror" style="display: none">
                                        <asp:CustomValidator runat="server" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType3PayloadTypeObject" />
                                    </div>
                                </div>
                            </div>
                            <div class="subsubseccion collapsable" id="divIntegrationType3Derivations" style="display: none">
                                <div class="title">
                                    <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-title">Mapeo de derivaciones</h2>
                                </div>
                                <div class="contents">
                                    <asp:HiddenField ID="hiddenIntegrationType3Derivations" runat="server" ClientIDMode="Static" />
                                    <yoizen:Message runat="server" Type="Information">
										<span data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-info-1">
											Permite configurar las distintas configuraciones para derivar a las distintas colas. 
											Se deberá ingresar los distintos valores a verificar, sin poder repetirse las claves.
											Por cada clave se deberá configurar
										</span>:
										<ul>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-key">Clave</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-key-desc">La clave a evaluar. Puede contener letras, números, y el símbolo _. No puede repetirse</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-queue">Cola</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-queue-desc">La cola a la cual se derivará</span></li>
										</ul>
                                    </yoizen:Message>
                                    <div id="divIntegrationType3DerivationsContainer">
                                    </div>
                                    <div class="validationerror" style="display: none">
                                        <asp:CustomValidator runat="server" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType3Derivations" />
                                    </div>
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-other_settings-title">Otros ajustes</h2>
                                        </div>
                                        <div class="contents">
                                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                                <tr class="dataRow dataRowSeparator">
                                                    <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-postback-more_settings-derivations-ignore_previous_queues">Ignorar colas previas del caso</span>:</th>
                                                    <td class="data">
                                                        <asp:DropDownList ID="dropdownlistIntegrationType3IgnorePreviousQueues" runat="server" ClientIDMode="Static">
                                                            <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                                            <asp:ListItem Value="1" data-i18n="globals-yes">Si</asp:ListItem>
                                                        </asp:DropDownList>
                                                        <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-serviceswhatsapp-postback-more_settings-derivations-ignore_previous_queues-info" Style="margin-top: 5px">
															En caso de seleccionar la opción <span class="underline">Si</span> el sistema ignorará si el caso tuvo una derivación a otra cola
															y el mensaje nuevo ignorará dicha cola en caso de cumplirse alguna de las condiciones de derivación configuradas arriba
                                                        </yoizen:Message>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType3" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType4" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_business_4_configuration-title">Configuración para Whatsapp Business 4</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-authorization_type">Tipo de autorización</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistIntegrationType4AuthorizationType" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Value="1" Text="Usuario y contraseña" data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-authorization_type-userpassword" />
                                    <asp:ListItem Value="2" Text="API Key" data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-authorization_type-apikey" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trIntegrationType4AuthorizationType1User">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-user">Usuario</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType4User" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trIntegrationType4AuthorizationType1Password">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-password">Contraseña</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType4Password" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trIntegrationType4AuthorizationType2ApiKey">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-authorization_type-apikey">API Key</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType4ApiKey" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-url">URL base</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType4BaseUrl" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType4" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType5" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_business_3_configuration-title">Configuración para Whatsapp Business 3</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp3_configuration-user">Usuario</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType5Username" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp3_configuration-token">Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType5Token" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType5" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType6" class="seccion" style="display: none">
                <div class="title">
                    <h2>Configuración para Whatsapp Movistar</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Client ID</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType6ClientID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Client Secret</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType6ClientSecret" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>URL base</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType6UrlBase" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                <label class="uiButton">
                                    <button type="button" onclick="TestIntegrationType6()">Probar</button>
                                </label>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Login (Relativo a URL base)</span>:</th>
                            <td class="data">
                                <div style="display: flex; flex-direction: row; align-items: center;">
                                    <span style="flex-grow: 0; flex-shrink: 0" rel="IntegrationType6UrlBase"></span>
                                    <asp:TextBox ID="textboxIntegrationType6Login" runat="server" autocomplete="off" ClientIDMode="Static" spellcheck="false" Style="flex-grow: 1; flex-shrink: 1" />
                                </div>
                                <yoizen:Message ID="messageIntegrationType6Login" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
									Puede utilizar los siguientes campos dentro del mensaje:<br />
									<ul>
										<li><span class='templatefieldname'>@@CONVERSATIONID@@</span>: <span class='templatefielddescription'>Indica el código de conversación</span></li>
										<li><span class='templatefieldname'>@@CHANNELID@@</span>: <span class='templatefielddescription'>Indica el canal</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Envío (Relativo a URL base)</span>:</th>
                            <td class="data">
                                <div style="display: flex; flex-direction: row; align-items: center;">
                                    <span style="flex-grow: 0; flex-shrink: 0" rel="IntegrationType6UrlBase"></span>
                                    <asp:TextBox ID="textboxIntegrationType6SendMessage" runat="server" autocomplete="off" ClientIDMode="Static" spellcheck="false" Style="flex-grow: 1; flex-shrink: 1" />
                                </div>
                                <yoizen:Message ID="messageIntegrationType6SendMessage" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
									Puede utilizar los siguientes campos dentro del mensaje:<br />
									<ul>
										<li><span class='templatefieldname'>@@CONVERSATIONID@@</span>: <span class='templatefielddescription'>Indica el código de conversación</span></li>
										<li><span class='templatefieldname'>@@CHANNELID@@</span>: <span class='templatefielddescription'>Indica el canal</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Historial Conversaciones (Relativo a URL base)</span>:</th>
                            <td class="data">
                                <div style="display: flex; flex-direction: row; align-items: center;">
                                    <span style="flex-grow: 0; flex-shrink: 0" rel="IntegrationType6UrlBase"></span>
                                    <asp:TextBox ID="textboxIntegrationType6ConversationActivities" runat="server" autocomplete="off" ClientIDMode="Static" spellcheck="false" Style="flex-grow: 1; flex-shrink: 1" />
                                </div>
                                <yoizen:Message ID="messageIntegrationType6ConversationActivities" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
									Puede utilizar los siguientes campos dentro del mensaje:<br />
									<ul>
										<li><span class='templatefieldname'>@@CONVERSATIONID@@</span>: <span class='templatefielddescription'>Indica el código de conversación</span></li>
										<li><span class='templatefieldname'>@@CHANNELID@@</span>: <span class='templatefielddescription'>Indica el canal</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Channel ID</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:TextBox ID="textboxIntegrationType6ChannelID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                            </td>
                                            <td class="vMid pls">Es el valor del campo <span class="mono">$.channelId</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>ID de Remitente</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:TextBox ID="textboxIntegrationType6FromID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                            </td>
                                            <td class="vMid pls">Es el valor del campo <span class="mono">$.from.id</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Nombre de Remitente</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:TextBox ID="textboxIntegrationType6FromName" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                            </td>
                                            <td class="vMid pls">Es el valor del campo <span class="mono">$.from.name</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important">Enviar
                                <spam class="mono">endOfConversation</spam>:</th>
                            <td class="data">
                                <asp:CheckBox ID="checkboxIntegrationType6SendEndOfConversation" runat="server" ClientIDMode="Static" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trIntegrationType6EndOfConversation">
                            <th class="label" style="width: 200px !important"><span>Cierre de Conversación (Relativo a URL base)</span>:</th>
                            <td class="data">
                                <div style="display: flex; flex-direction: row; align-items: center;">
                                    <span style="flex-grow: 0; flex-shrink: 0" rel="IntegrationType6UrlBase"></span>
                                    <asp:TextBox ID="textboxIntegrationType6EndOfConversation" runat="server" autocomplete="off" ClientIDMode="Static" spellcheck="false" Style="flex-grow: 1; flex-shrink: 1" />
                                </div>
                                <yoizen:Message ID="messageIntegrationType6EndOfConversation" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
									Puede utilizar los siguientes campos dentro del mensaje:<br />
									<ul>
										<li><span class='templatefieldname'>@@CONVERSATIONID@@</span>: <span class='templatefielddescription'>Indica el código de conversación</span></li>
										<li><span class='templatefieldname'>@@CHANNELID@@</span>: <span class='templatefielddescription'>Indica el canal</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                    </div>
                    <div class="subseccion collapsable">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-title">Más configuraciones</h2>
                        </div>
                        <div class="contents">
                            <yoizen:Message runat="server" Type="Information">
								<span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-info-1">La utilización de la propiedad <span class="mono">payload</span> dentro del mensaje de Whatsapp permite obtener información adicional al texto que se envía en el mensaje.</span><br />
								<span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-info-2">Es requisito de la propiedad es que sea un <span class="mono">objeto</span> y sus propiedades podrán ser de los siguientes tipos</span>:
								<ul>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string">Texto</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string-desc">Contiene un texto. Este es el único tipo de dato que soporta la configuración de las derivaciones a otras colas</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer">Numérico</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer-desc">Contiene un valor numérico</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date">Fecha</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date-desc">Contiene una fecha en formato texto</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean">Booleano</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean-desc">Contiene un valor booleano (si/no)</span></li>
									<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp">Timestamp</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp-desc">Contiene una fecha en formato Unix Timestamp (valor numérico con la cantidad de segundos desde el 01/01/1970)</span></li>
								</ul>
                            </yoizen:Message>
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label" style="width: 200px !important;"><span data-i18n="[html]configuration-serviceswhatsapp-postback-more_settings-allow_payload">Permitir <span class="mono">payload</span></span>:</th>
                                    <td class="data">
                                        <asp:DropDownList ID="dropdownlistIntegrationType6PayloadType" runat="server" ClientIDMode="Static">
                                            <asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
                                            <asp:ListItem Value="1" data-i18n="configuration-serviceswhatsapp-postback-more_settings-allow_payload-yes-object">Si, en formato objeto</asp:ListItem>
                                        </asp:DropDownList>
                                    </td>
                                </tr>
                            </table>
                            <div class="subsubseccion collapsable" id="divIntegrationType6PayloadTypeObject" style="display: none">
                                <div class="title">
                                    <h2 data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-title">Propiedades del objeto</h2>
                                </div>
                                <div class="contents">
                                    <asp:HiddenField ID="hiddenIntegrationType6PayloadTypeObject" runat="server" ClientIDMode="Static" />
                                    <yoizen:Message runat="server" Type="Information">
										<span data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-info-1">Por cada propiedad deberá especificar</span>:
										<ul>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-name">Nombre</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-name-desc">El nombre de la propiedad. Puede contener letras, números, los símbolos $ y _. No puede empezar con números y no puede repetirse</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-desc">Descripción</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-desc-desc">La descripción de la propiedad</span></li>
											<li><span class='templatefieldname' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type">Tipo</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-postback-more_settings-payload-property-type-desc">El tipo de la propiedad</span></li>
										</ul>
                                    </yoizen:Message>
                                    <div id="divIntegrationType6PayloadTypeObjectContainer">
                                    </div>
                                    <div class="validationerror" style="display: none">
                                        <asp:CustomValidator runat="server" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6PayloadTypeObject" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <asp:Panel ID="panelIntegrationType6Surveys" runat="server" CssClass="subseccion collapsable" Visible="false">
                        <div class="title">
                            <h2>Encuestas</h2>
                        </div>
                        <div class="contents">
                            <yoizen:Message ID="messageIntegrationType6NoSurveys" runat="server" Type="Warning" Visible="false">
								No existen encuestas creadas y habilitadas
                            </yoizen:Message>
                            <asp:Panel ID="panelIntegrationType6EnableSurveys" runat="server">
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label withdescription" style="width: 250px !important;"><span>Habilitar Envío de Encuestas</span>:</th>
                                        <td class="data">
                                            <asp:CheckBox ID="checkboxIntegrationType6EnableSurveys" ClientIDMode="Static" runat="server" />
                                        </td>
                                    </tr>
                                </table>
                                <div id="divIntegrationType6SurveyConfiguration" class="subsubseccion" style="margin-top: 20px">
                                    <div class="title">
                                        <h2>Configuración de encuestas</h2>
                                    </div>
                                    <div class="contents">
                                        <yoizen:Message ID="messageIntegrationType6SurveyDisabled" runat="server" Type="Warning" Style="display: none;" ClientIDMode="Static">
											El servicio tenía configurado una encuesta que está deshabilitada. Por favor seleccione otra
                                        </yoizen:Message>
                                        <table width="100%" border="0" class="uiInfoTable noBorder">
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label withdescription" style="width: 250px !important;"><span>Encuesta a enviar</span>:</th>
                                                <td class="data">
                                                    <asp:DropDownList ID="dropdownlistIntegrationType6Survey" runat="server" DataValueField="ID" DataTextField="Name" AppendDataBoundItems="true" ClientIDMode="Static">
                                                        <asp:ListItem Value="-1" Text="Seleccione una encuesta" Selected="True" />
                                                    </asp:DropDownList>
                                                    <asp:CustomValidator runat="server" ControlToValidate="dropdownlistIntegrationType6Survey" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6SurveyToSend" />
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label"><span>Invitación para Participar</span>:</th>
                                                <td class="data">
                                                    <asp:TextBox ID="textboxIntegrationType6SurveyInvitation" ClientIDMode="Static" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="3" />
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6SurveyInvitation" />
                                                    <yoizen:Message ID="messageIntegrationType6SurveyInvitationFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
														<span>Debe utilizar el campo @@LINK@@ dentro del texto y puede optar por utilizar los otros mencionados a continuación</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription'>Indica donde irá el link de la encuesta</span></li>
															<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription'>Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
															<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription'>Indica el alias del usuario al cual se le mandará el mensaje</span></li>
														</ul>
                                                    </yoizen:Message>
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label"><span>Tiempo de expiración de la invitación (minutos)</span>:</th>
                                                <td class="data">
                                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                        <tbody>
                                                            <tr>
                                                                <td class="vMid prs timeinfo">
                                                                    <asp:TextBox ID="textboxIntegrationType6SurveyExpiration" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" Style="text-align: right" />
                                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6SurveyExpiration" />
                                                                    <asp:RangeValidator runat="server" ControlToValidate="textboxIntegrationType6SurveyExpiration" Type="Integer" MaximumValue="99999" MinimumValue="0" />
                                                                    <div class="timeinfo" related="textboxIntegrationType6SurveyExpiration"></div>
                                                                </td>
                                                                <td class="vMid pls">Define la cantidad de minutos que deben pasar a partir de la fecha de envío de la encuesta para considerar la invitación como expirada.
																	Ingrese cero para indicar que la invitación nunca expira
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label withdescription" style="width: 250px !important;"><span>No encuestar si la última envíada fue dentro de los últimos (minutos)</span>:</th>
                                                <td class="data">
                                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                        <tbody>
                                                            <tr>
                                                                <td class="vMid prs timeinfo">
                                                                    <asp:TextBox ID="textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" Style="text-align: right" />
                                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes" />
                                                                    <asp:RangeValidator runat="server" ControlToValidate="textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes" Type="Integer" MaximumValue="172800" MinimumValue="0" />
                                                                    <div class="timeinfo" related="textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes"></div>
                                                                </td>
                                                                <td class="vMid pls">Especifica la cantidad de minutos a considerar hacia atrás para evaluar si no se envió ninguna encuesta al usuario del caso. En caso de que la última fecha de envío
																	de encuesta sea posterior a la fecha actual restando los minutos configurados, no se enviará la encuesta para dicho caso.
																	En caso de configurar cero, no se aplicará esta condición. El máximo valor son 172800 (4 meses)
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr class="dataRow dataRowSeparator">
                                                <th class="label" style="width: 200px !important">Enviar
                                                    <spam class="mono">endOfConversation</spam>:</th>
                                                <td class="data">
                                                    <asp:CheckBox ID="checkboxIntegrationType6SurveySendEndOfConversation" runat="server" ClientIDMode="Static" />
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </asp:Panel>
                        </div>
                    </asp:Panel>
                </div>
            </div>
            <div id="divIntegrationType7" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_business_7_configuration-title">Configuración para Whatsapp Business 7</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp7_configuration-user">Usuario</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType7User" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp7_configuration-password">Contraseña</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType7Password" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp4_configuration-url">URL base</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType7BaseUrl" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                                <label class="uiButton">
                                    <button type="button" data-i18n="configuration-serviceswhatsapp-whatsapp7_configuration-test" onclick="TestIntegrationType7()">Probar conexión</button>
                                </label>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important">Media ID:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType7MediaID" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType7" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType8" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_business_8_configuration-title">Configuración para Whatsapp Business (Gupshup)</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp_business_8_configuration-app_name">Nombre de aplicación</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType8AppName" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-whatsapp_business_8_configuration-apikey">API Key</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType8ApiKey" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType8" data-i18n="configuration-serviceswhatsapp-whatsapp2-error" />
                    </div>
                </div>
            </div>
            <div id="divIntegrationType9" class="seccion" style="display: none">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_business_9_configuration-title">Configuración para Whatsapp Business (Twilio)</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Account Sid</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType9AccountSid" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span>Auth Token</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxIntegrationType9AuthToken" runat="server" Width="90%" autocomplete="off" ClientIDMode="Static" spellcheck="false" />
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateIntegrationType9" data-i18n="configuration-serviceswhatsapp-whatsapp2-error" />
                    </div>
                </div>
            </div>
        </div>
        <div id="divWhatsappBehaviour">
            <div class="seccion hiddenAsGateway">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_behaviour-title">Acciones sobre mensajes próximos a superar la cantidad máxima de tiempo para responder</h2>
                </div>
                <div class="contents">
                    <div class="subseccion collapseWithCheckbox">
                        <div class="title">
                            <h2>
                                <asp:CheckBox ID="checkboxAutoReplyBeforeMaxTimeToAnswer" runat="server" ClientIDMode="Static" />
                                <span data-i18n="configuration-serviceswhatsapp-answer-title">Responder</span></h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-answer_message">Mensaje de respuesta</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeMaxTimeToAnswerText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
                                        <yoizen:Message ID="messageAutoReplyBeforeMaxTimeToAnswerText" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
											<span data-i18n="configuration-serviceswhatsapp-answer_template">Puede utilizar los siguientes campos dentro del mensaje</span>:<br />
											<ul>
												<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-answer_template-field-user_name">Indica el nombre del usuario (si no tiene nombre de usuario se utilizará su número de teléfono)</span></li>
												<li><span class='templatefieldname'>@@TELEFONO@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-answer_template-field-phone_number">Indica el número de teléfono del usuario</span></li>
												<li><span class='templatefieldname'>@@HORA@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-answer_template-field-edit-time">Indica la hora actual</span></li>
											</ul>
                                        </yoizen:Message>
                                    </td>
                                </tr>
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-minutes_before_limit">Cantidad minutos antes del límite</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeMaxTimeToAnswerMinutes" runat="server" MaxLength="4" ClientIDMode="Static" />
                                        <div class="timeinfo" related="textboxAutoReplyBeforeMaxTimeToAnswerMinutes"></div>
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyBeforeMaxTimeToAnswer" SkinID="validationerror" />
                            </div>
                        </div>
                    </div>
                    <div class="subseccion collapseWithCheckbox">
                        <div class="title">
                            <h2>
                                <asp:CheckBox ID="checkboxDiscardAfterMaxTimeToAnswer" runat="server" ClientIDMode="Static" />
                                <span data-i18n="configuration-serviceswhatsapp-discard-title">Descartar</span></h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-close_case">Cerrar el caso</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer" runat="server" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-whatsapp_behaviour_case_is_closed-title">Acciones sobre casos próximos a ser cerrados por inactividad</h2>
                </div>
                <div class="contents">
                    <div class="subseccion collapseWithCheckbox">
                        <div class="title">
                            <h2>
                                <asp:CheckBox ID="checkboxAutoReplyBeforeCaseIsClosed" runat="server" ClientIDMode="Static" />
                                <span data-i18n="configuration-serviceswhatsapp-answer-title">Responder</span>
                            </h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-answer_message">Mensaje de respuesta</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeCaseIsClosedText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
                                    </td>
                                </tr>
                                <tr class="dataRow">
                                    <th class="label"><span data-i18n="configuration-serviceswhatsapp-minutes_before_limit">Cantidad minutos antes del límite</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyBeforeCaseIsClosedMinutes" runat="server" MaxLength="4" ClientIDMode="Static" />
                                        <div class="timeinfo" related="textboxAutoReplyBeforeCaseIsClosedMinutes"></div>
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyBeforeCloseCase" SkinID="validationerror" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <asp:Panel ID="panelHSM" runat="server" CssClass="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-message_template-title">Plantillas de Mensajes (HSM)</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-serviceswhatsapp-message_template-tip">
						Las plantillas de mensajes son formatos que se utilizan para mensajes comunes reutilizables que una empresa tal vez quiera enviar. 
						Para enviar notificaciones a los clientes, las empresas deben utilizar las plantillas de mensajes.<br />
						El envío de este tipo de mensaje puede incluir costos
                    </yoizen:Message>
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-allow_templates">Permitir uso de Plantillas de Mensaje</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowToSendHSM" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator hiddenAsGateway" id="trAllowAgentsToSendHSM">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-allow_agents_to_send_templates">Permitir a los agentes enviar Plantillas de Mensaje</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowAgentsToSendHSM" runat="server">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                    <div id="divHSMTemplates" class="subseccion">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-message_template-title">Plantillas de Mensajes (HSM)</h2>
                        </div>
                        <div class="contents">
                            <asp:HiddenField ID="hiddenHSMTemplates" runat="server" ClientIDMode="Static"></asp:HiddenField>
                            <yoizen:Message runat="server" ID="messageHSMTemplatesEmpty" Type="Information" ClientIDMode="Static" Style="display: none" LocalizationKey="configuration-serviceswhatsapp-message_template-empty">
								No hay ninguna plantilla creada
                            </yoizen:Message>
                            <table id="tableHSMTemplatesv2" class="reporte" cellspacing="0" rules="all" border="1">
                                <thead>
                                    <tr class="header">
                                        <th scope="col" style="width: 350px;"><span data-i18n="configuration-serviceswhatsapp-template-description">Descripción</span></th>
                                        <th scope="col" style="width: 120px;"><span data-i18n="configuration-serviceswhatsapp-template-status">Estado</span></th>
                                        <th scope="col" style="width: 120px; word-break: break-word"><span data-i18n="configuration-serviceswhatsapp-template-is_multimedia">Es multimedia</span></th>
                                        <th scope="col" style="width: 120px; word-break: break-word" class="hiddenAsGateway"><span data-i18n="configuration-serviceswhatsapp-template-avail_for">Disponible para agentes</span></th>
                                        <th scope="col" style="width: 120px; word-break: break-word"><span data-i18n="configuration-serviceswhatsapp-template-send_hsm_with_case_open">Permite envío de plantilla con caso abierto</span></th>
                                        <th style="width: 80px"></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div class="buttons">
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ConfirmDeleteHSMs()" data-i18n="globals-delete" id="buttonDeleteHSMs">Eliminar</button>
                                </label>
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ImportYoizenHSMs()" data-i18n="globals-import" id="buttonImportHSMs">Importar</button>
                                </label>
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ImportCloudApiHSMs()" data-i18n="globals-import" id="buttonImportCloudApiHSMs">Importar</button>
                                </label>
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ImportInfobipHSMs()" data-i18n="globals-import" id="buttonImportInfobipHSMs">Importar</button>
                                </label>
                                <label class="uiButton uiButtonLarge uiButtonConfirm">
                                    <button type="button" onclick="ShowHSMDialog(null)" data-i18n="globals-new">Nuevo</button>
                                </label>
                            </div>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateHSMTemplates" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                            </div>
                        </div>
                    </div>
                </div>
            </asp:Panel>
            <asp:Panel ID="panelSurveys" runat="server" CssClass="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-surveys-title">Encuestas</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceswhatsapp-surveys-tip">
						La definición de las encuestas se definen en las colas de atención. Desde aquí puede deshabilitar el uso de encuestas para mensajes
						de Whatsapp
                    </yoizen:Message>
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-allow-answers">Permitir encuestas</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowSurveys" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                </div>
            </asp:Panel>
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-behaviour-other-title">Otras configuraciones</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important;"><span data-i18n="configuration-serviceswhatsapp-behaviour-other-delay">Aplicar espera entre envío de mensajes sucesivos</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxDelayBetweenReplies" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="0" max="2000" step="100" list="tickmarks" EnableTheming="false" />
                                <datalist id="tickmarks">
                                    <option value="0" label="0"></option>
                                    <option value="100"></option>
                                    <option value="200"></option>
                                    <option value="300"></option>
                                    <option value="400"></option>
                                    <option value="500" label="500"></option>
                                    <option value="600"></option>
                                    <option value="700"></option>
                                    <option value="800"></option>
                                    <option value="900"></option>
                                    <option value="1000" label="1000"></option>
                                    <option value="1100"></option>
                                    <option value="1200"></option>
                                    <option value="1300"></option>
                                    <option value="1400"></option>
                                    <option value="1500" label="1500"></option>
                                    <option value="1600"></option>
                                    <option value="1700"></option>
                                    <option value="1800"></option>
                                    <option value="1900"></option>
                                    <option value="2000" label="2000"></option>
                                </datalist>
                                <div id="spanDelayBetweenReplies" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
                                <asp:CompareValidator runat="server" ControlToValidate="textboxDelayBetweenReplies" Operator="DataTypeCheck" Type="Integer" />
                                <asp:RangeValidator runat="server" ControlToValidate="textboxDelayBetweenReplies" MinimumValue="0" MaximumValue="2000" Type="Integer" />
                                <yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-other-delay-info">
									Especifica la cantidad de milisengundos a esperar entre los envíos de respuestas sucesivas. El valor cero especifica que no habrá espera.
									El valor máximo son 2000 milisegundos (2 segundos)
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trDelayAfterMultimedia">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-other-delay_after_images">Aplicar espera luego de envío de mensaje con multimedia</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxDelayAfterMultimedia" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="0" max="2000" step="100" list="tickmarks" EnableTheming="false" />
                                <div id="spanDelayAfterMultimedia" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
                                <asp:CompareValidator runat="server" ControlToValidate="textboxDelayAfterMultimedia" Operator="DataTypeCheck" Type="Integer" />
                                <asp:RangeValidator runat="server" ControlToValidate="textboxDelayAfterMultimedia" MinimumValue="0" MaximumValue="2000" Type="Integer" />
                                <yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-other-delay_after_images-info">
									Especifica la cantidad de milisengundos a esperar luego del envío de un mensaje que contenga multimedia. El valor cero especifica que no habrá espera.
									El valor máximo son 2000 milisegundos (2 segundos)
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" id="trPreviewUrlForTextMessages">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-other-preview_for_links">Incluir vista previa de URLs para mensajes de texto con URL</span>:</th>
                            <td class="data">
                                <asp:CheckBox ID="checkboxPreviewUrlForTextMessages" runat="server" ClientIDMode="Static" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-behaviour-catalogs">Catálogos</h2>
                </div>
                <div class="contents">
                    <asp:HiddenField ID="hiddenFacebookCatalogAccessToken" runat="server" ClientIDMode="Static" />
                    <asp:HiddenField ID="hiddenFacebookCatalogCatalog" runat="server" ClientIDMode="Static" />
                    <yoizen:Message runat="server" Type="Information">
						<span data-i18n="configuration-serviceswhatsapp-use_facebook_catalog-info">Los catálogos de Facebook permiten el envío de mensajes que hacen referencia a productos dentro de un catálogo del administrador de
						ventas de Facebook. Puede abrir el asistente de configuración haciendo clic</span> <a href="javascript:ShowFacebookCatalogPopup()" data-i18n="configuration-servicesfacebook-here">aquí</a>
                    </yoizen:Message>
                    <table width="100%" border="0" class="uiInfoTable noBorder" id="tableFacebookCatalog">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-use_facebook_catalog">Vincular catálogos de Facebook</span>:</th>
                            <td class="data">
                                <asp:CheckBox ID="checkboxUseFacebookCatalog" runat="server" ClientIDMode="Static" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" rel="dependsOnFacebookCatalog" style="display: none">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-business">Negocio</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookCatalogBusiness" runat="server" ClientIDMode="Static" spellcheck="false" autocomplete="off" Width="200px" />
                                <span id="spanFacebookCatalogBusinessName" style="margin-left: 5px; font-style: italic"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" rel="dependsOnFacebookCatalog" style="display: none">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog">Catálogo</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxFacebookCatalogID" runat="server" ClientIDMode="Static" spellcheck="false" autocomplete="off" Width="200px" />
                                <span id="spanFacebookCatalogName" style="margin-left: 5px; font-style: italic"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator" rel="dependsOnFacebookCatalog" style="display: none">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog-products">Productos</span>:</th>
                            <td class="data">
                                <div class="facebook-catalog-products-container">
                                    <div id="divFacebookBusinessCatalogProductsSelected" class="facebook-catalog-products"></div>
                                </div>
                                <yoizen:Message ID="messageFacebookBusinessCatalogSelectedNoProducts" runat="server" ClientIDMode="Static" Type="Warning" Style="display: none; margin-top: 10px" LocalizationKey="configuration-serviceswhatsapp-behaviour-catalogs-wizard-catalog_no_products-error">
									El catálogo seleccionado no tiene ningún producto
                                </yoizen:Message>
                                <yoizen:Message runat="server" Type="Information">
									<span data-i18n="configuration-serviceswhatsapp-use_facebook_catalog-reload-info">
										Si las imágenes desaparecieron o los productos fueron modificados puede actualizar
										el listado de productos haciendo clic</span> <a href="javascript:ShowFacebookCatalogReloadProductsPopup()" data-i18n="configuration-servicesfacebook-here">aquí</a>
                                </yoizen:Message>
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateFacebookCatalog" data-i18n="configuration-serviceswhatsapp-behaviour-catalogs-error" />
                    </div>
                </div>
            </div>
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-flows">Flujos de meta</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-serviceswhatsapp-flows-tip">
						Las plantillas de mensajes son formatos que se utilizan para mensajes comunes reutilizables que una empresa tal vez quiera enviar. 
						Para enviar notificaciones a los clientes, las empresas deben utilizar las plantillas de mensajes.<br />
						El envío de este tipo de mensaje puede incluir costos
                    </yoizen:Message>
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-serviceswhatsapp-flow-allow_to_use">Permitir uso de Flows</span>:</th>
                            <td class="data">
                                <asp:DropDownList ID="dropdownlistAllowToSendFlows" runat="server" ClientIDMode="Static">
                                    <asp:ListItem Text="Sí" Value="1" data-i18n="globals-yes" />
                                    <asp:ListItem Text="No" Value="0" data-i18n="globals-no" />
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                    <div id="divFlows" class="subseccion">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-flows">Flujos de Meta (HSM)</h2>
                        </div>
                        <div class="contents">
                            <asp:HiddenField ID="hiddenFlows" runat="server" ClientIDMode="Static"></asp:HiddenField>
                            <yoizen:Message runat="server" ID="messageFlowsEmpty" Type="Information" ClientIDMode="Static" Style="display: none" LocalizationKey="configuration-serviceswhatsapp-flow-empty">
								No hay ninguna plantilla creada
                            </yoizen:Message>
                            <table id="tableFlows" class="reporte" cellspacing="0" rules="all" border="1">
                                <thead>
                                    <tr class="header">
                                        <th scope="col"><span data-i18n="configuration-serviceswhatsapp-flow-id">ID</span></th>
                                        <th scope="col"><span data-i18n="configuration-serviceswhatsapp-flow-name">Nombre</span></th>
                                        <th scope="col"><span data-i18n="configuration-serviceswhatsapp-flow-status">Estado</span></th>
                                        <th scope="col"><span data-i18n="configuration-serviceswhatsapp-flow-categories">Categorias</span></th>
                                        <th style="width: 80px"></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div class="buttons">
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" style="display: none" onclick="ConfirmDeleteFlows()" data-i18n="globals-delete" id="buttonDeleteFlows">Eliminar</button>
                                </label>
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ImportYoizenFlows()" data-i18n="globals-import" id="buttonImportYoizenFlows">Importar</button>
                                </label>
                                <label class="uiButton uiButtonLarge">
                                    <button type="button" onclick="ImportCloudApiFlows()" data-i18n="globals-import" id="buttonImportCloudApiFlows">Importar</button>
                                </label>
                            </div>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateFlows" data-i18n="configuration-serviceswhatsapp-configuration-error" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="divCapiService" runat="server" clientidmode="static" class="seccion" style="display:none;">
                <div class="title">
				    <h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;Capi</h2>
                </div>
                <div class="contents">
				<table width="100%" border="0" class="uiInfoTable noBorder">
					<tr class="dataRow dataRowSeparator">
						<th class="label withdescription"><span data-i18n="configuration-systemsettings-capi_enable">Habilitar CAPI</span>:</th>
						<td class="data">
							<table class="uiGrid" cellspacing="0" cellpadding="0">
								<tbody>
									<tr>
										<td class="vMid prs">
											<asp:CheckBox ID="checkboxEnableCapi" runat="server" />
										</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
				</table>
				</div>
            </div>
        </div>
        <div id="divAdvancedConfigurationYFlow">
            <div class="seccion hiddenAsGateway">
                <div class="title">
                    <h2 data-i18n="configuration-services-yFlow">yFlow</h2>
                </div>
                <div class="contents">
                    <table width="95%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label" style="width: 200px !important"><span data-i18n="configuration-services-common-yflow-flow">Flujo</span>:</th>
                            <td class="data">
                                <asp:HiddenField ID="hiddenFlow" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <asp:HiddenField ID="hiddenSurveys" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                <select id="selectFlowToUse"></select>
                                <a id="anchorFlowsReload">
                                    <span class="fa fa-lg fa-sync" title="Refrescar" data-i18n-title="configuration-services-common-yflow-refresh"></span>
                                </a>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-code">Código</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowID"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-name">Nombre</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowName"></span>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-services-common-yflow-version">Versión</span>:</th>
                            <td class="data noinput">
                                <span id="spanFlowVersion"></span>
                            </td>
                        </tr>
                    </table>
                    <div class="validationerror" style="display: none">
                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSelectFlowToUse" data-i18n="configuration-serviceschat-configuration-error" />
                    </div>
                </div>
                <div class="seccion hiddenAsGateway" id="divYFlowContingency" runat="server">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-Contingency-title">yFlow contingencia</h2>
                    </div>
                    <div class="contents">
                        <table width="95%" border="0" class="uiInfoTable noBorder">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 200px !important"><span data-i18n="configuration-services-common-yflow-flow">Flujo</span>:</th>
                                <td class="data">
                                    <asp:HiddenField ID="hiddenFlowContingency" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                    <asp:HiddenField ID="hiddenSurveysContingency" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                    <select id="selectFlowContingencyToUse"></select>
                                    <a id="anchorFlowsContingencyReload">
                                        <span class="fa fa-lg fa-sync" title="Refrescar" data-i18n-title="configuration-services-common-yflow-refresh"></span>
                                    </a>
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-code">Código</span>:</th>
                                <td class="data noinput">
                                    <span id="spanFlowContingencyID"></span>
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-name">Nombre</span>:</th>
                                <td class="data noinput">
                                    <span id="spanFlowContingencyName"></span>
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-version">Versión</span>:</th>
                                <td class="data noinput">
                                    <span id="spanFlowContingencyVersion"></span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="seccion hiddenAsGateway">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-flow_queue_transfers-title">Mapeo para transferencia a distintas colas</h2>
                    </div>
                    <div class="contents">
                        <asp:HiddenField ID="hiddenFlowQueueTransfersByKey" runat="server" ClientIDMode="Static"></asp:HiddenField>
                        <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-services-common-yflow-flow_queue_transfers">
							Este mapeo permite configurar distintas claves que se configuran en la pieza de yFlow que permite derivar el chat a agente
							a distintas colas. En caso de que no se encuentre ningún mapeo se utilizará la cola por defecto del servicio
                        </yoizen:Message>
                        <table id="tableFlowQueueTransfersByKey" class="reporte" cellspacing="0" rules="all" border="1">
                            <thead>
                                <tr class="header">
                                    <th style="width: 20px"></th>
                                    <th scope="col"><span data-i18n="configuration-services-common-yflow-flow_queue_transfers-key">Clave</span></th>
                                    <th scope="col"><span data-i18n="configuration-services-common-yflow-flow_queue_transfers-queue">Cola</span></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                            <tfoot>
                                <tr>
                                    <td style="text-align: center"><a id="anchorFlowQueueTransfersByKeyAdd"><span class="fa fa-lg fa-plus-square"></span></a></td>
                                    <td colspan="5"></td>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="validationerror" style="display: none">
                            <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateFlowQueueTransfersByKey" data-i18n="configuration-services-common-yflow-configuration-error" />
                        </div>
                    </div>
                </div>
                <div class="seccion hiddenAsGateway">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-more_configuration-title">Otras configuraciones</h2>
                    </div>
                    <div class="contents">
                        <table width="95%" border="0" class="uiInfoTable noBorder">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label" style="width: 250px !important"><span data-i18n="configuration-services-common-yflow-share_enqueued_messages">Compartir mensajes encolados de las siguientes colas</span>:</th>
                                <td class="data">
                                    <asp:ListBox ID="listboxFlowShareEnqueuedMessagesFromQueues" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-share_connected_agents">Compartir agentes conectados de las siguientes colas</span>:</th>
                                <td class="data">
                                    <asp:ListBox ID="listboxFlowShareConnectedAgentsFromQueues" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label"><span data-i18n="configuration-services-common-yflow-share_with_services">Compartir casos abiertos de un mismo perfil en otros servicios</span>:</th>
                                <td class="data">
                                    <asp:ListBox ID="listboxFlowShareWithServices" runat="server" SelectionMode="Multiple" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator">
                                <th class="label withdescription"><span data-i18n="configuration-services-common-yflow-minutes_to_call_yflow">Minutos para no invocar a yFlow luego de caso cerrado por agente</span>:</th>
                                <td class="data">
                                    <table class="uiGrid" cellspacing="0" cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="vMid prs">
                                                    <asp:TextBox ID="textboxFlowMinutesAfterAgentClosedCase" runat="server" MaxLength="5" Width="100" TextMode="Number" Style="text-align: right" ClientIDMode="Static" />
                                                    <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateFlowMinutesAfterAgentClosedCase" />
                                                    <div class="timeinfo" related="textboxFlowMinutesAfterAgentClosedCase"></div>
                                                </td>
                                                <td class="vMid pls" data-i18n="configuration-services-common-yflow-minutes_to_call_yflow-tip">Este parámetro indica cuantos minutos se considerarán, luego de un caso cerrado por Agente, para encolar el
													mensaje proveniente del mismo usuario y evitar invocar primero a yFlow.
													Especifique cero para indicar siempre invocar a yFlow independientemente del tiempo transcurrido desde que se
													cerraron los casos.
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <asp:PlaceHolder ID="placeholderAllowAgentsToReturnMessagesToYFlow" runat="server">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="configuration-services-common-yflow-allow_agents_to_return_messages">Permitir a agente devolver un mensaje a yFlow</span>:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxAllowAgentsToReturnMessagesToYFlow" runat="server" />
                                    </td>
                                </tr>
                            </asp:PlaceHolder>
                        </table>
                    </div>
                </div>
                <asp:Panel ID="panelYFlowSurveys" runat="server" CssClass="seccion" Visible="false">
                    <div class="title">
                        <h2 data-i18n="configuration-services-common-yflow-surveys-title">Encuestas</h2>
                    </div>
                    <div class="contents">
                        <yoizen:Message ID="messageNoSurveys" runat="server" Type="Warning" Visible="false" LocalizationKey="configuration-services-common-yflow-surveys-no_surveys">
							No existen encuestas creadas y habilitadas
                        </yoizen:Message>
                        <asp:Panel ID="panelEnableSurveys" runat="server">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-enable">Habilitar Envío de Encuestas</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxEnableSurveys" ClientIDMode="Static" runat="server" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-enable-tip">Habilita el envío automático de encuestas al finalizar un caso atendido exclusivamente por yFlow
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <yoizen:Message ID="messageNoSurveysInTable" runat="server" Type="Warning" Style="display: none; margin-top: 10px" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-no_surveys_in_service">
								No existen encuestas en el servicio.
                            </yoizen:Message>
                            <div id="divWithSurveys" style="display: none">
                                <table class="reporte" cellspacing="0" rules="all" border="1" id="tableSurveys" style="width: 100%; border-collapse: collapse">
                                    <thead>
                                        <tr class="header">
                                            <th scope="col"><span data-i18n="globals-name">Nombre</span></th>
                                            <th scope="col">&nbsp;</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bodySurveys"></tbody>
                                </table>
                                <div style="display: none">
                                    <div id="divSurvey">
                                        <div class="scrollable-y" style="max-height: 350px; max-height: 350px">
                                            <div id="divSurveyConfiguration" class="subseccion" style="margin-top: 20px">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-title">Configuración de encuestas</h2>
                                                </div>
                                                <div class="contents">
                                                    <yoizen:Message ID="messageSurveyDisabled" runat="server" Type="Warning" Style="display: none;" ClientIDMode="Static" LocalizationKey="configuration-services-common-yflow-surveys-configuration-survey_disabled">
														El servicio tenía configurado una encuesta que está deshabilitada. Por favor seleccione otra
                                                    </yoizen:Message>
                                                    <table width="100%" border="0" class="uiInfoTable noBorder">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-survey">Encuesta a enviar</span>:</th>
                                                            <td class="data">
                                                                <asp:DropDownList ID="dropdownSurvey" runat="server" DataValueField="ID" DataTextField="Name" AppendDataBoundItems="true" ClientIDMode="Static">
                                                                    <asp:ListItem Value="-1" Text="Seleccione una encuesta" Selected="True" data-i18n="configuration-services-common-yflow-surveys-configuration-survey-select_one" />
                                                                </asp:DropDownList>
                                                                <span id="spanSurvey"></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation">Invitación para Participar</span>:</th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveyInvitation" ClientIDMode="Static" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="3" />
                                                                <yoizen:Message ID="messageSurveyInvitationFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																	<span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields">Debe utilizar el campo @@LINK@@ dentro del texto y puede optar por utilizar los otros mencionados a continuación</span>:<br />
																	<ul>
																		<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-link">Indica donde irá el link de la encuesta</span></li>
																		<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																		<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																	</ul>
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-queues-survey_invitation_whatsapp">Invitación para canales de WhatsApp</span>:</th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveyInvitationInteractive" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
                                                                <yoizen:Message ID="messageFilterEmailSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																	<span data-i18n="configuration-queues-survey_invitation_fields_whatsapp">Debe utilizar el siguiente campo dentro del texto de la invitación</span>:<br />
																	<ul>
																		<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_name">Indica el nombre del usuario al cual se le mandará el mensaje</span></li>
																		<li><span class='templatefieldname'>@@ALIAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-fields-field-user_alias">Indica el alias del usuario al cual se le mandará el mensaje</span></li>
																	</ul>
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label"><span data-i18n="configuration-queues-survey_button">Texto del boton para canales de WhatsApp</span>:</th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveyInvitationButton" ClientIDMode="Static" runat="server" MaxLength="200" Width="60%" TextMode="MultiLine" Rows="3" autotrim="true" />
                                                                <yoizen:Message ID="message1" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
																	<span data-i18n="configuration-queues-survey_invitation_fields_button">Debe utilizar el siguiente campo dentro del texto de la invitación</span>
                                                                </yoizen:Message>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator" id="queueSurveyExpiration">
                                                            <th class="label"><span data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration">Tiempo de expiración de la invitación (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyExpiration" ClientIDMode="Static" runat="server" MaxLength="5" Width="150" />
                                                                                <div id="timeInfoSurveyExpiration" class="timeinfo" related="textboxSurveyExpiration"></div>
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-invitation-expiration-tip">Define la cantidad de minutos que deben pasar a partir de la fecha de envío de la encuesta para considerar la invitación como expirada.
																				Ingrese cero para indicar que la invitación nunca expira
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            <div id="divSurveyBehaviour" class="subseccion">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-title">Condiciones para el envío</h2>
                                                </div>
                                                <div class="contents">
                                                    <table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate">Tasa de envío (%)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs">
                                                                                <asp:TextBox ID="textboxSurveySentRate" ClientIDMode="Static" runat="server" Width="50px" MaxLength="3" Style="text-align: right" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-rate-tip">Mide el porcentaje de casos sobre los que se va a generar el envío de la encuesta.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send">Tiempo de envío (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyTimeToSend" ClientIDMode="Static" runat="server" Width="50px" MaxLength="5" Style="text-align: right" />
                                                                                <div id="timeInfoSurveyTimeToSend" class="timeinfo" related="textboxSurveyTimeToSend"></div>
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-time_to_send-tip">Define la cantidad de minutos que deben pasar a partir del cierre del caso para que se envíe la encuesta al usuario.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label">
                                                                <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
																		"toggle": "popover",
																		"html": true,
																		"maxWidth": "400px",
																		"trigger": "hover",
																		"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-title",
																		"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-popover-content"
																	}'
                                                                    data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas"
                                                                    data-content="Opcionalmente se pueden asociar etiquetas a la respuesta predefinida, para que luego los agentes puedan encontrar esta respuesta predefinida cuando el caso del mensaje contenga las etiquetas seleccionadas"></span>
                                                                <span data-i18n="configuration-predefinedanswersbyqueue-related_tags">Etiquetas relacionadas</span>:
                                                            </th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveyTags" runat="server" ClientIDMode="Static" Width="100%" />
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group">Etiquetas</span>:</th>
                                                            <td class="data">
                                                                <asp:ListBox ID="listboxSurveyTagGroup" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
                                                                <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
																		"toggle": "popover",
																		"html": true,
																		"maxWidth": "400px",
																		"trigger": "hover",
																		"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group-popover-title",
																		"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags-group-popover-content"
																		}'
                                                                    data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count">Cantidad de mensajes en la conversación</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs">
                                                                                <asp:TextBox ID="textboxSurveyMessagesCount" ClientIDMode="Static" runat="server" MaxLength="4" Width="50px" Style="text-align: right" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-message_count-tip">Establece una mínima cantidad de mensajes dentro de la conversación del caso.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration">Duración mínima del caso</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyCaseDuration" ClientIDMode="Static" runat="server" Width="50px" MaxLength="4" Style="text-align: right" />
                                                                                <div id="timeInfoSurveyCaseDuration" class="timeinfo" related="textboxSurveyCaseDuration"></div>
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-case_duration-tip">Establece una mínima duración en minutos del caso.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            <div id="divSurveyIgnoreConditions" class="subseccion">
                                                <div class="title">
                                                    <h2 data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont-title">Condiciones para no realizar el envío</h2>
                                                </div>
                                                <div class="contents">
                                                    <table width="100%" border="0" class="uiInfoTable noBorder" runat="server">
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case">Encuestar si ya existe un caso nuevo</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:CheckBox ID="checkboxSurveySendIfNewCaseExists" runat="server" ClientIDMode="Static" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-send_if_new_case-tip">Establece si un caso será encuestado dependiendo de si ya existe un caso nuevo del mismo perfil
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey">No encuestar si la última envíada fue dentro de los últimos (minutos)</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyDontSendIfLastSurveyAfterMinutes" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" Style="text-align: right" />
                                                                                <div id="timeInfoDontSendIfLastSurveyAfterMinutes" class="timeinfo" related="textboxSurveyDontSendIfLastSurveyAfterMinutes"></div>
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_last_survey-tip">Especifica la cantidad de minutos a considerar hacia atrás para evaluar si no se envió ninguna encuesta al usuario del caso. En caso de que la última fecha de envío
																				de encuesta sea posterior a la fecha actual restando los minutos configurados, no se enviará la encuesta para dicho caso.
																				En caso de configurar cero, no se aplicará esta condición. El máximo valor son 172800 (4 meses)
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly">No encuestar si ya se enviaron encuestas en el mes</span>:</th>
                                                            <td class="data">
                                                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="vMid prs timeinfo">
                                                                                <asp:TextBox ID="textboxSurveyDontSendTotalSendMonthly" ClientIDMode="Static" runat="server" Width="100px" MaxLength="6" Style="text-align: right" />
                                                                            </td>
                                                                            <td class="vMid pls" data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-dont_send_total_send_monthly-tip">Especifica la cantidad de encuestas que pueden enviarse en el més para cada cliente.
																					En caso de configurar cero, no se aplicará esta condición. El máximo valor son 30 encuestas al mes.
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags">Etiquetas que no debe incluir</span>:</th>
                                                            <td class="data">
                                                                <asp:TextBox ID="textboxSurveysIgnoreTags" runat="server" ClientIDMode="Static" Width="100%" />
                                                                <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas que no debe incluir" data-i18n-popover='{
																	"toggle": "popover",
																	"html": true,
																	"maxWidth": "400px",
																	"trigger": "hover",
																	"title": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags",
																	"content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags-tooltip"
																	}'
                                                                    data-content="Seleccione las etiquetas que deberán tener los casos cerrados para no ser considerados para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
                                                            </td>
                                                        </tr>
                                                        <tr class="dataRow dataRowSeparator">
                                                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags_group">Etiquetas</span>:</th>
                                                            <td class="data">
                                                                <asp:ListBox ID="listboxSurveyTagGroupToIgnore" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Multiple" Width="450px" />
                                                                <span class="fa fa-lg fa-info-circle" style="margin-left: 5px" data-i18n-popover='{
																	 "toggle": "popover",
																	 "html": true,
																	 "maxWidth": "400px",
																	 "trigger": "hover",
																	 "title": "configuration-services-common-yflow-surveys-configuration-send_conditions-tags_group-popover-title",
																	 "content": "configuration-services-common-yflow-surveys-configuration-send_conditions-not_including_tags_group-popover-content"
																	 }'
                                                                    data-toggle="popover" data-html="true" data-maxwidth="400px" data-trigger="hover" title="Etiquetas" data-content="Seleccione las etiquetas que deberán tener los casos cerrados para ser considerado para el envío de la encuesta. En caso de no seleccionar etiquetas, no se evaluará la condición para los casos cerrados"></span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="divSurveyError" class="validationerror" style="display: none"><span></span></div>
                                        <div class="buttons">
                                            <label class="uiButton uiButtonLarge uiButtonConfirm">
                                                <button type="button" data-i18n="globals-accept" onclick="ValidateSurvey()">Aceptar</button>
                                            </label>
                                            <label class="uiButton uiButtonLarge">
                                                <button type="button" data-i18n="globals-cancel" onclick="ReturnTableSurvey()">Cancelar</button>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSurveys" data-i18n="configuration-serviceswhatsapp-configuration-surveys-error" />
                            </div>
                            <div id="divNewSurvey" class="buttons">
                                <label class="uiButton uiButtonLarge uiButtonConfirm">
                                    <button type="button" onclick="AddNewSurvey()" data-i18n="configuration-surveys-new_survey">Nueva Encuesta</button>
                                </label>
                            </div>
                        </asp:Panel>
                    </div>
                </asp:Panel>
            </div>
        </div>
        <div id="divWhatsappServiceMedia" class="hiddenAsGateway">
            <table width="100%" border="0" class="uiInfoTable noBorder">
                <tr class="dataRow dataRowSeparator">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-allow_sending">Permitir envío</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:CheckBox ID="checkboxWhatsappAllowToSendMedia" runat="server" />
                                    </td>
                                    <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-allow_sending-tip">Este parámetro indica si el agente puede enviar archivos multimedia acompañando al mensaje
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-maximun_size">Máximo tamaño (en MB)</span>:</th>
                    <td class="data">
                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                            <tbody>
                                <tr>
                                    <td class="vMid prs">
                                        <asp:TextBox ID="textboxWhatsappMaxSizeAttachment" runat="server" MaxLength="1" Width="80" TextMode="Number" />
                                        <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWhatsappMaxSizeAttachment" />
                                    </td>
                                    <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-maximun_size-tip">Este parámetro indica el tamaño máximo permitido en MB para el total de archivos multimedia que se envían por Whatsapp.
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr class="dataRow dataRowSeparator" rel="AllowToSendAttachments">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-allowed_file_types">Tipos de archivos permitidos</span>:</th>
                    <td class="data">
                        <div class="subseccion collapsable">
                            <div class="title">
                                <h2 data-i18n="configuration-serviceswhatsapp-multimedia_files_options-title">Opciones para los archivos multimedia</h2>
                            </div>
                            <div class="contents">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWhatsAppMultimediaOptions" />
                                <table width="100%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-image"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeImages" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeImages" data-i18n="globals-file_types-images">Archivos de imagen (JPG, JPEG, PNG)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-audio"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeAudio" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeAudio" data-i18n="globals-file_types-audio">Archivos de audio (MP3, WAV)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-video"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeVideo" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeVideo" data-i18n="globals-file_types-video">Archivos de video (MP4, WMV, MOV)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-pdf"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypePDF" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypePDF" data-i18n="globals-file_types-pdf">Archivos PDF</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-word"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeWord" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeWord" data-i18n="globals-file_types-doc">Archivos de Word (DOC, DOCX)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-alt"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeText" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeText" data-i18n="globals-file_types-text">Archivos de texto</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeAllFiles" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeAllFiles" data-i18n="globals-file_types-all">Todos los archivos (*.*)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-excel"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeExcel" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeExcel" data-i18n="globals-file_types-excel">Archivos de Excel (XLS, XLSX)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-powerpoint"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypePPT" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypePPT" data-i18n="globals-file_types-ppt">Archivos de PowerPoint (PPT, PPTX)</asp:Label>
                                        </td>
                                    </tr>
                                    <tr class="dataRow">
                                        <td>
                                            <span class="fa fa-lg fa-file-archive"></span>
                                            <asp:CheckBox ID="checkboxWhatsappAcceptedTypeZip" runat="server" />
                                            <asp:Label runat="server" AssociatedControlID="checkboxWhatsappAcceptedTypeZip" data-i18n="globals-file_types-zip">Archivos comprimidos (ZIP)</asp:Label>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="dataRow" rel="AllowToSendAttachments">
                    <th class="label withdescription"><span data-i18n="configuration-serviceswhatsapp-default_extension">Extensión por defecto</span>:</th>
                    <td class="data">
                        <asp:HiddenField ID="hiddenWhatsappDefaultExtension" runat="server" ClientIDMode="Static"></asp:HiddenField>
                        <select id="dropdownlistWhatsappDefaultExtension"></select>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divNotifications">
            <div class="subseccion collapsable">
                <div class="title">
                    <h2 data-i18n="configuration-serviceswhatsapp-inactive_minutes-title">Minutos con inactividad</h2>
                </div>
                <div class="contents">
                    <yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-serviceswhatsapp-inactive_minutes_info">
						Este mail se mandará una vez cuando se detecte que luego de pasado cierta cantidad de minutos, no se hayan recibido novedades de Whatsapp.
                    </yoizen:Message>
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-minutes">Minutos</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxWhatsappMinutesForInactivity" runat="server" MaxLength="4" Width="80" TextMode="Number" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsappMinutesForInactivity" />
                                <asp:CompareValidator runat="server" ControlToValidate="textboxWhatsappMinutesForInactivity" Type="Integer" Operator="GreaterThan" ValueToCompare="0" />
                                <div class="timeinfo" related="textboxWhatsappMinutesForInactivity"></div>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
                            <td class="data">
                                <asp:HiddenField ID="hiddenInactivityDetectedConnection" runat="server" ClientIDMode="Static" />
                                <select id="listboxInactivityDetectedConnection">
                                    <option value="" selected="selected" data-i18n="globals-email_default">Por defecto</option>
                                </select>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-mail_subject">Asunto del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxWhatsappInactivityDetectedEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsappInactivityDetectedEmailSubject" />
                                <yoizen:Message ID="messageWhatsappInactivityDetectedEmailSubjectFields" runat="server" Type="Information" Small="true" ClientIDMode="Static" Style="margin-top: 10px">
									<span data-i18n="configuration-serviceswhatsapp-inactive_minutes_subject_template">Puede utilizar los siguientes campos dentro del asunto</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@CUENTA@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-inactive_minutes_subject-field-phone_numer">Indica el número de teléfono del servicio de Whatsapp</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-mail_recipients">Emails destinatarios</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxWhatsappInactivityDetectedEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsappInactivityDetectedEmails" />
                                <asp:RegularExpressionValidator runat="server" ControlToValidate="textboxWhatsappInactivityDetectedEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
                            </td>
                        </tr>
                        <tr class="dataRow dataRowSeparator">
                            <th class="label"><span data-i18n="configuration-serviceswhatsapp-mail_template">Plantilla del Email</span>:</th>
                            <td class="data">
                                <asp:TextBox ID="textboxWhatsappInactivityDetectedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" ClientIDMode="Static" />
                                <asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsappInactivityDetectedEmailTemplate" />
                                <yoizen:Message ID="messageWhatsappInactivityDetectedEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
									<span data-i18n="configuration-serviceswhatsapp-inactive_minutes_mail_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
									<ul>
										<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-inactive_minutes_mail-field-date_and_hour">Indica la fecha y hora cuando ocurrió el primer error</span></li>
										<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-inactive_minutes_mail-field-service_name">Indica el nombre del servicio que tiene el error</span></li>
										<li><span class='templatefieldname'>@@MINUTOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-serviceswhatsapp-edit-inactive_minutes_mail-field-inactive_minutes">Indica la cantidad de minutos que pasaron sin actividad en Whatsapp</span></li>
									</ul>
                                </yoizen:Message>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div id="divCases">
            <div class="seccion">
                <div class="title">
                    <h2 data-i18n="configuration-systemsettings-case_configuration-title">Configuraciones de casos</h2>
                </div>
                <div class="contents">
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator hiddenAsGateway">
                            <th class="label withdescription" style="width: 200px !important"><span data-i18n="configuration-services-common-cases_override">Sobreescribir configuración de casos</span>:</th>
                            <td class="data">
                                <table class="uiGrid" cellspacing="0" cellpadding="0">
                                    <tbody>
                                        <tr>
                                            <td class="vMid prs">
                                                <asp:CheckBox ID="checkboxCasesOverrideSystemSettings" runat="server" ClientIDMode="Static" />
                                            </td>
                                            <td class="vMid pls" data-i18n="configuration-services-common-cases_override-tip">Este parámetro indica si el servicio puede sobreescribir la configuración de Párámetros del sistema con respecto a los casos 
												y así poder definir dintintos valores para el tratamiento de casos.
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <div class="subseccion" id="divCasesOverride">
                        <div class="title">
                            <h2 data-i18n="configuration-services-common-cases_override-title">Configuraciones específicas de casos</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                                    <th class="label withdescription" style="width: 250px !important"><span data-i18n="configuration-systemsettings-verify_last_queue">Verificar última cola</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxCheckLastQueueOfOpenCase" runat="server" ClientIDMode="Static" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-systemsettings-verify_last_queue-tip">Este parámetro indica si el sistema verificará ante cada mensaje nuevo si ese usuario tiene
														un caso abierto y su último mensaje ingresó en otra cola (que no corresponde a la cola por defecto asignada al servicio). 
														De ser así se tomará como cola por defecto la cola del último mensaje
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator hiddenAsGateway">
                                    <th class="label withdescription"><span data-i18n="configuration-systemsettings-ignore_last_queue">Ignorar última cola si el último mensaje entrante fue movido por SL</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxIgnoreLastQueueForSLMovedMessage" runat="server" ClientIDMode="Static" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-systemsettings-ignore_last_queue-tip">Este parámetro indica si el sistema ignorará la última cola del caso en caso de que el último mensaje
														que haya sido escrito por el usuario fue movido por alguna acción del Service Level/Vencimiento.
														En caso de ignorar, se utilizará la cola por defecto del servicio por el cual ingresó el mensaje
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-time_to_close">Minutos para cierre automático</span>:
                                        <br />
                                    </th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs timeinfo">
                                                        <asp:TextBox ID="textboxMaxElapsedMinutesToCloseCases" runat="server" MaxLength="5" Width="100" TextMode="Number" Style="text-align: right" ClientIDMode="Static" />
                                                        <div class="timeinfo" related="textboxMaxElapsedMinutesToCloseCases"></div>
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-tip">Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario y el sistema. Pasado
														esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-auto_reply_close_case">Responder en cierre automático</span>:
                                    </th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top: 10px">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs timeinfo">
                                                        <asp:CheckBox ID="checkboxReplyInCloseCase" runat="server" ClientIDMode="Static" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-systemsettings-auto_reply_close_case-tip">Este parámetro indica si se evniará una respuesta al ultimo mensaje del caso antes de cerrarlo de forma automatica.
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trAutoReplyInCloseCaseText">
                                    <th class="label">
                                        <span data-i18n="configuration-systemsettings-auto_reply_close_case">Respuesta en cierre automático</span>:
                                    </th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxAutoReplyInCloseCaseText" runat="server" TextMode="MultiLine" Width="100%" Rows="4" ClientIDMode="Static" />
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-apply_tag_close_case">Aplicar etiqueta en cierre automático</span>:
                                    </th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxTagCloseCase" runat="server" ClientIDMode="Static" Width="100%" />
                                    </td>
                                </tr>
                                <asp:PlaceHolder ID="placeholderYFlowCasesRelated" runat="server">
                                    <tr class="dataRow dataRowSeparator" id="trMaxElapsedMinutesToCloseYFlowCases">
                                        <th class="label withdescription"><span data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case">Minutos para cierre automático de un caso atendido únicamente por yFlow </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs timeinfo">
                                                            <asp:TextBox ID="textboxMaxElapsedMinutesToCloseYFlowCases" runat="server" MaxLength="5" Width="100" TextMode="Number" Style="text-align: right" ClientIDMode="Static" Text="0" />
                                                            <div class="timeinfo" related="textboxMaxElapsedMinutesToCloseYFlowCases"></div>
                                                        </td>
                                                        <td class="vMid pls" data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case-tip">Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario e yFlow. Pasado
															esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
															Un caso únicamete de yFlow es cuando nunca fue derivado a un agente
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr class="dataRow dataRowSeparator" id="trInvokeYFlowWhenClosedCases">
                                        <th class="label withdescription"><span data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case">Invocar a yFlow al momento de cerrar el caso </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs">
                                                            <asp:CheckBox ID="checkboxInvokeYFlowWhenClosedCases" runat="server" ClientIDMode="Static" />
                                                        </td>
                                                        <td class="vMid pls" data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case-tip">Este parámetro indica si se deberá invocar a yFlow cuando el cierre automático de un caso atendido únicamente por yFlow se ejecute.
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </asp:PlaceHolder>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-time_to_close-hsm-cases">Minutos para cierre automático de casos HSM</span> <span class="fab fa-lg fa-whatsapp-square"></span>:
                                        <br />
                                    </th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs timeinfo">
                                                        <asp:TextBox ID="textboxMaxElapsedMinutesToCloseHsmCases" runat="server" MaxLength="4" ClientIDMode="Static" TextMode="Number" Style="text-align: right" Width="100" />
                                                        <div class="timeinfo" related="textboxMaxElapsedMinutesToCloseHsmCases"></div>
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-hsm-case-tip">Este parámetro indica la cantidad máxima de minutos que pueden estar los casos HSM abiertos sin tener interacciones entre el usuario y el sistema. Pasado
														esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 24 horas (1440 Minutos).
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div class="validationerror" style="display: none">
                                            <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateHsmCloseCases" SkinID="validationerror" />
                                        </div>
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label withdescription">
                                        <span data-i18n="configuration-systemsettings-apply_tag_close_hsm_case">Aplicar etiqueta en cierre automático de casos HSM</span> <span class="fab fa-lg fa-whatsapp-square"></span>:
                                    </th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxTagOnHsmCases" runat="server" ClientIDMode="Static" Width="100%" />
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateCasesSettings" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="divVideo">
            <asp:Panel ID="panelVideo" runat="server">
                <table width="100%" border="0" class="uiInfoTable noBorder">
                    <tr class="dataRow dataRowSeparator">
                        <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-serviceswhatsapp-video-enable_video">Habilitar uso de video</span>:</th>
                        <td class="data">
                            <asp:CheckBox ID="checkboxEnableVideo" ClientIDMode="Static" runat="server" />
                        </td>
                    </tr>
                </table>
            </asp:Panel>
        </div>
        <div id="divVoiceCalls">
            <asp:Panel ID="panelVoiceCalls" runat="server">
                <yoizen:Message ID="messageVoiceCallsNotAvailable" runat="server" Type="Warning" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-only_yoizen" ClientIDMode="Static">
					Las llamadas de Whatsapp sólo están disponibles para la integración nativa de Yoizen
                </yoizen:Message>
                <div id="divVoiceCallsAvailable">
                    <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-available">
						Las llamadas de Whatsapp permite a los agentes hablar mediante la voz con los usuarios
                    </yoizen:Message>
                    <table width="100%" border="0" class="uiInfoTable noBorder">
                        <tr class="dataRow dataRowSeparator">
                            <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-serviceswhatsapp-voice_calls-enable">Habilitar llamadas</span>:</th>
                            <td class="data">
                                <asp:CheckBox ID="checkboxVoiceCallsEnabled" runat="server" ClientIDMode="Static" />
                            </td>
                        </tr>
                    </table>
                    <div class="seccion" id="divVoiceCallsEnabled">
                        <div class="title">
                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-title">Configuración de llamadas</h2>
                        </div>
                        <div class="contents">
                            <div class="subseccion">
                                <div class="title">
                                    <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-invite_message-title">Mensaje de invitación a llamada</h2>
                                </div>
                                <div class="contents">
                                    <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-invite_message-tip">
										El mensaje de invitación será el que el agente enviará al usuario para invitarlo a iniciar una llamada
                                    </yoizen:Message>
                                    <div id="divVoiceCallInteractiveMessageInvite" class="whatsapp-interactive_message">
                                        <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageInvite" runat="server" ClientIDMode="Static" />
                                    </div>
                                    <div class="validationerror" style="display: none">
                                        <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallsInteractiveMessageInvite" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-invite_message-error" />
                                    </div>
                                </div>
                            </div>
                            <div class="subseccion">
                                <div class="title">
                                    <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-title">Mensajes de rechazo</h2>
                                </div>
                                <div class="contents">
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-user_case_not_assigned-title">Rechazo porque ningún mensaje del usuario está asignado</h2>
                                        </div>
                                        <div class="contents">
                                            <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-user_case_not_assigned-tip">
												El mensaje será enviado cuando se rechace una llamada porque no hay ningún mensaje del usuario asignado a un agente
                                            </yoizen:Message>
                                            <div id="divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned" class="whatsapp-interactive_message">
                                                <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned" runat="server" ClientIDMode="Static" />
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallInteractiveMessageRejectUsersCaseNotAssigned" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-user_case_not_assigned-error" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_not_available-title">Rechazo por agente no disponible</h2>
                                        </div>
                                        <div class="contents">
                                            <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_not_available-tip">
												El mensaje será enviado cuando se rechace una llamada porque el agente ya no se encuentra conectado
                                            </yoizen:Message>
                                            <div id="divVoiceCallInteractiveMessageRejectAgentNotAvailable" class="whatsapp-interactive_message">
                                                <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable" runat="server" ClientIDMode="Static" />
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallInteractiveMessageRejectAgentNotAvailable" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_not_available-error" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_with_another_call-title">Rechazo por agente con otra llamada</h2>
                                        </div>
                                        <div class="contents">
                                            <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_with_another_call-tip">
												El mensaje será enviado cuando se rechace una llamada porque el agente se encuentra con otra llamada
                                            </yoizen:Message>
                                            <div id="divVoiceCallInteractiveMessageRejectAgentWithAnotherCall" class="whatsapp-interactive_message">
                                                <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall" runat="server" ClientIDMode="Static" />
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallInteractiveMessageRejectAgentWithAnotherCall" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-agent_with_another_call-error" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_with_current_call-title">Rechazo por caso con llamada activa</h2>
                                        </div>
                                        <div class="contents">
                                            <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_with_current_call-tip">
												El mensaje será enviado cuando se rechace una llamada porque el el caso ya tiene una llamada activa
                                            </yoizen:Message>
                                            <div id="divVoiceCallInteractiveMessageRejectCaseWithCurrentCall" class="whatsapp-interactive_message">
                                                <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall" runat="server" ClientIDMode="Static" />
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallInteractiveMessageRejectCaseWithCurrentCall" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_with_current_call-error" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="subsubseccion">
                                        <div class="title">
                                            <h2 data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_without_invite-title">Rechazo por caso sin una invitación</h2>
                                        </div>
                                        <div class="contents">
                                            <yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_without_invite-tip">
												El mensaje será enviado cuando se rechace una llamada porque el el caso no tuvo una invitación a hablar
                                            </yoizen:Message>
                                            <div id="divVoiceCallInteractiveMessageRejectCaseWithoutInvite" class="whatsapp-interactive_message">
                                                <asp:HiddenField ID="hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite" runat="server" ClientIDMode="Static" />
                                            </div>
                                            <div class="validationerror" style="display: none">
                                                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCallInteractiveMessageRejectCaseWithoutInvite" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-rejection_messages-case_without_invite-error" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator" style="display: none">
                                    <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-serviceswhatsapp-voice_calls-allow_agents_to_send">Permitir a los agentes enviar invitación</span>:</th>
                                    <td class="data">
                                        <table class="uiGrid" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="vMid prs">
                                                        <asp:CheckBox ID="checkboxVoiceCallsAllowAgentsToSendInteractiveMessage" runat="server" ClientIDMode="Static" />
                                                    </td>
                                                    <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-voice_calls-allow_agents_to_send-tip">Este parámetro indica si los agentes en cualquier momento pueden enviar el mensaje para iniciar una llamada
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <asp:PlaceHolder ID="placeholderVoiceCallAllowRecording" runat="server" Visible="true">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label withdescription" style="width: 250px !important;"><span data-i18n="configuration-serviceswhatsapp-voice_calls-allow_recording">Grabación de llamadas</span>:</th>
                                        <td class="data">
                                            <table class="uiGrid" cellspacing="0" cellpadding="0">
                                                <tbody>
                                                    <tr>
                                                        <td class="vMid prs timeinfo">
                                                            <asp:CheckBox ID="checkboxVoiceCallsAllowRecording" runat="server" ClientIDMode="Static" />
                                                        </td>
                                                        <td class="vMid pls" data-i18n="configuration-serviceswhatsapp-voice_calls-allow_recording-tip">Este parámetro indica si los agentes en cualquier momento pueden enviar el mensaje para iniciar una llamada
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                </asp:PlaceHolder>
                            </table>
                        </div>
                    </div>
                </div>
            </asp:Panel>
            <div class="validationerror" style="display: none">
                <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateVoiceCall" data-i18n="configuration-serviceswhatsapp-voice_calls-configuration-act_as_chat_error" />
            </div>
        </div>
    </div>
    <div class="buttons">
        <asp:HiddenField ID="hiddenServiceToCopy" runat="server" ClientIDMode="Static" />
        <label class="uiButton uiButtonLarge">
            <button type="button" data-i18n="configuration-services-copy_from_service" onclick="ShowCopyServiceCommon('Whatsapp')">Copiar desde otro servicio</button>
        </label>
        <asp:Button ID="buttonCopyService" runat="server" Text="Aceptar" OnClick="buttonCopyService_Click" CausesValidation="false" Style="display: none" ClientIDMode="Static" />
        <label class="uiButton uiButtonLarge uiButtonConfirm">
            <asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" data-i18n="globals-accept" />
        </label>
        <label class="uiButton uiButtonLarge">
            <asp:Button ID="buttonCancel" runat="server" Text="Cancelar" OnClick="buttonCancel_Click" CausesValidation="false" data-i18n="globals-cancel" />
        </label>
    </div>
</asp:Content>
