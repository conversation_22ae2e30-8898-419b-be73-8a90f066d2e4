﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.SessionState;
using Yoizen.Common;

namespace FacebookCallback.Business
{
	public static class LogManager
	{
		#region Fields

		private static readonly string RootPath;
		private static string[] headersToIgnore = new string[] { 
			"Proxy-Connection"
			, "X-Hub-Signature"
			, "hash"
			, "Accept"
			, "Cookie"
			, "Connection"
			, "Host"
			, "Max-Forwards"
			, "X-LiveUpgrade"
			, "DISGUISED-HOST"
			, "X-ARR-LOG-ID"
			, "X-SITE-DEPLOYMENT-ID"
			, "X-Original-URL"
			, "X-Forwarded-For"
			, "X-AppService-Proto"
			, "X-ARR-SSL"
			, "X-WAWS-Unencoded-URL"
			, "WAS-DEFAULT-HOSTNAME"
			, "x-dynatrace"
			, "x-correlation-id"
			, "traceparent"
			, "tracestate"
			, "X-Forwarded-TlsVersion"
			, "ApplicationInsights-RequestTrackingTelemetryModule-RootRequest-Id"
			, "X-Forwarded-Proto"
			, "x-datadog-trace-id"
			, "x-datadog-parent-id"
			, "x-datadog-sampling-priority"
			, "x-datadog-tags"
			, "x-hub-signature-256"
			, "cf-ray"
			, "cdn-loop"
			, "cf-connecting-ip"
			, "cf-ipcountry"
			, "cf-visitor"
			, "CLIENT-IP"
			, "facebook-api-version"
			, "Accept-Encoding"
			, "x-api-key"
			, "User-Agent"
			, "trace-id"
			, "span-id"
			, "Authorization"
			, "auth-bm-token"
		};

		#endregion

		#region Constructors

		static LogManager()
		{
			try
			{
				RootPath = HttpContext.Current.Server.MapPath("~/Logs/");
			}
			catch
			{
				try
				{
					RootPath = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location), "Logs");
				}
				catch
				{
					RootPath = Path.Combine(@"D:\home\site\wwwroot\", "Logs");
				}
			}
			Yoizen.Common.Tracer.TraceInfo("Carpeta raíz: {0}", RootPath);
		}

		#endregion

		#region Public Methods

		public static void ProcessFolder()
		{	
			try
			{
				var dir = new DirectoryInfo(RootPath);
				ProcessFolder(dir);
			}
			catch { }

			try
			{
				var secondPath = Path.Combine(TheSystem.Instance.StoragePath, "Logs");
				var dir = new DirectoryInfo(secondPath);
				ProcessFolder(dir);
			}
			catch { }
		}

		public static void ProcessFolder(DirectoryInfo dir)
		{
			if (dir.Exists)
			{
				Tracer.TraceInfo("Se borrarán los archivos .log del directorio {0}", dir.FullName);

				var files = dir.GetFiles("*.log");
				var now = DateTime.Now;
				foreach (var file in files)
				{
					if (file.LastWriteTime.AddDays(4) < now)
					{
						try
						{
							file.Delete();
						}
						catch { }
					}
				}

				Tracer.TraceInfo("Se borraron los archivos .log del directorio {0}", dir.FullName);
			}
			else
			{
				Tracer.TraceInfo("El directorio {0} no existe", dir.FullName);
			}
		}

		public static string ConvertHeadersToString(System.Net.Http.Headers.HttpRequestHeaders headers, bool includeAllHeaders = false)
		{
			IEnumerable<KeyValuePair<string, IEnumerable<string>>> filteredHeaders;
			
			if (includeAllHeaders)
				filteredHeaders = headers;
			else
				filteredHeaders = headers.Where(h => !headersToIgnore.Contains(h.Key, StringComparer.InvariantCultureIgnoreCase));

			if (filteredHeaders.Any())
			{
				var sb = new StringBuilder();
				foreach (var header in filteredHeaders)
				{
					sb.Append(header.Key);
					sb.Append(": ");

					int index = 0;
					foreach (var value in header.Value)
					{
						if (index > 0)
							sb.Append(", ");
						sb.Append(value);

						index++;
					}
					sb.AppendLine();
				}

				if (sb.Length > 0)
					sb.Insert(0, Environment.NewLine);

				return sb.ToString();
			}
			else
			{
				return string.Empty;
			}
		}

		#endregion
	}
}