﻿/// <reference path="../Scripts/jquery-3.5.1.js" />
/// <reference path="../Scripts/Master.js" />
/// <reference path="ServicesCommon.js" />

var $tabsInstagram;
var $divInstagramServiceMedia;
var $checkboxInstagramAllowToSendMedia;

var $hiddenUserAccessToken;
var $textboxInstagramUserAccessToken;
var $messageInstagramAccountError;
var $trInstagramAccount;
var $divInstagramPage;
var $messageInstagramNoPages;
var $divInstagramRedirectUri;
var $inputInstagramUrl;
var $buttonEnterInstagramUrl;
var $messageInstagramCouldntValidateAccessToken;
var $divInstagramUser;
var $divInstagramLoginButton;

var $dropdownlistUseYFlow;
var $liTabAdvancedConfiguration;
var $liTabAdvancedConfigurationYFlow;
var $divAdvancedConfiguration;
var $divAdvancedConfigurationYFlow;
var $hiddenFlow;
var $selectFlowToUse;
var $anchorFlowsReload;
var $spanFlowID;
var $spanFlowName;
var $spanFlowVersion;
var $hiddenFlowContingency;
var $selectFlowContingencyToUse;
var $anchorFlowsContingencyReload;
var $spanFlowContingencyID;
var $spanFlowContingencyName;
var $spanFlowContingencyVersion;
var $hiddenFlowQueueTransfersByKey;
var $tableFlowQueueTransfersByKey;
var $anchorFlowQueueTransfersByKeyAdd;
var $listboxFlowShareEnqueuedMessagesFromQueues;
var $listboxFlowShareConnectedAgentsFromQueues;

var $listboxInstagramInactivityDetectedConnection;
var $hiddenInstagramInactivityDetectedConnection;

var $hiddenInstagramOAuthErrorConnection;
var $listboxInstagramOAuthErrorConnection;

var $liTabBehaviour;
var $divWhatsappBehaviour;
var $checkboxAutoReplyBeforeMaxTimeToAnswer;
var $textboxAutoReplyBeforeMaxTimeToAnswerText;
var $messageAutoReplyBeforeMaxTimeToAnswerText;
var $textboxAutoReplyBeforeMaxTimeToAnswerMinutes;
var $checkboxAutoReplyBeforeCloseCase;
var $textboxAutoReplyBeforeCloseCaseText;
var $messageAutoReplyBeforeMaxTimeToAnswerText;
var $textboxAutoReplyBeforeCloseCaseMinutes;
var $dropdownlistAllowToSendHSM;
var $trAllowAgentsToSendHSM;
var $divHSMTemplates;
var $hiddenHSMTemplates;
var $tableHSMTemplates;
var $thHSMTemplateIntegrationType2;
var $tbodyHSMTemplates;
var $anchorHSMTemplatesAdd;
var	$divCapiService;

$(function () {
	InitializeInstagram();

	$('a[rel="_blank"]').click(function () {
		window.open($(this).attr('href'));
		return false;
	});

	LoadCompositedElements();

	if (window.addEventListener) {
		addEventListener("message", InstagramTokenCallbackMessage, false);
	}
	else {
		attachEvent("onmessage", InstagramTokenCallbackMessage);
	}

	if (typeof (creatingService) !== 'undefined' && creatingService) {
		ConfigureInstagram();
	}

	$divCapiService = $('#divCapiService');
	$divCapiService.toggle(enabledCapi === true);
});

function InstagramTokenCallbackMessage(event) {
	if (typeof (InstagramUrlToken) != 'undefined') {
		if (!InstagramUrlToken.toLowerCase().startsWith(event.origin.toLowerCase()))
			return;
	}
	
	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var uri = new URI(event.data);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	var $messageInstagramUrlInvalid = $('#messageInstagramUrlInvalid');
	var $td = $('td.text', $messageInstagramUrlInvalid)

	if (!validUri) {
		$td.html('La URL ingresada es inválida');
		$divInstagramWizardError.show();
		$.colorbox.resize();
		return;
	}

	$messageInstagramUrlInvalid.hide();
	$divInstagramRedirectUri.show();
	$inputInstagramUrl.val(event.data);
	$buttonEnterInstagramUrl.hide();

	$.colorbox.resize();

	ValidateUserAccessToken(accessToken, expires);
}

function ShowInstagramUrlInput() {
	$divInstagramRedirectUri.show();
	$buttonEnterInstagramUrl.hide();
	$.colorbox.resize();
}

function RevalidateInstagramUrl() {
	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var $messageInstagramUrlInvalid = $('#messageInstagramUrlInvalid');
	var $td = $('td.text', $messageInstagramUrlInvalid)

	var url = $inputInstagramUrl.val();
	if (url.length === 0) {
		$td.html($.i18n("configuration-servicesinstagram-invalid_url"));
		$messageInstagramUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	var uri = new URI(url);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	if (!validUri) {
		$td.html($.i18n("configuration-servicesinstagram-invalid_url"));
		$messageInstagramUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	$.colorbox.resize();
	$messageInstagramUrlInvalid.hide();
	ValidateUserAccessToken(accessToken, expires);
}

function InitializeInstagram() {
	$tabsInstagram = $('#tabsInstagram');
	var $hiddenTab = $('#hiddenTab');

	$tabsInstagram.tabs({
		activate: function (event, page) {
			if (event.type == 'tabsactivate') {
				var $divTab;
				if ((page.newPanel instanceof jQuery)) {
					$divTab = page.newPanel;
				}
				else {
					$divTab = $(page.newPanel.selector);
				}
				var tabId = $divTab.get(0).id;

			    $hiddenTab.val(tabId);
    
			    if (history.pushState) {
				    history.pushState(null, null, '#' + tabId);
			    }
			    else {
				    location.hash = '#' + tabId;
			    }

				if (tabId === 'divNotifications') {
					var editor = $textboxInstagramOAuthErrorEmailTemplate.cleditor()[0];
					editor.refresh();
					editor = $textboxInstagramInactivityDetectedEmailTemplate.cleditor()[0];
					editor.refresh();
				}
				else if (tabId === 'divAdvancedConfigurationYFlow') {
					if (typeof (page.newPanel.get(0).firstTime) === 'undefined') {
						ReloadFlows();
						//ReloadFlowsContingency();
						page.newPanel.get(0).firstTime = false;
					}
				}
			}
		}
	});

	registerTabs($tabsInstagram);

	$liTabBehaviour = $('#liTabBehaviour');
	$divBehaviour = $('#divBehaviour');
	$checkboxAutoReplyBeforeMaxTimeToAnswer = $('#checkboxAutoReplyBeforeMaxTimeToAnswer');
	$textboxAutoReplyBeforeMaxTimeToAnswerText = $('#textboxAutoReplyBeforeMaxTimeToAnswerText');
	$messageAutoReplyBeforeMaxTimeToAnswerText = $('#messageAutoReplyBeforeMaxTimeToAnswerText');
	CallValidationFields($textboxAutoReplyBeforeMaxTimeToAnswerText, $messageAutoReplyBeforeMaxTimeToAnswerText);
	$textboxAutoReplyBeforeMaxTimeToAnswerMinutes = $('#textboxAutoReplyBeforeMaxTimeToAnswerMinutes');
	$checkboxAutoReplyBeforeCloseCase = $('#checkboxAutoReplyBeforeCloseCase');
	$textboxAutoReplyBeforeCloseCaseText = $('#textboxAutoReplyBeforeCloseCaseText');
	$messageAutoReplyBeforeMaxTimeToAnswerText = $('#messageAutoReplyBeforeCloseCaseText');
	$textboxAutoReplyBeforeCloseCaseMinutes = $('#textboxAutoReplyBeforeCloseCaseMinutes');
	$dropdownlistAllowToSendHSM = $('#dropdownlistAllowToSendHSM');
	$trAllowAgentsToSendHSM = $('#trAllowAgentsToSendHSM');
	$divHSMTemplates = $('#divHSMTemplates');
	$hiddenHSMTemplates = $('#hiddenHSMTemplates');
	$tableHSMTemplates = $('#tableHSMTemplates');
	$thHSMTemplateIntegrationType2 = $('#thHSMTemplateIntegrationType2');
	$tbodyHSMTemplates = $('tbody', $tableHSMTemplates);
	$anchorHSMTemplatesAdd = $('#anchorHSMTemplatesAdd');

	$dropdownlistAllowToSendHSM.change(function () {
		var allow = $dropdownlistAllowToSendHSM.val() === '1';
		$trAllowAgentsToSendHSM.toggle(allow);
		$divHSMTemplates.toggle(allow);
	}).trigger('change');

	$anchorHSMTemplatesAdd.click(AddHSMTemplateRow);

	var templates = $hiddenHSMTemplates.val();
	if (templates !== null && templates.length > 0) {
		templates = JSON.parse(templates);
		for (var i = 0; i < templates.length; i++) {
			var template = templates[i];
			AddHSMTemplateRow(null, template);
		}
	}

	$dropdownlistUseYFlow = $('#dropdownlistUseYFlow');
	$liTabAdvancedConfiguration = $('#liTabAdvancedConfiguration');
	$liTabAdvancedConfigurationYFlow = $('#liTabAdvancedConfigurationYFlow');
	$divAdvancedConfiguration = $('#divAdvancedConfiguration');
	$divAdvancedConfigurationYFlow = $('#divAdvancedConfigurationYFlow');
	$anchorFlowsReload = $('#anchorFlowsReload');
	$anchorFlowsContingencyReload = $('#anchorFlowsContingencyReload');
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		$dropdownlistUseYFlow.change(function () {
			var useYFlow = $dropdownlistUseYFlow.val() === 'true';
			$liTabAdvancedConfiguration.toggle(!useYFlow);
			$liTabAdvancedConfigurationYFlow.toggle(useYFlow);

			//$divAdvancedConfiguration.toggle(!useYFlow);
			//$divAdvancedConfigurationYFlow.toggle(useYFlow);

			$tabsInstagram.tabs('refresh');
			if (useYFlow) {
				ReloadFlows();
				//ReloadFlowsContingency();
			}
		}).trigger('change');

		$hiddenFlow = $('#hiddenFlow');
		$selectFlowToUse = $('#selectFlowToUse');
		$spanFlowID = $('#spanFlowID');
		$spanFlowName = $('#spanFlowName');
		$spanFlowVersion = $('#spanFlowVersion');

		$hiddenFlowContingency = $('#hiddenFlowContingency');
		$selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
		$spanFlowContingencyID = $('#spanFlowContingencyID');
		$spanFlowContingencyName = $('#spanFlowContingencyName');
		$spanFlowContingencyVersion = $('#spanFlowContingencyVersion');

		$hiddenFlowQueueTransfersByKey = $('#hiddenFlowQueueTransfersByKey');
		$tableFlowQueueTransfersByKey = $('#tableFlowQueueTransfersByKey');
		$anchorFlowQueueTransfersByKeyAdd = $('#anchorFlowQueueTransfersByKeyAdd');

		$listboxFlowShareEnqueuedMessagesFromQueues = $('#listboxFlowShareEnqueuedMessagesFromQueues');
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>400' }).multiselectfilter();
		$listboxFlowShareConnectedAgentsFromQueues = $('#listboxFlowShareConnectedAgentsFromQueues');
		$listboxFlowShareConnectedAgentsFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>400' }).multiselectfilter();
		
		$selectFlowToUse.change(function () {
			var $option = $('option:selected', $selectFlowToUse);
			var flow = $option.prop('definition');
			if (flow != null) {
				$spanFlowID.text(flow.id);
				$spanFlowName.text(flow.name);
				$spanFlowVersion.text(flow.ActiveProductionVersion.number);
				$hiddenFlow.val(JSON.stringify(flow));
			}
		});

		$anchorFlowsReload.click(ReloadFlows);
		$anchorFlowQueueTransfersByKeyAdd.click(AddFlowQueueTransfersByKeyRow);

		$selectFlowContingencyToUse.change(function () {
			var $option = $('option:selected', $selectFlowContingencyToUse);
			var flowContingency = $option.prop('definition');
			if (flowContingency != null) {
				$spanFlowContingencyID.text(flowContingency.id);
				$spanFlowContingencyName.text(flowContingency.name);
				$spanFlowContingencyVersion.text(flowContingency.ActiveProductionVersion.number);
				$hiddenFlowContingency.val(JSON.stringify(flowContingency));
			}
		});

		$anchorFlowsContingencyReload.click(ReloadFlowsContingency);

		var transfers = $hiddenFlowQueueTransfersByKey.val();
		if (transfers !== null && transfers.length > 0) {
			transfers = JSON.parse(transfers);
			for (var transfer in transfers) {
				AddFlowQueueTransfersByKeyRow(null, { Key: transfer, QueueID: transfers[transfer] });
			}
		}

		var currentFlow = $hiddenFlow.val();
		if (currentFlow !== null && currentFlow.length > 0) {
			currentFlow = JSON.parse(currentFlow);
			$spanFlowID.text(currentFlow.ID);
			$spanFlowName.text(currentFlow.Name);
			$spanFlowVersion.text(currentFlow.Version);
		}
	}
	else {
		$liTabAdvancedConfiguration.show();
		$tabsInstagram.tabs('refresh');
	}

	$hiddenUserAccessToken = $('#hiddenUserAccessToken');
	$textboxInstagramUserAccessToken = $('#textboxInstagramUserAccessToken');
	$messageInstagramAccountError = $('#messageInstagramAccountError');
	$trInstagramAccount = $('#trInstagramAccount');
	$divInstagramPage = $('#divInstagramPage');
	$messageInstagramNoPages = $('#messageInstagramNoPages');
	$divInstagramRedirectUri = $('#divInstagramRedirectUri');
	$inputInstagramUrl = $('#inputInstagramUrl');
	$buttonEnterInstagramUrl = $('#buttonEnterInstagramUrl');
	$messageInstagramCouldntValidateAccessToken = $('#messageInstagramCouldntValidateAccessToken');
	$divInstagramUser = $('#divInstagramUser');
	$divInstagramLoginButton = $('div.facebook-login-button-container');

	$("#textboxInstagramFromDate").datepicker({ changeMonth: true, changeYear: true, numberOfMonths: 1 });
	$('#textboxInstagramUserId').numeric();

	$divInstagramServiceMedia = $('#divInstagramServiceMedia');
	$checkboxInstagramAllowToSendMedia = $('input[type=checkbox][id$=checkboxInstagramAllowToSendMedia]', $divInstagramServiceMedia);

	$checkboxInstagramAllowToSendMedia.change(function () {
		var $table = $checkboxInstagramAllowToSendMedia.closest('table.uiInfoTable');
		var $trs = $('tr[rel=AllowToSendAttachments]', $table);
		if ($checkboxInstagramAllowToSendMedia.is(':checked')) {
			$trs.show();
		}
		else {
			$trs.hide();
		}
	}).trigger('change');

	var cleditorOptions = {
		height: 200,
		width: 'auto',
		fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
		bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
		controls:     // controls to add to the toolbar
          "bold italic underline | font size " +
          "style | color highlight removeformat | bullets numbering | outdent " +
          "indent | alignleft center alignright justify | " +
          "image link | cut copy paste pastetext | source",
	};

	$('textarea[id$=textboxInstagramOAuthErrorEmailTemplate],textarea[id$=textboxInstagramInactivityDetectedEmailTemplate]').cleditor(cleditorOptions);
	var $textboxInstagramOAuthErrorEmailTemplate = $('textarea[id$=textboxInstagramOAuthErrorEmailTemplate]');
	var $messageInstagramOAuthErrorEmailTemplateFields = $('#messageInstagramOAuthErrorEmailTemplateFields');
	CallValidationFields($textboxInstagramOAuthErrorEmailTemplate, $messageInstagramOAuthErrorEmailTemplateFields);

	var $textboxInstagramInactivityDetectedEmailTemplate = $('textarea[id$=textboxInstagramInactivityDetectedEmailTemplate]');
	var $messageInstagramInactivityDetectedEmailTemplateFields = $('#messageInstagramInactivityDetectedEmailTemplateFields');
	CallValidationFields($textboxInstagramInactivityDetectedEmailTemplate, $messageInstagramInactivityDetectedEmailTemplateFields);

	$listboxInstagramInactivityDetectedConnection = $('#listboxInstagramInactivityDetectedConnection');
	$hiddenInstagramInactivityDetectedConnection = $('#hiddenInstagramInactivityDetectedConnection');

	$hiddenInstagramOAuthErrorConnection = $('#hiddenInstagramOAuthErrorConnection');
	$listboxInstagramOAuthErrorConnection = $('#listboxInstagramOAuthErrorConnection');

	if (typeof (emails) !== 'undefined' && emails !== null && emails.length > 0) {

		let addOption = function (email, $select) {
			let $option = $('<option></option>');
			$option.text(email.Name);
			$option.val(email.ID);
			$select.append($option);
		};

		let selectOptions = function ($hidden, $select) {
			let value = $hidden.val();
			if (typeof (value) === 'string' && value.length > 0) {
				$select.val(value);
			}
		};

		for (let i = 0; i < emails.length; i++) {
			//No listamos la casilla por defult ya que al enviar el valor en null se utilizara casilla por defecto
			if (!emails[i].UseAsDefault) {
				addOption(emails[i], $listboxInstagramInactivityDetectedConnection);
				addOption(emails[i], $listboxInstagramOAuthErrorConnection);
			}
		}

		selectOptions($hiddenInstagramInactivityDetectedConnection, $listboxInstagramInactivityDetectedConnection);
		selectOptions($hiddenInstagramOAuthErrorConnection, $listboxInstagramOAuthErrorConnection);
	}

	$listboxInstagramInactivityDetectedConnection.multiselect({ multiple: false, noneSelectedText: "Por defecto(actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxInstagramInactivityDetectedConnection.change(function () {
		var value = $listboxInstagramInactivityDetectedConnection.val();
		$hiddenInstagramInactivityDetectedConnection.val('');
		if (value != null) {
			$hiddenInstagramInactivityDetectedConnection.val(value);
		}

	});

	$listboxInstagramOAuthErrorConnection.multiselect({ multiple: false, noneSelectedText: "Por defecto(actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxInstagramOAuthErrorConnection.change(function () {
		var value = $listboxInstagramOAuthErrorConnection.val();
		$hiddenInstagramOAuthErrorConnection.val('');
		if (value != null) {
			$hiddenInstagramOAuthErrorConnection.val(value);
		}

	});

	InitializeSurveysControls();
	InitializeCasesControls();

	var param = $(document).getUrlParam('tab');
	if (param && param != '') {
		$tabsInstagram.tabs('select', 'div' + param);
	}
	else {
		let tab = $hiddenTab.val();
		if (typeof(tab) === 'string' &&
			tab.length > 0) {
			$tabsInstagram.tabs('select', tab);
		}
	}
}

function i18nLoaded() {
	if ($listboxFlowShareEnqueuedMessagesFromQueues !== 'undefined') {
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_enqueued_messages-all_queues"));
		$listboxFlowShareConnectedAgentsFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_connected_agents-all_queues"));
	}
	if ($listboxInstagramInactivityDetectedConnection !== 'undefined') {
		$listboxInstagramInactivityDetectedConnection.multiselect('option', 'noneSelectedText', $.i18n("globals-email_default"));
		$listboxInstagramOAuthErrorConnection.multiselect('option', 'noneSelectedText', $.i18n("globals-email_default"));
	}
}

function InstagramLoggedIn() {
	FB.getLoginStatus(function (response) {
		statusChangeCallback(response);
	});
}

function FillInstagramAccounts(accounts, append) {
	if (accounts == null || accounts.data.length == 0) {
		$messageInstagramNoPages.show();
		$divInstagramPage.hide();
	}
	else {
		if (typeof (append) === 'undefined') {
			append = false;
		}

		var $divInstagramPages = $('#divInstagramPages');

		if (!append) {
			$divInstagramPages.empty();
		}

		var atLeastOneInstagramAccount = false;
		for (var i = 0; i < accounts.data.length; i++) {
			var account = accounts.data[i];
			if (account === null || typeof (account.id) === 'undefined')
				continue;

			if (typeof (account.instagramBusinessAccount) !== 'undefined' &&
				account.instagramBusinessAccount !== null) {
				var $divPage = $('<div class="facebook-pages-page"></div>');
				$divPage.append('<div class="facebook-pages-page-avatar"><img src="https://graph.facebook.com/' + account.id + '/picture" /></div>');
				var $divPageData = $('<div class="facebook-pages-page-data"></div>');
				$divPage.append($divPageData);
				$divPageData.append('<div class="facebook-pages-page-name"><span class="fab fa-facebook"></span> ' + account.name + '</div>');
				$divPageData.append('<div class="facebook-pages-page-moreinfo"><span>ID:</span>' + account.id + '</span></div>');
				$divPage.click(account, function (event) {
					$textboxInstagramUserAccessToken.val($hiddenUserAccessToken.val());
					$('#textboxInstagramPageId').val(event.data.id);
					$('#textboxInstagramPageName').val(event.data.name);
					if (typeof (event.data.access_token) !== 'undefined')
						$('#textboxInstagramPageAccessToken').val(event.data.access_token);
					else
						$('#textboxInstagramPageAccessToken').val(event.data.accessToken);
					$('#textboxInstagramAccountId').val(event.data.instagramBusinessAccount.id);
					$('#textboxInstagramAccountName').val(event.data.instagramBusinessAccount.name);
					$('#textboxInstagramAccountUsername').val(event.data.instagramBusinessAccount.username);
					$.colorbox.close();
				})
				$divPage.append('<div class="facebook-pages-page-instagram-linked"><span class="fa fa-4x fa-arrow-alt-right"></span></div>');
				var $divPageInstagramData = $('<div class="facebook-pages-page-instagram-data"></div>');
				$divPage.append($divPageInstagramData);
				$divPageInstagramData.append('<div class="facebook-pages-page-instagram-name"><span class="fab fa-instagram"></span> ' + account.instagramBusinessAccount.name + ' (' + account.instagramBusinessAccount.username + ')</div>');
				$divPageInstagramData.append('<div class="facebook-pages-page-instagram-moreinfo"><span>ID:</span>' + account.instagramBusinessAccount.id + '</span></div>');
				$divPage.append('<div class="facebook-pages-page-instagram-avatar"><img src="' + account.instagramBusinessAccount.profilePictureUrl + '" /></div>');
				$divInstagramPages.append($divPage);
				atLeastOneInstagramAccount = true;
			}
		}

		var $divButtonsLoadMore = $('#divButtonsLoadMore');
		var $buttonLoadMoreAccounts = $('#buttonLoadMoreAccounts');
		if (accounts.paging !== null && 
			typeof(accounts.paging.cursors) !== 'undefined' &&
			accounts.paging.cursors !== null) {
			$divButtonsLoadMore.show();
			$buttonLoadMoreAccounts.unbind('click');
			$buttonLoadMoreAccounts.click(accounts.paging.cursors.after, function (e) {
				var accessToken = $hiddenUserAccessToken.val();
				var after = e.data;

				var dataToSend = JSON.stringify({ accessToken: accessToken, after: after });

				$.ajax({
					type: "POST",
					url: "ServicesInstagram.aspx/GetAccountsAfter",
					data: dataToSend,
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					success: function (data) {
						if (data.d.Success) {
							if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
								FillInstagramAccounts(toCamel(data.d.Accounts), true);
							}
						}
						else {
							console.log('Ocurrió un error trayendo más cuentas: ' + data.d.Error.Message);
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						console.log('Ocurrió un error trayendo más cuentas: ' + jqXHR.responseText);
					}
				});
			});
		}
		else {
			$divButtonsLoadMore.hide();
		}

		if (atLeastOneInstagramAccount) {
			$divInstagramPage.show();
			$messageInstagramNoPages.hide();
		}
		else {
			if (accounts.paging !== null &&
				typeof (accounts.paging.cursors) !== 'undefined' &&
				accounts.paging.cursors !== null) {
				$buttonLoadMoreAccounts.trigger('click');
			}
			else {
				var $divPages = $('.facebook-pages-page', $divInstagramPages);
				if ($divPages.length === 0) {
					$messageInstagramNoPages.show();
					$divInstagramPage.hide();
				}
			}
		}
	}
	$.colorbox.resize();
}

function LoadUser(user) {
	$divInstagramUser.show();
	$('img', $divInstagramUser).attr('src', 'https://graph.facebook.com/' + user.Id + '/picture');
	$('div.facebook-user-name', $divInstagramUser).text(user.Name);
}

function ConfigureInstagram() {
	var $tableInstagramWizardLoading = $('#tableInstagramWizardLoading');
	var $messageInstagramWizardLoadingError = $('#messageInstagramWizardLoadingError');
	var $td = $('td.text', $messageInstagramWizardLoadingError);
	$messageInstagramWizardLoadingError.hide();
	$tableInstagramWizardLoading.show();

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divInstagramWizardLoading",
		width: '400px',
		initialWidth: '400px',
		preloading: false,
		showBackButton: false,
		closeButton: false
	});

	var dataToSend = JSON.stringify({
		serviceId: editingService ? editingServiceId : null,
		serviceType: editingService ? editingServiceType : creatingServiceType
	});

	$.ajax({
		type: "POST",
		url: "ServicesInstagram.aspx/BuildAuthorizationUriForInstagram",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				$divInstagramLoginButton.click(data.d.Uri, function (event) {
					$hiddenUserAccessToken.val('');
					$divInstagramPage.hide();
					$messageInstagramAccountError.hide();
					$messageInstagramCouldntValidateAccessToken.hide();
					$messageInstagramNoPages.hide();
					$messageInstagramWizardLoadingError.hide();
					$divInstagramUser.hide();
					$buttonEnterInstagramUrl.show();
					$inputInstagramUrl.val('');
					$.colorbox.resize();
					$('div.text', $divInstagramLoginButton).text($.i18n("configuration-servicesfacebookmessenger-log_in"));
					PopupCenter(event.data, 'login', 400, 300);
				});

				if (data.d.User !== null) {
					$buttonEnterInstagramUrl.hide();
					$divInstagramRedirectUri.hide();

					LoadUser(data.d.User);
					$('div.text', $divInstagramLoginButton).text($.i18n("configuration-servicesfacebookmessenger-change_user"));
				}

				if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
					FillInstagramAccounts(toCamel(data.d.Accounts));
					$buttonEnterInstagramUrl.hide();
					$divInstagramRedirectUri.hide();
				}

				$.colorbox({
					transition: 'elastic',
					speed: 100,
					inline: true,
					href: "#divInstagramWizard",
					overlayClose: false,
					width: '850px',
					initialWidth: '850px',
					preloading: false,
					showBackButton: false,
					closeButton: false
				});
			}
			else {
				$td.html($.i18n("configuration-servicesfacebookmessenger-error_retry_later"));
				$messageInstagramWizardLoadingError.show();
				$tableInstagramWizardLoading.hide();
				$.colorbox.resize();
				if (console)
					console.log('Ocurrió un error en BuildAuthorizationUriForInstagram: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$td.html($.i18n("configuration-servicesfacebookmessenger-error"));
			$messageInstagramWizardLoadingError.show();
			$tableInstagramWizardLoading.hide();
			$.colorbox.resize();
			if (console)
				console.log('Ocurrió un error en BuildAuthorizationUriForInstagram: ' + jqXHR.responseText);
		}
	});
}

function ValidateUserAccessToken(accessToken, expires) {
	var dataToSend = JSON.stringify({ accessToken: accessToken, expires: expires });

	$.ajax({
		type: "POST",
		url: "ServicesInstagram.aspx/GetAccountInfoForInstagram",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				$divInstagramRedirectUri.hide();
				$messageInstagramCouldntValidateAccessToken.hide();

				if (typeof (data.d.LongLivedAccessToken) === 'string' &&
					data.d.LongLivedAccessToken !== null &&
					data.d.LongLivedAccessToken.length > 0) {
					$hiddenUserAccessToken.val(data.d.LongLivedAccessToken);
				}
				else {
					$hiddenUserAccessToken.val(accessToken);
				}
				$('#spanInstagramUserId').text(data.d.User.Id);
				$('#spanInstagramUsername').text(data.d.User.Name);

				LoadUser(data.d.User);
				$('div.text', $divInstagramLoginButton).text($.i18n("configuration-servicesinstagram-change_user"));

				if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
					FillInstagramAccounts(toCamel(data.d.Accounts));
				}
			}
			else {
				$messageInstagramCouldntValidateAccessToken.show();
				if (console)
					console.log('Ocurrió un error validando la URL: ' + data.d.Error.Message);
			}

			$.colorbox.resize();
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$messageInstagramCouldntValidateAccessToken.show();
			$.colorbox.resize();
			if (console)
				console.log('Ocurrió un error validando la URL: ' + jqXHR.responseText);
		}
	});
}

function InstagramValidateAccessTokenExpiry() {
	var accessToken = $('input[type=text][id$=textboxInstagramPageAccessToken]').val();
	var $messageInstagramTokenExpiry = $('div[id$=messageInstagramTokenExpiry]');
	var $spanInstagramTokenExpiry = $('td.text > span[rel=expires]', $messageInstagramTokenExpiry);
	var dataToSend = JSON.stringify({ accessToken: accessToken });

	$.ajax({
		type: "POST",
		url: "ServicesInstagram.aspx/GetAccessTokenInfoForInstagram",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				if (data.d.Info.Expires) {
					$messageInstagramTokenExpiry.show();
					$spanInstagramTokenExpiry.text(data.d.Info.ExpiresAt.DateString);
				}
				else {
					$messageInstagramTokenExpiry.hide();
				}
			}
			else {
				$messageInstagramTokenExpiry.hide();
				if (console)
					console.log('Error al consultar expiración del token de Instagram: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$messageInstagramTokenExpiry.hide();
			if (console)
				console.log('Error al consultar expiración del token de Instagram: ' + jqXHR.responseText);
		}
	});
}

function ValidateInstagramMaxSizeAttachment(sender, e) {
	e.IsValid = true;

	if (!$checkboxInstagramAllowToSendMedia.is(':checked'))
		return;

	var $textbox = $('input[id$=textboxInstagramMaxSizeAttachment]', $divInstagramServiceMedia);
	var value = $textbox.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(value)) {
		e.IsValid = false;
		return;
	}

	value = parseInt(value, 10);

	if (value < 1 || value > 3) {
		e.IsValid = false;
		return;
	}
}

function ValidateInstagramMultimediaOptions(sender, e) {
	e.IsValid = true;

	if (!$checkboxInstagramAllowToSendMedia.is(':checked'))
		return;

	var $checkboxInstagramAcceptedTypeImages = $('#checkboxInstagramAcceptedTypeImages', $divInstagramServiceMedia);
	var $checkboxInstagramAcceptedTypeAudio = $('#checkboxInstagramAcceptedTypeAudio', $divInstagramServiceMedia);
	var $checkboxInstagramAcceptedTypeVideo = $('#checkboxInstagramAcceptedTypeVideo', $divInstagramServiceMedia);
	var $checkboxInstagramAcceptedTypeAllFiles = $('#checkboxInstagramAcceptedTypeAllFiles', $divInstagramServiceMedia);

	if (!$checkboxInstagramAcceptedTypeImages.is(':checked') &&
		!$checkboxInstagramAcceptedTypeAudio.is(':checked') &&
		!$checkboxInstagramAcceptedTypeVideo.is(':checked') &&
		!$checkboxInstagramAcceptedTypeAllFiles.is(':checked')) {
		e.IsValid = false;
		return;
	}
}

function VerifyOtherServices() {
	var pageId = $('input[type=text][id$=textboxInstagramPageId]').val();
	var dataToSend = JSON.stringify({ serviceId: (editingService ? editingServiceId : null), pageIdStr: pageId });
	var $buttonSave = $('#buttonSave');
	var $divAnotherInstagramServiceExists = $('#divAnotherInstagramServiceExists');
	var $messageAnotherInstagramServiceExists = $('#messageAnotherInstagramServiceExists');
	var $buttonSaveAndDisable = $('#buttonSaveAndDisable');

	$.ajax({
		type: "POST",
		url: "ServicesInstagram.aspx/IsPageUsedInAnotherService",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				console.log(data.d);

				if (!data.d.ExistsAnotherService) {
					$buttonSave.click();
				}
				else {
					var $spanServiceName = $('span[rel=servicename]', $divAnotherInstagramServiceExists);
					$spanServiceName.text(data.d.Service.Name);

					$.colorbox({
						transition: 'elastic',
						speed: 100,
						inline: true,
						href: $divAnotherInstagramServiceExists,
						overlayClose: false,
						width: '850px',
						initialWidth: '850px',
						preloading: false,
						showBackButton: false,
						closeButton: false
					});
				}
			}
			else {
				if (console)
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			if (console)
				console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
		}
	});
}

function PopupCenter(url, title, w, h) {
    // Fixes dual-screen position                         Most browsers      Firefox
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
    var top = ((height / 2) - (h / 2)) + dualScreenTop;
    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

    // Puts focus on the newWindow
    if (window.focus) {
        newWindow.focus();
    }
}

function ReloadFlows() {
	$('span.fa', $anchorFlowsReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.Instagram,
		function (flows) {
			if (flows !== null && flows.length > 0) {
				var currentFlow = $hiddenFlow.val();
				if (currentFlow !== null && currentFlow.length > 0) {
					currentFlow = JSON.parse(currentFlow);
				}
				else {
					currentFlow = null;
				}

				$selectFlowToUse.empty();
				for (var i = 0; i < flows.length; i++) {
					var $option = $('<option />');
					$option.val(flows[i].id);
					$option.text(flows[i].name);
					$option.prop('definition', flows[i]);
					$selectFlowToUse.append($option);
				}

				if (currentFlow !== null) {
					if (typeof (currentFlow.id) !== 'undefined') {
						$selectFlowToUse.val(currentFlow.id);
					}
					else {
						$selectFlowToUse.val(currentFlow.ID);
					}
				}
				$selectFlowToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('Flow', $.i18n("configuration-servicestwitter-no_flows"));
				$dropdownlistUseYFlow.val('false').trigger('change');
				$tabsInstagram.tabs('select', 'divBasicConfiguration');
				$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
			}

			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow: %o', err);
			AlertDialog('Flow', $.i18n("configuration-servicestwitter-flow_error"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsInstagram.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
			$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
		}
	);
}

function ReloadFlowsContingency() {
	$('span.fa', $anchorFlowsContingencyReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.Telegram,
		function (flowsContingency) {
			if (flowsContingency !== null && flowsContingency.length > 0) {
				var currentFlowContingency = $hiddenFlowContingency.val();
				if (currentFlowContingency !== null && currentFlowContingency.length > 0) {
					currentFlowContingency = JSON.parse(currentFlowContingency);
				}
				else {
					currentFlowContingency = null;
				}

				$selectFlowContingencyToUse.empty();
				for (var i = 0; i < flowsContingency.length; i++) {
					var $option = $('<option />');
					$option.val(flowsContingency[i].id);
					$option.text(flowsContingency[i].name);
					$option.prop('definition', flowsContingency[i]);
					$selectFlowContingencyToUse.append($option);
				}

				if (currentFlowContingency !== null) {
					if (typeof (currentFlowContingency.id) !== 'undefined') {
						$selectFlowContingencyToUse.val(currentFlowContingency.id);
					}
					else {
						$selectFlowContingencyToUse.val(currentFlowContingency.ID);
					}
				}
				$selectFlowContingencyToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('FlowContingency', $.i18n("configuration-serviceswhatsapp-no_flows"));
				$dropdownlistUseYFlowContingency.val('false').trigger('change');
				$tabsTelegram.tabs('select', 'divBasicConfiguration');
			}

			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow Contindencia: %o', err);
			AlertDialog('Flow', $.i18n("configuration-serviceswhatsapp-flow_list_error"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsTelegram.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		}
	);
}

function AddFlowQueueTransfersByKeyRow(e, data) {
	var $tbody = $('tbody', $tableFlowQueueTransfersByKey);

	var $lastTr = $("tr:last-child", $tbody);
	var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
	var $newTr = $('<tr class="' + newTrClass + '"><td style="text-align: center"><a rel="remove"><span class="fa fa-lg fa-minus-square"></span></a></td><td><input type="text" rel="key" class="inputtextmono" spellcheck="false" style="width:95%"/></td><td class="data"><select rel="queue" style="width: 95%";></select></td></tr>');
	
	var $inputKey = $('input[rel=key]', $newTr);
	var $selectQueue = $('select[rel=queue]', $newTr);
	for (var i = 0; i < queues.length; i++) {
		var $option = $('<option value="' + queues[i].ID + '">' + queues[i].Name + '</option>');
		$selectQueue.append($option);
	}

	$tbody.append($newTr);

	if (typeof (data) !== 'undefined' && data !== null) {
		$inputKey.val(data.Key);
		$selectQueue.val(data.QueueID);
	}

	$selectQueue.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' }).multiselectfilter();

	var $anchorRemove = $('a[rel=remove]', $newTr);
	$anchorRemove.click(function () {
		var $tr = $(this).parent().parent();
		$tr.remove();

		var $trs = $('tr', $tbody);
		if ($trs.length > 0) {
			for (var i = 0; i < $trs.length; i++) {
				var $tr = $($trs.get(i));
				$tr.removeClass('normal alternate');
				$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
			}
		}
	});
}

function ValidateFlowQueueTransfersByKey(sender, e) {
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		if ($dropdownlistUseYFlow.val() === 'false') {
			$hiddenFlowQueueTransfersByKey.val('');
			e.IsValid = true;
			return;
		}

		var $tbody = $('tbody', $tableFlowQueueTransfersByKey);
		var $trs = $('tr', $tbody);

		var transfers = [];

		for (var i = 0; i < $trs.length; i++) {
			var $tr = $($trs.get(i));
			var $inputKey = $('input[rel=key]', $tr);
			var $selectQueue = $('select[rel=queue]', $tr);

			var key = $inputKey.val();
			if (key.length == 0) {
				$(sender).text($.i18n("configuration-servicestwitter-no_flows", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			var regex = /^[a-zA-Z0-9]+$/;
			if (!regex.test(key)) {
				$(sender).text($.i18n("configuration-servicestwitter-key_regex", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			let queueId = parseInt($selectQueue.val(), 10);
			if (isNaN(queueId)) {
				$(sender).text($.i18n("configuration-services-common-yflow-flow_queue_transfers-invalid_queue", (i + 1).toString(), key));
				e.IsValid = false;
				return;
			}

			var transfer = {
				Key: key,
				QueueID: queueId
			};
			transfers.push(transfer);
		}

		if (transfers.length === 0) {
			$hiddenFlowQueueTransfersByKey.val('');
		}
		else {
			$hiddenFlowQueueTransfersByKey.val(JSON.stringify(transfers));
		}
	}

	e.IsValid = true;
}

function ValidateAutoReplyBeforeCloseCase(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyBeforeCloseCase.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeCloseCaseText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-servicesinstagram-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeCloseCaseMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-servicesinstagram-enter_time"));
		e.IsValid = false;
		return;
	}

	let minutesToCloseCases = casesSettings.MaxElapsedMinutesToCloseCases;
	if ($checkboxCasesOverrideSystemSettings.is(':checked')) {
		let maxElapsedMinutesToCloseCases = $textboxMaxElapsedMinutesToCloseCases.val();
		if (maxElapsedMinutesToCloseCases.length === 0) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		if (!/^\d{1,5}$/.test(maxElapsedMinutesToCloseCases)) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		maxElapsedMinutesToCloseCases = parseInt(maxElapsedMinutesToCloseCases, 10);
		if (isNaN(maxElapsedMinutesToCloseCases) ||
			maxElapsedMinutesToCloseCases < 1 ||
			maxElapsedMinutesToCloseCases > 43200) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		minutesToCloseCases = maxElapsedMinutesToCloseCases;
	}

	if (minutes == 0 || minutes >= minutesToCloseCases) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_valid_time", minutesToCloseCases));
		e.IsValid = false;
		return;
	}
}

function ValidateAutoReplyBeforeMaxTimeToAnswer(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyBeforeMaxTimeToAnswer.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeMaxTimeToAnswerText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeMaxTimeToAnswerMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_time"));
		e.IsValid = false;
		return;
	}

	minutes = parseInt(minutes);
	if (minutes < 0 || minutes > maxMinutesToAnswerMessages) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-time_info", maxMinutesToAnswerMessages));
		e.IsValid = false;
		return;
	}
}

function AddHSMTemplateRow(e, template) {
	var $lastTr = $("tr:last-child", $tbodyHSMTemplates);
	var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
	var $newTr = $('<tr></tr>');
	$newTr.addClass(newTrClass);

	var $td = $('<td style="text-align: center"></td>');
	$newTr.append($td);
	var $anchorRemove = $('<a rel="remove"><span class="fa fa-lg fa-minus-square"></span></a>');
	$td.append($anchorRemove);
	$anchorRemove.click(function () {
		var $tr = $(this).parent().parent();
		$tr.remove();

		var $trs = $('tr', $tbodyHSMTemplates);
		if ($trs.length > 0) {
			for (var i = 0; i < $trs.length; i++) {
				var $tr = $($trs.get(i));
				$tr.removeClass('normal alternate');
				$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
			}
		}
	});

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputID = $('<input type="text" readonly="readonly" rel="id" class="inputtext" spellcheck="false" style="width:95%"/>');
	$td.append($inputID);

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputDescription = $('<input type="text" rel="description" class="inputtext" spellcheck="false" style="width:95%"/>');
	$td.append($inputDescription);

	$td = $('<td></td>');
	$newTr.append($td);
	var $selectTag = $('<select rel="tag"></select>');
	$selectTag.append('<option title="Send the user reminders or updates for an event they have registered for (e.g., RSVP\'ed, purchased tickets). This tag may be used for upcoming events and events in progress.">CONFIRMED_EVENT_UPDATE</option>');
	$selectTag.append('<option title="Notify the user of an update on a recent purchase">POST_PURCHASE_UPDATE</option>');
	$selectTag.append('<option title="Notify the user of a non-recurring change to their application or account.">ACCOUNT_UPDATE</option>');
	$selectTag.append('<option title="Allows human agents to respond to user inquiries. Messages can be sent within 7 days after a user message">HUMAN_AGENT</option>');
	$td.append($selectTag);

	var $divTagTitle = $('<div rel="title"></div>');
	$td.append($divTagTitle);
	$selectTag.change(function () {
		var $this = $(this);
		var $parent = $this.parent();
		var $divTitle = $('div[rel=title]', $parent);
		var title = $('option:selected', $this).attr('title');
		$divTitle.text(title);
	}).trigger('change');

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputTemplate = $('<textarea rel="template" class="inputtext" spellcheck="false" style="width:100%; min-height: 120px; box-sizing: border-box"/>');
	$td.append($inputTemplate);

	$td = $('<td rel="parameters"></td>');
	$newTr.append($td);

	var $tableParameters = BuildDynamicTable({
		showHeaders: true,
		container: $td,
		onRowAdded: function () {
			var tableHeight = $tableParameters.height();
			var inputTemplateHeight = $inputTemplate.height();
			if (tableHeight > inputTemplateHeight) {
				$inputTemplate.height(tableHeight);
			}
		},
		onRowRemoved: function () {
			var tableHeight = $tableParameters.height();
			var inputTemplateHeight = $inputTemplate.height();
			if (tableHeight < inputTemplateHeight) {
				if (tableHeight < 120) {
					$inputTemplate.height(120);
				}
				else {
					$inputTemplate.height(tableHeight);
				}
			}
		},
		columns: [
			{
				header: {
					title: 'Nombre'
				},
				type: 'text',
				key: 'Name',
				placeholder: 'Nombre del parámetro'
			},
			{
				header: {
					title: 'Descripción'
				},
				key: 'Description',
				type: 'text',
				placeholder: 'Descripción del parámetro'
			}
		]
	});

	$td.prop('table', $tableParameters);

	$tbodyHSMTemplates.append($newTr);

	if (typeof (template) !== 'undefined' && template !== null) {
		if (typeof (template.ID) === 'undefined' || template.ID === null) {
			template.ID = uuidv4();
		}
		$inputID.val(template.ID);
		$inputDescription.val(template.Description);
		$selectTag.val(template.Tag);
		if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
			for (var i = 0; i < template.TemplateParameters.length; i++) {
				var p = template.TemplateParameters[i];
				$tableParameters.addRow([p.Name, p.Description]);
			}
		}
		$inputTemplate.val(template.Template);
	}
	else {
		$inputID.val(uuidv4());

		$inputTemplate.blur({ $tr: $newTr }, function (e) {
			var $this = $(this);
			var template = $this.val();
			var $tdParameters = $('td[rel=parameters]', e.data.$tr);
			var $tableParameters = $tdParameters.prop('table');
			var parameters = $tableParameters.getValuesRaw('=');
			if (parameters.length === 0 && template.length > 0) {
				var parametersRegex = /\{\{[a-zA-Z0-9]+\}\}/g;
				var matches = parametersRegex.exec(template);
				while (matches !== null) {
					var parameterName = matches[0].replace('{{', '').replace('}}', '');
					$tableParameters.addRow([parameterName, '']);

					matches = parametersRegex.exec(template);
				}
			}
		});
	}
}

function ValidateHSMTemplates(sender, e) {
	e.IsValid = true;

	var allowed = $dropdownlistAllowToSendHSM.val();
	if (allowed === "0") {
		return;
	}

	var $trs = $('> tbody > tr', $tableHSMTemplates);

	var templates = [];
	var regexElementName = /^[a-z_0-9]+$/;
	var regexNamespace = /^.+$/;
	var regexParameters = /^[\w$]+=.+$/;

	if ($trs.length === 0) {
		$(sender).text($.i18n("configuration-servicesfacebook-enter_template"));
		e.IsValid = false;
		return;
	}

	for (var i = 0; i < $trs.length; i++) {
		var $tr = $($trs.get(i));
		var $inputDescription = $('input[rel=description]', $tr);
		var $selectTag = $('select[rel=tag]', $tr);
		var $inputParameters = $('textarea[rel=parameters]', $tr);
		var $inputTemplate = $('textarea[rel=template]', $tr);
		var $inputID = $('input[rel=id]', $tr);

		id = $inputID.val();

		var description = $inputDescription.val();
		if (description.length == 0) {
			$(sender).text($.i18n("configuration-servicesfacebook-enter_description", (i + 1).toString()));
			e.IsValid = false;
			return;
		}

		var tag = $selectTag.val();

		var templateText = $inputTemplate.val();
		if (templateText.length === 0) {
			$(sender).text($.i18n("configuration-servicesfacebook-no_message_template", (i + 1).toString()));
			e.IsValid = false;
			return;
		}

		var $tdParameters = $('td[rel=parameters]', $tr);
		var $tableParameters = $tdParameters.prop('table');
		var parameters = $tableParameters.getValuesRaw('=');
		if (parameters.length > 0) {
			var parametersElements = parameters.split('\n');
			parameters = [];
			for (var j = 0; j < parametersElements.length; j++) {
				var parametersElement = parametersElements[j];
				if (parametersElement.length === 0) {
					$(sender).text($.i18n("configuration-servicesfacebook-no_message_template", (i + 1).toString()));
					e.IsValid = false;
					return;
				}

				var parameterName = parametersElement;

				if (!regexParameters.test(parametersElement)) {
					$(sender).html('Los parámetros del elemento del ítem ' + (i + 1).toString() + ' debe contener los nombres de los campos y su descripción separados con un caracter igual (=) ingresando uno por renglón. Ejemplo: <span class="mono">first_name=Primer Nombre</span>');
					e.IsValid = false;
					return;
				}

				parameterName = parameterName.substr(0, parameterName.indexOf('='));

				if (templateText.indexOf('{{' + parameterName + '}}') === -1) {
					$(sender).html($.i18n("configuration-servicesfacebook-no_message_template", parameterName, (i + 1).toString(), parameterName));
					e.IsValid = false;
					return;
				}

				parameters.push(parametersElement);
			}
		}

		var template = {
			ID: id,
			Description: description,
			Tag: tag,
			Parameters: parameters,
			Template: templateText
		};

		templates.push(template);
	}

	if (templates.length === 0) {
		$hiddenHSMTemplates.val('');
	}
	else {
		$hiddenHSMTemplates.val(JSON.stringify(templates));
	}
}

function UsingYFlow() {
	if (typeof (allowYFlow) !== 'undefined' &&
		allowYFlow &&
		$dropdownlistUseYFlow.val() === "true") {
		return true;
	}

	return false;
}

function ValidateYFlow(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	var flow = $hiddenFlow.val();
	if (flow.length === 0) {
		AlertDialog($.i18n("configuration-servicestwitter-yflow_configuration"), $.i18n("configuration-servicestwitter-yflow_configuration-tip"));
		e.IsValid = false;
		return;
	}
}

function ValidateSelectFlowToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	var $selectFlowToUse = $('#selectFlowToUse');
	var value = $selectFlowToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateSelectFlowContingencyToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlowContingency()) {
		return;
	}

	var $selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
	var value = $selectFlowContingencyToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}