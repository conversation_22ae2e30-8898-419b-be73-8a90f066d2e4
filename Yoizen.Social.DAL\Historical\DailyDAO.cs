﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Yoizen.Social.DomainModel.Historical;
using System.Data;
using System.Data.Common;
#if NETCOREAPP
using Microsoft.Data.SqlClient;
#else
using System.Data.SqlClient;
#endif

namespace Yoizen.Social.DAL.Historical
{
	public static class DailyDAO
	{
		/// <summary>
		/// Inserta los datos de muchos <see cref="DomainModel.Historical.Daily"/> en la tabla histórica
		/// </summary>
		/// <param name="infos">Una enumeración de <see cref="DomainModel.Historical.Daily"/> con la información a insertar</param>
		public static void Insert(IEnumerable<DomainModel.Historical.Daily> infos)
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				foreach (var info in infos)
				{
					try
					{
						Insert(info, conn);
					}
					catch (Exception ex)
					{
						Common.Tracer.TraceError("Ocurrió un error insertando los datos de la novedad correspondiente a {0}: {1}", info, ex);
					}
				}
			}
		}

		/// <summary>
		/// Inserta los datos de un <see cref="DomainModel.Historical.Daily"/> en la tabla histórica
		/// </summary>
		/// <param name="info">La información a insertar</param>
		/// <param name="conn">La <see cref="DbConnection"/> abierta a utilizar</param>
		private static void Insert(DomainModel.Historical.Daily info, DbConnection conn)
		{
			try
			{
				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_Insert";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("Date", DbType.Date, info.Date);
					cmd.AddParameter("Interval", DbType.Int16, info.Interval);
					cmd.AddParameter("PersonID", DbType.Int32, info.PersonID);
					cmd.AddParameter("QueueID", DbType.Int32, info.QueueID);
					cmd.AddParameter("AssignedMessages", DbType.Int32, info.AssignedMessages);
					cmd.AddParameter("RepliedMessages", DbType.Int32, info.RepliedMessages);
					cmd.AddParameter("DiscardedMessages", DbType.Int32, info.DiscardedMessages);
					cmd.AddParameter("DiscardedMessagesByAgents", DbType.Int32, info.DiscardedMessagesByAgents);
					cmd.AddParameter("DiscardedMessagesByUsers", DbType.Int32, info.DiscardedMessagesByUsers);
					cmd.AddParameter("UnassignedMessages", DbType.Int32, info.UnassignedMessages);
					cmd.AddParameter("LoginTime", DbType.Double, info.LoginTime);
					cmd.AddParameter("AvailTime", DbType.Double, info.AvailTime);
					cmd.AddParameter("AuxTime", DbType.Double, info.AuxTime);
					cmd.AddParameter("Aux0Time", DbType.Double, info.Aux0Time);
					cmd.AddParameter("Aux1Time", DbType.Double, info.Aux1Time);
					cmd.AddParameter("Aux2Time", DbType.Double, info.Aux2Time);
					cmd.AddParameter("Aux3Time", DbType.Double, info.Aux3Time);
					cmd.AddParameter("Aux4Time", DbType.Double, info.Aux4Time);
					cmd.AddParameter("Aux5Time", DbType.Double, info.Aux5Time);
					cmd.AddParameter("Aux6Time", DbType.Double, info.Aux6Time);
					cmd.AddParameter("Aux7Time", DbType.Double, info.Aux7Time);
					cmd.AddParameter("Aux8Time", DbType.Double, info.Aux8Time);
					cmd.AddParameter("Aux9Time", DbType.Double, info.Aux9Time);
					cmd.AddParameter("Aux10Time", DbType.Double, info.Aux10Time);
					cmd.AddParameter("Aux11Time", DbType.Double, info.Aux11Time);
					cmd.AddParameter("Aux12Time", DbType.Double, info.Aux12Time);
					cmd.AddParameter("Aux13Time", DbType.Double, info.Aux13Time);
					cmd.AddParameter("Aux14Time", DbType.Double, info.Aux14Time);
					cmd.AddParameter("Aux15Time", DbType.Double, info.Aux15Time);
					cmd.AddParameter("Aux16Time", DbType.Double, info.Aux16Time);
					cmd.AddParameter("Aux17Time", DbType.Double, info.Aux17Time);
					cmd.AddParameter("Aux18Time", DbType.Double, info.Aux18Time);
					cmd.AddParameter("Aux19Time", DbType.Double, info.Aux19Time);
					cmd.AddParameter("Aux20Time", DbType.Double, info.Aux20Time);
					cmd.AddParameter("Aux21Time", DbType.Double, info.Aux21Time);
					cmd.AddParameter("Aux22Time", DbType.Double, info.Aux22Time);
					cmd.AddParameter("Aux23Time", DbType.Double, info.Aux23Time);
					cmd.AddParameter("Aux24Time", DbType.Double, info.Aux24Time);
					cmd.AddParameter("Aux25Time", DbType.Double, info.Aux25Time);
					cmd.AddParameter("Aux26Time", DbType.Double, info.Aux26Time);
					cmd.AddParameter("Aux27Time", DbType.Double, info.Aux27Time);
					cmd.AddParameter("Aux28Time", DbType.Double, info.Aux28Time);
					cmd.AddParameter("Aux29Time", DbType.Double, info.Aux29Time);
					cmd.AddParameter("WorkingTime", DbType.Double, info.WorkingTime);
					cmd.AddParameter("AgentTime", DbType.Double, info.AgentTime);
					cmd.AddParameter("UnreadTime", DbType.Double, info.UnreadTime);
					cmd.AddParameter("ReadTime", DbType.Double, info.ReadTime);
					cmd.AddParameter("SystemDiscardedMessages", DbType.Int32, info.SystemDiscardedMessages);
					cmd.AddParameter("AutoRepliedMessages", DbType.Int32, info.AutoRepliedMessages);
					cmd.AddParameter("FilteredMessages", DbType.Int32, info.FilteredMessages);
					cmd.AddParameter("EnqueuedMessages", DbType.Int32, info.EnqueuedMessages);
					cmd.AddParameter("DequeuedMessages", DbType.Int32, info.DequeuedMessages);
					cmd.AddParameter("NewMessages", DbType.Int32, info.NewMessages);
					cmd.AddParameter("PeekEnqueuedMessages", DbType.Int32, info.PeekEnqueuedMessages);
					cmd.AddParameter("PeekMinutesEnqueuedMessages", DbType.Int32, info.PeekMinutesEnqueuedMessages);
					cmd.AddParameter("LoggedAgents", DbType.Int32, info.LoggedAgents);
					cmd.AddParameter("LoggedOutAgents", DbType.Int32, info.LoggedOutAgents);
					cmd.AddParameter("PeekLoggedAgents", DbType.Int32, info.PeekLoggedAgents);
					cmd.AddParameter("GroupedMessages", DbType.Int32, info.GroupedMessages);
					cmd.AddParameter("AgentGroupedMessages", DbType.Int32, info.AgentGroupedMessages);
					cmd.AddParameter("NewCases", DbType.Int32, info.NewCases);
					cmd.AddParameter("ClosedCases", DbType.Int32, info.ClosedCases);
					cmd.AddParameter("ClosedCasesByAgents", DbType.Int32, info.ClosedCasesByAgents);
					cmd.AddParameter("ClosedCasesByUsers", DbType.Int32, info.ClosedCasesByUsers);
					cmd.AddParameter("AutoClosedCases", DbType.Int32, info.AutoClosedCases);
					cmd.AddParameter("ReopenCases", DbType.Int32, info.ReopenCases);
					cmd.AddParameter("EnqueuedMessagesOnIntervalClose", DbType.Int32, info.EnqueuedMessagesOnIntervalClose);
					cmd.AddParameter("LoggedAgentsOnIntervalClose", DbType.Int32, info.LoggedAgentsOnIntervalClose);
					cmd.AddParameter("AbandonedMessages", DbType.Int32, info.AbandonedMessages);
					cmd.AddParameter("FinishedMessagesByAgent", DbType.Int32, info.FinishedMessagesByAgent);
					cmd.AddParameter("FinishedMessagesByUser", DbType.Int32, info.FinishedMessagesByUser);
					cmd.AddParameter("ReturnedToQueueMessages", DbType.Int32, info.ReturnedToQueueMessages);
					cmd.AddParameter("InboundMessages", DbType.Int32, info.InboundMessages);
					cmd.AddParameter("OutboundMessages", DbType.Int32, info.OutboundMessages);
					cmd.AddParameter("VerifiedMessages", DbType.Int32, info.VerifiedMessages);
					cmd.AddParameter("NewChats", DbType.Int32, info.NewChats);
					cmd.AddParameter("RepliedMessagesByUser", DbType.Int32, info.RepliedMessagesByUser);
					cmd.AddParameter("FlowIn", DbType.Int32, info.FlowIn);
					cmd.AddParameter("FlowOut", DbType.Int32, info.FlowOut);
					cmd.AddParameter("OutOfSLInQueue", DbType.Int32, info.OutOfSLInQueue);
					cmd.AddParameter("OutOfSLInAgent", DbType.Int32, info.OutOfSLInAgent);
					cmd.AddParameter("Expired", DbType.Int32, info.Expired);
					cmd.AddParameter("CasesOutOfSL", DbType.Int32, info.CasesOutOfSL);
					cmd.AddParameter("ReplyTime", DbType.Double, info.ReplyTime);
					cmd.AddParameter("MessagesRepliedOutOfSL", DbType.Int32, info.MessagesRepliedOutOfSL);
					cmd.AddParameter("FlowInSLL", DbType.Int32, info.FlowInSLL);
					cmd.AddParameter("FlowOutSLL", DbType.Int32, info.FlowOutSLL);
					cmd.AddParameter("OutOfSLLInQueue", DbType.Int32, info.OutOfSLLInQueue);
					cmd.AddParameter("OutOfSLLInAgent", DbType.Int32, info.OutOfSLLInAgent);
					cmd.AddParameter("ExpiredSLL", DbType.Int32, info.ExpiredSLL);
					cmd.AddParameter("CasesOutOfSLL", DbType.Int32, info.CasesOutOfSLL);
					cmd.AddParameter("MessagesRepliedOutOfSLL", DbType.Int32, info.MessagesRepliedOutOfSLL);
					cmd.AddParameter("ReopenCasesByOthers", DbType.Int32, info.ReopenCasesByOthers);
					cmd.AddParameter("OutgoingMessages", DbType.Int32, info.OutgoingMessages);
					cmd.AddParameter("AgentOutTime", DbType.Double, info.AgentOutTime);
					cmd.AddParameter("SurveysSent", DbType.Int32, info.SurveysSent);
					cmd.AddParameter("SurveysStarted", DbType.Int32, info.SurveysStarted);
					cmd.AddParameter("SurveysFinished", DbType.Int32, info.SurveysFinished);
					cmd.AddParameter("MyOutgoingCases", DbType.Int32, info.MyOutgoingCases);
					cmd.AddParameter("AttendedMessagesByAgent", DbType.Int32, info.AttendedMessagesByAgent);
					cmd.AddParameter("MessagesAttendedOutOfSL", DbType.Int32, info.MessagesAttendedOutOfSL);
					cmd.AddParameter("MessagesAttendedOutOfSLL", DbType.Int32, info.MessagesAttendedOutOfSLL);
					cmd.AddParameter("AgentTotalCallWaitingTime", DbType.Double, info.AgentTotalCallWaitingTime);
					cmd.AddParameter("AgentTotalCallMutedTime", DbType.Double, info.AgentTotalCallMutedTime);
					cmd.AddParameter("AgentTotalCallTalkingTime", DbType.Double, info.AgentTotalCallTalkingTime);
					cmd.AddParameter("AgentTotalCallTime", DbType.Double, info.AgentTotalCallTime);
					cmd.AddParameter("TotalAvailableCallRecords", DbType.Int32, info.TotalAvailableCallRecords);
					cmd.AddParameter("TotalRejectedCalls", DbType.Int32, info.TotalRejectedCalls);
					cmd.AddParameter("TotalIncomingCalls", DbType.Int32, info.TotalIncomingCalls);
					cmd.AddParameter("TotalAttendedCalls", DbType.Int32, info.TotalAttendedCalls);
					cmd.AddParameter("TotalMissedCalls", DbType.Int32, info.TotalMissedCalls);
					
					cmd.ExecuteNonQuery();
				}
			}
			catch (DbException ex)
			{
				if (ex is SqlException &&
					((SqlException) ex).Number == 2627)
				{
					// Violation of primary key. Handle Exception
					//Update(info, conn);
				}
				else
				{
					throw;
				}
			}
		}

		/// <summary>
		/// Actualiza los datos de un <see cref="DomainModel.Historical.Daily"/> en la tabla histórica
		/// </summary>
		/// <param name="info">La información a insertar</param>
		/// <param name="conn">La <see cref="DbConnection"/> abierta a utilizar</param>
		private static void Update(DomainModel.Historical.Daily info, DbConnection conn)
		{
			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_Update";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("Date", DbType.Date, info.Date);
				cmd.AddParameter("Interval", DbType.Int16, info.Interval);
				cmd.AddParameter("PersonID", DbType.Int32, info.PersonID);
				cmd.AddParameter("QueueID", DbType.Int32, info.QueueID);
				cmd.AddParameter("AssignedMessages", DbType.Int32, info.AssignedMessages);
				cmd.AddParameter("RepliedMessages", DbType.Int32, info.RepliedMessages);
				cmd.AddParameter("DiscardedMessages", DbType.Int32, info.DiscardedMessages);
				cmd.AddParameter("DiscardedMessagesByAgents", DbType.Int32, info.DiscardedMessagesByAgents);
				cmd.AddParameter("DiscardedMessagesByUsers", DbType.Int32, info.DiscardedMessagesByUsers);
				cmd.AddParameter("UnassignedMessages", DbType.Int32, info.UnassignedMessages);
				cmd.AddParameter("LoginTime", DbType.Double, info.LoginTime);
				cmd.AddParameter("AvailTime", DbType.Double, info.AvailTime);
				cmd.AddParameter("AuxTime", DbType.Double, info.AuxTime);
				cmd.AddParameter("Aux0Time", DbType.Double, info.Aux0Time);
				cmd.AddParameter("Aux1Time", DbType.Double, info.Aux1Time);
				cmd.AddParameter("Aux2Time", DbType.Double, info.Aux2Time);
				cmd.AddParameter("Aux3Time", DbType.Double, info.Aux3Time);
				cmd.AddParameter("Aux4Time", DbType.Double, info.Aux4Time);
				cmd.AddParameter("Aux5Time", DbType.Double, info.Aux5Time);
				cmd.AddParameter("Aux6Time", DbType.Double, info.Aux6Time);
				cmd.AddParameter("Aux7Time", DbType.Double, info.Aux7Time);
				cmd.AddParameter("Aux8Time", DbType.Double, info.Aux8Time);
				cmd.AddParameter("Aux9Time", DbType.Double, info.Aux9Time);
				cmd.AddParameter("Aux10Time", DbType.Double, info.Aux10Time);
				cmd.AddParameter("Aux11Time", DbType.Double, info.Aux11Time);
				cmd.AddParameter("Aux12Time", DbType.Double, info.Aux12Time);
				cmd.AddParameter("Aux13Time", DbType.Double, info.Aux13Time);
				cmd.AddParameter("Aux14Time", DbType.Double, info.Aux14Time);
				cmd.AddParameter("Aux15Time", DbType.Double, info.Aux15Time);
				cmd.AddParameter("Aux16Time", DbType.Double, info.Aux16Time);
				cmd.AddParameter("Aux17Time", DbType.Double, info.Aux17Time);
				cmd.AddParameter("Aux18Time", DbType.Double, info.Aux18Time);
				cmd.AddParameter("Aux19Time", DbType.Double, info.Aux19Time);
				cmd.AddParameter("Aux20Time", DbType.Double, info.Aux20Time);
				cmd.AddParameter("Aux21Time", DbType.Double, info.Aux21Time);
				cmd.AddParameter("Aux22Time", DbType.Double, info.Aux22Time);
				cmd.AddParameter("Aux23Time", DbType.Double, info.Aux23Time);
				cmd.AddParameter("Aux24Time", DbType.Double, info.Aux24Time);
				cmd.AddParameter("Aux25Time", DbType.Double, info.Aux25Time);
				cmd.AddParameter("Aux26Time", DbType.Double, info.Aux26Time);
				cmd.AddParameter("Aux27Time", DbType.Double, info.Aux27Time);
				cmd.AddParameter("Aux28Time", DbType.Double, info.Aux28Time);
				cmd.AddParameter("Aux29Time", DbType.Double, info.Aux29Time);
				cmd.AddParameter("WorkingTime", DbType.Double, info.WorkingTime);
				cmd.AddParameter("AgentTime", DbType.Double, info.AgentTime);
				cmd.AddParameter("UnreadTime", DbType.Double, info.UnreadTime);
				cmd.AddParameter("ReadTime", DbType.Double, info.ReadTime);
				cmd.AddParameter("SystemDiscardedMessages", DbType.Int32, info.SystemDiscardedMessages);
				cmd.AddParameter("AutoRepliedMessages", DbType.Int32, info.AutoRepliedMessages);
				cmd.AddParameter("FilteredMessages", DbType.Int32, info.FilteredMessages);
				cmd.AddParameter("EnqueuedMessages", DbType.Int32, info.EnqueuedMessages);
				cmd.AddParameter("DequeuedMessages", DbType.Int32, info.DequeuedMessages);
				cmd.AddParameter("NewMessages", DbType.Int32, info.NewMessages);
				cmd.AddParameter("PeekEnqueuedMessages", DbType.Int32, info.PeekEnqueuedMessages);
				cmd.AddParameter("PeekMinutesEnqueuedMessages", DbType.Int32, info.PeekMinutesEnqueuedMessages);
				cmd.AddParameter("LoggedAgents", DbType.Int32, info.LoggedAgents);
				cmd.AddParameter("LoggedOutAgents", DbType.Int32, info.LoggedOutAgents);
				cmd.AddParameter("PeekLoggedAgents", DbType.Int32, info.PeekLoggedAgents);
				cmd.AddParameter("GroupedMessages", DbType.Int32, info.GroupedMessages);
				cmd.AddParameter("AgentGroupedMessages", DbType.Int32, info.AgentGroupedMessages);
				cmd.AddParameter("NewCases", DbType.Int32, info.NewCases);
				cmd.AddParameter("ClosedCases", DbType.Int32, info.ClosedCases);
				cmd.AddParameter("ClosedCasesByAgents", DbType.Int32, info.ClosedCasesByAgents);
				cmd.AddParameter("ClosedCasesByUsers", DbType.Int32, info.ClosedCasesByUsers);
				cmd.AddParameter("AutoClosedCases", DbType.Int32, info.AutoClosedCases);
				cmd.AddParameter("ReopenCases", DbType.Int32, info.ReopenCases);
				cmd.AddParameter("EnqueuedMessagesOnIntervalClose", DbType.Int32, info.EnqueuedMessagesOnIntervalClose);
				cmd.AddParameter("LoggedAgentsOnIntervalClose", DbType.Int32, info.LoggedAgentsOnIntervalClose);
				cmd.AddParameter("AbandonedMessages", DbType.Int32, info.AbandonedMessages);
				cmd.AddParameter("FinishedMessagesByAgent", DbType.Int32, info.FinishedMessagesByAgent);
				cmd.AddParameter("FinishedMessagesByUser", DbType.Int32, info.FinishedMessagesByUser);
				cmd.AddParameter("ReturnedToQueueMessages", DbType.Int32, info.ReturnedToQueueMessages);
				cmd.AddParameter("InboundMessages", DbType.Int32, info.InboundMessages);
				cmd.AddParameter("OutboundMessages", DbType.Int32, info.OutboundMessages);
				cmd.AddParameter("VerifiedMessages", DbType.Int32, info.VerifiedMessages);
				cmd.AddParameter("NewChats", DbType.Int32, info.NewChats);
				cmd.AddParameter("RepliedMessagesByUser", DbType.Int32, info.RepliedMessagesByUser);
				cmd.AddParameter("FlowIn", DbType.Int32, info.FlowIn);
				cmd.AddParameter("FlowOut", DbType.Int32, info.FlowOut);
				cmd.AddParameter("OutOfSLInQueue", DbType.Int32, info.OutOfSLInQueue);
				cmd.AddParameter("OutOfSLInAgent", DbType.Int32, info.OutOfSLInAgent);
				cmd.AddParameter("Expired", DbType.Int32, info.Expired);
				cmd.AddParameter("CasesOutOfSL", DbType.Int32, info.CasesOutOfSL);
				cmd.AddParameter("ReplyTime", DbType.Double, info.ReplyTime);
				cmd.AddParameter("MessagesRepliedOutOfSL", DbType.Int32, info.MessagesRepliedOutOfSL);
				cmd.AddParameter("FlowInSLL", DbType.Int32, info.FlowInSLL);
				cmd.AddParameter("FlowOutSLL", DbType.Int32, info.FlowOutSLL);
				cmd.AddParameter("OutOfSLLInQueue", DbType.Int32, info.OutOfSLLInQueue);
				cmd.AddParameter("OutOfSLLInAgent", DbType.Int32, info.OutOfSLLInAgent);
				cmd.AddParameter("ExpiredSLL", DbType.Int32, info.ExpiredSLL);
				cmd.AddParameter("CasesOutOfSLL", DbType.Int32, info.CasesOutOfSLL);
				cmd.AddParameter("MessagesRepliedOutOfSLL", DbType.Int32, info.MessagesRepliedOutOfSLL);
				cmd.AddParameter("ReopenCasesByOthers", DbType.Int32, info.ReopenCasesByOthers);
				cmd.AddParameter("OutgoingMessages", DbType.Int32, info.OutgoingMessages);
				cmd.AddParameter("AgentOutTime", DbType.Double, info.AgentOutTime);
				cmd.AddParameter("SurveysSent", DbType.Int32, info.SurveysSent);
				cmd.AddParameter("SurveysStarted", DbType.Int32, info.SurveysStarted);
				cmd.AddParameter("SurveysFinished", DbType.Int32, info.SurveysFinished);
				cmd.AddParameter("MyOutgoingCases", DbType.Int32, info.MyOutgoingCases);
				cmd.AddParameter("AttendedMessagesByAgent", DbType.Int32, info.AttendedMessagesByAgent);
				cmd.AddParameter("MessagesAttendedOutOfSL", DbType.Int32, info.MessagesAttendedOutOfSL);
				cmd.AddParameter("MessagesAttendedOutOfSLL", DbType.Int32, info.MessagesAttendedOutOfSLL);
				cmd.ExecuteNonQuery();
			}
		}

		public static List<Daily> SearchByAgents(DateTime from, DateTime to, int[] queues, int[] agents, int? agentGroup, bool onlyAllQueues, string intervalsRange)
		{
			List<Daily> result = new List<Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_SearchByAgents";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("From", DbType.DateTime, from);
					cmd.AddParameter("To", DbType.DateTime, to);
					cmd.AddEnumerationParameter("Queues", queues);
					cmd.AddEnumerationParameter("Agents", agents);
					cmd.AddParameter("AgentGroupID", DbType.Int32, agentGroup);
					cmd.AddParameter("OnlyAllQueues", DbType.Boolean, onlyAllQueues);
					cmd.AddParameter("IntervalsRange", DbType.String, intervalsRange);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.HasRows)
						{
							while (reader.Read())
							{
								Daily item = new Daily(reader);
								item.ByAgents = true;
								result.Add(item);
							}
						}
					}
				}
			}

			return result;
		}

		public static Daily.DailyDataReader SearchByAgents(DomainModel.Reports.Export.DetailedByAgentExport filters)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_SearchByAgents";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, filters.From);
				cmd.AddParameter("To", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Queues", filters.Queues);
				cmd.AddEnumerationParameter("Agents", filters.Agents);
				cmd.AddParameter("AgentGroupID", DbType.Int32, filters.AgentGroup);
				cmd.AddParameter("OnlyAllQueues", DbType.Boolean, filters.ShowOnlyAllQueues);
				cmd.AddParameter("IntervalsRange", DbType.String, filters.IntervalsRange);

				return new Daily.DailyByAgentDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static Daily.DailyByAgentDataReader SearchByAgentsAdherence(DateTime from, DateTime to, int[] agents, short minutes)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_SearchByAgentsAdherence";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, from);
				cmd.AddParameter("To", DbType.DateTime, to);
				cmd.AddEnumerationParameter("Agents", agents);
				cmd.AddParameter("MinutesToConsider", DbType.Int16, minutes);

				return new Daily.DailyByAgentDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static Daily.DailyByAgentDataReader SearchByAgentsAdherence(DomainModel.Reports.Export.AdherenceConsolidatedExport filters)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_SearchByAgentsAdherence";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, filters.From);
				cmd.AddParameter("To", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Agents", filters.Agents);
				cmd.AddParameter("MinutesToConsider", DbType.Int16, filters.Minutes);

				return new Daily.DailyByAgentDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static Daily.DailyByAgentDataReader SearchByAgentsAdherence(DomainModel.Reports.Export.AdherenceDetailedExport filters)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_SearchByAgentsAdherence";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, filters.From);
				cmd.AddParameter("To", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Agents", filters.Agents);
				cmd.AddParameter("MinutesToConsider", DbType.Int16, filters.Minutes);

				return new Daily.DailyByAgentDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static List<Daily> SearchByQueues(DateTime from, DateTime to, int[] queues, string intervalsRange)
		{
			List<Daily> result = new List<Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_SearchByQueues";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("From", DbType.DateTime, from);
					cmd.AddParameter("To", DbType.DateTime, to);
					cmd.AddEnumerationParameter("Queues", queues);
					cmd.AddParameter("IntervalsRange", DbType.String, intervalsRange);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.HasRows)
						{
							while (reader.Read())
							{
								Daily item = new Daily(reader);
								item.ByQueue = true;
								result.Add(item);
							}
						}
					}
				}
			}

			return result;
		}

		public static List<Daily> SearchByQueue(DateTime from, DateTime to, int queueId)
		{
			List<Daily> result = new List<Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_SearchByQueue";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("From", DbType.DateTime, from);
					cmd.AddParameter("To", DbType.DateTime, to);
					cmd.AddParameter("QueueID", DbType.Int32, queueId);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.HasRows)
						{
							while (reader.Read())
							{
								Daily item = new Daily(reader);
								item.ByQueue = true;
								result.Add(item);
							}
						}
					}
				}
			}

			return result;
		}

		public static Daily.DailyDataReader SearchByQueues(DomainModel.Reports.Export.DetailedByQueueExport filters)
		{
			var conn = DbManager.CreateConnection(true);
			conn.Open();

			var cmd = conn.CreateCommand();
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_SearchByQueues";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, filters.From);
				cmd.AddParameter("To", DbType.DateTime, filters.To);
				cmd.AddEnumerationParameter("Queues", filters.Queues);
				cmd.AddParameter("IntervalsRange", DbType.String, filters.IntervalsRange);

				return new Daily.DailyByQueuesDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static List<DomainModel.Historical.Daily> ConsolidateByPerson(DateTime date, int personId)
		{
			var result = new List<DomainModel.Historical.Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_ConsolidateByPerson";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("Date", DbType.Date, date);
					cmd.AddParameter("PersonID", DbType.Int32, personId);

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							var record = new DomainModel.Historical.Daily(reader);
							record.Date = date;
							record.ByAgents = true;
							result.Add(record);
						}
					}
				}
			}

			return result;
		}

		public static Daily.DailyDataReader GetAllByDay(DateTime date)
		{
			var conn = DbManager.CreateConnection();
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_GetAllByDay";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("Date", DbType.Date, date);

				return new Daily.DailyDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static Daily.DailyDataReader GetAllByDates(DateTime from, DateTime to)
		{
			var conn = DbManager.CreateConnection();
			conn.Open();

			using (var cmd = conn.CreateCommand())
			{
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandText = "HistDaily_GetAllByDates";
				cmd.CommandTimeout = 0;

				cmd.AddParameter("From", DbType.DateTime, from);
				cmd.AddParameter("To", DbType.DateTime, to);

				return new Daily.DailyDataReader(cmd.ExecuteReader(CommandBehavior.CloseConnection));
			}
		}

		public static List<Daily> GetAllByInterval(Common.Interval interval)
		{
			List<Daily> result = new List<Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_GetAllByDates";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("From", DbType.DateTime, interval.IntervalDateTime);
					cmd.AddParameter("To", DbType.DateTime, interval.IntervalDateTime.AddMinutes(30));

					using (var reader = cmd.ExecuteReader())
					{
						while (reader.Read())
						{
							Daily item = new Daily(reader);
							result.Add(item);
						}
					}
				}
			}

			return result;
		}

		public static List<Daily> SearchByQueuesByInterval(Common.Interval interval, int[] queues)
		{
			List<Daily> result = new List<Daily>();
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "HistDaily_SearchByQueuesByInterval";
					cmd.CommandTimeout = 0;

					cmd.AddParameter("Date", DbType.Date, interval.IntervalDate);
					cmd.AddParameter("Interval", DbType.Int16, interval.IntervalTime);
					cmd.AddEnumerationParameter("Queues", queues);

					using (var reader = cmd.ExecuteReader())
					{
						if (reader.HasRows)
						{
							while (reader.Read())
							{
								Daily item = new Daily(reader);
								item.ByQueue = true;
								result.Add(item);
							}
						}
					}
				}
			}

			return result;
		}
	}
}