﻿//------------------------------------------------------------------------------
// <generado automáticamente>
//     Este código fue generado por una herramienta.
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código. 
// </generado automáticamente>
//------------------------------------------------------------------------------

namespace Yoizen.Social.Web.Reports
{


	public partial class Queues
	{

		/// <summary>
		/// Control messageError.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageError;

		/// <summary>
		/// Control messageNoQueues.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageNoQueues;

		/// <summary>
		/// Control panelContent.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelContent;

		/// <summary>
		/// Control panelFilters.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelFilters;

		/// <summary>
		/// Control messageTimeZoneWarning.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageTimeZoneWarning;

		/// <summary>
		/// Control dropdownlistByIntervals.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistByIntervals;

		/// <summary>
		/// Control hiddenDailyTimeZoneToShow.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.HiddenField hiddenDailyTimeZoneToShow;

		/// <summary>
		/// Control trFromDateFilter.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlTableRow trFromDateFilter;

		/// <summary>
		/// Control textboxFromDate.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxFromDate;

		/// <summary>
		/// Control trToDateFilter.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.HtmlControls.HtmlTableRow trToDateFilter;

		/// <summary>
		/// Control textboxToDate.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.TextBox textboxToDate;

		/// <summary>
		/// Control dropdownlistFromInterval.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistFromInterval;

		/// <summary>
		/// Control dropdownlistToInterval.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistToInterval;

		/// <summary>
		/// Control dropdownlistSearchBy.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.DropDownList dropdownlistSearchBy;

		/// <summary>
		/// Control listboxQueues.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxQueues;

		/// <summary>
		/// Control placeholderQueueGroups.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.PlaceHolder placeholderQueueGroups;

		/// <summary>
		/// Control listboxQueueGroup.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxQueueGroup;

		/// <summary>
		/// Control listboxSections.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.ListBox listboxSections;

		/// <summary>
		/// Control validationScheduleDuplicate.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.CustomValidator validationScheduleDuplicate;

		/// <summary>
		/// Control buttonOKFilter.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonOKFilter;

		/// <summary>
		/// Control buttonScheduleReport.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonScheduleReport;

		/// <summary>
		/// Control panelResults.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelResults;

		/// <summary>
		/// Control panelDetailed.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelDetailed;

		/// <summary>
		/// Control messageDetailedNoRecords.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageDetailedNoRecords;

		/// <summary>
		/// Control messageDetailedTotalResults.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageDetailedTotalResults;

		/// <summary>
		/// Control messageDetailedError.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageDetailedError;

		/// <summary>
		/// Control messageDetailedTimeout.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageDetailedTimeout;

		/// <summary>
		/// Control panelTagsCharts.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Panel panelTagsCharts;

		/// <summary>
		/// Control messageTagsChartsNoRecords.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageTagsChartsNoRecords;

		/// <summary>
		/// Control buttonNewSearch.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonNewSearch;

		/// <summary>
		/// Control buttonModifyFilters.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::System.Web.UI.WebControls.Button buttonModifyFilters;

		/// <summary>
		/// Control messageExportStep2.
		/// </summary>
		/// <remarks>
		/// Campo generado automáticamente.
		/// Para modificarlo, mueva la declaración del campo del archivo del diseñador al archivo de código subyacente.
		/// </remarks>
		protected global::Yoizen.Web.UI.Message messageExportStep2;
	}
}
