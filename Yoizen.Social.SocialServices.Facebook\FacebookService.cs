﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.Business.Exceptions;
using Yoizen.Social.DomainModel;
using Yoizen.Social.Facebook;

namespace Yoizen.Social.SocialServices.Facebook
{
	/// <summary>
	/// Implementación de un <see cref="ISocialService"/> para Facebook
	/// </summary>
	public abstract class FacebookService : Business.SocialService
	{
		#region Fields

		protected Dictionary<int, DateTime> dateOfLastErrorMailSent;
		protected FacebookMessengerSocialService relatedFacebookMessengerService;

		private HttpClient client = null;

		#endregion

		#region Constructors

		public FacebookService()
		{
			this.dateOfLastErrorMailSent = new Dictionary<int, DateTime>();

			Tracer.TraceInfo("Se utilizará la versión de la API: {0}", FacebookGraphApi.Version);

			this.relatedFacebookMessengerService = null;
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve la configuración del servicio
		/// </summary>
		internal FacebookServiceConfiguration ServiceConfiguration
		{
			get
			{
				return this.Configuration as FacebookServiceConfiguration;
			}
		}

		/// <summary>
		/// Devuelve los parámetros del servicio de facebook
		/// </summary>
		protected DomainModel.ServiceSettings.FacebookSettings ServiceSettings { get { return this.service.Settings as DomainModel.ServiceSettings.FacebookSettings; } }

		#endregion

		#region Protected Methods

		/// <summary>
		/// Envia el mensaje a Messenger
		/// </summary>
		/// <param name="data">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con los datos del mensaje</param>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está enviando</param>
		/// <param name="recipient">El código del usuario de Facebook</param>
		protected async Task<string> SendMessage(Newtonsoft.Json.Linq.JObject data, DomainModel.Message message, string recipient)
		{
			if (data["messaging_type"] == null)
				data["messaging_type"] = "RESPONSE";

			var payloadText = data.ToString();
			var url = $"https://graph.facebook.com/{FacebookGraphApi.Version}/me/messages?access_token={this.ServiceConfiguration.PageAccessToken}";
			
			using (var requestMessage = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, url))
			{
				requestMessage.Content = new StringContent(payloadText, global::System.Text.Encoding.UTF8, "application/json");

				if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
					this.PublishToServiceBus != null)
				{
					var uuid = new ShortGuid(Guid.NewGuid());

					var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
					{
						HttpRequestMessage = requestMessage,
						Message = message,
						ServiceId = this.ID,
						Uuid = uuid,
						SocialUserId = recipient,
						Delay = null
					});

					if (published)
						return uuid;
				}

				//using (var response = await this.client.SendAsync(requestMessage))
				Tracer.TraceVerb("Se realizará un POST a Facebook a la url {0} con datos {1}", url, payloadText);

				using (var cts = new CancellationTokenSource(new TimeSpan(0, 0, 5)))
				{
					using (var response = await this.client.SendAsync(requestMessage, cts.Token))
					{
						if (response.IsSuccessStatusCode)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								string jsonResponse = await sr.ReadToEndAsync();
								var jResponse = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);
								var messageId = jResponse["message_id"].ToString();

								return messageId;
							}
						}
						else
						{
							var shouldRetry = false;
							if (response.Content != null)
							{
								using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
								{
									var json = await sr.ReadToEndAsync();
									Tracer.TraceError("Falló al responder mensaje de facebook: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);

									try
									{
										var jResponse = JObject.Parse(json);
										var jError = (JObject) jResponse["error"];
										var code = jError["code"].ToObject<int>();
										var errorMessage = jError["message"].ToString();
										switch (code)
										{
											case 1200: // Error temporal al enviar el mensaje. Vuelve a intentarlo más tarde
												shouldRetry = true;
												break;
											case 190: // Token de acceso de OAuth no válido.
												shouldRetry = true;
#if !NETCOREAPP
												await FacebookMessengerTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, errorMessage, code);
#endif
												break;
											default:
												break;
										}

										throw new ReplyException("Falló response el mensaje", errorMessage, code, shouldRetry);
									}
									catch { }
								}
							}
							else
							{
								Tracer.TraceError("Falló al responder mensaje de facebook: {0}-{1}", response.ReasonPhrase, response.StatusCode);
							}

							if ((int) response.StatusCode >= 500)
								throw new ReplyException("No se pudo responder el mensaje", true);
							else
								throw new ReplyException("No se pudo responder el mensaje", false);
						}
					}
				}
			}
		}

		/// <summary>
		/// Envia el mensaje a Messenger
		/// </summary>
		/// <param name="data">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con los datos del mensaje</param>
		/// <param name="message"></param>
		private async Task<string> SendAttachMessage(DomainModel.Attachment attachment, DomainModel.Message message, string recipient)
		{
			string type;
			switch (attachment.Type)
			{
				case DomainModel.Attachment.AttachmentType.Image:
					type = "image";
					break;
				case DomainModel.Attachment.AttachmentType.Audio:
					type = "audio";
					break;
				case DomainModel.Attachment.AttachmentType.Video:
					type = "video";
					break;
				case DomainModel.Attachment.AttachmentType.Unknown:
				default:
					type = "file";
					break;
			}

			return await SendAttachMessage(attachment, type, message, recipient);
		}

		/// <summary>
		/// Envia el mensaje a Messenger
		/// </summary>
		/// <param name="data">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con los datos del mensaje</param>
		/// <param name="message"></param>
		private async Task<string> SendAttachMessage(DomainModel.Attachment attachment, string type, DomainModel.Message message, string recipient)
		{
			string attachUrl;

			if (attachment.Parameters.ContainsKey(DomainModel.Attachment.SavedInAzureStorageParameter) &&
				bool.Parse(attachment.Parameters[DomainModel.Attachment.SavedInAzureStorageParameter]) &&
				attachment.Parameters.ContainsKey(DomainModel.Attachment.AzureStorageUrlInlineParameter) &&
				!string.IsNullOrEmpty(attachment.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter]))
			{
				attachUrl = attachment.Parameters[DomainModel.Attachment.AzureStorageUrlInlineParameter];
			}
			else if (attachment.Parameters.ContainsKey("Url"))
			{
				attachUrl = attachment.Parameters["Url"];
			}
			else
			{
				var jData = new Newtonsoft.Json.Linq.JObject();
				jData["account"] = this.ServiceConfiguration.PageId.ToString();
				jData["socialServiceType"] = (short) message.SocialServiceType;
				var jMsg = new Newtonsoft.Json.Linq.JObject();
				jData["msg"] = jMsg;
				var jAttach = new Newtonsoft.Json.Linq.JObject();
				jMsg["attach"] = jAttach;
				jAttach["name"] = attachment.OriginalFileName;
				jAttach["mimeType"] = attachment.MimeType;
				jAttach["size"] = attachment.FileSize;
				jAttach["type"] = (short) attachment.Type;
				jAttach["data"] = Convert.ToBase64String(attachment.Data);

				attachUrl = await this.UploadFile(this.client, "facebook", this.ServiceConfiguration.PageId.ToString(), jData);
			}

			var jBody = new JObject();
			jBody["recipient"] = new JObject();
			jBody["recipient"]["id"] = recipient;
			jBody["message"] = new JObject();
			jBody["message"]["attachment"] = new JObject();
			jBody["message"]["attachment"]["type"] = type;
			jBody["message"]["attachment"]["payload"] = new JObject();
			jBody["message"]["attachment"]["payload"]["is_reusable"] = false;
			jBody["message"]["attachment"]["payload"]["url"] = attachUrl;
			jBody["messaging_type"] = "RESPONSE";

			var payloadText = jBody.ToString();
			var url = $"https://graph.facebook.com/{FacebookGraphApi.Version}/me/messages?access_token={this.ServiceConfiguration.PageAccessToken}";

			using (var requestMessage = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, url))
			{
				requestMessage.Content = new StringContent(payloadText, global::System.Text.Encoding.UTF8, "application/json");

				if (Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
					this.PublishToServiceBus != null)
				{
					var uuid = new ShortGuid(Guid.NewGuid());

					var published = await this.PublishToServiceBus(new PublicMessageServiceBusOptions()
					{
						HttpRequestMessage = requestMessage,
						Message = message,
						ServiceId = this.ID,
						Uuid = uuid,
						SocialUserId = recipient,
						Delay = null
					});

					if (published)
						return uuid;
				}

				using (var response = await this.client.SendAsync(requestMessage))
				{
					if (response.IsSuccessStatusCode)
					{
						using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
						{
							var jsonResponse = await sr.ReadToEndAsync();
							var jResponse = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);
							var messageId = jResponse["message_id"].ToString();

							return messageId;
						}
					}
					else
					{
						var shouldRetry = false;
						if (response.Content != null)
						{
							using (var sr = new StreamReader(await response.Content.ReadAsStreamAsync()))
							{
								var json = await sr.ReadToEndAsync();
								Tracer.TraceError("Falló al responder mensaje de facebook: {0}-{1} - {2}", response.ReasonPhrase, response.StatusCode, json);

								try
								{
									var jResponse = JObject.Parse(json);
									var jError = (JObject) jResponse["error"];
									var code = jError["code"].ToObject<int>();
									var errorMessage = jError["message"].ToString();
									switch (code)
									{
										case 1200: // Error temporal al enviar el mensaje. Vuelve a intentarlo más tarde
											shouldRetry = true;
											break;
										case 190: // Token de acceso de OAuth no válido.
											shouldRetry = true;
#if !NETCOREAPP
											await FacebookMessengerTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, errorMessage, code);
#endif
											break;
										default:
											break;
									}

									throw new ReplyException("Falló response el mensaje", errorMessage, code, shouldRetry);
								}
								catch { }
							}
						}
						else
						{
							Tracer.TraceError("Falló al responder mensaje de facebook: {0}-{1}", response.ReasonPhrase, response.StatusCode);
						}

						if ((int) response.StatusCode >= 500)
							throw new ReplyException("No se pudo responder el mensaje", true);
						else
							throw new ReplyException("No se pudo responder el mensaje", false);
					}
				}
			}
		}

#if !NETCOREAPP
		/// <summary>
		/// Busca información en Facebook correspondientes a entidades que soporten paginación
		/// </summary>
		/// <typeparam name="ObjectType">Indica el <see cref="System.Type"/> del objeto que soporta paginación</typeparam>
		/// <typeparam name="CollectionType">Indica el <see cref="System.Type"/> de la colección que tiene el el objeto de paginación 
		/// (Miembro <see cref="Wrappers.IData.Parameters"/>)</typeparam>
		/// <typeparam name="WrapperType">Indica el <see cref="System.Type"/> de los objetos que va a tener la colección adentro y que serán
		/// los objetos que se devuelvan en el parámetro <paramref name="objects"/>. Deberán heredar de <see cref="Wrappers.FacebookObject"/></typeparam>
		/// <param name="fb">El <see cref="FacebookClient"/> para conectarse a Facebook</param>
		/// <param name="objectWithData">El objeto de facebook que soporta paginación</param>
		/// <param name="objects">Una lista de <typeparamref name="WrapperType"/> con los wrappers de los objetos de Facebook</param>
		/// <param name="previous">Si es true y hay datos de paginación se buscarán datos previos</param>
		/// <param name="next">Si es true y hay datos de paginación se buscarán datos posteriores</param>
		protected void RetrieveObjects<ObjectType, CollectionType, WrapperType>(global::Facebook.FacebookClient fb, ObjectType objectWithData, List<WrapperType> objects, bool previous, bool next)
			where ObjectType : Wrappers.IPaging, Wrappers.IData<CollectionType, WrapperType>
			where CollectionType : List<WrapperType>
			where WrapperType : Wrappers.FacebookObject
		{
			RetrieveObjects<ObjectType, CollectionType, WrapperType>(fb, objectWithData, objects, previous, next, null);
		}

		/// <summary>
		/// Busca información en Facebook correspondientes a entidades que soporten paginación
		/// </summary>
		/// <typeparam name="ObjectType">Indica el <see cref="System.Type"/> del objeto que soporta paginación</typeparam>
		/// <typeparam name="CollectionType">Indica el <see cref="System.Type"/> de la colección que tiene el el objeto de paginación 
		/// (Miembro <see cref="Wrappers.IData.Parameters"/>)</typeparam>
		/// <typeparam name="WrapperType">Indica el <see cref="System.Type"/> de los objetos que va a tener la colección adentro y que serán
		/// los objetos que se devuelvan en el parámetro <paramref name="objects"/>. Deberán heredar de <see cref="Wrappers.FacebookObject"/></typeparam>
		/// <param name="fb">El <see cref="FacebookClient"/> para conectarse a Facebook</param>
		/// <param name="objectWithData">El objeto de facebook que soporta paginación</param>
		/// <param name="objects">Una lista de <typeparamref name="WrapperType"/> con los wrappers de los objetos de Facebook</param>
		/// <param name="previous">Si es true y hay datos de paginación se buscarán datos previos</param>
		/// <param name="next">Si es true y hay datos de paginación se buscarán datos posteriores</param>
		/// <param name="shouldStopProcessingEval">Un método que evalúe cuándo se debe dejar de procesar los objetos que se obtienen. Cuando es distinto
		/// de <code>null</code> la función devolverá <code>true</code> cuando un objeto de tipo <typeparamref name="WrapperType"/> cumple
		/// una condición y los futuros objetos no deberán ser considerados; en caso contrario, <code>false</code></param>
		protected void RetrieveObjects<ObjectType, CollectionType, WrapperType>(global::Facebook.FacebookClient fb, ObjectType objectWithData, List<WrapperType> objects, bool previous, bool next, Func<WrapperType, bool> shouldStopProcessingEval)
			where ObjectType : Wrappers.IPaging, Wrappers.IData<CollectionType, WrapperType>
			where CollectionType : List<WrapperType>
			where WrapperType : Wrappers.FacebookObject
		{
			foreach (WrapperType message in objectWithData.Data)
			{
				if (shouldStopProcessingEval != null && !shouldStopProcessingEval(message))
					return;

				objects.Add(message);
			}

			if (objectWithData.Paging != null)
			{
				if (next)
					RetrieveObjects<ObjectType, CollectionType, WrapperType>(fb, objectWithData.Paging.Next, objects, false, true, shouldStopProcessingEval);
				if (previous)
					RetrieveObjects<ObjectType, CollectionType, WrapperType>(fb, objectWithData.Paging.Previous, objects, true, false, shouldStopProcessingEval);
			}
		}

		/// <summary>
		/// Busca información en Facebook a partir de la consulta especificada por <paramref name="query"/>
		/// </summary>
		/// <typeparam name="ObjectType">Indica el <see cref="System.Type"/> del objeto que soporta paginación</typeparam>
		/// <typeparam name="CollectionType">Indica el <see cref="System.Type"/> de la colección que tiene el el objeto de paginación 
		/// (Miembro <see cref="Wrappers.IData.Parameters"/>)</typeparam>
		/// <typeparam name="WrapperType">Indica el <see cref="System.Type"/> de los objetos que va a tener la colección adentro y que serán
		/// los objetos que se devuelvan en el parámetro <paramref name="objects"/>. Deberán heredar de <see cref="Wrappers.FacebookObject"/></typeparam>
		/// <param name="fb">El <see cref="FacebookClient"/> para conectarse a Facebook</param>
		/// <param name="query">La consulta a Facebook que se realizará</param>
		/// <param name="objects">Una lista de <typeparamref name="WrapperType"/> con los wrappers de los objetos de Facebook</param>
		/// <param name="previous">Si es true y hay datos de paginación se buscarán datos previos</param>
		/// <param name="next">Si es true y hay datos de paginación se buscarán datos posteriores</param>
		/// <param name="shouldStopProcessingEval">Un método que evalúe cuándo se debe dejar de procesar los objetos que se obtienen. Cuando es distinto
		/// de <code>null</code> la función devolverá <code>true</code> cuando un objeto de tipo <typeparamref name="WrapperType"/> cumple
		/// una condición y los futuros objetos no deberán ser considerados; en caso contrario, <code>false</code></param>
		protected void RetrieveObjects<ObjectType, CollectionType, WrapperType>(global::Facebook.FacebookClient fb, string query, List<WrapperType> objects, bool previous, bool next, Func<WrapperType, bool> shouldStopProcessingEval)
			where ObjectType : Wrappers.IPaging, Wrappers.IData<CollectionType, WrapperType>
			where CollectionType : List<WrapperType>
			where WrapperType : Wrappers.FacebookObject
		{
			if (query == null)
				return;

			Tracer.TraceVerb("Facebook {0}: Se fue a buscar: {1}", ServiceConfiguration.PageName, query);
			var resultObject = (global::Facebook.JsonObject) fb.Get(query);

			ObjectType objectWithData = (ObjectType) Activator.CreateInstance(typeof(ObjectType), resultObject);
			if (objectWithData.Data.Count == 0)
				return;

			RetrieveObjects<ObjectType, CollectionType, WrapperType>(fb, objectWithData, objects, previous, next, shouldStopProcessingEval);
		}
#endif

		/// <summary>
		/// Devuelve si se debe reintentar hacer un Request a facebook luego de que falló alguna operación
		/// </summary>
		/// <param name="ex">La <see cref="Exception"/> que ocurrió en el último llamado</param>
		/// <returns><code>true</code> si se debe reintentar el request; en caso contrario, <code>false</code></returns>
		protected bool ShouldRetryQueryByException(Exception ex)
		{
			var shouldTryLater = false;
			if (ex as WebException != null)
			{
				var webEx = ex as WebException;
				switch (webEx.Status)
				{
					case WebExceptionStatus.NameResolutionFailure:
					case WebExceptionStatus.ConnectFailure:
					case WebExceptionStatus.ReceiveFailure:
					case WebExceptionStatus.ProtocolError:
					case WebExceptionStatus.ConnectionClosed:
					case WebExceptionStatus.Timeout:
					case WebExceptionStatus.ProxyNameResolutionFailure:
					case WebExceptionStatus.UnknownError:
					case WebExceptionStatus.RequestProhibitedByProxy:
						shouldTryLater = true;
						break;
					default:
						break;
				}
			}
#if !NETCOREAPP
			else if (ex as global::Facebook.FacebookApiException != null)
			{
				var fbEx = ex as global::Facebook.FacebookApiException;
				switch (fbEx.ErrorCode)
				{
					case 1: /* An unknown error occurred */
					case 2: /* An unexpected error has occurred. Please retry your request later */
					case 4: /* Demasiadas llamadas a la API */
					case 32: /* Page request limited reached */
					case 190: /* Error validating access token */
					case 341: /* Límite de aplicación alcanzado */
						shouldTryLater = true;
						break;
					default:
						break;
				}
			}
			else if (ex as global::Facebook.WebExceptionWrapper != null)
			{
				var webEx = ex as global::Facebook.WebExceptionWrapper;
				switch (webEx.Status)
				{
					case WebExceptionStatus.NameResolutionFailure:
					case WebExceptionStatus.ConnectFailure:
					case WebExceptionStatus.ReceiveFailure:
					case WebExceptionStatus.ProtocolError:
					case WebExceptionStatus.ConnectionClosed:
					case WebExceptionStatus.Timeout:
					case WebExceptionStatus.ProxyNameResolutionFailure:
					case WebExceptionStatus.UnknownError:
					case WebExceptionStatus.RequestProhibitedByProxy:
						shouldTryLater = true;
						break;
					default:
						break;
				}
			}
#endif

			return shouldTryLater;
		}

#if !NETCOREAPP
		/// <summary>
		/// Devuelve un <see cref="Post"/> a partir de su código
		/// </summary>
		/// <param name="postId">El código de publicación</param>
		/// <param name="shouldRetry">Cuando retorna, devuelve si se debe reintentar luego volver a pedir esta publicación</param>
		/// <returns>Un <see cref="Post"/> con los datos de la publicación o <code>null</code> en caso de que no se haya encontrado o haya ocurrido algún error</returns>
		protected async Task<(Post, bool)> GetPostDetails(string postId)
		{
			bool shouldRetry = false;

			try
			{
				var parameters = new Dictionary<string, object>
				{
					{
						"fields",
						"is_hidden,attachments,created_time,from{id,about,birthday,email,first_name,gender,hometown,last_name,link,location,locale,middle_name,name,website,is_community_page},id,likes,message,parent_id,comments,status_type,place"
					}
				};
				var fb = new global::Facebook.FacebookClient(this.ServiceConfiguration.PageAccessToken) { Version = FacebookGraphApi.Version };
				dynamic resultFeed = await fb.GetTaskAsync(string.Format("/{0}", postId), parameters);

				Wrappers.Post wrappedPost = new Wrappers.Post(resultFeed);

				var postToInsert = await Converter.Convert(wrappedPost, this.ServiceConfiguration);
				if (postToInsert.PostedBy.ID == this.ServiceConfiguration.PageId)
				{
					// Por definición las publicaciones de la página nunca son respuestas ya que no pueden mencionar a usuarios
					postToInsert.Incoming = false;
					postToInsert.IsReply = false;
				}

				shouldRetry = false;
				return (postToInsert, shouldRetry);
			}
			catch (global::Facebook.FacebookOAuthException ex)
			{
				Tracer.TraceError("Facebook {0}: Falló obtener publicación {1} de facebook: {2}", this.ServiceConfiguration.PageName, postId, ex);
				await FacebookTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, ex);
				shouldRetry = ShouldRetryQueryByException(ex);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Facebook {0}: Falló obtener publicación {1} de facebook: {2}", this.ServiceConfiguration.PageName, postId, ex);
				shouldRetry = ShouldRetryQueryByException(ex);
			}

			return (null, shouldRetry);
		}

		/// <summary>
		/// Devuelve un <see cref="Comment"/> a partir de su código
		/// </summary>
		/// <param name="commentId">El código de comentario</param>
		/// <param name="postId">El código de publicación al que pertenece el comentario</param>
		/// <param name="shouldRetry">Cuando retorna, devuelve si se debe reintentar luego volver a pedir este comentario</param>
		/// <returns>Un <see cref="Comment"/> con los datos del comentario o <code>null</code> en caso de que no se haya encontrado o haya ocurrido algún error</returns>
		protected async Task<(Comment, bool)> GetCommentDetails(string commentId, string postId)
		{
			bool shouldRetry = false;

			try
			{
				var parameters = new Dictionary<string, object>
				{
					{
						"fields",
						"id,attachment{media,media_type,type,url,description},can_comment,can_remove,can_hide,can_like,can_reply_privately,comment_count,created_time,from{id,about,birthday,email,first_name,gender,hometown,last_name,link,location,locale,middle_name,name,website,is_community_page},is_hidden,is_private,like_count,message,parent,private_reply_conversation,user_likes"
					}
				};

				Tracer.TraceInfo("Facebook {0}: Obteniendo comentario", this.ServiceConfiguration.PageName);
				var fb = new global::Facebook.FacebookClient(this.ServiceConfiguration.PageAccessToken) { Version = FacebookGraphApi.Version };
				dynamic resultFeed = await fb.GetTaskAsync(string.Format("/{0}", commentId), parameters);

				var wrappedComment = new Wrappers.Comment(resultFeed);

				var commentToInsert = await Converter.Convert(wrappedComment, this.ServiceConfiguration, postId);

				if (commentToInsert.PostedBy.ID == this.ServiceConfiguration.PageId)
				{
					commentToInsert.Incoming = false;
					commentToInsert.IsReply = true;
				}

				shouldRetry = false;
				return (commentToInsert, shouldRetry);
			}
			catch (global::Facebook.FacebookOAuthException ex)
			{
				Tracer.TraceError("Facebook {0}: Falló obtener comentario {1} de facebook: {2}", this.ServiceConfiguration.PageName, commentId, ex);
				await FacebookTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, ex);
				shouldRetry = ShouldRetryQueryByException(ex);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Facebook {0}: Falló obtener comentario {1} de facebook: {2}", this.ServiceConfiguration.PageName, commentId, ex);
				shouldRetry = ShouldRetryQueryByException(ex);
			}

			return (null, shouldRetry);
		}
#endif

		#endregion

		#region ISocialService Methods

		/// <summary>
		/// Devuelve el <see cref="System.Type"/> de la configuración del servicio
		/// </summary>
		public override Type ConfigurationType { get { return typeof(FacebookServiceConfiguration); } }

		/// <summary>
		/// Devuelve si el servicio necesita de otro servicio para realizar sus operaciones
		/// </summary>
		/// <param name="relatedSocialServiceId">Cuando el servicio necesita de otro devuelve el código del servicio</param>
		/// <returns>true en caso de necesitar de otro servicio; en caso contrario, false</returns>
		public override bool RequiresRelatedSocialService(out int relatedSocialServiceId)
		{
			relatedSocialServiceId = 0;
			return false;
		}

		/// <summary>
		/// Establece el servicio relacionado
		/// </summary>
		/// <param name="connector">Un <see cref="SocialServiceConnector"/> que se utilizará para realizar las operaciones
		/// del <see cref="ISocialServiceOperations"/></param>
		public override void SetRelatedSocialService(DomainModel.ISocialServiceOperations connector)
		{
			throw new NotSupportedException("El servicio no necesita de ningún servicio relacionado");
		}

		/// <summary>
		/// Devuelve si un mensaje fue enviado por el usuario configurado en el servicio
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje</param>
		/// <returns>true si el mensaje fue enviado por el usuario configurado; en caso contrario, false</returns>
		public override bool DoesMessageBelongsToConfiguredUser(DomainModel.Message message)
		{
			if (message == null)
				throw new ArgumentNullException("message");

			if (!message.Outgoing)
				return false;

			return message.PostedBy.ID == this.ServiceConfiguration.PageId;
		}

		/// <summary>
		/// Inicializa el servicio a partir de los datos de configuración
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con la información del servicio</param>
		/// <param name="serviceConfiguration">Los datos de configuración del servicio</param>
		/// <param name="serviceStatus">El estado inicial del servicio</param>
		/// <param name="validUntil">Especifica la fecha hasta la cual el servicio es válido o <code>null</code> para indicar que no vence</param>
		/// <param name="pathForFiles">Especifica el directorio donde se pueden grabar archivos</param>
		public override void Initialize(DomainModel.Service service, DomainModel.IServiceConfiguration serviceConfiguration, DomainModel.ISocialServiceStatus serviceStatus, DateTime? validUntil, string pathForFiles)
		{
			base.Initialize(service, serviceConfiguration, serviceStatus, validUntil, pathForFiles);

			this.relatedFacebookMessengerService = null;

			if (this.client != null)
				this.client.Dispose();

			this.client = new HttpClient();
			//this.client.Timeout = TimeSpan.FromSeconds(5);
		}

		/// <summary>
		/// Reconfigura el servicio a partir de los datos de configuración
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con la información del servicio</param>
		/// <param name="serviceConfiguration">Los datos de configuración del servicio</param>
		/// <remarks>
		/// Este método es llamado cuando cambian los datos del usuario
		/// </remarks>
		public override void Reconfigure(DomainModel.Service service, DomainModel.IServiceConfiguration serviceConfiguration)
		{
			base.Reconfigure(service, serviceConfiguration);
			this.dateOfLastErrorMailSent = new Dictionary<int, DateTime>();

			this.relatedFacebookMessengerService = null;
		}

		/// <summary>
		/// Obtiene los mensajes pendientes de la red social
		/// </summary>
		/// <returns>Una lista de <see cref="DomainModel.Message"/> con los mensajes de la red social</returns>
		public override abstract Task<IEnumerable<DomainModel.Message>> Query();

#if NETCOREAPP
		/// <summary>
		/// Obtiene un mensaje de la red social
		/// </summary>
		/// <param name="messageId">El código de mensaje a obtener</param>
		/// <returns>Un <see cref="DomainModel.Message"/> con los mensaje de la red social</returns>
		public override Task<DomainModel.Message> Query(string messageId)
		{
			throw new NotImplementedException("El servicio NO soporta mensajes públicos de facebook");
		}
#else
		/// <summary>
		/// Obtiene un mensaje de la red social
		/// </summary>
		/// <param name="messageId">El código de mensaje a obtener</param>
		/// <returns>Un <see cref="DomainModel.Message"/> con los mensaje de la red social</returns>
		public override async Task<DomainModel.Message> Query(string messageId)
		{
			Tracer.TraceInfo("Facebook {0}: Se consultará por el post/comentario {1}", this.ServiceConfiguration.PageName, messageId);

			var fb = new global::Facebook.FacebookClient(ServiceConfiguration.PageAccessToken);
			fb.Version = FacebookGraphApi.Version;

			var objectMetadata = await fb.GetTaskAsync<global::Facebook.JsonObject>(string.Format("/{0}?metadata=1", messageId));
			string type = ((global::Facebook.JsonObject) objectMetadata["metadata"])["type"].ToString();

			if (type.Equals("comment"))
			{
				string postId = string.Format("{0}_{1}", this.ServiceConfiguration.PageId, messageId.Substring(0, messageId.IndexOf("_")));
				var (message, _) = await GetCommentDetails(messageId, postId);
				return message;
			}
			else
			{
				var (message, _) = await GetPostDetails(messageId);
				return message;
			}
		}
#endif

		/// <summary>
		/// Responde un mensaje
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje que se responderá</param>
		/// <returns>El código del mensaje respondido</returns>
		public override async Task<string> Reply(DomainModel.Message message)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Common.Tracer.TraceInfo("La licencia indica que se trabaja en modo lectura. No se envía");
				return Guid.NewGuid().ToString();
			}

			if (message.IsDirectMessage)
			{
				DomainModel.SocialUser socialUser = null;
				if (message.SocialUser != null)
					socialUser = message.SocialUser;
				else if (message.RepliesToSocialUser != null)
					socialUser = message.RepliesToSocialUser;
				else if (message.RepliesTo != null && message.RepliesTo.SocialUser != null)
					socialUser = message.RepliesTo.SocialUser;

				if (socialUser != null)
				{
					long replyTo = socialUser.ID;
					if (socialUser.Parameters.ContainsKey(Social.Facebook.User.MessengerIDParameter))
						replyTo = long.Parse(socialUser.Parameters[Social.Facebook.User.MessengerIDParameter]);

					if (!message.HasAttach &&
						message.AssociatedMessage != null && 
						message.AssociatedMessage.Parameters.ContainsKey(Social.Facebook.Comment.CanReplyPrivatelyParameter) && 
						Convert.ToBoolean(message.AssociatedMessage.Parameters[Social.Facebook.Comment.CanReplyPrivatelyParameter]))
					{
						Tracer.TraceVerb("El mensaje {0} es un mensaje privado que responde al comentario {1}. Se lo responde utilizando la nueva funcionalidad de respuestas privadas en comentarios", message, message.AssociatedMessage);

						try
						{
							JObject jRecipient = new JObject();
							
							if (message.AssociatedMessage.RepliesToSocialMessageID == null)
								jRecipient["post_id"] = message.AssociatedMessage.SocialMessageID;
							else
								jRecipient["comment_id"] = message.AssociatedMessage.SocialMessageID;

							JObject data = new JObject();
							data["recipient"] = jRecipient;
							data["message"] = new JObject();
							data["message"]["text"] = message.Body;
							data["messaging_type"] = "RESPONSE";

							if (socialUser.Parameters.ContainsKey(Social.Facebook.User.ThreadLastUpdatedParameter))
							{
								var date = DateTime.Parse(socialUser.Parameters[Social.Facebook.User.ThreadLastUpdatedParameter]);
								if (DateTime.Now.Subtract(date).TotalHours >= 24)
								{
									data["messaging_type"] = "MESSAGE_TAG";
									data["tag"] = "HUMAN_AGENT";
								}
							}

							return await SendMessage(data, message, replyTo.ToString());
						}
#if !NETCOREAPP
						catch (global::Facebook.FacebookOAuthException ex)
						{
							Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a Facebook. Mensaje: {2} - Código: {3}", this.ServiceConfiguration.PageName, message.ID, ex.Message, ex.ErrorCode);
							await FacebookTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, ex);
						}
						catch (global::Facebook.FacebookApiException ex)
						{
							Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a Facebook. Mensaje: {2} - Código: {3}", this.ServiceConfiguration.PageName, message.ID, ex.Message, ex.ErrorCode);
						}
#endif
						catch (Exception ex)
						{
							Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a Facebook. Mensaje: {2}", this.ServiceConfiguration.PageName, message.ID, ex.Message);
						}
					}

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow &&
						DomainModel.SystemSettings.Instance.YFlow.Enabled &&
						service.UsesYFlow)
					{
						if (this.relatedFacebookMessengerService == null)
						{
							this.relatedFacebookMessengerService = new FacebookMessengerSocialService();
							var messengerConfiguration = new FacebookMessengerServiceConfiguration(this.ServiceConfiguration);
							this.relatedFacebookMessengerService.Initialize(this.service, messengerConfiguration, this.Status, this.ValidUntil, this.pathForFiles);

							if (this.PublishToServiceBus != null)
								this.relatedFacebookMessengerService.PublishToServiceBus = this.PublishToServiceBus;
						}

						return await this.relatedFacebookMessengerService.Reply(message);
					}

					try
					{
						JObject jRecipient = new JObject();
						jRecipient["id"] = replyTo.ToString();

						JObject data = new JObject();
						data["recipient"] = jRecipient;
						data["message"] = new JObject();
						data["messaging_type"] = "RESPONSE";
						data["message"]["text"] = message.Body;

						if (socialUser.Parameters.ContainsKey(Social.Facebook.User.ThreadLastUpdatedParameter))
						{
							var date = DateTime.Parse(socialUser.Parameters[Social.Facebook.User.ThreadLastUpdatedParameter]);
							if (DateTime.Now.Subtract(date).TotalHours >= 24)
							{
								data["messaging_type"] = "MESSAGE_TAG";
								data["tag"] = "HUMAN_AGENT";
							}
						}

						var messageId = await SendMessage(data, message, replyTo.ToString());

						if (message.HasAttach && message.Attachments != null && message.Attachments.Length > 0)
						{
							var attachMessageId = await SendAttachMessage(message.Attachments[0], message, replyTo.ToString());
							if (messageId == null)
								messageId = attachMessageId;
						}

						return messageId;
					}
					catch (Exception ex)
					{
						Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a Facebook. Mensaje: {2}", this.ServiceConfiguration.PageName, message.ID, ex.Message);

						throw new ReplyException(string.Format("Falló el envío del mensaje privado {0} a Facebook", message.ID),
							ex.Message,
							false);
					}
				}
				else
				{
					Tracer.TraceInfo("No se encontró información del usuario para el mensaje {0}. No se lo mandará", message);

					return null;
				}
			}
#if NETCOREAPP
			throw new ReplyException(string.Format("Falló el envío del comentario/publicación {0} a Facebook", message.ID), false);
#else
			else
			{
				var fb = new global::Facebook.FacebookClient(ServiceConfiguration.PageAccessToken);
				fb.Version = FacebookGraphApi.Version;

				var parameters = new Dictionary<string, object>();
				var body = message.Body;
				if (this.ServiceConfiguration.MentionUsersOnReply)
				{
					if (!body.StartsWith(message.SocialUser.DisplayName, StringComparison.InvariantCultureIgnoreCase))
					{
						body = string.Format("@[{0}] {1}", message.SocialUser.ID, body);
					}
					else
					{
						body = body.Substring(message.SocialUser.DisplayName.Length);
						if (body.StartsWith(" "))
							body = string.Format("@[{0}]{1}", message.SocialUser.ID, body);
						else
							body = string.Format("@[{0}] {1}", message.SocialUser.ID, body);
					}
				}

				parameters.Add("message", body);

				// --> Si tengo un attachment lo agrego al comentario

				if (message.Attachments != null && message.Attachments.Any())
				{
					var attach = message.Attachments.FirstOrDefault();
					if (attach != null)
					{
						var fmo = new global::Facebook.FacebookMediaObject
						{
							FileName = attach.OriginalFileName,
							ContentType = "image/jpeg"
						};

						fmo.SetValue(attach.Data);
						parameters.Add("attachment", fmo);
					}
				}

				try
				{
					string endpoint = null;
					if (this.ServiceConfiguration.AllowCommentReplies && message.RepliesTo != null && message.RepliesTo is Social.Facebook.Comment)
					{
						if (message.RepliesTo.Parameters.ContainsKey(Social.Facebook.Comment.ParentParameter))
							endpoint = string.Format("/{0}/comments", message.RepliesTo.Parameters[Social.Facebook.Comment.ParentParameter]);
						else if (message.RepliesTo.Parameters.ContainsKey(Social.Facebook.Comment.CanCommentParameter) &&
							bool.Parse(message.RepliesTo.Parameters[Social.Facebook.Comment.CanCommentParameter]))
							endpoint = string.Format("/{0}/comments", message.RepliesTo.SocialMessageID);
					}

					if (endpoint == null)
					{
						if (message.RepliesToSocialMessageID != null)
							endpoint = string.Format("/{0}/comments", message.RepliesToSocialMessageID);
						else if (message.SocialConversationID != null)
							endpoint = string.Format("/{0}/comments", message.SocialConversationID);
					}

					if (endpoint == null)
					{
						Tracer.TraceWarning("Facebook {0}: Falló el envío del comentario {1} a Facebook ya que no se tiene conocimiendo del post al cual se aplicará el comentario", this.ServiceConfiguration.PageName, message.ID);
						throw new ServiceException(string.Format("Falló el envío del comentario {0} a Facebook ya que no se tiene conocimiendo del post al cual se aplicará el comentario", message.ID));
					}

					string commentId = null;

					object result = await fb.PostTaskAsync(endpoint, parameters);

					if (result is global::Facebook.JsonObject)
						commentId = ((global::Facebook.JsonObject) result)["id"].ToString();
					else
						commentId = result.ToString();

					try
					{
						var commentObject = await fb.GetTaskAsync<global::Facebook.JsonObject>(string.Format("/{0}?fields=is_hidden,attachment,can_comment,can_hide,can_like,can_reply_privately,can_remove,comment_count,created_time,from,id,is_private,like_count,message,parent,private_reply_conversation,user_likes", commentId));
						var commentWrapper = new Wrappers.Comment(commentObject);

						if (message.Parameters == null)
							message.Parameters = new Dictionary<string, string>();
						Converter.FillParameters(message.Parameters, commentWrapper);
					}
					catch (global::Facebook.FacebookOAuthException ex)
					{
						Tracer.TraceWarning("Facebook {0}: Falló obtener información del nuevo comentario {1}. Mensaje: {2} - Código: {3}", this.ServiceConfiguration.PageName, commentId, ex.Message, ex.ErrorCode);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Facebook {0}: Falló obtener datos del comentario envíado: {1}", this.ServiceConfiguration.PageName, ex);
					}

					return commentId;
				}
				catch (global::Facebook.FacebookOAuthException ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del comentario {1} a Facebook. Mensaje: {2} - Código: {3}", this.ServiceConfiguration.PageName, message.ID, ex.Message, ex.ErrorCode);

					await FacebookTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, ex);

					throw new ReplyException(string.Format("Falló el envío del comentario {0} a Facebook", message.ID),
						ex.Message,
						ex.ErrorCode,
						true);
				}
				catch (global::Facebook.FacebookApiException ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del comentario {1} a Facebook. Mensaje: {2} - Código: {3}", this.ServiceConfiguration.PageName, message.ID, ex.Message, ex.ErrorCode);

					bool shouldRetry = false;
					switch (ex.ErrorCode)
					{
						case 102: // API Session
						case 1: // API Unknown
						case 2: // API Service temporary down
						case 4: // API Too Many Calls
						case 17: // API User Too Many Calls
						case 190: // Access token has expired
						case 341: // Application limit reached
						case 368: // Temporarily blocked for policies violations
							shouldRetry = true;
							break;
						default:
							break;
					}

					throw new ReplyException(string.Format("Falló el envío del comentario {0} a Facebook", message.ID),
						ex.Message,
						ex.ErrorCode,
						shouldRetry);
				}
				catch (Exception ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del comentario {1} a Facebook. Mensaje: {2}", this.ServiceConfiguration.PageName, message.ID, ex.Message);

					throw new ReplyException(string.Format("Falló el envío del comentario {0} a Facebook", message.ID),
						ex.Message,
						false);
				}
			}
#endif
		}

		/// <summary>
		/// Envía un mensaje
		/// </summary>
		/// <param name="message">Un <see cref="DomainModel.Message"/> con los datos del mensaje que se enviará</param>
		/// <returns>El código del mensaje enviado</returns>
		/// <remarks>
		/// A diferencia de <see cref="ISocialServiceOperations.Reply(DomainModel.Message)"/> este método sirve para enviar mensajes que no estén relacionados a ningún
		/// otro ni que respondan a otro mensaje
		/// </remarks>
		public override async Task<string> Send(DomainModel.Message message)
		{
			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Common.Tracer.TraceInfo("La licencia indica que se trabaja en modo lectura. No se envía");
				return Guid.NewGuid().ToString();
			}

			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (!message.IsDirectMessage)
				throw new ArgumentOutOfRangeException(nameof(message), message.IsDirectMessage, "El mensaje debe ser un mensaje privado");

			if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow &&
				DomainModel.SystemSettings.Instance.YFlow.Enabled &&
				service.UsesYFlow)
			{
				if (this.relatedFacebookMessengerService == null)
				{
					this.relatedFacebookMessengerService = new FacebookMessengerSocialService();
					var messengerConfiguration = new FacebookMessengerServiceConfiguration(this.ServiceConfiguration);
					this.relatedFacebookMessengerService.Initialize(this.service, messengerConfiguration, this.Status, this.ValidUntil, this.pathForFiles);
				}

				return await this.relatedFacebookMessengerService.Send(message);
			}

			DomainModel.SocialUser socialUser = null;
			if (message.SocialUser != null)
				socialUser = message.SocialUser;
			else if (message.RepliesToSocialUser != null)
				socialUser = message.RepliesToSocialUser;
			else if (message.RepliesTo != null && message.RepliesTo.SocialUser != null)
				socialUser = message.RepliesTo.SocialUser;

			if (socialUser != null)
			{
				long replyTo = socialUser.ID;
				if (socialUser.Parameters.ContainsKey(Social.Facebook.User.MessengerIDParameter))
					replyTo = long.Parse(socialUser.Parameters[Social.Facebook.User.MessengerIDParameter]);

				try
				{
					JObject jRecipient = new JObject();
					jRecipient["id"] = replyTo.ToString();

					JObject data = new JObject();
					data["recipient"] = jRecipient;
					data["message"] = new JObject();
					data["message"]["text"] = message.Body;

					return await SendMessage(data, message, replyTo.ToString());
				}
#if !NETCOREAPP
				catch (global::Facebook.FacebookOAuthException ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a la conversación del usuario {2} de Facebook. Mensaje: {3} - Código: {4}", this.ServiceConfiguration.PageName, message.Body, socialUser.ID, ex.Message, ex.ErrorCode);
					await FacebookTokens.SendMail(this, this.ServiceSettings.OAuthErrorOcurred, ex);

					throw new ServiceException(string.Format("Falló el envío del mensaje privado {0} a Facebook", message.Body),
						ex.Message,
						ex.ErrorCode);
				}
				catch (global::Facebook.FacebookApiException ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a la conversación del usuario {2} de Facebook. Mensaje: {3} - Código: {4}", this.ServiceConfiguration.PageName, message.Body, socialUser.ID, ex.Message, ex.ErrorCode);

					throw new ServiceException(string.Format("Falló el envío del mensaje privado {0} a Facebook", message.Body),
						ex.Message,
						ex.ErrorCode);
				}
#endif
				catch (Exception ex)
				{
					Tracer.TraceWarning("Facebook {0}: Falló el envío del mensaje privado {1} a la conversación del usuario {2} de Facebook. Mensaje: {3}", this.ServiceConfiguration.PageName, message.Body, socialUser.ID, ex.Message);

					throw new ServiceException(string.Format("Falló el envío del mensaje privado {0} a Facebook", message.Body),
						ex.Message);
				}
			}
			else
			{
				Tracer.TraceInfo("No se encontró información del usuario para el mensaje {0}. No se lo mandará", message);
				throw new SendException("No se puede enviar el mensaje privado ya que no existe una conversación privada con el usuario");
			}
		}

		/// <summary>
		/// Extrae el código de mensaje luego de un envío exitoso de mensaje
		/// </summary>
		/// <param name="jResponse">El <see cref="Newtonsoft.Json.Linq.JObject"/> que retornó la invocación a enviar mensaje</param>
		/// <returns>El código de mensaje</returns>
		public override string ExtractMessageIDFromResponse(Newtonsoft.Json.Linq.JObject jResponse)
		{
			if (jResponse["message_id"] != null && jResponse["message_id"].Type == JTokenType.String)
			{
				return jResponse["message_id"].ToString();
			}

			return new DomainModel.ShortGuid(Guid.NewGuid());
		}

		/// <summary>
		/// Extrae información de error de una respuesta luego del envío de un mensaje
		/// </summary>
		/// <param name="statusCode">El <see cref="System.Net.HttpStatusCode"/> de la respuesta</param>
		/// <param name="jResponse">El <see cref="Newtonsoft.Json.Linq.JObject"/> que retornó la invocación a enviar mensaje</param>
		/// <param name="errorMessage">Cuando retorna, devuelve el mensaje de error</param>
		/// <param name="errorCode">Cuando retorna, devuelve el código de error o <code>null</code> si no se encontró</param>
		/// <param name="shouldRetry">Cuando retorna, devuelve si se debe reintentar el envío o no</param>
		public override void ExtractErrorInfoFromResponse(System.Net.HttpStatusCode statusCode, Newtonsoft.Json.Linq.JObject jResponse, out string errorMessage, out int? errorCode, out bool shouldRetry)
		{
			errorCode = null;
			errorMessage = null;
			shouldRetry = false;

			if ((int) statusCode >= 500)
				shouldRetry = true;
						
			try
			{
				if (jResponse["error"] != null && jResponse["error"].Type == JTokenType.Object)
				{
					var jError = (Newtonsoft.Json.Linq.JObject) jResponse["error"];
					errorMessage = jError["message"].ToString();
					errorCode = jError["code"].ToObject<int>();

					switch (errorCode)
					{
						case 1200: // Error temporal al enviar el mensaje. Vuelve a intentarlo más tarde
							shouldRetry = true;
							break;
						case 190: // Token de acceso de OAuth no válido.
							shouldRetry = true;
							break;
						default:
							break;
					}
				}
			}
			catch { }
		}

		/// <summary>
		/// Devuelve si el servicio dada su configuración actual permite el envío directo a una cola del service bus en vez de publicar directamente en la red social
		/// </summary>
		/// <returns><code>true</code> si lo permite; en caso contrario, <code>false</code></returns>
		public override bool SupportsPublishToServiceBus()
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend)
				return false;
			
			return true;
		}

#endregion
	}
}