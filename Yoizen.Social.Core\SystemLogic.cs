﻿using CsQuery.Engine.PseudoClassSelectors;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.Core.ActionOptions;
using Yoizen.Social.Core.Enums;
using Yoizen.Social.Core.Exceptions;
using Yoizen.Social.Core.FilterConditions;
using Yoizen.Social.Core.Services;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.DTO.YSmart.Enums;
using Yoizen.Social.DomainModel.DTO.YSmart.Requests;
using Yoizen.Social.DomainModel.Reports.RealTime;
using Yoizen.Social.DomainModel.Metrics;
using Yoizen.Social.DomainModel.ServiceSettings;
using Yoizen.Social.DomainModel.Whatsapp;
using static System.Net.Mime.MediaTypeNames;

namespace Yoizen.Social.Core
{
	public partial class SystemLogic
	{
		#region Fields

		private global::System.Collections.Concurrent.ConcurrentDictionary<int, global::System.Threading.Tasks.Task> runningTasks = new global::System.Collections.Concurrent.ConcurrentDictionary<int, global::System.Threading.Tasks.Task>();
		private global::System.Collections.Concurrent.ConcurrentDictionary<int, object> completedTasks = new global::System.Collections.Concurrent.ConcurrentDictionary<int, object>();

		private Azure.Messaging.ServiceBus.ServiceBusSender sbSenderWhatsappHSM = null;
		private Newtonsoft.Json.JsonSerializer serializerWhatsappHSM = null;

		#endregion

		#region Private Methods

		private static string[] GetEntryNames(string[] names, string sourceFolder, bool includeBaseName)
		{
			if (names == null || names.Length == 0)
				return new string[0];

			if (includeBaseName)
				sourceFolder = Path.GetDirectoryName(sourceFolder);

			int length = string.IsNullOrEmpty(sourceFolder) ? 0 : sourceFolder.Length;
			if (length > 0 && sourceFolder != null && sourceFolder[length - 1] != Path.DirectorySeparatorChar && sourceFolder[length - 1] != Path.AltDirectorySeparatorChar)
				length++;

			var result = new string[names.Length];
			for (int i = 0; i < names.Length; i++)
			{
				result[i] = names[i].Substring(length);
			}

			return result;
		}

		private static void CreateZipFromDirectory(string sourceDirectoryName
			, string destinationArchiveFileName
			, global::System.IO.Compression.CompressionLevel compressionLevel
			, bool includeBaseDirectory
			, Predicate<string> filter
			)
		{
			if (string.IsNullOrEmpty(sourceDirectoryName))
			{
				throw new ArgumentNullException("sourceDirectoryName");
			}
			if (string.IsNullOrEmpty(destinationArchiveFileName))
			{
				throw new ArgumentNullException("destinationArchiveFileName");
			}
			var filesToAdd = Directory.GetFiles(sourceDirectoryName, "*", SearchOption.AllDirectories);
			var entryNames = GetEntryNames(filesToAdd, sourceDirectoryName, includeBaseDirectory);
			using (var zipFileStream = new FileStream(destinationArchiveFileName, FileMode.Create))
			{
				using (var archive = new ZipArchive(zipFileStream, ZipArchiveMode.Create))
				{
					for (int i = 0; i < filesToAdd.Length; i++)
					{
						// Add the following condition to do filtering:
						if (filter != null && !filter(filesToAdd[i]))
						{
							continue;
						}

						using (var fs = new FileStream(filesToAdd[i], FileMode.Open, FileAccess.Read))
						{
							var entry = archive.CreateEntry(entryNames[i], compressionLevel);
							using (var es = entry.Open())
							{
								fs.CopyTo(es);
							}
						}
					}
				}
			}
		}

		/// <summary>
		/// Procesa las imágenes embebidas dentro del texto de un mail para marcarlas como archivos adjuntos
		/// </summary>
		/// <param name="text">El texto del mail que contiene el HTML a procesar pasado por referencia, dado que se modifica</param>
		/// <param name="attachments">La colección de archivos adjuntos</param>
		private void ProcessEmbededMailImages(ref string text, List<DomainModel.Attachment> attachments, IDictionary<string, string> parameters)
		{
			var inlineIndexes = new Dictionary<string, string>();

			var hasInlineImages = false;
			var dom = CsQuery.CQ.CreateDocument(text);
			var images = dom["img"];
			foreach (var image in images)
			{
				if (image.HasAttribute("uploaded") && image.Attributes["uploaded"].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					var src = image.Attributes["src"];
					if (src.StartsWith("./"))
						src = src.Substring(2);

					DomainModel.Attachment attachment = null;
					try
					{
						var path = global::System.Web.Hosting.HostingEnvironment.MapPath("~/");
						var directory = new global::System.IO.DirectoryInfo(path);
						directory = directory.Parent;
#if DEBUG
						string uploadedFile = global::System.IO.Path.Combine(directory.FullName, "Yoizen.Social.WebAgent", src.Replace("/", "\\"));
#else
						string uploadedFile = global::System.IO.Path.Combine(directory.FullName, "WebAgent", src.Replace("/", "\\"));
#endif
						if (global::System.IO.File.Exists(uploadedFile))
						{
							attachment = new DomainModel.Attachment(0, (byte) (attachments.Count + 1));
							attachment.Data = File.ReadAllBytes(uploadedFile);
							attachment.FileSize = attachment.Data.Length;
							attachment.FileName = Path.GetFileName(uploadedFile);
							attachment.MimeType = DomainModel.MimeTypeMap.GetMimeType(Path.GetExtension(uploadedFile));
							attachment.Parameters[DomainModel.Attachment.IsInlineParameter] = bool.TrueString;
							attachments.Add(attachment);

							var guid = Guid.NewGuid().ToString();
							var cid = string.Format("{0}@ysocial", guid);
							image["src"] = string.Format("cid:{0}", cid);
							attachment.Parameters[DomainModel.Attachment.ContentIdParameter] = cid;

							inlineIndexes[cid] = attachment.Index.ToString();

							hasInlineImages = true;
						}
					}
					catch { }

					if (attachment == null)
					{
						var fullUrl = string.Format("{0}{1}{2}",
						  DomainModel.SystemSettings.Instance.WebAgentURL,
						  DomainModel.SystemSettings.Instance.WebAgentURL.EndsWith("/") ? string.Empty : "/",
						  src);

						try
						{
							attachment = new DomainModel.Attachment(0, (byte) (attachments.Count + 1));

							var request = (HttpWebRequest) HttpWebRequest.Create(fullUrl);
							using (var response = (HttpWebResponse) request.GetResponse())
							{
								attachment.MimeType = response.ContentType;

								using (var stream = response.GetResponseStream())
								using (var ms = new global::System.IO.MemoryStream())
								{
									byte[] buffer = new byte[16384];
									int bytesRead = stream.Read(buffer, 0, buffer.Length);
									while (bytesRead > 0)
									{
										ms.Write(buffer, 0, bytesRead);
										bytesRead = stream.Read(buffer, 0, buffer.Length);
									}

									attachment.Data = ms.ToArray();
									attachment.FileSize = attachment.Data.Length;
									attachment.FileName = Path.GetFileName(fullUrl);
									attachment.Parameters[DomainModel.Attachment.IsInlineParameter] = bool.TrueString;
									attachments.Add(attachment);

									var guid = Guid.NewGuid().ToString();
									var cid = string.Format("{0}@ysocial", guid);
									image["src"] = string.Format("cid:{0}", cid);
									attachment.Parameters[DomainModel.Attachment.ContentIdParameter] = cid;

									inlineIndexes[cid] = attachment.Index.ToString();

									hasInlineImages = true;
								}
							}

							fullUrl = string.Format("{0}{1}api/upload?file={2}",
								DomainModel.SystemSettings.Instance.WebAgentURL,
								DomainModel.SystemSettings.Instance.WebAgentURL.EndsWith("/") ? string.Empty : "/",
								attachment.FileName);
							request = (HttpWebRequest) HttpWebRequest.Create(fullUrl);
							request.Method = "DELETE";
							using (var response = (HttpWebResponse) request.GetResponse())
							{
								Tracer.TraceInfo("Se borró el archivo subido del agente web: {0}", attachment.FileName);
							}
						}
						catch (Exception ex)
						{
							Tracer.TraceError("No se pudo obtener el attachment inline consultando la url {0}: {1}", fullUrl, ex);
						}
					}
				}
			}

			if (hasInlineImages)
			{
				text = dom.Document.Body.FirstElementChild.Render();
				parameters[Social.Mail.MailMessage.InlineAttachmentsIndexesParameter] = Common.Conversions.ConvertDictionaryToString(inlineIndexes);
			}
		}

		/// <summary>
		/// Devuelve el texto que se utilizará para la respuesta reemplazando las variables por sus varoles adecuados
		/// </summary>
		/// <param name="text">El texto de la respuesta</param>
		/// <param name="message">El <see cref="Message"/> que se responderá</param>
		/// <returns>Un <see cref="string"/> con el mensaje que se utilizará para responder</returns>
		private string GetReplyTextForMessage(string text, DomainModel.Message message)
		{
			if (text.Contains("@@FECHA@@"))
				text = text.Replace("@@FECHA@@", DateTime.Now.ToString("HH:mm:ss:fff"));

			if (text.Contains("@@CASO@@"))
				text = text.Replace("@@CASO@@", message.Case.GetID().ToString());

			if (text.Contains("@@USUARIO@@"))
			{
				switch (message.SocialServiceType)
				{
					case DomainModel.SocialServiceTypes.Twitter:
					case DomainModel.SocialServiceTypes.Instagram:
						text = text.Replace("@@USUARIO@@", string.Format("@{0}", message.PostedBy.DisplayName));
						break;
					case DomainModel.SocialServiceTypes.Facebook:
						text = text.Replace("@@USUARIO@@", message.PostedBy.DisplayName);
						break;
					case SocialServiceTypes.FacebookMessenger:
						text = text.Replace("@@USUARIO@@", message.PostedBy.DisplayName);
						break;
					default:
						text = text.Replace("@@USUARIO@@", message.PostedBy.DisplayName);
						break;
				}
			}

			return text;
		}

		/// <summary>
		/// Procesa los mensajes agrupados de un mensaje respondiendo o descartando según indique <paramref name="discard"/>
		/// </summary>
		/// <param name="message">El mensaje que contiene los mensajes agrupados</param>
		/// <param name="groupingSettings">Las <see cref="DomainModel.ServiceSettings.GroupingSettings"/> con la información de qué se hará
		/// con los mensajes agrupados</param>
		/// <param name="messagesToAvoidReply">Una enumeración de códigos de mensajes que se evitarán responder</param>
		/// <param name="socialConversationsToAvoidReply">Una enumeración con código de conversaciones que se evitarán responder</param>
		/// <param name="groupedMessageReplies">Las respuestas que se le darán a cada mensaje agrupado</param>
		private async Task ProcessGroupedMessages(DomainModel.Message message, DomainModel.ServiceSettings.GroupingSettings groupingSettings, IEnumerable<long> messagesToAvoidReply, IEnumerable<string> socialConversationsToAvoidReply, IDictionary<long, string> groupedMessageReplies)
		{
			List<string> repliedConversations = new List<string>();

			for (int i = 0; i < message.Groups.Length; i++)
			{
				var groupedMessage = message.Groups[i];

				if (groupedMessageReplies != null && groupedMessageReplies.ContainsKey(groupedMessage.ID))
				{
					Tracer.TraceVerb("Respondiendo el mensaje agrupado {0} con la respuesta generada por el agente", groupedMessage);

					Dictionary<string, string> parameters = null;
					if (groupedMessage.SocialServiceType == SocialServiceTypes.Mail)
					{
						parameters = new Dictionary<string, string>();
						parameters[Mail.MailMessage.SubjectParameter] = groupedMessage.Parameters[Mail.MailMessage.SubjectParameter];
					}

					long? insertedMessageID, insertedAssociatedMessageID;
					MessageDAO.Reply(groupedMessage, message.AssignedTo, GetReplyTextForMessage(groupedMessageReplies[groupedMessage.ID], groupedMessage), false, null, false, null, parameters, null, null, out insertedMessageID, out insertedAssociatedMessageID, false, ReplySources.Agent, DateTime.Now);
					groupedMessage.Status = DomainModel.MessageStatuses.Replied;
					groupedMessage.ReplySource = ReplySources.Agent;

					if (!string.IsNullOrEmpty(groupedMessage.SocialConversationID))
						repliedConversations.Add(groupedMessage.SocialConversationID);

					Tracer.TraceVerb("Se respondió el mensaje agrupado {0} con la respuesta generada por el agente", groupedMessage);

					if (insertedMessageID != null)
						await Reply(insertedMessageID.Value, message.Case);
					if (insertedAssociatedMessageID != null)
						await Reply(insertedAssociatedMessageID.Value, message.Case);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(groupedMessage);
					}

					continue;
				}

				if (!groupedMessage.RetrievedFromDatabase)
					groupedMessage = MessageDAO.GetOne(groupedMessage.ID);

				/*
				 * Los mensajes privados que se agrupan se descartan ya que al contestar el último está 
				 * respondiendo a todos los mensajes anteriores. 
				 * En caso de descartar, tiene sentido de que se descarten todos los anteriores!
				 */
				if (groupedMessage.Status == MessageStatuses.Grouped)
				{
					Tracer.TraceVerb("Descartando el mensaje agrupado: {0}", groupedMessage);

					MessageDAO.Discard(groupedMessage, null, null, false, false, DiscardSources.Grouping);
					groupedMessage.Status = DomainModel.MessageStatuses.Discarded;
					groupedMessage.FinishedDate = DateTime.Now;
					groupedMessage.DiscardSource = DiscardSources.Grouping;

					try
					{
						if (message.Case?.Parameters != null && groupedMessage.EnqueuedDate.HasValue && groupedMessage.FinishedDate.HasValue)
						{
							MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, groupedMessage.FinishedDate.Value, groupedMessage.EnqueuedDate.Value);
						}
					}

					catch { }

					Tracer.TraceVerb("Se descartó el mensaje agrupado: {0}", groupedMessage);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(groupedMessage);
					}
				}
				else if (groupedMessage.Status == MessageStatuses.Attended)
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(groupedMessage);
					}
				}

				if (groupedMessage.SocialServiceType == DomainModel.SocialServiceTypes.Facebook &&
					groupingSettings.TryToDeleteOrHideMessage)
				{
					Tracer.TraceVerb("Ocultando el mensaje agrupado {0} de facebook", groupedMessage);

					try
					{
						SocialServices.Facebook.FacebookTokens.Hide(groupedMessage);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("No se pudo hacer hide del mensaje {0}: {1}", groupedMessage, ex);
					}
				}
			}
		}

		/// <summary>
		///	Procesa la configuración de usuario molesto si el número de mensajes recibidos por un usuario excede la configurada
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/>para acceder a la cantidad de mensajes agrupados</param>
		/// <param name="discarded">Cuando retorna, devuelve si el mensaje fue descartado</param>
		internal async Task<bool> ProcessAnnoyingUserConfiguration(Message message)
		{
			if (!message.IsGrouping || message.Groups == null)
				return false;

			var discarded = false;

			int length = message.Groups.Length + 1;
			var settings = DomainModel.SystemSettings.Instance;
			if (length >= settings.AnnoyingEmailSettings.MaxMessagesAnnoyingUser)
			{
				if (settings.AnnoyingEmailSettings.MarkMessageAsVIM)
				{
					MarkAsImportant(message);
				}

				if (settings.AnnoyingEmailSettings.DiscardMessagesFromAnnoyingUser)
				{
					try
					{
						var options = new ActionOptions.DiscardOptions()
						{
							Message = message,
							Reason = "Mensaje descartado porque se excedió la máxima cantidad de mensajes recibidos configurados, el usuario de red social es considerado Molesto",
							DiscardSource = DomainModel.DiscardSources.System,
							CloseCase = settings.AnnoyingEmailSettings.DiscardMessagesAndCloseCaseFromAnnoyingUser
						};

						await this.DiscardAsync(options);
						Tracer.TraceInfo("Se ha descartado el mensaje {0} por que se ha superado la máxima cantidad de mensajes recibidos configurados, el usuario de red social es considerado Molesto", message);

						discarded = true;
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló al intentar descartar el mensaje del usuario considerado Molesto: {0}", ex);
					}
				}

				if (settings.AnnoyingEmailSettings.AddAnnoyingUserToBlackList)
				{
					try
					{
						SocialUserProfile profile = message.PostedBy.Profile;
						SocialUserProfileDAO.ChangeBlockStatus(profile.ID, true);
						Tracer.TraceInfo("Se ha agregado al usuario {0} a black list por superar la máxima cantidad configurada de mensajes recibidos, el usuario es considerado Molesto", profile.Name);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló agregar al usuario molesto a la Black List: {0}", ex);
					}
				}

				if (discarded || (length % settings.AnnoyingEmailSettings.MaxMessagesAnnoyingUser == 0))
				{
					// Enviamos el mail cada múltiplo de la cantidad de mensajes agrupados, para no BOMBARDEAR de mails
					var templateSettings = new Dictionary<string, object>();
					try
					{
						templateSettings.Add("@@FECHA@@", message.Date.ToString());
						templateSettings.Add("@@FECHA_PRIMER_MENSAJE@@", message.Groups[0].Date.ToString());
						templateSettings.Add("@@USUARIO@@", message.PostedBy.ToString());
						templateSettings.Add("@@SERVICIO@@", message.Service.Name);
						templateSettings.Add("@@CANTIDAD_DE_MENSAJES@@", message.Groups.Length.ToString());

						global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(ct =>
						{
							settings.SendMailMessage(settings.AnnoyingEmailSettings, templateSettings);
						});
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló al intentar enviar el mail a los supervisores informando del usuario considerado como molesto: {0}", ex);
					}

					if (settings.AnnoyingEmailSettings.NotifySupervisorFromScreen)
					{
						try
						{
							var notification = new DomainModel.Notifications.AnnoyingUser();
							notification.Parameters["Count"] = message.Groups.Length.ToString();
							notification.Parameters["Profile"] = message.PostedBy.Profile.Name;

							IEnumerable<DomainModel.User> supervisors = DomainModel.Cache.Instance.GetList<DomainModel.User>();
							supervisors = supervisors.Where(u => u.IsSupervisor);
							List<int> ids = new List<int>();
							foreach (var supervisor in supervisors)
							{
								notification.NotifiedUser = supervisor;
								DAL.NotificationDAO.Insert(notification);
								ids.Add(supervisor.ID);
							}

							Core.System.Instance.RealTimeService.NotifySupervisors(notification, ids);
						}
						catch (Exception ex)
						{
							Tracer.TraceError("Falló el envío de la notificación de usuario molesto: {0}", ex);
						}
					}
				}
			}

			return discarded;
		}

		private void SaveTag(Tag tag, List<Tag> allTags, List<dynamic> failed, User user, short[] tagGroups, int[] queues)
		{

			try
			{
				Tracer.TraceVerb("Dando de alta la etiqueta {0}", tag.Name);

				if (!tag.HasChildTags)
				{
					#region Queues

					if (queues != null)
					{
						foreach (var queueId in queues)
						{
							Queue queue = QueueDAO.GetOneFromCache(queueId);

							if (!tag.Queues.Contains(queue))
								tag.Queues.Add(queue);

							if (!queue.Tags.Contains(tag))
								queue.Tags.Add(tag);

							if (!queue.Tags.Contains(tag.Parent))
								queue.Tags.Add(tag.Parent);

							if (!tag.Parent.Queues.Contains(queue))
								tag.Parent.Queues.Add(queue);
						}
					}

					#endregion

					#region TagGroups

					if (tagGroups != null)
					{
						if (tag.Parent.TagGroups.Count > 0)
						{
							foreach (var tagGroupId in tagGroups)
							{
								var tagGroup = TagGroupDAO.GetOneFromCache(tagGroupId);
								tagGroup.Tags.Remove(tag.Parent.ID);
							}
						}

						foreach (var tagGroupId in tagGroups)
						{
							var tagGroup = TagGroupDAO.GetOneFromCache(tagGroupId);

							if (!tag.TagGroups.Contains(tagGroupId))
								tag.TagGroups.Add(tagGroupId);

							if (!tagGroup.Tags.Contains(tag.ID))
								tagGroup.Tags.Add(tag.ID);
						}
					}

					#endregion
				}

				var tagDAO = new DAL.TagDAO(tag);
				tagDAO.Insert();
				var newParameters = tag.AsDictionary(true);

				UserLogDAO.Insert(user, SystemEntityTypes.Labels, SystemActionTypes.Add, null, newParameters, tag.ID, tag.Name);

				allTags.Add(tag);
			}
			catch (Exception ex)
			{
				Tracer.TraceVerb("Falló dar de alta la etiqueta {0}", tag.Name, ex);

				failed.Add(new
				{
					Tag = new
					{
						Name = tag.Name,
					},
					Exception = ex.Message
				});
			}
		}

		private void UpdateTag(Tag tag, int tagToModifyId, List<Tag> allTags, List<dynamic> failed, User user, short[] tagGroups, int[] queues, bool replace)
		{
			Dictionary<string, string> oldParameters = null;

			try
			{
				Tracer.TraceVerb("Modificando la etiqueta {0}", tag.Name);

				var tagCache = TagDAO.GetOneFromCache(tagToModifyId);
				oldParameters = tagCache.AsDictionary(true);

				if (!tag.HasChildTags)
				{
					#region Queues

					if (queues != null)
					{
						if (replace)
						{
							IEnumerable<Queue> removedQueues = null;
							removedQueues = tagCache.Queues.Where(q => !queues.Contains(q.ID)).ToArray();

							if (removedQueues != null)
							{
								foreach (var queue in removedQueues)
								{
									Queue removedQueue = QueueDAO.GetOneFromCache(queue.ID);

									if (tagCache.Queues.Contains(removedQueue))
										tagCache.Queues.Remove(removedQueue);

									if (removedQueue.Tags.Contains(tagCache))
										removedQueue.Tags.Remove(tagCache);

									if (removedQueue.Tags.Contains(tagCache.Parent) && removedQueue.Tags.Intersect(tagCache.Parent.ChildTags).Count() == 0)
									{
										removedQueue.Tags.Remove(tagCache.Parent);

										if (tagCache.Parent.Queues.Contains(removedQueue))
											tagCache.Parent.Queues.Remove(removedQueue);
									}
								}
							}
						}

						foreach (var queueId in queues)
						{
							Queue queue = QueueDAO.GetOneFromCache(queueId);

							if (!tagCache.Queues.Contains(queue))
								tagCache.Queues.Add(queue);

							if (!queue.Tags.Contains(tagCache))
								queue.Tags.Add(tagCache);

							if (!queue.Tags.Contains(tagCache.Parent))
								queue.Tags.Add(tagCache.Parent);

							if (!tagCache.Parent.Queues.Contains(queue))
								tagCache.Parent.Queues.Add(queue);
						}


					}

					#endregion

					#region TagGroups

					if (tagGroups != null)
					{
						if (replace)
						{
							IEnumerable<short> removedTagGroups = null;
							removedTagGroups = tagCache.TagGroups.Where(q => !tagGroups.Contains(q)).ToArray();

							if (removedTagGroups != null)
							{
								foreach (var tagGroupId in removedTagGroups)
								{
									var removedtagGroup = TagGroupDAO.GetOneFromCache(tagGroupId);

									if (tagCache.TagGroups.Contains(tagGroupId))
										tagCache.TagGroups.Remove(tagGroupId);

									if (removedtagGroup.Tags.Contains(tagCache.ID))
										removedtagGroup.Tags.Remove(tagCache.ID);
								}
							}
						}

						foreach (var tagGroupId in tagGroups)
						{
							var tagGroup = TagGroupDAO.GetOneFromCache(tagGroupId);

							if (!tagCache.TagGroups.Contains(tagGroupId))
								tagCache.TagGroups.Add(tagGroupId);

							if (!tagGroup.Tags.Contains(tagCache.ID))
								tagGroup.Tags.Add(tagCache.ID);
						}
					}

					#endregion
				}
				else
				{
					tag.ID = tagCache.ID;
					tag.TagGroups.AddRange(tagCache.TagGroups);
					tagCache.ChildTags.AddRange(tag.ChildTags);
				}

				var tagDAO = new DAL.TagDAO(tagCache);

				tagDAO.Update();
				var newParameters = tagCache.AsDictionary(true);

				var entityName = oldParameters.ContainsKey("Name") ? oldParameters["Name"] : tagCache.Name;

				UserLogDAO.Insert(user
					, SystemEntityTypes.Labels
					, SystemActionTypes.Edit
					, oldParameters.Except(newParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
					, newParameters.Except(oldParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
					, tagCache.ID
					, entityName);

				var tagModifiedIndex = allTags.FindIndex(t => t.ID == tagToModifyId);
				allTags[tagModifiedIndex] = tagCache;
			}
			catch (Exception ex)
			{
				Tracer.TraceVerb("Falló modificar la etiqueta {0}: {1}", tag.Name, ex);

				failed.Add(new
				{
					Tag = new
					{
						Name = tag.Name,
					},
					Exception = ex.Message
				});
			}
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Encola un mensaje en el Service Bus para realizar envíos de HSM en forma más controlada cuando invoca una integración a la API 
		/// de envío saliente
		/// </summary>
		/// <param name="messageGuid">El ID de mensaje retornado por la api</param>
		/// <param name="options">Un <see cref="Core.ActionOptions.SendWhatsappOptions"/> con las opciones de envío</param>
		/// <param name="service">El <see cref="DomainModel.Service"/> asociado al servicio</param>
		public void EnqueueWhatsappHSM(Guid messageGuid, Core.ActionOptions.SendWhatsappOptions options, DomainModel.Service service)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowedServiceTypes.Contains(ServiceTypes.WhatsApp))
				return;

			this.CreateServiceBusIfNotExists();

			if (this.sbSenderWhatsappHSM == null)
			{
				var queueName = $"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-whatsapp-hsm";
				this.sbSenderWhatsappHSM = this.sbClient.CreateSender(queueName);

				this.serializerWhatsappHSM = new Newtonsoft.Json.JsonSerializer();
				this.serializerWhatsappHSM.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
				this.serializerWhatsappHSM.DefaultValueHandling = Newtonsoft.Json.DefaultValueHandling.Ignore;
			}

			try
			{
				var jMessage = new Newtonsoft.Json.Linq.JObject();
				jMessage["guid"] = messageGuid;
				jMessage["serviceId"] = service.ID;
				jMessage["options"] = Newtonsoft.Json.Linq.JObject.FromObject(options, this.serializerWhatsappHSM);

				var body = jMessage.ToString();

				var messageToSend = new Azure.Messaging.ServiceBus.ServiceBusMessage();
				messageToSend.Body = new BinaryData(body);
				messageToSend.ContentType = "application/json";

				this.sbSenderWhatsappHSM.SendMessageAsync(messageToSend).Wait();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrió un error intentando encolar un envío de HSM con GUID={0}: {1}", messageGuid, ex);
				throw;
			}
		}

		/// <summary>
		/// Libera un mensaje asignado a un agente al iniciar el sistema
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está liberando</param>
		public void ReleaseMessageFromAgentOnStartup(DomainModel.Message message)
		{
			ReleaseMessageFromAgent(message, message.AssignedTo, null, null, true);
		}

		/// <summary>
		/// Finaliza una llada de voz de whatsapp en curso
		/// </summary>
		/// <param name="message"></param>
		/// <param name="agent"></param>
		/// <param name="callId"></param>
		/// <param name="notifyAgent"></param>
		public void WhatsappVoiceCallTerminate(Message message, Agent agent, string callId, bool notifyAgent)
		{
			Task.Run(async () => await WhatsappVoiceCallTerminateAsync(message, agent, callId, notifyAgent));
		}

		/// <summary>
		/// Libera un mensaje asignado a un agente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está liberando</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que dejará de tener asignado el mensaje</param>
		/// <param name="queue">El <see cref="SystemQueue"/> al que pertenece le mensaje</param>
		/// <param name="connectionInfo">El <see cref="DomainModel.ConnectionInfo"/> con los datos de conexión del agente</param>
		/// <param name="starting">Indica si ocurre cuadno el sistema está iniciando</param>
		public void ReleaseMessageFromAgent(DomainModel.Message message, DomainModel.Agent agent, SystemQueue queue, DomainModel.ConnectionInfo connectionInfo, bool starting = false, bool releaseByInactivity = false)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (queue == null && message.Queue == null)
				throw new ArgumentException("La cola del mensaje o la cola de encolamiento deben ser proporcionadas");

			if (message.Status != DomainModel.MessageStatuses.Assigned || message.AssignedTo == null || !message.AssignedTo.Equals(agent))
				return;

			Tracer.TraceVerb("Desasignando el mensaje '{0}' que estaba asignado al agente '{1}'", message, agent);
			try
			{

				if (message.Case?.CurrentCall != null)
				{
					Tracer.TraceInfo("El agente actualmente esta en una llamada, se finaliza la misma antes de desasignar el mensaje");
					WhatsappVoiceCallTerminate(message, agent, message.Case.CurrentCall.SocialCallID, true);
				}

				DomainModel.Queue domainQueue;
				if (queue != null)
					domainQueue = queue.Queue;
				else
					domainQueue = message.Queue;

				bool shouldReserve = false;
				if (domainQueue.ReserveMessages && message.Read)
					shouldReserve = true;
				this.FinishedRead(message, agent);
				MessageDAO.ReleaseFromAgent(message, agent.ID, shouldReserve, releaseByInactivity);

				if (!starting)
				{
					if (queue != null)
						queue.Unassign(message, agent);

					if (message.SocialServiceType == SocialServiceTypes.Chat)
					{
						try
						{
							Core.System.Instance.ChatsService.ReturnToQueue(message, null, null, true);
						}
						catch (Exception ex)
						{
							Tracer.TraceInfo("Falló notificar al servicio de chat que el mensaje fue retornado a la cola. Se obtuvo {0}", ex);
						}
					}

					message.Status = DomainModel.MessageStatuses.NotAssigned;
					message.AssignedTo = null;
					message.ShouldBeAssignedTo = null;
					message.Read = false;

					var lastSegment = message.LastSegment;
					if (lastSegment != null)
					{
						lastSegment.FinishedDate = DateTime.Now;

						try
						{
							MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, lastSegment.FinishedDate.Value, lastSegment.EnqueuedDate.Value);
						}
						catch (Exception ex)
						{
							Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
						}

						lastSegment = message.AddSegment();
						lastSegment.InQueue = message.Queue;
						lastSegment.EnqueuedDate = DateTime.Now;
					}

					if (connectionInfo != null)
					{
						connectionInfo.MessageStats.UnassignedMessages++;
						connectionInfo.CurrentlyAssignedMessages--;
					}
				}

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = agent.ID;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = agent.ID;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = agent.ID;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = agent.ID;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo desasignar el mensaje {0} del agente {1}: {2}", message, agent, ex);
				throw;
			}
		}

		/// <summary>
		/// Libera un mensaje asignado a un agente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está liberando</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que dejará de tener asignado el mensaje</param>
		/// <param name="queue">El <see cref="SystemQueue"/> al que pertenece le mensaje</param>
		/// <param name="connectionInfo">El <see cref="DomainModel.ConnectionInfo"/> con los datos de conexión del agente</param>
		public async Task ReleaseChatFromAgent(DomainModel.Message message, DomainModel.Agent agent, SystemQueue queue, DomainModel.ConnectionInfo connectionInfo)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (queue == null && message.Queue == null)
				throw new ArgumentException("La cola del mensaje o la cola de encolamiento deben ser proporcionadas");

			if (message.Status != DomainModel.MessageStatuses.Assigned || message.AssignedTo == null || !message.AssignedTo.Equals(agent))
				return;

			Tracer.TraceVerb("Desasignando el mensaje '{0}' que estaba asignado al agente '{1}'", message, agent);
			try
			{
				DomainModel.Queue domainQueue;
				if (queue != null)
					domainQueue = queue.Queue;
				else
					domainQueue = message.Queue;

				bool shouldReserve = false;
				MessageDAO.ReleaseFromAgent(message, agent.ID, shouldReserve);

				if (queue != null)
					queue.Unassign(message, agent);

				message.Status = DomainModel.MessageStatuses.NotAssigned;
				message.AssignedTo = null;
				message.ShouldBeAssignedTo = null;
				message.Read = false;

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedDate = DateTime.Now;

					lastSegment = message.AddSegment();
					lastSegment.InQueue = message.Queue;
					lastSegment.EnqueuedDate = DateTime.Now;
				}

				if (connectionInfo != null)
				{
					connectionInfo.MessageStats.UnassignedMessages++;
					connectionInfo.CurrentlyAssignedMessages--;
				}

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = agent.ID;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = agent.ID;
				info.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = agent.ID;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = agent.ID;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.UnassignedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				Tracer.TraceVerb("Se notificará al agente {0} que el mensaje {1} con chat {2} fue desasignado por desconexión de SignalR", agent, message.ID, message.Chat.ID);
				await Core.System.Instance.RealTimeService.NotifyAgentAsync(new DomainModel.AgentNotifications.ChatUnasignedAfterDisconnection(agent, message.ID, message.Chat.ID));
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo desasignar el mensaje {0} del agente {1}: {2}", message, agent, ex);
				throw;
			}
		}

		/// <summary>
		/// Retorna un mensaje a la cola
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se está retornando a la cola</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que retornó el mensaje a la cola</param>
		/// <param name="reason">El motivo que describe la razón del retorno a la cola</param>
		/// <exception cref="InvalidOperationException">Ocurre cuando el servicio de colas no ha sido inicializado</exception>
		public void ReturnToQueue(DomainModel.Message message, DomainModel.Agent agent, string reason, SystemQueue systemQueue)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (systemQueue == null)
				throw new ArgumentNullException(nameof(systemQueue), "La cola no puede ser null");

			if (message.Status != DomainModel.MessageStatuses.Assigned)
				throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser retornado a la cola", message.Status), nameof(message));

			if (message.AssignedTo.ID != agent.ID)
				throw new ArgumentException("El mensaje tiene que ser retornado a la cola por el agente al que fue asignado", nameof(agent));

			if (!message.Queue.WorkingHoursForReceivingMessages.IsTimeWorkable(DateTime.Now))
				throw new CoreException("No se puede retornar el mensaje porque la cola está fuera de horario laboral");

			if (message.Queue.ConnectedAgentsForReceivingMessages &&
				!systemQueue.SubscribedAgents.Any(a => a != agent))
				throw new CoreException("No se puede retornar el mensaje porque la cola no tiene otros agentes conectados");

			Tracer.TraceInfo("El agente {0} está retornando el mensaje {1} a la cola {2}", agent, message, systemQueue.Name);

			try
			{
				DateTime now = DateTime.Now;

				MessageDAO.ReturnToQueue(message, agent, reason);
				message.Status = DomainModel.MessageStatuses.NotAssigned;
				message.AssignedTo = null;
				message.ShouldBeAssignedTo = null;
				message.Read = false;
				message.ShouldNotBeAssignedTo = agent;
				message.ReturnedToQueueDate = now;
				message.TimesReturnedToQueue++;
				message.AssignedDate = null;
				message.ReadDate = null;
				message.FinishedReadDate = null;

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.OutQueue = message.Queue;
					lastSegment.FinishedDate = now;
					if (agent != null)
						lastSegment.Person = agent;

					var newSegment = message.AddSegment();
					newSegment.InQueue = message.Queue;
					newSegment.EnqueuedDate = now;
				}

				try
				{
					if (message.Case?.Parameters != null && message.EnqueuedDate.HasValue && message.FinishedDate.HasValue)
					{
						MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, message.FinishedDate.Value, message.EnqueuedDate.Value);
						CaseDAO.UpdateParameters(message.Case);
					}
				}

				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}

				// TODO: agregar el agrupamiento si existe algún mensaje en la cola

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = agent.ID;
				info.ReturnedToQueueMessages = 1;
				if (lastSegment != null)
				{
					info.AgentTime = lastSegment.ActionTime ?? 0;
					info.UnreadTime = lastSegment.UnreadTime ?? 0;
					info.ReadTime = lastSegment.ReadTime ?? 0;
				}
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.ReturnedToQueueMessages = 1;
				if (lastSegment != null)
				{
					info.AgentTime = lastSegment.ActionTime ?? 0;
					info.UnreadTime = lastSegment.UnreadTime ?? 0;
					info.ReadTime = lastSegment.ReadTime ?? 0;
				}
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = agent.ID;
				info.ReturnedToQueueMessages = 1;
				if (lastSegment != null)
				{
					info.AgentTime = lastSegment.ActionTime ?? 0;
					info.UnreadTime = lastSegment.UnreadTime ?? 0;
					info.ReadTime = lastSegment.ReadTime ?? 0;
				}
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = agent.ID;
				infoService.ReturnedToQueueMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.ReturnedToQueueMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = agent.ID;
				infoService.ReturnedToQueueMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				Tracer.TraceInfo("El agente {0} devolvió el mensaje {1} a la cola", agent, message);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se devolvía el mensaje {0} a la cola: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Elimina un mensaje de chat abandonado
		/// </summary>
		/// <param name="message">El mensaje</param>
		/// <param name="tags">Un arreglo con los códigos de tags a aplicar</param>
		/// <param name="reason">Opcional. Una descripción de por qué se desencoló el mensaje</param>
		internal async Task Abandon(DomainModel.Message message, string reason)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			try
			{
				var date = DateTime.Now;

				MessageDAO.Abandon(message, reason);
				message.FinishedDate = date;
				Tracer.TraceInfo("El sistema desencoló el mensaje {0}", message);

				var interval = new Common.Interval(date, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				var info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.AbandonedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.AbandonedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				var infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.PersonID = 0;
				infoService.QueueID = message.Queue.ID;
				infoService.AbandonedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.PersonID = 0;
				infoService.QueueID = 0;
				infoService.AbandonedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
				{
					Case = message.Case,
					ClosedBy = CaseClosingResponsibles.System,
					Person = null,
					Interval = interval,
					StoreInDatabase = false,
					Queue = null,
					Message = message
				});
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se desencolaba el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Finaliza la atención de un mensaje de un servicio que se comporta como chat
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se va a finalizar</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que tiene asignado el mensaje</param>
		/// <param name="closeCase">Indica si el caso debe ser cerradod</param>
		public async Task Finish(DomainModel.Message message, DomainModel.Agent agent, bool closeCase, DateTime now)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (message.SocialServiceType == SocialServiceTypes.Chat)
				throw new InvalidOperationException("El método solo acepta mensajes que no son chat");

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowServicesAsChats || !message.Service.Settings.ActAsChat)
				throw new InvalidOperationException("Solo se puede usar este método si está licenciado y el servicio está configurado como chat");

			try
			{
				MessageDAO.MarkAsAttended(message, agent, closeCase);
				message.Status = DomainModel.MessageStatuses.Attended;
				message.FinishedDate = now;

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedDate = now;
				}

				if (message.IsGrouping && message.Service.Settings != null && message.Service.Settings.Grouping != null)
				{
					Tracer.TraceVerb("Se procesarán los mensajes agrupados del mensaje: {0}", message);
					global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(ct => ProcessGroupedMessages(message, message.Service.Settings.Grouping, null, null, null));
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				if (message.Parameters == null ||
					!message.Parameters.ContainsKey(Message.AttendedAtParameter))
				{
					DomainModel.Historical.Daily info;
					DomainModel.Historical.DailyService infoService;

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = message.AssignedTo.ID;
					info.AttendedMessagesByAgent = 1;
					info.MessagesAttendedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
					info.MessagesAttendedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
					info.AgentTime = message.LastSegment.ActionTime ?? 0;
					info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
					info.ReadTime = message.LastSegment.ReadTime ?? 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = 0;
					info.AttendedMessagesByAgent = 1;
					info.MessagesAttendedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
					info.MessagesAttendedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
					info.AgentTime = message.LastSegment.ActionTime ?? 0;
					info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
					info.ReadTime = message.LastSegment.ReadTime ?? 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = message.AssignedTo.ID;
					info.AttendedMessagesByAgent = 1;
					info.MessagesAttendedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
					info.MessagesAttendedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
					info.AgentTime = message.LastSegment.ActionTime ?? 0;
					info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
					info.ReadTime = message.LastSegment.ReadTime ?? 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = message.AssignedTo.ID;
					infoService.AttendedMessagesByAgent = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = 0;
					infoService.AttendedMessagesByAgent = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = message.AssignedTo.ID;
					infoService.AttendedMessagesByAgent = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = message.AssignedTo.ID;
					infoService.AttendedMessagesByAgent = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.AttendedMessagesByAgent = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				try
				{
					if (message.Case?.Parameters != null && message.EnqueuedDate.HasValue && message.FinishedDate.HasValue)
					{
						MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, message.FinishedDate.Value, message.EnqueuedDate.Value);
						message.ComputeTimes();
						if (message.SystemTime.HasValue)
						{
							MetricsAdapter.CalculateTotalSystemTime(message.Case?.Parameters, message.SystemTime.Value);
						}

						if (message.WorkingTime.HasValue)
						{
							MetricsAdapter.CalculateTotalWorkingTimeParameter(message.Case?.Parameters, message.WorkingTime.Value);
						}

						CaseDAO.UpdateParameters(message.Case);
					}
				}

				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}


				if (closeCase)
				{
					var caseClosedBy = CaseClosingResponsibles.Agent;
					var caseClosedByPerson = agent;

					if (message.Case != null)
					{
						message.Case.LastPerson = agent;
					}

					await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
					{
						Case = message.Case,
						ClosedBy = caseClosedBy,
						Person = caseClosedByPerson,
						Interval = interval,
						StoreInDatabase = false,
						Queue = null,
						Message = message
					});
				}
				else
				{
					await Core.System.Instance.CasesService.RegisterCaseToBeClosed(message.Case, message.Service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(message);
				}

				Tracer.TraceInfo("El agente {0} finalizó la atención del mensaje {1}", agent, message);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se finalizaba la atención el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Elimina un mensaje de chat finalizado
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se va a finalizar</param>
		/// <param name="reason">La razón por la cual se está finalizando</param>
		/// <param name="sender">Un valor de la enumeración <see cref="DomainModel.ChatSenders"/> con el responsable de finalizar el chat</param>
		public async Task Finish(DomainModel.Message message, DomainModel.ChatSenders sender, string reason)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			try
			{
				DateTime now = DateTime.Now;

				MessageDAO.Finish(message, sender, reason);

				message.FinishedDate = now;

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedDate = now;
					lastSegment.Parameters["Sender"] = ((short) sender).ToString();
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

				switch (sender)
				{
					case ChatSenders.Agent:
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = message.Queue.ID;
						info.PersonID = message.AssignedTo.ID;
						info.FinishedMessagesByAgent = 1;
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = message.Queue.ID;
						info.PersonID = 0;
						info.FinishedMessagesByAgent = 1;
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
						if (message.Chat != null && message.Chat.Messages != null)
						{
							try
							{
								var firstAgentMessage = message.Chat.Messages.FirstOrDefault(m => m.SentBy == ChatSenders.Agent || m.SentBy == ChatSenders.Supervisor);
								if (firstAgentMessage != null && firstAgentMessage.SentOn.Value != null)
									info.ReplyTime = Convert.ToInt32(firstAgentMessage.SentOn.Value.Subtract(message.EnqueuedDate.Value).TotalSeconds);
							}
							catch { }
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = message.AssignedTo.ID;
						info.FinishedMessagesByAgent = 1;
						info.AgentTime = message.LastSegment.ActionTime ?? 0;
						info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
						info.ReadTime = message.LastSegment.ReadTime ?? 0;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = message.Service.ID;
						infoService.QueueID = message.Queue.ID;
						infoService.PersonID = message.AssignedTo.ID;
						infoService.FinishedMessagesByAgent = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = message.Service.ID;
						infoService.QueueID = message.Queue.ID;
						infoService.PersonID = 0;
						infoService.FinishedMessagesByAgent = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = message.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = message.AssignedTo.ID;
						infoService.FinishedMessagesByAgent = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						break;

					case ChatSenders.User:
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = message.Queue.ID;
						info.PersonID = 0;
						info.FinishedMessagesByUser = 1;
						if (message.Chat != null && message.Chat.Messages != null)
						{
							try
							{
								var firstAgentMessage = message.Chat.Messages.FirstOrDefault(m => m.SentBy == ChatSenders.Agent || m.SentBy == ChatSenders.Supervisor);
								if (firstAgentMessage != null && firstAgentMessage.SentOn.Value != null)
									info.ReplyTime = Convert.ToInt32(firstAgentMessage.SentOn.Value.Subtract(message.EnqueuedDate.Value).TotalSeconds);
							}
							catch { }
						}
						Core.System.Instance.IntervalService.StoreInfo(info, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = message.Service.ID;
						infoService.QueueID = message.Queue.ID;
						infoService.PersonID = 0;
						infoService.FinishedMessagesByUser = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						break;
					default:
						break;
				}

				var caseClosedBy = CaseClosingResponsibles.System;
				DomainModel.Person caseClosedByPerson = null;
				if (sender == ChatSenders.Agent)
				{
					caseClosedBy = CaseClosingResponsibles.Agent;
					caseClosedByPerson = message.AssignedTo;

					if (message.Case != null)
					{
						message.Case.LastPerson = message.AssignedTo ?? message.Case.LastPerson;
					}
				}

				await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
				{
					Case = message.Case,
					ClosedBy = caseClosedBy,
					Person = caseClosedByPerson,
					Interval = interval,
					StoreInDatabase = false,
					Queue = null,
					Message = message
				});

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(message);
				}

				Tracer.TraceInfo("El sistema finalizó el mensaje {0}", message);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se finalizaba el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como leído
		/// </summary>
		/// <param name="message">El mensaje</param>
		/// <param name="agent">El agente que leyó el mensaje</param>
		public void Read(DomainModel.Message message, DomainModel.Agent agent)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (message.Status > DomainModel.MessageStatuses.Assigned)
				throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser marcado como leído", message.Status), nameof(message));

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (message.AssignedTo == null || message.AssignedTo != agent)
				throw new ArgumentException("El mensaje sólo puede ser marcado como leído por el agente al que fue asignado", nameof(agent));

			Tracer.TraceInfo("El agente {0} está marcando como leído el mensaje {1}", agent, message);

			try
			{
				DateTime now = DateTime.Now;

				MessageDAO.MarkAsRead(message, agent);

				message.Read = true;
				message.ReadDate = now;
				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.ReadDate = now;
					lastSegment.Person = agent;

					if (!message.Case.Parameters.ContainsKey(Case.FirstIncomingMessageReadDateParameter))
					{
						message.Case.Parameters[Case.FirstIncomingMessageReadDateParameter] = now.ToString("o");
					}

					try
					{
						if (lastSegment.EnqueuedDate.HasValue && lastSegment.AssignedDate.HasValue)
						{
							MetricsAdapter.CalculateTotalUnreadMessagesTime(message.Case?.Parameters, lastSegment.AssignedDate.Value, now);
							CaseDAO.UpdateParameters(message.Case);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
					}
				}

				Tracer.TraceInfo("El agente {0} marcó como leído el mensaje {1}", agent, message);

				#region Integraciones

				System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.AgentMessageMarkAsRead, new Dictionary<string, string>()
				{
					{ "@@AGENTE[CODIGO]@@", agent.ID.ToString() }
					, { "@@AGENTE[NOMBRE]@@", agent.FullName }
					, { "@@AGENTE[USERNAME]@@", agent.UserName }
					, { "@@MENSAJE[CODIGO]@@", message.ID.ToString() }
					, { "@@MENSAJE[TEXTO]@@", message.Body }
					, { "@@MENSAJE[VIM]@@", message.Important ? "1" : "0" }
					, { "@@CASO[CODIGO]@@", message.Case.ID.ToString() }
					, { "@@SERVICIO[CODIGO]@@", message.Service.ID.ToString() }
					, { "@@SERVICIO[TIPO]@@", ((short) message.SocialServiceType).ToString() }
					, { "@@USUARIO[CODIGO]@@", message.PostedBy.ID.ToString() }
					, { "@@USUARIO[NOMBRE]@@", message.PostedBy.Name }
					, { "@@USUARIO[ALIAS]@@", message.PostedBy.DisplayName }
					, { "@@USUARIO[EMAIL]@@", message.PostedBy?.Email }
					, { "@@USUARIO[DATOS]@@", message.PostedBy?.BusinessData }
					, { "@@PERFIL[CODIGO]@@", message.PostedBy?.Profile?.ID.ToString() }
					, { "@@PERFIL[DATOS]@@", message.PostedBy?.Profile?.PrimaryEmail }
					, { "@@CHAT[CODIGO]@@", (message.SocialServiceType == SocialServiceTypes.Chat) ? message.Chat?.ID.ToString() : string.Empty }
				});

				if (message.Case != null &&
					message.Case.Agents == 1 &&
					!message.Case.Parameters.ContainsKey(DomainModel.Case.AgentMarkedAsReadFirstAssignedMessageOfCaseParameter))
				{
					message.Case.Parameters[DomainModel.Case.AgentMarkedAsReadFirstAssignedMessageOfCaseParameter] = true.ToString();
					DAL.CaseDAO.UpdateParameters(message.Case);

					if (Core.System.Instance.QueueService.AreCaseMessagesEnqueued(message.Case.ID, out DomainModel.Message enqueuedMessage))
					{
						Tracer.TraceVerb("Hay otro mensaje del caso {0} encolado. Se actualizará esa referencia", message.Case.ID);
						enqueuedMessage.Case.Parameters[DomainModel.Case.AgentMarkedAsReadFirstAssignedMessageOfCaseParameter] = true.ToString();
					}

					System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.AgentMarkedAsReadFirstAssignedMessageOfCase, new Dictionary<string, string>()
					{
						{ "@@AGENTE[CODIGO]@@", agent.ID.ToString() }
						, { "@@AGENTE[NOMBRE]@@", agent.FullName }
						, { "@@AGENTE[USERNAME]@@", agent.UserName }
						, { "@@MENSAJE[CODIGO]@@", message.ID.ToString() }
						, { "@@MENSAJE[TEXTO]@@", message.Body }
						, { "@@MENSAJE[VIM]@@", message.Important ? "1" : "0" }
						, { "@@CASO[CODIGO]@@", message.Case.ID.ToString() }
						, { "@@SERVICIO[CODIGO]@@", message.Service.ID.ToString() }
						, { "@@SERVICIO[TIPO]@@", ((short) message.SocialServiceType).ToString() }
						, { "@@USUARIO[CODIGO]@@", message.PostedBy.ID.ToString() }
						, { "@@USUARIO[NOMBRE]@@", message.PostedBy.Name }
						, { "@@USUARIO[ALIAS]@@", message.PostedBy.DisplayName }
						, { "@@USUARIO[EMAIL]@@", message.PostedBy?.Profile?.PrimaryEmail }
						, { "@@USUARIO[DATOS]@@", message.PostedBy?.BusinessData }
						, { "@@PERFIL[CODIGO]@@", message.PostedBy?.Profile?.ID.ToString() }
						, { "@@PERFIL[DATOS]@@", message.PostedBy?.Profile?.PrimaryEmail }
						, { "@@CHAT[CODIGO]@@", (message.SocialServiceType == SocialServiceTypes.Chat) ? message.Chat?.ID.ToString() : string.Empty }
					});
				}

				#endregion
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló cuando marcaba como leído el mensaje {0}: {{0}}", message), ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza la clasificación de un mensaje
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a clasificar</param>
		/// <param name="classificationContactReason"></param>
		public void Classify(Message message, int classificationContactReason)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			try
			{
				var contactReason = DomainModel.Cache.Instance.GetItem<DomainModel.ContactReason>(classificationContactReason);
				if (contactReason != null)
				{
					message.Classified = true;
					message.ClassificationContactReason = contactReason;
					message.TopClassification = null;
					message.ClassificationContactReason.UseCount++;

					if (message.Case.Classifications == null)
						message.Case.Classifications = new List<short>();
					message.Case.Classifications.Add(message.ClassificationContactReason.ID);

					DAL.MessageDAO.UpdateClassification(message);

#pragma warning disable CS0612
					DomainModel.Historical.DailyCase infoCase;
					var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.ClassifiedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);
#pragma warning restore CS0612
				}
				else
				{
					Tracer.TraceInfo("No se pudo encontrar el motivo de contacto {0}", classificationContactReason);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se clasificaba el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como que el agente ya terminó de leerlo y procede a realizar alguna acción con este
		/// </summary>
		/// <param name="message">El mensaje</param>
		/// <param name="agent">El agente que leyó el mensaje</param>
		public void FinishedRead(DomainModel.Message message, DomainModel.Agent agent)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (message.AssignedTo == null || message.AssignedTo != agent)
				throw new ArgumentException("El mensaje sólo puede ser marcado como leído por el agente al que fue asignado", "agent");

			Tracer.TraceInfo("El agente {0} terminó de leer el mensaje {1}", agent, message);

			try
			{
				if (!message.Read)
				{
					this.Read(message, agent);
				}
				message.FinishedReadDate = DateTime.Now;
				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.FinishedReadDate = DateTime.Now;
					lastSegment.Person = agent;
				}
				MessageDAO.MarkAsFinishedRead(message, agent);

				try
				{
					if (lastSegment.ReadDate.HasValue && lastSegment.FinishedReadDate.HasValue)
					{
						MetricsAdapter.CalculateTotalReadingMessagesTime(message.Case?.Parameters, lastSegment.ReadDate.Value, lastSegment.FinishedReadDate.Value);
						CaseDAO.UpdateParameters(message.Case);
					}
				}

				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}


				Tracer.TraceInfo("El agente {0} terminó de leer el mensaje {1}", agent, message);

				#region Integraciones

				System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.AgentMessageMarkAsFinishedRead, new Dictionary<string, string>()
				{
					{ "@@AGENTE[CODIGO]@@", agent.ID.ToString() }
					, { "@@AGENTE[NOMBRE]@@", agent.FullName }
					, { "@@AGENTE[USERNAME]@@", agent.UserName }
					, { "@@MENSAJE[CODIGO]@@", message.ID.ToString() }
					, { "@@MENSAJE[TEXTO]@@", message.Body }
					, { "@@MENSAJE[VIM]@@", message.Important ? "1" : "0" }
					, { "@@CASO[CODIGO]@@", message.Case.ID.ToString() }
					, { "@@SERVICIO[CODIGO]@@", message.Service.ID.ToString() }
					, { "@@SERVICIO[TIPO]@@", ((short) message.SocialServiceType).ToString() }
					, { "@@USUARIO[CODIGO]@@", message.PostedBy.ID.ToString() }
					, { "@@USUARIO[NOMBRE]@@", message.PostedBy.Name }
					, { "@@USUARIO[ALIAS]@@", message.PostedBy.DisplayName }
					, { "@@USUARIO[EMAIL]@@", message.PostedBy?.Profile?.PrimaryEmail }
					, { "@@CHAT[CODIGO]@@", (message.SocialServiceType == SocialServiceTypes.Chat) ? message.Chat?.ID.ToString() : string.Empty }
				});

				#endregion
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se terminaba de leer el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Transfiere un mensaje que está en cola o asignado a yFlow
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a transferir</param>
		/// <param name="person">El <see cref="DomainModel.Person"/> que está transfiriendo el mensaje nuevamente a yFlow o <code>null</code>
		/// para indicar que es el sistema</param>
		/// <param name="transfer">Un <see cref="YFlowSettings.ReturnFromAgent"/> con la información de transferencia</param>
		internal async Task TransferToYFlow(Message message, Person person, YFlowSettings.ReturnFromAgent transfer, SystemQueue socialQueue)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowAgentsToTransferMessagesToYFlow)
				return;

			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (message.Status == MessageStatuses.Discarded || message.Status == MessageStatuses.Replied)
				return;

			if (person != null)
			{
				if (person.Type == DomainModel.PersonTypes.Agent)
				{
					if (message.Status != DomainModel.MessageStatuses.Assigned)
						throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser transferido a yflow por el agente", message.Status), nameof(message));

					if (message.AssignedTo.ID != person.ID)
						throw new ArgumentException("El mensaje tiene que ser transferido a yFlow por el agente al que fue asignado", nameof(person));
				}
				else
				{
					if (message.Status != DomainModel.MessageStatuses.NotAssigned)
						throw new ArgumentException(string.Format("El mensaje se encuentra en estado {0} y no puede ser transferido a yFlow por el supervisor", message.Status), nameof(message));
				}
			}

			try
			{
				var invokeParameters = new Social.YFlow.InvokeParameters(message);
				invokeParameters.TransferedFromAgent = true;
				invokeParameters.ReturnFromAgent = transfer;
				invokeParameters.ThrowOnError = true;
				invokeParameters.AllowedToDerive = false;

				try
				{
					MessageLogDAO.Insert(message, person, MessageLogTypes.MessageTransferedToYFlow, transfer.Description);
					if (person != null && person.Type == PersonTypes.Agent)
						AgentLogDAO.Insert(person.ID, AgentLogTypes.TransferedMessageToYFlow, message.ID);

					var invokeResults = await this.InvokeYFlowAsync(invokeParameters);

					var now = DateTime.Now;

					var lastSegment = message.LastSegment;
					if (lastSegment != null)
					{
						lastSegment.FinishedDate = now;
						if (person != null)
							lastSegment.Person = person;

						lastSegment = message.AddSegment();
					}

					message.Case.Parameters[DomainModel.Case.YFlowTransferedFromAgentParameter] = true.ToString();
					invokeResults.MustUpdateCaseParameters = true;

					if (invokeResults.Queue == null)
					{
						// Si destQueue es null, entonces lo respondió yFlow
						if (message.Parameters.ContainsKey(DomainModel.Message.YFlowInvokedParameter))
							message.Parameters.Remove(DomainModel.Message.YFlowInvokedParameter);

						if (message.Case.Parameters.ContainsKey(DomainModel.Case.YFlowDoNotInvokeParameter))
						{
							message.Case.Parameters.Remove(DomainModel.Case.YFlowDoNotInvokeParameter);
							invokeResults.MustUpdateCaseParameters = true;
						}

						socialQueue.Remove(message);

						var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

						DomainModel.Historical.Daily info;
						DomainModel.Historical.DailyService infoService;

						if (person != null)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = message.Queue.ID;
							info.PersonID = person.ID;
							info.TransferedToYFlowMessages = 1;
							info.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							info.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							if (person.Type == PersonTypes.Agent)
							{
								info.AgentTime = message.LastSegment.ActionTime ?? 0;
								info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
								info.ReadTime = message.LastSegment.ReadTime ?? 0;
							}
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = 0;
							info.PersonID = person.ID;
							info.TransferedToYFlowMessages = 1;
							info.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							info.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							if (person.Type == PersonTypes.Agent)
							{
								info.AgentTime = message.LastSegment.ActionTime ?? 0;
								info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
								info.ReadTime = message.LastSegment.ReadTime ?? 0;
							}
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = message.Queue.ID;
							info.PersonID = 0;
							info.TransferedToYFlowMessages = 1;
							info.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							info.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							if (person.Type == PersonTypes.Agent)
							{
								info.AgentTime = message.LastSegment.ActionTime ?? 0;
								info.UnreadTime = message.LastSegment.UnreadTime ?? 0;
								info.ReadTime = message.LastSegment.ReadTime ?? 0;
							}
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = message.Service.ID;
							infoService.QueueID = message.Queue.ID;
							infoService.PersonID = person.ID;
							infoService.TransferedToYFlowMessages = 1;
							infoService.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							infoService.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = message.Service.ID;
							infoService.QueueID = 0;
							infoService.PersonID = person.ID;
							infoService.TransferedToYFlowMessages = 1;
							infoService.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							infoService.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = message.Service.ID;
							infoService.QueueID = message.Queue.ID;
							infoService.PersonID = 0;
							infoService.TransferedToYFlowMessages = 1;
							infoService.TransferedToYFlowMessagesByUsers = (person.Type == PersonTypes.User) ? 1 : 0;
							infoService.TransferedToYFlowMessagesByAgent = (person.Type == PersonTypes.Agent) ? 1 : 0;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						}
						else
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = message.Queue == null ? 0 : message.Queue.ID;
							info.PersonID = 0;
							info.TransferedToYFlowMessages = 1;
							Core.System.Instance.IntervalService.StoreInfo(info, interval);

							infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
							infoService.ServiceID = message.Service.ID;
							infoService.QueueID = message.Queue == null ? 0 : message.Queue.ID;
							infoService.PersonID = 0;
							infoService.TransferedToYFlowMessages = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
						}

						if (person == null)
							Tracer.TraceInfo("El sistema transfirió el mensaje {0} a yflow", message);
						else if (person.Type == DomainModel.PersonTypes.Agent)
							Tracer.TraceInfo("El agente {0} transfirió el mensaje {1} a yflow", person, message);
						else
							Tracer.TraceInfo("El usuario {0} transfirió el mensaje {1} a yflow", person, message);
					}
					else
					{
						Tracer.TraceError("YFlow indica que hay que volver a derivar. Se lo toma como error");
						throw new Exception("YFlow no devolvió ningún mensaje para devolver");
					}

					if (invokeResults.MustUpdateCaseParameters)
						DAL.CaseDAO.UpdateParameters(message.Case);
					if (invokeResults.MustUpdateMessageParameters)
						DAL.MessageDAO.UpdateParameters(message);
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error al querer invocar a yFlow luego de una transferencia de agente. Se cancela. Error: {0}", ex);
					MessageLogDAO.Insert(message, person, MessageLogTypes.MessageFailedTransferToYFlow, transfer.Description);
					if (person != null && person.Type == PersonTypes.Agent)
						AgentLogDAO.Insert(person.ID, AgentLogTypes.FailedTransferToYFlow, message.ID);
					throw;
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se transfería el mensaje {0} a yflow: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Envía un mensaje saliente
		/// </summary>
		/// <param name="person">El <see cref="DomainModel.Person"/> que está enviando el mensaje</param>
		/// <param name="profileId">El código de perfil de usuario al cual se enviará el mensaje</param>
		/// <param name="queue">El <see cref="DomainModel.Queue"/> que, asociado al perfil de usuario, permite identificar a los casos</param>
		/// <param name="service">El <see cref="DomainModel.Service"/> por donde saldrá el mensaje</param>
		/// <param name="socialServiceType">El tipo de servicio de red social</param>
		/// <param name="socialUserId">El código de usuario de red social al cual se le enviará el mensaje</param>
		/// <param name="messageIsPrivate">Indica si el mensaje será privado (<c>true</c>) o público (<c>false</c>)</param>
		/// <param name="createNewCase">Indica si creará un nuevo caso para el perfil de usuario (<c>true</c>) o se continuará
		/// con el caso existente (<c>false</c>)</param>
		/// <param name="closeCase"><c>true</c> para indicar que el caso se deberá cerrar; en caso contrario, <c>false</c></param>
		/// <param name="text">El texto del mensaje</param>
		/// <param name="associatedText">El texto del mensaje asociado o <c>null</c> si no se asocia otro mensaje</param>
		/// <param name="caseId">El código de caso que se continuará/reabrirá o <c>null</c> cuando se cree un nuevo caso</param>
		/// <param name="messageIdToReply">El código de mensaje que se responderá (para armar la conversación) o null cuando
		/// se trate de una nueva conversación</param>
		/// <param name="socialUser">Cuando se envia un mensaje a un usuario de Twitter que no tiene casos, contiene el
		/// <see cref="DomainModel.SocialUser"/> que se insertará. En el resto de los casos deberá ser <c>null</c></param>
		/// <param name="attachments">La colección de archivos adjuntos</param>
		/// <param name="sendParameters">Parámetros asociados al mensaje de respuesta</param>
		public async Task<long> Send(DomainModel.Person person
			, int? profileId
			, DomainModel.Queue queue
			, long? caseId
			, DomainModel.Service service
			, long? messageIdToReply
			, DomainModel.SocialServiceTypes socialServiceType
			, bool messageIsPrivate
			, bool createNewCase
			, bool closeCase
			, string text
			, string associatedText
			, long? socialUserId
			, DomainModel.SocialUser socialUser
			, List<DomainModel.Attachment> attachments
			, Dictionary<string, string> sendParameters
			, List<DomainModel.Tag> tags
			, int? importantTag)
		{
			if (person == null)
				throw new ArgumentNullException(nameof(person), "Debe especificar la persona que envia el mensaje");

			if (service == null)
				throw new ArgumentNullException(nameof(service), "Debe especificar el servicio");

			if (service.SocialServiceType != socialServiceType)
				throw new ArgumentOutOfRangeException(nameof(socialServiceType), socialServiceType, "El tipo de servicio de red social no corresponde con el del servicio");

			if (profileId == null && socialUser == null)
				throw new ArgumentException("El perfil de usuario (profileId) o usuario de red social (socialUser) son obligatorios");

			if (profileId != null && socialUser != null)
				throw new ArgumentException("No se puede especificar simultáneamente el perfil de usuario (profileId) y usuario de red social (socialUser)");

			if (profileId != null && caseId == null)
				throw new ArgumentNullException(nameof(caseId), "Cuando se especifica el perfil de usuario se deberá especificar el código del caso");

			if (profileId != null && socialUserId == null)
				throw new ArgumentNullException(nameof(caseId), "Cuando se especifica el perfil de usuario se deberá especificar el código de usuario de red social al cual se le enviará el mensaje");

			/*
			 * Cuando se especifica el parámetro socialUser el mensaje deberá ser de Twitter y público
			 */
			if (socialUser != null)
			{
				if (!createNewCase)
					throw new ArgumentOutOfRangeException(nameof(createNewCase), createNewCase, "Se deberá crear un nuevo caso para poder encribir mensajes públicos a Twitter de usuarios que no tienen casos previos");

				if (!string.IsNullOrEmpty(associatedText))
					throw new ArgumentOutOfRangeException(nameof(associatedText), associatedText, "Solamente se podrá escribir el mensaje público a Twitter de usuarios que no tienen casos previos");
			}

			if (string.IsNullOrWhiteSpace(text))
				text = null;

			if (string.IsNullOrWhiteSpace(associatedText))
				associatedText = null;

			switch (socialServiceType)
			{
				case Yoizen.Social.DomainModel.SocialServiceTypes.Twitter:
					if (messageIsPrivate)
					{
						if (string.IsNullOrEmpty(text))
						{
							if (string.IsNullOrEmpty(associatedText))
								throw new ArgumentException("Debe especificar un texto para la mensaje público asociado", "associatedMessage");

							if (associatedText.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
								throw new ArgumentOutOfRangeException(nameof(associatedText), string.Format("El texto del mensaje asociado público no puede superar los {1} caracteres: {0}", associatedText, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
						}
						else
						{
							if (!string.IsNullOrEmpty(associatedText) && associatedText.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
								throw new ArgumentOutOfRangeException(nameof(associatedText), string.Format("El texto del mensaje público asociado no puede superar los {1} caracteres: {0}", associatedText, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
						}
					}
					else
					{
						if (text.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit)
							throw new ArgumentOutOfRangeException(nameof(text), string.Format("El texto del mensaje público no puede superar los {1} caracteres: {0}", text, DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));
					}
					break;

				case Yoizen.Social.DomainModel.SocialServiceTypes.Facebook:
					if (string.IsNullOrEmpty(text))
						throw new ArgumentException("Debe especificar un texto para la respuesta", "text");

					// Cuando la conversación es privada, no se puede mandar un mensaje público
					if (messageIsPrivate && !string.IsNullOrEmpty(associatedText))
						throw new ArgumentOutOfRangeException(nameof(associatedText), associatedText, "Para mensajes salientes privados de Facebook se podrá mandar únicamente mensajes privados");
					break;

				case Yoizen.Social.DomainModel.SocialServiceTypes.Mail:
				case Yoizen.Social.DomainModel.SocialServiceTypes.Chat:
				default:
					if (string.IsNullOrEmpty(text))
						throw new ArgumentException("Debe especificar un texto para la respuesta", "text");
					break;
			}

			if (person.Type == DomainModel.PersonTypes.Agent)
				Tracer.TraceInfo("El agente {0} está enviando un mensaje saliente", person);
			else
				Tracer.TraceInfo("El supervisor {0} está enviando un mensaje saliente", person);

			try
			{
				if (socialUser != null)
				{
					DAL.SocialUserDAO socialUserDAO = new SocialUserDAO(socialUser);

					socialUserDAO.Insert(out profileId);

					socialUserId = socialUser.ID;
				}

				if (closeCase &&
					caseId != null &&
					Core.System.Instance.QueueService.AreCaseMessagesEnqueuedOrAssigned(caseId.Value))
					closeCase = false;

				if (socialServiceType == SocialServiceTypes.Mail &&
					sendParameters != null &&
					sendParameters.ContainsKey("InsertedImages") &&
					sendParameters["InsertedImages"].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					if (attachments == null)
						attachments = new List<DomainModel.Attachment>();

					ProcessEmbededMailImages(ref text, attachments, sendParameters);
				}

				CaseStartedBySources caseStartedBySources = CaseStartedBySources.OutgoingMessage;

				var insertedMessageId = MessageDAO.Send(person
					, (int) profileId
					, queue?.ID
					, caseId
					, service
					, messageIdToReply
					, socialServiceType
					, (long) socialUserId
					, messageIsPrivate
					, createNewCase
					, closeCase
					, text
					, associatedText
					, attachments
					, sendParameters
					, caseStartedBySources);

				var @case = DAL.CaseDAO.GetOneByMessage(insertedMessageId, false);
				if (@case != null)
				{
					@case.LastPerson = person ?? @case.LastPerson;
					@case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					@case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageId.ToString();
					@case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) socialServiceType).ToString();
					@case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();

					DAL.CaseDAO.UpdateParameters(@case);
				}

				if (person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} envió el mensaje saliente", person);
				else
					Tracer.TraceInfo("El supervisor {0} envió el mensaje saliente", person);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = person.ID;
				info.OutgoingMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.OutboundMessages = 1;
				infoService.NewCases = (createNewCase) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = person.ID;
				infoService.OutboundMessages = 1;
				infoService.NewCases = (createNewCase) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (queue != null)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = queue.ID;
					info.PersonID = person.ID;
					info.OutgoingMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = service.ID;
					infoService.QueueID = queue.ID;
					infoService.PersonID = 0;
					infoService.OutboundMessages = 1;
					infoService.NewCases = (createNewCase) ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				if (createNewCase)
				{
					if (queue != null)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = queue.ID;
						info.PersonID = 0;
						info.NewCases = 1;
						Core.System.Instance.IntervalService.StoreInfo(info, interval);
					}

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = 0;
					info.NewCases = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					DomainModel.Historical.DailyCase infoCase;
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.CasesStarted = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

					if (tags != null && tags.Count > 0)
					{
						@case.Tags.AddRange(tags);
						this.ApplyTags(@case, tags.Select(t => t.ID), TaggedBySources.Agent, person);
					}

					if (importantTag != null && importantTag > 0)
					{
						this.UpdateCase(@case, person, null, null, null, TaggedBySources.Agent, importantTag);
					}
				}

				if (closeCase)
				{
					await this.CloseCaseAsync(@case, CaseClosingResponsibles.Agent, person, interval, false, queue);
				}

				if (person.Type == DomainModel.PersonTypes.Agent)
				{
					var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) person);
					if (connectionInfo.Status == ConnectionStatuses.Aux &&
						DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages != null &&
						connectionInfo.AuxReason == DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages.Value)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = person.ID;
						info.AgentOutTime = connectionInfo.GetSecondsSinceLastStatusChange();
						Core.System.Instance.IntervalService.StoreInfo(info, interval);
					}

					HistSessionsAgentsCasesMessagesDAO.Insert(insertedMessageId, @case.ID, connectionInfo.SessionID, person.ID, false);
				}

				await Reply(insertedMessageId, @case);
				Tracer.TraceInfo("Se envió el mensaje {0} al caso {1}", insertedMessageId, @case);

				await Core.System.Instance.CasesService.RegisterCaseToBeClosed(@case, service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);

				return insertedMessageId;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló cuando se envíaba el mensaje saliente {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Actualiza el service level del último segmento de un mensaje
		/// </summary>
		/// <param name="message">El mensaje a actualizar</param>
		/// <param name="expired">Indica si expiró o no</param>
		public void UpdateServiceLevel(Message message, bool unfulfilled, bool expired)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			try
			{
				var lastSegment = message.LastSegment;
				var serviceLevel = message.Queue.ServiceLevelParameters[ServiceLevelTypes.ServiceLevel];

				if (!Licensing.LicenseManager.Instance.License.Configuration.CalculateSLLIndependently)
				{
					message.OutOfSLL = unfulfilled || expired ?
						message.LastSegment != null &&
						message.LastSegment.EnqueuedDate.HasValue &&
						serviceLevel.Actions.AvailableDaysAndTimes &&
						serviceLevel.Actions.DaysAndTimes.IsTimeWorkable(message.LastSegment.EnqueuedDate.Value)
						: false;
				}

				MessageSegmentDAO.UpdateServiceLevel(message, unfulfilled, expired);
				if (lastSegment.Unfulfilled != true)
					lastSegment.Unfulfilled = unfulfilled;
				lastSegment.Expired = expired;
				message.OutOfServiceLevel = unfulfilled || expired;

				var wasCaseOutOfSL = message.Case.OutOfSL;
				message.Case.OutOfSL = unfulfilled || expired;

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily info;

				if (!expired)
				{
					if (unfulfilled)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = message.Queue.ID;
						info.PersonID = 0;
						info.OutOfSLInQueue = (message.AssignedTo == null) ? 1 : 0;
						info.OutOfSLInAgent = (message.AssignedTo == null) ? 0 : 1;
						info.CasesOutOfSL = !wasCaseOutOfSL ? info.OutOfSLInQueue : 0;

						if (!Licensing.LicenseManager.Instance.License.Configuration.CalculateSLLIndependently)
						{
							info.OutOfSLLInQueue = (message.AssignedTo == null && message.OutOfSLL == true)
														? 1
														: 0;
							info.OutOfSLLInAgent = (message.AssignedTo != null && message.OutOfSLL == true)
														? 1
														: 0;
							info.CasesOutOfSLL = !wasCaseOutOfSL && message.OutOfSLL == true
														? info.OutOfSLLInQueue
														: 0;

							info = DomainModel.Historical.Daily.CreateForInterval(interval);

							info.OutOfSLLInQueue = (message.AssignedTo == null && message.OutOfSLL == true)
														? 1
														: 0;

							info.OutOfSLLInAgent = (message.AssignedTo != null && message.OutOfSLL == true)
														? 1
														: 0;
						}

						Core.System.Instance.IntervalService.StoreInfo(info, interval);
					}
				}
				else
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = 0;
					info.Expired = 1;
					info.ExpiredSLL = message.OutOfSLL == true ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo grabar la marca de impuntual en el mensaje {0}: {1}", message, ex);
				throw;
			}
		}


		/// <summary>
		/// Actualiza el service level laboral del último segmento de un mensaje
		/// </summary>
		/// <param name="message">El mensaje a actualizar</param>
		/// <param name="expired">Indica si expiró o no</param>
		public void UpdateOutOfSLL(Message message, bool expired)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			try
			{
				var lastSegment = message.LastSegment;
				message.OutOfSLL = expired;

				MessageDAO.UpdateWorkingServiceLevel(message);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;

				info.ExpiredSLL = message.OutOfSLL == true ? 1 : 0;

				info.OutOfSLLInQueue = (message.AssignedTo == null && message.OutOfSLL == true)
														? 1
														: 0;
				info.OutOfSLLInAgent = (message.AssignedTo != null && message.OutOfSLL == true)
											? 1
											: 0;
				info.CasesOutOfSLL = message.OutOfSLL == true
											? info.OutOfSLLInQueue
											: 0;

				Core.System.Instance.IntervalService.StoreInfo(info, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[SLL] No se pudo grabar la marca de impuntual en el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Envía un mensaje saliente
		/// </summary>
		/// <param name="person">El <see cref="DomainModel.Person"/> que está enviando el mensaje</param>
		/// <param name="profileId">El código de perfil de usuario al cual se enviará el mensaje</param>
		/// <param name="service">El <see cref="DomainModel.Service"/> por donde saldrá el mensaje</param>
		/// <param name="socialUserId">El código de usuario de red social al cual se le enviará el mensaje</param>
		/// <param name="text">El texto del mensaje</param>
		/// <param name="attachments">La colección de archivos adjuntos</param>
		/// <param name="sendParameters">Parámetros asociados al mensaje de respuesta</param>
		public async Task<long> SendMail(DomainModel.Person person
			, long socialUserId
			, int? profileId
			, DomainModel.Service service
			, string text
			, List<DomainModel.Attachment> attachments
			, Dictionary<string, string> sendParameters
			, List<DomainModel.Tag> tags
			, int? importantTag
			, string observations
			, bool closeCase)
		{
			if (person == null)
				throw new ArgumentNullException(nameof(person), "Debe especificar la persona que envia el mensaje");

			if (service == null)
				throw new ArgumentNullException(nameof(service), "Debe especificar el servicio");

			if (string.IsNullOrEmpty(text))
				throw new ArgumentException("Debe especificar un texto para la respuesta", "text");

			if (person.Type == DomainModel.PersonTypes.Agent)
				Tracer.TraceInfo("El agente {0} está enviando un mail saliente", person);
			else
				Tracer.TraceInfo("El supervisor {0} está enviando un mail saliente", person);

			try
			{
				if (sendParameters != null &&
					sendParameters.ContainsKey("InsertedImages") &&
					sendParameters["InsertedImages"].Equals("true", StringComparison.InvariantCultureIgnoreCase))
				{
					if (attachments == null)
						attachments = new List<DomainModel.Attachment>();

					ProcessEmbededMailImages(ref text, attachments, sendParameters);
				}

				CaseStartedBySources caseStartedBySources = CaseStartedBySources.OutgoingMessage;

				var insertedMessageId = MessageDAO.SendMail(person
					, socialUserId
					, profileId.Value
					, service.ID
					, text
					, attachments
					, sendParameters
					, caseStartedBySources);

				if (person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} envió el mensaje saliente", person);
				else
					Tracer.TraceInfo("El supervisor {0} envió el mensaje saliente", person);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.NewCases = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = person.ID;
				info.OutgoingMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.NewCases = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				var @case = DAL.CaseDAO.GetOneByMessage(insertedMessageId, false);

				if (person.Type == DomainModel.PersonTypes.Agent)
				{
					var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) person);
					if (connectionInfo.Status == ConnectionStatuses.Aux &&
						DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages != null &&
						connectionInfo.AuxReason == DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages.Value)
					{
						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.PersonID = person.ID;
						info.AgentOutTime = connectionInfo.GetSecondsSinceLastStatusChange();
						Core.System.Instance.IntervalService.StoreInfo(info, interval);
					}

					HistSessionsAgentsCasesMessagesDAO.Insert(insertedMessageId, @case.ID, connectionInfo.SessionID, person.ID, false);
				}

				DomainModel.Historical.DailyCase infoCase;
				infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
				infoCase.CasesStarted = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

				sendParameters.TryGetValue("closeCase", out string close);

				if (sendParameters != null && sendParameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter))
				{
					@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = sendParameters[DomainModel.Case.ExtendedFieldsParameter];
				}

				if (tags != null && tags.Count > 0)
				{
					@case.Tags.AddRange(tags);
					this.ApplyTags(@case, tags.Select(t => t.ID), TaggedBySources.Agent, person);
					Tracer.TraceInfo("Se agregaron {0} etiquetas al caso {1} del mensaje {2}", tags.Count, @case.ID, text);
				}
				if (sendParameters != null && sendParameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter) || (tags != null && tags.Count > 0))
				{
					this.UpdateCase(@case, null, null, observations, @case.Parameters, TaggedBySources.Agent, importantTag);
				}

				if (closeCase)
				{
					var caseClosedBy = CaseClosingResponsibles.System;
					if (person != null)
						caseClosedBy = person.Type == PersonTypes.Agent ? CaseClosingResponsibles.Agent : CaseClosingResponsibles.Supervisor;
					await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
					{
						Case = @case,
						ClosedBy = caseClosedBy,
						Person = person,
						Interval = interval,
						StoreInDatabase = true,
						Queue = null,
						Message = null
					});

					Core.System.Instance.CasesService.RemovePendingCase(@case, person);
				}

				this.EnsureServiceInstance(service);
				var insertedMessage = await Reply(insertedMessageId, @case, sendInBackground: !service.SocialService.SupportsPublishToServiceBus(), person: person, readAssociatedMessage: false, readAttachments: true);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(insertedMessage);
				}

				await Core.System.Instance.CasesService.RegisterCaseToBeClosed(@case, service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);

				return insertedMessageId;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló cuando se envíaba el mensaje saliente {0}", ex);
				throw;
			}
		}

		/// <summary>
		/// Crea un perfil desde un mensaje saliente
		/// </summary>
		/// <param name="serviceType"></param>
		/// <param name="name"></param>
		/// <param name="email"></param>
		/// <param name="phoneNumber"></param>
		/// <param name="businessData"></param>
		/// <param name="extendedFields"></param>
		public SocialUser CreateSocialUserFromOutgoing(SocialServiceTypes serviceType,
			string name,
			string email,
			long phoneNumber,
			string businessData,
			string extendedFields)
		{
			SocialUser socialUser = null;
			try
			{
				int? profileId = null;
				switch (serviceType)
				{
					case SocialServiceTypes.Mail:
						if (string.IsNullOrEmpty(email))
							throw new ArgumentNullException("Missing mail parameter for user");

						socialUser = new Mail.User();
						socialUser.ID = -1;
						break;
					case SocialServiceTypes.WhatsApp:
						if (phoneNumber == 0)
							throw new ArgumentNullException("Missing phoneNuber parameter for user");

						socialUser = new WhatsApp.User(phoneNumber);
						break;
					case SocialServiceTypes.Twitter:
						socialUser = new Twitter.User();
						socialUser.ID = -1;
						break;
					default:
						throw new NotImplementedException("Invalid SocialServiceType");
				}

				socialUser.Name = !string.IsNullOrEmpty(name) ? name : email;
				socialUser.DisplayName = !string.IsNullOrEmpty(name) ? name : email;
				socialUser.Email = email;

				if (phoneNumber > 0)
					socialUser.Parameters[DomainModel.SocialUser.PhoneNumberParameter] = phoneNumber.ToString();

				if (!string.IsNullOrEmpty(businessData))
					socialUser.BusinessData = businessData;

				if (!string.IsNullOrEmpty(extendedFields))
					socialUser.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter] = extendedFields;

				DAL.SocialUserDAO socialUserDAO = new SocialUserDAO(socialUser);
				socialUserDAO.Insert(out profileId);

				return socialUser;
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"Falló al crear el perfil {name} para la red social {serviceType} con error {ex}");
				return null;
			}
		}

		/// <summary>
		/// Envía un mensaje saliente de twitter
		/// </summary>
		/// <param name="person">El <see cref="DomainModel.Person"/> que está enviando el mensaje</param>
		/// <param name="service">El <see cref="DomainModel.Service"/> por donde saldrá el mensaje</param>
		/// <param name="twitterUser">El alias del usuario de twitter al cual se enviará el mensaje</param>
		/// <param name="text">El texto del mensaje</param>
		/// <param name="socialUser">Los datos del usuario de Twitter obtenidos desde la red social</param>
		public async Task<long> SendTwitter(Person person, string twitterUser, Service service, string text, SocialUser socialUser, int? importantTag, string observations, List<DomainModel.Tag> tags/*, Dictionary<string, string> sendParameters*/)
		{
			if (service == null)
				throw new ArgumentNullException(nameof(service), "Debe especificar el servicio");

			if (person != null && person.Type == PersonTypes.Agent && !((DomainModel.Agent) person).OutgoingMessagesForTwitterUser)
				throw new InvalidOperationException("El agente no puede enviar mensajes salientes de Twitter");

			if (string.IsNullOrEmpty(text))
				throw new ArgumentException("Debe especificar un texto para la respuesta", nameof(text));

			int maxLength = DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit;
			if (text.IndexOf($"@{twitterUser}", StringComparison.InvariantCultureIgnoreCase) == -1)
				maxLength -= (twitterUser.Length + 2);

			if (text.Length > maxLength)
				throw new ArgumentOutOfRangeException(nameof(text), string.Format("El texto del mensaje no puede superar los {0} caracteres", DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));

			if (socialUser == null || !socialUser.DisplayName.Equals(twitterUser, StringComparison.InvariantCultureIgnoreCase))
				throw new ArgumentException("Los datos del usuario de twitter son inválidos", nameof(socialUser));

			if (person != null)
			{
				if (person.Type == DomainModel.PersonTypes.Agent)
					Tracer.TraceInfo("El agente {0} está enviando un mensaje de twitter saliente", person);
				else
					Tracer.TraceInfo("El supervisor {0} está enviando un mensaje de twitter saliente", person);
			}
			else
			{
				Tracer.TraceInfo("El sistema está enviando un mensaje de twitter saliente");
			}

			try
			{
				int? socialUserProfileId;
				DAL.SocialUserDAO socialUserDAO = new SocialUserDAO(socialUser);
				socialUserDAO.Insert(out socialUserProfileId);

				CaseStartedBySources caseStartedBySources = CaseStartedBySources.OutgoingMessage;

				var insertedMessageId = MessageDAO.SendTwitter(person
					, socialUser.ID
					, socialUserProfileId.Value
					, service.ID
					, text
					, caseStartedBySources
					, out bool newCaseCreated);

				var @case = DAL.CaseDAO.GetOneByMessage(insertedMessageId, false);
				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				if (@case != null)
				{
					@case.LastPerson = person ?? @case.LastPerson;
					@case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					@case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageId.ToString();
					@case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) SocialServiceTypes.WhatsApp).ToString();
					@case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();

					if (tags != null)
					{
						// Actualizamos las estadísticas de etiquetas de tiempo real de las colas
						foreach (var tag in tags)
						{
							if (!@case.Tags.Contains(tag))
								@case.Tags.Add(tag);

							tag.Used = true;

							var infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
							infoTag.QueueID = 0;
							infoTag.PersonID = 0;
							infoTag.TagID = tag.ID;
							infoTag.Count = 1;
							Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);

							if (person != null)
							{
								infoTag = DomainModel.Historical.DailyTag.CreateForInterval(interval);
								infoTag.QueueID = 0;
								infoTag.PersonID = person.ID;
								infoTag.TagID = tag.ID;
								infoTag.Count = 1;
								Core.System.Instance.IntervalService.StoreInfo(infoTag, interval);
							}

							this.ApplyTags(@case, tags.Select(t => t.ID), TaggedBySources.Agent, person);
							Tracer.TraceInfo("Se agregaron etiquetas al caso {0}", @case.ID);
						}

						this.UpdateCase(@case, null, null, observations, @case.Parameters, TaggedBySources.Agent, importantTag);
					}

					//if (sendParameters != null && sendParameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter))
					//{
					//	@case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = sendParameters[DomainModel.Case.ExtendedFieldsParameter];
					//}

					//if (sendParameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter) || (tags != null))
					//{
					//	this.UpdateCase(@case, null, null, observations, @case.Parameters, TaggedBySources.Agent, importantTag);
					//}

					DAL.CaseDAO.UpdateParameters(@case);
				}

				if (person != null)
				{
					if (person.Type == DomainModel.PersonTypes.Agent)
						Tracer.TraceInfo("El agente {0} envió el mensaje saliente", person);
					else
						Tracer.TraceInfo("El supervisor {0} envió el mensaje saliente", person);
				}
				else
				{
					Tracer.TraceInfo("Se envió el mensaje saliente", person);
				}

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.NewCases = 1;
				info.OutgoingMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				if (person != null)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = person.ID;
					info.OutgoingMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					if (person.Type == DomainModel.PersonTypes.Agent)
					{
						var connectionInfo = Core.System.Instance.AgentsService.GetConnectionInfo((DomainModel.Agent) person);
						if (connectionInfo.Status == ConnectionStatuses.Aux &&
							DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages != null &&
							connectionInfo.AuxReason == DomainModel.SystemSettings.Instance.AuxReasonForOutgoingMessages.Value)
						{
							info = DomainModel.Historical.Daily.CreateForInterval(interval);
							info.QueueID = 0;
							info.PersonID = person.ID;
							info.AgentOutTime = connectionInfo.GetSecondsSinceLastStatusChange();
							Core.System.Instance.IntervalService.StoreInfo(info, interval);
						}

						HistSessionsAgentsCasesMessagesDAO.Insert(insertedMessageId, @case.ID, connectionInfo.SessionID, person.ID, false);
					}
				}

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.OutboundMessages = 1;
				infoService.NewCases = newCaseCreated ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (person != null)
				{
					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = person.ID;
					infoService.OutboundMessages = 1;
					infoService.NewCases = newCaseCreated ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				if (newCaseCreated)
				{
					DomainModel.Historical.DailyCase infoCase;
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.CasesStarted = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);
				}

				var insertedMessage = await Reply(insertedMessageId, @case, sendInBackground: !service.SocialService.SupportsPublishToServiceBus(), person: person, readAssociatedMessage: false, readAttachments: true);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
				{
					System.Instance.AutomaticExportService.StoreMessage(insertedMessage);
				}

				await Core.System.Instance.CasesService.RegisterCaseToBeClosed(@case, service.CasesSettings.MaxElapsedMinutesToCloseCases, CaseClosingSources.Normal);

				return insertedMessageId;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló cuando se envíaba el mensaje saliente {0}", ex);
				throw;
			}
		}

		public ActionOptions.SendWhatsappOptions ParseSendWhatsapp(Newtonsoft.Json.Linq.JObject jBody, ReplySources source, out string errorMessage, out int? errorCode)
		{
			errorMessage = null;
			errorCode = null;

			int serviceId;
			long phoneNumber;
			string text = null;
			DomainModel.Attachment[] attachments = null;
			List<DomainModel.Tag> tags = null;
			Dictionary<string, string> sendParameters = null;
			int? importantTag = null;
			string observations = null;

			if (jBody["serviceId"] == null || jBody["serviceId"].Type != Newtonsoft.Json.Linq.JTokenType.Integer)
			{
				errorMessage = "Must specify serviceId parameter";
				errorCode = 251;
				return null;
			}

			serviceId = jBody["serviceId"].ToObject<int>();
			DomainModel.Service service = DAL.ServiceDAO.GetOneFromCache(serviceId);
			if (service == null || service.Type != ServiceTypes.WhatsApp || !service.Enabled)
			{
				errorMessage = string.Format("Couldn't find service with id = {0}", serviceId);
				errorCode = 251;
				return null;
			}

			if (jBody["phoneNumber"] == null || jBody["phoneNumber"].Type != Newtonsoft.Json.Linq.JTokenType.String)
			{
				errorMessage = "Must specify phoneNumber parameter";
				errorCode = 251;
				return null;
			}

			try
			{
				phoneNumber = long.Parse(jBody["phoneNumber"].ToString());
			}
			catch
			{
				errorMessage = "Must specify phoneNumber parameter";
				errorCode = 251;
				return null;
			}

			if (jBody["text"] == null ||
				jBody["text"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
				jBody["text"].ToString().Length == 0)
			{
				if ((jBody["parameters"] == null ||
					jBody["parameters"].Type == Newtonsoft.Json.Linq.JTokenType.Null) &&
					(jBody["hsm"] == null ||
					jBody["hsm"].Type == Newtonsoft.Json.Linq.JTokenType.Null))
				{
					errorMessage = "Must specify text parameter";
					errorCode = 251;
					return null;
				}
			}
			else
			{
				text = jBody["text"].ToString();
			}

			string businessData = null;
			long socialUserId = 0;
			if (jBody["profileId"] != null && jBody["profileId"].Type == Newtonsoft.Json.Linq.JTokenType.Integer)
			{
				socialUserId = jBody["profileId"].ToObject<long>();
			}

			if (socialUserId == -1 && source == ReplySources.Agent)
			{
				string name = null;
				string mail = null;
				string profileExtendedFields = null;

				if (jBody["name"] != null && jBody["name"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					name = jBody["name"].ToString();
				}

				if (jBody["mail"] != null && jBody["mail"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					mail = jBody["mail"].ToString();
				}

				if (jBody["businessData"] != null && jBody["businessData"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					businessData = jBody["businessData"].ToString();
				}

				if (jBody["profileExtendedFields"] != null && jBody["profileExtendedFields"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
				{
					profileExtendedFields = jBody["profileExtendedFields"].ToString();
				}

				var newSocialUser = CreateSocialUserFromOutgoing(service.SocialServiceType, name, mail, phoneNumber, businessData, profileExtendedFields);
			}

			OptionsBussinesData businessDataOption = OptionsBussinesData.NoneAction;

			bool replaceBusinessData = false;
			if (jBody["replaceBusinessData"] != null &&
				jBody["replaceBusinessData"].Type == JTokenType.Boolean &&
				source != ReplySources.Agent)
			{
				replaceBusinessData = jBody["replaceBusinessData"].ToObject<bool>();
			}

			if (replaceBusinessData)
			{
				string jBusinessData = string.Empty;

				if (jBody["businessData"] != null &&
					jBody["businessData"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					jBusinessData = jBody["businessData"].ToString();
				}

				//Si el param viene vacio BORRAMOS el businessData
				if (string.IsNullOrEmpty(jBusinessData))
				{
					businessDataOption = OptionsBussinesData.DeleteAll;
				}
				else
				{
					if (!Core.System.Instance.Logic.IsBusinessDataValid(jBusinessData))
					{
						errorMessage = "businessData has invalid contents, must be a string separated by , and #";
						errorCode = 251;
						return null;
					}

					businessDataOption = OptionsBussinesData.Replace;
					businessData = jBusinessData;
				}
			}

			DomainModel.ServiceSettings.WhatsappSettings.HSMTemplate template = null;
			DomainModel.Whatsapp.HSMTemplateSendDefinition sendDefinition = null;

			if (jBody["parameters"] != null &&
				jBody["parameters"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
			{
				if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound)
				{
					errorMessage = string.Format("Send HSM is not licensed", serviceId);
					errorCode = 251;
					return null;
				}

				var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				if (!settings.AllowToSendHSM)
				{
					errorMessage = string.Format("Service {0} doesn't allow to send HSM", serviceId);
					errorCode = 251;
					return null;
				}

				if (source == ReplySources.Agent && !settings.AllowAgentsToSendHSM)
				{
					errorMessage = string.Format("Service {0} doesn't allow to send HSM to agents", serviceId);
					errorCode = 251;
					return null;
				}

				sendDefinition = new DomainModel.Whatsapp.HSMTemplateSendDefinition();

				var jParameters = (Newtonsoft.Json.Linq.JObject) jBody["parameters"];

				if (jParameters["templateName"] != null &&
					jParameters["templateName"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					sendDefinition.ElementName = jParameters["templateName"].ToString();
				}
				else
				{
					errorMessage = string.Format("Missing templateName", serviceId);
					errorCode = 251;
					return null;
				}

				if (jParameters["templateNamespace"] != null &&
					jParameters["templateNamespace"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					sendDefinition.Namespace = jParameters["templateNamespace"].ToString();
				}
				else
				{
					errorMessage = string.Format("Missing templateNamespace", serviceId);
					errorCode = 251;
					return null;
				}

				if (jParameters["language"] != null &&
					jParameters["language"].Type == Newtonsoft.Json.Linq.JTokenType.String)
				{
					sendDefinition.Language = jParameters["language"].ToString();
				}
				else
				{
					errorMessage = string.Format("Missing language", serviceId);
					errorCode = 251;
					return null;
				}

				template = settings.HSMTemplates.FirstOrDefault(t =>
					t.Namespace.Equals(sendDefinition.Namespace) &&
					t.ElementName.Equals(sendDefinition.ElementName) &&
					t.Language.Equals(sendDefinition.Language));

				if (template == null)
				{
					errorMessage = "Couldn't find template in specified service";
					errorCode = 251;
					return null;
				}

				if (source == ReplySources.Agent && !template.AvaiableForAgents)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for agents", serviceId);
					errorCode = 251;
					return null;
				}
				else if (source == ReplySources.Supervisor && !template.AvaiableForSupervisors)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for supervisors", serviceId);
					errorCode = 251;
					return null;
				}
				else if (source == ReplySources.ExternalIntegration && !template.AvaiableForIntegrations)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for external integrations", serviceId);
					errorCode = 251;
					return null;
				}

				if (template.TemplateParameters != null && template.TemplateParameters.Length > 0)
				{
					if (jParameters["templateData"] == null ||
						jParameters["templateData"].Type != Newtonsoft.Json.Linq.JTokenType.Array)
					{
						errorMessage = string.Format("Missing templateData", serviceId);
						errorCode = 251;
						return null;
					}

					var jTemplateData = (Newtonsoft.Json.Linq.JArray) jParameters["templateData"];
					if (jTemplateData.Count != template.TemplateParameters.Length)
					{
						errorMessage = string.Format("Missing templateData parameters. Received {0} but expected {1}", jTemplateData.Count, template.TemplateParameters.Length);
						errorCode = 251;
						return null;
					}

					var parameters = new List<DomainModel.Whatsapp.HSMTemplateParameter>();

					if (string.IsNullOrEmpty(text))
						text = template.Template;

					int index = 0;
					foreach (var templateParameter in template.TemplateParameters)
					{
						if (jTemplateData[index] == null ||
							jTemplateData[index].Type != Newtonsoft.Json.Linq.JTokenType.String)
						{
							errorMessage = string.Format("template[{0}] is missing", index);
							errorCode = 251;
							return null;
						}

						var value = jTemplateData[index].ToString();
						if (string.IsNullOrEmpty(value))
						{
							errorMessage = string.Format("template[{0}] must be a non empty string", index);
							errorCode = 251;
							return null;
						}

						if (value.IndexOf("    ") >= 0 ||
							value.IndexOf('\t') >= 0 ||
							value.IndexOf('\n') >= 0)
						{
							errorMessage = string.Format("template[{0}] cannot contain four consecutive spaces, new lines or tab", templateParameter.Name);
							errorCode = 251;
							return null;
						}

						parameters.Add(new DomainModel.Whatsapp.HSMTemplateParameter()
						{
							Name = templateParameter.Name,
							Value = value
						});

						text = text.Replace($"{{{{{templateParameter.Name}}}}}", value);

						index++;
					}

					sendDefinition.Parameters = parameters.ToArray();
				}
				else if (string.IsNullOrEmpty(text))
					text = template.Template;

				sendParameters = new Dictionary<string, string>();
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter] = sendDefinition.ElementName;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter] = sendDefinition.Namespace;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage] = sendDefinition.Language;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter] = Newtonsoft.Json.JsonConvert.SerializeObject(sendDefinition);
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMParameter] = true.ToString();
			}

			if (jBody["hsm"] != null && jBody["hsm"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
			{
				if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound)
				{
					errorMessage = string.Format("Send HSM is not licensed", serviceId);
					errorCode = 251;
					return null;
				}

				var jHSM = (Newtonsoft.Json.Linq.JObject) jBody["hsm"];

				var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				if (!settings.AllowToSendHSM)
				{
					errorMessage = string.Format("Service {0} doesn't allow to send HSM", serviceId);
					errorCode = 251;
					return null;
				}

				if (source == ReplySources.Agent && !settings.AllowAgentsToSendHSM)
				{
					errorMessage = string.Format("Service {0} doesn't allow to send HSM to agents", serviceId);
					errorCode = 251;
					return null;
				}

				if (jHSM["elementName"] == null || jHSM["elementName"].Type != Newtonsoft.Json.Linq.JTokenType.String)
				{
					errorMessage = "Missing elementName";
					errorCode = 251;
					return null;
				}

				if (jHSM["namespace"] == null || jHSM["namespace"].Type != Newtonsoft.Json.Linq.JTokenType.String)
				{
					errorMessage = "Missing namespace";
					errorCode = 251;
					return null;
				}

				if (jHSM["language"] == null || jHSM["language"].Type != Newtonsoft.Json.Linq.JTokenType.String)
				{
					errorMessage = "Missing language";
					errorCode = 251;
					return null;
				}
				var elementName = jHSM["elementName"].ToString();
				var @namespace = jHSM["namespace"].ToString();
				var language = jHSM["language"].ToString();


				template = settings.HSMTemplates.FirstOrDefault(t =>
					t.Namespace.Equals(@namespace) &&
					t.ElementName.Equals(elementName) &&
					t.Language.Equals(language));

				if (template == null)
				{
					errorMessage = "Couldn't find template in specified service";
					errorCode = 251;
					return null;
				}
				sendDefinition = new DomainModel.Whatsapp.HSMTemplateSendDefinition();
				sendDefinition.Namespace = @namespace;
				sendDefinition.ElementName = elementName;
				sendDefinition.Language = language;

				if (source == ReplySources.Agent && !template.AvaiableForAgents)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for agents", serviceId);
					errorCode = 251;
					return null;
				}
				else if (source == ReplySources.Supervisor && !template.AvaiableForSupervisors)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for supervisors", serviceId);
					errorCode = 251;
					return null;
				}
				else if (source == ReplySources.ExternalIntegration && !template.AvaiableForIntegrations)
				{
					errorMessage = string.Format("HSM Template from service {0} is not available for external integrations", serviceId);
					errorCode = 251;
					return null;
				}

				var jHeader = jHSM["header"];
				sendDefinition.HeaderType = template.HeaderType;
				switch (template.HeaderType)
				{
					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Text:
						if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = "HSM Template header missing";
							errorCode = 251;
							return null;
						}

						if (jHeader["type"] == null ||
							jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
							!jHeader["type"].ToString().Equals("text"))
						{
							errorMessage = "HSM Template header's type must be 'text'";
							errorCode = 251;
							return null;
						}

						if (template.HeaderTextParameter != null)
						{
							var jText = jHeader["text"];
							if (jText == null || jText.Type != Newtonsoft.Json.Linq.JTokenType.Object)
							{
								errorMessage = "HSM Template header is missing object 'text'";
								errorCode = 251;
								return null;
							}

							var jParameter = jText["parameter"];
							if (jParameter == null || jParameter.Type != Newtonsoft.Json.Linq.JTokenType.Object)
							{
								errorMessage = "HSM Template header.text is missing object 'parameter'";
								errorCode = 251;
								return null;
							}

							if (jParameter["name"] == null ||
								jParameter["name"].Type != Newtonsoft.Json.Linq.JTokenType.String)
							{
								errorMessage = "HSM Template header.text.parameter.name is missing";
								errorCode = 251;
								return null;
							}

							if (jParameter["value"] == null || jParameter["value"].Type != Newtonsoft.Json.Linq.JTokenType.String)
							{
								errorMessage = "HSM Template header.text.parameter.name is missing";
								errorCode = 251;
								return null;
							}

							var parameterName = jParameter["name"].ToString();
							var parameterValue = jParameter["value"].ToString();

							if (!template.HeaderTextParameter.Name.Equals(parameterName))
							{
								errorMessage = "HSM Template header.text.parameter.name is not the expected";
								errorCode = 251;
								return null;
							}

							if (string.IsNullOrEmpty(parameterValue))
							{
								errorMessage = "HSM Template header.text.parameter.value must be a non empty string";
								errorCode = 251;
								return null;
							}

							sendDefinition.HeaderTextParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
							{
								Name = parameterName,
								Value = parameterValue
							};
						}
						break;

					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Media:
						if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = "HSM Template header missing";
							errorCode = 251;
							return null;
						}

						if (jHeader["type"] == null ||
							jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
							!jHeader["type"].ToString().Equals("media"))
						{
							errorMessage = "HSM Template header.type must be 'media'";
							errorCode = 251;
							return null;
						}

						var jMedia = jHeader["media"];
						if (jMedia == null || jMedia.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = "HSM Template header.media is missing";
							errorCode = 251;
							return null;
						}

						if (jMedia["type"] == null ||
							jMedia["type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
						{
							errorMessage = "HSM Template header.media.type is missing";
							errorCode = 251;
							return null;
						}

						var mediaType = jMedia["type"].ToString();

						string mediaPropertyName = null;
						sendDefinition.HeaderMediaType = template.HeaderMediaType;
						switch (template.HeaderMediaType)
						{
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Document:
								mediaPropertyName = "document";
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Image:
								mediaPropertyName = "image";
								break;
							case DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes.Video:
								mediaPropertyName = "video";
								break;
							default:
								break;
						}

						if (mediaPropertyName != null)
						{
							if (!mediaType.Equals(mediaPropertyName))
							{
								errorMessage = string.Format("HSM Template header.media.type must be '{0}'", mediaPropertyName);
								errorCode = 251;
								return null;
							}

							if (jMedia[mediaPropertyName] == null || jMedia[mediaPropertyName].Type != Newtonsoft.Json.Linq.JTokenType.Object)
							{
								errorMessage = string.Format("HSM Template header.media.{0} is missing", mediaPropertyName);
								errorCode = 251;
								return null;
							}

							var jMediaObject = (Newtonsoft.Json.Linq.JObject) jMedia[mediaPropertyName];

							if (jMediaObject["filename"] == null || jMediaObject["filename"].Type != Newtonsoft.Json.Linq.JTokenType.String)
							{
								errorMessage = string.Format("HSM Template header.media.{0}.filename is missing", mediaPropertyName);
								errorCode = 251;
								return null;
							}

							var filename = jMediaObject["filename"].ToString();
							if (string.IsNullOrEmpty(filename))
							{
								errorMessage = string.Format("HSM Template header.media.{0}.filename must be a non empty string", mediaPropertyName);
								errorCode = 251;
								return null;
							}

							if (jMediaObject["url"] != null && jMediaObject["url"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							{
								sendDefinition.HeaderMediaUrl = jMediaObject["url"].ToString();
								if (string.IsNullOrEmpty(sendDefinition.HeaderMediaUrl))
								{
									errorMessage = string.Format("HSM Template header.media.{0}.url must be a valid url", mediaPropertyName);
									errorCode = 251;
									return null;
								}

								if (!Uri.TryCreate(sendDefinition.HeaderMediaUrl, UriKind.Absolute, out Uri headerMediaUrl))
								{
									errorMessage = string.Format("HSM Template header.media.{0}.url must be a valid url", mediaPropertyName);
									errorCode = 251;
									return null;
								}

								sendDefinition.HeaderMediaUrlIsPublic = true;
								if (jMediaObject["isPublic"] != null && jMediaObject["isPublic"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
								{
									sendDefinition.HeaderMediaUrlIsPublic = jMediaObject["isPublic"].ToObject<bool>();
								}

								if (sendDefinition.HeaderMediaUrlIsPublic.Value)
								{
									if (!headerMediaUrl.Scheme.Equals("https"))
									{
										errorMessage = string.Format("HSM Template header.media.{0}.url must be secure for public urls", mediaPropertyName);
										errorCode = 251;
										return null;
									}
								}
								else
								{
									if (!headerMediaUrl.Scheme.Equals("https") && !headerMediaUrl.Scheme.Equals("http"))
									{
										errorMessage = string.Format("HSM Template header.media.{0}.url must be http or https for urls", mediaPropertyName);
										errorCode = 251;
										return null;
									}
								}
							}
							else if (jMediaObject["media"] != null && jMediaObject["media"].Type == Newtonsoft.Json.Linq.JTokenType.String)
							{

							}
							else if (jMediaObject["file"] != null && jMediaObject["file"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
							{
								var jFile = (Newtonsoft.Json.Linq.JObject) jMediaObject["file"];
								var attachment = new DomainModel.Attachment(0, 1);

								if (jFile["mimeType"] == null || jFile["mimeType"].Type != Newtonsoft.Json.Linq.JTokenType.String)
								{
									errorMessage = string.Format("HSM Template header.media.{0}.file.mimeType must be a non empty string", mediaPropertyName);
									errorCode = 251;
									return null;
								}

								attachment.MimeType = jFile["mimeType"].ToString();

								if (jFile["name"] != null && jFile["name"].Type == Newtonsoft.Json.Linq.JTokenType.String)
								{
									var name = jFile["name"].ToString();
									var filepath = global::System.Web.HttpContext.Current.Server.MapPath(string.Format("~/Configuration/Uploads/{0}", name));
									if (!File.Exists(filepath))
									{
										errorMessage = string.Format("HSM Template header.media.{0}.file.name must be an existing file on the server", mediaPropertyName);
										errorCode = 251;
										return null;
									}

									attachment.FileName = filename;
									attachment.Data = File.ReadAllBytes(filepath);
									attachment.FileSize = attachment.Data.Length;
								}
								else if (jFile["data"] != null)
								{
									byte[] data = null;
									try
									{
										if (jFile["data"].Type == Newtonsoft.Json.Linq.JTokenType.Bytes)
										{
											data = jFile["data"].ToObject<byte[]>();
										}
										else if (jFile["data"].Type == Newtonsoft.Json.Linq.JTokenType.String)
										{
											data = Convert.FromBase64String(jFile["data"].ToString());
										}
										else
										{
											errorMessage = string.Format("HSM Template header.media.{0}.file.data must be a base64 string with the file contents", mediaPropertyName);
											errorCode = 251;
											return null;
										}
									}
									catch
									{
										errorMessage = string.Format("HSM Template header.media.{0}.file.data must be a base64 string with the file contents", mediaPropertyName);
										errorCode = 251;
										return null;
									}

									if (data == null || data.Length == 0)
									{
										errorMessage = string.Format("HSM Template header.media.{0}.file.data must be a base64 string with the file contents", mediaPropertyName);
										errorCode = 251;
										return null;
									}

									attachment.FileName = filename;
									attachment.Data = data;
									attachment.FileSize = data.Length;
								}

								attachments = new DomainModel.Attachment[1] { attachment };

								sendDefinition.HeaderMediaFromAttachment = true;
							}
							else
							{
								errorMessage = string.Format("HSM Template header.media.{0} is missing one of the file contents' properties", mediaPropertyName);
								errorCode = 251;
								return null;
							}

							sendDefinition.HeaderMediaFileName = filename;
						}

						break;
					case DomainModel.Whatsapp.HSMTemplateHeaderTypes.Location:
						if (jHeader == null || jHeader.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = "HSM Template header missing";
							errorCode = 251;
							return null;
						}

						if (jHeader["type"] == null ||
							jHeader["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
							!jHeader["type"].ToString().Equals("location"))
						{
							errorMessage = "HSM Template header.type must be 'location'";
							errorCode = 251;
							return null;
						}

						var jLocation = jHeader["location"];
						if (jLocation == null || jLocation.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = "HSM Template header.location is missing";
							errorCode = 251;
							return null;
						}

						if (jLocation["latitude"] == null ||
							(
							jLocation["latitude"].Type != Newtonsoft.Json.Linq.JTokenType.Float &&
							jLocation["latitude"].Type != Newtonsoft.Json.Linq.JTokenType.Integer
							))
						{
							errorMessage = "HSM Template header.location.latitude is missing";
							errorCode = 251;
							return null;
						}

						if (jLocation["longitude"] == null ||
							(
							jLocation["longitude"].Type != Newtonsoft.Json.Linq.JTokenType.Float &&
							jLocation["longitude"].Type != Newtonsoft.Json.Linq.JTokenType.Integer)
							)
						{
							errorMessage = "HSM Template header.location.longitude is missing";
							errorCode = 251;
							return null;
						}

						var latitude = jLocation["latitude"].ToObject<double>();
						var longitude = jLocation["longitude"].ToObject<double>();

						if (latitude < -90 || latitude > 90)
						{
							errorMessage = "HSM Template header.location.latitude must be between -90 and 90";
							errorCode = 251;
							return null;
						}

						if (longitude < -180 || longitude > 180)
						{
							errorMessage = "HSM Template header.location.longitude must be between -180 and 180";
							errorCode = 251;
							return null;
						}

						sendDefinition.HeaderLocationLatitude = latitude;
						sendDefinition.HeaderLocationLongitude = longitude;

						if (jLocation["name"] != null &&
							jLocation["name"].Type == JTokenType.String &&
							!string.IsNullOrEmpty(jLocation["name"].ToString()) &&
							jLocation["address"] != null &&
							jLocation["address"].Type == JTokenType.String &&
							!string.IsNullOrEmpty(jLocation["address"].ToString()))
						{
							sendDefinition.HeaderLocationName = jLocation["name"].ToString();
							sendDefinition.HeaderLocationAddress = jLocation["address"].ToString();
						}

						break;
					default:
						break;
				}

				if (template.TemplateParameters != null && template.TemplateParameters.Length > 0)
				{
					var jHSMBody = (Newtonsoft.Json.Linq.JObject) jHSM["body"];
					if (jHSMBody == null || jHSMBody.Type != Newtonsoft.Json.Linq.JTokenType.Object)
					{
						errorMessage = "HSM Template body missing";
						errorCode = 251;
						return null;
					}

					var parameters = new List<DomainModel.Whatsapp.HSMTemplateParameter>();

					if (string.IsNullOrEmpty(text))
						text = template.Template;

					foreach (var templateParameter in template.TemplateParameters)
					{
						if (jHSMBody[templateParameter.Name] == null || jHSMBody[templateParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
						{
							errorMessage = string.Format("HSM Template body.{0} is missing", templateParameter.Name);
							errorCode = 251;
							return null;
						}

						var value = jHSMBody[templateParameter.Name].ToString();
						if (string.IsNullOrEmpty(value))
						{
							errorMessage = string.Format("HSM Template body.{0} must be a non empty string", templateParameter.Name);
							errorCode = 251;
							return null;
						}

						if (value.IndexOf("    ") >= 0 ||
							value.IndexOf('\t') >= 0 ||
							value.IndexOf('\n') >= 0)
						{
							errorMessage = string.Format("HSM Template body.{0} cannot contain four consecutive spaces, new lines or tab", templateParameter.Name);
							errorCode = 251;
							return null;
						}

						parameters.Add(new DomainModel.Whatsapp.HSMTemplateParameter()
						{
							Name = templateParameter.Name,
							Value = value
						});

						text = text.Replace($"{{{{{templateParameter.Name}}}}}", value);
					}

					sendDefinition.Parameters = parameters.ToArray();
				}
				else if (string.IsNullOrEmpty(text))
					text = template.Template;

				if (template.ButtonsType != DomainModel.Whatsapp.HSMTemplateButtonsTypes.None)
				{
					if (jHSM["buttons"] == null || jHSM["buttons"].Type != Newtonsoft.Json.Linq.JTokenType.Array)
					{
						errorMessage = "HSM Template buttons is missing and must be an array of objects";
						errorCode = 251;
						return null;
					}

					var jButtons = (Newtonsoft.Json.Linq.JArray) jHSM["buttons"];

					if (jButtons.Count != template.Buttons.Length)
					{
						errorMessage = string.Format("HSM Template buttons length must be {0}", template.Buttons.Length);
						errorCode = 251;
						return null;
					}

					sendDefinition.Buttons = new DomainModel.Whatsapp.HSMTemplateButton[template.Buttons.Length];

					int index = 0;
					foreach (var jButton in jButtons)
					{
						if (jButton.Type != Newtonsoft.Json.Linq.JTokenType.Object)
						{
							errorMessage = string.Format("HSM Template buttons[{0}] must be an object", index);
							errorCode = 251;
							return null;
						}

						if (jButton["type"] == null || jButton["type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
						{
							errorMessage = string.Format("HSM Template buttons[{0}].type is missing", index);
							errorCode = 251;
							return null;
						}

						if (jButton["index"] == null || jButton["index"].Type != Newtonsoft.Json.Linq.JTokenType.Integer)
						{
							errorMessage = string.Format("HSM Template buttons[{0}].index is missing", index);
							errorCode = 251;
							return null;
						}

						if (index != jButton["index"].ToObject<int>())
						{
							errorMessage = string.Format("HSM Template buttons[{0}].index must be {1}", index, index);
							errorCode = 251;
							return null;
						}

						var type = jButton["type"].ToString();
						var button = template.Buttons[index];

						sendDefinition.ButtonsType = template.ButtonsType;

						switch (template.ButtonsType)
						{
							case DomainModel.Whatsapp.HSMTemplateButtonsTypes.QuickReply:
								{
									if (!type.Equals("quick_reply"))
									{
										errorMessage = string.Format("HSM Template buttons[{0}].type must be 'quick_reply'", index);
										errorCode = 251;
										return null;
									}

									if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
										errorCode = 251;
										return null;
									}

									var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
									if (jParameter[button.QuickReplyParameter.Name] == null || jParameter[button.QuickReplyParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.QuickReplyParameter.Name);
										errorCode = 251;
										return null;
									}

									var value = jParameter[button.QuickReplyParameter.Name].ToString();
									if (string.IsNullOrEmpty(value))
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.QuickReplyParameter.Name);
										errorCode = 251;
										return null;
									}

									sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
									{
										QuickReplyParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
										{
											Name = button.QuickReplyParameter.Name,
											Value = value
										}
									};
								}
								break;
							case DomainModel.Whatsapp.HSMTemplateButtonsTypes.AuthCode:
								{
									switch (button.AuthCodeButtonType.Value)
									{
										case DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode:
											if (!type.Equals("url"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'url'", index);
												errorCode = 251;
												return null;
											}

											if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
												errorCode = 251;
												return null;
											}

											var jAuthParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
											if (jAuthParameter[button.AuthCodeParameter.Name] == null || jAuthParameter[button.AuthCodeParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.AuthCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											var jAuthValue = jAuthParameter[button.AuthCodeParameter.Name].ToString();
											if (string.IsNullOrEmpty(jAuthValue))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.AuthCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												AuthCodeButtonType = DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes.AuthCode,
												AuthCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
												{
													Name = button.AuthCodeParameter.Name,
													Value = jAuthValue
												}
											};
											break;
										default:
											break;
									}
								}
								break;
							case DomainModel.Whatsapp.HSMTemplateButtonsTypes.CallToAction:
								{
									switch (button.CallToActionButtonType.Value)
									{
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
											if (!type.Equals("url"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'url'", index);
												errorCode = 251;
												return null;
											}

											if (jButton["sub_type"] == null || jButton["sub_type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].sub_type is missing", index);
												errorCode = 251;
												return null;
											}

											var subType = jButton["sub_type"].ToString();

											switch (button.UrlButtonType.Value)
											{
												case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed:
													if (!subType.Equals("fixed"))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].sub_type must be 'fixed'", index);
														errorCode = 251;
														return null;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
														UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed
													};
													break;
												case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic:
													if (!subType.Equals("dynamic"))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].sub_type must be 'dynamic'", index);
														errorCode = 251;
														return null;
													}

													if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
														errorCode = 251;
														return null;
													}

													var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
													if (jParameter[button.UrlParameter.Name] == null || jParameter[button.UrlParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.UrlParameter.Name);
														errorCode = 251;
														return null;
													}

													var value = jParameter[button.UrlParameter.Name].ToString();
													if (string.IsNullOrEmpty(value))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.UrlParameter.Name);
														errorCode = 251;
														return null;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
														UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic,
														UrlParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
														{
															Name = button.UrlParameter.Name,
															Value = value
														}
													};
													break;
												default:
													break;
											}

											break;
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
											if (!type.Equals("offer"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'offer'", index);
												errorCode = 251;
												return null;
											}

											if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
												errorCode = 251;
												return null;
											}

											var jOfferParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
											if (jOfferParameter[button.OfferCodeParameter.Name] == null || jOfferParameter[button.OfferCodeParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.OfferCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											var jOfferValue = jOfferParameter[button.OfferCodeParameter.Name].ToString();
											if (string.IsNullOrEmpty(jOfferValue))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.OfferCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode,
												OfferCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
												{
													Name = button.OfferCodeParameter.Name,
													Value = jOfferValue
												}
											};
											break;
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call:
											if (!type.Equals("call"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'call'", index);
												errorCode = 251;
												return null;
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call
											};
											break;

										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow:

											if (button.FlowParameter != null)
											{
												var serviceSettings = service.Settings as WhatsappSettings;
												var screens = serviceSettings.FindFlowScreensData(button.FlowParameter.FlowID, button.FlowParameter.NavigateScreen);

												var flowParameter = new DomainModel.Whatsapp.HSMTemplateFlowParameters()
												{
													Name = button.FlowParameter.Name
												};

												flowParameter.ActionData = new ActionPayload();

												if (screens != null && screens.Length > 0)
												{
													var screenData = screens[0].Data;

													if (screenData != null && screenData.HasValues)
													{
														if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
															errorCode = 251;
															return null;
														}

														var jFlowParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
														if (jFlowParameter[button.FlowParameter.Name] == null || jFlowParameter[button.FlowParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.FlowParameter.Name);
															errorCode = 251;
															return null;
														}

														var jFlowValue = (JObject) jFlowParameter[button.FlowParameter.Name];
														flowParameter.ActionData.Data = jFlowValue;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow,
														FlowParameter = flowParameter,
													};
												}
											}

											break;
										default:
											break;
									}
								}
								break;
							case DomainModel.Whatsapp.HSMTemplateButtonsTypes.Mixed:

								if (!type.Equals("quick_reply") && !type.Equals("url") &&
									!type.Equals("offer") && !type.Equals("call"))
								{
									errorMessage = string.Format("HSM Template buttons[{0}].type is invalid", index);
									errorCode = 251;
									return null;
								}

								if (button.QuickReplyParameter != null)
								{

									if (!type.Equals("quick_reply"))
									{
										errorMessage = string.Format("HSM Template buttons[{0}].type must be 'quick_reply'", index);
										errorCode = 251;
										return null;
									}

									if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
										errorCode = 251;
										return null;
									}

									var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
									if (jParameter[button.QuickReplyParameter.Name] == null || jParameter[button.QuickReplyParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.QuickReplyParameter.Name);
										errorCode = 251;
										return null;
									}

									var value = jParameter[button.QuickReplyParameter.Name].ToString();
									if (string.IsNullOrEmpty(value))
									{
										errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.QuickReplyParameter.Name);
										errorCode = 251;
										return null;
									}

									sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
									{
										QuickReplyParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
										{
											Name = button.QuickReplyParameter.Name,
											Value = value
										}
									};
								}

								else
								{
									switch (button.CallToActionButtonType.Value)
									{
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url:
											if (!type.Equals("url"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'url'", index);
												errorCode = 251;
												return null;
											}

											if (jButton["sub_type"] == null || jButton["sub_type"].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].sub_type is missing", index);
												errorCode = 251;
												return null;
											}

											var subType = jButton["sub_type"].ToString();

											switch (button.UrlButtonType.Value)
											{
												case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed:
													if (!subType.Equals("fixed"))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].sub_type must be 'fixed'", index);
														errorCode = 251;
														return null;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
														UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Fixed
													};
													break;
												case DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic:
													if (!subType.Equals("dynamic"))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].sub_type must be 'dynamic'", index);
														errorCode = 251;
														return null;
													}

													if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
														errorCode = 251;
														return null;
													}

													var jParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
													if (jParameter[button.UrlParameter.Name] == null || jParameter[button.UrlParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.UrlParameter.Name);
														errorCode = 251;
														return null;
													}

													var value = jParameter[button.UrlParameter.Name].ToString();
													if (string.IsNullOrEmpty(value))
													{
														errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.UrlParameter.Name);
														errorCode = 251;
														return null;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Url,
														UrlButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes.Dynamic,
														UrlParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
														{
															Name = button.UrlParameter.Name,
															Value = value
														}
													};
													break;
												default:
													break;
											}

											break;
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode:
											if (!type.Equals("offer"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'offer'", index);
												errorCode = 251;
												return null;
											}

											if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
												errorCode = 251;
												return null;
											}

											var jOfferParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
											if (jOfferParameter[button.OfferCodeParameter.Name] == null || jOfferParameter[button.OfferCodeParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.String)
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.OfferCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											var jOfferValue = jOfferParameter[button.OfferCodeParameter.Name].ToString();
											if (string.IsNullOrEmpty(jOfferValue))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} must be a non empty string", index, button.OfferCodeParameter.Name);
												errorCode = 251;
												return null;
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.OfferCode,
												OfferCodeParameter = new DomainModel.Whatsapp.HSMTemplateParameter()
												{
													Name = button.OfferCodeParameter.Name,
													Value = jOfferValue
												}
											};
											break;
										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call:
											if (!type.Equals("call"))
											{
												errorMessage = string.Format("HSM Template buttons[{0}].type must be 'call'", index);
												errorCode = 251;
												return null;
											}

											sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
											{
												CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Call
											};
											break;

										case DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow:

											if (button.FlowParameter != null)
											{
												var serviceSettings = service.Settings as WhatsappSettings;
												var screens = serviceSettings.FindFlowScreensData(button.FlowParameter.FlowID, button.FlowParameter.NavigateScreen);

												var flowParameter = new DomainModel.Whatsapp.HSMTemplateFlowParameters()
												{
													Name = button.FlowParameter.Name
												};

												flowParameter.ActionData = new ActionPayload();

												if (screens != null && screens.Length > 0)
												{
													var screenData = screens[0].Data;

													if (screenData != null && screenData.HasValues)
													{
														if (jButton["parameter"] == null || jButton["parameter"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															errorMessage = string.Format("HSM Template buttons[{0}].parameter is missing", index);
															errorCode = 251;
															return null;
														}

														var jFlowParameter = (Newtonsoft.Json.Linq.JObject) jButton["parameter"];
														if (jFlowParameter[button.FlowParameter.Name] == null || jFlowParameter[button.FlowParameter.Name].Type != Newtonsoft.Json.Linq.JTokenType.Object)
														{
															errorMessage = string.Format("HSM Template buttons[{0}].parameter.{1} is missing", index, button.FlowParameter.Name);
															errorCode = 251;
															return null;
														}

														var jFlowValue = (JObject) jFlowParameter[button.FlowParameter.Name];
														flowParameter.ActionData.Data = jFlowValue;
													}

													sendDefinition.Buttons[index] = new DomainModel.Whatsapp.HSMTemplateButton()
													{
														CallToActionButtonType = DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes.Flow,
														FlowParameter = flowParameter,
													};
												}
											}

											break;
										default:
											break;
									}
								}

								break;
							default:
								break;
						}

						index++;
					}
				}

				sendParameters = new Dictionary<string, string>();
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateElementNameParameter] = sendDefinition.ElementName;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateNamespaceParameter] = sendDefinition.Namespace;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMTemplateLanguage] = sendDefinition.Language;
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMSendDefinitionParameter] = Newtonsoft.Json.JsonConvert.SerializeObject(sendDefinition);
				sendParameters[Social.WhatsApp.WhatsAppMessage.HSMParameter] = true.ToString();
			}

			bool extendedCase = false;
			if (jBody["extendedCase"] != null && jBody["extendedCase"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
			{
				extendedCase = jBody["extendedCase"].ToObject<bool>();
			}

			Newtonsoft.Json.Linq.JObject extendedCaseDataObject;
			var extendedCaseData = string.Empty;
			var extendedCaseOption = OptionsExtendedCaseData.NoneAction;

			if (extendedCase)
			{
				if (jBody["extendedCaseData"] == null || jBody["extendedCaseData"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
				{
					errorMessage = string.Format("Mising extended case data");
					errorCode = 251;
					return null;
				}
				else
				{
					extendedCaseDataObject = (Newtonsoft.Json.Linq.JObject) jBody["extendedCaseData"];
					extendedCaseData = Newtonsoft.Json.JsonConvert.SerializeObject(extendedCaseDataObject);
					if (!Core.System.Instance.Logic.IsExtendedCaseValid(extendedCaseData))
					{
						errorMessage = string.Format("Invalid extended case data");
						errorCode = 251;
						return null;
					}

				}

				if (jBody["extendedCaseOption"] == null || jBody["extendedCaseOption"].Type != Newtonsoft.Json.Linq.JTokenType.Integer)
				{
					errorMessage = string.Format("Mising extended case data option");
					errorCode = 251;
					return null;
				}
				else
				{
					try
					{
						extendedCaseOption = (OptionsExtendedCaseData) Enum.Parse(typeof(OptionsExtendedCaseData), jBody["extendedCaseOption"].ToObject<string>());
					}
					catch
					{
						errorMessage = string.Format("Invalid extended case option");
						errorCode = 251;
						return null;
					}
				}
			}

			var sendHSMIfCaseOpenAnyways = true;
			if (jBody["sendHSMIfCaseOpenAnyways"] != null && jBody["sendHSMIfCaseOpenAnyways"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
			{
				sendHSMIfCaseOpenAnyways = jBody["sendHSMIfCaseOpenAnyways"].ToObject<bool>();

				if ((source == ReplySources.Supervisor || source == ReplySources.Agent) &&
					!template.AllowToConfigureSendHSMIfCaseOpen)
				{
					sendHSMIfCaseOpenAnyways = true;
				}
			}
			var validateDoNotCallList = true;
			if (jBody["validateDoNotCallList"] != null && jBody["validateDoNotCallList"].Type == Newtonsoft.Json.Linq.JTokenType.Boolean)
			{
				validateDoNotCallList = jBody["validateDoNotCallList"].ToObject<bool>();
			}

			if (jBody["tags"] != null && jBody["tags"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
			{
				var jTags = (Newtonsoft.Json.Linq.JArray) jBody["tags"];
				foreach (var jTag in jTags)
				{
					DomainModel.Tag tag = null;
					try
					{
						if (jTag.Type == Newtonsoft.Json.Linq.JTokenType.String)
						{
							var tagKey = jTag.ToString();
							tag = DomainModel.Cache.Instance.FindItem<DomainModel.Tag>(t => !string.IsNullOrEmpty(t.Key) && t.Key.Equals(tagKey, StringComparison.InvariantCultureIgnoreCase));
						}
						else if (jTag.Type == Newtonsoft.Json.Linq.JTokenType.Integer)
						{
							var tagId = jTag.ToObject<int>();
							tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(tagId);
						}
					}
					catch { }

					if (tag != null)
					{
						if (tags == null)
							tags = new List<Tag>();
						tags.Add(tag);
					}
				}

				if (jBody["importantTag"] != null && jBody["importantTag"].Type == Newtonsoft.Json.Linq.JTokenType.Integer)
				{
					importantTag = jBody["importantTag"].ToObject<int?>();
				}
			}

			if (jBody["observations"] != null)
			{
				observations = jBody["observations"].ToString();
			}

			if (validateDoNotCallList)
			{
				var user = SocialUserDAO.GetOne(phoneNumber, DomainModel.SocialServiceTypes.WhatsApp, true);
				if (user == null)
				{
					var socialUserReference = new DomainModel.Settings.WhatsappSocialUserReference(phoneNumber);
					if (DomainModel.SystemSettings.Instance.DoNotCallSocialUsers.Contains(socialUserReference))
					{
						errorMessage = "It's not allowed to send hsm because the phone name is in do not call list";
						errorCode = 273;
						return null;
					}
				}
				else if (user.DoNotCall.HasValue && user.DoNotCall.Value)
				{
					errorMessage = "It's not allowed to send hsm because the phone name is in do not call list";
					errorCode = 273;
					return null;
				}
				//validamos tambien el perfil del usuario en caso de que tenga
				else if (user.Profile != null &&
						 user.Profile.DoNotCall.HasValue &&
						 user.Profile.DoNotCall.Value)
				{
					errorMessage = "It's not allowed to send hsm because the userProfile is in do not call list";
					errorCode = 273;
					return null;
				}
			}

			if (!sendHSMIfCaseOpenAnyways)
			{
				if (Core.System.Instance.QueueService.AreSocialUserMessagesEnqueuedOrAssigned(phoneNumber.ToString(), DomainModel.SocialServiceTypes.WhatsApp, service.ID, out DomainModel.Message _))
				{
					errorMessage = "It's not allowed to send hsm if there are open cases";
					errorCode = 272;
					return null;
				}
				else
				{
					if (DAL.CaseDAO.ExistsOpenForSocialUser(phoneNumber, DomainModel.SocialServiceTypes.WhatsApp, service.ID))
					{
						errorMessage = "It's not allowed to send hsm if there are open cases";
						errorCode = 272;
						return null;
					}
				}
			}

			var options = new Core.ActionOptions.SendWhatsappOptions()
			{
				PhoneNumber = phoneNumber,
				Service = service,
				Text = text,
				Attachments = attachments,
				Parameters = sendParameters,
				Source = source,
				Tags = tags,
				Template = template,
				SendDefinition = sendDefinition,
				SendInBackground = false,
				Observations = observations,
				ImportantTag = importantTag,
				ExtendedCaseDataOption = extendedCaseOption,
				ExtendedCaseData = extendedCaseData,
				BusinessData = businessData,
				BusinessDataOption = businessDataOption
			};

			return options;
		}

		/// <summary>
		/// Envía un mail cuando llega un mensaje por acción de filtro
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que desencadena el envío del mail</param>
		/// <param name="filter">El <see cref="DomainModel.Filter"/> que desencadenó el envío del mail</param>
		private void SendMail(DomainModel.Message message, DomainModel.Filter filter)
		{
			try
			{
				string to = filter.Actions.EmailAddresses;
				string cc = null;

				var filterSettings = DomainModel.SystemSettings.Instance.FilterEmailSettings;

				if (filterSettings.SendToAdministrators || filterSettings.SendToSupervisors)
				{
					IEnumerable<DomainModel.User> recipients = DomainModel.Cache.Instance.GetList<DomainModel.User>();
					recipients = recipients.Where(u => u.Enabled && !string.IsNullOrEmpty(u.Email) && ((filterSettings.SendToAdministrators && u.IsAdministrator && u.ID != 1) || (filterSettings.SendToSupervisors && u.IsSupervisor)));
					cc = string.Join(",", recipients.Select(a => a.Email));
				}

				if (!string.IsNullOrEmpty(filterSettings.Emails))
				{
					if (string.IsNullOrEmpty(cc))
						cc = filterSettings.Emails;
					else
						cc = string.Concat(cc, ",", filterSettings.Emails);
				}

				string body;
				string subject;
				string emailConnection = string.Empty;

				if (!string.IsNullOrEmpty(filter.Actions.EmailConnection))
					emailConnection = filter.Actions.EmailConnection;

				if (filter.Actions.OverrideSystemSettingsEmail && !string.IsNullOrEmpty(filter.Actions.EmailSubject) && !string.IsNullOrEmpty(filter.Actions.EmailTemplate))
				{
					subject = filter.Actions.EmailSubject;
					body = filter.Actions.EmailTemplate;
				}
				else
				{
					subject = filterSettings.Subject;
					body = filterSettings.Template;
				}

				subject = subject.Replace("@@USUARIO@@", message.PostedBy.ToString());
				subject = subject.Replace("@@SERVICIO@@", message.Service.Name);
				subject = subject.Replace("@@TIPO_DE_SERVICIO@@", message.SocialServiceType.ToString());

				body = body.Replace("@@FECHA@@", message.Date.ToString("dddd d 'de' MMMM 'del' yyyy", new global::System.Globalization.CultureInfo("es-AR")));
				body = body.Replace("@@USUARIO@@", message.PostedBy.ToString());
				body = body.Replace("@@SERVICIO@@", message.Service.Name);
				body = body.Replace("@@TIPO_DE_SERVICIO@@", message.SocialServiceType.ToString());
				body = body.Replace("@@TEXTO@@", message.Body);
				if (body.Contains("@@COORDENADAS@@"))
				{
					if (message.HasCoordinates)
					{
						var culture = new global::System.Globalization.CultureInfo("en-US");
						body = body.Replace("@@COORDENADAS@@", string.Format(culture, "<a href='https://www.google.com/maps?q={0},{1}'>{0}, {1}</a>", message.Coordinates.Latitude, message.Coordinates.Longitude));
					}
					else
					{
						body = body.Replace("@@COORDENADAS@@", string.Empty);
					}
				}

				if (body.Contains("@@CASO@@"))
				{
					try
					{
						body = body.Replace("@@CASO@@", Core.System.Instance.Logic.RenderMessageCase(message.Case));
					}
					catch (Exception ex)
					{
						Tracer.TraceError("No se pudo renderizar el caso: {0}", ex);
						body = body.Replace("@@CASO@@", string.Empty);
					}
				}

				if (message.HasAttach && message.Attachments != null && message.Attachments.Length > 0)
					DomainModel.SystemSettings.Instance.SendMailMessage(subject, to, cc, null, body, null, message.Attachments.Select(a => a.ConvertToMimePart()), emailConnection);
				else
					DomainModel.SystemSettings.Instance.SendMailMessage(subject, to, cc, null, body, emailConnection);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se enviaba el mail del mensaje {0}: {1}", message, ex);
			}
		}

		/// <summary>
		/// Envía un mail cuando se produce un error 
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que desencadena el envío del mail</param>
		/// <param name="filter"></param>
		public void SendMail(DomainModel.Message message, DomainModel.Filter filter, DomainModel.Filters.WebServiceInvokeResults results)
		{
			try
			{
				string to = filter.Actions.WebServiceActions.EmailErrorAddresses;
				string cc = null;

				var subject = filter.Actions.WebServiceActions.EmailErrorSubject;
				var body = filter.Actions.WebServiceActions.EmailErrorTemplate;

				subject = subject.Replace("@@USUARIO@@", message.PostedBy.ToString());
				subject = subject.Replace("@@SERVICIO@@", message.Service.Name);
				subject = subject.Replace("@@TIPO_DE_SERVICIO@@", message.SocialServiceType.ToString());

				body = body.Replace("@@FECHA@@", message.Date.ToString("dddd d 'de' MMMM 'del' yyyy", new global::System.Globalization.CultureInfo("es-AR")));
				body = body.Replace("@@USUARIO@@", message.PostedBy.ToString());
				body = body.Replace("@@SERVICIO@@", message.Service.Name);
				body = body.Replace("@@TIPO_DE_SERVICIO@@", message.SocialServiceType.ToString());
				body = body.Replace("@@TEXTO@@", message.Body);
				if (body.Contains("@@CASO@@"))
				{
					try
					{
						body = body.Replace("@@CASO@@", Core.System.Instance.Logic.RenderMessageCase(message.Case));
					}
					catch (Exception ex)
					{
						Tracer.TraceError("No se pudo renderizar el caso: {0}", ex);
						body = body.Replace("@@CASO@@", string.Empty);
					}
				}

				var error = results.Exception.ToString();
				error = error.Replace("\n", "<br />").Replace("\t", "&nbsp;&nbsp;");
				body = body.Replace("@@ERROR@@", error);

				DomainModel.SystemSettings.Instance.SendMailMessage(subject, to, cc, null, body, null);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se enviaba el mail del mensaje {0}: {1}", message, ex);
			}
		}

		/// <summary>
		/// Procesa los filtros para ver si el mensaje <paramref name="message"/> aplica a alguno de ellos
		/// </summary>
		/// <param name="filters">La lista de los filtros que hay por servicio</param>
		/// <param name="message">El <see cref="Message"/> a verificar</param>
		/// <param name="queue">La <see cref="SystemQueue"/> a la que pertenece el mensaje</param>
		/// <param name="discarded">Cuando retorna, devuelve si el mensaje fue descartado</param>
		/// <returns>true si el mensaje deberá encolarse; en caso contrario, false</returns>
		public async Task<bool> CheckFilters(DomainModel.Message message, IEnumerable<DomainModel.Filter> filters, SystemQueue queue)
		{
			var discarded = false;

			int filtersApplied = 0;
			bool autoReplied = false;

			foreach (var filter in filters)
			{
				if (!filter.Enabled)
					continue;

				if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWebServiceCallsInFilters && filter.Actions.InvokeWebService)
				{
					Tracer.TraceInfo("Se ignora el filtro {0} porque no hay licencia para invocar web services", filter.Name);
					continue;
				}

				if (!Licensing.LicenseManager.Instance.License.Configuration.AllowAssemblyCallsInFilters && filter.Actions.InvokeAssembly)
				{
					Tracer.TraceInfo("Se ignora el filtro {0} porque no hay licencia para invocar assemblies externos", filter.Name);
					continue;
				}

				try
				{
					if (!filter.MessageApplies(message))
					{
						Tracer.TraceInfo("El filtro {0} NO aplica al mensaje {1}", filter, message);
						continue;
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Ocurrió un error verificando si el filtro {0} aplica al mensaje {1}: {2}", filter, message, ex);
					continue;
				}

				Tracer.TraceInfo("El filtro {0} aplica al mensaje {1}", filter, message);

				SystemService systemService = System.Instance.ServicesServices.GetServiceById(message.Service.ID);

				if (filter.Actions.InvokeAssembly)
				{
					#region Procesamiento de acciones de filtro con invocación a assembly

					var actionType = Common.Encryption.Decrypt(filter.Actions.AssemblyAction, "filters");
					var type = Licensing.LicenseManager.Instance.License.Configuration.TypesForCallsInFilters.SingleOrDefault(t => t.FullName.Equals(actionType));
					if (type != null)
					{
						DomainModel.Filters.FilterActionInvokeResults results = null;

						if (message.AssemblyFilterResults.ContainsKey(filter.ID) && message.AssemblyFilterResults[filter.ID] != null)
						{
							results = message.AssemblyFilterResults[filter.ID];
						}
						else
						{
							try
							{
								var instance = (DomainModel.Filters.IFilterActions) Activator.CreateInstance(type);
								results = instance.Invoke(message);
							}
							catch (DomainModel.Filters.FilterActionAssemblyException ex)
							{
								Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
								results = new DomainModel.Filters.FilterActionInvokeResults(ex);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
								results = new DomainModel.Filters.FilterActionInvokeResults(ex);
							}
						}

						if (results != null)
						{
							if (results.Exception == null)
							{
								bool discards = false;

								if (results.Discard &&
									!discarded)
								{
									discards = true;
								}

								if (results.VIM &&
									!message.Important)
								{
									this.MarkAsImportant(message);
								}

								if (results.AutoReply &&
									!autoReplied)
								{
									await this.AutoReply(message, filter, results.AutoReplyText, !discarded);
									if (queue != null)
										queue.RealTimeInfo.SystemAutoReplyMessages++;
									if (systemService != null)
										systemService.RealTimeInfo.AutoRepliedMessages++;
									autoReplied = true;
								}

								if (discards)
								{
									Tracer.TraceVerb("El filtro {0} tiene configurado descartar. No se aplicarán más filtros", filter);
									var options = new ActionOptions.DiscardOptions()
									{
										Message = message,
										DiscardSource = DiscardSources.Filter,
										RemoveFromQueue = false
									};
									await this.DiscardAsync(options);

									if (queue != null)
										queue.RealTimeInfo.SystemDiscardedMessages++;
									message.Case.Discards++;
									if (systemService != null)
										systemService.RealTimeInfo.SystemDiscardedMessages++;
									discarded = true;
								}

								bool movesToAnotherQueue = false;
								if (!string.IsNullOrEmpty(results.QueueKey))
								{
									// TODO
									movesToAnotherQueue = true;
								}

								try
								{
									this.SaveFilter(message, filter);
								}
								catch (Exception ex)
								{
									Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1} porque no se pudo grabar en la base de datos: {2}", filter, message, ex);
								}

								filtersApplied++;

								if (movesToAnotherQueue || discarded)
								{
									break;
								}
							}
						}
					}

					#endregion
				}
				else if (filter.Actions.InvokeWebService)
				{
					#region Procesamiento de acciones de filtro con invocación a web service

					if (!message.Case.CanApplyFilter(filter))
					{
						Tracer.TraceInfo("No se puede aplicar el filtro {0} al mensaje {1} porque el caso tiene indicado que no se puede invocar más a este filtro", filter, message);
						continue;
					}

					DomainModel.Filters.WebServiceInvokeResults results = null;

					try
					{
						/*
						 * Puede ocurrir que la invocación al web service ya se haya hecho en el chequeo de cola de destino.
						 * En ese caso, buscamos el resultado de la invocación para evitar ir 2 veces al web service
						 */
						if (message.WebServiceFilterResults.ContainsKey(filter.ID) && message.WebServiceFilterResults[filter.ID] != null)
						{
							results = message.WebServiceFilterResults[filter.ID];
						}
						else
						{
							if (filter.Actions.WebServiceActions.RetryOnError)
							{
								try
								{
									results = filter.Actions.WebServiceActions.Invoke(message);
								}
								catch (Exception ex)
								{
									Tracer.TraceError("Ocurrió un error en el procesamiento del filtro {0} en el mensaje {1}: {2}. Se reintentará", filter, message, ex);
								}
							}

							try
							{
								if (results == null)
								{
									results = filter.Actions.WebServiceActions.Invoke(message);
								}
							}
							catch (DomainModel.Filters.FilterActionWebServiceException ex)
							{
								Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
								results = new DomainModel.Filters.WebServiceInvokeResults(filter.Actions.WebServiceActions.InvokeSettings, ex);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
								results = new DomainModel.Filters.WebServiceInvokeResults(filter.Actions.WebServiceActions.InvokeSettings, ex);
							}
						}

						if (results.Exception != null)
						{
							if (filter.Actions.WebServiceActions.SendEmailError)
							{
								SendMail(message, filter, results);
							}

							if (filter.Actions.WebServiceActions.AutoReplyOnError && !autoReplied)
							{
								await this.AutoReply(message, filter.Actions.WebServiceActions.AutoReplyTextOnError, !filter.Actions.WebServiceActions.Discard, ReplySources.Filter);
								if (queue != null)
									queue.RealTimeInfo.SystemAutoReplyMessages++;
								if (systemService != null)
									systemService.RealTimeInfo.AutoRepliedMessages++;
								autoReplied = true;
							}

							if (filter.Actions.WebServiceActions.Discard && !discarded)
							{
								Tracer.TraceVerb("El filtro {0} tiene configurado descartar cuando ocurre un error. No se aplicarán más filtros", filter);
								var options = new ActionOptions.DiscardOptions()
								{
									Message = message,
									DiscardSource = DiscardSources.Filter,
									RemoveFromQueue = false,
									CloseCase = filter.Actions.WebServiceActions.DiscardAndCloseCaseOnError
								};
								await this.DiscardAsync(options);

								if (queue != null)
									queue.RealTimeInfo.SystemDiscardedMessages++;
								message.Case.Discards++;
								if (systemService != null)
									systemService.RealTimeInfo.SystemDiscardedMessages++;
								discarded = true;
							}

							if (filter.Actions.WebServiceActions.StopInvokeThisFilterForCaseMessages)
							{
								if (message.Case.Parameters.ContainsKey(DomainModel.Case.FiltersToAvoidParameter))
								{
									message.Case.Parameters[DomainModel.Case.FiltersToAvoidParameter] = string.Format("{0},{1}", message.Case.Parameters[DomainModel.Case.FiltersToAvoidParameter], filter.ID);
								}
								else
								{
									message.Case.Parameters[DomainModel.Case.FiltersToAvoidParameter] = filter.ID.ToString();
								}

								DAL.CaseDAO.UpdateParameters(message.Case);
							}
						}
						else
						{
							bool discards = false;

							if (filter.Actions.WebServiceActions.Discard &&
								filter.Actions.WebServiceActions.DiscardConditions.Count > 0 &&
								!discarded)
							{
								for (int i = 0; i < filter.Actions.WebServiceActions.DiscardConditions.Count; i++)
								{
									var condition = filter.Actions.WebServiceActions.DiscardConditions[i];
									if (condition.Matches(results.Contents))
									{
										discards = true;
										break;
									}
								}
							}

							if (filter.Actions.WebServiceActions.VIM &&
								filter.Actions.WebServiceActions.VIMConditions.Count > 0 &&
								!message.Important)
							{
								for (int i = 0; i < filter.Actions.WebServiceActions.VIMConditions.Count; i++)
								{
									var condition = filter.Actions.WebServiceActions.VIMConditions[i];
									if (condition.Matches(results.Contents))
									{
										this.MarkAsImportant(message);
										break;
									}
								}
							}

							if (message.SocialServiceType != SocialServiceTypes.Chat &&
								filter.Actions.WebServiceActions.AutoReply &&
								filter.Actions.WebServiceActions.AutoReplyConditions.Count > 0 &&
								!autoReplied)
							{
								for (int i = 0; i < filter.Actions.WebServiceActions.AutoReplyConditions.Count; i++)
								{
									var condition = filter.Actions.WebServiceActions.AutoReplyConditions[i];
									if (condition.Matches(results.Contents))
									{
										await this.AutoReply(message, filter, results.Contents, condition, !discarded);
										if (queue != null)
											queue.RealTimeInfo.SystemAutoReplyMessages++;
										if (systemService != null)
											systemService.RealTimeInfo.AutoRepliedMessages++;
										autoReplied = true;
										break;
									}
								}
							}

							if (discards)
							{
								Tracer.TraceVerb("El filtro {0} tiene configurado descartar. No se aplicarán más filtros", filter);
								var options = new ActionOptions.DiscardOptions()
								{
									Message = message,
									DiscardSource = DiscardSources.Filter,
									RemoveFromQueue = false
								};
								await this.DiscardAsync(options);

								if (queue != null)
									queue.RealTimeInfo.SystemDiscardedMessages++;
								message.Case.Discards++;
								if (systemService != null)
									systemService.RealTimeInfo.SystemDiscardedMessages++;
								discarded = true;
							}

							bool movesToAnotherQueue = false;
							if (filter.Actions.WebServiceActions.MoveToQueue &&
								filter.Actions.WebServiceActions.MoveToQueueConditions.Count > 0)
							{
								for (int i = 0; i < filter.Actions.WebServiceActions.MoveToQueueConditions.Count; i++)
								{
									var condition = filter.Actions.WebServiceActions.MoveToQueueConditions[i];
									if (condition.Matches(results.Contents))
									{
										Tracer.TraceVerb("El filtro {0} tiene configurado mover a otra cola. No se aplicarán más filtros", filter);
										movesToAnotherQueue = true;
										break;
									}
								}
							}

							if (DomainModel.SystemSettings.Instance.ExtendedCasesFields != null &&
								DomainModel.SystemSettings.Instance.ExtendedCasesFields.Length > 0 &&
								filter.Actions.WebServiceActions.AddParametersToCase &&
								filter.Actions.WebServiceActions.AddParametersToCaseConditions.Count > 0)
							{
								Dictionary<string, string> extendedCaseFields;
								var changed = false;
								if (message.Case.Parameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter))
								{
									var currentExtendedCaseFieldsText = message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter];
									extendedCaseFields = Common.Conversions.ConvertStringToDictionary(currentExtendedCaseFieldsText);
								}
								else
								{
									extendedCaseFields = new Dictionary<string, string>();
								}

								for (int i = 0; i < filter.Actions.WebServiceActions.AddParametersToCaseConditions.Count; i++)
								{
									var condition = filter.Actions.WebServiceActions.AddParametersToCaseConditions[i];
									var value = condition.ExtractProperty(results.Contents);

									var extendedCaseField = DomainModel.SystemSettings.Instance.ExtendedCasesFields.FirstOrDefault(f => f.Name.Equals(condition.Value));
									if (extendedCaseField != null)
									{
										Tracer.TraceVerb("Se establecerá el valor {0} en el campo extendido {1} a partir del filtro {2} en la acción {3}", value, extendedCaseField.Name, filter.Name, i);
										extendedCaseFields[extendedCaseField.Name] = value;
										changed = true;
									}
									else
									{
										Tracer.TraceVerb("No se encontró el campo extendido {0} para el filtro {1} en la acción {2}", condition.Value, filter.Name, i);
									}
								}

								if (changed)
								{
									if (this.IsExtendedCaseValid(extendedCaseFields))
									{
										var extendedCaseFieldsText = Common.Conversions.ConvertDictionaryToString(extendedCaseFields);
										message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter] = extendedCaseFieldsText;
										DAL.CaseDAO.UpdateParameters(message.Case);
									}
									else
									{
										var extendedCaseFieldsText = Common.Conversions.ConvertDictionaryToString(extendedCaseFields);
										Tracer.TraceVerb("No se puede actualizar el valor de los campos extendidos del caso {0} porque el valor {1} es inválido", message.Case, extendedCaseFieldsText);
									}
								}
							}

							try
							{
								this.SaveFilter(message, filter);
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1} porque no se pudo grabar en la base de datos: {2}", filter, message, ex);
							}

							filtersApplied++;

							if (movesToAnotherQueue || discarded)
							{
								break;
							}
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
					}

					#endregion
				}
				else
				{
					#region Procesamiento de acciones de filtro normal

					try
					{
						this.SaveFilter(message, filter);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1} porque no se pudo grabar en la base de datos: {2}", filter, message, ex);
					}

					try
					{
						if (filter.Actions.Enqueue)
						{
							if (filter.Actions.VIM)
								this.MarkAsImportant(message);

							if (filter.Actions.SendEmail)
								this.SendMail(message, filter);
						}

						if (message.SocialServiceType != SocialServiceTypes.Chat && filter.Actions.AutoReply && !autoReplied)
						{
							await this.AutoReply(message, filter, filter.Actions.Enqueue, filter.Actions.AutoReplyParameters);
							if (queue != null)
								queue.RealTimeInfo.SystemAutoReplyMessages++;
							if (systemService != null)
								systemService.RealTimeInfo.AutoRepliedMessages++;
							autoReplied = true;
						}

						if (filter.Actions.AddTags && filter.Actions.Tags != null && filter.Actions.Tags.Length > 0)
						{
							this.ApplyTags(message.Case, filter.Actions.Tags, TaggedBySources.Filter);
						}

						if (message.SocialServiceType != SocialServiceTypes.Chat && filter.Actions.Discard && !discarded)
						{
							var options = new ActionOptions.DiscardOptions()
							{
								Message = message,
								DiscardSource = DiscardSources.Filter,
								AddToBlackList = filter.Actions.DiscardAndAddToBlackList,
								RemoveFromQueue = false
							};

							await this.DiscardAsync(options);
							if (queue != null)
								queue.RealTimeInfo.SystemDiscardedMessages++;
							message.Case.Discards++;

							Tracer.TraceVerb("El filtro {0} descartó el mensaje {1}", filter, message);

							if (systemService != null)
								systemService.RealTimeInfo.SystemDiscardedMessages++;

							if (!Licensing.LicenseManager.Instance.License.Configuration.WorkAsGateway)
							{
								if (filter.Actions.DiscardAndDiscardEnqueuedMessages)
								{
									try
									{
										if (Core.System.Instance.QueueService.AreCaseMessagesEnqueued(message.Case.ID, out DomainModel.Message enqueuedMessage, out SystemQueue q))
										{
											Tracer.TraceVerb("El mensaje {0} pertenece al mismo caso [{2}] que el mensaje {1} y se lo debe descartar", enqueuedMessage, message, enqueuedMessage.Case.ID);
											options = new ActionOptions.DiscardOptions()
											{
												Message = enqueuedMessage,
												DiscardSource = DiscardSources.Filter,
												RemoveFromQueue = false
											};
											await this.DiscardAsync(options);

											q.Discard(enqueuedMessage, null);

											if (q != null)
												q.RealTimeInfo.SystemDiscardedMessages++;
											message.Case.Discards++;
											if (systemService != null)
												systemService.RealTimeInfo.SystemDiscardedMessages++;

											Tracer.TraceVerb("El mensaje {0} fue descartado", enqueuedMessage);
										}
									}
									catch { }
								}

								if (filter.Actions.DiscardAndCloseCase)
								{
									if (Core.System.Instance.QueueService.IsAnyAgentWorkingWithCase(message.Case))
									{
										Tracer.TraceVerb("El filtro {0} tiene configurado cerrar el caso pero hay un agente trabajando con un mensaje del caso", filter);
									}
									else
									{
										Tracer.TraceVerb("El filtro {0} cerró el caso del mensaje {1}", filter, message);
										await this.CloseCaseAsync(new ActionOptions.CloseCaseOptions()
										{
											Case = message.Case,
											ClosedBy = DomainModel.CaseClosingResponsibles.Filter,
											Person = null,
											Interval = Interval.Null,
											StoreInDatabase = true,
											Queue = null,
											Message = message
										});
									}
								}
							}

							discarded = true;
						}

						if (filter.Actions.Notify)
						{
							try
							{
								var notif = new DomainModel.Notifications.MessageFiltered();
								notif.NotifiedUser = null;
								notif.Parameters["ID"] = message.ID.ToString();
								notif.Parameters["SocialServiceType"] = ((short) message.SocialServiceType).ToString();
								notif.Parameters["Body"] = message.GetPlainText();
								notif.Parameters["FilterName"] = filter.Name;
								if (filter.Actions.NotifySupervisors == null)
								{
									DAL.NotificationDAO.Insert(notif);
									Core.System.Instance.RealTimeService.NotifySupervisors(notif, null);
								}
								else
								{
									string[] supervisorIDs = filter.Actions.NotifySupervisors.Split(',');
									List<int> ids = new List<int>();
									foreach (var id in supervisorIDs)
									{
										notif.NotifiedUser = Cache.Instance.GetItem<DomainModel.User>(int.Parse(id));
										DAL.NotificationDAO.Insert(notif);
										ids.Add(int.Parse(id));
									}
									Core.System.Instance.RealTimeService.NotifySupervisors(notif, ids);
								}
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Falló el envío de la notificación en RT: {0}", ex);
							}
						}

						if (filter.Actions.AddParametersToCase && filter.Actions.ParametersForCase != null && filter.Actions.ParametersForCase.Count > 0)
						{
							if (message.Case.Parameters == null)
								message.Case.Parameters = new Dictionary<string, string>();

							foreach (var parameter in filter.Actions.ParametersForCase)
							{
								string currentValue = null;
								if (message.Case.Parameters.ContainsKey(parameter.Key))
									currentValue = message.Case.Parameters[parameter.Key];

								string value = parameter.Value;
								if (value.IndexOf("@@VALOR_ANTERIOR@@") >= 0)
								{
									string valueToReplace = string.Empty;
									if (currentValue != null)
									{
										if (value.StartsWith("@@VALOR_ANTERIOR@@"))
											valueToReplace = string.Concat(currentValue, ",");
										else if (value.StartsWith("@@VALOR_ANTERIOR@@"))
											valueToReplace = string.Concat(",", currentValue);
										else
											valueToReplace = currentValue;
									}

									value = value.Replace("@@VALOR_ANTERIOR@@", valueToReplace);
								}

								if (value.IndexOf("@@TEXTO_MENSAJE@@") >= 0)
									value = value.Replace("@@TEXTO_MENSAJE@@", message.GetPlainText());

								message.Case.Parameters[parameter.Key] = value;
							}

							Tracer.TraceVerb("El filtro {0} tiene configurado aplicar parámetros al caso {1}", filter, message.Case.ID);
							DAL.CaseDAO.UpdateParameters(message.Case);
						}

						if (filter.Actions.AlertAgents && !string.IsNullOrEmpty(filter.Actions.AlertMessage))
						{
							string alertMessage = filter.Actions.AlertMessage;
							if (alertMessage.IndexOf("@@USUARIO@@") >= 0)
								alertMessage = alertMessage.Replace("@@USUARIO@@", message.PostedBy.ToString());

							if (alertMessage.IndexOf("@@SERVICIO@@") >= 0)
								alertMessage = alertMessage.Replace("@@SERVICIO@@", message.Service.Name);

							if (alertMessage.IndexOf("@@NOMBRE_FILTRO@@") >= 0)
								alertMessage = alertMessage.Replace("@@NOMBRE_FILTRO@@", filter.Name);

							message.Parameters[DomainModel.Message.AlertMessageParameter] = alertMessage;

							Tracer.TraceVerb("El filtro {0} tiene configurado aplicar el mensaje de alerta '{1}' a los agentes", filter, alertMessage);
							DAL.MessageDAO.UpdateParameters(message.ID, message.Parameters);
						}

						filtersApplied++;

						if (filter.Actions.AssignToQueue)
						{
							Tracer.TraceVerb("El filtro {0} tiene configurado mover a otra cola. No se aplicarán más filtros", filter);
							break;
						}

						if (filter.Actions.StopProcessingFilters)
						{
							Tracer.TraceVerb("El filtro {0} tiene configurado dejar de procesar otros filtros. No se aplicarán más filtros", filter);
							break;
						}

						Tracer.TraceVerb("Finalizó el procesamiento del filtro {0} en el mensaje {1}", filter, message);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló el procesamiento del filtro {0} en el mensaje {1}: {2}", filter, message, ex);
					}

					#endregion
				}
			}

			if (filtersApplied == 0)
				Tracer.TraceInfo("No aplica ningún filtro al mensaje {0}", message);
			else
				Tracer.TraceInfo("Finalizó el procesamiento de filtros del mensaje {0} y se aplicaron {1} filtros", message, filtersApplied);

			return discarded;
		}

		/// <summary>
		/// Se asegura que la instancia del servicio de redes sociales esté creada. Si no lo está, se crea
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> del cual se creará la instancia de servicio de redes sociales</param>
		public void EnsureServiceInstance(DomainModel.Service service)
		{
#pragma warning disable CS0618
#pragma warning disable CS0612
			if (service.Status == null)
			{
				var status = DAL.ServiceDAO.GetStatus<Social.Business.SocialServiceStatus>(service.ID);
				if (status == null)
					status = new SocialServiceStatus();

				service.Status = status.GetStatus();
				service.ServiceStatus = status;
			}

			if (service.ServiceStatus == null)
			{
				service.ServiceStatus = new SocialServiceStatus(service.Status);
			}

			var serviceStatus = service.ServiceStatus;

			var serviceConfiguration = service.ServiceConfiguration;
			if (serviceConfiguration == null)
			{
				switch (service.Type)
				{
					case ServiceTypes.Twitter:
						serviceConfiguration = new SocialServices.Twitter.TwitterServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Facebook:
						serviceConfiguration = new SocialServices.Facebook.FacebookServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.TwitterSearches:
						serviceConfiguration = new SocialServices.Twitter.TwitterSearchServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Chat:
						serviceConfiguration = new SocialServices.Chat.ChatServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Mail:
						serviceConfiguration = new SocialServices.Mail.MailServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.WhatsApp:
						serviceConfiguration = new SocialServices.WhatsApp.WhatsAppServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.SMS:
						serviceConfiguration = new SocialServices.SMS.SMSServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Telegram:
						serviceConfiguration = new SocialServices.Telegram.TelegramServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.FacebookRt:
						serviceConfiguration = new SocialServices.Facebook.FacebookServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Instagram:
						serviceConfiguration = new SocialServices.Instagram.InstagramServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.LinkedIn:
						serviceConfiguration = new SocialServices.LinkedIn.LinkedInServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.Skype:
						serviceConfiguration = new SocialServices.Skype.SkypeServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.FacebookMessenger:
						serviceConfiguration = new SocialServices.Facebook.FacebookMessengerServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.TwitterRt:
						serviceConfiguration = new SocialServices.Twitter.TwitterServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.MercadoLibre:
						serviceConfiguration = new SocialServices.MercadoLibre.MercadoLibreServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.YouTube:
						serviceConfiguration = new SocialServices.YouTube.YouTubeServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.GooglePlay:
						serviceConfiguration = new SocialServices.GooglePlay.GooglePlayServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.GoogleRBM:
						serviceConfiguration = new SocialServices.GoogleRBM.GoogleRBMServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.IntegrationChat:
						serviceConfiguration = new SocialServices.Chat.IntegrationChatServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.AppleMessaging:
						serviceConfiguration = new SocialServices.AppleMessaging.AppleMessagingServiceConfiguration(service.Configuration);
						break;
					case ServiceTypes.GoogleMyBusiness:
						serviceConfiguration = new SocialServices.GoogleBusiness.GoogleBusinessServiceConfiguration(service.Configuration);
						break;
					default:
						Tracer.TraceError("EnsureServiceInstance: No se encontro el ServiceType");
						break;
				}

				service.ServiceConfiguration = serviceConfiguration;
			}

			if (serviceConfiguration != null)
			{
				var socialService = service.SocialService;
				if (socialService == null)
				{
					if (service.SocialServiceClassType == null)
					{
						switch (service.Type)
						{
							case ServiceTypes.Twitter:
								socialService = new SocialServices.Twitter.TwitterSocialService();
								break;
							case ServiceTypes.Facebook:
								socialService = new SocialServices.Facebook.FacebookSocialService();
								break;
							case ServiceTypes.TwitterSearches:
								socialService = new SocialServices.Twitter.TwitterSearchSocialService();
								break;
							case ServiceTypes.Mail:
								socialService = new SocialServices.Mail.MailService();
								break;
							case ServiceTypes.Chat:
								socialService = new SocialServices.Chat.ChatService();
								break;
							case ServiceTypes.WhatsApp:
								socialService = new SocialServices.WhatsApp.WhatsAppService();
								break;
							case ServiceTypes.SMS:
								socialService = new SocialServices.SMS.SMSSocialService();
								break;
							case ServiceTypes.Telegram:
								socialService = new SocialServices.Telegram.TelegramSocialService();
								break;
							case ServiceTypes.FacebookRt:
								socialService = new SocialServices.Facebook.FacebookRTSocialService();
								break;
							case ServiceTypes.Instagram:
								socialService = new SocialServices.Instagram.InstagramSocialService();
								break;
							case ServiceTypes.LinkedIn:
								socialService = new SocialServices.LinkedIn.LinkedInSocialService();
								break;
							case ServiceTypes.Skype:
								socialService = new SocialServices.Skype.SkypeSocialService();
								break;
							case ServiceTypes.FacebookMessenger:
								socialService = new SocialServices.Facebook.FacebookMessengerSocialService();
								break;
							case ServiceTypes.TwitterRt:
								socialService = new SocialServices.Twitter.TwitterRTSocialService();
								break;
							case ServiceTypes.MercadoLibre:
								socialService = new SocialServices.MercadoLibre.MercadoLibreSocialService();
								break;
							case ServiceTypes.YouTube:
								socialService = new SocialServices.YouTube.YouTubeSocialService();
								break;
							case ServiceTypes.GooglePlay:
								socialService = new SocialServices.GooglePlay.GooglePlaySocialService();
								break;
							case ServiceTypes.GoogleRBM:
								socialService = new SocialServices.GoogleRBM.GoogleRBMSocialService();
								break;
							case ServiceTypes.IntegrationChat:
								socialService = new SocialServices.Chat.IntegrationChatService();
								break;
							case ServiceTypes.AppleMessaging:
								socialService = new SocialServices.AppleMessaging.AppleMessagingService();
								break;
							case ServiceTypes.GoogleMyBusiness:
								socialService = new SocialServices.GoogleBusiness.GoogleBusinessSocialService();
								break;
							default:
								Tracer.TraceError("EnsureServiceInstance: No se encontro el ServiceType");

								break;
						}
					}
					else
					{
						socialService = (DomainModel.ISocialService) Activator.CreateInstance(service.SocialServiceClassType);
					}

					if (socialService != null)
					{
						socialService.Initialize(service, serviceConfiguration, serviceStatus, null, null);

						service.SocialService = socialService;
					}

					if (socialService != null &&
						Licensing.LicenseManager.Instance.License.Configuration.SendToServiceBusInsteadOfDirectSend &&
						socialService.PublishToServiceBus == null)
						socialService.PublishToServiceBus = PublishMessage;
				}
			}
#pragma warning restore CS0612
#pragma warning restore CS0618
		}

		/// <summary>
		/// Responde un mensaje automáticamente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> del mensaje que se está autorespondiendo</param>
		/// <param name="text">El texto de la respuesta</param>
		/// <remarks>
		/// Este método es utilizado por el sistema para responder un mensaje de forma automática por Service Level
		/// </remarks>
		public async Task AutoReply(DomainModel.Message message, string text, bool leaveUnassigned, ReplySources replySource)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (string.IsNullOrEmpty(text))
				throw new ArgumentException("El texto del mensaje no puede ser null o vacío", nameof(text));

			if (message.SocialServiceType == DomainModel.SocialServiceTypes.Twitter && text.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit && !message.IsDirectMessage)
				throw new ArgumentOutOfRangeException(nameof(text), string.Format("El texto no puede superar los {0} caracteres", DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));

			if (message.SocialServiceType == SocialServiceTypes.WhatsApp)
			{
				var settings = message.Service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				if (settings != null && settings.IntegrationType != 1)
				{
					if (DateTime.Now.Subtract(message.Date).TotalMinutes >= DomainModel.SystemSettings.Instance.Whatsapp.MaxMinutesToAnswerMessages)
					{
						throw new InvalidOperationException("El mensaje de whatsapp fue envíado hace más de 24 horas. No se puede responder");
					}
				}
			}

			Tracer.TraceInfo("El sistema está auto respondiendo el mensaje {0}", message);

			try
			{
				DateTime now = DateTime.Now;

				text = GetReplyTextForMessage(text, message);

				long? insertedMessageID, insertedAssociatedMessageID;
				MessageDAO.Reply(message, null, text, leaveUnassigned, null, false, null, null, null, null, out insertedMessageID, out insertedAssociatedMessageID, false, replySource, DateTime.Now);

				message.ReplySource = replySource;

				if (message.Case != null)
				{
					message.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					if (insertedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageID.Value.ToString();
					else if (insertedAssociatedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedAssociatedMessageID.Value.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) message.SocialServiceType).ToString();

					if (message.IsDirectMessage)
					{
						message.Case.Parameters[DomainModel.Case.HasPrivateAnswerParameter] = true.ToString();
					}

					if (replySource == ReplySources.ServiceLevel)
						message.Case.Parameters[DomainModel.Case.HasReplyBySLParameter] = true.ToString();

					DAL.CaseDAO.UpdateParameters(message.Case);
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;
				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.RepliedMessages = 1;
				info.AutoRepliedMessages = 1;
				info.MessagesRepliedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
				info.MessagesRepliedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.RepliedMessages = 1;
				infoService.AutoRepliedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (insertedMessageID != null)
					await Reply(insertedMessageID.Value, message.Case);
				if (insertedAssociatedMessageID != null)
					await Reply(insertedAssociatedMessageID.Value, message.Case);

				if (!leaveUnassigned)
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(message);
					}
				}

				Tracer.TraceInfo("El sistema auto respondió el mensaje {0}", message);
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló cuando se auto respondía el mensaje {0}: {{0}}", message), ex);
				throw;
			}
		}

		/// <summary>
		/// Responde un mensaje automáticamente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> del mensaje que se está autorespondiendo</param>
		/// <param name="filter">El <see cref="DomainModel.Filter"/> que inició la respuesta automática</param>
		/// <param name="leaveUnassigned"><code>true</code> para indicar si el mensaje debe permanecer en estado No Asignado o <code>false</code>
		/// para indicar que debe quedar en Respodido</param>
		/// <param name="parameters">Una colección de parámetros que se agregarán a la respuesta</param>
		/// <remarks>
		/// Este método es utilizado por el sistema para responder un mensaje de forma automática
		/// </remarks>
		public async Task AutoReply(DomainModel.Message message, DomainModel.Filter filter, bool leaveUnassigned, Dictionary<string, string> parameters)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (filter == null || filter.Actions == null)
				throw new ArgumentNullException(nameof(filter), "El filtro no puede ser nulo");

			if (string.IsNullOrEmpty(filter.Actions.Text))
				throw new ArgumentException("Debe especificar un texto para la respuesta", "text");

			string text = filter.Actions.Text;

			if (message.SocialServiceType == DomainModel.SocialServiceTypes.Twitter && text.Length > DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit && !message.IsDirectMessage)
				throw new ArgumentOutOfRangeException(nameof(text), string.Format("El texto no puede superar los {0} caracteres", DomainModel.SystemSettings.Instance.Twitter.StatusTextCharacterLimit));

			Tracer.TraceInfo("El sistema está auto respondiendo el mensaje {0}", message);

			try
			{
				DateTime now = DateTime.Now;

				string privateText = null;
				text = GetReplyTextForMessage(filter.Actions.Text, message);

				if (message.SocialServiceType == SocialServiceTypes.Facebook || message.SocialServiceType == SocialServiceTypes.Twitter)
				{
					if (filter.Actions.EditPrivateMessage)
					{
						if (message.IsDirectMessage)
							text = GetReplyTextForMessage(filter.Actions.PrivateText, message);
						else if (filter.Actions.SendBothReplies)
							privateText = GetReplyTextForMessage(filter.Actions.PrivateText, message);
					}
					else if (filter.Actions.SendBothReplies)
					{
						privateText = text;
					}

					if (message.SocialServiceType == SocialServiceTypes.Twitter)
					{
						if (!message.IsDirectMessage)
						{
							if (!filter.Actions.SendBothReplies &&
								filter.Actions.AutoReplyTwitterDeepLinkWelcomeMessage &&
								message.Service.Parameters.ContainsKey(Social.SocialServices.Twitter.TwitterSocialService.WelcomeMessageIDParameter))
							{
								var configuration = new Social.SocialServices.Twitter.TwitterServiceConfiguration(message.Service.Configuration);
								text += string.Format(" https://twitter.com/messages/compose?recipient_id={0}&welcome_message_id={1}", configuration.UserId, message.Service.Parameters[Social.SocialServices.Twitter.TwitterSocialService.WelcomeMessageIDParameter]);
							}
						}
						else
						{
							try
							{
								var jMessage = Newtonsoft.Json.Linq.JObject.Parse(text);
								if (jMessage["text"] != null)
									text = jMessage["text"].ToString();
								else
									text = string.Empty;

								if (parameters == null)
									parameters = new Dictionary<string, string>();
								parameters[Social.Twitter.TwitterMessage.ReplyParameter] = jMessage.ToString();
							}
							catch { }
						}
					}
				}
				else if (message.SocialServiceType == SocialServiceTypes.FacebookMessenger)
				{
					try
					{
						var jMessage = Newtonsoft.Json.Linq.JToken.Parse(text);
						if (jMessage.Type == JTokenType.Object && jMessage["text"] != null)
							text = jMessage["text"].ToString();
						else
							text = string.Empty;

						if (parameters == null)
							parameters = new Dictionary<string, string>();
						parameters[Social.Facebook.MessengerMessage.ReplyParameter] = jMessage.ToString();
					}
					catch { }
				}

				DomainModel.Attachment[] attachments = null;
				if (filter.Actions.Attachment != null && message.Service.Settings.AllowToSendAttachments)
				{
					DomainModel.Attachment attachment = null;

					try
					{
						if (filter.Actions.Attachment.Type == DomainModel.FilterActions.AutoReplyAttachment.AttachmentType.File)
						{
							attachment = new DomainModel.Attachment(0, 1);

							var data = (Newtonsoft.Json.Linq.JObject) Newtonsoft.Json.JsonConvert.DeserializeObject(filter.Actions.Attachment.Data);
							var file = (Newtonsoft.Json.Linq.JObject) data["File"];
							attachment.FileName = file["Name"].ToString();
							if (file["Size"] != null)
								attachment.FileSize = Convert.ToInt32(((Newtonsoft.Json.Linq.JValue) file["Size"]).Value);
							if (file["Type"] != null)
								attachment.MimeType = file["Type"].ToString();

							var filename = Path.Combine(DomainModel.SystemSettings.Instance.AttachmentsRoute, "__FilterAttachments", string.Format("{0}{1}", filter.ID, Path.GetExtension(file["Name"].ToString())));
							attachment.Data = File.ReadAllBytes(filename);
						}
						else if (filter.Actions.Attachment.Type == DomainModel.FilterActions.AutoReplyAttachment.AttachmentType.Url)
						{
							attachment = new DomainModel.Attachment(0, 1);

							var url = filter.Actions.Attachment.Data;
							url = url.Replace("@@USUARIO@@", message.PostedBy.ID.ToString());
							url = url.Replace("@@CODIGO_CLIENTE@@", message.PostedBy.BusinessData ?? string.Empty);

							HttpWebRequest request = HttpWebRequest.CreateHttp(url);
							using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
							{
								attachment.MimeType = response.ContentType;
								using (Stream sr = response.GetResponseStream())
								using (MemoryStream ms = new MemoryStream())
								{
									byte[] buffer = new byte[1024];
									int read = sr.Read(buffer, 0, 1024);
									while (read > 0)
									{
										ms.Write(buffer, 0, read);
										read = sr.Read(buffer, 0, 1024);
									}
									attachment.Data = ms.ToArray();
									attachment.FileSize = attachment.Data.Length;
								}

								var contentDisposition = response.Headers["Content-Disposition"];
								if (!string.IsNullOrEmpty(contentDisposition))
								{
									if (contentDisposition.StartsWith("attachment", StringComparison.InvariantCultureIgnoreCase))
									{
										try
										{
											contentDisposition = contentDisposition.Substring(contentDisposition.IndexOf("filename=", 0, StringComparison.InvariantCultureIgnoreCase) + 9);
											contentDisposition = contentDisposition.Replace("\"", string.Empty);
											attachment.FileName = contentDisposition;
										}
										catch { }
									}
								}

								if (string.IsNullOrEmpty(attachment.FileName))
									attachment.FileName = "attachment";
							}
						}

						if (attachment != null)
							attachments = new DomainModel.Attachment[] { attachment };
					}
					catch (Exception ex)
					{
						Tracer.TraceError("No se pudo obtener y generar el archivo adjunto para mandar al mensaje {0} con el filtro {1}: {2}", message, filter.Name, ex);
					}
				}

				if (message.SocialServiceType == SocialServiceTypes.Mail && filter.Actions.MailParameters != null)
				{
					if (parameters == null)
					{
						parameters = filter.Actions.MailParameters;
					}
					else
					{
						foreach (var item in filter.Actions.MailParameters)
						{
							parameters[item.Key] = item.Value;
						}
					}
				}

				long? insertedMessageID, insertedAssociatedMessageID;
				MessageDAO.Reply(message, null, text, leaveUnassigned, privateText, false, attachments, parameters, filter.Actions.Coordinates, null, out insertedMessageID, out insertedAssociatedMessageID, false, ReplySources.Filter, DateTime.Now);
				message.ReplySource = ReplySources.Filter;

				if (message.Case != null)
				{
					message.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					if (insertedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageID.Value.ToString();
					else if (insertedAssociatedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedAssociatedMessageID.Value.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) message.SocialServiceType).ToString();

					if (message.IsDirectMessage ||
						(!message.IsDirectMessage && !string.IsNullOrEmpty(privateText)))
					{
						message.Case.Parameters[DomainModel.Case.HasPrivateAnswerParameter] = true.ToString();
					}

					DAL.CaseDAO.UpdateParameters(message.Case);
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;
				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.RepliedMessages = 1;
				info.AutoRepliedMessages = 1;
				info.MessagesRepliedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
				info.MessagesRepliedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.RepliedMessages = 1;
				infoService.AutoRepliedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (insertedMessageID != null)
					await Reply(insertedMessageID.Value, message.Case);
				if (insertedAssociatedMessageID != null)
					await Reply(insertedAssociatedMessageID.Value, message.Case);

				if (!leaveUnassigned)
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(message);
					}
				}

				Tracer.TraceInfo("El sistema auto respondió el mensaje {0}", message);
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló cuando se auto respondía el mensaje {0}: {{0}}", message), ex);
				throw;
			}
		}

		/// <summary>
		/// Responde un mensaje automáticamente a partir de la obtención de una invocación de web service
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> del mensaje que se está autorespondiendo</param>
		/// <param name="filter">El <see cref="DomainModel.Filter"/> que inició la respuesta automática</param>
		/// <param name="jResult">El <see cref="Newtonsoft.Json.Linq.JObject"/> con los resultados de la invocación al web service</param>
		/// <param name="condition">El <see cref="DomainModel.Filters.WebServiceActionEvaluatorAutoReply"/> que se cumplió para realizar la respuesta automática</param>
		/// <param name="leaveUnassigned"><code>true</code> para indicar si el mensaje debe permanecer en estado No Asignado o <code>false</code>
		/// para indicar que debe quedar en Respodido</param>
		/// <remarks>
		/// Este método es utilizado por el sistema para responder un mensaje de forma automática
		/// </remarks>
		internal async Task AutoReply(DomainModel.Message message, DomainModel.Filter filter, Newtonsoft.Json.Linq.JObject jResult, DomainModel.Filters.WebServiceActionEvaluatorAutoReply condition, bool leaveUnassigned)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (filter == null || filter.Actions == null)
				throw new ArgumentNullException(nameof(filter), "El filtro no puede ser nulo");

			if (!filter.Actions.InvokeWebService)
				throw new ArgumentNullException(nameof(filter), "El filtro debe invocar a web services");

			Tracer.TraceInfo("El sistema está auto respondiendo el mensaje {0} luego de aplicar el filtro con llamada a web services", message);

			string text = condition.ReplyText;

			if (text.Contains("@@TEXTO@@"))
			{
				try
				{
					var jToken = jResult.SelectToken(condition.JsonPathTextKey);
					if (jToken != null)
					{
						if (jToken.Type == JTokenType.String ||
							jToken.Type == JTokenType.Integer ||
							jToken.Type == JTokenType.Float ||
							jToken.Type == JTokenType.Date)
						{
							text = text.Replace("@@TEXTO@@", jToken.ToString());
						}
						else if (jToken.Type == JTokenType.Boolean)
						{
							text = text.Replace("@@TEXTO@@", jToken.ToObject<bool>() ? "Si" : "No");
						}
						else
						{
							text = text.Replace("@@TEXTO@@", jToken.ToString());
						}
					}
					else
					{
						text = text.Replace("@@TEXTO@@", string.Empty);
					}
				}
				catch
				{
					text = text.Replace("@@TEXTO@@", string.Empty);
				}
			}

			await AutoReply(message, filter, text, leaveUnassigned);
		}

		/// <summary>
		/// Responde un mensaje automáticamente a partir de la obtención de una invocación de web service
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> del mensaje que se está autorespondiendo</param>
		/// <param name="filter">El <see cref="DomainModel.Filter"/> que inició la respuesta automática</param>
		/// <param name="jResult">El <see cref="Newtonsoft.Json.Linq.JObject"/> con los resultados de la invocación al web service</param>
		/// <param name="condition">El <see cref="DomainModel.Filters.WebServiceActionEvaluatorAutoReply"/> que se cumplió para realizar la respuesta automática</param>
		/// <param name="leaveUnassigned"><code>true</code> para indicar si el mensaje debe permanecer en estado No Asignado o <code>false</code>
		/// para indicar que debe quedar en Respodido</param>
		/// <remarks>
		/// Este método es utilizado por el sistema para responder un mensaje de forma automática
		/// </remarks>
		internal async Task AutoReply(DomainModel.Message message, DomainModel.Filter filter, string text, bool leaveUnassigned)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (filter == null || filter.Actions == null)
				throw new ArgumentNullException(nameof(filter), "El filtro no puede ser nulo");

			Tracer.TraceInfo("El sistema está auto respondiendo el mensaje {0} luego de aplicar el filtro {1}", message, filter.Name);

			try
			{
				DateTime now = DateTime.Now;
				Dictionary<string, string> parameters = null;

				if (message.SocialServiceType == SocialServiceTypes.Facebook || message.SocialServiceType == SocialServiceTypes.Twitter)
				{
					if (message.SocialServiceType == SocialServiceTypes.Twitter)
					{
						if (message.IsDirectMessage)
						{
							try
							{
								var jMessage = Newtonsoft.Json.Linq.JToken.Parse(text);
								if (jMessage.Type == JTokenType.Object && jMessage["text"] != null)
									text = jMessage["text"].ToString();
								else
									text = string.Empty;

								if (parameters == null)
									parameters = new Dictionary<string, string>();
								parameters[Social.Twitter.TwitterMessage.ReplyParameter] = jMessage.ToString();
							}
							catch { }
						}
					}
				}
				else if (message.SocialServiceType == SocialServiceTypes.FacebookMessenger)
				{
					try
					{
						var jMessage = Newtonsoft.Json.Linq.JToken.Parse(text);
						if (jMessage.Type == JTokenType.Object && jMessage["text"] != null)
							text = jMessage["text"].ToString();
						else
							text = string.Empty;

						if (parameters == null)
							parameters = new Dictionary<string, string>();
						parameters[Social.Facebook.MessengerMessage.ReplyParameter] = jMessage.ToString();
					}
					catch { }
				}
				else if (message.SocialServiceType == SocialServiceTypes.WhatsApp)
				{
					try
					{
						var jMessage = Newtonsoft.Json.Linq.JToken.Parse(text);
						if (jMessage.Type == JTokenType.Object && jMessage["text"] != null)
							text = jMessage["text"].ToString();
						else
							text = string.Empty;

						if (parameters == null)
							parameters = new Dictionary<string, string>();
						parameters[Social.WhatsApp.WhatsAppMessage.ReplyParameter] = jMessage.ToString();
					}
					catch { }
				}

				long? insertedMessageID, insertedAssociatedMessageID;
				MessageDAO.Reply(message, null, text, leaveUnassigned, null, false, null, parameters, filter.Actions.Coordinates, null, out insertedMessageID, out insertedAssociatedMessageID, false, ReplySources.Filter, DateTime.Now);
				message.ReplySource = ReplySources.Filter;

				if (message.Case != null)
				{
					message.Case.Parameters[DomainModel.Case.LastReplyTimeParameter] = DateTime.Now.ToString("o");
					if (insertedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedMessageID.Value.ToString();
					else if (insertedAssociatedMessageID != null)
						message.Case.Parameters[DomainModel.Case.LastReplyMessageIDParameter] = insertedAssociatedMessageID.Value.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageIsHSMParameter] = false.ToString();
					message.Case.Parameters[DomainModel.Case.LastReplyMessageSocialServiceTypeIDParameter] = ((short) message.SocialServiceType).ToString();

					if (message.IsDirectMessage)
					{
						message.Case.Parameters[DomainModel.Case.HasPrivateAnswerParameter] = true.ToString();
					}

					DAL.CaseDAO.UpdateParameters(message.Case);
				}

				var interval = new Common.Interval(now, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;
				DomainModel.Historical.DailyService infoService;
				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.RepliedMessages = 1;
				info.AutoRepliedMessages = 1;
				info.MessagesRepliedOutOfSL = (message.OutOfServiceLevel == true) ? 1 : 0;
				info.MessagesRepliedOutOfSLL = (message.OutOfServiceLevel == true && message.OutOfSLL == true) ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.RepliedMessages = 1;
				infoService.AutoRepliedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (insertedMessageID != null)
					await Reply(insertedMessageID.Value, message.Case);
				if (insertedAssociatedMessageID != null)
					await Reply(insertedAssociatedMessageID.Value, message.Case);

				if (!leaveUnassigned)
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Messages))
					{
						System.Instance.AutomaticExportService.StoreMessage(message);
					}
				}

				Tracer.TraceInfo("El sistema auto respondió el mensaje {0}", message);
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló cuando se auto respondía el mensaje {0}: {{0}}", message), ex);
				throw;
			}
		}

		/// <summary>
		/// Guardar el filtro aplicado sobre el mensaje
		/// </summary>
		/// <param name="message">El mensaje al cual se le aplicará el filtro</param>
		/// <param name="filter">Filtro aplicado</param>
		public void SaveFilter(DomainModel.Message message, DomainModel.Filter filter)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (filter == null)
				throw new ArgumentNullException(nameof(filter), "El filtro no puede ser nulo");

			Tracer.TraceInfo("El mensaje {0} aplica al filtro {1}", message, filter.Name);

			try
			{
				MessageDAO.SaveFilter(message, filter.Name, filter.ID);

				filter.Count++;
				// se agrega codigo de filtro al caso del mensaje.
				if (message.Case.Filters == null)
					message.Case.Filters = new List<int>();
				message.Case.Filters.Add(filter.ID);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				var info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.FilteredMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				var infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.FilteredMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se asignaba el filtro {0} al mensaje {1}: {2}", filter, message, ex);
				throw;
			}
		}

		/// <summary>
		/// Encola un mensaje
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se va a encolar</param>
		/// <param name="queue">La <see cref="SystemQueue"/> donde se va a encolar</param>
		/// <param name="shouldEnqueue">Indica si se está encolando (<code>true</code>) o si agrupó a otro mensaje (<code>false</code>)</param>
		public void EnqueueMessage(DomainModel.Message message, SystemQueue queue, bool shouldEnqueue)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (queue == null)
				throw new ArgumentNullException(nameof(queue), "La cola no puede ser nulo");

			Tracer.TraceVerb("Encolando el mensaje {0} en la base de datos", message);

			try
			{
				MessageDAO.EnqueueMessage(message, queue.ID, queue.Count);

				var now = DateTime.Now;
				message.EnqueuedDate = now;
				var segment = message.AddSegment();
				segment.InQueue = queue.Queue;
				segment.EnqueuedDate = now;
				Tracer.TraceVerb("Se insertó el primer segmento del mensaje {0} en la cola {1}", message, queue.Name);

				if (shouldEnqueue)
				{
					var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

					DomainModel.Historical.Daily info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = 0;
					info.EnqueuedMessages = 1;
					info.PeekEnqueuedMessages = queue.Count;
					info.PeekMinutesEnqueuedMessages = now.Hour * 100 + now.Minute;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					DomainModel.Historical.DailyService infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = 0;
					infoService.EnqueuedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.EnqueuedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se encolaba el mensaje {0} en la base de datos: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Asigna un mensaje
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se va a asignar</param>
		/// <param name="queue">La <see cref="SystemQueue"/> donde se va a asignar</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> al cual se le va a asignar el mensaje</param>
		/// <param name="info">Un <see cref="DomainModel.ConnectionInfo"/> con los datos de conexión del agente</param>
		public void AssignMessage(DomainModel.Message message, DomainModel.Agent agent, SystemQueue queue, DomainModel.ConnectionInfo connectionInfo)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (queue == null)
				throw new ArgumentNullException(nameof(queue), "La cola no puede ser nulo");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser nulo");

			if (connectionInfo == null)
				throw new ArgumentNullException(nameof(connectionInfo), "Los datos de conexión del agente no pueden ser nulo");

			if (!agent.CanAssignMessageOfType(message.SocialServiceType))
				throw new ArgumentException(string.Format("El mensaje de tipo {0} no se puede asignar al agente {1}", message.SocialServiceType, agent), nameof(message));

			if (message.Status == MessageStatuses.Grouped)
				throw new ArgumentException(string.Format("El mensaje {0} no se puede asignar al agente porque ya está en estado Agrupado", message), nameof(message));

			Tracer.TraceVerb("Asignando el mensaje {0} al agente {1}", message, agent);

			try
			{
				// Se guarda la asignación del mensaje al agente por si el agente se desconecta y vuelve a pedir
				// sus mensajes asignados
				queue.Assign(message, agent);

				bool alreadyAssigned;
				MessageDAO.AssignToAgent(message, agent.ID, out alreadyAssigned);

				//asigno a la lista de agentes asignados el id del agente
				if (message.Case.AssignedAgents == null)
					message.Case.AssignedAgents = new List<int>();

				var firstTimeCaseAssignedToAgent = false;
				if (!message.Case.AssignedAgents.Contains(agent.ID))
					firstTimeCaseAssignedToAgent = true;

				message.Case.AssignedAgents.Add(agent.ID);
				message.Case.Agents++;
				message.Case.LastPerson = agent;

				/* No saco la marca de pendiente automaticamente.
				if (message.Case.PendingPerson != null)
				{
					Core.System.Instance.CasesService.RemovePendingCase(message.Case, message.Case.PendingPerson);
					message.Case.PendingPerson = null;
				}
				*/

				if (!alreadyAssigned)
				{
					message.AssignedDate = DateTime.Now;

					var lastSegment = message.LastSegment;
					if (lastSegment != null)
					{
						lastSegment.AssignedDate = DateTime.Now;
						lastSegment.Person = agent;
					}

					connectionInfo.MessageStats.IncrementAssignedMessage(message);
					connectionInfo.CurrentlyAssignedMessages++;
					if (firstTimeCaseAssignedToAgent)
						connectionInfo.CasesStats.NewCases++;

					var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

					DomainModel.Historical.Daily info;

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = agent.ID;
					info.AssignedMessages = 1;
					info.NewCases = firstTimeCaseAssignedToAgent ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = message.Queue.ID;
					info.PersonID = 0;
					info.AssignedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = agent.ID;
					info.AssignedMessages = 1;
					info.NewCases = firstTimeCaseAssignedToAgent ? 1 : 0;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					DomainModel.Historical.DailyService infoService;

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = agent.ID;
					infoService.AssignedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = message.Queue.ID;
					infoService.PersonID = 0;
					infoService.AssignedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = agent.ID;
					infoService.AssignedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = 0;
					infoService.PersonID = 0;
					infoService.AssignedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					try
					{
						HistSessionsAgentsCasesMessagesDAO.Insert(message.ID, message.Case.ID, connectionInfo.SessionID, agent.ID, false);

						if (message.Service != null &&
							message.Service.Settings != null &&
							message.Service.Settings.ActAsChat)
						{
							if (message.Parameters == null)
							{
								message.Parameters = new Dictionary<string, string>();
							}

							if (!message.Parameters.ContainsKey(Message.IsChatMode))
							{
								message.Parameters.Add(Message.IsChatMode, true.ToString());
								MessageDAO.UpdateParameters(message);
							}
						}
					}
					catch { }

					if (!message.Case.Parameters.ContainsKey(Case.FirstIncomingMessageAssignedDateParameter))
					{
						message.Case.Parameters[Case.FirstIncomingMessageAssignedDateParameter] = lastSegment.AssignedDate.Value.ToString("o");
					}
				}

				Tracer.TraceVerb("Se asignó el mensaje {0} al agente {1}", message, agent);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se asignaba el mensaje {0} al agente {1}: {2}", message, agent, ex);
				throw;
			}
			global::System.Web.Hosting.HostingEnvironment.QueueBackgroundWorkItem(ct =>
			{
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowChatSummary &&
				message.Case.Parameters.ContainsKey(DomainModel.Case.RequireSummaryYSmart) &&
				!message.Case.Parameters.ContainsKey(DomainModel.Case.SummaryYSmart))
				{
					if (bool.TryParse(message.Case.Parameters[DomainModel.Case.RequireSummaryYSmart], out bool value) && value)
					{
						var stringType = message.Case.Parameters[DomainModel.Case.TypeSummaryYSmart];
						var type = 0;
						var stringExtended = string.Empty;
						var stringProfieExtended = string.Empty;
						var _ySmartService = new ySmartService();

						if (stringType == TypeChatSummaryStrings.Detailed)
						{
							type = (int) TypeChatSummary.Detailed;
						}
						if (message.Case.Parameters.ContainsKey(DomainModel.Case.ExtendedFieldsParameter))
						{
							stringExtended = message.Case.Parameters[DomainModel.Case.ExtendedFieldsParameter];
						}
						if (message.Case.Parameters.ContainsKey(DomainModel.SocialUserProfile.ExtendedFieldsParameter))
						{
							stringProfieExtended = message.Case.Profile.Parameters[DomainModel.SocialUserProfile.ExtendedFieldsParameter];
						}
						var requestBody = new RequestChatSummary
						{
							Case = new RequestCaseInfo()
							{
								ID = message.Case.ID,
								StartedOn = message.Case.StartedOn,
								Observations = message.Case.Observations,
								Messages = message.Case.Messages.Select(x => new RequestMessage
								{
									Body = x.Body,
									ReplySource = x.ReplySource,
									IsReply = x.IsReply,
									HasAttach = x.HasAttach,
									Date = x.Date
								}).ToList(),

								ExtendedFieldsParameter = stringExtended,
								Profile = new ProfileRequest()
								{
									ID = message.Case.Profile.ID,
									DisplayName = message.Case.Profile.DisplayName,
									ExtendedFieldsParameter = stringProfieExtended,

								}
							},
							Type = type.ToString()
						};
						var response = Task.Run(async () =>
						{
							return await _ySmartService.GetChatSummary(requestBody);
						}).Result;
						if (response.Success)
						{
							if (string.IsNullOrWhiteSpace(response.Data.Summary))
							{
								throw new ArgumentException("El summary no puede ser nulo o vacío", nameof(response.Data.Summary));
							}
							else
							{
								message.Case.Parameters[DomainModel.Case.SummaryYSmart] = response.Data.Summary;
								DAL.CaseDAO.UpdateParameters(message.Case);
								var agentNotification = new DomainModel.AgentNotifications.ChatSummary(response.Data.Summary, requestBody.Case.ID);
								System.Instance.RealTimeService.NotifyAgent(agentNotification);
							}
						}
					}
				}
			});
		}

		/// <summary>
		/// Agrupa un mensaje
		/// </summary>
		/// <param name="messageToGroup">El <see cref="DomainModel.Message"/> que quedará agrupado</param>
		/// <param name="messageThatGroups">El <see cref="DomainModel.Message"/> que agrupará al otro mensaje</param>
		public void GroupMessage(DomainModel.Message messageToGroup, DomainModel.Message messageThatGroups)
		{
			GroupMessage(messageToGroup, messageThatGroups, null);
		}

		/// <summary>
		/// Agrupa un mensaje
		/// </summary>
		/// <param name="messageToGroup">El <see cref="DomainModel.Message"/> que quedará agrupado</param>
		/// <param name="messageThatGroups">El <see cref="DomainModel.Message"/> que agrupará al otro mensaje</param>
		/// <param name="agent">El agente que está realizando el agrupamiento o <code>null</code> para cuando el mensaje se agrupa en cola</param>
		public void GroupMessage(DomainModel.Message messageToGroup, DomainModel.Message messageThatGroups, DomainModel.Agent agent)
		{
			if (messageThatGroups == null)
				throw new ArgumentNullException(nameof(messageThatGroups), "El mensaje que agrupa no puede ser nulo");

			if (messageToGroup == null)
				throw new ArgumentNullException(nameof(messageToGroup), "El mensaje que se agrupará no puede ser nulo");

			if (agent == null && messageToGroup.Status != MessageStatuses.NotAssigned)
				throw new ArgumentNullException(nameof(messageToGroup), string.Format("El mensaje que se agrupará tiene que estar en estado No Asignado y está en estado {0}", messageToGroup.Status));

			Tracer.TraceInfo("El mensaje {0} agrupará al mensaje {1}", messageThatGroups, messageToGroup);

			// Marcamos inmediatamente el mensaje como agrupado para que no sea asignado a ningún usuario
			var previousStatus = messageToGroup.Status;

			try
			{
				var now = DateTime.Now;
				var shouldComputeAttendedMessage = false;

				messageToGroup.Status = DomainModel.MessageStatuses.Grouped;
				messageToGroup.FinishedDate = now;
				if (messageToGroup.LastSegment != null)
				{
					messageToGroup.LastSegment.FinishedDate = now;
					messageToGroup.LastSegment.Person = agent;
				}

				var queueToGroup = Core.System.Instance.QueueService.GetQueueById(messageToGroup.Queue.ID);
				var queueThatGroups = Core.System.Instance.QueueService.GetQueueById(messageThatGroups.Queue.ID);
				if (queueToGroup.ID == queueThatGroups.ID)
				{
					if (queueThatGroups.Contains(messageToGroup))
					{
						if (previousStatus == DomainModel.MessageStatuses.NotAssigned)
						{
							Tracer.TraceInfo("El mensaje {0} reemplazará al mensaje {1}", messageThatGroups, messageToGroup);
							queueThatGroups.Replace(messageToGroup, messageThatGroups);
						}
						else
						{
							Tracer.TraceInfo("El mensaje {0} será removido de la cola", messageToGroup);
							queueThatGroups.Remove(messageToGroup);
						}
					}
				}
				else
				{
					if (queueToGroup.Contains(messageToGroup))
					{
						Tracer.TraceInfo("El mensaje {0} será removido de la cola", messageToGroup);
						queueToGroup.Remove(messageToGroup);
					}
				}

				messageThatGroups.Group(messageToGroup);

				if (messageToGroup.Service != null &&
					messageToGroup.Service.Settings != null &&
					messageToGroup.Service.Settings.ActAsChat)
				{
					if (messageToGroup.Parameters == null)
					{
						messageToGroup.Parameters = new Dictionary<string, string>();
					}

					if (!messageToGroup.Parameters.ContainsKey(Message.IsChatMode))
					{
						messageToGroup.Parameters.Add(Message.IsChatMode, true.ToString());
						MessageDAO.UpdateParameters(messageToGroup);
					}

					if (messageToGroup.Parameters.ContainsKey(DomainModel.Message.AttendedParameter) &&
						bool.Parse(messageToGroup.Parameters[DomainModel.Message.AttendedParameter]))
					{
						Tracer.TraceInfo("El mensaje {0} que se agrupará es modo chat y fue atendido. Se lo deja en estado Atendido", messageToGroup);
						messageToGroup.Status = DomainModel.MessageStatuses.Attended;

						shouldComputeAttendedMessage = true;
					}
				}

				MessageDAO.GroupMessage(messageToGroup, previousStatus, messageThatGroups, agent);

				if (messageToGroup.Important)
				{
					Tracer.TraceInfo("El mensaje {0} será marcado como importante", messageThatGroups);
					this.MarkAsImportant(messageThatGroups);
				}

				try
				{
					var messageToGroupInCase = messageThatGroups.Case.Messages.SingleOrDefault(m => m.ID == messageToGroup.ID);
					messageToGroupInCase.Status = MessageStatuses.Grouped;
					messageToGroupInCase.GroupedBy = messageThatGroups;
					if (messageToGroupInCase.Groups != null)
					{
						foreach (var groupedMessage in messageToGroupInCase.Groups)
							groupedMessage.GroupedBy = messageThatGroups;
					}
					messageToGroupInCase.Groups = null;
				}
				catch { }

				if (!shouldComputeAttendedMessage)
				{
					queueToGroup.RealTimeInfo.GroupedMessages++;
				}

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				if (agent != null)
				{
					if (shouldComputeAttendedMessage)
					{
						//queueToGroup.RealTimeInfo.AttendedMessagesByAgent++;
						//if (messageToGroup.OutOfServiceLevel != null && messageToGroup.OutOfServiceLevel.Value)
						//	queueToGroup.RealTimeInfo.MessagesAttendedOutOfSL++;
					}
					else
					{
						queueToGroup.RealTimeInfo.AgentGroupedMessages++;
					}

					bool alreadyAssigned;
					MessageDAO.AssignToAgent(messageThatGroups, agent.ID, out alreadyAssigned);
					messageThatGroups.AssignedDate = now;

					var lastSegment = messageThatGroups.LastSegment;
					if (lastSegment != null)
						lastSegment.AssignedDate = now;

					// Se guarda la asignación del mensaje al agente por si el agente se desconecta y vuelve a pedir
					// sus mensajes asignados
					queueThatGroups.Assign(messageThatGroups, agent);

					DomainModel.ConnectionInfo connectionInfo = System.Instance.AgentsService.GetConnectionInfo(agent);
					connectionInfo.MessageStats.IncrementAssignedMessage(messageThatGroups);
					if (!shouldComputeAttendedMessage)
					{
						connectionInfo.MessageStats.AgentGroupedMessages++;
					}

					HistSessionsAgentsCasesMessagesDAO.Insert(messageThatGroups.ID, messageThatGroups.Case.ID, connectionInfo.SessionID, agent.ID, false);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = messageToGroup.Queue.ID;
					info.PersonID = agent.ID;
					if (shouldComputeAttendedMessage)
					{
						//info.AttendedMessagesByAgent++;
						//info.MessagesAttendedOutOfSL = (messageToGroup.OutOfServiceLevel == true) ? 1 : 0;
						//info.MessagesAttendedOutOfSLL = (messageToGroup.OutOfServiceLevel == true && messageToGroup.OutOfSLL == true) ? 1 : 0;
					}
					else
					{
						info.GroupedMessages = 1;
						info.AgentGroupedMessages = 1;
					}
					info.AssignedMessages = 1;
					try
					{
						info.AgentTime = messageToGroup.LastSegment.ActionTime ?? 0;
						info.UnreadTime = messageToGroup.LastSegment.UnreadTime ?? 0;
						info.ReadTime = messageToGroup.LastSegment.ReadTime ?? 0;
					}
					catch { }
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = messageToGroup.Queue.ID;
					info.PersonID = 0;
					if (shouldComputeAttendedMessage)
					{
						//info.AttendedMessagesByAgent++;
						//info.MessagesAttendedOutOfSL = (messageToGroup.OutOfServiceLevel == true) ? 1 : 0;
						//info.MessagesAttendedOutOfSLL = (messageToGroup.OutOfServiceLevel == true && messageToGroup.OutOfSLL == true) ? 1 : 0;
					}
					else
					{
						info.GroupedMessages = 1;
						info.AgentGroupedMessages = 1;
					}
					info.AssignedMessages = 1;
					try
					{
						info.AgentTime = messageToGroup.LastSegment.ActionTime ?? 0;
						info.UnreadTime = messageToGroup.LastSegment.UnreadTime ?? 0;
						info.ReadTime = messageToGroup.LastSegment.ReadTime ?? 0;
					}
					catch { }
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = agent.ID;
					if (shouldComputeAttendedMessage)
					{
						//info.AttendedMessagesByAgent++;
						//info.MessagesAttendedOutOfSL = (messageToGroup.OutOfServiceLevel == true) ? 1 : 0;
						//info.MessagesAttendedOutOfSLL = (messageToGroup.OutOfServiceLevel == true && messageToGroup.OutOfSLL == true) ? 1 : 0;
					}
					else
					{
						info.GroupedMessages = 1;
						info.AgentGroupedMessages = 1;
					}
					info.AssignedMessages = 1;
					try
					{
						info.AgentTime = messageToGroup.LastSegment.ActionTime ?? 0;
						info.UnreadTime = messageToGroup.LastSegment.UnreadTime ?? 0;
						info.ReadTime = messageToGroup.LastSegment.ReadTime ?? 0;
					}
					catch { }
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					if (messageToGroup.Service != null)
					{

					}
					DomainModel.Historical.DailyService infoService;

					if (messageToGroup.Service != null)
					{
						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = messageToGroup.Service.ID;
						infoService.QueueID = messageToGroup.Queue.ID;
						infoService.PersonID = agent.ID;
						infoService.AssignedMessages = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = messageToGroup.Service.ID;
						infoService.QueueID = messageToGroup.Queue.ID;
						infoService.PersonID = 0;
						infoService.AssignedMessages = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = messageToGroup.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = agent.ID;
						infoService.AssignedMessages = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

						infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
						infoService.ServiceID = messageToGroup.Service.ID;
						infoService.QueueID = 0;
						infoService.PersonID = 0;
						infoService.AssignedMessages = 1;
						Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
					}

					this.Read(messageThatGroups, agent);
				}
				else
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = messageToGroup.Queue.ID;
					info.PersonID = 0;
					info.GroupedMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);
				}
			}
			catch (Exception ex)
			{
				messageToGroup.Status = previousStatus;

				Tracer.TraceError("Falló cuando se agrupaba el mensaje {0} con el mensaje {1}: {2}", messageToGroup, messageThatGroups, ex);
				throw;
			}
		}

		/// <summary>
		/// Asigna un mensaje a una cola
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a asignar</param>
		/// <param name="queue">La <see cref="SystemQueue"/> a la que se asignará el mensaje</param>
		/// <param name="newCaseCreated">Indica si para el mensaje <paramref name="message"/> se acaba de crear un caso nuevo</param>
		public void AssignToQueue(DomainModel.Message message, SystemQueue queue, bool newCaseCreated)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (queue == null)
				throw new ArgumentNullException(nameof(queue), "La cola no puede ser nulo");

			Tracer.TraceInfo("Se asignará el mensaje {0} a la cola {1}", message, queue);

			try
			{
				MessageDAO.AssignToQueue(message, queue.ID, newCaseCreated);
				message.Queue = queue.Queue;
				message.Case.Queue = queue.Queue;

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.NewMessages = 1;
				info.NewChats = (message.SocialServiceType == SocialServiceTypes.Chat) ? 1 : 0;
				info.NewCases = newCaseCreated ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.NewMessages = 1;
				info.NewChats = (message.SocialServiceType == SocialServiceTypes.Chat) ? 1 : 0;
				info.NewCases = newCaseCreated ? 1 : 0;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = message.Queue.ID;
				infoService.PersonID = 0;
				infoService.NewCases = newCaseCreated ? 1 : 0;
				infoService.NewMessages = 1;
				if (message.SocialServiceType == SocialServiceTypes.Twitter ||
					message.SocialServiceType == SocialServiceTypes.Facebook)
				{
					infoService.NewMessagesPrivate = message.IsDirectMessage ? 1 : 0;
					infoService.NewMessagesPublic = message.IsDirectMessage ? 0 : 1;
				}
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.NewCases = newCaseCreated ? 1 : 0;
				infoService.NewMessages = 1;
				if (message.SocialServiceType == SocialServiceTypes.Twitter ||
					message.SocialServiceType == SocialServiceTypes.Facebook)
				{
					infoService.NewMessagesPrivate = message.IsDirectMessage ? 1 : 0;
					infoService.NewMessagesPublic = message.IsDirectMessage ? 0 : 1;
				}
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se asignaba el mensaje {0} a la cola {1}: {2}", message, queue, ex);
				throw;
			}
		}

		/// <summary>
		/// Reserva un mensaje a un agente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que será reservado</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> al cual se reservará el mensaje</param>
		/// <returns><code>true</code> cuando el mensaje pudo ser reservado al agente; en caso contrario, <code>false</code></returns>
		public bool ReserveForAgent(DomainModel.Message message, DomainModel.Agent agent)
		{
			return ReserveForAgent(message, agent, null);
		}

		/// <summary>
		/// Reserva un mensaje a un agente
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que será reservado</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> al cual se reservará el mensaje</param>
		/// <param name="user">Un <see cref="DomainModel.User"/> que realizar la reserva o <code>null</code> para indicar que fue el sistema</param>
		/// <returns><code>true</code> cuando el mensaje pudo ser reservado al agente; en caso contrario, <code>false</code></returns>
		public bool ReserveForAgent(DomainModel.Message message, DomainModel.Agent agent, DomainModel.User user)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");

			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser null");

			if (agent.AllowedSocialServiceTypes == null || !agent.AllowedSocialServiceTypes.Any(sst => sst == message.SocialServiceType))
				throw new InvalidOperationException(string.Format("El agente no puede puede recibir mensajes del tipo {0}", message.SocialServiceType));

			try
			{
				if (!message.Queue.ReserveMessages)
				{
					Tracer.TraceVerb("No se puede reservar el mensaje {0} al agente {1} porque la cola {2} no soporta reservas", message, agent, message.Queue);
					return false;
				}

				if (agent.Queues.Contains(message.Queue))
				{
					if (Core.System.Instance.AgentsService.AgentIsConnected(agent))
					{
						Core.SystemQueue queue = Core.System.Instance.QueueService.GetQueueById(message.Queue.ID);

						//verifico si el caso lo tiene el agente en su bandeja. Si ya lo tiene, no controlo la cantidad de reserva ni el estado.
						if (!queue.GetAssignedMessages(agent).Any(assignedMessage => assignedMessage.Case.ID == message.Case.ID))
						{
							if (message.Queue.DontReserveWithStatusID.Count != 0)
							{
								var info = System.Instance.AgentsService.GetConnectionInfo(agent);
								if (info.Status == ConnectionStatuses.Aux && message.Queue.DontReserveWithStatusID.Exists(statusID => statusID == info.AuxReason))
								{
									Tracer.TraceVerb("El agente {0} se encuentra en un estado auxiliar que no permite reservar mensajes", agent);
									message.ShouldBeAssignedTo = null;
									return false;
								}
							}

							if (message.Queue.MaxReservedMessagesPerAgent != 0)
							{
								var messagesToAssign = queue.GetTotalMessagesToAssign(agent);
								if (messagesToAssign >= message.Queue.MaxReservedMessagesPerAgent)
								{
									Tracer.TraceVerb("El agente {0} supera la cantidad máxima de mensajes reservados. Tiene {1} asignados y el máxima para la cola {2} es de {3}", agent, messagesToAssign, message.Queue.ID, message.Queue.MaxReservedMessagesPerAgent);
									message.ShouldBeAssignedTo = null;
									return false;
								}
							}
						}

						if (user == null)
							Tracer.TraceVerb("El sistema reservará el mensaje {0} al agente {1}", message, agent);
						else
							Tracer.TraceVerb("El usuario {2} está reservando el mensaje {0} al agente {1}", message, agent, user);

						MessageDAO.ReserveForAgent(message, agent, user);
						queue.Reserve(message, agent);

						if (user != null)
						{
							message.ManualyReserved = true;
							message.ManualyReservedDate = DateTime.Now;
						}

						Tracer.TraceVerb("Se reservó el mensaje {0} al agente {1}", message, agent);

						return true;
					}
					else
					{
						Tracer.TraceVerb("El agente {0} no está conectado por lo tanto no se le reservará el mensaje", agent);
						message.ShouldBeAssignedTo = null;
						return false;
					}
				}
				else
				{
					Tracer.TraceVerb("El agente {0} no está más en la cola {1} por lo tanto no se le reservará el mensaje", agent, message.Queue.Name);
					message.ShouldBeAssignedTo = null;
					return false;
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se reservaba el mensaje {0} al agente {1}: {2}", message, user, ex);
				throw;
			}
		}

		/// <summary>
		/// Marca un mensaje como importante
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se marcará como importante</param>
		public void MarkAsImportant(DomainModel.Message message)
		{
			MarkAsImportant(message, null);
		}

		/// <summary>
		/// Marca un mensaje como importante
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se marcará como importante</param>
		/// <param name="supervisor">El <see cref="DomainModel.User"/> con el supervisor que está haciendo la acción o <code>null</code> si es el sistema</param>
		public void MarkAsImportant(DomainModel.Message message, DomainModel.User supervisor)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "Debe especificar el mensaje a marcar como importante");

			if (message.Important)
				return;

			if (supervisor != null)
				Tracer.TraceInfo("El supervisor {0} está marcando el mensaje {1} como importante", supervisor, message);
			else
				Tracer.TraceInfo("El sistema está marcando el mensaje {0} como importante", message);

			try
			{
				MessageDAO.MarkAsImportant(message, supervisor);

				message.Important = true;
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló cuando se marcaba como importante el mensaje {0}: {{0}}", message), ex);
				throw;
			}
		}

		/// <summary>
		/// Transfiere un mensaje de la cola en la que está a la cola <paramref name="destinationQueue"/>
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que se va a mover</param>
		/// <param name="sourceQueue">La <see cref="SystemQueue"/> donde está encolado el mensaje</param>
		/// <param name="destinationQueue">La <see cref="SystemQueue"/> donde se encolará el mensaje</param>
		/// <param name="person">El <see cref="DomainModel.Person"/> que mueve el mensaje</param>
		/// <param name="reason">El motivo que describe la razón del retorno a la cola</param>
		/// <param name="isExpired">Indica si el movimiento es por vencimiento de un mensaje</param>
		/// <param name="isServiceLevel">Indica si el movimiento es por inclumplimiento del service level</param>
		/// <param name="oldStatus">El estado en el que estaba el mensaje antes de ser movido</param>
		public void MoveToQueue(DomainModel.Message message, SystemQueue sourceQueue, SystemQueue destinationQueue, DomainModel.Person person, string reason, bool isServiceLevel, bool isExpired, MessageStatuses oldStatus)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			if (sourceQueue == null)
				throw new ArgumentNullException(nameof(sourceQueue), "La cola de origen no puede ser nulo");

			if (destinationQueue == null)
				throw new ArgumentNullException(nameof(destinationQueue), "La cola de destino no puede ser nulo");

			if (person != null)
			{
				if (person.Type == PersonTypes.Agent)
				{
					if (!System.Instance.AgentsService.ConnectedAgents.Contains(person))
						throw new CoreException("El agente no puede transferir mensajes porque se encuentra desconectado");

					if (!destinationQueue.Queue.WorkingHoursForReceivingMessages.IsTimeWorkable(DateTime.Now))
						throw new CoreException("No se puede retornar el mensaje porque la cola está fuera de horario laboral");

					if (destinationQueue.Queue.ConnectedAgentsForReceivingMessages &&
						!destinationQueue.SubscribedAgents.Any(a => a != person))
						throw new CoreException("No se puede retornar el mensaje porque la cola no tiene otros agentes conectados");
				}
				else if (person.Type == PersonTypes.User)
				{
					if (!message.Queue.WorkingHoursForReceivingMessagesExceptionSupervisors &&
						!message.Queue.WorkingHoursForReceivingMessages.IsTimeWorkable(DateTime.Now))
						throw new CoreException("No se puede retornar el mensaje porque la cola está fuera de horario laboral");

					if (destinationQueue.Queue.ConnectedAgentsForReceivingMessages &&
						!destinationQueue.Queue.ConnectedAgentsForReceivingMessagesExceptionsSupervisors &&
						destinationQueue.TotalSubscribedAgents == 0)
						throw new CoreException("No se puede retornar el mensaje porque la cola no tiene otros agentes conectados");
				}
			}
			else if (isServiceLevel || isExpired)
			{
				if (!destinationQueue.Queue.WorkingHoursForReceivingMessagesExceptionSL &&
					!destinationQueue.Queue.WorkingHoursForReceivingMessages.IsTimeWorkable(DateTime.Now))
					throw new CoreException("No se puede retornar el mensaje porque la cola está fuera de horario laboral");

				if (destinationQueue.Queue.ConnectedAgentsForReceivingMessages &&
					!destinationQueue.Queue.ConnectedAgentsForReceivingMessagesExceptionsSL &&
					destinationQueue.TotalSubscribedAgents == 0)
					throw new CoreException("No se puede retornar el mensaje porque la cola no tiene otros agentes conectados");
			}

			Tracer.TraceInfo("Se moverá el mensaje {0} a la cola {1}", message, destinationQueue);

			try
			{
				MessageDAO.MoveToQueue(message, destinationQueue.ID, destinationQueue.Count, person, reason, isServiceLevel, isExpired, oldStatus);

				var now = DateTime.Now;
				sourceQueue.Move(message, destinationQueue);
				message.Queue = destinationQueue.Queue;
				if (person != null && person.Type == DomainModel.PersonTypes.Agent)
					message.ShouldNotBeAssignedTo = (DomainModel.Agent) person;

				message.Status = DomainModel.MessageStatuses.NotAssigned;
				message.AssignedTo = null;
				message.ShouldBeAssignedTo = null;
				message.Read = false;
				message.ReturnedToQueueDate = now;
				message.TimesReturnedToQueue++;
				message.Moved = true;
				message.AssignedDate = null;
				message.ReadDate = null;
				message.FinishedReadDate = null;
				message.Case.Queue = destinationQueue.Queue;

				if (message.IsGrouping)
				{
					foreach (var groupedMessage in message.Groups)
					{
						groupedMessage.Case = message.Case;
						groupedMessage.Queue = message.Queue;
					}
				}

				var lastSegment = message.LastSegment;
				if (lastSegment != null)
				{
					lastSegment.OutQueue = destinationQueue.Queue;
					lastSegment.FinishedDate = now;
					if (person != null)
						lastSegment.Person = person;

					lastSegment = message.AddSegment();
					lastSegment.InQueue = destinationQueue.Queue;
					lastSegment.EnqueuedDate = now;
				}

				DomainModel.ServiceLevel serviceLevelIn = null;
				if (destinationQueue.Queue.ServiceLevelParameters.ContainsKey(ServiceLevelTypes.ServiceLevel))
					serviceLevelIn = destinationQueue.Queue.ServiceLevelParameters[ServiceLevelTypes.ServiceLevel];
				DomainModel.ServiceLevel serviceLevelOut = null;
				if (sourceQueue.Queue.ServiceLevelParameters.ContainsKey(ServiceLevelTypes.ServiceLevel))
					serviceLevelOut = sourceQueue.Queue.ServiceLevelParameters[ServiceLevelTypes.ServiceLevel];
				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;


				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = destinationQueue.ID;
				info.PersonID = 0;
				info.EnqueuedMessages = 1;
				info.InboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				info.FlowIn = (isServiceLevel || isExpired) ? 1 : 0;
				info.FlowInSLL = ((isServiceLevel || isExpired) &&
								 lastSegment != null &&
								 serviceLevelIn != null &&
								 serviceLevelIn.Actions.AvailableDaysAndTimes &&
								 serviceLevelIn.Actions.DaysAndTimes.IsTimeWorkable(lastSegment.EnqueuedDate.Value))
									? 1
									: 0;
				info.PeekEnqueuedMessages = destinationQueue.Count;
				info.PeekMinutesEnqueuedMessages = now.Hour * 100 + now.Minute;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = sourceQueue.ID;
				info.PersonID = 0;
				info.DequeuedMessages = 1;
				info.OutboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				info.FlowOut = (isServiceLevel || isExpired) ? 1 : 0;
				info.FlowOutSLL = ((isServiceLevel || isExpired) &&
									 lastSegment != null &&
									 serviceLevelOut != null &&
									 serviceLevelOut.Actions.AvailableDaysAndTimes &&
									 serviceLevelOut.Actions.DaysAndTimes.IsTimeWorkable(lastSegment.EnqueuedDate.Value))
										? 1
										: 0;
				info.PeekEnqueuedMessages = sourceQueue.Count;
				info.PeekMinutesEnqueuedMessages = now.Hour * 100 + now.Minute;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				if (person != null)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = destinationQueue.ID;
					info.PersonID = person.ID;
					info.EnqueuedMessages = 1;
					info.InboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = person.ID;
					info.EnqueuedMessages = 1;
					info.InboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = sourceQueue.ID;
					info.PersonID = person.ID;
					info.DequeuedMessages = 1;
					info.OutboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = person.ID;
					info.DequeuedMessages = 1;
					info.OutboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(info, interval);
				}

				DomainModel.Historical.DailyService infoService;

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = destinationQueue.ID;
				infoService.PersonID = 0;
				infoService.EnqueuedMessages = 1;
				infoService.InboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = sourceQueue.ID;
				infoService.PersonID = 0;
				infoService.DequeuedMessages = 1;
				infoService.OutboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
				infoService.ServiceID = message.Service.ID;
				infoService.QueueID = 0;
				infoService.PersonID = 0;
				infoService.EnqueuedMessages = 1;
				infoService.InboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				infoService.DequeuedMessages = 1;
				infoService.OutboundMessages = (isServiceLevel || isExpired) ? 0 : 1;
				Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

				if (person != null)
				{
					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = destinationQueue.ID;
					infoService.PersonID = person.ID;
					infoService.EnqueuedMessages = 1;
					infoService.InboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);

					infoService = DomainModel.Historical.DailyService.CreateForInterval(interval);
					infoService.ServiceID = message.Service.ID;
					infoService.QueueID = sourceQueue.ID;
					infoService.PersonID = person.ID;
					infoService.DequeuedMessages = 1;
					infoService.OutboundMessages = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoService, interval);
				}

				Tracer.TraceVerb("Se movió el mensaje {0} a la cola {1}", message, destinationQueue.Name);

				try
				{
					if (lastSegment.FinishedDate.HasValue && lastSegment.EnqueuedDate.HasValue)
					{
						MetricsAdapter.CalculateTotalOperationTimeParameter(message.Case?.Parameters, lastSegment.FinishedDate.Value, lastSegment.EnqueuedDate.Value);
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError($"Falló al actualizar tiempos del caso con error, {ex}");
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se movía el mensaje {0} a la cola {1}: {2}", message, destinationQueue, ex);
				throw;
			}
		}

		/// <summary>
		/// Crea (si no existe uno abierto) o continua (en caso de existir uno abierto) un caso que contiene únicamente mails asignando el mensaje al 
		/// caso
		/// </summary>
		/// <param name="message">El <see cref="Mail.MailMessage"/> que se asignará al caso</param>
		/// <param name="newCaseCreated">Cuando retorna establece si un nuevo Caso fue creado para el mensaje (<code>true</code>) o si se
		/// continuó con un caso ya existente (<code>false</code>)</param>
		public void CreateOrContinueMailCase(Mail.MailMessage message, out bool newCaseCreated)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			Tracer.TraceInfo("Se creará o continuará un caso de mail con el mensaje {0}", message);

			try
			{

				long? repliedMessageId = null;
				if (!string.IsNullOrEmpty(message.RepliesToSocialMessageID) &&
					DomainModel.StorageManager.Instance.ExistsMessage(message.RepliesToSocialMessageID, SocialServiceTypes.Mail, out repliedMessageId))
				{
					Tracer.TraceInfo("El mensaje {0} responde al código de mail {1} que corresponde al código de mensaje {2}", message, message.RepliesToSocialMessageID, repliedMessageId.Value);
				}

				var @case = CaseDAO.CreateOrContinueMailCase(message, repliedMessageId, CaseStartedBySources.IncomingMessage, out newCaseCreated);
				message.PostedBy.Profile = @case.Profile;
				message.Case = @case;

				if (newCaseCreated)
				{
					DomainModel.Historical.DailyCase infoCase;
					var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
					infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
					infoCase.CasesStarted = 1;
					Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
					{
						Core.System.Instance.AutomaticExportService.StoreCase(@case);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se creaba o continuaba un caso de mail con el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Crea un caso que contiene únicamente chats asignando el mensaje al caso
		/// </summary>
		/// <param name="message">El <see cref="Mail.MailMessage"/> que se asignará al caso</param>
		public void CreateChatCase(Chat.BaseChatMessage message)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser nulo");

			Tracer.TraceInfo("Se creará un caso de chat con el mensaje {0}", message);

			try
			{
				DomainModel.Case @case = CaseDAO.CreateChatCase(message);
				message.PostedBy.Profile = @case.Profile;
				message.Case = @case;

				DomainModel.Historical.DailyCase infoCase;
				var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
				infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
				infoCase.CasesStarted = 1;
				Core.System.Instance.IntervalService.StoreInfo(infoCase, interval);

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
				{
					Core.System.Instance.AutomaticExportService.StoreCase(@case);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se creaba un caso de chat con el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Cierra un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a cerrar</param>
		/// <param name="closedBy">Indica quién fue el que cerró el caso</param>
		/// <param name="person">Indica el <see cref="DomainModel.Person"/> que cerró el caso</param>
		/// <param name="interval">Indica el intervalo en el que ocurrió la operación o <see cref="Interval.Null"/> para el intervalo actual</param>
		/// <param name="storeInDatabase">Indica si la operación de cierre de caso debe invocar a la base de datos (<code>true</code>) o únicamente
		/// contabilizará estadísticas (<code>false</code>)</param>
		/// <param name="queue">En caso de que <paramref name="@case"/> sea <code>null</code> se debe proveer la cola donde se realiza el cierre del caso</param>
		public async Task CloseCaseAsync(DomainModel.Case @case, DomainModel.CaseClosingResponsibles closedBy, DomainModel.Person person, Interval interval, bool storeInDatabase, DomainModel.Queue queue)
		{
			await CloseCaseAsync(new ActionOptions.CloseCaseOptions()
			{
				Case = @case,
				ClosedBy = closedBy,
				Person = person,
				Interval = interval,
				StoreInDatabase = storeInDatabase,
				Queue = queue,
				Message = null,
				InvokeServiceOperations = true
			});
		}

		public void MergeProfiles(int fromSocialUserProfileID, int toSocialUserProfileID, int personID)
		{
			try
			{
				if (fromSocialUserProfileID == toSocialUserProfileID)
				{
					throw new ArgumentException("fromSocialUserProfileId", "No se puede mergear un perfil con él mismo, los ids de perfiles deben ser distintos.");
				}

				SocialUserProfileDAO.MergeProfiles(fromSocialUserProfileID, toSocialUserProfileID, personID);

				var agent = DomainModel.Cache.Instance.GetItem<DomainModel.Agent>(personID);
				System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.AgentMergedAccounts, new Dictionary<string, string>()
				{
					{ "@@AGENTE[CODIGO]@@", agent.ID.ToString() }
					, { "@@AGENTE[NOMBRE]@@", agent.FullName }
					, { "@@AGENTE[USERNAME]@@", agent.UserName }
					, { "@@PERFIL[CODIGO]@@", toSocialUserProfileID.ToString() }
					, { "@@PERFIL_VIEJO[CODIGO]@@", fromSocialUserProfileID.ToString() }
				});
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se intentaba mergear perfiles {0}", ex);
				throw;
			}
		}

		public void UnmergeUserAccount(SocialUser socialUser, int personID)
		{
			try
			{
				if (SocialUserProfileDAO.SocialUserProfileHasMessagesEnqueued(socialUser.Profile.ID))
				{
					throw new Exception(string.Format("El perfil {0} tiene mensajes en cola. No se pueden unir los perfiles.", socialUser.Profile.ID));
				}

				if (SocialUserProfileDAO.SocialUserProfileHasAssignedMessages(socialUser.Profile.ID))
				{
					throw new Exception(string.Format("El perfil {0} tiene mensajes asignados. No se pueden unir los perfiles.", socialUser.Profile.ID));
				}

				SocialUserProfileDAO.UnmergeSocialUserAccount(socialUser, personID);

				var agent = DomainModel.Cache.Instance.GetItem<DomainModel.Agent>(personID);
				var user = DomainModel.Cache.Instance.GetItem<DomainModel.User>(personID);

				System.Instance.ServerIntegrationsService.ExecuteActions(ServerIntegrationTypes.UnmergedAccounts, new Dictionary<string, string>()
				{
					{ "@@AGENTE[CODIGO]@@", agent?.ID.ToString() ?? "" }
					, { "@@AGENTE[NOMBRE]@@", agent?.FullName ?? "" }
					, { "@@AGENTE[USERNAME]@@", agent?.UserName ?? "" }
					, { "@@SUPERVISOR[CODIGO]@@", user?.ID.ToString() ?? "" }
					, { "@@SUPERVISOR[NOMBRE]@@", user?.FullName ?? "" }
					, { "@@SUPERVISOR[USERNAME]@@", user?.UserName ?? "" }
					, { "@@PERFIL[CODIGO]@@", socialUser.Profile.ID.ToString() }
					, { "@@PERFIL_VIEJO[CODIGO]@@", socialUser.OldProfile.ID.ToString() }
				});
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se intentaba desasociar la cuenta de usuario", ex);
				throw;
			}
		}

		public void ChangeAgentConnectionStatus(DomainModel.Agent agent, DateTime previousStatusStartedOn, ConnectionStatuses previousStatus, double secondsInPreviousStatus, short? auxReason, ConnectionStatuses? newConnectionStatus, short? newAuxReason)
		{
			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser null");

			if (previousStatus == ConnectionStatuses.Aux)
				Tracer.TraceVerb("Actualizando tiempos de {0}[{1}] del agente {2}", previousStatus, auxReason, agent);
			else
				Tracer.TraceVerb("Actualizando tiempos de {0} del agente {1}", previousStatus, agent);

			try
			{
				if (previousStatus != ConnectionStatuses.Aux && previousStatus != ConnectionStatuses.AuxPending)
					auxReason = null;

				if (newConnectionStatus != null)
				{
					AgentDAO.ChangeConnectionStatus(agent.ID, previousStatusStartedOn, previousStatus, secondsInPreviousStatus, auxReason, newConnectionStatus, newAuxReason);
				}

				DomainModel.Historical.Daily info;
				var interval = new Common.Interval(previousStatusStartedOn, DomainModel.SystemSettings.Instance.IntervalsPerHour);

				byte intervalsPerHour = DomainModel.SystemSettings.Instance.IntervalsPerHour;
				int maxSecondsFromPreviousState = Interval.GetMinutesPerInterval(intervalsPerHour) * 60 - Interval.GetSecondsOfDateTimeInItsInterval(previousStatusStartedOn, intervalsPerHour);
				if (secondsInPreviousStatus > maxSecondsFromPreviousState)
				{
					double secondsInPreviousStateButInNewInterval = secondsInPreviousStatus - maxSecondsFromPreviousState;

					var nextInterval = interval.Next();

					info = DomainModel.Historical.Daily.CreateForInterval(nextInterval);
					info.QueueID = 0;
					info.PersonID = agent.ID;
					info.LoginTime = secondsInPreviousStateButInNewInterval;

					switch (previousStatus)
					{
						case ConnectionStatuses.Available:
							info.AvailTime = secondsInPreviousStateButInNewInterval;
							break;
						case ConnectionStatuses.Working:
						case ConnectionStatuses.AuxPending:
							info.WorkingTime = secondsInPreviousStateButInNewInterval;
							break;
						case ConnectionStatuses.Aux:
							info.AuxTime = secondsInPreviousStateButInNewInterval;
							info.Aux0Time = (auxReason != null && auxReason.Value == 0) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux1Time = (auxReason != null && auxReason.Value == 1) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux2Time = (auxReason != null && auxReason.Value == 2) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux3Time = (auxReason != null && auxReason.Value == 3) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux4Time = (auxReason != null && auxReason.Value == 4) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux5Time = (auxReason != null && auxReason.Value == 5) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux6Time = (auxReason != null && auxReason.Value == 6) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux7Time = (auxReason != null && auxReason.Value == 7) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux8Time = (auxReason != null && auxReason.Value == 8) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux9Time = (auxReason != null && auxReason.Value == 9) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux10Time = (auxReason != null && auxReason.Value == 10) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux11Time = (auxReason != null && auxReason.Value == 11) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux12Time = (auxReason != null && auxReason.Value == 12) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux13Time = (auxReason != null && auxReason.Value == 13) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux14Time = (auxReason != null && auxReason.Value == 14) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux15Time = (auxReason != null && auxReason.Value == 15) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux16Time = (auxReason != null && auxReason.Value == 16) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux17Time = (auxReason != null && auxReason.Value == 17) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux18Time = (auxReason != null && auxReason.Value == 18) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux19Time = (auxReason != null && auxReason.Value == 19) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux20Time = (auxReason != null && auxReason.Value == 20) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux21Time = (auxReason != null && auxReason.Value == 21) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux22Time = (auxReason != null && auxReason.Value == 22) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux23Time = (auxReason != null && auxReason.Value == 23) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux24Time = (auxReason != null && auxReason.Value == 24) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux25Time = (auxReason != null && auxReason.Value == 25) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux26Time = (auxReason != null && auxReason.Value == 26) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux27Time = (auxReason != null && auxReason.Value == 27) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux28Time = (auxReason != null && auxReason.Value == 28) ? secondsInPreviousStateButInNewInterval : 0;
							info.Aux29Time = (auxReason != null && auxReason.Value == 29) ? secondsInPreviousStateButInNewInterval : 0;
							break;
						default:
							break;
					}

					Core.System.Instance.IntervalService.StoreInfo(info, nextInterval);

					secondsInPreviousStatus = maxSecondsFromPreviousState;
				}

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = agent.ID;
				info.LoginTime = secondsInPreviousStatus;

				switch (previousStatus)
				{
					case ConnectionStatuses.Available:
						info.AvailTime = secondsInPreviousStatus;
						break;
					case ConnectionStatuses.Working:
					case ConnectionStatuses.AuxPending:
						info.WorkingTime = secondsInPreviousStatus;
						break;
					case ConnectionStatuses.Aux:
						info.AuxTime = secondsInPreviousStatus;
						info.Aux0Time = (auxReason != null && auxReason.Value == 0) ? secondsInPreviousStatus : 0;
						info.Aux1Time = (auxReason != null && auxReason.Value == 1) ? secondsInPreviousStatus : 0;
						info.Aux2Time = (auxReason != null && auxReason.Value == 2) ? secondsInPreviousStatus : 0;
						info.Aux3Time = (auxReason != null && auxReason.Value == 3) ? secondsInPreviousStatus : 0;
						info.Aux4Time = (auxReason != null && auxReason.Value == 4) ? secondsInPreviousStatus : 0;
						info.Aux5Time = (auxReason != null && auxReason.Value == 5) ? secondsInPreviousStatus : 0;
						info.Aux6Time = (auxReason != null && auxReason.Value == 6) ? secondsInPreviousStatus : 0;
						info.Aux7Time = (auxReason != null && auxReason.Value == 7) ? secondsInPreviousStatus : 0;
						info.Aux8Time = (auxReason != null && auxReason.Value == 8) ? secondsInPreviousStatus : 0;
						info.Aux9Time = (auxReason != null && auxReason.Value == 9) ? secondsInPreviousStatus : 0;
						info.Aux10Time = (auxReason != null && auxReason.Value == 10) ? secondsInPreviousStatus : 0;
						info.Aux11Time = (auxReason != null && auxReason.Value == 11) ? secondsInPreviousStatus : 0;
						info.Aux12Time = (auxReason != null && auxReason.Value == 12) ? secondsInPreviousStatus : 0;
						info.Aux13Time = (auxReason != null && auxReason.Value == 13) ? secondsInPreviousStatus : 0;
						info.Aux14Time = (auxReason != null && auxReason.Value == 14) ? secondsInPreviousStatus : 0;
						info.Aux15Time = (auxReason != null && auxReason.Value == 15) ? secondsInPreviousStatus : 0;
						info.Aux16Time = (auxReason != null && auxReason.Value == 16) ? secondsInPreviousStatus : 0;
						info.Aux17Time = (auxReason != null && auxReason.Value == 17) ? secondsInPreviousStatus : 0;
						info.Aux18Time = (auxReason != null && auxReason.Value == 18) ? secondsInPreviousStatus : 0;
						info.Aux19Time = (auxReason != null && auxReason.Value == 19) ? secondsInPreviousStatus : 0;
						info.Aux20Time = (auxReason != null && auxReason.Value == 20) ? secondsInPreviousStatus : 0;
						info.Aux21Time = (auxReason != null && auxReason.Value == 21) ? secondsInPreviousStatus : 0;
						info.Aux22Time = (auxReason != null && auxReason.Value == 22) ? secondsInPreviousStatus : 0;
						info.Aux23Time = (auxReason != null && auxReason.Value == 23) ? secondsInPreviousStatus : 0;
						info.Aux24Time = (auxReason != null && auxReason.Value == 24) ? secondsInPreviousStatus : 0;
						info.Aux25Time = (auxReason != null && auxReason.Value == 25) ? secondsInPreviousStatus : 0;
						info.Aux26Time = (auxReason != null && auxReason.Value == 26) ? secondsInPreviousStatus : 0;
						info.Aux27Time = (auxReason != null && auxReason.Value == 27) ? secondsInPreviousStatus : 0;
						info.Aux28Time = (auxReason != null && auxReason.Value == 28) ? secondsInPreviousStatus : 0;
						info.Aux29Time = (auxReason != null && auxReason.Value == 29) ? secondsInPreviousStatus : 0;
						break;
					default:
						break;
				}

				Core.System.Instance.IntervalService.StoreInfo(info, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se intentaba cambiar el estado de conexión del agente {0}: {1}", agent, ex);
				throw;
			}
		}

		public void AgentConnect(DomainModel.Agent agent, ConnectionInfo connectionInfo)
		{
			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser null");
			if (connectionInfo == null)
				throw new ArgumentNullException(nameof(connectionInfo), "El connectionInfo no puede ser null");

			try
			{
				AgentDAO.Connect(agent.ID, connectionInfo);

				Dictionary<int, int> subscribedAgentsByQueue = System.Instance.QueueService.GetSubscribedAgentsByAgentQueues(agent).ToDictionary(
						v => v.Key.ID,
						v => v.Value.Length);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily info;
				foreach (var item in subscribedAgentsByQueue)
				{
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = item.Key;
					info.PersonID = 0;
					info.LoggedAgents = 1;
					info.PeekLoggedAgents = item.Value;

					Core.System.Instance.IntervalService.StoreInfo(info, interval);
				}

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = 0;
				info.LoggedAgents = 1;
				info.PeekLoggedAgents = System.Instance.AgentsService.TotalConnectedAgents;

				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				Tracer.TraceInfo("Se conectó al agente {0} a {1} colas", agent, subscribedAgentsByQueue.Count);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se intentaba conectar al agente {0}: {1}", agent, ex);
				throw;
			}
		}

		public void AgentDisconnect(DomainModel.Agent agent, ConnectionInfo connectionInfo)
		{
			if (agent == null)
				throw new ArgumentNullException(nameof(agent), "El agente no puede ser null");
			if (connectionInfo == null)
				throw new ArgumentNullException(nameof(connectionInfo), "El connectionInfo no puede ser null");

			try
			{
				AgentDAO.Disconnect(agent.ID, connectionInfo.SessionID);

				Dictionary<int, int> subscribedAgentsByQueue = System.Instance.QueueService.GetSubscribedAgentsByAgentQueues(agent).ToDictionary(
						v => v.Key.ID,
						v => v.Value.Length - 1);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);
				foreach (var item in subscribedAgentsByQueue)
				{
					var info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = item.Key;
					info.PersonID = 0;
					info.LoggedOutAgents = 1;
					info.PeekLoggedAgents = item.Value;

					Core.System.Instance.IntervalService.StoreInfo(info, interval);
				}

				Tracer.TraceInfo("Se desconectó al agente {0} a {1} colas", agent, subscribedAgentsByQueue.Count);

				if (connectionInfo.Session != null)
				{
					try
					{
						connectionInfo.Session.Abandon();
						Tracer.TraceInfo("Se abandonó la sesión del agente {0}", agent);
					}
					catch { }
				}

				connectionInfo.RealTimeConnectionId = null;
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se intentaba desconectar al agente {0}: {1}", agent, ex);
				throw;
			}
		}

		/// <summary>
		/// Autoriza un mensaje a ser envíado
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a autorizar</param>
		/// <param name="supervisor">El <see cref="DomainModel.User"/> que realizó la autorización</param>
		public async Task AuthorizeMessage(Message message, User supervisor)
		{
			await AuthorizeMessage(message, supervisor, true);
		}

		/// <summary>
		/// Autoriza un mensaje a ser envíado
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> a autorizar</param>
		/// <param name="supervisor">El <see cref="DomainModel.User"/> que realizó la autorización</param>
		/// <param name="updateDb">Indica si se debe actualizar la db y computar métricas (<code>true</code>) o solo computar métricas (<code>false</code>)</param>
		public async Task AuthorizeMessage(Message message, User supervisor, bool updateDb)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message), "El mensaje no puede ser null");
			if (supervisor == null)
				throw new ArgumentNullException(nameof(supervisor), "El supervisor no puede ser null");

			try
			{
				if (updateDb)
					MessageDAO.UpdateAuthorized(message, supervisor.ID);

				await this.PublishReply(message);

				var interval = new Common.Interval(DomainModel.SystemSettings.Instance.IntervalsPerHour);

				DomainModel.Historical.Daily info;

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = message.RepliedBy.ID;
				info.VerifiedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = supervisor.ID;
				info.VerifiedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = message.Queue.ID;
				info.PersonID = 0;
				info.VerifiedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);

				info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = 0;
				info.PersonID = message.RepliedBy.ID;
				info.VerifiedMessages = 1;
				Core.System.Instance.IntervalService.StoreInfo(info, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se autorizaba el mensaje {0}: {1}", message, ex);
				throw;
			}
		}

		/// <summary>
		/// Rechaza una llamada de voz de whatsapp
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que está asignado al agente</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que está rechazando la llamada</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException">Ocurre cuando <paramref name="message"/> es <code>null</code></exception>
		/// <exception cref="ArgumentOutOfRangeException">Ocurre cuando <paramref name="message"/> no es <see cref="SocialServiceTypes.WhatsApp"/></exception>
		public async Task WhatsappVoiceCallReject(Message message, Agent agent, string callId)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				throw new ArgumentOutOfRangeException(nameof(message.SocialServiceType), "El único tipo de mensaje soportado es Whatsapp");

			var @case = message.Case;
			if (@case.CurrentCall == null)
				throw new InvalidOperationException($"El caso {@case.ID} del mensaje {message.ID} no tiene una llamada de voz de whatsapp asociada");

			try
			{
				var socialService = (SocialServices.WhatsApp.WhatsAppService) message.Service.SocialService;
				await socialService.VoiceCallReject(callId, @case);

				var oldStatus = @case.CurrentCall.Status;
				@case.CurrentCall.Status = CallStatuses.Rejected;
				DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.AgentRejected);

				DAL.CaseLogDAO.Insert(@case.ID, CaseStatuses.Open, CaseStatuses.Open, CaseLogTypes.CallRejected, agent, null, null, @case.CurrentCall.ID);

				DAL.AgentLogDAO.Insert(agent.ID, AgentLogTypes.CallRejected, @case.ID, @case.CurrentCall.ID);

				@case.FinishCurrentCall();

				var info = Core.System.Instance.AgentsService.GetConnectionInfo(agent);
				info.CurrentCall = null;

				var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily dailyInfo;
				dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
				dailyInfo.TotalRejectedCalls = 1;
				dailyInfo.PersonID = agent.ID;
				Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);

				if (message.Queue != null)
				{
					var systemQueue = Core.System.Instance.QueueService.GetQueueById(message.Queue.ID);
					systemQueue.RealTimeInfo.RejectedCalls++;
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se rechazaba la llamada de voz de whatsapp {0}: {1}", callId, ex);
				throw;
			}
		}

		/// <summary>
		/// Rechaza una llamada de voz de whatsapp
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que está asignado al agente</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que está rechazando la llamada</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException">Ocurre cuando <paramref name="message"/> es <code>null</code></exception>
		/// <exception cref="ArgumentOutOfRangeException">Ocurre cuando <paramref name="message"/> no es <see cref="SocialServiceTypes.WhatsApp"/></exception>
		public void WhatsappVoiceCallConnected(Message message, Agent agent, string callId)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				throw new ArgumentOutOfRangeException(nameof(message.SocialServiceType), "El único tipo de mensaje soportado es Whatsapp");

			var @case = message.Case;
			if (@case.CurrentCall == null)
				throw new InvalidOperationException($"El caso {@case.ID} del mensaje {message.ID} no tiene una llamada de voz de whatsapp asociada");

			try
			{
				var oldStatus = @case.CurrentCall.Status;
				@case.CurrentCall.Status = CallStatuses.Connected;
				@case.CurrentCall.StartDate = DateTime.Now;
				DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.Connected);

				var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily dailyInfo;
				dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
				dailyInfo.PersonID = agent.ID;
				dailyInfo.TotalAttendedCalls = 1;
				Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);

				DAL.CaseLogDAO.Insert(@case.ID, CaseStatuses.Open, CaseStatuses.Open, CaseLogTypes.CallConnected, agent, null, null, @case.CurrentCall.ID);

				DAL.AgentLogDAO.Insert(agent.ID, AgentLogTypes.CallConnected, @case.ID, @case.CurrentCall.ID);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se marcaba la llamada de voz de whatsapp como conectada {0}: {1}", callId, ex);
				throw;
			}
		}

		/// <summary>
		/// Termina una llamada de voz de whatsapp
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que está asignado al agente</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que está rechazando la llamada</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException">Ocurre cuando <paramref name="message"/> es <code>null</code></exception>
		/// <exception cref="ArgumentOutOfRangeException">Ocurre cuando <paramref name="message"/> no es <see cref="SocialServiceTypes.WhatsApp"/></exception>
		public async Task WhatsappVoiceCallTerminateAsync(Message message, Agent agent, string callId, bool notifyAgent)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				throw new ArgumentOutOfRangeException(nameof(message.SocialServiceType), "El único tipo de mensaje soportado es Whatsapp");

			var @case = message.Case;
			if (@case.CurrentCall == null)
				throw new InvalidOperationException($"El caso {@case.ID} del mensaje {message.ID} no tiene una llamada de voz de whatsapp asociada");

			try
			{
				if (notifyAgent)
				{
					var notification = new DomainModel.AgentNotifications.WhatsappVoiceCallTerminate(agent, message.ID, message.Case.ID, @case.CurrentCall.ID, callId, null);
					await Core.System.Instance.RealTimeService.NotifyAgentAsync(notification);
				}

				var socialService = (SocialServices.WhatsApp.WhatsAppService) message.Service.SocialService;

				var oldStatus = @case.CurrentCall.Status;
				@case.CurrentCall.Status = CallStatuses.Terminated;
				@case.CurrentCall.EndDate = DateTime.Now;
				@case.CurrentCall.EndResponsible = CallEndResponsibles.Agent;
				DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.AgentTerminated);

				DAL.CaseLogDAO.Insert(@case.ID, CaseStatuses.Open, CaseStatuses.Open, CaseLogTypes.CallTerminated, agent, null, null, @case.CurrentCall.ID);

				DAL.AgentLogDAO.Insert(agent.ID, AgentLogTypes.CallTerminated, @case.ID, @case.CurrentCall.ID);

				var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);
				DomainModel.Historical.Daily dailyInfo;
				dailyInfo = DomainModel.Historical.Daily.CreateForInterval(interval);
				dailyInfo.PersonID = agent.ID;

				if (@case.CurrentCall.TotalMutedTime.HasValue)
				{
					dailyInfo.AgentTotalCallMutedTime = @case.CurrentCall.TotalMutedTime.Value;
				}

				if (@case.CurrentCall.TotalConnectionTimeWithoutMute.HasValue)
				{
					dailyInfo.AgentTotalCallTalkingTime = @case.CurrentCall.TotalConnectionTimeWithoutMute.Value;
				}

				if (@case.CurrentCall.TotalConnectionTime != null && @case.CurrentCall.TotalConnectionTime.TotalSeconds > 0)
				{
					dailyInfo.AgentTotalCallTime = Math.Round(@case.CurrentCall.TotalConnectionTime.TotalSeconds);
				}

				if (@case.CurrentCall.TotalWaitingTime != null && @case.CurrentCall.TotalWaitingTime.TotalSeconds > 0)
				{
					dailyInfo.AgentTotalCallWaitingTime = Math.Round(@case.CurrentCall.TotalWaitingTime.TotalSeconds);
				}
				Core.System.Instance.IntervalService.StoreInfo(dailyInfo, interval);

				var info = Core.System.Instance.AgentsService.GetConnectionInfo(agent);
				info.CurrentCall = null;

				@case.FinishCurrentCall();
				await socialService.VoiceCallTerminate(callId, @case);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se terminaba la llamada de voz de whatsapp {0}: {1}", callId, ex);
				throw;
			}
		}

		/// <summary>
		/// Acepta una llamada de voz de whatsapp
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que está asignado al agente</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que está rechazando la llamada</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Datos de la sesión de WebRtc</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException">Ocurre cuando <paramref name="message"/> es <code>null</code></exception>
		/// <exception cref="ArgumentOutOfRangeException">Ocurre cuando <paramref name="message"/> no es <see cref="SocialServiceTypes.WhatsApp"/></exception>
		public async Task WhatsappVoiceCallAccept(Message message, Agent agent, string callId, string sdp)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				throw new ArgumentOutOfRangeException(nameof(message.SocialServiceType), "El único tipo de mensaje soportado es Whatsapp");

			var @case = message.Case;
			if (@case.CurrentCall == null)
				throw new InvalidOperationException($"El caso {@case.ID} del mensaje {message.ID} no tiene una llamada de voz de whatsapp asociada");

			try
			{
				var socialService = (SocialServices.WhatsApp.WhatsAppService) message.Service.SocialService;
				await socialService.VoiceCallAccept(callId, sdp, @case);

				var info = Core.System.Instance.AgentsService.GetConnectionInfo(agent);
				info.CallsStats.AttendedCalls++;

				if (message.Queue != null)
				{
					var systemQueue = Core.System.Instance.QueueService.GetQueueById(message.Queue.ID);
					systemQueue.RealTimeInfo.AttendedCalls++;
				}

				if (@case.CurrentCall.Status == CallStatuses.New)
				{
					var oldStatus = @case.CurrentCall.Status;
					@case.CurrentCall.Status = CallStatuses.Connecting;
					DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.AgentAccepted);
				}
				else
				{
					Tracer.TraceVerb("No se cambia el estado de la llamada {0} del caso {1} porque ya estaba en estado Conectando, seguramente por un pre-accept", @case.CurrentCall.ID, @case.ID);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se aceptaba la llamada de voz de whatsapp {0}: {1}", callId, ex);
				throw;
			}
		}

		/// <summary>
		/// Pre-Acepta una llamada de voz de whatsapp
		/// </summary>
		/// <param name="message">El <see cref="DomainModel.Message"/> que está asignado al agente</param>
		/// <param name="agent">El <see cref="DomainModel.Agent"/> que está rechazando la llamada</param>
		/// <param name="callId">El código de llamada de WhatsApp</param>
		/// <param name="sdp">Datos de la sesión de WebRtc</param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException">Ocurre cuando <paramref name="message"/> es <code>null</code></exception>
		/// <exception cref="ArgumentOutOfRangeException">Ocurre cuando <paramref name="message"/> no es <see cref="SocialServiceTypes.WhatsApp"/></exception>
		public async Task WhatsappVoiceCallPreAccept(Message message, Agent agent, string callId, string sdp)
		{
			if (message == null)
				throw new ArgumentNullException(nameof(message));

			if (message.SocialServiceType != SocialServiceTypes.WhatsApp)
				throw new ArgumentOutOfRangeException(nameof(message.SocialServiceType), "El único tipo de mensaje soportado es Whatsapp");

			var @case = message.Case;
			if (@case.CurrentCall == null)
				throw new InvalidOperationException($"El caso {@case.ID} del mensaje {message.ID} no tiene una llamada de voz de whatsapp asociada");

			try
			{
				var socialService = (SocialServices.WhatsApp.WhatsAppService) message.Service.SocialService;
				await socialService.VoiceCallPreAccept(callId, sdp, @case);

				var oldStatus = @case.CurrentCall.Status;
				@case.CurrentCall.Status = CallStatuses.Connecting;
				DAL.CallDAO.UpdateStatus(@case.CurrentCall, oldStatus, CallLogTypes.AgentAccepted);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló cuando se pre-aceptaba la llamada de voz de whatsapp {0}: {1}", callId, ex);
				throw;
			}
		}

		/// <summary>
		/// Realiza un alta masiva de agentes
		/// </summary>
		/// <param name="agents">Una enumeración de <see cref="DomainModel.Agent"/> con los agentes a crear</param>
		/// <param name="agentGroup">Un <see cref="DomainModel.AgentGroup"/> con los datos del grupo a partir del cual se le asignarán las propiedades
		/// a los agentes</param>
		/// <param name="sendMail">Indica si se enviará mail a los agentes indicando sus datos de acceso</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task CreateAgents(IEnumerable<DomainModel.Agent> agents, DomainModel.AgentGroup agentGroup, bool sendMail)
		{
			if (agents == null)
				throw new ArgumentNullException(nameof(agents));
			if (agentGroup == null)
				throw new ArgumentNullException(nameof(agentGroup));

			if (!agents.Any())
				return null;

			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				var allPersons = new List<DomainModel.Person>();
				allPersons.AddRange(DomainModel.Cache.Instance.GetList<DomainModel.Agent>());
				allPersons.AddRange(DomainModel.Cache.Instance.GetList<DomainModel.User>());

				var failed = new List<dynamic>();

				int count = agents.Count();
				int index = 1;
				Tracer.TraceVerb("Comienzo del proceso de alta masiva de agentes con {0} registros", count);

				foreach (var agent in agents)
				{
					if (Licensing.LicenseManager.Instance.License.Configuration.MaxNominalAgents > 0)
					{
						// Le resto a la cantidad total de usuarios los 2 usuarios default de social
						int totalUsers = DomainModel.Cache.Instance.GetList<User>().Where(u => !u.Deleted).Count() - 2;
						int totalAgents = DomainModel.Cache.Instance.GetList<Agent>().Count();

						int totalNominalUsers = totalUsers + totalAgents;

						if (totalNominalUsers >= Licensing.LicenseManager.Instance.License.Configuration.MaxNominalUsers)
						{
							Tracer.TraceVerb("No se puede agregar el agente con username {0} porque ya se llegó al máximo nominal de usuarios [{1}/{2}]", agent.UserName, index, count);
							failed.Add(new
							{
								Index = index,
								Agent = new
								{
									FirstName = agent.FirstName,
									LastName = agent.LastName,
									UserName = agent.UserName,
									Email = agent.Email,
								},
								AlreadyExists = false,
								MaxNominalUsers = true,
								MaxNominalAgents = false,
								Exception = new Exception("Cannot create more users")
							});

							continue;
						}
					}

					if (Licensing.LicenseManager.Instance.License.Configuration.MaxNominalAgents > 0)
					{
						IEnumerable<Agent> allTheAgents = DomainModel.Cache.Instance.GetList<Agent>();
						int totalAgents = agents.Count();

						if (totalAgents >= Licensing.LicenseManager.Instance.License.Configuration.MaxNominalAgents)
						{
							Tracer.TraceVerb("No se puede agregar el agente con username {0} porque ya se llegó al máximo nominal de agentes [{1}/{2}]", agent.UserName, index, count);
							failed.Add(new
							{
								Index = index,
								Agent = new
								{
									FirstName = agent.FirstName,
									LastName = agent.LastName,
									UserName = agent.UserName,
									Email = agent.Email,
								},
								AlreadyExists = false,
								MaxNominalUsers = false,
								MaxNominalAgents = true,
								Exception = new Exception("Cannot create more agentes")
							});

							continue;
						}
					}

					try
					{
						if (allPersons.Any(p =>
							p.UserName.Equals(agent.UserName, StringComparison.InvariantCultureIgnoreCase) ||
							(!string.IsNullOrEmpty(p.Email) && !string.IsNullOrEmpty(agent.Email) && p.Email.Equals(agent.Email, StringComparison.InvariantCultureIgnoreCase))))
						{
							Tracer.TraceVerb("Ya existe un agente con username {0} o mail {1} [{2}/{3}]", agent.UserName, agent.Email, index, count);
							failed.Add(new
							{
								Index = index,
								Agent = new
								{
									FirstName = agent.FirstName,
									LastName = agent.LastName,
									UserName = agent.UserName,
									Email = agent.Email,
								},
								AlreadyExists = true,
								MaxNominalUsers = false,
								MaxNominalAgents = false,
							});
						}
						else
						{
							try
							{
								agentGroup.ApplyConfiguration(agent);

								Tracer.TraceVerb("Dando de alta agente {0} [{1}/{2}]", agent, index, count);
								var agentDAO = new DAL.AgentDAO(agent);
								agentDAO.Insert();

								Tracer.TraceVerb("Asignando el agente {0} al grupo {3} [{1}/{2}]", agent, index, count, agentGroup);
								agentGroup.Agents.Add(agent);
								agent.AgentGroups.Add(agentGroup);
								agentDAO.UpdateGroups();
								agentDAO.UpdateDistribution();

								if (sendMail && !string.IsNullOrEmpty(agent.Email))
								{
									var settings = DomainModel.SystemSettings.Instance.AgentCreatedLoginInformation;
									Dictionary<string, object> templateSettings = new Dictionary<string, object>();
									templateSettings["@@FECHA@@"] = DateTime.Now.ToString("dddd d 'de' MMMM 'del' yyyy", new global::System.Globalization.CultureInfo("es-AR"));
									templateSettings["@@NOMBRE@@"] = agent.FirstName;
									templateSettings["@@APELLIDO@@"] = agent.LastName;
									templateSettings["@@USERNAME@@"] = agent.UserName;
									templateSettings["@@CLAVE@@"] = agent.Password;
									templateSettings["@@URL@@"] = DomainModel.SystemSettings.Instance.WebAgentURL;

									DomainModel.SystemSettings.Instance.SendMailMessage(settings.Subject, agent.Email, settings.Template, templateSettings);
								}

								allPersons.Add(agent);
							}
							catch (Exception ex)
							{
								Tracer.TraceVerb("Falló dar de alta el agente {0} [{1}/{2}]: {3}", agent, index, count, agentGroup, ex);

								failed.Add(new
								{
									Index = index,
									Agent = new
									{
										FirstName = agent.FirstName,
										LastName = agent.LastName,
										UserName = agent.UserName,
										Email = agent.Email,
									},
									AlreadyExists = false,
									MaxNominalUsers = false,
									MaxNominalAgents = false,
									Exception = ex
								});
							}
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceVerb("Falló dar de alta el agente {0} [{1}/{2}]: {3}", agent, index, count, agentGroup, ex);

						failed.Add(new
						{
							Index = index,
							Agent = new
							{
								FirstName = agent.FirstName,
								LastName = agent.LastName,
								UserName = agent.UserName,
								Email = agent.Email,
							},
							AlreadyExists = false,
							MaxNominalUsers = false,
							MaxNominalAgents = false,
							Exception = ex
						});
					}

					index++;
				}

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, failed);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Realiza un alta masiva de etiquetas
		/// </summary>
		/// <param name="tags">Una enumeración de <see cref="DomainModel.Tag"/> con las etiquetas a crear</param>
		/// <param name="tagGroups">Una enumeración de <see cref="short"/> con los ids de los grupos de etiquetas a los que perteneceran las etiquetas creadas</param>
		/// <param name="queues">Una enumeración de <see cref="int"/> con los ids de las colas a las que perteneceran las etiquetas creadas</param>
		/// <param name="replace">Indica si los datos de los registros a modificar se combinaran con los nuevos datos o si seran reemplazados</param>
		/// <param name="user">El <see cref="DomainModel.User"/> que realizó la importacion masiva</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task CreateTags(IEnumerable<DomainModel.Tag> tags, short[] tagGroups, int[] queues, bool replace, User user)
		{
			if (tags == null)
				throw new ArgumentNullException(nameof(tags));

			if (!tags.Any())
				return null;

			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				var allTags = new List<DomainModel.Tag>();
				allTags.AddRange(DomainModel.Cache.Instance.GetList<DomainModel.Tag>());
				var failed = new List<dynamic>();

				Tracer.TraceVerb("Comienzo del proceso de alta masiva de etiquetas");

				foreach (var tag in tags)
				{
					var tagToModify = allTags.FirstOrDefault(t => t.FullName.Equals(tag.FullName, StringComparison.InvariantCultureIgnoreCase) && t.TreeLevel == tag.TreeLevel);

					if (tagToModify == null)
					{
						SaveTag(tag, allTags, failed, user, tagGroups, queues);
					}
					else
					{
						tag.ID = tagToModify.ID;
					}

					foreach (var child1 in tag.ChildTags)
					{
						if (child1.Parent != null && failed.Find(f => f.Tag.Name == child1.Parent.Name) != null)
						{
							Tracer.TraceVerb("No se pudo procesar la etiqueta {0} por fallo en etiqueta {1}. No se procesaran las etiquetas hijas", child1.Name, child1.Parent.Name);

							failed.Add(new
							{
								Tag = new
								{
									Name = child1.Name,
									ParentName = child1.Parent.Name
								},
								Exception = "configuration-tags-import-error-child-tags"
							});

							continue;
						}

						tagToModify = allTags.FirstOrDefault(t => t.FullName.Equals(child1.FullName, StringComparison.InvariantCultureIgnoreCase) && t.TreeLevel == child1.TreeLevel);

						if (tagToModify == null)
						{
							SaveTag(child1, allTags, failed, user, tagGroups, queues);
						}
						else
						{
							if (!tagToModify.HasChildTags)
							{
								UpdateTag(child1, tagToModify.ID, allTags, failed, user, tagGroups, queues, replace);
							}
							else
							{
								child1.ID = tagToModify.ID;
								child1.TagGroups.AddRange(tagToModify.TagGroups);
							}
						}

						if (child1.HasChildTags)
						{
							foreach (var child2 in child1.ChildTags)
							{
								if (child2.Parent != null && failed.Find(f => f.Tag.Name == child2.Parent.Name) != null)
								{
									Tracer.TraceVerb("No se pudo procesar la etiqueta {0} por fallo en etiqueta {1}. No se procesaran las etiquetas hijas", child2.Name, child2.Parent.Name);

									failed.Add(new
									{
										Tag = new
										{
											Name = child2.Name,
											ParentName = child2.Parent.Name
										},
										Exception = "configuration-tags-import-error-child-tags"
									});

									continue;
								}

								tagToModify = allTags.FirstOrDefault(t => t.FullName.Equals(child2.FullName, StringComparison.InvariantCultureIgnoreCase) && t.TreeLevel == child2.TreeLevel);

								if (tagToModify == null)
								{
									SaveTag(child2, allTags, failed, user, tagGroups, queues);
								}
								else
								{
									if (!tagToModify.HasChildTags)
									{
										UpdateTag(child2, tagToModify.ID, allTags, failed, user, tagGroups, queues, replace);
									}
									else
									{
										child2.ID = tagToModify.ID;
										child2.TagGroups.AddRange(tagToModify.TagGroups);
									}
								}

								if (child2.HasChildTags)
								{

									foreach (var child3 in child2.ChildTags)
									{
										if (child3.Parent != null && failed.Find(f => f.Tag.Name == child3.Parent.Name) != null)
										{
											Tracer.TraceVerb("No se pudo procesar la etiqueta {0} por fallo en etiqueta {1}. No se procesaran las etiquetas hijas", child3.Name, child3.Parent.Name);

											failed.Add(new
											{
												Tag = new
												{
													Name = child3.Name,
													ParentName = child3.Parent.Name
												},
												Exception = "configuration-tags-import-error-child-tags"
											});

											continue;
										}

										tagToModify = allTags.FirstOrDefault(t => t.FullName.Equals(child3.FullName, StringComparison.InvariantCultureIgnoreCase) && t.TreeLevel == child3.TreeLevel);

										if (tagToModify == null)
										{
											SaveTag(child3, allTags, failed, user, tagGroups, queues);
										}
										else
										{
											UpdateTag(child3, tagToModify.ID, allTags, failed, user, tagGroups, queues, replace);
										}
									}
								}
							}
						}
					}
				}

				if (Core.System.Instance.EventsService != null)
				{
					var @event = new DomainModel.Events.DomainObjectSyncEvent();
					@event.Action = SystemActionTypes.Edit;
					@event.EntityType = SystemEntityTypes.Labels;
					@event.EntityID = null;
					@event.MoreInfo = "Massive";
					Core.System.Instance.EventsService.PublishEvent(@event);
				}

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, failed);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Realiza la actualización de los componentes de sistema
		/// </summary>
		/// <param name="settings">Un <see cref="DomainModel.SystemUpdateSettings"/> con los datos los componentes que se actualizarán</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task UpdateSystem(SystemUpdateSettings settings)
		{
			if (settings == null)
				throw new ArgumentNullException(nameof(settings));

			if (settings.Components == null || settings.Components.Length == 0)
				throw new ArgumentException("Debe especificar al menos un componente a actualizar", nameof(settings));

			var backup = false;
			for (var i = 0; i < settings.Components.Length; i++)
			{
				var component = settings.Components[i];
				if (string.IsNullOrEmpty(component.ZipFile))
					throw new ArgumentException(string.Format("Debe especifcar el archivo zip del elemento {0}", i), nameof(settings));
				if (!File.Exists(component.ZipFile))
					throw new ArgumentException(string.Format("El archivo zip {0} del elemento {1} no existe", component.ZipFile, i), nameof(settings));
				if (component.Backup && string.IsNullOrEmpty(settings.BackupsDir))
					throw new ArgumentException("El directorio de backup no puede ser vacío", nameof(settings));

				backup |= component.Backup;
			}

			if (backup)
			{
				try
				{
					Directory.CreateDirectory(settings.BackupsDir);
				}
				catch (Exception ex)
				{
					throw new ArgumentException("No se pudo crear el directorio para realizar backups", nameof(settings), ex);
				}
			}

			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				Tracer.TraceVerb("Comienzo de la tarea de actualización del sistema");

				var failed = new List<dynamic>();

				if (backup)
				{
					Tracer.TraceVerb("Tarea de actualización del sistema -> Backup");

					var dateForBackupFiles = DateTime.Now;
					for (var i = 0; i < settings.Components.Length; i++)
					{
						var component = settings.Components[i];
						if (component.Backup)
						{
							if (Directory.Exists(component.DestDirectory))
							{
								string backupFileName = Path.Combine(settings.BackupsDir, component.GenerateBackupFileName(dateForBackupFiles));

								try
								{
									CreateZipFromDirectory(component.DestDirectory, backupFileName, CompressionLevel.Optimal, false, (filename) =>
									{
										return component.ShouldIncludeFile(filename);
									});

									Tracer.TraceInfo("Se generó el archivo de backup {0}", backupFileName);
								}
								catch (Exception ex)
								{
									failed.Add(new
									{
										SubTask = "backup",
										Component = component,
										Success = false,
										Ex = ex
									});
									Tracer.TraceError("Falló crear el archivo de backup {0} del componente {1} del índice {2}: {3}", backupFileName, component.Type, i, ex);
								}
							}
							else
							{
								failed.Add(new
								{
									SubTask = "backup",
									Component = component,
									Success = false,
									Error = "La carpeta no existe"
								});

								Tracer.TraceError("No se crea el archivo de backup del componente {0} del índice {1} porque la carpeta {2} no existe", component.Type, i, component.DestDirectory);
							}
						}
					}
				}

				Tracer.TraceVerb("Tarea de actualización del sistema -> Descomprimir y pisar archivos");

				for (var i = 0; i < settings.Components.Length; i++)
				{
					var component = settings.Components[i];

					component.StopRelatedServices();

					// Normalizes the path.
					var extractPath = Path.GetFullPath(component.DestDirectory);

					// Ensures that the last character on the extraction path
					// is the directory separator char. 
					// Without this, a malicious zip file could try to traverse outside of the expected
					// extraction path.
					if (!extractPath.EndsWith(Path.DirectorySeparatorChar.ToString(), StringComparison.Ordinal))
						extractPath += Path.DirectorySeparatorChar;

					if (!Directory.Exists(extractPath))
					{
						try
						{
							Directory.CreateDirectory(extractPath);
						}
						catch (Exception ex)
						{
							failed.Add(new
							{
								SubTask = "overwrite",
								Component = component,
								Success = false,
								Ex = ex
							});
							Tracer.TraceError("Falló crear el directorio {0} para el componente {1} del índice {2}: {3}", extractPath, component.Type, i, ex);

							continue;
						}
					}

					try
					{
						var destDirectoryName = new DirectoryInfo(extractPath).Name;

						using (ZipArchive archive = global::System.IO.Compression.ZipFile.OpenRead(component.ZipFile))
						{
							foreach (ZipArchiveEntry entry in archive.Entries)
							{
								var fullName = entry.FullName;
								if (fullName.StartsWith($"{destDirectoryName}/", StringComparison.OrdinalIgnoreCase))
									fullName = fullName.Substring($"{destDirectoryName}/".Length);

								if (string.IsNullOrEmpty(fullName))
									continue;

								if (fullName.EndsWith("/"))
								{
									// Gets the full path to ensure that relative segments are removed.
									string destinationPath = Path.GetFullPath(Path.Combine(extractPath, fullName));

									if (!Directory.Exists(destinationPath))
									{
										try
										{
											Directory.CreateDirectory(destinationPath);
										}
										catch (Exception ex)
										{
											Tracer.TraceError("Falló crear la subcarpeta {5} al descomprimir el archivo ZIP {0} en el directorio {1} para el componente {2} del índice {3}: {4}", component.ZipFile, extractPath, component.Type, i, ex, destinationPath);
										}
									}
								}
								else
								{
									// Gets the full path to ensure that relative segments are removed.
									string destinationPath = Path.GetFullPath(Path.Combine(extractPath, fullName));
									// Ordinal match is safest, case-sensitive volumes can be mounted within volumes that
									// are case-insensitive.
									if (destinationPath.StartsWith(extractPath, StringComparison.Ordinal))
									{
										if (component.ShouldExtractFile(fullName))
										{
											entry.ExtractToFile(destinationPath, true);
										}
										else
										{
											component.HandleFile(entry, destinationPath);
										}
									}
								}
							}
						}

						Tracer.TraceError("Se descomprimió el archivo ZIP {0} en el directorio {1} para el componente {2} del índice {3}", component.ZipFile, extractPath, component.Type, i);
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló descomprimir el archivo ZIP {0} en el directorio {1} para el componente {2} del índice {3}: {4}", component.ZipFile, extractPath, component.Type, i, ex);
						failed.Add(new
						{
							SubTask = "overwrite",
							Component = component,
							Success = false,
							Ex = ex
						});
					}
				}

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, failed);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Ejecuta una tarea invocando un método
		/// </summary>
		/// <param name="method">Un <see cref="Func{TResult}"/> con la función que se invocará</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task RunTask(Func<bool> method)
		{
			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				var result = method();

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, result);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Realiza un alta masiva de respuestas predefinidas
		/// </summary>
		/// <param name="service">Un <see cref="DomainModel.Service"/> con el servicio al cual se agregarán las respuestas predefinidas</param>
		/// <param name="predefinedAnswers">Una enumeración de <see cref="DomainModel.Template"/> con las respuestas predefinidas a crear</param>
		/// <param name="user">Un <see cref="DomainModel.User"/> que indica el usuario que está realizando la operación</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task CreatePredefinedAnswers(DomainModel.Service service, IEnumerable<DomainModel.Template> predefinedAnswers, DomainModel.User user)
		{
			if (service == null)
				throw new ArgumentNullException(nameof(service));
			if (predefinedAnswers == null)
				throw new ArgumentNullException(nameof(predefinedAnswers));
			if (user == null)
				throw new ArgumentNullException(nameof(user));

			if (!predefinedAnswers.Any())
				return null;

			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				var allPredefinedAnswers = service.Answers;

				var failed = new List<dynamic>();

				int count = predefinedAnswers.Count();
				int index = 1;
				Tracer.TraceVerb("Comienzo del proceso de alta masiva de respuestas predefinidas con {0} registros", count);
				foreach (var predefinedAnswer in predefinedAnswers)
				{
					if (allPredefinedAnswers.Any(p => p.Name.Equals(predefinedAnswer.Name, StringComparison.InvariantCultureIgnoreCase)))
					{
						Tracer.TraceVerb("Ya existe una respuesta predefinida con nombre {0} [{1}/{2}]", predefinedAnswer.Name, index, count);
						failed.Add(new
						{
							Index = index,
							PredefinedAnswer = new
							{
								Name = predefinedAnswer.Name,
								Body = predefinedAnswer.Body
							},
							AlreadyExists = true
						});
					}
					else
					{
						try
						{

							Tracer.TraceVerb("Dando de alta respuesta predefinida {0} [{1}/{2}]", predefinedAnswer, index, count);
							var dao = new DAL.TemplateDAO(predefinedAnswer);
							dao.Insert();

							Tracer.TraceVerb("Asignando el respuesta predefinida {0} al servicio {3} [{1}/{2}]", predefinedAnswer, index, count, service);
							service.Answers.Add(predefinedAnswer);

							var newParameters = predefinedAnswer.AsDictionary(true);
							UserLogDAO.Insert(user, SystemEntityTypes.PredefinedAnswers, SystemActionTypes.Add, null, newParameters, predefinedAnswer.ID, predefinedAnswer.Name);
						}
						catch (Exception ex)
						{
							Tracer.TraceVerb("Falló dar de alta el respuesta predefinida {0} [{1}/{2}]: {3}", predefinedAnswer, index, count, ex);

							failed.Add(new
							{
								Index = index,
								PredefinedAnswer = new
								{
									Name = predefinedAnswer.Name,
									Body = predefinedAnswer.Body
								},
								AlreadyExists = false,
								Exception = ex
							});
						}
					}

					index++;
				}

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, failed);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Realiza un alta masiva de respuestas predefinidas
		/// </summary>
		/// <param name="service">Un <see cref="DomainModel.Service"/> con el servicio al cual se agregarán las respuestas predefinidas</param>
		/// <param name="predefinedAnswers">Una enumeración de <see cref="DomainModel.Template"/> con las respuestas predefinidas a crear</param>
		/// <param name="user">Un <see cref="DomainModel.User"/> que indica el usuario que está realizando la operación</param>
		/// <returns>El código de tarea creada</returns>
		public global::System.Threading.Tasks.Task CreatePredefinedAnswers(DomainModel.Queue queue, IEnumerable<DomainModel.Template> predefinedAnswers, DomainModel.User user)
		{
			if (queue == null)
				throw new ArgumentNullException(nameof(queue));
			if (predefinedAnswers == null)
				throw new ArgumentNullException(nameof(predefinedAnswers));
			if (user == null)
				throw new ArgumentNullException(nameof(user));

			if (!predefinedAnswers.Any())
				return null;

			var task = global::System.Threading.Tasks.Task.Run(() =>
			{
				var allPredefinedAnswers = queue.Answers;

				var failed = new List<dynamic>();

				int count = predefinedAnswers.Count();
				int index = 1;
				Tracer.TraceVerb("Comienzo del proceso de alta masiva de respuestas predefinidas con {0} registros", count);
				foreach (var predefinedAnswer in predefinedAnswers)
				{
					if (allPredefinedAnswers.Any(p => p.Name.Equals(predefinedAnswer.Name, StringComparison.InvariantCultureIgnoreCase)))
					{
						Tracer.TraceVerb("Ya existe una respuesta predefinida con nombre {0} [{1}/{2}]", predefinedAnswer.Name, index, count);
						failed.Add(new
						{
							Index = index,
							PredefinedAnswer = new
							{
								Name = predefinedAnswer.Name,
								Body = predefinedAnswer.Body
							},
							AlreadyExists = true
						});
					}
					else
					{
						try
						{

							Tracer.TraceVerb("Dando de alta respuesta predefinida {0} [{1}/{2}]", predefinedAnswer, index, count);
							var dao = new DAL.TemplateDAO(predefinedAnswer);
							dao.Insert();

							Tracer.TraceVerb("Asignando el respuesta predefinida {0} a la cola {3} [{1}/{2}]", predefinedAnswer, index, count, queue);
							queue.Answers.Add(predefinedAnswer);

							var newParameters = predefinedAnswer.AsDictionary(true);
							UserLogDAO.Insert(user, SystemEntityTypes.PredefinedAnswersByQueue, SystemActionTypes.Add, null, newParameters, predefinedAnswer.ID, predefinedAnswer.Name);
						}
						catch (Exception ex)
						{
							Tracer.TraceVerb("Falló dar de alta el respuesta predefinida {0} [{1}/{2}]: {3}", predefinedAnswer, index, count, ex);

							failed.Add(new
							{
								Index = index,
								PredefinedAnswer = new
								{
									Name = predefinedAnswer.Name,
									Body = predefinedAnswer.Body
								},
								AlreadyExists = false,
								Exception = ex
							});
						}
					}

					index++;
				}

				if (this.runningTasks.ContainsKey(global::System.Threading.Tasks.Task.CurrentId.Value))
				{
					global::System.Threading.Tasks.Task removedTask;
					this.runningTasks.TryRemove(global::System.Threading.Tasks.Task.CurrentId.Value, out removedTask);
					this.completedTasks.TryAdd(global::System.Threading.Tasks.Task.CurrentId.Value, failed);
				}
			});

			this.runningTasks.TryAdd(task.Id, task);

			return task;
		}

		/// <summary>
		/// Devuelve si una tarea se está ejecutando
		/// </summary>
		/// <param name="taskId">El código de la tarea</param>
		/// <returns><code>true</code> si se está ejecutando; en caso contrario <code>false</code></returns>
		public bool IsTaskRunning(int taskId)
		{
			return this.runningTasks.ContainsKey(taskId);
		}

		/// <summary>
		/// Devuelve si una tarea está completada
		/// </summary>
		/// <param name="taskId">El código de la tarea</param>
		/// <returns><code>true</code> si está completada; en caso contrario <code>false</code></returns>
		public bool IsTaskCompleted(int taskId)
		{
			return this.completedTasks.ContainsKey(taskId);
		}

		/// <summary>
		/// Devuelve el resultado de ejecución de una tarea
		/// </summary>
		/// <param name="taskId">El código de tarea</param>
		/// <returns>Un <see cref="object"/> si se completó la tarea o <code>null</code> si no terminó</returns>
		public object GetTaskResult(int taskId)
		{
			if (this.completedTasks.ContainsKey(taskId))
			{
				var result = this.completedTasks[taskId];

				object completedResult;
				this.completedTasks.TryRemove(taskId, out completedResult);

				return result;
			}

			return null;
		}

		/// <summary>
		/// Renderiza en HTML un caso
		/// </summary>
		/// <param name="case">El <see cref="DomainModel.Case"/> a renderizar</param>
		/// <returns>El HTML del caso</returns>
		public string RenderMessageCase(DomainModel.Case @case)
		{
			var dom = CsQuery.CQ.CreateFromUrl(string.Format("{0}/reports/case.aspx?caseid={1}", System.Instance.SiteRootWithApplication, @case.ID));
			var head = dom["head"];
			var links = head["link"];
			foreach (var link in links)
			{
				string href = link.Attributes["href"];
				if (href.StartsWith(".."))
					href = href.Replace("..", System.Instance.SiteRootWithApplication);

				if (href.StartsWith("http") &&
					href.IndexOf("font-awesome", 0, StringComparison.InvariantCultureIgnoreCase) == -1 &&
					href.IndexOf("fonts", 0, StringComparison.InvariantCultureIgnoreCase) == -1 &&
					href.IndexOf("jquery.ui", 0, StringComparison.InvariantCultureIgnoreCase) == -1 &&
					href.IndexOf("popover", 0, StringComparison.InvariantCultureIgnoreCase) == -1 &&
					href.IndexOf("tooltip", 0, StringComparison.InvariantCultureIgnoreCase) == -1)
				{
					var style = new CsQuery.CQ(string.Format("<style>{0}</style>", LoadFileContents(href)));
					head.Append(style);
				}
			}

			links.Remove("link");

			string html = dom.Render();
			PreMailer.Net.PreMailer pm = new PreMailer.Net.PreMailer(html, new Uri(System.Instance.SiteRootWithApplication));
			var result = pm.MoveCssInline(false, null, null, false, true);

			dom = CsQuery.CQ.CreateDocument(result.Html);
			var body = dom["body"];
			var divCase = body["#divCase"];

			var imgs = divCase["img"];
			foreach (var img in imgs)
			{
				var src = img.Attributes["src"];
				if (!src.StartsWith("http"))
				{
					src = string.Concat(System.Instance.SiteRoot, src);

					img.Attributes["src"] = src;
				}
			}

			var spans = divCase["span.fa"];
			foreach (var span in spans)
			{
				var parent = new CsQuery.CQ(span.ParentNode);

				if (span.HasClass("fa-user"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/User.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-link"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Groups.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-paper-clip"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/HasAttach.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-telegram"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Telegram.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-facebook"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Facebook.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-facebook-messenger"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/FacebookMessenger.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-twitter-square"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Twitter.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-envelope"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Mail.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-comments"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Chat.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-whatsapp") || span.HasClass("fa-whatsapp-square"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Whatsapp.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-star"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/VIM.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-archive"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Groups.png' />", System.Instance.SiteRootWithApplication)));
				}
				else if (span.HasClass("fa-info-circle"))
				{
					parent.Append(new CsQuery.CQ(string.Format("<img src='{0}/Images/Icons/Info.png' />", System.Instance.SiteRootWithApplication)));
				}
			}

			divCase["div, span, td, th"].Css("font-family", "\"Segoe UI\", Tahoma, Verdana, Arial, sans-serif");

			return divCase.RenderSelection();
		}

		/// <summary>
		/// Carga el contenido de un documento de una URL
		/// </summary>
		/// <param name="url">La Url a cargar</param>
		/// <returns>El contenido del documento</returns>
		private string LoadFileContents(string url)
		{
			var request = HttpWebRequest.CreateHttp(url);
			using (var response = (HttpWebResponse) request.GetResponse())
			using (var stream = response.GetResponseStream())
			using (var sr = new global::System.IO.StreamReader(stream))
			{
				string html = sr.ReadToEnd();

				return html;
			}
		}

		/// <summary>
		/// Descarga un archivo desde una Url
		/// </summary>
		/// <param name="url">La Url desde donde se descargará el archivo</param>
		/// <returns>Un <see cref="byte[]"/> con el contenido del archivo adjunto</returns>
		public byte[] DownloadFile(string url)
		{
			return DownloadFile(url, null);
		}

		/// <summary>
		/// Descarga un archivo desde una Url
		/// </summary>
		/// <param name="url">La Url desde donde se descargará el archivo</param>
		/// <param name="headers">Un <see cref="IDictionary{string, string}"/> con los headers a agregar</param>
		/// <returns>Un <see cref="byte[]"/> con el contenido del archivo adjunto</returns>
		public byte[] DownloadFile(string url, IDictionary<string, string> headers)
		{
			string contentType;
			return DownloadFile(url, headers, out contentType);
		}

		/// <summary>
		/// Descarga un archivo desde una Url
		/// </summary>
		/// <param name="url">La Url desde donde se descargará el archivo</param>
		/// <param name="headers">Un <see cref="IDictionary{string, string}"/> con los headers a agregar</param>
		/// <param name="contentType">Cuando retorna, devuelve el Content-Type del archivo</param>
		/// <returns>Un <see cref="byte[]"/> con el contenido del archivo adjunto</returns>
		public byte[] DownloadFile(string url, IDictionary<string, string> headers, out string contentType)
		{
			string filename;
			return DownloadFile(url, headers, out contentType, out filename);
		}

		/// <summary>
		/// Descarga un archivo desde una Url
		/// </summary>
		/// <param name="url">La Url desde donde se descargará el archivo</param>
		/// <param name="headers">Un <see cref="IDictionary{string, string}"/> con los headers a agregar</param>
		/// <param name="contentType">Cuando retorna, devuelve el Content-Type del archivo</param>
		/// <param name="filename">Cuando retorna, devuelve el nombre del archivo</param>
		/// <returns>Un <see cref="byte[]"/> con el contenido del archivo adjunto</returns>
		public byte[] DownloadFile(string url, IDictionary<string, string> headers, out string contentType, out string filename)
		{
			Tracer.TraceVerb("Se irá a buscar el archivo adjunto a la url: {0}", url);

			filename = null;
			contentType = null;

			try
			{
				byte[] data;

				HttpWebRequest request = (HttpWebRequest) HttpWebRequest.Create(url);
				using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
				{
					contentType = response.ContentType;

					try
					{
						string contentDisposition = response.Headers["Content-Disposition"];
						if (!string.IsNullOrEmpty(contentDisposition) && contentDisposition.StartsWith("attachment; filename=", StringComparison.InvariantCultureIgnoreCase))
						{
							filename = contentDisposition.Substring(contentDisposition.IndexOf("=") + 1);
						}
					}
					catch { }

					using (Stream sr = response.GetResponseStream())
					using (MemoryStream ms = new MemoryStream())
					{
						byte[] buffer = new byte[1024];
						int read = sr.Read(buffer, 0, 1024);
						while (read > 0)
						{
							ms.Write(buffer, 0, read);
							read = sr.Read(buffer, 0, 1024);
						}
						data = ms.ToArray();
					}
				}

				filename = CheckDownloadedFileName(url, filename, contentType);

				return data;
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("No se pudo descargar el archivo de la url {0}: {1}", url, ex);
				return null;
			}
		}

		private static string CheckDownloadedFileName(string url, string filename, string contentType)
		{
			if (string.IsNullOrEmpty(filename) && !string.IsNullOrEmpty(url))
			{
				Uri uri = new Uri(url);
				try
				{
					filename = Path.GetFileName(uri.LocalPath);
				}
				catch { }
			}

			if ((string.IsNullOrEmpty(filename) || filename.IndexOf(".") == -1) &&
				!string.IsNullOrEmpty(contentType))
			{
				string extension = null;
				string defaultFileNameWithoutExtension = null;

				if (contentType.StartsWith("image/") ||
					contentType.StartsWith("audio/") ||
					contentType.StartsWith("video/") ||
					contentType.StartsWith("document/") ||
					contentType.StartsWith("text/"))
				{
					var index = contentType.IndexOf(";");
					if (index >= 0)
					{
						contentType = contentType.Substring(0, index);
					}
				}

				switch (contentType.ToLower())
				{
					case "image/jpeg":
					case "image/jpg":
						extension = ".jpg";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/bmp":
						extension = ".bmp";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/gif":
						extension = ".gif";
						defaultFileNameWithoutExtension = "image";
						break;
					case "image/png":
						extension = ".png";
						defaultFileNameWithoutExtension = "image";
						break;
					case "audio/aac":
						extension = ".aac";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/mp4":
						extension = ".mp4";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/mpeg":
						extension = ".mp3";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/ogg":
					case "audio/opus":
						extension = ".ogg";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/wav":
						extension = ".wav";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "audio/x-ms-wma":
						extension = ".wma";
						defaultFileNameWithoutExtension = "audio";
						break;
					case "video/avi":
						extension = ".avi";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/mp4":
						extension = ".mp4";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/mpeg":
						extension = ".mpeg";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/quicktime":
						extension = ".mov";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/x-flv":
						extension = ".flv";
						defaultFileNameWithoutExtension = "video";
						break;
					case "video/x-ms-asf":
						extension = ".asf";
						defaultFileNameWithoutExtension = "video";
						break;
					case "text/plain":
						extension = ".txt";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
						extension = ".xlsx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/excel":
					case "application/vnd.ms-excel":
					case "application/x-msexcel":
						extension = ".xls";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/pdf":
						extension = ".pdf";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
						extension = ".docx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/msword":
						extension = ".doc";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/vnd.openxmlformats-officedocument.presentationml.slideshow":
					case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
					case "application/vnd.openxmlformats-officedocument.presentationml.slide":
					case "application/powerpoint":
					case "application/mspowerpoint":
					case "application/vnd.ms-powerpoint":
					case "application/x-mspowerpoint":
						extension = ".pptx";
						defaultFileNameWithoutExtension = "document";
						break;
					case "application/x-compressed":
					case "application/x-zip-compressed":
					case "application/zip":
					case "multipart/x-zip":
						extension = ".zip";
						defaultFileNameWithoutExtension = "document";
						break;
					default:
						break;
				}

				if (extension != null)
				{
					if (string.IsNullOrEmpty(filename))
						filename = string.Concat(defaultFileNameWithoutExtension, extension);
					else
						filename = string.Concat(filename, extension);
				}
			}

			return filename;
		}

		#endregion
	}
}