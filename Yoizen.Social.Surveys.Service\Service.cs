﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.ServiceProcess;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Yoizen.Common;
using Yoizen.Social.Business;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using static Yoizen.Social.DomainModel.SurveyAnswer;

namespace Yoizen.Social.Surveys.Service
{
	public partial class Service : ServicePreshutdownBase
	{
		#region Constants

		private const int SurveysRefreshInterval = 10;
		
		#endregion
		
		#region Fields

		private System.Threading.Timer timer = null;
		private Dictionary<int, Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService>> services = null;
		private bool stopping = false;
		private int surveysRefreshInterval;
		private int surveysMaxConcurrentCalls;
		private string pathForFiles;
		private Social.Intervals.IntervalsStorageManager intervalStorageManager;
		private DateTime lastReloadCache = DateTime.MinValue;

		private Azure.Messaging.ServiceBus.ServiceBusClient sbClient = null;
		private Azure.Messaging.ServiceBus.ServiceBusProcessor sbProcessorSurveys = null;
		private readonly Dictionary<Guid, Newtonsoft.Json.JsonSerializer> serializers;

		#endregion

		#region Constructors

		public Service()
			: base()
		{
			InitializeComponent();
			this.CanShutdown = false;

			services = new Dictionary<int, Tuple<DomainModel.Service, DomainModel.ISocialService>>();

			try
			{
				if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["SurveysRefreshInterval"], out this.surveysRefreshInterval))
				{
					if (this.surveysRefreshInterval < 2)
						this.surveysRefreshInterval = 2;
				}
				else
				{
					this.surveysRefreshInterval = SurveysRefreshInterval;
				}
			}
			catch
			{
				this.surveysRefreshInterval = SurveysRefreshInterval;
			}

			this.surveysRefreshInterval = this.surveysRefreshInterval * 1000 * 60;

			this.surveysMaxConcurrentCalls = 5;
			try
			{
				if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["Surveys.MaxConcurrentCalls"], out int surveysMaxConcurrentCalls))
				{
					this.surveysMaxConcurrentCalls = surveysMaxConcurrentCalls;
					if (this.surveysMaxConcurrentCalls > 50)
						this.surveysMaxConcurrentCalls = 50;
				}
			}
			catch { }

			this.serializers = new Dictionary<Guid, Newtonsoft.Json.JsonSerializer>();
		}

		#endregion
		
		#region ServiceBase Methods

		protected override void OnStart(string[] args)
		{
			Tracer.TraceInfo("========================================================================");

			CultureInfo culture = new CultureInfo("es-AR");
			System.Threading.Thread.CurrentThread.CurrentCulture = culture;
			System.Threading.Thread.CurrentThread.CurrentUICulture = culture;

			System.Net.ServicePointManager.SecurityProtocol |= System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;

			Thread MyThread = new Thread(new ThreadStart(StartService));
			MyThread.Start();
		}

		protected override void OnStop()
		{
			if (Preshutdown)
				Tracer.TraceInfo("El servicio de encuestas detectó que se apagará el equipo");
			else
				Tracer.TraceInfo("Solicitud de detener el servicio de encuestas");

			this.stopping = true;

#if DEBUG
			Tracer.TraceInfo("Deteniendo timer");
#endif
			if (timer != null)
			{
				timer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
				timer.Dispose();
				timer = null;
			}

			if (this.sbProcessorSurveys != null)
			{
				if (this.sbProcessorSurveys.IsProcessing)
					this.sbProcessorSurveys.StopProcessingAsync().Wait();

				this.sbProcessorSurveys.DisposeAsync().ConfigureAwait(false);
				this.sbProcessorSurveys = null;
			}

			if (this.sbClient != null)
			{
				this.sbClient.DisposeAsync().ConfigureAwait(false);
				this.sbClient = null;
			}

#if DEBUG
			Tracer.TraceInfo("Liberando recursos");
#endif
			if (this.services != null)
			{
				foreach (var service in this.services)
				{
					try
					{
						base.RequestAdditionalTime(2000);
						service.Value.Item2.Dispose();
					}
					catch { }
				}

				this.services.Clear();
				this.services = null;
			}

			base.RequestAdditionalTime(2000);
#if DEBUG
			Tracer.TraceInfo("Guardando valores en la base de datos");
#endif
			try
			{
				DomainModel.SystemStatus.Instance.SurveysServiceStatus.Started = false;
				DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.SurveysServiceStatus.StatusPath));
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo actualizar los valores en la base de datos: {0}", ex);
			}

			if (Preshutdown)
				Tracer.TraceInfo("Finalizó el procesamiento previo a que se apague el equipo");
			else
				Tracer.TraceInfo("Servicio detenido");
		}

		#endregion

		#region Private Methods

#if RUNASPROGRAM
		public void StartService()
#else
		private void StartService()
#endif
		{
			Tracer.TraceInfo("Identificación del equipo: {0}", Licensing.LicenseManager.Identification);
			if (!Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				Tracer.TraceError("No hay licencia para utilizar el producto: {0}", Licensing.LicenseManager.Instance.ValidationException);
				this.Stop();
				return;
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.ReadOnly)
			{
				Tracer.TraceError("La licencia indica que se trabaja en modo lectura. El servicio no se usa");
				this.Stop();
				return;
			}

			DomainModel.Cache.Instance.Enabled = false;

			this.pathForFiles = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().Location), "ServiceFiles");
			if (!Directory.Exists(this.pathForFiles))
			{
				Directory.CreateDirectory(this.pathForFiles);
				Tracer.TraceInfo("Se creó el directorio {0} para guardar archivos", pathForFiles);
			}

			try
			{
				SystemSettingsDAO.GetAll();
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo cargar la configuración del sistema: {0}", ex);
				this.Stop();
				return;
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.UseDatabaseForIntervals)
				this.intervalStorageManager = new Social.Intervals.IntervalsStorageManagerDb();
			else
				this.intervalStorageManager = new Social.Intervals.IntervalsStorageManagerFileSystem();
			this.intervalStorageManager.Initialize();

			if (!LoadServices())
			{
				Tracer.TraceError("No hay servicios configurados para utilizar el producto");
				this.Stop();
				return;
			}

			var clientOptions = new Azure.Messaging.ServiceBus.ServiceBusClientOptions();
			clientOptions.RetryOptions.TryTimeout = TimeSpan.FromSeconds(10);
			clientOptions.TransportType = DomainModel.SystemSettings.Instance.PushNotificationsServiceBusUseWebSockets ?
				Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpWebSockets :
				Azure.Messaging.ServiceBus.ServiceBusTransportType.AmqpTcp;

			Tracer.TraceVerb("ServiceBus con transporte: {0}", clientOptions.TransportType);

			this.sbClient = new Azure.Messaging.ServiceBus.ServiceBusClient(DomainModel.SystemSettings.Instance.Subscription.NamespaceConnectionString, clientOptions);

			var processorOptions = new Azure.Messaging.ServiceBus.ServiceBusProcessorOptions()
			{
				AutoCompleteMessages = false,
				MaxAutoLockRenewalDuration = TimeSpan.FromMinutes(10),
				MaxConcurrentCalls = this.surveysMaxConcurrentCalls
			};

			this.sbProcessorSurveys = this.sbClient.CreateProcessor($"{Licensing.LicenseManager.Instance.License.Configuration.ClientID}-surveys-cases", processorOptions);
			this.sbProcessorSurveys.ProcessErrorAsync += ExceptionReceivedHandler;
			this.sbProcessorSurveys.ProcessMessageAsync += ProcessMessagesAsync;
			this.sbProcessorSurveys.StartProcessingAsync().Wait();

			DomainModel.SystemStatus.Instance.SurveysServiceStatus.Started = true;
			DAL.SystemStatusDAO.Update(string.Format("{0}.Started", DomainModel.SystemStatus.Instance.SurveysServiceStatus.StatusPath));

			if (services.Count > 0)
			{
				Tracer.TraceInfo("Iniciando servicio con {0} servicios de redes sociales configurados", services.Count);

#if RUNASPROGRAM
				DoWork().Wait();
#else
				Tracer.TraceInfo("Se utilizará un tiempo de refresco de {0} milisegundos ({1} segundos)", this.surveysRefreshInterval, this.surveysRefreshInterval / 1000);
				timer = new System.Threading.Timer(async (c) => { await DoWork(); }, null, 5000, this.surveysRefreshInterval);
#endif
			}

			Tracer.TraceInfo("Iniciado correctamente");
			this.EventLog.WriteEntry("Servicio de encuestas iniciado correctamente", System.Diagnostics.EventLogEntryType.Information);
		}

		/// <summary>
		/// Actualiza las entidades que van al Caché
		/// </summary>
		private void ReloadCache()
		{
			if (DateTime.Now.Subtract(this.lastReloadCache).TotalMinutes < 30)
				return;

			Tracer.TraceInfo("Memory used before collection: {0:N0}", GC.GetTotalMemory(false));

			// Collect all generations of memory.
			GC.Collect();
			Tracer.TraceInfo("Memory used after full collection: {0:N0}", GC.GetTotalMemory(true));

			if (this.sbProcessorSurveys.IsProcessing)
			{
				Tracer.TraceInfo("Deteniendo momentáneamente el procesamiento del service bus hasta reinicializar caché");
				this.sbProcessorSurveys.StopProcessingAsync().Wait();
				Tracer.TraceInfo("Se detuvo momentáneamente el procesamiento del service bus hasta reinicializar caché");
			}

			Tracer.TraceInfo("Inicializando Caché");
			DomainModel.Cache.Instance.Enabled = false;

			Tracer.TraceVerb("Obteniendo Servicios");
			var services = DAL.ServiceDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Colas");
			var queues = DAL.QueueDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Encuestas");
			var surveys = DAL.SurveyDAO.GetAll();
			Tracer.TraceVerb("Obteniendo Etiquetas");
			List<DomainModel.Tag> tags = DAL.TagDAO.GetAll(true);
			Tracer.TraceVerb("Obteniendo Agents");
			List<DomainModel.Agent> agents = DAL.AgentDAO.GetAll(false);
			Tracer.TraceVerb("Obteniendo Sitios");
			List<Site> sites = SiteDAO.GetAll();
			foreach (var tag in tags)
			{
				if (tag.Parent != null)
				{
					tag.Parent = tags.Find(t => t.ID == tag.Parent.ID);
					tag.Parent.ChildTags.Add(tag);
				}
			}

			Tracer.TraceVerb("Procesando referencias de {0} agentes", agents.Count());
			foreach (var agent in agents)
			{
				if (agent.Site != null)
				{
					agent.Site = sites.FirstOrDefault(s => s.ID == agent.Site.ID);
				}
			}

			// Le agrega

			DomainModel.Cache.Instance.Enabled = true;
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Service>(services);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Queue>(queues);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Survey>(surveys);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Tag>(tags);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Site>(sites);
			DomainModel.Cache.Instance.UpdateList<Yoizen.Social.DomainModel.Agent>(agents);

			Tracer.TraceInfo("Se finalizó de inicializar el Caché");

			this.lastReloadCache = DateTime.Now;

			if (!this.sbProcessorSurveys.IsProcessing)
			{
				Tracer.TraceInfo("Iniciando nuevamente el procesamiento del service bus luego de haber reinicializado el caché");
				this.sbProcessorSurveys.StartProcessingAsync().Wait();
				Tracer.TraceInfo("Se inició nuevamente el procesamiento del service bus luego de haber reinicializado el caché");
			}
		}

		/// <summary>
		/// Procesa los mensajes del service bus
		/// </summary>
		/// <param name="args">Un <see cref="Azure.Messaging.ServiceBus.ProcessMessageEventArgs"/> con los datos del mensaje del service bus</param>
		/// <returns>Un <see cref="System.Threading.Tasks.Task"/></returns>
		private async System.Threading.Tasks.Task ProcessMessagesAsync(Azure.Messaging.ServiceBus.ProcessMessageEventArgs args)
		{
			// Complete the message so that it is not received again.
			// This can be done only if the queueClient is created in ReceiveMode.PeekLock mode (which is default).

			var body = args.Message.Body.ToString();

			Tracer.TraceVerb("Se recibió mensaje del service bus de la cola de encuestas con número de secuencia {0} y contenido {1}", args.Message.SequenceNumber, body);

			await args.CompleteMessageAsync(args.Message);

			var jBody = Newtonsoft.Json.Linq.JObject.Parse(body);

			if (jBody["type"] == null ||
				jBody["type"].Type != Newtonsoft.Json.Linq.JTokenType.String ||
				!jBody["type"].ToString().Equals("survey"))
			{
				await args.CompleteMessageAsync(args.Message);
				return;
			}

			if (jBody["subtype"] == null ||
				jBody["subtype"].Type != Newtonsoft.Json.Linq.JTokenType.String)
			{
				jBody["subtype"] = "news";
			}

			var subtype = jBody["subtype"].ToString();

			switch (subtype)
			{
				case "news":
					ProcessNews(jBody);
					break;
				case "send":
					await ProcessSend(jBody);
					break;
				default:
					Tracer.TraceWarning("El subtipo {0} no está contemplado. Se ignora", subtype);
					return;
			}
		}

		/// <summary>
		/// Manejador de problemas del Service Bus
		/// </summary>
		/// <param name="exceptionReceivedEventArgs">Un <see cref="Azure.Messaging.ServiceBus.ProcessErrorEventArgs"/> con los datos
		/// del error</param>
		/// <returns>Un <see cref="System.Threading.Tasks.Task"/></returns>
		private System.Threading.Tasks.Task ExceptionReceivedHandler(Azure.Messaging.ServiceBus.ProcessErrorEventArgs exceptionReceivedEventArgs)
		{
			StringBuilder sb = new StringBuilder();
			sb.AppendLine($"Message handler encountered an exception {exceptionReceivedEventArgs.Exception.Message}.");
			var context = exceptionReceivedEventArgs.ErrorSource;
			sb.AppendLine("Exception context for troubleshooting:");
			sb.AppendLine($"- ErrorSource: {exceptionReceivedEventArgs.ErrorSource}");
			sb.AppendLine($"- FullyQualifiedNamespace: {exceptionReceivedEventArgs.FullyQualifiedNamespace}");
			sb.AppendLine($"- Executing EntityPath: {exceptionReceivedEventArgs.EntityPath}");
			Tracer.TraceError("Ocurrió un error obteniendo mensajes de la cola: {0}", sb.ToString());
			return System.Threading.Tasks.Task.CompletedTask;
		}

		/// <summary>
		/// Procesa el envío de una encuesta que llegaron por el Service Bus
		/// </summary>
		/// <param name="jBody">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la información que vino con la encuesta a través del service bus</param>
		private async Task ProcessSend(Newtonsoft.Json.Linq.JObject jBody)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled)
				return;

			if (!DomainModel.SystemSettings.Instance.EnableSurveys)
				return;

			var caseId = jBody["caseId"].ToObject<long>();
			var lastIncomingMessageId = jBody["lastIncomingMessageId"].ToObject<long>();
			var serviceId = jBody["serviceId"].ToObject<int>();
			Guid surveyId;
			if (jBody["surveyId"].Type == Newtonsoft.Json.Linq.JTokenType.Guid)
				surveyId = jBody["surveyId"].ToObject<Guid>();
			else
				surveyId = Guid.Parse(jBody["surveyId"].ToString());
			var byQueue = jBody["byQueue"].ToObject<bool>();
			var entityId = jBody["entityId"].ToObject<int>();
			var closedDateTimeStamp = jBody["closedDate"].ToObject<long>();
			var surveyConfiguration = jBody["configuration"].ToObject<DomainModel.QueueSurveyConfiguration>();

			var survey = DomainModel.Cache.Instance.GetItem<DomainModel.Survey>(surveyId.ToString());
			if (survey == null || !survey.Enabled)
			{
				Tracer.TraceInfo("La encuesta {0} no existe o no está habilitada. NO se puede encuestar el caso {1}", surveyId, caseId);
				return;
			}

			bool shouldGetRelatedInfo = survey.Type == SurveyTypes.ThirdParty;

			var @case = DAL.CaseDAO.GetOneWithoutMessages(caseId, shouldGetRelatedInfo);
			if (@case == null)
			{
				Tracer.TraceInfo("No se encontró el caso {0}, se ignora", caseId);
				return;
			}

			if (@case.Status == CaseStatuses.Open)
			{
				Tracer.TraceInfo("El caso {0} se encuentra abierto. No se lo puede encuestar", caseId);
				return;
			}

			if (@case.ClosedDate == null)
			{
				Tracer.TraceInfo("El caso {0} no tiene fecha de cierre. No se lo puede encuestar", caseId);
				return;
			}

			var closedDate = Common.Conversions.UnixTimeToDateTime(closedDateTimeStamp).ToLocalTime();
			if (Math.Abs(closedDate.Subtract(@case.ClosedDate.Value).TotalSeconds) > 2)
			{
				Tracer.TraceInfo("El caso {0} tiene fecha de cierre {1} pero el pedido de encuesta es con fecha de cierre {2}. No se lo puede encuestar porque seguramente se reabrió y volvió a cerrar", caseId, @case.ClosedDate, closedDate);
				return;
			}

			if (!@case.SurveyShouldSend)
			{
				Tracer.TraceInfo("El caso {0} indica que no se debe enviar la encuesta", caseId);
				return;
			}

			if (!surveyConfiguration.SendIfNewCaseExists && @case.Next != null)
			{
				if (surveyConfiguration.SendIfNewCaseClosedByYflow)
				{
					@case.Next = CaseDAO.GetOneWithoutMessages(@case.Next.ID);
					//Corroboro que este abierto o (en caso que exista un caso nuevo cerrado) que este haya sido cerrado por yFlow, y en caso que haya sido cerrado por sistema, que no tenga Cola ni Agentes asociados (lo que significa que fue cerrado por yFlow por inactividad)
					if (@case.Next.Status == CaseStatuses.Open)
					{
						Tracer.TraceVerb("Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} esta abierto",
							@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
						return;
					}
					else if (@case.Next.ClosedBy != null && @case.Next.ClosedBy != CaseClosingResponsibles.YFlow && !(@case.Next.ClosedBy == CaseClosingResponsibles.System && @case.Next.Queue == null && @case.Next.Agents == 0))
					{
						Tracer.TraceVerb("Se ignora el caso {0} para la encuesta {1} porque el caso nuevo {2} no fue cerrado por yFlow",
							@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
						return;
					}
				}
				else if (surveyConfiguration.SendIfNewCaseHasTag)
				{
					@case.Next = CaseDAO.GetOneWithoutMessages(@case.Next.ID);
					HashSet<int> allTags = new HashSet<int>();

					if (surveyConfiguration.Tags != null && surveyConfiguration.Tags.Length > 0)
					{
						allTags.UnionWith(surveyConfiguration.Tags);
					}

					if (surveyConfiguration.TagGroups != null && surveyConfiguration.TagGroups.Length > 0)
					{
						foreach (var tg in surveyConfiguration.TagGroups)
						{
							var tagGroup = TagGroupDAO.GetOneFromCache((short) tg);

							if (tagGroup != null)
							{
								if (tagGroup.Tags != null && tagGroup.Tags.Count > 0)
								{
									var arrayGroupTags = tagGroup.Tags;
									allTags.UnionWith(arrayGroupTags);
								}
							}
						}
					}

					if (allTags.Count > 0)
					{
						bool hasTags = allTags.Any(tag => @case.Next.Tags.Contains(tag));
						if (!hasTags)
						{
							Tracer.TraceVerb("[SURVEYS] Se ignora el caso {0} para la encuesta {1} ya que el siguiente caso no contiene las etiquetas configuradas", @case.ID, surveyConfiguration.SurveyID);
							return;
						}
					}
				}
				else
				{
					Tracer.TraceVerb("Se ignora el caso {0} para la encuesta {1} porque ya existe un caso nuevo {2}",
						@case.ID, surveyConfiguration.SurveyID, @case.Next.ID);
					return;
				}
			}

			if (surveyConfiguration.DontSendIfLastSurveyAfterMinutes > 0)
			{
				if (@case.Profile == null || !@case.Profile.RetrievedFromDatabase)
					@case.Profile = DAL.SocialUserProfileDAO.GetOne(@case.Profile.ID, false);

				if (@case.Profile != null &&
					@case.Profile.LastSurveySent != null)
				{
					var minutes = DateTime.Now.Subtract(@case.Profile.LastSurveySent.Value).TotalMinutes;
					if (minutes < surveyConfiguration.DontSendIfLastSurveyAfterMinutes)
					{
						Tracer.TraceVerb("Se ignora el caso {0} para la encuesta {1} porque la última encuesta al perfil {2} fue envíada hace {3} minutos y está configurado no enviar dentro de los {4} minutos", @case.ID, surveyConfiguration.SurveyID, @case.Profile, minutes, surveyConfiguration.DontSendIfLastSurveyAfterMinutes);
						return;
					}
				}
			}

			if (!this.services.ContainsKey(serviceId))
			{
				Tracer.TraceError("No se encontró el servicio {0} para ser utilizado para enviar la encuesta del caso {1}", serviceId, caseId);
				return;
			}

			if (@case.SurveySentDate != null)
			{
				Tracer.TraceVerb("Se ignora el caso {0} para la encuesta {1} porque ya se envió la encuesta el {2}", @case.ID, surveyConfiguration.SurveyID, @case.SurveySentDate.Value.ToString("o"));
				return;
			}

			var socialService = this.services[serviceId].Item2;

			//Encuestas de Movisatar
			if (survey.Type == SurveyTypes.Movistar)
			{
				await ProcessMovistarSurvey(@case, survey, socialService);
				Tracer.TraceInfo("Se procesó y notificó el HandOff de la encuesta de movistar para el caso {0}, no se computan metricas", @case.ID);
				return;
			}

			try
			{
				surveyConfiguration.Survey = survey;

				string surveyLink;

				//No acortamos link ya que whatsapp nos permite enviar un mensaje url de boton
				bool useInteractive = @case.Service.SocialServiceType == SocialServiceTypes.WhatsApp &&
								!string.IsNullOrEmpty(surveyConfiguration.InteractiveInvitation) &&
								!string.IsNullOrEmpty(surveyConfiguration.InteractiveInvitationButtonText);
				try
				{
					if (survey.Type == SurveyTypes.ThirdParty && 
						survey.Configuration.ImportantTagFullName && 
						@case.ImportantTag != null)
					{
						@case.ImportantTag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(@case.ImportantTag.ID);
					}

					surveyLink = await DomainModel.Survey.GetSurveyLink(survey, Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID, @case, surveyConfiguration.Expiration, useInteractive);
				}
				catch (Exception ex)
				{
					Tracer.TraceVerb("Se produjo un error al obtener el link para el caso {0}, se continúa con el siguiente caso: {1}", @case.ID, ex);
					return;
				}

				Tracer.TraceVerb("Se irá a buscar los datos del último mensaje entrante del caso {0} cuyo ID es {1}", @case.ID, lastIncomingMessageId);
				var message = DAL.MessageDAO.GetOne(lastIncomingMessageId, new MessageDAO.RelatedEntitiesToRead(false)
				{
					Service = true
				}, false);

				if (message == null)
				{
					Tracer.TraceVerb("Se produjo un error al obtener el mensaje {0} del caso {0}, se ignora", lastIncomingMessageId, caseId);
					return;
				}

				if (message.SocialUser != null)
				{
					if (message.IsReply)
						message.RepliesToSocialUser = DAL.SocialUserDAO.GetOne(message.SocialUser.ID, message.SocialUser.SocialServiceType, false, false);
					else
						message.PostedBy = DAL.SocialUserDAO.GetOne(message.SocialUser.ID, message.SocialUser.SocialServiceType, false, false);
					var socialUser = message.SocialUser;

					var messageSurvey = new DomainModel.Message();

					messageSurvey.Body = !useInteractive ? surveyConfiguration.Invitation.Replace("@@LINK@@", surveyLink) : surveyConfiguration.InteractiveInvitation;
					
					if (messageSurvey.Body.Contains("@@USUARIO@@"))
						messageSurvey.Body = messageSurvey.Body.Replace("@@USUARIO@@", socialUser.Name);
					if (messageSurvey.Body.Contains("@@ALIAS@@"))
						messageSurvey.Body = messageSurvey.Body.Replace("@@ALIAS@@", socialUser.DisplayName);
					messageSurvey.IsDirectMessage = true;
					messageSurvey.RepliesToSocialUser = socialUser;
					messageSurvey.RepliesTo = message;
					messageSurvey.SocialConversationID = message.SocialConversationID;
					if (@case.IsMailCase)
					{
						messageSurvey.Parameters = message.Parameters;
						messageSurvey.RepliesToSocialMessageID = message.SocialMessageID;
					}

					//Insertamos parametros extra para crear el mensaje inractivo de boton URL
					if (useInteractive)
					{
						InsertInteractiveParameters(messageSurvey, surveyLink, surveyConfiguration.InteractiveInvitationButtonText);
					}

					bool sent = false;
					try
					{	
						Tracer.TraceInfo("Enviando la encuesta del caso {0} a través del servicio {1}", @case.ID, socialService.Name);
						await socialService.Send(messageSurvey);
						Tracer.TraceInfo("Se envió la encuesta del caso {0}", @case.ID);
						
						
						if (socialService.SupportsCaseSurveySent)
							await socialService.CaseSurveySent(@case);

						sent = true;
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Falló el envío de la encuesta del caso {0}: {1}", @case.ID, ex);

						if (surveyConfiguration.SendMailIfFailed)
						{
							@case.Profile = SocialUserProfileDAO.GetOne(@case.Profile.ID, false);
							if (!string.IsNullOrEmpty(@case.Profile.PrimaryEmail))
							{
								Tracer.TraceInfo("Se enviará la encuesta por mail al perfil {0}", @case.Profile);

								DomainModel.Settings.EmailSettings settings = new DomainModel.Settings.EmailSettings("Temp");
								settings.Emails = @case.Profile.PrimaryEmail;
								settings.Subject = surveyConfiguration.EmailSubject;
								settings.Template = surveyConfiguration.EmailTemplate;
								settings.From = surveyConfiguration.EmailFrom;

								var templateParameters = new Dictionary<string, object>();
								templateParameters["@@LINK@@"] = surveyLink;

								DomainModel.SystemSettings.Instance.SendMailMessage(settings, templateParameters);
								sent = true;
							}
						}
					}

					if (sent)
					{
						@case.SurveySentDate = DateTime.Now;
						@case.Survey = survey;
						if (byQueue)
							@case.Parameters[DomainModel.Case.SurveyQueueParameter] = entityId.ToString();
						else
							@case.Parameters[DomainModel.Case.SurveyServiceParameter] = entityId.ToString();
						DAL.CaseDAO.UpdateSurvey(@case, survey.ID);

						var interval = new Common.Interval(@case.SurveySentDate.Value, DomainModel.SystemSettings.Instance.IntervalsPerHour);

						int? queueId = null;
						if (@case.Parameters.ContainsKey(DomainModel.Case.SurveyQueueParameter))
							queueId = int.Parse(@case.Parameters[DomainModel.Case.SurveyQueueParameter]);
						else if (@case.Queue != null)
							queueId = @case.Queue.ID;

						DomainModel.Historical.Daily info;
						DomainModel.Historical.DailySurvey infoSurvey;
						DomainModel.Historical.DailyCase infoCase;

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = queueId != null ? queueId.Value : -1;
						info.SurveysSent = 1;
						info.PersonID = 0;
						this.intervalStorageManager.StoreInfo(info, interval);

						info = DomainModel.Historical.Daily.CreateForInterval(interval);
						info.QueueID = 0;
						info.SurveysSent = 1;
						info.PersonID = 0;
						this.intervalStorageManager.StoreInfo(info, interval);

						infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
						infoSurvey.QueueID = queueId != null ? queueId.Value : -1;
						infoSurvey.Sent = 1;
						infoSurvey.SurveyID = survey.ID;
						this.intervalStorageManager.StoreInfo(infoSurvey, interval);

						infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
						infoSurvey.QueueID = 0;
						infoSurvey.Sent = 1;
						infoSurvey.SurveyID = survey.ID;
						this.intervalStorageManager.StoreInfo(infoSurvey, interval);

						infoCase = DomainModel.Historical.DailyCase.CreateForInterval(interval);
						infoCase.SentSurveys = 1;
						this.intervalStorageManager.StoreInfo(infoCase, interval);

						if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
						{
							if (@case.Queue != null && !@case.Queue.RetrievedFromDatabase)
								@case.Queue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(@case.Queue.ID);
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveySent);
						}
					}
					else
					{
						DAL.CaseDAO.UpdateSurveySentFailed(@case.ID);
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("No se pudo enviar la encuesta para el caso {0}: {1}", caseId, ex);
			}
		}

		/// <summary>
		/// Procesa las novedades de encuesta que llegaron por el Service Bus
		/// </summary>
		/// <param name="jBody">Un <see cref="Newtonsoft.Json.Linq.JObject"/> con la información que vino con la encuesta a través del service bus</param>
		private void ProcessNews(Newtonsoft.Json.Linq.JObject jBody)
		{
			if (!Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled)
				return;

			if (!DomainModel.SystemSettings.Instance.EnableSurveys)
				return;

			if (jBody["info"] == null || jBody["info"].Type != Newtonsoft.Json.Linq.JTokenType.Object)
				return;

			var jInfo = (Newtonsoft.Json.Linq.JObject) jBody["info"];

			var partial = jInfo["partial"].ToObject<bool>();
			var jAsnwer = (Newtonsoft.Json.Linq.JObject) jInfo["answer"];
			var surveyId = Guid.Parse(jAsnwer["survey"].ToString());
			var survey = DomainModel.Cache.Instance.GetItem<DomainModel.Survey>(surveyId.ToString());
			if (survey == null)
				return;

			var caseId = jAsnwer["case"].ToObject<long>();

			try
			{
				Newtonsoft.Json.JsonSerializer serializer;

				if (!this.serializers.ContainsKey(surveyId))
				{
					var converter = new DomainModel.SurveyAnswer.SurveyAnswerConverter(survey, Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID);
					serializer = new Newtonsoft.Json.JsonSerializer();
					serializer.Converters.Add(converter);
					this.serializers.Add(surveyId, serializer);
				}
				else
				{
					serializer = this.serializers[surveyId];
				}

				var surveyAnswer = jAsnwer.ToObject<DomainModel.SurveyAnswer>(serializer);

				var @case = DAL.CaseDAO.GetOneWithoutMessages(surveyAnswer.Case.ID, true);
				if (@case == null)
				{
					Tracer.TraceInfo("[SURVEYS] No se encontró el caso {0}, se ignora", surveyAnswer.Case);
					return;
				}

				if (@case.Survey != null && @case.Survey.ID == survey.ID)
					@case.Survey = survey;

				if (@case.SurveySentDate == null)
				{
					if (@case.IsChatCase)
					{
						Tracer.TraceInfo("[SURVEYS] El caso de chat {0} no tenía fecha de envíado. Se utiliza la fecha de inicio de la encuesta", @case.ID);
						@case.SurveySentDate = surveyAnswer.StartDate;
						DAL.CaseDAO.UpdateSurvey(@case, survey.ID);

						if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
						{
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveySent);
						}
					}
					else
					{
						Tracer.TraceInfo("[SURVEYS] No se encontró la fecha de envío de la encuesta del caso {0}, se ignora", surveyAnswer.Case);
						return;
					}
				}

				surveyAnswer.SentDate = @case.SurveySentDate.Value;

				bool alreadyStarted = DAL.SurveyAnswerDAO.Insert(surveyAnswer);
				
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
					DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
				{
					@case.SurveyAnswer = surveyAnswer;

					if (surveyAnswer.Completed)
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveyFinished);
					else
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveyProcessed);
				}

				int? serviceId = null;
				int? queueId = null;
				if (@case.Parameters.ContainsKey(DomainModel.Case.SurveyServiceParameter))
					serviceId = int.Parse(@case.Parameters[DomainModel.Case.SurveyServiceParameter]);
				else if (@case.Parameters.ContainsKey(DomainModel.Case.SurveyQueueParameter))
					queueId = int.Parse(@case.Parameters[DomainModel.Case.SurveyQueueParameter]);
				else if (@case.Queue != null)
					queueId = @case.Queue.ID;
				else if (@case.LastService != null && @case.ContainsOnlyYFlowReplies)
					serviceId = @case.LastService.ID;

				var interval = new Common.Interval(DateTime.Now, DomainModel.SystemSettings.Instance.IntervalsPerHour);  //@case.SurveySentDate.Value

                DomainModel.Historical.Daily info;
				DomainModel.Historical.DailySurvey infoSurvey;
				int started = alreadyStarted ? 0 : 1;
				int finished = (surveyAnswer.Status == SurveyAnswerStatuses.Completed) ? 1 : 0;

                info = DomainModel.Historical.Daily.CreateForInterval(interval);
				info.QueueID = queueId != null ? queueId.Value : -1;
				info.PersonID = 0;
				info.SurveysStarted = started;
				info.SurveysFinished = finished;
				this.intervalStorageManager.StoreInfo(info, interval);
				
				info.QueueID = 0;
				this.intervalStorageManager.StoreInfo(info, interval);

				infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
				infoSurvey.QueueID = queueId != null ? queueId.Value : -1;
				infoSurvey.SurveyID = survey.ID;
                infoSurvey.Started = started;
                infoSurvey.Finished = finished;
                this.intervalStorageManager.StoreInfo(infoSurvey, interval);

				infoSurvey.QueueID = 0;
				this.intervalStorageManager.StoreInfo(infoSurvey, interval);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("[SURVEYS] Se produjo un error al grabar los datos de la encuesta {0} para el caso {1}: {2}", survey, caseId, ex);
			}
		}

		/// <summary>
		/// Carga los servicios a partir de los datos de la licencia y de la base de datos
		/// </summary>
		/// <returns>true si se pudieron configurar; en caso contrario, false</returns>
		private bool LoadServices()
		{
			try
			{
				if (Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
				{
					var domainServices = DAL.ServiceDAO.GetAll(false);
					foreach (var domainService in domainServices)
					{
						if (domainService.Type == DomainModel.ServiceTypes.Chat || 
							domainService.Type == DomainModel.ServiceTypes.IntegrationChat ||
							domainService.Type == DomainModel.ServiceTypes.VideoCall)
							continue;

						if (this.services.ContainsKey(domainService.ID))
							continue;

						var socialService = LoadService(domainService);
						if (socialService != null)
						{
							Tracer.TraceInfo("El servicio {0} será configurado y agregado a la lista de servicios a procesar", domainService.Name);
							var tuple = new Tuple<DomainModel.Service, DomainModel.ISocialService>(domainService, socialService);
							this.services.Add(domainService.ID, tuple);
						}
						else
						{
							Tracer.TraceInfo("El servicio {0} todavía no fue configurado", domainService.Name);
						}
					}
				}
				else
				{
					Licensing.ServiceType[] services = Licensing.LicenseManager.Instance.License.Services;
					if (services == null || services.Length == 0)
						return false;

					foreach (Licensing.ServiceType service in services)
					{
						if (service.Type != DomainModel.ServiceTypes.Chat &&
							service.Type != DomainModel.ServiceTypes.IntegrationChat &&
							service.Type != DomainModel.ServiceTypes.VideoCall)
						{
							if (service != null)
							{
								var tuple = LoadService(service);
								this.services.Add(service.ID, tuple);
							}
							else
							{
								Tracer.TraceInfo("El servicio {0} todavía no fue configurado", service.Name);
								this.services.Add(service.ID, null);
							}
						}
					}
				}

				return true;
			}
			catch (Exception ex)
			{
				Tracer.TraceException("Falló la inicialización de los servicios {0}", ex);
				return false;
			}
		}

		/// <summary>
		/// Intenta carga e inicializar un servicio
		/// </summary>
		/// <param name="service">El <see cref="Licensing.ServiceType"/> que representa un servicio de la licencia</param>
		/// <returns>Devuelve un <see cref="Tuple<DomainModel.Service, ISocialService>"/> con los datos del
		/// servicio de la base de datos y el <see cref="ISocialService"/> inicializado o null en caso de error o
		/// falta de configuración</returns>
		private Tuple<DomainModel.Service, DomainModel.ISocialService> LoadService(Licensing.ServiceType service)
		{
			Tuple<DomainModel.Service, DomainModel.ISocialService> tuple = null;
			try
			{
				Tracer.TraceInfo("Intentando inicializar y configurar el servicio {0}", service.Name);

				var domainService = ServiceDAO.GetOne(service.ID, false);
				if (domainService != null && domainService.Queue != null)
				{
					DomainModel.ISocialService socialService = LoadService(domainService, service.ValidUntil);
					if (domainService.SocialServiceClassType != null)
					{
						tuple = new Tuple<DomainModel.Service, DomainModel.ISocialService>(domainService, socialService);
					}
					else
					{
						Tracer.TraceInfo("El servicio {0} tiene un tipo {1} que no es válido", service.Name, domainService.ClassType);
					}
				}
				else
				{
					Tracer.TraceInfo("El servicio {0} todavía no fue configurado", service.Name);
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló la inicialización del servicio {0} ({1})", service.Name, ex);
			}

			return tuple;
		}

		/// <summary>
		/// Intenta cargar e inicializar un servicio
		/// </summary>
		/// <param name="domainService">El <see cref="DomainModel.Service"/> a inicializar</param>
		/// <returns>Devuelve la instancia de <see cref="ISocialService"/> creada para el servicio indicado</returns>
		private DomainModel.ISocialService LoadService(DomainModel.Service domainService)
		{
			return LoadService(domainService, null);
		}

		/// <summary>
		/// Intenta cargar e inicializar un servicio
		/// </summary>
		/// <param name="domainService">El <see cref="DomainModel.Service"/> a inicializar</param>
		/// <param name="validUntil">Indica hasta cuándo es válido un servicio o <code>null</code> si no expira</param>
		/// <returns>Devuelve la instancia de <see cref="ISocialService"/> creada para el servicio indicado</returns>
		private DomainModel.ISocialService LoadService(DomainModel.Service domainService, DateTime? validUntil)
		{
			if (domainService == null)
				throw new ArgumentNullException(nameof(domainService));

			if (domainService.Type == DomainModel.ServiceTypes.Chat || 
				domainService.Type == DomainModel.ServiceTypes.IntegrationChat ||
				domainService.Type == DomainModel.ServiceTypes.VideoCall)
				throw new ArgumentOutOfRangeException(nameof(domainService), "El servicio no puede ser de tipo chat");

			DomainModel.ISocialService socialService = null;
			try
			{
				Tracer.TraceInfo("Intentando inicializar y configurar el servicio {0}", domainService.Name);

				if (domainService.Queue != null)
				{
					if (domainService.SocialServiceClassType != null)
					{
						socialService = (DomainModel.ISocialService) Activator.CreateInstance(domainService.SocialServiceClassType);
						SocialServiceConfiguration serviceConfiguration = (SocialServiceConfiguration) Activator.CreateInstance(socialService.ConfigurationType, domainService.Configuration);
						SocialServiceStatus serviceStatus = (SocialServiceStatus) Activator.CreateInstance(socialService.StatusType, domainService.Status);

						string servicePathForFiles = Path.Combine(this.pathForFiles, domainService.ID.ToString());
						if (!Directory.Exists(servicePathForFiles))
						{
							Directory.CreateDirectory(servicePathForFiles);
							Tracer.TraceInfo("Se creó el directorio {0} para el servicio {1}", servicePathForFiles, domainService.Name);
						}

						socialService.Initialize(domainService, serviceConfiguration, serviceStatus, validUntil, servicePathForFiles);

						Tracer.TraceInfo("El servicio {0} fue inicializado", domainService.Name);
						
						if (socialService.Status.IsDirty)
						{
							DAL.ServiceDAO.UpdateStatus(domainService, socialService.Status);
							serviceStatus.ApplyChanges();
						}
					}
					else
					{
						Tracer.TraceInfo("El servicio {0} tiene un tipo {1} que no es válido", domainService.Name, domainService.ClassType);
					}
				}
				else
				{
					Tracer.TraceInfo("El servicio {0} todavía no fue configurado", domainService.Name);
				}

				return socialService;
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Falló la inicialización del servicio {0} ({1})", domainService.Name, ex);
				throw ex;
			}
		}

		/// <summary>
		/// Inserta los parametros para el mensaje interactivo de URL
		/// </summary>
		/// <param name="message"></param>
		/// <param name="link"></param>
		/// <param name="buttonText"></param>
		private void InsertInteractiveParameters(Message message, string link, string buttonText)
		{
			message.Parameters["SurveyInteractiveMsg"] = Newtonsoft.Json.JsonConvert.SerializeObject(new
			{
				displayText = buttonText,
				url = link
			});
		}

		/// <summary>
		/// Realiza las tareas de consultas y respuestas a las redes sociales cada vez que expira el tiempo del timer
		/// </summary>
		private async Task DoWork()
		{
			ReloadCache();

#if !RUNASPROGRAM
			timer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
#endif

			if (!this.sbProcessorSurveys.IsProcessing)
				await this.sbProcessorSurveys.StartProcessingAsync();

			if (Licensing.LicenseManager.Instance.IsLicenseValid)
			{
				try
				{
					SystemSettingsDAO.GetAll();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló la actualización de los parámetros del sistema: {0}", ex);
					DomainModel.SystemSettings.Instance.HandleException(ex);

					if (ex.GetType() == typeof(OutOfMemoryException))
						Environment.Exit(1);

#if !RUNASPROGRAM
					timer.Change(this.surveysRefreshInterval, this.surveysRefreshInterval);
#endif

					return;
				}

				try
				{
					SystemStatusDAO.GetAll();
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Falló la actualización de los parámetros de estado del sistema: {0}", ex);
					DomainModel.SystemSettings.Instance.HandleException(ex);

					if (ex.GetType() == typeof(OutOfMemoryException))
						Environment.Exit(1);

#if !RUNASPROGRAM
					timer.Change(this.surveysRefreshInterval, this.surveysRefreshInterval);
#endif

					return;
				}

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowToCreateServices)
				{
					LoadServices();

					foreach (var keyvaluePair in this.services)
					{
						if (this.stopping)
							return;

						Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService> tuple = keyvaluePair.Value;
						this.ProcessSocialService(tuple.Item1, tuple.Item2);
					}
				}
				else
				{
					foreach (var keyvaluePair in this.services)
					{
						var serviceId = keyvaluePair.Key;
						Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService> tuple = keyvaluePair.Value;
						if (tuple == null)
						{
							Licensing.ServiceType[] services = Licensing.LicenseManager.Instance.License.Services;
							var service = services.SingleOrDefault(s => s.ID == serviceId);
							if (service != null)
								tuple = LoadService(service);
							else
								Tracer.TraceWarning("No se encontró el servicio licenciado con código {0}", serviceId);
						}

						if (tuple != null)
						{
							if (this.stopping)
								return;

							this.ProcessSocialService(tuple.Item1, tuple.Item2);
						}
					}
				}

				try
				{
					//Procesamiento de Encuestas
					if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
						DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						var surveys = DomainModel.Cache.Instance.GetList<DomainModel.Survey>().Where(s => s.Enabled);
												
						foreach (var surveyToProcess in surveys)
						{
							try
							{
								switch (surveyToProcess.Type)
								{
									case DomainModel.SurveyTypes.Yoizen:
										Tracer.TraceInfo("Se comenzará el proceso de buscar las encuestas completas de la encuesta {0}", surveyToProcess.Name);
										int completed = await ProcessAnsweredSurveys(surveyToProcess);
										Tracer.TraceInfo("Se finalizó la búsqueda de las encuestas completas de la encuesta {0}, se obtuvieron {1}", surveyToProcess.Name, completed);
										break;
									default:
										Tracer.TraceInfo("Se comenzará ignora el proceso de buscar las encuestas completas de la encuesta {0} por no ser propia", surveyToProcess.Name);
										break;
								}
								
							}
							catch (Exception ex)
							{
								Tracer.TraceError("Se produjo un error al buscar las encuestas completadas: {0}", ex);
							}
						}
					}
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Se produjo un error al realizar el procesamiento de encuestas: {0}", ex);
				}
			}

#if !RUNASPROGRAM
			timer.Change(this.surveysRefreshInterval, this.surveysRefreshInterval);
#endif
		}

		/// <summary>
		/// Hace el procesamiento de un servicio
		/// </summary>
		/// <param name="domainService">El <see cref="DomainModel.Service"/> con los datos del servicio y configuración actualizada</param>
		/// <param name="socialService">El <see cref="ISocialService"/> a procesar</param>
		private void ProcessSocialService(DomainModel.Service domainService, DomainModel.ISocialService socialService)
		{
			if (this.stopping)
				return;

			ReconfigureService(domainService, socialService);
			if (this.stopping)
				return;
		}

		/// <summary>
		/// Reconfigura un servicio ya inicializado
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> con los datos del servicio y configuración actualizada</param>
		/// <param name="socialService">El <see cref="ISocialService"/> a reconfigurar</param>
		private void ReconfigureService(DomainModel.Service service, DomainModel.ISocialService socialService)
		{
			Tracer.TraceInfo("Verificando por nuevas configuraciones del servicio {0}", socialService.Name);

			try
			{
				Yoizen.Social.DomainModel.Service serviceTemp = ServiceDAO.GetOne(service.ID, false);

				if (serviceTemp.Enabled != service.Enabled)
				{
					if (serviceTemp.Enabled)
						Tracer.TraceInfo("El servicio {0} se habilitó", socialService.Name);
					else
						Tracer.TraceInfo("El servicio {0} se deshabilitó", socialService.Name);

					service.Enabled = serviceTemp.Enabled;
				}

				if (serviceTemp.Configuration != service.Configuration)
				{
					Tracer.TraceInfo("Se modificó la configuración del servicio {0}", socialService.Name);

					service.Configuration = serviceTemp.Configuration;
					SocialServiceConfiguration serviceConfiguration = (SocialServiceConfiguration) Activator.CreateInstance(socialService.ConfigurationType, service.Configuration);
					socialService.Reconfigure(service, serviceConfiguration);
				}

				int relatedSocialServiceId;
				if (socialService.RequiresRelatedSocialService(out relatedSocialServiceId))
				{
					foreach (var key in this.services.Keys)
					{
						if (key == relatedSocialServiceId)
						{
							Tuple<Yoizen.Social.DomainModel.Service, DomainModel.ISocialService> tuple = this.services[key];
							if (tuple == null)
								break;

							SocialServiceConnector connector = new SocialServiceConnector(tuple.Item2);
							socialService.SetRelatedSocialService(connector);
							break;
						}
					}
				}

				service.Settings = serviceTemp.Settings;
				service.YFlow = serviceTemp.YFlow;
				service.YFlowSettings = serviceTemp.YFlowSettings;
				/* no lo necesito más ya que voy a buscar las encuestas a la db
				 * if (service.YFlowSettings != null && service.YFlowSettings.Survey != null)
				{
					service.YFlowSettings.Survey = SurveyDAO.GetOne(service.YFlowSettings.Survey.ID);
				}*/
			}
			catch (Exception ex)
			{
				Tracer.TraceException(string.Format("Falló la actualización de la configuración del servicio {0}: {{0}}", socialService.Name), ex);
			}
		}

		/// <summary>
		/// Procesa las respuestas a una encuesta específica
		/// </summary>
		/// <param name="survey">El <see cref="DomainModel.Survey"/> que se irá a buscar</param>
		/// <returns>La cantidad de respuestas procesadas</returns>
		private async Task<int> ProcessAnsweredSurveys(DomainModel.Survey survey)
		{
			IEnumerable<DomainModel.SurveyAnswer> surveyAnswers;
			try
			{
				surveyAnswers = await DomainModel.Survey.GetCompletedSurveys(Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID, survey);
			}
			catch (Exception ex)
			{
				Tracer.TraceError("Se produjo un error al buscar las encuestas completas de la encuesta {0}: {1}", survey, ex);
				return 0;
			}

			foreach (var surveyAnswer in surveyAnswers)
			{
				try
				{
					DomainModel.Case @case = DAL.CaseDAO.GetOneWithoutMessages(surveyAnswer.Case.ID, true);
					if (@case == null)
					{
						Tracer.TraceInfo("No se encontró el caso {0}, se continúa con la siguiente encuesta", surveyAnswer.Case);
						continue;
					}

					if (@case.Survey != null && @case.Survey.ID == survey.ID)
						@case.Survey = survey;

					if (@case.SurveySentDate == null)
					{
						if (@case.IsChatCase)
						{
							Tracer.TraceInfo("El caso de chat {0} no tenía fecha de envíado. Se utiliza la fecha de inicio de la encuesta", @case.ID);
							@case.SurveySentDate = surveyAnswer.StartDate;
							DAL.CaseDAO.UpdateSurvey(@case, survey.ID);

							if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
								DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
							{
								DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveySent);
							}
						}
						else
						{
							Tracer.TraceInfo("No se encontró la fecha de envío de la encuesta del caso {0}, se continúa con la siguiente encuesta", surveyAnswer.Case);
							continue;
						}
					}

					surveyAnswer.SentDate = @case.SurveySentDate.Value;

					DAL.SurveyAnswerDAO.Insert(surveyAnswer);

					if (Licensing.LicenseManager.Instance.License.Configuration.AllowAutomaticExport &&
						DomainModel.SystemSettings.Instance.AutomaticExportSettings.ReportsSettings.ContainsKey(DomainModel.Settings.AutomaticExportSettings.AutomaticExportReportTypes.Cases))
					{
						@case.SurveyAnswer = surveyAnswer;

						if (surveyAnswer.Completed)
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveyFinished);
						else
							DomainModel.SystemSettings.Instance.AutomaticExportSettings.StoreCaseNews(@case, DomainModel.CaseLogTypes.SurveyProcessed);
					}

					int? serviceId = null;
					int? queueId = null;
					if (@case.Parameters.ContainsKey(DomainModel.Case.SurveyServiceParameter))
						serviceId = int.Parse(@case.Parameters[DomainModel.Case.SurveyServiceParameter]);
					else if (@case.Parameters.ContainsKey(DomainModel.Case.SurveyQueueParameter))
						queueId = int.Parse(@case.Parameters[DomainModel.Case.SurveyQueueParameter]);
					else if (@case.Queue != null)
						queueId = @case.Queue.ID;
					else if (@case.LastService != null && @case.ContainsOnlyYFlowReplies)
						serviceId = @case.LastService.ID;

					var interval = new Common.Interval(@case.SurveySentDate.Value, DomainModel.SystemSettings.Instance.IntervalsPerHour);

					DomainModel.Historical.Daily info;
					DomainModel.Historical.DailySurvey infoSurvey;
					
					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = queueId != null ? queueId.Value : -1;
					info.PersonID = 0;
					if (surveyAnswer.StartDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Started)
					{
						info.SurveysStarted = (interval.IsInside(surveyAnswer.StartDate.Value)) ? 1 : 0;
					}
					if (surveyAnswer.FinishDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Completed)
					{
						info.SurveysFinished = (interval.IsInside(surveyAnswer.FinishDate.Value)) ? 1 : 0;
					}
					this.intervalStorageManager.StoreInfo(info, interval);

					info = DomainModel.Historical.Daily.CreateForInterval(interval);
					info.QueueID = 0;
					info.PersonID = 0;
					if (surveyAnswer.StartDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Started)
					{
						info.SurveysStarted = (interval.IsInside(surveyAnswer.StartDate.Value)) ? 1 : 0;
					}
					if (surveyAnswer.FinishDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Completed)
					{
						info.SurveysFinished = (interval.IsInside(surveyAnswer.FinishDate.Value)) ? 1 : 0;
					}
					this.intervalStorageManager.StoreInfo(info, interval);

					infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
					infoSurvey.QueueID = queueId != null ? queueId.Value : -1;
					infoSurvey.SurveyID = survey.ID;
					if (surveyAnswer.StartDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Started)
					{
						infoSurvey.Started = (interval.IsInside(surveyAnswer.StartDate.Value)) ? 1 : 0;
					}

					if (surveyAnswer.FinishDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Completed)
					{
						infoSurvey.Finished = (interval.IsInside(surveyAnswer.FinishDate.Value)) ? 1 : 0;
					}
					this.intervalStorageManager.StoreInfo(infoSurvey, interval);

					infoSurvey = DomainModel.Historical.DailySurvey.CreateForInterval(interval);
					infoSurvey.QueueID = 0;
					infoSurvey.SurveyID = survey.ID;
					if (surveyAnswer.StartDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Started)
					{
						infoSurvey.Started = (interval.IsInside(surveyAnswer.StartDate.Value)) ? 1 : 0;
					}

					if (surveyAnswer.FinishDate != null &&
						surveyAnswer.Status == SurveyAnswer.SurveyAnswerStatuses.Completed)
					{
						infoSurvey.Finished = (interval.IsInside(surveyAnswer.FinishDate.Value)) ? 1 : 0;
					}
					this.intervalStorageManager.StoreInfo(infoSurvey, interval);
				}
				catch (Exception ex)
				{
					Tracer.TraceError("Se produjo un error al grabar los datos de la encuesta {0} para el caso {1}: {2}", survey, surveyAnswer.Case, ex);
				}
			}

			if (surveyAnswers.Any())
			{
				try
				{
					await DomainModel.Survey.InformCompletedSurveysProcessed(Licensing.LicenseManager.Instance.License.Configuration.SurveysClientID, survey);
				}
				catch { }
			}

			return surveyAnswers.Count();
		}


		/// <summary>
		/// Procesa una encuesta de Movistar y notifica a movistar mediante CaseSurveySent
		/// </summary>
		/// <param name="case"></param>
		/// <param name="survey"></param>
		/// <param name="socialService"></param>
		/// <returns></returns>

		private async Task ProcessMovistarSurvey(Case @case, Survey survey, ISocialService socialService)
		{
			try
			{
				Tracer.TraceInfo("Se notificará a movistar que debe enviarse la encuesta del caso {0}", @case.ID);

				@case.Parameters["MovistarSurveyButtons"] = Newtonsoft.Json.JsonConvert.SerializeObject(new
				{
					Button1Payload = survey.Configuration.MovistarButton1Payload,
					Button1Text = survey.Configuration.MovistarButton1Text,
					Button2Payload = survey.Configuration.MovistarButton2Payload,
					Button2Text = survey.Configuration.MovistarButton2Text,
					Button3Payload = survey.Configuration.MovistarButton3Payload,
					Button3Text = survey.Configuration.MovistarButton3Text,
					NoAgent = survey.Configuration.MovistarNoAgent,
					LastAgent = @case.LastPerson != null && @case.LastPerson.Type == DomainModel.PersonTypes.Agent ? (int?) @case.LastPerson.ID : null,
				});

				if (socialService.SupportsCaseSurveySent)
					await socialService.CaseSurveySent(@case);
			}

			catch(Exception ex)
			{
				Tracer.TraceError("Ocurrio un error al procesar la encuesta de Movistar", ex);
			}
		}

		#endregion
	}
}