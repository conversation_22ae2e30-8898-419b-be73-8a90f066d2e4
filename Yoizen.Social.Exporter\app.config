﻿<?xml version="1.0"?>
<configuration>
	<appSettings>
		<add key="SocialURL" value="http://localhost/Social/"/>
		<add key="SocialPathForGeneratedReports" value="C:\Source\Yoizen\Social\Yoizen.Social.Web\Reports\Exported\"/>
		<add key="log4netConfig" value="log4net.config"/>
		<add key="RefreshInterval" value="20000"/>
		<add key="MinutesToAbortReport" value="15"/>
		<add key="ClientSettingsProvider.ServiceUri" value=""/>
		<add key="LicenseFile" value="..\..\..\Yoizen.Social.License.xml"/>
  </appSettings>
  <connectionStrings>
		<add name="LAPTOP-2OUF2JT8" connectionString="data source=(local);User id=**;Password=**;Database=Social.Chat" providerName="System.Data.SqlClient"/>
		<add name="MIEZZI" connectionString="data source=localhost,1433;User id=**;Password=*********;Database=Social.Chat" providerName="System.Data.SqlClient" />
	    <add name="VMWin7" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient"/>
	    <add name="LAPTOP-413V3OLM" connectionString="data source=localhost,1433;User id=**;Password=*********;Database=Social.Chat" providerName="System.Data.SqlClient"/>
	    <add name="MATIAS-PC" connectionString="data source=(local);User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient"/>
		<add name="MALBER-PC" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient"/>
		<add name="YN-MMALBERGIER" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient" />
		<add name="******-pc" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient"/>
		<add name="Maximiliano-PC" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=********;" providerName="System.Data.SqlClient"/>
		<add name="DESKTOP-0794CLP" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient"/>
		<add name="MAXO" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=********;" providerName="System.Data.SqlClient"/>
		<add name="DESKTOP-DVGU4H7" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient"/>
		<add name="LAPTOP-ULG84BQH" connectionString="data source=(local);User id=**;Password=root;Database=Social.Chat" providerName="System.Data.SqlClient"/>
	    <add name="LAPTOP-Q67KLMF9" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=******;" providerName="System.Data.SqlClient"/>
		<add name="DESKTOP-R8CDOPD" connectionString="Data Source=(local);Database=Social.Chat;User ID=**;Password=*****;" providerName="System.Data.SqlClient"/>
		<add name="YN-ALANARI" connectionString="Data Source=(local)\SQLEXPRESS01;Database=Social.Chat;User ID=social;Password=social;" providerName="System.Data.SqlClient" />
		<add name="YN-TVALLEJOS" connectionString="data source=(local);User id=**;Password=**;Database=Social.Chat" providerName="System.Data.SqlClient" />
		<add name="ASUS000178364" connectionString="Data Source=(local)\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******1234;" providerName="System.Data.SqlClient"/>
		<add name="DESKTOP-3KA820C" connectionString="Data Source=DESKTOP-3KA820C\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******" providerName="System.Data.SqlClient" />
	  <add name="YN-MFERNANDEZ" connectionString="Data Source=YN-MFERNANDEZ\SQLEXPRESS;Database=Social.Chat;User ID=**;Password=******" providerName="System.Data.SqlClient" />
	  <add name="NOTEBOOK-25" connectionString="Data Source=NOTEBOOK-25;Database=Social.Chat;User ID=Social;Password=******" providerName="System.Data.SqlClient" />
		<add name="MATIAS-Q" connectionString="data source=MATIAS-Q\SQLEXPRESS;User id=**;Password=******;Database=Social.Chat" providerName="System.Data.SqlClient"/>
		<add name="YN-TPOCHAT" connectionString="data source=(local)\SQLEXPRESS;User id=**;Password=**********;Database=Social.Chat" providerName="System.Data.SqlClient"/>
  </connectionStrings>
	<!--system.net>
		<defaultProxy enabled="true" useDefaultCredentials="false">
			<module type="Yoizen.Social.Business.SocialProxy, Yoizen.Social.Business, Version=2.0.0.0, Culture=neutral, PublicKeyToken=8c5134be849e992c"/>
		</defaultProxy>
	</system.net-->
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30AD4FE6B2A6AEED" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="9.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Business" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Chat" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.DAL" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.DomainModel" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Facebook" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Instagram" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Licensing" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.LinkedIn" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Mail" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Skype" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.SMS" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Telegram" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.Twitter" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Yoizen.Social.WhatsApp" publicKeyToken="8c5134be849e992c" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-9.4.0.0" newVersion="9.4.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="DocumentFormat.OpenXml" publicKeyToken="8fb06cb64d019a17" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-2.10.0.0" newVersion="2.10.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
	</startup>
	<system.web>
		<membership defaultProvider="ClientAuthenticationMembershipProvider">
			<providers>
				<add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri=""/>
			</providers>
		</membership>
		<roleManager defaultProvider="ClientRoleProvider" enabled="true">
			<providers>
				<add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400"/>
			</providers>
		</roleManager>
	</system.web>
</configuration>
