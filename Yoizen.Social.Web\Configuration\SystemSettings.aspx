<%@ Page Title="" Language="C#" MasterPageFile="~/Master.Master" AutoEventWireup="true" CodeBehind="SystemSettings.aspx.cs" Inherits="Yoizen.Social.Web.Configuration.SystemSettings" ValidateRequest="false" Async="true" %>

<%@ MasterType VirtualPath="~/Master.master" %>
<%@ Register assembly="Yoizen.Web.UI" namespace="Yoizen.Web.UI" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="contentplaceholderHead" runat="server">
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.multiselect.filter.css")%>' rel="stylesheet" type="text/css" />
	<link href='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Styles/jquery.cleditor.css")%>' rel="stylesheet" type="text/css" />
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery-ui-1.8.7.custom.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.filter.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.multiselect.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.getUrlParam.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.numeric.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.cleditor.min.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Scripts/jquery.tokeninput.js")%>'></script>
	<script type="text/javascript" src='<%=Yoizen.Social.Web.Helpers.Fingerprint.Tag("~/Configuration/SystemSettings.js")%>'></script>
	<style type="text/css">
		.uiGrid .checkbox { width: 25px !important; }
	</style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="contentplaceholderIzquierda" runat="server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="contentplaceholderTitulo" runat="server">
	<asp:Image runat="server" ImageUrl="~/Images/SystemSettings.png" ImageAlign="AbsMiddle" />
	<span data-i18n="configuration-systemsettings-title">Parámetros del Sistema</span>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="contentplaceholderContenido" runat="server">
	<yoizen:Message ID="messageError" runat="server" Type="Error" Visible="false"/>
	<asp:Panel ID="panelContent" runat="server">
		<yoizen:PopupMessage ID="popupmessageChangesApplied" runat="server" TitleLocalizationKey="configuration-systemsettings-title" Title="Parámetros del Sistema" Height="220" Type="Information" LocalizationKey="configuration-systemsettings-changes_applied">Los cambios a los parámetros del sistema fueron guardados con éxito</yoizen:PopupMessage>
		<yoizen:PopupMessage ID="popupmessageChangesAppliedButCouldntSubscribe" runat="server" TitleLocalizationKey="configuration-systemsettings-title" Title="Parámetros del Sistema" Height="220" Type="Information" LocalizationKey="configuration-systemsettings-changes_applied_but_couldnt_subscribe">
			Los cambios a los parámetros del sistema fueron guardados con éxito pero no pudo realizarse la suscripción a las notificaciones push
		</yoizen:PopupMessage>
		<yoizen:PopupMessage ID="popupmessageWhatsAppServiceVoiceCallRecordingCouldntCreateApp" runat="server" Title="Configuración de Grabaciones de Whatsapp" Height="220" Type="Warning">No se pudo crear la aplicación de grabación en el servidor</yoizen:PopupMessage>
		<yoizen:PopupMessage ID="popupmessageWhatsAppServiceVoiceCallRecordingCouldntCheckApp" runat="server" Title="Configuración de Grabaciones de Whatsapp" Height="220" Type="Warning">No se pudo verificar si existe la aplicación de grabación en el servidor</yoizen:PopupMessage>
		<yoizen:Message runat="server" Type="Warning" Text="Modificar parámetros del sistema afectará el comportamiento" LocalizationKey="configuration-systemsettings-parameters_change-warning" />
		<asp:HiddenField ID="hiddenTab" runat="server"></asp:HiddenField>
		<div style="display: none">
			<div class="seccion" id="divLoading">
				<div class="title">
					<h2 data-i18n="globals-loading">Cargando...</h2>
				</div>
				<div class="contents">
					<div style="text-align: center">
						<i class="fa fa-3x fa-spinner fa-pulse"></i>
					</div>
				</div>
			</div>
		</div>
		<div id="tabsSystemSettings" style="display: none">
			<ul>
				<li>
					<a href="#divBehavior" data-i18n="configuration-systemsettings-behaviour">Comportamiento</a>
				</li>
				<li>
					<a href="#divCases" data-i18n="configuration-systemsettings-cases">Casos</a>
				</li>
				<li>
					<a href="#divFilters" data-i18n="configuration-systemsettings-filters">Filtros</a>
				</li>
				<li runat="server" id="liSocialServices" visible="false">
					<a href="#divSocialServices" data-i18n="configuration-systemsettings-services">Servicios</a>
				</li>
				<li>
					<a href="#divAlerts" data-i18n="configuration-systemsettings-alerts">Alertas</a>
				</li>
				<li>
					<a href="#divGlobalConfig" data-i18n="configuration-systemsettings-global">Global</a>
				</li>
				<li runat="server" id="liMoreServices" visible="false">
					<a href="#divMoreServices" data-i18n="configuration-systemsettings-more_services">Otros Servicios</a>
				</li>
				<li>
					<a href="#divSecurityPolitics" data-i18n="configuration-systemsettings-security_policies">Políticas de Seguridad</a>
				</li>
			</ul>
			<div id="divBehavior">
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-general-title">General</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-default_language">Lenguaje principal</span>:</th>
								<td>
									<asp:DropDownList ID="dropdownlistDefaultLanguage" runat="server">
										<asp:ListItem Value="es" data-i18n="globals-spanish">Español</asp:ListItem>
										<asp:ListItem Value="pt" data-i18n="globals-portuguese">Portugués</asp:ListItem>
										<asp:ListItem Value="en" data-i18n="globals-english">Inglés</asp:ListItem>
									</asp:DropDownList>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderTheme" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label"><span>Tema</span>:</th>
									<td>
										<asp:DropDownList ID="dropdownlistTheme" runat="server">
										</asp:DropDownList>
									</td>
								</tr>
							</asp:PlaceHolder>
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-web_agent_url">URL del agente web</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:HiddenField ID="hiddenWebAgentVersion" runat="server" ClientIDMode="Static"></asp:HiddenField>
													<div class="input-with-icons">
														<asp:TextBox ID="textboxWebAgentURL" runat="server" Width="400" spellcheck="false" ClientIDMode="Static" EnableTheming="false" />
														<i class="icon-status icon-status-spin fa fa-spinner fa-spin"></i>
														<i class="icon-status icon-status-ok fa fa-check-circle"></i>
														<i class="icon-status icon-status-error fa fa-exclamation-triangle"></i>
														<asp:Label ID="labelWebAgentVersion" runat="server" CssClass="more-info" />
													</div>
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWebAgentURL" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-web_agent_url-tip">
													URL donde se accede al agente web
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label"><span data-i18n="configuration-systemsettings-web_agent-allow_outdated">Permitir inicio de sesión de agentes desactualizados</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxWebAgentAllowOutdatedLogins" runat="server" />
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderEncryptMessages" runat="server">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-encrypt_messages">Encriptar (Cifrar) Mensajes</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxEncryptMessages" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="[html]configuration-systemsettings-encrypt_messages-tip">
														Este parámetro indica si los mensajes serán encriptados antes de guardarse en la base de datos.<br />
														Seleccionar esta opción implica que no se podrá buscar mensajes por contenido.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</asp:PlaceHolder>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-use_sent_date">Utilizar Fecha de Envio de mensaje</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxUseSentDate" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-use_sent_date-tip">
													Este parámetro indica si se utilizará la fecha de enviado de un mensaje para encolar. En caso de no 
													marcar esta opción se utilizará la fecha en la que el mensaje ingresó al sistema
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-save_notifications">Guardar Notificaciones</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxDeleteNotifications" runat="server" MaxLength="1" Width="50" TextMode="Number" min="1" max="7" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDeleteNotifications" />
													<asp:RangeValidator runat="server" ControlToValidate="textboxDeleteNotifications" Type="Integer" MinimumValue="1" MaximumValue="7" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-save_notifications-tip">
													Este parámetro indica la cantidad de días que se guardará el historial de Notificaciones. El valor debe estar entre 1 y 7
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div class="seccion collapsable">
					<div class="title">
						<h2><span class="fas fa-lg fa-star"></span> <span data-i18n="configuration-systemsettings-important_messages_handling-title">Manejo de Mensajes importantes (VIM)</span></h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-whitelist_messages_as_vim">Marcar mensajes de la WhiteList como VIM</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxMarkWhiteListMessagesAsVIM" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="[html]configuration-systemsettings-whitelist_messages_as_vim-tip">
													Este parámetro indica si automáticamente se marcarán como VIM (Importante) todos
													aquellos mensajes provenientes de usuarios de la
													<asp:HyperLink runat="server" NavigateUrl="~/Configuration/WhiteList.aspx" Text="WhiteList" data-i18n="configuration-systemsettings-whitelist" />
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label"><span data-i18n="configuration-systemsettings-vim_messages_priority">Manejo de prioridades de mensajes VIM</span>:</th>
								<td class="data">
									<asp:DropDownList ID="dropdownlistPrioritizeVIMOverQueueLevel" runat="server">
										<asp:ListItem Value="0" Selected="True" data-i18n="configuration-systemsettings-priority_over_system">Prioridad sobre el sistema</asp:ListItem>
										<asp:ListItem Value="1" data-i18n="configuration-systemsettings-priority_over_atention_queue">Prioridad sobre la cola de atención</asp:ListItem>
									</asp:DropDownList>
									<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-systemsettings-vim_messages_priority-tip">
										La configuración de este parámetro modifica el comportamiento a la hora de asignar mensajes importantes (VIM) a los agentes.<br />
										<span class="italic">Prioridad sobre el sistema:</span> El sistema asginará primero los mensajes VIM de las colas de atención que el agente tenga asignadas por sobre cualquier mensaje con prioridad normal. En caso de que existan mensajes VIM en una cola con menor prioridad, estos serán asginados con mayor prioridad que los mensajes (No VIM) de colas con mayor prioridad.<br />
										<span class="italic">Prioridad sobre la cola de atención:</span> El sistema asignará mensajes según la prioridad de las colas de atención definida. Los mensajes VIM tendrán prioridad sobre los normales pero únicamente dentro de la cola de atención.
									</yoizen:Message>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<asp:Panel ID="panelAttachments" runat="server" CssClass="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-attached_files-title">Archivos adjuntos</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-attachments_route">Ruta para los adjuntos</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxAttachmentsRoute" runat="server" Width="250" spellcheck="false" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAttachmentsRoute" />
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAttachmentsRoute" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-attachments_route-tip">
													Ruta donde se guardarán los archivos adjuntos recibidos de los servicios que los soportan. Se debe tener permisos en el directorio.
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</asp:Panel>
				<div class="seccion collapsable hiddenAsGateway">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-agents-title">Agentes</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-acd-title">ACD</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-messages_per_agent">Máxima cantidad de mensajes por Agente</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxMaxAssignableMessagesPerUser" runat="server" MaxLength="1" Width="50" TextMode="Number" min="1" max="10" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaxAssignableMessagesPerUser" />
															<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMaxAssignableMessagesPerUser" ValidationExpression="^(\d|10)$" />
															<asp:RangeValidator runat="server" ControlToValidate="textboxMaxAssignableMessagesPerUser" Type="Integer" MinimumValue="1" MaximumValue="10" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-messages_per_agent-tip">
															Este parámetro indica la cantidad de Mensajes que se le puede asignar a un Agente. El valor debe estar entre 1 y 10
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderAllowToConfigureACDBalancing" runat="server" Visible="false">
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-acd_balancing">Balancear entrega de mensaje entre agentes conectados</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxUseACDBalancing" runat="server" ClientIDMode="Static" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-acd_balancing-tip">
																Este parámetro indica si se hará una distribución equitativa de mensajes entre los agentes conectados 
																(disponibles y trabajando) o si asignará a medida que haya agentes disponibles
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator" id="trACDBalancingWithQueueLevels">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-acd_balancing_queue_levels">Consideración de niveles de asignación a colas</span>:</th>
											<td class="data">
												<asp:DropDownList ID="dropdownlistACDBalancingWithQueueLevels" runat="server" ClientIDMode="Static">
													<asp:ListItem Value="0" Selected="True" data-i18n="configuration-systemsettings-consider_queue_assignation">Tener en cuenta niveles de asignación de colas</asp:ListItem>
													<asp:ListItem Value="1" data-i18n="configuration-systemsettings-not_consider_queue_assignation">No considerar los niveles de asignación de colas</asp:ListItem>
												</asp:DropDownList>
												<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="[html]configuration-systemsettings-acd_balancing_queue_levels-tip">
													La configuración de este parámetro modifica el comportamiento a la hora de asignar mensajes en forma balanceada entre los agentes.<br />
													<span class="italic">Tener en cuenta niveles de asignación de colas:</span> El sistema evaluará el nivel de asignación de las colas 
													para considerar si existe otro agente disponible con mayor nivel de prioridad para al cual se pueda asignar mensajes<br />
													<span class="italic">No considerar los niveles de asignación de colas:</span> El sistema evaluará si hay otro agente
													disponible hace más tiempo en las colas del agente al cual se le pueda asignar mensaje sin importar los niveles de distribución
												</yoizen:Message>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator" id="trACDBalancingWithQueueLevelsAndWorking">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-acd_balancing_queue_levels-working">Considerar estado trabajando antes que disponible</span>:</th>
											<td class="data">
												<asp:CheckBox ID="checkboxACDBalancingWithQueueLevelsWorking" runat="server" />
												<yoizen:Message runat="server" Type="Information" style="margin-top: 10px" LocalizationKey="configuration-systemsettings-acd_balancing_queue_levels-working-tip">
													Marcar este campo implica que un agente con nivel X estando en estado Trabajando (y con capacidad de recibir más mensajes) 
													tendrá prioridad por sobre un agente en estado Disponible con un nivel mayor a X
												</yoizen:Message>
											</td>
										</tr>
									</asp:PlaceHolder>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-behaviour-title">Comportamiento</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-mark_as_read">Marcar automáticamente como leído</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxAutoMarkAsReadMessages" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-mark_as_read-tip">
															Este parámetro indica si los mensajes se marcarán automáticamente como leídos en el Agente cuando se le asigne un mensaje.
															En caso de que en simultáneo se le asigne más de un mensaje al agente este parámetro no será tenido en cuenta.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-time_to_discconect_agent">Minutos para desconectar un Agente</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxSessionTimeOut" runat="server" MaxLength="2" Width="50" TextMode="Number" min="2" max="20" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxSessionTimeOut" />
															<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxSessionTimeOut" ValidationExpression="^\d{1,2}$" />
															<asp:RangeValidator runat="server" ControlToValidate="textboxSessionTimeOut" Type="Integer" MinimumValue="2" MaximumValue="20" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-time_to_discconect_agent-tip">
															Este parámetro indica los minutos que espera el sistema para desconectar forzosamente a un agente que no envía
															un Keep Alive. El valor debe estar entre 2 y 20 minutos
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-max_messages">Cantidad de mensajes dentro de casos</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxAgentHistoricMaxMessages" runat="server" MaxLength="2" Width="50" TextMode="Number" min="1" max="20" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentHistoricMaxMessages" />
															<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxAgentHistoricMaxMessages" ValidationExpression="^\d{1,2}$" />
															<asp:RangeValidator runat="server" ControlToValidate="textboxAgentHistoricMaxMessages" Type="Integer" MinimumValue="1" MaximumValue="20" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-max_messages-tip">
															Este parámetro indica la cantidad máxima de mensajes que tendrán los casos y conversaciones que se le devuelve a un Agente cuando 
															se le asignan mensajes. El valor máximo puede ser 20
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-discard_reasons">Motivo de descarte obligatorio</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs timeinfo">
															<asp:CheckBox ID="checkboxAgentMustEnterDiscardReason" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-discard_reasons-tip">
															Este parámetro indica si será obligatorio que los agentes ingresen el motivo
															en el momento de descartar mensajes
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div id="divAllowForwardAction" class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-forward_actions-title">Reenvio mediante acciones</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_forward">Permitir reenviar mensajes</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxAllowForwardAction" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-allow_forward-tip">
															Este parámetro indica si los agentes pueden reenviar mensajes por correo
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-forward_outside_domain">Reenvio mensajes fuera del dominio</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxForwardOutsideDomainAvailable" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-forward_outside_domain-tip">
															Este parámetro indica si se pueden forwardear mensajes por fuera del dominio de la empresa.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trAvailableDomains" style="display: none" rel="trAllowForwardAction">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-allowed_domains">Dominios permitidos</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs checkbox">
															<asp:TextBox ID="textboxAvailableDomains" runat="server" Width="350" ClientIDMode="Static" autocomplete="off" TextMode="MultiLine" Rows="3" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMailAvailableDomains" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-allowed_domains-tip">
															Ingresar los dominios habilitados para envío de mails via forward (Ej.: gmail.com)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderActionsFacebookSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-facebook"></span><span data-i18n="configuration-systemsettings-facebook_forward">Asunto de forward de Facebook</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxFacebookSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-facebook_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Facebook.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsFacebookMessengerSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-facebook-messenger"></span> <span data-i18n="configuration-systemsettings-messenger_forward">Asunto de forward de Messenger</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxFacebookMessengerSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-messenger_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Facebook Messenger.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsTwitterSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-twitter-square"></span> <span data-i18n="configuration-systemsettings-twitter_forward">Asunto de forward de Twitter</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxTwitterSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-twitter_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Twitter.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsWhatsappSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-whatsapp-square"></span> <span data-i18n="configuration-systemsettings-whatsapp_forward">Asunto de forward de WhatsApp</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxWhatsappSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-whatsapp_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de WhatsApp.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsChatSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fa fa-lg fa-comments"></span> <span data-i18n="configuration-systemsettings-chat_forward">Asunto de forward de Chat</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxChatSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-chat_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Chat.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsTelegramSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-telegram"></span> <span data-i18n="configuration-systemsettings-telegram_forward">Asunto de forward de Telegram</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxTelegramSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-telegram_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Telegram.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsInstagramSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-instagram"></span> <span data-i18n="configuration-systemsettings-instagram_forward">Asunto de forward de Instagram</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxInstagramSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-instagram_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Instagram.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsSMSSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fa fa-lg fa-comment"></span> <span data-i18n="configuration-systemsettings-sms_forward">Asunto de forward de SMS</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxSMSSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-sms_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de SMS.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsSkypeSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fab fa-lg fa-skype"></span> <span data-i18n="configuration-systemsettings-skype_forward">Asunto de forward de Skype</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxSkypeSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-skype_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Skype.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderActionsMailSubject" runat="server">
										<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
											<th class="label withdescription"><span class="fa fa-lg fa-envelope"></span> <span data-i18n="configuration-systemsettings-mail_forward">Asunto de forward de Mail</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxMailMaskSubject" runat="server" Width="250" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-mail_forward-tip">
																Este parámetro indica el asunto que se utilizará para el envío del mail de forward de un mensaje de Mail. @@ASUNTO@@ indica el asunto del mail original.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<tr class="dataRow dataRowSeparator" id="trMailMaskBody" rel="trAllowForwardAction">
										<th class="label"><span data-i18n="configuration-systemsettings-forward_mail_template">Plantilla del cuerpo del mail</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxMailMaskBody" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMailMaskBody" />
											<yoizen:Message ID="messageMailMaskBodyFields" runat="server" Type="Information" Small="true" Style="margin-top: 10px" ClientIDMode="Static">
												<span data-i18n="configuration-systemsettings-forward_mail_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<ul>
													<li><span class='templatefieldname'>@@TEXTO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-text">Indica el texto del forward del mensaje</span></li>
													<li><span class='templatefieldname'>@@TEXTO_MENSAJE_RECIBIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-received_text_message">Indica el texto del mensaje sobre el cual se está haciendo un forward</span></li>
													<li><span class='templatefieldname'>@@REMITENTE_MENSAJE_RECIBIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-mail_sender">Indica la persona que envió el mensaje sobre el cual se está haciendo un forward</span></li>
													<li><span class='templatefieldname'>@@FECHA_MENSAJE_RECIBIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-date">Indica la fecha del mensaje sobre el cual se está haciendo un forward</span></li>
													<li><span class='templatefieldname'>@@TIPO_MENSAJE_RECIBIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-message_type">Indica el tipo de servicio del mensaje sobre el cual se está haciendo un forward</span></li>
													<li><span class='templatefieldname'>@@TEXTO_MENSAJE_SALIENTE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-message_text">Si el mensaje es saliente, indica el texto principal del mensaje que se está enviando</span></li>
													<li><span class='templatefieldname'>@@DESTINATARIO_MENSAJE_SALIENTE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-mail_recipient">Si el mensaje es saliente, indica el destinatario del mensaje que se está enviando</span></li>
													<li><span class='templatefieldname'>@@CASO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-forward_mail-field-case">El caso del mensaje</span></li>
												</ul>
											</yoizen:Message>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trAllowForwardAction">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-favorite_mails">Mails favoritos</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs" style="width: 70%">
															<asp:TextBox ID="textboxFavoriteMails" runat="server" Width="100%" TextMode="MultiLine" Rows="3" spellcheck="false" />
															<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxFavoriteMails" ValidationExpression="^((\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*([,])*)*$" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-favorite_mails-tip">
															Este parámetro indica la lista de mails favoritos, separados por coma, para su utilización en el sistema.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-permissions-title">Permisos</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_to_block_users">Permitir bloquear usuarios</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxAllowAgentToBlockUsers" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-allow_to_block_users-tip">
															Este parámetro indica si los agentes tendrán permitido bloquear usuarios (agregarlos a la black list).
															Este comportamiento no realiza cambios en las redes sociales del sistema, solamente ignorará los mensajes
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderAllowAgentsToReturnMessagesToQueue" runat="server">
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_to_return_messages_to_queue">Permitir retornar mensajes a la cola</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxAllowAgentsToReturnMessagesToQueue" runat="server" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-allow_to_return_messages_to_queue-tip">
																Este parámetro indica si los agentes pueden devolver los mensajes que le fueron asignados
																a la cola a la que pertenecen
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-must_enter_return_to_queue_reason">Motivo de retorno a la cola obligatorio</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxAgentMustEnterReturnToQueueReason" runat="server" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-must_enter_return_to_queue_reason-tip">
																Este parámetro indica si será obligatorio que los agentes ingresen el motivo
																en el momento de retornar mensajes a la cola
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-maximum_return_times">Cantidad máxima de retornos</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxMaximumNumberOfTimesMessageCanBeReturned" runat="server" MaxLength="3" TextMode="Number" Width="40" />
																<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMaximumNumberOfTimesMessageCanBeReturned" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-maximum_return_times-tip">
																Este parámetro indica la cantidad máxima de veces que un mensaje puede ser devuelto a las colas. 
																El valor deberá estar entre 1 y 100.
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderAllowAgentsToReturnMessagesToSpecifiedQueue" runat="server">
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_to_select_queue_on_return">Permitir seleccionar cola en el retorno</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxAllowAgentsToSelectQueueOnReturnToQueue" runat="server" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-allow_to_select_queue_on_return-tip">
																Este parámetro indica si los agentes pueden especificar a qué cola se devolverán los mensajes
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<asp:PlaceHolder ID="placeholderAllowAgentsToReturnMessagesWithRelatedMessagesToQueue" runat="server">
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_to_return_related_messages">Permitir retornar mensajes con respuestas o salientes</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxAllowAgentsToReturnMessagesWithRelatedMessagesToQueue" runat="server" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-allow_to_return_related_messages-tip">
																Este parámetro indica si los agentes pueden devolver mensajes a la cola que ya tienen una respuesta
																o generaron algún mensaje saliente
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-outgoing_messages">Redactar mensajes salientes</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxOutgoingMessagesEnabled" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-outgoing_messages-tip">
															Este parámetro indica si la aplicación permitirá el envío de mensajes salientes por parte de los
															agentes/supervisores en cualquier momento
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<asp:Panel ID="panelPbxIntegration" runat="server" CssClass="subseccion collapsable" ClientIDMode="Static">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-call_centre_integration_configuration-title">Configuración para integración con central telefónica</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-default_agent_group">Grupo de agentes por defecto</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:DropDownList ID="dropdownlistPbxIntegrationAgentGroupForNewAgents" runat="server" DataTextField="Name" DataValueField="ID" ClientIDMode="Static" AppendDataBoundItems="true">
																<asp:ListItem Selected="True" Value="" data-i18n="configuration-systemsettings-none">Ninguno</asp:ListItem>
															</asp:DropDownList>
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-default_agent_group-tip">
															Este parámetro indica el grupo de agentes que se utilizará como plantilla para la creación de agentes
															por integración con sistemas externos de telefonía
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</asp:Panel>
						<asp:Panel ID="panelWebAgentConfigurationAllowUrlLogin" runat="server" CssClass="subseccion collapsable" ClientIDMode="Static">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-web_agent_configuration-title">Configuración de Login del Agente Web</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_url_login">Permitir login por URL</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxWebAgentConfigurationAllowUrlLogin" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="[html]configuration-systemsettings-allow_url_login-tip">
															Este parámetro indica si se permitirá el login por Url en el Agente Web (proporcionando parámetros dentro del <span class="mono">QueryString</span>)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-parameter_name">Nombre del parámetro</span> <span class="report-field" data-i18n="configuration-systemsettings-user_name">nombre de usuario</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationUserNameLoginParameter" runat="server" ClientIDMode="Static" autocomplete="off" />
														</td>
														<td class="vMid prs">
															Este parámetro indica cómo se llamará el parámetro que proveerá el nombre del usuario dentro del <span class="mono">QueryString</span> (se admiten de 5 a 20 caracteres alfanuméricos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_required">Password obligatorio</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxWebAgentConfigurationPasswordRequired" runat="server" />
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-password_required-tip">
															Este parámetro indica si se deberá proveer la contraseña como un parámetro más dentro del <span class="mono">QueryString</span>
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trWebAgentConfigurationPasswordParameter" rel="trWebAgentConfiguration" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-parameter_name">Nombre del parámetro</span><span class="report-field" data-i18n="configuration-systemsettings-password">contraseña</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationPasswordParameter" runat="server" ClientIDMode="Static" autocomplete="off" />
														</td>
														<td class="vMid prs">
															Este parámetro indica cómo se llamará el parámetro que proveerá la contraseña del <span class="mono">QueryString</span> (se admiten de 5 a 20 caracteres alfanuméricos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trWebAgentConfigurationKeyToDecryptPassword" rel="trWebAgentConfiguration" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-decription_key">Clave para desencriptar contraseña</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationKeyToDecryptPassword" runat="server" ClientIDMode="Static" autocomplete="off" />
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-decription_key-tip">
															Este parámetro indica la clave que se utilizará para desencriptar la contraseña envíada dentro del <span class="mono">QueryString</span> (se admiten de 5 a 20 caracteres alfanuméricos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-parameter_name">Nombre del parámetro</span><span class="report-field" data-i18n="configuration-systemsettings-hash">hash</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationHashParameter" runat="server" ClientIDMode="Static" autocomplete="off" />
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-hash-tip">
															Este parámetro indica cómo se llamará el parámetro que proveerá el hash (firma) del resto de los parámetros dentro del <span class="mono">QueryString</span> (se admiten de 5 a 20 caracteres alfanuméricos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-hash_key">Clave para hashear</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationKeyToHash" runat="server" ClientIDMode="Static" autocomplete="off" />
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-hash_key-tip">
															Este parámetro indica la clave que se debe utilizar para crear la firma del resto de los parámetros (se admiten de 5 a 20 caracteres alfanuméricos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-remove_login_form">Quitar formulario Login</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxWebAgentConfigurationRemoveLoginForm" runat="server" />
														</td>
														<td class="vMid prs">
															<span data-i18n="configuration-systemsettings-remove_login_form-tip">Este parámetro indica si el agente web proporcionará el formulario de inicio de sesión o no.</span>
															<ul style="list-style-type: disc">
																<li style="list-style-position: inside;" data-i18n="configuration-systemsettings-remove_login_form-tip-1">
																	En caso de seleccionar esta opción, la única forma de iniciar sesión en el agente web será a través de los parámetros. Si se proporcionan parámetros inválidos, 
																	el inicio de sesión fallará y se mostrará un mensaje de error.
																</li>
																<li style="list-style-position: inside;" data-i18n="configuration-systemsettings-remove_login_form-tip-2">
																	En caso de no seleccionar esta opción se podrá iniciar sesión ya sea invocando al agente web con la url normal o bien cuando no se proporcionen parámetros
																	o los parámetros proporcionados sean inválidos.
																</li>
															</ul>
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-remove_logout_button">Quitar botón de Logout</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxWebAgentConfigurationRemoveLogoutButton" runat="server" />
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-remove_logout_button-tip">
															Este parámetro indica si el agente web proporcionará el botón de cierre de sesión  
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="trWebAgentConfiguration">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-end_session_behaviour">Comportamiento al finalizar sesión</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:DropDownList ID="dropdownlistLogoutAction" runat="server" ClientIDMode="Static">
																<asp:ListItem Value="0" Selected="True" data-i18n="configuration-systemsettings-none">Ninguno</asp:ListItem>
																<asp:ListItem Value="1" data-i18n="configuration-systemsettings-show_logout_message">Mostrar mensaje de cierre de sesión</asp:ListItem>
																<asp:ListItem Value="2" data-i18n="configuration-systemsettings-redirect_to_url">Redireccionar a una Url</asp:ListItem>
															</asp:DropDownList>
														</td>
														<td class="vMid prs" data-i18n="[html]configuration-systemsettings-end_session_behaviour-tip">
															Este parámetro indica el comportamiento a realizar al finalizar la sesión
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trWebAgentConfigurationRedirectUrl" rel="trWebAgentConfiguration" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-redirect_url">Url de redirección al finalizar sesión</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationRedirectUrl" runat="server" Width="500px" autocomplete="off" spellcheck="false" ClientIDMode="Static" />
														</td>
														<td class="vMid prs" data-i18n="configuration-systemsettings-redirect_url-tip">
															Este parámetro indica la Url a la que se redireccionará al finalizar sesión (se debe anteponer el protocolo http o https)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trWebAgentConfigurationLogoutMessage" rel="trWebAgentConfiguration" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-logout_message">Mensaje al finalizar sesión</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" width="100%">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWebAgentConfigurationLogoutMessage" runat="server" Width="500px" TextMode="MultiLine" Rows="2" autocomplete="off" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-logout_message-tip">
															Este parámetro indica el mensaje que se mostrará al finalizar sesión (10 a 80 caracteres admitidos)
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
								<yoizen:Message ID="messageWebAgentConfigurationAllowUrlLoginCloud" runat="server" ClientIDMode="Static" style="display: none" Type="Information" LocalizationKey="[html]configuration-systemsettings-allow_url_login_cloud">
									Para formar la URL se deberá agregar además de los parámetros configurados arriba, 2 parámetros adicionales:
									<ul>
										<li><span class="templatefieldname">ts</span>: la fecha y hora actual en formato UNIX (UTC)</li>
										<li><span class="templatefieldname">nonce</span>: un valor único y aleatorio</li>
									</ul>
									Estos campos se adicionarán a los otros para generar el hash de la invocación.
								</yoizen:Message>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateWebAgentLoginConfiguration" data-i18n="configuration-systemsettings-configuration-error" />
								</div>
							</div> 
						</asp:Panel>
						<asp:Panel ID="panelWebAgentConfigurationStateManagement" runat="server" CssClass="subseccion collapsable" ClientIDMode="Static">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-web_agent_state_managment-title">Manejo de estado en el Agente Web</h2>
							</div>
							<div class="contents">
								<div class="subsubseccion" id="divWebAgentConfigurationStateManagement">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-state_managment-title">Manejo de estado</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowStateManagementByInternalMessaging">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_state_change">Permitir invocar el manejo de cambio de estado</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationAllowChangeState" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-allow_state_change-tip">
																	Este parámetro indica si se permitirá cambiar el estado del agente web (cambiar disponibilidad, auxiliar, pendiente de auxiliar) mediante mensajería interna del Browser
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowChangeState" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-show_informative_message">Mostrar mensaje informativo</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-show_informative_message-tip">
																	Este parámetro indica si se permitirá mostrar un mensaje informativo ante el cambio de estado
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationShowMessageAfterChangeStateReceived" style="display: none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-change_state_message">Mensaje al recibir el evento de cambio de estado</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" width="100%">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:textbox ID="textboxWebAgentConfigurationShowInformativeMessageAfterChangeStateReceived" runat="server" Width="500px" TextMode="MultiLine" Rows="2" />							
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-change_state_message-tip">
																	Este parámetro indica el mensaje que se mostrará al agente al recibir el evento de cambio de estado (10 a 200 caracteres admitidos)
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowChangeState" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_change_state">Permitir a los agentes cambiar de estado</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationAllowAgentsToChangeState" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-allow_change_state-tip">
																	Este parámetro indica si se permitirá a los agentes cambiar de estado
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowAgentsToChangeState" style="display:none">
												<th class="label"></th>
												<td class="data">
													<yoizen:Message runat="server" Type="Warning" Text="Modificar parámetros del sistema afectará el comportamiento" LocalizationKey="[html]configuration-systemsettings-change_parameters-warning">
														En caso de no estar activa la opción <span class="mono">Permitir a los agentes cambiar de estado</span>, 
														y la opción <span class="mono">Quitar formulario login</span> este activa el agente solamente podrá iniciar sesión solamente
														por url pero no cambiar de estado
													</yoizen:Message>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowChangeState" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-send_state_of_change_by_private_message">Enviar por mensajería privada el estado del cambio</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationSendStateOfChangeByPrivateMessaging" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-send_state_of_change_by_private_message-tip">
																	Este parámetro indica si se permitirá enviar por mensajería privada si el cambio fue exitoso o no
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowChangeState">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-origin_target">Target Origin</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:TextBox id="textboxWebAgentConfigurationChangeStateTargetOrigin" runat="server" type="text" class="inputtext" style="width:500px" />
																</td>
																<td class="vMid prs" data-i18n="configuration-systemsettings-origin_target-tip">
																	Este parámetro indica la Url que recibirá los mensajes. Se puede indicar la no preferencia de la misma completando con el caracter(*)
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<!-- Logout Invoke -->
								<div class="subsubseccion" id="divWebAgentConfigurationLogoutInvoke">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-logout_invoke-title">Manejo de logout</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowStateManagementByInternalMessaging">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_logout_invoke">Permitir invocar al logout</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationAllowLogoutInvoke" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-allow_logout_invoke-tip">
																	Este parámetro indica si se permitirá al agente invocar al logout
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowLogoutInvocation" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-show_messages_after_logout">Mostrar mensaje ante recepción de logout</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationShowMessageAfterLogoutReceived" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-show_messages_after_logout-tip">
																	Mostrar mensaje informativo ante recepción de logout
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationShowMessageAfterLogoutReceived" style="display: none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-logout_received_message">Mensaje al recibir el evento de finalizar sesión</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" width="100%">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:textbox ID="textboxWebAgentConfigurationShowMessageAfterLogoutReceived" runat="server" Width="500px" TextMode="MultiLine" Rows="2" />							
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-logout_received_message-tip">
																	Este parámetro indica el mensaje que se mostrará al agente al recibir el evento de finalizar la sesión (10 a 200 caracteres admitidos)
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowLogoutInvocation" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_logout_invocation">Ignorar en caso de no poder hacer logout</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationIgnoreLogoutAfterError" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-allow_logout_invocation-tip">
																	Este parámetro indica si se ignorara en caso de no poder hacer logout (actualmente el agente tiene mensajes)
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowLogoutInvocation" style="display:none">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-send_logout_state_by_private_message">Enviar por mensajería privada estado del logout</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:CheckBox ID="checkboxWebAgentConfigurationSendLogoutStateOfChangeByPrivateMessaging" runat="server" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-send_logout_state_by_private_message-tip">
																	Enviar por mensajería privada si el logout fue exitoso o no
																</td>
																</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="trWebAgentConfigurationAllowLogoutInvocation">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-origin_target">Target Origin</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:TextBox id="textboxWebAgentConfigurationLogoutInvokeTargetOrigin" runat="server" type="text" class="inputtext" style="width:500px" />
																</td>
																<td class="vMid prs" data-i18n="configuration-systemsettings-origin_target-tip">
																	Este parámetro indica la Url que recibirá los mensajes. Se puede indicar la no preferencia de la misma completando con el caracter(*)
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateWebAgentStateManagementConfiguration" data-i18n="configuration-systemsettings-configuration-error" />
								</div>
							</div>
						</asp:Panel>	
					</div>
				</div>
				<div class="seccion collapsable hiddenAsGateway">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-queues-title">Colas</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-minutes_predicted_aht">Cantidad de minutos para calcular el AHT</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxMinutesPredictedAht" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="5" max="55" step="5" list="minutesPredictedAHT" EnableTheming="false" />
									<datalist id="minutesPredictedAHT">
										<option value="5"></option>
										<option value="10"></option>
										<option value="15"></option>
										<option value="20"></option>
										<option value="25"></option>
										<option value="30"></option>
										<option value="35"></option>
										<option value="40"></option>
										<option value="45"></option>
										<option value="50"></option>
										<option value="55"></option>
									</datalist>
									<div id="spanMinutesPredictedAht" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
									<asp:CompareValidator runat="server" ControlToValidate="textboxMinutesPredictedAht" Operator="DataTypeCheck" Type="Integer" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxMinutesPredictedAht" MinimumValue="5" MaximumValue="60" Type="Integer" />
									<yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px" LocalizationKey="configuration-systemsettings-queues-minutes_predicted_aht_info">
										Cantidad de minutos 
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-seconds_calculate_ewt">Cantidad de segundos para calcular el EWT</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxSecondsEwt" runat="server" TextMode="Range" ClientIDMode="Static" Width="250px" min="15" max="60" step="15" list="secondsEwt" EnableTheming="false" />
									<datalist id="secondsEwt">
										<option value="15"></option>
										<option value="30"></option>
										<option value="45"></option>
										<option value="60"></option>
									</datalist>
									<div id="spanSecondsEwt" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
									<asp:CompareValidator runat="server" ControlToValidate="textboxSecondsEwt" Operator="DataTypeCheck" Type="Integer" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxSecondsEwt" MinimumValue="15" MaximumValue="60" Type="Integer" />
									<yoizen:Message runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px" LocalizationKey="configuration-systemsettings-queues-seconds_calculate_ewt_info">
										Cantidad de segundos 
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-queues-asa_base">Personalizar el EWT mediante un ASA base</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxASAPersonalized" runat="server" />
													<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px; display:none" rel="tableASABase" >
														<tbody>
															<tr>
																<td class="dataRow dataRowSeparator">
																	<asp:TextBox ID="textboxAsaBase" runat="server" Width="100" TextMode="Number" min="30" style="text-align: right" />
																	<div class="secondinfo" related="textboxAsaBase"></div>
																</td>
															</tr>
														</tbody>
													</table>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-queues-asa_base_seconds">
													Permite calcular el EWT tomando el valor del ASA base expresado en segundos, siempre y cuando el sistema no cuente con los datos.
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
			<div id="divCases">
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-case_configuration-title">Configuraciones de casos</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-verify_last_queue">Verificar última cola</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxCheckLastQueueOfOpenCase" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-verify_last_queue-tip">
													Este parámetro indica si el sistema verificará ante cada mensaje nuevo si ese usuario tiene
													un caso abierto y su último mensaje ingresó en otra cola (que no corresponde a la cola por defecto asignada al servicio). 
													De ser así se tomará como cola por defecto la cola del último mensaje
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-ignore_last_queue">Ignorar última cola si el último mensaje entrante fue movido por SL</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxIgnoreLastQueueForSLMovedMessage" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-ignore_last_queue-tip">
													Este parámetro indica si el sistema ignorará la última cola del caso en caso de que el último mensaje
													que haya sido escrito por el usuario fue movido por alguna acción del Service Level/Vencimiento.
													En caso de ignorar, se utilizará la cola por defecto del servicio por el cual ingresó el mensaje
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>

							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">
									<span data-i18n="configuration-systemsettings-time_to_close">Minutos para cierre automático</span>: <br />
								</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:TextBox ID="textboxMaxElapsedMinutesToCloseCases" runat="server" MaxLength="5" Width="100" TextMode="Number" min="0" max="65535" style="text-align: right" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseCases" />
													<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseCases" ValidationExpression="^\d{1,5}$" />
													<asp:RangeValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseCases" Type="Integer" MinimumValue="1" MaximumValue="43200" />
													<div class="timeinfo" related="textboxMaxElapsedMinutesToCloseCases"></div>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-tip">
													Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario y el sistema. Pasado
													esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">
									<span data-i18n="configuration-systemsettings-auto_reply_close_case">Responder al cerrar el caso de forma automatica</span>:
								</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxReplyInCloseCase" runat="server" ClientIDMode="Static" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-auto_reply_close_case-tip">
													Este parámetro indica si se evniará una respuesta al ultimo mensaje del caso antes de cerrarlo de forma automatica.
												</td>
											</tr>
								      </tbody>
									</table>
									<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top:10px" >
										<tbody>
											<tr>
												<td class="dataRow dataRowSeparator">
													<asp:TextBox ID="textboxAutoReplyInCloseCaseText" style="display: none;" runat="server" TextMode="MultiLine" Width="300%" Rows="4" ClientIDMode="Static" />
												    <asp:CustomValidator runat="server" EnableClientScript="true"  ClientValidationFunction="ValidateAutoReplyMessage" />
												</td>
											</tr>
										</tbody>
									</table>
									<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateAutoReplyMessage" SkinID="validationerror" data-i18n="configuration-systemsettings-fill_information">Debe completar la información</asp:CustomValidator></div>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">
									<span data-i18n="configuration-systemsettings-apply_tag_close_case">Aplicar etiqueta en cierre automático</span>:
								</th>
								<td class="data">
									<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">                 
										<asp:TextBox ID="textboxTagCloseCase" runat="server" ClientIDMode="Static" Width="100%" />
									</div>
									 <yoizen:Message ID="messageTagAutoCloseCase" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-apply_tag_close_case-tip">
										Este parámetro indica si se aplicará una etiqueta al momento de cierre automático por inactividad.
									 </yoizen:Message>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderYFlowCasesRelated" runat="server">
								<tr class="dataRow dataRowSeparator" id="trMaxElapsedMinutesToCloseYFlowCases">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case">Minutos para cierre automático de un caso atendido únicamente por yFlow </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs timeinfo">
														<asp:TextBox ID="textboxMaxElapsedMinutesToCloseYFlowCases" runat="server" MaxLength="5" Width="100" TextMode="Number" style="text-align: right" ClientIDMode="Static" Text="0" />
														<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateMaxElapsedMinutesToCloseYFlowCases" />
														<div class="timeinfo" related="textboxMaxElapsedMinutesToCloseYFlowCases"></div>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-minutes_to_close_yFlow_case-tip">
														Este parámetro indica la cantidad máxima de minutos que pueden estar los casos abiertos sin tener interacciones entre el usuario e yFlow. Pasado
														esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 30 días (43200 Minutos).
														Un caso únicamete de yFlow es cuando nunca fue derivado a un agente
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case">Invocar a yFlow al momento de cerrar el caso </span><span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxInvokeYFlowWhenClosedCases" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-invoke_yFlow_when_closed_case-tip">
														Este parámetro indica si se deberá invocar a yFlow cuando el cierre automático de un caso atendido únicamente por yFlow se ejecute.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</asp:PlaceHolder>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">
									<span data-i18n="configuration-systemsettings-time_to_close-hsm-cases">Minutos para cierre automático de casos HSM</span> <span class="fab fa-lg fa-whatsapp-square"></span>: <br />
								</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:TextBox ID="textboxMaxElapsedMinutesToCloseHsmCases" runat="server" MaxLength="5" Width="100" TextMode="Number" min="0" max="65535" style="text-align: right" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseHsmCases" />
													<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseHsmCases" ValidationExpression="^\d{1,5}$" />
													<asp:RangeValidator runat="server" ControlToValidate="textboxMaxElapsedMinutesToCloseHsmCases" Type="Integer" MinimumValue="1" MaximumValue="1440" />
													<div class="timeinfo" related="textboxMaxElapsedMinutesToCloseHsmCases"></div>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-time_to_close-hsm-case-tip">
													Este parámetro indica la cantidad máxima de minutos que pueden estar los casos HSM abiertos sin tener interacciones entre el usuario y el sistema. Pasado
													esta cantidad de minutos, el sistema automáticamente cerrará los casos. El límite maximo establecido son 24 horas (1440 Minutos).
												</td>
											</tr>
										
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">
									<span data-i18n="configuration-systemsettings-apply_tag_close_hsm_case">Aplicar etiqueta en cierre automático</span> <span class="fab fa-lg fa-whatsapp-square"></span>:
								</th>
								<td class="data">
									<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">                 
										<asp:TextBox ID="textboxTagOnHsmCases" runat="server" ClientIDMode="Static" Width="100%" />
									</div>
									 <yoizen:Message ID="messageTagCloseCaseHsm" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-apply_tag_close_case-tip">
										Este parámetro indica si se aplicará una etiqueta al momento de cierre automático por inactividad.
									 </yoizen:Message>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderCasesCloseConcurrent" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">Cantidad de casos a procesar para el cierre en forma concurrente:</th>
									<td class="data">
										<asp:TextBox ID="textboxCasesCloseConcurrent" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
										<div id="spanCasesCloseConcurrent" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxCasesCloseConcurrent" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxCasesCloseConcurrent" MinimumValue="1" MaximumValue="500" Type="Integer" />
									</td>
								</tr>
							</asp:PlaceHolder>
						</table>
					</div>
				</div>
				<div class="seccion collapsable hiddenAsGateway">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-agent_configuration-title">Configuraciones para los agentes</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-tag_cases_on_start">Etiquetado obligatorio al inicio</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<%--<asp:CheckBox ID="checkboxTagCasesOnStart" runat="server" />--%>
													<asp:DropDownList id="dropdownlistTagCasesOnStart" runat="server">
													</asp:DropDownList>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-tag_cases_on_start-tip">
													Este parámetro indica si los agentes deberán etiquetar el caso al momento de trabajar con la primera interacción
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-tag_cases_on_close">Etiquetado obligatorio al cierre</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<%--<asp:CheckBox ID="checkboxTagCasesOnClose" runat="server" />--%>
													<asp:DropDownList ID="dropdownlistTagCasesOnClose" runat="server">
													</asp:DropDownList>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-tag_cases_on_close-tip">
													Este parámetro indica si los agentes deberán etiquetar el caso al momento de cerrarlo tanto en la respuesta
													como en el descarte (si es que el caso no estaba etiquetado)
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator"  style="display: none" id="importantTag">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-important_tag">Etiquetado importante obligatorio</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxImportantTag" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-important_tag-tip">
													Este parámetro indica si los agentes deberán ponerle una etiqueta importante al caso
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-tag_cases_on_discard">Etiquetado obligatorio en descarte</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<%--<asp:CheckBox ID="checkboxTagCasesOnDiscard" runat="server" />--%>
													<asp:DropDownList id="dropdownlistTagCasesOnDiscard" runat="server">
													</asp:DropDownList>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-tag_cases_on_discard-tip">
													Este parámetro indica si los agentes deberán etiquetar el caso al momento de descartsar mensajes (si es que el caso
													no estaba etiquetado)
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="">Etiquetado obligatorio al enviar salientes</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxTagOutgoing" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="">
													Este parámetro indica si los agentes deberán etiquetar el caso al enviar salientes
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" style="display: none" id="importantTagOutgoing">
								<th class="label withdescription"><span data-i18n="">Etiquetado importante al enviar salientes</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxImportantTagOutgoing" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="">
													Este parámetro indica si los agentes deberán ponerle una etiqueta importante al caso al enviar salientes
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" style="display: none">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-update_cases">Actualización del caso ante nuevos mensajes</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxAlwaysUpdateCase" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-update_cases-tip">
													Este parámetro indica si el sistema actualizará el caso cuando
													hayan nuevos mensajes en cola o si se le dará al agente la posibilidad de elegir 
													qué acción tomar
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
<%--							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-mark_messages_as_pending">Permitir marcar casos como pendiente</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxAllowAgentsToMarkCasesAsPending" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-mark_messages_as_pending-tip">
													Este parámetro indica si los agentes podrán marcar los casos como pendientes
													(tanto de respuesta del cliente como para remotar el caso en el futuro)
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>--%>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-allow_to_add_message_to_cases_with_messages_in_queue">Permitir enviar mensajes a través de Mis Casos a un caso con mensajes en cola</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxAllowToAddMessagesToCasesWithMessagesInQueue" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-allow_to_add_message_to_cases_with_messages_in_queue-tip">
													Este parámetro indica si los agentes pueden seleccionar un caso dentro de la sección Mis Casos para agregar mensajes nuevos aún
													cuando hayan mensajes en cola.
													En caso de desmarcar esta opción, si el caso tiene en el momento de elegirlo mensajes en cola, no se permitirá agregar mensajes
													al caso
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-notify_when_pending_case">Notificar al cerrar casos pendientes</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs timeinfo">
													<asp:CheckBox ID="checkboxAllowNotificationWhenClosePendingCase" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-notify_when_pending_case-tip">
													Este parámetro indica si los agentes recibirán una notificación cuando se cierra un caso que ellos tengan marcado como pendiente.
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div class="seccion collapsable hiddenAsGateway">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-case_configuration-title-global">Configuraciones de casos global</h2>
					</div>
				<div class="contents">
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator hiddenAsGateway">
							<th class="label withdescription"><span data-i18n="configuration-systemsettings-move_last_queue_by_time">Mover a última cola por tiempo configurable</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
											<asp:CheckBox ID="checkboxCheckLastQueueByTime" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-systemsettings-move_last_queue_by_time-tip">Este parámetro indica un tiempo límite en que un mensaje puede ser movido a la cola donde ingresó el último mensaje
											</td>
										</tr>
									</tbody>
								</table>
								<table class="uiGrid" cellspacing="0" cellpadding="0" style="margin-top: 10px; display: none" rel="tableLastQueueByTime">
									<tbody>
									<tr>
										<td class="dataRow dataRowSeparator">
										<asp:TextBox ID="textboxLastQueueByTime" runat="server" Width="100" TextMode="Number" min="60" max="600" Style="text-align: right" />
										<div class="secondinfo" related="textboxLastQueueByTime"></div>
										<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLastQueueByTime" />
										</td>
									</tr>
								</tbody>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLastQueueByTime" SkinID="validationerror" data-i18n="configuration-systemsettings-under_limit_number">Debe completar la información</asp:CustomValidator>
								</div>
							</td>
						</tr>
					</table>
				</div>
				</div>
			</div>
			<div id="divFilters">
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-mail_settings-title">Configuración para envio de mail</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxFilterEmailSubject" runat="server" MaxLength="200" Width="90%" />
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFilterEmailSubject" />
									<yoizen:Message ID="messageFilterEmailSubjectFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
										<span data-i18n="configuration-systemsettings-mail_subject_fields">Puede utilizar los siguientes campos dentro del asunto</span>:<br />
										<ul>
											<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_subject-field-user">Indica el usuario de red social que mandó el mensaje</span></li>
											<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_subject-field-service_name">Indica el nombre del servicio por el cual ingresó el mensaje</span></li>
											<li><span class='templatefieldname'>@@TIPO_DE_SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_subject-field-service_type">Indica el nombre del tipo de servicio de red social por el cual ingresó el mensaje</span></li>
										</ul>
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-recipients">Destinatarios</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxFilterEmailEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
									<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxFilterEmailEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
									<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 10px" LocalizationKey="configuration-systemsettings-recipients-tip">
										En caso de completar destinatarios, se enviarán Con Copia
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-send_to_admin">Enviar a todos los administradores</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxFilterEmailSendToAdministrators" runat="server" ClientIDMode="Static" />
									<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 10px" LocalizationKey="configuration-systemsettings-send_to_admin-tip">
										En caso de enviar a todos los administradores, se enviarán Con Copia
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-send_to_supervisors">Enviar a todos los supervisores</span>:</th>
								<td class="data">
									<asp:CheckBox ID="checkboxFilterEmailSendToSupervisors" runat="server" ClientIDMode="Static" />
									<yoizen:Message runat="server" Type="Information" Small="true" Style="margin-top: 10px" LocalizationKey="configuration-systemsettings-send_to_supervisors-tip">
										En caso de enviar a todos los supervisores, se enviarán Con Copia
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxFilterEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxFilterEmailTemplate" />
									<yoizen:Message ID="messageFilterEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
										<span data-i18n="configuration-systemsettings-filter_email_template">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
										<ul>
											<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-date">Indica la fecha y hora del mensaje</span></li>
											<li><span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-user">Indica el usuario de red social que mandó el mensaje</span></li>
											<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-service">Indica el nombre del servicio por el cual ingresó el mensaje</span></li>
											<li><span class='templatefieldname'>@@TIPO_DE_SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-service_type">Indica el nombre del tipo de servicio de red social por el cual ingresó el mensaje</span></li>
											<li><span class='templatefieldname'>@@TEXTO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-text">Indica el texto del mensaje</span></li>
											<li><span class='templatefieldname'>@@COORDENADAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-coordinates">Indica las coordenadas del mensaje</span></li>
											<li><span class='templatefieldname'>@@CASO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-filter_email-field-case">Indica que en este lugar se incluirá el caso del mensaje</span></li>
										</ul>
									</yoizen:Message>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
			<div id="divSocialServices" runat="server" clientidmode="Static" visible="false">
				<div id="divTwitterService" runat="server" clientidmode="static" class="seccion collapsable">
					<div class="title">
						<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;<span class="fab fa-lg fa-twitter-square"></span>&nbsp;Twitter</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-mention_in_multiple_services">Mensajes en múltiples servicios</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxTwitterAllowMentionInMultipleServices" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-mention_in_multiple_services-tip">
													Este parámetro indica, cuando está seleccionado, si un Tweet que menciona a 2 cuentas configuradas en el sistema
													ingresa como un mensaje nuevo para las cuentas mencionadas. En caso de no seleccionar esta opción el mensaje
													ingresará por la primer cuenta que lo encuentre
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div id="divRestChatService" runat="server" clientidmode="Static" class="seccion collapsable">
					<div class="title">
						<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;<span class="fa fa-lg fa-comments-alt"></span>&nbsp;Chat Integrado</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-concurrent_session">Sesiones concurrentes</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxRestChatMaxConcurrentSessions" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
									<div id="spanRestChatMaxConcurrentSessions" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
									<asp:CompareValidator runat="server" ControlToValidate="textboxRestChatMaxConcurrentSessions" Operator="DataTypeCheck" Type="Integer" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxRestChatMaxConcurrentSessions" MinimumValue="1" MaximumValue="500" Type="Integer" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-concurrent_calls_per_session">Mensajes concurrentes por sesión</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxRestChatMaxConcurrentCallsPerSession" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
									<div id="spanRestChatMaxConcurrentCallsPerSession" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
									<asp:CompareValidator runat="server" ControlToValidate="textboxRestChatMaxConcurrentCallsPerSession" Operator="DataTypeCheck" Type="Integer" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxRestChatMaxConcurrentCallsPerSession" MinimumValue="1" MaximumValue="500" Type="Integer" />
									<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-yflow-pending_messages-concurrent_calls_per_session-tip" style="margin-top: 5px">
										Este valor debería ser siempre 1, dado que así se procesará una novedad por cada caso (no habrá procesamiento simultáneo para el mismo caso)
									</yoizen:Message>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div id="divChatService" runat="server" clientidmode="Static" class="seccion collapsable">
					<div class="title">
						<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;<span class="fa fa-lg fa-comments"></span>&nbsp;Chat</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_server_location">Ubicación del servidor de Chat</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:DropDownList ID="dropdownlistChatCloudSameServer" runat="server" ClientIDMode="Static">
														<asp:ListItem Selected="True" Value="1" data-i18n="configuration-systemsettings-same_server">Mismo servidor</asp:ListItem>
														<asp:ListItem Value="0" data-i18n="configuration-systemsettings-other_server">Otro servidor</asp:ListItem>
													</asp:DropDownList>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-chat_server_location-tip">
													Especifica si el servidor de chat se encuentra instalado en el mismo servidor o está hosteado en un servidor dedicado
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id="trChatCloudOtherServer">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_server_host">Host de servidor de chat</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxChatCloudOtherServer" runat="server" Width="400" spellcheck="false" ClientIDMode="Static" />
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateChatCloudOtherServer" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-chat_server_host-tip">
													Especifica el host donde está alojado el servidor de chat
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_port">Puerto para Chat</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxChatCloudPort" runat="server" Width="100" spellcheck="false" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatCloudPort" />
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateChatCloudPort" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-chat_port-tip">
													Especifica el puerto donde estará escuchando el servicio de Chat (debe ser 80, 443 o debe ser mayor a 8000) para las conexiones hacia la WAN
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_port_from_ySocial">Puerto para Chat para conexiones desde</span>&nbsp;<span class="productname"></span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxChatCloudPortForYSocial" runat="server" Width="100" spellcheck="false" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatCloudPortForYSocial" />
													<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateChatCloudPortForYSocial" />
												</td>
												<td class="vMid pls" data-i18n="[html]configuration-systemsettings-chat_port_from_ySocial-tip">
													Especifica el puerto donde estará escuchando el servicio de Chat (debe ser 80, 443 o debe ser mayor a 8000) para las conexiones entre <span class="productname"></span> 
													y el servidor de chat
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderChatOnPremise" runat="server">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_port_ssl">SSL de puerto para Chat</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:DropDownList ID="dropdownlisCloudPortOverHttps" runat="server" ClientIDMode="Static">
															<asp:ListItem Selected="True" Value="1" data-i18n="configuration-systemsettings-with_ssl">Con SSL (https://)</asp:ListItem>
															<asp:ListItem Value="0" data-i18n="configuration-systemsettings-without_ssl">Sin SSL (http://)</asp:ListItem>
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-chat_port_ssl-tip">
														Especifica si el servidor de chat iniciará la conexión para internet utilizando Https o Http
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-chat_port_ssl_from_ySocial">SSL de puerto para Chat para conexiones desde ySocial</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:DropDownList ID="dropdownlisCloudOtherServerPortYSocialOverHttps" runat="server" ClientIDMode="Static">
															<asp:ListItem Selected="True" Value="1" data-i18n="configuration-systemsettings-with_ssl">Con SSL (https://)</asp:ListItem>
															<asp:ListItem Value="0" data-i18n="configuration-systemsettings-without_ssl">Sin SSL (http://)</asp:ListItem>
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-chat_port_ssl_from_ySocial-tip">
														Especifica si el servidor de chat iniciará la conexión para ySocial utilizando Https o Http
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</asp:PlaceHolder>
						</table>
						<asp:Panel ID="panelChatOnPremiseAlerts" runat="server" Visible="false" CssClass="subseccion">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-alerts-title">Alertas</h2>
							</div>
							<div class="contents">
								<div class="seccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-started_service-title">Servicio iniciado</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStartedEmailSubject" runat="server" MaxLength="200" Width="90%" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStartedEmailSubject" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStartedDestinationEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" spellcheck="false" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStartedDestinationEmails" />
													<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxChatStartedDestinationEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStartedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStartedEmailTemplate" />
													<yoizen:Message ID="messageChatStartedEmailTemplateFields" runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px;">
														<span data-i18n="configuration-systemsettings-started_service-fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-started_service-field-date">Indica la hora en la que sucedió el error</span></li>
															<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-started_service-field-service">Indica el nombre del servicio de chat</span></li>
															<li><span class='templatefieldname'>@@HOSTNAME@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-started_service-field-host_name">Indica el nombre del servidor de chat</span></li>
															<li><span class='templatefieldname'>@@IP@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-started_service-field-ip">Indica la IP del servidor de chat</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="seccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-stopped_service-title">Servicio detenido</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStoppedEmailSubject" runat="server" MaxLength="200" Width="90%" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStoppedEmailSubject" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStoppedDestinationEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" spellcheck="false" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStoppedDestinationEmails" />
													<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxChatStoppedDestinationEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatStoppedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatStoppedEmailTemplate" />
													<yoizen:Message ID="messageChatStoppedEmailTemplateFields" runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px;">
														<span data-i18n="configuration-systemsettings-stopped_service_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-stopped_service-fields-date">Indica la hora en la que sucedió el error</span></li>
															<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-stopped_service-fields-service">Indica el nombre del servicio de chat</span></li>
															<li><span class='templatefieldname'>@@HOSTNAME@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-stopped_service-fields-host_name">Indica el nombre del servidor de chat</span></li>
															<li><span class='templatefieldname'>@@IP@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-stopped_service-fields-ip">Indica la IP del servidor de chat</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="seccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-service_error-title">Error en el Servicio</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatCrashedEmailSubject" runat="server" MaxLength="200" Width="90%" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatCrashedEmailSubject" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatCrashedDestinationEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" spellcheck="false" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatCrashedDestinationEmails" />
													<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxChatCrashedDestinationEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxChatCrashedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" Style="resize: vertical; max-height: 300px" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxChatCrashedEmailTemplate" />
													<yoizen:Message ID="messageChatCrashedEmailTemplateFields" runat="server" Type="Information" Small="true" ClientIDMode="Static" style="margin-top: 10px;">
														<span data-i18n="configuration-systemsettings-service_error_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-service_error-field-date">Indica la hora en la que sucedió el error</span></li>
															<li><span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-service_error-field-service">Indica el nombre del servicio de chat</span></li>
															<li><span class='templatefieldname'>@@HOSTNAME@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-service_error-field-host_name">Indica el nombre del servidor de chat</span></li>
															<li><span class='templatefieldname'>@@IP@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-service_error-field-ip">Indica la IP del servidor de chat</span></li>
															<li><span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-service_error-field-error">Indica el error que ocurrió en el servicio</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>
						</asp:Panel>
					</div>
				</div>
				<div id="divWhatsappService" runat="server" clientidmode="Static" class="seccion collapsable">
					<div class="title">
						<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;<span class="fab fa-lg fa-whatsapp-square"></span>&nbsp;Whatsapp</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-whatsapp_server_location">Ubicación del callback</span>:</th>
								<td class="data">
									<div class="input-with-icons">
										<asp:TextBox ID="textboxWhatsappUrlRtNotifications" runat="server" Width="400" spellcheck="false" ClientIDMode="Static" EnableTheming="false" />
										<i class="icon-status icon-status-spin fa fa-spinner fa-spin"></i>
										<i class="icon-status icon-status-ok fa fa-check-circle"></i>
										<i class="icon-status icon-status-error fa fa-exclamation-triangle"></i>
									</div>
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsappUrlRtNotifications" />
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateWhatsappUrlRtNotifications" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription">Código de país por defecto:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:DropDownList ID="dropdownlistWhatsappDefaultInternationCode" runat="server" DataTextField="FullDescription" DataValueField="InternationalCode" Width="300px" />
												</td>
												<td class="vMid pls">
													Este parámetro indica el país por defecto que se utilizará cuando se pueda seleccionar un país para el número telefónico de Whatsapp
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderWhatsAppServiceVoiceCalls" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator" style="display:none">
									<th class="label withdescription" style="width: 200px"><span class="fa fa-phone-alt"></span><span data-i18n="configuration-systemsettings-whatsapp-voice_calls-on_call_behaviour">Comportamiento con llamadas</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:DropDownList ID="dropdownlistWhatsAppVoiceCallsOnCallBehaviour" runat="server" ClientIDMode="Static">
															<asp:ListItem Value="1" data-i18n="configuration-agents-whatsapp-voice_calls-on_call_behaviour-switch_to_pending">Recibir llamada y pasar a Pendiente de Auxiliar (dejar de recibir nuevos mensajes)</asp:ListItem>
															<asp:ListItem Value="2" data-i18n="configuration-agents-whatsapp-voice_calls-on_call_behaviour-dont_receive_with_multiple_message">No recibir llamadas si se encuentra atendiendo más de un mensaje</asp:ListItem>
															<asp:ListItem Value="3" data-i18n="configuration-agents-whatsapp-voice_calls-on_call_behaviour-receive">Recibir llamada</asp:ListItem>
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-whatsapp-voice_calls-on_call_behaviour-tip">
														Este parámetro especifica qué harán los agentes cuando inicie una llamada para un mensaje asignado. Esta configuración permite ser aplicada a todos los agentes
														donde se haya elegido la opción de utilizar lo mismo definido aquí
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<asp:PlaceHolder ID="placeholderWhatsAppServiceVoiceCallsSuperYoizen" runat="server" Visible="false">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription" style="width: 200px"><span class="fa fa-phone-alt"></span>Configuración de Ice Servers:</th>
										<td class="data">
											<asp:TextBox ID="textboxWhatsAppServiceVoiceCallsIceServers" runat="server" TextMode="MultiLine" ClientIDMode="Static" style="width: 70%;" Rows="5" SkinID="Mono" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsIceServers" />
											<asp:CustomValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsIceServers" EnableClientScript="true" ClientValidationFunction="ValidateWhatsAppServiceVoiceCallsIceServers" ErrorMessage="El formato JSON es inválido. Debe ser un array de objetos" />
										</td>
									</tr>
								</asp:PlaceHolder>
							</asp:PlaceHolder>
							<asp:PlaceHolder ID="placeholderWhatsappServiceCatalogApp" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">ID de Aplicación de Facebook para Catálogo:</th>
									<td class="data">
										<asp:DropDownList ID="dropdownlistWhatsappServiceCatalogApp" runat="server">
											<asp:ListItem Value="197887303586344">YSC (Default)</asp:ListItem>
											<asp:ListItem Value="780860039961826">YSC 2 (Alternativa)</asp:ListItem>
										</asp:DropDownList>
									</td>
								</tr>
							</asp:PlaceHolder>
							<asp:PlaceHolder ID="placeholderWhatsappEmbeddedSignUpApp" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription">ID de Aplicación de Facebook para EmbeddedSignUp:</th>
									<td class="data">
										<asp:DropDownList ID="dropdownlistWhatsappEmbeddedSignUpApp" runat="server">
											<asp:ListItem Value="197887303586344">YSC (Default)</asp:ListItem>
											<asp:ListItem Value="780860039961826">YSC 2 (YSCLLC)</asp:ListItem>
										</asp:DropDownList>
									</td>
								</tr>
							</asp:PlaceHolder>
						</table>
						<asp:Panel ID="panelWhatsAppServiceVoiceCallsRecording" runat="server" Visible="false" CssClass="subseccion">
							<div class="title">
								<h2>Configuración de grabaciones</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription" style="width: 200px">Hostname Grabador:</th>
										<td class="data">
											<asp:TextBox ID="textboxWhatsAppServiceVoiceCallsRecordingHostname" runat="server" ClientIDMode="Static" style="max-width: 70%; min-width: 400px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingHostname" />
											<asp:CustomValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingHostname" EnableClientScript="true" ClientValidationFunction="ValidateWhatsAppServiceVoiceCallsRecordingHostname" ErrorMessage="El formato del hostname es inváido" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription" style="width: 200px">Puerto Grabador:</th>
										<td class="data">
											<asp:TextBox ID="textboxWhatsAppServiceVoiceCallsRecordingPort" runat="server" ClientIDMode="Static" style="width: 100px" MaxLength="5" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingPort" />
											<asp:CompareValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingPort" Operator="GreaterThan" ValueToCompare="1" />
											<asp:CompareValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingPort" Operator="LessThan" ValueToCompare="65000" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription" style="width: 200px">JWT Secret Grabador:</th>
										<td class="data">
											<asp:TextBox ID="textboxWhatsAppServiceVoiceCallsRecordingJWTSecret" runat="server" ClientIDMode="Static" style="width: 70%;" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingJWTSecret" />
											<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingJWTSecret" ValidationExpression="^[A-Za-z0-9-_]{32}$" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription" style="width: 200px">Hostname descarga grabación:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname" runat="server" ClientIDMode="Static" style="max-width: 70%; min-width: 400px" />
															<asp:CustomValidator runat="server" ControlToValidate="textboxWhatsAppServiceVoiceCallsRecordingDownloadHostname" EnableClientScript="true" ClientValidationFunction="ValidateWhatsAppServiceVoiceCallsRecordingDownloadHostname" ErrorMessage="El formato del hostname es inváido" />
														</td>
														<td class="vMid pls">
															Este parámetro especifica si la descarga de las grabaciones se realizará desde un host alternativo (cuando está escalado horizontal). En caso de dejarlo vacío
															se utilizará el mismo hostname definido arriba
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
								<div class="buttons">
									<label class="uiButton">
										<button type="button" onclick="ValidateWhatsAppVoiceCallRecording()">Test de configuración</button>
									</label>
								</div>
							</div>
						</asp:Panel>
					</div>
				</div>
				<div id="divTelegramService" runat="server" clientidmode="Static" class="seccion collapsable">
					<div class="title">
						<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;<span class="fab fa-lg fa-telegram"></span>&nbsp;Telegram</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-whatsapp_server_location">Ubicación del callback</span>:</th>
								<td class="data">
									<div class="input-with-icons">
										<asp:TextBox ID="textboxTelegramUrlRtNotifications" runat="server" Width="400" spellcheck="false" ClientIDMode="Static" EnableTheming="false" />
										<i class="icon-status icon-status-spin fa fa-spinner fa-spin"></i>
										<i class="icon-status icon-status-ok fa fa-check-circle"></i>
										<i class="icon-status icon-status-error fa fa-exclamation-triangle"></i>
									</div>
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxTelegramUrlRtNotifications" />
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateTelegramUrlRtNotifications" />
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div id="divCapiService" runat="server" clientidmode="static" class="seccion collapsable">
				<div class="title">
					<h2><span data-i18n="configuration-systemsettings-configuration_of-title">Configuración de</span>&nbsp;Capi</h2>
				</div>
				<div class="contents">
					<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-capi-tip">
						En esta seccion se configurara el uso de CAPI de META
					</yoizen:Message>
					<table width="100%" border="0" class="uiInfoTable noBorder">
						<tr class="dataRow dataRowSeparator">
							<th class="label withdescription"><span data-i18n="configuration-systemsettings-capi_enable">Habilitar CAPI</span>:</th>
							<td class="data">
								<table class="uiGrid" cellspacing="0" cellpadding="0">
									<tbody>
										<tr>
											<td class="vMid prs">
												<asp:CheckBox ID="checkboxEnableCapi" runat="server" />
											</td>
											<td class="vMid pls" data-i18n="configuration-systemsettings-capi_description-tip">
												Este campo habilitará el AB; de eventos y la gestión a través de Servicios
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</table>
					<div class="subseccion" style="display:none" rel="subseccionCapi">
						<div class="title">
							<h2 data-i18n="configuration-systemsettings-capi_associate_events">Asociar eventos</h2>
						</div>
						<div class="contents">
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-capi_associate_events-tip">
									Aqui se deberan asociar los eventos disponibilizados por META a las etiquetas de Yoizen para implementar su uso en el entorno de ySocial e yFlow
								</yoizen:Message>
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" id="dropdownMetaEvents">
										<th class="label"><span data-i18n="configuration-systemsettings-capi_meta_event">Evento de meta</span>:</th>
										<td class="data">
											<asp:ListBox ID="listboxMetaEvents" runat="server" SelectionMode="Single" ClientIDMode="Static" DataTextField="Name" DataValueField="ID">
											</asp:ListBox>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="dropdownTags">
										<th class="label"><span data-i18n="configuration-systemsettings-capi_yoizen_event">Evento Yoizen</span>:</th>
										<td class="data">
											<asp:ListBox ID="listboxTags" runat="server" SelectionMode="Multiple" ClientIDMode="Static" DataTextField="Name" DataValueField="ID">
											</asp:ListBox>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="dropdownGroupTags">
										<th class="label"><span data-i18n="configuration-systemsettings-capi_tag_groups">Grupos de etiquetas</span>:</th>
										<td class="data">
											<asp:ListBox ID="listboxGroupTags" runat="server" SelectionMode="Multiple" ClientIDMode="Static" DataTextField="Name" DataValueField="ID">
											</asp:ListBox>
											<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-capi_tag_groups_tip" style="margin-top: 5px">
													Seleccione los grupos de etiquetas que pertenezcan al evento de META
											</yoizen:Message>
										</td>
									</tr>
								</table>
								<div class="buttons">
									<label class="uiButton uiButtonLarge uiButtonConfirm">
										<button id="btnAccept" type="button" onclick="acceptValues()" data-i18n="globals-accept">Aceptar</button>
									</label>
									<label class="uiButton uiButtonLarge">
										<button id="btnCancel" type="button" onclick="resetValues()" data-i18n="globals-cancel">Cancelar</button> 
									</label>
								</div>
							</div>
						</div>
						<div class="subseccion" style="display:none" rel="subseccionCapi">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-capi_created_events">Eventos creados</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-capi_created_events_tip">
									Aquí se listarán los eventos asociados
								</yoizen:Message>
								<table id="createdAssociationsTable" class="reporte" cellspacing="0" rules="all" border="1">
								  <thead>
									<tr class="header">
									  <th style="width: 20px"></th>
									  <th style="width:auto" scope="col">Evento de Meta</th>
									  <th scope="col">Etiquetas Yoizen</th>
									</tr>
								  </thead>
								  <tbody></tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="divAlerts">
				<div id="divAlertsOutgoingFailed" class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-failed_delivery_notification-title">Notificaciones de mensajes que no pudieron ser envíados</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-notify_when">Notificar cuando</span>:</th>
								<td class="data">
									<asp:ListBox ID="listboxAlertMessagesDeliveryFailedVia" runat="server" SelectionMode="Multiple" ClientIDMode="Static">
										<asp:ListItem Value="1" data-i18n="configuration-systemsettings-by_mail">Por mail</asp:ListItem>
										<asp:ListItem Value="2" data-i18n="configuration-systemsettings-by_screen">Por notificación por pantalla a supervisores logueados</asp:ListItem>
									</asp:ListBox>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" id="trAlertMessagesDeliveryFailedFromServices" style="display: none">
								<th class="label"><span data-i18n="configuration-systemsettings-services_to_notify">Servicios a notificar</span>:</th>
								<td class="data">
									<asp:ListBox ID="listboxAlertMessagesDeliveryFailedFromServices" runat="server" SelectionMode="Multiple" ClientIDMode="Static" DataTextField="Name" DataValueField="ID">
									</asp:ListBox>
								</td>
							</tr>
						</table>
						<div id="divAlertMessagesDeliveryFailedViaMail" class="subseccion collapsable" style="display: none">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-mail_configuration-title">Configuración para el envío de mail</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenDeliveryFailedConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxDeliveryFailedConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAlertMessagesDeliveryFailedViaMailEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAlertMessagesDeliveryFailedViaMailEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
											<yoizen:Message ID="messageAlertMessagesDeliveryFailedViaMailEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-failed_delivery_mail_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<ul>
													<li><span class='templatefieldname'>@@MINUTOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-failed_delivery_mail-field-minutes">Indica la cantidad de minutos hacia atrás que se verificó por mensajes no enviados</span></li>
													<li><span class='templatefieldname'>@@CANTIDAD_DE_MENSAJES@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-failed_delivery_mail-field-message_quantity">Indica la cantidad de mensajes que fallaron enviarse</span></li>
													<li><span class='templatefieldname'>@@COLAS@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-failed_delivery_mail-field-queues">Indica el nombre de las colas, separadas por coma, de las cuales hay mensajes que fallaron enviarse</span></li>
													<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-failed_delivery_mail-field-link">Indica el link a ingresar para ver los mensajes</span></li>
												</ul>
											</yoizen:Message>
										</td>
									</tr>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateAlertMessagesDeliveryFailedViaMail" data-i18n="configuration-systemsettings-fill_data-error">Debe completar los datos</asp:CustomValidator>
								</div>
							</div>
						</div>
					</div>
				</div>
				<asp:Panel ID="panelAlertsOtherProblems" runat="server" CssClass="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-other_problems_mails-title">Notificaciones por email de otros problemas</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-expired_license-title">Licencia expirada</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenLicenseExpiredConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxLicenseExpiredConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxLicenseExpiredEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLicenseExpiredEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxLicenseExpiredEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLicenseExpiredEmails" />
											<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxLicenseExpiredEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxLicenseExpiredEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxLicenseExpiredEmailTemplate" />
											<yoizen:Message ID="messageLicenseExpiredEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-expired_license_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-expired_license-field-date">Indica la fecha indicada de expiración en la licencia</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-no_free_space-title">Poco espacio en disco para archivos adjuntos</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-no_free_space-tip">
									Aquí se configurará el mail que se enviará cuando el espacio en disco para guardar archivos adjuntos esté por debajo
									del límite especificado
								</yoizen:Message>
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-minumun_space_to_consider">Espacio libre mínimo a considerar (GB)</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxAttachmentsMinimumFreeSpace" runat="server" MaxLength="3" Width="90%" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAttachmentsMinimumFreeSpace" />
															<asp:CompareValidator runat="server" ControlToValidate="textboxAttachmentsMinimumFreeSpace" Type="Integer" ValueToCompare="1" Operator="GreaterThanEqual" />
														</td>
														<td class="vMid pls" data-i18n="[html]configuration-systemsettings-minumun_space_to_consider-tip">
															Especifica la cantidad mínima de espacio libre en GB que se tiene que considerar. Cuando el espacio
															libre sea inferior al especificado se enviará el mail (una vez por día).<br />
															El valor debe ser mayor o igual a 1.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldOutOfDiskSpaceForAttachmentsConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxOutOfDiskSpaceForAttachmentsConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfDiskSpaceForAttachmentsEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfDiskSpaceForAttachmentsEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfDiskSpaceForAttachmentsEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfDiskSpaceForAttachmentsEmails" />
											<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxOutOfDiskSpaceForAttachmentsEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfDiskSpaceForAttachmentsEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfDiskSpaceForAttachmentsEmailTemplate" />
											<yoizen:Message ID="messageOutOfDiskSpaceForAttachmentsEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-no_free_space_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<ul>
													<li><span class='templatefieldname'>@@RUTA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-no_free_space-field-route">Indica la ruta donde se está quedando sin espacio (ej: C:\)</span></li>
													<li><span class='templatefieldname'>@@ESPACIO_LIBRE_MINIMO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-no_free_space-field-minimun_free_space">Indica la cantidad de espacio libre configurada para la alerta</span></li>
													<li><span class='templatefieldname'>@@ESPACIO_LIBRE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-no_free_space-field-free_space">Indica la cantidad de espacio libre</span></li>
												</ul>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-database_problems-title">Problemas con la base de datos</h2>
							</div>
							<div class="contents">
								<yoizen:Message ClientIDMode="Static" runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-database_problems-tip">
									Los problemas de base de datos indicarán problemas relacionados con el Software o Hardware de la base de datos, como ser problemas
									de espacio en disco o errores críticos que deben ser solucionados por un administrador de base de datos
								</yoizen:Message>
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldDatabaseProblemsConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxDatabaseProblemsConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDatabaseProblemsEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDatabaseProblemsEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDatabaseProblemsEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDatabaseProblemsEmails" />
											<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxDatabaseProblemsEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDatabaseProblemsEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDatabaseProblemsEmailTemplate" />
											<yoizen:Message ID="messageDatabaseProblemsEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-database_problems_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-database_problems-field-date">Indica la fecha y hora en la que ocurrió el problema</span><br />
												<span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-database_problems-field-error">Indica el error de la base de datos que desencadenó el mail</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-out_of_memory-title">Problemas con memoria en el servidor</h2>
							</div>
							<div class="contents">
								<yoizen:Message ClientIDMode="Static" runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-out_of_memory-tip">
									Los problemas de memoria indicarán que el servidor se está quedando sin memoria disponible para seguir ejecutando los procesos. Se enviará un mail cada 20 minutos
									mientras el problema persista
								</yoizen:Message>
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldOutOfMemoryConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxOutOfMemoryConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfMemoryEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfMemoryEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfMemoryEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfMemoryEmails" />
											<asp:RegularExpressionValidator runat="server" ControlToValidate="textboxOutOfMemoryEmails" ValidationExpression="^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxOutOfMemoryEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxOutOfMemoryEmailTemplate" />
											<yoizen:Message ID="messageOutOfMemoryEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-out_of_memory_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-out_of_memory-field-date">Indica la fecha y hora en la que ocurrió el problema</span><br />
												<span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-out_of_memory-field-error">Indica el error de la base de datos que desencadenó el mail</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</asp:Panel>
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-user_actions_notifications-title">Notificaciones por email de acciones de usuarios</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-annoying_user_notification-title">Notificaciones por usuario molesto</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder" id="tableAnnoyingUser">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-use_annoying_user">Utilizar funcionalidad</span>:</th> 
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxUseAnnoyingUser" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-use_annoying_user-tip">
															Este parámetro indíca si se utilizará la funcionalidad de usuario "Molesto"
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-max_messages_annoying_user">Cantidad de Mensajes</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxMaxMessagesAnnoyingUser" runat="server" ClientIDMode="Static" autocomplete="off" MaxLength="5" Width="50" TextMode="Number" max="500" min="20" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-max_messages_annoying_user-tip">
															Este parámetro indíca la cantidad de mensajes que tiene que escribir el usuario en forma seguida para considerarlo "Molesto".
															El valor debe estar entre 20 y 500.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-black_list_user">Agregar usuario a Black List</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxAddAnnoyingUserToBlackList" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-black_list_user-tip">
															Este parámetro indíca si el usuario será agregado a la black list cuando haya envíado en forma seguida 
															la cantidad de mensajes configurado
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-annoying_user-discard_messages">Descartar mensajes</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:DropDownList ID="dropdownlistDiscardMessagesFromAnnoyingUser" runat="server" ClientIDMode="Static">
																<asp:ListItem Value="0" Selected="True" data-i18n="configuration-systemsettings-annoying_user-discard_messages-no">No</asp:ListItem>
																<asp:ListItem Value="1" data-i18n="configuration-systemsettings-annoying_user-discard_messages-yes">Si</asp:ListItem>
																<asp:ListItem Value="2" data-i18n="configuration-systemsettings-annoying_user-discard_messages-yes_and_close_case">Si y también cerrar el caso</asp:ListItem>
															</asp:DropDownList>
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-annoying_user-discard_messages-tip">
															Este parámetro indíca si se descartarán los mensajes del usuario una vez que haya escrito en forma seguida
															la cantidad de mensajes configurado
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-notify_supervisors">Notificar a los supervisores en pantalla</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxNotifySupervisorFromScreen" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-notify_supervisors-tip">
															Este parámetro indíca si se notificará a los supervisores cuando un usuario haya escrito en forma seguida
															la cantidad de mensajes configurado
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label withdescription">Marcar mensaje como VIM:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxMarkAnnoyingUserMessageAsVIM" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls">
															Este parámetro indíca si el mensaje del usuario molesto será marcado como VIM
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldAnnoyingUserConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxAnnoyingUserConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAnnoyingUserEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAnnoyingUserEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAnnoyingUserEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
											<yoizen:Message ID="messageAnnoyingUserEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-annoying_user_notification_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-annoying_user_notification-field-date">Indica la fecha y hora del último mensaje</span><br />
												<span class='templatefieldname'>@@FECHA_PRIMER_MENSAJE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-annoying_user_notification-field-first_message">Indica la fecha y hora del primer mensaje</span><br />
												<span class='templatefieldname'>@@USUARIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-annoying_user_notification-field-user">Usuario de Red Social</span><br />
												<span class='templatefieldname'>@@SERVICIO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-annoying_user_notification-field-service">Servicio al que pertenece el usuario</span><br />
												<span class='templatefieldname'>@@CANTIDAD_DE_MENSAJES@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-annoying_user_notification-field-message_count">Cantidad de Mensajes</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateAnnoyingUser" data-i18n="configuration-systemsettings-fill_data-error">Debe completar los datos</asp:CustomValidator>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-agent_action_notifications-title">Notificaciones por email de acciones sobre agentes</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-login_information_created-title">Información de login de agente creado</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldAgentCreatedLoginInformationConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxAgentCreatedLoginInformationConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAgentCreatedLoginInformationEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentCreatedLoginInformationEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAgentCreatedLoginInformationEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentCreatedLoginInformationEmailTemplate" />
											<yoizen:Message ID="messageAgentCreatedLoginInformationEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-login_information_created_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-date">Indica la fecha de creación</span><br />
												<span class='templatefieldname'>@@NOMBRE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-name">Indica el nombre</span><br />
												<span class='templatefieldname'>@@APELLIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-surname">Indica el apellido</span><br />
												<span class='templatefieldname'>@@USERNAME@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-username">Indica el nombre de usuario</span><br />
												<span class='templatefieldname'>@@CLAVE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-password">Indica la contraseña establecida</span><br />
												<span class='templatefieldname'>@@URL@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-login_information_created-field-url">Indica la URL para acceder al agente</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-agent_password_change-title">Información de cambio de contraseña a agente</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" rel="annoyinguser">
										<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
										<td class="data">
											<asp:HiddenField ID="hiddenFieldAgentPasswordChangedConnection" runat="server" ClientIDMode="Static" />
											<select id="listboxAgentPasswordChangedConnection">
												<option value="" selected="selected" data-i18n="globals-email_default">Casilla por defecto</option>
											</select>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAgentPasswordChangedEmailSubject" runat="server" MaxLength="200" Width="90%" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordChangedEmailSubject" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxAgentPasswordChangedEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordChangedEmailTemplate" />
											<yoizen:Message ID="messageAgentPasswordChangedEmailTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
												<span data-i18n="configuration-systemsettings-agent_password_change_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-date">Indica la fecha de creación</span><br />
												<span class='templatefieldname'>@@NOMBRE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-name">Indica el nombre</span><br />
												<span class='templatefieldname'>@@APELLIDO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-surname">Indica el apellido</span><br />
												<span class='templatefieldname'>@@USERNAME@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-username">Indica el nombre de usuario</span><br />
												<span class='templatefieldname'>@@CLAVE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-password">Indica la contraseña establecida</span><br />
												<span class='templatefieldname'>@@URL@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-agent_password_change-field-url">Indica la URL para acceder al agente</span>
											</yoizen:Message>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="divGlobalConfig">
				<div class="seccion collapsable" id="divEmails">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-server_smtp_configuration-title">Configuración de servidor SMTP para envío de mails</h2>
					</div>
					<div class="contents">
						<yoizen:Message runat="server" Type="Information">
							<span data-i18n="configuration-systemsettings-emailconnection-tip">Usted puede optar por utilizar el asistente de configuración. Para ello haga clic</span> <a href="javascript:ConfigureEmailConnection()" data-i18n="configuration-servicesfacebook-here">aquí</a>.
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
								<td class="data">
									<asp:HiddenField ID="hiddenFieldDefaultEmailConnection" runat="server" ClientIDMode="Static" />
									<select id="listboxDefaultEmailConnection">
										<option value="" selected="selected" data-i18n="globals-email_non_selected">Ninguna</option>
									</select>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-report_exportation-title">Exportación de reportes</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-daily_report-title">Reporte diario</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-systemsettings-daily_report-tip">
									Luego de la generación del reporte diario se enviará un mail a los usuarios especificados a continuación
									para indicar cómo obtener el reporte
								</yoizen:Message>
								<table id="tableGenerateDailyReport" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-generate_daily_report">Generar reporte diario</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxGenerateDailyReport" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid prs" data-i18n="configuration-systemsettings-generate_daily_report-tip">
															Este campo indica si se generarán reportes diarios del día anterior
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-daily_report_ftp_enable">Habilitar FTP</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0" style="width: 100%">
												<tbody>
													<tr>
														<td class="vMid prs checkbox">
															<asp:CheckBox ID="checkboxEnableFtpDailyReport" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid prs" data-i18n="configuration-systemsettings-daily_report_ftp_enable-tip">
															Este campo indica si debe guardarse los reportes en un repositorio FTP
														</td>
													</tr>
												</tbody>
											</table>
											<div class="subseccion" id="trDailyReportFtpCredentials">
												<div class="title">
													<h2 data-i18n="configuration-systemsettings-daily_report_ftp_enable-config">Configuración de FTP</h2>
												</div>
												<div class="contents">
													<table width="100%" border="0" class="uiInfoTable noBorder">
														<tr class="dataRow dataRowSeparator" rel="searchByFtps">
															<th class="label noinput"><span data-i18n="configuration-systemsettings-daily_report_ftp_enable-connection">Conexión</span>:</th>
															<td class="data">
																<asp:ListBox ID="listboxFtps" runat="server" ClientIDMode="Static" DataValueField="ID" DataTextField="Name" SelectionMode="Single" Width="450px" />
																<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSearchFtp" />
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" rel="searchByFtps">
															<th class="label noinput"><span data-i18n="configuration-systemsettings-daily_report_ftp_enable-ftp_directory">Directorio</span>:</th>
															<td class="data">
																<asp:TextBox ID="textboxFtpDirectoryDailyReports" runat="server" Width="400" Text="/" />
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" rel="searchByFtps">
															<th class="label noinput"><span data-i18n="configuration-systemsettings-daily_report_ftp_enable-zip_excel">Comprimir archivos Excel</span>:</th>
															<td class="data">
																<asp:CheckBox ID="checkboxDailyReportsZipExcel" runat="server" />
															</td>
														</tr>
														<tr class="dataRow dataRowSeparator" rel="searchByFtps">
															<th class="label noinput"><span data-i18n="configuration-systemsettings-daily_report_ftp_enable-zip_csv">Comprimir archivos CSV</span>:</th>
															<td class="data">
																<asp:CheckBox ID="checkboxDailyReportsZipCSV" runat="server" />
															</td>
														</tr>
													</table>
												</div>
											</div>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderDailyReportsToMantain" runat="server">
										<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-daily_reports_to_save">Días de histórico a mantener</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxDailyReportsToMantain" runat="server" MaxLength="2" CssClass="numeric" Width="50" Text="7" ClientIDMode="Static" min="1" max="31" />
															</td>
															<td class="vMid prs" data-i18n="configuration-systemsettings-daily_reports_to_save-tip">
																Este campo indica la cantidad máxima de días que se guardarán para los reporetes (Máximo valor: 31 días). Los reportes
																viejos se irán borrando
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-report_type">Tipos de reporte a generar</span>:</th>
										<td class="data">
											<asp:CheckBoxList ID="checkboxlistDailyReportsToGenerate" runat="server" RepeatColumns="2"></asp:CheckBoxList>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDailyReportsEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
											<yoizen:Message ID="messageDailyReportsEmailSubject" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
												<span data-i18n="configuration-systemsettings-daily_report_mail_fields">Puede utilizar los siguientes campos dentro del asunto</span>:<br />
												<ul>
													<li><span class='templatefieldname'>@@TIPOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-daily_report_mail-field-types">Indica los tipos de reporte generados</span></li>
													<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-daily_report_mail-field-date">Indica la fecha del reporte diario</span></li>
												</ul>
											</yoizen:Message>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-alert_recipients">Emails destinatarios de la alerta</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDailyReportsEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxDailyReportsEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
											<yoizen:Message ID="messageDailyReportsEmailTemplate" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
												<span data-i18n="configuration-systemsettings-alert_mail_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
												<ul>
													<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="[html]configuration-systemsettings-alert_mail-field-link">Indica el LINK desde donde se descargará el reporte generado. Será reemplazado por el texto <u>aquí</u></span></li>
													<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-alert_mail-field-date">Indica la fecha a la que pertenece el reporte</span></li>
													<li><span class='templatefieldname'>@@TIPOS@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-alert_mail-field-types">Indica los tipos de reportes generados</span></li>
												</ul>
											</yoizen:Message>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label"><span data-i18n="configuration-systemsettings-reports_export_format">Formatos de salida</span>:</th>
										<td class="data">
											<asp:DropDownList ID="dropdownlistDailyReportsExportFormat" runat="server">
												<asp:ListItem Value="1" Text="Excel"/>
												<asp:ListItem Value="2" Text="Separados por Coma (CSV)" Selected="True" data-i18n="reports-globals-export-format-csv" />
											</asp:DropDownList>
											<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-excel-warning">
												En caso de utilizar el formato Excel y que la cantidad de registros supere el máximo permitido por Excel, se utilizará CSV.
											</yoizen:Message>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderDailyReportsDeleteLocalCopy" runat="server" Visible="false">
										<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
											<th class="label"><span>Borrar archivos locales luego de subir a Storage</span>:</th>
											<td class="data">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:CheckBox ID="checkboxDailyReportsDeleteLocalCopy" runat="server" />
															</td>
															<td class="vMid pls">
																Este parámetro indica si luego de subir los reportes a Storage se deben borrar los archivos locales
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateDailyReportsSettings" data-i18n="[html]configuration-systemsettings-specify_data-error">
										Debe especificar:
										<ul>
											<li>La cantidad de días a mantener en el histórico (un número entre 1 y 31)</li>
											<li>Los tipos de reporte a generar</li>
											<li>El asunto del email</li>
											<li>Los destinatarios del email</li>
											<li>La plantilla del email</li>
										</ul>
									</asp:CustomValidator>
								</div>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-mail_exportation-title">Exportación vía email</h2>
							</div>
							<div class="contents">
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-max_records_to_export">Cantidad de registros a partir de la cual se exporta por mail</span>:</th>
										<td class="data noinput">
											<asp:Literal ID="literalMaxRecordsToExport" runat="server" />
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderMinutesToAbortExporting" runat="server">
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-systemsettings-mail_exportation-minutes_to_abort">Cantidad de minutos para abortar un reporte</span>:</th>
											<td class="data noinput">
												<table class="uiGrid" cellspacing="0" cellpadding="0">
													<tbody>
														<tr>
															<td class="vMid prs">
																<asp:TextBox ID="textboxMinutesToAbortExporting" runat="server" ClientIDMode="Static" MaxLength="3" Text="15" />
																<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMinutesToAbortExporting" />
																<asp:CompareValidator runat="server" ControlToValidate="textboxMinutesToAbortExporting" Type="Integer" Operator="GreaterThan" ValueToCompare="0" />
															</td>
															<td class="vMid pls" data-i18n="configuration-systemsettings-mail_exportation-minutes_to_abort-tip">
																Este parámetro indica cuantos minutos se aguardará como máximo para la generación de un reporte grande antes de abortarlo y continuar
																con el siguiente
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</asp:PlaceHolder>
								</table>
								<div class="subsubseccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-mail_exportation-success-title">Notificación exitosa</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxExportEmailSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxExportEmailSubject" />
													<yoizen:Message ID="messageExportEmailSubject" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
														<span data-i18n="configuration-systemsettings-mail_exportation_subject_fields">Puede utilizar los siguientes campos dentro del asunto</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@TIPO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation_subject-field-type">Indica el tipo de reporte que se solicitó</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxExportEmailTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxExportEmailTemplate" />
													<yoizen:Message ID="messageExportEmailTemplate" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
														<span data-i18n="configuration-systemsettings-mail_exportation_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@LINK@@</span>: <span class='templatefielddescription' data-i18n="[html]configuration-systemsettings-mail_exportation-field-link">Indica el LINK desde donde se descargará el reporte generado. Será reemplazado por el texto <u>aquí</u></span></li>
															<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation-field-date">Indica la fecha en la que se solicitó el reporte</span></li>
															<li><span class='templatefieldname'>@@FECHA_GENERADO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation-field-generated_date">Indica la fecha en la que se generó el reporte</span></li>
															<li><span class='templatefieldname'>@@TIPO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation-field-type">Indica el tipo de reporte que se solicitó</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="subsubseccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-mail_exportation-aborted-title">Notificación por generación de reporte abortada</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxExportEmailAbortedSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxExportEmailAbortedSubject" />
													<yoizen:Message ID="messageExportEmailAbortedSubject" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
														<span data-i18n="configuration-systemsettings-mail_exportation_subject_fields">Puede utilizar los siguientes campos dentro del asunto</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@TIPO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation_subject-field-type">Indica el tipo de reporte que se solicitó</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxExportEmailAbortedTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
													<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxExportEmailAbortedTemplate" />
													<yoizen:Message ID="messageExportEmailAbortedTemplate" runat="server" Type="Information" Small="true" style="margin-top: 10px" ClientIDMode="Static">
														<span data-i18n="configuration-systemsettings-mail_exportation_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
														<ul>
															<li><span class='templatefieldname'>@@TIPO@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation-field-type">Indica el tipo de reporte que se solicitó</span></li>
															<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-mail_exportation-field-date">Indica la fecha en la que se solicitó el reporte</span></li>
														</ul>
													</yoizen:Message>
												</td>
											</tr>
										</table>
									</div>
								</div>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-schedule_report-title">Reporte programado</h2>
							</div>
							<div class="contents">
								<table id="tableGenerateScheduleReport" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" rel="generatedailyreport">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-daily_reports_to_save">Días de histórico a mantener</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxScheduleReportsToMantain" runat="server" MaxLength="2" CssClass="numeric" Width="50" Text="20" ClientIDMode="Static" min="1" max="31" />
														</td>
														<td class="vMid prs" data-i18n="configuration-systemsettings-daily_reports_to_save-tip">
															Este campo indica la cantidad máxima de días que se guardarán para los reporetes (Máximo valor: 31 días). Los reportes
															viejos se irán borrando
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</div>
				<div class="seccion collapsable" id="divBitLy" clientidmode="Static" runat="server">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-bitly_configuration-title">Configuración de bit.ly</h2>
					</div>
					<div class="contents">
						<table id="tableBitLy" width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-use_own">Utilizar propio</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:CheckBox ID="checkboxBitLyCustomShortener" runat="server" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-use_own-tip">
													Este parámetro indica si se utiliza un usuario distinto para utilizar el acortador de URL de BitLy
													al que viene por defecto
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator" rel="usecustombitly">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-access_token">Access Token</span></th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:TextBox ID="textboxBitLyAccessToken" runat="server" Width="300" spellcheck="false" />
													<asp:RequiredFieldValidator ID="requiredfieldvalidatorBitLyAcessToken" ClientIDMode="Static" runat="server" ControlToValidate="textboxBitLyAccessToken" />
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-access_token-tip">
													Este parámetro indica el Access Token que se utilizará para realizar la conexión a bit.ly para acortar URLs
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div class="seccion collapsable" id="divAllowToExtendBusinessData" clientidmode="Static" runat="server">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-bussines_data-title">Datos de negocio</h2>
					</div>
					<div class="contents">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-systemsettings-bussines_data-information">
							En esta sección se puede definir los controles que facilitarán el ingreso del código de cliente.<br />
							Un perfil de usuario puede tener más de un código de cliente (si así se lo permite), los cuales se separarán por una coma.
							Cada código de cliente puede estar compuestos por más de un campo. Internamente (para ser almacenado) cada campo estará separado por un numeral.
						</yoizen:Message>
						<asp:HiddenField ID="hiddenExtendedProfileBusinessCodeFields" runat="server" ClientIDMode="Static"></asp:HiddenField>
						<table id="tableExtendedProfileBusinessCodeFields" class="reporte advanced-input" cellspacing="0" rules="all" border="1">
							<thead>
								<tr class="header">
									<th style="width: 20px"></th>
									<th style="width: 200px" scope="col"><span data-i18n="globals-name">Nombre</span></th>
									<th style="max-width: 200px" scope="col"><span data-i18n="configuration-systemsettings-description">Descripción</span></th>
									<th style="width: 150px" scope="col"><span data-i18n="configuration-systemsettings-data_type">Tipo de dato</span></th>
									<th scope="col"><span data-i18n="configuration-systemsettings-advanced">Avanzado</span></th>
								</tr>
							</thead>
							<tbody></tbody>
							<tfoot>
								<tr>
									<td style="text-align: center"><a id="aExtendedProfileBusinessCodeFieldsAdd" title="Agregar nuevo campo"><span class="fa fa-lg fa-plus-square"></span></a></td>
									<td colspan="4"></td>
								</tr>
							</tfoot>
						</table>
						<div class="validationerror" style="display: none"><asp:CustomValidator ID="customvalidatorExtendedProfileBusinessCodeFields" runat="server" EnableClientScript="true" ClientValidationFunction="ValidateExtendedProfileBusinessCodeFields" SkinID="validationerror" /></div>
						<div class="seccion collapsable" id="divBusinessDataRegex" clientidmode="Static" runat="server">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-user_bussines_data_configuration-title">Configuración de Datos de negocio de usuarios</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-systemsettings-bussines_data_regex_validation">
									Puede definir una expresión regular a utilizar para validar los datos de negocio para cuando no se desee utilizar controles
									que permitan el ingreso de la información.
								</yoizen:Message>
								<table width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator">
										<th class="label"><span data-i18n="configuration-systemsettings-regex_to_validate">Expresión regular a validar</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxBusinessDataRegex" runat="server" Width="90%" spellcheck="false" autocomplete="false" ClientIDMode="Static" />
											<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxBusinessDataRegex" />
											<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateBusinessDataRegex" />
											<yoizen:Message runat="server" Type="Information" style="margin-top: 10px;" LocalizationKey="[html]configuration-systemsettings-regex_to_validate-tip">
												Este parámetro indica la expresión regular a utilizar para verificar los datos de negocios de los usuarios. Recuerde que la expresión regular
												debe contemplar poder ingresar varios códigos de clientes separados por coma (,).
												La expresión regular debe iniciar con el caracter de inicio de línea <span class="underline bold">^</span> y
												debe finalizar con el caracter de inicio de línea <span class="underline bold">$</span>.
											</yoizen:Message>
											<div id="divBusinessDataRegexVisualizer" style="display: none" class="subseccion collapsable">
												<div class="title">
													<h2 data-i18n="configuration-systemsettings-regex_visualization-title">Visualización de la expresión regular</h2>
												</div>
												<div class="contents">
												</div>
											</div>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-data_format_message">Mensaje informativo de formato</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs" style="width: 70%">
															<asp:TextBox ID="textboxBusinessDataFormatMessage" runat="server" Width="90%" autocomplete="false" TextMode="MultiLine" Rows="3" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxBusinessDataFormatMessage" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-data_format_message-tip">
															Este parámetro indica el mensaje informativo del formato de los datos de negocio
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-error_message">Mensaje de error</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs" style="width: 70%">
															<asp:TextBox ID="textboxBusinessDataWrongInputMessage" runat="server" Width="90%" autocomplete="false" TextMode="MultiLine" Rows="3" />
															<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxBusinessDataWrongInputMessage" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-error_message-tip">
															Este parámetro indica el mensaje de error a mostrar cuando se ingrese datos incorrectos
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</div>
				<div class="seccion collapsable" id="divAllowToExtendProfile" clientidmode="Static" runat="server">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-extended_profile_fields-title">Campos adicionales para el perfil de usuario</h2>
					</div>
					<div class="contents">
						<asp:HiddenField ID="hiddenExtendedProfileFields" runat="server" ClientIDMode="Static"></asp:HiddenField>
						<yoizen:Message runat="server" Type="Information">
							<span data-i18n="configuration-systemsettings-extended_profile_fields-info">
								Puede expandir los datos del perfil del usuario con los campos que se deseen agregar. Por cada dato se solicita
							</span>:
							<ul>
								<li><span class="templatefieldname" data-i18n="globals-name">Nombre</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_profile-field-name">Una identificación del campo. Debe ser un valor sin espacios. No será visible para los usuarios ni los agentes</span></li>
								<li><span class="templatefieldname" data-i18n="configuration-systemsettings-description">Descripción</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_profile-field-description">Una descripción del campo. Es un texto amigable que se utilizará para solicitar el campo</span></li>
								<li><span class="templatefieldname" data-i18n="configuration-systemsettings-data_type">Tipo de dato</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_profile-field-data_type">El tipo de dato del campo</span></li>
								<li>
									<span class="templatefieldname" data-i18n="configuration-systemsettings-advanced">Avanzado</span>: 
									<span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_profile-field-advanced">
										Definición avanzada para el control lista. 
										La lista deberá tener en cada renglón un valor (que no puede contener espacios ni caracteres especiales) seguido por un igual (=)
										seguido por el texto. El valor no será visible para el usuario, mientras que el texto será lo que el usuario verá.
									</span>
								</li>
							</ul>
						</yoizen:Message>
						<table id="tableExtendedProfileFields" class="reporte advanced-input" cellspacing="0" rules="all" border="1">
							<thead>
								<tr class="header">
									<th style="width: 20px"></th>
									<th style="width: 200px" scope="col"><span data-i18n="globals-name">Nombre</span></th>
									<th style="max-width: 200px" scope="col"><span data-i18n="configuration-systemsettings-description">Descripción</span></th>
									<th style="width: 150px" scope="col"><span data-i18n="configuration-systemsettings-data_type">Tipo de dato</span></th>
									<th scope="col"><span data-i18n="configuration-systemsettings-advanced">Avanzado</span></th>
								</tr>
							</thead>
							<tbody></tbody>
							<tfoot>
								<tr>
									<td style="text-align: center"><a id="aExtendedProfileFieldsAdd" title="Agregar nuevo campo"><span class="fa fa-lg fa-plus-square"></span></a></td>
									<td colspan="4"></td>
								</tr>
							</tfoot>
						</table>
						<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateExtendedProfileFields" SkinID="validationerror" /></div>
					</div>
				</div>
				<div class="seccion collapsable" id="divAllowToExtendCase" clientidmode="Static" runat="server">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-extended_case_fields-title">Campos adicionales para los Casos</h2>
					</div>
					<div class="contents">
						<asp:HiddenField ID="hiddenExtendedCaseFields" runat="server" ClientIDMode="Static"></asp:HiddenField>
						<yoizen:Message runat="server" Type="Information">
							<span data-i18n="configuration-systemsettings-extended_case_fields-info">
								Puede expandir los datos de casos con los campos que se deseen agregar. Por cada dato se solicita
							</span>:
							<ul>
								<li><span class="templatefieldname" data-i18n="globals-name">Nombre</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_case-field-name">Una identificación del campo. Debe ser un valor sin espacios. No será visible para los usuarios ni los agentes</span></li>
								<li><span class="templatefieldname" data-i18n="configuration-systemsettings-description">Descripción</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_case-field-description">Una descripción del campo. Es un texto amigable que se utilizará para solicitar el campo</span></li>
								<li><span class="templatefieldname" data-i18n="configuration-systemsettings-data_type">Tipo de dato</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_case-field-data_type">El tipo de dato del campo</span></li>
								<li>
									<span class="templatefieldname" data-i18n="configuration-systemsettings-advanced">Avanzado</span>: 
									<span class='templatefielddescription' data-i18n="configuration-systemsettings-extended_case-field-advanced">
										Definición avanzada para el control lista. 
										La lista deberá tener en cada renglón un valor (que no puede contener espacios ni caracteres especiales) seguido por un igual (=)
										seguido por el texto. El valor no será visible para el usuario, mientras que el texto será lo que el usuario verá.
									</span>
								</li>
							</ul>
						</yoizen:Message>
						<table id="tableExtendedCaseFields" class="reporte advanced-input" cellspacing="0" rules="all" border="1">
							<thead>
								<tr class="header">
									<th style="width: 20px"></th>
									<th style="width: 200px" scope="col"><span data-i18n="globals-name">Nombre</span></th>
									<th style="max-width: 200px" scope="col"><span data-i18n="configuration-systemsettings-description">Descripción</span></th>
									<th style="width: 150px" scope="col"><span data-i18n="configuration-systemsettings-data_type">Tipo de dato</span></th>
									<th scope="col"><span data-i18n="configuration-systemsettings-advanced">Avanzado</span></th>
								</tr>
							</thead>
							<tbody></tbody>
							<tfoot>
								<tr>
									<td style="text-align: center"><a id="aExtendedCaseFieldsAdd" title="Agregar nuevo campo"><span class="fa fa-lg fa-plus-square"></span></a></td>
									<td colspan="4"></td>
								</tr>
							</tfoot>
						</table>
						<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateExtendedCaseFields" SkinID="validationerror" /></div>
					</div>
				</div>
				<div class="seccion collapsable" id="divPushNotifications" runat="server" clientidmode="Static">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-push_notifications-title">Notificaciones push</h2>
					</div>
					<div class="contents">
						<datalist id="datalistTickmarks"></datalist>
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label withdescription"><span data-i18n="configuration-systemsettings-push_notifications-service_bus-use_websockets">Utilizar WebSockets</span>:</th>
								<td class="data">
									<asp:DropDownList ID="dropdownlistPushNotificationsServiceBusProtocol" runat="server">
										<asp:ListItem Value="1" data-i18n="globals-yes">Si</asp:ListItem>
										<asp:ListItem Value="0" data-i18n="globals-no">No</asp:ListItem>
									</asp:DropDownList>
								</td>
							</tr>
							<asp:PlaceHolder ID="placeholderPushNotificationsServiceBusConcurrentMessages" runat="server">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-push_notifications-service_bus-concurrent">Mensajes concurrentes</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxPushNotificationsServiceBusConcurrentMessages" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="1000" step="1" EnableTheming="false" />
										<div id="spanPushNotificationsServiceBusConcurrentMessages" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentMessages" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentMessages" MinimumValue="1" MaximumValue="1000" Type="Integer" />
									</td>
								</tr>
							</asp:PlaceHolder>
							<asp:PlaceHolder ID="placeholderPushNotificationsServiceBusConcurrentStatuses" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-push_notifications-service_bus-concurrent_statuses">Estados de Mensajes concurrentes</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxPushNotificationsServiceBusConcurrentStatuses" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
										<div id="spanPushNotificationsServiceBusConcurrentStatuses" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentStatuses" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentStatuses" MinimumValue="1" MaximumValue="500" Type="Integer" />
									</td>
								</tr>
							</asp:PlaceHolder>
							<asp:PlaceHolder ID="placeholderPushNotificationsServiceBusConcurrentMassive" runat="server" Visible="false">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-push_notifications-service_bus-concurrent_massive">Mensajes masivos salientes concurrentes</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxPushNotificationsServiceBusConcurrentMassive" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
										<div id="spanPushNotificationsServiceBusConcurrentMassive" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
										<asp:CompareValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentMassive" Operator="DataTypeCheck" Type="Integer" />
										<asp:RangeValidator runat="server" ControlToValidate="textboxPushNotificationsServiceBusConcurrentMassive" MinimumValue="1" MaximumValue="500" Type="Integer" />
									</td>
								</tr>
							</asp:PlaceHolder>
						</table>
					</div>
				</div>
				<asp:Panel ID="panelMaintenance" runat="server" CssClass="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-manteinance-title">Mantenimiento</h2>
					</div>
					<div class="contents">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-systemsettings-manteinance-information">
							En esta sección se configurará las variables que indican qué registros borrar en la base de datos como mantenimiento diario del sistema.
							Aquellos registros que superen la cantidad de días especificados serán borrados.
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-maintenance_cases">Días para casos cerrados</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxMaintenanceCases" runat="server" Width="80" MaxLength="4" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" min="1" max="1000" />
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaintenanceCases" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxMaintenanceCases" Type="Integer" MinimumValue="1" MaximumValue="1000" />
									<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" LocalizationKey="[html]configuration-systemsettings-maintenance_cases-information">
										Aquellos casos cerrados antes de los días especificados serán borrados. Con ellos, se borrarán:
										<ul>
											<li>
												Los mensajes del caso
												<ul>
													<li>Segmentos</li>
													<li>Archivos adjuntos</li>
													<li>Histórico de estados</li>
													<li class="dependsOnTwitter dependsOnFacebook">Conversaciones</li>
													<li class="dependsOnChat">Mensajes de chat</li>
												</ul>
											</li>
											<li>Histórico de estados</li>
											<li>Etiquetas del caso</li>
											<li>Encuestas</li>
										</ul>
										El valor configurado <span class="bold">NO</span> puede superar los <span class="underline">500</span> días.
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-maintenance_history">Días para datos históricos</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxMaintenanceHist" runat="server" Width="80" MaxLength="4" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" min="1" max="3650" />
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaintenanceHist" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxMaintenanceHist" Type="Integer" MinimumValue="1" MaximumValue="3650" />
									<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" LocalizationKey="[html]configuration-systemsettings-maintenance_history-information">
										Aquellos datos pertenecientes a días anteriores a los especificados serán borrados. Se borrarán:
										<ul>
											<li>
												Agentes
												<ul>
													<li>Consolidado diario</li>
													<li>Login/Logout</li>
												</ul>
											</li>
											<li>
												Colas
												<ul>
													<li>Consolidado diario</li>
												</ul>
											</li>
											<li>Consolidado de etiquetas</li>
											<li>Consolidado de servicios</li>
											<li>Consolidado de filtros</li>
											<li>Eventos de Usuarios</li>
										</ul>
										El valor configurado <span class="bold">NO</span> puede superar los <span class="underline">3560</span> días.
									</yoizen:Message>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-maintenance_history_by_interval">Días para datos históricos por intervalo</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxMaintenanceHistByInterval" runat="server" Width="80" MaxLength="3" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" min="1" max="730" />
									<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxMaintenanceHistByInterval" />
									<asp:RangeValidator runat="server" ControlToValidate="textboxMaintenanceHistByInterval" Type="Integer" MinimumValue="1" MaximumValue="730" />
									<yoizen:Message runat="server" Type="Information" Small="true" style="margin-top: 10px" LocalizationKey="[html]configuration-systemsettings-maintenance_history_by_interval-information">
										Aquellos datos pertenecientes a días anteriores a los especificados serán borrados. Se borrarán:
										<ul>
											<li>
												Agentes
												<ul>
													<li>Consolidado por intervalo</li>
												</ul>
											</li>
											<li>
												Colas
												<ul>
													<li>Consolidado por intervalo</li>
												</ul>
											</li>
										</ul>
										El valor configurado <span class="bold">NO</span> puede superar los <span class="underline">180</span> días.
									</yoizen:Message>
								</td>
							</tr>
						</table>
					</div>
				</asp:Panel>
				<asp:Panel ID="panelTimeZoneConfiguration" runat="server" CssClass="seccion collapsable" ClientIDMode="Static">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-time_zones_configuration-title">Configuración de Husos horarios</h2>
					</div>
					<div class="contents">
						<yoizen:Message runat="server" Type="Information" LocalizationKey="[html]configuration-systemsettings-time_zones_configuration-information">
							En esta sección se configurará el huso horario general de <span class="productname"></span> y los distintos husos horarios en los cuales se desea
							consolidar la información
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label noinput"><span data-i18n="configuration-systemsettings-server_time_zone">Huso horario servidor</span>:</th>
								<td class="data">
									<asp:Literal ID="literalTimeZoneServer" runat="server" />
									<asp:Literal ID="literalTimeZoneServerDST" runat="server"> (con horario de verano)</asp:Literal>
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-default_time_zone">Huso horario por defecto</span>:</th>
								<td class="data">
									<asp:HiddenField ID="hiddenDefaultTimeZone" runat="server" ClientIDMode="Static"></asp:HiddenField>
									<select id="selectDefaultTimeZone"></select>
								</td>
							</tr>
						</table>
						<asp:Panel ID="panelTimeZoneConsolidation" runat="server" CssClass="subseccion">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-time_zone_consolidation-title">Consolidación diaria</h2>
							</div>
							<div class="contents">
								<yoizen:Message runat="server" Type="Information" LocalizationKey="configuration-systemsettings-time_zone_consolidation">
									Se pueden definir hasta 3 husos horarios en los que se desea que se consolide la información diaria. Por defecto estará configurado en el huso
									horario del servidor, pero se podrá agregar distintos husos horarios.
								</yoizen:Message>
								<div id="divTimeZonesForConsolidation"></div>
								<asp:HiddenField ID="hiddenTimeZonesToConsolide" runat="server" ClientIDMode="Static"></asp:HiddenField>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateTimeZones" data-i18n="configuration-systemsettings-time_zone_consolidation-error">Debe completar los husos horarios. No se pueden repetir</asp:CustomValidator>
								</div>
							</div>
						</asp:Panel>
					</div>
				</asp:Panel>
            </div>
			<div id="divMoreServices" runat="server" visible="false" clientidmode="static">
				<asp:PlaceHolder ID="placeholderAllowYFlow" runat="server" Visible="false">
					<div id="divYFlow" class="seccion collapsable">
						<div class="title">
							<h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-yFlow-title">yFlow</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label">Utilizar <span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span> yFlow:</th>
									<td class="data">
										<asp:CheckBox ID="checkboxEnableYFlow" runat="server" ClientIDMode="Static" />
									</td>
								</tr>
								<tr hidden="hidden">
									<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url">Url yFlow</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxYFlowUrl" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url_web">Url yFlow Api</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxYFlowUrlWeb" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url_api">Url yFlow Web</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxYFlowUrlApi" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
									</td>
								</tr>
								<asp:Panel ID="panelAccessYFlow" runat="server">
									<tr class="dataRow dataRowSeparator" rel="useYFlow">
										<th class="label"><span data-i18n="configuration-systemsettings-access_token">Access Token</span>:</th>
										<td class="data">
											<div style="display: flex; flex-direction: row; align-items: center;">
												<span id="spanAccessTokenOk" runat="server" ClientIDMode="static" class="fa fa-lg fa-yes"></span>
												<span id="spanAccessTokenError" runat="server" ClientIDMode="static" class="fa fa-lg fa-no"></span>
												<label class="uiButton" style="margin-left: 3px">
													<button type="button" id="buttonLoginYFlow" data-i18n="login-login">Iniciar Sesión</button>
												</label>
											</div>
										</td>
										<td><div id="divAccessYFlowError" class="validationerror" style="display: none"><div></div></div></td>
									</tr>
								</asp:Panel>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-timeout">Timeout</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxYFlowTimeout" runat="server" MaxLength="2" Width="80" TextMode="Number" min="1" max="60" Text="1" ClientIDMode="Static" style="text-align: right" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-timeout-tip">
														Indica el timeout (segundos) al realizar una invocación (el valor elegido debe estar entre 1 y 60 segundos).
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-derivation_enabled">Derivación activada</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxYFlowDerivationEnabled" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-derivation_enabled-tip">
														Indica si está habilitada la derivación a agentes para informar en la invocación a yFlow
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-invocation-failed">Falla en la invocación de yFlow</span>:</th>
									<td class="data">
										<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">                 
											<asp:TextBox ID="textboxTagInvocation" runat="server" ClientIDMode="Static" />
										</div>
										 <yoizen:Message ID="messageFirstAutomaticActionReply" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-yflow-invocation-failed-tip">
											Este parámetro indica si se aplicará una etiqueta en caso de presentarse una falla al invocar a yFlow.
										 </yoizen:Message>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYFlow">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-timeout-failed">Falla por timeout</span>:</th>
									<td class="data">
										<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">                 
											<asp:TextBox ID="textboxTagTimeout" runat="server" ClientIDMode="Static" />
										</div>
										 <yoizen:Message ID="messageTagTimeout" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-yflow-timeout-failed-tip">
											Este parámetro indica si se aplicará una etiqueta en caso de presentarse un timeout al invocar a yFlow.
										 </yoizen:Message>
									</td>
								</tr>
							</table>						
							<asp:PlaceHolder ID="placeholderAllowYFlowContingency" runat="server" Visible="false">
								<div class="subseccion collapsable">
									<div class="title">
										<h2 data-i18n="configuration-systemsettings-yFlow-contingency-title">yFlow Contingencia</h2>
									</div>
									<div class="contents">
										<table width="100%" border="0" class="uiInfoTable noBorder">
											<tr class="dataRow dataRowSeparator">
												<th class="label">Utilizar <span class="yzn-yFlowISO" style="font-size: 1.25em; margin-left: 3px"></span> yFlow Contingencia:</th>
												<td class="data">
													<asp:CheckBox ID="checkboxEnableYFlowContingency" runat="server" ClientIDMode="Static" />
												</td>
											</tr>
											<tr hidden="hidden">
												<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url">Url yFlow</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxYFlowContingencyUrl" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="useYFlow">
												<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url_web">Url yFlow Api</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxYFlowContingencyUrlWeb" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="useYFlow">
												<th class="label"><span data-i18n="configuration-systemsettings-yFlow_url_api">Url yFlow Web</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxYFlowContingencyUrlApi" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
												</td>
											</tr>
											<asp:Panel ID="panelAccessYFlowContingency" runat="server">
												<tr class="dataRow dataRowSeparator" rel="useYFlow">
													<th class="label"><span data-i18n="configuration-systemsettings-access_token">Access Token</span>:</th>
													<td class="data">
														<div style="display: flex; flex-direction: row; align-items: center;">
															<span id="spanAccessTokenContingencyOk" runat="server" ClientIDMode="static" class="fa fa-lg fa-yes"></span>
															<span id="spanAccessTokenContingencyError" runat="server" ClientIDMode="static" class="fa fa-lg fa-no"></span>
															<label class="uiButton" style="margin-left: 3px">
																<button type="button" id="buttonLoginYFlowContingency" data-i18n="login-login">Iniciar Sesión</button>
															</label>
														</div>
													</td>
													<td><div id="divAccessYFlowContingencyError" class="validationerror" style="display: none"><div></div></div></td>
												</tr>
											</asp:Panel>
											<tr class="dataRow dataRowSeparator" rel="useYFlow">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-timeout">Timeout</span>:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<asp:TextBox ID="textboxYFlowContingencyTimeout" runat="server" MaxLength="2" Width="80" TextMode="Number" min="1" max="60" Text="1" ClientIDMode="Static" style="text-align: right" />
																</td>
																<td class="vMid pls" data-i18n="configuration-systemsettings-timeout-tip">
																	Indica el timeout (segundos) al realizar una invocación (el valor elegido debe estar entre 1 y 60 segundos).
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" rel="useYFlow">
												<th class="label withdescription"><span data-i18n="reports-globals-tag-title">Etiqueta</span>:</th>
												<td class="data">
													<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">                 
														<asp:TextBox ID="textboxTagLabel" runat="server" ClientIDMode="Static" />
													</div>
												</td>
											</tr>
										</table>		
									</div>
								</div>
							</asp:PlaceHolder>
							<div class="subseccion collapsable">
								<div class="title">
									<h2 data-i18n="configuration-systemsettings-yFlow_pending_messages-title">Mensajes pendientes</h2>
								</div>
								<div class="contents">
									<table width="100%" border="0" class="uiInfoTable noBorder">
										<tr class="dataRow dataRowSeparator" rel="useYFlow">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-max_time">Máximo tiempo para dejar pendiente los mensajes</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxYFlowMaxMinutesForPendingMessages" runat="server" MaxLength="2" Width="80" TextMode="Number" style="text-align: right" ClientIDMode="Static" />
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateYFlowMaxMinutesForPendingMessages" />
												<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-yflow-pending_messages-max_time-tip" style="margin-top: 5px">
													Este parámetro indica la cantidad máxima de minutos que pueden estar los mensajes marcados como pendiente por yFlow esperando novedades (descartar, responder o encolar). 
													Pasado esta cantidad de minutos, el sistema realizará la siguiente acción configurada. El límite maximo establecido son 60 minutos.
													Si el valor configurado es mayor al valor configurado para cierre de casos de yFlow, el sistema descartará el mensaje pendiente cuando cierre el caso.
												</yoizen:Message>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator" rel="useYFlow">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-action_after_max_time">Acción a tomar con mensajes pendientes luego del tiempo</span>:</th>
											<td class="data">
												<asp:DropDownList ID="dropdownlistYFlowActionAfterMaxMinutesForPendingMessages" runat="server" ClientIDMode="Static">
													<asp:ListItem Value="1" data-i18n="CasePendingMessagesActions.Discard">Descartar</asp:ListItem>
													<asp:ListItem Value="2" data-i18n="CasePendingMessagesActions.Enqueue">Encolar</asp:ListItem>
												</asp:DropDownList>
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator" rel="useYFlow">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-statuses_callback">Endpoint a invocar estados de error</span>:</th>
											<td class="data">
												<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-yflow-pending_messages-statuses_callback-tip" style="margin-top: 5px">
													El Endpoint que se puede configurar a continuación permite notificar a una URL específica errores relacionados a acciones sobre mensajes pendientes. En caso
													de no configurar, no se notificará. El Endpoint debe ser un servicio REST que reciba un POST con un JSON que contiene la información del error.
												</yoizen:Message>
												<div id="divYFlowPendingMessagesCallbackEndpoint" class="http-request">
													<asp:HiddenField ID="hiddenYFlowPendingMessagesCallbackEndpoint" runat="server" ClientIDMode="Static" />
												</div>
												<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateYFlowPendingMessagesCallbackEndpoint" />
											</td>
										</tr>
										<asp:PlaceHolder ID="placeholderYFlowMaxConcurrentCallsPerSessionPendingMessages" runat="server" Visible="false">
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-concurrent_session">Sesiones concurrentes</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxYFlowMaxConcurrentSessionsPendingMessages" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
													<div id="spanYFlowMaxConcurrentSessionsPendingMessages" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
													<asp:CompareValidator runat="server" ControlToValidate="textboxYFlowMaxConcurrentSessionsPendingMessages" Operator="DataTypeCheck" Type="Integer" />
													<asp:RangeValidator runat="server" ControlToValidate="textboxYFlowMaxConcurrentSessionsPendingMessages" MinimumValue="1" MaximumValue="500" Type="Integer" />
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription"><span data-i18n="configuration-systemsettings-yflow-pending_messages-concurrent_calls_per_session">Mensajes concurrentes por sesión</span>:</th>
												<td class="data">
													<asp:TextBox ID="textboxYFlowMaxConcurrentCallsPerSessionPendingMessages" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
													<div id="spanYFlowMaxConcurrentCallsPerSessionPendingMessages" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
													<asp:CompareValidator runat="server" ControlToValidate="textboxYFlowMaxConcurrentCallsPerSessionPendingMessages" Operator="DataTypeCheck" Type="Integer" />
													<asp:RangeValidator runat="server" ControlToValidate="textboxYFlowMaxConcurrentCallsPerSessionPendingMessages" MinimumValue="1" MaximumValue="500" Type="Integer" />
													<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="configuration-systemsettings-yflow-pending_messages-concurrent_calls_per_session-tip" style="margin-top: 5px">
														Este valor debería ser siempre 1, dado que así se procesará una novedad por cada caso (no habrá procesamiento simultáneo para el mismo caso)
													</yoizen:Message>
												</td>
											</tr>
										</asp:PlaceHolder>
										<asp:PlaceHolder ID="placeholderYFlowPendingMessagesConfig" runat="server" Visible="false">
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription">JWT:</th>
												<td class="data">
													<label class="uiButton" style="margin-left: 3px">
														<button type="button" onclick="YFlowPendingMessagesGenerateJWT()">Generar JWT</button>
													</label>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator">
												<th class="label withdescription">Utilizar callback personalizado para invocaciones:</th>
												<td class="data">
													<asp:DropDownList ID="dropdownlistYFlowPendingMessagesUseCustomCallback" runat="server" ClientIDMode="Static">
														<asp:ListItem Value="0" Selected="True">No</asp:ListItem>
														<asp:ListItem Value="1">Si</asp:ListItem>
													</asp:DropDownList>
												</td>
											</tr>
											<tr class="dataRow dataRowSeparator" id="trFlowPendingMessagesCustomCallbackUrl">
												<th class="label withdescription">URL de callback:</th>
												<td class="data">
													<table class="uiGrid" cellspacing="0" cellpadding="0">
														<tbody>
															<tr>
																<td class="vMid prs">
																	<div class="input-with-icons">
																		<asp:TextBox ID="textboxYFlowPendingMessagesCustomCallbackUrl" runat="server" Width="400" spellcheck="false" ClientIDMode="Static" EnableTheming="false" autotrim="true" />
																		<i class="icon-status icon-status-spin fa fa-spinner fa-spin"></i>
																		<i class="icon-status icon-status-ok fa fa-check-circle"></i>
																		<i class="icon-status icon-status-error fa fa-exclamation-triangle"></i>
																	</div>
																	<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateYFlowPendingMessagesCustomCallbackUrl" />
																</td>
																<td class="vMid pls">
																	URL del callback donde el cliente enviará las notificaciones de casos pendientes para luego ser descargadas por ySocial
																</td>
															</tr>
														</tbody>
													</table>
												</td>
											</tr>
										</asp:PlaceHolder>
									</table>
								</div>
							</div>
							<asp:Panel ID="panelYFlowPendingReplyCases" runat="server" Visible="false" CssClass="subseccion collapsable">
								<div class="title">
									<h2>Casos Pendientes de Respuestas</h2>
								</div>
								<div class="contents">
									<table width="100%" border="0" class="uiInfoTable noBorder">
										<tr class="dataRow dataRowSeparator">
											<th class="label withdescription"><span>Casos concurrentes</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxYFlowPendingReplyCasesConcurrentCalls" runat="server" TextMode="Range" ClientIDMode="Static" Width="400px" min="1" max="500" step="1" EnableTheming="false" />
												<div id="spanYFlowPendingReplyCasesConcurrentCalls" style="display: inline-block; font-style: italic; margin-left: 3px">0</div>
												<asp:CompareValidator runat="server" ControlToValidate="textboxYFlowPendingReplyCasesConcurrentCalls" Operator="DataTypeCheck" Type="Integer" />
												<asp:RangeValidator runat="server" ControlToValidate="textboxYFlowPendingReplyCasesConcurrentCalls" MinimumValue="1" MaximumValue="500" Type="Integer" />
											</td>
										</tr>
									</table>  
								</div>
							</asp:Panel>
							<div id="divYFlowNotifications" class="subseccion collapsable">
								<div class="title">
									<h2 data-i18n="configuration-systemsettings-yFlow_mail_notifications-title">Notificaciones por email relacionadas a yFlow</h2>
								</div>
								<div class="contents">
									<div class="subsubseccion collapsable">
										<div class="title">
											<h2 data-i18n="configuration-systemsettings-authentication_error-title">Error de autenticación</h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
													<td class="data">
														<asp:HiddenField ID="hiddenYFlowAuthenticationFailedConnection" runat="server" ClientIDMode="Static" />
														<select id="listboxYFlowAuthenticationFailedConnection">
															<option value="" selected="selected" data-i18n="globals-email_default">Ninguna</option>
														</select>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowAuthenticationFailedSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowAuthenticationFailedEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" spellcheck="false" />
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowAuthenticationFailedTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
														<yoizen:Message ID="messageYFlowAuthenticationFailedTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
															<span data-i18n="configuration-systemsettings-authentication_failed_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
															<ul>
																<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-authentication_failed-field-date">Indica la hora en la que ocurrió el error de autenticación</span></li>
																<li><span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-authentication_failed-field-error">Contiene más información del error ocurrido</span></li>
															</ul>
														</yoizen:Message>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateYFlowAuthenticationFailedEmail" SkinID="validationerror" data-i18n="configuration-systemsettings-fill_information">Debe completar la información</asp:CustomValidator></div>
										</div>
									</div>
									<div class="subsubseccion collapsable">
										<div class="title">
											<h2 data-i18n="configuration-systemsettings-invocation_error-title">Error de invocación</h2>
										</div>
										<div class="contents">
											<table width="100%" border="0" class="uiInfoTable noBorder">
												<tr class="dataRow dataRowSeparator">
													<th class="label noinput"><span data-i18n="globals-email_connection">Conexión de correo</span>:</th>
													<td class="data">
														<asp:HiddenField ID="hiddenYFlowInvokeFailed" runat="server" ClientIDMode="Static" />
														<select id="listboxYFlowInvokeFailedConnection">
															<option value="" selected="selected" data-i18n="globals-email_default">Ninguna</option>
														</select>
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-subject">Asunto del Email</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowInvokeFailedSubject" runat="server" MaxLength="200" Width="90%" ClientIDMode="Static" />
													</td>
												</tr>
												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-mail_recipients">Emails destinatarios</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowInvokeFailedEmails" runat="server" MaxLength="200" Width="90%" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" spellcheck="false" />
													</td>
												</tr>												<tr class="dataRow dataRowSeparator">
													<th class="label"><span data-i18n="configuration-systemsettings-mail_template">Plantilla del Email</span>:</th>
													<td class="data">
														<asp:TextBox ID="textboxYFlowInvokeFailedTemplate" runat="server" TextMode="MultiLine" Rows="5" style="resize: vertical; max-height: 300px; min-height: 100px; height: 100px" ClientIDMode="Static" />
														<yoizen:Message ID="messageYFlowInvokeFailedTemplateFields" ClientIDMode="Static" runat="server" Type="Information" Small="true" Style="margin-top: 10px">
															<span data-i18n="configuration-systemsettings-invocation_error_fields">Puede utilizar los siguientes campos dentro del texto de la plantilla</span>:<br />
															<ul>
																<li><span class='templatefieldname'>@@FECHA@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-invocation_error-field-date">Indica la hora en la que ocurrió el error de invocación</span></li>
																<li><span class='templatefieldname'>@@ERROR@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-invocation_error-field-error">Contiene más información del error ocurrido</span></li>
																<li><span class='templatefieldname'>@@MENSAJE@@</span>: <span class='templatefielddescription' data-i18n="configuration-systemsettings-invocation_error-field-message">Indica el mensaje en el que ocurrió el error de invocación</span></li>
															</ul>
														</yoizen:Message>
													</td>
												</tr>
											</table>
											<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateYFlowInvokeFailedEmail" SkinID="validationerror" data-i18n="configuration-systemsettings-fill_information">Debe completar la información</asp:CustomValidator></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</asp:PlaceHolder>
				<asp:PlaceHolder ID="placeholderAllowYSmart" runat="server" Visible="false">
					<div id="divYSmart" class="seccion collapsable">
						<div class="title">
							<h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-ySmart-title">ySmart</h2>
						</div>
						<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label">Utilizar <span class="yzn-ySmartISO" style="font-size: 1.25em; margin-left: 3px"></span>ySmart:</th>
									<td class="data">
										<asp:CheckBox ID="checkboxEnableYSmart" runat="server" ClientIDMode="Static" />
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" rel="useYSmart">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-timeout">Timeout</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxYSmartTimeout" runat="server" MaxLength="2" Width="80" TextMode="Number" min="1" max="60" Text="1" ClientIDMode="Static" Style="text-align: right" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-timeout-tip">Indica el timeout (segundos) al realizar una invocación (el valor elegido debe estar entre 1 y 60 segundos).
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>

								<tr class="dataRow dataRowSeparator" rel="useYSmart">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-ysmart-timeout-failed">Falla por timeout</span>:</th>
									<td class="data">
										<div style="display: flex; flex-direction: row; align-items: flex-end; margin-bottom: 5px">
											<asp:TextBox ID="textboxTagTimeout1" runat="server" ClientIDMode="Static" />
										</div>
										<yoizen:Message ID="messageTagTimeout1" runat="server" Type="Information" Small="true" ClientIDMode="Static" LocalizationKey="configuration-systemsettings-ysmart-timeout-failed-tip">
										Este parámetro indica si se aplicará una etiqueta en caso de presentarse un timeout al invocar a ySmart.
                                        </yoizen:Message>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div id="divYSmartProjects" class="seccion collapsable">
                            <div class="title">
                                <h2 data-i18n="configuration-systemsettings-ysmart-projects-title">Proyectos a utilizar</h2>
                            </div>
                            <div class="contents">
                                <table width="95%" border="0" class="uiInfoTable noBorder">
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label" style="width: 200px !important"><span data-i18n="configuration-systemsettings-ysmart-project">Proyecto</span>:</th>
                                        <td class="data">
                                            <asp:HiddenField ID="hiddenProject" runat="server" ClientIDMode="Static"></asp:HiddenField>
                                           <asp:HiddenField ID="hiddenProjectId" runat="server" ClientIDMode="Static" />
                                            <select id="selectProjectToUse"></select>
                                            <a id="anchorProjectReload">
                                                <span class="fa fa-lg fa-sync" title="Refrescar" data-i18n-title="configuration-systemsettings-ysmart-refresh"></span>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label"><span data-i18n="configuration-systemsettings-ysmart-project-id">ID</span>:</th>
                                        <td class="data noinput">
                                            <span id="spanProjectID"></span>
                                        </td>
                                    </tr>
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label"><span data-i18n="configuration-systemsettings-ysmart-project-service_name">Tipo de servicio</span>:</th>
                                        <td class="data noinput">
                                            <span id="spanProjectServiceName"></span>
                                        </td>
                                    </tr>
                                    <tr class="dataRow dataRowSeparator">
                                        <th class="label"><span data-i18n="configuration-systemsettings-ysmart-project-lastchange">Último cambio</span>:</th>
                                        <td class="data noinput">
                                            <span id="spanProjectLastChange"></span>
                                        </td>
                                    </tr>
                                </table>
                                <div class="validationerror" style="display: none">
                                    <asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="ValidateSelectProjectToUse" data-i18n="configuration-serviceschat-configuration-error" />
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderCognitiveServicesEnabled" runat="server" Visible="false">
                    <div class="seccion collapsable">
                        <div class="title">
                            <h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-ySmart-title">ySmart</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label">Utilizar <span class="yzn-ySmartISO" style="font-size: 1.25em"></span>ySmart:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxUseCognitiveServices" runat="server" ClientIDMode="Static" />
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trUseCognitiveServicesToken">
                                    <th class="label"><span data-i18n="configuration-systemsettings-token">Token</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxCognitiveServicesToken" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
                                    </td>
                                </tr>
                                <tr class="dataRow dataRowSeparator" id="trUseCognitiveServicesTokenSecret">
                                    <th class="label"><span data-i18n="configuration-systemsettings-token_secret">Token Secret</span>:</th>
                                    <td class="data">
                                        <asp:TextBox ID="textboxCognitiveServicesTokenSecret" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateCognitiveServicesToken" SkinID="validationerror" data-i18n="configuration-systemsettings-token_secret-error">El Token y el Token Secret para los servicios cognitivos son inválidos</asp:CustomValidator></div>
                        </div>
                    </div>
                </asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderSurveysEnabled" runat="server" Visible="false">
                    <div class="seccion collapsable">
                        <div class="title">
                            <h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-ySurvey-title">ySurvey</h2>
                        </div>
                        <div class="contents">
                            <table width="100%" border="0" class="uiInfoTable noBorder">
                                <tr class="dataRow dataRowSeparator">
                                    <th class="label"><span data-i18n="globals-use">Utilizar</span> <span class="yzn-ySurveyISO" style="font-size: 1.25em"></span>ySurvey:</th>
                                    <td class="data">
                                        <asp:CheckBox ID="checkboxEnableSurveys" runat="server" ClientIDMode="Static" />
                                    </td>
                                </tr>
                            </table>
                            <div class="validationerror" style="display: none">
                                <asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateSurvey" SkinID="validationerror" data-i18n="configuration-systemsettings-ySurvey_validation_error">No se pudo validar el estado del servicio de ySurvey</asp:CustomValidator></div>
                        </div>
                    </div>
                </asp:PlaceHolder>
                <yoizen:Message ID="messageSurveysDisabled" runat="server" Type="Information" Visible="false" LocalizationKey="[html]configuration-systemsettings-surveys_disabled">
					No hay licencias para utilizar el módulo de encuestas de <span class="productname"></span>. Contacte a su administrador
				</yoizen:Message>
				<asp:PlaceHolder ID="placeholderVideoCallsEnabled" runat="server">
					<div class="seccion collapsable">
						<div class="title">
							<h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-videocall-title">Videollamadas</h2>
						</div>
						<div class="contents">
							<div id="divCubiq" class="subseccion collapsable" runat="server">
								<div class="title">
									<h2 data-i18n="configuration-systemsettings-videocall-cubiq-title">Cubiq</h2>
								</div>
								<div class="contents">
									<table width="100%" border="0" class="uiInfoTable noBorder">
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-systemsettings-videocall-cubiq-url">API Url</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxCubiqUrl" runat="server"  Width="90%" ClientIDMode="Static" />
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-systemsettings-videocall-cubiq-api_key">Api Key</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxCubiqApiKey" runat="server" Width="90%" ClientIDMode="Static" />
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-systemsettings-videocall-cubiq-secret">Secret</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxCubiqSecret" runat="server" Width="90%" ClientIDMode="Static" />
											</td>
										</tr>
										<tr class="dataRow dataRowSeparator">
											<th class="label"><span data-i18n="configuration-systemsettings-videocall-cubiq-recording_url">Recording Url</span>:</th>
											<td class="data">
												<asp:TextBox ID="textboxCubiqRecordingUrl" runat="server" Width="90%" ClientIDMode="Static" />
											</td>
										</tr>
									</table>
								</div>
							</div>	
						</div>
					</div>
				</asp:PlaceHolder>
                <asp:PlaceHolder ID="placeholderAllowYUsage" runat="server" Visible="false">
                <div id="divYUsage" class="seccion collapsable">
                    <div class="title">
                        <h2 style="text-transform: none; font-variant: normal" data-i18n="configuration-systemsettings-yUsage-title">yUsage</h2>
                    </div>
                    <div class="contents">
                        <table width="100%" border="0" class="uiInfoTable noBorder">
                            <tr class="dataRow dataRowSeparator">
                                <th class="label">Utilizar <span class="yzn-yUsageISO" style="font-size: 1.25em; margin-left: 3px"></span> yUsage:</th>
                                <td class="data">
                                    <asp:CheckBox ID="checkboxEnableYUsage" runat="server" ClientIDMode="Static" />
                                </td>
                            </tr>
                            <tr class="dataRow dataRowSeparator" rel="useYUsage">
                                <th class="label"><span data-i18n="configuration-systemsettings-yUsage_url">Url yUsage</span>:</th>
                                <td class="data">
                                    <asp:TextBox ID="textboxYUsageUrl" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
                                </td>
                            </tr>
							<tr class="dataRow dataRowSeparator" rel="useYUsage">
								<th class="label"><span data-i18n="configuration-systemsettings-yUsage_urlapi">Url yUsage</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxYUsageUrlApi" runat="server" Width="80%" spellcheck="false" autocomplete="off" ClientIDMode="Static" />
								</td>
							</tr>
                           <tr class="dataRow dataRowSeparator" rel="useYUsage">
								<th class="label">
									<span data-i18n="configuration-systemsettings-access_token">Access Token</span>:
								</th>
								<td class="data">
									<div style="display: flex; flex-direction: row; align-items: center;">
										<span id="spanYUsageTokenOk" runat="server" ClientIDMode="static" class="fa fa-lg fa-yes"></span>
										<span id="spanYUsageTokenError" runat="server" ClientIDMode="static" class="fa fa-lg fa-no"></span>
										<label class="uiButton" style="margin-left: 3px">
											<button type="button" id="buttonGenerateYUsageToken" data-i18n="configuration-systemsettings-generate_token">Generar Token</button>
										</label>
										<i id="spinnerYUsageToken" class="icon-status icon-status-spin fa fa-spinner fa-spin" style="display: none; margin-left: 6px;"></i>
									</div>
								</td>
							</tr>
                        </table>
                    </div>
                </div>
                </asp:PlaceHolder>
			</div>
			<div id="divSecurityPolitics" runat="server" clientidmode="Static" visible="true">
				<div class="seccion collapsable" id="divAuthenticationType">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-authentication-title">Autenticación</h2>
					</div>
					<div class="contents">
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-authentication_type">Tipo de autenticación</span>:</th>
								<td class="data">
									<table class="uiGrid" cellspacing="0" cellpadding="0">
										<tbody>
											<tr>
												<td class="vMid prs">
													<asp:DropDownList ID="dropdownlistAuthenticationType" runat="server" ClientIDMode="Static">
														<asp:ListItem Value="1" data-i18n="configuration-systemsettings-local">Local</asp:ListItem>
														<asp:ListItem Value="2" data-i18n="configuration-systemsettings-domain">Dominio</asp:ListItem>
														<asp:ListItem Value="3" data-i18n="configuration-systemsettings-google">Google</asp:ListItem>
														<asp:ListItem Value="4" data-i18n="configuration-systemsettings-saml">SAML</asp:ListItem>
													</asp:DropDownList>
												</td>
												<td class="vMid pls" data-i18n="configuration-systemsettings-authentication_type-tip">
													Este parámetro indica si permitirá crear usuarios que son de Active Directory
												</td>
											</tr>
										</tbody>
									</table>
								</td>
							</tr>
						</table>
						<div class="subseccion collapsable" id="divLdap">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-active_directory-title">Active Directory</h2>
							</div>
							<div class="contents">
								<table id="tableLdap" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" style="display: none">
										<th class="label"><span data-i18n="configuration-systemsettings-use_active_directory">Utilizar Active Directory</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxLdapUseLdap" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-use_active_directory-tip">
															Este parámetro indica si permitirá crear usuarios que son de Active Directory
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-server_auth">Servidor</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxLdapServer" runat="server" Width="150" ClientIDMode="Static" spellcheck="false" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapServer" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-server_auth-tip">
															Este parámetro indica el nombre de servidor o IP que será el encargado de autenticar usuarios de LDAP
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-validation_port">Puerto</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxLdapPort" runat="server" MaxLength="5" Width="60" TextMode="Number" ClientIDMode="Static" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapPort" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-validation_port-tip">
															Este parámetro indica el puerto del servidor LDAP. Normalmente este valor es el 389.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-ldap_route">Ruta de inicio de LDAP</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxLdapSearchDN" runat="server" Width="350" ClientIDMode="Static" spellcheck="false" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapSearchDN" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-ldap_route-tip">
															Este parámetro indica el valor que corresponde al inicio del árbol de LDAP. Por ejemplo, si su
															empresa tiene como dominio midominio.com, el valor que se debe ingresar aquí es: DC=midominio,DC=com.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-auth_user">Usuario</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxLdapUser" runat="server" Width="350" ClientIDMode="Static" spellcheck="false" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapUser" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-auth_user-tip">
															Este parámetro indica el usuario que se utilizará para realizar la autenticación cuando se realicen 
															búsquedas contra LDAP.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<asp:PlaceHolder ID="placeholderLdapPassword" runat="server">
										<tr class="dataRow dataRowSeparator" rel="useldap">
											<th class="label withdescription"><span data-i18n="configuration-systemsettings-configured_password_auth">Contraseña configurada</span>:</th>
											<td class="data">
												<asp:Label ID="labelLdapPassword" runat="server" ClientIDMode="Static" />
											</td>
										</tr>
									</asp:PlaceHolder>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription">
											<asp:Literal ID="literalLdapPasswordChangePasswordTitle" runat="server">Nueva contraseña:</asp:Literal>
											<asp:Literal ID="literalLdapPasswordNewPasswordTitle" runat="server">Contraseña:</asp:Literal>
										</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs" style="width: 200px;">
															<asp:CheckBox ID="checkboxLdapPassword" runat="server" ClientIDMode="Static" ToolTip="Establecer una nueva contraseña" />
															<asp:TextBox ID="textboxLdapPassword" runat="server" Width="150" TextMode="Password" ClientIDMode="Static" placeholder="Contraseña" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapPassword" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-new_password-tip">
															Este parámetro indica la contraseña del usuario que se utilizará para realizar la autenticación 
															cuando se realicen búsquedas contra LDAP.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-secure_authentication">Autenticación segura</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxLdapUseSecureAuthentication" runat="server" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-secure_authentication-tip">
															Este parámetro indica si la autenticación contra LDAP será en modo seguro
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-user_search_filter">Filtro para búsqueda de usuarios</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxLdapUserSearchFilter" runat="server" Width="350" ClientIDMode="Static" spellcheck="false" />
															<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateLdapUserSearchFilter" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-user_search_filter-tip">
															Este parámetro indica el filtro que se utilizará para realizar búsquedas de usuarios contra LDAP.
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-local_users">Permitir crear usuarios Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxLdapLocalUsers" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-local_users-tip">
															Este parámetro indica si se podrá crear usuarios Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-local_pages">Permitir crear agentes Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxLdapLocalAgents" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-local_pages-tip">
															Este parámetro indica si se podrá crear agentes Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-parameters_maping">Personalización de mapeo de parámetros</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxLDAPUseConfigurationParams" runat="server" ClientIDMode="Static" />
														</td>

													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr id="trLDAPUseConfigurationParams" class="dataRow" style="display:none">
										<th></th>
										<td>
											<div class="subseccion">
												<div class="title">
													<h2 data-i18n="configuration-systemsettings-parameters_configuration-title">Configuración de parámetros</h2>
												</div>
												<div class="contents">
													<yoizen:Message runat="server" Type="Information" Small="true" LocalizationKey="[html]configuration-systemsettings-parameters_configuration">
														Configure el nombre de los parámetros que serán mapeados para ser utilizados en <b>Active Directory</b>. Los parámetros no podrán contener espacios en blanco.<br />
													</yoizen:Message>
													<table id="tableConfigurationParams" class="reporte" cellspacing="0" rules="all" border="1">
														<thead>
															<tr class="header">
																<th style="width:auto" scope="col"><span data-i18n="configuration-systemsettings-parameters">Parámetros</span></th>
																<th scope="col"><span data-i18n="configuration-systemsettings-values">Valores</span></th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>Nombre</td>
																<td>
																	<asp:TextBox ID="textboxLDAPConfigurationFirstName" Width="320px" ClientIDMode="Static" runat="server" autocomplete="off" />
																</td>
															</tr>
															<tr>
																<td>Apellido</td>
																<td>
																	<asp:TextBox ID="textboxLDAPConfigurationLastName" Width="320px" runat="server" ClientIDMode="Static" autocomplete="off" />
																</td>
															</tr>
															<tr>
																<td>Username</td>
																<td>
																	<asp:TextBox ID="textboxLDAPConfigurationUserName" Width="320px" runat="server" ClientIDMode="Static" autocomplete="off" />
																</td>
															</tr>
															<tr>
																<td>Email</td>
																<td>
																	<asp:TextBox ID="textboxLDAPConfigurationEmail" Width="320px" runat="server" ClientIDMode="Static" autocomplete="off" />
																</td>
															</tr>
															<tr>
																<td>LDAP</td>
																<td>
																	<asp:TextBox ID="textboxLDAPConfigurationLDAP" Width="320px" runat="server" ClientIDMode="Static" autocomplete="off" />
																</td>
															</tr>
														</tbody>
													</table>
													<div class="validationerror" style="display: none">
														<asp:CustomValidator runat="server" SkinID="validationerror" ErrorMessage="Error en la configuración" EnableClientScript="true" ClientValidationFunction="validateConfigurationParams" data-i18n="configuration-systemsettings-configuration-error" />
													 </div> 
												</div>
											</div>
										</td>
									</tr>
								</table>
								<div id="divLdapTestFailed" style="display: none" class="validationerror"><span class="validationerror" data-i18n="configuration-systemsettings-active_directory-error">Falló la prueba de configuración de Active Directory</span></div>
								<div id="divLdapTestButtons" class="buttons">
									<label class="uiButton">
										<button type="button" data-i18n="configuration-systemsettings-configuration_test" id="buttonLdapTestConfig">Test de configuración</button>
									</label>
								</div>
							</div>
						</div>
						<div class="subseccion collapsable" id="divGoogleAuth" clientidmode="Static" runat="server">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-google_auth_configuration-title">Configuración de Google Auth</h2>
							</div>
							<div class="contents">
								<table id="tableBitLy" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-enable_google_authentication">Habilitar autenticación con Google</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxGoogleAuthEnabled" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-enable_google_authentication-tip">
															Este parámetro indica si se permite autenticación con Google Auth
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-domain">Dominio</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxGoogleAuthHostedDomain" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-domain-tip">
															Este parámetro indica el dominio que deberán tener los usuarios de Google (por ejemplo: yoizen.com)
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-google_auth_local_users">Permitir crear usuarios Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxGoogleAuthLocalUsers" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-google_auth_local_users-tip">
															Este parámetro indica si se podrá crear usuarios Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator hiddenAsGateway" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-google_auth_local_agents">Permitir crear agentes Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxGoogleAuthLocalAgents" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-google_auth_local_agents-tip">
															Este parámetro indica si se podrá crear agentes Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-use_own">Utilizar propio</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxGoogleAuthUseCustom" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-use_own_google_api-tip">
															Este parámetro indica si se utiliza un aplicación propia de Google APIs
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trGoogleAuthClientID">
										<th class="label"><span data-i18n="configuration-systemsettings-client_id">Client ID</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxGoogleAuthClientID" runat="server" Width="300" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trGoogleAuthClientSecret">
										<th class="label"><span data-i18n="configuration-systemsettings-client_secret">Client Secret</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxGoogleAuthClientSecret" runat="server" Width="300" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateGoogleAuth" data-i18n="configuration-systemsettings-invalid_configuration">
										La configuración es inválida
									</asp:CustomValidator>
								</div>
							</div>
						</div>
						<div class="subseccion collapsable" id="divLoginWithKeycloak" clientidmode="Static" runat="server">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-saml_auth_configuration-title">Configuración de SAML</h2>
							</div>
							<div class="contents" id="divKeycloakConfig" Visible="false" clientidmode="Static" runat="server">
								<table id="tableSaml" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" style="display: none">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-enable_saml_authentication">Habilitar autenticación con SAML</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxKeycloakAuthEnabled" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-enable_saml_authentication-tip">
															Este parámetro indica si se permite autenticación con SAML
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-keycloak-endpoint">URL</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:TextBox ID="textboxKeycloakEndpoint" runat="server" Width="300" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-keycloak-endpoint-tip">
															Este parámetro indica la URL donde deberan ingresar los usuarios
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trKeycloakClientName">
										<th class="label"><span data-i18n="configuration-systemsettings-keycloak-client_name">Nombre de Cliente (SSO URL name)</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxKeycloakClientName" runat="server" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trKeycloakhRealmName">
										<th class="label"><span data-i18n="configuration-systemsettings-realm_name">Nombre del Realm</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxKeycloakRealmName" runat="server" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
									<tr class="dataRow dataRowSeparator" id="trSamlButtonText">
										<th class="label"><span data-i18n="configuration-systemsettings-button_text">Texto del boton login</span>:</th>
										<td class="data">
											<asp:TextBox ID="textboxSamlButtonText" runat="server" spellcheck="false" ClientIDMode="Static" />
										</td>
									</tr>
								</table>
								<div class="validationerror" style="display: none">
									<asp:CustomValidator runat="server" Display="Dynamic" SkinID="validationerror" EnableClientScript="true" ClientValidationFunction="ValidateGoogleAuth" data-i18n="configuration-systemsettings-invalid_configuration">
										La configuración es inválida
									</asp:CustomValidator>
								</div>
							</div>
							<div class="contents">
								<table id="tableSamlUsers" width="100%" border="0" class="uiInfoTable noBorder">
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-local_users">Permitir crear usuarios Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxSamlLocalUsers" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-local_users-tip">
															Este parámetro indica si se podrá crear usuarios Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
									<tr class="dataRow dataRowSeparator" rel="useldap">
										<th class="label withdescription"><span data-i18n="configuration-systemsettings-local_pages">Permitir crear agentes Locales</span>:</th>
										<td class="data">
											<table class="uiGrid" cellspacing="0" cellpadding="0">
												<tbody>
													<tr>
														<td class="vMid prs">
															<asp:CheckBox ID="checkboxSamlLocalAgents" runat="server" ClientIDMode="Static" />
														</td>
														<td class="vMid pls" data-i18n="configuration-systemsettings-local_pages-tip">
															Este parámetro indica si se podrá crear agentes Locales.
														</td>
													</tr>
												</tbody>
											</table>
										 </td>
									</tr>
								</table>
							</div>
						</div>
					</div>
				</div>
				<div class="seccion collapsable" id="divCloudIPRestrictions" runat="server" clientidmode="Static">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-ip_access_restrictions-title">Restricciones de acceso por IP</h2>
					</div>
					<div class="contents">
						<yoizen:Message runat="server" Type="Information" style="margin-top: 5px" LocalizationKey="configuration-systemsettings-ip_access_restrictions">
							Ingrese las IPs a las cuales se restringirá el acceso ingresando una por línea o deje vacío para permitir el acceso de cualquier IP
						</yoizen:Message>
						<table width="100%" border="0" class="uiInfoTable noBorder">
							<tr class="dataRow dataRowSeparator">
								<th class="label"><span data-i18n="configuration-systemsettings-supervisiors_site_ip">IPs para sitio de Supervisión/Administración</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxCloudIPRestrictionsWeb" runat="server" spellcheck="false" autocomplete="off" Width="90%" ClientIDMode="Static" TextMode="MultiLine" Rows="5" />
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateCloudIPRestrictionsWeb" />
								</td>
							</tr>
							<tr class="dataRow dataRowSeparator hiddenAsGateway">
								<th class="label"><span data-i18n="configuration-systemsettings-agents_site_ip">IPs para sitio de Agente</span>:</th>
								<td class="data">
									<asp:TextBox ID="textboxCloudIPRestrictionsWebAgent" runat="server" spellcheck="false" autocomplete="off" Width="90%" ClientIDMode="Static" TextMode="MultiLine" Rows="5" />
									<asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateCloudIPRestrictionsWebAgent" />
								</td>
							</tr>
						</table>
					</div>
				</div>
				<div id="divUserSecurityPolitics" runat="server" clientidmode="static" class="seccion collapsable">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-user_configuration-title">Configuración de Usuario</h2>
					</div>
					<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-user-mandatory-2FA">Verificación en dos pasos</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxMandatory2FAUser" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-user-mandatory-2FA-description">
														Este parámetro indica si es opcional u obligatoria la activación de la verificación en dos pasos.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-pasword_strenght">Nivel mínimo de seguridad de contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:DropDownList ID="dropdownlistPasswordStrength" runat="server">
															<asp:ListItem value="0" data-i18n="configuration-systemsettings-no_validate">No validar</asp:ListItem>
															<asp:ListItem value="1" data-i18n="configuration-systemsettings-bad">Mala</asp:ListItem>
															<asp:ListItem value="2" data-i18n="configuration-systemsettings-weak">Débil</asp:ListItem>
															<asp:ListItem value="3" data-i18n="configuration-systemsettings-good">Buena</asp:ListItem>
															<asp:ListItem value="4" data-i18n="configuration-systemsettings-strong">Fuerte</asp:ListItem>
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-pasword_strenght-tip">
														Este parámetro indica el nivel mínimo de seguridad que deberá tener la contraseña de los usuarios
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_expiration">Expiro de Contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxUserPasswordExpireDay" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxUserPasswordExpireDay" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxUserPasswordExpireDay" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_expiration-tip">
														Este parámetro indica cada cuantos días expira una contraeña. Paso este lapso el usuario deberá cambiar su contraseña. 
														El valor está expresado en Días. Valores habilitados 0 - 999.
														Ingrese 0 si desea que la contraseña no expire.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_repeat">Repetición de Contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxUserPasswordRepeat" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_repeat-tip">
														Este parámetro indica si el usuario puede repetir contraseñas ya ingresadas anteriormente. 
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" id="trUserPasswordRepeat">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_repeat_times">Cantidad de Repeticiones</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxUserPasswordRepeat" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxUserPasswordRepeat" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxUserPasswordRepeat" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_repeat_times-tip">
														Este parámetro indica que la contraseña ingresada deberá ser distinta a las anteriores. 
														Valores habilitados 0 - 999.
														Ingrese 0 si desea que nunca se pueda ingresar la misma contraseña.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_wrong_block">Cantidad de Contraseñas Incorrectas</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxUserPasswordWrongBlock" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxUserPasswordWrongBlock" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxUserPasswordWrongBlock" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_wrong_block-tip">
														Este parámetro indica la cantidad de veces en un día que el usuario puede ingresar su contraseña de forma incorrecta antes que se bloque la cuenta. Se la avisará que al usuario que es su último intento antes de ser bloqueada.
														Valores habilitados 0 - 999.
														Ingrese 0 si desea no se bloque nunca la cuenta.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" id="trUserCaptcha">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-captcha_security_level">Nivel de Segruridad de Captcha</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxUserPasswordWrongsCaptha" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxUserPasswordWrongsCaptha" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxUserPasswordWrongsCaptha" Type="Integer" MinimumValue="0" MaximumValue="10" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-captcha_security_level-tip">
														Este parámetro indica el nivel de seguridad que va a tener contra el tráfico abusivo. Siendo 1 el menor nivel de seguridad y 10 el mayor. Se recomienda utilizar 8.
														Valores habilitados 0 - 10.
														Ingrese 0 si desea que no se valide el captcha
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-refresh_password">Blanqueo de contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxUserRefreshPassword" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-refresh_password-tip">
														Este parámetro indica si el usuario puede blanquear su contraeña. Recibirá un mail con los
														pasos para poder cambiarla.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-inactive_user_block">Días de Inactividad</span></th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxDaysInactiveUserBlock" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDaysInactiveUserBlock" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxDaysInactiveUserBlock" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-inactive_user_block-tip">
														Este parámetro indica la cantidad de días de inactividad para que se bloque una cuenta de usuario.
														Valores habilitados 0 - 999. Valor expresado en días
														Ingrese 0 si desea no se bloque ninguna cuenta.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_first_change">Primer Inicio de Sesión</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxUserPasswordFirstChange" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_first_change-tip">
														Este parámetro indica si el usuario debe cambiar la contraseña al primer inicio de sesión o
														cuando le hayan blanqueado su contraseña.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<th class="label"><span data-i18n="configuration-systemsettings-validate_regex">Expresión regular a validar</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxUserRegex" runat="server" Width="100%" spellcheck="false" autocomplete="false" ClientIDMode="Static" />
										<%--<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxUserRegex" />--%>
										<yoizen:Message runat="server" Type="Information" style="margin-top: 10px;" LocalizationKey="configuration-systemsettings-validate_regex-tip">
											Este parámetro indica la expresión regular a utilizar para validar la contraseña. Recuerde que esta se verificará luego de
											de que la contraseña haya sido analizada por el nivel mínimo de seguridad de contraseña (punto 1)
										</yoizen:Message>
										<div id="divUserRegexVisualizer" style="display: none" class="subseccion collapsable">
											<div class="title">
												<h2 data-i18n="configuration-systemsettings-regex_visualization-title">Visualización de la expresión regular</h2>
											</div>
											<div class="contents">
											</div>
										</div>
									</td>
								</tr>
								<tr>
									<th class="label"><span data-i18n="configuration-systemsettings-error_message">Mensaje de error</span>:</th>
									<td class="data">
									<asp:TextBox ID="textboxMessageUserRegex" runat="server" Width="100%" spellcheck="false" autocomplete="false" ClientIDMode="Static" />
										<yoizen:Message runat="server" Type="Information" style="margin-top: 10px;" LocalizationKey="configuration-systemsettings-error_message_regex-tip">
											Este parámetro indica el mensaje de error que se notificará al user en caso de que su contraseña no cumpla con la expresión regular.
										</yoizen:Message>
									</td>
								</tr>
						</table>
					</div>
				</div>
				<div id="divAgentSecurityPolitics" runat="server" clientidmode="static" class="seccion collapsable hiddenAsGateway">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-configuration_agents-title">Configuración de agente</h2>
					</div>
					<div class="contents">
							<table width="100%" border="0" class="uiInfoTable noBorder">
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-agent-mandatory-2FA">Verificación en dos pasos</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxMandatory2FAAgent" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-agent-mandatory-2FA-description">
														Este parámetro indica si es opcional u obligatoria la activación de la verificación en dos pasos.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-pasword_strenght">Nivel mínimo de seguridad de contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:DropDownList ID="dropdownlistAgentPasswordStrength" runat="server">
															<asp:ListItem value="0" data-i18n="configuration-systemsettings-no_validate">No validar</asp:ListItem>
															<asp:ListItem value="1" data-i18n="configuration-systemsettings-bad">Mala</asp:ListItem>
															<asp:ListItem value="2" data-i18n="configuration-systemsettings-weak">Débil</asp:ListItem>
															<asp:ListItem value="3" data-i18n="configuration-systemsettings-good">Buena</asp:ListItem>
															<asp:ListItem value="4" data-i18n="configuration-systemsettings-strong">Fuerte</asp:ListItem>
														</asp:DropDownList>
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-pasword_strenght_agents-tip">
														Este parámetro indica el nivel mínimo de seguridad que deberá tener la contraseña de los agentes
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_expiration_agents">Expiro de Contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxAgentPasswordExpireDay" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordExpireDay" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxAgentPasswordExpireDay" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_expiration_agents-tip">
														Este parámetro indica cada cuantos días expira una contraeña. Paso este lapso el agente deberá cambiar su contraseña. 
														El valor está expresado en Días. Valores habilitados 0 - 999.
														Ingrese 0 si desea que la contraseña no expire.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_repeat_agents">Repetición de Contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAgentPasswordRepeat" runat="server" ClientIDMode="Static" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_repeat_agents-tip">
														Este parámetro indica si el agente puede repetir contraseñas ya ingresadas anteriormente. 
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" id="trAgentPasswordRepeat">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_repeat_times_agents">Cantidad de Repeticiones</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxAgentPasswordRepeat" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordRepeat" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxAgentPasswordRepeat" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_repeat_times_agents-tip">
														Este parámetro indica que la contraseña ingresada deberá ser distinta a las anteriores. 
														Valores habilitados 0 - 999.
														Ingrese 0 si desea que nunca se pueda ingresar la misma contraseña.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_wrong_block_agents">Cantidad de Contraseñas Incorrectas para Bloqueo</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxAgentPasswordWrongBlock" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordWrongBlock" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxAgentPasswordWrongBlock" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_wrong_block_agents-tip">
														Este parámetro indica la cantidad de veces en un día que el agente puede ingresar su contraseña de forma incorrecta antes que se bloque la cuenta. Se la avisará que al agente que es su último intento antes de ser bloqueada.
														Valores habilitados 0 - 999.
														Ingrese 0 si desea no se bloque nunca la cuenta.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator" id="trAgentCaptcha">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-captcha_security_level_agents">Nivel de Segurida de Captcha</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxAgentPasswordWrongsCaptha" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentPasswordWrongsCaptha" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxAgentPasswordWrongsCaptha" Type="Integer" MinimumValue="0" MaximumValue="10" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-captcha_security_level_agents-tip">
														Este parámetro indica el nivel de seguridad que va a tener contra el tráfico abusivo. Siendo 1 el menor nivel de seguridad y 10 el mayor. Se recomienda utilizar 8.
														Valores habilitados 0 - 10.
														Ingrese 0 si desea que no se valide el captcha
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-inactive_agent_block">Días de Inactividad</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:TextBox ID="textboxDaysInactiveAgentBlock" runat="server" Width="50" spellcheck="false" autocomplete="off" ClientIDMode="Static" TextMode="Number" />
														<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxDaysInactiveAgentBlock" />
														<asp:RangeValidator runat="server" ControlToValidate="textboxDaysInactiveAgentBlock" Type="Integer" MinimumValue="0" MaximumValue="999" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-inactive_agent_block-tip">
														Este parámetro indica la cantidad de días de inactividad para que se bloque una cuenta de agente.
														Valores habilitados 0 - 999. Valor expresado en días
														Ingrese 0 si desea no se bloque ninguna cuenta.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-change_password_agent">Cambiar contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAgentCanChangePassword" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-change_password_agent-tip">
														Este parámetro indica si el agente puede cambiar su contraseña cuando desee.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-refresh_password_agent">Blanqueo de contraseña</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAgentRefreshPassword" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-refresh_password_agent-tip">
														Este parámetro indica si el agente puede blanquear su contraseña. Recibirá un mail con los
														pasos para poder cambiarla.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr class="dataRow dataRowSeparator">
									<th class="label withdescription"><span data-i18n="configuration-systemsettings-password_first_change_agent">Primer Inicio de Sesión</span>:</th>
									<td class="data">
										<table class="uiGrid" cellspacing="0" cellpadding="0">
											<tbody>
												<tr>
													<td class="vMid prs">
														<asp:CheckBox ID="checkboxAgentPasswordFirstChange" runat="server" />
													</td>
													<td class="vMid pls" data-i18n="configuration-systemsettings-password_first_change_agent-tip">
														Este parámetro indica si el agente debe cambiar la contraseña al primer inicio de sesión y 
														cuando le hayan blanqueado su contraseña.
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
								<tr>
									<th class="label"><span data-i18n="configuration-systemsettings-regex_to_validate">Expresión regular a validar</span>:</th>
									<td class="data">
										<asp:TextBox ID="textboxAgentRegex" runat="server" Width="100%" spellcheck="false" autocomplete="false" ClientIDMode="Static" />
										<%--<asp:RequiredFieldValidator runat="server" ControlToValidate="textboxAgentRegex" />--%>
										<yoizen:Message runat="server" Type="Information" style="margin-top: 10px;" LocalizationKey="[html]configuration-systemsettings-regex_to_validate-tip">
											Este parámetro indica la expresión regular a utilizar para validar la contraseña. Recuerde que esta se verificará luego de
											de que la contraseña haya sido analizada por el nivel mínimo de seguridad de contraseña (punto 1)
										</yoizen:Message>
										<div id="divAgentRegexVisualizer" style="display: none" class="subseccion collapsable">
											<div class="title">
												<h2 data-i18n="configuration-systemsettings-regex_visualization-title">Visualización de la expresión regular</h2>
											</div>
											<div class="contents">
											</div>
										</div>
									</td>
								</tr>
								<tr>
									<th class="label"><span data-i18n="configuration-systemsettings-error_message">Mensaje de error</span>:</th>
									<td class="data">
									<asp:TextBox ID="textboxMessageAgentRegex" runat="server" Width="100%" spellcheck="false" autocomplete="false" ClientIDMode="Static" />
										<yoizen:Message runat="server" Type="Information" style="margin-top: 10px;" LocalizationKey="configuration-systemsettings-regex_error">
											Este parámetro indica el mensaje de error que se notificará al agente en caso de que su contraseña no cumpla con la expresión regular.
										</yoizen:Message>
									</td>
								</tr>
						</table>
					</div>
				</div>
				<asp:Panel ID="panelCloudHeaders" runat="server" CssClass="seccion collapsable" ClientIDMode="Static">
					<div class="title">
						<h2 data-i18n="configuration-systemsettings-cloud_headers-title">Encabezados HTTP</h2>
					</div>
					<div class="contents">
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-cloud_headers-add-title">Encabezados HTTP a agregar</h2>
							</div>
							<div class="contents">
								<asp:HiddenField ID="hiddenCloudHeadersToAdd" runat="server" ClientIDMode="Static" />
								<div id="divCloudHeadersToAdd"></div>
								<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateCloudHeadersToAdd" SkinID="validationerror" /></div>
							</div>
						</div>
						<div class="subseccion collapsable">
							<div class="title">
								<h2 data-i18n="configuration-systemsettings-cloud_headers-remove-title">Encabezados HTTP a quitar</h2>
							</div>
							<div class="contents">
								<asp:HiddenField ID="hiddenCloudHeadersToRemove" runat="server" ClientIDMode="Static" />
								<div id="divCloudHeadersToRemove"></div>
								<div class="validationerror" style="display: none"><asp:CustomValidator runat="server" EnableClientScript="true" ClientValidationFunction="ValidateCloudHeadersToRemove" SkinID="validationerror" /></div>
							</div>
						</div>
					</div>
				</asp:Panel>
			</div>
		</div>
		<div class="buttons">
			<label class="uiButton uiButtonLarge uiButtonConfirm">
				<asp:Button ID="buttonSave" runat="server" Text="Aceptar" OnClick="buttonSave_Click" data-i18n="globals-accept" />
			</label>
		</div>
	</asp:Panel>
</asp:Content>