﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Yoizen.Common;

namespace Yoizen.Social.WhatsApp
{
	/// <summary>
	/// Clase base para los distintos tipos de mensaje de WhatsApp
	/// </summary>
    public abstract class WhatsAppMessage : DomainModel.Message
	{
		#region Constants

		/// <summary>Especifica el nombre del parámetro que tendrá el código de usuario de WhatsApp</summary>
		public const string SocialUserIdParameter = "SocialUserId";

		/// <summary>Especifica el nombre del parámetro que tendrá el código de usuario de WhatsApp</summary>
		public const string GroupIDParameter = "GroupID";

		/// <summary>Especifica el nombre del parámetro que tendrá el nombre del archivo generado en la respuesta</summary>
		public const string ReplyFileNameParameter = "ReplyFileName";

		/// <summary>Indica si el mensaje fue enviado</summary>
		public const string SentParameter = "Sent";

		/// <summary>Código de estado generado por ChatClub para un mensaje que indica el estado de envío</summary>
		public const string SentStatusCodeParameter = "SentStatusCode";

		/// <summary>Descripción del estado enviado</summary>
		public const string SentStatusParameter = "SentStatus";

		/// <summary>Fecha en que se envió el mensaje</summary>
		public const string SentDateParameter = "SentDate";

		/// <summary>Hora en que se envió el mensaje</summary>
		public const string SentAtParameter = "SentAt";

		/// <summary>Indica si el mensaje fue entregado al destino</summary>
		public const string DeliveredParameter = "Delivered";

		/// <summary>Código de estado generado por ChatClub para indicar que el mensaje fue entregado</summary>
		public const string DeliveredStatusCodeParameter = "DeliveredStatusCode";

		/// <summary>Descripción del estado de entrega</summary>
		public const string DeliveredStatusParameter = "DeliveredStatus";

		/// <summary>Fecha en que se entregó el mensaje</summary>
		public const string DeliveredDateParameter = "DeliveredDate";

		/// <summary>Hora en que se entregó el mensaje</summary>
		public const string DeliveredAtParameter = "DeliveredAt";

		/// <summary>Indica si el mensaje fue leído por el destinatario</summary>
		public const string ReadParameter = "Read";

		/// <summary>Fecha en que se leyó el mensaje</summary>
		public const string ReadDateParameter = "ReadDate";

		/// <summary>Hora en que se leyó el mensaje</summary>
		public const string ReadAtParameter = "ReadAt";

		/// <summary>Indica si el mensaje es un HSM</summary>
		public const string HSMParameter = "HSM";

		/// <summary>Indica la definición de los mensajes HSM</summary>
		public const string HSMSendDefinitionParameter = "HSMSendDef";

		/// <summary>El espacio de nombres del mensaje HSM</summary>
		public const string HSMTemplateNamespaceParameter = "TemplateNamespace";

		/// <summary>El nombre de elemento del mensaje HSM</summary>
		public const string HSMTemplateElementNameParameter = "TemplateName";

		/// <summary>El espacio de nombres del mensaje HSM</summary>
		public const string HSMTemplateDataParameter = "TemplateData";

		/// <summary>El lenguage del mensaje HSM</summary>
		public const string HSMTemplateLanguage = "Language";

		/// <summary>Indica si se enviará HSM a los usuarios con casos abiertos/summary>
		public const string HSMTemplateAllowToConfigureSendHSMIfCaseOpen = "AllowToConfigureSendHSMIfCaseOpen";

		/// <summary>El payload del mensaje</summary>
		public const string PayloadParameter = "Payload";

		/// <summary>El payload del mensaje para cuando es PostBack y ya hay un payload informado por el mensaje entrante</summary>
		public const string PayloadPostbackParameter = "PayloadPostback";

		/// <summary>El timestamp del payload del mensaje</summary>
		public const string PayloadTimeStampParameter = "PayloadTs";

		/// <summary>El id de campaña (en caso de que exista) del payload del mensaje </summary>
		public const string PayloadCampaignIdParameter = "PayloadCId";

		/// <summary>El payload del mensaje</summary>
		public const string PreviousBotEventsParameter = "Events";

		/// <summary>El código del mensaje sobre el cuál respondió el usuario (puede ser el código de ySocial o el código de whatsapp)</summary>
		public const string ContextMessageIDParameter = "CMID";

		/// <summary>El código del mensaje sobre el cuál respondió el usuario (el código de whatsapp)</summary>
		public const string ContextWhatsappIDParameter = "CWID";

		/// <summary>El nombre del elemento de template sobre el cual el mensaje entrante está respondiendo</summary>
		public const string ContextSourceTemplateNameParameter = "CSTN";

		/// <summary>El lenguage de template sobre el cual el mensaje entrante está respondiendo</summary>
		public const string ContextSourceTemplateLanguageParameter = "CSLN";

		/// <summary>Indica si el mensaje proviene de una campaña HSM sin caso</summary>
		public const string ContextSourceIsFromHSMWithoutCase = "CSHSMWC";

		/// <summary>Indica los parametros del mensaje saliente sin caso </summary>
		public const string ContextSourceOutgoingTemplateParameters = "CSTCP";

		/// <summary>Indica el TimeStamp del mensaje saliente sin caso</summary>
		public const string ContextSourceOutgoingTimeStamp = "CSOTS";

		/// <summary>Indica el SocialmessageId del mensaje saliente sin caso</summary>
		public const string ContextSourceOutgoingSocialMessageID = "CSOSMID";

		/// <summary>Indica los estados mensaje saliente sin caso</summary>
		public const string ContextStatusesForOutgoingMessage = "CSFOM";

		/// <summary>El código del mensaje sobre el cuál respondió el usuario</summary>
		public const string ContextFromParameter = "CMFr";

		/// <summary>Indica si el mensaje es un handOff</summary>
		public const string IsHandoffParameter = "IsHanddoff";

		/// <summary>Indica si el mensaje es un handOff</summary>
		public const string IsSurveyHandoffParameter = "IsSurveyHandoff";

		/// <summary>Indica el ID de mensaje asignado por whatsapp a los mensajes envíados</summary>
		public const string WaIdParameter = "wid";

		/// <summary>Indica el nombre de la campaña asociada a un mensaje de whatsapp</summary>
		public const string CampaignParameter = "Campaign";

		/// <summary>Indica el código de la campaña asociada a un mensaje de whatsapp</summary>
		public const string CampaignIdParameter = "CampaignId";

		/// <summary>Indica el código de la campaña asociada a un mensaje de whatsapp</summary>
		public const string ReactionParameter = "Reaction";

		/// <summary>Indica la url de la pùblicación o anuncio asociado a un mensaje entrante de whatsapp</summary>
		public const string ReferralSourceUrlParameter = "ReferralSourceUrl";

		/// <summary>Indica el id de la pùblicación o anuncio asociado a un mensaje entrante de whatsapp</summary>
		public const string ReferralSourceIdParameter = "ReferralSourceId";

		/// <summary>Indica si el mensaje provino de una publicación o anuncio para los mensajes entrantes de whatsapp</summary>
		public const string ReferralSourceTypeParameter = "ReferralSourceType";

		/// <summary>Indica el identificador de click de WhatsApp (ctwa_clid) para mensajes entrantes de whatsapp que provienen de anuncios</summary>
		public const string ReferralCTWAClickIdParameter = "ReferralCTWAClickId";

		/// <summary>Indica los parametros para invocar a yFlow con los bloques de destino para un WhatsApp Flow</summary>
		public const string MetaFlowInvokeParameter = "MFIP";

		#endregion

		#region Enums

		/// <summary>
		/// Enumeración con los posibles tipos de mensajes de WhatsApp
		/// </summary>
		public enum MessageTypes
		{
			/// <summary>
			/// Un mensaje nuevo con un tipo no especificado
			/// </summary>
			Unknown = 0,

			/// <summary>
			/// Mensaje de texto
			/// </summary>
			Text = 1,

			/// <summary>
			/// Mensaje de tipo imagen
			/// </summary>
			Image = 2,

			/// <summary>
			/// Mensaje de tipo video
			/// </summary>
			Video = 3,

			/// <summary>
			/// Mensaje de tipo audio
			/// </summary>
			Audio = 4,

			/// <summary>
			/// Mensaje de tipo VCard
			/// </summary>
			VCard = 5,

			/// <summary>
			/// Mensaje de tipo Ubicación
			/// </summary>
			Location = 6,

			/// <summary>
			/// Mensaje de tipo documento
			/// </summary>
			Document = 7,

			/// <summary>
			/// Mensaje de tipo payload
			/// </summary>
			Payload = 8,

			/// <summary>
			/// Mensaje de tipo sticker
			/// </summary>
			Sticker = 9,

			/// <summary>
			/// Mensaje de tipo interactivo
			/// </summary>
			Interactive = 10,

			/// <summary>
			/// Mensaje de tipo órden (carrito)
			/// </summary>
			Order = 11,

			/// <summary>
			/// Mensaje de tipo producto referido
			/// </summary>
			ReferredProduct = 12
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve el tipo de mensaje
		/// </summary>
		public MessageTypes MessageType
		{
			get
			{
				if (this.Parameters.ContainsKey(MessageTypeParameter))
					return (MessageTypes) short.Parse(this.Parameters[MessageTypeParameter]);

				return MessageTypes.Unknown;
			}
			protected set
			{
				this.Parameters[MessageTypeParameter] = ((short) value).ToString();
			}
		}

		/// <summary>
		/// Devuelve o establece el paylaod del mensaje
		/// </summary>
		public string Payload
		{
			get
			{
				if (this.Parameters.ContainsKey(PayloadParameter))
					return this.Parameters[PayloadParameter];

				return null;
			}
			set
			{
				this.Parameters[PayloadParameter] = value;
			}
		}

		/// <summary>
		/// Devuelve o establece el timestamp del paylaod del mensaje
		/// </summary>
		public string PayloadTimeStamp
		{
			get
			{
				if (this.Parameters.ContainsKey(PayloadTimeStampParameter))
					return this.Parameters[PayloadTimeStampParameter];

				return null;
			}
			set
			{
				this.Parameters[PayloadTimeStampParameter] = value;
			}
		}

		/// <summary>
		/// Devuelve o establece el campaignId del paylaod del mensaje
		/// </summary>
		public string PayloadCampaignId
		{
			get
			{
				if (this.Parameters.ContainsKey(PayloadCampaignIdParameter))
					return this.Parameters[PayloadCampaignIdParameter];

				return null;
			}
			set
			{
				this.Parameters[PayloadCampaignIdParameter] = value;
			}
		}

		/// <summary>
		/// Devuelve si el mensaje actual contiene datos provenientes de la red social como acción de un botón
		/// </summary>
		/// <returns><code>true</code> en caso de contener; en caso contrario, <code>false</code></returns>
		public override bool ContainsPayload
		{
			get
			{
				return this.Parameters.ContainsKey(PayloadParameter);
			}
		}

		#endregion

		#region Constructors

		/// <summary>
		/// Inicializa una nueva instancia de <see cref="WhatsAppMessage"/>
		/// </summary>
		public WhatsAppMessage()
			: base()
		{
			this.SocialServiceType = DomainModel.SocialServiceTypes.WhatsApp;
			this.IsDirectMessage = true;
		}
		
		/// <summary>
		/// Inicializa una nueva instancia de <see cref="WhatsAppMessage"/> a partir de los datos de un registro de la base de datos
		/// </summary>
		/// <param name="record">Un <see cref="IDataRecord"/> con los datos de la base de datos</param>
		public WhatsAppMessage(System.Data.IDataRecord record)
			: base(record)
		{
			this.SocialServiceType = DomainModel.SocialServiceTypes.WhatsApp;
			this.IsDirectMessage = true;
		}

		#endregion

		#region Static Methods

		/// <summary>
		/// Devuelve una instancia de un mensaje de whatsapp de acuerdo al tipo
		/// </summary>
		/// <param name="type">Un valor de la enumeración <see cref="MessageTypes"/> especificando el tipo de
		/// mensaje a crear</param>
		/// <returns>Un <see cref="WhatsAppMessage"/></returns>
		public static WhatsAppMessage Create(MessageTypes type)
		{
			switch (type)
			{
				case MessageTypes.Unknown:
					return new WhatsAppUnknownMessage();
				case MessageTypes.Text:
					return new WhatsAppTextMessage();
				case MessageTypes.Image:
					return new WhatsAppImageMessage();
				case MessageTypes.Video:
					return new WhatsAppVideoMessage();
				case MessageTypes.Audio:
					return new WhatsAppAudioMessage();
				case MessageTypes.VCard:
					return new WhatsAppVCardMessage();
				case MessageTypes.Location:
					return new WhatsAppLocationMessage();
				case MessageTypes.Document:
					return new WhatsAppDocumentMessage();
				case MessageTypes.Payload:
					return new WhatsAppPayloadMessage();
				case MessageTypes.Sticker:
					return new WhatsAppStickerMessage();
				case MessageTypes.Interactive:
					return new WhatsAppInteractiveMessage();
				case MessageTypes.Order:
					return new WhatsAppOrderMessage();
				case MessageTypes.ReferredProduct:
					return new WhatsAppReferredProductMessage();
				default:
					throw new ArgumentOutOfRangeException("type", type, "El tipo especificado es inválido");
			}
		}

		/// <summary>
		/// Crea un nuevo <see cref="WhatsAppMessage"/> a partir de los datos de un registro de la base de datos
		/// </summary>
		/// <param name="record">Un <see cref="System.Data.IDataRecord"/> con los datos de la base de datos</param>
		/// <returns>Un <see cref="WhatsAppMessage"/></returns>
		public static DomainModel.Message Create(System.Data.IDataRecord record)
		{
			try
			{
				bool isReply = (bool) record["IsReply"];

				if (isReply)
				{
					return new WhatsAppTextMessage(record);
				}
				else
				{
					string parametersText = (string) record["Parameters"];
					Dictionary<string, string> parameters = Common.Conversions.ConvertStringToDictionary(parametersText);
					MessageTypes type = (MessageTypes) short.Parse(parameters[MessageTypeParameter]);

					switch (type)
					{
						case MessageTypes.Unknown:
							return new WhatsAppUnknownMessage(record);
						case MessageTypes.Text:
							return new WhatsAppTextMessage(record);
						case MessageTypes.Image:
							return new WhatsAppImageMessage(record);
						case MessageTypes.Video:
							return new WhatsAppVideoMessage(record);
						case MessageTypes.Audio:
							return new WhatsAppAudioMessage(record);
						case MessageTypes.VCard:
							return new WhatsAppVCardMessage(record);
						case MessageTypes.Location:
							return new WhatsAppLocationMessage(record);
						case MessageTypes.Document:
							return new WhatsAppDocumentMessage(record);
						case MessageTypes.Payload:
							return new WhatsAppPayloadMessage(record);
						case MessageTypes.Sticker:
							return new WhatsAppStickerMessage(record);
						case MessageTypes.Interactive:
							return new WhatsAppInteractiveMessage(record);
						case MessageTypes.Order:
							return new WhatsAppOrderMessage(record);
						case MessageTypes.ReferredProduct:
							return new WhatsAppReferredProductMessage(record);
						default:
							return new WhatsAppTextMessage(record);
					}
				}
			}
			catch
			{
				return new WhatsAppTextMessage(record);
			}
		}

		#endregion

		#region Public Methods

		/// <summary>
		/// Escribe el parámetro especificado por <paramref name="parameterKey"/> con valor <paramref name="parameterValue"/> dentro del un <see cref="Newtonsoft.Json.JsonWriter"/>
		/// </summary>
		/// <param name="writer">El <see cref="Newtonsoft.Json.JsonWriter"/> donde se escribe el parámetro</param>
		/// <param name="serializer">El <see cref="Newtonsoft.Json.JsonSerializer"/> que se está utilizando para escribir</param>
		/// <param name="parameterKey">El nombre del parámetro a escribir</param>
		/// <param name="parameterValue">El valor del parámetro a escribir</param>
		public override void WriteParameterInJson(Newtonsoft.Json.JsonWriter writer, Newtonsoft.Json.JsonSerializer serializer, string parameterKey, string parameterValue)
		{
			var parametersToIgnore = new string[] {
				PayloadParameter
				, SocialUserIdParameter
				, GroupIDParameter
				, ReplyFileNameParameter
			};

			if (parametersToIgnore.Contains(parameterKey))
				return;

			switch (parameterKey)
			{
				case SentParameter:
				case DeliveredParameter:
				case ReadParameter:
				case HSMParameter:
					if (!string.IsNullOrEmpty(parameterValue))
					{
						writer.WritePropertyName(parameterKey);
						try
						{
							writer.WriteValue(bool.Parse(parameterValue));
						}
						catch
						{
							writer.WriteValue(parameterValue);
						}

						return;
					}
					break;

				case SentAtParameter:
				case DeliveredAtParameter:
				case ReadAtParameter:
					if (!string.IsNullOrEmpty(parameterValue))
					{
						writer.WritePropertyName(parameterKey);
						try
						{
							writer.WriteValue(Convert.ToInt64(parameterValue));
						}
						catch
						{
							writer.WriteValue(parameterValue);
						}

						return;
					}
					break;

				case SentDateParameter:
				case DeliveredDateParameter:
				case ReadDateParameter:
					if (!string.IsNullOrEmpty(parameterValue))
					{
						writer.WritePropertyName(parameterKey);
						try
						{
							writer.WriteValue(Convert.ToDateTime(parameterValue));
						}
						catch
						{
							writer.WriteValue(parameterValue);
						}

						return;
					}
					break;

				case SentStatusCodeParameter:
				case DeliveredStatusCodeParameter:
					if (!string.IsNullOrEmpty(parameterValue))
					{
						writer.WritePropertyName(parameterKey);
						try
						{
							writer.WriteValue(Convert.ToInt32(parameterValue));
						}
						catch
						{
							writer.WriteValue(parameterValue);
						}

						return;
					}
					break;

				case PreviousBotEventsParameter:
				case HSMTemplateDataParameter:
					if (!string.IsNullOrEmpty(parameterValue))
					{
						writer.WritePropertyName(parameterKey);
						try
						{
							writer.WriteRawValue(parameterValue);
						}
						catch
						{
							writer.WriteValue(parameterValue);
						}

						return;
					}
					break;

				case SentStatusParameter:
				case DeliveredStatusParameter:
				case HSMTemplateNamespaceParameter:
				case HSMTemplateElementNameParameter:
				case HSMTemplateLanguage:
				default:
					break;
			}

			base.WriteParameterInJson(writer, serializer, parameterKey, parameterValue);
		}


		/// <summary>
		/// Convierte parametros de un FlowToken para invocar posteriormente a yFlow
		/// </summary>
		/// <param name="info"></param>
		public void ConvertWhatsappFlowInvokeParams(string parameters)
		{
			try
			{
				//TODO revisar esto 
				var obj = JsonConvert.DeserializeObject(parameters);
				var info = JObject.Parse(obj.ToString());

				dynamic metaFlowParameters = new System.Dynamic.ExpandoObject();
				metaFlowParameters.successBlockId = info["sbid"];
				metaFlowParameters.expirationBlockId = info["ebid"];
				metaFlowParameters.caseExceptionBlockId = info["cebid"];
				metaFlowParameters.caseId = info["caseId"];
				metaFlowParameters.exp = info["exp"];

				this.Parameters[MetaFlowInvokeParameter] = Newtonsoft.Json.JsonConvert.SerializeObject(metaFlowParameters);
			}
			
			catch(Exception ex)
			{
				Tracer.TraceError("Fallo al convertir los parametros del flow de META {0}", ex);
			}
		}

		/// <summary>
		/// Completa datos para la invocación de yFlow
		/// </summary>
		/// <param name="jContent">Los datos de la invocación</param>
		public override void FillYFlowParameters(Newtonsoft.Json.Linq.JObject jContent)
		{
			if (this.Parameters.ContainsKey(PayloadParameter))
				jContent["message"]["payload"] = this.Parameters[PayloadParameter];

			if (this.Parameters.ContainsKey(ContextSourceTemplateNameParameter))
			{
				jContent["message"]["repliesTemplate"] = true;
				jContent["message"]["repliesTemplateName"] = this.Parameters[ContextSourceTemplateNameParameter];

				bool isHSMWithoutCase = false;
				if (this.Parameters.ContainsKey(ContextSourceIsFromHSMWithoutCase))
					bool.TryParse(this.Parameters[ContextSourceIsFromHSMWithoutCase], out isHSMWithoutCase);
				
				jContent["message"]["repliesFromHSMWithoutCase"] = isHSMWithoutCase;
			}

			if (this.Parameters.ContainsKey(ContextSourceTemplateLanguageParameter))
			{
				jContent["message"]["repliesTemplate"] = true;
				jContent["message"]["repliesTemplateLanguage"] = this.Parameters[ContextSourceTemplateLanguageParameter];
			}

			if (this.Parameters.ContainsKey(CampaignParameter))
			{
				jContent["message"]["relatedCampaign"] = this.Parameters[CampaignParameter];
			}

			if (this.Parameters.ContainsKey(ReferralSourceIdParameter))
			{
				if (jContent["message"]["referral"] == null)
					jContent["message"]["referral"] = new Newtonsoft.Json.Linq.JObject();

				jContent["message"]["referral"]["sourceId"] = this.Parameters[ReferralSourceIdParameter];
			}

			if (this.Parameters.ContainsKey(ReferralSourceUrlParameter))
			{
				if (jContent["message"]["referral"] == null)
					jContent["message"]["referral"] = new Newtonsoft.Json.Linq.JObject();

				jContent["message"]["referral"]["sourceUrl"] = this.Parameters[ReferralSourceUrlParameter];
			}

			if (this.Parameters.ContainsKey(ReferralSourceTypeParameter))
			{
				if (jContent["message"]["referral"] == null)
					jContent["message"]["referral"] = new Newtonsoft.Json.Linq.JObject();

				jContent["message"]["referral"]["sourceType"] = this.Parameters[ReferralSourceTypeParameter];
			}

			if (this.Parameters.ContainsKey(ReferralCTWAClickIdParameter))
			{
				if (jContent["message"]["referral"] == null)
					jContent["message"]["referral"] = new Newtonsoft.Json.Linq.JObject();

				jContent["message"]["referral"]["ctwa_clid"] = this.Parameters[ReferralCTWAClickIdParameter];
			}

			if (this.Parameters.ContainsKey(MetaFlowInvokeParameter) &&
				!string.IsNullOrEmpty(this.Parameters[MetaFlowInvokeParameter]))
			{
				jContent["message"]["whatsappFlow"] = Newtonsoft.Json.Linq.JObject.Parse(this.Parameters[MetaFlowInvokeParameter]);
			}
		}

		#endregion
	}
}
