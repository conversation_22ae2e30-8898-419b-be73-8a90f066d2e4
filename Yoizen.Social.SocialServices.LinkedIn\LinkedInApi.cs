﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yoizen.Social.SocialServices.LinkedIn
{
	public static class LinkedInApi
	{
		#region Constructors

		static LinkedInApi()
		{
			Version = System.Configuration.ConfigurationManager.AppSettings["LinkedInApiVersion"];
			if (string.IsNullOrEmpty(Version))
			{
				Version = DateTime.Now.AddMonths(-2).ToString("yyyyMM");
			}
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve la versión a utilizar en la Graph Api
		/// </summary>
		public static string Version { get; private set; }

		#endregion
	}
}
