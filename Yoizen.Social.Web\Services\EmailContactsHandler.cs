﻿using Newtonsoft.Json.Schema;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using Yoizen.Common;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.SocialServices.Facebook.Wrappers;

namespace Yoizen.Social.Web.Services
{
    public class EmailContactsHandler : SyncHandler
    {
        #region Constructors

        public EmailContactsHandler() { }

        #endregion

        #region IHttpHandler Implementation
        protected override string[] Operations
        {
            get
            {
                return new string[] {
                    "getemailcontacts"
                    , "insert"
                    , "update"
                    , "delete"
                };
            }
        }

        protected override string EndPoint { get { return "emailcontacts"; } }

        public override void ProcessRequest(HttpContext context)
        {
            if (context.Request.HttpMethod.Equals("OPTIONS"))
            {
                if (Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
                {
                    base.HandleCorsRequest(context);
                    return;
                }
                else
                {
                    base.AddCorsHeaders(context);
                    context.Response.StatusCode = 200;
                    context.Response.End();
                    return;
                }
            }

            if (!IsRequestValid(context))
                return;

            try
            {
                string operation = GetOperationFromRequest(context);

                switch (operation.ToLower())
                {
                    case "update":
                        Update(context);
                        break;
                    case "insert":
                        Insert(context);
                        break;
                    case "delete":
                        Delete(context);
                        break;
                    case "getemailcontacts":
                        GetEmailContacts(context);
                        break;
                    default:
                        return;
                }
            }
            catch (Exception ex)
            {
                Tracer.TraceError("Ocurrió un error procesando el request: {0}", ex);
                ResponseError(context, ex);
            }
        }
        #endregion

        #region Private Methods
        private void Update(HttpContext context)
        {
            if (!context.Request.HttpMethod.Equals("POST"))
            {
                ResponseError(context, string.Format("Invalid HTTP Method for operation: {0}. Only accepted: {1}", "update", "POST"), 3, System.Net.HttpStatusCode.MethodNotAllowed);
                return;
            }

            if (context.Request.InputStream == null)
            {
                ResponseError(context, "Expecting the request body", 3);
                return;
            }

            string body;
            try
            {
                using (StreamReader sr = new StreamReader(context.Request.InputStream))
                {
                    body = sr.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                Tracer.TraceError("No se pudo obtener los datos del POST: {0}", ex);
                ResponseError(context, string.Format("Couldn't get the POST request {0}", ex), 3);
                return;
            }

            Newtonsoft.Json.Linq.JObject jContents;

            try
            {
                var schemaContents = System.IO.File.ReadAllText(System.Web.Hosting.HostingEnvironment.MapPath("~/Services/EmailContactsSchemas/EmailContacts_Update.json"));
                var schema = Newtonsoft.Json.Schema.JSchema.Parse(schemaContents);

                jContents = Newtonsoft.Json.Linq.JObject.Parse(body);
                IList<string> errorMessages;
                var isValid = jContents.IsValid(schema, out errorMessages);

                if (!isValid)
                {
                    var error = new
                    {
                        message = "Invalid json contents",
                        errors = errorMessages
                    };

                    Tracer.TraceError("El contenido es inválido: {0}. Error: {1}", body, string.Join(Environment.NewLine, errorMessages));
                    ResponseError(context, "Couldn't update the email contact. The JSON contents are invalid", 4, error);
                    return;
                }
            }
            catch (Exception ex)
            {
                var error = new
                {
#if DEBUG
                    message = string.Format("Invalid json contents. Error: {0}", ex)
#else
						message = "Invalid json contents"
#endif
                };

                Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, ex);
                ResponseError(context, "Couldn't update the email contact", 4, error);
                return;
            }

            var agent = jContents["agentId"];
            int agentId;

            if (agent != null && int.TryParse(agent.ToString(), out agentId))
            {
                if (DAL.AgentDAO.GetOneFromCache(agentId) != null)
                {
                    var emailContact = new EmailContact()
                    {
                        ID = (int)jContents["id"],
                        AgentID = agentId,
                        Name = (string)jContents["name"],
                        LastName = (string)jContents["lastName"],
                        Email = (string)jContents["email"]
                    };
                    var dao = new EmailContactDAO(emailContact);
                    var result = dao.Update();

                    switch (result)
                    {
                        case 1:
                            {
                                ResponseSuccess(context, null);
                                break;
                            }
                        case -1:
                            {
                                ResponseError(context, $"Couldn´t find EmailContact with ID {(int)jContents["id"]}", 100);
                                break;
                            }
                        case -2:
                            {
                                ResponseError(context, $"The email {(string)jContents["email"]} is already used for this agent.", 200);
                                break;
                            }
                        default:
                            {
                                ResponseError(context, $"Couldn't update the email contact.", 300);
                                break;
                            }
                    }
                    return;
                }
                else
                {
                    Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, $"El el agente con el id {agent} no existe");
                    ResponseError(context, "Couldn't update the email contact", 252, $"Agent with agentId {agent} does not exist");
                    return;
                }
            }
            else
            {
                Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, "El id del agente es erroneo.");
                ResponseError(context, "Couldn't update the email contact", 252, "Wrong agentId.");
                return;
            }


        }

        private void Insert(HttpContext context)
        {
            if (!context.Request.HttpMethod.Equals("POST"))
            {
                ResponseError(context, string.Format("Invalid HTTP Method for operation: {0}. Only accepted: {1}", "insert", "POST"), 3, System.Net.HttpStatusCode.MethodNotAllowed);
                return;
            }

            if (context.Request.InputStream == null)
            {
                ResponseError(context, "Expecting the request body", 3);
                return;
            }

            string body;
            try
            {
                using (StreamReader sr = new StreamReader(context.Request.InputStream))
                {
                    body = sr.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                Tracer.TraceError("No se pudo obtener los datos del POST: {0}", ex);
                ResponseError(context, string.Format("Couldn't get the POST request {0}", ex), 3);
                return;
            }

            Newtonsoft.Json.Linq.JObject jContents;

            try
            {
                var schemaContents = System.IO.File.ReadAllText(System.Web.Hosting.HostingEnvironment.MapPath("~/Services/EmailContactsSchemas/EmailContacts_Create.json"));
                var schema = Newtonsoft.Json.Schema.JSchema.Parse(schemaContents);

                jContents = Newtonsoft.Json.Linq.JObject.Parse(body);
                IList<string> errorMessages;
                var isValid = jContents.IsValid(schema, out errorMessages);

                if (!isValid)
                {
                    var error = new
                    {
                        message = "Invalid json contents",
                        errors = errorMessages
                    };

                    Tracer.TraceError("El contenido es inválido: {0}. Error: {1}", body, string.Join(Environment.NewLine, errorMessages));
                    ResponseError(context, "Couldn't insert the email contact. The JSON contents are invalid", 4, error);
                    return;
                }
            }
            catch (Exception ex)
            {
                var error = new
                {
#if DEBUG
                    message = string.Format("Invalid json contents. Error: {0}", ex)
#else
						message = "Invalid json contents"
#endif
                };

                Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, ex);
                ResponseError(context, "Couldn't insert the email contact", 4, error);
                return;
            }

            var agent = jContents["agentId"];
            int agentId;

            if (agent != null && int.TryParse(agent.ToString(), out agentId))
            {
                if (DAL.AgentDAO.GetOneFromCache(agentId) != null)
                {
                    var emailContact = new EmailContact()
                    {
                        AgentID = agentId,
                        Name = (string)jContents["name"],
                        LastName = (string)jContents["lastName"],
                        Email = (string)jContents["email"]
                    };
                    var dao = new EmailContactDAO(emailContact);
                    int? newId;
                    string errorMessage = "";
                    var result = dao.Insert(out newId, out errorMessage);

                    if (result)
                    {
                        ResponseSuccess(context, new { newId });
                    }
                    else
                    {
                        ResponseError(context, errorMessage, 100);
                    }

                    return;
                }
                else
                {
                    Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, $"El el agente con el id {agent} no existe");
                    ResponseError(context, "Couldn't insert the email contact", 252, $"Agent with agentId {agent} does not exist");
                    return;
                }
            }
            else
            {
                Tracer.TraceError("El contenido del contacto de email es inválido: {0}. Error: {1}", body, "El id del agente es erroneo.");
                ResponseError(context, "Couldn't insert the email contact", 252, "Wrong agentId.");
                return;
            }

        }

        private void Delete(HttpContext context)
        {
            if (!context.Request.HttpMethod.Equals("DELETE", StringComparison.OrdinalIgnoreCase))
            {
                ResponseError(
                    context,
                    $"Invalid HTTP Method for operation: DELETE. Only accepted: DELETE",
                    3,
                    System.Net.HttpStatusCode.MethodNotAllowed
                );
                return;
            }

            int id;
            if (!IsNumericParameterValid<int>(context, context.Request.QueryString, "id", out id))
            {
                ResponseError(context, "Missing or invalid 'id' parameter.", 400);
                return;
            }

            string errorMessage;
            var deleted = EmailContactDAO.DeleteEmailContact(id, out errorMessage);

            if (deleted)
            {
                ResponseSuccess(context, new { message = "EmailContact deleted successfully."});
            }
            else
            {
                ResponseError(context, errorMessage ?? "Failed to delete EmailContact.", 500);
            }
        }

        private void GetEmailContacts(HttpContext context)
        {
            if (!context.Request.HttpMethod.Equals("GET"))
            {
                ResponseError(context, $"Invalid HTTP Method for operation: GetEmailContacts. Only accepted: GET", 3, System.Net.HttpStatusCode.MethodNotAllowed);
                return;
            }

            int agentId;
            if (!IsNumericParameterValid<int>(context, context.Request.QueryString, "agentId", out agentId))
            {
                ResponseError(context, "Missing or invalid 'agentId' parameter", 250);
                return;
            }

            string text = context.Request.QueryString["text"];
            if (string.IsNullOrWhiteSpace(text))
            {
                text = null;
            }

            int lastEmailContactIDValue;
            int? lastEmailContactID = null;
            if (int.TryParse(context.Request.QueryString[""], out lastEmailContactIDValue))
            {
                lastEmailContactID = lastEmailContactIDValue;
            }

            try
            {
                bool? moreEmailContactsAvailable;
                var result = EmailContactDAO.GetAllByAgentID(agentId, text, lastEmailContactID, out moreEmailContactsAvailable);

                var responseObject = new
                {
                    items = result,
                    moreAvailable = moreEmailContactsAvailable
                };

                ResponseSuccess(context, responseObject);
                return;
            }
            catch (Exception ex)
            {
                ResponseError(context, ex);
                return;
            }
        }

        #endregion
    }
}