﻿{
    "$schema": "http://json-schema.org/draft-04/schema",
    "title": "JSON Schema para actualizar un contacto de email",
    "type": "object",
    "properties": {
        "id": {
            "type": "number",
            "description": "Indica el id del contacto de email"
        },
        "name": {
            "type": "string",
            "description": "Indica el nombre"
        },
        "lastName": {
            "type": "string",
            "description": "Indica el apellido"
        },
        "email": {
            "type": "string",
            "description": "Indica el email"
        },
        "agentId": {
            "type": "number",
            "description": "Indica el id del agente"
        }
    },
    "required": [
        "id",
        "name",
        "lastName",
        "email",
        "agentId"
    ]
}