﻿using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Common.LDAP;
using Yoizen.Social.Core.Enums;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;

namespace Yoizen.Social.Web.Administration
{
	public partial class Users : LoginRequiredBasePage
	{
		#region Constants

		private enum CopyCriteria : byte
		{
			CopyProfile = 1,
			CopyAllowed = 2,
			CopyAsociatedQueues = 4,
			CopyAgentsGroups = 8,
			CopySettings = 16
		}

		#endregion

		#region Properties

		protected override string RedirectUrl
		{
			get
			{
				if (Request.QueryString.ToString().Equals("Startup", StringComparison.InvariantCultureIgnoreCase))
					return "~/Administration/Users.aspx?Startup";
				return "~/Administration/Users.aspx";
			}
		}

		protected override string PageDescription { get { return "Usuarios"; } }

		protected override string PageDescriptionLocalizationKey { get { return "administration-users-title"; } }

		public bool Nuevo
		{
			get { return (bool) ViewState["Nuevo"]; }
			set { ViewState["Nuevo"] = value; }
		}

		public int EntityID
		{
			get { return (int) ViewState["ID"]; }
			set { ViewState["ID"] = value; }
		}

		public string Username
		{
			get { return (string) ViewState["Username"]; }
			set { ViewState["Username"] = value; }
		}

		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			panelUserCannotBeDeleted.Visible = false;

			if (!Page.IsPostBack)
			{
				panelButtons.Visible = this.LoggedUser.HasPermission(Permissions.UsersAdministration);

				panelUserType.Visible = DomainModel.SystemSettings.Instance.AuthenticationType != AuthenticationTypes.Local;
				if (!DomainModel.SystemSettings.Instance.LDAP.UseLDAP)
					dropdownlistUserType.Items.Remove(dropdownlistUserType.Items.FindByValue(((short) DomainModel.AuthenticationTypes.Domain).ToString()));
				if (!DomainModel.SystemSettings.Instance.GoogleAuth.Enabled)
					dropdownlistUserType.Items.Remove(dropdownlistUserType.Items.FindByValue(((short) DomainModel.AuthenticationTypes.Google).ToString()));
				if (!DomainModel.SystemSettings.Instance.SamlAuth.Enabled)
					dropdownlistUserType.Items.Remove(dropdownlistUserType.Items.FindByValue(((short) DomainModel.AuthenticationTypes.Saml).ToString()));
			}
			else
			{
				string script = (string) ViewState["Script"];
				if (!string.IsNullOrEmpty(script))
					ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "ProfileAndPermissions", script, true);
			}

			this.RegisterJsonVariable("userHasUserAdministrationPermission", this.LoggedUser.HasPermission(Permissions.UsersAdministration));
			var profiles = DomainModel.Cache.Instance.GetList<DomainModel.Profile>().OrderBy(p => p.Name);
			this.RegisterJsonVariable("profiles", profiles.Select(p => new
			{
				p.ID,
				p.Name
			}));

			var settings = DomainModel.SystemSettings.Instance;

			this.RegisterJsonVariable("authenticationType", (short) settings.AuthenticationType);

			switch (DomainModel.SystemSettings.Instance.AuthenticationType)
			{
				case AuthenticationTypes.Domain:
					this.RegisterJsonVariable("allowCreateLocalUsers", settings.LDAP.AllowCreateLocalUsers);
					if (!settings.LDAP.AllowCreateLocalUsers)
					{
						dropdownlistUserType.Visible = false;
						labelDomainUser.Visible = true;
						labelDisplaySearchButton.Visible = true;
						trUserType.Visible = true;
					}
					else
					{
						trUserType.Visible = true;
						labelDomainUser.Visible = false;
						dropdownlistUserType.Visible = true;
					}
					break;
				case AuthenticationTypes.Google:
					this.RegisterJsonVariable("allowCreateLocalUsers", settings.GoogleAuth.AllowCreateLocalUsers);
					if (!settings.GoogleAuth.AllowCreateLocalUsers)
					{
						dropdownlistUserType.Visible = false;
						labelGoogleUser.Visible = true;
						labelDisplaySearchButton.Visible = false;
						trUserType.Visible = true;
					}
					else
					{
						trUserType.Visible = true;
						labelGoogleUser.Visible = false;
						dropdownlistUserType.Visible = true;
					}
					break;
				case AuthenticationTypes.Saml:
					this.RegisterJsonVariable("allowCreateLocalUsers", settings.SamlAuth.AllowCreateLocalUsers);
					if (!settings.SamlAuth.AllowCreateLocalUsers)
					{
						dropdownlistUserType.Visible = false;
						labelSamlUser.Visible = true;
						labelDisplaySearchButton.Visible = false;
						trUserType.Visible = true;
					}
					else
					{
						trUserType.Visible = true;
						labelSamlUser.Visible = false;
						dropdownlistUserType.Visible = true;
					}
					break;
				default:
					trUserType.Visible = false;
					break;
			}

			this.RegisterJsonVariable("minimumPasswordStrength", DomainModel.SystemSettings.Instance.MinimumPasswordStrength);

			IEnumerable<DomainModel.Queue> queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();
			this.RegisterJsonVariable("queues", queues.Where(q => !q.Deleted && q.Enabled).OrderBy(q => q.Name).Select(q => new
			{
				ID = q.ID,
				Name = q.Name
			}));

			IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
			this.RegisterJsonVariable("services", services.Where(q => q.Enabled).OrderBy(q => q.Name).Select(q => new
			{
				ID = q.ID,
				Name = q.Name
			}));

			if (Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
				literalScriptZXCVBN.Text = "<script src='https://cdnjs.cloudflare.com/ajax/libs/zxcvbn/4.4.2/zxcvbn.js'></script>";
			else
				literalScriptZXCVBN.Text = string.Format("<script type='text/javascript' language='javascript' src='{0}'></script>", Helpers.Fingerprint.Tag(ResolveUrl("~/Scripts/zxcvbn.js")));

			if (Licensing.LicenseManager.Instance.License.Configuration.MaxNominalUsers > 0)
			{
				// Le resto a la cantidad total de usuarios los 1 usuarios default de social
				int totalAgents = DomainModel.Cache.Instance.GetList<Agent>().Count;
				int totalUsers = DomainModel.Cache.Instance.GetList<User>().Where(u => !u.Deleted).Count() - 1;
				int nominalUsers = totalAgents + totalUsers;

				buttonNew.Parent.Visible = nominalUsers < Licensing.LicenseManager.Instance.License.Configuration.MaxNominalUsers;
				messageCannotCreateMoreUsers.Visible = nominalUsers >= Licensing.LicenseManager.Instance.License.Configuration.MaxNominalUsers;
				messageCannotCreateMoreUsers.LocalizationKey = "configuration-cannot_create_more-number";

				this.RegisterJsonVariable("cannotCreateMoreUsers", nominalUsers >= Licensing.LicenseManager.Instance.License.Configuration.MaxNominalUsers);
			}
		}

		#region Private Methods

		private bool ValidateUser()
		{
			if (!this.LoggedUser.HasAnyPermissions(Permissions.UsersAdministration, Permissions.ChangeUserPassword))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private void ToggleToEditMode()
		{
			ToggleToEditMode(null);
		}

		private void ToggleToEditMode(DomainModel.User user)
		{
			panelNew.Visible = true;
			panelList.Visible = false;

			IEnumerable<DomainModel.Permission> permissions = PermissionDAO.GetAll();
			permissions = permissions.OrderBy(p => Localizations.Instance.GetLocalizedString($"Permissions.{p.PermissionType}", this.LoggedUserLanguage, p.Name));
			gridviewPermissions.DataSource = permissions;
			gridviewPermissions.DataBind();

			IEnumerable<Profile> profiles = DomainModel.Cache.Instance.GetList<DomainModel.Profile>().OrderBy(p => p.Name);
			gridviewProfiles.DataSource = profiles;
			gridviewProfiles.DataBind();

			var profilesAndPermissions = from profile
											 in profiles
										 select new
										 {
											 ProfileID = profile.ID,
											 Permissions = (from permission
															in profile.Permissions
															select permission.ID)
										 };

			string script = string.Format("var profileAndPermissions = {0};\n", Newtonsoft.Json.JsonConvert.SerializeObject(profilesAndPermissions));
			if (user != null)
			{
				var userPermissions = user.Permissions.Select(p => p.ID);
				script = string.Concat(script, string.Format("var userPermissions = {0};\n", Newtonsoft.Json.JsonConvert.SerializeObject(userPermissions)));
			}
			else
			{
				script = string.Concat(script, "var userPermissions = null;\n");
			}

			ViewState["Script"] = script;
			ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "ProfileAndPermissions", script, true);

			panelNewUser.Visible = this.Nuevo;
			panelEditUser.Visible = !this.Nuevo;
		}

		private static object BuildUsers(IEnumerable<User> users)
		{
			if (users == null)
				return null;

			return users.Select(a => BuildUserInfo(a));
		}

		private static object BuildUserInfo(User user)
		{
			if (user == null)
				return null;

			return new
			{
				user.ID,
				user.FullName,
				user.UserName,
				user.Enabled,
				user.AuthenticationType,
				user.Email,
				user.LastLogin,
				Queues = user.Queues.Select(q => q.ID),
				Profiles = user.Profiles.Select(p => p.ID),
				user.IsAdministrator,
				user.IsSupervisor,
				TwoFactorEnabled = user.Parameters.ContainsKey(DomainModel.Person.TwoFactorEnabledParameter) && bool.Parse(user.Parameters[DomainModel.Person.TwoFactorEnabledParameter]),
				user.Services
			};
		}

		private byte LoadSelectedCopyCriteria()
		{
			byte copyCriteriaVal = 0;

			bool copyProfile = bool.TryParse(hiddenCopyProfile.Value, out bool resultCopyProfile) && resultCopyProfile;
			bool copyAllowed = bool.TryParse(hiddenCopyAllowed.Value, out bool resultCopyAllowed) && resultCopyAllowed;
			bool copyAsociatedQueues = bool.TryParse(hiddenCopyAsociatedQueues.Value, out bool resultCopyAsociatedQueues) && resultCopyAsociatedQueues;
			bool copyAgentsGroups = bool.TryParse(hiddenCopyAgentsGroups.Value, out bool resultCopyAgentsGroups) && resultCopyAgentsGroups;
			bool copySettings = bool.TryParse(hiddenCopySettings.Value, out bool resultCopySettings) && resultCopySettings;

			if (copyProfile)
				copyCriteriaVal += (byte) CopyCriteria.CopyProfile;
			if (copyAllowed)
				copyCriteriaVal += (byte) CopyCriteria.CopyAllowed;
			if (copyAsociatedQueues)
				copyCriteriaVal += (byte) CopyCriteria.CopyAsociatedQueues;
			if (copyAgentsGroups)
				copyCriteriaVal += (byte) CopyCriteria.CopyAgentsGroups;
			if (copySettings)
				copyCriteriaVal += (byte) CopyCriteria.CopySettings;

			return copyCriteriaVal;
		}

		private void LoadCopyData(int userID)
		{
			this.Nuevo = true;

			User user = UserDAO.GetOneFromCache(userID);
			ToggleToEditMode(user);

			byte criteria = LoadSelectedCopyCriteria();

			if (criteria != 0)
			{
				ClearData();
				LoadDataInForm(user, criteria);
			}
		}

		private void LoadDataInForm(User user, byte copyCriteriaCombination = 0)
		{
			bool copyProfile = (copyCriteriaCombination & (byte) CopyCriteria.CopyProfile) != 0;
			bool copyAllowed = (copyCriteriaCombination & (byte) CopyCriteria.CopyAllowed) != 0;

			if (copyProfile)
				LoadProfile(user);

			if (copyAllowed)
				LoadAllowed(user);

		}

		private void ClearData()
		{
			dropdownlistUserType.SelectedIndex = 0;
			textboxLastName.Text = string.Empty;
			textboxFirstName.Text = string.Empty;
			textboxUsername.Text = string.Empty;
			textboxEmail.Text = string.Empty;
			textboxLDAP.Text = string.Empty;
		}

		private void LoadProfile(User user)
		{
			foreach (GridViewRow item in gridviewProfiles.Rows)
			{
				if (item.RowType == DataControlRowType.DataRow)
				{
					short profileID = (short) gridviewProfiles.DataKeys[item.RowIndex].Value;

                    foreach (var profile in user.Profiles)
					{
						if (profile.ID == profileID)
						{
							CheckBox checkboxSelected = (CheckBox)item.FindControl("checkboxSelected");
							checkboxSelected.Checked = true;
							break;
						}
					}
				}
			}
		}

		private void LoadAllowed(User user)
		{
			foreach (GridViewRow item in gridviewPermissions.Rows)
			{
				if (item.RowType == DataControlRowType.DataRow)
				{
					short permissionID = (short) gridviewPermissions.DataKeys[item.RowIndex].Value;
					CheckBox checkboxSelected = (CheckBox) item.FindControl("checkboxSelected");
					checkboxSelected.Checked = false;

					bool isAlreadyInProfile = false;

					foreach (var profile in user.Profiles)
					{
						foreach (var permission in profile.Permissions)
						{
							if (permission.ID == permissionID)
							{
								checkboxSelected.Checked = true;
								isAlreadyInProfile = true;
								break;
							}
						}
					}

					if (!isAlreadyInProfile)
					{
						foreach (var permission in user.Permissions)
						{
							if (permission.ID == permissionID)
							{
								checkboxSelected.Checked = true;
								break;
							}
						}
					}
				}
			}
		}

		private void LoadSettings(User user, User sourceUser)
		{
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeAgentsAuxReason))
				user.Settings[DomainModel.User.ReportsRealTimeAgentsAuxReason] = sourceUser.Settings[DomainModel.User.ReportsRealTimeAgentsAuxReason];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeAgentsIncludeUsername))
				user.Settings[DomainModel.User.ReportsRealTimeAgentsIncludeUsername] = sourceUser.Settings[DomainModel.User.ReportsRealTimeAgentsIncludeUsername];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeAgentsTotals))
				user.Settings[DomainModel.User.ReportsRealTimeAgentsTotals] = sourceUser.Settings[DomainModel.User.ReportsRealTimeAgentsTotals];

			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeMessagesQueues))
				user.Settings[DomainModel.User.ReportsRealTimeMessagesQueues] = sourceUser.Settings[DomainModel.User.ReportsRealTimeMessagesQueues];

			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesFavouriteQueues))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesFavouriteQueues] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesFavouriteQueues];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesTypesResponses))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesTypesResponses] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesTypesResponses];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesDefaultSections))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesDefaultSections] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesDefaultSections];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesDateFormat))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesDateFormat] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesDateFormat];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesServiceLevelFormat))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesServiceLevelFormat] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesServiceLevelFormat];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeQueuesTagChartStyle))
				user.Settings[DomainModel.User.ReportsRealTimeQueuesTagChartStyle] = sourceUser.Settings[DomainModel.User.ReportsRealTimeQueuesTagChartStyle];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ReportsRealTimeAgentsAuxReasonsStatusesToShow))
				user.Settings[DomainModel.User.ReportsRealTimeAgentsAuxReasonsStatusesToShow] = sourceUser.Settings[DomainModel.User.ReportsRealTimeAgentsAuxReasonsStatusesToShow];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.PaginationSize))
				user.Settings[DomainModel.User.PaginationSize] = sourceUser.Settings[DomainModel.User.PaginationSize];

			if (sourceUser.Settings.ContainsKey(DomainModel.User.MaxUnreadNotifications))
				user.Settings[DomainModel.User.MaxUnreadNotifications] = sourceUser.Settings[DomainModel.User.MaxUnreadNotifications];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.PlayNotificationsSound))
				user.Settings[DomainModel.User.PlayNotificationsSound] = sourceUser.Settings[DomainModel.User.PlayNotificationsSound];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.NewChatMessageSound))
				user.Settings[DomainModel.User.NewChatMessageSound] = sourceUser.Settings[DomainModel.User.NewChatMessageSound];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.NewNotificationSound))
				user.Settings[DomainModel.User.NewNotificationSound] = sourceUser.Settings[DomainModel.User.NewNotificationSound];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.MailShowSubjectInsteadOfBody))
				user.Settings[DomainModel.User.MailShowSubjectInsteadOfBody] = sourceUser.Settings[DomainModel.User.MailShowSubjectInsteadOfBody];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.ShowMoreInformationInTheLeft))
				user.Settings[DomainModel.User.ShowMoreInformationInTheLeft] = sourceUser.Settings[DomainModel.User.ShowMoreInformationInTheLeft];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.AutoCompleteFromDate))
				user.Settings[DomainModel.User.AutoCompleteFromDate] = sourceUser.Settings[DomainModel.User.AutoCompleteFromDate];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.NotifyCoaching))
				user.Settings[DomainModel.User.NotifyCoaching] = sourceUser.Settings[DomainModel.User.NotifyCoaching];
			if (sourceUser.Settings.ContainsKey(DomainModel.User.DifferentiateConsolidatedReportsTotalsBetweenColors))
				user.Settings[DomainModel.User.DifferentiateConsolidatedReportsTotalsBetweenColors] = sourceUser.Settings[DomainModel.User.DifferentiateConsolidatedReportsTotalsBetweenColors];

			user.WordCloudSettings = sourceUser.WordCloudSettings;
		}

		private void CopyQueues(User user, User sourceUser)
		{
			var queues = sourceUser.Queues;

			foreach (var queue in queues)
			{
				if (!queue.Supervisors.Contains(user))
					queue.Supervisors.Add(user);

				if (!user.Queues.Contains(queue))
					user.Queues.Add(queue);
			}
		}

		private void CopyGroups(User user, User sourceAgent)
		{
			foreach (var group in sourceAgent.AgentGroups)
			{
				if (!user.AgentGroups.Contains(group))
					user.AgentGroups.Add(group);

				if (!group.Supervisors.Contains(user))
					group.Supervisors.Add(user);

			}
		}

		#endregion

		#region Button Events

		protected void buttonAction_Click(object sender, EventArgs e)
		{
			int userId = int.Parse(hiddenActionUserID.Value);
			var user = UserDAO.GetOneFromCache(userId);
			this.EntityID = userId;

			switch (hiddenActionName.Value)
			{
				case "Edit":
					{
						this.Nuevo = false;

						ToggleToEditMode(user);

						textboxFirstNameEdit.Text = user.FirstName;
						textboxLastNameEdit.Text = user.LastName;
						literalUsername.Text = user.UserName;
						placeholderEmailEdit.Visible = user.AuthenticationType == AuthenticationTypes.Local;
						placeholderNotAdministratorUser.Visible = user.ID != 1;
						placeholderAdministratorUser.Visible = user.ID == 1;
						textboxEmailEdit.Text = user.Email;

						foreach (GridViewRow item in gridviewProfiles.Rows)
						{
							if (item.RowType == DataControlRowType.DataRow)
							{
								short profileID = (short) gridviewProfiles.DataKeys[item.RowIndex].Value;

								foreach (var profile in user.Profiles)
								{
									if (profile.ID == profileID)
									{
										CheckBox checkboxSelected = (CheckBox) item.FindControl("checkboxSelected");
										checkboxSelected.Checked = true;
										break;
									}
								}
							}
						}

						string type = "image/jpeg";
						if (user.Parameters.ContainsKey(DomainModel.User.AvatarContentTypeParameter) &&
							!string.IsNullOrEmpty(user.Parameters[DomainModel.User.AvatarContentTypeParameter]))
							type = user.Parameters[DomainModel.User.AvatarContentTypeParameter];

						var uri = ResolveUrl(string.Format("~/services/configuration/personavatar?id={0}", user.ID));
						uri = Common.AuthorizationHelper.AppendSignatureToUrl(new Uri(new Uri(Core.System.Instance.SiteRoot), uri), "avatar", true, "sig").ToString();
						hiddenUserAvatarFile.Value = uri;
						hiddenUserAvatarFileContentType.Value = type;

						foreach (GridViewRow item in gridviewPermissions.Rows)
						{
							if (item.RowType == DataControlRowType.DataRow)
							{
								short permissionID = (short) gridviewPermissions.DataKeys[item.RowIndex].Value;
								CheckBox checkboxSelected = (CheckBox) item.FindControl("checkboxSelected");
								checkboxSelected.Checked = false;

								bool isAlreadyInProfile = false;

								foreach (var profile in user.Profiles)
								{
									foreach (var permission in profile.Permissions)
									{
										if (permission.ID == permissionID)
										{
											checkboxSelected.Checked = true;
											isAlreadyInProfile = true;
											break;
										}
									}
								}

								if (!isAlreadyInProfile)
								{
									foreach (var permission in user.Permissions)
									{
										if (permission.ID == permissionID)
										{
											checkboxSelected.Checked = true;
											break;
										}
									}
								}
							}
						}
					}
					break;

				case "ChangePassword":
					{
						this.Username = user.UserName;

						literalChangePasswordFirstName.Text = user.FirstName;
						literalChangePasswordLastName.Text = user.LastName;
						literalChangePasswordUsername.Text = user.UserName;

						panelChangePassword.Visible = true;
						panelList.Visible = false;
					}
					break;

				case "Delete":
					{
						bool isLogicalDelete = !(bool) UserDAO.CanBeDeleted(userId);

						try
						{
							UserDAO.Delete(user, isLogicalDelete);
						}
						catch (Exception ex)
						{
							Common.Tracer.TraceError("No se pudo borrar el usuario {0}: {1}", user, ex);
							if (!isLogicalDelete)
							{
								Common.Tracer.TraceInfo("Se intentará borrar el usuario en forma lógica");
								UserDAO.Delete(user, true);
							}
						}

						if (user.IsSupervisor)
						{
							foreach (var group in user.AgentGroups)
							{
								var cachedGroup = AgentGroupDAO.GetOneFromCache(group.ID);

								if (cachedGroup != null && cachedGroup.Supervisors.Contains(user))
									cachedGroup.Supervisors.Remove(user);

								DomainModel.Cache.Instance.UpdateItem<AgentGroup>(cachedGroup);
							}
						}
						var entityNameSB = new StringBuilder();
						entityNameSB.Append(user.FullName);
						entityNameSB.Append($" ({user.UserName})");
						DAL.UserLogDAO.Insert(this.LoggedUser
							, SystemEntityTypes.Users
							, SystemActionTypes.Delete
							, new Dictionary<string, string>() { { "FullName", user.FullName } }
							, null
							, user.ID
							, entityNameSB.ToString());

						if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
							Core.System.Instance.EventsService != null)
						{
							var @event = new DomainModel.Events.DomainObjectSyncEvent();
							@event.Action = SystemActionTypes.Delete;
							@event.EntityType = SystemEntityTypes.Users;
							@event.EntityID = user.ID;
							Core.System.Instance.EventsService.PublishEvent(@event);
						}

						Response.Redirect(this.RedirectUrl);
					}
					break;

				case "Disable":
					{
						var oldParameters = user.AsDictionary(true);
						user.Enabled = false;
						UserDAO dao = new UserDAO(user);
						dao.Update();
                        var entityNameSB = new StringBuilder();
                        entityNameSB.Append(user.FullName);
                        entityNameSB.Append($" ({user.UserName})");
						var newParameters = user.AsDictionary(true);
                        DAL.UserLogDAO.Insert(this.LoggedUser
							, SystemEntityTypes.Users
							, SystemActionTypes.Disable
                            , oldParameters.Except(newParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
							, newParameters.Except(oldParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
                            , user.ID
							, entityNameSB.ToString());

						if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
							Core.System.Instance.EventsService != null)
						{
							var @event = new DomainModel.Events.DomainObjectSyncEvent();
							@event.Action = SystemActionTypes.Disable;
							@event.EntityType = SystemEntityTypes.Users;
							@event.EntityID = user.ID;
							Core.System.Instance.EventsService.PublishEvent(@event);
						}

						Response.Redirect(this.RedirectUrl);
					}
					break;

				case "Enable":
					{
                        var oldParameters = user.AsDictionary(true);
                        user.Enabled = true;
						user.TotalLoginInvalid = 0;

						UserDAO dao = new UserDAO(user);
						dao.Update();
                        var entityNameSB = new StringBuilder();
                        entityNameSB.Append(user.FullName);
                        entityNameSB.Append($" ({user.UserName})");
                        var newParameters = user.AsDictionary(true);
                        DAL.UserLogDAO.Insert(this.LoggedUser
							, SystemEntityTypes.Users
                        , SystemActionTypes.Enable
                            , oldParameters.Except(newParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
                            , newParameters.Except(oldParameters).ToDictionary(k => k.Key, value => value.Value?.ToString())
                            , user.ID
							, entityNameSB.ToString());

						if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
							Core.System.Instance.EventsService != null)
						{
							var @event = new DomainModel.Events.DomainObjectSyncEvent();
							@event.Action = SystemActionTypes.Enable;
							@event.EntityType = SystemEntityTypes.Users;
							@event.EntityID = user.ID;
							Core.System.Instance.EventsService.PublishEvent(@event);
						}

						Response.Redirect(this.RedirectUrl);
					}
					break;
				case "Copy":
					{
						LoadCopyData(userId);
					}
					break;

				default:
					break;
			}
		}

		protected void buttonNew_Click(object sender, EventArgs e)
		{
			this.Nuevo = true;
			ToggleToEditMode();
			dropdownlistUserType.SelectedIndex = 0;
			textboxLastName.Text = string.Empty;
			textboxFirstName.Text = string.Empty;
			textboxUsername.Text = string.Empty;
			textboxEmail.Text = string.Empty;
			textboxLDAP.Text = string.Empty;
		}

		protected void buttonCancel_Click(object sender, EventArgs e)
		{
			panelList.Visible = true;
			panelNew.Visible = false;
			ViewState["Script"] = null;
		}

		protected void buttonOK_Click(object sender, EventArgs e)
		{
			if (Page.IsValid)
			{
				User user = null;
				bool enabledUserValue = false;
				Dictionary<string, string> oldParameters = null;
				DomainModel.SystemSettings settings = DomainModel.SystemSettings.Instance;
				if (this.Nuevo)
				{
					user = new User();
					user.LastName = textboxLastName.Text.Trim();
					user.FirstName = textboxFirstName.Text.Trim();
					user.UserName = textboxUsername.Text.Trim();
					user.Email = textboxEmail.Text;



					switch (settings.AuthenticationType)
					{
						case AuthenticationTypes.Domain:
							if (!settings.LDAP.AllowCreateLocalAgents)
							{
								user.AuthenticationType = AuthenticationTypes.Domain;
								user.LDAP = textboxLDAP.Text;
							}
							else
							{
								if (dropdownlistUserType.SelectedIndex == 0)
								{
									user.Password = textboxPassword.Text;
									user.AuthenticationType = AuthenticationTypes.Local;
								}
								else
								{
									user.LDAP = textboxLDAP.Text;
									user.AuthenticationType = AuthenticationTypes.Domain;
								}
							}
							break;
						case AuthenticationTypes.Google:
							if (settings.GoogleAuth.AllowCreateLocalAgents)
							{
								if (dropdownlistUserType.SelectedIndex == 0)
								{
									user.Password = textboxPassword.Text;
									user.AuthenticationType = AuthenticationTypes.Local;
								}
								else
								{
									user.AuthenticationType = AuthenticationTypes.Google;
								}
							}
							else
							{
								user.AuthenticationType = AuthenticationTypes.Google;
							}
							break;
						case AuthenticationTypes.Saml:
							if (settings.SamlAuth.AllowCreateLocalAgents)
							{
								if (dropdownlistUserType.SelectedIndex == 0)
								{
									user.Password = textboxPassword.Text;
									user.AuthenticationType = AuthenticationTypes.Local;
								}
								else
								{
									user.AuthenticationType = AuthenticationTypes.Saml;
								}
							}
							else
							{
								user.AuthenticationType = AuthenticationTypes.Saml;
							}
							break;
						default:
							user.Password = textboxPassword.Text;
							user.AuthenticationType = AuthenticationTypes.Local;
							break;
					}
				}
				else
				{
					user = UserDAO.GetOneFromCache(this.EntityID);
					oldParameters = user.AsDictionary(true);
					user.LastName = textboxLastNameEdit.Text;
					user.FirstName = textboxFirstNameEdit.Text;
					enabledUserValue = user.Enabled;
					if (user.AuthenticationType == AuthenticationTypes.Local)
						user.Email = textboxEmailEdit.Text;
					user.Profiles.Clear();
					user.Permissions.Clear();
				}

				string serviceAvatar = Path.Combine(Server.MapPath("~/Configuration/Uploads"), (string) hiddenUserAvatarFile.Value);
				if (serviceAvatar != null)
				{
					try
					{
						user.Avatar = File.ReadAllBytes(serviceAvatar);
					}
					catch { }

					if (!string.IsNullOrEmpty(hiddenUserAvatarFileContentType.Value))
						user.Parameters[DomainModel.User.AvatarContentTypeParameter] = hiddenUserAvatarFileContentType.Value;
				}

				foreach (GridViewRow item in gridviewProfiles.Rows)
				{
					if (item.RowType == DataControlRowType.DataRow)
					{
						var checkboxSelected = (CheckBox) item.FindControl("checkboxSelected");
						short profileID = (short) gridviewProfiles.DataKeys[item.RowIndex].Value;

						if (!this.Nuevo && profileID == 1 && this.EntityID == 1)
							checkboxSelected.Checked = true;

						var profile = DomainModel.Cache.Instance.GetItem<Profile>(profileID);
						if (checkboxSelected.Checked)
						{
							user.Profiles.Add(profile);

							if (!profile.Users.Contains(user))
								profile.Users.Add(user);
						}
						else
						{
							if (profile.Users.Contains(user))
								profile.Users.Remove(user);
						}
					}
				}

				foreach (GridViewRow item in gridviewPermissions.Rows)
				{
					if (item.RowType == DataControlRowType.DataRow)
					{
						var checkboxSelected = (CheckBox) item.FindControl("checkboxSelected");
						if (checkboxSelected.Checked)
						{
							short permissionID = (short) gridviewPermissions.DataKeys[item.RowIndex].Value;
							if (!user.Profiles.Any(pr => pr.Permissions.Any(p => p.ID == permissionID)))
							{
								user.Permissions.Add(new Permission(permissionID));
							}
						}
					}
				}

				UserDAO dao = new UserDAO(user);
				if (this.Nuevo)
				{
					dao.Insert();
					var newParameters = user.AsDictionary(true);
                    var entityNameSB = new StringBuilder();
                    entityNameSB.Append(user.FullName);
                    entityNameSB.Append($" ({user.UserName})");
                    UserLogDAO.Insert(this.LoggedUser, SystemEntityTypes.Users, SystemActionTypes.Add, null, newParameters, user.ID, entityNameSB.ToString());

					if (this.Nuevo && hiddenActionName.Value == "Copy")
					{
						bool copyAsociatedQueues = bool.TryParse(hiddenCopyAsociatedQueues.Value, out bool resultCopyAsociatedQueues) && resultCopyAsociatedQueues;
						bool copyAgentsGroups = bool.TryParse(hiddenCopyAgentsGroups.Value, out bool resultCopyAgentsGroups) && resultCopyAgentsGroups;
						bool copySettings = bool.TryParse(hiddenCopySettings.Value, out bool resultCopySettings) && resultCopySettings;

						int userId = int.Parse(hiddenActionUserID.Value);
						var sourceUser = UserDAO.GetOneFromCache(userId);

						if (copyAsociatedQueues)
						{
							CopyQueues(user, sourceUser);
							dao.UpdateQueues();
						}
						if (copyAgentsGroups)
						{
							CopyGroups(user, sourceUser);
							dao.UpdateGroups();
						}
						if (copySettings)
						{
							LoadSettings(user, sourceUser);
							dao.Update(false, false);
						}
					}

					if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
						Core.System.Instance.EventsService != null)
					{
						var @event = new DomainModel.Events.DomainObjectSyncEvent();
						@event.Action = SystemActionTypes.Add;
						@event.EntityType = SystemEntityTypes.Users;
						@event.EntityID = user.ID;
						Core.System.Instance.EventsService.PublishEvent(@event);
					}
				}
				else
				{
					dao.Update();
					var newParameters = user.AsDictionary(true);
                    var entityNameSB = new StringBuilder();
                    entityNameSB.Append(oldParameters.ContainsKey("FullName") ? oldParameters["FullName"] : user.FullName);
					var username = oldParameters.ContainsKey("UserName") ? oldParameters["UserName"] : user.UserName;
                    entityNameSB.Append($" ({username})");
                    UserLogDAO.Insert(this.LoggedUser
						, SystemEntityTypes.Users
						, SystemActionTypes.Edit
						, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, user.ID
						, entityNameSB.ToString());

					if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
						Core.System.Instance.EventsService != null)
					{
						var @event = new DomainModel.Events.DomainObjectSyncEvent();
						@event.Action = SystemActionTypes.Edit;
						@event.EntityType = SystemEntityTypes.Users;
						@event.EntityID = user.ID;
						Core.System.Instance.EventsService.PublishEvent(@event);
					}
				}

				Response.Redirect(this.RedirectUrl);
			}
		}

		protected void buttonChangePasswordCancel_Click(object sender, EventArgs e)
		{
			messageChangePasswordDialogError.Style.Add("display", "none");
			panelChangePassword.Visible = false;
			panelList.Visible = true;
		}

		protected void buttonChangePasswordOK_Click(object sender, EventArgs e)
		{
			string username = (string) ViewState["Username"];
			var result = Core.System.Instance.SystemAuthentication.RefreshPassword(UserDAO.GetOneFromCache(this.EntityID), textboxChangePassword.Text, this.LoggedUser);
			switch (result)
			{
				case ChangePasswordResults.Success:
					panelChangePassword.Visible = false;
					panelList.Visible = true;
					messageChangePasswordDialogError.Style.Add("display", "none");
					return;

				case ChangePasswordResults.FailedOnlyLocalUsers:
					messageChangePasswordDialogError.Style.Remove("display");
					literalMensajeError.Text = "Solamente se puede cambiar contraseñas de usuarios locales.";
					return;

				case ChangePasswordResults.FailedPasswordNotStrongEnough:
					messageChangePasswordDialogError.Style.Remove("display");
					literalMensajeError.Text = "La contraseña no cumple con el nivel mínimo de seguridad.";
					return;

				case ChangePasswordResults.FailedPasswordRepeat:
					messageChangePasswordDialogError.Style.Remove("display");
					literalMensajeError.Text = "La contraseña es igual a la anteriores.";
					return;

				case ChangePasswordResults.FailedPasswordRegex:
					messageChangePasswordDialogError.Style.Remove("display");
					literalMensajeError.Text = "La contraseña no cumple con el nivel avanzado de seguridad. " + SystemSettings.Instance.UserPasswordMessageRegex;

					return;
			}
		}

		#region Exports

		protected void buttonExport_Click(object sender, EventArgs e)
		{
			Response.Clear();
			Response.Buffer = true;
			Response.Charset = string.Empty;
			Response.BufferOutput = false;

			using (MemoryStream ms = new MemoryStream())
			{
				IEnumerable<User> users = DomainModel.Cache.Instance.GetList<User>();
				users = users.Where(u => !u.Deleted).OrderBy(u => u.LastName);
				if (this.LoggedUser.ID != 1)
					users = users.Where(u => u.ID != 1);
				DomainModel.Reports.Export.UsersExport export;
				export = (DomainModel.Reports.Export.UsersExport) DomainModel.Reports.Export.ReportExport.Create(DomainModel.Reports.ReportTypes.Users);
				export.Format = (DomainModel.Reports.Export.ExportFormats) byte.Parse(hiddenExportFormat.Value);
				export.Email = this.LoggedUser.Email;
				export.Owner = this.LoggedUser;
				export.OwnerTimeZone = this.LoggedUser.GetTimeZoneToUse();
				export.Language = DomainModel.SystemSettings.Instance.DefaultLanguage;
				if (this.LoggedUser.Settings != null && this.LoggedUser.Settings.ContainsKey(DomainModel.User.Locale))
					export.Language = this.LoggedUser.Settings[DomainModel.User.Locale];
				export.Generate(users, ms);

				if (Response.IsClientConnected)
				{
					var filename = DomainModel.Localizations.Instance.GetLocalizedString("administration-users-list-title", export.Language, "Listado de usuarios");
					switch (export.Format)
					{
						case DomainModel.Reports.Export.ExportFormats.Excel:
							Response.Headers.Add("content-disposition", $"attachment;filename={filename}.xlsx");
							Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
							break;
						case DomainModel.Reports.Export.ExportFormats.CSV:
							Response.Headers.Add("content-disposition", $"attachment;filename={filename}.csv");
							Response.ContentType = "text/csv";
							break;
						default:
							Response.Headers.Add("content-disposition", $"attachment;filename={filename}.txt");
							Response.ContentType = "text/plain";
							break;
					}

					ms.Position = 0;
					Response.AddHeader("content-length", ms.Length.ToString());

					using (BinaryReader br = new BinaryReader(ms))
					{
						byte[] buffer = new byte[1024];
						int read = br.Read(buffer, 0, buffer.Length);
						while (read > 0 && Response.IsClientConnected)
						{
							Response.OutputStream.Write(buffer, 0, read);
							Response.Flush();

							read = br.Read(buffer, 0, buffer.Length);
						}
					}
				}

				Response.Flush();
				Response.End();
			}
		}

		#endregion

		#endregion

		#region GridView Events

		protected void gridviewProfiles_RowDataBound(object sender, GridViewRowEventArgs e)
		{
			if (e.Row.RowType == DataControlRowType.DataRow)
			{
				Profile profile = (Profile) e.Row.DataItem;
				CheckBox checkboxSelected = (CheckBox) e.Row.FindControl("checkboxSelected");
				//checkboxSelected.InputAttributes.Add("rel", "profiles");
				//checkboxSelected.InputAttributes.Add("rowid", profile.ID.ToString());
				checkboxSelected.Attributes.Add("rel", "profiles");
				checkboxSelected.Attributes.Add("rowid", profile.ID.ToString());

				if (!this.Nuevo && profile.ID == 1 && this.EntityID == 1)
				{
					checkboxSelected.Enabled = false;
					checkboxSelected.Checked = true;
					checkboxSelected.InputAttributes.CssStyle[HtmlTextWriterStyle.Display] = "none";
				}
				else
				{
					checkboxSelected.Enabled = true;
				}
			}
		}

		protected void gridviewPermissions_RowDataBound(object sender, GridViewRowEventArgs e)
		{
			if (e.Row.RowType == DataControlRowType.DataRow)
			{
				Permission permission = (Permission) e.Row.DataItem;
				CheckBox checkboxSelected = (CheckBox) e.Row.FindControl("checkboxSelected");
				//checkboxSelected.InputAttributes.Add("rowid", permission.ID.ToString());
				checkboxSelected.Attributes.Add("rowid", permission.ID.ToString());

				var labelPermissionName = (Label) e.Row.FindControl("labelPermissionName");
				labelPermissionName.Text = permission.Name;
				labelPermissionName.Attributes["data-i18n"] = $"Permissions.{permission.PermissionType}";

				var labelPermissionDescription = (Label) e.Row.FindControl("labelPermissionDescription");
				labelPermissionDescription.Text = permission.Description;
				labelPermissionDescription.Attributes["data-i18n"] = $"Permissions.{permission.PermissionType}-desc";
			}
		}

		protected void gridviewProfiles_PreRender(object sender, EventArgs e)
		{
			foreach (GridViewRow row in gridviewProfiles.Rows)
			{
				if (row.RowType == DataControlRowType.DataRow)
				{
					CheckBox checkboxSelected = (CheckBox) row.FindControl("checkboxSelected");
					checkboxSelected.InputAttributes.Add("rel", "profiles");
					checkboxSelected.InputAttributes.Add("rowid", checkboxSelected.Attributes["rowid"]);
				}
			}
		}

		protected void gridviewPermissions_PreRender(object sender, EventArgs e)
		{
			foreach (GridViewRow row in gridviewPermissions.Rows)
			{
				if (row.RowType == DataControlRowType.DataRow)
				{
					CheckBox checkboxSelected = (CheckBox) row.FindControl("checkboxSelected");
					checkboxSelected.InputAttributes.Add("rowid", checkboxSelected.Attributes["rowid"]);
				}
			}
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object RetrieveUsers()
		{
			try
			{
				DomainModel.User user = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (user == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				IEnumerable<User> users = DomainModel.Cache.Instance.GetList<User>();
				users = users.Where(u => !u.Deleted).OrderBy(a => a.FirstName).OrderBy(u => u.LastName);
				if (user.ID != 1)
					users = users.Where(u => u.ID != 1);

				return new
				{
					Success = true,
					Users = BuildUsers(users)
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object SearchDomainUsers(string filter)
		{
			try
			{
				DomainModel.Settings.LDAPSettings settings = DomainModel.SystemSettings.Instance.LDAP;
				List<ADUser> users;

				if (settings.UseConfigurationParams)
				{
					string firstName = settings.ConfigurationParamFirstName;
					string lastName = settings.ConfigurationParamLastName;
					string userName = settings.ConfigurationParamUserName;
					string email = settings.ConfigurationParamEmail;
					string ldap = settings.ConfigurationParamLDAP;
					string[] propsToRetrieve = new string[] {
						firstName,
						lastName,
						userName,
						email,
						ldap
					};

					users = ADManager.SearchUsers(settings.Server, settings.Port, settings.User, settings.Password, settings.Secure, settings.RootDN, settings.UserSearchFilter, filter, propsToRetrieve);
				}
				else
				{
					users = ADManager.SearchUsers(settings.Server, settings.Port, settings.User, settings.Password, settings.Secure, settings.RootDN, settings.UserSearchFilter, filter, 50);
				}

				return new
				{
					Success = true,
					Info = users,
					ConfigurationParams = new
					{
						Use = settings.UseConfigurationParams,
						FirstName = settings.ConfigurationParamFirstName,
						LastName = settings.ConfigurationParamLastName,
						UserName = settings.ConfigurationParamUserName,
						Email = settings.ConfigurationParamEmail,
						LDAP = settings.ConfigurationParamLDAP
					}
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateUserName(string username)
		{
			try
			{
				return new
				{
					Success = true,
					IsValid = !PersonDAO.Exists(username)
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateEmail(string email)
		{
			try
			{
				var user = DomainModel.Cache.Instance.FindItem<DomainModel.User>(u => u.Email.Equals(email, StringComparison.InvariantCultureIgnoreCase));
				var agent = DomainModel.Cache.Instance.FindItem<DomainModel.Agent>(u => u.Email.Equals(email, StringComparison.InvariantCultureIgnoreCase));

				return new
				{
					Success = true,
					IsValid = user == null && agent == null
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object CanBeDeleted(int userId)
		{
			var user = UserDAO.GetOneFromCache(userId);
			bool CanBeDeleted = !(Core.System.Instance.UsersService.Users.Contains(user)); //Si el usuario esta logeado no puede ser borrado

			try
			{
				return new
				{
					Success = true,
					CanBeDeleted = CanBeDeleted
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object SaveQueues(int userId, int[] queueIds)
		{
			try
			{
				DomainModel.User supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var editedUser = UserDAO.GetOneFromCache(userId);

				IEnumerable<DomainModel.Queue> allQueues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>();

				if (editedUser == null)
					throw new ArgumentException("El código de usuario es incorrecto", nameof(userId));

				var currentQueues = editedUser.Queues.Select(q => q.ID).ToArray();

				var update = false;
				var oldParameters = editedUser.AsDictionary(true);

				if (queueIds != null)
				{
					var newQueueIds = queueIds.Except(currentQueues);
					foreach (var queueId in newQueueIds)
					{
						var queue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueId);
						if (queue != null)
						{
							if (!editedUser.Queues.Contains(queue))
							{
								editedUser.Queues.Add(queue);

								if (editedUser.IsSupervisor)
								{
									if (!queue.Supervisors.Contains(editedUser))
										queue.Supervisors.Add(editedUser);
								}
								else
								{
									if (!queue.Users.Contains(editedUser))
										queue.Users.Add(editedUser);
								}

								update = true;
							}
						}
					}
				}

				IEnumerable<int> removedQueueIds = currentQueues;
				if (queueIds != null)
					removedQueueIds = currentQueues.Except(queueIds);

				foreach (var queueId in removedQueueIds)
				{
					var queue = DomainModel.Cache.Instance.GetItem<DomainModel.Queue>(queueId);
					if (queue != null)
					{
						if (editedUser.Queues.Contains(queue))
						{
							editedUser.Queues.Remove(queue);

							if (editedUser.IsSupervisor)
							{
								if (queue.Supervisors.Contains(editedUser))
									queue.Supervisors.Remove(editedUser);
							}
							else
							{
								if (queue.Users.Contains(editedUser))
									queue.Users.Remove(editedUser);
							}

							update = true;
						}
					}
				}

				if (update)
				{
					new DAL.UserDAO(editedUser).UpdateQueues();
					var newParameters = editedUser.AsDictionary(true);
                    var entityNameSB = new StringBuilder();
                    entityNameSB.Append(oldParameters.ContainsKey("FullName") ? oldParameters["FullName"] : editedUser.FullName);
                    var username = oldParameters.ContainsKey("UserName") ? oldParameters["UserName"] : editedUser.UserName;
                    entityNameSB.Append($" ({username})");
                    DAL.UserLogDAO.Insert(supervisor
						, SystemEntityTypes.Users
						, SystemActionTypes.Edit
						, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
						, editedUser.ID
						, entityNameSB.ToString());

					if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
						Core.System.Instance.EventsService != null)
					{
						var @event = new DomainModel.Events.DomainObjectSyncEvent();
						@event.Action = SystemActionTypes.Edit;
						@event.EntityType = SystemEntityTypes.Users;
						@event.EntityID = editedUser.ID;
						Core.System.Instance.EventsService.PublishEvent(@event);
					}
				}

				return new
				{
					Success = true
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object SaveServices(int userId, int[] serviceIds)
		{
			try
			{
				DomainModel.User supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var editedUser = UserDAO.GetOneFromCache(userId);

				if (editedUser == null)
					throw new ArgumentException("El código de usuario es incorrecto", nameof(userId));

				IEnumerable<DomainModel.Service> allServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();

				var currentServices = editedUser.Services;

				var oldParameters = editedUser.AsDictionary(true);

				editedUser.Services = serviceIds;

				DAL.UserDAO.UpdateParameters(editedUser);
				var newParameters = editedUser.AsDictionary(true);
                var entityNameSB = new StringBuilder();
                entityNameSB.Append(oldParameters.ContainsKey("FullName") ? oldParameters["FullName"] : editedUser.FullName);
                var username = oldParameters.ContainsKey("UserName") ? oldParameters["UserName"] : editedUser.UserName;
                entityNameSB.Append($" ({username})");
                DAL.UserLogDAO.Insert(supervisor
					, SystemEntityTypes.Users
					, SystemActionTypes.Edit
					, oldParameters.Except(newParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
					, newParameters.Except(oldParameters).ToDictionary(key => key.Key, value => value.Value?.ToString())
					, editedUser.ID
					, entityNameSB.ToString());

				if (Licensing.LicenseManager.Instance.License.Configuration.UseExternalServiceForIncomingMessages &&
					Core.System.Instance.EventsService != null)
				{
					var @event = new DomainModel.Events.DomainObjectSyncEvent();
					@event.Action = SystemActionTypes.Edit;
					@event.EntityType = SystemEntityTypes.Users;
					@event.EntityID = editedUser.ID;
					Core.System.Instance.EventsService.PublishEvent(@event);
				}

				return new
				{
					Success = true
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RemoveTwoFactor(int userId)
		{
			try
			{
				DomainModel.User supervisor = (DomainModel.User) HttpContext.Current.Session["Usuario"];
				if (supervisor == null)
					throw new UserNotLoggedException("No hay ningún usuario logueado");

				var editedUser = UserDAO.GetOneFromCache(userId);
				if (editedUser == null)
					throw new ArgumentException("No se encontró el usuario");

				if (editedUser.ID == 1 && supervisor.ID != 1)
					throw new Exception("Solo el usuario administrador puede editarse a si mismo");

				if (!editedUser.Parameters.ContainsKey(DomainModel.Person.TwoFactorEnabledParameter) ||
					!bool.Parse(editedUser.Parameters[DomainModel.Person.TwoFactorEnabledParameter]))
					throw new Exception("El usuario no tiene doble factor habilitado");

				editedUser.Parameters[DomainModel.Person.TwoFactorEnabledParameter] = false.ToString();
				DAL.UserDAO.UpdateParameters(editedUser);

				if (editedUser.ID == supervisor.ID &&
					!ReferenceEquals(editedUser, supervisor))
				{
					supervisor.Parameters[DomainModel.Person.TwoFactorEnabledParameter] = false.ToString();
				}

				return new
				{
					Success = true
				};
			}
			catch (UserNotLoggedException ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						NotLogged = true
					}
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		#endregion
	}
}