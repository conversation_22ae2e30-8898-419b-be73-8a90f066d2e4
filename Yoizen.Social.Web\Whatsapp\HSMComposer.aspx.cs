﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Yoizen.Common;
using Yoizen.Social.DomainModel;
using Yoizen.Social.DomainModel.Whatsapp;

namespace Yoizen.Social.Web.Whatsapp
{
	public partial class HSMComposer : LoginRequiredBasePage
	{
		#region Propiedades

		protected override string RedirectUrl { get { return "~/Whatsapp/HSMComposer.aspx"; } }

		protected override string PageDescription { get { return "Envío de mensaje HSM de Whatsapp"; } }

		protected override string PageDescriptionLocalizationKey { get { return "whatsapp-hsmcomposer-title"; } }

		#endregion

		protected void Page_Load(object sender, EventArgs e)
		{
			if (!ValidateUser())
				return;

			if (!IsPostBack)
			{
				IEnumerable<DomainModel.Service> services = DomainModel.Cache.Instance.GetList<DomainModel.Service>();

				services = services.Where(s => s != null && 
					s.Enabled && 
					s.Type == ServiceTypes.WhatsApp && 
					s.Settings != null && 
					!((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).IntegrationType.Equals("1") &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).AllowToSendHSM &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).HSMTemplates != null &&
					((DomainModel.ServiceSettings.WhatsappSettings) s.Settings).HSMTemplates.Where(t => t.AvaiableForSupervisors).Any());
				this.RegisterJsonVariable("services", BuildServices(services));

				var countries = (List<Country>) Application["Countries"];
				if (countries == null)
				{
					countries = DAL.CountryDAO.GetAll();

					Application.Lock();
					Application.Add("Countries", countries);
					Application.UnLock();
				}

				this.RegisterJsonVariable("countries", countries);
				this.RegisterJsonVariable("defaultCountry", DomainModel.SystemSettings.Instance.Whatsapp.DefaultInternationCode);

				this.RegisterEnums(new Type[] {
					typeof(DomainModel.Whatsapp.HSMTemplateButtonsTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateFooterTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes)
				});

				checkboxDoNotCall.Checked = true;
			}
		}

		#region Private Methods

		private bool ValidateUser()
		{
			if (!this.LoggedUser.HasPermission(DomainModel.Permissions.WhatsappSection))
			{
				messageError.Visible = true;
				messageError.Text = "El usuario no tiene permisos para acceder a esta sección";
				messageError.LocalizationKey = "globals-user-doesnt-have-permissions";
				panelContent.Visible = false;
				return false;
			}

			if (!Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound)
			{
				messageError.Visible = true;
				messageError.Text = "No hay licencia para acceder a esta sección";
				messageError.LocalizationKey = "globals-no_license";
				panelContent.Visible = false;
				return false;
			}

			return true;
		}

		private object BuildServices(IEnumerable<Service> services)
		{
			if (services == null || !services.Any())
				return null;

			return services.Select(s => BuildService(s));
		}

		private object BuildService(Service service)
		{
			if (service == null)
				return null;

			Core.System.Instance.Logic.EnsureServiceInstance(service);

			var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			var configuration = service.ServiceConfiguration as SocialServices.WhatsApp.WhatsAppServiceConfiguration;

			return new
			{
				ID = service.ID,
				Name = service.Name,
				PhoneNumber = configuration.FullPhoneNumber,
				IntegrationType = settings.IntegrationType,
				HSMTemplates = settings.HSMTemplates?.Where(t => t.AvaiableForSupervisors).OrderBy(t => t.Description)
			};
		}

		#endregion

		#region Web Methods
		[System.Web.Services.WebMethod]
		public static object GetFlowParametersData(HSMTemplateFlowParameters flowParameter, int serviceId)
		{
			try
			{
				var service = DomainModel.Cache.Instance.GetItem<DomainModel.Service>(serviceId);

				Core.System.Instance.Logic.EnsureServiceInstance(service);
				var serviceSettings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;

				var screenData = serviceSettings.FindFlowScreensData(flowParameter.FlowID, flowParameter.NavigateScreen);

				if (screenData != null && screenData.Length > 0)
				{
					return new
					{
						Success = true,
						Parameters = screenData[0].Data.ToString()
					};
				}

				return new
				{
					Success = true,
				};

			}
			catch (Exception ex)
			{
				Tracer.TraceError("Ocurrio un error al obtener los parametros del flow: {0}", ex);
				return new
				{
					Success = false,
				};
			}
		}
		#endregion
	}
}