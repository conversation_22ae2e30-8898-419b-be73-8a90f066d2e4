﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using Yoizen.Social.DomainModel;
using System.Data.SqlClient;

namespace Yoizen.Social.DAL
{
	public class SystemSettingsDAO
	{
		#region Static Methods

		public static void GetAll()
		{
			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				using (var cmd = conn.CreateCommand())
				{
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.CommandText = "SystemSettings_GetAll";

					using (DbDataReader reader = cmd.ExecuteReader())
					{
						Dictionary<string, object> settings = new Dictionary<string, object>();
						while (reader.Read())
						{
							object value = Convert.ChangeType(reader["Value"], Type.GetType((string) reader["Type"]));
							string key = (string) reader["Key"];

							if (!key.Equals("SocialUserBusinessInfoType"))
							{
								settings.Add(key, value);
							}
						}

						SystemSettings.Instance.Load(settings);
					}
				}
			}

			if (Licensing.LicenseManager.Instance.License.Configuration.InTheCloud)
			{
				SystemSettings.Instance.Service.EmailLicenseExpired.Emails = "<EMAIL>";
				SystemSettings.Instance.EmailDatabaseProblems.Emails = "<EMAIL>";
				SystemSettings.Instance.EmailOutOfMemory.Emails = "<EMAIL>";
				SystemSettings.Instance.OutOfDiskSpaceForAttachments.Emails = "<EMAIL>";
				SystemSettings.Instance.AttachmentsMinimumFreeSpace = 10;
			}

			if (!string.IsNullOrEmpty(Licensing.LicenseManager.Instance.License.Configuration.AlternativeServiceBusConnectionString))
				SystemSettings.Instance.Subscription.NamespaceConnectionString = Licensing.LicenseManager.Instance.License.Configuration.AlternativeServiceBusConnectionString;
		}

		public static void Update()
		{
			Update(null);
		}

		public static void Update(params string[] keys)
		{
			DbTransaction transaction = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				transaction = conn.BeginTransaction();

				try
				{
					Dictionary<string, object> data = SystemSettings.Instance.Retrieve();
					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.Transaction = transaction;
						cmd.CommandText = "SystemSettings_Update";

						var parameterKey = cmd.AddParameter("Key", DbType.String, null);
						var parameterValue = cmd.AddParameter("Value", DbType.String, null);
						var parameterType = cmd.AddParameter("Type", DbType.String, null);

						foreach (var item in data)
						{
							if ((keys == null || keys.Any(k => item.Key.StartsWith(k))) && item.Value != null)
							{
								parameterKey.Value = item.Key;
								if (item.Value is DateTime)
									parameterValue.Value = ((DateTime) item.Value).ToString("o");
								else
									parameterValue.Value = item.Value;
								parameterType.Value = item.Value.GetType().ToString();
								cmd.ExecuteNonQuery();
							}
						}
					}

					transaction.Commit();
				}
				catch
				{
					if (transaction != null)
						transaction.Rollback();

					throw;
				}
			}
		}

		public static void Delete(params string[] keys)
		{
			DbTransaction transaction = null;

			using (var conn = DbManager.CreateConnection())
			{
				conn.Open();

				transaction = conn.BeginTransaction();

				try
				{
					using (var cmd = conn.CreateCommand())
					{
						cmd.CommandType = CommandType.StoredProcedure;
						cmd.Transaction = transaction;
						cmd.CommandText = "SystemSettings_Delete";

						var parameterKey = cmd.AddParameter("Key", DbType.String, null);

						foreach (string keyToDelete in keys)
						{
							if (keyToDelete != null && keyToDelete != string.Empty)
							{
								parameterKey.Value = keyToDelete;
								cmd.ExecuteNonQuery();
							}
						}
					}

					transaction.Commit();
				}
				catch
				{
					if (transaction != null)
						transaction.Rollback();

					throw;
				}
			}
		}
		#endregion
	}
}
