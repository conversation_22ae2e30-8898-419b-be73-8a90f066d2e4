﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yoizen.Social.DomainModel.Whatsapp
{
	/// <summary>
	/// Representa un botón dentro de los HSM
	/// </summary>
	public class HSMTemplateButton
	{
		#region Properties

		/// <summary>
		/// Devuelve o establece el tipo de botón para cuando <see cref="HSMTemplateSendDefinition.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>
		/// </summary>
		public HSMTemplateCallToActionButtonTypes? CallToActionButtonType { get; set; }

		/// <summary>
		/// Devuelve o establece el tipo de botón para cuando <see cref="HSMTemplateSendDefinition.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.AuthCode"/>
		/// </summary>
		public HSMTemplateAuthCodeButtonTypes? AuthCodeButtonType { get; set; }

		/// <summary>
		/// Devuelve o establece el tipo de botón de url para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/> y para cuando <see cref="CallToActionButtonType"/> sea
		/// <see cref="HSMTemplateCallToActionButtonTypes.Url"/>
		/// </summary>
		public HSMTemplateCallToActionUrlButtonTypes? UrlButtonType { get; set; }

		/// <summary>
		/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
		/// <see cref="HSMTemplateCallToActionButtonTypes.Url"/> y para cuando <see cref="UrlButtonType"/> sea
		/// <see cref="HSMTemplateCallToActionUrlButtonTypes.Dynamic"/>
		/// </summary>
		public HSMTemplateParameter UrlParameter { get; set; }

		/// <summary>
		/// Devuelve o establece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.QuickReply"/>
		/// </summary>
		public HSMTemplateParameter QuickReplyParameter { get; set; }

		/// <summary>
		/// Devuelve o esstablece el parámetro que se utiliza en la URL para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
		/// <see cref="HSMTemplateCallToActionButtonTypes.OfferCode"/> 
		/// </summary>
		public HSMTemplateParameter OfferCodeParameter { get; set; }

		/// <summary>
		/// Devuelve o esstablece el parámetro que se utiliza en el codigo de autenticacion para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.AuthCode"/>, para cuando <see cref="AuthCodeButtonType"/> sea
		/// <see cref="HSMTemplateAuthCodeButtonTypes.AuthCode"/> 
		/// </summary>
		public HSMTemplateParameter AuthCodeParameter { get; set; }

		/// <summary>
		/// /// Devuelve o esstablece los parámetros que se utiliza para cuando <see cref="HSMTemplate.ButtonsType"/> del template
		/// sea <see cref="HSMTemplateButtonsTypes.CallToAction"/>, para cuando <see cref="CallToActionButtonType"/> sea
		/// <see cref="HSMTemplateCallToActionButtonTypes.Flow"/> 
		/// </summary>
		public HSMTemplateFlowParameters FlowParameter {  get; set; }

		#endregion
	}
}
