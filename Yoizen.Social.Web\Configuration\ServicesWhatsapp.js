﻿/// <reference path="../Scripts/jquery-3.5.1.js" />

/// <reference path="../Scripts/Master.js" />
/// <reference path="ServicesCommon.js" />

var $dropdownlistWhatsAppCountries;
var $textboxWhatsAppSelectedPhoneCode;
var $textboxWhatsAppPhoneNumber;
var $divWhatsappServiceMedia;
var $checkboxWhatsappAllowToSendMedia;

var $checkboxUseFacebookCatalog;
var $divFacebookCatalogsSelectBusiness;
var $divFacebookCatalogsSelectCatalog;
var $divFacebookCatalogs;
var $hiddenFacebookCatalogAccessToken;
var $textboxFacebookCatalogBusiness;
var $textboxFacebookCatalogID;
var $hiddenFacebookCatalogCatalog;
var $divFacebookBusiness;
var $messageFacebookNoBusinesses;
var $divFacebookBusinessCatalogs;
var $messageFacebookNoBusinessCatalogs;
var $divFacebookRedirectUri;
var $inputFacebookUrl;
var $buttonEnterFacebookUrl;
var $buttonChangeFacebookBusiness;
var $buttonFacebookBusinessCatalogAccept;
var $messageFacebookCouldntValidateAccessToken;
var $divFacebookUser;
var $divFacebookLoginButton;

var $messageVoiceCallsNotAvailable;
var $divVoiceCallsAvailable;
var $checkboxVoiceCallsEnabled;
var $divVoiceCallsEnabled;
var $divVoiceCallInteractiveMessageInvite;
var $divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned;
var $divVoiceCallInteractiveMessageRejectAgentNotAvailable;
var $divVoiceCallInteractiveMessageRejectAgentWithAnotherCall;
var $divVoiceCallInteractiveMessageRejectCaseWithCurrentCall;
var $divVoiceCallInteractiveMessageRejectCaseWithoutInvite;

var currentBusiness = null;
var currentCatalog = null;

var $dropdownlistIntegrationType;
var $divIntegrationType2;
var $divIntegrationType3;
var $divIntegrationType3ReplyEndpoint;
var $divIntegrationType3VoiceCallsEndpoint;
var $dropdownlistIntegrationType3PullType;
var $trIntegrationType3PullTypeSpecificGet;
var $trIntegrationType3PullTypeSpecificPost;
var $divIntegrationType3GetNewsEndpoint;
var $divIntegrationType3PostNewsProcessedEndpoint;
var $dropdownlistIntegrationType3NotifyClosedCases;
var $trIntegrationType3NotifyClosedCasesTrue;
var $divIntegrationType3CloseCaseEndpoint;
var $dropdownlistIntegrationType3PayloadType;
var $divIntegrationType3PayloadTypeObject;
var $hiddenIntegrationType3PayloadTypeObject;
var $divIntegrationType3PayloadTypeObjectContainer;
var $divIntegrationType3Derivations;
var $hiddenIntegrationType3Derivations;
var $divIntegrationType3DerivationsContainer;
var $tableIntegrationType3Derivations;
var $tableIntegrationType3PayloadTypeObjectProps;
var $checkboxIntegrationType3UseWhatsappFormat;
var $trIntegrationType3UseWhatsappFormatIncludeFrom;
var $divIntegrationType4;
var $divIntegrationType5;
var $divIntegrationType6;
var $divIntegrationType7;
var $divIntegrationType8;
var $divIntegrationType9;
var $divIntegrationType10;
var $divCapiService;
var $textboxIntegrationType6ClientID;
var $textboxIntegrationType6ClientSecret;
var $textboxIntegrationType6UrlBase;
var $textboxIntegrationType6ChannelID;
var $textboxIntegrationType6FromID;
var $textboxIntegrationType6FromName;
var $checkboxIntegrationType6SendEndOfConversation;
var $trIntegrationType6EndOfConversation;
var $textboxIntegrationType6EndOfConversation;
var $messageIntegrationType6EndOfConversation;
var $dropdownlistIntegrationType6PayloadType;
var $divIntegrationType6PayloadTypeObject;
var $hiddenIntegrationType6PayloadTypeObject;
var $divIntegrationType6PayloadTypeObjectContainer;
var $tableIntegrationType6PayloadTypeObjectProps;
var $checkboxIntegrationType6EnableSurveys;
var $divIntegrationType6SurveyConfiguration;
var $dropdownlistIntegrationType6Survey;
var $messageIntegrationType6SurveyDisabled;
var $textboxIntegrationType7User;
var $textboxIntegrationType7Password;
var $textboxIntegrationType7BaseUrl;
var $textboxIntegrationType7MediaID;
var $textboxIntegrationType8AppName;
var $textboxIntegrationType9AccountSid;
var $textboxIntegrationType9AuthToken;
var $checkboxIntegrationType11TestingAccount;
var $trIntegrationType11TestingMapping;
var $textboxIntegrationType11TestingMapping;

var $trSubscribeToPushWithoutYFlow;
var $checkboxSubscribeToPushWithoutYFlow;

var $dropdownlistUseYFlow;
var $liTabAdvancedConfigurationYFlow;
var $divAdvancedConfigurationYFlow;
var $hiddenFlow;
var $selectFlowToUse;
var $anchorFlowsReload;
var $spanFlowID;
var $spanFlowName;
var $spanFlowVersion;
var $hiddenFlowContingency;
var $selectFlowContingencyToUse;
var $anchorFlowsContingencyReload;
var $spanFlowContingencyID;
var $spanFlowContingencyName;
var $spanFlowContingencyVersion;
var $hiddenFlowQueueTransfersByKey;
var $tableFlowQueueTransfersByKey;
var $anchorFlowQueueTransfersByKeyAdd;
var $listboxFlowShareEnqueuedMessagesFromQueues;
var $listboxFlowShareConnectedAgentsFromQueues;
var $listboxFlowShareWithServices;

var $liTabBehaviour;
var $divWhatsappBehaviour;
var $checkboxAutoReplyBeforeMaxTimeToAnswer;
var $textboxAutoReplyBeforeMaxTimeToAnswerText;
var $messageAutoReplyBeforeMaxTimeToAnswerText;
var $textboxAutoReplyBeforeMaxTimeToAnswerMinutes;
var $checkboxAutoReplyBeforeCloseCase;
var $textboxAutoReplyBeforeCloseCaseText;
var $messageAutoReplyBeforeCaseIsClosedText;
var $textboxAutoReplyBeforeCloseCaseMinutes;
var $dropdownlistAllowToSendHSM;
var $dropdownlistAllowToSendFlows;
var $trAllowAgentsToSendHSM;
var $divHSMTemplates;
var $divFlows;
var $messageHSMTemplatesEmpty;
var $tableHSMTemplatesv2;
var $tbodyHSMTemplatesv2;
var $hiddenHSMTemplates;
var $buttonImportHSMs;
var $buttonDeleteHSMs;
var $buttonImportCloudApiHSMs;
var $buttonImportInfobipHSMs;

var $hiddenFlows;
var $messageFlowsEmpty;
var $tableFlows;

var $textboxWhatsappInactivityDetectedEmailSubject;
var $textboxWhatsappInactivityDetectedEmailTemplate;
var $hiddenInactivityDetectedConnection;
var $listboxInactivityDetectedConnection;

var $divHSM;
var $inputHSMDescription;
var $selectHSMLanguage;
var $inputHSMNamespace;
var $inputHSMElementName;
var $selectHSMAllowedUses;
var $selectAllowToConfigureSendHSMIfCaseOpen;
var $selectHSMHeaderType;
var $divHSMHeaderTypeText;
var $inputHSMHeaderTypeText;
var $divHSMHeaderTypeTextParameterName;
var $inputHSMHeaderTypeTextParameterName;
var $divHSMHeaderTypeTextParameterDescription;
var $inputHSMHeaderTypeTextParameterDescription;
var $divHSMHeaderTypeMedia;
var $selectHSMHeaderTypeMedia;
var $inputHSMHeaderTypeMediaUrl;
var $divHSMHeaderTypeLocation;
var $inputHSMHeaderTypeLatitude;
var $inputHSMHeaderTypeLongitude;
var $textareaHSMTemplate;
var $divHSMParametersContainer;
var $selectHSMFooterType;
var $divHSMFooterTypeText;
var $inputHSMFooterTypeText;
var $selectHSMButtonsType;
var $divHSMButtonsTypeCallToAction;
var $divHSMButtonsTypeCallToActionContainer;
var $divHSMButtonsTypeAuthCode;
var $divHSMButtonsTypeAuthCodeContainer;
var $divHSMButtonsTypeQuickReply;
var $divHSMButtonsTypeQuickReplyContainer;
var $divHSMError;
var $textboxMaxElapsedMinutesToCloseHsmCases;
var maxCallToActionButtons = 4;
var maxAuthCodeButtons = 4;
var maxUrlButton = 2;
var maxCallButton = 1;
var maxAuthCodeButton = 1
var maxOfferButton = 1;
var maxTotalButtons = 10;
var maxQuickReplyButtons = 10;
var $panelIntegrationType3VoiceCallsEndpoint;

var $checkboxActAsChat;
var $trAllowToReplyToSpecificMessage;
var $trDelayAfterMultimedia;
var $trPreviewUrlForTextMessages;

var $listboxTagsForHsmClose;
var $hiddenTagCloseHsmCase;

var drake;

var datatableTemplates = null;
var templates = [];

var datatableFlows = null;
var metaFlows = [];

$(function () {
	InitializeWhatsApp();

	LoadCompositedElements();
});

function InitializeWhatsApp() {
	$tabsWhatsapp = $('#tabsWhatsapp');
	var $hiddenTab = $('#hiddenTab');

	$dropdownlistIntegrationType = $('#dropdownlistIntegrationType');

	$tabsWhatsapp.tabs({
		create: function (event, ui) {
			if (event.type === 'tabscreate') {
				$tabsWhatsapp.tabs('option', 'activate')({
					type: 'tabsactivate'
				}, {
					newPanel: ui.panel
				});
			}
		},
		activate: function (event, page) {
			var $divTab;
			if ((page.newPanel instanceof jQuery)) {
				$divTab = page.newPanel;
			}
			else {
				$divTab = $(page.newPanel.selector);
			}
			var tabId = $divTab.get(0).id;

			$hiddenTab.val(tabId);

			if (history.pushState) {
				history.pushState(null, null, '#' + tabId);
			}
			else {
				location.hash = '#' + tabId;
			}

			if (event.type == 'tabsactivate') {
				if (tabId == 'divNotifications') {
					editor = $textboxWhatsappInactivityDetectedEmailTemplate.cleditor()[0];
					editor.refresh();
				}
				else if (tabId == 'divAdvancedConfigurationYFlow') {
					if (typeof ($divTab.get(0).firstTime) !== 'boolean' ||
						$divTab.get(0).firstTime) {
						ReloadFlows();
						//ReloadFlowsContingency();
						$divTab.get(0).firstTime = false;
					}
				}
				else if (tabId === 'divWhatsappBehaviour') {
					if (typeof ($divTab.get(0).firstTime) !== 'boolean' ||
						$divTab.get(0).firstTime) {

						CreateHsmTable();
						CreateFlowTable();

						if (typeof ($checkboxUseFacebookCatalog) === 'undefined') {
							$checkboxUseFacebookCatalog = $('#checkboxUseFacebookCatalog');
						}

						if ($checkboxUseFacebookCatalog.is(':checked')) {
							if (typeof ($hiddenFacebookCatalogCatalog) === 'undefined') {
								$hiddenFacebookCatalogCatalog = $('#hiddenFacebookCatalogCatalog');
							}

							let json = $hiddenFacebookCatalogCatalog.val();
							if (json.length > 0) {
								let $tableFacebookCatalog = $('#tableFacebookCatalog');
								let $trsDependsOnFacebookCatalog = $('tr[rel=dependsOnFacebookCatalog]', $tableFacebookCatalog);
								$trsDependsOnFacebookCatalog.show();

								let $spanFacebookCatalogName = $('#spanFacebookCatalogName');
								let $spanFacebookCatalogBusinessName = $('#spanFacebookCatalogBusinessName');
								let catalog = JSON.parse($hiddenFacebookCatalogCatalog.val());
								$spanFacebookCatalogName.text(catalog.Name);
								$spanFacebookCatalogBusinessName.text(catalog.Business.Name);

								let $divFacebookBusinessCatalogProductsSelected = $('#divFacebookBusinessCatalogProductsSelected');
								let $messageFacebookBusinessCatalogSelectedNoProducts = $('#messageFacebookBusinessCatalogSelectedNoProducts');
								FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProductsSelected, $messageFacebookBusinessCatalogSelectedNoProducts);
							}
						}
					}

					if (typeof ($dropdownlistAllowToSendHSM) === 'undefined') {
						$dropdownlistAllowToSendHSM = $('#dropdownlistAllowToSendHSM');
					}

					if (typeof ($dropdownlistAllowToSendFlows) === 'undefined') {
						$dropdownlistAllowToSendFlows = $('#dropdownlistAllowToSendFlows');
					}

					if (typeof ($dropdownlistIntegrationType) === 'undefined') {
						$dropdownlistIntegrationType = $('#dropdownlistIntegrationType');
					}

					if ($dropdownlistAllowToSendHSM.val() === '1') {
						if ($dropdownlistIntegrationType.val() === '10') {
							let e = {
								IsValid: true
							}
							ValidateIntegrationType10(null, e);

							if (e.IsValid) {
								if (typeof ($divTab.get(0).firstTime) === 'undefined') {
									CheckNotDefinedYoizenHSMs();
								}
								$divTab.get(0).firstTime = false;
							}
						}
						else if ($dropdownlistIntegrationType.val() === '11') {
							let e = {
								IsValid: true
							}
							ValidateIntegrationType11(null, e);

							if (e.IsValid) {
								if (typeof ($divTab.get(0).firstTime) === 'undefined') {
									CheckNotDefinedCloudApiHSMs();
								}
								$divTab.get(0).firstTime = false;
							}
						}
						else if ($dropdownlistIntegrationType.val() === '4') {
							let e = {
								IsValid: true
							}
							ValidateIntegrationType4(null, e);

							if (e.IsValid) {
								if (typeof ($divTab.get(0).firstTime) === 'undefined') {
									CheckNotDefinedInfobipHSMs();
								}
								$divTab.get(0).firstTime = false;
							}
						}
					}

					if ($dropdownlistAllowToSendFlows.val() === '1') {
						if ($dropdownlistIntegrationType.val() === '10') {
							let e = {
								IsValid: true
							}
							ValidateIntegrationType10(null, e);

							if (e.IsValid) {
								if (typeof ($divTab.get(0).firstTime) === 'undefined') {
									ImportYoizenFlows();
								}
								$divTab.get(0).firstTime = false;
							}
						}
	
						else if ($dropdownlistIntegrationType.val() === '4') {

							//TODO imoprtar flows desde infobip (no existe el endpoint)
							/*let e = {
								IsValid: true
							}
							ValidateIntegrationType4(null, e);

							if (e.IsValid) {
								if (typeof ($divTab.get(0).firstTime) === 'undefined') {
									CheckNotDefinedInfobipHSMs();
								}
								$divTab.get(0).firstTime = false;
							} */
						}
					}
				}
			}
		}
	});

	$divFacebookCatalogs = $('#divFacebookCatalogs');
	$divFacebookCatalogsSelectBusiness = $('#divFacebookCatalogsSelectBusiness');
	$divFacebookCatalogsSelectCatalog = $('#divFacebookCatalogsSelectCatalog');
	$hiddenFacebookCatalogAccessToken = $('#hiddenFacebookCatalogAccessToken');
	$textboxFacebookCatalogBusiness = $('#textboxFacebookCatalogBusiness');
	$hiddenFacebookCatalogCatalog = $('#hiddenFacebookCatalogCatalog');
	$textboxFacebookCatalogID = $('#textboxFacebookCatalogID');
	$divFacebookLoginButton = $('div.facebook-login-button-container');
	$messageFacebookAccountError = $('#messageFacebookAccountError');
	$trFacebookAccount = $('#trFacebookAccount');
	$divFacebookBusiness = $('#divFacebookBusiness');
	$messageFacebookNoBusinesses = $('#messageFacebookNoBusinesses');
	$divFacebookBusinessCatalogs = $('#divFacebookBusinessCatalogs');
	$messageFacebookNoBusinessCatalogs = $('#messageFacebookNoBusinessCatalogs');
	$divFacebookRedirectUri = $('#divFacebookRedirectUri');
	$inputFacebookUrl = $('#inputFacebookUrl');
	$buttonEnterFacebookUrl = $('#buttonEnterFacebookUrl');
	$buttonChangeFacebookBusiness = $('#buttonChangeFacebookBusiness');
	$buttonFacebookBusinessCatalogAccept = $('#buttonFacebookBusinessCatalogAccept');
	$messageFacebookCouldntValidateAccessToken = $('#messageFacebookCouldntValidateAccessToken');
	$divFacebookUser = $('#divFacebookUser');
	$checkboxUseFacebookCatalog = $('#checkboxUseFacebookCatalog');

	let $tableFacebookCatalog = $('#tableFacebookCatalog');
	let $trsDependsOnFacebookCatalog = $('tr[rel=dependsOnFacebookCatalog]', $tableFacebookCatalog);

	$checkboxUseFacebookCatalog.change(function () {
		$trsDependsOnFacebookCatalog.toggle(this.checked);
	});

	if ($checkboxUseFacebookCatalog.is(':checked')) {
		let json = $hiddenFacebookCatalogCatalog.val();
		if (json.length > 0) {
			$trsDependsOnFacebookCatalog.show();

			let $spanFacebookCatalogName = $('#spanFacebookCatalogName');
			let $spanFacebookCatalogBusinessName = $('#spanFacebookCatalogBusinessName');
			let catalog = JSON.parse($hiddenFacebookCatalogCatalog.val());
			$spanFacebookCatalogName.text(catalog.Name);
			$spanFacebookCatalogBusinessName.text(catalog.Business.Name);

			let $divFacebookBusinessCatalogProductsSelected = $('#divFacebookBusinessCatalogProductsSelected');
			let $messageFacebookBusinessCatalogSelectedNoProducts = $('#messageFacebookBusinessCatalogSelectedNoProducts');
			//FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProductsSelected, $messageFacebookBusinessCatalogSelectedNoProducts);
		}
		else {
			$checkboxUseFacebookCatalog.prop('checked', false);
		}
	}

	if (window.addEventListener) {
		addEventListener("message", FacebookTokenCallbackMessage, false);
	}
	else {
		attachEvent("onmessage", FacebookTokenCallbackMessage);
	}

	$liTabBehaviour = $('#liTabBehaviour');
	$divWhatsappBehaviour = $('#divWhatsappBehaviour');
	$checkboxAutoReplyBeforeMaxTimeToAnswer = $('#checkboxAutoReplyBeforeMaxTimeToAnswer');
	$textboxAutoReplyBeforeMaxTimeToAnswerText = $('#textboxAutoReplyBeforeMaxTimeToAnswerText');
	$messageAutoReplyBeforeMaxTimeToAnswerText = $('#messageAutoReplyBeforeMaxTimeToAnswerText');
	CallValidationFields($textboxAutoReplyBeforeMaxTimeToAnswerText, $messageAutoReplyBeforeMaxTimeToAnswerText);
	$textboxAutoReplyBeforeMaxTimeToAnswerMinutes = $('#textboxAutoReplyBeforeMaxTimeToAnswerMinutes');

	$textboxMinutesToCloseHsmCases = $('#textboxMinutesToCloseHsmCases');
	$hsmCloseCaseOptions = $('#hsmCloseCaseOptions');

	$checkboxAutoReplyBeforeCloseCase = $('#checkboxAutoReplyBeforeCaseIsClosed');
	$textboxAutoReplyBeforeCloseCaseText = $('#textboxAutoReplyBeforeCaseIsClosedText');
	$textboxAutoReplyBeforeCloseCaseMinutes = $('#textboxAutoReplyBeforeCaseIsClosedMinutes');

	$dropdownlistAllowToSendFlows = $('#dropdownlistAllowToSendFlows');
	$dropdownlistAllowToSendHSM = $('#dropdownlistAllowToSendHSM');
	$trAllowAgentsToSendHSM = $('#trAllowAgentsToSendHSM');
	$divHSMTemplates = $('#divHSMTemplates');
	$divFlows = $('#divFlows');
	$messageHSMTemplatesEmpty = $('#messageHSMTemplatesEmpty');
	$tableHSMTemplatesv2 = $('#tableHSMTemplatesv2');
	$tbodyHSMTemplatesv2 = $('tbody', $tableHSMTemplatesv2);
	$hiddenHSMTemplates = $('#hiddenHSMTemplates');
	$buttonImportHSMs = $('#buttonImportHSMs');
	$buttonDeleteHSMs = $('#buttonDeleteHSMs');
	$buttonImportCloudApiHSMs = $('#buttonImportCloudApiHSMs');
	$buttonImportInfobipHSMs = $('#buttonImportInfobipHSMs');
	$buttonImportYoizenFlows = $('#buttonImportYoizenFlows');
	$buttonImportCloudApiFlows = $('#buttonImportCloudApiFlows');

	$hiddenFlows = $('#hiddenFlows');
	$messageFlowsEmpty = $('#messageFlowsEmpty');
	$tableFlows = $('#tableFlows');
	$tbodyFlow = $('tbody', $tableFlows);

	$divHSM = $('#divHSM');
	$inputHSMDescription = $('#inputHSMDescription');
	$selectHSMLanguage = $('#selectHSMLanguage');
	$selectHSMLanguage.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$inputHSMNamespace = $('#inputHSMNamespace');
	$inputHSMElementName = $('#inputHSMElementName');
	$selectHSMAllowedUses = $('#selectHSMAllowedUses');
	$selectAllowToConfigureSendHSMIfCaseOpen = $('#selectAllowToConfigureSendHSMIfCaseOpen');
	$selectHSMAllowedUses.multiselect({ multiple: true, selectedList: 3, buttonWidth: '>400', noneSelectedText: "Seleccione un uso" });
	$selectAllowToConfigureSendHSMIfCaseOpen.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$selectHSMHeaderType = $('#selectHSMHeaderType');
	$selectHSMHeaderType.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$divHSMHeaderTypeText = $('#divHSMHeaderTypeText');
	$inputHSMHeaderTypeText = $('#inputHSMHeaderTypeText');
	$divHSMHeaderTypeTextParameterName = $('#divHSMHeaderTypeTextParameterName');
	$inputHSMHeaderTypeTextParameterName = $('#inputHSMHeaderTypeTextParameterName');
	$divHSMHeaderTypeTextParameterDescription = $('#divHSMHeaderTypeTextParameterDescription');
	$inputHSMHeaderTypeTextParameterDescription = $('#inputHSMHeaderTypeTextParameterDescription');
	$divHSMHeaderTypeMedia = $('#divHSMHeaderTypeMedia');
	$selectHSMHeaderTypeMedia = $('#selectHSMHeaderTypeMedia');
	$selectHSMHeaderTypeMedia.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$inputHSMHeaderTypeMediaUrl = $('#inputHSMHeaderTypeMediaUrl');
	$divHSMHeaderTypeLocation = $('#divHSMHeaderTypeLocation');
	$inputHSMHeaderTypeLatitude = $('#inputHSMHeaderTypeLatitude');
	$inputHSMHeaderTypeLongitude = $('#inputHSMHeaderTypeLongitude');
	$textareaHSMTemplate = $('#textareaHSMTemplate');
	$divHSMParametersContainer = $('#divHSMParametersContainer');
	$selectHSMFooterType = $('#selectHSMFooterType');
	$selectHSMFooterType.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$divHSMFooterTypeText = $('#divHSMFooterTypeText');
	$inputHSMFooterTypeText = $('#inputHSMFooterTypeText');
	$selectHSMButtonsType = $('#selectHSMButtonsType');
	$selectHSMButtonsType.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' });
	$divHSMButtonsTypeCallToAction = $('#divHSMButtonsTypeCallToAction');
	$divHSMButtonsTypeCallToActionContainer = $('#divHSMButtonsTypeCallToActionContainer');
	$divHSMButtonsTypeAuthCode = $('#divHSMButtonsTypeAuthCode');
	$divHSMButtonsTypeAuthCodeContainer = $('#divHSMButtonsTypeAuthCodeContainer');
	$divHSMButtonsTypeQuickReply = $('#divHSMButtonsTypeQuickReply');
	$divHSMButtonsTypeQuickReplyContainer = $('#divHSMButtonsTypeQuickReplyContainer');
	$divHSMButtonsTypeMixed = $('#divHSMButtonsTypeMixed');
	$divHSMButtonsTypeMixedContainer = $('#divHSMButtonsTypeMixedContainer');
	$divHSMError = $('#divHSMError');
	$divPanelIntegrationType3VoiceCallsEndpoint = $('#divPanelIntegrationType3VoiceCallsEndpoint');

	$selectHSMHeaderType.change(function () {
		let headerType = parseInt($selectHSMHeaderType.val(), 10);
		$divHSMHeaderTypeText.toggle(headerType === HSMTemplateHeaderTypes.Text);
		$divHSMHeaderTypeMedia.toggle(headerType === HSMTemplateHeaderTypes.Media);
		$divHSMHeaderTypeLocation.toggle(headerType === HSMTemplateHeaderTypes.Location);
	});

	$inputHSMHeaderTypeText.blur(function () {
		var text = $inputHSMHeaderTypeText.val();
		if (text !== null && text.length > 0) {
			var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
			var matches = parametersRegex.exec(text);
			if (matches !== null) {
				var parameterName = matches[0].replace('{{', '').replace('}}', '');
				$inputHSMHeaderTypeTextParameterName.val(parameterName);
				$divHSMHeaderTypeTextParameterDescription.show();
				$divHSMHeaderTypeTextParameterName.show();
			}
			else {
				$divHSMHeaderTypeTextParameterDescription.hide();
				$divHSMHeaderTypeTextParameterName.hide();
			}
		}
		else {
			$divHSMHeaderTypeTextParameterDescription.hide();
			$divHSMHeaderTypeTextParameterName.hide();
		}
	});

	$selectHSMFooterType.change(function () {
		let footerType = parseInt($selectHSMFooterType.val(), 10);
		$divHSMFooterTypeText.toggle(footerType === HSMTemplateFooterTypes.Text);
	});

	$selectHSMButtonsType.change(function () {
		let buttonsType = parseInt($selectHSMButtonsType.val(), 10);
		$divHSMButtonsTypeCallToAction.toggle(buttonsType === HSMTemplateButtonsTypes.CallToAction);
		$divHSMButtonsTypeAuthCode.toggle(buttonsType === HSMTemplateButtonsTypes.AuthCode);
		$divHSMButtonsTypeQuickReply.toggle(buttonsType === HSMTemplateButtonsTypes.QuickReply);
		$divHSMButtonsTypeMixed.toggle(buttonsType === HSMTemplateButtonsTypes.Mixed);
	});

	$dropdownlistAllowToSendHSM.change(function () {
		var allow = $dropdownlistAllowToSendHSM.val() === '1';
		$trAllowAgentsToSendHSM.toggle(allow);
		$divHSMTemplates.toggle(allow);
	}).trigger('change');

	$dropdownlistAllowToSendFlows.change(function () {
		var allow = $dropdownlistAllowToSendFlows.val() === '1';
		$divFlows.toggle(allow);
	}).trigger('change');

	$messageVoiceCallsNotAvailable = $('#messageVoiceCallsNotAvailable');
	$divVoiceCallsAvailable = $('#divVoiceCallsAvailable');
	$checkboxVoiceCallsEnabled = $('#checkboxVoiceCallsEnabled');
	$divVoiceCallsEnabled = $('#divVoiceCallsEnabled');

	$checkboxVoiceCallsEnabled.change(function () {
		$divVoiceCallsEnabled.toggle(this.checked);
		$divPanelIntegrationType3VoiceCallsEndpoint.toggle(this.checked);
	}).trigger('change');

	$divVoiceCallInteractiveMessageInvite = $('#divVoiceCallInteractiveMessageInvite');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageInvite, {
		interactiveMessageType: InteractiveMessageTypes.VoiceCall,
		allowHeader: true,
		allowFooter: false,
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageInvite, 'invite', InteractiveMessageTypes.VoiceCall);
		}
	});

	$divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned = $('#divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned, {
		allowHeader: true,
		allowFooter: true,
		canEdit: true,
		canRemove: true,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned, 'users_case_not_assigned');
		}
	});

	$divVoiceCallInteractiveMessageRejectAgentNotAvailable = $('#divVoiceCallInteractiveMessageRejectAgentNotAvailable');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageRejectAgentNotAvailable, {
		allowHeader: true,
		allowFooter: true,
		canEdit: true,
		canRemove: true,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageRejectAgentNotAvailable, 'agent_not_available');
		}
	});

	$divVoiceCallInteractiveMessageRejectAgentWithAnotherCall = $('#divVoiceCallInteractiveMessageRejectAgentWithAnotherCall');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageRejectAgentWithAnotherCall, {
		allowHeader: true,
		allowFooter: true,
		canEdit: true,
		canRemove: true,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageRejectAgentWithAnotherCall, 'agent_another_call');
		}
	});

	$divVoiceCallInteractiveMessageRejectCaseWithCurrentCall = $('#divVoiceCallInteractiveMessageRejectCaseWithCurrentCall');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageRejectCaseWithCurrentCall, {
		allowHeader: true,
		allowFooter: true,
		canEdit: true,
		canRemove: true,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageRejectCaseWithCurrentCall, 'case_with_current_call');
		}
	});

	$divVoiceCallInteractiveMessageRejectCaseWithoutInvite = $('#divVoiceCallInteractiveMessageRejectCaseWithoutInvite');
	BuildDynamicWhatsappInteractiveMessageInfo($divVoiceCallInteractiveMessageRejectCaseWithoutInvite, {
		allowHeader: true,
		allowFooter: true,
		canEdit: true,
		canRemove: true,
		onEdit: function () {
			ShowEditWhatsappInteractiveMessage($divVoiceCallInteractiveMessageRejectCaseWithoutInvite, 'case_without_invite');
		}
	});


	$trSubscribeToPushWithoutYFlow = $('#trSubscribeToPushWithoutYFlow');
	$checkboxSubscribeToPushWithoutYFlow = $('#checkboxSubscribeToPushWithoutYFlow');

	$dropdownlistUseYFlow = $('#dropdownlistUseYFlow');
	$liTabAdvancedConfigurationYFlow = $('#liTabAdvancedConfigurationYFlow');
	$divAdvancedConfigurationYFlow = $('#divAdvancedConfigurationYFlow');
	$anchorFlowsReload = $('#anchorFlowsReload');
	$anchorFlowsContingencyReload = $('#anchorFlowsContingencyReload');
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		$dropdownlistUseYFlow.change(function () {
			let useYFlow = $dropdownlistUseYFlow.val() === 'true';
			$liTabAdvancedConfigurationYFlow.toggle(useYFlow);
			//$divAdvancedConfigurationYFlow.toggle(useYFlow);
			$tabsWhatsapp.tabs('refresh');
			$trSubscribeToPushWithoutYFlow.toggle(!useYFlow);
			if (useYFlow) {
				ReloadFlows();
				//ReloadFlowsContingency();
			}
		}).trigger('change');

		$hiddenFlow = $('#hiddenFlow');
		$selectFlowToUse = $('#selectFlowToUse');
		$spanFlowID = $('#spanFlowID');
		$spanFlowName = $('#spanFlowName');
		$spanFlowVersion = $('#spanFlowVersion');

		$hiddenFlowContingency = $('#hiddenFlowContingency');
		$selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
		$spanFlowContingencyID = $('#spanFlowContingencyID');
		$spanFlowContingencyName = $('#spanFlowContingencyName');
		$spanFlowContingencyVersion = $('#spanFlowContingencyVersion');

		$hiddenFlowQueueTransfersByKey = $('#hiddenFlowQueueTransfersByKey');
		$tableFlowQueueTransfersByKey = $('#tableFlowQueueTransfersByKey');
		$anchorFlowQueueTransfersByKeyAdd = $('#anchorFlowQueueTransfersByKeyAdd');

		$listboxFlowShareEnqueuedMessagesFromQueues = $('#listboxFlowShareEnqueuedMessagesFromQueues');
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
		$listboxFlowShareConnectedAgentsFromQueues = $('#listboxFlowShareConnectedAgentsFromQueues');
		$listboxFlowShareConnectedAgentsFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
		$listboxFlowShareWithServices = $('#listboxFlowShareWithServices');
		$listboxFlowShareWithServices.multiselect({ multiple: true, noneSelectedText: "Todos los servicios", selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

		$selectFlowToUse.change(function () {
			let $option = $('option:selected', $selectFlowToUse);
			let flow = $option.prop('definition');
			if (flow != null) {
				$spanFlowID.text(flow.id);
				$spanFlowName.text(flow.name);
				$spanFlowVersion.text(flow.ActiveProductionVersion.number);
				$hiddenFlow.val(JSON.stringify(flow));
			}
		});

		$anchorFlowsReload.click(ReloadFlows);
		$anchorFlowQueueTransfersByKeyAdd.click(AddFlowQueueTransfersByKeyRow);

		$selectFlowContingencyToUse.change(function () {
			var $option = $('option:selected', $selectFlowContingencyToUse);
			var flowContingency = $option.prop('definition');
			if (flowContingency != null) {
				$spanFlowContingencyID.text(flowContingency.id);
				$spanFlowContingencyName.text(flowContingency.name);
				$spanFlowContingencyVersion.text(flowContingency.ActiveProductionVersion.number);
				$hiddenFlowContingency.val(JSON.stringify(flowContingency));
			}
		});

		$anchorFlowsContingencyReload.click(ReloadFlowsContingency);

		let transfers = $hiddenFlowQueueTransfersByKey.val();
		if (transfers !== null && transfers.length > 0) {
			transfers = JSON.parse(transfers);
			for (let transfer in transfers) {
				AddFlowQueueTransfersByKeyRow(null, { Key: transfer, QueueID: transfers[transfer] });
			}
		}

		let currentFlow = $hiddenFlow.val();
		if (currentFlow !== null && currentFlow.length > 0) {
			currentFlow = JSON.parse(currentFlow);
			$spanFlowID.text(currentFlow.ID);
			$spanFlowName.text(currentFlow.Name);
			$spanFlowVersion.text(currentFlow.Version);
		}
	}
	else {
		$liTabAdvancedConfigurationYFlow.hide();
		$tabsWhatsapp.tabs('refresh');
	}

	$("#textboxWhatsAppFromDate").datepicker({ changeMonth: true, changeYear: true, numberOfMonths: 1 });

	$divWhatsappServiceMedia = $('#divWhatsappServiceMedia');
	$checkboxWhatsappAllowToSendMedia = $('input[type=checkbox][id$=checkboxWhatsappAllowToSendMedia]', $divWhatsappServiceMedia);
	$('select[id$=dropdownlistWhatsappDefaultInternationCode]').multiselect({ multiple: false, selectedList: 4, buttonWidth: '>500' }).multiselectfilter();

	$checkboxWhatsappAllowToSendMedia.change(function () {
		var $table = $checkboxWhatsappAllowToSendMedia.closest('table.uiInfoTable');
		var $trs = $('tr[rel=AllowToSendAttachments]', $table);
		$trs.toggle(this.checked);
	}).trigger('change');

	$checkboxActAsChat = $('#checkboxActAsChat');
	$trAllowToReplyToSpecificMessage = $('#trAllowToReplyToSpecificMessage');
	$checkboxActAsChat.change(function () {
		$trAllowToReplyToSpecificMessage.toggle(this.checked);
	}).trigger('change');

	$textboxWhatsappInactivityDetectedEmailSubject = $('#textboxWhatsappInactivityDetectedEmailSubject');
	$textboxWhatsappInactivityDetectedEmailTemplate = $('#textboxWhatsappInactivityDetectedEmailTemplate');
	$hiddenInactivityDetectedConnection = $('#hiddenInactivityDetectedConnection');
	$listboxInactivityDetectedConnection = $('#listboxInactivityDetectedConnection');

	if (typeof (emails) !== 'undefined' && emails !== null && emails.length > 0) {
		let addOption = function (email, $select) {
			let $option = $('<option></option>');
			$option.text(email.Name);
			$option.val(email.ID);
			$select.append($option);
		};

		let selectOptions = function ($hidden, $select) {
			let value = $hidden.val();
			if (typeof (value) === 'string' && value.length > 0) {
				$select.val(value);
			}
		};

		for (let i = 0; i < emails.length; i++) {
			//No listamos la casilla por defult ya que al enviar el valor en null se utilizara casilla por defecto
			if (!emails[i].UseAsDefault) {
				addOption(emails[i], $listboxInactivityDetectedConnection);
			}
		}

		selectOptions($hiddenInactivityDetectedConnection, $listboxInactivityDetectedConnection);
	}

	$listboxInactivityDetectedConnection.multiselect({ multiple: false, noneSelectedText: "Por defecto (actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxInactivityDetectedConnection.change(function () {
		var value = $listboxInactivityDetectedConnection.val();
		$hiddenInactivityDetectedConnection.val('');
		if (value != null) {
			$hiddenInactivityDetectedConnection.val(value);
		}

	});

	$trDelayAfterMultimedia = $('#trDelayAfterMultimedia');
	$trPreviewUrlForTextMessages = $('#trPreviewUrlForTextMessages');
	$divCapiService = $('#divCapiService');

	$divIntegrationType2 = $('#divIntegrationType2');
	$divIntegrationType3 = $('#divIntegrationType3');
	$divIntegrationType4 = $('#divIntegrationType4');
	$divIntegrationType5 = $('#divIntegrationType5');
	$divIntegrationType6 = $('#divIntegrationType6');
	$divIntegrationType7 = $('#divIntegrationType7');
	$divIntegrationType8 = $('#divIntegrationType8');
	$divIntegrationType9 = $('#divIntegrationType9');
	$divIntegrationType10 = $('#divIntegrationType10');
	$divIntegrationType11 = $('#divIntegrationType11');

	$dropdownlistIntegrationType.change(function () {
		var value = $dropdownlistIntegrationType.val();
		$divIntegrationType2.toggle(value === "2");
		$divIntegrationType3.toggle(value === "3");
		$divIntegrationType4.toggle(value === "4");
		$divIntegrationType5.toggle(value === "5");
		$divIntegrationType6.toggle(value === "6");
		$divIntegrationType7.toggle(value === "7");
		$divIntegrationType8.toggle(value === "8");
		$divIntegrationType9.toggle(value === "9");
		$divIntegrationType10.toggle(value === "10");
		$divIntegrationType11.toggle(value === "11");
		$buttonImportHSMs.parent().toggle(value === "10");
		$buttonDeleteHSMs.parent().toggle(value === "10");
		$buttonImportYoizenFlows.parent().toggle(value === "10");
		$buttonImportCloudApiHSMs.parent().toggle(value === "11");
		$buttonImportCloudApiFlows.parent().toggle(value === "11");
		$buttonImportInfobipHSMs.parent().toggle(value === "4");
		$trDelayAfterMultimedia.toggle(value === "10" || value === "11");
		$trPreviewUrlForTextMessages.toggle(value === "10" || value === "11" || value === "4");
		$divCapiService.toggle(value === "10" && enabledCapi === true);

		$liTabBehaviour.toggle(value !== "1");
		$tabsWhatsapp.tabs('refresh');

		$messageVoiceCallsNotAvailable.toggle(value !== '10' &&
			(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || value !== '11'));
		$divVoiceCallsAvailable.toggle(value === '10' ||
			(typeof (allowAgentsToStartVoiceCallOnCloudApi) === 'boolean' && allowAgentsToStartVoiceCallOnCloudApi && value === '11'));

		$selectHSMButtonsType.find('option.mixed').prop('disabled', (value !== "11" && value !== "10"));

		if (typeof (datatableTemplates) === 'object' && datatableTemplates !== null) {
			datatableTemplates.column(3).visible(value === '10' || value === '11' || value === '4');
		}

		if (typeof (datatableFlows) === 'object' && datatableFlows !== null) {
			datatableFlows.column(3).visible(value === '10' || value === '11' || value === '4');
		}
	}).trigger('change');

	var $dropdownlistIntegrationType4AuthorizationType = $('#dropdownlistIntegrationType4AuthorizationType');
	var $trIntegrationType4AuthorizationType1User = $('#trIntegrationType4AuthorizationType1User');
	var $trIntegrationType4AuthorizationType1Password = $('#trIntegrationType4AuthorizationType1Password');
	var $trIntegrationType4AuthorizationType2ApiKey = $('#trIntegrationType4AuthorizationType2ApiKey');
	$dropdownlistIntegrationType4AuthorizationType.change(function () {
		let type = parseInt($dropdownlistIntegrationType4AuthorizationType.val(), 10);
		$trIntegrationType4AuthorizationType1User.toggle(type === 1);
		$trIntegrationType4AuthorizationType1Password.toggle(type === 1);
		$trIntegrationType4AuthorizationType2ApiKey.toggle(type === 2);
	}).trigger('change')

	$divIntegrationType3ReplyEndpoint = $('#divIntegrationType3ReplyEndpoint');
	BuildDynamicHttpRequestInfo($divIntegrationType3ReplyEndpoint, {
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditHttpRequest('reply', $divIntegrationType3ReplyEndpoint);
		}
	});

	$divIntegrationType3VoiceCallsEndpoint = $('#divIntegrationType3VoiceCallsEndpoint');
	BuildDynamicHttpRequestInfo($divIntegrationType3VoiceCallsEndpoint, {
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditHttpRequest('voicecalls', $divIntegrationType3VoiceCallsEndpoint);
		}
	});

	$dropdownlistIntegrationType3PullType = $('#dropdownlistIntegrationType3PullType');
	$trIntegrationType3PullTypeSpecificGet = $('#trIntegrationType3PullTypeSpecificGet');
	$trIntegrationType3PullTypeSpecificPost = $('#trIntegrationType3PullTypeSpecificPost');
	$dropdownlistIntegrationType3PullType.change(function () {
		let type = parseInt($dropdownlistIntegrationType3PullType.val());
		$trIntegrationType3PullTypeSpecificGet.toggle(type === 2);
		$trIntegrationType3PullTypeSpecificPost.toggle(type === 2);
	}).trigger('change');

	$divIntegrationType3GetNewsEndpoint = $('#divIntegrationType3GetNewsEndpoint');
	BuildDynamicHttpRequestInfo($divIntegrationType3GetNewsEndpoint, {
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditHttpRequest('getnews', $divIntegrationType3GetNewsEndpoint);
		},
		onRemove: function () {
			$divIntegrationType3GetNewsEndpoint.setInfo(null);
		}
	});

	$divIntegrationType3PostNewsProcessedEndpoint = $('#divIntegrationType3PostNewsProcessedEndpoint');
	BuildDynamicHttpRequestInfo($divIntegrationType3PostNewsProcessedEndpoint, {
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditHttpRequest('postnewsprocessed', $divIntegrationType3PostNewsProcessedEndpoint);
		},
		onRemove: function () {
			$divIntegrationType3PostNewsProcessedEndpoint.setInfo(null);
		}
	});

	$dropdownlistIntegrationType3NotifyClosedCases = $('#dropdownlistIntegrationType3NotifyClosedCases');
	$trIntegrationType3NotifyClosedCasesTrue = $('#trIntegrationType3NotifyClosedCasesTrue');
	$dropdownlistIntegrationType3NotifyClosedCases.change(function () {
		$trIntegrationType3NotifyClosedCasesTrue.toggle($dropdownlistIntegrationType3NotifyClosedCases.val() === '1');
	}).trigger('change');

	$divIntegrationType3CloseCaseEndpoint = $('#divIntegrationType3CloseCaseEndpoint');
	BuildDynamicHttpRequestInfo($divIntegrationType3CloseCaseEndpoint, {
		canEdit: true,
		canRemove: false,
		onEdit: function () {
			ShowEditHttpRequest('closecase', $divIntegrationType3CloseCaseEndpoint);
		},
		onRemove: function () {
			$divIntegrationType3CloseCaseEndpoint.setInfo(null);
		}
	});

	$checkboxIntegrationType3UseWhatsappFormat = $('#checkboxIntegrationType3UseWhatsappFormat');
	$trIntegrationType3UseWhatsappFormatIncludeFrom = $('#trIntegrationType3UseWhatsappFormatIncludeFrom');
	$checkboxIntegrationType3UseWhatsappFormat.change(function () {
		$trIntegrationType3UseWhatsappFormatIncludeFrom.toggle(this.checked);
	}).trigger('change');

	$dropdownlistIntegrationType3PayloadType = $('#dropdownlistIntegrationType3PayloadType');
	$divIntegrationType3PayloadTypeObject = $('#divIntegrationType3PayloadTypeObject');
	$hiddenIntegrationType3PayloadTypeObject = $('#hiddenIntegrationType3PayloadTypeObject');
	$divIntegrationType3PayloadTypeObjectContainer = $('#divIntegrationType3PayloadTypeObjectContainer');
	$divIntegrationType3Derivations = $('#divIntegrationType3Derivations');
	$hiddenIntegrationType3Derivations = $('#hiddenIntegrationType3Derivations');
	$divIntegrationType3DerivationsContainer = $('#divIntegrationType3DerivationsContainer');

	$dropdownlistIntegrationType3PayloadType.change(function () {
		$divIntegrationType3PayloadTypeObject.toggle($dropdownlistIntegrationType3PayloadType.val() === '1');
	}).trigger('change');

	var initialData = null;
	if ($dropdownlistIntegrationType3PayloadType.val() === '1') {
		var value = $hiddenIntegrationType3PayloadTypeObject.val();
		if (typeof (value) !== 'undefined' &&
			value !== null &&
			value.length > 0) {
			initialData = JSON.parse(value);
		}
	}

	$tableIntegrationType3PayloadTypeObjectProps = BuildDynamicTable({
		columns: [
			{
				header: {
					title: 'Nombre',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-name'
				},
				key: 'name'
			}, {
				header: {
					title: 'Descripción',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-desc'
				},
				key: 'description'
			}, {
				header: {
					title: 'Tipo',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type'
				},
				type: 'select',
				key: 'type',
				options: [
					{
						title: 'Texto',
						value: 'string',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string'
					}, {
						title: 'Numérico',
						value: 'integer',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer'
					}, {
						title: 'Fecha',
						value: 'date',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date'
					}, {
						title: 'Boooleano',
						value: 'boolean',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean'
					}, {
						title: 'Timestamp',
						value: 'timestamp',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp'
					}
				],
				onChange: function (key, $select, $tr) {
					var value = $select.val();
					var $inputUseForDerivation = $('input[rel=useForDerivation]', $tr);
					if (value === 'string') {
						$inputUseForDerivation.show();
					}
					else {
						$inputUseForDerivation.removeAttr('checked').trigger('change');
						$inputUseForDerivation.hide();
					}
				}
			}, {
				header: {
					title: 'Usar para segmentar',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-use_for_queue'
				},
				type: 'checkbox',
				style: 'text-align: center',
				key: 'useForDerivation',
				rel: 'useForDerivation',
				onChange: function (key, $select, $tr) {
					var $selectsUseForQueue = $('input[rel=useForDerivation]', $tableIntegrationType3PayloadTypeObjectProps);
					var showDerivations = false;
					for (var i = 0; i < $selectsUseForQueue.length; i++) {
						if ($($selectsUseForQueue.get(i)).is(':checked')) {
							showDerivations = true;
							break;
						}
					}

					$divIntegrationType3Derivations.toggle(showDerivations);
				}
			}, {
				header: {
					title: 'Mostrar a agentes',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-show_to_agents'
				},
				type: 'checkbox',
				style: 'text-align: center',
				key: 'showToAgents'
			}
		],
		container: $divIntegrationType3PayloadTypeObjectContainer,
		initialData: initialData
	});

	initialData = null;
	if ($dropdownlistIntegrationType3PayloadType.val() === '1') {
		var value = $hiddenIntegrationType3PayloadTypeObject.val();
		if (typeof (value) !== 'undefined' &&
			value !== null &&
			value.length > 0) {
			var props = JSON.parse(value);
			if (props.findIndex(p => p.useForDerivation) >= 0) {
				value = $hiddenIntegrationType3Derivations.val();
				if (typeof (value) !== 'undefined' &&
					value !== null &&
					value.length > 0) {
					var derivations = JSON.parse(value);
					initialData = [];
					for (var derivation in derivations) {
						initialData.push({
							key: derivation,
							queue: derivations[derivation]
						});
					}
				}
			}
		}
	}

	$tableIntegrationType3Derivations = BuildDynamicTable({
		columns: [
			{
				header: {
					title: 'Clave',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-derivations-key'
				},
				key: 'key'
			}, {
				header: {
					title: 'Cola',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-derivations-queue'
				},
				type: 'select',
				key: 'queue',
				options: queues,
				textField: 'Name',
				valueField: 'ID'
			}
		],
		container: $divIntegrationType3DerivationsContainer,
		initialData: initialData
	});

	$dropdownlistIntegrationType6PayloadType = $('#dropdownlistIntegrationType6PayloadType');
	$divIntegrationType6PayloadTypeObject = $('#divIntegrationType6PayloadTypeObject');
	$hiddenIntegrationType6PayloadTypeObject = $('#hiddenIntegrationType6PayloadTypeObject');
	$divIntegrationType6PayloadTypeObjectContainer = $('#divIntegrationType6PayloadTypeObjectContainer');

	$dropdownlistIntegrationType6PayloadType.change(function () {
		$divIntegrationType6PayloadTypeObject.toggle($dropdownlistIntegrationType6PayloadType.val() === '1');
	}).trigger('change');

	var initialData = null;
	if ($dropdownlistIntegrationType6PayloadType.val() === '1') {
		var value = $hiddenIntegrationType6PayloadTypeObject.val();
		if (typeof (value) !== 'undefined' &&
			value !== null &&
			value.length > 0) {
			initialData = JSON.parse(value);
		}
	}

	$tableIntegrationType6PayloadTypeObjectProps = BuildDynamicTable({
		columns: [
			{
				header: {
					title: 'Nombre',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-name'
				},
				key: 'name'
			}, {
				header: {
					title: 'Descripción',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-desc'
				},
				key: 'description'
			}, {
				header: {
					title: 'Tipo',
					'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type'
				},
				type: 'select',
				key: 'type',
				options: [
					{
						title: 'Texto',
						value: 'string',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-string'
					}, {
						title: 'Numérico',
						value: 'integer',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-integer'
					}, {
						title: 'Fecha',
						value: 'date',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-date'
					}, {
						title: 'Boooleano',
						value: 'boolean',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-boolean'
					}, {
						title: 'Timestamp',
						value: 'timestamp',
						'data-i18n': 'configuration-serviceswhatsapp-postback-more_settings-payload-property-type-timestamp'
					}
				],
				onChange: function (key, $select, $tr) {
					var value = $select.val();
					var $inputUseForDerivation = $('input[rel=useForDerivation]', $tr);
					if (value === 'string') {
						$inputUseForDerivation.show();
					}
					else {
						$inputUseForDerivation.removeAttr('checked').trigger('change');
						$inputUseForDerivation.hide();
					}
				}
			}
		],
		container: $divIntegrationType6PayloadTypeObjectContainer,
		initialData: initialData
	});

	$checkboxIntegrationType6EnableSurveys = $('#checkboxIntegrationType6EnableSurveys');
	$divIntegrationType6SurveyConfiguration = $('#divIntegrationType6SurveyConfiguration');
	$dropdownlistIntegrationType6Survey = $('#dropdownlistIntegrationType6Survey');
	$messageIntegrationType6SurveyDisabled = $('#messageIntegrationType6SurveyDisabled');

	$dropdownlistIntegrationType6Survey.change(function () {
		if ($dropdownlistIntegrationType6Survey.val() != '-1') {
			$messageIntegrationType6SurveyDisabled.hide();
		}
	});

	$checkboxIntegrationType6EnableSurveys.change(function () {
		$divIntegrationType6SurveyConfiguration.toggle($checkboxIntegrationType6EnableSurveys.is(':checked'));
	}).trigger('change');

	var $textboxIntegrationType6SurveyInvitation = $('#textboxIntegrationType6SurveyInvitation');
	var $messageIntegrationType6SurveyInvitationFields = $('#messageIntegrationType6SurveyInvitationFields');
	CallValidationFields($textboxIntegrationType6SurveyInvitation, $messageIntegrationType6SurveyInvitationFields);

	$textboxIntegrationType7User = $('#textboxIntegrationType7User');
	$textboxIntegrationType7Password = $('#textboxIntegrationType7Password');
	$textboxIntegrationType7BaseUrl = $('#textboxIntegrationType7BaseUrl');
	$textboxIntegrationType7MediaID = $('#textboxIntegrationType7MediaID');

	$textboxIntegrationType8AppName = $('#textboxIntegrationType8AppName');

	$textboxIntegrationType9AccountSid = $('#textboxIntegrationType9AccountSid');
	$textboxIntegrationType9AuthToken = $('#textboxIntegrationType9AuthToken');

	var $checkboxWhatsappAcceptedTypes = $('input[type=checkbox][id*=checkboxWhatsappAcceptedType]');
	var $dropdownlistWhatsappDefaultExtension = $('#dropdownlistWhatsappDefaultExtension');

	var $options = $('option', $dropdownlistWhatsappDefaultExtension);
	var $optionNoAcceptedTypeSelected = $('<option></option>');
	$optionNoAcceptedTypeSelected.attr('value', '');
	$optionNoAcceptedTypeSelected.text($.i18n("configuration-serviceswhatsapp-select_file"));

	if ($options.length == 0) {
		$dropdownlistWhatsappDefaultExtension.append($optionNoAcceptedTypeSelected);
		$dropdownlistWhatsappDefaultExtension.attr('disabled', 'disabled');
	}

	$checkboxWhatsappAcceptedTypes.change(function () {
		var $this = $(this);
		var id = $this.attr('id');
		var $parent = $this.parent();
		if ($parent.hasClass('switch')) {
			$parent = $parent.parent();
		}
		var attachmentType = $parent.attr('attachmentType');
		var $label = $('label[for=' + id + ']', $parent.parent());
		var currentValue = $dropdownlistWhatsappDefaultExtension.val();

		if ($this.is(':checked')) {
			var $option = $('<option></option>');
			$option.attr('value', attachmentType);
			$option.text($label.text());
			$option.attr('data-i18n', $label.attr('data-i18n'));
			$dropdownlistWhatsappDefaultExtension.append($option);

			if ($dropdownlistWhatsappDefaultExtension.attr('disabled') === 'disabled') {
				$dropdownlistWhatsappDefaultExtension.removeAttr('disabled');
				$optionNoAcceptedTypeSelected.detach();
			}
		}
		else {
			var $option = $('option[value=' + attachmentType + ']', $dropdownlistWhatsappDefaultExtension);
			if ($option.length > 0) {
				$option.detach();

				if (currentValue === attachmentType) {
					$dropdownlistWhatsappDefaultExtension.val($('option:first', $dropdownlistWhatsappDefaultExtension).val());
				}

				var $options = $('option', $dropdownlistWhatsappDefaultExtension);
				if ($options.length == 0) {
					$dropdownlistWhatsappDefaultExtension.append($optionNoAcceptedTypeSelected);
					$dropdownlistWhatsappDefaultExtension.attr('disabled', 'disabled');
				}
			}
		}
	}).trigger('change');

	var $hiddenWhatsappDefaultExtension = $('#hiddenWhatsappDefaultExtension');
	$dropdownlistWhatsappDefaultExtension.val($hiddenWhatsappDefaultExtension.val());

	$dropdownlistWhatsappDefaultExtension.change(function () {
		$hiddenWhatsappDefaultExtension.val($dropdownlistWhatsappDefaultExtension.val());
	});

	var cleditorOptions = {
		height: 200,
		width: 'auto',
		fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
		bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
		controls:     // controls to add to the toolbar
			"bold italic underline | font size " +
			"style | color highlight removeformat | bullets numbering | outdent " +
			"indent | alignleft center alignright justify | " +
			"link | cut copy paste pastetext | source",
	};

	var $messageWhatsappInactivityDetectedEmailTemplateFields = $('#messageWhatsappInactivityDetectedEmailTemplateFields');
	$textboxWhatsappInactivityDetectedEmailTemplate.cleditor(cleditorOptions);
	CallValidationFields($textboxWhatsappInactivityDetectedEmailTemplate, $messageWhatsappInactivityDetectedEmailTemplateFields);

	var $messageWhatsappInactivityDetectedEmailSubjectFields = $('#messageWhatsappInactivityDetectedEmailSubjectFields');
	CallValidationFields($textboxWhatsappInactivityDetectedEmailSubject, $messageWhatsappInactivityDetectedEmailSubjectFields);

	$textboxWhatsAppSelectedPhoneCode = $('#textboxWhatsAppSelectedPhoneCode');
	$textboxWhatsAppPhoneNumber = $('#textboxWhatsAppPhoneNumber');

	$dropdownlistWhatsAppCountries = $('#dropdownlistWhatsAppCountries');
	$dropdownlistWhatsAppCountries.multiselect({ multiple: false, selectedList: 4, buttonWidth: '>500' }).multiselectfilter();
	$dropdownlistWhatsAppCountries.change(function () {
		$textboxWhatsAppSelectedPhoneCode.show();
		if ($('option:selected', $dropdownlistWhatsAppCountries).text() != "Sin Seleccionar") {
			$textboxWhatsAppSelectedPhoneCode.val($dropdownlistWhatsAppCountries.val());
		}
		else {
			$textboxWhatsAppSelectedPhoneCode.val('');
		}
	}).trigger('change');

	registerTabs($tabsWhatsapp);

	var $textboxDelayBetweenReplies = $('#textboxDelayBetweenReplies');
	var $spanDelayBetweenReplies = $('#spanDelayBetweenReplies');
	$textboxDelayBetweenReplies.change(function () {
		if (translationsLoaded) {
			$spanDelayBetweenReplies.text($.i18n('configuration-serviceswhatsapp-behaviour-other-delay-milliseconds', $textboxDelayBetweenReplies.val()));
		}
		else {
			$spanDelayBetweenReplies.text($textboxDelayBetweenReplies.val());
		}
	}).trigger('change');

	var $textboxDelayAfterMultimedia = $('#textboxDelayAfterMultimedia');
	var $spanDelayAfterMultimedia = $('#spanDelayAfterMultimedia');
	$textboxDelayAfterMultimedia.change(function () {
		if (translationsLoaded) {
			$spanDelayAfterMultimedia.text($.i18n('configuration-serviceswhatsapp-behaviour-other-delay-milliseconds', $textboxDelayAfterMultimedia.val()));
		}
		else {
			$spanDelayAfterMultimedia.text($textboxDelayAfterMultimedia.val());
		}
	}).trigger('change');

	$textboxIntegrationType6ClientID = $('#textboxIntegrationType6ClientID');
	$textboxIntegrationType6ClientSecret = $('#textboxIntegrationType6ClientSecret');
	$textboxIntegrationType6UrlBase = $('#textboxIntegrationType6UrlBase');
	$textboxIntegrationType6ChannelID = $('#textboxIntegrationType6ChannelID');
	$textboxIntegrationType6FromID = $('#textboxIntegrationType6FromID');
	$textboxIntegrationType6FromName = $('#textboxIntegrationType6FromName');

	$checkboxIntegrationType6SendEndOfConversation = $('#checkboxIntegrationType6SendEndOfConversation');
	$trIntegrationType6EndOfConversation = $('#trIntegrationType6EndOfConversation');
	$textboxIntegrationType6EndOfConversation = $('#textboxIntegrationType6EndOfConversation');
	CallValidationFields($textboxIntegrationType6EndOfConversation, $('#messageIntegrationType6EndOfConversation'));

	$checkboxIntegrationType6SendEndOfConversation.change(function () {
		$trIntegrationType6EndOfConversation.toggle(this.checked);
	}).trigger('change');

	CallValidationFields($('#textboxIntegrationType6Login'), $('#messageIntegrationType6Login'));
	CallValidationFields($('#textboxIntegrationType6SendMessage'), $('#messageIntegrationType6SendMessage'));
	CallValidationFields($('#textboxIntegrationType6ConversationActivities'), $('#messageIntegrationType6ConversationActivities'));
	var $spanIntegrationType6UrlBase = $('span[rel=IntegrationType6UrlBase]', $divIntegrationType6);
	$textboxIntegrationType6UrlBase.change(function () {
		let urlbase = $textboxIntegrationType6UrlBase.val();
		if (!urlbase.endsWith('/')) {
			urlbase += '/';
		}
		$spanIntegrationType6UrlBase.text(urlbase);
	}).trigger('change');

	var $checkboxIntegrationType10SendToServiceBus = $('#checkboxIntegrationType10SendToServiceBus');
	var $trIntegrationType10UseSessionInServiceBus = $('#trIntegrationType10UseSessionInServiceBus');
	var $trIntegrationType10UseSessionForHsmInServiceBus = $('#trIntegrationType10UseSessionForHsmInServiceBus');
	var $trIntegrationType10AccountID = $('#trIntegrationType10AccountID');
	var $trIntegrationType10LineID = $('#trIntegrationType10LineID');
	var $trIntegrationType10UseSeparateQueueForSingle = $('#trIntegrationType10UseSeparateQueueForSingle');
	$checkboxIntegrationType10SendToServiceBus.change(function () {
		$trIntegrationType10UseSessionInServiceBus.toggle($checkboxIntegrationType10SendToServiceBus.is(':checked'));
		$trIntegrationType10UseSessionForHsmInServiceBus.toggle($checkboxIntegrationType10SendToServiceBus.is(':checked'));
		$trIntegrationType10AccountID.toggle($checkboxIntegrationType10SendToServiceBus.is(':checked'));
		$trIntegrationType10LineID.toggle($checkboxIntegrationType10SendToServiceBus.is(':checked'));
		$trIntegrationType10UseSeparateQueueForSingle.toggle($checkboxIntegrationType10SendToServiceBus.is(':checked'));
	}).trigger('change');

	InitializeSurveysControls();
	InitializeCasesControls();

	$textboxMaxElapsedMinutesToCloseHsmCases = $('#textboxMaxElapsedMinutesToCloseHsmCases');
	$textboxMaxElapsedMinutesToCloseHsmCases.val(casesSettings.MaxElapsedMinutesToCloseHsmCases).trigger('change');

	$checkboxIntegrationType11TestingAccount = $('#checkboxIntegrationType11TestingAccount');
	$trIntegrationType11TestingMapping = $('#trIntegrationType11TestingMapping');
	$textboxIntegrationType11TestingMapping = $('#textboxIntegrationType11TestingMapping');

	$checkboxIntegrationType11TestingAccount.change(function () {
		$trIntegrationType11TestingMapping.toggle($checkboxIntegrationType11TestingAccount.is(':checked'));
	}).trigger('change');

	var param = $(document).getUrlParam('tab');
	if (param && param != '') {
		$tabsWhatsapp.tabs('select', 'div' + param);
	}
	else {
		let tab = $hiddenTab.val();
		if (typeof (tab) === 'string' &&
			tab.length > 0) {
			$tabsWhatsapp.tabs('select', tab);
		}
	}

	$hiddenTagCloseHsmCase = $('#hiddenTagCloseHsmCase');
	$listboxTagsForHsmClose = $('#listboxTagsForHsmClose');

	let selectedTags = null;
	if (casesSettings.TagOnAutoCloseHsmCases > 0 &&
		typeof (casesSettingsTagOnHsmClose) === 'object' &&
		casesSettingsTagOnHsmClose !== null) {
		selectedTags = [];
		selectedTags.push(casesSettingsTagOnHsmClose);
	}

	var $textboxTagOnHsmCases = $('#textboxTagOnHsmCases');
	$textboxTagOnHsmCases.tokenInput("../Reports/Cases.aspx/SearchTags", {
		method: 'POST',
		queryParam: 'q',
		searchDelay: 300,
		minChars: 3,
		tokenLimit: 1,
		propertyToSearch: 'FullName',
		//jsonContainer: 'd',
		preventDuplicates: true,
		tokenValue: 'ID',
		contentType: 'json',
		resultsLimit: 20,
		excludeCurrent: true,
		styles: {
			dropdown: {
				'max-height': '200px',
				'overflow-y': 'auto'
			},
			tokenList: {
				'width': '100%'
			}
		},
		prePopulate: selectedTags,
		onSend: function (params) {
			params.data = JSON.stringify(params.data);
			params.contentType = "application/json; charset=utf-8";
		},
		onResult: function (results, query, cache_key) {
			var settingsTags = $textboxTagOnHsmCases.data('settings');
			if (typeof (settingsTags.local_data) === 'undefined') {
				delete settingsTags.url;
				settingsTags.local_data = results.d;

				return $.grep(settingsTags.local_data, function (row) {
					return row[settingsTags.propertyToSearch].toLowerCase().indexOf(query.toLowerCase()) > -1;
				});
			}

			return results;
		}
	});
}

function i18nLoaded() {
	if ($listboxFlowShareEnqueuedMessagesFromQueues !== 'undefined') {
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_enqueued_messages-all_queues"));
		$listboxFlowShareConnectedAgentsFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_connected_agents-all_queues"));
	}

	if ($listboxFlowShareWithServices !== 'undefined') {
		$listboxFlowShareWithServices.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_with_services-all_services"));
	}

	if ($listboxInactivityDetectedConnection !== 'undefined') {
		$listboxInactivityDetectedConnection.multiselect('option', 'noneSelectedText', $.i18n("globals-email_default"));
	}

	var $textboxDelayBetweenReplies = $('#textboxDelayBetweenReplies');
	$textboxDelayBetweenReplies.trigger('change');

	var $textboxDelayAfterMultimedia = $('#textboxDelayAfterMultimedia');
	$textboxDelayAfterMultimedia.trigger('change');

	$selectHSMAllowedUses.multiselect('option', 'noneSelectedText', $.i18n('configuration-serviceswhatsapp-template-allowed_uses-select'));
}

function ValidateWhatsappMaxSizeAttachment(sender, e) {
	e.IsValid = true;

	if (!$checkboxWhatsappAllowToSendMedia.is(':checked'))
		return;

	var $textbox = $('input[id$=textboxWhatsappMaxSizeAttachment]', $divWhatsappServiceMedia);
	var value = $textbox.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(value)) {
		e.IsValid = false;
		return;
	}

	value = parseInt(value, 10);

	if (value < 1 || value > 3) {
		e.IsValid = false;
		return;
	}
}

function ValidateWhatsAppMultimediaOptions(sender, e) {
	e.IsValid = true;

	if (!$checkboxWhatsappAllowToSendMedia.is(':checked'))
		return;

	var $checkboxWhatsappAcceptedTypeImages = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeImages]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeText = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeText]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeAudio = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeAudio]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypePDF = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypePDF]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeWord = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeWord]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeExcel = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeExcel]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypePPT = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypePPT]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeZip = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeZip]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeAllFiles = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeAllFiles]', $divWhatsappServiceMedia);
	var $checkboxWhatsappAcceptedTypeVideo = $('input[type=checkbox][id$=checkboxWhatsappAcceptedTypeVideo]', $divWhatsappServiceMedia);

	if (!$checkboxWhatsappAcceptedTypeImages.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeText.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeAudio.is(':checked') &&
		!$checkboxWhatsappAcceptedTypePDF.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeWord.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeExcel.is(':checked') &&
		!$checkboxWhatsappAcceptedTypePPT.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeZip.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeAllFiles.is(':checked') &&
		!$checkboxWhatsappAcceptedTypeVideo.is(':checked')) {
		e.IsValid = false;
		return;
	}
}

function ReloadFlows() {
	$('span.fa', $anchorFlowsReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.WhatsApp,
		function (flows) {
			if (flows !== null && flows.length > 0) {
				var currentFlow = $hiddenFlow.val();
				if (currentFlow !== null && currentFlow.length > 0) {
					currentFlow = JSON.parse(currentFlow);
				}
				else {
					currentFlow = null;
				}

				$selectFlowToUse.empty();
				for (var i = 0; i < flows.length; i++) {
					var $option = $('<option />');
					$option.val(flows[i].id);
					$option.text(flows[i].name);
					$option.prop('definition', flows[i]);
					$selectFlowToUse.append($option);
				}

				if (currentFlow !== null) {
					if (typeof (currentFlow.id) !== 'undefined') {
						$selectFlowToUse.val(currentFlow.id);
					}
					else {
						$selectFlowToUse.val(currentFlow.ID);
					}
				}
				$selectFlowToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('Flow', $.i18n("configuration-serviceswhatsapp-no_flows"));
				$dropdownlistUseYFlow.val('false').trigger('change');
				$tabsWhatsapp.tabs('select', 'divBasicConfiguration');
				$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
			}

			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow: %o', err);
			AlertDialog('Flow', $.i18n("configuration-serviceswhatsapp-flow_list_error"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsWhatsapp.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
			$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
		}
	);
}

function ReloadFlowsContingency() {
	$('span.fa', $anchorFlowsContingencyReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.Telegram,
		function (flowsContingency) {
			if (flowsContingency !== null && flowsContingency.length > 0) {
				var currentFlowContingency = $hiddenFlowContingency.val();
				if (currentFlowContingency !== null && currentFlowContingency.length > 0) {
					currentFlowContingency = JSON.parse(currentFlowContingency);
				}
				else {
					currentFlowContingency = null;
				}

				$selectFlowContingencyToUse.empty();
				for (var i = 0; i < flowsContingency.length; i++) {
					var $option = $('<option />');
					$option.val(flowsContingency[i].id);
					$option.text(flowsContingency[i].name);
					$option.prop('definition', flowsContingency[i]);
					$selectFlowContingencyToUse.append($option);
				}

				if (currentFlowContingency !== null) {
					if (typeof (currentFlowContingency.id) !== 'undefined') {
						$selectFlowContingencyToUse.val(currentFlowContingency.id);
					}
					else {
						$selectFlowContingencyToUse.val(currentFlowContingency.ID);
					}
				}
				$selectFlowContingencyToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('FlowContingency', $.i18n("configuration-serviceswhatsapp-no_flows"));
				$dropdownlistUseYFlowContingency.val('false').trigger('change');
				$tabsTelegram.tabs('select', 'divBasicConfiguration');
			}

			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow Contindencia: %o', err);
			AlertDialog('Flow', $.i18n("configuration-serviceswhatsapp-flow_list_error"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsTelegram.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		}
	);
}

function AddFlowQueueTransfersByKeyRow(e, data) {
	var $tbody = $('tbody', $tableFlowQueueTransfersByKey);

	var $lastTr = $("tr:last-child", $tbody);
	var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
	var $newTr = $('<tr class="' + newTrClass + '"><td style="text-align: center"><a rel="remove"><span class="fa fa-lg fa-minus-square"></span></a></td><td><input type="text" rel="key" class="inputtextmono" spellcheck="false" style="width:95%"/></td><td class="data"><select rel="queue" style="width: 95%";></select></td></tr>');

	var $inputKey = $('input[rel=key]', $newTr);
	var $selectQueue = $('select[rel=queue]', $newTr);
	for (var i = 0; i < queues.length; i++) {
		var $option = $('<option value="' + queues[i].ID + '">' + queues[i].Name + '</option>');
		$selectQueue.append($option);
	}

	$tbody.append($newTr);

	if (typeof (data) !== 'undefined' && data !== null) {
		$inputKey.val(data.Key);
		$selectQueue.val(data.QueueID);
	}

	$selectQueue.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' }).multiselectfilter();

	var $anchorRemove = $('a[rel=remove]', $newTr);
	$anchorRemove.click(function () {
		var $tr = $(this).parent().parent();
		$tr.remove();

		var $trs = $('tr', $tbody);
		if ($trs.length > 0) {
			for (var i = 0; i < $trs.length; i++) {
				var $tr = $($trs.get(i));
				$tr.removeClass('normal alternate');
				$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
			}
		}
	});
}

function ValidateFlowQueueTransfersByKey(sender, e) {
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		if ($dropdownlistUseYFlow.val() === 'false') {
			$hiddenFlowQueueTransfersByKey.val('');
			e.IsValid = true;
			return;
		}

		var $tbody = $('tbody', $tableFlowQueueTransfersByKey);
		var $trs = $('tr', $tbody);

		var transfers = [];

		for (var i = 0; i < $trs.length; i++) {
			var $tr = $($trs.get(i));
			var $inputKey = $('input[rel=key]', $tr);
			var $selectQueue = $('select[rel=queue]', $tr);

			var key = $inputKey.val();
			if (key.length == 0) {
				$(sender).text($.i18n("configuration-serviceswhatsapp-enter_key", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			var regex = /^[a-zA-Z0-9_]+$/;
			if (!regex.test(key)) {
				$(sender).text($.i18n("configuration-serviceswhatsapp-key_regex", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			let queueId = parseInt($selectQueue.val(), 10);
			if (isNaN(queueId)) {
				$(sender).text($.i18n("configuration-services-common-yflow-flow_queue_transfers-invalid_queue", (i + 1).toString(), key));
				e.IsValid = false;
				return;
			}

			var transfer = {
				Key: key,
				QueueID: queueId
			};
			transfers.push(transfer);
		}

		if (transfers.length === 0) {
			$hiddenFlowQueueTransfersByKey.val('');
		}
		else {
			$hiddenFlowQueueTransfersByKey.val(JSON.stringify(transfers));
		}
	}

	e.IsValid = true;
}

function ValidateIntegrationType1(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "1") {
		return;
	}

	var $textboxIntegrationType1ConnectionURL = $('#textboxIntegrationType1ConnectionURL');
	var url = $textboxIntegrationType1ConnectionURL.val();

	if (url.length === 0) {
		e.IsValid = false;
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}(\.[a-z]{2,6})?\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;

	if (!urlRegex.test(url)) {
		e.IsValid = false;
		return;
	}

	var $textboxIntegrationType1ConnectionPort = $('#textboxIntegrationType1ConnectionPort');
	var port = $textboxIntegrationType1ConnectionPort.val();

	if (port === null || port.length === 0 || !$.isNumeric(port)) {
		e.IsValid = false;
		return;
	}

	if (!/^[0-9]{3,5}$/.test(port)) {
		e.IsValid = false;
		return;
	}

	port = parseInt(port);
	if (isNaN(port) || port < 0 || port > 99999) {
		e.IsValid = false;
		return;
	}
}

function ValidateIntegrationType2(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "2") {
		return;
	}

	var $textboxIntegrationType2AccessToken = $('#textboxIntegrationType2AccessToken');
	var $textboxIntegrationType2Secret = $('#textboxIntegrationType2Secret');
	var $textboxIntegrationType2RefreshToken = $('#textboxIntegrationType2RefreshToken');

	var accessToken = $textboxIntegrationType2AccessToken.val();
	var secret = $textboxIntegrationType2Secret.val();
	var refreshToken = $textboxIntegrationType2RefreshToken.val();

	if (accessToken === null ||
		accessToken.length === 0 ||
		/*secret === null ||
		secret.length === 0 || */
		refreshToken === null ||
		refreshToken.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateEndpoint(info) {
	if (info === null) {
		return false;
	}

	if (typeof (info.valid) === 'boolean' && !info.valid) {
		return false;
	}

	var postbackUrl = info.url;
	var hashKey = info.hashKey;

	if (postbackUrl === null ||
		postbackUrl.length === 0 ||
		hashKey === null ||
		hashKey.length < 10) {
		return false;
	}

	var urlRegex = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:127|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^(?:(?:(?:https|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:127|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/;
	}

	if (!urlRegex.test(postbackUrl)) {
		if (!postbackUrl.startsWith('http://localhost')) {
			return false;
		}
	}

	var hashKeyRegex = /^[a-zA-Z0-9]{10,}$/;
	if (!hashKeyRegex.test(hashKey)) {
		return false;
	}

	return true;
}

function ValidateIntegrationType3(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "3") {
		return;
	}

	var info = $divIntegrationType3ReplyEndpoint.getInfo();
	if (!ValidateEndpoint(info)) {
		$divIntegrationType3ReplyEndpoint.addClass('invalid');
		e.IsValid = false;
		return;
	}

	$divIntegrationType3ReplyEndpoint.removeClass('invalid');

	var pullType = parseInt($dropdownlistIntegrationType3PullType.val(), 10);
	if (pullType === 2) {
		info = $divIntegrationType3GetNewsEndpoint.getInfo();
		if (!ValidateEndpoint(info)) {
			$divIntegrationType3GetNewsEndpoint.addClass('invalid');
			e.IsValid = false;
			return;
		}
		$divIntegrationType3GetNewsEndpoint.removeClass('invalid');

		info = $divIntegrationType3PostNewsProcessedEndpoint.getInfo();
		if (!ValidateEndpoint(info)) {
			$divIntegrationType3PostNewsProcessedEndpoint.addClass('invalid');
			e.IsValid = false;
			return;
		}
		$divIntegrationType3PostNewsProcessedEndpoint.removeClass('invalid');
	}

	var notifyClosedCases = parseInt($dropdownlistIntegrationType3NotifyClosedCases.val(), 10) === 1;
	if (notifyClosedCases) {
		info = $divIntegrationType3CloseCaseEndpoint.getInfo();
		if (!ValidateEndpoint(info)) {
			$divIntegrationType3CloseCaseEndpoint.addClass('invalid');
			e.IsValid = false;
			return;
		}
		$divIntegrationType3CloseCaseEndpoint.removeClass('invalid');
	}

	if (typeof (allowAgentsToStartVoiceCallOnPostback) === 'boolean' &&
		allowAgentsToStartVoiceCallOnPostback) {
		var info = $divIntegrationType3VoiceCallsEndpoint.getInfo();
		if (!ValidateEndpoint(info)) {
			$divIntegrationType3VoiceCallsEndpoint.addClass('invalid');
			e.IsValid = false;
			return;
		}
	}
}

function ValidateIntegrationType3PayloadTypeObject(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "3") {
		return;
	}

	value = $dropdownlistIntegrationType3PayloadType.val();
	if (value === '0') {
		return;
	}

	var props = $tableIntegrationType3PayloadTypeObjectProps.getValues();
	if (props.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-empty"));
		e.IsValid = false;
		return;
	}

	var propertynameRegex = /^(?![0-9])[a-zA-Z0-9$_]+$/;
	for (var i = 0; i < props.length; i++) {
		if (props[i].name.length === 0 ||
			!propertynameRegex.test(props[i].name)) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_invalid_name", i + 1));
			e.IsValid = false;
			return;
		}

		if (i > 0) {
			for (var j = 0; j < i; j++) {
				if (props[i].name === props[j].name) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_duplicated_name", i + 1));
					e.IsValid = false;
					return;
				}

				if (props[i].useForDerivation &&
					props[i].useForDerivation === props[j].useForDerivation) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_duplicated_use_for_derivation", props[i].name));
					e.IsValid = false;
					return;
				}
			}
		}

		if (props[i].description.length === 0 ||
			props[i].description.trim().length === 0) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_invalid_desc", i + 1));
			e.IsValid = false;
			return;
		}
	}

	$hiddenIntegrationType3PayloadTypeObject.val(JSON.stringify(props));
}

function ValidateIntegrationType3Derivations(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "3") {
		return;
	}

	value = $dropdownlistIntegrationType3PayloadType.val();
	if (value === '0') {
		return;
	}

	var $selectsUseForQueue = $('input[rel=useForDerivation]', $tableIntegrationType3PayloadTypeObjectProps);
	var showDerivations = false;
	for (var i = 0; i < $selectsUseForQueue.length; i++) {
		if ($($selectsUseForQueue.get(i)).is(':checked')) {
			showDerivations = true;
			break;
		}
	}

	if (!showDerivations) {
		return;
	}

	var props = $tableIntegrationType3Derivations.getValues();
	if (props.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-derivations-fields-empty"));
		e.IsValid = false;
		return;
	}

	var regex = /^[a-zA-Z0-9_]+$/;
	var info = {};
	for (var i = 0; i < props.length; i++) {
		if (props[i].key.length === 0 ||
			!regex.test(props[i].key)) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-derivations-fields-record_invalid_key", i + 1));
			e.IsValid = false;
			return;
		}

		if (i > 0) {
			for (var j = 0; j < i; j++) {
				if (props[i].key === props[j].key) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-derivations-fields-record_duplicated_key", i + 1));
					e.IsValid = false;
					return;
				}
			}
		}

		info[props[i].key] = props[i].queue;
	}

	$hiddenIntegrationType3Derivations.val(JSON.stringify(info));
}

function ValidateIntegrationType4(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "4") {
		return;
	}

	var $dropdownlistIntegrationType4AuthorizationType = $('#dropdownlistIntegrationType4AuthorizationType');
	var authorizationType = parseInt($dropdownlistIntegrationType4AuthorizationType.val(), 10);

	if (authorizationType === 1) {
		var $textboxIntegrationType4User = $('#textboxIntegrationType4User');
		var $textboxIntegrationType4Password = $('#textboxIntegrationType4Password');

		var user = $textboxIntegrationType4User.val().trim();
		var password = $textboxIntegrationType4Password.val().trim();

		if (user === null ||
			user.length === 0 ||
			password === null ||
			password.length === 0) {
			e.IsValid = false;
			return;
		}
	}
	else if (authorizationType === 2) {
		var $textboxIntegrationType4ApiKey = $('#textboxIntegrationType4ApiKey');
		var apiKey = $textboxIntegrationType4ApiKey.val().trim();

		if (apiKey === null ||
			apiKey.length === 0) {
			e.IsValid = false;
			return;
		}
	}

	var $textboxIntegrationType4BaseUrl = $('#textboxIntegrationType4BaseUrl');
	var baseUrl = $textboxIntegrationType4BaseUrl.val();

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('http://localhost')) {
			e.IsValid = false;
			return;
		}
	}
}

function ValidateIntegrationType5(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "5") {
		return;
	}

	var $textboxIntegrationType5Username = $('#textboxIntegrationType5Username');
	var $textboxIntegrationType5Token = $('#textboxIntegrationType5Token');

	var user = $textboxIntegrationType5Username.val().trim();
	var token = $textboxIntegrationType5Token.val().trim();

	if (user === null ||
		user.length === 0 ||
		token === null ||
		token.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateIntegrationType6(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "6") {
		return;
	}

	var clientId = $textboxIntegrationType6ClientID.val().trim();
	var clientSecret = $textboxIntegrationType6ClientSecret.val().trim();
	var baseUrl = $textboxIntegrationType6UrlBase.val();
	var channelId = $textboxIntegrationType6ChannelID.val().trim();
	var fromId = $textboxIntegrationType6FromID.val().trim();
	var fromName = $textboxIntegrationType6FromName.val().trim();

	if (clientId === null ||
		clientId.length === 0 ||
		clientSecret === null ||
		clientSecret.length === 0 ||
		baseUrl === null ||
		baseUrl.length === 0 ||
		channelId === null ||
		channelId.length === 0 ||
		fromId === null ||
		fromId.length === 0 ||
		fromName === null ||
		fromName.length === 0) {
		e.IsValid = false;
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('http://localhost')) {
			e.IsValid = false;
			return;
		}
	}
}

function ValidateIntegrationType6PayloadTypeObject(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "6") {
		return;
	}

	value = $dropdownlistIntegrationType6PayloadType.val();
	if (value === '0') {
		return;
	}

	var props = $tableIntegrationType6PayloadTypeObjectProps.getValues();
	if (props.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-empty"));
		e.IsValid = false;
		return;
	}

	var propertynameRegex = /^(?![0-9])[a-zA-Z0-9$_]+$/;
	for (var i = 0; i < props.length; i++) {
		if (props[i].name.length === 0 ||
			!propertynameRegex.test(props[i].name)) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_invalid_name", i + 1));
			e.IsValid = false;
			return;
		}

		if (i > 0) {
			for (var j = 0; j < i; j++) {
				if (props[i].name === props[j].name) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_duplicated_name", i + 1));
					e.IsValid = false;
					return;
				}
			}
		}

		if (props[i].description.length === 0 ||
			props[i].description.trim().length === 0) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-postback-more_settings-payload-fields-record_invalid_desc", i + 1));
			e.IsValid = false;
			return;
		}
	}

	$hiddenIntegrationType6PayloadTypeObject.val(JSON.stringify(props));
}

function ValidateNotifications(sender, e, $textboxSubject, $textboxEmails, $textboxTemplate, integrationType) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (Array.isArray(integrationType)) {
		if (integrationType.indexOf(value) === -1) {
			return;
		}
	}
	else {
		if (value !== integrationType) {
			return;
		}
	}

	var subject = $textboxSubject.val().trim();
	var emails = $textboxEmails.val().trim();
	var template = $textboxTemplate.val().trim();

	if (subject.length === 0) {
		e.IsValid = false;
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_subject"));
		return;
	}

	if (emails.length === 0) {
		e.IsValid = false;
		$(sender).text($.i18n("configuration-serviceswhatsapp-mail_recepients"));
		return;
	}

	var emailsRegex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*([,]\s*\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)*$/;
	if (!emailsRegex.test(emails)) {
		e.IsValid = false;
		$(sender).text($.i18n("configuration-serviceswhatsapp-invalid_mail_recepients_format"));
		return;
	}

	if (template.length === 0) {
		e.IsValid = false;
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_template"));
		return;
	}
}

function ValidateAutoReplyBeforeMaxTimeToAnswer(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value === "1") {
		return;
	}

	if (!$checkboxAutoReplyBeforeMaxTimeToAnswer.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeMaxTimeToAnswerText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeMaxTimeToAnswerMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_time"));
		e.IsValid = false;
		return;
	}

	minutes = parseInt(minutes);
	if (minutes < 0 || minutes > maxMinutesToAnswerMessages) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-max_time", maxMinutesToAnswerMessages));
		e.IsValid = false;
		return;
	}
}

function ValidateAutoReplyBeforeCloseCase(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyBeforeCloseCase.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeCloseCaseText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeCloseCaseMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_time"));
		e.IsValid = false;
		return;
	}

	let minutesToCloseCases = casesSettings.MaxElapsedMinutesToCloseCases;
	if ($checkboxCasesOverrideSystemSettings.is(':checked')) {
		let maxElapsedMinutesToCloseCases = $textboxMaxElapsedMinutesToCloseCases.val();
		if (maxElapsedMinutesToCloseCases.length === 0) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		if (!/^\d{1,5}$/.test(maxElapsedMinutesToCloseCases)) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		maxElapsedMinutesToCloseCases = parseInt(maxElapsedMinutesToCloseCases, 10);
		if (isNaN(maxElapsedMinutesToCloseCases) ||
			maxElapsedMinutesToCloseCases < 1 ||
			maxElapsedMinutesToCloseCases > 43200) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		minutesToCloseCases = maxElapsedMinutesToCloseCases;
	}

	if (minutes == 0 || minutes >= minutesToCloseCases) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_valid_time", minutesToCloseCases));
		e.IsValid = false;
		return;
	}
}

function ValidateHsmCloseCases(sender, e) {
	if ($checkboxCasesOverrideSystemSettings.is(':checked')) {
		let maxElapsedMinutesToCloseHsmCases = $textboxMaxElapsedMinutesToCloseHsmCases.val();
		let regex = /^\d{1,1440}$/;
		if (maxElapsedMinutesToCloseHsmCases.length === 0 || !$.isNumeric(maxElapsedMinutesToCloseHsmCases) || !regex.test(maxElapsedMinutesToCloseHsmCases)) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-enter_time"));
			e.IsValid = false;
			return;
		}

		maxElapsedMinutesToCloseHsmCases = parseInt(maxElapsedMinutesToCloseHsmCases, 10);
		if (isNaN(maxElapsedMinutesToCloseHsmCases) ||
			maxElapsedMinutesToCloseHsmCases < 1 ||
			maxElapsedMinutesToCloseHsmCases > 1440) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-hsm-cases')));
			e.IsValid = false;
			return;
		}
	}

	e.IsValid = true;
}

var hsmTemplatesParsed = false;
function ParseHSMTemplates() {
	if (hsmTemplatesParsed) {
		return;
	}

	if (typeof ($hiddenHSMTemplates) === 'undefined') {
		$hiddenHSMTemplates = $('#hiddenHSMTemplates');
	}

	let templatesText = $hiddenHSMTemplates.val();
	if (typeof (templatesText) === 'string' && templatesText.length > 0) {
		templates = JSON.parse(templatesText);
		templates = templates.sort((x, y) => {
			if (x.Description.toLowerCase() < y.Description.toLowerCase()) { return -1; }
			if (x.Description.toLowerCase() > y.Description.toLowerCase()) { return 1; }
			return 0;
		});
	}

	hsmTemplatesParsed = true;
}

var flowsParsed = false;
function ParseMetaFlows() {
	if (flowsParsed) {
		return;
	}

	if (typeof ($hiddenFlows) === 'undefined') {
		$hiddenFlows = $('#hiddenFlows');
	}

	let flowsText = $hiddenFlows.val();
	if (typeof (flowsText) === 'string' && flowsText.length > 0) {

		metaFlows = JSON.parse(flowsText);
		const convertedFlows = metaFlows.map(flow => {
			const newFlow = { ...flow };
			newFlow.Categories = flow.Categories.map(category => {
				const convertedCategory = ConvertToStringCategory(category);
				return convertedCategory || '';
			});

			return newFlow;
		});
		metaFlows = convertedFlows;
	}

	flowsParsed = true;
}

function CreateHsmTable() {
	if (typeof (allowWhatsappOutbound) === 'boolean' && !allowWhatsappOutbound) {
		console.log('No hay licencia para HSM');
		return;
	}

	if (typeof ($hiddenHSMTemplates) === 'undefined') {
		$hiddenHSMTemplates = $('#hiddenHSMTemplates');
	}

	if (typeof ($messageHSMTemplatesEmpty) === 'undefined') {
		$messageHSMTemplatesEmpty = $('#messageHSMTemplatesEmpty');
	}

	if (typeof ($tableHSMTemplatesv2) === 'undefined') {
		$tableHSMTemplatesv2 = $('#tableHSMTemplatesv2');
	}

	ParseHSMTemplates();

	if (datatableTemplates !== null &&
		typeof (datatableTemplates.destroy) === 'function') {
		datatableTemplates.destroy();
		datatableTemplates = null;
	}

	datatableTemplates = $tableHSMTemplatesv2.DataTable({
		ordering: false,
		searching: true,
		paging: true,
		pageLength: 15,
		lengthChange: false,
		deferRender: true,
		data: templates,
		createdRow: function (row, data, dataIndex) {
			let $tr = $(row);
			let template = data;
			$tr.prop('template', template);
			$tr.addClass('template');

			if (typeof (template.Exists) === 'boolean') {
				$tr.addClass(template.Exists ? 'exists' : 'not-exists');
			}

			if (typeof (template.Status) === 'string' && template.Status.length > 0) {
				$tr.attr('status', template.Status);
			}
		},
		columns: [
			{
				render: function (data, type, template) {
					let language = template.Language;
					let elementName = template.ElementName;
					let category = template.Category?.toLowerCase() ?? '';

					if (translationsLoaded) {
						language = $.i18n('configuration-serviceswhatsapp-template-lenguage-' + template.Language);
					}
					
					let span = `<b>${elementName}</b> - ${language}`;
					if (typeof (category) === 'string' && category.length > 0) {
						span += ` <br /> <div class="category"><span class="fa fa-list"></span class="info">${category}</span></div>`;
					}
					return span;
				},
				defaultContent: ''
			},
			{
				render: function (data, type, template) {
					let html = '';

					html += '<div class="not-exists"><span class="fa fa-lg fa-exclamation-triangle"></span class="info" data-i18n="configuration-serviceswhatsapp-template-not_exists">No existe</span></div>';
					html += '<div class="exists approved"><span class="fas fa-circle"></span class="info" data-i18n="configuration-serviceswhatsapp-template-approved">Aprobado</span></div>';
					html += '<div class="exists rejected"><span class="fas fa-circle"></span class="info" data-i18n="configuration-serviceswhatsapp-template-rejected">Rechazado</span></div>';

					return html;
				},
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					$td.attr('align', 'center');
				},
				defaultContent: ''
			},
			{
				render: function (data, type, template) {
					return '<span rel="media" class="fa fa-lg ' + ((template.HeaderType === HSMTemplateHeaderTypes.Media) ? "fa-yes" : "fa-no") + '"></span>';
				},
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					$td.addClass('icons');
				},
				defaultContent: ''
			},
			{
				render: function (data, type, template) {
					let html = '';
					html += template.AvaiableForAgents ? '<a class="action" rel="availablefor"><span class="fa fa-lg fa-user" data-i18n-title="configuration-serviceswhatsapp-template-avail_for_agents"></span></a>' : ""; 
					html += template.AvaiableForSupervisors ? '<a class="action" rel="availablefor"><span class="fa fa-lg fa-eye" data-i18n-title="configuration-serviceswhatsapp-template-avail_for_supervisors"></span></a>' : ""; 
					html += template.AvaiableForIntegrations ? '<a class="action" rel="availablefor"><span class="fa fa-lg fa-cloud" data-i18n-title="configuration-serviceswhatsapp-template-avail_for_integrations"></span></a>' : ""; 
					
					return html;
				},
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					$td.addClass('icons hiddenAsGateway');
				},
				defaultContent: ''
			},
			{
				render: function (data, type, template) {
					return '<span rel="allowToConfigureSendHSMIfCaseOpen" class="fa fa-lg ' + (template.AllowToConfigureSendHSMIfCaseOpen ? "fa-yes" : "fa-no") + '"></span>';
				},
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					$td.addClass('icons');
				},
				defaultContent: ''
			},
			{
				data: null,
				className: 'icons',
				width: '100px',
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					let $tr = $td.parent();

					let template = cellData;

					let $anchorEdit = $('a[rel=edit]', $td);

					$anchorEdit.click({
						$tr: $tr
					}, function (e) {
						let $tr = e.data.$tr;
						let rowIndex = datatableTemplates.row($tr).index();
						let template = $tr.prop('template');
						ShowHSMDialog(template, $tr, rowIndex);
					});

					let $anchorDelete = $('a[rel=delete]', $td);
					$anchorDelete.click({
						$tr: $tr,
						template: template
					}, function (e) {
						let $tr = e.data.$tr;
						let template = e.data.template;
						let rowIndex = datatableTemplates.row($tr).index();

						ConfirmDialog({
							title: $.i18n('configuration-serviceswhatsapp-message_template-title'),
							message: $.i18n('configuration-serviceswhatsapp-message_template-delete-question', template.Description),
							onAccept: function (args) {
								let $tr = args.$tr;
								let template = args.template;
								let rowIndex = datatableTemplates.row($tr).index();

								datatableTemplates.row(rowIndex).remove().draw();
								templates.splice(rowIndex, 1);

								if (templates.length === 0) {
									$messageHSMTemplatesEmpty.show();
									$tableHSMTemplatesv2.parent().hide();
								}

								$.colorbox.close();
							},
							acceptArguments: e.data
						});
					});
				},
				render: function (data, type, agent) {
					let html = '';

					html += '<a class="action" rel="edit"><span class="fa fa-lg fa-edit" data-i18n-title="globals-edit"></span></a>';
					html += '<a class="action" rel="delete"><span class="fa fa-lg fa-trash" data-i18n-title="globals-delete"></span></a>';

					return html;
				}
			}
		],
		initComplete: function (settings, json) {
			let $tableHSMTemplatesv2_filter = $('#tableHSMTemplatesv2_filter');
			let $input = $('input[type=search]', $tableHSMTemplatesv2_filter);
			$input.addClass('inputtext');
			$input.css('margin-bottom', '5px');
		},
		drawCallback: function () {
			LoadCompositedElements();
		}
	});

	datatableTemplates.column(3).visible($dropdownlistIntegrationType.val() === '10' || $dropdownlistIntegrationType.val() === '11' || $dropdownlistIntegrationType.val() === '4');

	if (templates.length > 0) {
		$messageHSMTemplatesEmpty.hide();
		$tableHSMTemplatesv2.parent().show();
	}
	else {
		$messageHSMTemplatesEmpty.show();
		$tableHSMTemplatesv2.parent().hide();
	}
}

var currentTemplate;
var currentTemplateIndex;
var current$Tr;

function ShowHSMDialog(template, $tr, index) {
	if (typeof (template) === 'undefined') {
		template = null;
	}
	if (typeof ($tr) === 'undefined') {
		$tr = null;
	}

	current$Tr = $tr;
	currentTemplate = template;
	currentTemplateIndex = index;

	ToggleValidator($divHSMError, true);
	$divHSMButtonsTypeCallToActionContainer.empty();
	$divHSMButtonsTypeAuthCodeContainer.empty();
	$divHSMButtonsTypeQuickReplyContainer.empty();
	$divHSMButtonsTypeMixedContainer.empty();
	$divHSMParametersContainer.empty();
	$textareaHSMTemplate.unbind('blur');

	var $tableParameters = BuildDynamicTable({
		showHeaders: true,
		container: $divHSMParametersContainer,
		columns: [
			{
				header: {
					title: 'Nombre del parámetro',
					'data-i18n': "configuration-serviceswhatsapp-parameter_name"
				},
				type: 'text',
				key: 'Name',
				placeholder: 'Nombre del parámetro',
				'data-i18n-placeholder': 'configuration-serviceswhatsapp-parameter_name'
			},
			{
				header: {
					title: 'Descripción del parámetro',
					'data-i18n': 'configuration-serviceswhatsapp-parameter_description'
				},
				key: 'Description',
				type: 'text',
				placeholder: 'Descripción del parámetro',
				'data-i18n-placeholder': 'configuration-serviceswhatsapp-parameter_description'
			}
		]
	});

	$divHSMParametersContainer.prop('table', $tableParameters);

	var $tableButtonsCallToAction = BuildDynamicTable({
		showHeaders: true,
		container: $divHSMButtonsTypeCallToActionContainer,
		maxRows: maxCallToActionButtons,
		columns: [{
			header: {
				title: 'Tipo de acción',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type"
			},
			type: 'select',
			key: 'CallToActionButtonType',
			rel: 'CallToActionButtonType',
			options: [
				{
					title: 'Visitar sitio web',
					value: HSMTemplateCallToActionButtonTypes.Url,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url'
				},
				{
					title: 'Llamar a número de teléfono',
					value: HSMTemplateCallToActionButtonTypes.Call,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-call'
				},
				{
					title: 'Copiar código de oferta',
					value: HSMTemplateCallToActionButtonTypes.OfferCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-copy_code'
				},
				{
					title: 'Enviar Flow',
					value: HSMTemplateCallToActionButtonTypes.Flow,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-flow'
				}
			]
		}, {
			header: {
				title: 'Texto del botón',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-text"
			},
			type: 'text',
			key: 'Text',
			maxLength: 20,
			visibleIfColumn: [
				{
				key: 'CallToActionButtonType',
				value: HSMTemplateCallToActionButtonTypes.Flow,
				visibleIfColumnNot: true
				}
			]
		}, {
			header: {
				title: 'Tipo de URL',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type"
			},
			type: 'select',
			key: 'UrlButtonType',
			rel: 'UrlButtonType',
			visibleIfColumn: {
				key: 'CallToActionButtonType',
				value: HSMTemplateCallToActionButtonTypes.Url
			},
			options: [
				{
					title: 'Dinámica',
					value: HSMTemplateCallToActionUrlButtonTypes.Dynamic,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-dynamic'
				}, {
					title: 'Fija',
					value: HSMTemplateCallToActionUrlButtonTypes.Fixed,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-fixed'
				}
			]
		}, {
			header: {
				title: 'Nombre parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_name"
			},
			type: 'text',
			key: 'Parameter_Name',
			visibleIfColumn: [
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Call,
					visibleIfColumnNot: true
				},
				{
					key: 'UrlButtonType',
					value: HSMTemplateCallToActionUrlButtonTypes.Dynamic
				}
			]
		}, {
			header: {
				title: 'Descripción parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_description"
			},
			type: 'text',
			key: 'Parameter_Desc',
			visibleIfColumn: [
				{
				key: 'CallToActionButtonType',
				value: HSMTemplateCallToActionButtonTypes.Call,
				visibleIfColumnNot: true
				},
				{
				key: 'UrlButtonType',
				value: HSMTemplateCallToActionUrlButtonTypes.Dynamic
				},
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Flow,
					visibleIfColumnNot: true
				}
			]
		}]
	});

	$divHSMButtonsTypeCallToActionContainer.prop('table', $tableButtonsCallToAction);

	var $tableButtonsAuthCode = BuildDynamicTable({
		showHeaders: true,
		container: $divHSMButtonsTypeAuthCodeContainer,
		maxRows: maxAuthCodeButtons,
		columns: [{
			header: {
				title: 'Tipo de autenticacion',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type"
			},
			type: 'select',
			key: 'AuthCodeButtonType',
			rel: 'AuthCodeButtonType',
			options: [
				{
					title: 'Copiar código de autenticacion',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-auth_code_type-copy_code'
				}
			]
		}, {
			header: {
				title: 'Texto del botón',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-text"
			},
			type: 'text',
			key: 'Text',
			maxLength: 20,
			visibleIfColumn: [
				{
					key: 'AuthCodeButtonType',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					
				}
			]
		}, {
			header: {
				title: 'Tipo de Codigo Autenticacion',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-auth_code-type"
			},
			type: 'select',
			key: 'AuthCodeButtonType',
			rel: 'AuthCodeButtonType',
			visibleIfColumn: {
				key: 'AuthCodeButtonType',
				value: HSMTemplateAuthCodeButtonTypes.AuthCode
			},
			options: [
				{
					title: 'Codigo autenticacion',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-auth_code_button'
				}
			]
		}, {
			header: {
				title: 'Nombre parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_name"
			},
			type: 'text',
			key: 'Parameter_Name',
			visibleIfColumn: [
				{
					key: 'AuthCodeButtonType',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					
				}
			]
		}, {
			header: {
				title: 'Descripción parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_description"
			},
			type: 'text',
			key: 'Parameter_Desc',
			visibleIfColumn: [
				{
					key: 'AuthCodeButtonType',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					
				}
			]
		}]
	});
	$divHSMButtonsTypeAuthCodeContainer.prop('table', $tableButtonsAuthCode);

	var $tableButtonsQuickReply = BuildDynamicTable({
		showHeaders: true,
		container: $divHSMButtonsTypeQuickReplyContainer,
		maxRows: maxQuickReplyButtons,
		columns: [{
			header: {
				title: 'Texto del botón',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-text"
			},
			type: 'text',
			key: 'Text',
			maxLength: 20
		}, {
			header: {
				title: 'Nombre parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_name"
			},
			type: 'text',
			key: 'QuickReplyParameter_Name'
		}, {
			header: {
				title: 'Descripción parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_description"
			},
			type: 'text',
			key: 'QuickReplyParameter_Desc'
		}]
	});

	$divHSMButtonsTypeQuickReplyContainer.prop('table', $tableButtonsQuickReply);

	var $tableButtonsTypeMixed = BuildDynamicTable({
		showHeaders: true,
		container: $divHSMButtonsTypeMixedContainer,
		maxRows: maxTotalButtons,
		columns: [{
			header: {
				title: 'Texto del botón',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-text"
			},
			type: 'text',
			key: 'Text',
			maxLength: 20
		}, {
			header: {
				title: 'Tipo de boton',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type"
			},
			type: 'select',
			key: 'ButtonType',
			rel: 'ButtonType',
			options: [
				{
					title: 'Llamada a la accion',
					value: HSMTemplateButtonsTypes.CallToAction,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action'
				}, {
					title: 'Respuesta Rapida',
					value: HSMTemplateButtonsTypes.QuickReply,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-quick_reply'
				}, , {
					title: 'Codigo Autenticacion',
					value: HSMTemplateButtonsTypes.AuthCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-authcode'
				}
			]
		}, {
			header: {
				title: 'Tipo de acción',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type"
			},
			type: 'select',
			key: 'CallToActionButtonType',
			rel: 'CallToActionButtonType',
			visibleIfColumn: {
				key: 'ButtonType',
				value: HSMTemplateButtonsTypes.CallToAction
			},
			options: [
				{
					title: 'Visitar sitio web',
					value: HSMTemplateCallToActionButtonTypes.Url,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url'
				},
				{
					title: 'Llamar a número de teléfono',
					value: HSMTemplateCallToActionButtonTypes.Call,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-call'
				},
				{
					title: 'Copiar código de oferta',
					value: HSMTemplateCallToActionButtonTypes.OfferCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-copy_code'
				},
				{
					title: 'Enviar Flow',
					value: HSMTemplateCallToActionButtonTypes.Flow,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-flow'
				}]
			}, {
				header: {
				title: 'Tipo de acción',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type"
			},
			type: 'select',
			key: 'CallToActionButtonType',
			rel: 'CallToActionButtonType',
			visibleIfColumn: {
				key: 'ButtonType',
				value: HSMTemplateButtonsTypes.AuthCode
			},
			options: [
				{
					title: 'Copiar código de autenticacion',
					value: HSMTemplateAuthCodeButtonTypes.AuthCode,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-copy_code'
				}]
		}, {
			header: {
				title: 'Tipo de URL',
				'data-i18n': "configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type"
			},
			type: 'select',
			key: 'UrlButtonType',
			rel: 'UrlButtonType',
			visibleIfColumn: [
				{
					key: 'ButtonType',
					value: HSMTemplateButtonsTypes.CallToAction
				},
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Url
				}],
			options: [
				{
					title: 'Dinámica',
					value: HSMTemplateCallToActionUrlButtonTypes.Dynamic,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-dynamic'
				}, {
					title: 'Fija',
					value: HSMTemplateCallToActionUrlButtonTypes.Fixed,
					'data-i18n': 'configuration-serviceswhatsapp-template-buttons-type-call_to_action-action_type-url-type-fixed'
				}]
		}, {
			header: {
				title: 'Nombre parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_name"
			},
			type: 'text',
			key: 'ButtonParameter_Name',
			rel: 'ButtonParameter_Name',
			visibleIfColumn: [
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Call,
					visibleIfColumnNot: true
				},
				{
					key: 'UrlButtonType',
					value: HSMTemplateCallToActionUrlButtonTypes.Dynamic
				},
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Flow,
					visibleIfColumnNot: true
				}
			]
		}, {
			header: {
				title: 'Descripción parámetro',
				'data-i18n': "configuration-serviceswhatsapp-parameter_description"
			},
			type: 'text',
			key: 'ButtonParameter_Desc',
			rel: 'ButtonParameter_Desc',
			visibleIfColumn: [
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Call,
					visibleIfColumnNot: true
				},
				{
					key: 'UrlButtonType',
					value: HSMTemplateCallToActionUrlButtonTypes.Dynamic
				},
				{
					key: 'CallToActionButtonType',
					value: HSMTemplateCallToActionButtonTypes.Flow,
					visibleIfColumnNot: true
				}
			]
		}]
	});

	$divHSMButtonsTypeMixedContainer.prop('table', $tableButtonsTypeMixed);

	if (template === null) {
		$inputHSMDescription.val('');
		$selectHSMLanguage.val('es');
		$selectHSMLanguage.multiselect('resync');
		$inputHSMNamespace.val('');
		$inputHSMElementName.val('');
		$selectAllowToConfigureSendHSMIfCaseOpen.val(0).multiselect('resync').trigger('change');
		$selectHSMAllowedUses.val([1, 2, 3]);
		$selectHSMAllowedUses.multiselect('resync');
		$selectHSMHeaderType.val(0).multiselect('resync').trigger('change');
		$inputHSMHeaderTypeText.val('');
		$inputHSMHeaderTypeTextParameterName.val('');
		$inputHSMHeaderTypeTextParameterDescription.val('');
		$selectHSMHeaderTypeMedia.val(1).multiselect('resync');
		$inputHSMHeaderTypeMediaUrl.val('');
		$textareaHSMTemplate.val('');
		$selectHSMFooterType.val(0).multiselect('resync').trigger('change');
		$inputHSMFooterTypeText.val('');
		$inputHSMHeaderTypeLatitude.val('');
		$inputHSMHeaderTypeLongitude.val('');

		$textareaHSMTemplate.blur(function () {
			var template = $textareaHSMTemplate.val();
			var parameters = $tableParameters.getValuesRaw('=');
			if (parameters.length === 0 && template.length > 0) {
				var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
				var matches = parametersRegex.exec(template);
				while (matches !== null) {
					var parameterName = matches[0].replace('{{', '').replace('}}', '');
					$tableParameters.addRow([parameterName, '']);

					matches = parametersRegex.exec(template);
				}
			}
		});

		$selectHSMButtonsType.val(0).multiselect('resync').trigger('change');
	}
	else {
		$inputHSMDescription.val(template.Description);
		$selectHSMLanguage.val(template.Language).multiselect('resync');
		$inputHSMNamespace.val(template.Namespace);
		$inputHSMElementName.val(template.ElementName);
		$selectAllowToConfigureSendHSMIfCaseOpen.val(template.AllowToConfigureSendHSMIfCaseOpen ? '1' : '0').multiselect('resync').trigger('change');
		let uses = [];
		if (template.AvaiableForAgents) {
			uses.push(1);
		}
		if (template.AvaiableForSupervisors) {
			uses.push(2);
		}
		if (template.AvaiableForIntegrations) {
			uses.push(3);
		}

		$selectHSMAllowedUses.val(uses).multiselect('resync');
		$selectHSMHeaderType.val(template.HeaderType).multiselect('resync').trigger('change');
		$inputHSMHeaderTypeText.val('');
		$inputHSMHeaderTypeTextParameterName.val('');
		$inputHSMHeaderTypeTextParameterDescription.val('');
		$selectHSMHeaderTypeMedia.val(1);
		$inputHSMHeaderTypeMediaUrl.val('');
		if (template.HeaderType === HSMTemplateHeaderTypes.Text) {
			$inputHSMHeaderTypeText.val(template.HeaderText);
			$inputHSMHeaderTypeText.blur();

			if (template.HeaderTextParameter !== null) {
				$inputHSMHeaderTypeTextParameterName.val(template.HeaderTextParameter.Name);
				$inputHSMHeaderTypeTextParameterDescription.val(template.HeaderTextParameter.Description);
			}
		}
		else if (template.HeaderType === HSMTemplateHeaderTypes.Media) {
			$selectHSMHeaderTypeMedia.val(template.HeaderMediaType);
			$inputHSMHeaderTypeMediaUrl.val(template.HeaderMediaUrl);
		}
		else if (template.HeaderType === HSMTemplateHeaderTypes.Location) {
			$inputHSMHeaderTypeLatitude.val(template.HeaderLocationLatitude);
			$inputHSMHeaderTypeLongitude.val(template.HeaderLocationLongitude);
		}
		$selectHSMHeaderTypeMedia.multiselect('resync');
		$textareaHSMTemplate.val(template.Template);
		$selectHSMFooterType.val(template.FooterType).multiselect('resync').trigger('change');
		$inputHSMFooterTypeText.val('');
		if (template.FooterType === HSMTemplateFooterTypes.Text) {
			$inputHSMFooterTypeText.val(template.FooterText);
		}

		if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
			for (var i = 0; i < template.TemplateParameters.length; i++) {
				var p = template.TemplateParameters[i];
				if (typeof (p) === 'string') {
					let parts = p.split('=');
					$tableParameters.addRow(parts);
				}
				else {
					$tableParameters.addRow([p.Name, p.Description]);
				}
			}
		}

		$selectHSMButtonsType.val(template.ButtonsType).multiselect('resync').trigger('change');
		switch (template.ButtonsType) {
			case HSMTemplateButtonsTypes.QuickReply:
				for (var i = 0; i < template.Buttons.length; i++) {
					$tableButtonsQuickReply.addRow([
						template.Buttons[i].Text,
						(template.Buttons[i].QuickReplyParameter !== null) ? template.Buttons[i].QuickReplyParameter.Name : null,
						(template.Buttons[i].QuickReplyParameter !== null) ? template.Buttons[i].QuickReplyParameter.Description : null
					]);
				}
				break;
			case HSMTemplateButtonsTypes.AuthCode:
				for (var i = 0; i < template.Buttons.length; i++) {
					let parameterName = null;
					let parameterDesc = null;

					switch (template.Buttons[i].AuthCodeButtonType) {
						case HSMTemplateAuthCodeButtonTypes.AuthCode:
							if (template.Buttons[i].AuthCodeParameter !== null) {
								parameterName = template.Buttons[i].AuthCodeParameter.Name;
								parameterDesc = template.Buttons[i].AuthCodeParameter.Description;
							}
							break;
					}
					$tableButtonsAuthCode.addRow([
						template.Buttons[i].AuthCodeButtonType,
						template.Buttons[i].Text,
						template.Buttons[i].AuthCodeButtonType,
						parameterName,
						parameterDesc,
					]);
				}
				break;
			case HSMTemplateButtonsTypes.CallToAction:
				for (var i = 0; i < template.Buttons.length; i++) {

					let parameterName = null;
					let parameterDesc = null;

					switch (template.Buttons[i].CallToActionButtonType) {
						case HSMTemplateCallToActionButtonTypes.OfferCode:
							if (template.Buttons[i].OfferCodeParameter !== null) {
								parameterName = template.Buttons[i].OfferCodeParameter.Name;
								parameterDesc = template.Buttons[i].OfferCodeParameter.Description;
							}
							break;
						case HSMTemplateCallToActionButtonTypes.Url:
							if (template.Buttons[i].UrlButtonType === HSMTemplateCallToActionUrlButtonTypes.Dynamic &&
								template.Buttons[i].UrlParameter !== null) {
								parameterName = template.Buttons[i].UrlParameter.Name;
								parameterDesc = template.Buttons[i].UrlParameter.Description;
							}
							break;
						case HSMTemplateCallToActionButtonTypes.Flow:
							if (template.Buttons[i].FlowParameter !== null) {
								parameterName = (template.Buttons[i].FlowParameter.Name);
								parameterDesc = JSON.stringify(template.Buttons[i].FlowParameter);
							}
							break;
					}

					$tableButtonsCallToAction.addRow([
						template.Buttons[i].CallToActionButtonType,
						template.Buttons[i].Text,
						template.Buttons[i].UrlButtonType,
						parameterName,
						parameterDesc,
					]);
				}
				break;
			case HSMTemplateButtonsTypes.Mixed:
				for (var i = 0; i < template.Buttons.length; i++) {
					if (typeof (template.Buttons[i].CallToActionButtonType) != 'undefined' &&
						template.Buttons[i].CallToActionButtonType != null) {

						let parameterName = null;
						let parameterDesc = null;

						switch (template.Buttons[i].CallToActionButtonType) {
							case HSMTemplateCallToActionButtonTypes.OfferCode:
								if (template.Buttons[i].OfferCodeParameter !== null) {
									parameterName = template.Buttons[i].OfferCodeParameter.Name;
									parameterDesc = template.Buttons[i].OfferCodeParameter.Description;
								}
								break;
							case HSMTemplateCallToActionButtonTypes.Url:
								if (template.Buttons[i].UrlButtonType === HSMTemplateCallToActionUrlButtonTypes.Dynamic &&
									template.Buttons[i].UrlParameter !== null) {
									parameterName = template.Buttons[i].UrlParameter.Name;
									parameterDesc = template.Buttons[i].UrlParameter.Description;
								}
								break;
							case HSMTemplateCallToActionButtonTypes.Flow:
								if (template.Buttons[i].FlowParameter !== null) {
									parameterName = (template.Buttons[i].FlowParameter.Name);
									parameterDesc = JSON.stringify(template.Buttons[i].FlowParameter);
								}
								break;
						}

						$tableButtonsTypeMixed.addRow([
							template.Buttons[i].Text,
							HSMTemplateButtonsTypes.CallToAction,
							template.Buttons[i].CallToActionButtonType,
							template.Buttons[i].UrlButtonType,
							parameterName,
							parameterDesc,
						]);
					}
					else {
						$tableButtonsTypeMixed.addRow([
							template.Buttons[i].Text,
							HSMTemplateButtonsTypes.QuickReply,
							null,
							null,
							(template.Buttons[i].QuickReplyParameter !== null) ? template.Buttons[i].QuickReplyParameter.Name : null,
							(template.Buttons[i].QuickReplyParameter !== null) ? template.Buttons[i].QuickReplyParameter.Description : null
						]);
					}
				}
				break;
		}
	}

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: $divHSM,
		width: '70%',
		initialWidth: '70%',
		preloading: false,
		showBackButton: false,
		closeButton: false,
		onComplete: function () {
			$inputHSMDescription.focus();
		}
	});
}
function SaveHSM() {
	ToggleValidator($divHSMError, true);

	var $inputDescription = $inputHSMDescription;
	var $inputNamespace = $inputHSMNamespace;
	var $inputElementName = $inputHSMElementName;
	var $inputTemplate = $textareaHSMTemplate;
	var $selectLanguage = $selectHSMLanguage;
	var $spanError = $('span', $divHSMError);

	var regexElementName = /^[a-z_0-9]+$/;
	var regexNamespace = /^.+$/;
	var regexParameters = /^[\w$]+=.+$/;

	var language = $selectLanguage.val();

	var description = $inputDescription.val();
	if (description.length == 0) {
		$spanError.text($.i18n("configuration-serviceswhatsapp-enter_description-v2"));
		ToggleValidator($divHSMError, false);
		$inputDescription.focus();
		return;
	}

	var namespace = $inputNamespace.val();
	if (namespace.length === 0 || !regexNamespace.test(elementName)) {
		$spanError.text($.i18n("configuration-serviceswhatsapp-item_caracters-v2"));
		ToggleValidator($divHSMError, false);
		$inputNamespace.focus();
		return;
	}

	var elementName = $inputElementName.val();
	if (elementName.length === 0 || !regexElementName.test(elementName)) {
		$spanError.text($.i18n("configuration-serviceswhatsapp-item_regex-v2"));
		ToggleValidator($divHSMError, false);
		$inputElementName.focus();
		return;
	}

	var templateText = $inputTemplate.val();
	if (templateText.length === 0) {
		$spanError.text($.i18n("configuration-serviceswhatsapp-template_text-v2"));
		ToggleValidator($divHSMError, false);
		$inputTemplate.focus();
		return;
	}

	var uses = $selectHSMAllowedUses.val();
	if (typeof (uses) === 'undefined' || uses === null || uses.length === 0) {
		$spanError.text($.i18n("configuration-serviceswhatsapp-template-allowed_uses-invalid"));
		ToggleValidator($divHSMError, false);
		$selectHSMAllowedUses.focus();
		return;
	}
	var allowToConfigureSendHSMIfCaseOpen = Boolean(Number($selectAllowToConfigureSendHSMIfCaseOpen.val()));
	var avaiableForAgents = uses.indexOf('1') >= 0;
	var avaiableForSupervisors = uses.indexOf('2') >= 0;
	var avaiableForIntegrations = uses.indexOf('3') >= 0;
	let notSupportedForAgents = false;

	var $tableParameters = $divHSMParametersContainer.prop('table');
	var parametersRaw = $tableParameters.getValuesRaw('=');
	var parameters = [];
	if (parametersRaw.length > 0) {
		var parametersElements = parametersRaw.split('\n');
		if (parametersElements.length === 0) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-invalid_parameters-v2"));
			ToggleValidator($divHSMError, false);
			return;
		}

		for (let j = 0; j < parametersElements.length; j++) {
			var parametersElement = parametersElements[j];
			if (!regexParameters.test(parametersElement)) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-invalid_parameters-v2", (j + 1).toString()));
				ToggleValidator($divHSMError, false);
				return;
			}

			var parameterName = parametersElement;

			parameterName = parameterName.substr(0, parameterName.indexOf('='));

			if (templateText.indexOf('{{' + parameterName + '}}') === -1) {
				$spanError.html($.i18n("configuration-serviceswhatsapp-parameter_format-v2", parameterName, parameterName));
				ToggleValidator($divHSMError, false);
				return;
			}

			parameters.push(parametersElement);
		}
	}
	else {
		var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
		var matches = parametersRegex.exec(templateText);
		var invalidParameter = null;;
		while (matches !== null) {
			var parameterName = matches[0].replace('{{', '').replace('}}', '');
			$tableParameters.addRow([parameterName, '']);
			matches = parametersRegex.exec(templateText);

			if (invalidParameter === null) {
				invalidParameter = parameterName;
			}
		}

		if (invalidParameter !== null) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-invalid_parameters-missing", invalidParameter));
			ToggleValidator($divHSMError, false);
			return;
		}
	}

	for (let i = 0; i < parameters.length; i++) {
		for (let j = 0; j < parameters.length; j++) {
			if (i !== j) {
				let parameterNameI = parameters[i].substr(0, parameters[i].indexOf('='));
				let parameterNameJ = parameters[j].substr(0, parameters[j].indexOf('='));
				if (parameterNameI.toLowerCase() === parameterNameJ.toLowerCase()) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-body-duplicated_parameter", parameterNameJ));
					ToggleValidator($divHSMError, false);
					return;
				}
			}
		}
	}

	let headerType = parseInt($selectHSMHeaderType.val(), 10);
	let headerText = null;
	let headerMediaType = HSMTemplateHeaderMediaTypes.None;
	let headerMediaUrl = null;
	let headerTextParameter = null;
	let headerLocationLatitude = null;
	let headerLocationLongitude = null;
	if (headerType === HSMTemplateHeaderTypes.Text) {
		headerText = $inputHSMHeaderTypeText.val();
		if (headerText.length === 0 || headerText.length > 60) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-text-invalid"));
			ToggleValidator($divHSMError, false);
			$inputHSMHeaderTypeText.focus();
			return;
		}

		var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
		var matches = parametersRegex.exec(headerText);
		var totalMatches = 0;
		while (matches !== null) {
			totalMatches++;

			if (totalMatches === 2) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-text-invalid-one_variable"));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeText.focus();
				return;
			}

			var parameterName = matches[0].replace('{{', '').replace('}}', '');
			headerTextParameter = {
				Name: $inputHSMHeaderTypeTextParameterName.val(),
				Description: $inputHSMHeaderTypeTextParameterDescription.val()
			};

			if (parameterName !== headerTextParameter.Name) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-text-parameter_name-missing", parameterName));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeTextParameterName.focus();
				return;
			}

			if (headerTextParameter.Description === null || headerTextParameter.Description.length === 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-text-parameter_description-missing", parameterName));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeTextParameterDescription.focus();
				return;
			}

			if (parameters.length > 0 && parameters.findIndex(p => p.toLowerCase().startsWith(`${parameterName.toLowerCase()}=`)) >= 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-duplicated_parameter"));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeTextParameterName.focus();
				return;
			}

			matches = parametersRegex.exec(headerText);
		}
	}
	else if (headerType === HSMTemplateHeaderTypes.Media) {
		headerMediaType = parseInt($selectHSMHeaderTypeMedia.val(), 10);
		headerMediaUrl = $inputHSMHeaderTypeMediaUrl.val();

		var urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
		if (headerMediaUrl.length > 0 && !urlRegex.test(headerMediaUrl)) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-media-url-invalid"));
			ToggleValidator($divHSMError, false);
			$inputHSMHeaderTypeMediaUrl.focus();
			return;
		}

		if (headerMediaUrl.length === 0) {
			headerMediaUrl = null;
		}
	}
	else if (headerType === HSMTemplateHeaderTypes.Location) {
		notSupportedForAgents = true;

		headerLocationLatitude = $inputHSMHeaderTypeLatitude.val();
		headerLocationLongitude = $inputHSMHeaderTypeLongitude.val();

		if (headerLocationLatitude.length !== 0) {
			headerLocationLatitude = parseFloat(headerLocationLatitude);

			if (isNaN(headerLocationLatitude) || headerLocationLatitude < -90 || headerLocationLatitude > 90) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-location-latitude-invalid"));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeLatitude.focus();
				return;
			}
		}
		else {
			headerLocationLatitude = null;
		}

		if (headerLocationLongitude.length !== 0) {
			headerLocationLongitude = parseFloat(headerLocationLongitude);

			if (isNaN(headerLocationLongitude) || headerLocationLongitude < -180 || headerLocationLongitude > 180) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-location-longitude-invalid"));
				ToggleValidator($divHSMError, false);
				$inputHSMHeaderTypeLongitude.focus();
				return;
			}
		}
		else {
			headerLocationLongitude = null;
		}

		if (
			(headerLocationLatitude !== null && headerLocationLongitude === null) ||
			(headerLocationLatitude === null && headerLocationLongitude !== null)
		) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-template-header-location-must_enter_both_coordinates"));
			ToggleValidator($divHSMError, false);
			if (headerLocationLatitude === null) {
				$inputHSMHeaderTypeLatitude.focus();
			}
			else {
				$inputHSMHeaderTypeLongitude.focus();
			}
			return;
		}
	}

	let footerType = parseInt($selectHSMFooterType.val(), 10);
	let footerText = null;
	if (footerType === HSMTemplateFooterTypes.Text) {
		footerText = $inputHSMFooterTypeText.val();
		if (footerText.length === 0 || footerText.length > 60) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-template-footer-text-invalid"));
			ToggleValidator($divHSMError, false);
			$inputHSMFooterTypeText.focus();
			return;
		}
	}

	let buttonsType = parseInt($selectHSMButtonsType.val(), 10);
	let buttons = null;

	let callToActionCount = 0;
	let authCodeCount = 0;
	let authCodeButtonCount = 0;
	let urlButtonCount = 0;
	let offerButtonCount = 0;
	let callButtonCount = 0;
	let flowButtonCount = 0;
	let errorMessage;

	switch (buttonsType) {
		case HSMTemplateButtonsTypes.QuickReply:
			let $tableButtonsQuickReply = $divHSMButtonsTypeQuickReplyContainer.prop('table');
			buttons = $tableButtonsQuickReply.getValues();
			if (buttons.length === 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-empty"));
				ToggleValidator($divHSMError, false);
				return;
			}

			for (let i = 0; i < buttons.length; i++) {
				let button = buttons[i];
				if (i > 0) {
					for (let j = i; j >= 0; j--) {
						if (button.Text === buttons[j]) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-button-text-invalid", (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}
					}
				}

				errorMessage = ValidateQuickReplyButton(button, parameters);

				if (typeof (errorMessage) == 'string' && errorMessage.length > 0) {
					$spanError.text($.i18n(errorMessage, (i + 1).toString()));
					ToggleValidator($divHSMError, false);
					return;
				}
			}
			break;
		case HSMTemplateButtonsTypes.CallToAction:
			var $tableButtonsCallToAction = $divHSMButtonsTypeCallToActionContainer.prop('table');
			buttons = $tableButtonsCallToAction.getValues();
			if (buttons.length === 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-empty"));
				ToggleValidator($divHSMError, false);
				return;
			}

			for (var i = 0; i < buttons.length; i++) {
				var button = buttons[i];
				if (i > 0) {
					for (var j = i - 1; j >= 0; j--) {
						if (button.Text === buttons[j].Text) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-button-text-invalid", (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}
					}
				}

				callToActionCount++;
				switch (parseInt(button.CallToActionButtonType, 10)) {
					case HSMTemplateCallToActionButtonTypes.Url:
						urlButtonCount++;
						break;
					case HSMTemplateCallToActionButtonTypes.OfferCode:
						offerButtonCount++;
						break;
					case HSMTemplateCallToActionButtonTypes.Call:
						callButtonCount++;
						break;
					case HSMTemplateCallToActionButtonTypes.Flow:
						notSupportedForAgents = true;
						flowButtonCount++;
						break;
				}

				if (callToActionCount > maxCallToActionButtons) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-call-to-action-max", maxCallToActionButtons));
					ToggleValidator($divHSMError, false);
					return;
				}

				if (urlButtonCount > maxUrlButton) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-url-max", maxUrlButton));
					ToggleValidator($divHSMError, false);
					return;
				}

				if (offerButtonCount > maxOfferButton) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-offer-code-max", maxOfferButton));
					ToggleValidator($divHSMError, false);
					return;
				}

				if (callButtonCount > maxCallButton) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-call-max", maxCallButton));
					ToggleValidator($divHSMError, false);
					return;
				}

				errorMessage = ValidateCallToActionButton(button, parameters);

				if (typeof (errorMessage) == 'string' && errorMessage.length > 0) {
					$spanError.text($.i18n(errorMessage, (i + 1).toString()));
					ToggleValidator($divHSMError, false);
					return;
				}
			}
			break;
			case HSMTemplateButtonsTypes.AuthCode:
			var $tableButtonsAuthCode = $divHSMButtonsTypeAuthCodeContainer.prop('table');
			buttons = $tableButtonsAuthCode.getValues();
			if (buttons.length === 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-empty"));
				ToggleValidator($divHSMError, false);
				return;
			}

			for (var i = 0; i < buttons.length; i++) {
				var button = buttons[i];
				if (i > 0) {
					for (var j = i - 1; j >= 0; j--) {
						if (button.Text === buttons[j].Text) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-button-text-invalid", (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}
					}
				}

				authCodeCount++;
				switch (parseInt(button.AuthCodeButtonType, 10)) {
					case HSMTemplateAuthCodeButtonTypes.AuthCode:
						authCodeButtonCount++;
						break;
				}
				if (authCodeCount > maxAuthCodeButtons) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-auth_code-max", maxAuthCodeButtons));
					ToggleValidator($divHSMError, false);
					return;
				}
				if (authCodeButtonCount > maxAuthCodeButton) {
					$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-auth_code_button-max", maxAuthCodeButton));
					ToggleValidator($divHSMError, false);
					return;
				}

				errorMessage = ValidateAuthCodeButton(button, parameters);

				if (typeof (errorMessage) == 'string' && errorMessage.length > 0) {
					$spanError.text($.i18n(errorMessage, (i + 1).toString()));
					ToggleValidator($divHSMError, false);
					return;
				}
			}
			break;
		case HSMTemplateButtonsTypes.Mixed:
			var $tableButtonsTypeMixed = $divHSMButtonsTypeMixedContainer.prop('table');
			buttons = $tableButtonsTypeMixed.getValues();
			if (buttons.length === 0) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-empty"));
				ToggleValidator($divHSMError, false);
				return;
			}

			if (!isOrderedTemplate(buttons)) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-not-ordered"));
				ToggleValidator($divHSMError, false);
				return;
			}

			for (var i = 0; i < buttons.length; i++) {
				var button = buttons[i];

				if (i > 0) {
					for (let j = i; j >= 0; j--) {
						if (button.Text === buttons[j]) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-button-text-invalid", (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}
					}
				}

				switch (parseInt(button.ButtonType, 10)) {
					case HSMTemplateButtonsTypes.CallToAction:
						callToActionCount++;
						switch (parseInt(button.CallToActionButtonType, 10)) {
							case HSMTemplateCallToActionButtonTypes.Url:
								urlButtonCount++;
								break;
							case HSMTemplateCallToActionButtonTypes.OfferCode:
								offerButtonCount++;
								break;
							case HSMTemplateCallToActionButtonTypes.Call:
								callButtonCount++;
								break;
							case HSMTemplateCallToActionButtonTypes.Flow:
								flowButtonCount++;
								break;
						}

						if (callToActionCount > maxCallToActionButtons) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-call-to-action-max", maxCallToActionButtons));
							ToggleValidator($divHSMError, false);
							return;
						}

						if (urlButtonCount > maxUrlButton) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-url-max", maxUrlButton));
							ToggleValidator($divHSMError, false);
							return;
						}

						if (offerButtonCount > maxOfferButton) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-offer-code-max", maxOfferButton));
							ToggleValidator($divHSMError, false);
							return;
						}

						if (callButtonCount > maxCallButton) {
							$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-call-max", maxCallButton));
							ToggleValidator($divHSMError, false);
							return;
						}

						errorMessage = ValidateCallToActionButton(button, parameters);

						if (typeof (errorMessage) == 'string' && errorMessage.length > 0) {
							$spanError.text($.i18n(errorMessage, (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}

						break;
					case HSMTemplateButtonsTypes.QuickReply:

						button.QuickReplyParameter_Name = button.ButtonParameter_Name;
						button.QuickReplyParameter_Desc = button.ButtonParameter_Desc;
						delete button['ButtonParameter_Name'];
						delete button['ButtonParameter_Desc'];

						errorMessage = ValidateQuickReplyButton(button, parameters);

						if (typeof (errorMessage) == 'string' && errorMessage.length > 0) {
							$spanError.text($.i18n(errorMessage, (i + 1).toString()));
							ToggleValidator($divHSMError, false);
							return;
						}
						break;
					default:
						break;
				}
			}
			break;
		default:
			break;

			if (buttons.length > maxTotalButtons) {
				$spanError.text($.i18n("configuration-serviceswhatsapp-template-buttons-button-text-invalid", (i + 1).toString()));
				ToggleValidator($divHSMError, false);
				return;
			}
	}

	//TODO: revisar a futuro
	if (avaiableForAgents) {
		if (notSupportedForAgents) {
			$spanError.text($.i18n("configuration-serviceswhatsapp-template-invalid_for_agents"));
			ToggleValidator($divHSMError, false);
			return;
		}
	}

	ToggleValidator($divHSMError, true);

	let template = {
		Description: description,
		Namespace: namespace,
		ElementName: elementName,
		Parameters: parametersRaw,
		TemplateParameters: parameters,
		Template: templateText,
		Language: language,
		HeaderType: headerType,
		HeaderText: headerText,
		HeaderTextParameter: headerTextParameter,
		HeaderMediaType: headerMediaType,
		HeaderMediaUrl: headerMediaUrl,
		HeaderLocationLatitude: headerLocationLatitude,
		HeaderLocationLongitude: headerLocationLongitude,
		FooterType: footerType,
		FooterText: footerText,
		AvaiableForAgents: avaiableForAgents,
		AvaiableForSupervisors: avaiableForSupervisors,
		AvaiableForIntegrations: avaiableForIntegrations,
		AllowToConfigureSendHSMIfCaseOpen: allowToConfigureSendHSMIfCaseOpen,
		ButtonsType: buttonsType,
		Buttons: buttons
	};

	if (currentTemplate === null) {
		templates.push(template);

		$tableHSMTemplatesv2.parent().show();

		datatableTemplates.row.add(template);
		datatableTemplates.columns.adjust().draw();

		$tableHSMTemplatesv2.css('width', '');

		$messageHSMTemplatesEmpty.hide();
	}
	else {
		current$Tr.prop('template', template);
		currentTemplate = template;
		templates[currentTemplateIndex] = template;

		$('span[rel=description]', current$Tr).text(template.Description);
		$('span[rel=namespace]', current$Tr).text(template.Namespace + ' - ' + template.ElementName);
		$('span[rel=language]', current$Tr).text($.i18n('configuration-serviceswhatsapp-template-lenguage-' + template.Language));
		$('span[rel=media]', current$Tr).toggleClass('fa-yes', template.HeaderType === HSMTemplateHeaderTypes.Media).toggleClass('fa-no', template.HeaderType !== HSMTemplateHeaderTypes.Media);
		$('span[rel=availableforagents]', current$Tr).toggleClass('fa-yes', template.AvaiableForAgents).toggleClass('fa-no', !template.AvaiableForAgents);
		$('span[rel=availableforsupervisors]', current$Tr).toggleClass('fa-yes', template.AvaiableForSupervisors).toggleClass('fa-no', !template.AvaiableForSupervisors);
		$('span[rel=availableforintegrations]', current$Tr).toggleClass('fa-yes', template.AvaiableForIntegrations).toggleClass('fa-no', !template.AvaiableForIntegrations);
		$('span[rel=allowToConfigureSendHSMIfCaseOpen]', current$Tr).toggleClass('fa-yes', template.AllowToConfigureSendHSMIfCaseOpen).toggleClass('fa-no', !template.AllowToConfigureSendHSMIfCaseOpen);
	}

	$.colorbox.close();
}

function ValidateCallToActionButton(button, parameters) {
	if (button.Text === null || button.Text.length === 0) {
		return "configuration-serviceswhatsapp-template-buttons-button-text-invalid";
	}

	if (typeof (button.CallToActionButtonType) === 'string') {
		button.CallToActionButtonType = parseInt(button.CallToActionButtonType, 10);
		if (isNaN(button.CallToActionButtonType)) {
			button.CallToActionButtonType = null;
		}
	}

	if (typeof (button.UrlButtonType) === 'string') {
		button.UrlButtonType = parseInt(button.UrlButtonType, 10);
		if (isNaN(button.UrlButtonType)) {
			button.UrlButtonType = null;
		}
	}

	if (button.CallToActionButtonType === HSMTemplateCallToActionButtonTypes.Url) {
		if (button.UrlButtonType === HSMTemplateCallToActionUrlButtonTypes.Dynamic) {
			button.UrlParameter = {
				Name: button['Parameter_Name'],
				Description: button['Parameter_Desc']
			};

			delete button['Parameter_Name'];
			delete button['Parameter_Desc'];

			if (button.UrlParameter.Name === null || button.UrlParameter.Name.length === 0) {
				return "configuration-serviceswhatsapp-template-buttons-button-url_parameter_name-invalid";
			}

			if (parameters.length > 0 && parameters.findIndex(p => p.toLowerCase().startsWith(`${button.UrlParameter.Name.toLowerCase()}=`)) >= 0) {
				return "configuration-serviceswhatsapp-template-buttons-duplicated_parameter";
			}

			if (button.UrlParameter.Description === null || button.UrlParameter.Description.length === 0) {
				return "configuration-serviceswhatsapp-template-buttons-button-url_parameter_description-invalid";
			}
		}
		else {
			button.UrlParameter = null;
		}
	}
	else {
		button.UrlButtonType = null;
		button.UrlParameter = null;
	}

	if (button.CallToActionButtonType === HSMTemplateCallToActionButtonTypes.OfferCode) {
		button.OfferCodeParameter = {
			Name: button['Parameter_Name'],
			Description: button['Parameter_Desc']
		};

		delete button['Parameter_Name'];
		delete button['Parameter_Desc'];
	}
	else {
		button.OfferCodeParameter = null;
	}

	if (button.CallToActionButtonType === HSMTemplateCallToActionButtonTypes.Flow) {

		if (typeof (button['Parameter_Desc']) !== 'string' || button['Parameter_Desc'].length === 0) {
			return "configuration-serviceswhatsapp-template-buttons-button-flow-invalid";
		}

		try
		{
			button.FlowParameter = JSON.parse(button['Parameter_Desc']);
		}
		catch (err)
		{
			console.error("Error al parsear JSON del flow", err);
			return "configuration-serviceswhatsapp-template-buttons-button-flow-invalid";
		}

		button.FlowParameter.Name = button['Parameter_Name'];

		delete button['Parameter_Name'];
		delete button['Parameter_Desc'];
	}
	else {
		button.FlowParameter = null;
	}
}
function ValidateAuthCodeButton(button, parameters) {
	if (button.Text === null || button.Text.length === 0) {
		return "configuration-serviceswhatsapp-template-buttons-button-text-invalid";
	}

	if (typeof (button.AuthCodeButtonType) === 'string') {
		button.AuthCodeButtonType = parseInt(button.AuthCodeButtonType, 10);
		if (isNaN(button.AuthCodeButtonType)) {
			button.AuthCodeButtonType = null;
		}
	}

	if (button.AuthCodeButtonType === HSMTemplateAuthCodeButtonTypes.AuthCode) {
		button.AuthCodeParameter = {
			Name: button['Parameter_Name'],
			Description: button['Parameter_Desc']
		};

		delete button['Parameter_Name'];
		delete button['Parameter_Desc'];
	}
	else {
		button.AuthCodeParameter = null;
	}
}
function ValidateQuickReplyButton(button, parameters) {

	if (button.Text === null || button.Text.length === 0) {
		return "configuration-serviceswhatsapp-template-buttons-button-text-invalid";
	}

	button.QuickReplyParameter = {
		Name: button['QuickReplyParameter_Name'],
		Description: button['QuickReplyParameter_Desc']
	};

	delete button['QuickReplyParameter_Name'];
	delete button['QuickReplyParameter_Desc'];
	delete button['CallToActionButtonType'];

	if (button.QuickReplyParameter.Name === null || button.QuickReplyParameter.Name.length === 0) {
		return "configuration-serviceswhatsapp-template-buttons-button-quick_reply_parameter_name-invalid";
	}

	if (parameters.length > 0 && parameters.findIndex(p => p.toLowerCase().startsWith(`${button.QuickReplyParameter.Name.toLowerCase()}=`)) >= 0) {
		return "configuration-serviceswhatsapp-template-buttons-duplicated_parameter";
	}

	if (button.QuickReplyParameter.Description === null || button.QuickReplyParameter.Description.length === 0) {
		return "configuration-serviceswhatsapp-template-buttons-button-quick_reply_parameter_description-invalid";
	}
}

function isOrderedTemplate(buttons) {

	let actualType = buttons[0].ButtonType;
	const proccessedTypes = [actualType];

	for (let i = 0; i < buttons.length; i++) {

		let type = buttons[i].ButtonType;

		if (type !== actualType) {
			if (proccessedTypes.includes(type)) {
				return false;
			}

			actualType = type;
			proccessedTypes.push(type);
		}
	}

	return true;
}

function ValidateHSMTemplates(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value === "1") {
		return;
	}

	var allowed = $dropdownlistAllowToSendHSM.val();
	if (allowed === "0") {
		return;
	}

	ParseHSMTemplates();

	if (templates.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_message_template"));
		e.IsValid = false;
		return;
	}

	for (let i = 0; i < templates.length; i++) {
		let template = templates[i];

		if (typeof (template.Parameters) === 'string' &&
			template.Parameters.length > 0) {
			template.Parameters = template.Parameters.split('\n');
		}

		for (var j = 0; j < templates.length; j++) {
			if (i === j) {
				continue;
			}

			if (template.Description.toLowerCase() === templates[j].Description.toLowerCase()) {
				$(sender).text($.i18n("configuration-serviceswhatsapp-error-duplicate-description", (j + 1).toString()));
				e.IsValid = false;
				return;
			}

			if (typeof (template.Namespace) === 'string' &&
				typeof (templates[j].Namespace) === 'string') {
				if (template.ElementName.toLowerCase() === templates[j].ElementName.toLowerCase() &&
					template.Language.toLowerCase() === templates[j].Language.toLowerCase() &&
					template.Namespace.toLowerCase() === templates[j].Namespace.toLowerCase()) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-error-duplicate-definition", (j + 1).toString()));
					e.IsValid = false;
					return;
				}
			}
			else {
				if (template.ElementName.toLowerCase() === templates[j].ElementName.toLowerCase() &&
					template.Language.toLowerCase() === templates[j].Language.toLowerCase()) {
					$(sender).text($.i18n("configuration-serviceswhatsapp-error-duplicate-definition", (j + 1).toString()));
					e.IsValid = false;
					return;
				}
			}
		}
	}

	$hiddenHSMTemplates.val(JSON.stringify(templates));
}

function ShowFlowDialog(flow, $tr, index) {
	if (typeof (template) === 'undefined') {
		template = null;
	}
	if (typeof ($tr) === 'undefined') {
		$tr = null;
	}

	var escapedUrl = encodeURIComponent(flow.JsonPreviewUrl);
	window.open = escapedUrl;
}

function CreateFlowTable() {
	if (typeof ($hiddenFlows) === 'undefined') {
		$hiddenFlows = $('#hiddenFlows');
	}

	if (typeof ($messageFlowsEmpty) === 'undefined') {
		$messageFlowsEmpty = $('#messageHSMTemplatesEmpty');
	}

	if (typeof ($tableFlows) === 'undefined') {
		$tableFlows = $('#tableFlows');
	}

	ParseMetaFlows();

	if (datatableFlows !== null &&
		typeof (datatableFlows.destroy) === 'function') {
		datatableFlows.destroy();
		datatableFlows = null;
	}

	datatableFlows = $tableFlows.DataTable({
		ordering: false,
		searching: true,
		paging: true,
		pageLength: 15,
		lengthChange: false,
		deferRender: true,
		data: metaFlows,
		createdRow: function (row, data, dataIndex) {
			let $tr = $(row);
			let flow = data;
			$tr.prop('flow', flow);
			$tr.addClass('flow');

			if (typeof (flow.Status) === 'string' && flow.Status.length > 0) {
				$tr.attr('status', flow.Status);
			}
		},
		columns: [
			{
				render: function (data, type, flow) {
					return flow.ID;
				},
				defaultContent: ''
			},
			{
				render: function (data, type, flow) {
					return flow.Name;
				},
				defaultContent: ''
			},
			{
				render: function (data, type, flow) {
					let html = BuildFlowStatus(flow.Status);
					return html;
				},
				defaultContent: ''
			},
			{
				render: function (data, type, flow) {
					let html = BuildFlowCategories(flow.Categories);
					return html;
				},
				defaultContent: ''
			},
			{
				data: null,
				className: 'icons',
				width: '100px',
				createdCell: function (td, cellData, rowData, rowIndex, colIndex) {
					let $td = $(td);
					let $tr = $td.parent();

					let flow = cellData;
					let updateIcon = function (enabled) {
						let $icon = $td.find('a[rel=status] span.fa');

						if (enabled) {
							$icon.removeClass('fa-toggle-on').addClass('fa-toggle-off');
						} else {
							$icon.removeClass('fa-toggle-off').addClass('fa-toggle-on');
						}
					};
					
					let $anchorStatus = $('a[rel=status]', $td);
					$anchorStatus.click({
						$tr: $tr
					}, function (e) {
						let $tr = e.data.$tr;
						let rowIndex = datatableTemplates.row($tr).index();
						let flow = $tr.prop('flow');
						
						flow.Enabled = !flow.Enabled;
						metaFlows[rowIndex].Enabled = flow.Enabled;
						updateIcon(flow.Enabled);
						datatableFlows.draw();
					});

					let $anchorDelete = $('a[rel=delete]', $td);
					$anchorDelete.click({
						$tr: $tr,
						flow: flow
					}, function (e) {
						let $tr = e.data.$tr;
						let flow = e.data.flow;
						let rowIndex = datatableFlows.row($tr).index();

						if (typeof (templates) === 'object' &&
							Array.isArray(templates)) {
							let usedInTemplate = templates.find(t => t.ButtonsType === HSMTemplateButtonsTypes.CallToAction &&
								t.Buttons !== null &&
								t.Buttons.length > 0 &&
								t.Buttons.findIndex(b => b.CallToActionButtonType === HSMTemplateCallToActionButtonTypes.Flow &&
									b.FlowParameter !== null &&
									b.FlowParameter.FlowID === flow.ID) !== -1);
							if (typeof (usedInTemplate) === 'object') {
								AlertDialog($.i18n('configuration-serviceswhatsapp-flow'), $.i18n('configuration-serviceswhatsapp-flow-delete-cannot_be_deleted-used', flow.Name, usedInTemplate.Description, usedInTemplate.ElementName, usedInTemplate.Language), undefined, undefined, 'Warning');
								return;
							}
						}						

						ConfirmDialog({
							title: $.i18n('configuration-serviceswhatsapp-flow'),
							message: $.i18n('configuration-serviceswhatsapp-flow-delete-question', flow.Name),
							onAccept: function (args) {
								let $tr = args.$tr;
								let flow = args.flow;
								let rowIndex = datatableFlows.row($tr).index();

								datatableFlows.row(rowIndex).remove().draw();
								metaFlows.splice(rowIndex, 1);

								if (metaFlows.length === 0) {
									$messageHSMTemplatesEmpty.show();
									$tableFlows.parent().hide();
								}

								$.colorbox.close();
							},
							acceptArguments: e.data
						});
					});
				},
				render: function (data, type, agent) {

					let flow = data;
					let html = '<a class="action" rel="status"><span class="fa fa-lg ' + (flow.Enabled ? 'fa-toggle-off' : 'fa-toggle-on') + '" data-i18n-title="globals-' + (flow.Enabled ? 'disable' : 'enable') + '"></span></a>';
					//html += '<a class="action" rel="search"><span class="fa fa-lg fa-search-plus" data-i18n-title="globals-viewmore"></span></a>';
					html += '<a class="action" rel="delete"><span class="fa fa-lg fa-trash" data-i18n-title="globals-delete"></span></a>';

					return html;
				}
			}
		],
		initComplete: function (settings, json) {
			let $tableHSMTemplatesv2_filter = $('#tableHSMTemplatesv2_filter');
			let $input = $('input[type=search]', $tableHSMTemplatesv2_filter);
			$input.addClass('inputtext');
			$input.css('margin-bottom', '5px');
		},
		drawCallback: function () {
			LoadCompositedElements();
		}
	});

	datatableFlows.column(3).visible($dropdownlistIntegrationType.val() === '10' || $dropdownlistIntegrationType.val() === '11' || $dropdownlistIntegrationType.val() === '4');

	if (metaFlows.length > 0) {
		$messageFlowsEmpty.hide();
		$tableFlows.parent().show();
	}
	else {
		$messageFlowsEmpty.show();
		$tableFlows.parent().hide();
	}
}

function BuildFlowStatus(status) {

	switch (status) {
		case FlowStatuses.Draft:
			return '<span class="info" data-i18n="FlowStatuses.Draft">Dibujado</span>';
		case FlowStatuses.Published:
			return '<span class="info" data-i18n="FlowStatuses.Published">Publicado</span>';
		case FlowStatuses.Deprecated:
			return '<span class="info" data-i18n="FlowStatuses.Deprecated">Obsoleto</span>';
		case FlowStatuses.Blocked:
			return '<span class="info" data-i18n="FlowStatuses.Blocked">Bloqueado</span>';
		case FlowStatuses.Throttled:
			return '<span class="info" data-i18n="FlowStatuses.Throttled">Limitado</span>';
		default:
			return '';
	}
	}

function BuildFlowCategories(categories) {
	if (!categories || categories.length === 0) {
		return '';
	}

	const categoryLabels = categories.map(category => {
		return category.toLowerCase();
	});

	return categoryLabels.join(', ');
}

function ValidateFlows(sender, e) {
	var allowed = $dropdownlistAllowToSendFlows.val();
	if (allowed === "0") {
		return;
	}

	ParseMetaFlows();

	if (metaFlows.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_flow"));
		e.IsValid = false;
		return;
	}
	e.IsValid = true;
	$hiddenFlows.val(JSON.stringify(metaFlows));
}


function ValidatePhoneNumber(sender, e) {
	var countryCode = $textboxWhatsAppSelectedPhoneCode.val();
	var phoneNumber = $textboxWhatsAppPhoneNumber.val();
	var fullPhoneNumber = countryCode + phoneNumber;

	var dataToSend = JSON.stringify({ serviceId: (editingService ? editingServiceId : null), phoneNumber: fullPhoneNumber });

	$.ajax({
		type: "POST",
		url: "ServicesWhatsapp.aspx/IsPhoneNumberUsedInAnotherService",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		async: false,
		success: function (data) {
			if (data.d.Success) {
				if (!data.d.ExistsAnotherService) {
					e.IsValid = true;
				}
				else {
					e.IsValid = false;
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-another_service_exists", data.d.Service.Name), null, null, 'Warning');
				}
			}
			else {
				console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
				e.IsValid = false;
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
			e.IsValid = false;
		}
	});
}

function UsingYFlow() {
	if (typeof (allowYFlow) !== 'undefined' &&
		allowYFlow &&
		$dropdownlistUseYFlow.val() === "true") {
		return true;
	}

	return false;
}

function ValidateYFlow(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	let flow = $hiddenFlow.val();
	if (flow.length === 0) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-yflow_configuration"), $.i18n("configuration-serviceswhatsapp-yflow_configuration-tip"));
		e.IsValid = false;
		return;
	}
}

var $divEditWhatsappInteractiveMessage = null;
function ShowEditWhatsappInteractiveMessage($divInfo, type, interactiveMessageType) {
	let fields = {};

	let $divRightPanelContainer = $('#divRightPanelContainer');
	if (typeof ($divRightPanelContainer.prop('last-type')) === 'undefined' ||
		$divRightPanelContainer.prop('last-type') !== type) {
		$divRightPanelContainer.empty();
		$divEditWhatsappInteractiveMessage = null;
	}

	$divRightPanelContainer.prop('last-type', type);

	let allowFooter = true;
	let allowedInteractiveMessageTypes;
	switch (type) {
		case 'invite':
			allowFooter = false;
			break;
		case 'agent_not_available':
			allowedInteractiveMessageTypes = [InteractiveMessageTypes.Button, InteractiveMessageTypes.Text, InteractiveMessageTypes.CtaUrl];
			break;
		case 'agent_another_call':
			allowedInteractiveMessageTypes = [InteractiveMessageTypes.Button, InteractiveMessageTypes.Text, InteractiveMessageTypes.CtaUrl];
			break;
		case 'users_case_not_assigned':
			allowedInteractiveMessageTypes = [InteractiveMessageTypes.Button, InteractiveMessageTypes.Text, InteractiveMessageTypes.CtaUrl];
			break;
		case 'case_with_current_call':
			allowedInteractiveMessageTypes = [InteractiveMessageTypes.Button, InteractiveMessageTypes.Text, InteractiveMessageTypes.CtaUrl];
			break;
		case 'case_without_invite':
			allowedInteractiveMessageTypes = [InteractiveMessageTypes.Button, InteractiveMessageTypes.Text, InteractiveMessageTypes.CtaUrl];
			break;
		default:
			break;
	}

	if ($divEditWhatsappInteractiveMessage === null) {
		$divEditWhatsappInteractiveMessage = BuildDynamicWhatsappInteractiveMessageDefinition({
			id: 'divWhatsappInteractiveMessage',
			container: $divRightPanelContainer,
			allowFooter: allowFooter,
			interactiveMessageType: interactiveMessageType,
			allowedInteractiveMessageTypes: allowedInteractiveMessageTypes,
			onAccept: function (values) {
				console.log(values);
				if (!values.valid) {
					ConfirmDialog({
						title: $.i18n('configuration-httprequest-title'),
						message: $.i18n('configuration-httprequest-invalid-question'),
						onAccept: function (args) {
							$divInfo.setInfo(args);
							ShowRightPanel(false);
							$.colorbox.close();
						},
						acceptArguments: values,
						closeRightPanel: false
					});
				}
				else {
					$divInfo.setInfo(values);
					ShowRightPanel(false);
				}
			},
			onCancel: function () {
				ShowRightPanel(false);
			}
		});
	}

	let info = $divInfo.getInfo();
	if (info !== null) {
		$divEditWhatsappInteractiveMessage.setValues(info);
	}

	ShowRightPanel(true);

	$('#divRightPanelContainer').scrollTop(0);
}

var $divEditRequest = null;
function ShowEditHttpRequest(type, $divInfo) {
	let fields = {
		'@@NUMERO_TELEFONO@@': {
			text: 'Número de teléfono de la cuenta de Whatsapp',
			datai18n: 'configuration-serviceswhatsapp-fields-phone_number',
			dataType: 'Texto',
			dataTypei18n: 'globals-datatype-string'
		},
		'@@SERVICIO[CODIGO]@@': {
			text: 'Código de servicio',
			datai18n: 'configuration-serviceswhatsapp-fields-service_id',
			dataType: 'Numérico',
			dataTypei18n: 'globals-datatype-numeric'
		}
	};

	let $divRightPanelContainer = $('#divRightPanelContainer');
	if (typeof ($divRightPanelContainer.prop('last-type')) === 'undefined' ||
		$divRightPanelContainer.prop('last-type') !== type) {
		$divRightPanelContainer.empty();
		$divEditRequest = null;
	}

	$divRightPanelContainer.prop('last-type', type);

	let defaultMethod, canEditMethod, hashKeyRequired, hashKeyRegex, canEditBody;
	switch (type) {
		case 'reply':
			defaultMethod = 'POST';
			canEditMethod = false;
			hashKeyRequired = true;
			hashKeyRegex = '^([a-f0-9][a-f0-9]){3,25}$';
			canEditBody = false;
			break;
		case 'voicecalls':
			defaultMethod = 'POST';
			canEditMethod = false;
			hashKeyRequired = false;
			hashKeyRegex = '^([a-f0-9][a-f0-9]){3,25}$';
			canEditBody = false;
			break;
		case 'getnews':
			defaultMethod = 'GET';
			canEditMethod = false;
			hashKeyRequired = true;
			hashKeyRegex = '^([a-f0-9][a-f0-9]){3,25}$';
			canEditBody = false;
			break;
		case 'postnewsprocessed':
			defaultMethod = 'POST';
			canEditMethod = false;
			hashKeyRequired = true;
			hashKeyRegex = '^([a-f0-9][a-f0-9]){3,25}$';
			canEditBody = false;
			break;
		case 'closecase':
			defaultMethod = 'POST';
			canEditMethod = false;
			hashKeyRequired = true;
			hashKeyRegex = '^([a-f0-9][a-f0-9]){3,25}$';
			canEditBody = true;

			fields['@@CASO[CODIGO]@@'] = {
				text: 'Código de caso que se cerró',
				datai18n: 'configuration-serviceswhatsapp-fields-case_id',
				dataType: 'Numérico',
				dataTypei18n: 'globals-datatype-numeric'
			};
			fields['@@USUARIO[TELEFONO]@@'] = {
				text: 'Número de teléfono de usuario',
				datai18n: 'configuration-serviceswhatsapp-fields-user_phone',
				dataType: 'Texto',
				dataTypei18n: 'globals-datatype-string'
			};
			break;
		default:
			defaultMethod = 'GET';
			canEditMethod = true;
			hashKeyRequired = false;
			hashKeyRegex = null;
			canEditBody = true;
			break;
	}

	if ($divEditRequest === null) {
		$divEditRequest = BuildDynamicHttpRequestDefinition({
			id: 'divRequest',
			container: $divRightPanelContainer,
			urlFields: fields,
			headersFields: fields,
			bodyFields: fields,
			defaultMethod: defaultMethod,
			canEditMethod: canEditMethod,
			hashKeyRequired: hashKeyRequired,
			hashKeyRegex: hashKeyRegex,
			canEditBody: canEditBody,
			onAccept: function (values) {
				if (!values.valid) {
					ConfirmDialog({
						title: $.i18n('configuration-httprequest-title'),
						message: $.i18n('configuration-httprequest-invalid-question'),
						onAccept: function (args) {
							$divInfo.setInfo(args);
							ShowRightPanel(false);
							$.colorbox.close();
						},
						acceptArguments: values,
						closeRightPanel: false
					});
				}
				else {
					$divInfo.setInfo(values);
					ShowRightPanel(false);
				}
			},
			onCancel: function () {
				ShowRightPanel(false);
			}
		});
	}

	let info = $divInfo.getInfo();
	if (info !== null) {
		$divEditRequest.setValues(info);
	}

	ShowRightPanel(true);

	$('#divRightPanelContainer').scrollTop(0);
}

function TestIntegrationType6() {
	var clientId = $textboxIntegrationType6ClientID.val().trim();
	var clientSecret = $textboxIntegrationType6ClientSecret.val().trim();
	var baseUrl = $textboxIntegrationType6UrlBase.val();

	if (clientId === null ||
		clientId.length === 0 ||
		clientSecret === null ||
		clientSecret.length === 0 ||
		baseUrl === null ||
		baseUrl.length === 0) {
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('http://localhost')) {
			return;
		}
	}

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var url = baseUrl;
			if (!url.endsWith('/')) {
				url += '/';
			}
			url += 'v1/oauth2/token';
			$.ajax({
				type: "POST",
				url: url,
				data: $.param({ client_id: clientId, client_secret: clientSecret, grant_type: 'client_credentials', scope: 'scope1' }),
				contentType: "application/x-www-form-urlencoded",
				dataType: "json",
				dontAddHeaders: true,
				success: function (data) {
					console.log(data);

					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), 'Se validó correctamente la URL');
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), 'No se pudo validar la url con los datos configurados');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ValidateIntegrationType7(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "7") {
		return;
	}

	var user = $textboxIntegrationType7User.val().trim();
	var password = $textboxIntegrationType7Password.val().trim();
	var baseUrl = $textboxIntegrationType7BaseUrl.val();
	var mediaId = $textboxIntegrationType7MediaID.val().trim();

	if (user === null ||
		user.length === 0 ||
		password === null ||
		password.length === 0 ||
		mediaId === null ||
		mediaId.length === 0) {
		e.IsValid = false;
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('http://localhost')) {
			e.IsValid = false;
			return;
		}
	}
}

function TestIntegrationType7() {
	var user = $textboxIntegrationType7User.val().trim();
	var password = $textboxIntegrationType7Password.val().trim();
	var baseUrl = $textboxIntegrationType7BaseUrl.val().trim();

	if (user === null ||
		user.length === 0 ||
		password === null ||
		password.length === 0 ||
		baseUrl === null ||
		baseUrl.length === 0) {
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('http://localhost')) {
			return;
		}
	}

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			$.ajax({
				type: "POST",
				url: 'ServicesWhatsapp.aspx/ValidateInteraxaUrl',
				data: JSON.stringify({ url: baseUrl, username: user, password: password }),
				contentType: "application/json",
				dataType: "json",
				dontAddHeaders: true,
				success: function (data) {
					console.log(data);

					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n('configuration-serviceswhatsapp-whatsapp7_validate-success'));
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n('configuration-serviceswhatsapp-whatsapp7_validate-error'));
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ValidateIntegrationType8(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "8") {
		return;
	}

	var appname = $textboxIntegrationType8AppName.val().trim();

	if (appname === null ||
		appname.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateIntegrationType9(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "9") {
		return;
	}

	var accountsid = $textboxIntegrationType9AccountSid.val().trim();
	var authtoken = $textboxIntegrationType9AuthToken.val().trim();

	if (accountsid === null ||
		accountsid.length === 0 ||
		authtoken === null ||
		authtoken.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateIntegrationType10(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "10") {
		return;
	}

	var $textboxIntegrationType10AccessToken = $('#textboxIntegrationType10AccessToken');
	var $textboxIntegrationType10BaseUrl = $('#textboxIntegrationType10BaseUrl');

	var accessToken = $textboxIntegrationType10AccessToken.val();
	var baseUrl = $textboxIntegrationType10BaseUrl.val();

	if (accessToken === null ||
		accessToken.length === 0 ||
		baseUrl === null ||
		baseUrl.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp10-error"));
		e.IsValid = false;
		return;
	}

	var urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	if (typeof (inTheCloud) !== 'undefined' && inTheCloud) {
		urlRegex = /^https:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;
	}

	if (!urlRegex.test(baseUrl)) {
		if (!baseUrl.startsWith('https://localhost')) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp10-error"));
			e.IsValid = false;
			return;
		}
	}

	let $checkboxIntegrationType10SendToServiceBus = $('#checkboxIntegrationType10SendToServiceBus');
	if ($checkboxIntegrationType10SendToServiceBus.is(':checked')) {
		let $textboxIntegrationType10AccountID = $('#textboxIntegrationType10AccountID');
		let $textboxIntegrationType10LineID = $('#textboxIntegrationType10LineID');
		let accountId = $textboxIntegrationType10AccountID.val().trim();
		let lineId = $textboxIntegrationType10LineID.val().trim();
		if (accountId.length === 0 ||
			lineId.length === 0 ||
			accountId === lineId) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp10-error-service_bus"));
			e.IsValid = false;
			return;
		}

		let shortguidRegex = /^[a-zA-Z-_0-9]{22}$/;
		if (!shortguidRegex.test(accountId) ||
			!shortguidRegex.test(lineId)) {
			$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp10-error-service_bus"));
			e.IsValid = false;
			return;
		}
	}
}

function ValidateIntegrationType11(sender, e) {
	e.IsValid = true;

	var value = $dropdownlistIntegrationType.val();
	if (value !== "11") {
		return;
	}

	var $textboxIntegrationType11AccessToken = $('#textboxIntegrationType11AccessToken');
	var $textboxIntegrationType11GraphApiVersion = $('#textboxIntegrationType11GraphApiVersion');
	var $textboxIntegrationType11PhoneNumberId = $('#textboxIntegrationType11PhoneNumberId');
	var $textboxIntegrationType11WabaId = $('#textboxIntegrationType11WabaId');

	var accessToken = $textboxIntegrationType11AccessToken.val();
	var version = $textboxIntegrationType11GraphApiVersion.val();
	var phoneNumberId = $textboxIntegrationType11PhoneNumberId.val();
	var wabaId = $textboxIntegrationType11WabaId.val();

	if (accessToken === null ||
		accessToken.length === 0 ||
		version === null ||
		version.length === 0 ||
		phoneNumberId === null ||
		phoneNumberId.length === 0 ||
		wabaId === null ||
		wabaId.length === 0) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp11-error"));
		e.IsValid = false;
		return;
	}

	var versionRegex = /^v\d{1,2}\.\d$/;

	if (!versionRegex.test(version)) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-whatsapp11-error"));
		e.IsValid = false;
		return;
	}
}

function TestIntegrationType11() {
	var value = $dropdownlistIntegrationType.val();
	if (value !== "11") {
		return;
	}

	var $textboxIntegrationType11AccessToken = $('#textboxIntegrationType11AccessToken');
	var $textboxIntegrationType11GraphApiVersion = $('#textboxIntegrationType11GraphApiVersion');
	var $textboxIntegrationType11PhoneNumberId = $('#textboxIntegrationType11PhoneNumberId');
	var $textboxIntegrationType11WabaId = $('#textboxIntegrationType11WabaId');

	var accessToken = $textboxIntegrationType11AccessToken.val();
	var version = $textboxIntegrationType11GraphApiVersion.val();
	var phoneNumberId = $textboxIntegrationType11PhoneNumberId.val();
	var wabaId = $textboxIntegrationType11WabaId.val();

	if (accessToken === null ||
		accessToken.length === 0 ||
		version === null ||
		version.length === 0 ||
		wabaId === null ||
		wabaId.length === 0) {
		return;
	}

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, version: version, wabaId: wabaId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/ValidateCloudApiAccessToken",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (data.d.TokenInfo.IsValid) {
							if (data.d.TokenInfo.Scopes.indexOf('whatsapp_business_management') === -1) {
								AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-missing_scope", 'whatsapp_business_management'), null, null, 'Warning');
								return;
							}

							if (data.d.TokenInfo.Scopes.indexOf('whatsapp_business_messaging') === -1) {
								AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-missing_scope", 'whatsapp_business_messaging'), null, null, 'Warning');
								return;
							}

							if (typeof (data.d.PhoneNumbers) !== 'undefined') {
								if (typeof (data.d.PhoneNumbers) === 'string') {
									data.d.PhoneNumbers = JSON.parse(data.d.PhoneNumbers);
								}
							}

							if (typeof (data.d.PhoneNumbers) !== 'object' ||
								!Array.isArray(data.d.PhoneNumbers) ||
								data.d.PhoneNumbers.length === 0) {
								AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-valid_no_accounts"), null, null, 'Warning');
								return;
							}

							let $divIntegrationType11PhoneNumbers = $('#divIntegrationType11PhoneNumbers');
							$divIntegrationType11PhoneNumbers.empty();

							for (let i = 0; i < data.d.PhoneNumbers.length; i++) {
								var $divPage = $('<div class="facebook-pages-page"></div>');
								$divPage.append('<div class="facebook-pages-page-avatar"><span class="fab fa-2x fa-whatsapp"></span></div>');
								var $divPageData = $('<div class="facebook-pages-page-data"></div>');
								$divPage.append($divPageData);
								$divPageData.append('<div class="facebook-pages-page-name">' + data.d.PhoneNumbers[i].display_phone_number + '</div>');
								$divPageData.append('<div class="facebook-pages-page-moreinfo"><span>ID:</span>' + data.d.PhoneNumbers[i].id + '</span></div>');
								$divPage.click(data.d.PhoneNumbers[i], function (event) {
									$('#textboxIntegrationType11PhoneNumberId').val(event.data.id);
									$.colorbox.close();
								});
								$divIntegrationType11PhoneNumbers.append($divPage);
							}

							$.colorbox({
								transition: 'elastic',
								speed: 100,
								inline: true,
								href: "#divCloudApiTokenAndPhoneNumbers",
								overlayClose: false,
								width: '500px',
								initialWidth: '500px',
								preloading: false,
								showBackButton: false,
								closeButton: false
							});
						}
						else {
							AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-invalid"), null, null, 'Error');
						}
					}
					else {
						console.log('Error al consultar si el access token de cloud api es válido: ' + data.d.Error.Message);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-failed"), null, null, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar si el access token de cloud api es válido: ' + jqXHR.responseText);
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-whatsapp11-access_token-failed"), null, null, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ValidateIntegrationType6SurveyToSend(sender, e) {
	e.IsValid = false;

	if (!$checkboxIntegrationType6EnableSurveys.is(':checked')) {
		e.IsValid = true;
		return;
	}

	if ($dropdownlistIntegrationType6Survey.val() != '-1') {
		e.IsValid = true;
		return;
	}
}

function ValidateIntegrationType6SurveyInvitation(sender, e) {
	e.IsValid = false;

	if (!$checkboxIntegrationType6EnableSurveys.is(':checked')) {
		e.IsValid = true;
		return;
	}

	let surveyId = $dropdownlistIntegrationType6Survey.val();
	let survey = availableSurveys.find(s => s.ID === surveyId);

	if ($('#textboxIntegrationType6SurveyInvitation').val().length > 0) {
		if (survey.Type === SurveyTypes.Movistar) {
			e.IsValid = true;
			return;
		}

		if ($('#textboxIntegrationType6SurveyInvitation').val().indexOf("@@LINK@@") > -1) {
			e.IsValid = true;
			return;
		}
	}
}

function ValidateIntegrationType6SurveyExpiration(sender, e) {
	e.IsValid = false;

	if (!$checkboxIntegrationType6EnableSurveys.is(':checked')) {
		e.IsValid = true;
		return;
	}

	if ($('#textboxIntegrationType6SurveyExpiration').val().length > 0) {
		e.IsValid = true;
		return;
	}
}

function ValidateIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes(sender, e) {
	e.IsValid = false;

	if (!$checkboxIntegrationType6EnableSurveys.is(':checked')) {
		e.IsValid = true;
		return;
	}

	var minutes = $('#textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes').val();
	if (minutes.length === 0) {
		return;
	}

	minutes = parseInt(minutes, 10);
	if (isNaN(minutes) || minutes < 0 || minutes > 172800) {
		return;
	}

	e.IsValid = true;
}

function ImportYoizenHSMs() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType10(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-invalid_config"), null, null, 'Warning');
		return;
	}

	var $textboxIntegrationType10AccessToken = $('#textboxIntegrationType10AccessToken');
	var $textboxIntegrationType10BaseUrl = $('#textboxIntegrationType10BaseUrl');

	var accessToken = $textboxIntegrationType10AccessToken.val();
	var baseUrl = $textboxIntegrationType10BaseUrl.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, baseUrl: baseUrl });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountHSMs",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Templates) === 'string') {
							if (data.d.Templates.length > 0) {
								data.d.Templates = JSON.parse(data.d.Templates);
							}
							else {
								data.d.Templates = [];
							}
						}

						for (let i = 0; i < templates.length; i++) {
							let template = templates[i];

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								template.Exists = true;
								template.Status = existingTemplate.status;
							}
							else {
								template.Exists = false;
							}
						}

						for (let i = 0; i < data.d.Templates.length; i++) {
							let template = data.d.Templates[i];

							if (template.status !== 'APPROVED') {
								continue;
							}

							if (templates.findIndex(t => t.ElementName === template.name &&
								t.Language === template.language) >= 0) {
								continue;
							}

							let convertedTemplate = {
								AvaiableForAgents: false,
								AvaiableForIntegrations: false,
								AvaiableForSupervisors: false,
								AllowToConfigureSendHSMIfCaseOpen: template.AllowToConfigureSendHSMIfCaseOpen,
								Description: `${template.name.replaceAll('_', ' ')} - ${template.language}`,
								Category: template.category,
								ElementName: template.name,
								Language: template.language,
								Namespace: data.d.Namespace,
								HeaderType: HSMTemplateHeaderTypes.None,
								FooterType: HSMTemplateFooterTypes.None,
								ButtonsType: HSMTemplateButtonsTypes.None
							};

							let headerComponent = template.components.find(c => c.type === 'HEADER');
							if (typeof (headerComponent) === 'object' && headerComponent !== null) {
								switch (headerComponent.format) {
									case 'TEXT':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Text;
										convertedTemplate.HeaderText = headerComponent.text;
										convertedTemplate.HeaderTextParameter = null;
										if (headerComponent.text.indexOf('{{1}}') >= 0) {
											convertedTemplate.HeaderTextParameter = {
												Name: `header_1`,
												Description: `${$.i18n('configuration-serviceswhatsapp-template-header')}`
											};

											convertedTemplate.HeaderText = convertedTemplate.HeaderText.replace('{{1}}', '{{header_1}}');
										}
										break;
									case 'IMAGE':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Image;
										break;
									case 'DOCUMENT':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Document;
										break;
									case 'VIDEO':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Video;
										break;
									case 'LOCATION':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Location;
										break;
									default:
								}
							}

							let bodyComponent = template.components.find(c => c.type === 'BODY');
							if (typeof (bodyComponent) === 'object' && bodyComponent !== null) {
								convertedTemplate.Template = bodyComponent.text;
								convertedTemplate.TemplateParameters = [];

								if (bodyComponent.text.indexOf('{{1}}') >= 0) {
									var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
									var matches = parametersRegex.exec(bodyComponent.text);
									while (matches !== null) {
										var parameterName = matches[0].replace('{{', '').replace('}}', '');

										convertedTemplate.TemplateParameters.push(`${parameterName}=${parameterName}`);

										matches = parametersRegex.exec(bodyComponent.text);
									}
								}
							}

							let footerComponent = template.components.find(c => c.type === 'FOOTER');
							if (typeof (footerComponent) === 'object' && footerComponent !== null) {
								convertedTemplate.FooterType = HSMTemplateFooterTypes.Text;
								convertedTemplate.FooterText = footerComponent.text;
							}

							let buttonsComponent = template.components.find(c => c.type === 'BUTTONS');
							if (typeof (buttonsComponent) === 'object' && buttonsComponent !== null) {
								if (typeof (buttonsComponent.buttons) === 'object' &&
									Array.isArray(buttonsComponent.buttons) &&
									buttonsComponent.buttons.length > 0) {

									const hasQuickReply = buttonsComponent.buttons.some(b => b.type === 'QUICK_REPLY');
									const hasCallToAction = buttonsComponent.buttons.some(b => ['PHONE_NUMBER', 'URL', 'COPY_CODE'].includes(b.type));
									const hasAuthetnticationCategory = template.category === 'AUTHENTICATION';
									if (hasQuickReply && hasCallToAction) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.Mixed;
									}
									else if (hasQuickReply && !hasCallToAction) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.QuickReply;
									} else if (hasCallToAction && hasAuthetnticationCategory) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.AuthCode;
									}
									else {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.CallToAction;
									}

									convertedTemplate.Buttons = [];
									for (let j = 0; j < buttonsComponent.buttons.length; j++) {

										let button = buttonsComponent.buttons[j];
										let convertedButton = {
											Text: button.text,
											CallToActionButtonType: null,
											UrlButtonType: null,
											UrlParameter: null,
											QuickReplyParameter: null,
											FlowParameter: null
										};

										convertedTemplate.Buttons.push(convertedButton);

										switch (button.type) {
											case 'QUICK_REPLY':
												convertedButton.QuickReplyParameter = {
													Name: `quickreply_${(j + 1).toString()}`,
													Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
												};
												break;
											case 'URL':
												if (convertedTemplate.ButtonsType === HSMTemplateButtonsTypes.AuthCode) {
													convertedButton.AuthCodeButtonType = HSMTemplateAuthCodeButtonTypes.AuthCode;
													convertedButton.AuthCodeParameter = {
														Name: `authCode_${(j + 1).toString()}`,
														Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
													};
													break;
												}
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Url;
												if (button.url.indexOf('{{1}}') >= 0) {
													convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Dynamic;
													convertedButton.UrlParameter = {
														Name: `url_${(j + 1).toString()}`,
														Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
													};

													convertedButton.Text = convertedButton.Text.replace('{{1}}', `{{button_${(j + 1).toString()}}}`);
												}
												else {
													convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Fixed;
												}
												break;
											case 'PHONE_NUMBER':
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Call;
												break;
											case 'COPY_CODE':
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.OfferCode;
												convertedButton.OfferCodeParameter = {
													Name: `offerCode_${(j + 1).toString()}`,
													Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
												};
												break;
											case "FLOW":
												const urlData = GetUrlFlowData(button.flow_id);
												if (typeof (urlData) !== 'string') {
													console.log("Se ignora la plantilla por no tener URL del flow");
													continue;
												}

												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Flow;
												convertedButton.FlowParameter = {
													Name: `flow_${(j + 1).toString()}`,
													FlowID: button.flow_id,
													FlowAction: button.flow_action,
													NavigateScreen: button.navigate_screen,
													ActionData: null,
													FlowDataUrl: urlData
												};
												break;
											default:
										}
									}
								}
							}

							convertedTemplate.Exists = true;
							convertedTemplate.Status = template.status;

							templates.push(convertedTemplate);
							datatableTemplates.row.add(convertedTemplate);

							if ($hiddenHSMTemplates.val() == 0 && data.d.Templates.length > 0) {
								$messageHSMTemplatesEmpty.hide();
								$tableHSMTemplatesv2.parent().show();
							}
						}

						datatableTemplates.draw();

						$.colorbox.close();
					}
					else {
						console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function GetUrlFlowData(flowId) {

	if (metaFlows.length == 0)
		return;

	let flow = metaFlows.find(f => f.ID == flowId);

	if (flow != null && typeof flow == 'object') {
		return flow.Data.DownloadUrl;
	}

	console.error(`El flowID ${flowId} no existe en el listado de flujos`);
	return;
}

function ConvertToStringCategory(category) {

	let intCategory = parseInt(category, 10);

	if (isNaN(intCategory)) {
		return category;
	}

	switch (intCategory) {
		case FlowCategories.Other:
			return "OTHER";
		case FlowCategories.Generic:
			return "LEAD_GENERATION";
		case FlowCategories.SignIn:
			return "SIGN_IN";
		case FlowCategories.SignUp:
			return "SIGN_UP";
		case FlowCategories.AppointmentBooking:
			return "APPOINTMENT_BOOKING";
		case FlowCategories.ContactUs:
			return "CONTACT_US";
		case FlowCategories.CustomerSupport:
			return "CUSTOMER_SUPPORT";
		case FlowCategories.Survey:
			return "SURVEY";
		default:
			return intCategory;
	}
}

function ConvertToStatus(statusString) {
	let status = statusString.toUpperCase();
	switch (status) {
		case "DRAFT":
			return FlowStatuses.Draft;
		case "PUBLISHED":
			return FlowStatuses.Published;
		case "DEPRECATED":
			return FlowStatuses.Deprecated;
		case "BLOCKED":
			return FlowStatuses.Blocked;
		case "THROTTLED":
			return FlowStatuses.Throttled;
		default:
			break;
	}
}

function GetAssetFlowData(flow) {
	if (flow.assets != null && typeof (flow.assets) === 'object') {
		if (typeof (flow.assets.data) === 'object' &&
			Array.isArray(flow.assets.data) &&
			flow.assets.data.length > 0) {

			let flowData = {
				AssetType: flow.assets.data[0].asset_type,
				Name: flow.assets.data[0].name,
				DownloadUrl: flow.assets.data[0].download_url
			}

			return flowData;
		}
	}

	return;
};

function ImportYoizenFlows() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType10(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-invalid_config"), null, null, 'Warning');
		return;
	}

	var $textboxIntegrationType10AccessToken = $('#textboxIntegrationType10AccessToken');
	var $textboxIntegrationType10BaseUrl = $('#textboxIntegrationType10BaseUrl');

	var accessToken = $textboxIntegrationType10AccessToken.val();
	var baseUrl = $textboxIntegrationType10BaseUrl.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, baseUrl: baseUrl });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountFlows",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Flows) === 'string') {
							if (data.d.Flows.length > 0) {
								data.d.Flows = JSON.parse(data.d.Flows);
							}
							else {
								data.d.Flows = [];
							}
						}

						for (let i = 0; i < data.d.Flows.length; i++) {
							let flow = data.d.Flows[i];

							let existingFlowIndex = metaFlows.findIndex(f => flow.id === f.ID);

							if (existingFlowIndex !== -1) {
								metaFlows[existingFlowIndex].Status = ConvertToStatus(flow.status);
								metaFlows[existingFlowIndex].Data = GetAssetFlowData(flow);
								metaFlows[existingFlowIndex].Categories = flow.categories;
								continue;
							}

							let convertedFlow = {
								ID: flow.id,
								Enabled: true,
								Name: flow.name,
								ValidationErrors: flow.validation_errors,
								JsonVersion: flow.json_version,
								Status: flow.status,
								Categories: flow.categories,
								JsonPreviewUrl: null,
								ExpirationJsonPreview: null,
								Token: null
							};
							convertedFlow.Status = ConvertToStatus(flow.status);

							if (flow.preview != null && typeof (flow.preview) === 'object') {
								convertedFlow.JsonPreviewUrl = flow.preview.preview_url;
								convertedFlow.ExpirationJsonPreview = flow.preview.expires_at;
							}

							convertedFlow.Data = GetAssetFlowData(flow);

							convertedFlow.Exists = true;
							convertedFlow.Status = flow.status;

							metaFlows.push(convertedFlow);
							datatableFlows.row.add(convertedFlow);

							if ($hiddenFlows.val() == 0 && data.d.Flows.length > 0) {
								$messageFlowsEmpty.hide();
								$tableFlows.parent().show();
							}
						}

						datatableFlows.draw();
						$.colorbox.close();
					}
					else {
						console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}


function ImportCloudApiFlows() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType11(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-invalid_config"), null, null, 'Warning');
		return;
	}

	var $textboxIntegrationType11AccessToken = $('#textboxIntegrationType11AccessToken');
	var $textboxIntegrationType11GraphApiVersion = $('#textboxIntegrationType11GraphApiVersion');
	var $textboxIntegrationType11PhoneNumberId = $('#textboxIntegrationType11PhoneNumberId');
	var $textboxIntegrationType11WabaId = $('#textboxIntegrationType11WabaId');

	var accessToken = $textboxIntegrationType11AccessToken.val();
	var version = $textboxIntegrationType11GraphApiVersion.val();
	var phoneNumberId = $textboxIntegrationType11PhoneNumberId.val();
	var wabaId = $textboxIntegrationType11WabaId.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, version: version, wabaId: wabaId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountFlowsCloudApi",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Flows) === 'string') {
							if (data.d.Flows.length > 0) {
								data.d.Flows = JSON.parse(data.d.Flows);
							}
							else {
								data.d.Flows = [];
							}
						}

						for (let i = 0; i < data.d.Flows.length; i++) {
							let flow = data.d.Flows[i];

							let existingFlowIndex = metaFlows.findIndex(f => flow.id === f.ID);

							if (existingFlowIndex !== -1) {
								metaFlows[existingFlowIndex].Status = ConvertToStatus(flow.status);
								metaFlows[existingFlowIndex].Data = GetAssetFlowData(flow);
								metaFlows[existingFlowIndex].Categories = flow.categories;
								continue;
							}

							let convertedFlow = {
								ID: flow.id,
								Enabled: true,
								Name: flow.name,
								ValidationErrors: flow.validation_errors,
								JsonVersion: flow.json_version,
								Status: flow.status,
								Categories: flow.categories,
								JsonPreviewUrl: null,
								ExpirationJsonPreview: null,
								Token: null
							};
							convertedFlow.Status = ConvertToStatus(flow.status);

							if (flow.preview != null && typeof (flow.preview) === 'object') {
								convertedFlow.JsonPreviewUrl = flow.preview.preview_url;
								convertedFlow.ExpirationJsonPreview = flow.preview.expires_at;
							}

							convertedFlow.Data = GetAssetFlowData(flow);

							convertedFlow.Exists = true;
							convertedFlow.Status = flow.status;

							metaFlows.push(convertedFlow);
							datatableFlows.row.add(convertedFlow);

							if ($hiddenFlows.val() == 0 && data.d.Flows.length > 0) {
								$messageFlowsEmpty.hide();
								$tableFlows.parent().show();
							}
						}

						datatableFlows.draw();
						$.colorbox.close();
					}
					else {
						console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ConfirmDeleteHSMs() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType10(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-invalid_config"), null, null, 'Warning');
		return;
	}

	var countryCode = $textboxWhatsAppSelectedPhoneCode.val();
	var phoneNumber = $textboxWhatsAppPhoneNumber.val();
	var fullPhoneNumber = countryCode + phoneNumber;

	var $textboxIntegrationType10AccessToken = $('#textboxIntegrationType10AccessToken');
	var $textboxIntegrationType10BaseUrl = $('#textboxIntegrationType10BaseUrl');

	var accessToken = $textboxIntegrationType10AccessToken.val();
	var baseUrl = $textboxIntegrationType10BaseUrl.val();
	ConfirmDialog({
		title: $.i18n('configuration-serviceswhatsapp-message_template-title'),
		message: $.i18n('configuration-serviceswhatsapp-message_template-massive-delete-question', fullPhoneNumber),
		onAccept: function (args) {

			LoadingDialog({
				title: $.i18n("configuration-serviceswhatsapp-title"),
				onTimeout: function () {
					var dataToSend = JSON.stringify({ accessToken: accessToken, baseUrl: baseUrl });

					$.ajax({
						type: "POST",
						url: "ServicesWhatsapp.aspx/RetrieveAccountHSMs",
						data: dataToSend,
						contentType: "application/json; charset=utf-8",
						dataType: "json",
						success: function (data) {
							if (data.d.Success) {
								if (typeof (data.d.Templates) === 'string') {
									if (data.d.Templates.length > 0) {
										data.d.Templates = JSON.parse(data.d.Templates);
									}
									else {
										data.d.Templates = [];
									}
								}

								let removesTemplates = 0;
								for (let i = templates.length - 1; i >= 0; i--) {
									let template = templates[i];

									let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
									if (typeof (existingTemplate) === 'undefined') {
										templates.splice(i, 1);
										removesTemplates++;
									}
								}

								if (removesTemplates > 0) {
									AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-delete_templates_count", removesTemplates), null, null);

									$hiddenHSMTemplates.val(JSON.stringify(templates));
									CreateHsmTable();
								}
								else {
									AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-delete_templates_not_found"), null, null);
								}

							}
							else {
								console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
								AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
							}
						},
						error: function (jqXHR, textStatus, errorThrown) {
							console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
							AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
						}
					});
				},
				timeout: 500,
				autoClose: false
			});
		}
	});
}

function CheckNotDefinedYoizenHSMs() {
	var $textboxIntegrationType10AccessToken = $('#textboxIntegrationType10AccessToken');
	var $textboxIntegrationType10BaseUrl = $('#textboxIntegrationType10BaseUrl');

	var accessToken = $textboxIntegrationType10AccessToken.val();
	var baseUrl = $textboxIntegrationType10BaseUrl.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, baseUrl: baseUrl });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountHSMs",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Templates) === 'string') {
							if (data.d.Templates.length > 0) {
								data.d.Templates = JSON.parse(data.d.Templates);
							}
							else {
								data.d.Templates = [];
							}
						}

						for (let i = 0; i < templates.length; i++) {
							let template = templates[i];

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								template.Exists = true;
								template.Status = existingTemplate.status;

								if (typeof (data.d.Namespace) === 'string' && template.Namespace !== data.d.Namespace) {
									template.Namespace = data.d.Namespace;
								}

								if (typeof (existingTemplate.category) === 'string' && template.Category !== existingTemplate.category) {
									template.Category = existingTemplate.category;
								}
							}
							else {
								template.Exists = false;
							}
						}

						let $trs = $('> tbody > tr', $tableHSMTemplatesv2);

						for (let i = 0; i < $trs.length; i++) {
							let $tr = $($trs.get(i))
							let template = $tr.prop('template');

							$tr.removeClass('exists not-exists');
							$tr.removeAttr('status');

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								$tr.addClass('exists');
								$tr.attr('status', existingTemplate.status);
							}
							else {
								$tr.addClass('not-exists');
							}
						}

						$.colorbox.close();
					}
					else {
						console.log('Error al consultar los HSMs definidos en el servicio: ' + data.d.Error.Message);
						$.colorbox.close();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar los HSMs definidos en el servicio: ' + jqXHR.responseText);
					$.colorbox.close();
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ImportCloudApiHSMs() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType11(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_11-invalid_config"), null, null, 'Warning');
		return;
	}

	var $textboxIntegrationType11AccessToken = $('#textboxIntegrationType11AccessToken');
	var $textboxIntegrationType11GraphApiVersion = $('#textboxIntegrationType11GraphApiVersion');
	var $textboxIntegrationType11PhoneNumberId = $('#textboxIntegrationType11PhoneNumberId');
	var $textboxIntegrationType11WabaId = $('#textboxIntegrationType11WabaId');

	var accessToken = $textboxIntegrationType11AccessToken.val();
	var version = $textboxIntegrationType11GraphApiVersion.val();
	var phoneNumberId = $textboxIntegrationType11PhoneNumberId.val();
	var wabaId = $textboxIntegrationType11WabaId.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, version: version, wabaId: wabaId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountHSMsCloudApi",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Templates) === 'string') {
							if (data.d.Templates.length > 0) {
								data.d.Templates = JSON.parse(data.d.Templates);
							}
							else {
								data.d.Templates = [];
							}
						}

						for (let i = 0; i < templates.length; i++) {
							let template = templates[i];

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								template.Exists = true;
								template.Status = existingTemplate.status;
							}
							else {
								template.Exists = false;
							}
						}

						for (let i = 0; i < data.d.Templates.length; i++) {
							let template = data.d.Templates[i];

							if (template.status !== 'APPROVED') {
								continue;
							}

							if (templates.findIndex(t => t.ElementName === template.name &&
								t.Language === template.language) >= 0) {
								continue;
							}

							let convertedTemplate = {
								AvaiableForAgents: false,
								AvaiableForIntegrations: false,
								AvaiableForSupervisors: false,
								AllowToConfigureSendHSMIfCaseOpen: template.AllowToConfigureSendHSMIfCaseOpen,
								Description: `${template.name.replaceAll('_', ' ')} - ${template.language}`,
								ElementName: template.name,
								Category: template.category,
								Language: template.language,
								Namespace: data.d.Namespace,
								HeaderType: HSMTemplateHeaderTypes.None,
								FooterType: HSMTemplateFooterTypes.None,
								ButtonsType: HSMTemplateButtonsTypes.None
							};

							let headerComponent = template.components.find(c => c.type === 'HEADER');
							if (typeof (headerComponent) === 'object' && headerComponent !== null) {
								switch (headerComponent.format) {
									case 'TEXT':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Text;
										convertedTemplate.HeaderText = headerComponent.text;
										convertedTemplate.HeaderTextParameter = null;
										if (headerComponent.text.indexOf('{{1}}') >= 0) {
											convertedTemplate.HeaderTextParameter = {
												Name: `header_1`,
												Description: `${$.i18n('configuration-serviceswhatsapp-template-header')}`
											};

											convertedTemplate.HeaderText = convertedTemplate.HeaderText.replace('{{1}}', '{{header_1}}');
										}
										break;
									case 'IMAGE':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Image;
										break;
									case 'DOCUMENT':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Document;
										break;
									case 'VIDEO':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
										convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Video;
										break;
									case 'LOCATION':
										convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Location;
										break;
									
									default:
								}
							}

							let bodyComponent = template.components.find(c => c.type === 'BODY');
							if (typeof (bodyComponent) === 'object' && bodyComponent !== null) {
								convertedTemplate.Template = bodyComponent.text;
								convertedTemplate.TemplateParameters = [];

								if (bodyComponent.text.indexOf('{{1}}') >= 0) {
									var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
									var matches = parametersRegex.exec(bodyComponent.text);
									while (matches !== null) {
										var parameterName = matches[0].replace('{{', '').replace('}}', '');

										convertedTemplate.TemplateParameters.push(`${parameterName}=${parameterName}`);

										matches = parametersRegex.exec(bodyComponent.text);
									}
								}
							}

							let footerComponent = template.components.find(c => c.type === 'FOOTER');
							if (typeof (footerComponent) === 'object' && footerComponent !== null) {
								convertedTemplate.FooterType = HSMTemplateFooterTypes.Text;
								convertedTemplate.FooterText = footerComponent.text;
							}

							let buttonsComponent = template.components.find(c => c.type === 'BUTTONS');
							if (typeof (buttonsComponent) === 'object' && buttonsComponent !== null) {
								if (typeof (buttonsComponent.buttons) === 'object' &&
									Array.isArray(buttonsComponent.buttons) &&
									buttonsComponent.buttons.length > 0) {

									const hasQuickReply = buttonsComponent.buttons.some(b => b.type === 'QUICK_REPLY');
									const hasCallToAction = buttonsComponent.buttons.some(b => ['PHONE_NUMBER', 'URL', 'COPY_CODE'].includes(b.type));
                                    const hasAuthetnticationCategory = template.category === 'AUTHENTICATION';
									if (hasQuickReply && hasCallToAction) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.Mixed;
									} else if (hasQuickReply && !hasCallToAction) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.QuickReply;
									} else if (hasCallToAction && hasAuthetnticationCategory) {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.AuthCode;
									} else {
										convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.CallToAction;
									}

									convertedTemplate.Buttons = [];
									for (let j = 0; j < buttonsComponent.buttons.length; j++) {

										let button = buttonsComponent.buttons[j];
										let convertedButton = {
											Text: button.text,
											CallToActionButtonType: null,
											UrlButtonType: null,
											UrlParameter: null,
											QuickReplyParameter: null
										};

										convertedTemplate.Buttons.push(convertedButton);

										switch (button.type) {
											case 'QUICK_REPLY':
												convertedButton.QuickReplyParameter = {
													Name: `quickreply_${(j + 1).toString()}`,
													Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
												};
												break;
											case 'URL':
												if (convertedTemplate.ButtonsType === HSMTemplateButtonsTypes.AuthCode)
												{
													convertedButton.AuthCodeButtonType = HSMTemplateAuthCodeButtonTypes.AuthCode;
													convertedButton.AuthCodeParameter = {
														Name: `authCode_${(j + 1).toString()}`,
														Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
													};
													break;
												}
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Url;
												if (button.url.indexOf('{{1}}') >= 0) {
													convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Dynamic;
													convertedButton.UrlParameter = {
														Name: `url_${(j + 1).toString()}`,
														Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
													};

													convertedButton.Text = convertedButton.Text.replace('{{1}}', `{{button_${(j + 1).toString()}}}`);
												}
												else {
													convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Fixed;
												}
												break;
											case 'PHONE_NUMBER':
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Call;
												break;
											case 'COPY_CODE':
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.OfferCode;
												convertedButton.OfferCodeParameter = {
													Name: `offerCode_${(j + 1).toString()}`,
													Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
												};
												break;
											case "FLOW":
												const urlData = GetUrlFlowData(button.flow_id);
												if (typeof (urlData) !== 'string') {
													break;
												}
												convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Flow;
												convertedButton.FlowParameter = {
													Name: `flow_${(j + 1).toString()}`,
													FlowID: button.flow_id,
													FlowAction: button.flow_action,
													NavigateScreen: button.navigate_screen,
													ActionData: null,
													FlowDataUrl: urlData
												};
												break;
											default:
										}
									}
								}
							}

							convertedTemplate.Exists = true;
							convertedTemplate.Status = template.status;

							templates.push(convertedTemplate);
							datatableTemplates.row.add(convertedTemplate);

							if ($hiddenHSMTemplates.val() == 0 && data.d.Templates.length > 0) {
								$messageHSMTemplatesEmpty.hide();
								$tableHSMTemplatesv2.parent().show();
							}
						}

						datatableTemplates.draw();

						$.colorbox.close();
					}
					else {
						console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
					AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function CheckNotDefinedCloudApiHSMs() {
	var $textboxIntegrationType11AccessToken = $('#textboxIntegrationType11AccessToken');
	var $textboxIntegrationType11GraphApiVersion = $('#textboxIntegrationType11GraphApiVersion');
	var $textboxIntegrationType11PhoneNumberId = $('#textboxIntegrationType11PhoneNumberId');
	var $textboxIntegrationType11WabaId = $('#textboxIntegrationType11WabaId');

	var accessToken = $textboxIntegrationType11AccessToken.val();
	var version = $textboxIntegrationType11GraphApiVersion.val();
	var phoneNumberId = $textboxIntegrationType11PhoneNumberId.val();
	var wabaId = $textboxIntegrationType11WabaId.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ accessToken: accessToken, version: version, wabaId: wabaId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountHSMsCloudApi",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Templates) === 'string') {
							if (data.d.Templates.length > 0) {
								data.d.Templates = JSON.parse(data.d.Templates);
							}
							else {
								data.d.Templates = [];
							}
						}

						for (let i = 0; i < templates.length; i++) {
							let template = templates[i];

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								template.Exists = true;
								template.Status = existingTemplate.status;

								if (typeof (data.d.Namespace) === 'string' && template.Namespace !== data.d.Namespace) {
									template.Namespace = data.d.Namespace;
								}

								if (typeof (existingTemplate.category) === 'string' && template.Category !== existingTemplate.category) {
									template.Category = existingTemplate.category;
								}
							}
							else {
								template.Exists = false;
							}
						}

						let $trs = $('> tbody > tr', $tableHSMTemplatesv2);

						for (let i = 0; i < $trs.length; i++) {
							let $tr = $($trs.get(i))
							let template = $tr.prop('template');

							$tr.removeClass('exists not-exists');
							$tr.removeAttr('status');

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								$tr.addClass('exists');
								$tr.attr('status', existingTemplate.status);
							}
							else {
								$tr.addClass('not-exists');
							}
						}

						$.colorbox.close();
					}
					else {
						console.log('Error al consultar los HSMs definidos en el servicio: ' + data.d.Error.Message);
						$.colorbox.close();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar los HSMs definidos en el servicio: ' + jqXHR.responseText);
					$.colorbox.close();
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ImportInfobipHSMs() {
	let e = {
		IsValid: true
	}
	ValidateIntegrationType4(null, e);

	if (!e.IsValid) {
		AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-configuration-error"), null, null, 'Warning');
		return;
	}

	var $divImportHSMsWithoutNamespace = $('#divImportHSMsWithoutNamespace');
	var $inputImportHSMWihtoutNamespaceNamespace = $('#inputImportHSMWihtoutNamespaceNamespace');
	var $buttonImportHSMWihtoutNamespaceAccept = $('#buttonImportHSMWihtoutNamespaceAccept');
	var $divImportHSMWihtoutNamespaceError = $('#divImportHSMWihtoutNamespaceError');

	$inputImportHSMWihtoutNamespaceNamespace.val('');
	ToggleValidator($divImportHSMWihtoutNamespaceError, true);
	$buttonImportHSMWihtoutNamespaceAccept.unbind('click');
	$buttonImportHSMWihtoutNamespaceAccept.click(function () {
		var namespace = $inputImportHSMWihtoutNamespaceNamespace.val();
		if (namespace.trim().length === 0) {
			ToggleValidator($divImportHSMWihtoutNamespaceError, false);
			return;
		}

		ToggleValidator($divImportHSMWihtoutNamespaceError, true);

		let $dropdownlistIntegrationType4AuthorizationType = $('#dropdownlistIntegrationType4AuthorizationType');
		let authorizationType = parseInt($dropdownlistIntegrationType4AuthorizationType.val(), 10);

		let countryCode = $textboxWhatsAppSelectedPhoneCode.val();
		let phoneNumber = $textboxWhatsAppPhoneNumber.val();
		let fullPhoneNumber = countryCode + phoneNumber;

		let user = null, password = null, apiKey = null;

		if (authorizationType === 1) {
			let $textboxIntegrationType4User = $('#textboxIntegrationType4User');
			let $textboxIntegrationType4Password = $('#textboxIntegrationType4Password');
			user = $textboxIntegrationType4User.val().trim();
			password = $textboxIntegrationType4Password.val().trim();
		}
		else if (authorizationType === 2) {
			let $textboxIntegrationType4ApiKey = $('#textboxIntegrationType4ApiKey');
			apiKey = $textboxIntegrationType4ApiKey.val().trim();
		}

		let $textboxIntegrationType4BaseUrl = $('#textboxIntegrationType4BaseUrl');
		let baseUrl = $textboxIntegrationType4BaseUrl.val();

		LoadingDialog({
			title: $.i18n("configuration-serviceswhatsapp-title"),
			onTimeout: function () {
				var dataToSend = JSON.stringify({ authorizationType: authorizationType, user: user, password: password, apiKey: apiKey, baseUrl: baseUrl, phoneNumber: fullPhoneNumber });

				$.ajax({
					type: "POST",
					url: "ServicesWhatsapp.aspx/RetrieveAccountHSMsInfobip",
					data: dataToSend,
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					success: function (data) {
						if (data.d.Success) {
							if (typeof (data.d.Templates) === 'string') {
								if (data.d.Templates.length > 0) {
									data.d.Templates = JSON.parse(data.d.Templates);
								}
								else {
									data.d.Templates = [];
								}
							}

							if (typeof (data.d.Namespace) === 'undefined') {
								data.d.Namespace = namespace;
							}

							for (let i = 0; i < templates.length; i++) {
								let template = templates[i];

								let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
								if (typeof (existingTemplate) !== 'undefined') {
									template.Exists = true;
									template.Status = existingTemplate.status;
								}
								else {
									template.Exists = false;
								}
							}

							for (let i = 0; i < data.d.Templates.length; i++) {
								let template = data.d.Templates[i];

								if (template.status !== 'APPROVED') {
									continue;
								}

								if (templates.findIndex(t => t.ElementName === template.name &&
									t.Language === template.language) >= 0) {
									continue;
								}

								let convertedTemplate = {
									AvaiableForAgents: false,
									AvaiableForIntegrations: false,
									AvaiableForSupervisors: false,
									AllowToConfigureSendHSMIfCaseOpen: template.AllowToConfigureSendHSMIfCaseOpen,
									Description: `${template.name.replaceAll('_', ' ')} - ${template.language}`,
									ElementName: template.name,
									Language: template.language,
									Category: template.category,
									Namespace: data.d.Namespace,
									HeaderType: HSMTemplateHeaderTypes.None,
									FooterType: HSMTemplateFooterTypes.None,
									ButtonsType: HSMTemplateButtonsTypes.None
								};

								let headerComponent = template.structure.header;
								if (typeof (headerComponent) === 'object' && headerComponent !== null) {
									switch (headerComponent.format) {
										case 'TEXT':
											convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Text;
											convertedTemplate.HeaderText = headerComponent.text;
											convertedTemplate.HeaderTextParameter = null;
											if (headerComponent.text.indexOf('{{1}}') >= 0) {
												convertedTemplate.HeaderTextParameter = {
													Name: `header_1`,
													Description: `${$.i18n('configuration-serviceswhatsapp-template-header')}`
												};

												convertedTemplate.HeaderText = convertedTemplate.HeaderText.replace('{{1}}', '{{header_1}}');
											}
											break;
										case 'IMAGE':
											convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
											convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Image;
											break;
										case 'DOCUMENT':
											convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
											convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Document;
											break;
										case 'VIDEO':
											convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Media;
											convertedTemplate.HeaderMediaType = HSMTemplateHeaderMediaTypes.Video;
											break;
										case 'LOCATION':
											convertedTemplate.HeaderType = HSMTemplateHeaderTypes.Location;
											break;
										default:
									}
								}

								let bodyComponent = template.structure.body;
								if (typeof (bodyComponent) === 'object' && bodyComponent !== null) {
									convertedTemplate.Template = bodyComponent.text;
									convertedTemplate.TemplateParameters = [];

									if (bodyComponent.text.indexOf('{{1}}') >= 0) {
										var parametersRegex = /\{\{[a-zA-Z0-9_]+\}\}/g;
										var matches = parametersRegex.exec(bodyComponent.text);
										while (matches !== null) {
											var parameterName = matches[0].replace('{{', '').replace('}}', '');

											convertedTemplate.TemplateParameters.push(`${parameterName}=${parameterName}`);

											matches = parametersRegex.exec(bodyComponent.text);
										}
									}
								}

								let footerComponent = template.structure.footer;
								if (typeof (footerComponent) === 'object' && footerComponent !== null) {
									convertedTemplate.FooterType = HSMTemplateFooterTypes.Text;
									convertedTemplate.FooterText = footerComponent.text;
								}

								let buttonsComponent = template.structure.buttons;
								if (typeof (buttonsComponent) === 'object' && buttonsComponent !== null) {
									if (typeof (buttonsComponent.buttons) === 'object' &&
										Array.isArray(buttonsComponent.buttons) &&
										buttonsComponent.buttons.length > 0) {
										if (buttonsComponent.buttons[0].type === 'QUICK_REPLY') {
											convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.QuickReply;
										}
										else {
											convertedTemplate.ButtonsType = HSMTemplateButtonsTypes.CallToAction;
										}

										convertedTemplate.Buttons = [];
										for (let j = 0; j < buttonsComponent.buttons.length; j++) {
											let button = buttonsComponent.buttons[j];
											let convertedButton = {
												Text: button.text,
												CallToActionButtonType: null,
												UrlButtonType: null,
												UrlParameter: null,
												QuickReplyParameter: null
											};

											convertedTemplate.Buttons.push(convertedButton);

											switch (button.type) {
												case 'QUICK_REPLY':
													convertedButton.QuickReplyParameter = {
														Name: `button_${(j + 1).toString()}`,
														Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
													};
													break;
												case 'URL':
													convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Url;
													if (button.url.indexOf('{{1}}') >= 0) {
														convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Dynamic;
														convertedButton.UrlParameter = {
															Name: `button_${(j + 1).toString()}`,
															Description: `${$.i18n('configuration-serviceswhatsapp-template-buttons')} ${(j + 1).toString()}`
														};

														convertedButton.Text = convertedButton.Text.replace('{{1}}', `{{button_${(j + 1).toString()}}}`);
													}
													else {
														convertedButton.UrlButtonType = HSMTemplateCallToActionUrlButtonTypes.Fixed;
													}
													break;
												case 'PHONE_NUMBER':
													convertedButton.CallToActionButtonType = HSMTemplateCallToActionButtonTypes.Call;
													break;
												default:
											}
										}
									}
								}

								convertedTemplate.Exists = true;
								convertedTemplate.Status = template.status;

								templates.push(convertedTemplate);
								datatableTemplates.row.add(convertedTemplate);

								if ($hiddenHSMTemplates.val() == 0 && data.d.Templates.length > 0) {
									$messageHSMTemplatesEmpty.hide();
									$tableHSMTemplatesv2.parent().show();
								}
							}

							datatableTemplates.draw();

							$.colorbox.close();
						}
						else {
							console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
							AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
						AlertDialog($.i18n("configuration-serviceswhatsapp-whatsapp_service"), $.i18n("configuration-serviceswhatsapp-integration_type_10-retrieve_templaltes-failed"), null, null, 'Error');
					}
				});
			},
			timeout: 500,
			autoClose: false
		});
	});

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: $divImportHSMsWithoutNamespace,
		width: '600px',
		initialWidth: '600px',
		preloading: false,
		showBackButton: false,
		closeButton: false,
		onComplete: function () {
			$inputImportHSMWihtoutNamespaceNamespace.focus();
		}
	});
}

function CheckNotDefinedInfobipHSMs() {
	let $dropdownlistIntegrationType4AuthorizationType = $('#dropdownlistIntegrationType4AuthorizationType');
	let authorizationType = parseInt($dropdownlistIntegrationType4AuthorizationType.val(), 10);

	let countryCode = $textboxWhatsAppSelectedPhoneCode.val();
	let phoneNumber = $textboxWhatsAppPhoneNumber.val();
	let fullPhoneNumber = countryCode + phoneNumber;

	let user = null, password = null, apiKey = null;

	if (authorizationType === 1) {
		let $textboxIntegrationType4User = $('#textboxIntegrationType4User');
		let $textboxIntegrationType4Password = $('#textboxIntegrationType4Password');
		user = $textboxIntegrationType4User.val().trim();
		password = $textboxIntegrationType4Password.val().trim();
	}
	else if (authorizationType === 2) {
		let $textboxIntegrationType4ApiKey = $('#textboxIntegrationType4ApiKey');
		apiKey = $textboxIntegrationType4ApiKey.val().trim();
	}

	let $textboxIntegrationType4BaseUrl = $('#textboxIntegrationType4BaseUrl');
	let baseUrl = $textboxIntegrationType4BaseUrl.val();

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			var dataToSend = JSON.stringify({ authorizationType: authorizationType, user: user, password: password, apiKey: apiKey, baseUrl: baseUrl, phoneNumber: fullPhoneNumber });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/RetrieveAccountHSMsInfobip",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						if (typeof (data.d.Templates) === 'string') {
							if (data.d.Templates.length > 0) {
								data.d.Templates = JSON.parse(data.d.Templates);
							}
							else {
								data.d.Templates = [];
							}
						}

						for (let i = 0; i < templates.length; i++) {
							let template = templates[i];

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								template.Exists = true;
								template.Status = existingTemplate.status;

								if (typeof (data.d.Namespace) === 'string' && template.Namespace !== data.d.Namespace) {
									template.Namespace = data.d.Namespace;
								}

								if (typeof (existingTemplate.category) === 'string' && template.Category !== existingTemplate.category) {
									template.Category = existingTemplate.category;
								}
							}
							else {
								template.Exists = false;
							}
						}

						let $trs = $('> tbody > tr', $tableHSMTemplatesv2);

						for (let i = 0; i < $trs.length; i++) {
							let $tr = $($trs.get(i))
							let template = $tr.prop('template');

							$tr.removeClass('exists not-exists');
							$tr.removeAttr('status');

							let existingTemplate = data.d.Templates.find(t => template.ElementName === t.name && template.Language === t.language);
							if (typeof (existingTemplate) !== 'undefined') {
								$tr.addClass('exists');
								$tr.attr('status', existingTemplate.status);
							}
							else {
								$tr.addClass('not-exists');
							}
						}

						$.colorbox.close();
					}
					else {
						console.log('Error al consultar los HSMs definidos en el servicio: ' + data.d.Error.Message);
						$.colorbox.close();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					console.log('Error al consultar los HSMs definidos en el servicio: ' + jqXHR.responseText);
					$.colorbox.close();
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ConfigureFacebookCatalog() {
	var $tableFacebookWizardLoading = $('#tableFacebookWizardLoading');
	var $messageFacebookWizardLoadingError = $('#messageFacebookWizardLoadingError');
	var $td = $('td.text', $messageFacebookWizardLoadingError);
	$messageFacebookWizardLoadingError.hide();
	$tableFacebookWizardLoading.show();

	$buttonChangeFacebookBusiness.hide();
	$buttonFacebookBusinessCatalogAccept.hide();
	$('#messageMustSelectBusiness').hide();
	$('#messageMustSelectCatalog').hide();

	$divFacebookCatalogsSelectBusiness.show();
	$divFacebookCatalogsSelectCatalog.hide();

	$divFacebookLoginButton.unbind('click').click(function () {
		$hiddenFacebookCatalogAccessToken.val('');
		$divFacebookBusiness.hide();
		$messageFacebookAccountError.hide();
		$messageFacebookCouldntValidateAccessToken.hide();
		$messageFacebookNoBusinesses.hide();
		$messageFacebookWizardLoadingError.hide();
		$divFacebookUser.hide();
		$buttonEnterFacebookUrl.show();
		$inputFacebookUrl.val('');
		$.colorbox.resize();
		$('div.text', $divFacebookLoginButton).text($.i18n("configuration-servicesfacebook-login"));
		PopupCenter(facebookLoginUrl, 'login', 400, 300);
	});

	ShowFacebookBusinessCatalogDialog();
}

function PopupCenter(url, title, w, h) {
	// Fixes dual-screen position                         Most browsers      Firefox
	var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
	var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

	var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
	var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

	var left = ((width / 2) - (w / 2)) + dualScreenLeft;
	var top = ((height / 2) - (h / 2)) + dualScreenTop;
	var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

	// Puts focus on the newWindow
	if (window.focus) {
		newWindow.focus();
	}
}

function FacebookTokenCallbackMessage(event) {
	if (typeof (facebookUrlToken) != 'undefined') {
		if (!facebookUrlToken.toLowerCase().startsWith(event.origin.toLowerCase()))
			return;
	}
	
	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var uri = new URI(event.data);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	var $messageFacebookUrlInvalid = $('#messageFacebookUrlInvalid');
	var $td = $('td.text', $messageFacebookUrlInvalid)

	if (!validUri) {
		$td.html($.i18n("configuration-servicesfacebook-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	$messageFacebookUrlInvalid.hide();
	$divFacebookRedirectUri.show();
	$inputFacebookUrl.val(event.data);
	$buttonEnterFacebookUrl.hide();

	console.log(event.data);

	ValidateUserAccessToken(accessToken, expires);
}

function ValidateUserAccessToken(accessToken, expires) {
	var dataToSend = JSON.stringify({ accessToken: accessToken, expires: expires });

	$.ajax({
		type: "POST",
		url: "ServicesWhatsapp.aspx/GetAccountInfoForFacebook",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				$divFacebookRedirectUri.hide();
				$messageFacebookCouldntValidateAccessToken.hide();

				if (typeof (data.d.LongLivedAccessToken) === 'string' &&
					data.d.LongLivedAccessToken !== null &&
					data.d.LongLivedAccessToken.length > 0) {
					$hiddenFacebookCatalogAccessToken.val(data.d.LongLivedAccessToken);
				}
				else {
					$hiddenFacebookCatalogAccessToken.val(accessToken);
				}

				$('#spanFacebookUserId').text(data.d.User.Id);
				$('#spanFacebookUsername').text(data.d.User.Name);

				LoadUser(data.d.User);
				$('div.text', $divFacebookLoginButton).text($.i18n("configuration-servicesfacebook-change_user"));

				if (data.d.Businesses !== null && data.d.Businesses.Data !== null) {
					FillFacebookBusinesses(toCamel(data.d.Businesses));
				}
			}
			else {
				$messageFacebookCouldntValidateAccessToken.show();
				console.log('Ocurrió un error validando la URL: ' + data.d.Error.Message);
			}

			$.colorbox.resize();
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$messageFacebookCouldntValidateAccessToken.show();
			$.colorbox.resize();
			console.log('Ocurrió un error validando la URL: ' + jqXHR.responseText);
		}
	});
}

function LoadUser(user) {
	$divFacebookUser.show();
	var $divFacebookUserInfo = $('#divFacebookUserInfo');
	$('img', $divFacebookUserInfo).attr('src', 'https://graph.facebook.com/' + user.Id + '/picture');
	$('div.facebook-user-name', $divFacebookUserInfo).text(user.Name);
}

function LoadBusiness(business) {
	var $divFacebookBusinessInfo = $('#divFacebookBusinessInfo');
	$('img', $divFacebookBusinessInfo).attr('src', business.picture);
	$('div.facebook-user-name', $divFacebookBusinessInfo).text(business.name);
}

function FillFacebookBusinesses(businesses, append) {
	if (businesses == null || businesses.data.length == 0) {
		$messageFacebookNoBusinesses.show();
		$divFacebookBusiness.hide();
	}
	else {
		if (typeof (append) === 'undefined') {
			append = false;
		}

		var $divFacebookBusinesses = $('#divFacebookBusinesses');

		if (!append) {
			$divFacebookBusinesses.empty();
		}

		let businessesAdded = false;
		for (var i = 0; i < businesses.data.length; i++) {
			var business = businesses.data[i];
			if (business === null || typeof (business.id) === 'undefined') {
				continue;
			}

			/*if (business.clientWhatsappBusinessAccounts === null && business.ownedWhatsappBusinessAccounts === null) {
				console.log(`El negocio ${business.name} no tiene cuentas de whatsapp asociadas`);
				continue;
			}*/

			var $divPage = $('<div class="facebook-pages-page"></div>');
			$divPage.append('<div class="facebook-pages-page-avatar"><img src="' + business.picture + '" /></div>');
			var $divPageData = $('<div class="facebook-pages-page-data"></div>');
			$divPage.append($divPageData);
			$divPageData.append('<div class="facebook-pages-page-name">' + business.name + '</div>');
			$divPageData.append('<div class="facebook-pages-page-moreinfo"><span>ID:</span>' + business.id + '</span></div>');
			$divPage.click(business, function (event) {
				let business = event.data;
				currentBusiness = business;
				$divFacebookCatalogsSelectBusiness.hide();
				$divFacebookCatalogsSelectCatalog.show();

				LoadingDialog({
					title: $.i18n("configuration-serviceswhatsapp-title"),
					onTimeout: function () {
						var accessToken = $hiddenFacebookCatalogAccessToken.val();

						var dataToSend = JSON.stringify({ accessToken: accessToken, businessId: business.id });

						var $divFacebookBusinessCatalogs = $('#divFacebookBusinessCatalogs');
						var $messageFacebookNoBusinessCatalogs = $('#messageFacebookNoBusinessCatalogs');
						var $messageFacebookBusinessCatalogNoProducts = $('#messageFacebookBusinessCatalogNoProducts');
						$messageFacebookBusinessCatalogNoProducts.hide();

						$.ajax({
							type: "POST",
							url: "ServicesWhatsapp.aspx/GetBusinessInfo",
							data: dataToSend,
							contentType: "application/json; charset=utf-8",
							dataType: "json",
							success: function (data) {
								console.log(data);
								if (data.d.Success) {
									let businness = data.d.Business;
									LoadBusiness(business);

									let catalogs = businness.OwnedProductCatalogs.Data;
									if (catalogs === null) {
										catalogs = businness.ClientProductCatalogs.Data;
									}
									else {
										if (businness.ClientProductCatalogs !== null && businness.ClientProductCatalogs.Data !== null) {
											catalogs = catalogs.concat(businness.ClientProductCatalogs.Data);
										}
									}

									if (catalogs === null || catalogs.length === 0) {
										$divFacebookBusinessCatalogs.hide();
										$messageFacebookNoBusinessCatalogs.show();
									}
									else {
										FillFacebookBusinessCatalogs(catalogs);

										$divFacebookBusinessCatalogs.show();
										$messageFacebookNoBusinessCatalogs.hide();

										$buttonChangeFacebookBusiness.show();
										$buttonFacebookBusinessCatalogAccept.show();
									}
								}
								else {
									console.log('Ocurrió un error trayendo catálogos: ' + data.d.Error.Message);
								}

								ShowFacebookBusinessCatalogDialog();
							},
							error: function (jqXHR, textStatus, errorThrown) {
								console.log('Ocurrió un error trayendo más catálogos: ' + jqXHR.responseText);

								ShowFacebookBusinessCatalogDialog();
							}
						});
					},
					timeout: 500,
					autoClose: false
				});

				$.colorbox.resize();
			})

			$divFacebookBusinesses.append($divPage);

			businessesAdded = true;
		}

		$divFacebookBusiness.show();
		$messageFacebookNoBusinesses.hide();

		var $divButtonsLoadMoreBusinesses = $('#divButtonsLoadMoreBusinesses');
		var $buttonLoadMoreBusinesses = $('#buttonLoadMoreBusinesses');
		if (businesses.paging !== null &&
			typeof (businesses.paging.cursors) !== 'undefined' &&
			businesses.paging.cursors !== null) {
			$divButtonsLoadMoreBusinesses.show();
			$buttonLoadMoreBusinesses.unbind('click');
			$buttonLoadMoreBusinesses.click(businesses.paging.cursors.after, function (e) {
				var accessToken = $hiddenFacebookCatalogAccessToken.val();
				var after = e.data;

				var dataToSend = JSON.stringify({ accessToken: accessToken, after: after });

				$.ajax({
					type: "POST",
					url: "ServicesWhatsapp.aspx/GetBusinessesAfter",
					data: dataToSend,
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					success: function (data) {
						if (data.d.Success) {
							if (data.d.Businesses !== null && data.d.Businesses.Data !== null) {
								FillFacebookBusinesses(toCamel(data.d.Businesses), true);
							}
						}
						else {
							console.log('Ocurrió un error trayendo más negocios: ' + data.d.Error.Message);
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						console.log('Ocurrió un error trayendo más negocios: ' + jqXHR.responseText);
					}
				});
			});

			if (!businessesAdded) {
				$buttonLoadMoreBusinesses.trigger('click');
			}
		}
		else {
			$divButtonsLoadMoreBusinesses.hide();
		}
	}
	$.colorbox.resize();
}

function ShowFacebookBusinessCatalogDialog() {
	$('#messageMustSelectBusiness').hide();
	$('#messageMustSelectCatalog').hide();

	$.colorbox({
		transition: 'elastic',
		speed: 100,
		inline: true,
		href: "#divFacebookCatalogs",
		overlayClose: false,
		width: '80%',
		initialWidth: '80%',
		preloading: false,
		showBackButton: false,
		closeButton: false
	});
}

function FillFacebookBusinessCatalogs(catalogs) {
	if (catalogs == null || catalogs.length == 0) {
		$messageFacebookNoBusinessCatalogs.show();
		$divFacebookBusinessCatalogs.hide();
	}
	else {
		var $selectFacebookBusinessCatalog = $('#selectFacebookBusinessCatalog');
		var $messageFacebookBusinessCatalogNoProducts = $('#messageFacebookBusinessCatalogNoProducts');

		$selectFacebookBusinessCatalog.empty();

		for (var i = 0; i < catalogs.length; i++) {
			var catalog = catalogs[i];
			var $option = $('<option></option>');
			$option.val(catalog.Id);
			$option.text(catalog.Name);
			$option.prop('catalog', catalog);
			$selectFacebookBusinessCatalog.append($option);
		}

		$selectFacebookBusinessCatalog.unbind('change').change(function () {
			let $option = $('option:selected', $selectFacebookBusinessCatalog);
			let catalog = $option.prop('catalog');
			let $divFacebookBusinessCatalogProducts = $('#divFacebookBusinessCatalogProducts');

			FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProducts, $messageFacebookBusinessCatalogNoProducts);
		}).trigger('change');

		$messageFacebookNoBusinessCatalogs.hide();
		$divFacebookBusinessCatalogs.show();
	}

	$.colorbox.resize();
}

function FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProducts, $messageFacebookBusinessCatalogNoProducts) {
	if (catalog.Products === null ||
		catalog.Products.Data === null ||
		catalog.Products.Data.length === 0) {
		$messageFacebookBusinessCatalogNoProducts.show();
		$divFacebookBusinessCatalogProducts.parent().hide();
	}
	else {
		$divFacebookBusinessCatalogProducts.empty();

		catalog.Products.Data.sort(function (a, b) { return a.Name.trim().localeCompare(b.Name.trim()) });

		for (let i = 0; i < catalog.Products.Data.length; i++) {
			let product = catalog.Products.Data[i];
			let $divProduct = $('<div class="facebook-catalog-products-product"></div>');
			$divProduct.append('<div class="image"><img src="' + product.ImageUrl + '" /></div>');
			let $divProductInfo = $('<div class="info"></div>');
			$divProduct.append($divProductInfo);
			$divProductInfo.append('<div class="name"><span>' + product.Name + '</span><span class="id">(' + product.Id + ')</span></div>');
			$divProductInfo.append('<div class="description"><div>' + product.Description + '</div></div>');
			$divProductInfo.append('<div class="price"><span>' + product.Price + '</span></div>');
			$divFacebookBusinessCatalogProducts.append($divProduct);
		}

		$messageFacebookBusinessCatalogNoProducts.hide();
		$divFacebookBusinessCatalogProducts.parent().show();
	}
}

function ShowFacebookUrlInput() {
	$divFacebookRedirectUri.show();
	$buttonEnterFacebookUrl.hide();
	$.colorbox.resize();
}

function RevalidateFacebookUrl() {
	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var $messageFacebookUrlInvalid = $('#messageFacebookUrlInvalid');
	var $td = $('td.text', $messageFacebookUrlInvalid)

	var url = $inputFacebookUrl.val();
	if (url.length === 0) {
		$td.html($.i18n("configuration-servicesfacebook-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	var uri = new URI(url);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	if (!validUri) {
		$td.html($.i18n("configuration-servicesfacebook-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	$.colorbox.resize();
	$messageFacebookUrlInvalid.hide();
	ValidateUserAccessToken(accessToken, expires);
}

function ChangeFacebookBusiness() {
	currentBusiness = null;
	$divFacebookCatalogsSelectBusiness.show();
	$divFacebookCatalogsSelectCatalog.hide();
	$buttonChangeFacebookBusiness.hide();
	$buttonFacebookBusinessCatalogAccept.hide();
	$('#messageMustSelectBusiness').hide();
	$('#messageMustSelectCatalog').hide();

	$.colorbox.resize();
}

function AcceptFacebookBusinessDialog() {
	if (currentBusiness === null) {
		$('#messageMustSelectBusiness').show();
		return;
	}

	$('#messageMustSelectBusiness').hide();

	let $selectFacebookBusinessCatalog = $('#selectFacebookBusinessCatalog');
	let $optionCatalog = $('option:selected', $selectFacebookBusinessCatalog);
	let catalog = $optionCatalog.prop('catalog');

	if (catalog === null ||
		catalog.Products === null ||
		catalog.Products.Data === null ||
		catalog.Products.Data.length === 0) {
		$('#messageMustSelectCatalog').show();
		return;
	}

	$('#messageMustSelectCatalog').hide();

	let $spanFacebookCatalogName = $('#spanFacebookCatalogName');

	$hiddenFacebookCatalogCatalog.val(JSON.stringify(catalog));
	$textboxFacebookCatalogID.val(catalog.Id);
	$textboxFacebookCatalogBusiness.val(currentBusiness.id);
	$spanFacebookCatalogName.text(catalog.Name);

	let $spanFacebookCatalogBusinessName = $('#spanFacebookCatalogBusinessName');
	$spanFacebookCatalogBusinessName.text(catalog.Business.Name);

	$checkboxUseFacebookCatalog.prop('checked', true).trigger('change');

	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			let accessToken = $hiddenFacebookCatalogAccessToken.val();
			let catalogId = catalog.Id;
			let dataToSend = JSON.stringify({ accessToken: accessToken, catalogId: catalogId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/GetBusinessCatalogProducts",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						catalog.Products.Data = data.d.Products;

						let $divFacebookBusinessCatalogProductsSelected = $('#divFacebookBusinessCatalogProductsSelected');
						let $messageFacebookBusinessCatalogSelectedNoProducts = $('#messageFacebookBusinessCatalogSelectedNoProducts');
						FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProductsSelected, $messageFacebookBusinessCatalogSelectedNoProducts);

						$hiddenFacebookCatalogCatalog.val(JSON.stringify(catalog));

						$.colorbox.close();
					}
					else {
						AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n("configuration-serviceswhatsapp-use_facebook_catalog-couldnt_retrieve-error"), undefined, undefined, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n("configuration-serviceswhatsapp-use_facebook_catalog-couldnt_retrieve-error"), undefined, undefined, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ValidateFacebookCatalog(sender, e) {
	e.IsValid = true;
	if (!$checkboxUseFacebookCatalog.is(':checked')) {
		return;
	}

	let info = $hiddenFacebookCatalogCatalog.val();
	let catalogId = $textboxFacebookCatalogID.val();
	let business = $textboxFacebookCatalogBusiness.val();

	if (info === null ||
		info.length === null ||
		catalogId === null ||
		catalogId.length === null ||
		business === null ||
		business.length === null) {
		e.IsValid = false;
		return;
	}
}

function ShowFacebookCatalogPopup() {
	ConfigureFacebookCatalog();
}

function ShowFacebookCatalogReloadProductsPopup() {
	LoadingDialog({
		title: $.i18n("configuration-serviceswhatsapp-title"),
		onTimeout: function () {
			let accessToken = $hiddenFacebookCatalogAccessToken.val();
			let businessId = $textboxFacebookCatalogBusiness.val();
			let catalogId = $textboxFacebookCatalogID.val();
			let dataToSend = JSON.stringify({ accessToken: accessToken, catalogId: catalogId });

			$.ajax({
				type: "POST",
				url: "ServicesWhatsapp.aspx/GetBusinessCatalogProducts",
				data: dataToSend,
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				success: function (data) {
					if (data.d.Success) {
						let info = $hiddenFacebookCatalogCatalog.val();
						let catalog = JSON.parse(info);
						catalog.Products.Data = data.d.Products;

						let currentCatalogId = $textboxFacebookCatalogID.val();

						if (typeof (currentCatalogId) == 'string' && currentCatalogId !== catalog.Id)
							catalog.Id = currentCatalogId;

						let $divFacebookBusinessCatalogProductsSelected = $('#divFacebookBusinessCatalogProductsSelected');
						let $messageFacebookBusinessCatalogSelectedNoProducts = $('#messageFacebookBusinessCatalogSelectedNoProducts');
						FillFacebookCatalogProducts(catalog, $divFacebookBusinessCatalogProductsSelected, $messageFacebookBusinessCatalogSelectedNoProducts);

						$hiddenFacebookCatalogCatalog.val(JSON.stringify(catalog));

						$.colorbox.close();
					}
					else {
						AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n("configuration-serviceswhatsapp-use_facebook_catalog-couldnt_retrieve-error"), undefined, undefined, 'Error');
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					AlertDialog($.i18n("configuration-serviceswhatsapp-title"), $.i18n("configuration-serviceswhatsapp-use_facebook_catalog-couldnt_retrieve-error"), undefined, undefined, 'Error');
				}
			});
		},
		timeout: 500,
		autoClose: false
	});
}

function ValidateSelectFlowToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	var $selectFlowToUse = $('#selectFlowToUse');
	var value = $selectFlowToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallsInteractiveMessageInvite(sender, e) {
	e.IsValid = true;

	$divVoiceCallInteractiveMessageInvite.removeClass('invalid');

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageInvite.getInfo();
	if (values === null) {
		$divVoiceCallInteractiveMessageInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}
	
	let body = values.body;
	if (typeof (body) !== 'string' || body.length === 0 || body.length > 1024) {
		$divVoiceCallInteractiveMessageInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}

	let buttonText = values.displayText;
	if (typeof (buttonText) !== 'string' || buttonText.length === 0 || buttonText.length > 20) {
		$divVoiceCallInteractiveMessageInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}

	let ttlMinutes = values.ttlMinutes;
	if (typeof (ttlMinutes) !== 'number' || ttlMinutes <= 0 || ttlMinutes > 43200) {
		$divVoiceCallInteractiveMessageInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallInteractiveMessageRejectUsersCaseNotAssigned(sender, e) {
	e.IsValid = true;

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.getInfo();
	if (values === null) {
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallInteractiveMessageRejectAgentNotAvailable(sender, e) {
	e.IsValid = true;

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageRejectAgentNotAvailable.getInfo();
	if (values === null) {
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageRejectAgentNotAvailable.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallInteractiveMessageRejectAgentWithAnotherCall(sender, e) {
	e.IsValid = true;

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageRejectAgentWithAnotherCall.getInfo();
	if (values === null) {
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageRejectAgentWithAnotherCall.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallInteractiveMessageRejectCaseWithCurrentCall(sender, e) {
	e.IsValid = true;

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageRejectCaseWithCurrentCall.getInfo();
	if (values === null) {
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageRejectCaseWithCurrentCall.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCallInteractiveMessageRejectCaseWithoutInvite(sender, e) {
	e.IsValid = true;

	let integrationType = $dropdownlistIntegrationType.val();

	if (integrationType !== '10' &&
		(typeof (allowAgentsToStartVoiceCallOnPostback) !== 'boolean' || !allowAgentsToStartVoiceCallOnPostback || integrationType !== '3') &&
		(typeof (allowAgentsToStartVoiceCallOnCloudApi) !== 'boolean' || !allowAgentsToStartVoiceCallOnCloudApi || integrationType !== '11')) {
		return;
	}

	if (!$checkboxVoiceCallsEnabled.is(':checked')) {
		return;
	}

	let values = $divVoiceCallInteractiveMessageRejectCaseWithoutInvite.getInfo();
	if (values === null) {
		return;
	}

	if (typeof (values.valid) === 'boolean' && !values.valid) {
		$divVoiceCallInteractiveMessageRejectCaseWithoutInvite.addClass('invalid');
		e.IsValid = false;
		return;
	}
}

function ValidateVoiceCall(sender, e) {
	e.IsValid = true;

	if ($checkboxVoiceCallsEnabled.is(':checked') && !$checkboxActAsChat.is(':checked')) {
		e.IsValid = false;
		return;
	}
}

function ValidateSelectFlowContingencyToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlowContingency()) {
		return;
	}

	var $selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
	var value = $selectFlowContingencyToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}