<div class="modal fade">
    <div class="modal-dialog modal-agentsettings">
        <div class="modal-content">
            <div class="modal-header modal-header-social">
                <button type="button" class="close" ng-click="cancel()">
                    <i class="fa fa-times"></i>
                </button>
                <p class="modal-title">
                    <span class="bold">{{ 'EMAIL_CONTACTS_NEW' | translate }} </span>
                </p>
            </div>
            <div class="modal-body">
                <div class="inChangePassword">
                        <div class="changePassword-controls-form">
                            <div class="form-group has-feedback">
                                <input name="name" class="form-control " placeholder="{{'EMAIL_CONTACTS_NAME' | translate }}" ng-model='name' required>
                            </div>
                            <div class="form-group has-feedback">
                                <input name="lastName" class="form-control" placeholder="{{'EMAIL_CONTACTS_LASTNAME' | translate }}" ng-model='lastName' required>
                            </div>
                            <div class="form-group has-feedback">
                                <input name="email" class="form-control" placeholder="{{'EMAIL_CONTACTS_EMAIL' | translate }}" ng-model='email' required>
                            </div>
                        </div>
                </div>
                <div class="modal-footer">
                    <button type="button"
                            class="btn btn-flat btn-action"
                            ng-click="saveEmailContact()">
                        {{'SAVE' | translate}}
                    </button>
                    <button type="button"
                            class="btn btn-flat"
                            ng-click="cancel()">
                        {{'CANCEL' | translate}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>