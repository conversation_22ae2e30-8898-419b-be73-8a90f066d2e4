﻿/// <reference path="../Scripts/jquery-3.5.1.js" />

/// <reference path="../Scripts/Master.js" />
/// <reference path="ServicesCommon.js" />

var $tabsFacebookMessenger;

var $dropdownlistGroupingFacebook;
var $divGroupingTwitterReply;
var $textboxGroupingTwitterReply;
var $divFacebookServiceMedia;
var $checkboxFacebookAllowToSendMedia;
var $textboxWelcomeMessage;
var $checkboxUseGetStartedButton;
var $messageFacebookAccountError;
var $trFacebookAccount;
var $divFacebookPage;
var $messageFacebookNoPages;
var $divFacebookRedirectUri;
var $inputFacebookUrl;
var $buttonEnterFacebookUrl;
var $messageFacebookCouldntValidateAccessToken;
var $divFacebookUser;
var $divFacebookLoginButton;

var $trSubscribeToPushWithoutYFlow;
var $checkboxSubscribeToPushWithoutYFlow;

var $textboxFacebookPageId;
var $textboxFacebookPageName;
var $textboxFacebookPageAccessToken;

var $checkboxAutoReplyBeforeCloseCase;
var $textboxAutoReplyBeforeCloseCaseText;
var $messageAutoReplyBeforeMaxTimeToAnswerText;
var $textboxAutoReplyBeforeCloseCaseMinutes;

var $dropdownlistUseYFlow;
var $liTabAdvancedConfiguration;
var $liTabAdvancedConfigurationYFlow;
var $divAdvancedConfiguration;
var $divAdvancedConfigurationYFlow;
var $hiddenFlow;
var $selectFlowToUse;
var $anchorFlowsReload;
var $spanFlowID;
var $spanFlowName;
var $spanFlowVersion;
var $hiddenFlowContingency;
var $selectFlowContingencyToUse;
var $anchorFlowsContingencyReload;
var $spanFlowContingencyID;
var $spanFlowContingencyName;
var $spanFlowContingencyVersion;
var $hiddenFlowQueueTransfersByKey;
var $tableFlowQueueTransfersByKey;
var $anchorFlowQueueTransfersByKeyAdd;
var $listboxFlowShareEnqueuedMessagesFromQueues;
var $listboxFlowShareConnectedAgentsFromQueues;

var $liTabBehaviour;
var $divWhatsappBehaviour;
var $checkboxAutoReplyBeforeMaxTimeToAnswer;
var $textboxAutoReplyBeforeMaxTimeToAnswerText;
var $messageAutoReplyBeforeMaxTimeToAnswerText;
var $textboxAutoReplyBeforeMaxTimeToAnswerMinutes;
var $dropdownlistAllowToSendHSM;
var $trAllowAgentsToSendHSM;
var $divHSMTemplates;
var $hiddenHSMTemplates;
var $tableHSMTemplates;
var $thHSMTemplateIntegrationType2;
var $tbodyHSMTemplates;
var $anchorHSMTemplatesAdd;

var $textboxFacebookOAuthErrorEmailTemplate;
var $textboxFacebookInactivityDetectedEmailTemplate;

var $checkboxAutoReplyToChatPluingGetStarted;
var $textboxAutoReplyToChatPluingGetStartedText;

var $hiddenConnectionInactivity;
var $listboxConnectionInactivity;

var $hiddenConnectionOAuth;
var $listboxConnectionOAuth;
var $divCapiService;
$(function () {
	InitializeFacebook();

	$('a[rel="_blank"]').click(function () {
		window.open($(this).attr('href'));
		return false;
	});

	LoadCompositedElements();

	if (window.addEventListener) {
		addEventListener("message", FacebookTokenCallbackMessage, false);
	}
	else {
		attachEvent("onmessage", FacebookTokenCallbackMessage);
	}

	if (typeof (creatingService) !== 'undefined' && creatingService) {
		ConfigureFacebook();
	}

	$divCapiService = $('#divCapiService');
	$divCapiService.toggle(enabledCapi === true);
});

function FacebookTokenCallbackMessage(event) {
	if (typeof (facebookUrlToken) != 'undefined') {
		if (!facebookUrlToken.toLowerCase().startsWith(event.origin.toLowerCase()))
			return;
	}

	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var uri = new URI(event.data);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	var $messageFacebookUrlInvalid = $('#messageFacebookUrlInvalid');
	var $td = $('td.text', $messageFacebookUrlInvalid)

	if (!validUri) {
		$td.html($.i18n("configuration-servicesfacebookmessenger-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	$messageFacebookUrlInvalid.hide();
	$divFacebookRedirectUri.show();
	$inputFacebookUrl.val(event.data);
	$buttonEnterFacebookUrl.hide();

	$.colorbox.resize();

	ValidateUserAccessToken(accessToken, expires);
}

function ShowFacebookUrlInput() {
	$divFacebookRedirectUri.show();
	$buttonEnterFacebookUrl.hide();
	$.colorbox.resize();
}

function RevalidateFacebookUrl() {
	var validUri = true;
	var accessToken = null;
	var expires = 0;

	var $messageFacebookUrlInvalid = $('#messageFacebookUrlInvalid');
	var $td = $('td.text', $messageFacebookUrlInvalid)

	var url = $inputFacebookUrl.val();
	if (url.length === 0) {
		$td.html($.i18n("configuration-servicesfacebookmessenger-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	var uri = new URI(url);
	if (validUri) {
		var fragment = uri.fragment();
		var query = URI.parseQuery(fragment);
		if (typeof (query.access_token) == 'undefined' || query.access_token == '')
			validUri = false;
		else
			accessToken = query.access_token;

		if (typeof (query.expires_in) == 'undefined' || query.expires_in == '')
			validUri = false;
		else
			expires = parseInt(query.expires_in, 10);
	}

	if (!validUri) {
		$td.html($.i18n("configuration-servicesfacebookmessenger-invalid_url"));
		$messageFacebookUrlInvalid.show();
		$.colorbox.resize();
		return;
	}

	$.colorbox.resize();
	$messageFacebookUrlInvalid.hide();
	ValidateUserAccessToken(accessToken, expires);
}

function InitializeFacebook() {
	$tabsFacebookMessenger = $('#tabsFacebookMessenger');
	var $hiddenTab = $('#hiddenTab');

	$tabsFacebookMessenger.tabs({
		activate: function (event, page) {
			if (event.type == 'tabsactivate') {
				var $divTab;
				if ((page.newPanel instanceof jQuery)) {
					$divTab = page.newPanel;
				}
				else {
					$divTab = $(page.newPanel.selector);
				}
				var tabId = $divTab.get(0).id;

			    $hiddenTab.val(tabId);
    
			    if (history.pushState) {
				    history.pushState(null, null, '#' + tabId);
			    }
			    else {
				    location.hash = '#' + tabId;
			    }

				if (tabId === 'divNotifications') {
					var editor = $textboxFacebookOAuthErrorEmailTemplate.cleditor()[0];
					editor.refresh();
					editor = $textboxFacebookInactivityDetectedEmailTemplate.cleditor()[0];
					editor.refresh();
				}
				else if (tabId === 'divAdvancedConfigurationYFlow') {
					if (typeof (page.newPanel.get(0).firstTime) === 'undefined') {
						ReloadFlows();
						//ReloadFlowsContingency();
						page.newPanel.get(0).firstTime = false;
					}
				}
			}
		}
	});

	registerTabs($tabsFacebookMessenger);

	$hiddenConnectionInactivity = $('#hiddenConnectionInactivity');
	$listboxConnectionInactivity = $('#listboxConnectionInactivity');

	$hiddenConnectionOAuth = $('#hiddenConnectionOAuth');
	$listboxConnectionOAuth = $('#listboxConnectionOAuth');

	if (typeof (emails) !== 'undefined' && emails !== null && emails.length > 0) {

		let addOption = function (email, $select) {
			let $option = $('<option></option>');
			$option.text(email.Name);
			$option.val(email.ID);
			$select.append($option);
		};

		let selectOptions = function ($hidden, $select) {
			let value = $hidden.val();
			if (typeof (value) === 'string' && value.length > 0) {
				$select.val(value);
			}
		};

		for (let i = 0; i < emails.length; i++) {
			//No listamos la casilla por defult ya que al enviar el valor en null se utilizara la por defecto
			if (!emails[i].UseAsDefault) {
				addOption(emails[i], $listboxConnectionInactivity);
				addOption(emails[i], $listboxConnectionOAuth);
			}
		}

		selectOptions($hiddenConnectionInactivity, $listboxConnectionInactivity);
		selectOptions($hiddenConnectionOAuth, $listboxConnectionOAuth);
	}

	$listboxConnectionInactivity.multiselect({ multiple: false, noneSelectedText: "Por defecto(actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxConnectionInactivity.change(function () {
		var value = $listboxConnectionInactivity.val();
		$hiddenConnectionInactivity.val('');
		if (value != null) {
			$hiddenConnectionInactivity.val(value);
		}

	});

	$listboxConnectionOAuth.multiselect({ multiple: false, noneSelectedText: "Por defecto(actual en parámetros del sistema)", selectedList: 1, buttonWidth: '>500' }).multiselectfilter();
	$listboxConnectionOAuth.change(function () {
		var value = $listboxConnectionOAuth.val();
		$hiddenConnectionOAuth.val('');
		if (value != null) {
			$hiddenConnectionOAuth.val(value);
		}
	});

	$liTabBehaviour = $('#liTabBehaviour');
	$divBehaviour = $('#divBehaviour');
	$checkboxAutoReplyBeforeMaxTimeToAnswer = $('#checkboxAutoReplyBeforeMaxTimeToAnswer');
	$textboxAutoReplyBeforeMaxTimeToAnswerText = $('#textboxAutoReplyBeforeMaxTimeToAnswerText');
	$messageAutoReplyBeforeMaxTimeToAnswerText = $('#messageAutoReplyBeforeMaxTimeToAnswerText');
	CallValidationFields($textboxAutoReplyBeforeMaxTimeToAnswerText, $messageAutoReplyBeforeMaxTimeToAnswerText);
	$textboxAutoReplyBeforeMaxTimeToAnswerMinutes = $('#textboxAutoReplyBeforeMaxTimeToAnswerMinutes');
	$checkboxAutoReplyBeforeCloseCase = $('#checkboxAutoReplyBeforeCloseCase');
	$textboxAutoReplyBeforeCloseCaseText = $('#textboxAutoReplyBeforeCloseCaseText');
	$textboxAutoReplyBeforeCloseCaseMinutes = $('#textboxAutoReplyBeforeCloseCaseMinutes');
	$dropdownlistAllowToSendHSM = $('#dropdownlistAllowToSendHSM');
	$trAllowAgentsToSendHSM = $('#trAllowAgentsToSendHSM');
	$divHSMTemplates = $('#divHSMTemplates');
	$hiddenHSMTemplates = $('#hiddenHSMTemplates');
	$tableHSMTemplates = $('#tableHSMTemplates');
	$thHSMTemplateIntegrationType2 = $('#thHSMTemplateIntegrationType2');
	$tbodyHSMTemplates = $('tbody', $tableHSMTemplates);
	$anchorHSMTemplatesAdd = $('#anchorHSMTemplatesAdd');

	$dropdownlistAllowToSendHSM.change(function () {
		var allow = $dropdownlistAllowToSendHSM.val() === '1';
		$trAllowAgentsToSendHSM.toggle(allow);
		$divHSMTemplates.toggle(allow);
	}).trigger('change');

	$anchorHSMTemplatesAdd.click(AddHSMTemplateRow);

	var templates = $hiddenHSMTemplates.val();
	if (templates !== null && templates.length > 0) {
		templates = JSON.parse(templates);
		for (var i = 0; i < templates.length; i++) {
			var template = templates[i];
			AddHSMTemplateRow(null, template);
		}
	}

	$trSubscribeToPushWithoutYFlow = $('#trSubscribeToPushWithoutYFlow');
	$checkboxSubscribeToPushWithoutYFlow = $('#checkboxSubscribeToPushWithoutYFlow');

	$dropdownlistUseYFlow = $('#dropdownlistUseYFlow');
	$liTabAdvancedConfiguration = $('#liTabAdvancedConfiguration');
	$liTabAdvancedConfigurationYFlow = $('#liTabAdvancedConfigurationYFlow');
	$divAdvancedConfiguration = $('#divAdvancedConfiguration');
	$divAdvancedConfigurationYFlow = $('#divAdvancedConfigurationYFlow');
	$anchorFlowsReload = $('#anchorFlowsReload');
	$anchorFlowsContingencyReload = $('#anchorFlowsContingencyReload');
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		$dropdownlistUseYFlow.change(function () {
			var useYFlow = $dropdownlistUseYFlow.val() === 'true';
			$liTabAdvancedConfiguration.toggle(!useYFlow);
			$liTabAdvancedConfigurationYFlow.toggle(useYFlow);

			//$divAdvancedConfiguration.toggle(!useYFlow);
			//$divAdvancedConfigurationYFlow.toggle(useYFlow);

			$tabsFacebookMessenger.tabs('refresh');
			$trSubscribeToPushWithoutYFlow.toggle(!useYFlow);
			if (useYFlow) {
				ReloadFlows();
				//ReloadFlowsContingency();
			}
		}).trigger('change');

		$hiddenFlow = $('#hiddenFlow');
		$selectFlowToUse = $('#selectFlowToUse');
		$spanFlowID = $('#spanFlowID');
		$spanFlowName = $('#spanFlowName');
		$spanFlowVersion = $('#spanFlowVersion');

		$hiddenFlowContingency = $('#hiddenFlowContingency');
		$selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
		$spanFlowContingencyID = $('#spanFlowContingencyID');
		$spanFlowContingencyName = $('#spanFlowContingencyName');
		$spanFlowContingencyVersion = $('#spanFlowContingencyVersion');

		$hiddenFlowQueueTransfersByKey = $('#hiddenFlowQueueTransfersByKey');
		$tableFlowQueueTransfersByKey = $('#tableFlowQueueTransfersByKey');
		$anchorFlowQueueTransfersByKeyAdd = $('#anchorFlowQueueTransfersByKeyAdd');

		$listboxFlowShareEnqueuedMessagesFromQueues = $('#listboxFlowShareEnqueuedMessagesFromQueues');
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>400' }).multiselectfilter();
		$listboxFlowShareConnectedAgentsFromQueues = $('#listboxFlowShareConnectedAgentsFromQueues');
		$listboxFlowShareConnectedAgentsFromQueues.multiselect({ multiple: true, noneSelectedText: "Todas las colas", selectedList: 4, buttonWidth: '>400' }).multiselectfilter();
		
		$selectFlowToUse.change(function () {
			var $option = $('option:selected', $selectFlowToUse);
			var flow = $option.prop('definition');
			if (flow != null) {
				$spanFlowID.text(flow.id);
				$spanFlowName.text(flow.name);
				$spanFlowVersion.text(flow.ActiveProductionVersion.number);
				$hiddenFlow.val(JSON.stringify(flow));
			}
		});

		$anchorFlowsReload.click(ReloadFlows);
		$anchorFlowQueueTransfersByKeyAdd.click(AddFlowQueueTransfersByKeyRow);

		$selectFlowContingencyToUse.change(function () {
			var $option = $('option:selected', $selectFlowContingencyToUse);
			var flowContingency = $option.prop('definition');
			if (flowContingency != null) {
				$spanFlowContingencyID.text(flowContingency.id);
				$spanFlowContingencyName.text(flowContingency.name);
				$spanFlowContingencyVersion.text(flowContingency.ActiveProductionVersion.number);
				$hiddenFlowContingency.val(JSON.stringify(flowContingency));
			}
		});

		$anchorFlowsContingencyReload.click(ReloadFlowsContingency);

		var transfers = $hiddenFlowQueueTransfersByKey.val();
		if (transfers !== null && transfers.length > 0) {
			transfers = JSON.parse(transfers);
			for (var transfer in transfers) {
				AddFlowQueueTransfersByKeyRow(null, { Key: transfer, QueueID: transfers[transfer] });
			}
		}

		var currentFlow = $hiddenFlow.val();
		if (currentFlow !== null && currentFlow.length > 0) {
			currentFlow = JSON.parse(currentFlow);
			$spanFlowID.text(currentFlow.ID);
			$spanFlowName.text(currentFlow.Name);
			$spanFlowVersion.text(currentFlow.Version);
		}
	}
	else {
		$liTabAdvancedConfiguration.show();
		//$divAdvancedConfiguration.show();
		$tabsFacebookMessenger.tabs('refresh');
	}

	$hiddenUserAccessToken = $('#hiddenUserAccessToken');
	$messageFacebookAccountError = $('#messageFacebookAccountError');
	$trFacebookAccount = $('#trFacebookAccount');
	$divFacebookPage = $('#divFacebookPage');
	$messageFacebookNoPages = $('#messageFacebookNoPages');
	$divFacebookRedirectUri = $('#divFacebookRedirectUri');
	$inputFacebookUrl = $('#inputFacebookUrl');
	$buttonEnterFacebookUrl = $('#buttonEnterFacebookUrl');
	$messageFacebookCouldntValidateAccessToken = $('#messageFacebookCouldntValidateAccessToken');
	$divFacebookUser = $('#divFacebookUser');
	$divFacebookLoginButton = $('div.facebook-login-button-container');

	$("#textboxFacebookFromDate").datepicker({ changeMonth: true, changeYear: true, numberOfMonths: 1 });
	
	$textboxFacebookPageId = $('#textboxFacebookPageId');
	$textboxFacebookPageName = $('#textboxFacebookPageName');
	$textboxFacebookPageAccessToken = $('#textboxFacebookPageAccessToken');
	
	$divFacebookServiceMedia = $('#divFacebookServiceMedia');
	$checkboxFacebookAllowToSendMedia = $('input[type=checkbox][id$=checkboxFacebookAllowToSendMedia]', $divFacebookServiceMedia);

	$checkboxFacebookAllowToSendMedia.change(function () {
		var $table = $checkboxFacebookAllowToSendMedia.closest('table.uiInfoTable');
		var $trs = $('tr[rel=AllowToSendAttachments]', $table);
		if ($checkboxFacebookAllowToSendMedia.is(':checked')) {
			$trs.show();
		}
		else {
			$trs.hide();
		}
	}).trigger('change');

	var cleditorOptions = {
		height: 200,
		width: 'auto',
		fonts: "Arial,Arial Black,Comic Sans MS,Courier New,Lucida Sans,lucida grande,Lucida Sans Unicode,Segoe UI,Narrow,Garamond,Georgia,Impact,Sans Serif,Serif,Tahoma,Trebuchet MS,Verdana",
		bodyStyle: 'margin:4px; font-size:10pt; font-family: "Lucida Sans","lucida grande","Lucida Sans Unicode","Segoe UI",tahoma,verdana,arial,sans-serif"; cursor:text',
		controls:     // controls to add to the toolbar
          "bold italic underline | font size " +
          "style | color highlight removeformat | bullets numbering | outdent " +
          "indent | alignleft center alignright justify | " +
          "image link | cut copy paste pastetext | source",
	};

	$textboxFacebookOAuthErrorEmailTemplate = $('#textboxFacebookOAuthErrorEmailTemplate');
	$textboxFacebookOAuthErrorEmailTemplate.cleditor(cleditorOptions);
	var $messageFacebookOAuthErrorEmailTemplateFields = $('#messageFacebookOAuthErrorEmailTemplateFields');
	CallValidationFields($textboxFacebookOAuthErrorEmailTemplate, $messageFacebookOAuthErrorEmailTemplateFields);

	$textboxFacebookInactivityDetectedEmailTemplate = $('#textboxFacebookInactivityDetectedEmailTemplate');
	$textboxFacebookInactivityDetectedEmailTemplate.cleditor(cleditorOptions);
	var $messageFacebookInactivityDetectedEmailTemplateFields = $('#messageFacebookInactivityDetectedEmailTemplateFields');
	CallValidationFields($textboxFacebookInactivityDetectedEmailTemplate, $messageFacebookInactivityDetectedEmailTemplateFields);

	$textboxWelcomeMessage = $('#textboxWelcomeMessage');
	var $messageWelcomeMessage = $('#messageWelcomeMessage');
	CallValidationFields($textboxWelcomeMessage, $messageWelcomeMessage);

	$checkboxUseGetStartedButton = $('#checkboxUseGetStartedButton');

	$checkboxAutoReplyToChatPluingGetStarted = $('#checkboxAutoReplyToChatPluingGetStarted');
	var $divAutoReplyToChatPluingGetStarted = $('#divAutoReplyToChatPluingGetStarted');
	$textboxAutoReplyToChatPluingGetStartedText = $('#textboxAutoReplyToChatPluingGetStartedText');
	$checkboxAutoReplyToChatPluingGetStarted.change(function () {
		$divAutoReplyToChatPluingGetStarted.toggle(this.checked);
	}).trigger('change');

	InitializeSurveysControls();
	InitializeCasesControls();

	var param = $(document).getUrlParam('tab');
	if (param && param != '') {
		$tabsFacebookMessenger.tabs('select', 'div' + param);
	}
	else {
		let tab = $hiddenTab.val();
		if (typeof(tab) === 'string' &&
			tab.length > 0) {
			$tabsFacebookMessenger.tabs('select', tab);
		}
	}
}

function i18nLoaded() {
	if ($listboxFlowShareEnqueuedMessagesFromQueues !== 'undefined') {
		$listboxFlowShareEnqueuedMessagesFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_enqueued_messages-all_queues"));
		$listboxFlowShareConnectedAgentsFromQueues.multiselect('option', 'noneSelectedText', $.i18n("configuration-services-common-yflow-share_connected_agents-all_queues"));
	}

	if ($listboxConnectionInactivity !== 'undefined') {
		$listboxConnectionInactivity.multiselect('option', 'noneSelectedText', $.i18n("globals-email_default"));
		$listboxConnectionOAuth.multiselect('option', 'noneSelectedText', $.i18n("globals-email_default"));	
	}
}

function FillFacebookAccounts(accounts, append) {
	if (accounts == null || accounts.data.length == 0) {
		$messageFacebookNoPages.show();
		$divFacebookPage.hide();
	}
	else {
		if (typeof (append) === 'undefined') {
			append = false;
		}

		var $divFacebookPages = $('#divFacebookPages');

		if (!append) {
			$divFacebookPages.empty();
		}

		for (var i = 0; i < accounts.data.length; i++) {
			var account = accounts.data[i];
			if (account === null || typeof (account.id) === 'undefined')
				continue;

			var $divPage = $('<div class="facebook-pages-page"></div>');
			$divPage.append('<div class="facebook-pages-page-avatar"><img src="https://graph.facebook.com/' + account.id + '/picture" /></div>');
			var $divPageData = $('<div class="facebook-pages-page-data"></div>');
			$divPage.append($divPageData);
			$divPageData.append('<div class="facebook-pages-page-name">' + account.name + '</div>');
			$divPageData.append('<div class="facebook-pages-page-moreinfo"><span>ID:</span>' + account.id + '</span></div>');
			$divPage.click(account, function (event) {
				$textboxFacebookPageId.val(event.data.id);
				$textboxFacebookPageName.val(event.data.name);
				if (typeof (event.data.access_token) !== 'undefined')
					$textboxFacebookPageAccessToken.val(event.data.access_token);
				else
					$textboxFacebookPageAccessToken.val(event.data.accessToken);

				$.colorbox.close();
			})
			$divFacebookPages.append($divPage);
		}

		$divFacebookPage.show();
		$messageFacebookNoPages.hide();

		var $divButtonsLoadMore = $('#divButtonsLoadMore');
		var $buttonLoadMoreAccounts = $('#buttonLoadMoreAccounts');
		if (accounts.paging !== null && 
			typeof(accounts.paging.cursors) !== 'undefined' &&
			accounts.paging.cursors !== null) {
			$divButtonsLoadMore.show();
			$buttonLoadMoreAccounts.unbind('click');
			$buttonLoadMoreAccounts.click(accounts.paging.cursors.after, function (e) {
				var accessToken = $hiddenUserAccessToken.val();
				var after = e.data;

				var dataToSend = JSON.stringify({ accessToken: accessToken, after: after });

				$.ajax({
					type: "POST",
					url: "ServicesFacebookMessenger.aspx/GetAccountsAfter",
					data: dataToSend,
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					success: function (data) {
						if (data.d.Success) {
							if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
								FillFacebookAccounts(toCamel(data.d.Accounts), true);
							}
						}
						else {
							console.log('Ocurrió un error trayendo más cuentas: ' + data.d.Error.Message);
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						console.log('Ocurrió un error trayendo más cuentas: ' + jqXHR.responseText);
					}
				});
			});
		}
		else {
			$divButtonsLoadMore.hide();
		}
	}
	$.colorbox.resize();
}

function LoadUser(user) {
	$divFacebookUser.show();
	$('img', $divFacebookUser).attr('src', 'https://graph.facebook.com/' + user.Id + '/picture');
	$('div.facebook-user-name', $divFacebookUser).text(user.Name);
}

function ConfigureFacebook() {
	var $tableFacebookWizardLoading = $('#tableFacebookWizardLoading');
	var $messageFacebookWizardLoadingError = $('#messageFacebookWizardLoadingError');
	var $td = $('td.text', $messageFacebookWizardLoadingError);
	$messageFacebookWizardLoadingError.hide();
	$tableFacebookWizardLoading.show();

	$.colorbox({
		transition: 'none',
		speed: 100,
		inline: true,
		href: "#divFacebookWizardLoading",
		width: '400px',
		initialWidth: '400px',
		preloading: false,
		showBackButton: false,
		closeButton: false
	});

	var dataToSend = JSON.stringify({
		serviceId: editingService ? editingServiceId : null,
		serviceType: editingService ? editingServiceType : creatingServiceType
	});

	$.ajax({
		type: "POST",
		url: "ServicesFacebookMessenger.aspx/BuildAuthorizationUriForFacebook",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				$divFacebookLoginButton.click(data.d.Uri, function (event) {
					$hiddenUserAccessToken.val('');
					$divFacebookPage.hide();
					$messageFacebookAccountError.hide();
					$messageFacebookCouldntValidateAccessToken.hide();
					$messageFacebookNoPages.hide();
					$messageFacebookWizardLoadingError.hide();
					$divFacebookUser.hide();
					$buttonEnterFacebookUrl.show();
					$inputFacebookUrl.val('');
					$.colorbox.resize();
					$('div.text', $divFacebookLoginButton).text($.i18n("configuration-servicesfacebookmessenger-log_in"));
					PopupCenter(event.data, 'login', 400, 300);
				});

				if (data.d.User !== null) {
					$buttonEnterFacebookUrl.hide();
					$divFacebookRedirectUri.hide();

					LoadUser(data.d.User);
					$('div.text', $divFacebookLoginButton).text($.i18n("configuration-servicesfacebookmessenger-change_user"));
				}

				if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
					FillFacebookAccounts(toCamel(data.d.Accounts));
					$buttonEnterFacebookUrl.hide();
					$divFacebookRedirectUri.hide();
				}

				$.colorbox({
					transition: 'elastic',
					speed: 100,
					inline: true,
					href: "#divFacebookWizard",
					overlayClose: false,
					width: '850px',
					initialWidth: '850px',
					preloading: false,
					showBackButton: false,
					closeButton: false
				});
			}
			else {
				$td.html($.i18n("configuration-servicesfacebookmessenger-error_retry_later"));
				$messageFacebookWizardLoadingError.show();
				$tableFacebookWizardLoading.hide();
				$.colorbox.resize();
				if (console)
					console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$td.html($.i18n("configuration-servicesfacebookmessenger-error"));
			$messageFacebookWizardLoadingError.show();
			$tableFacebookWizardLoading.hide();
			$.colorbox.resize();
			if (console)
				console.log('Ocurrió un error en BuildAuthorizationUriForFacebook: ' + jqXHR.responseText);
		}
	});
}

function ValidateUserAccessToken(accessToken, expires) {
	var dataToSend = JSON.stringify({ accessToken: accessToken, expires: expires });

	$.ajax({
		type: "POST",
		url: "ServicesFacebookMessenger.aspx/GetAccountInfoForFacebook",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				$divFacebookRedirectUri.hide();
				$messageFacebookCouldntValidateAccessToken.hide();

				if (typeof (data.d.LongLivedAccessToken) === 'string' &&
					data.d.LongLivedAccessToken !== null &&
					data.d.LongLivedAccessToken.length > 0) {
					$hiddenUserAccessToken.val(data.d.LongLivedAccessToken);
				}
				else {
					$hiddenUserAccessToken.val(accessToken);
				}
				$('#spanFacebookUserId').text(data.d.User.Id);
				$('#spanFacebookUsername').text(data.d.User.Name);

				LoadUser(data.d.User);
				$('div.text', $divFacebookLoginButton).text($.i18n("configuration-servicesinstagram-change_user"));

				if (data.d.Accounts !== null && data.d.Accounts.Data !== null) {
					FillFacebookAccounts(toCamel(data.d.Accounts));
				}
			}
			else {
				$messageFacebookCouldntValidateAccessToken.show();
				if (console)
					console.log('Ocurrió un error validando la URL: ' + data.d.Error.Message);
			}

			$.colorbox.resize();
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$messageFacebookCouldntValidateAccessToken.show();
			$.colorbox.resize();
			if (console)
				console.log('Ocurrió un error validando la URL: ' + jqXHR.responseText);
		}
	});
}

function FacebookValidateAccessTokenExpiry() {
	var accessToken = $textboxFacebookPageAccessToken.val();
	var $messageFacebookTokenExpiry = $('div[id$=messageFacebookTokenExpiry]');
	var $spanFacebookTokenExpiry = $('td.text > span[rel=expires]', $messageFacebookTokenExpiry);
	var dataToSend = JSON.stringify({ accessToken: accessToken });

	$.ajax({
		type: "POST",
		url: "ServicesFacebookMessenger.aspx/GetAccessTokenInfoForFacebook",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				if (data.d.Info.Expires) {
					$messageFacebookTokenExpiry.show();
					$spanFacebookTokenExpiry.text(data.d.Info.ExpiresAt.DateString);
				}
				else {
					$messageFacebookTokenExpiry.hide();
				}
			}
			else {
				$messageFacebookTokenExpiry.hide();
				if (console)
					console.log('Error al consultar expiración del token de facebook: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$messageFacebookTokenExpiry.hide();
			if (console)
				console.log('Error al consultar expiración del token de facebook: ' + jqXHR.responseText);
		}
	});
}

function ValidateFacebookMaxSizeAttachment(sender, e) {
	e.IsValid = true;

	if (!$checkboxFacebookAllowToSendMedia.is(':checked'))
		return;

	var $textbox = $('input[id$=textboxFacebookMaxSizeAttachment]', $divFacebookServiceMedia);
	var value = $textbox.val();
	if (value.length == 0) {
		e.IsValid = false;
		return;
	}

	if (!$.isNumeric(value)) {
		e.IsValid = false;
		return;
	}

	value = parseInt(value, 10);

	if (value < 1 || value > 3) {
		e.IsValid = false;
		return;
	}
}

function ValidateFacebookMultimediaOptions(sender, e) {
	e.IsValid = true;

	if (!$checkboxFacebookAllowToSendMedia.is(':checked'))
		return;

	var $checkboxFacebookAcceptedTypeImages = $('#checkboxFacebookAcceptedTypeImages', $divFacebookServiceMedia);
	var $checkboxFacebookAcceptedTypeAudio = $('#checkboxFacebookAcceptedTypeAudio', $divFacebookServiceMedia);
	var $checkboxFacebookAcceptedTypeVideo = $('#checkboxFacebookAcceptedTypeVideo', $divFacebookServiceMedia);
	var $checkboxFacebookAcceptedTypeAllFiles = $('#checkboxFacebookAcceptedTypeAllFiles', $divFacebookServiceMedia);

	if (!$checkboxFacebookAcceptedTypeImages.is(':checked') &&
		!$checkboxFacebookAcceptedTypeAudio.is(':checked') &&
		!$checkboxFacebookAcceptedTypeVideo.is(':checked') &&
		!$checkboxFacebookAcceptedTypeAllFiles.is(':checked')) {
		e.IsValid = false;
		return;
	}
}

function ValidateWelcomeMessage(sender, e) {
	e.IsValid = true;

	if (typeof (allowYFlow) !== 'undefined' && allowYFlow && $dropdownlistUseYFlow.val() === 'true') {
		return;
	}

	var val = $textboxWelcomeMessage.val().trim();
	if (val.length === 0) {
		e.IsValid = false;
		return;
	}
}

function VerifyOtherServices() {
	var pageId = $textboxFacebookPageId.val();
	var dataToSend = JSON.stringify({ serviceId: (editingService ? editingServiceId : null), pageIdStr: pageId });
	var $buttonSave = $('#buttonSave');
	var $divAnotherFacebookMessengerServiceExists = $('#divAnotherFacebookMessengerServiceExists');
	var $divAnotherFacebookServiceExists = $('#divAnotherFacebookServiceExists');
	var $messageAnotherFacebookServiceExists = $('#messageAnotherFacebookServiceExists');
	var $spanServiceName = $('span[rel=servicename]', $messageAnotherFacebookServiceExists);
	var $buttonSaveAndDisable = $('#buttonSaveAndDisable');
	var $hiddenServiceIDToDisable = $('#hiddenServiceIDToDisable');
	$hiddenServiceIDToDisable.val('');

	$.ajax({
		type: "POST",
		url: "ServicesFacebookMessenger.aspx/IsPageUsedInAnotherService",
		data: dataToSend,
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		success: function (data) {
			if (data.d.Success) {
				console.log(data.d);

				if (!data.d.ExistsAnotherService) {
					$buttonSave.click();
				}
				else {
					if (data.d.Service.Type == ServiceTypes.FacebookMessenger) {
						$.colorbox({
							transition: 'elastic',
							speed: 100,
							inline: true,
							href: $divAnotherFacebookMessengerServiceExists,
							overlayClose: false,
							width: '850px',
							initialWidth: '850px',
							preloading: false,
							showBackButton: false,
							closeButton: false
						});
					}
					else {
						if (data.d.UsesPrivateMessaging) {
							$spanServiceName.text(data.d.Service.Name);
							$hiddenServiceIDToDisable.val(data.d.Service.ID);

							$.colorbox({
								transition: 'elastic',
								speed: 100,
								inline: true,
								href: $divAnotherFacebookServiceExists,
								overlayClose: false,
								width: '850px',
								initialWidth: '850px',
								preloading: false,
								showBackButton: false,
								closeButton: false
							});
						}
						else {
							$buttonSave.click();
						}
					}
				}
			}
			else {
				if (console)
					console.log('Error al consultar si la página especificada es usada en otro servicio: ' + data.d.Error.Message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			if (console)
				console.log('Error al consultar si la página especificada es usada en otro servicio: ' + jqXHR.responseText);
		}
	});
}

function SaveAndDisable() {
	var $buttonSave = $('#buttonSave');
	$buttonSave.click();

	$.colorbox.close();
}

function PopupCenter(url, title, w, h) {
    // Fixes dual-screen position                         Most browsers      Firefox
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : window.screenX;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : window.screenY;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
    var top = ((height / 2) - (h / 2)) + dualScreenTop;
    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

    // Puts focus on the newWindow
    if (window.focus) {
        newWindow.focus();
    }
}

function ReloadFlows() {
	$('span.fa', $anchorFlowsReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.FacebookMessenger,
		function (flows) {
			if (flows !== null && flows.length > 0) {
				var currentFlow = $hiddenFlow.val();
				if (currentFlow !== null && currentFlow.length > 0) {
					currentFlow = JSON.parse(currentFlow);
				}
				else {
					currentFlow = null;
				}

				$selectFlowToUse.empty();
				for (var i = 0; i < flows.length; i++) {
					var $option = $('<option />');
					$option.val(flows[i].id);
					$option.text(flows[i].name);
					$option.prop('definition', flows[i]);
					$selectFlowToUse.append($option);
				}

				if (currentFlow !== null) {
					if (typeof (currentFlow.id) !== 'undefined') {
						$selectFlowToUse.val(currentFlow.id);
					}
					else {
						$selectFlowToUse.val(currentFlow.ID);
					}
				}
				$selectFlowToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('Flow', $.i18n("configuration-servicesfacebookmessenger-no_existing_flows"));
				$dropdownlistUseYFlow.val('false').trigger('change');
				$tabsFacebookMessenger.tabs('select', 'divBasicConfiguration');
				$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
			}

			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow: %o', err);
			AlertDialog('Flow', $.i18n("configuration-servicesfacebookmessenger-error_getting_flow_list"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsFacebookMessenger.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsReload).removeClass('fa-spin');
			$('#divAdvancedConfigurationYFlow').get(0).firstTime = true;
		}
	);
}

function ReloadFlowsContingency() {
	$('span.fa', $anchorFlowsContingencyReload).addClass('fa-spin');
	RetrieveFlows(SocialServiceTypes.Telegram,
		function (flowsContingency) {
			if (flowsContingency !== null && flowsContingency.length > 0) {
				var currentFlowContingency = $hiddenFlowContingency.val();
				if (currentFlowContingency !== null && currentFlowContingency.length > 0) {
					currentFlowContingency = JSON.parse(currentFlowContingency);
				}
				else {
					currentFlowContingency = null;
				}

				$selectFlowContingencyToUse.empty();
				for (var i = 0; i < flowsContingency.length; i++) {
					var $option = $('<option />');
					$option.val(flowsContingency[i].id);
					$option.text(flowsContingency[i].name);
					$option.prop('definition', flowsContingency[i]);
					$selectFlowContingencyToUse.append($option);
				}

				if (currentFlowContingency !== null) {
					if (typeof (currentFlowContingency.id) !== 'undefined') {
						$selectFlowContingencyToUse.val(currentFlowContingency.id);
					}
					else {
						$selectFlowContingencyToUse.val(currentFlowContingency.ID);
					}
				}
				$selectFlowContingencyToUse.trigger('change');
				$.colorbox.close();
			}
			else {
				AlertDialog('FlowContingency', $.i18n("configuration-serviceswhatsapp-no_flows"));
				$dropdownlistUseYFlowContingency.val('false').trigger('change');
				$tabsTelegram.tabs('select', 'divBasicConfiguration');
			}

			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		},
		function (err) {
			console.error('Ocurrió un error intentando obtener el listado de Flujos de yFlow Contindencia: %o', err);
			AlertDialog('Flow', $.i18n("configuration-serviceswhatsapp-flow_list_error"), undefined, undefined, 'error');
			$dropdownlistUseYFlow.val('false').trigger('change');
			$tabsTelegram.tabs('select', 'divBasicConfiguration');
			$('span.fa', $anchorFlowsContingencyReload).removeClass('fa-spin');
		}
	);
}

function AddFlowQueueTransfersByKeyRow(e, data) {
	var $tbody = $('tbody', $tableFlowQueueTransfersByKey);

	var $lastTr = $("tr:last-child", $tbody);
	var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
	var $newTr = $('<tr class="' + newTrClass + '"><td style="text-align: center"><a rel="remove"><span class="fa fa-lg fa-minus-square"></span></a></td><td><input type="text" rel="key" class="inputtextmono" spellcheck="false" style="width:95%"/></td><td class="data"><select rel="queue" style="width: 95%";></select></td></tr>');
	
	var $inputKey = $('input[rel=key]', $newTr);
	var $selectQueue = $('select[rel=queue]', $newTr);
	for (var i = 0; i < queues.length; i++) {
		var $option = $('<option value="' + queues[i].ID + '">' + queues[i].Name + '</option>');
		$selectQueue.append($option);
	}

	$tbody.append($newTr);

	if (typeof (data) !== 'undefined' && data !== null) {
		$inputKey.val(data.Key);
		$selectQueue.val(data.QueueID);
	}

	$selectQueue.multiselect({ multiple: false, selectedList: 1, buttonWidth: '>400' }).multiselectfilter();

	var $anchorRemove = $('a[rel=remove]', $newTr);
	$anchorRemove.click(function () {
		var $tr = $(this).parent().parent();
		$tr.remove();

		var $trs = $('tr', $tbody);
		if ($trs.length > 0) {
			for (var i = 0; i < $trs.length; i++) {
				var $tr = $($trs.get(i));
				$tr.removeClass('normal alternate');
				$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
			}
		}
	});
}

function ValidateFlowQueueTransfersByKey(sender, e) {
	if (typeof (allowYFlow) !== 'undefined' && allowYFlow) {
		if ($dropdownlistUseYFlow.val() === 'false') {
			$hiddenFlowQueueTransfersByKey.val('');
			e.IsValid = true;
			return;
		}

		var $tbody = $('tbody', $tableFlowQueueTransfersByKey);
		var $trs = $('tr', $tbody);

		var transfers = [];

		for (var i = 0; i < $trs.length; i++) {
			var $tr = $($trs.get(i));
			var $inputKey = $('input[rel=key]', $tr);
			var $selectQueue = $('select[rel=queue]', $tr);

			var key = $inputKey.val();
			if (key.length == 0) {
				$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_key", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			var regex = /^[a-zA-Z0-9]+$/;
			if (!regex.test(key)) {
				$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_key", (i + 1).toString()));
				e.IsValid = false;
				return;
			}

			let queueId = parseInt($selectQueue.val(), 10);
			if (isNaN(queueId)) {
				$(sender).text($.i18n("configuration-services-common-yflow-flow_queue_transfers-invalid_queue", (i + 1).toString(), key));
				e.IsValid = false;
				return;
			}

			var transfer = {
				Key: key,
				QueueID: queueId
			};
			transfers.push(transfer);
		}

		if (transfers.length === 0) {
			$hiddenFlowQueueTransfersByKey.val('');
		}
		else {
			$hiddenFlowQueueTransfersByKey.val(JSON.stringify(transfers));
		}
	}

	e.IsValid = true;
}

function ValidateAutoReplyBeforeMaxTimeToAnswer(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyBeforeMaxTimeToAnswer.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeMaxTimeToAnswerText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeMaxTimeToAnswerMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_time"));
		e.IsValid = false;
		return;
	}

	minutes = parseInt(minutes);
	if (minutes < 0 || minutes > maxMinutesToAnswerMessages) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-time_info", maxMinutesToAnswerMessages));
		e.IsValid = false;
		return;
	}
}

function ValidateAutoReplyBeforeCloseCase(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyBeforeCloseCase.prop('checked')) {
		return;
	}

	var text = $textboxAutoReplyBeforeCloseCaseText.val();
	if (text.length === 0 || text.trim().length === 0) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_answer_text"));
		e.IsValid = false;
		return;
	}

	var minutes = $textboxAutoReplyBeforeCloseCaseMinutes.val();
	var regex = /^\d{1,5}$/;
	if (minutes.length === 0 || !$.isNumeric(minutes) || !regex.test(minutes)) {
		$(sender).text($.i18n("configuration-servicesfacebookmessenger-enter_time"));
		e.IsValid = false;
		return;
	}

	let minutesToCloseCases = casesSettings.MaxElapsedMinutesToCloseCases;
	if ($checkboxCasesOverrideSystemSettings.is(':checked')) {
		let maxElapsedMinutesToCloseCases = $textboxMaxElapsedMinutesToCloseCases.val();
		if (maxElapsedMinutesToCloseCases.length === 0) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		if (!/^\d{1,5}$/.test(maxElapsedMinutesToCloseCases)) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		maxElapsedMinutesToCloseCases = parseInt(maxElapsedMinutesToCloseCases, 10);
		if (isNaN(maxElapsedMinutesToCloseCases) ||
			maxElapsedMinutesToCloseCases < 1 ||
			maxElapsedMinutesToCloseCases > 43200) {
			$(sender).text($.i18n('configuration-services-common-cases_override-invalid-field', $.i18n('configuration-systemsettings-time_to_close')));
			e.IsValid = false;
			return;
		}

		minutesToCloseCases = maxElapsedMinutesToCloseCases;
	}

	if (minutes == 0 || minutes >= minutesToCloseCases) {
		$(sender).text($.i18n("configuration-serviceswhatsapp-enter_valid_time", minutesToCloseCases));
		e.IsValid = false;
		return;
	}
}

function AddHSMTemplateRow(e, template) {
	var $lastTr = $("tr:last-child", $tbodyHSMTemplates);
	var newTrClass = $lastTr.hasClass('normal') ? 'alternate' : 'normal';
	var $newTr = $('<tr></tr>');
	$newTr.addClass(newTrClass);

	var $td = $('<td style="text-align: center"></td>');
	$newTr.append($td);
	var $anchorRemove = $('<a rel="remove"><span class="fa fa-lg fa-minus-square"></span></a>');
	$td.append($anchorRemove);
	$anchorRemove.click(function () {
		var $tr = $(this).parent().parent();
		$tr.remove();

		var $trs = $('tr', $tbodyHSMTemplates);
		if ($trs.length > 0) {
			for (var i = 0; i < $trs.length; i++) {
				var $tr = $($trs.get(i));
				$tr.removeClass('normal alternate');
				$tr.addClass((i % 2 == 0) ? 'normal' : 'alternate');
			}
		}
	});

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputID = $('<input type="text" readonly="readonly" rel="id" class="inputtext" spellcheck="false" style="width:95%"/>');
	$td.append($inputID);

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputDescription = $('<input type="text" rel="description" class="inputtext" spellcheck="false" style="width:95%"/>');
	$td.append($inputDescription);

	$td = $('<td></td>');
	$newTr.append($td);
	var $selectTag = $('<select rel="tag"></select>');
	$selectTag.append('<option title="Send the user reminders or updates for an event they have registered for (e.g., RSVP\'ed, purchased tickets). This tag may be used for upcoming events and events in progress.">CONFIRMED_EVENT_UPDATE</option>');
	$selectTag.append('<option title="Notify the user of an update on a recent purchase">POST_PURCHASE_UPDATE</option>');
	$selectTag.append('<option title="Notify the user of a non-recurring change to their application or account.">ACCOUNT_UPDATE</option>');
	$selectTag.append('<option title="Allows human agents to respond to user inquiries. Messages can be sent within 7 days after a user message">HUMAN_AGENT</option>');
	$td.append($selectTag);

	var $divTagTitle = $('<div rel="title"></div>');
	$td.append($divTagTitle);
	$selectTag.change(function () {
		var $this = $(this);
		var $parent = $this.parent();
		var $divTitle = $('div[rel=title]', $parent);
		var title = $('option:selected', $this).attr('title');
		$divTitle.text(title);
	}).trigger('change');

	$td = $('<td></td>');
	$newTr.append($td);
	var $inputTemplate = $('<textarea rel="template" class="inputtext" spellcheck="false" style="width:100%; min-height: 120px; box-sizing: border-box"/>');
	$td.append($inputTemplate);

	$td = $('<td rel="parameters"></td>');
	$newTr.append($td);

	var $tableParameters = BuildDynamicTable({
		showHeaders: true,
		container: $td,
		onRowAdded: function () {
			var tableHeight = $tableParameters.height();
			var inputTemplateHeight = $inputTemplate.height();
			if (tableHeight > inputTemplateHeight) {
				$inputTemplate.height(tableHeight);
			}
		},
		onRowRemoved: function () {
			var tableHeight = $tableParameters.height();
			var inputTemplateHeight = $inputTemplate.height();
			if (tableHeight < inputTemplateHeight) {
				if (tableHeight < 120) {
					$inputTemplate.height(120);
				}
				else {
					$inputTemplate.height(tableHeight);
				}
			}
		},
		columns: [
			{
				header: {
					title: 'Nombre'
				},
				type: 'text',
				key: 'Name',
				placeholder: 'Nombre del parámetro'
			},
			{
				header: {
					title: 'Descripción'
				},
				key: 'Description',
				type: 'text',
				placeholder: 'Descripción del parámetro'
			}
		]
	});

	$td.prop('table', $tableParameters);

	$tbodyHSMTemplates.append($newTr);

	if (typeof (template) !== 'undefined' && template !== null) {
		if (typeof (template.ID) === 'undefined' || template.ID === null) {
			template.ID = uuidv4();
		}
		$inputID.val(template.ID);
		$inputDescription.val(template.Description);
		$selectTag.val(template.Tag);
		if (template.TemplateParameters !== null && template.TemplateParameters.length > 0) {
			for (var i = 0; i < template.TemplateParameters.length; i++) {
				var p = template.TemplateParameters[i];
				$tableParameters.addRow([p.Name, p.Description]);
			}
		}
		$inputTemplate.val(template.Template);
	}
	else {
		$inputID.val(uuidv4());

		$inputTemplate.blur({ $tr: $newTr }, function (e) {
			var $this = $(this);
			var template = $this.val();
			var $tdParameters = $('td[rel=parameters]', e.data.$tr);
			var $tableParameters = $tdParameters.prop('table');
			var parameters = $tableParameters.getValuesRaw('=');
			if (parameters.length === 0 && template.length > 0) {
				var parametersRegex = /\{\{[a-zA-Z0-9]+\}\}/g;
				var matches = parametersRegex.exec(template);
				while (matches !== null) {
					var parameterName = matches[0].replace('{{', '').replace('}}', '');
					$tableParameters.addRow([parameterName, '']);

					matches = parametersRegex.exec(template);
				}
			}
		});
	}
}

function ValidateHSMTemplates(sender, e) {
	e.IsValid = true;

	var allowed = $dropdownlistAllowToSendHSM.val();
	if (allowed === "0") {
		return;
	}

	var $trs = $('> tbody > tr', $tableHSMTemplates);

	var templates = [];
	var regexElementName = /^[a-z_0-9]+$/;
	var regexNamespace = /^.+$/;
	var regexParameters = /^[\w$]+=.+$/;

	if ($trs.length === 0) {
		$(sender).text($.i18n("configuration-servicesfacebook-enter_template"));
		e.IsValid = false;
		return;
	}

	for (var i = 0; i < $trs.length; i++) {
		var $tr = $($trs.get(i));
		var $inputDescription = $('input[rel=description]', $tr);
		var $selectTag = $('select[rel=tag]', $tr);
		var $inputParameters = $('textarea[rel=parameters]', $tr);
		var $inputTemplate = $('textarea[rel=template]', $tr);
		var $inputID = $('input[rel=id]', $tr);

		id = $inputID.val();

		var description = $inputDescription.val();
		if (description.length == 0) {
			$(sender).text($.i18n("configuration-servicesfacebook-enter_description", (i + 1).toString()));
			e.IsValid = false;
			return;
		}

		var tag = $selectTag.val();

		var templateText = $inputTemplate.val();
		if (templateText.length === 0) {
			$(sender).text($.i18n("configuration-servicesfacebook-no_message_template", (i + 1).toString()));
			e.IsValid = false;
			return;
		}

		var $tdParameters = $('td[rel=parameters]', $tr);
		var $tableParameters = $tdParameters.prop('table');
		var parameters = $tableParameters.getValuesRaw('=');
		if (parameters.length > 0) {
			var parametersElements = parameters.split('\n');
			parameters = [];
			for (var j = 0; j < parametersElements.length; j++) {
				var parametersElement = parametersElements[j];
				if (parametersElement.length === 0) {
					$(sender).text($.i18n("configuration-servicesfacebook-no_message_template", (i + 1).toString()));
					e.IsValid = false;
					return;
				}

				var parameterName = parametersElement;

				if (!regexParameters.test(parametersElement)) {
					$(sender).html('Los parámetros del elemento del ítem ' + (i + 1).toString() + ' debe contener los nombres de los campos y su descripción separados con un caracter igual (=) ingresando uno por renglón. Ejemplo: <span class="mono">first_name=Primer Nombre</span>');
					e.IsValid = false;
					return;
				}

				parameterName = parameterName.substr(0, parameterName.indexOf('='));

				if (templateText.indexOf('{{' + parameterName + '}}') === -1) {
					$(sender).html($.i18n("configuration-servicesfacebook-no_message_template", parameterName, (i + 1).toString(), parameterName));
					e.IsValid = false;
					return;
				}

				parameters.push(parametersElement);
			}
		}

		var template = {
			ID: id,
			Description: description,
			Tag: tag,
			Parameters: parameters,
			Template: templateText
		};

		templates.push(template);
	}

	if (templates.length === 0) {
		$hiddenHSMTemplates.val('');
	}
	else {
		$hiddenHSMTemplates.val(JSON.stringify(templates));
	}
}

function UsingYFlow() {
	if (typeof (allowYFlow) !== 'undefined' &&
		allowYFlow &&
		$dropdownlistUseYFlow.val() === "true") {
		return true;
	}

	return false;
}

function ValidateYFlow(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	var flow = $hiddenFlow.val();
	if (flow.length === 0) {
		AlertDialog($.i18n("configuration-servicestwitter-yflow_configuration"), $.i18n("configuration-servicestwitter-yflow_configuration-tip"));
		e.IsValid = false;
		return;
	}
}

function ValidateAutoReplyToChatPluingGetStartedText(sender, e) {
	e.IsValid = true;

	if (!$checkboxAutoReplyToChatPluingGetStarted.is(':checked')) {
		return;
	}

	let text = $textboxAutoReplyToChatPluingGetStartedText.val();
	if (text.trim().length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateSelectFlowToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlow()) {
		return;
	}

	var $selectFlowToUse = $('#selectFlowToUse');
	var value = $selectFlowToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}

function ValidateSelectFlowContingencyToUse(sender, e) {
	e.IsValid = true;

	if (!UsingYFlowContingency()) {
		return;
	}

	var $selectFlowContingencyToUse = $('#selectFlowContingencyToUse');
	var value = $selectFlowContingencyToUse.val();

	if (value === null || value.length === 0) {
		e.IsValid = false;
		return;
	}
}