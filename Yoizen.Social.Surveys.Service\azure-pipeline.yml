trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - 'Yoizen.Social.Surveys.Service/**'
pool:
  vmImage: 'windows-latest'

variables:
  - name: buildConfiguration
    value: 'Release'
  - name: buildPlatform
    value: 'x86'
  - name: packagesDirectory
    value: '$(Build.SourcesDirectory)/packages'
  - group: StorageSecrets
  - group: global-variables

jobs:
- job: Build_ySocial_Surveys
  displayName: 'Build ySocial Surveys Service'
  steps:
  # 1. Restauración de paquetes
  - task: NuGetCommand@2
    displayName: 'NuGet Restore'
    inputs:
      command: 'restore'
      restoreSolution: '**/Yoizen.Social.sln'

  # 2. Preparación de directorios
  - script: |
      mkdir "$(Build.BinariesDirectory)\$(buildConfiguration)"
      mkdir "$(Build.SourcesDirectory)\Deploy"
      mkdir "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)"
      mkdir "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\SurveysService"
    displayName: 'Create directory structure'

  # 3. Compilación con PowerShell
  - task: PowerShell@2
    displayName: 'Build Solution (PowerShell)'
    inputs:
      targetType: 'inline'
      script: |
        $msbuild = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
        $project = "$(Build.SourcesDirectory)\Yoizen.Social.Surveys.Service\Yoizen.Social.Surveys.Service.csproj"
        
        & $msbuild $project `
          /p:Configuration=$(buildConfiguration) `
          /p:Platform=$(buildPlatform) `
          /p:OutputPath="$(Build.BinariesDirectory)\$(buildConfiguration)\" `
          /p:SolutionDir="$(Build.SourcesDirectory)\" `
          /p:WarningLevel=4 `
          /p:DebugType=pdbonly `
          /p:Optimize=true `
          /p:Prefer32Bit=false `
          /nr:false
        
        if ($LASTEXITCODE -ne 0) { exit $LASTEXITCODE }
      pwsh: false

  # 4. Preparación de artefactos
  - task: PowerShell@2
    displayName: 'Prepare Deployment Files'
    inputs:
      targetType: 'inline'
      script: |
        $source = "$(Build.BinariesDirectory)\$(buildConfiguration)\*"
        $destination = "$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\SurveysService"
        
        # Copiar archivos necesarios
        Copy-Item -Path $source -Destination $destination -Recurse -Force
        
        # Eliminar archivos no requeridos
        Remove-Item "$destination\*.pdb" -Force -ErrorAction SilentlyContinue
        Remove-Item "$destination\*.xml" -Force -ErrorAction SilentlyContinue
        Remove-Item "$destination\*vshost*" -Force -ErrorAction SilentlyContinue
      pwsh: true

  # 5. Creación del paquete ZIP
  - task: ArchiveFiles@2
    displayName: 'Create Deployment Package'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)\Deploy\$(buildConfiguration)\SurveysService'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)\SurveysService.zip'
      replaceExistingArchive: true

  # 6. Publicación del artifact
  - task: PublishBuildArtifacts@1
    displayName: 'Publish Artifact'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)\SurveysService.zip'
      ArtifactName: 'SurveysService'

  # 7. Subida a Azure Blob Storage
  - task: AzureCLI@2
    displayName: 'Upload to Blob Storage'
    inputs:
      azureSubscription: 'devops-pipeline-sp'
      scriptType: 'ps'
      scriptLocation: 'inlineScript'
      inlineScript: |
        az storage blob upload `
          --account-name "$(StorageAccountName)" `
          --container-name "$(ContainerName)" `
          --file "$(Build.ArtifactStagingDirectory)\SurveysService.zip" `
          --name "dev/SurveysService.zip" `
          --overwrite `
          --auth-mode key `
          --account-key "$(StorageAccountKey)"

- job: Notify_GoogleChat
  displayName: 'Notificar Resultado del Pipeline a Google Chat'
  dependsOn:
    - Build_ySocial_Surveys
  condition: always()
  steps:
  - checkout: none

  - task: PowerShell@2
    displayName: 'Notificar Éxito a Google Chat'
    condition: succeeded()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "✅ *Pipeline ySocial-Surveys-CI-DEV completado exitosamente*.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers

  - task: PowerShell@2
    displayName: 'Notificar Falla a Google Chat'
    condition: failed()
    inputs:
      targetType: 'inline'
      script: |
        $buildUrl = "https://yoizen.visualstudio.com/ySocial/_build/results?buildId=$(Build.BuildId)"
        $payload = @{
          text = "❌ *Pipeline ySocial-Surveys-CI-DEV falló*. Revisar errores.`n🔗 Ver build: $buildUrl"
        } | ConvertTo-Json
        $headers = @{ "Content-Type" = "application/json" }
        Invoke-RestMethod -Uri "$(googleChatWebhook)" -Method Post -Body $payload -Headers $headers  