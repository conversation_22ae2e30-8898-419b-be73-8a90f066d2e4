﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using Yoizen.Social.DAL;
using Yoizen.Social.DomainModel;
using Yoizen.Social.Licensing;
using System.Net;
using System.IO;
using System.Text;
using System.Web.Script.Serialization;
using System.Web.Script.Services;
using Yoizen.Common;
using Newtonsoft.Json.Linq;
using Yoizen.Social.SocialServices.Facebook.Wrappers;
using RestSharp.Extensions;
using System.Net.Http;
using System.ServiceModel;
using Yoizen.Social.Core.YWhatsapp;
using Yoizen.Social.Core.YoizenBsp.Lines;
using DocumentFormat.OpenXml.Spreadsheet;
using Yoizen.Social.DomainModel.Whatsapp;

namespace Yoizen.Social.Web.Configuration
{
	public partial class ServicesWhatsapp : ServicesBasePage
	{
		protected override ServiceTypes ServiceType { get { return DomainModel.ServiceTypes.WhatsApp; } }

		protected override string PageDescription { get { return "Servicio de WhatsApp"; } }

		protected override string PageDescriptionLocalizationKey { get { return "configuration-serviceswhatsapp-title"; } }

		protected override TextBox TextboxServiceName { get { return textboxServiceName; } }

		protected void Page_Load(object sender, EventArgs e)
		{
			var isCopying = false;

			if (!string.IsNullOrEmpty(hiddenServiceToCopy.Value))
			{
				isCopying = true;
			}

			if (!IsPostBack || isCopying)
			{
				var queues = DomainModel.Cache.Instance.GetList<DomainModel.Queue>()
					.OrderBy(q => q.Name)
					.Select(q => new
					{
						ID = q.ID,
						Name = q.Name
					});
				dropdownlistWhatsAppQueue.DataSource = queues;
				dropdownlistWhatsAppQueue.DataBind();
				listboxFlowShareEnqueuedMessagesFromQueues.DataSource = queues;
				listboxFlowShareEnqueuedMessagesFromQueues.DataBind();
				listboxFlowShareConnectedAgentsFromQueues.DataSource = queues;
				listboxFlowShareConnectedAgentsFromQueues.DataBind();

				var whatsappServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>()
					.Where(s => s.Type == ServiceTypes.WhatsApp)
                    .OrderBy(q => q.Name)
                    .Select(q => new
                    {
                        ID = q.ID,
                        Name = q.Name
                    });
				listboxFlowShareWithServices.DataSource = whatsappServices;
				listboxFlowShareWithServices.DataBind();

				var license = Licensing.LicenseManager.Instance.License.Configuration;

				placeholderWhatsAppCheckSpelling.Visible = !license.OnlyWebAgent;
				liTabVideo.Visible = license.AllowAgentsToStartVideoCall;
				panelVideo.Visible = license.AllowAgentsToStartVideoCall;
				liTabVoiceCalls.Visible = license.AllowWhatsappVoiceCalls;
				panelVoiceCalls.Visible = license.AllowWhatsappVoiceCalls;
				panelIntegrationType3VoiceCallsEndpoint.Visible = license.AllowWhatsappVoiceCalls;
				placeholderVoiceCallAllowRecording.Visible = license.AllowWhatsappVoiceCalls && license.AllowWhatsappVoiceCallsRecording;

				if (Licensing.LicenseManager.Instance.DevelopmentEnvironment ||
					Core.System.Instance.Host.Equals("qa.ysocial.net", StringComparison.InvariantCultureIgnoreCase) ||
					Core.System.Instance.Host.Equals("demo.ysocial.net", StringComparison.InvariantCultureIgnoreCase))
				{
					this.RegisterJsonVariable("allowAgentsToStartVoiceCallOnPostback", true);
				}

				this.RegisterJsonVariable("allowAgentsToStartVoiceCallOnCloudApi", Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCallsCloudApi);

				if (!license.AllowWhatsappIntegrationTypePostback)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}

				if (!license.AllowWhatsappIntegrationTypeTwilio)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}

				if (!license.AllowWhatsappIntegrationTypeGupshup)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}

				if (!license.AllowWhatsappIntegrationTypeWavy)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}

				if (!license.AllowWhatsappIntegrationTypeInteraxa)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}

				if (!license.AllowWhatsappIntegrationTypeMovistar)
				{
					var listitem = dropdownlistIntegrationType.Items.FindByValue(((short) DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar).ToString());
					dropdownlistIntegrationType.Items.Remove(listitem);
				}
				else
				{
					if (license.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						panelIntegrationType6Surveys.Visible = true;

						var surveys = DomainModel.Cache.Instance.GetList<Survey>()
							.Where(s => s.Enabled)
							.OrderBy(s => s.Name)
							.Select(s => new
							{
								s.ID,
								s.Name,
								s.Type
							});
						if (surveys.Any())
						{
							panelIntegrationType6EnableSurveys.Visible = true;
							messageIntegrationType6NoSurveys.Visible = false;
							dropdownlistIntegrationType6Survey.DataSource = surveys;
							dropdownlistIntegrationType6Survey.DataBind();

							this.RegisterJsonVariable("availableSurveys", surveys);
						}
						else
						{
							panelIntegrationType6EnableSurveys.Visible = false;
							messageIntegrationType6NoSurveys.Visible = true;
						}
					}
					else
					{
						panelIntegrationType6Surveys.Visible = false;
					}
				}

				if (license.AllowYFlow && DomainModel.SystemSettings.Instance.YFlow.Enabled)
				{
					placeholderAllowYFlow.Visible = true;
					placeholderAllowAgentsToReturnMessagesToYFlow.Visible = license.AllowAgentsToTransferMessagesToYFlow;
					placeholderYFlowCasesRelated.Visible = true;

					if (license.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						panelYFlowSurveys.Visible = true;

						var surveys = DomainModel.Cache.Instance.GetList<Survey>()
							.Where(s => s.Enabled)
							.OrderBy(s => s.Name)
							.Select(s => new
							{
								s.ID,
								s.Name,
								s.Type
							});
						if (surveys.Any())
						{
							panelEnableSurveys.Visible = true;
							messageNoSurveys.Visible = false;
							dropdownSurvey.DataSource = surveys;
							dropdownSurvey.DataBind();

							this.RegisterJsonVariable("availableSurveys", surveys);
						}
						else
						{
							panelEnableSurveys.Visible = false;
							messageNoSurveys.Visible = true;
						}

						var tagGroups = DomainModel.Cache.Instance.GetList<TagGroup>()
							.OrderBy(tg => tg.Name)
							.Select(tg => new
							{
								tg.ID,
								tg.Name
							});

						listboxSurveyTagGroup.DataSource = tagGroups;
						listboxSurveyTagGroup.DataBind();
						listboxSurveyTagGroupToIgnore.DataSource = tagGroups;
						listboxSurveyTagGroupToIgnore.DataBind();
					}
					else
					{
						panelYFlowSurveys.Visible = false;
					}

					divYFlowContingency.Visible = Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled &&
						DomainModel.SystemStatus.Instance.ContingencyBot.AllowContingencyBot &&
						this.LoggedUser.AccessTokenYFlowContingency != null;
				}
				else
				{
					placeholderYFlowCasesRelated.Visible = false;
					placeholderAllowYFlow.Visible = false;
				}

				var countries = (List<Country>) Application["Countries"];
				if (countries == null)
				{
					countries = DAL.CountryDAO.GetAll();

					Application.Lock();
					Application.Add("Countries", countries);
					Application.UnLock();
				}

				dropdownlistWhatsAppCountries.DataSource = countries.Select(c => new
				{
					c.FullDescription,
					c.InternationalCode
				});
				dropdownlistWhatsAppCountries.DataBind();
				dropdownlistWhatsAppCountries.SelectedValue = DomainModel.SystemSettings.Instance.Whatsapp.DefaultInternationCode.ToString();
				
				checkboxWhatsappAcceptedTypeAllFiles.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.All).ToString());
				checkboxWhatsappAcceptedTypeImages.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Image).ToString());
				checkboxWhatsappAcceptedTypeText.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Text).ToString());
				checkboxWhatsappAcceptedTypeAudio.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Audio).ToString());
				checkboxWhatsappAcceptedTypeVideo.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Video).ToString());
				checkboxWhatsappAcceptedTypePDF.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.PDF).ToString());
				checkboxWhatsappAcceptedTypeWord.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Document).ToString());
				checkboxWhatsappAcceptedTypeExcel.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Spreadsheet).ToString());
				checkboxWhatsappAcceptedTypePPT.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Presentation).ToString());
				checkboxWhatsappAcceptedTypeZip.Attributes.Add("attachmentType", ((short) DomainModel.ServiceSettings.ServiceAttachmentTypes.Zip).ToString());

				panelSurveys.Visible = license.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys;

				placeholderActAsChat.Visible = license.AllowServicesAsChats;

				this.RegisterJsonVariable("defaultInternationCode", DomainModel.SystemSettings.Instance.Whatsapp.DefaultInternationCode);
				this.RegisterJsonVariable("maxMinutesToAnswerMessages", DomainModel.SystemSettings.Instance.Whatsapp.MaxMinutesToAnswerMessages);
				this.RegisterJsonVariable("MinutesToCloseCases", DomainModel.SystemSettings.Instance.Cases.MaxElapsedMinutesToCloseCases);
				this.RegisterJsonVariable("minutesToCloseHsmCases", DomainModel.SystemSettings.Instance.Cases.MaxElapsedMinutesToCloseHsmCases);
				this.RegisterJsonVariable("enabledCapi", DomainModel.SystemSettings.Instance.EnableCapi);


				placeholderIntegrationType10SendToServiceBus.Visible = this.LoggedUserIsSuper;
				placeholderIntegrationType10MarkAsRead.Visible = license.AllowToMarkAsReadWhatsappMessages;
				placeholderIntegrationType11MarkAsRead.Visible = license.AllowToMarkAsReadWhatsappMessages;
				placeholderIntegrationType11Testing.Visible = this.LoggedUserIsSuper;

				panelHSM.Visible = license.AllowWhatsappOutbound;
			}

			this.RegisterEnums(new Type[] {
					typeof(DomainModel.Whatsapp.HSMTemplateButtonsTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderMediaTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateHeaderTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateFooterTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateCallToActionUrlButtonTypes),
					typeof(DomainModel.Whatsapp.HSMTemplateAuthCodeButtonTypes),
					typeof(DomainModel.Whatsapp.InteractiveMessageFooterTypes),
					typeof(DomainModel.Whatsapp.InteractiveMessageHeaderTypes),
					typeof(DomainModel.Whatsapp.InteractiveMessageTypes)
				});

			string oem = null;
			var pages = (System.Web.Configuration.PagesSection) System.Web.Configuration.WebConfigurationManager.GetSection("system.web/pages");
			if (!pages.Theme.Equals("Default"))
				oem = pages.Theme;

			Uri uri = Yoizen.Social.SocialServices.Facebook.FacebookTokens.BuildAuthorizationUri(oem, DomainModel.SystemSettings.Instance.Whatsapp.CatalogApp, new string[] { "catalog_management" } );
			this.RegisterJsonVariable("facebookLoginUrl", uri.ToString());
			this.RegisterJsonVariable("facebookUrlToken", DomainModel.SystemSettings.Instance.Facebook.UrlToken);
			this.RegisterJsonVariable("allowWhatsappOutbound", Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappOutbound);
		}

		#region Protected Methods

		/// <summary>
		/// Inicializa un nuevo servicio
		/// </summary>
		protected override void InitializeNewService()
		{
			textboxWhatsAppFromDate.Text = this.LoggedUser.FormatDate(DateTime.Now);
			textboxWhatsAppPhoneNumber.Text = string.Empty;
			dropdownlistWhatsAppQueue.Items.Insert(0, Helpers.ControlsHelper.CreateListItem("Sin definir", "0", "globals-undefined"));
			dropdownlistWhatsAppQueue.SelectedIndex = 0;
			checkboxWhatsAppCheckSpelling.Checked = true;
			textboxDelayBetweenReplies.Text = "0";
			textboxDelayAfterMultimedia.Text = "0";
			textboxIntegrationType2AccessToken.Text = string.Empty;
			textboxIntegrationType2RefreshToken.Text = string.Empty;
			textboxIntegrationType2Secret.Text = string.Empty;
			checkboxIntegrationType3UseWhatsappFormat.Checked = false;
			checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat.Checked = false;
			hiddenIntegrationType3ReplyEndpoint.Value = string.Empty;
			hiddenIntegrationType3VoiceCallsEndpoint.Value = string.Empty;
			dropdownlistIntegrationType3PullType.SelectedValue = "1";
			hiddenIntegrationType3GetNewsEndpoint.Value = string.Empty;
			hiddenIntegrationType3PostNewsProcessedEndpoint.Value = string.Empty;
			dropdownlistIntegrationType3NotifyClosedCases.SelectedValue = "0";
			hiddenIntegrationType3CloseCaseEndpoint.Value = string.Empty;
			dropdownlistIntegrationType3PayloadType.SelectedValue = "0";
			dropdownlistIntegrationType3IgnorePreviousQueues.SelectedValue = "1";
			hiddenIntegrationType3PayloadTypeObject.Value = string.Empty;
			dropdownlistIntegrationType4AuthorizationType.SelectedValue = "1";
			textboxIntegrationType4Password.Text = string.Empty;
			textboxIntegrationType4User.Text = string.Empty;
			textboxIntegrationType4ApiKey.Text = string.Empty;
			textboxIntegrationType4BaseUrl.Text = string.Empty;
			textboxIntegrationType5Token.Text = string.Empty;
			textboxIntegrationType5Username.Text = string.Empty;
			textboxIntegrationType6ClientID.Text = string.Empty;
			textboxIntegrationType6ClientSecret.Text = string.Empty;
			textboxIntegrationType6UrlBase.Text = string.Empty;
			textboxIntegrationType6Login.Text = "v1/oauth2/token";
			textboxIntegrationType6SendMessage.Text = "plataforma-bot/v1/conversations/@@CONVERSATIONID@@/activities";
			textboxIntegrationType6ConversationActivities.Text = "plataforma-bot/v1/conversations/@@CONVERSATIONID@@/activities";
			textboxIntegrationType6ChannelID.Text = string.Empty;
			textboxIntegrationType6FromID.Text = string.Empty;
			textboxIntegrationType6FromName.Text = string.Empty;
			checkboxIntegrationType6SendEndOfConversation.Checked = true;
			dropdownlistIntegrationType6PayloadType.SelectedValue = "0";
			hiddenIntegrationType6PayloadTypeObject.Value = string.Empty;
			checkboxIntegrationType6EnableSurveys.Checked = false;
			textboxIntegrationType6SurveyExpiration.Text = "0";
			textboxIntegrationType6SurveyInvitation.Text = string.Empty;
			textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
			textboxIntegrationType7Password.Text = string.Empty;
			textboxIntegrationType7User.Text = string.Empty;
			textboxIntegrationType7BaseUrl.Text = string.Empty;
			textboxIntegrationType7MediaID.Text = string.Empty;
			textboxIntegrationType8AppName.Text = string.Empty;
			textboxIntegrationType8ApiKey.Text = string.Empty;
			textboxIntegrationType9AccountSid.Text = string.Empty;
			textboxIntegrationType9AuthToken.Text = string.Empty;
			textboxIntegrationType10AccessToken.Text = string.Empty;
			textboxIntegrationType10BaseUrl.Text = string.Empty;
			textboxIntegrationType10AccountID.Text = string.Empty;
			textboxIntegrationType10LineID.Text = string.Empty;
			checkboxIntegrationType10SendToServiceBus.Checked = false;
			checkboxIntegrationType10UseSeparateQueueForSingle.Checked = true;
			dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.UseWithPhoneNumber).ToString();
			dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.DoNotUse).ToString();
			dropdownlistIntegrationType10MarkAsRead.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.DoNotMark).ToString();
			textboxIntegrationType11AccessToken.Text = string.Empty;
			textboxIntegrationType11GraphApiVersion.Text = "v21.0";
			textboxIntegrationType11PhoneNumberId.Text = string.Empty;
			textboxIntegrationType11WabaId.Text = string.Empty;
			dropdownlistIntegrationType11MarkAsRead.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.DoNotMark).ToString();
			checkboxIntegrationType11TestingAccount.Checked = false;
			textboxIntegrationType11TestingMapping.Text = "54911=541115";

			var settings = new DomainModel.ServiceSettings.WhatsappSettings();
			textboxWhatsappMinutesForInactivity.Text = settings.MinutesForInactivity.ToString();
			hiddenInactivityDetectedConnection.Value = settings.InactivityDetected.EmailConnection;
			textboxWhatsappInactivityDetectedEmailSubject.Text = settings.InactivityDetected.Subject;
			textboxWhatsappInactivityDetectedEmails.Text = settings.InactivityDetected.Emails;
			textboxWhatsappInactivityDetectedEmailTemplate.Text = settings.InactivityDetected.Template;

			textboxFlowMinutesAfterAgentClosedCase.Text = "0";

			checkboxAutoReplyBeforeMaxTimeToAnswer.Checked = false;
			textboxAutoReplyBeforeMaxTimeToAnswerText.Text = string.Empty;
			textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text = "60";

			checkboxDiscardAfterMaxTimeToAnswer.Checked = false;
			checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked = false;

			checkboxAutoReplyBeforeCaseIsClosed.Checked = false;
			textboxAutoReplyBeforeCaseIsClosedText.Text = string.Empty;
			textboxAutoReplyBeforeCaseIsClosedMinutes.Text = "60";

			dropdownlistAllowToSendHSM.SelectedValue = "0";
			dropdownlistAllowAgentsToSendHSM.SelectedValue = "0";
			hiddenHSMTemplates.Value = string.Empty;
			dropdownlistAllowSurveys.SelectedValue = "0";

			dropdownlistAllowToSendFlows.SelectedValue = "0";
			hiddenFlow.Value = string.Empty;

			checkboxEnableSurveys.Checked = false;
			textboxSurveyExpiration.Text = string.Empty;
			textboxSurveyInvitation.Text = string.Empty;
			textboxSurveySentRate.Text = "100";
			textboxSurveyTimeToSend.Text = "24";
			textboxSurveyMessagesCount.Text = "0";
			textboxSurveyCaseDuration.Text = "0";
			checkboxSurveySendIfNewCaseExists.Checked = true;
			textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
			textboxSurveyDontSendTotalSendMonthly.Text = "0";

			checkboxActAsChat.Checked = false;
			checkboxAllowToReplyToSpecificMessage.Checked = false;

			checkboxCasesOverrideSystemSettings.Checked = false;
			this.RegisterJsonVariable("casesSettings", DomainModel.SystemSettings.Instance.Cases);

			checkboxPreviewUrlForTextMessages.Checked = true;

			hiddenVoiceCallInteractiveMessageInvite.Value = string.Empty;
			hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable.Value = string.Empty;
			hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall.Value = string.Empty;
			hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.Value = string.Empty;
			hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall.Value = string.Empty;
			hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite.Value = string.Empty;
		}

		/// <summary>
		/// Carga los datos relacionados con el servicio
		/// </summary>
		/// <param name="service">El <see cref="DomainModel.Service"/> del cual se cargarán los datos</param>
		protected override void LoadServiceData(Service service)
		{
			var configuration = new SocialServices.WhatsApp.WhatsAppServiceConfiguration(service.Configuration);

			if (!IsCopyingService)
			{
				if (configuration.CountryCode == 0)
				{
					var phone = new Social.WhatsApp.PhoneNumber(configuration.PhoneNumber.ToString());
					textboxWhatsAppSelectedPhoneCode.Text = phone.CC;
				}
				else
				{
					textboxWhatsAppSelectedPhoneCode.Text = configuration.CountryCode.ToString();
				}
				dropdownlistWhatsAppCountries.SelectedValue = textboxWhatsAppSelectedPhoneCode.Text;
				textboxWhatsAppPhoneNumber.Text = configuration.PhoneNumber.ToString();

				textboxDelayBetweenReplies.Text = configuration.DelayBetweenReplies.ToString();
				textboxDelayAfterMultimedia.Text = configuration.DelayAfterMultimedia.ToString();
				checkboxActAsChat.Checked = false;
				checkboxAllowToReplyToSpecificMessage.Checked = false;

				dropdownlistIntegrationType.SelectedValue = configuration.IntegrationType;
				textboxIntegrationType2AccessToken.Text = string.Empty;
				textboxIntegrationType2RefreshToken.Text = string.Empty;
				textboxIntegrationType2Secret.Text = string.Empty;
				checkboxIntegrationType3UseWhatsappFormat.Checked = false;
				checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat.Checked = false;
				hiddenIntegrationType3ReplyEndpoint.Value = string.Empty;
				hiddenIntegrationType3VoiceCallsEndpoint.Value = string.Empty;
				dropdownlistIntegrationType3PullType.SelectedValue = "2";
				hiddenIntegrationType3GetNewsEndpoint.Value = string.Empty;
				hiddenIntegrationType3PostNewsProcessedEndpoint.Value = string.Empty;
				dropdownlistIntegrationType3NotifyClosedCases.SelectedValue = "0";
				hiddenIntegrationType3CloseCaseEndpoint.Value = string.Empty;
				dropdownlistIntegrationType3PayloadType.SelectedValue = "0";
				hiddenIntegrationType3PayloadTypeObject.Value = string.Empty;
				hiddenIntegrationType3Derivations.Value = string.Empty;
				dropdownlistIntegrationType3IgnorePreviousQueues.SelectedValue = "1";
				dropdownlistIntegrationType4AuthorizationType.SelectedValue = "1";
				textboxIntegrationType4Password.Text = string.Empty;
				textboxIntegrationType4User.Text = string.Empty;
				textboxIntegrationType4ApiKey.Text = string.Empty;
				textboxIntegrationType4BaseUrl.Text = string.Empty;
				textboxIntegrationType5Token.Text = string.Empty;
				textboxIntegrationType5Username.Text = string.Empty;
				textboxIntegrationType6ClientID.Text = string.Empty;
				textboxIntegrationType6ClientSecret.Text = string.Empty;
				textboxIntegrationType6UrlBase.Text = string.Empty;
				textboxIntegrationType6Login.Text = "v1/oauth2/token";
				textboxIntegrationType6SendMessage.Text = "plataforma-bot/v1/conversations/@@CONVERSATIONID@@/activities";
				textboxIntegrationType6ConversationActivities.Text = "plataforma-bot/v1/conversations/@@CONVERSATIONID@@/activities";
				textboxIntegrationType6ChannelID.Text = string.Empty;
				textboxIntegrationType6FromID.Text = string.Empty;
				textboxIntegrationType6FromName.Text = string.Empty;
				checkboxIntegrationType6SendEndOfConversation.Checked = true;
				dropdownlistIntegrationType6PayloadType.SelectedValue = "0";
				hiddenIntegrationType6PayloadTypeObject.Value = string.Empty;
				checkboxIntegrationType6EnableSurveys.Checked = false;
				textboxIntegrationType6SurveyExpiration.Text = "0";
				textboxIntegrationType6SurveyInvitation.Text = string.Empty;
				textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
				textboxIntegrationType7Password.Text = string.Empty;
				textboxIntegrationType7Password.Text = string.Empty;
				textboxIntegrationType7User.Text = string.Empty;
				textboxIntegrationType7BaseUrl.Text = string.Empty;
				textboxIntegrationType7MediaID.Text = string.Empty;
				textboxIntegrationType8AppName.Text = string.Empty;
				textboxIntegrationType8ApiKey.Text = string.Empty;
				textboxIntegrationType9AuthToken.Text = string.Empty;
				textboxIntegrationType9AccountSid.Text = string.Empty;
				textboxIntegrationType10AccessToken.Text = string.Empty;
				textboxIntegrationType10BaseUrl.Text = string.Empty;
				textboxIntegrationType10AccountID.Text = string.Empty;
				textboxIntegrationType10LineID.Text = string.Empty;
				checkboxIntegrationType10SendToServiceBus.Checked = false;
				checkboxIntegrationType10UseSeparateQueueForSingle.Checked = true;
				dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.UseWithPhoneNumber).ToString();
				dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.DoNotUse).ToString();
				textboxIntegrationType11AccessToken.Text = string.Empty;
				textboxIntegrationType11GraphApiVersion.Text = "v21.0";
				textboxIntegrationType11PhoneNumberId.Text = string.Empty;
				textboxIntegrationType11WabaId.Text = string.Empty;
				checkboxIntegrationType11TestingAccount.Checked = false;
				textboxIntegrationType11TestingMapping.Text = "54911=541115";
				dropdownlistIntegrationType11MarkAsRead.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.DoNotMark).ToString();
				dropdownlistIntegrationType10MarkAsRead.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours.DoNotMark).ToString();
				checkboxEnableVideo.Checked = configuration.EnableVideo;
				hiddenVoiceCallInteractiveMessageInvite.Value = string.Empty;
				hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable.Value = string.Empty;
				hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall.Value = string.Empty;
				hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.Value = string.Empty;
				hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall.Value = string.Empty;
				hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite.Value = string.Empty;

				switch (configuration.ServiceIntegrationType)
				{
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
						textboxIntegrationType10AccessToken.Text = configuration.IntegrationType10AccessToken;
						textboxIntegrationType10BaseUrl.Text = configuration.IntegrationType10BaseUrl;
						textboxIntegrationType10AccountID.Text = configuration.IntegrationType10AccountID;
						textboxIntegrationType10LineID.Text = configuration.IntegrationType10LineID;
						checkboxIntegrationType10SendToServiceBus.Checked = configuration.IntegrationType10SendToServiceBus;

						if (configuration.IntegrationType10UseSessionInServiceBus)
						{
							if (configuration.IntegrationType10UseSessionWithPhoneNumberInServiceBus)
								dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.UseWithPhoneNumber).ToString();
							else
								dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.Use).ToString();
						}
						else
						{
							dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.DoNotUse).ToString();
						}

						if (configuration.IntegrationType10UseSessionForHsmInServiceBus)
						{
							if (configuration.IntegrationType10UseSessionForHsmWithPhoneNumberInServiceBus)
								dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.UseWithPhoneNumber).ToString();
							else
								dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.Use).ToString();
						}
						else
						{
							dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue = ((short) SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus.DoNotUse).ToString();
						}

						dropdownlistIntegrationType10MarkAsRead.SelectedValue = ((short) configuration.MarkAsReadBehaviour).ToString();
						checkboxIntegrationType10UseSeparateQueueForSingle.Checked = configuration.IntegrationType10UseSeparateQueueForSingle;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
						textboxIntegrationType11AccessToken.Text = configuration.IntegrationType11AccessToken;
						textboxIntegrationType11GraphApiVersion.Text = configuration.IntegrationType11GraphApiVersion;
						textboxIntegrationType11PhoneNumberId.Text = configuration.IntegrationType11PhoneNumberId;
						textboxIntegrationType11WabaId.Text = configuration.IntegrationType11WabaId;
						dropdownlistIntegrationType11MarkAsRead.SelectedValue = ((short) configuration.MarkAsReadBehaviour).ToString();
						checkboxIntegrationType11TestingAccount.Checked = configuration.IntegrationType11TestingAccount;
						var mappings = new StringBuilder();
						foreach (var mapping in configuration.IntegrationType11TestingMapping)
						{
							mappings.AppendLine($"{mapping.Key}={mapping.Value}");
						}
						textboxIntegrationType11TestingMapping.Text = mappings.ToString();
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
						textboxIntegrationType9AccountSid.Text = configuration.IntegrationType9AccountSid;
						textboxIntegrationType9AuthToken.Text = configuration.IntegrationType9AuthToken;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
						textboxIntegrationType8AppName.Text = configuration.IntegrationType8AppName;
						textboxIntegrationType8ApiKey.Text = configuration.IntegrationType8ApiKey;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
						textboxIntegrationType7Password.Text = configuration.IntegrationType7Password;
						textboxIntegrationType7User.Text = configuration.IntegrationType7User;
						textboxIntegrationType7BaseUrl.Text = configuration.IntegrationType7BaseUrl;
						textboxIntegrationType7MediaID.Text = configuration.IntegrationType7MediaID;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
						textboxIntegrationType6ClientID.Text = configuration.IntegrationType6ClientID;
						textboxIntegrationType6ClientSecret.Text = configuration.IntegrationType6ClientSecret;
						textboxIntegrationType6UrlBase.Text = configuration.IntegrationType6UrlBase;
						textboxIntegrationType6Login.Text = configuration.IntegrationType6Login;
						textboxIntegrationType6SendMessage.Text = configuration.IntegrationType6SendMessage;
						textboxIntegrationType6ConversationActivities.Text = configuration.IntegrationType6ConversationActivities;
						textboxIntegrationType6EndOfConversation.Text = configuration.IntegrationType6EndOfConversation;
						textboxIntegrationType6ChannelID.Text = configuration.IntegrationType6ChannelID;
						textboxIntegrationType6FromID.Text = configuration.IntegrationType6FromID;
						textboxIntegrationType6FromName.Text = configuration.IntegrationType6FromName;
						checkboxIntegrationType6SendEndOfConversation.Checked = configuration.IntegrationType6SendEndOfConversation;
						dropdownlistIntegrationType6PayloadType.SelectedValue = configuration.IntegrationType6UsePayload ? "1" : "0";
						if (configuration.IntegrationType6UsePayload)
						{
							hiddenIntegrationType6PayloadTypeObject.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType6PayloadProperties);
						}
						if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
							DomainModel.SystemSettings.Instance.EnableSurveys &&
							configuration.IntegrationType6SurveyEnabled &&
							configuration.IntegrationType6SurveyID != Guid.Empty)
						{
							checkboxIntegrationType6EnableSurveys.Checked = true;
							try
							{
								dropdownlistIntegrationType6Survey.SelectedValue = configuration.IntegrationType6SurveyID.ToString();
							}
							catch
							{
								messageIntegrationType6SurveyDisabled.Style[HtmlTextWriterStyle.Display] = "";
							}
							textboxIntegrationType6SurveyExpiration.Text = configuration.IntegrationType6SurveyExpiration.ToString();
							textboxIntegrationType6SurveyInvitation.Text = configuration.IntegrationType6SurveyInvitation;
							textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes.Text = configuration.IntegrationType6SurveyDontSendIfLastSurveyAfterMinutes.ToString();
							checkboxIntegrationType6SurveySendEndOfConversation.Checked = configuration.IntegrationType6SurveySendEndOfConversation;
						}
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
						textboxIntegrationType5Token.Text = configuration.IntegrationType5Token;
						textboxIntegrationType5Username.Text = configuration.IntegrationType5Username;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
						dropdownlistIntegrationType4AuthorizationType.SelectedValue = configuration.IntegrationType4AuthorizationType.ToString();
						textboxIntegrationType4Password.Text = configuration.IntegrationType4Password;
						textboxIntegrationType4User.Text = configuration.IntegrationType4User;
						textboxIntegrationType4ApiKey.Text = configuration.IntegrationType4ApiKey;
						textboxIntegrationType4BaseUrl.Text = configuration.IntegrationType4BaseUrl;
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
						checkboxIntegrationType3UseWhatsappFormat.Checked = configuration.IntegrationType3UseWhatsappFormat;
						checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat.Checked = configuration.IntegrationType3IncludeFromWhenUsingWhatsappFormat;
						hiddenIntegrationType3ReplyEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3ReplyEndpoint);
						dropdownlistIntegrationType3PullType.SelectedValue = ((short) configuration.IntegrationType3PullType).ToString();
						if (configuration.IntegrationType3PullType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationWithPostbackPullTypes.SpecificUrls)
						{
							hiddenIntegrationType3GetNewsEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3GetNewsEndpoint);
							hiddenIntegrationType3PostNewsProcessedEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3PostNewsProcessedEndpoint);
						}
						dropdownlistIntegrationType3NotifyClosedCases.SelectedValue = configuration.IntegrationType3NotifyClosedCases ? "1" : "0";
						if (configuration.IntegrationType3NotifyClosedCases)
						{
							hiddenIntegrationType3CloseCaseEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3CloseCaseEndpoint);
						}
						dropdownlistIntegrationType3PayloadType.SelectedValue = configuration.IntegrationType3UsePayload ? "1" : "0";
						if (configuration.IntegrationType3UsePayload)
						{
							hiddenIntegrationType3PayloadTypeObject.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3PayloadProperties);

							if (configuration.IntegrationType3PayloadProperties.Any(p => p.UseForDerivation))
							{
								hiddenIntegrationType3Derivations.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3QueueTransfersByKey);
								dropdownlistIntegrationType3IgnorePreviousQueues.SelectedValue = configuration.IntegrationType3IgnorePreviousQueues ? "1" : "0";
							}
						}
						if (Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls &&
							configuration.IntegrationType3VoiceCallEndpoint != null)
							hiddenIntegrationType3VoiceCallsEndpoint.Value = Newtonsoft.Json.JsonConvert.SerializeObject(configuration.IntegrationType3VoiceCallEndpoint);
						break;
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
						textboxIntegrationType2AccessToken.Text = configuration.IntegrationType2AccessToken;
						textboxIntegrationType2RefreshToken.Text = configuration.IntegrationType2RefreshToken;
						textboxIntegrationType2Secret.Text = configuration.IntegrationType2Secret;
						break;
					default:
						break;
				}
			}

			if (configuration.UseFacebookCatalog)
			{
				checkboxUseFacebookCatalog.Checked = true;
				hiddenFacebookCatalogAccessToken.Value = configuration.FacebookCatalogAccessToken;
				textboxFacebookCatalogBusiness.Text = configuration.FacebookCatalogBusinessId;
				textboxFacebookCatalogID.Text = configuration.FacebookCatalogId;
				hiddenFacebookCatalogCatalog.Value = configuration.FacebookCatalogInfo;
			}
			else
			{
				checkboxUseFacebookCatalog.Checked = false;
			}

			if (configuration.FromDate == DateTime.MinValue)
				textboxWhatsAppFromDate.Text = string.Empty;
			else
				textboxWhatsAppFromDate.Text = this.LoggedUser.FormatDate(configuration.FromDate);

			if (service.Queue != null)
			{
				dropdownlistWhatsAppQueue.SelectedValue = service.Queue.ID.ToString();
			}
			else
			{
				dropdownlistWhatsAppQueue.Items.Insert(0, Helpers.ControlsHelper.CreateListItem("Sin definir", "0", "globals-undefined"));
				dropdownlistWhatsAppQueue.SelectedIndex = 0;
			}

			var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
			checkboxWhatsAppCheckSpelling.Checked = settings.MustCheckSpellingBeforeSend;
			textboxWhatsappMinutesForInactivity.Text = settings.MinutesForInactivity.ToString();
			hiddenInactivityDetectedConnection.Value = settings.InactivityDetected.EmailConnection;
			textboxWhatsappInactivityDetectedEmailSubject.Text = settings.InactivityDetected.Subject;
			textboxWhatsappInactivityDetectedEmails.Text = settings.InactivityDetected.Emails;
			textboxWhatsappInactivityDetectedEmailTemplate.Text = settings.InactivityDetected.Template;
			checkboxWhatsappAllowToSendMedia.Checked = settings.AllowToSendMultimedia;
			textboxWhatsappMaxSizeAttachment.Text = settings.Attachments.MaxSizeAllowed.ToString();
			checkboxWhatsappAcceptedTypeAudio.Checked = settings.Attachments.AllowAttachAudio;
			checkboxWhatsappAcceptedTypeExcel.Checked = settings.Attachments.AllowAttachExcel;
			checkboxWhatsappAcceptedTypeImages.Checked = settings.Attachments.AllowAttachImages;
			checkboxWhatsappAcceptedTypePDF.Checked = settings.Attachments.AllowAttachPDF;
			checkboxWhatsappAcceptedTypePPT.Checked = settings.Attachments.AllowAttachPowerPoint;
			checkboxWhatsappAcceptedTypeText.Checked = settings.Attachments.AllowAttachText;
			checkboxWhatsappAcceptedTypeWord.Checked = settings.Attachments.AllowAttachWord;
			checkboxWhatsappAcceptedTypeZip.Checked = settings.Attachments.AllowAttachZip;
			checkboxWhatsappAcceptedTypeAllFiles.Checked = settings.Attachments.AllowAttachAllFiles;
			checkboxWhatsappAcceptedTypeVideo.Checked = settings.Attachments.AllowAttachVideo;
			hiddenWhatsappDefaultExtension.Value = ((short) settings.Attachments.DefaultFileType).ToString();
			checkboxEnableCapi.Checked = settings.EnableCapi;

			checkboxActAsChat.Checked = settings.ActAsChat;
			checkboxAllowToReplyToSpecificMessage.Checked = settings.AllowToReplyToSpecificMessage;
			textboxFlowMinutesAfterAgentClosedCase.Text = "0";

			dropdownlistUseYFlow.SelectedValue = service.UsesYFlow.ToString().ToLower();
			if (service.YFlowSettings != null)
			{
				hiddenFlow.Value = Newtonsoft.Json.JsonConvert.SerializeObject(new
				{
					ID = service.YFlowSettings.ID,
					Version = service.YFlowSettings.Version.ToString(),
					Name = service.YFlowSettings.Name
				});

				this.RegisterJsonVariable("hiddenFlowValueForCopy", hiddenFlow.Value);

				textboxFlowMinutesAfterAgentClosedCase.Text = service.YFlowSettings.MinutesAfterAgentClosedCase.ToString();
				checkboxAllowAgentsToReturnMessagesToYFlow.Checked = service.YFlowSettings.AllowAgentsToReturnMessagesToYFlow;

				if (service.YFlowSettings.QueueTransfersByKey != null)
				{
					hiddenFlowQueueTransfersByKey.Value = Newtonsoft.Json.JsonConvert.SerializeObject(service.YFlowSettings.QueueTransfersByKey);
				}
				else
				{
					hiddenFlowQueueTransfersByKey.Value = string.Empty;
				}

				if (service.YFlowSettings.QueuesToShareEnqueuedMessages == null ||
					service.YFlowSettings.QueuesToShareEnqueuedMessages.Length == 0)
				{
					listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowSettings.QueuesToShareEnqueuedMessages)
					{
						try
						{
							var item = listboxFlowShareEnqueuedMessagesFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}

				if (service.YFlowSettings.QueuesToShareConnectedAgents == null ||
					service.YFlowSettings.QueuesToShareConnectedAgents.Length == 0)
				{
					listboxFlowShareConnectedAgentsFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowSettings.QueuesToShareConnectedAgents)
					{
						try
						{
							var item = listboxFlowShareConnectedAgentsFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}

                if (service.YFlowSettings.ServicesToShareOpenCasesWithSameProfile == null ||
                    service.YFlowSettings.ServicesToShareOpenCasesWithSameProfile.Length == 0)
                {
                    listboxFlowShareWithServices.SelectedIndex = -1;
                }
                else
                {
                    var itemToRemove = listboxFlowShareWithServices.Items.FindByValue(service.ID.ToString());
                    if (itemToRemove != null)
                    {
                        listboxFlowShareWithServices.Items.Remove(itemToRemove);
                    }

                    foreach (var queueId in service.YFlowSettings.ServicesToShareOpenCasesWithSameProfile)
                    {
                        try
                        {
                            var item = listboxFlowShareWithServices.Items.FindByValue(queueId.ToString());
                            if (item != null)
                                item.Selected = true;
                        }
                        catch { }
                    }
                }
            }
			else
			{
				hiddenFlow.Value = string.Empty;
				hiddenFlowQueueTransfersByKey.Value = string.Empty;
			}

			if (service.YFlowContingencySettings != null)
			{
				hiddenFlowContingency.Value = Newtonsoft.Json.JsonConvert.SerializeObject(new
				{
					ID = service.YFlowContingencySettings.ID,
					Version = service.YFlowContingencySettings.Version.ToString(),
					Name = service.YFlowContingencySettings.Name
				});
				textboxFlowMinutesAfterAgentClosedCase.Text = service.YFlowContingencySettings.MinutesAfterAgentClosedCase.ToString();
				checkboxAllowAgentsToReturnMessagesToYFlow.Checked = service.YFlowContingencySettings.AllowAgentsToReturnMessagesToYFlow;

				if (service.YFlowContingencySettings.QueueTransfersByKey != null)
				{
					hiddenFlowQueueTransfersByKey.Value = Newtonsoft.Json.JsonConvert.SerializeObject(service.YFlowContingencySettings.QueueTransfersByKey);
				}
				else
				{
					hiddenFlowQueueTransfersByKey.Value = string.Empty;
				}

				if (service.YFlowContingencySettings.QueuesToShareEnqueuedMessages == null ||
					service.YFlowContingencySettings.QueuesToShareEnqueuedMessages.Length == 0)
				{
					listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowContingencySettings.QueuesToShareEnqueuedMessages)
					{
						try
						{
							var item = listboxFlowShareEnqueuedMessagesFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}

				if (service.YFlowContingencySettings.QueuesToShareConnectedAgents == null ||
					service.YFlowContingencySettings.QueuesToShareConnectedAgents.Length == 0)
				{
					listboxFlowShareConnectedAgentsFromQueues.SelectedIndex = -1;
				}
				else
				{
					foreach (var queueId in service.YFlowContingencySettings.QueuesToShareConnectedAgents)
					{
						try
						{
							var item = listboxFlowShareConnectedAgentsFromQueues.Items.FindByValue(queueId.ToString());
							if (item != null)
								item.Selected = true;
						}
						catch { }
					}
				}
			}
			else
			{
				hiddenFlowContingency.Value = string.Empty;
			}

			this.RegisterJsonVariable("hiddenFlowValueForCopy", hiddenFlow.Value);

			checkboxAutoReplyBeforeMaxTimeToAnswer.Checked = settings.AutoReplyBeforeMaxTimeToAnswer;
			textboxAutoReplyBeforeMaxTimeToAnswerText.Text = settings.AutoReplyBeforeMaxTimeToAnswerText;
			textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text = settings.AutoReplyBeforeMaxTimeToAnswerMinutes.ToString();

			checkboxDiscardAfterMaxTimeToAnswer.Checked = settings.DiscardAfterMaxTimeToAnswer;
			checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked = settings.DiscardAndCloseCaseAfterMaxTimeToAnswer;

			checkboxAutoReplyBeforeCaseIsClosed.Checked = settings.UseAutoReplyBeforeCloseCase;
			textboxAutoReplyBeforeCaseIsClosedText.Text = settings.AutoReplyBeforeCloseCaseText;
			textboxAutoReplyBeforeCaseIsClosedMinutes.Text = settings.AutoReplyBeforeCloseCaseMinutes.ToString();

			dropdownlistAllowToSendHSM.SelectedValue = settings.AllowToSendHSM ? "1" : "0";
			dropdownlistAllowAgentsToSendHSM.SelectedValue = (settings.AllowToSendHSM && settings.AllowAgentsToSendHSM) ? "1" : "0";
			hiddenHSMTemplates.Value = string.Empty;
			if (settings.HSMTemplates != null && settings.HSMTemplates.Length > 0)
				hiddenHSMTemplates.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.HSMTemplates);

			dropdownlistAllowToSendFlows.SelectedValue = settings.AllowToSendFlows ? "1" : "0";
			hiddenFlows.Value = string.Empty;
			if (settings.MetaFlows != null && settings.MetaFlows.Length > 0)
				hiddenFlows.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.MetaFlows);

			checkboxVoiceCallsEnabled.Checked = settings.VoiceCallsEnabled;
			//checkboxVoiceCallsAllowAgentsToSendInteractiveMessage.Checked = settings.VoiceCallsAllowAgentsToSendInteractiveMessage;
			checkboxVoiceCallsAllowRecording.Checked = settings.VoiceCallsRecordingEnabled;

			if (settings.VoiceCallsInteractiveMessageInvite != null)
				hiddenVoiceCallInteractiveMessageInvite.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageInvite);

			if (settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable != null) 
				hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable);

			if (settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall != null) 
				hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall);

			if (settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned != null)
				hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned);

			if (settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall != null)
				hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall);

			if (settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite != null)
				hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite.Value = Newtonsoft.Json.JsonConvert.SerializeObject(settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite);

			dropdownlistAllowSurveys.SelectedValue = settings.AllowSurveys ? "1" : "0";

			checkboxEnableSurveys.Checked = false;
			textboxSurveyExpiration.Text = string.Empty;
			textboxSurveyInvitation.Text = string.Empty;
			textboxSurveySentRate.Text = "100";
			textboxSurveyTimeToSend.Text = "24";
			textboxSurveyMessagesCount.Text = "0";
			textboxSurveyCaseDuration.Text = "0";
			checkboxSurveySendIfNewCaseExists.Checked = true;
			textboxSurveyDontSendIfLastSurveyAfterMinutes.Text = "0";
			textboxSurveyDontSendTotalSendMonthly.Text = "0";

			if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled &&
				DomainModel.SystemSettings.Instance.EnableSurveys &&
				service.YFlowSettings != null &&
				service.YFlowSettings.SurveyEnabled &&
				service.YFlowSettings.SurveyList != null &&
				service.YFlowSettings.SurveyList.Count > 0)
			{
				var surveys = (from serviceSurvey
								   in service.YFlowSettings.SurveyList
							   where serviceSurvey != null
							   join s in DomainModel.Cache.Instance.GetList<Survey>()
							   on serviceSurvey.SurveyID equals s.ID
							   select new
							   {
								   ID = s.ID,
								   Name = s.Name,
								   SurveyConfiguration = serviceSurvey
							   });
				this.RegisterJsonVariable("surveys", surveys);
				checkboxEnableSurveys.Checked = true;
			}
			this.RegisterJsonVariable("serviceID", service.ID);

			checkboxCasesOverrideSystemSettings.Checked = service.OverrideCasesSystemSettings;
			this.RegisterJsonVariable("casesSettings", service.CasesSettings);
			if (service.CasesSettings.TagOnAutoCloseCase != -1)
			{
				var tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(service.CasesSettings.TagOnAutoCloseCase);
				if (tag != null)
				{
					this.RegisterJsonVariable("casesSettingsTag", new
					{
						ID = tag.ID,
						FullName = tag.FullName
					});
				}
			}

			if (service.CasesSettings.TagOnAutoCloseHsmCases != -1)
			{
				var tag = DomainModel.Cache.Instance.GetItem<DomainModel.Tag>(service.CasesSettings.TagOnAutoCloseHsmCases);
				if (tag != null)
				{
					this.RegisterJsonVariable("casesSettingsTagOnHsmClose", new
					{
						ID = tag.ID,
						FullName = tag.FullName
					});
				}
			}

			checkboxPreviewUrlForTextMessages.Checked = configuration.PreviewUrlForTextMessages;
		}

		/// <summary>
		/// Devuelve el identificador del servicio configurado a partir de los datos de cada tipo de servicio
		/// </summary>
		/// <returns>El identificador del servicio</returns>
		protected override string GetServiceAccountId()
		{
			return dropdownlistWhatsAppCountries.SelectedValue + textboxWhatsAppPhoneNumber.Text;
		}

		#endregion

		#region Validator Events

		protected virtual void customvalidatorPhoneNumber_ServerValidate(object source, ServerValidateEventArgs e)
		{
			if (string.IsNullOrEmpty(textboxWhatsAppPhoneNumber.Text) || string.IsNullOrEmpty(textboxWhatsAppSelectedPhoneCode.Text))
			{
				e.IsValid = false;
				return;
			}

			var phoneNumber = string.Format("{0}{1}", textboxWhatsAppSelectedPhoneCode.Text, textboxWhatsAppPhoneNumber.Text);

			var services = DomainModel.Cache.Instance.GetList<DomainModel.Service>().Where(s => s.Type == ServiceTypes.WhatsApp);
			foreach (var service in services)
			{
				Core.System.Instance.Logic.EnsureServiceInstance(service);
			}

			if (this.CreatingNewService)
			{
				if (services.Any(s => 
					s != null && 
					s.ServiceConfiguration != null &&
					((SocialServices.WhatsApp.WhatsAppServiceConfiguration) s.ServiceConfiguration).FullPhoneNumber.Equals(phoneNumber, StringComparison.InvariantCultureIgnoreCase)))
				{
					e.IsValid = false;
					return;
				}
			}
			else
			{
				if (services.Any(s =>
					s != null &&
					s.ID != this.ServiceID &&
					s.ServiceConfiguration != null &&
					((SocialServices.WhatsApp.WhatsAppServiceConfiguration) s.ServiceConfiguration).FullPhoneNumber.Equals(phoneNumber, StringComparison.InvariantCultureIgnoreCase)))
				{
					e.IsValid = false;
					return;
				}
			}

			e.IsValid = true;
		}

		#endregion

		#region Button Events

		protected async void buttonSave_Click(object sender, EventArgs e)
		{
			if (Page.IsValid)
			{
				Service service = GetService();
				
				DateTime fromDate = DateTime.MinValue;
				if (!string.IsNullOrEmpty(textboxWhatsAppFromDate.Text))
					fromDate = this.LoggedUser.ParseDate(textboxWhatsAppFromDate.Text);

				SocialServices.WhatsApp.WhatsAppServiceConfiguration configuration = null;
				Queue queue;

				var integrationType = (DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes) short.Parse(dropdownlistIntegrationType.SelectedValue);

				switch (integrationType)
				{
					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.CloudApi:
						Dictionary<string, string> mappings = null;
						if (checkboxIntegrationType11TestingAccount.Checked)
						{
							mappings = new Dictionary<string, string>();
							var lines = textboxIntegrationType11TestingMapping.Text.Replace("\r\n", "\n").Split("\n".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
							foreach (var line in lines)
							{
								if (string.IsNullOrEmpty(line))
									continue;

								var parts = line.Split("=".ToCharArray());
								if (parts.Length == 2)
									mappings[parts[0]] = parts[1];
							}
						}

						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateCloudAPI(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							int.Parse(textboxDelayBetweenReplies.Text),
							textboxIntegrationType11AccessToken.Text,
							textboxIntegrationType11PhoneNumberId.Text,
							textboxIntegrationType11GraphApiVersion.Text,
							textboxIntegrationType11WabaId.Text,
							checkboxIntegrationType11TestingAccount.Checked,
							mappings,
							checkboxEnableVideo.Checked,
							(SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours) short.Parse(dropdownlistIntegrationType11MarkAsRead.SelectedValue));
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateYoizen(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							int.Parse(textboxDelayBetweenReplies.Text),
							textboxIntegrationType10AccessToken.Text,
							textboxIntegrationType10BaseUrl.Text,
							checkboxEnableVideo.Checked,
							textboxIntegrationType10AccountID.Text,
							textboxIntegrationType10LineID.Text,
							checkboxIntegrationType10SendToServiceBus.Checked,
							(SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus) short.Parse(dropdownlistIntegrationType10UseSessionInServiceBus.SelectedValue),
							(SocialServices.WhatsApp.WhatsAppServiceConfiguration.SessionsInServiceBus) short.Parse(dropdownlistIntegrationType10UseSessionForHsmInServiceBus.SelectedValue),
							checkboxIntegrationType10UseSeparateQueueForSingle.Checked,
							(SocialServices.WhatsApp.WhatsAppServiceConfiguration.MarkAsReadBehaviours) short.Parse(dropdownlistIntegrationType10MarkAsRead.SelectedValue));
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Twilio:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateTwilio(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							int.Parse(textboxDelayBetweenReplies.Text),
							textboxIntegrationType9AccountSid.Text,
							textboxIntegrationType9AuthToken.Text,
							checkboxEnableVideo.Checked);
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Gupshup:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateGupshup(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							int.Parse(textboxDelayBetweenReplies.Text),
							textboxIntegrationType8AppName.Text,
							textboxIntegrationType8ApiKey.Text,
							checkboxEnableVideo.Checked);
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Interaxa:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateInteraxa(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							int.Parse(textboxDelayBetweenReplies.Text),
							textboxIntegrationType7User.Text,
							textboxIntegrationType7Password.Text,
							textboxIntegrationType7BaseUrl.Text,
							textboxIntegrationType7MediaID.Text,
							checkboxEnableVideo.Checked);
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Movistar:
						{
							bool usePayload = dropdownlistIntegrationType6PayloadType.SelectedValue.Equals("1");
							DomainModel.ServiceSettings.WhatsappSettings.PayloadProperty[] payloadProperties = null;
							if (usePayload)
							{
								payloadProperties = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ServiceSettings.WhatsappSettings.PayloadProperty[]>(hiddenIntegrationType6PayloadTypeObject.Value);
							}
							
							configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateMovistar(
								Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
								Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
								fromDate,
								textboxIntegrationType6ClientID.Text,
								textboxIntegrationType6ClientSecret.Text,
								textboxIntegrationType6UrlBase.Text,
								textboxIntegrationType6Login.Text,
								textboxIntegrationType6SendMessage.Text,
								textboxIntegrationType6ConversationActivities.Text,
								textboxIntegrationType6ChannelID.Text,
								textboxIntegrationType6FromID.Text,
								textboxIntegrationType6FromName.Text,
								checkboxIntegrationType6SendEndOfConversation.Checked,
								checkboxIntegrationType6SendEndOfConversation.Checked ? textboxIntegrationType6EndOfConversation.Text : null,
								usePayload,
								payloadProperties,
								int.Parse(textboxDelayBetweenReplies.Text),
								checkboxEnableVideo.Checked);

							if (checkboxIntegrationType6EnableSurveys.Checked)
							{
								configuration.IntegrationType6SurveyEnabled = true;
								configuration.IntegrationType6SurveyExpiration = int.Parse(textboxIntegrationType6SurveyExpiration.Text);
								configuration.IntegrationType6SurveyID = Guid.Parse(dropdownlistIntegrationType6Survey.SelectedValue);
								configuration.IntegrationType6SurveyDontSendIfLastSurveyAfterMinutes = int.Parse(textboxIntegrationType6SurveyDontSendIfLastSurveyAfterMinutes.Text);
								configuration.IntegrationType6SurveyInvitation = textboxIntegrationType6SurveyInvitation.Text;
								configuration.IntegrationType6SurveySendEndOfConversation = checkboxIntegrationType6SurveySendEndOfConversation.Checked;
							}
							else
							{
								configuration.IntegrationType6SurveyEnabled = false;
							}
						}
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Wavy:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateWavy(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							textboxIntegrationType5Username.Text,
							textboxIntegrationType5Token.Text,
							int.Parse(textboxDelayBetweenReplies.Text),
							checkboxEnableVideo.Checked);
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Infobip:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateInfobip(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							byte.Parse(dropdownlistIntegrationType4AuthorizationType.SelectedValue),
							textboxIntegrationType4User.Text,
							textboxIntegrationType4Password.Text,
							textboxIntegrationType4ApiKey.Text,
							textboxIntegrationType4BaseUrl.Text,
							int.Parse(textboxDelayBetweenReplies.Text),
							checkboxEnableVideo.Checked);
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Postback:
						{
							var replyEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenIntegrationType3ReplyEndpoint.Value);

							var pullType = (DomainModel.ServiceSettings.WhatsappSettings.IntegrationWithPostbackPullTypes) short.Parse(dropdownlistIntegrationType3PullType.SelectedValue);
							DomainModel.HttpRequestSettings getNewsEndpoint = null;
							DomainModel.HttpRequestSettings postNewsProcessedEndpoint = null;
							if (pullType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationWithPostbackPullTypes.SpecificUrls)
							{
								getNewsEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenIntegrationType3GetNewsEndpoint.Value);
								postNewsProcessedEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenIntegrationType3PostNewsProcessedEndpoint.Value);
							}
							DomainModel.HttpRequestSettings closeCaseEndpoint = null;
							var notifyClosedCases = dropdownlistIntegrationType3NotifyClosedCases.SelectedValue.Equals("1");
							if (notifyClosedCases)
								closeCaseEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenIntegrationType3CloseCaseEndpoint.Value);

							bool usePayload = dropdownlistIntegrationType3PayloadType.SelectedValue.Equals("1");
							DomainModel.ServiceSettings.WhatsappSettings.PayloadProperty[] payloadProperties = null;
							Dictionary<string, int> queueTransfersByKey = null;
							bool ignorePreviousQueues = true;
							if (usePayload)
							{
								payloadProperties = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ServiceSettings.WhatsappSettings.PayloadProperty[]>(hiddenIntegrationType3PayloadTypeObject.Value);
								if (payloadProperties.Any(p => p.UseForDerivation))
								{
									queueTransfersByKey = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, int>>(hiddenIntegrationType3Derivations.Value);
									ignorePreviousQueues = dropdownlistIntegrationType3IgnorePreviousQueues.SelectedValue.Equals("1"); 
								}
							}

							DomainModel.HttpRequestSettings voiceCallsEndpoint = null;
							if (Licensing.LicenseManager.Instance.License.Configuration.AllowWhatsappVoiceCalls &&
								!string.IsNullOrEmpty(hiddenIntegrationType3VoiceCallsEndpoint.Value))
								voiceCallsEndpoint = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.HttpRequestSettings>(hiddenIntegrationType3VoiceCallsEndpoint.Value);

							configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreatePostback(
								Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
								Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
								fromDate,
								pullType,
								replyEndpoint,
								getNewsEndpoint,
								postNewsProcessedEndpoint,
								notifyClosedCases,
								closeCaseEndpoint,
								usePayload,
								payloadProperties,
								queueTransfersByKey,
								ignorePreviousQueues,
								int.Parse(textboxDelayBetweenReplies.Text),
								checkboxIntegrationType3UseWhatsappFormat.Checked,
								checkboxIntegrationType3IncludeFromWhenUsingWhatsappFormat.Checked,
								checkboxEnableVideo.Checked,
								voiceCallsEndpoint);
						}
						break;

					case DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.BotMaker:
						configuration = SocialServices.WhatsApp.WhatsAppServiceConfiguration.CreateBotMaker(
							Convert.ToInt32(dropdownlistWhatsAppCountries.SelectedValue),
							Convert.ToInt64(textboxWhatsAppPhoneNumber.Text),
							fromDate,
							textboxIntegrationType2AccessToken.Text,
							textboxIntegrationType2Secret.Text,
							textboxIntegrationType2RefreshToken.Text,
							int.Parse(textboxDelayBetweenReplies.Text),
							checkboxEnableVideo.Checked);
						break;

					default:
						throw new Exception($"El tipo {dropdownlistIntegrationType.SelectedValue} no está soportado");
				}

				configuration.DelayAfterMultimedia = int.Parse(textboxDelayAfterMultimedia.Text);
				configuration.PreviewUrlForTextMessages = checkboxPreviewUrlForTextMessages.Checked;

				if (checkboxUseFacebookCatalog.Checked)
				{
					configuration.UseFacebookCatalog = true;
					configuration.FacebookCatalogAccessToken = hiddenFacebookCatalogAccessToken.Value;
					configuration.FacebookCatalogBusinessId = textboxFacebookCatalogBusiness.Text;
					configuration.FacebookCatalogId = textboxFacebookCatalogID.Text;
					configuration.FacebookCatalogInfo = hiddenFacebookCatalogCatalog.Value;
				}
				else
				{
					configuration.UseFacebookCatalog = false;
				}

				if (dropdownlistWhatsAppQueue.SelectedValue != "0")
				{
					queue = QueueDAO.GetOneFromCache(int.Parse(dropdownlistWhatsAppQueue.SelectedValue));
					service.Queue = queue;
				}

				var settings = service.Settings as DomainModel.ServiceSettings.WhatsappSettings;
				settings.IntegrationType = byte.Parse(dropdownlistIntegrationType.SelectedValue);
				settings.MustCheckSpellingBeforeSend = checkboxWhatsAppCheckSpelling.Checked;
				settings.AllowToSendMultimedia = checkboxWhatsappAllowToSendMedia.Checked;
				if (settings.AllowToSendMultimedia)
				{
					settings.Attachments.MaxSizeAllowed = int.Parse(textboxWhatsappMaxSizeAttachment.Text);
					settings.Attachments.AllowAttachAudio = checkboxWhatsappAcceptedTypeAudio.Checked;
					settings.Attachments.AllowAttachExcel = checkboxWhatsappAcceptedTypeExcel.Checked;
					settings.Attachments.AllowAttachImages = checkboxWhatsappAcceptedTypeImages.Checked;
					settings.Attachments.AllowAttachPDF = checkboxWhatsappAcceptedTypePDF.Checked;
					settings.Attachments.AllowAttachPowerPoint = checkboxWhatsappAcceptedTypePPT.Checked;
					settings.Attachments.AllowAttachText = checkboxWhatsappAcceptedTypeText.Checked;
					settings.Attachments.AllowAttachWord = checkboxWhatsappAcceptedTypeWord.Checked;
					settings.Attachments.AllowAttachZip = checkboxWhatsappAcceptedTypeZip.Checked;
					settings.Attachments.AllowAttachAllFiles = checkboxWhatsappAcceptedTypeAllFiles.Checked;
					if (!string.IsNullOrEmpty(hiddenWhatsappDefaultExtension.Value))
						settings.Attachments.DefaultFileType = (DomainModel.ServiceSettings.ServiceAttachmentTypes) short.Parse(hiddenWhatsappDefaultExtension.Value);
					settings.Attachments.AllowAttachVideo = checkboxWhatsappAcceptedTypeVideo.Checked;
				}
				settings.MinutesForInactivity = int.Parse(textboxWhatsappMinutesForInactivity.Text);
				settings.InactivityDetected.EmailConnection = hiddenInactivityDetectedConnection.Value;
				settings.InactivityDetected.Subject = textboxWhatsappInactivityDetectedEmailSubject.Text;
				settings.InactivityDetected.Emails = textboxWhatsappInactivityDetectedEmails.Text;
				settings.InactivityDetected.Template = textboxWhatsappInactivityDetectedEmailTemplate.Text;

				settings.ActAsChat = checkboxActAsChat.Checked;
				settings.AllowToReplyToSpecificMessage = checkboxAllowToReplyToSpecificMessage.Checked;

				service.Configuration = configuration.Serialize();
				service.ServiceConfiguration = configuration;
				service.AccountID = configuration.GetAccountID();

				settings.AutoReplyBeforeMaxTimeToAnswer = checkboxAutoReplyBeforeMaxTimeToAnswer.Checked;
				if (settings.AutoReplyBeforeMaxTimeToAnswer)
				{
					settings.AutoReplyBeforeMaxTimeToAnswerText = textboxAutoReplyBeforeMaxTimeToAnswerText.Text;
					settings.AutoReplyBeforeMaxTimeToAnswerMinutes = short.Parse(textboxAutoReplyBeforeMaxTimeToAnswerMinutes.Text);
				}
				settings.DiscardAfterMaxTimeToAnswer = checkboxDiscardAfterMaxTimeToAnswer.Checked;
				settings.DiscardAndCloseCaseAfterMaxTimeToAnswer = checkboxDiscardAndCloseCaseAfterMaxTimeToAnswer.Checked;
				
				settings.UseAutoReplyBeforeCloseCase = checkboxAutoReplyBeforeCaseIsClosed.Checked;
				if (settings.UseAutoReplyBeforeCloseCase)
				{
					settings.AutoReplyBeforeCloseCaseText = textboxAutoReplyBeforeCaseIsClosedText.Text;
					settings.AutoReplyBeforeCloseCaseMinutes = short.Parse(textboxAutoReplyBeforeCaseIsClosedMinutes.Text);
				}
				settings.AllowToSendHSM = dropdownlistAllowToSendHSM.SelectedValue.Equals("1");
				if (settings.AllowToSendHSM)
				{
					settings.AllowAgentsToSendHSM = dropdownlistAllowAgentsToSendHSM.SelectedValue.Equals("1");
					settings.HSMTemplates = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.ServiceSettings.WhatsappSettings.HSMTemplate[]>(hiddenHSMTemplates.Value);
				}
				else
				{
					settings.AllowAgentsToSendHSM = false;
					settings.HSMTemplates = null;
				}
				settings.EnableVideo = checkboxEnableVideo.Checked;
				settings.EnableCapi = checkboxEnableCapi.Checked;

				settings.AllowToSendFlows = dropdownlistAllowToSendFlows.SelectedValue.Equals("1");
				if (settings.AllowToSendFlows)
				{
					settings.MetaFlows = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.Flows.Flow[]>(hiddenFlows.Value);
				}
				else
				{
					settings.MetaFlows = null;
				}

				settings.VoiceCallsEnabled = checkboxVoiceCallsEnabled.Checked;
				if (settings.VoiceCallsEnabled)
				{
					//settings.VoiceCallsAllowAgentsToSendInteractiveMessage = checkboxVoiceCallsAllowAgentsToSendInteractiveMessage.Checked;
					settings.VoiceCallsRecordingEnabled = checkboxVoiceCallsAllowRecording.Checked;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageInvite.Value))
						settings.VoiceCallsInteractiveMessageInvite = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessageVoiceCall>(hiddenVoiceCallInteractiveMessageInvite.Value);
					else
						settings.VoiceCallsInteractiveMessageInvite = null;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable.Value))
						settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessage>(hiddenVoiceCallInteractiveMessageRejectAgentNotAvailable.Value);
					else
						settings.VoiceCallsInteractiveMessageRejectAgentNotAvailable = null;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall.Value))
						settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessage>(hiddenVoiceCallInteractiveMessageRejectAgentWithAnotherCall.Value);
					else
						settings.VoiceCallsInteractiveMessageRejectAgentWithAnotherCall = null;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.Value))
						settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessage>(hiddenVoiceCallInteractiveMessageRejectUsersCaseNotAssigned.Value);
					else
						settings.VoiceCallsInteractiveMessageRejectUsersCaseNotAssigned = null;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall.Value))
						settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessage>(hiddenVoiceCallInteractiveMessageRejectCaseWithCurrentCall.Value);
					else
						settings.VoiceCallsInteractiveMessageRejectCaseWithCurrentCall = null;

					if (!string.IsNullOrEmpty(hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite.Value))
						settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite = Newtonsoft.Json.JsonConvert.DeserializeObject<DomainModel.Whatsapp.InteractiveMessage>(hiddenVoiceCallInteractiveMessageRejectCaseWithoutInvite.Value);
					else
						settings.VoiceCallsInteractiveMessageRejectCaseWithoutInvite = null;
				}

				settings.AllowSurveys = dropdownlistAllowSurveys.SelectedValue.Equals("1");

				if (Licensing.LicenseManager.Instance.License.Configuration.AllowYFlow &&
					DomainModel.SystemSettings.Instance.YFlow.Enabled &&
					(dropdownlistUseYFlow.SelectedValue.Equals("true") || Convert.ToBoolean(dropdownlistUseYFlow.SelectedValue)))
				{
					var jYFlowSettings = Newtonsoft.Json.Linq.JObject.Parse(hiddenFlow.Value);
					if (jYFlowSettings["id"] != null || jYFlowSettings["ID"] != null)
					{
						service.YFlow = (jYFlowSettings["id"] != null) ? jYFlowSettings["id"].ToObject<int>() : jYFlowSettings["ID"].ToObject<int>();
						service.YFlowSettings = new DomainModel.ServiceSettings.YFlowSettings();
						service.YFlowSettings.ID = service.YFlow.Value;
						service.YFlowSettings.Name = (jYFlowSettings["name"] != null) ? jYFlowSettings["name"].ToString() : jYFlowSettings["Name"].ToString();
						service.YFlowSettings.Version = (jYFlowSettings["Version"] != null) ? jYFlowSettings["Version"].ToObject<int>() : Convert.ToInt32(jYFlowSettings["ActiveProductionVersion"]["number"].ToString());

						if (!string.IsNullOrEmpty(hiddenFlowQueueTransfersByKey.Value))
						{
							service.YFlowSettings.QueueTransfersByKey = new Dictionary<string, int>();
							var jFlowQueueTransfersByKey = (Newtonsoft.Json.Linq.JArray) Newtonsoft.Json.JsonConvert.DeserializeObject(hiddenFlowQueueTransfersByKey.Value);
							foreach (Newtonsoft.Json.Linq.JObject jTransfer in jFlowQueueTransfersByKey)
							{
								try
								{
									service.YFlowSettings.QueueTransfersByKey[jTransfer["Key"].ToString()] = jTransfer["QueueID"].ToObject<int>();
								}
								catch { }
							}
						}

						if (listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex == -1)
						{
							service.YFlowSettings.QueuesToShareEnqueuedMessages = null;
						}
						else
						{
							service.YFlowSettings.QueuesToShareEnqueuedMessages = listboxFlowShareEnqueuedMessagesFromQueues.GetSelectedIndices()
								.Select(index => int.Parse(listboxFlowShareEnqueuedMessagesFromQueues.Items[index].Value)).ToArray();
						}

						if (listboxFlowShareConnectedAgentsFromQueues.SelectedIndex == -1)
						{
							service.YFlowSettings.QueuesToShareConnectedAgents = null;
						}
						else
						{
							service.YFlowSettings.QueuesToShareConnectedAgents = listboxFlowShareConnectedAgentsFromQueues.GetSelectedIndices()
								.Select(index => int.Parse(listboxFlowShareConnectedAgentsFromQueues.Items[index].Value)).ToArray();
						}

                        if (listboxFlowShareWithServices.SelectedIndex == -1)
                        {
                            service.YFlowSettings.ServicesToShareOpenCasesWithSameProfile = null;
                        }
                        else
                        {
                            service.YFlowSettings.ServicesToShareOpenCasesWithSameProfile = listboxFlowShareWithServices.GetSelectedIndices()
                                .Select(index => int.Parse(listboxFlowShareWithServices.Items[index].Value)).ToArray();
                        }

                        short.TryParse(textboxFlowMinutesAfterAgentClosedCase.Text, out short minutesAfterAgentClosedCase);
						service.YFlowSettings.MinutesAfterAgentClosedCase = minutesAfterAgentClosedCase;

						service.YFlowSettings.AllowAgentsToReturnMessagesToYFlow = checkboxAllowAgentsToReturnMessagesToYFlow.Checked;
					}

					if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
					{
						service.YFlowSettings.SurveyEnabled = checkboxEnableSurveys.Checked;
						if (service.YFlowSettings.SurveyEnabled)
						{
							service.YFlowSettings.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(hiddenSurveys.Value);
						}
						else
						{
							service.YFlowSettings.SurveyList = new List<QueueSurveyConfiguration>();
						}
					}

					#region YFlow Contingencia

					if (Licensing.LicenseManager.Instance.License.Configuration.ContingencyBotEnabled &&
						DomainModel.SystemStatus.Instance.ContingencyBot.AllowContingencyBot &&
						this.LoggedUser.AccessTokenYFlowContingency != null)
					{
						var jYFlowContingencySettings = Newtonsoft.Json.Linq.JObject.Parse(hiddenFlowContingency.Value);
						if (jYFlowContingencySettings["id"] != null || jYFlowContingencySettings["ID"] != null)
						{
							service.YFlowContingency = (jYFlowContingencySettings["id"] != null) ? jYFlowContingencySettings["id"].ToObject<int>() : jYFlowContingencySettings["ID"].ToObject<int>();
							service.YFlowContingencySettings = new DomainModel.ServiceSettings.YFlowSettings();
							service.YFlowContingencySettings.ID = service.YFlowContingency.Value;
							service.YFlowContingencySettings.Name = (jYFlowContingencySettings["name"] != null) ? jYFlowContingencySettings["name"].ToString() : jYFlowContingencySettings["Name"].ToString();
							service.YFlowContingencySettings.Version = (jYFlowContingencySettings["Version"] != null) ? jYFlowContingencySettings["Version"].ToObject<int>() : Convert.ToInt32(jYFlowContingencySettings["ActiveProductionVersion"]["number"].ToString());

							if (!string.IsNullOrEmpty(hiddenFlowQueueTransfersByKey.Value))
							{
								service.YFlowContingencySettings.QueueTransfersByKey = new Dictionary<string, int>();
								var jFlowQueueTransfersByKey = (Newtonsoft.Json.Linq.JArray) Newtonsoft.Json.JsonConvert.DeserializeObject(hiddenFlowQueueTransfersByKey.Value);
								foreach (Newtonsoft.Json.Linq.JObject jTransfer in jFlowQueueTransfersByKey)
								{
									try
									{
										service.YFlowContingencySettings.QueueTransfersByKey[jTransfer["Key"].ToString()] = jTransfer["QueueID"].ToObject<int>();
									}
									catch { }
								}
							}

							if (listboxFlowShareEnqueuedMessagesFromQueues.SelectedIndex == -1)
							{
								service.YFlowContingencySettings.QueuesToShareEnqueuedMessages = null;
							}
							else
							{
								service.YFlowContingencySettings.QueuesToShareEnqueuedMessages = listboxFlowShareEnqueuedMessagesFromQueues.GetSelectedIndices()
									.Select(index => int.Parse(listboxFlowShareEnqueuedMessagesFromQueues.Items[index].Value)).ToArray();
							}

							if (listboxFlowShareConnectedAgentsFromQueues.SelectedIndex == -1)
							{
								service.YFlowContingencySettings.QueuesToShareConnectedAgents = null;
							}
							else
							{
								service.YFlowContingencySettings.QueuesToShareConnectedAgents = listboxFlowShareConnectedAgentsFromQueues.GetSelectedIndices()
									.Select(index => int.Parse(listboxFlowShareConnectedAgentsFromQueues.Items[index].Value)).ToArray();
							}

							short.TryParse(textboxFlowMinutesAfterAgentClosedCase.Text, out short minutesAfterAgentClosedCase);
							service.YFlowContingencySettings.MinutesAfterAgentClosedCase = minutesAfterAgentClosedCase;

							service.YFlowContingencySettings.AllowAgentsToReturnMessagesToYFlow = checkboxAllowAgentsToReturnMessagesToYFlow.Checked;
						}

						if (Licensing.LicenseManager.Instance.License.Configuration.SurveysEnabled && DomainModel.SystemSettings.Instance.EnableSurveys)
						{
							service.YFlowContingencySettings.SurveyEnabled = checkboxEnableSurveys.Checked;
							if (service.YFlowContingencySettings.SurveyEnabled)
							{
								service.YFlowContingencySettings.SurveyList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<QueueSurveyConfiguration>>(hiddenSurveys.Value);
							}
							else
							{
								service.YFlowContingencySettings.SurveyList = new List<QueueSurveyConfiguration>();
							}
						}
					}
					else
					{
						service.YFlowContingency = null;
					}

					#endregion
				}
				else
				{
					service.YFlow = null;
					service.YFlowContingency = null;
					//service.YFlowSettings = null;
					/*service.YFlowSettings.SurveyEnabled = false;
					service.YFlowSettings.Survey = null;
					service.YFlowSettings.SurveyConfiguration = null;*/
				}

				service.OverrideCasesSystemSettings = checkboxCasesOverrideSystemSettings.Checked;
				if (service.OverrideCasesSystemSettings)
				{
					service.CasesSettings = new DomainModel.Settings.CasesSettings();
					service.CasesSettings.CheckLastQueueOfOpenCase = checkboxCheckLastQueueOfOpenCase.Checked;
					service.CasesSettings.IgnoreLastQueueForSLMovedMessage = checkboxIgnoreLastQueueForSLMovedMessage.Checked;
					service.CasesSettings.MaxElapsedMinutesToCloseCases = ushort.Parse(textboxMaxElapsedMinutesToCloseCases.Text);
					service.CasesSettings.AllowToAutoReplyCloseCases = checkboxReplyInCloseCase.Checked;
					service.CasesSettings.AutoReplyInCloseCaseText = textboxAutoReplyInCloseCaseText.Text;
					if (!string.IsNullOrEmpty(textboxTagCloseCase.Text))
						service.CasesSettings.TagOnAutoCloseCase = int.Parse(textboxTagCloseCase.Text);
					else
						service.CasesSettings.TagOnAutoCloseCase = -1;

					service.CasesSettings.MaxElapsedMinutesToCloseHsmCases = ushort.Parse(textboxMaxElapsedMinutesToCloseHsmCases.Text);
					if (!string.IsNullOrEmpty(textboxTagOnHsmCases.Text))
						service.CasesSettings.TagOnAutoCloseHsmCases = int.Parse(textboxTagOnHsmCases.Text);
					else
						service.CasesSettings.TagOnAutoCloseHsmCases = -1;

					service.CasesSettings.MaxElapsedMinutesToCloseYFlowCases = ushort.Parse(textboxMaxElapsedMinutesToCloseYFlowCases.Text);
					service.CasesSettings.InvokeYFlowWhenClosedCases = checkboxInvokeYFlowWhenClosedCases.Checked;
				}

				try
				{
					if (configuration.ServiceIntegrationType == DomainModel.ServiceSettings.WhatsappSettings.IntegrationTypes.Yoizen)
					{

						var yoizenBspService = new LinesService(configuration);

						if (service.Enabled)
						{
							await yoizenBspService.UpdateLineQueuesName(configuration.IntegrationType10AccountID, configuration.IntegrationType10LineID);

							if (settings.VoiceCallsEnabled)
							{
								bool hasCallEnabeld = await yoizenBspService.LineHasCallsEnabled(configuration.FullPhoneNumber);

								if (!hasCallEnabeld)
								{
									await yoizenBspService.UpdateLinesCalls(configuration.FullPhoneNumber, true);
								}
							}
						}
					}
				}
				catch (Exception ex)
				{

				}

				if (this.CreatingNewService)
					ServiceDAO.Insert(service);
				else
					ServiceDAO.Update(service);

				await this.AfterSaveAsync(service);
				
				if (service.UsesYFlow &&
					service.YFlow != null)
				{
					try
					{
						var response = await YFlow.Manager.Instance.GetConfiguration(service.YFlow.Value, SocialServiceTypes.WhatsApp);
						if (response.Succeed)
						{
							if (response.Response["settings"] != null &&
								response.Response["settings"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
							{
								var jSettings = (Newtonsoft.Json.Linq.JObject) response.Response["settings"];
								if (jSettings["returnsFromAgent"] != null &&
									jSettings["returnsFromAgent"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
								{
									var jReturnsFromAgent = (Newtonsoft.Json.Linq.JArray) jSettings["returnsFromAgent"];
									service.YFlowSettings.ReturnsFromAgents = jReturnsFromAgent.ToObject<DomainModel.ServiceSettings.YFlowSettings.ReturnFromAgent[]>();
									DAL.ServiceDAO.Update(service);
								}
							}
						}
						else
						{
							Tracer.TraceError("No se pudo obtener la configuración del servicio desde yFlow: {0}", response.Exception);
						}
					}
					catch (Exception ex)
					{
						Tracer.TraceError("Ocurrió un error al intentar obtener la configuración del servicio desde yFlow: {0}", ex);
					}
				}
				
				Response.Redirect(this.RedirectUrl, false);
			}
		}

		protected void buttonCancel_Click(object sender, EventArgs e)
		{
			Response.Redirect(this.RedirectUrl);
		}

		protected override string GetServiceToCopyId()
		{
			return hiddenServiceToCopy.Value;
		}

		protected override void CleanHiddenServiceToCopyId()
		{
			hiddenServiceToCopy.Value = "";
		}

		#endregion

		#region WebMethods

		[System.Web.Services.WebMethod]
		public static object IsPhoneNumberUsedInAnotherService(int? serviceId, string phoneNumber)
		{
			try
			{
				if (string.IsNullOrEmpty(phoneNumber))
					throw new ArgumentNullException(nameof(phoneNumber));

				var existingServices = DomainModel.Cache.Instance.GetList<DomainModel.Service>();
				if (existingServices != null && existingServices.Any())
				{
					foreach (var existingService in existingServices)
					{
						if (serviceId != null && existingService.ID == serviceId.Value)
							continue;

						if (existingService.Type == ServiceTypes.WhatsApp)
						{
							var configuration = new Social.SocialServices.WhatsApp.WhatsAppServiceConfiguration(existingService.Configuration);
							if (configuration.FullPhoneNumber.Equals(phoneNumber))
							{
								Tracer.TraceInfo("El servicio ({0} - {1}) que monitorea el mismo número de línea", existingService.ID, existingService.Name);

								return new
								{
									Success = true,
									ExistsAnotherService = true,
									Service = new
									{
										ID = existingService.ID,
										Name = existingService.Name,
										Type = existingService.Type
									}
								};
							}
						}
					}
				}

				return new
				{
					Success = true,
					ExistsAnotherService = false
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveAccountHSMs(string accessToken, string baseUrl)
		{
			try
			{
				var client = new RestSharp.RestClient(baseUrl);

				string @namespace = null;

				var request = new RestSharp.RestRequest("api/v1/waba", RestSharp.Method.GET);
				request.AddHeader("Authorization", $"Bearer {accessToken}");
				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

					@namespace = jContent["info"]["message_template_namespace"].ToString();
				}

				request = new RestSharp.RestRequest("api/v1/templates", RestSharp.Method.GET);
				request.AddHeader("Authorization", $"Bearer {accessToken}");
				response = client.Execute(request);
				if (response.IsSuccessful)
				{
					var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

					return new
					{
						Success = true,
						Templates = jContent["templates"].ToString(),
						Namespace = @namespace
					};
				}
				else
				{
					return new
					{
						Success = false,
						Error = new
						{
							Message = response.Content
						}
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		private static JArray GetFlowAssetData(string accessToken, string baseUrl, string flowId)
		{
			try
			{
				var client = new RestSharp.RestClient(baseUrl);

				var url = $"api/v1/flows/assets/{flowId}";

				var request = new RestSharp.RestRequest(url, RestSharp.Method.GET);
				request.AddHeader("Authorization", $"Bearer {accessToken}");
				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

					if (jContent["flow_assets"] != null && jContent["flow_assets"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
					{
						var jAssets = (JArray)jContent["flow_assets"];
						
						return jAssets;
					}
				}
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"Falló al obtener los assets del Flow: {ex}");
			}
			return null;
		}
		private static JArray GetCloudApiFlowAssetData(string version, string flowId, string accessToken)
		{
			try
			{
				var baseUrl = $"https://graph.facebook.com/{version}/{flowId}/assets?access_token={accessToken}";
				var url = baseUrl;
				var retrieveMore = true;

				var jAssets = new Newtonsoft.Json.Linq.JArray();

				var httpClient = new System.Net.Http.HttpClient();

				while (retrieveMore)
				{
					using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
					{
						using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
						{
							if (response.IsSuccessStatusCode)
							{
								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);

								if (jContents["data"] != null && jContents["data"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
								{
									var jData = (Newtonsoft.Json.Linq.JArray) jContents["data"];
									if (jData.Count > 0)
									{
										foreach (Newtonsoft.Json.Linq.JObject jAsset in jData)
										{
											jAssets.Add(jAsset);
										}

										if (jContents["paging"] != null && jContents["paging"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
										{
											var jPaging = (Newtonsoft.Json.Linq.JObject) jContents["paging"];
											if (jPaging["cursors"] != null && jPaging["cursors"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
											{
												var jCursors = (Newtonsoft.Json.Linq.JObject) jPaging["cursors"];
												if (jCursors["after"] != null && jCursors["after"].Type == Newtonsoft.Json.Linq.JTokenType.String)
												{
													var after = jCursors["after"].ToString();

													url = $"{baseUrl}&after={Uri.EscapeUriString(after)}";
												}
												else
												{
													retrieveMore = false;
												}
											}
											else
											{
												retrieveMore = false;
											}
										}
										else
										{
											retrieveMore = false;
										}
									}
									else
									{
										retrieveMore = false;
									}
								}
								else
								{
									retrieveMore = false;
								}
							}
							else
							{
								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								Tracer.TraceError("No se pudo obtener los assets del flow: {0}", contents);
								retrieveMore = false;
							}
						}
					}
				}

				return jAssets;
			}
			catch (Exception ex)
			{
				Tracer.TraceError($"Fallo al obtener los assets de CloudApi {ex}");
			}

			return null;
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveAccountFlows(string accessToken, string baseUrl)
		{
			try
			{
				var client = new RestSharp.RestClient(baseUrl);

				var request = new RestSharp.RestRequest("api/v1/flows", RestSharp.Method.GET);
				request.AddHeader("Authorization", $"Bearer {accessToken}");
				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);
					
					if (jContent["flows"] != null && jContent["flows"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
					{
						JArray flowsArray = (JArray) jContent["flows"];

						foreach (JObject flow in flowsArray)
						{
							//Si meta no devuelve los assetis del flow, los buscamos
							if (flow["assets"] == null)
							{
								var flowAsset = GetFlowAssetData(accessToken, baseUrl, flow["id"].ToString());

								if (flowAsset != null)
								{
									var jAssets = new JObject();
									jAssets["data"] = flowAsset;

									flow["assets"] = jAssets;
								}
							}
						}
					}

					return new
					{
						Success = true,
						Flows = jContent["flows"].ToString()
					};
				}
				else
				{
					return new
					{
						Success = false,
						Error = new
						{
							Message = response.Content
						}
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveFlowAssetsData(string accessToken, string baseUrl, string flowId)
		{
			try
			{
				var jAssets = GetFlowAssetData(accessToken, baseUrl, flowId);
				return new
				{
					Success = true,
					FlowAssets = jAssets != null ? jAssets.ToString() : null
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveAccountHSMsInfobip(byte authorizationType, string user, string password, string apiKey, string baseUrl, string phoneNumber)
		{
			try
			{
				var client = new RestSharp.RestClient(baseUrl);

				var request = new RestSharp.RestRequest($"whatsapp/2/senders/{phoneNumber}/templates", RestSharp.Method.GET);
				if (authorizationType == 1)
					request.AddHeader("Authorization", $"Basic {Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", user, password)))}");
				else
					request.AddHeader("Authorization", $"App {apiKey}");
				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

					return new
					{
						Success = true,
						Templates = jContent["templates"].ToString(),
					};
				}
				else
				{
					return new
					{
						Success = false,
						Error = new
						{
							Message = response.Content
						}
					};
				}
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveAccountFlowsCloudApi(string accessToken, string version, string wabaId)
		{

			try
			{
				var baseUrl = $"https://graph.facebook.com/{version}/{wabaId}/flows?access_token={accessToken}";
				var url = baseUrl;
				var retrieveMore = true;
				var atLeastOnePageRetrieved = false;

				var jMetaFlows = new Newtonsoft.Json.Linq.JArray();

				var httpClient = new System.Net.Http.HttpClient();

				while (retrieveMore)
				{
					using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
					{
						using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
						{
							if (response.IsSuccessStatusCode)
							{
								atLeastOnePageRetrieved = true;

								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);

								if (jContents["data"] != null && jContents["data"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
								{
									var jData = (Newtonsoft.Json.Linq.JArray) jContents["data"];
									if (jData.Count > 0)
									{
										foreach (Newtonsoft.Json.Linq.JObject jMetaFlow in jData)
										{
											if (jMetaFlow["assets"] == null) 
											{
												JObject jAsset = new JObject();
												jAsset["data"] = GetCloudApiFlowAssetData(version, jMetaFlow["id"].ToString(), accessToken);
												jMetaFlow["assets"] = jAsset;
											}

											jMetaFlows.Add(jMetaFlow);
										}

										if (jContents["paging"] != null && jContents["paging"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
										{
											var jPaging = (Newtonsoft.Json.Linq.JObject) jContents["paging"];
											if (jPaging["cursors"] != null && jPaging["cursors"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
											{
												var jCursors = (Newtonsoft.Json.Linq.JObject) jPaging["cursors"];
												if (jCursors["after"] != null && jCursors["after"].Type == Newtonsoft.Json.Linq.JTokenType.String)
												{
													var after = jCursors["after"].ToString();

													url = $"{baseUrl}&after={Uri.EscapeUriString(after)}";
												}
												else
												{
													retrieveMore = false;
												}
											}
											else
											{
												retrieveMore = false;
											}
										}
										else
										{
											retrieveMore = false;
										}
									}
									else
									{
										retrieveMore = false;
									}
								}
								else
								{
									retrieveMore = false;
								}
							}
							else
							{
								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								Tracer.TraceError("No se pudo obtener los templates: {0}", contents);

								retrieveMore = false;

								if (!atLeastOnePageRetrieved)
								{
									return new
									{
										Success = false,
										Error = new
										{
											Message = "couldn't retrieve the templates"
										}
									};
								}
							}
						}
					}
				}

				return new
				{
					Success = true,
					Flows = jMetaFlows.ToString(),
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveAccountHSMsCloudApi (string accessToken, string version, string wabaId)
		{
			var limit = 1200;
			var url = $"https://graph.facebook.com/{version}/{wabaId}/message_templates?limit={limit}";

			var jResponse = new Newtonsoft.Json.Linq.JObject();
			jResponse["success"] = true;
			var jTemplates = new Newtonsoft.Json.Linq.JArray();
			jResponse["templates"] = jTemplates;
			var httpClient = new System.Net.Http.HttpClient();
			try
			{
				while (!string.IsNullOrEmpty(url))
				{

					using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
					{
						request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
						using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
						{
							var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
							
							if (!response.IsSuccessStatusCode)
							{
								Tracer.TraceError("No se pudo obtener los datos de la waba: {0}", contents);

								return new
								{
									Success = false,
									Error = new
									{
										Message = "couldn't retrieve the templates"
									}
								};
							}

							var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);
							
							if (jContents["data"] is Newtonsoft.Json.Linq.JArray jData && jData.Count > 0)
							{
								jTemplates.Merge(jData);
							}

							url = jContents["paging"]?["next"]?.ToString();
						}
					}
				}

				string @namespace = null;

				url = $"https://graph.facebook.com/{version}/{wabaId}?access_token={accessToken}";

				using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
				{
					using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
					{
						if (response.IsSuccessStatusCode)
						{
							var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
							var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);

							@namespace = jContents["message_template_namespace"].ToString();
						}
						else
						{
							var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
							Tracer.TraceError("No se pudo obtener los datos de la waba: {0}", contents);

							return new
							{
								Success = false,
								Error = new
								{
									Message = "couldn't retrieve the waba"
								}
							};
						}
					}
				}

				return new
				{
					Success = true,
					Templates = jTemplates.ToString(),
					Namespace = @namespace
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object ValidateCloudApiAccessToken(string accessToken, string version, string wabaId)
		{
			try
			{
				string[] scopes = null;
				var isValid = false;

				var httpClient = new System.Net.Http.HttpClient();

				var url = $"https://graph.facebook.com/{version}/debug_token?access_token={accessToken}&input_token={accessToken}";
				using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
				{
					using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
					{
						if (response.IsSuccessStatusCode)
						{
							var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
							var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);

							if (jContents["data"] != null && jContents["data"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
							{
								var jData = (Newtonsoft.Json.Linq.JObject) jContents["data"];
								scopes = ((Newtonsoft.Json.Linq.JArray) jData["scopes"]).ToObject<string[]>();
								isValid = jData["is_valid"].ToObject<bool>();
							}
							else
							{
								return new
								{
									Success = false,
									Error = new
									{
										Message = "couldn't retrieve the access_token info"
									}
								};
							}
						}
						else
						{
							var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
							Tracer.TraceError("No se pudo validar el access token de la cloud api: {0}", contents);

							return new
							{
								Success = false,
								Error = new
								{
									Message = "couldn't retrieve the access_token info",
									Contents = contents
								}
							};
						}
					}
				}

				var baseUrl = $"https://graph.facebook.com/{version}/{wabaId}/phone_numbers?phone_numbers?fields=account_mode,code_verification_status,display_phone_number,id,name_status,new_name_status,status&access_token={accessToken}";
				url = baseUrl;
				var retrieveMore = true;

				var jPhoneNumbers = new Newtonsoft.Json.Linq.JArray();

				while (retrieveMore)
				{
					using (var request = new System.Net.Http.HttpRequestMessage(System.Net.Http.HttpMethod.Get, url))
					{
						using (var response = httpClient.SendAsync(request).GetAwaiter().GetResult())
						{
							if (response.IsSuccessStatusCode)
							{
								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								var jContents = Newtonsoft.Json.Linq.JObject.Parse(contents);

								if (jContents["data"] != null && jContents["data"].Type == Newtonsoft.Json.Linq.JTokenType.Array)
								{
									var jData = (Newtonsoft.Json.Linq.JArray) jContents["data"];
									if (jData.Count > 0)
									{
										foreach (Newtonsoft.Json.Linq.JObject jPhoneNumber in jData)
										{
											jPhoneNumbers.Add(jPhoneNumber);
										}

										if (jContents["paging"] != null && jContents["paging"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
										{
											var jPaging = (Newtonsoft.Json.Linq.JObject) jContents["paging"];
											if (jPaging["cursors"] != null && jPaging["cursors"].Type == Newtonsoft.Json.Linq.JTokenType.Object)
											{
												var jCursors = (Newtonsoft.Json.Linq.JObject) jPaging["cursors"];
												if (jCursors["after"] != null && jCursors["after"].Type == Newtonsoft.Json.Linq.JTokenType.String)
												{
													var after = jCursors["after"].ToString();

													url = $"{baseUrl}&after={Uri.EscapeUriString(after)}";
												}
												else
												{
													retrieveMore = false;
												}
											}
											else
											{
												retrieveMore = false;
											}
										}
										else
										{
											retrieveMore = false;
										}
									}
									else
									{
										retrieveMore = false;
									}
								}
								else
								{
									retrieveMore = false;
								}
							}
							else
							{
								var contents = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
								Tracer.TraceError("No se pudo obtener los templates: {0}", contents);

								retrieveMore = false;
							}
						}
					}
				}

				return new
				{
					Success = true,
					TokenInfo = new
					{
						IsValid = isValid,
						Scopes = scopes
					},
					PhoneNumbers = jPhoneNumbers.ToString()
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}
		
		[System.Web.Services.WebMethod]
		public static object ValidateInteraxaUrl(string url, string username, string password)
		{
			try
			{
				var client = new RestSharp.RestClient(url);

				var request = new RestSharp.RestRequest("api/v2/users/sign_in", RestSharp.Method.POST);
				var body = Newtonsoft.Json.JsonConvert.SerializeObject(new { username = username, password = password });
				request.AddParameter("application/json; charset=utf-8", body, RestSharp.ParameterType.RequestBody);

				var response = client.Execute(request);
				if (response.IsSuccessful)
				{
					if (response.ContentType.StartsWith("application/json"))
					{
						var jContent = Newtonsoft.Json.Linq.JObject.Parse(response.Content);

						return new
						{
							Success = true,
							IsValid = true,
							Token = jContent["jwt"].ToString()
						};
					}
					else
					{
						var content = response.Content; // raw content as string

						return new
						{
							Success = true,
							IsValid = true,
							Content = content
						};
					}
				}
				else
				{
					return new
					{
						Success = true,
						IsValid = false
					};
				}
			}
			catch (Exception ex)
			{
				Common.Tracer.TraceError("Error al validar la url de interaxa: {0}", ex);
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetAccountInfoForFacebook(string accessToken, int expires)
		{
			try
			{
				string longLivedAccessToken = null;
				int? longLivedExpires = 0;

				if (expires > 0)
				{
					TimeSpan expiresTimeSpan = TimeSpan.FromSeconds(expires);
					if (expiresTimeSpan.TotalHours < 24)
						Yoizen.Social.SocialServices.Facebook.FacebookTokens.GetLongLivedAccessToken(accessToken, DomainModel.SystemSettings.Instance.Whatsapp.CatalogApp, out longLivedAccessToken, out longLivedExpires);
				}

				Yoizen.Social.SocialServices.Facebook.Wrappers.User user;
				Yoizen.Social.SocialServices.Facebook.Wrappers.Businesses businesses;

				if (longLivedAccessToken != null)
				{
					SocialServices.Facebook.FacebookTokens.GetUserAccountInfo(longLivedAccessToken, out user, out businesses);
				}
				else
				{
					SocialServices.Facebook.FacebookTokens.GetUserAccountInfo(accessToken, out user, out businesses);
				}

				return new
				{
					Success = true,
					User = user,
					Businesses = businesses,
					LongLivedAccessToken = longLivedAccessToken
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetBusinessesAfter(string accessToken, string after)
		{
			try
			{
				var businesses = Yoizen.Social.SocialServices.Facebook.FacebookTokens.GetBusinessesAfter(accessToken, after);

				return new
				{
					Success = true,
					Businesses = businesses
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetBusinessInfo(string accessToken, string businessId)
		{
			try
			{
				var business = Yoizen.Social.SocialServices.Facebook.FacebookTokens.GetBusiness(accessToken, businessId);

				return new
				{
					Success = true,
					Business = business
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetBusinessCatalogProducts(string accessToken, string catalogId)
		{
			try
			{
				var products = Yoizen.Social.SocialServices.Facebook.FacebookTokens.GetBusinessCatalogProducts(accessToken, catalogId);

				return new
				{
					Success = true,
					Products = products
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object GetAccessTokenInfoForFacebook(string accessToken)
		{
			try
			{
				Yoizen.Social.SocialServices.Facebook.Wrappers.AccessToken accessTokenInfo;
				accessTokenInfo = Yoizen.Social.SocialServices.Facebook.FacebookTokens.GetAccessTokenInfo(accessToken);

				return new
				{
					Success = true,
					Info = accessTokenInfo
				};
			}
			catch (Exception ex)
			{
				return new
				{
					Success = false,
					Error = new
					{
						Message = ex.Message,
						StackTrace = ex.StackTrace
					}
				};
			}
		}

		[System.Web.Services.WebMethod]
		public static object RetrieveServicesByServiceType(int serviceId)
		{
			return RetrieveServicesByServiceType(serviceId, ServiceTypes.WhatsApp);
		}

		#endregion
	}
}