﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Yoizen.Social.DomainModel.ServiceSettings
{
	public class InstagramSettings : ServiceSettings
	{
		#region Inner Classes

		public class TagTemplateParameter
		{
			#region Properties

			/// <summary>
			/// Devuelve o establece el nombre del parámetro
			/// </summary>
			public string Name { get; set; }

			/// <summary>
			/// Devuelve o establece la descripción del parámetro
			/// </summary>
			public string Description { get; set; }

			#endregion

			#region Constructors

			/// <summary>
			/// Inicializa una nueva instancia de TagTemplateParameter
			/// </summary>
			public TagTemplateParameter()
			{
			}

			/// <summary>
			/// Inicializa una nueva instancia de TagTemplateParameter
			/// </summary>
			/// <param name="parameter">Los datos de un parámetro ingresados como [nombre]=[descripción]</param>
			public TagTemplateParameter(string parameter)
			{
				var index = parameter.IndexOf("=");
				if (index >= 0)
				{
					this.Name = parameter.Substring(0, index);
					this.Description = parameter.Substring(index + 1);
				}
				else
				{
					this.Name = parameter;
					this.Description = parameter;
				}
			}

			#endregion
		}

		/// <summary>
		/// Definición de un mensaje de plantilla de etiqueta de Messenger
		/// </summary>
		public class TagTemplate
		{
			/// <summary>
			/// Devuelve o establece el ID de la plantilla
			/// </summary>
			public string ID { get; set; }

			/// <summary>
			/// Devuelve o establece la descripción de la plantilla
			/// </summary>
			public string Description { get; set; }

			/// <summary>
			/// Devuelve o establece el tipo de etiqueta
			/// </summary>
			public string Tag { get; set; }

			/// <summary>
			/// Devuelve o establece el texto de la plantilla
			/// </summary>
			public string Template { get; set; }

			/// <summary>
			/// Devuelve o establece los parámetros de la plantilla
			/// </summary>
			public string[] Parameters { get; set; }

			/// <summary>
			/// Devuelve o establece los parámetros de la plantilla
			/// </summary>
			public TagTemplateParameter[] TemplateParameters
			{
				get
				{
					if (this.Parameters == null || this.Parameters.Length == 0)
						return null;

					return this.Parameters.Select(p => new TagTemplateParameter(p)).ToArray();
				}
			}
		}

		#endregion

		#region Properties

		/// <summary>
		/// Devuelve el tipo de servicio asociado a los parámetros definidos en la instancia
		/// </summary>
		[LocalizedDescription("InstagramSettings_ServiceType")]
		public override ServiceTypes ServiceType { get { return ServiceTypes.Instagram; } }

		/// <summary>
		/// Devuelve o establece si el servicio de Instagram permite mandar archivos multimedia
		/// </summary>
		[LocalizedDescription("InstagramSettings_AllowToSendMultimedia")]
		public bool AllowToSendMultimedia { get; set; }

		/// <summary>
		/// Devuelve si el servicio soporta enviar archivos adjuntos
		/// </summary>
		[LocalizedDescription("InstagramSettings_AllowToSendAttachments")]
		public override bool AllowToSendAttachments { get { return this.AllowToSendMultimedia; } }

		/// <summary>
		/// Devuelve o establece la configuración de archivos adjuntos
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("InstagramSettings_Attachments")]
		public InstagramAttachmentsSettings Attachments { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de mail para cuando ocurre un error de OAuth de Instagram
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("InstagramSettings_OAuthErrorOcurred")]
		public Settings.EmailSettings OAuthErrorOcurred { get; set; }

		/// <summary>
		/// Devuelve o establece los minutos que deben pasar para considerar inactivo el servicio indicando que puede haber un problema
		/// </summary>
		[LocalizedDescription("InstagramSettings_MinutesForInactivity")]
		public int MinutesForInactivity { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de mail para cuando se detecta inactividad en Instagram
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("InstagramSettings_InactivityDetected")]
		public Settings.EmailSettings InactivityDetected { get; set; }

		/// <summary>
		/// Devuelve o establece los permisos de Instagram
		/// </summary>
		[LocalizedDescription("InstagramSettings_Permissions")]
		public string[] Permissions { get; set; }

		/// <summary>
		/// Devuelve o establece si se responderán los mensajes dentro de la ventana de 24 horas
		/// </summary>
		[LocalizedDescription("InstagramSettings_AutoReplyBeforeMaxTimeToAnswer")]
		public bool AutoReplyBeforeMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece el texto de la respuesta de los mensajes dentro de la ventana de 24 horas cuando <see cref="AutoReplyBeforeMaxTimeToAnswer"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("InstagramSettings_AutoReplyBeforeMaxTimeToAnswerText")]
		public string AutoReplyBeforeMaxTimeToAnswerText { get; set; }

		/// <summary>
		/// Devuelve o establece la cantidad de minutos previos a las 24 horas para enviar la respuesta siempre y cuando
		/// <see cref="AutoReplyBeforeMaxTimeToAnswer"/> sea <code>true</code>
		/// </summary>
		[LocalizedDescription("InstagramSettings_AutoReplyBeforeMaxTimeToAnswerMinutes")]
		public short AutoReplyBeforeMaxTimeToAnswerMinutes { get; set; }

		/// <summary>
		/// Devuelve o establece si se descartarán los mensajes cuando se cumplan 24 horas de encolado
		/// </summary>
		[LocalizedDescription("InstagramSettings_DiscardAfterMaxTimeToAnswer")]
		public bool DiscardAfterMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece si se cerrará el caso al descartar los mensajes cuando se cumplan 24 horas de encolado
		/// </summary>
		/// <remarks>
		/// Esta propiedad depende que <see cref=" DiscardAfterMaxTimeToAnswer"/> sea <code>true</code>
		/// </remarks>
		[LocalizedDescription("InstagramSettings_DiscardAndCloseCaseAfterMaxTimeToAnswer")]
		public bool DiscardAndCloseCaseAfterMaxTimeToAnswer { get; set; }

		/// <summary>
		/// Devuelve o establece si se utilizará el mensaje previo al cierre del caso
		/// </summary>
		[LocalizedDescription("InstagramSettings_UseAutoReplyBeforeCloseCase")]
		public bool UseAutoReplyBeforeCloseCase { get; set; }

		/// <summary>
		/// Devuelve o establece el mensaje previo al cierre del caso
		/// </summary>
		[LocalizedDescription("InstagramSettings_AutoReplyBeforeCloseCaseText")]
		public string AutoReplyBeforeCloseCaseText { get; set; }

		/// <summary>
		/// Devuelve o establece los minutos previos al cierre del caso
		/// </summary>
		[LocalizedDescription("InstagramSettings_AutoReplyBeforeCloseCaseMinutes")]
		public int AutoReplyBeforeCloseCaseMinutes { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite enviar mensajes de plantilla (HSM)
		/// </summary>
		[LocalizedDescription("InstagramSettings_AllowToSendTags")]
		public bool AllowToSendTags { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite a los agentes enviar mensajes de plantilla (HSM) simpre y cuando <see cref="AllowToSendTags"/>
		/// sea <code>true</code>
		/// </summary>
		[LocalizedDescription("InstagramSettings_AllowAgentsToSendTags")]
		public bool AllowAgentsToSendTags { get; set; }

		/// <summary>
		/// Devuelve o establece la configuración de las plantillas de mensaje (HSM)
		/// </summary>
		[ExpandableProperty]
		[LocalizedDescription("InstagramSettings_HSMTemplates")]
		public TagTemplate[] TagTemplates { get; set; }

		/// <summary>
		/// Devuelve o establece si se permite enviar encuestas a mensajes de Whatsapp
		/// </summary>
		[LocalizedDescription("InstagramSettings_AllowSurveys")]
		public bool AllowSurveys { get; set; }

		/// <summary>
		/// Devuelve o establece si se permitirán configurar Conversations API
		/// </summary>
		[LocalizedDescription("InstagramSettings_EnableCapi")]
		public bool EnableCapi { get; set; }

		#endregion

		#region Constructors

		public InstagramSettings()
			: base()
		{
			this.Attachments = new InstagramAttachmentsSettings();
			this.OAuthErrorOcurred = new Settings.EmailSettings("Instagram.OAuthErrorOcurred");
			this.InactivityDetected = new Settings.EmailSettings("Instagram.InactivityDetected");
			this.MinutesForInactivity = 1440;
			this.Permissions = new string[] { "pages_manage_metadata", "pages_show_list", "instagram_basic", "instagram_manage_comments", "instagram_manage_messages" };

#if !DEBUG
			this.OAuthErrorOcurred.Emails = "<EMAIL>";
			this.InactivityDetected.Emails = "<EMAIL>";
#endif
			this.OAuthErrorOcurred.Subject = "Ocurrió un error autenticando contra Instagram";
			this.OAuthErrorOcurred.Template = "<div style=\"font-family: 'Trebuchet MS';\">El @@FECHA@@ ocurrió un error autenticando el servicio @@SERVICIO@@ contra Instagram.</div><div style=\"font-family: 'Trebuchet MS';\">El mensaje de error es @@ERROR@@.</div>";
			this.InactivityDetected.Subject = "Se detectó inactividad en el servicio de Instagram";
			this.InactivityDetected.Template = "<div style=\"font-family: 'Trebuchet MS';\">El @@FECHA@@ se detectó que no se reciben novedades del servicio de Instagram @@SERVICIO@@ desde hace @@MINUTOS@@ minutos.</div>";
#if DEBUG
			this.MinutesForInactivity = 999999;
#endif
			this.AutoReplyBeforeMaxTimeToAnswer = false;
			this.AutoReplyBeforeMaxTimeToAnswerText = string.Empty;
			this.AutoReplyBeforeMaxTimeToAnswerMinutes = 60;
			this.DiscardAfterMaxTimeToAnswer = false;
			this.DiscardAndCloseCaseAfterMaxTimeToAnswer = false;
			this.AllowToSendTags = false;
			this.AllowAgentsToSendTags = false;
			this.TagTemplates = null;
			this.AllowSurveys = false;
			this.UseAutoReplyBeforeCloseCase = false;
		}
		#endregion

		#region Public Methods

		public override bool HasEmailConnnectionInUse(Guid emailId)
		{
			if (!string.IsNullOrEmpty(this.OAuthErrorOcurred.EmailConnection) &&
				Guid.TryParse(this.OAuthErrorOcurred.EmailConnection, out Guid authGuid))
			{
				return emailId == authGuid;
			}
			if (!string.IsNullOrEmpty(this.InactivityDetected.EmailConnection) &&
				Guid.TryParse(this.InactivityDetected.EmailConnection, out Guid inactivityGuid))
			{
				return emailId == inactivityGuid;
			}

			return false;
		}

		/// <summary>
		/// Retorna los parámetros relacionados a enviar mensaje previo a cerrar el caso 
		/// </summary>
		/// <param name="text">Indica el texto del mensaje a agregar, cuando se utilice la funcionalidad</param>
		/// <param name="minutes">Indica cuántos minutos antes del cierre del caso se enviará el mensaje, cuando se utilice la funcionalidad</param>
		/// <returns><code>true</code> si se utilizará la funcionalidad de agregar mensaje al caso minutos antes del cierre; en caso contrario, <code>false</code></returns>
		public override bool UseAutoReplyBeforeCaseClose(out string text, out int minutes)
		{
			text = this.AutoReplyBeforeCloseCaseText;
			minutes = this.AutoReplyBeforeCloseCaseMinutes;

			return this.UseAutoReplyBeforeCloseCase;
		}

		#endregion
	}
}
